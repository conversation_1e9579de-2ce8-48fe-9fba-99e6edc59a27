2025-05-20 00:00:01,991 - INFO - =================使用默认全量同步=============
2025-05-20 00:00:03,562 - INFO - MySQL查询成功，共获取 3296 条记录
2025-05-20 00:00:03,563 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 00:00:03,591 - INFO - 开始处理日期: 2025-01
2025-05-20 00:00:03,594 - INFO - Request Parameters - Page 1:
2025-05-20 00:00:03,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:03,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:04,268 - INFO - Response - Page 1:
2025-05-20 00:00:04,468 - INFO - 第 1 页获取到 100 条记录
2025-05-20 00:00:04,468 - INFO - Request Parameters - Page 2:
2025-05-20 00:00:04,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:04,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:05,004 - INFO - Response - Page 2:
2025-05-20 00:00:05,205 - INFO - 第 2 页获取到 100 条记录
2025-05-20 00:00:05,205 - INFO - Request Parameters - Page 3:
2025-05-20 00:00:05,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:05,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:05,738 - INFO - Response - Page 3:
2025-05-20 00:00:05,938 - INFO - 第 3 页获取到 100 条记录
2025-05-20 00:00:05,938 - INFO - Request Parameters - Page 4:
2025-05-20 00:00:05,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:05,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:06,568 - INFO - Response - Page 4:
2025-05-20 00:00:06,768 - INFO - 第 4 页获取到 100 条记录
2025-05-20 00:00:06,768 - INFO - Request Parameters - Page 5:
2025-05-20 00:00:06,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:06,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:07,431 - INFO - Response - Page 5:
2025-05-20 00:00:07,631 - INFO - 第 5 页获取到 100 条记录
2025-05-20 00:00:07,631 - INFO - Request Parameters - Page 6:
2025-05-20 00:00:07,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:07,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:08,162 - INFO - Response - Page 6:
2025-05-20 00:00:08,363 - INFO - 第 6 页获取到 100 条记录
2025-05-20 00:00:08,363 - INFO - Request Parameters - Page 7:
2025-05-20 00:00:08,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:08,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:08,958 - INFO - Response - Page 7:
2025-05-20 00:00:09,159 - INFO - 第 7 页获取到 82 条记录
2025-05-20 00:00:09,159 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 00:00:09,159 - INFO - 获取到 682 条表单数据
2025-05-20 00:00:09,170 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 00:00:09,181 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 00:00:09,182 - INFO - 开始处理日期: 2025-02
2025-05-20 00:00:09,182 - INFO - Request Parameters - Page 1:
2025-05-20 00:00:09,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:09,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:09,714 - INFO - Response - Page 1:
2025-05-20 00:00:09,914 - INFO - 第 1 页获取到 100 条记录
2025-05-20 00:00:09,914 - INFO - Request Parameters - Page 2:
2025-05-20 00:00:09,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:09,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:10,429 - INFO - Response - Page 2:
2025-05-20 00:00:10,630 - INFO - 第 2 页获取到 100 条记录
2025-05-20 00:00:10,630 - INFO - Request Parameters - Page 3:
2025-05-20 00:00:10,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:10,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:11,156 - INFO - Response - Page 3:
2025-05-20 00:00:11,357 - INFO - 第 3 页获取到 100 条记录
2025-05-20 00:00:11,357 - INFO - Request Parameters - Page 4:
2025-05-20 00:00:11,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:11,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:11,873 - INFO - Response - Page 4:
2025-05-20 00:00:12,074 - INFO - 第 4 页获取到 100 条记录
2025-05-20 00:00:12,074 - INFO - Request Parameters - Page 5:
2025-05-20 00:00:12,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:12,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:12,551 - INFO - Response - Page 5:
2025-05-20 00:00:12,751 - INFO - 第 5 页获取到 100 条记录
2025-05-20 00:00:12,751 - INFO - Request Parameters - Page 6:
2025-05-20 00:00:12,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:12,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:13,301 - INFO - Response - Page 6:
2025-05-20 00:00:13,501 - INFO - 第 6 页获取到 100 条记录
2025-05-20 00:00:13,501 - INFO - Request Parameters - Page 7:
2025-05-20 00:00:13,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:13,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:13,968 - INFO - Response - Page 7:
2025-05-20 00:00:14,168 - INFO - 第 7 页获取到 70 条记录
2025-05-20 00:00:14,168 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 00:00:14,168 - INFO - 获取到 670 条表单数据
2025-05-20 00:00:14,181 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 00:00:14,194 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 00:00:14,194 - INFO - 开始处理日期: 2025-03
2025-05-20 00:00:14,195 - INFO - Request Parameters - Page 1:
2025-05-20 00:00:14,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:14,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:14,786 - INFO - Response - Page 1:
2025-05-20 00:00:14,987 - INFO - 第 1 页获取到 100 条记录
2025-05-20 00:00:14,987 - INFO - Request Parameters - Page 2:
2025-05-20 00:00:14,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:14,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:15,448 - INFO - Response - Page 2:
2025-05-20 00:00:15,648 - INFO - 第 2 页获取到 100 条记录
2025-05-20 00:00:15,648 - INFO - Request Parameters - Page 3:
2025-05-20 00:00:15,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:15,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:16,135 - INFO - Response - Page 3:
2025-05-20 00:00:16,335 - INFO - 第 3 页获取到 100 条记录
2025-05-20 00:00:16,335 - INFO - Request Parameters - Page 4:
2025-05-20 00:00:16,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:16,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:16,861 - INFO - Response - Page 4:
2025-05-20 00:00:17,061 - INFO - 第 4 页获取到 100 条记录
2025-05-20 00:00:17,061 - INFO - Request Parameters - Page 5:
2025-05-20 00:00:17,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:17,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:17,565 - INFO - Response - Page 5:
2025-05-20 00:00:17,765 - INFO - 第 5 页获取到 100 条记录
2025-05-20 00:00:17,765 - INFO - Request Parameters - Page 6:
2025-05-20 00:00:17,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:17,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:18,293 - INFO - Response - Page 6:
2025-05-20 00:00:18,494 - INFO - 第 6 页获取到 100 条记录
2025-05-20 00:00:18,494 - INFO - Request Parameters - Page 7:
2025-05-20 00:00:18,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:18,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:18,992 - INFO - Response - Page 7:
2025-05-20 00:00:19,192 - INFO - 第 7 页获取到 61 条记录
2025-05-20 00:00:19,192 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 00:00:19,192 - INFO - 获取到 661 条表单数据
2025-05-20 00:00:19,203 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 00:00:19,215 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 00:00:19,215 - INFO - 开始处理日期: 2025-04
2025-05-20 00:00:19,215 - INFO - Request Parameters - Page 1:
2025-05-20 00:00:19,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:19,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:19,751 - INFO - Response - Page 1:
2025-05-20 00:00:19,951 - INFO - 第 1 页获取到 100 条记录
2025-05-20 00:00:19,951 - INFO - Request Parameters - Page 2:
2025-05-20 00:00:19,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:19,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:20,415 - INFO - Response - Page 2:
2025-05-20 00:00:20,616 - INFO - 第 2 页获取到 100 条记录
2025-05-20 00:00:20,616 - INFO - Request Parameters - Page 3:
2025-05-20 00:00:20,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:20,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:21,131 - INFO - Response - Page 3:
2025-05-20 00:00:21,331 - INFO - 第 3 页获取到 100 条记录
2025-05-20 00:00:21,331 - INFO - Request Parameters - Page 4:
2025-05-20 00:00:21,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:21,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:21,768 - INFO - Response - Page 4:
2025-05-20 00:00:21,968 - INFO - 第 4 页获取到 100 条记录
2025-05-20 00:00:21,968 - INFO - Request Parameters - Page 5:
2025-05-20 00:00:21,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:21,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:22,488 - INFO - Response - Page 5:
2025-05-20 00:00:22,688 - INFO - 第 5 页获取到 100 条记录
2025-05-20 00:00:22,688 - INFO - Request Parameters - Page 6:
2025-05-20 00:00:22,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:22,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:23,157 - INFO - Response - Page 6:
2025-05-20 00:00:23,357 - INFO - 第 6 页获取到 100 条记录
2025-05-20 00:00:23,357 - INFO - Request Parameters - Page 7:
2025-05-20 00:00:23,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:23,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:23,826 - INFO - Response - Page 7:
2025-05-20 00:00:24,027 - INFO - 第 7 页获取到 56 条记录
2025-05-20 00:00:24,027 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 00:00:24,027 - INFO - 获取到 656 条表单数据
2025-05-20 00:00:24,038 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 00:00:24,050 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 00:00:24,050 - INFO - 开始处理日期: 2025-05
2025-05-20 00:00:24,051 - INFO - Request Parameters - Page 1:
2025-05-20 00:00:24,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:24,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:24,544 - INFO - Response - Page 1:
2025-05-20 00:00:24,744 - INFO - 第 1 页获取到 100 条记录
2025-05-20 00:00:24,744 - INFO - Request Parameters - Page 2:
2025-05-20 00:00:24,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:24,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:25,237 - INFO - Response - Page 2:
2025-05-20 00:00:25,437 - INFO - 第 2 页获取到 100 条记录
2025-05-20 00:00:25,437 - INFO - Request Parameters - Page 3:
2025-05-20 00:00:25,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:25,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:25,928 - INFO - Response - Page 3:
2025-05-20 00:00:26,128 - INFO - 第 3 页获取到 100 条记录
2025-05-20 00:00:26,128 - INFO - Request Parameters - Page 4:
2025-05-20 00:00:26,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:26,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:26,606 - INFO - Response - Page 4:
2025-05-20 00:00:26,806 - INFO - 第 4 页获取到 100 条记录
2025-05-20 00:00:26,806 - INFO - Request Parameters - Page 5:
2025-05-20 00:00:26,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:26,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:27,264 - INFO - Response - Page 5:
2025-05-20 00:00:27,465 - INFO - 第 5 页获取到 100 条记录
2025-05-20 00:00:27,465 - INFO - Request Parameters - Page 6:
2025-05-20 00:00:27,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:27,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:27,961 - INFO - Response - Page 6:
2025-05-20 00:00:28,161 - INFO - 第 6 页获取到 100 条记录
2025-05-20 00:00:28,161 - INFO - Request Parameters - Page 7:
2025-05-20 00:00:28,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:00:28,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:00:28,619 - INFO - Response - Page 7:
2025-05-20 00:00:28,820 - INFO - 第 7 页获取到 27 条记录
2025-05-20 00:00:28,820 - INFO - 查询完成，共获取到 627 条记录
2025-05-20 00:00:28,820 - INFO - 获取到 627 条表单数据
2025-05-20 00:00:28,836 - INFO - 当前日期 2025-05 有 627 条MySQL数据需要处理
2025-05-20 00:00:28,837 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-20 00:00:29,260 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-20 00:00:29,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86272.0, 'new_value': 92112.0}, {'field': 'offline_amount', 'old_value': 94291.28, 'new_value': 98575.28}, {'field': 'total_amount', 'old_value': 180563.28, 'new_value': 190687.28}, {'field': 'order_count', 'old_value': 3864, 'new_value': 4070}]
2025-05-20 00:00:29,261 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-20 00:00:29,709 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-20 00:00:29,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1933.6, 'new_value': 2084.6}, {'field': 'total_amount', 'old_value': 26862.4, 'new_value': 27013.4}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-20 00:00:29,710 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-20 00:00:30,119 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-20 00:00:30,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 802705.0, 'new_value': 840163.0}, {'field': 'offline_amount', 'old_value': 253224.0, 'new_value': 263447.0}, {'field': 'total_amount', 'old_value': 1055929.0, 'new_value': 1103610.0}, {'field': 'order_count', 'old_value': 1234, 'new_value': 1288}]
2025-05-20 00:00:30,119 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-20 00:00:30,585 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-20 00:00:30,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194600.0, 'new_value': 202400.0}, {'field': 'total_amount', 'old_value': 194600.0, 'new_value': 202400.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-20 00:00:30,585 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-20 00:00:31,021 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-20 00:00:31,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253820.89, 'new_value': 263620.89}, {'field': 'total_amount', 'old_value': 253820.89, 'new_value': 263620.89}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-20 00:00:31,022 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-20 00:00:31,414 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-20 00:00:31,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208600.13, 'new_value': 214200.13}, {'field': 'total_amount', 'old_value': 247960.13, 'new_value': 253560.13}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-20 00:00:31,414 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-20 00:00:31,858 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-20 00:00:31,858 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21123.2, 'new_value': 21919.2}, {'field': 'offline_amount', 'old_value': 110823.18, 'new_value': 122874.54}, {'field': 'total_amount', 'old_value': 131946.38, 'new_value': 144793.74}, {'field': 'order_count', 'old_value': 196, 'new_value': 206}]
2025-05-20 00:00:31,858 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-20 00:00:32,332 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-20 00:00:32,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28493.84, 'new_value': 30985.74}, {'field': 'total_amount', 'old_value': 28493.84, 'new_value': 30985.74}, {'field': 'order_count', 'old_value': 2135, 'new_value': 2338}]
2025-05-20 00:00:32,332 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-20 00:00:32,716 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-20 00:00:32,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30830.0, 'new_value': 32210.0}, {'field': 'total_amount', 'old_value': 34406.0, 'new_value': 35786.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 162}]
2025-05-20 00:00:32,717 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-20 00:00:33,168 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-20 00:00:33,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52323.38, 'new_value': 52575.3}, {'field': 'total_amount', 'old_value': 52323.38, 'new_value': 52575.3}, {'field': 'order_count', 'old_value': 91, 'new_value': 92}]
2025-05-20 00:00:33,169 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-20 00:00:33,639 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-20 00:00:33,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7993.0, 'new_value': 8513.0}, {'field': 'total_amount', 'old_value': 7993.0, 'new_value': 8513.0}, {'field': 'order_count', 'old_value': 301, 'new_value': 302}]
2025-05-20 00:00:33,640 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-20 00:00:34,070 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-20 00:00:34,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38410.2, 'new_value': 41144.3}, {'field': 'total_amount', 'old_value': 38422.1, 'new_value': 41156.2}, {'field': 'order_count', 'old_value': 222, 'new_value': 234}]
2025-05-20 00:00:34,070 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-20 00:00:34,496 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-20 00:00:34,496 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38866.53, 'new_value': 40790.61}, {'field': 'offline_amount', 'old_value': 513218.84, 'new_value': 533421.34}, {'field': 'total_amount', 'old_value': 552085.37, 'new_value': 574211.95}, {'field': 'order_count', 'old_value': 2308, 'new_value': 2431}]
2025-05-20 00:00:34,496 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-20 00:00:34,876 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-20 00:00:34,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59414.85, 'new_value': 63271.91}, {'field': 'offline_amount', 'old_value': 32040.51, 'new_value': 33344.51}, {'field': 'total_amount', 'old_value': 91455.36, 'new_value': 96616.42}, {'field': 'order_count', 'old_value': 3111, 'new_value': 3291}]
2025-05-20 00:00:34,876 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-20 00:00:35,334 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-20 00:00:35,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40826.0, 'new_value': 42721.0}, {'field': 'total_amount', 'old_value': 40826.0, 'new_value': 42721.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 51}]
2025-05-20 00:00:35,335 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-20 00:00:35,706 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-20 00:00:35,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101750.0, 'new_value': 103049.0}, {'field': 'offline_amount', 'old_value': 47579.0, 'new_value': 51674.0}, {'field': 'total_amount', 'old_value': 149329.0, 'new_value': 154723.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 59}]
2025-05-20 00:00:35,707 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-20 00:00:36,203 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-20 00:00:36,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101757.64, 'new_value': 107146.62}, {'field': 'total_amount', 'old_value': 105181.08, 'new_value': 110570.06}, {'field': 'order_count', 'old_value': 485, 'new_value': 508}]
2025-05-20 00:00:36,206 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-20 00:00:36,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-20 00:00:36,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1331434.97, 'new_value': 1384210.47}, {'field': 'total_amount', 'old_value': 1331434.97, 'new_value': 1384210.47}, {'field': 'order_count', 'old_value': 10665, 'new_value': 11168}]
2025-05-20 00:00:36,717 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-20 00:00:37,134 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-20 00:00:37,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69146.2, 'new_value': 76390.94}, {'field': 'offline_amount', 'old_value': 50834.07, 'new_value': 56815.73}, {'field': 'total_amount', 'old_value': 119980.27, 'new_value': 133206.67}, {'field': 'order_count', 'old_value': 4968, 'new_value': 5552}]
2025-05-20 00:00:37,135 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-20 00:00:37,580 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-20 00:00:37,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84530.0, 'new_value': 89970.0}, {'field': 'total_amount', 'old_value': 84530.0, 'new_value': 89970.0}, {'field': 'order_count', 'old_value': 4359, 'new_value': 4498}]
2025-05-20 00:00:37,581 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-20 00:00:38,006 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-20 00:00:38,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1350.0, 'new_value': 1400.0}, {'field': 'offline_amount', 'old_value': 17863.05, 'new_value': 18221.74}, {'field': 'total_amount', 'old_value': 19213.05, 'new_value': 19621.74}, {'field': 'order_count', 'old_value': 360, 'new_value': 372}]
2025-05-20 00:00:38,007 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-20 00:00:38,412 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-20 00:00:38,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7573.0, 'new_value': 8711.0}, {'field': 'total_amount', 'old_value': 7573.0, 'new_value': 8711.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 64}]
2025-05-20 00:00:38,413 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-20 00:00:38,858 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-20 00:00:38,858 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62695.87, 'new_value': 66656.15}, {'field': 'offline_amount', 'old_value': 174099.23, 'new_value': 182200.55}, {'field': 'total_amount', 'old_value': 236795.1, 'new_value': 248856.7}, {'field': 'order_count', 'old_value': 10949, 'new_value': 11767}]
2025-05-20 00:00:38,858 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-20 00:00:39,277 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-20 00:00:39,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 544827.11, 'new_value': 564048.82}, {'field': 'total_amount', 'old_value': 544827.11, 'new_value': 564048.82}, {'field': 'order_count', 'old_value': 3724, 'new_value': 3876}]
2025-05-20 00:00:39,278 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-20 00:00:39,756 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-20 00:00:39,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216765.0, 'new_value': 228775.0}, {'field': 'total_amount', 'old_value': 220865.0, 'new_value': 232875.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 161}]
2025-05-20 00:00:39,757 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-20 00:00:40,185 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-20 00:00:40,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24506.0, 'new_value': 26878.0}, {'field': 'total_amount', 'old_value': 24506.0, 'new_value': 26878.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 149}]
2025-05-20 00:00:40,186 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-20 00:00:40,652 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-20 00:00:40,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153314.81, 'new_value': 158030.12}, {'field': 'offline_amount', 'old_value': 290946.92, 'new_value': 300946.92}, {'field': 'total_amount', 'old_value': 444261.73, 'new_value': 458977.04}, {'field': 'order_count', 'old_value': 1072, 'new_value': 1111}]
2025-05-20 00:00:40,653 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-20 00:00:41,076 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-20 00:00:41,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18972.4, 'new_value': 19503.4}, {'field': 'offline_amount', 'old_value': 136662.7, 'new_value': 141085.0}, {'field': 'total_amount', 'old_value': 155635.1, 'new_value': 160588.4}, {'field': 'order_count', 'old_value': 4861, 'new_value': 5069}]
2025-05-20 00:00:41,079 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-20 00:00:41,543 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-20 00:00:41,543 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2825.92, 'new_value': 3179.72}, {'field': 'offline_amount', 'old_value': 25599.01, 'new_value': 29506.81}, {'field': 'total_amount', 'old_value': 28424.93, 'new_value': 32686.53}, {'field': 'order_count', 'old_value': 1118, 'new_value': 1288}]
2025-05-20 00:00:41,543 - INFO - 日期 2025-05 处理完成 - 更新: 29 条，插入: 0 条，错误: 0 条
2025-05-20 00:00:41,543 - INFO - 数据同步完成！更新: 29 条，插入: 0 条，错误: 0 条
2025-05-20 00:00:41,545 - INFO - =================同步完成====================
2025-05-20 03:00:02,018 - INFO - =================使用默认全量同步=============
2025-05-20 03:00:03,433 - INFO - MySQL查询成功，共获取 3296 条记录
2025-05-20 03:00:03,434 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 03:00:03,462 - INFO - 开始处理日期: 2025-01
2025-05-20 03:00:03,465 - INFO - Request Parameters - Page 1:
2025-05-20 03:00:03,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:03,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:04,464 - INFO - Response - Page 1:
2025-05-20 03:00:04,664 - INFO - 第 1 页获取到 100 条记录
2025-05-20 03:00:04,664 - INFO - Request Parameters - Page 2:
2025-05-20 03:00:04,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:04,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:05,569 - INFO - Response - Page 2:
2025-05-20 03:00:05,770 - INFO - 第 2 页获取到 100 条记录
2025-05-20 03:00:05,770 - INFO - Request Parameters - Page 3:
2025-05-20 03:00:05,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:05,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:06,313 - INFO - Response - Page 3:
2025-05-20 03:00:06,514 - INFO - 第 3 页获取到 100 条记录
2025-05-20 03:00:06,514 - INFO - Request Parameters - Page 4:
2025-05-20 03:00:06,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:06,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:07,067 - INFO - Response - Page 4:
2025-05-20 03:00:07,267 - INFO - 第 4 页获取到 100 条记录
2025-05-20 03:00:07,267 - INFO - Request Parameters - Page 5:
2025-05-20 03:00:07,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:07,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:07,729 - INFO - Response - Page 5:
2025-05-20 03:00:07,929 - INFO - 第 5 页获取到 100 条记录
2025-05-20 03:00:07,929 - INFO - Request Parameters - Page 6:
2025-05-20 03:00:07,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:07,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:08,404 - INFO - Response - Page 6:
2025-05-20 03:00:08,605 - INFO - 第 6 页获取到 100 条记录
2025-05-20 03:00:08,605 - INFO - Request Parameters - Page 7:
2025-05-20 03:00:08,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:08,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:09,052 - INFO - Response - Page 7:
2025-05-20 03:00:09,252 - INFO - 第 7 页获取到 82 条记录
2025-05-20 03:00:09,252 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 03:00:09,252 - INFO - 获取到 682 条表单数据
2025-05-20 03:00:09,264 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 03:00:09,276 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 03:00:09,276 - INFO - 开始处理日期: 2025-02
2025-05-20 03:00:09,276 - INFO - Request Parameters - Page 1:
2025-05-20 03:00:09,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:09,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:09,750 - INFO - Response - Page 1:
2025-05-20 03:00:09,950 - INFO - 第 1 页获取到 100 条记录
2025-05-20 03:00:09,950 - INFO - Request Parameters - Page 2:
2025-05-20 03:00:09,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:09,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:10,434 - INFO - Response - Page 2:
2025-05-20 03:00:10,634 - INFO - 第 2 页获取到 100 条记录
2025-05-20 03:00:10,634 - INFO - Request Parameters - Page 3:
2025-05-20 03:00:10,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:10,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:11,181 - INFO - Response - Page 3:
2025-05-20 03:00:11,381 - INFO - 第 3 页获取到 100 条记录
2025-05-20 03:00:11,381 - INFO - Request Parameters - Page 4:
2025-05-20 03:00:11,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:11,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:11,876 - INFO - Response - Page 4:
2025-05-20 03:00:12,076 - INFO - 第 4 页获取到 100 条记录
2025-05-20 03:00:12,076 - INFO - Request Parameters - Page 5:
2025-05-20 03:00:12,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:12,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:12,667 - INFO - Response - Page 5:
2025-05-20 03:00:12,867 - INFO - 第 5 页获取到 100 条记录
2025-05-20 03:00:12,867 - INFO - Request Parameters - Page 6:
2025-05-20 03:00:12,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:12,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:13,358 - INFO - Response - Page 6:
2025-05-20 03:00:13,559 - INFO - 第 6 页获取到 100 条记录
2025-05-20 03:00:13,559 - INFO - Request Parameters - Page 7:
2025-05-20 03:00:13,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:13,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:14,026 - INFO - Response - Page 7:
2025-05-20 03:00:14,226 - INFO - 第 7 页获取到 70 条记录
2025-05-20 03:00:14,226 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 03:00:14,226 - INFO - 获取到 670 条表单数据
2025-05-20 03:00:14,239 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 03:00:14,250 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 03:00:14,251 - INFO - 开始处理日期: 2025-03
2025-05-20 03:00:14,251 - INFO - Request Parameters - Page 1:
2025-05-20 03:00:14,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:14,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:14,821 - INFO - Response - Page 1:
2025-05-20 03:00:15,022 - INFO - 第 1 页获取到 100 条记录
2025-05-20 03:00:15,022 - INFO - Request Parameters - Page 2:
2025-05-20 03:00:15,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:15,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:15,561 - INFO - Response - Page 2:
2025-05-20 03:00:15,761 - INFO - 第 2 页获取到 100 条记录
2025-05-20 03:00:15,761 - INFO - Request Parameters - Page 3:
2025-05-20 03:00:15,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:15,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:16,218 - INFO - Response - Page 3:
2025-05-20 03:00:16,419 - INFO - 第 3 页获取到 100 条记录
2025-05-20 03:00:16,419 - INFO - Request Parameters - Page 4:
2025-05-20 03:00:16,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:16,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:16,909 - INFO - Response - Page 4:
2025-05-20 03:00:17,109 - INFO - 第 4 页获取到 100 条记录
2025-05-20 03:00:17,109 - INFO - Request Parameters - Page 5:
2025-05-20 03:00:17,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:17,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:17,648 - INFO - Response - Page 5:
2025-05-20 03:00:17,849 - INFO - 第 5 页获取到 100 条记录
2025-05-20 03:00:17,849 - INFO - Request Parameters - Page 6:
2025-05-20 03:00:17,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:17,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:18,320 - INFO - Response - Page 6:
2025-05-20 03:00:18,521 - INFO - 第 6 页获取到 100 条记录
2025-05-20 03:00:18,521 - INFO - Request Parameters - Page 7:
2025-05-20 03:00:18,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:18,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:18,948 - INFO - Response - Page 7:
2025-05-20 03:00:19,150 - INFO - 第 7 页获取到 61 条记录
2025-05-20 03:00:19,150 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 03:00:19,150 - INFO - 获取到 661 条表单数据
2025-05-20 03:00:19,163 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 03:00:19,175 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 03:00:19,175 - INFO - 开始处理日期: 2025-04
2025-05-20 03:00:19,175 - INFO - Request Parameters - Page 1:
2025-05-20 03:00:19,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:19,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:19,662 - INFO - Response - Page 1:
2025-05-20 03:00:19,862 - INFO - 第 1 页获取到 100 条记录
2025-05-20 03:00:19,862 - INFO - Request Parameters - Page 2:
2025-05-20 03:00:19,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:19,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:20,347 - INFO - Response - Page 2:
2025-05-20 03:00:20,547 - INFO - 第 2 页获取到 100 条记录
2025-05-20 03:00:20,547 - INFO - Request Parameters - Page 3:
2025-05-20 03:00:20,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:20,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:21,043 - INFO - Response - Page 3:
2025-05-20 03:00:21,243 - INFO - 第 3 页获取到 100 条记录
2025-05-20 03:00:21,243 - INFO - Request Parameters - Page 4:
2025-05-20 03:00:21,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:21,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:21,733 - INFO - Response - Page 4:
2025-05-20 03:00:21,933 - INFO - 第 4 页获取到 100 条记录
2025-05-20 03:00:21,933 - INFO - Request Parameters - Page 5:
2025-05-20 03:00:21,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:21,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:22,411 - INFO - Response - Page 5:
2025-05-20 03:00:22,611 - INFO - 第 5 页获取到 100 条记录
2025-05-20 03:00:22,611 - INFO - Request Parameters - Page 6:
2025-05-20 03:00:22,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:22,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:23,182 - INFO - Response - Page 6:
2025-05-20 03:00:23,383 - INFO - 第 6 页获取到 100 条记录
2025-05-20 03:00:23,383 - INFO - Request Parameters - Page 7:
2025-05-20 03:00:23,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:23,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:24,988 - INFO - Response - Page 7:
2025-05-20 03:00:25,190 - INFO - 第 7 页获取到 56 条记录
2025-05-20 03:00:25,190 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 03:00:25,190 - INFO - 获取到 656 条表单数据
2025-05-20 03:00:25,202 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 03:00:25,214 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 03:00:25,214 - INFO - 开始处理日期: 2025-05
2025-05-20 03:00:25,214 - INFO - Request Parameters - Page 1:
2025-05-20 03:00:25,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:25,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:25,727 - INFO - Response - Page 1:
2025-05-20 03:00:25,928 - INFO - 第 1 页获取到 100 条记录
2025-05-20 03:00:25,928 - INFO - Request Parameters - Page 2:
2025-05-20 03:00:25,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:25,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:26,427 - INFO - Response - Page 2:
2025-05-20 03:00:26,627 - INFO - 第 2 页获取到 100 条记录
2025-05-20 03:00:26,627 - INFO - Request Parameters - Page 3:
2025-05-20 03:00:26,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:26,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:27,145 - INFO - Response - Page 3:
2025-05-20 03:00:27,345 - INFO - 第 3 页获取到 100 条记录
2025-05-20 03:00:27,345 - INFO - Request Parameters - Page 4:
2025-05-20 03:00:27,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:27,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:27,819 - INFO - Response - Page 4:
2025-05-20 03:00:28,020 - INFO - 第 4 页获取到 100 条记录
2025-05-20 03:00:28,020 - INFO - Request Parameters - Page 5:
2025-05-20 03:00:28,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:28,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:28,554 - INFO - Response - Page 5:
2025-05-20 03:00:28,754 - INFO - 第 5 页获取到 100 条记录
2025-05-20 03:00:28,754 - INFO - Request Parameters - Page 6:
2025-05-20 03:00:28,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:28,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:29,270 - INFO - Response - Page 6:
2025-05-20 03:00:29,470 - INFO - 第 6 页获取到 100 条记录
2025-05-20 03:00:29,470 - INFO - Request Parameters - Page 7:
2025-05-20 03:00:29,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:00:29,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:00:29,856 - INFO - Response - Page 7:
2025-05-20 03:00:30,057 - INFO - 第 7 页获取到 27 条记录
2025-05-20 03:00:30,057 - INFO - 查询完成，共获取到 627 条记录
2025-05-20 03:00:30,057 - INFO - 获取到 627 条表单数据
2025-05-20 03:00:30,069 - INFO - 当前日期 2025-05 有 627 条MySQL数据需要处理
2025-05-20 03:00:30,080 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 03:00:30,080 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 03:00:30,082 - INFO - =================同步完成====================
2025-05-20 06:00:01,990 - INFO - =================使用默认全量同步=============
2025-05-20 06:00:03,373 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-20 06:00:03,373 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 06:00:03,401 - INFO - 开始处理日期: 2025-01
2025-05-20 06:00:03,403 - INFO - Request Parameters - Page 1:
2025-05-20 06:00:03,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:03,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:04,807 - INFO - Response - Page 1:
2025-05-20 06:00:05,007 - INFO - 第 1 页获取到 100 条记录
2025-05-20 06:00:05,007 - INFO - Request Parameters - Page 2:
2025-05-20 06:00:05,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:05,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:05,618 - INFO - Response - Page 2:
2025-05-20 06:00:05,818 - INFO - 第 2 页获取到 100 条记录
2025-05-20 06:00:05,818 - INFO - Request Parameters - Page 3:
2025-05-20 06:00:05,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:05,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:06,403 - INFO - Response - Page 3:
2025-05-20 06:00:06,604 - INFO - 第 3 页获取到 100 条记录
2025-05-20 06:00:06,604 - INFO - Request Parameters - Page 4:
2025-05-20 06:00:06,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:06,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:07,196 - INFO - Response - Page 4:
2025-05-20 06:00:07,397 - INFO - 第 4 页获取到 100 条记录
2025-05-20 06:00:07,397 - INFO - Request Parameters - Page 5:
2025-05-20 06:00:07,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:07,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:07,913 - INFO - Response - Page 5:
2025-05-20 06:00:08,113 - INFO - 第 5 页获取到 100 条记录
2025-05-20 06:00:08,113 - INFO - Request Parameters - Page 6:
2025-05-20 06:00:08,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:08,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:08,667 - INFO - Response - Page 6:
2025-05-20 06:00:08,867 - INFO - 第 6 页获取到 100 条记录
2025-05-20 06:00:08,867 - INFO - Request Parameters - Page 7:
2025-05-20 06:00:08,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:08,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:09,406 - INFO - Response - Page 7:
2025-05-20 06:00:09,607 - INFO - 第 7 页获取到 82 条记录
2025-05-20 06:00:09,607 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 06:00:09,607 - INFO - 获取到 682 条表单数据
2025-05-20 06:00:09,618 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 06:00:09,630 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 06:00:09,630 - INFO - 开始处理日期: 2025-02
2025-05-20 06:00:09,631 - INFO - Request Parameters - Page 1:
2025-05-20 06:00:09,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:09,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:10,097 - INFO - Response - Page 1:
2025-05-20 06:00:10,297 - INFO - 第 1 页获取到 100 条记录
2025-05-20 06:00:10,297 - INFO - Request Parameters - Page 2:
2025-05-20 06:00:10,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:10,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:10,908 - INFO - Response - Page 2:
2025-05-20 06:00:11,109 - INFO - 第 2 页获取到 100 条记录
2025-05-20 06:00:11,109 - INFO - Request Parameters - Page 3:
2025-05-20 06:00:11,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:11,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:11,618 - INFO - Response - Page 3:
2025-05-20 06:00:11,818 - INFO - 第 3 页获取到 100 条记录
2025-05-20 06:00:11,818 - INFO - Request Parameters - Page 4:
2025-05-20 06:00:11,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:11,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:12,311 - INFO - Response - Page 4:
2025-05-20 06:00:12,511 - INFO - 第 4 页获取到 100 条记录
2025-05-20 06:00:12,511 - INFO - Request Parameters - Page 5:
2025-05-20 06:00:12,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:12,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:13,056 - INFO - Response - Page 5:
2025-05-20 06:00:13,257 - INFO - 第 5 页获取到 100 条记录
2025-05-20 06:00:13,257 - INFO - Request Parameters - Page 6:
2025-05-20 06:00:13,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:13,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:13,782 - INFO - Response - Page 6:
2025-05-20 06:00:13,983 - INFO - 第 6 页获取到 100 条记录
2025-05-20 06:00:13,983 - INFO - Request Parameters - Page 7:
2025-05-20 06:00:13,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:13,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:14,466 - INFO - Response - Page 7:
2025-05-20 06:00:14,666 - INFO - 第 7 页获取到 70 条记录
2025-05-20 06:00:14,666 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 06:00:14,666 - INFO - 获取到 670 条表单数据
2025-05-20 06:00:14,679 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 06:00:14,691 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 06:00:14,691 - INFO - 开始处理日期: 2025-03
2025-05-20 06:00:14,691 - INFO - Request Parameters - Page 1:
2025-05-20 06:00:14,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:14,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:15,258 - INFO - Response - Page 1:
2025-05-20 06:00:15,458 - INFO - 第 1 页获取到 100 条记录
2025-05-20 06:00:15,458 - INFO - Request Parameters - Page 2:
2025-05-20 06:00:15,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:15,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:15,996 - INFO - Response - Page 2:
2025-05-20 06:00:16,196 - INFO - 第 2 页获取到 100 条记录
2025-05-20 06:00:16,196 - INFO - Request Parameters - Page 3:
2025-05-20 06:00:16,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:16,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:16,705 - INFO - Response - Page 3:
2025-05-20 06:00:16,906 - INFO - 第 3 页获取到 100 条记录
2025-05-20 06:00:16,906 - INFO - Request Parameters - Page 4:
2025-05-20 06:00:16,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:16,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:17,415 - INFO - Response - Page 4:
2025-05-20 06:00:17,616 - INFO - 第 4 页获取到 100 条记录
2025-05-20 06:00:17,616 - INFO - Request Parameters - Page 5:
2025-05-20 06:00:17,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:17,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:18,101 - INFO - Response - Page 5:
2025-05-20 06:00:18,301 - INFO - 第 5 页获取到 100 条记录
2025-05-20 06:00:18,301 - INFO - Request Parameters - Page 6:
2025-05-20 06:00:18,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:18,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:18,781 - INFO - Response - Page 6:
2025-05-20 06:00:18,981 - INFO - 第 6 页获取到 100 条记录
2025-05-20 06:00:18,981 - INFO - Request Parameters - Page 7:
2025-05-20 06:00:18,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:18,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:19,416 - INFO - Response - Page 7:
2025-05-20 06:00:19,616 - INFO - 第 7 页获取到 61 条记录
2025-05-20 06:00:19,616 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 06:00:19,616 - INFO - 获取到 661 条表单数据
2025-05-20 06:00:19,629 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 06:00:19,642 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 06:00:19,642 - INFO - 开始处理日期: 2025-04
2025-05-20 06:00:19,643 - INFO - Request Parameters - Page 1:
2025-05-20 06:00:19,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:19,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:20,141 - INFO - Response - Page 1:
2025-05-20 06:00:20,341 - INFO - 第 1 页获取到 100 条记录
2025-05-20 06:00:20,341 - INFO - Request Parameters - Page 2:
2025-05-20 06:00:20,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:20,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:21,041 - INFO - Response - Page 2:
2025-05-20 06:00:21,241 - INFO - 第 2 页获取到 100 条记录
2025-05-20 06:00:21,241 - INFO - Request Parameters - Page 3:
2025-05-20 06:00:21,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:21,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:21,763 - INFO - Response - Page 3:
2025-05-20 06:00:21,963 - INFO - 第 3 页获取到 100 条记录
2025-05-20 06:00:21,963 - INFO - Request Parameters - Page 4:
2025-05-20 06:00:21,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:21,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:22,468 - INFO - Response - Page 4:
2025-05-20 06:00:22,669 - INFO - 第 4 页获取到 100 条记录
2025-05-20 06:00:22,669 - INFO - Request Parameters - Page 5:
2025-05-20 06:00:22,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:22,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:23,284 - INFO - Response - Page 5:
2025-05-20 06:00:23,484 - INFO - 第 5 页获取到 100 条记录
2025-05-20 06:00:23,484 - INFO - Request Parameters - Page 6:
2025-05-20 06:00:23,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:23,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:23,968 - INFO - Response - Page 6:
2025-05-20 06:00:24,168 - INFO - 第 6 页获取到 100 条记录
2025-05-20 06:00:24,168 - INFO - Request Parameters - Page 7:
2025-05-20 06:00:24,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:24,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:24,650 - INFO - Response - Page 7:
2025-05-20 06:00:24,851 - INFO - 第 7 页获取到 56 条记录
2025-05-20 06:00:24,851 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 06:00:24,852 - INFO - 获取到 656 条表单数据
2025-05-20 06:00:24,865 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 06:00:24,876 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 06:00:24,876 - INFO - 开始处理日期: 2025-05
2025-05-20 06:00:24,876 - INFO - Request Parameters - Page 1:
2025-05-20 06:00:24,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:24,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:25,421 - INFO - Response - Page 1:
2025-05-20 06:00:25,621 - INFO - 第 1 页获取到 100 条记录
2025-05-20 06:00:25,621 - INFO - Request Parameters - Page 2:
2025-05-20 06:00:25,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:25,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:26,083 - INFO - Response - Page 2:
2025-05-20 06:00:26,283 - INFO - 第 2 页获取到 100 条记录
2025-05-20 06:00:26,283 - INFO - Request Parameters - Page 3:
2025-05-20 06:00:26,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:26,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:26,859 - INFO - Response - Page 3:
2025-05-20 06:00:27,060 - INFO - 第 3 页获取到 100 条记录
2025-05-20 06:00:27,060 - INFO - Request Parameters - Page 4:
2025-05-20 06:00:27,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:27,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:27,545 - INFO - Response - Page 4:
2025-05-20 06:00:27,745 - INFO - 第 4 页获取到 100 条记录
2025-05-20 06:00:27,745 - INFO - Request Parameters - Page 5:
2025-05-20 06:00:27,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:27,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:28,279 - INFO - Response - Page 5:
2025-05-20 06:00:28,479 - INFO - 第 5 页获取到 100 条记录
2025-05-20 06:00:28,479 - INFO - Request Parameters - Page 6:
2025-05-20 06:00:28,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:28,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:29,006 - INFO - Response - Page 6:
2025-05-20 06:00:29,207 - INFO - 第 6 页获取到 100 条记录
2025-05-20 06:00:29,207 - INFO - Request Parameters - Page 7:
2025-05-20 06:00:29,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:00:29,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:00:29,601 - INFO - Response - Page 7:
2025-05-20 06:00:29,801 - INFO - 第 7 页获取到 27 条记录
2025-05-20 06:00:29,801 - INFO - 查询完成，共获取到 627 条记录
2025-05-20 06:00:29,801 - INFO - 获取到 627 条表单数据
2025-05-20 06:00:29,813 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-20 06:00:29,824 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-20 06:00:30,307 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-20 06:00:30,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60528.0, 'new_value': 63263.0}, {'field': 'total_amount', 'old_value': 62378.0, 'new_value': 65113.0}, {'field': 'order_count', 'old_value': 360, 'new_value': 375}]
2025-05-20 06:00:30,309 - INFO - 开始批量插入 1 条新记录
2025-05-20 06:00:30,453 - INFO - 批量插入响应状态码: 200
2025-05-20 06:00:30,454 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 19 May 2025 22:00:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '470E5106-A035-76CC-A4FB-3B8D98B71F4C', 'x-acs-trace-id': '31fe108d2cb426c522dfc0829f73c981', 'etag': '65+7PUwboKbBHk/9ULtMB8w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 06:00:30,454 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5']}
2025-05-20 06:00:30,454 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-20 06:00:30,454 - INFO - 成功插入的数据ID: ['FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5']
2025-05-20 06:00:33,455 - INFO - 批量插入完成，共 1 条记录
2025-05-20 06:00:33,455 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 1 条，错误: 0 条
2025-05-20 06:00:33,455 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 0 条
2025-05-20 06:00:33,456 - INFO - =================同步完成====================
2025-05-20 09:00:02,048 - INFO - =================使用默认全量同步=============
2025-05-20 09:00:03,460 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-20 09:00:03,461 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 09:00:03,490 - INFO - 开始处理日期: 2025-01
2025-05-20 09:00:03,493 - INFO - Request Parameters - Page 1:
2025-05-20 09:00:03,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:03,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:04,945 - INFO - Response - Page 1:
2025-05-20 09:00:05,146 - INFO - 第 1 页获取到 100 条记录
2025-05-20 09:00:05,146 - INFO - Request Parameters - Page 2:
2025-05-20 09:00:05,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:05,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:05,892 - INFO - Response - Page 2:
2025-05-20 09:00:06,092 - INFO - 第 2 页获取到 100 条记录
2025-05-20 09:00:06,092 - INFO - Request Parameters - Page 3:
2025-05-20 09:00:06,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:06,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:06,724 - INFO - Response - Page 3:
2025-05-20 09:00:06,924 - INFO - 第 3 页获取到 100 条记录
2025-05-20 09:00:06,924 - INFO - Request Parameters - Page 4:
2025-05-20 09:00:06,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:06,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:07,455 - INFO - Response - Page 4:
2025-05-20 09:00:07,656 - INFO - 第 4 页获取到 100 条记录
2025-05-20 09:00:07,656 - INFO - Request Parameters - Page 5:
2025-05-20 09:00:07,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:07,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:08,250 - INFO - Response - Page 5:
2025-05-20 09:00:08,451 - INFO - 第 5 页获取到 100 条记录
2025-05-20 09:00:08,451 - INFO - Request Parameters - Page 6:
2025-05-20 09:00:08,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:08,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:08,936 - INFO - Response - Page 6:
2025-05-20 09:00:09,136 - INFO - 第 6 页获取到 100 条记录
2025-05-20 09:00:09,136 - INFO - Request Parameters - Page 7:
2025-05-20 09:00:09,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:09,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:09,771 - INFO - Response - Page 7:
2025-05-20 09:00:09,972 - INFO - 第 7 页获取到 82 条记录
2025-05-20 09:00:09,972 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 09:00:09,972 - INFO - 获取到 682 条表单数据
2025-05-20 09:00:09,985 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 09:00:09,996 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 09:00:09,997 - INFO - 开始处理日期: 2025-02
2025-05-20 09:00:09,997 - INFO - Request Parameters - Page 1:
2025-05-20 09:00:09,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:09,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:10,536 - INFO - Response - Page 1:
2025-05-20 09:00:10,737 - INFO - 第 1 页获取到 100 条记录
2025-05-20 09:00:10,737 - INFO - Request Parameters - Page 2:
2025-05-20 09:00:10,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:10,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:11,356 - INFO - Response - Page 2:
2025-05-20 09:00:11,556 - INFO - 第 2 页获取到 100 条记录
2025-05-20 09:00:11,556 - INFO - Request Parameters - Page 3:
2025-05-20 09:00:11,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:11,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:12,271 - INFO - Response - Page 3:
2025-05-20 09:00:12,471 - INFO - 第 3 页获取到 100 条记录
2025-05-20 09:00:12,471 - INFO - Request Parameters - Page 4:
2025-05-20 09:00:12,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:12,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:12,985 - INFO - Response - Page 4:
2025-05-20 09:00:13,185 - INFO - 第 4 页获取到 100 条记录
2025-05-20 09:00:13,185 - INFO - Request Parameters - Page 5:
2025-05-20 09:00:13,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:13,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:13,700 - INFO - Response - Page 5:
2025-05-20 09:00:13,900 - INFO - 第 5 页获取到 100 条记录
2025-05-20 09:00:13,900 - INFO - Request Parameters - Page 6:
2025-05-20 09:00:13,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:13,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:14,479 - INFO - Response - Page 6:
2025-05-20 09:00:14,679 - INFO - 第 6 页获取到 100 条记录
2025-05-20 09:00:14,679 - INFO - Request Parameters - Page 7:
2025-05-20 09:00:14,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:14,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:15,144 - INFO - Response - Page 7:
2025-05-20 09:00:15,345 - INFO - 第 7 页获取到 70 条记录
2025-05-20 09:00:15,345 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 09:00:15,346 - INFO - 获取到 670 条表单数据
2025-05-20 09:00:15,357 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 09:00:15,368 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 09:00:15,368 - INFO - 开始处理日期: 2025-03
2025-05-20 09:00:15,368 - INFO - Request Parameters - Page 1:
2025-05-20 09:00:15,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:15,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:15,901 - INFO - Response - Page 1:
2025-05-20 09:00:16,101 - INFO - 第 1 页获取到 100 条记录
2025-05-20 09:00:16,101 - INFO - Request Parameters - Page 2:
2025-05-20 09:00:16,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:16,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:16,603 - INFO - Response - Page 2:
2025-05-20 09:00:16,803 - INFO - 第 2 页获取到 100 条记录
2025-05-20 09:00:16,803 - INFO - Request Parameters - Page 3:
2025-05-20 09:00:16,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:16,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:17,747 - INFO - Response - Page 3:
2025-05-20 09:00:17,948 - INFO - 第 3 页获取到 100 条记录
2025-05-20 09:00:17,948 - INFO - Request Parameters - Page 4:
2025-05-20 09:00:17,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:17,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:18,528 - INFO - Response - Page 4:
2025-05-20 09:00:18,728 - INFO - 第 4 页获取到 100 条记录
2025-05-20 09:00:18,728 - INFO - Request Parameters - Page 5:
2025-05-20 09:00:18,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:18,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:19,677 - INFO - Response - Page 5:
2025-05-20 09:00:19,877 - INFO - 第 5 页获取到 100 条记录
2025-05-20 09:00:19,877 - INFO - Request Parameters - Page 6:
2025-05-20 09:00:19,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:19,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:20,572 - INFO - Response - Page 6:
2025-05-20 09:00:20,772 - INFO - 第 6 页获取到 100 条记录
2025-05-20 09:00:20,772 - INFO - Request Parameters - Page 7:
2025-05-20 09:00:20,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:20,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:21,300 - INFO - Response - Page 7:
2025-05-20 09:00:21,501 - INFO - 第 7 页获取到 61 条记录
2025-05-20 09:00:21,501 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 09:00:21,501 - INFO - 获取到 661 条表单数据
2025-05-20 09:00:21,514 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 09:00:21,526 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 09:00:21,526 - INFO - 开始处理日期: 2025-04
2025-05-20 09:00:21,526 - INFO - Request Parameters - Page 1:
2025-05-20 09:00:21,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:21,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:22,133 - INFO - Response - Page 1:
2025-05-20 09:00:22,334 - INFO - 第 1 页获取到 100 条记录
2025-05-20 09:00:22,334 - INFO - Request Parameters - Page 2:
2025-05-20 09:00:22,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:22,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:22,765 - INFO - Response - Page 2:
2025-05-20 09:00:22,966 - INFO - 第 2 页获取到 100 条记录
2025-05-20 09:00:22,966 - INFO - Request Parameters - Page 3:
2025-05-20 09:00:22,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:22,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:23,450 - INFO - Response - Page 3:
2025-05-20 09:00:23,651 - INFO - 第 3 页获取到 100 条记录
2025-05-20 09:00:23,651 - INFO - Request Parameters - Page 4:
2025-05-20 09:00:23,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:23,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:24,132 - INFO - Response - Page 4:
2025-05-20 09:00:24,333 - INFO - 第 4 页获取到 100 条记录
2025-05-20 09:00:24,333 - INFO - Request Parameters - Page 5:
2025-05-20 09:00:24,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:24,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:24,777 - INFO - Response - Page 5:
2025-05-20 09:00:24,977 - INFO - 第 5 页获取到 100 条记录
2025-05-20 09:00:24,977 - INFO - Request Parameters - Page 6:
2025-05-20 09:00:24,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:24,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:25,618 - INFO - Response - Page 6:
2025-05-20 09:00:25,819 - INFO - 第 6 页获取到 100 条记录
2025-05-20 09:00:25,819 - INFO - Request Parameters - Page 7:
2025-05-20 09:00:25,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:25,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:26,273 - INFO - Response - Page 7:
2025-05-20 09:00:26,473 - INFO - 第 7 页获取到 56 条记录
2025-05-20 09:00:26,473 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 09:00:26,473 - INFO - 获取到 656 条表单数据
2025-05-20 09:00:26,485 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 09:00:26,496 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 09:00:26,496 - INFO - 开始处理日期: 2025-05
2025-05-20 09:00:26,496 - INFO - Request Parameters - Page 1:
2025-05-20 09:00:26,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:26,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:27,255 - INFO - Response - Page 1:
2025-05-20 09:00:27,455 - INFO - 第 1 页获取到 100 条记录
2025-05-20 09:00:27,455 - INFO - Request Parameters - Page 2:
2025-05-20 09:00:27,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:27,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:28,323 - INFO - Response - Page 2:
2025-05-20 09:00:28,524 - INFO - 第 2 页获取到 100 条记录
2025-05-20 09:00:28,524 - INFO - Request Parameters - Page 3:
2025-05-20 09:00:28,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:28,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:28,966 - INFO - Response - Page 3:
2025-05-20 09:00:29,166 - INFO - 第 3 页获取到 100 条记录
2025-05-20 09:00:29,166 - INFO - Request Parameters - Page 4:
2025-05-20 09:00:29,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:29,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:29,876 - INFO - Response - Page 4:
2025-05-20 09:00:30,076 - INFO - 第 4 页获取到 100 条记录
2025-05-20 09:00:30,076 - INFO - Request Parameters - Page 5:
2025-05-20 09:00:30,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:30,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:30,567 - INFO - Response - Page 5:
2025-05-20 09:00:30,767 - INFO - 第 5 页获取到 100 条记录
2025-05-20 09:00:30,767 - INFO - Request Parameters - Page 6:
2025-05-20 09:00:30,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:30,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:31,252 - INFO - Response - Page 6:
2025-05-20 09:00:31,452 - INFO - 第 6 页获取到 100 条记录
2025-05-20 09:00:31,452 - INFO - Request Parameters - Page 7:
2025-05-20 09:00:31,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:00:31,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:00:31,849 - INFO - Response - Page 7:
2025-05-20 09:00:32,050 - INFO - 第 7 页获取到 28 条记录
2025-05-20 09:00:32,050 - INFO - 查询完成，共获取到 628 条记录
2025-05-20 09:00:32,050 - INFO - 获取到 628 条表单数据
2025-05-20 09:00:32,062 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-20 09:00:32,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-20 09:00:32,562 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-20 09:00:32,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1720.0, 'new_value': 1745.0}, {'field': 'offline_amount', 'old_value': 31000.0, 'new_value': 31960.0}, {'field': 'total_amount', 'old_value': 32720.0, 'new_value': 33705.0}, {'field': 'order_count', 'old_value': 437, 'new_value': 452}]
2025-05-20 09:00:32,562 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-20 09:00:33,041 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-20 09:00:33,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270740.0, 'new_value': 281565.0}, {'field': 'total_amount', 'old_value': 270740.0, 'new_value': 281565.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 212}]
2025-05-20 09:00:33,042 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-20 09:00:33,483 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-20 09:00:33,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246310.0, 'new_value': 266390.0}, {'field': 'total_amount', 'old_value': 246310.0, 'new_value': 266390.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 153}]
2025-05-20 09:00:33,483 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-20 09:00:33,921 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-20 09:00:33,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70986.0, 'new_value': 72036.0}, {'field': 'total_amount', 'old_value': 70986.0, 'new_value': 72036.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 85}]
2025-05-20 09:00:33,922 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-20 09:00:34,413 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-20 09:00:34,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124769.0, 'new_value': 145066.0}, {'field': 'total_amount', 'old_value': 124769.0, 'new_value': 145066.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 41}]
2025-05-20 09:00:34,414 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-20 09:00:34,882 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-20 09:00:34,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41706.79, 'new_value': 44575.22}, {'field': 'offline_amount', 'old_value': 81895.45, 'new_value': 86456.45}, {'field': 'total_amount', 'old_value': 123602.24, 'new_value': 131031.67}, {'field': 'order_count', 'old_value': 1448, 'new_value': 1531}]
2025-05-20 09:00:34,882 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-20 09:00:35,376 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-20 09:00:35,376 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16203.16, 'new_value': 16846.76}, {'field': 'offline_amount', 'old_value': 19430.05, 'new_value': 20314.2}, {'field': 'total_amount', 'old_value': 35633.21, 'new_value': 37160.96}, {'field': 'order_count', 'old_value': 1734, 'new_value': 1806}]
2025-05-20 09:00:35,376 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-20 09:00:35,953 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-20 09:00:35,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248964.6, 'new_value': 257649.7}, {'field': 'total_amount', 'old_value': 363984.3, 'new_value': 372669.4}, {'field': 'order_count', 'old_value': 2632, 'new_value': 2732}]
2025-05-20 09:00:35,954 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-20 09:00:36,359 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-20 09:00:36,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74408.5, 'new_value': 79050.0}, {'field': 'total_amount', 'old_value': 74408.5, 'new_value': 79050.0}, {'field': 'order_count', 'old_value': 4024, 'new_value': 4281}]
2025-05-20 09:00:36,359 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-20 09:00:36,808 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-20 09:00:36,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106831.67, 'new_value': 111493.35}, {'field': 'total_amount', 'old_value': 106831.67, 'new_value': 111493.35}, {'field': 'order_count', 'old_value': 1210, 'new_value': 1269}]
2025-05-20 09:00:36,809 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-20 09:00:37,392 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-20 09:00:37,392 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5610.0, 'new_value': 5801.0}, {'field': 'total_amount', 'old_value': 22014.0, 'new_value': 22205.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-05-20 09:00:37,392 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-20 09:00:37,884 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-20 09:00:37,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142558.0, 'new_value': 146674.0}, {'field': 'total_amount', 'old_value': 142558.0, 'new_value': 146674.0}, {'field': 'order_count', 'old_value': 261, 'new_value': 276}]
2025-05-20 09:00:37,885 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-20 09:00:38,344 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-20 09:00:38,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56875.0, 'new_value': 59811.0}, {'field': 'total_amount', 'old_value': 56875.0, 'new_value': 59811.0}, {'field': 'order_count', 'old_value': 490, 'new_value': 517}]
2025-05-20 09:00:38,344 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-20 09:00:38,778 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-20 09:00:38,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138629.52, 'new_value': 142455.15}, {'field': 'offline_amount', 'old_value': 23150.18, 'new_value': 24174.16}, {'field': 'total_amount', 'old_value': 161779.7, 'new_value': 166629.31}, {'field': 'order_count', 'old_value': 586, 'new_value': 605}]
2025-05-20 09:00:38,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-20 09:00:39,270 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-20 09:00:39,270 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132439.0, 'new_value': 135653.0}, {'field': 'offline_amount', 'old_value': 49141.42, 'new_value': 50031.42}, {'field': 'total_amount', 'old_value': 181580.42, 'new_value': 185684.42}, {'field': 'order_count', 'old_value': 1122, 'new_value': 1145}]
2025-05-20 09:00:39,271 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-20 09:00:39,739 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-20 09:00:39,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64332.47, 'new_value': 67850.69}, {'field': 'total_amount', 'old_value': 64332.47, 'new_value': 67850.69}, {'field': 'order_count', 'old_value': 1712, 'new_value': 1799}]
2025-05-20 09:00:39,740 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-20 09:00:40,234 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-20 09:00:40,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163766.1, 'new_value': 167724.1}, {'field': 'total_amount', 'old_value': 163766.1, 'new_value': 167724.1}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-05-20 09:00:40,234 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-20 09:00:40,707 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-20 09:00:40,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121825.0, 'new_value': 125607.0}, {'field': 'offline_amount', 'old_value': 52208.82, 'new_value': 54787.72}, {'field': 'total_amount', 'old_value': 174033.82, 'new_value': 180394.72}, {'field': 'order_count', 'old_value': 1214, 'new_value': 1267}]
2025-05-20 09:00:40,708 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-20 09:00:41,219 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-20 09:00:41,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8231.49, 'new_value': 8505.43}, {'field': 'offline_amount', 'old_value': 130245.1, 'new_value': 137021.91}, {'field': 'total_amount', 'old_value': 138476.59, 'new_value': 145527.34}, {'field': 'order_count', 'old_value': 1501, 'new_value': 1576}]
2025-05-20 09:00:41,219 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-20 09:00:41,839 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-20 09:00:41,839 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1350.0, 'new_value': 1450.0}, {'field': 'offline_amount', 'old_value': 23331.0, 'new_value': 24353.0}, {'field': 'total_amount', 'old_value': 24681.0, 'new_value': 25803.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 91}]
2025-05-20 09:00:41,839 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-20 09:00:42,256 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-20 09:00:42,256 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6487.87, 'new_value': 6820.18}, {'field': 'offline_amount', 'old_value': 78572.02, 'new_value': 80439.42}, {'field': 'total_amount', 'old_value': 85059.89, 'new_value': 87259.6}, {'field': 'order_count', 'old_value': 2004, 'new_value': 2064}]
2025-05-20 09:00:42,256 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-20 09:00:42,750 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-20 09:00:42,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104556.0, 'new_value': 110502.0}, {'field': 'total_amount', 'old_value': 104556.0, 'new_value': 110502.0}, {'field': 'order_count', 'old_value': 2589, 'new_value': 2741}]
2025-05-20 09:00:42,751 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-20 09:00:43,194 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-20 09:00:43,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16877.83, 'new_value': 17437.79}, {'field': 'total_amount', 'old_value': 16877.83, 'new_value': 17437.79}, {'field': 'order_count', 'old_value': 83, 'new_value': 93}]
2025-05-20 09:00:43,194 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-20 09:00:43,635 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-20 09:00:43,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119695.0, 'new_value': 128110.0}, {'field': 'total_amount', 'old_value': 119695.0, 'new_value': 128110.0}, {'field': 'order_count', 'old_value': 4432, 'new_value': 4748}]
2025-05-20 09:00:43,635 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-20 09:00:44,123 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-20 09:00:44,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352968.0, 'new_value': 354780.0}, {'field': 'total_amount', 'old_value': 363887.0, 'new_value': 365699.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-05-20 09:00:44,124 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-20 09:00:44,590 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-20 09:00:44,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98576.48, 'new_value': 104390.48}, {'field': 'total_amount', 'old_value': 98576.48, 'new_value': 104390.48}, {'field': 'order_count', 'old_value': 3635, 'new_value': 3849}]
2025-05-20 09:00:44,590 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-20 09:00:45,021 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-20 09:00:45,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15291.2, 'new_value': 16554.14}, {'field': 'total_amount', 'old_value': 15291.2, 'new_value': 16554.14}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-20 09:00:45,021 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-20 09:00:45,455 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-20 09:00:45,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49440.0, 'new_value': 49716.0}, {'field': 'total_amount', 'old_value': 49440.0, 'new_value': 49716.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 92}]
2025-05-20 09:00:45,456 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-20 09:00:45,923 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-20 09:00:45,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54241.43, 'new_value': 55963.85}, {'field': 'offline_amount', 'old_value': 343244.3, 'new_value': 380280.3}, {'field': 'total_amount', 'old_value': 397485.73, 'new_value': 436244.15}, {'field': 'order_count', 'old_value': 607, 'new_value': 637}]
2025-05-20 09:00:45,923 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-20 09:00:46,475 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-20 09:00:46,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140930.05, 'new_value': 149637.07}, {'field': 'total_amount', 'old_value': 145965.08, 'new_value': 154672.1}, {'field': 'order_count', 'old_value': 963, 'new_value': 1019}]
2025-05-20 09:00:46,475 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-20 09:00:46,857 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-20 09:00:46,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130390.0, 'new_value': 132989.0}, {'field': 'total_amount', 'old_value': 130390.0, 'new_value': 132989.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-20 09:00:46,858 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-20 09:00:47,230 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-20 09:00:47,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41408.0, 'new_value': 45255.0}, {'field': 'total_amount', 'old_value': 70054.0, 'new_value': 73901.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-20 09:00:47,230 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-20 09:00:47,689 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-20 09:00:47,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4708.05, 'new_value': 4936.84}, {'field': 'offline_amount', 'old_value': 11938.41, 'new_value': 12533.01}, {'field': 'total_amount', 'old_value': 16646.46, 'new_value': 17469.85}, {'field': 'order_count', 'old_value': 583, 'new_value': 617}]
2025-05-20 09:00:47,689 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-20 09:00:48,209 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-20 09:00:48,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131011.56, 'new_value': 137552.51}, {'field': 'offline_amount', 'old_value': 103094.04, 'new_value': 108811.23}, {'field': 'total_amount', 'old_value': 234105.6, 'new_value': 246363.74}, {'field': 'order_count', 'old_value': 2049, 'new_value': 2179}]
2025-05-20 09:00:48,210 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-20 09:00:48,653 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-20 09:00:48,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 264703.0, 'new_value': 275394.2}, {'field': 'offline_amount', 'old_value': 62645.8, 'new_value': 68279.8}, {'field': 'total_amount', 'old_value': 327348.8, 'new_value': 343674.0}, {'field': 'order_count', 'old_value': 412, 'new_value': 431}]
2025-05-20 09:00:48,653 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-20 09:00:49,099 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-20 09:00:49,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21716.0, 'new_value': 21984.0}, {'field': 'total_amount', 'old_value': 21716.0, 'new_value': 21984.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-05-20 09:00:49,100 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-20 09:00:49,584 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-20 09:00:49,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55130.0, 'new_value': 57462.0}, {'field': 'total_amount', 'old_value': 55130.0, 'new_value': 57462.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 44}]
2025-05-20 09:00:49,585 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-20 09:00:50,121 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-20 09:00:50,122 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55969.6, 'new_value': 58969.6}, {'field': 'offline_amount', 'old_value': 3527.05, 'new_value': 3692.85}, {'field': 'total_amount', 'old_value': 59496.65, 'new_value': 62662.45}, {'field': 'order_count', 'old_value': 179, 'new_value': 192}]
2025-05-20 09:00:50,122 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-20 09:00:50,589 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-20 09:00:50,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54757.57, 'new_value': 56402.42}, {'field': 'total_amount', 'old_value': 54757.57, 'new_value': 56402.42}, {'field': 'order_count', 'old_value': 1515, 'new_value': 1566}]
2025-05-20 09:00:50,589 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-20 09:00:51,081 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-20 09:00:51,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329512.72, 'new_value': 342922.56}, {'field': 'total_amount', 'old_value': 329512.72, 'new_value': 342922.56}, {'field': 'order_count', 'old_value': 433, 'new_value': 453}]
2025-05-20 09:00:51,081 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-20 09:00:51,558 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-20 09:00:51,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11600.21, 'new_value': 12026.47}, {'field': 'offline_amount', 'old_value': 313078.54, 'new_value': 323176.66}, {'field': 'total_amount', 'old_value': 324678.75, 'new_value': 335203.13}, {'field': 'order_count', 'old_value': 1325, 'new_value': 1378}]
2025-05-20 09:00:51,559 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-20 09:00:52,034 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-20 09:00:52,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9108.0, 'new_value': 9236.0}, {'field': 'total_amount', 'old_value': 9108.0, 'new_value': 9236.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-20 09:00:52,034 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-20 09:00:52,518 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-20 09:00:52,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40008.0, 'new_value': 40862.0}, {'field': 'total_amount', 'old_value': 40008.0, 'new_value': 40862.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-20 09:00:52,518 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-20 09:00:52,973 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-20 09:00:52,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64312.35, 'new_value': 67367.35}, {'field': 'offline_amount', 'old_value': 830341.5, 'new_value': 873935.74}, {'field': 'total_amount', 'old_value': 894653.85, 'new_value': 941303.09}, {'field': 'order_count', 'old_value': 7161, 'new_value': 7581}]
2025-05-20 09:00:52,974 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-20 09:00:53,422 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-20 09:00:53,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65262.75, 'new_value': 66534.75}, {'field': 'total_amount', 'old_value': 65262.75, 'new_value': 66534.75}, {'field': 'order_count', 'old_value': 364, 'new_value': 371}]
2025-05-20 09:00:53,423 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-20 09:00:53,868 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-20 09:00:53,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6229.61, 'new_value': 6517.27}, {'field': 'offline_amount', 'old_value': 23425.0, 'new_value': 23842.0}, {'field': 'total_amount', 'old_value': 29654.61, 'new_value': 30359.27}, {'field': 'order_count', 'old_value': 158, 'new_value': 166}]
2025-05-20 09:00:53,869 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-20 09:00:54,275 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-20 09:00:54,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36727.38, 'new_value': 38729.27}, {'field': 'total_amount', 'old_value': 36924.18, 'new_value': 38926.07}, {'field': 'order_count', 'old_value': 312, 'new_value': 329}]
2025-05-20 09:00:54,275 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-20 09:00:54,780 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-20 09:00:54,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4119.0, 'new_value': 4319.0}, {'field': 'offline_amount', 'old_value': 18921.6, 'new_value': 19777.2}, {'field': 'total_amount', 'old_value': 23040.6, 'new_value': 24096.2}, {'field': 'order_count', 'old_value': 914, 'new_value': 954}]
2025-05-20 09:00:54,780 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-20 09:00:55,220 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-20 09:00:55,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74808.72, 'new_value': 76299.72}, {'field': 'total_amount', 'old_value': 74808.72, 'new_value': 76299.72}, {'field': 'order_count', 'old_value': 258, 'new_value': 268}]
2025-05-20 09:00:55,220 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-20 09:00:55,663 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-20 09:00:55,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21395.8, 'new_value': 21729.0}, {'field': 'offline_amount', 'old_value': 16601.6, 'new_value': 17605.6}, {'field': 'total_amount', 'old_value': 37997.4, 'new_value': 39334.6}, {'field': 'order_count', 'old_value': 201, 'new_value': 208}]
2025-05-20 09:00:55,664 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-20 09:00:56,105 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-20 09:00:56,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151636.4, 'new_value': 159071.6}, {'field': 'total_amount', 'old_value': 151636.4, 'new_value': 159071.6}, {'field': 'order_count', 'old_value': 556, 'new_value': 587}]
2025-05-20 09:00:56,105 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-20 09:00:56,520 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-20 09:00:56,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11690.05, 'new_value': 12608.26}, {'field': 'offline_amount', 'old_value': 199046.54, 'new_value': 207667.84}, {'field': 'total_amount', 'old_value': 210736.59, 'new_value': 220276.1}, {'field': 'order_count', 'old_value': 11419, 'new_value': 12021}]
2025-05-20 09:00:56,521 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-20 09:00:56,985 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-20 09:00:56,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38696.4, 'new_value': 40626.34}, {'field': 'offline_amount', 'old_value': 23045.0, 'new_value': 24646.0}, {'field': 'total_amount', 'old_value': 61741.4, 'new_value': 65272.34}, {'field': 'order_count', 'old_value': 773, 'new_value': 811}]
2025-05-20 09:00:56,985 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-20 09:00:57,453 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-20 09:00:57,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111060.4, 'new_value': 113874.57}, {'field': 'total_amount', 'old_value': 111060.4, 'new_value': 113874.57}, {'field': 'order_count', 'old_value': 544, 'new_value': 562}]
2025-05-20 09:00:57,454 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-20 09:00:57,892 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-20 09:00:57,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16784.64, 'new_value': 17787.1}, {'field': 'offline_amount', 'old_value': 29684.02, 'new_value': 31565.02}, {'field': 'total_amount', 'old_value': 46468.66, 'new_value': 49352.12}, {'field': 'order_count', 'old_value': 1666, 'new_value': 1773}]
2025-05-20 09:00:57,893 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-20 09:00:58,422 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-20 09:00:58,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49066.0, 'new_value': 53784.0}, {'field': 'total_amount', 'old_value': 51474.0, 'new_value': 56192.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 234}]
2025-05-20 09:00:58,422 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-20 09:00:58,882 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-20 09:00:58,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40912.0, 'new_value': 42756.0}, {'field': 'total_amount', 'old_value': 40912.0, 'new_value': 42756.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-20 09:00:58,882 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-20 09:00:59,299 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-20 09:00:59,299 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18048.7, 'new_value': 18283.7}, {'field': 'offline_amount', 'old_value': 44425.46, 'new_value': 46609.46}, {'field': 'total_amount', 'old_value': 62474.16, 'new_value': 64893.16}, {'field': 'order_count', 'old_value': 709, 'new_value': 734}]
2025-05-20 09:00:59,299 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-20 09:00:59,747 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-20 09:00:59,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73943.5, 'new_value': 76575.3}, {'field': 'offline_amount', 'old_value': 100922.79, 'new_value': 105275.79}, {'field': 'total_amount', 'old_value': 174866.29, 'new_value': 181851.09}, {'field': 'order_count', 'old_value': 1169, 'new_value': 1240}]
2025-05-20 09:00:59,748 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-20 09:01:00,190 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-20 09:01:00,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40480.0, 'new_value': 40759.0}, {'field': 'total_amount', 'old_value': 40829.0, 'new_value': 41108.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 72}]
2025-05-20 09:01:00,190 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-20 09:01:00,643 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-20 09:01:00,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90994.89, 'new_value': 95365.58}, {'field': 'total_amount', 'old_value': 90994.89, 'new_value': 95365.58}, {'field': 'order_count', 'old_value': 2649, 'new_value': 2766}]
2025-05-20 09:01:00,644 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-20 09:01:01,077 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-20 09:01:01,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200497.28, 'new_value': 212297.63}, {'field': 'total_amount', 'old_value': 292317.44, 'new_value': 304117.79}, {'field': 'order_count', 'old_value': 3455, 'new_value': 3619}]
2025-05-20 09:01:01,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-20 09:01:01,565 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-20 09:01:01,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158124.0, 'new_value': 168936.0}, {'field': 'total_amount', 'old_value': 158124.0, 'new_value': 168936.0}, {'field': 'order_count', 'old_value': 13177, 'new_value': 14078}]
2025-05-20 09:01:01,566 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-20 09:01:02,015 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-20 09:01:02,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9326.0, 'new_value': 9564.0}, {'field': 'total_amount', 'old_value': 9326.0, 'new_value': 9564.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-20 09:01:02,015 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-20 09:01:02,456 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-20 09:01:02,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34846.0, 'new_value': 35644.0}, {'field': 'total_amount', 'old_value': 34846.0, 'new_value': 35644.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-05-20 09:01:02,456 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-20 09:01:02,908 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-20 09:01:02,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34079.75, 'new_value': 35177.75}, {'field': 'total_amount', 'old_value': 34079.75, 'new_value': 35177.75}, {'field': 'order_count', 'old_value': 1488, 'new_value': 1543}]
2025-05-20 09:01:02,908 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-20 09:01:03,322 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-20 09:01:03,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72725.7, 'new_value': 80232.1}, {'field': 'total_amount', 'old_value': 187690.95, 'new_value': 195197.35}, {'field': 'order_count', 'old_value': 4825, 'new_value': 5061}]
2025-05-20 09:01:03,322 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-20 09:01:03,883 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-20 09:01:03,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39563.41, 'new_value': 42100.84}, {'field': 'offline_amount', 'old_value': 24449.04, 'new_value': 25905.87}, {'field': 'total_amount', 'old_value': 64012.45, 'new_value': 68006.71}, {'field': 'order_count', 'old_value': 3494, 'new_value': 3724}]
2025-05-20 09:01:03,884 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-20 09:01:04,338 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-20 09:01:04,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17570.18, 'new_value': 17995.18}, {'field': 'total_amount', 'old_value': 17570.18, 'new_value': 17995.18}, {'field': 'order_count', 'old_value': 140, 'new_value': 149}]
2025-05-20 09:01:04,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-20 09:01:04,744 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-20 09:01:04,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303869.9, 'new_value': 319452.04}, {'field': 'total_amount', 'old_value': 303869.9, 'new_value': 319452.04}, {'field': 'order_count', 'old_value': 1095, 'new_value': 1147}]
2025-05-20 09:01:04,744 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-20 09:01:05,199 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-20 09:01:05,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234490.4, 'new_value': 244586.8}, {'field': 'total_amount', 'old_value': 234490.4, 'new_value': 244586.8}, {'field': 'order_count', 'old_value': 5789, 'new_value': 6075}]
2025-05-20 09:01:05,199 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-20 09:01:05,676 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-20 09:01:05,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28381.47, 'new_value': 29851.63}, {'field': 'total_amount', 'old_value': 28381.47, 'new_value': 29851.63}, {'field': 'order_count', 'old_value': 3631, 'new_value': 3819}]
2025-05-20 09:01:05,677 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-20 09:01:06,208 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-20 09:01:06,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18629.54, 'new_value': 19343.66}, {'field': 'offline_amount', 'old_value': 24484.18, 'new_value': 25455.41}, {'field': 'total_amount', 'old_value': 43113.72, 'new_value': 44799.07}, {'field': 'order_count', 'old_value': 1926, 'new_value': 2009}]
2025-05-20 09:01:06,208 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-20 09:01:06,781 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-20 09:01:06,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59481.0, 'new_value': 61309.0}, {'field': 'total_amount', 'old_value': 64682.0, 'new_value': 66510.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 194}]
2025-05-20 09:01:06,782 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-20 09:01:07,352 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-20 09:01:07,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243164.8, 'new_value': 243628.8}, {'field': 'total_amount', 'old_value': 243164.8, 'new_value': 243628.8}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-20 09:01:07,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-20 09:01:07,895 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-20 09:01:07,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343089.36, 'new_value': 353404.36}, {'field': 'total_amount', 'old_value': 343089.36, 'new_value': 353404.36}, {'field': 'order_count', 'old_value': 1683, 'new_value': 1746}]
2025-05-20 09:01:07,895 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-20 09:01:08,433 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-20 09:01:08,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127714.0, 'new_value': 127853.0}, {'field': 'total_amount', 'old_value': 127714.0, 'new_value': 127853.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-05-20 09:01:08,434 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-20 09:01:08,931 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-20 09:01:08,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 927817.0, 'new_value': 935016.0}, {'field': 'total_amount', 'old_value': 927817.0, 'new_value': 935016.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-05-20 09:01:08,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-20 09:01:09,442 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-20 09:01:09,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32896.89, 'new_value': 34668.67}, {'field': 'total_amount', 'old_value': 74184.89, 'new_value': 75956.67}, {'field': 'order_count', 'old_value': 247, 'new_value': 256}]
2025-05-20 09:01:09,442 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-20 09:01:09,890 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-20 09:01:09,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 243371.92, 'new_value': 251860.92}, {'field': 'offline_amount', 'old_value': 6147.5, 'new_value': 6459.5}, {'field': 'total_amount', 'old_value': 249519.42, 'new_value': 258320.42}, {'field': 'order_count', 'old_value': 2094, 'new_value': 2199}]
2025-05-20 09:01:09,891 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-20 09:01:10,297 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-20 09:01:10,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18563.0, 'new_value': 20531.4}, {'field': 'total_amount', 'old_value': 18563.0, 'new_value': 20531.4}, {'field': 'order_count', 'old_value': 532, 'new_value': 571}]
2025-05-20 09:01:10,298 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-20 09:01:10,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-20 09:01:10,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6057.1, 'new_value': 6176.1}, {'field': 'offline_amount', 'old_value': 29634.8, 'new_value': 30612.1}, {'field': 'total_amount', 'old_value': 35691.9, 'new_value': 36788.2}, {'field': 'order_count', 'old_value': 401, 'new_value': 421}]
2025-05-20 09:01:10,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-20 09:01:11,181 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-20 09:01:11,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12220.0, 'new_value': 13301.0}, {'field': 'total_amount', 'old_value': 26478.0, 'new_value': 27559.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-05-20 09:01:11,182 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-20 09:01:11,724 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-20 09:01:11,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 656530.0, 'new_value': 677305.0}, {'field': 'total_amount', 'old_value': 656530.0, 'new_value': 677305.0}, {'field': 'order_count', 'old_value': 2926, 'new_value': 3030}]
2025-05-20 09:01:11,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-20 09:01:12,119 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-20 09:01:12,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20145.0, 'new_value': 26918.0}, {'field': 'total_amount', 'old_value': 20145.0, 'new_value': 26918.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-20 09:01:12,119 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-20 09:01:12,560 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-20 09:01:12,561 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34281.82, 'new_value': 35670.42}, {'field': 'offline_amount', 'old_value': 247296.59, 'new_value': 256308.45}, {'field': 'total_amount', 'old_value': 281578.41, 'new_value': 291978.87}, {'field': 'order_count', 'old_value': 1761, 'new_value': 1831}]
2025-05-20 09:01:12,561 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-20 09:01:13,090 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-20 09:01:13,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63059.0, 'new_value': 63895.0}, {'field': 'total_amount', 'old_value': 63059.0, 'new_value': 63895.0}, {'field': 'order_count', 'old_value': 1863, 'new_value': 1887}]
2025-05-20 09:01:13,090 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-20 09:01:13,555 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-20 09:01:13,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68757.22, 'new_value': 71600.81}, {'field': 'offline_amount', 'old_value': 80943.41, 'new_value': 84727.01}, {'field': 'total_amount', 'old_value': 149700.63, 'new_value': 156327.82}, {'field': 'order_count', 'old_value': 6023, 'new_value': 6307}]
2025-05-20 09:01:13,555 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-20 09:01:14,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-20 09:01:14,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45279.0, 'new_value': 46209.0}, {'field': 'total_amount', 'old_value': 45279.0, 'new_value': 46209.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 98}]
2025-05-20 09:01:14,014 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-20 09:01:14,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-20 09:01:14,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 519636.4, 'new_value': 536670.4}, {'field': 'total_amount', 'old_value': 519636.4, 'new_value': 536670.4}, {'field': 'order_count', 'old_value': 3932, 'new_value': 4101}]
2025-05-20 09:01:14,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-20 09:01:14,848 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-20 09:01:14,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19986.0, 'new_value': 21253.0}, {'field': 'total_amount', 'old_value': 19986.0, 'new_value': 21253.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 114}]
2025-05-20 09:01:14,848 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-20 09:01:15,340 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-20 09:01:15,340 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43484.45, 'new_value': 45166.45}, {'field': 'offline_amount', 'old_value': 35198.33, 'new_value': 36556.33}, {'field': 'total_amount', 'old_value': 78682.78, 'new_value': 81722.78}, {'field': 'order_count', 'old_value': 1548, 'new_value': 1613}]
2025-05-20 09:01:15,341 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-20 09:01:15,796 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-20 09:01:15,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1780000.0, 'new_value': 1830000.0}, {'field': 'total_amount', 'old_value': 1780000.0, 'new_value': 1830000.0}, {'field': 'order_count', 'old_value': 275, 'new_value': 276}]
2025-05-20 09:01:15,797 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-20 09:01:16,344 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-20 09:01:16,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144766.3, 'new_value': 148108.1}, {'field': 'total_amount', 'old_value': 144766.3, 'new_value': 148108.1}, {'field': 'order_count', 'old_value': 1838, 'new_value': 1887}]
2025-05-20 09:01:16,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-20 09:01:16,772 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-20 09:01:16,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74549.83, 'new_value': 78266.83}, {'field': 'total_amount', 'old_value': 74549.83, 'new_value': 78266.83}, {'field': 'order_count', 'old_value': 96, 'new_value': 101}]
2025-05-20 09:01:16,772 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-20 09:01:17,232 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-20 09:01:17,232 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30244.24, 'new_value': 31684.84}, {'field': 'offline_amount', 'old_value': 838298.24, 'new_value': 867590.89}, {'field': 'total_amount', 'old_value': 868542.48, 'new_value': 899275.73}, {'field': 'order_count', 'old_value': 4171, 'new_value': 4333}]
2025-05-20 09:01:17,232 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-20 09:01:17,731 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-20 09:01:17,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36895.2, 'new_value': 38697.4}, {'field': 'total_amount', 'old_value': 36895.2, 'new_value': 38697.4}, {'field': 'order_count', 'old_value': 139, 'new_value': 146}]
2025-05-20 09:01:17,731 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-20 09:01:18,222 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-20 09:01:18,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 406022.0, 'new_value': 429151.0}, {'field': 'total_amount', 'old_value': 406022.0, 'new_value': 429151.0}, {'field': 'order_count', 'old_value': 359, 'new_value': 377}]
2025-05-20 09:01:18,222 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-20 09:01:18,657 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-20 09:01:18,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 196435.12, 'new_value': 203830.32}, {'field': 'offline_amount', 'old_value': 116321.56, 'new_value': 121705.59}, {'field': 'total_amount', 'old_value': 312756.68, 'new_value': 325535.91}, {'field': 'order_count', 'old_value': 2801, 'new_value': 2842}]
2025-05-20 09:01:18,657 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-20 09:01:19,174 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-20 09:01:19,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475000.0, 'new_value': 480000.0}, {'field': 'total_amount', 'old_value': 475000.0, 'new_value': 480000.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 143}]
2025-05-20 09:01:19,174 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-20 09:01:19,614 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-20 09:01:19,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 455000.0, 'new_value': 460000.0}, {'field': 'total_amount', 'old_value': 455000.0, 'new_value': 460000.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 142}]
2025-05-20 09:01:19,614 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-20 09:01:20,054 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-20 09:01:20,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2898674.0, 'new_value': 2948674.0}, {'field': 'total_amount', 'old_value': 2898674.0, 'new_value': 2948674.0}, {'field': 'order_count', 'old_value': 295, 'new_value': 296}]
2025-05-20 09:01:20,054 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-20 09:01:20,504 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-20 09:01:20,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66819.0, 'new_value': 70335.0}, {'field': 'offline_amount', 'old_value': 792578.0, 'new_value': 833262.0}, {'field': 'total_amount', 'old_value': 859397.0, 'new_value': 903597.0}, {'field': 'order_count', 'old_value': 20746, 'new_value': 22123}]
2025-05-20 09:01:20,505 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-20 09:01:20,920 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-20 09:01:20,920 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 13510.48}, {'field': 'total_amount', 'old_value': 305348.08, 'new_value': 318858.56}, {'field': 'order_count', 'old_value': 1004, 'new_value': 1034}]
2025-05-20 09:01:20,920 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-20 09:01:21,334 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-20 09:01:21,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28639.0, 'new_value': 28909.0}, {'field': 'offline_amount', 'old_value': 144216.0, 'new_value': 185893.0}, {'field': 'total_amount', 'old_value': 172855.0, 'new_value': 214802.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 204}]
2025-05-20 09:01:21,335 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-20 09:01:21,822 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-20 09:01:21,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36032.6, 'new_value': 36110.6}, {'field': 'total_amount', 'old_value': 50828.8, 'new_value': 50906.8}, {'field': 'order_count', 'old_value': 530, 'new_value': 532}]
2025-05-20 09:01:21,822 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-20 09:01:22,242 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-20 09:01:22,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78294.0, 'new_value': 80944.0}, {'field': 'total_amount', 'old_value': 78294.0, 'new_value': 80944.0}, {'field': 'order_count', 'old_value': 337, 'new_value': 348}]
2025-05-20 09:01:22,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-20 09:01:22,701 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-20 09:01:22,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130945.07, 'new_value': 135970.25}, {'field': 'total_amount', 'old_value': 130945.07, 'new_value': 135970.25}, {'field': 'order_count', 'old_value': 6773, 'new_value': 6973}]
2025-05-20 09:01:22,701 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-20 09:01:23,177 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-20 09:01:23,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122226.5, 'new_value': 126382.1}, {'field': 'total_amount', 'old_value': 122226.5, 'new_value': 126382.1}]
2025-05-20 09:01:23,178 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-20 09:01:23,644 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-20 09:01:23,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108211.2, 'new_value': 110151.7}, {'field': 'total_amount', 'old_value': 108211.2, 'new_value': 110151.7}, {'field': 'order_count', 'old_value': 2987, 'new_value': 3041}]
2025-05-20 09:01:23,645 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-20 09:01:24,110 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-20 09:01:24,111 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7858.0, 'new_value': 8433.0}, {'field': 'total_amount', 'old_value': 16465.0, 'new_value': 17040.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 91}]
2025-05-20 09:01:24,111 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-20 09:01:24,559 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-20 09:01:24,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69774.0, 'new_value': 71962.0}, {'field': 'total_amount', 'old_value': 69774.0, 'new_value': 71962.0}, {'field': 'order_count', 'old_value': 1688, 'new_value': 1689}]
2025-05-20 09:01:24,560 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-20 09:01:24,978 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-20 09:01:24,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68264.96, 'new_value': 69687.02}, {'field': 'total_amount', 'old_value': 69525.39, 'new_value': 70947.45}, {'field': 'order_count', 'old_value': 314, 'new_value': 321}]
2025-05-20 09:01:24,979 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-20 09:01:25,402 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-20 09:01:25,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3662.0, 'new_value': 3720.0}, {'field': 'total_amount', 'old_value': 10090.0, 'new_value': 10148.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 99}]
2025-05-20 09:01:25,403 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-20 09:01:25,882 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-20 09:01:25,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39866.86, 'new_value': 41802.96}, {'field': 'offline_amount', 'old_value': 35831.55, 'new_value': 38998.02}, {'field': 'total_amount', 'old_value': 75698.41, 'new_value': 80800.98}, {'field': 'order_count', 'old_value': 3901, 'new_value': 4128}]
2025-05-20 09:01:25,882 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-20 09:01:26,494 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-20 09:01:26,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73407.35, 'new_value': 77924.76}, {'field': 'offline_amount', 'old_value': 82247.29, 'new_value': 87348.34}, {'field': 'total_amount', 'old_value': 155654.64, 'new_value': 165273.1}, {'field': 'order_count', 'old_value': 3928, 'new_value': 4172}]
2025-05-20 09:01:26,495 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-20 09:01:26,958 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-20 09:01:26,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 716495.0, 'new_value': 747192.0}, {'field': 'total_amount', 'old_value': 716495.0, 'new_value': 747192.0}, {'field': 'order_count', 'old_value': 833, 'new_value': 869}]
2025-05-20 09:01:26,958 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-20 09:01:27,370 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-20 09:01:27,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150377.6, 'new_value': 157433.5}, {'field': 'total_amount', 'old_value': 156327.9, 'new_value': 163383.8}, {'field': 'order_count', 'old_value': 286, 'new_value': 305}]
2025-05-20 09:01:27,370 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-20 09:01:27,795 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-20 09:01:27,795 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32516.55, 'new_value': 34309.35}, {'field': 'offline_amount', 'old_value': 83525.0, 'new_value': 86790.0}, {'field': 'total_amount', 'old_value': 116041.55, 'new_value': 121099.35}, {'field': 'order_count', 'old_value': 1315, 'new_value': 1386}]
2025-05-20 09:01:27,795 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-20 09:01:28,181 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-20 09:01:28,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6765.5, 'new_value': 7032.4}, {'field': 'offline_amount', 'old_value': 15838.33, 'new_value': 17226.23}, {'field': 'total_amount', 'old_value': 22603.83, 'new_value': 24258.63}, {'field': 'order_count', 'old_value': 241, 'new_value': 251}]
2025-05-20 09:01:28,182 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-20 09:01:28,590 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-20 09:01:28,590 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7585.42, 'new_value': 8678.46}, {'field': 'offline_amount', 'old_value': 110510.0, 'new_value': 111010.0}, {'field': 'total_amount', 'old_value': 118095.42, 'new_value': 119688.46}, {'field': 'order_count', 'old_value': 56, 'new_value': 60}]
2025-05-20 09:01:28,590 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-20 09:01:29,001 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-20 09:01:29,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24840.62, 'new_value': 26164.62}, {'field': 'offline_amount', 'old_value': 22818.6, 'new_value': 24171.0}, {'field': 'total_amount', 'old_value': 47659.22, 'new_value': 50335.62}, {'field': 'order_count', 'old_value': 204, 'new_value': 218}]
2025-05-20 09:01:29,002 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-20 09:01:29,373 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-20 09:01:29,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2187.0, 'new_value': 2399.0}, {'field': 'offline_amount', 'old_value': 42588.0, 'new_value': 45158.0}, {'field': 'total_amount', 'old_value': 44775.0, 'new_value': 47557.0}, {'field': 'order_count', 'old_value': 361, 'new_value': 376}]
2025-05-20 09:01:29,374 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-20 09:01:29,790 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-20 09:01:29,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165499.5, 'new_value': 169907.5}, {'field': 'total_amount', 'old_value': 165499.5, 'new_value': 169907.5}, {'field': 'order_count', 'old_value': 811, 'new_value': 838}]
2025-05-20 09:01:29,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-20 09:01:30,242 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-20 09:01:30,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37266.11, 'new_value': 39762.12}, {'field': 'total_amount', 'old_value': 41430.11, 'new_value': 43926.12}, {'field': 'order_count', 'old_value': 361, 'new_value': 384}]
2025-05-20 09:01:30,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-20 09:01:30,690 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-20 09:01:30,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137526.5, 'new_value': 144337.1}, {'field': 'total_amount', 'old_value': 137526.5, 'new_value': 144337.1}, {'field': 'order_count', 'old_value': 501, 'new_value': 527}]
2025-05-20 09:01:30,691 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-20 09:01:31,132 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-20 09:01:31,132 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53998.0, 'new_value': 55448.0}, {'field': 'offline_amount', 'old_value': 217071.0, 'new_value': 233289.0}, {'field': 'total_amount', 'old_value': 271069.0, 'new_value': 288737.0}, {'field': 'order_count', 'old_value': 1098, 'new_value': 1158}]
2025-05-20 09:01:31,132 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-20 09:01:31,483 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-20 09:01:31,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133429.78, 'new_value': 143654.78}, {'field': 'total_amount', 'old_value': 234298.48, 'new_value': 244523.48}, {'field': 'order_count', 'old_value': 227, 'new_value': 246}]
2025-05-20 09:01:31,483 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-20 09:01:31,859 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-20 09:01:31,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40202.8, 'new_value': 42016.0}, {'field': 'total_amount', 'old_value': 40202.8, 'new_value': 42016.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 217}]
2025-05-20 09:01:31,859 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-20 09:01:32,367 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-20 09:01:32,367 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151614.0, 'new_value': 159312.0}, {'field': 'offline_amount', 'old_value': 127196.0, 'new_value': 130015.0}, {'field': 'total_amount', 'old_value': 278810.0, 'new_value': 289327.0}, {'field': 'order_count', 'old_value': 757, 'new_value': 788}]
2025-05-20 09:01:32,367 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-20 09:01:32,864 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-20 09:01:32,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 525811.97, 'new_value': 532138.13}, {'field': 'total_amount', 'old_value': 525811.97, 'new_value': 532138.13}, {'field': 'order_count', 'old_value': 3251, 'new_value': 3307}]
2025-05-20 09:01:32,864 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-20 09:01:33,301 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-20 09:01:33,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108196.9, 'new_value': 112043.84}, {'field': 'total_amount', 'old_value': 108196.9, 'new_value': 112043.84}, {'field': 'order_count', 'old_value': 7437, 'new_value': 7708}]
2025-05-20 09:01:33,302 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-20 09:01:33,754 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-20 09:01:33,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368988.0, 'new_value': 372503.0}, {'field': 'total_amount', 'old_value': 368988.0, 'new_value': 372503.0}, {'field': 'order_count', 'old_value': 8355, 'new_value': 8432}]
2025-05-20 09:01:33,755 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-20 09:01:34,231 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-20 09:01:34,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100401.0, 'new_value': 107911.0}, {'field': 'total_amount', 'old_value': 100401.0, 'new_value': 107911.0}, {'field': 'order_count', 'old_value': 7396, 'new_value': 7954}]
2025-05-20 09:01:34,232 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-20 09:01:34,745 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-20 09:01:34,745 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18171.9, 'new_value': 19269.6}, {'field': 'offline_amount', 'old_value': 43474.9, 'new_value': 46867.1}, {'field': 'total_amount', 'old_value': 61646.8, 'new_value': 66136.7}, {'field': 'order_count', 'old_value': 2326, 'new_value': 2518}]
2025-05-20 09:01:34,745 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-20 09:01:35,203 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-20 09:01:35,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46557.54, 'new_value': 48735.71}, {'field': 'total_amount', 'old_value': 51917.51, 'new_value': 54095.68}, {'field': 'order_count', 'old_value': 826, 'new_value': 846}]
2025-05-20 09:01:35,203 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-20 09:01:35,580 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-20 09:01:35,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19267.26, 'new_value': 19833.26}, {'field': 'total_amount', 'old_value': 19267.26, 'new_value': 19833.26}, {'field': 'order_count', 'old_value': 86, 'new_value': 90}]
2025-05-20 09:01:35,581 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-20 09:01:35,989 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-20 09:01:35,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234729.0, 'new_value': 248515.0}, {'field': 'total_amount', 'old_value': 234729.0, 'new_value': 248515.0}, {'field': 'order_count', 'old_value': 5099, 'new_value': 5400}]
2025-05-20 09:01:35,990 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-20 09:01:36,462 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-20 09:01:36,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123415.8, 'new_value': 132996.8}, {'field': 'total_amount', 'old_value': 123415.8, 'new_value': 132996.8}, {'field': 'order_count', 'old_value': 4333, 'new_value': 4676}]
2025-05-20 09:01:36,463 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-20 09:01:36,932 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-20 09:01:36,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234598.65, 'new_value': 243583.46}, {'field': 'total_amount', 'old_value': 234598.65, 'new_value': 243583.46}, {'field': 'order_count', 'old_value': 648, 'new_value': 670}]
2025-05-20 09:01:36,932 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-20 09:01:37,376 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-20 09:01:37,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107180.0, 'new_value': 108693.0}, {'field': 'total_amount', 'old_value': 107180.0, 'new_value': 108693.0}, {'field': 'order_count', 'old_value': 416, 'new_value': 432}]
2025-05-20 09:01:37,376 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-20 09:01:37,839 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-20 09:01:37,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3288.0, 'new_value': 3291.0}, {'field': 'total_amount', 'old_value': 18876.0, 'new_value': 18879.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-20 09:01:37,840 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-20 09:01:38,300 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-20 09:01:38,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36301.0, 'new_value': 38593.0}, {'field': 'total_amount', 'old_value': 36301.0, 'new_value': 38593.0}, {'field': 'order_count', 'old_value': 698, 'new_value': 739}]
2025-05-20 09:01:38,300 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-20 09:01:38,763 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-20 09:01:38,764 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1780.95, 'new_value': 1938.95}, {'field': 'total_amount', 'old_value': 8580.95, 'new_value': 8738.95}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-05-20 09:01:38,764 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-20 09:01:39,207 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-20 09:01:39,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8458.0, 'new_value': 9532.0}, {'field': 'offline_amount', 'old_value': 38989.29, 'new_value': 41032.29}, {'field': 'total_amount', 'old_value': 47447.29, 'new_value': 50564.29}, {'field': 'order_count', 'old_value': 223, 'new_value': 240}]
2025-05-20 09:01:39,207 - INFO - 日期 2025-05 处理完成 - 更新: 145 条，插入: 0 条，错误: 0 条
2025-05-20 09:01:39,207 - INFO - 数据同步完成！更新: 145 条，插入: 0 条，错误: 0 条
2025-05-20 09:01:39,209 - INFO - =================同步完成====================
2025-05-20 12:00:01,995 - INFO - =================使用默认全量同步=============
2025-05-20 12:00:03,416 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-20 12:00:03,417 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 12:00:03,447 - INFO - 开始处理日期: 2025-01
2025-05-20 12:00:03,450 - INFO - Request Parameters - Page 1:
2025-05-20 12:00:03,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:03,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:04,779 - INFO - Response - Page 1:
2025-05-20 12:00:04,979 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:00:04,979 - INFO - Request Parameters - Page 2:
2025-05-20 12:00:04,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:04,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:05,561 - INFO - Response - Page 2:
2025-05-20 12:00:05,761 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:00:05,761 - INFO - Request Parameters - Page 3:
2025-05-20 12:00:05,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:05,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:06,315 - INFO - Response - Page 3:
2025-05-20 12:00:06,515 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:00:06,515 - INFO - Request Parameters - Page 4:
2025-05-20 12:00:06,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:06,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:07,037 - INFO - Response - Page 4:
2025-05-20 12:00:07,237 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:00:07,237 - INFO - Request Parameters - Page 5:
2025-05-20 12:00:07,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:07,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:07,779 - INFO - Response - Page 5:
2025-05-20 12:00:07,980 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:00:07,980 - INFO - Request Parameters - Page 6:
2025-05-20 12:00:07,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:07,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:08,562 - INFO - Response - Page 6:
2025-05-20 12:00:08,763 - INFO - 第 6 页获取到 100 条记录
2025-05-20 12:00:08,763 - INFO - Request Parameters - Page 7:
2025-05-20 12:00:08,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:08,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:09,259 - INFO - Response - Page 7:
2025-05-20 12:00:09,461 - INFO - 第 7 页获取到 82 条记录
2025-05-20 12:00:09,461 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 12:00:09,461 - INFO - 获取到 682 条表单数据
2025-05-20 12:00:09,473 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 12:00:09,485 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 12:00:09,485 - INFO - 开始处理日期: 2025-02
2025-05-20 12:00:09,485 - INFO - Request Parameters - Page 1:
2025-05-20 12:00:09,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:09,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:10,051 - INFO - Response - Page 1:
2025-05-20 12:00:10,251 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:00:10,251 - INFO - Request Parameters - Page 2:
2025-05-20 12:00:10,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:10,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:10,802 - INFO - Response - Page 2:
2025-05-20 12:00:11,004 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:00:11,004 - INFO - Request Parameters - Page 3:
2025-05-20 12:00:11,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:11,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:11,557 - INFO - Response - Page 3:
2025-05-20 12:00:11,758 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:00:11,758 - INFO - Request Parameters - Page 4:
2025-05-20 12:00:11,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:11,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:12,252 - INFO - Response - Page 4:
2025-05-20 12:00:12,452 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:00:12,452 - INFO - Request Parameters - Page 5:
2025-05-20 12:00:12,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:12,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:13,146 - INFO - Response - Page 5:
2025-05-20 12:00:13,347 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:00:13,347 - INFO - Request Parameters - Page 6:
2025-05-20 12:00:13,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:13,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:13,813 - INFO - Response - Page 6:
2025-05-20 12:00:14,013 - INFO - 第 6 页获取到 100 条记录
2025-05-20 12:00:14,013 - INFO - Request Parameters - Page 7:
2025-05-20 12:00:14,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:14,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:14,489 - INFO - Response - Page 7:
2025-05-20 12:00:14,689 - INFO - 第 7 页获取到 70 条记录
2025-05-20 12:00:14,689 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 12:00:14,689 - INFO - 获取到 670 条表单数据
2025-05-20 12:00:14,702 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 12:00:14,714 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 12:00:14,714 - INFO - 开始处理日期: 2025-03
2025-05-20 12:00:14,714 - INFO - Request Parameters - Page 1:
2025-05-20 12:00:14,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:14,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:15,428 - INFO - Response - Page 1:
2025-05-20 12:00:15,629 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:00:15,629 - INFO - Request Parameters - Page 2:
2025-05-20 12:00:15,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:15,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:16,132 - INFO - Response - Page 2:
2025-05-20 12:00:16,332 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:00:16,332 - INFO - Request Parameters - Page 3:
2025-05-20 12:00:16,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:16,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:16,891 - INFO - Response - Page 3:
2025-05-20 12:00:17,091 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:00:17,091 - INFO - Request Parameters - Page 4:
2025-05-20 12:00:17,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:17,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:17,629 - INFO - Response - Page 4:
2025-05-20 12:00:17,829 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:00:17,829 - INFO - Request Parameters - Page 5:
2025-05-20 12:00:17,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:17,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:18,353 - INFO - Response - Page 5:
2025-05-20 12:00:18,553 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:00:18,553 - INFO - Request Parameters - Page 6:
2025-05-20 12:00:18,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:18,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:19,071 - INFO - Response - Page 6:
2025-05-20 12:00:19,272 - INFO - 第 6 页获取到 100 条记录
2025-05-20 12:00:19,272 - INFO - Request Parameters - Page 7:
2025-05-20 12:00:19,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:19,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:19,745 - INFO - Response - Page 7:
2025-05-20 12:00:19,945 - INFO - 第 7 页获取到 61 条记录
2025-05-20 12:00:19,945 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 12:00:19,945 - INFO - 获取到 661 条表单数据
2025-05-20 12:00:19,958 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 12:00:19,970 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 12:00:19,970 - INFO - 开始处理日期: 2025-04
2025-05-20 12:00:19,970 - INFO - Request Parameters - Page 1:
2025-05-20 12:00:19,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:19,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:20,502 - INFO - Response - Page 1:
2025-05-20 12:00:20,702 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:00:20,702 - INFO - Request Parameters - Page 2:
2025-05-20 12:00:20,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:20,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:21,214 - INFO - Response - Page 2:
2025-05-20 12:00:21,415 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:00:21,415 - INFO - Request Parameters - Page 3:
2025-05-20 12:00:21,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:21,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:21,917 - INFO - Response - Page 3:
2025-05-20 12:00:22,117 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:00:22,117 - INFO - Request Parameters - Page 4:
2025-05-20 12:00:22,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:22,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:22,708 - INFO - Response - Page 4:
2025-05-20 12:00:22,908 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:00:22,908 - INFO - Request Parameters - Page 5:
2025-05-20 12:00:22,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:22,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:23,376 - INFO - Response - Page 5:
2025-05-20 12:00:23,576 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:00:23,576 - INFO - Request Parameters - Page 6:
2025-05-20 12:00:23,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:23,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:24,117 - INFO - Response - Page 6:
2025-05-20 12:00:24,317 - INFO - 第 6 页获取到 100 条记录
2025-05-20 12:00:24,317 - INFO - Request Parameters - Page 7:
2025-05-20 12:00:24,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:24,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:24,761 - INFO - Response - Page 7:
2025-05-20 12:00:24,962 - INFO - 第 7 页获取到 56 条记录
2025-05-20 12:00:24,962 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 12:00:24,962 - INFO - 获取到 656 条表单数据
2025-05-20 12:00:24,974 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 12:00:24,986 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 12:00:24,986 - INFO - 开始处理日期: 2025-05
2025-05-20 12:00:24,986 - INFO - Request Parameters - Page 1:
2025-05-20 12:00:24,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:24,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:25,451 - INFO - Response - Page 1:
2025-05-20 12:00:25,652 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:00:25,652 - INFO - Request Parameters - Page 2:
2025-05-20 12:00:25,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:25,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:26,187 - INFO - Response - Page 2:
2025-05-20 12:00:26,387 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:00:26,387 - INFO - Request Parameters - Page 3:
2025-05-20 12:00:26,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:26,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:26,933 - INFO - Response - Page 3:
2025-05-20 12:00:27,133 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:00:27,133 - INFO - Request Parameters - Page 4:
2025-05-20 12:00:27,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:27,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:27,671 - INFO - Response - Page 4:
2025-05-20 12:00:27,872 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:00:27,872 - INFO - Request Parameters - Page 5:
2025-05-20 12:00:27,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:27,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:28,386 - INFO - Response - Page 5:
2025-05-20 12:00:28,587 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:00:28,587 - INFO - Request Parameters - Page 6:
2025-05-20 12:00:28,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:28,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:29,190 - INFO - Response - Page 6:
2025-05-20 12:00:29,391 - INFO - 第 6 页获取到 100 条记录
2025-05-20 12:00:29,391 - INFO - Request Parameters - Page 7:
2025-05-20 12:00:29,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:00:29,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:00:29,791 - INFO - Response - Page 7:
2025-05-20 12:00:29,991 - INFO - 第 7 页获取到 28 条记录
2025-05-20 12:00:29,991 - INFO - 查询完成，共获取到 628 条记录
2025-05-20 12:00:29,991 - INFO - 获取到 628 条表单数据
2025-05-20 12:00:30,003 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-20 12:00:30,004 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-20 12:00:30,421 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-20 12:00:30,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7200000.0, 'new_value': 7600000.0}, {'field': 'total_amount', 'old_value': 7300000.0, 'new_value': 7700000.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 37}]
2025-05-20 12:00:30,421 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-20 12:00:30,831 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-20 12:00:30,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30740.0, 'new_value': 33390.0}, {'field': 'total_amount', 'old_value': 32330.0, 'new_value': 34980.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 132}]
2025-05-20 12:00:30,831 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-20 12:00:31,317 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-20 12:00:31,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125842.07, 'new_value': 135817.56}, {'field': 'total_amount', 'old_value': 125842.07, 'new_value': 135817.56}, {'field': 'order_count', 'old_value': 5057, 'new_value': 5362}]
2025-05-20 12:00:31,317 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-20 12:00:31,699 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-20 12:00:31,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 540400.98, 'new_value': 564576.98}, {'field': 'total_amount', 'old_value': 540400.98, 'new_value': 564576.98}, {'field': 'order_count', 'old_value': 1596, 'new_value': 1681}]
2025-05-20 12:00:31,700 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-20 12:00:32,598 - INFO - 更新表单数据成功: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-20 12:00:32,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36378.0, 'new_value': 39591.0}, {'field': 'total_amount', 'old_value': 36378.0, 'new_value': 39591.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-20 12:00:32,599 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-20 12:00:33,046 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-20 12:00:33,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29258.21, 'new_value': 31617.32}, {'field': 'total_amount', 'old_value': 29258.21, 'new_value': 31617.32}, {'field': 'order_count', 'old_value': 5670, 'new_value': 6148}]
2025-05-20 12:00:33,046 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-20 12:00:33,466 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-20 12:00:33,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43690.78, 'new_value': 44208.78}, {'field': 'total_amount', 'old_value': 43690.78, 'new_value': 44208.78}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-05-20 12:00:33,466 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-20 12:00:33,904 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-20 12:00:33,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34880.0, 'new_value': 37520.0}, {'field': 'total_amount', 'old_value': 39000.0, 'new_value': 41640.0}, {'field': 'order_count', 'old_value': 384, 'new_value': 407}]
2025-05-20 12:00:33,904 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-20 12:00:34,386 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-20 12:00:34,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35815.5, 'new_value': 37875.3}, {'field': 'total_amount', 'old_value': 39775.5, 'new_value': 41835.3}, {'field': 'order_count', 'old_value': 297, 'new_value': 304}]
2025-05-20 12:00:34,387 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-20 12:00:34,881 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-20 12:00:34,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2633900.0, 'new_value': 2848800.0}, {'field': 'total_amount', 'old_value': 2633900.0, 'new_value': 2848800.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-20 12:00:34,882 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-20 12:00:35,395 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-20 12:00:35,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303124.48, 'new_value': 316743.48}, {'field': 'total_amount', 'old_value': 303124.48, 'new_value': 316743.48}, {'field': 'order_count', 'old_value': 339, 'new_value': 352}]
2025-05-20 12:00:35,395 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-20 12:00:35,869 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-20 12:00:35,869 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 840163.0, 'new_value': 860163.0}, {'field': 'total_amount', 'old_value': 1103610.0, 'new_value': 1123610.0}, {'field': 'order_count', 'old_value': 1288, 'new_value': 1292}]
2025-05-20 12:00:35,869 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-20 12:00:36,379 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-20 12:00:36,379 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9436.19, 'new_value': 10371.93}, {'field': 'offline_amount', 'old_value': 28302.29, 'new_value': 29374.79}, {'field': 'total_amount', 'old_value': 37738.48, 'new_value': 39746.72}, {'field': 'order_count', 'old_value': 668, 'new_value': 697}]
2025-05-20 12:00:36,379 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-20 12:00:36,824 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-20 12:00:36,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15800.0, 'new_value': 37600.0}, {'field': 'total_amount', 'old_value': 22677.0, 'new_value': 44477.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 10}]
2025-05-20 12:00:36,825 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-20 12:00:37,254 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-20 12:00:37,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75089.7, 'new_value': 75208.7}, {'field': 'total_amount', 'old_value': 81458.75, 'new_value': 81577.75}, {'field': 'order_count', 'old_value': 1780, 'new_value': 1785}]
2025-05-20 12:00:37,254 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-20 12:00:37,651 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-20 12:00:37,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 202400.0, 'new_value': 232400.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-05-20 12:00:37,652 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-20 12:00:38,158 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-20 12:00:38,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1257.4, 'new_value': 42636.2}, {'field': 'total_amount', 'old_value': 639024.4, 'new_value': 680403.2}, {'field': 'order_count', 'old_value': 68, 'new_value': 73}]
2025-05-20 12:00:38,158 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-20 12:00:38,569 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-20 12:00:38,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43961.6, 'new_value': 44079.6}, {'field': 'total_amount', 'old_value': 59356.3, 'new_value': 59474.3}, {'field': 'order_count', 'old_value': 640, 'new_value': 641}]
2025-05-20 12:00:38,569 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-20 12:00:39,056 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-20 12:00:39,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65751.0, 'new_value': 70277.0}, {'field': 'total_amount', 'old_value': 115338.0, 'new_value': 119864.0}, {'field': 'order_count', 'old_value': 1504, 'new_value': 1570}]
2025-05-20 12:00:39,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-20 12:00:39,525 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-20 12:00:39,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19946.5, 'new_value': 32273.5}, {'field': 'total_amount', 'old_value': 19946.5, 'new_value': 32273.5}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-20 12:00:39,525 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-20 12:00:40,005 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-20 12:00:40,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139437.64, 'new_value': 142702.19}, {'field': 'total_amount', 'old_value': 139437.64, 'new_value': 142702.19}, {'field': 'order_count', 'old_value': 178, 'new_value': 189}]
2025-05-20 12:00:40,005 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-20 12:00:40,484 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-20 12:00:40,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9619.0, 'new_value': 10195.0}, {'field': 'total_amount', 'old_value': 9619.0, 'new_value': 10195.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-05-20 12:00:40,485 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-20 12:00:40,927 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-20 12:00:40,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6932.0, 'new_value': 7356.0}, {'field': 'total_amount', 'old_value': 8425.0, 'new_value': 8849.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 169}]
2025-05-20 12:00:40,928 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-20 12:00:41,439 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-20 12:00:41,440 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38888.06, 'new_value': 41597.18}, {'field': 'offline_amount', 'old_value': 402167.37, 'new_value': 413704.57}, {'field': 'total_amount', 'old_value': 441055.43, 'new_value': 455301.75}, {'field': 'order_count', 'old_value': 1409, 'new_value': 1453}]
2025-05-20 12:00:41,440 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-20 12:00:41,885 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-20 12:00:41,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61272.0, 'new_value': 65646.0}, {'field': 'total_amount', 'old_value': 116280.0, 'new_value': 120654.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-05-20 12:00:41,886 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-20 12:00:42,334 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-20 12:00:42,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155246.0, 'new_value': 161656.0}, {'field': 'total_amount', 'old_value': 155246.0, 'new_value': 161656.0}, {'field': 'order_count', 'old_value': 769, 'new_value': 820}]
2025-05-20 12:00:42,335 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-20 12:00:42,917 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-20 12:00:42,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52390.0, 'new_value': 52759.0}, {'field': 'total_amount', 'old_value': 57474.32, 'new_value': 57843.32}, {'field': 'order_count', 'old_value': 446, 'new_value': 447}]
2025-05-20 12:00:42,918 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-20 12:00:43,405 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-20 12:00:43,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152216.62, 'new_value': 157925.82}, {'field': 'total_amount', 'old_value': 152216.62, 'new_value': 157925.82}, {'field': 'order_count', 'old_value': 496, 'new_value': 521}]
2025-05-20 12:00:43,405 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-20 12:00:43,810 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-20 12:00:43,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59964.2, 'new_value': 62748.5}, {'field': 'total_amount', 'old_value': 64773.15, 'new_value': 67557.45}, {'field': 'order_count', 'old_value': 3700, 'new_value': 3870}]
2025-05-20 12:00:43,810 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-20 12:00:44,268 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-20 12:00:44,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10046.61, 'new_value': 10525.61}, {'field': 'total_amount', 'old_value': 10046.61, 'new_value': 10525.61}, {'field': 'order_count', 'old_value': 259, 'new_value': 271}]
2025-05-20 12:00:44,268 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-20 12:00:44,714 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-20 12:00:44,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62514.5, 'new_value': 65889.92}, {'field': 'offline_amount', 'old_value': 652065.34, 'new_value': 675429.34}, {'field': 'total_amount', 'old_value': 714579.84, 'new_value': 741319.26}, {'field': 'order_count', 'old_value': 2310, 'new_value': 2400}]
2025-05-20 12:00:44,714 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-20 12:00:45,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-20 12:00:45,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21616.0, 'new_value': 24416.0}, {'field': 'total_amount', 'old_value': 21616.0, 'new_value': 24416.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-05-20 12:00:45,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-20 12:00:45,686 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-20 12:00:45,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 513245.42, 'new_value': 542012.29}, {'field': 'total_amount', 'old_value': 513245.42, 'new_value': 542012.29}]
2025-05-20 12:00:45,687 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-20 12:00:46,041 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-20 12:00:46,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41868.8, 'new_value': 43693.11}, {'field': 'offline_amount', 'old_value': 78511.19, 'new_value': 80964.75}, {'field': 'total_amount', 'old_value': 120379.99, 'new_value': 124657.86}, {'field': 'order_count', 'old_value': 4111, 'new_value': 4278}]
2025-05-20 12:00:46,041 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-20 12:00:46,587 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-20 12:00:46,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12521.0, 'new_value': 14102.0}, {'field': 'total_amount', 'old_value': 16831.0, 'new_value': 18412.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 109}]
2025-05-20 12:00:46,588 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-20 12:00:47,094 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-20 12:00:47,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208331.36, 'new_value': 224937.63}, {'field': 'total_amount', 'old_value': 209122.36, 'new_value': 225728.63}, {'field': 'order_count', 'old_value': 2400, 'new_value': 2596}]
2025-05-20 12:00:47,094 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-20 12:00:47,637 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-20 12:00:47,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240338.0, 'new_value': 244218.0}, {'field': 'total_amount', 'old_value': 240338.0, 'new_value': 244218.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 142}]
2025-05-20 12:00:47,637 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-20 12:00:48,054 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-20 12:00:48,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131848.17, 'new_value': 137296.05}, {'field': 'total_amount', 'old_value': 131848.17, 'new_value': 137296.05}, {'field': 'order_count', 'old_value': 737, 'new_value': 777}]
2025-05-20 12:00:48,054 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-20 12:00:48,545 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-20 12:00:48,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137842.96, 'new_value': 146801.83}, {'field': 'offline_amount', 'old_value': 350088.65, 'new_value': 356343.33}, {'field': 'total_amount', 'old_value': 487931.61, 'new_value': 503145.16}, {'field': 'order_count', 'old_value': 3490, 'new_value': 3628}]
2025-05-20 12:00:48,546 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-20 12:00:49,040 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-20 12:00:49,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22033.7, 'new_value': 23143.7}, {'field': 'total_amount', 'old_value': 22033.7, 'new_value': 23143.7}, {'field': 'order_count', 'old_value': 123, 'new_value': 126}]
2025-05-20 12:00:49,040 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-20 12:00:49,549 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-20 12:00:49,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21933.98, 'new_value': 22039.4}, {'field': 'total_amount', 'old_value': 21999.53, 'new_value': 22104.95}, {'field': 'order_count', 'old_value': 194, 'new_value': 196}]
2025-05-20 12:00:49,550 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-20 12:00:49,989 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-20 12:00:49,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1336065.0, 'new_value': 1371681.6}, {'field': 'total_amount', 'old_value': 1389510.1, 'new_value': 1425126.7}, {'field': 'order_count', 'old_value': 2414, 'new_value': 2504}]
2025-05-20 12:00:49,989 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-20 12:00:50,418 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-20 12:00:50,418 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141434.65, 'new_value': 151038.65}, {'field': 'offline_amount', 'old_value': 80190.0, 'new_value': 86382.0}, {'field': 'total_amount', 'old_value': 221624.65, 'new_value': 237420.65}, {'field': 'order_count', 'old_value': 1127, 'new_value': 1309}]
2025-05-20 12:00:50,419 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-20 12:00:50,890 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-20 12:00:50,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4603.0, 'new_value': 6412.0}, {'field': 'total_amount', 'old_value': 4603.0, 'new_value': 6412.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 17}]
2025-05-20 12:00:50,891 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-20 12:00:51,448 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-20 12:00:51,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30911.9, 'new_value': 31398.9}, {'field': 'total_amount', 'old_value': 30911.9, 'new_value': 31398.9}, {'field': 'order_count', 'old_value': 139, 'new_value': 142}]
2025-05-20 12:00:51,449 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-20 12:00:51,912 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-20 12:00:51,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99209.0, 'new_value': 102434.0}, {'field': 'total_amount', 'old_value': 99211.0, 'new_value': 102436.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-05-20 12:00:51,912 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-20 12:00:52,367 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-20 12:00:52,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129195.0, 'new_value': 130487.0}, {'field': 'total_amount', 'old_value': 136770.8, 'new_value': 138062.8}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-20 12:00:52,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-20 12:00:52,892 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-20 12:00:52,892 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32987.18, 'new_value': 34476.26}, {'field': 'offline_amount', 'old_value': 72683.39, 'new_value': 75009.16}, {'field': 'total_amount', 'old_value': 105670.57, 'new_value': 109485.42}, {'field': 'order_count', 'old_value': 3825, 'new_value': 3977}]
2025-05-20 12:00:52,892 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-20 12:00:53,352 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-20 12:00:53,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67062.49, 'new_value': 69092.39}, {'field': 'total_amount', 'old_value': 70831.59, 'new_value': 72861.49}, {'field': 'order_count', 'old_value': 377, 'new_value': 379}]
2025-05-20 12:00:53,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-20 12:00:53,772 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-20 12:00:53,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35565.0, 'new_value': 40470.0}, {'field': 'offline_amount', 'old_value': 135615.0, 'new_value': 139342.0}, {'field': 'total_amount', 'old_value': 171180.0, 'new_value': 179812.0}, {'field': 'order_count', 'old_value': 3676, 'new_value': 3880}]
2025-05-20 12:00:53,772 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-20 12:00:54,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-20 12:00:54,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10604.88, 'new_value': 10930.68}, {'field': 'offline_amount', 'old_value': 209418.3, 'new_value': 215806.6}, {'field': 'total_amount', 'old_value': 220023.18, 'new_value': 226737.28}, {'field': 'order_count', 'old_value': 1526, 'new_value': 1580}]
2025-05-20 12:00:54,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-20 12:00:54,638 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-20 12:00:54,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79343.64, 'new_value': 81661.64}, {'field': 'total_amount', 'old_value': 84683.64, 'new_value': 87001.64}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-20 12:00:54,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-20 12:00:55,085 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-20 12:00:55,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204727.0, 'new_value': 240321.0}, {'field': 'total_amount', 'old_value': 231111.0, 'new_value': 266705.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 73}]
2025-05-20 12:00:55,085 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-20 12:00:55,576 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-20 12:00:55,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111241.23, 'new_value': 115341.09}, {'field': 'offline_amount', 'old_value': 95077.45, 'new_value': 95744.45}, {'field': 'total_amount', 'old_value': 206318.68, 'new_value': 211085.54}, {'field': 'order_count', 'old_value': 2054, 'new_value': 2103}]
2025-05-20 12:00:55,576 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-20 12:00:55,955 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-20 12:00:55,955 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 485233.04, 'new_value': 485697.04}, {'field': 'offline_amount', 'old_value': 208878.1, 'new_value': 208913.1}, {'field': 'total_amount', 'old_value': 694111.14, 'new_value': 694610.14}, {'field': 'order_count', 'old_value': 6209, 'new_value': 6228}]
2025-05-20 12:00:55,955 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-20 12:00:56,412 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-20 12:00:56,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35269.6, 'new_value': 35756.6}, {'field': 'total_amount', 'old_value': 35812.6, 'new_value': 36299.6}, {'field': 'order_count', 'old_value': 153, 'new_value': 157}]
2025-05-20 12:00:56,412 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-20 12:00:56,851 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-20 12:00:56,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113509.0, 'new_value': 118208.0}, {'field': 'total_amount', 'old_value': 139031.66, 'new_value': 143730.66}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-20 12:00:56,851 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-20 12:00:57,352 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-20 12:00:57,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6402.6, 'new_value': 6845.6}, {'field': 'offline_amount', 'old_value': 36215.0, 'new_value': 37935.0}, {'field': 'total_amount', 'old_value': 42617.6, 'new_value': 44780.6}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-20 12:00:57,352 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-20 12:00:57,837 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-20 12:00:57,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 453911.0, 'new_value': 460301.0}, {'field': 'total_amount', 'old_value': 453911.0, 'new_value': 460301.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-05-20 12:00:57,837 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-20 12:00:58,317 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-20 12:00:58,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90458.48, 'new_value': 96090.68}, {'field': 'offline_amount', 'old_value': 314675.35, 'new_value': 330561.33}, {'field': 'total_amount', 'old_value': 405133.83, 'new_value': 426652.01}, {'field': 'order_count', 'old_value': 2382, 'new_value': 2440}]
2025-05-20 12:00:58,317 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-20 12:00:58,798 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-20 12:00:58,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43018.0, 'new_value': 43317.0}, {'field': 'total_amount', 'old_value': 43366.0, 'new_value': 43665.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 89}]
2025-05-20 12:00:58,799 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-20 12:00:59,216 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-20 12:00:59,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 629796.0, 'new_value': 665336.0}, {'field': 'total_amount', 'old_value': 629796.0, 'new_value': 665336.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 116}]
2025-05-20 12:00:59,216 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-20 12:00:59,625 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-20 12:00:59,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67126.0, 'new_value': 71517.0}, {'field': 'offline_amount', 'old_value': 56545.46, 'new_value': 58742.46}, {'field': 'total_amount', 'old_value': 123671.46, 'new_value': 130259.46}, {'field': 'order_count', 'old_value': 147, 'new_value': 155}]
2025-05-20 12:00:59,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-20 12:01:00,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-20 12:01:00,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128497.3, 'new_value': 131403.2}, {'field': 'total_amount', 'old_value': 128497.3, 'new_value': 131403.2}, {'field': 'order_count', 'old_value': 298, 'new_value': 305}]
2025-05-20 12:01:00,061 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-20 12:01:00,560 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-20 12:01:00,560 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168540.61, 'new_value': 175711.64}, {'field': 'offline_amount', 'old_value': 90368.91, 'new_value': 92836.25}, {'field': 'total_amount', 'old_value': 258909.52, 'new_value': 268547.89}, {'field': 'order_count', 'old_value': 969, 'new_value': 1002}]
2025-05-20 12:01:00,560 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-20 12:01:01,004 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-20 12:01:01,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177656.3, 'new_value': 178785.42}, {'field': 'offline_amount', 'old_value': 68670.82, 'new_value': 72844.09}, {'field': 'total_amount', 'old_value': 246327.12, 'new_value': 251629.51}, {'field': 'order_count', 'old_value': 2630, 'new_value': 2707}]
2025-05-20 12:01:01,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-20 12:01:01,491 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-20 12:01:01,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76519.35, 'new_value': 78293.15}, {'field': 'total_amount', 'old_value': 76519.35, 'new_value': 78293.15}, {'field': 'order_count', 'old_value': 2929, 'new_value': 3000}]
2025-05-20 12:01:01,491 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-20 12:01:01,960 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-20 12:01:01,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16019.42, 'new_value': 16887.47}, {'field': 'offline_amount', 'old_value': 221350.07, 'new_value': 230050.03}, {'field': 'total_amount', 'old_value': 237369.49, 'new_value': 246937.5}, {'field': 'order_count', 'old_value': 1130, 'new_value': 1171}]
2025-05-20 12:01:01,960 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-20 12:01:02,404 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-20 12:01:02,404 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26414.64, 'new_value': 27315.49}, {'field': 'offline_amount', 'old_value': 427202.73, 'new_value': 438807.11}, {'field': 'total_amount', 'old_value': 453617.37, 'new_value': 466122.6}, {'field': 'order_count', 'old_value': 2493, 'new_value': 2556}]
2025-05-20 12:01:02,404 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-20 12:01:02,847 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-20 12:01:02,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59817.42, 'new_value': 65762.1}, {'field': 'total_amount', 'old_value': 59817.42, 'new_value': 65762.1}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-05-20 12:01:02,847 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-20 12:01:03,317 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-20 12:01:03,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7405.81, 'new_value': 8269.41}, {'field': 'total_amount', 'old_value': 18926.79, 'new_value': 19790.39}, {'field': 'order_count', 'old_value': 79, 'new_value': 84}]
2025-05-20 12:01:03,317 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-20 12:01:03,768 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-20 12:01:03,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91597.04, 'new_value': 92462.09}, {'field': 'total_amount', 'old_value': 91597.04, 'new_value': 92462.09}, {'field': 'order_count', 'old_value': 613, 'new_value': 621}]
2025-05-20 12:01:03,768 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-20 12:01:04,244 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-20 12:01:04,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80272.2, 'new_value': 82461.3}, {'field': 'total_amount', 'old_value': 80272.2, 'new_value': 82461.3}, {'field': 'order_count', 'old_value': 246, 'new_value': 251}]
2025-05-20 12:01:04,244 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-20 12:01:04,688 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-20 12:01:04,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9144.79, 'new_value': 9687.29}, {'field': 'offline_amount', 'old_value': 93814.02, 'new_value': 97930.33}, {'field': 'total_amount', 'old_value': 102958.81, 'new_value': 107617.62}, {'field': 'order_count', 'old_value': 2643, 'new_value': 2780}]
2025-05-20 12:01:04,688 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-20 12:01:05,154 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-20 12:01:05,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128262.04, 'new_value': 138574.03}, {'field': 'offline_amount', 'old_value': 272807.24, 'new_value': 279673.74}, {'field': 'total_amount', 'old_value': 401069.28, 'new_value': 418247.77}, {'field': 'order_count', 'old_value': 10842, 'new_value': 11460}]
2025-05-20 12:01:05,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-20 12:01:05,637 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-20 12:01:05,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36283.0, 'new_value': 36559.0}, {'field': 'total_amount', 'old_value': 36283.0, 'new_value': 36559.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 96}]
2025-05-20 12:01:05,637 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-20 12:01:06,194 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-20 12:01:06,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27515.0, 'new_value': 27924.0}, {'field': 'total_amount', 'old_value': 27515.0, 'new_value': 27924.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 88}]
2025-05-20 12:01:06,195 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-20 12:01:06,667 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-20 12:01:06,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 933000.0, 'new_value': 973000.0}, {'field': 'total_amount', 'old_value': 933000.0, 'new_value': 973000.0}, {'field': 'order_count', 'old_value': 337, 'new_value': 338}]
2025-05-20 12:01:06,668 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-20 12:01:07,131 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-20 12:01:07,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13179.48, 'new_value': 14158.09}, {'field': 'offline_amount', 'old_value': 23897.11, 'new_value': 24892.63}, {'field': 'total_amount', 'old_value': 37076.59, 'new_value': 39050.72}, {'field': 'order_count', 'old_value': 1629, 'new_value': 1715}]
2025-05-20 12:01:07,131 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-20 12:01:07,634 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-20 12:01:07,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1516.0, 'new_value': 2044.0}, {'field': 'total_amount', 'old_value': 41519.0, 'new_value': 42047.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 174}]
2025-05-20 12:01:07,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-20 12:01:08,171 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-20 12:01:08,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254390.0, 'new_value': 267435.0}, {'field': 'total_amount', 'old_value': 254390.0, 'new_value': 267435.0}, {'field': 'order_count', 'old_value': 210, 'new_value': 217}]
2025-05-20 12:01:08,171 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-20 12:01:08,732 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-20 12:01:08,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5529.0, 'new_value': 5628.0}, {'field': 'total_amount', 'old_value': 5529.0, 'new_value': 5628.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-20 12:01:08,733 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-20 12:01:09,275 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-20 12:01:09,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215512.34, 'new_value': 227170.97}, {'field': 'total_amount', 'old_value': 215512.34, 'new_value': 227170.97}, {'field': 'order_count', 'old_value': 5805, 'new_value': 6148}]
2025-05-20 12:01:09,275 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-20 12:01:09,715 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-20 12:01:09,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243493.8, 'new_value': 248560.0}, {'field': 'total_amount', 'old_value': 243493.8, 'new_value': 248560.0}, {'field': 'order_count', 'old_value': 2709, 'new_value': 2761}]
2025-05-20 12:01:09,715 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-20 12:01:10,250 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-20 12:01:10,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33250.0, 'new_value': 37130.0}, {'field': 'total_amount', 'old_value': 33250.0, 'new_value': 37130.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-20 12:01:10,251 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-20 12:01:10,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-20 12:01:10,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195607.3, 'new_value': 200098.8}, {'field': 'total_amount', 'old_value': 195607.3, 'new_value': 200098.8}, {'field': 'order_count', 'old_value': 337, 'new_value': 347}]
2025-05-20 12:01:10,797 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-20 12:01:11,241 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-20 12:01:11,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64584.16, 'new_value': 78819.47}, {'field': 'total_amount', 'old_value': 470459.27, 'new_value': 484694.58}, {'field': 'order_count', 'old_value': 2047, 'new_value': 2087}]
2025-05-20 12:01:11,241 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-20 12:01:11,664 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-20 12:01:11,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96129.0, 'new_value': 100128.0}, {'field': 'total_amount', 'old_value': 96129.0, 'new_value': 100128.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-20 12:01:11,664 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-20 12:01:12,155 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-20 12:01:12,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151148.0, 'new_value': 163702.0}, {'field': 'total_amount', 'old_value': 151281.0, 'new_value': 163835.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 109}]
2025-05-20 12:01:12,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-20 12:01:12,571 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-20 12:01:12,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81846.19, 'new_value': 84205.19}, {'field': 'total_amount', 'old_value': 87457.71, 'new_value': 89816.71}, {'field': 'order_count', 'old_value': 7802, 'new_value': 8041}]
2025-05-20 12:01:12,571 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-20 12:01:13,041 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-20 12:01:13,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7083.31, 'new_value': 7462.11}, {'field': 'offline_amount', 'old_value': 68584.48, 'new_value': 70984.5}, {'field': 'total_amount', 'old_value': 75667.79, 'new_value': 78446.61}, {'field': 'order_count', 'old_value': 2125, 'new_value': 2184}]
2025-05-20 12:01:13,041 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-20 12:01:13,505 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-20 12:01:13,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14584.88, 'new_value': 15816.61}, {'field': 'offline_amount', 'old_value': 29294.05, 'new_value': 31122.54}, {'field': 'total_amount', 'old_value': 43878.93, 'new_value': 46939.15}, {'field': 'order_count', 'old_value': 2344, 'new_value': 2471}]
2025-05-20 12:01:13,506 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-20 12:01:13,954 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-20 12:01:13,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95861.1, 'new_value': 98361.1}, {'field': 'total_amount', 'old_value': 95861.1, 'new_value': 98361.1}, {'field': 'order_count', 'old_value': 977, 'new_value': 978}]
2025-05-20 12:01:13,955 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-20 12:01:14,379 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-20 12:01:14,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24199.9, 'new_value': 25615.7}, {'field': 'total_amount', 'old_value': 25986.8, 'new_value': 27402.6}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-20 12:01:14,379 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-20 12:01:15,008 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-20 12:01:15,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6511.61, 'new_value': 7014.68}, {'field': 'offline_amount', 'old_value': 9680.92, 'new_value': 9793.72}, {'field': 'total_amount', 'old_value': 16192.53, 'new_value': 16808.4}, {'field': 'order_count', 'old_value': 1257, 'new_value': 1319}]
2025-05-20 12:01:15,009 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-20 12:01:15,678 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-20 12:01:15,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4682459.0, 'new_value': 4882459.0}, {'field': 'total_amount', 'old_value': 4682459.0, 'new_value': 4882459.0}, {'field': 'order_count', 'old_value': 78449, 'new_value': 78450}]
2025-05-20 12:01:15,678 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-20 12:01:16,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-20 12:01:16,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61631.0, 'new_value': 63008.0}, {'field': 'total_amount', 'old_value': 61631.0, 'new_value': 63008.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 83}]
2025-05-20 12:01:16,181 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-20 12:01:16,601 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-20 12:01:16,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40052.0, 'new_value': 42294.0}, {'field': 'total_amount', 'old_value': 40052.0, 'new_value': 42294.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 298}]
2025-05-20 12:01:16,601 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-20 12:01:17,080 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-20 12:01:17,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28337.0, 'new_value': 29908.0}, {'field': 'total_amount', 'old_value': 28337.0, 'new_value': 29908.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-20 12:01:17,080 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-20 12:01:17,602 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-20 12:01:17,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37735.29, 'new_value': 39675.9}, {'field': 'offline_amount', 'old_value': 317786.59, 'new_value': 323714.27}, {'field': 'total_amount', 'old_value': 355521.88, 'new_value': 363390.17}, {'field': 'order_count', 'old_value': 2967, 'new_value': 3034}]
2025-05-20 12:01:17,603 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-20 12:01:18,029 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-20 12:01:18,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40986.98, 'new_value': 44959.98}, {'field': 'offline_amount', 'old_value': 349542.5, 'new_value': 357017.5}, {'field': 'total_amount', 'old_value': 390529.48, 'new_value': 401977.48}, {'field': 'order_count', 'old_value': 3031, 'new_value': 3135}]
2025-05-20 12:01:18,029 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-20 12:01:18,544 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-20 12:01:18,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23288.98, 'new_value': 24689.38}, {'field': 'total_amount', 'old_value': 23288.98, 'new_value': 24689.38}, {'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-05-20 12:01:18,545 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-20 12:01:19,000 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-20 12:01:19,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88442.0, 'new_value': 91742.0}, {'field': 'total_amount', 'old_value': 88442.0, 'new_value': 91742.0}, {'field': 'order_count', 'old_value': 3253, 'new_value': 3254}]
2025-05-20 12:01:19,000 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-20 12:01:19,459 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-20 12:01:19,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65048.63, 'new_value': 67395.09}, {'field': 'offline_amount', 'old_value': 31274.84, 'new_value': 32133.26}, {'field': 'total_amount', 'old_value': 96323.47, 'new_value': 99528.35}, {'field': 'order_count', 'old_value': 6083, 'new_value': 6302}]
2025-05-20 12:01:19,459 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-20 12:01:19,940 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-20 12:01:19,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 298274.24, 'new_value': 304601.24}, {'field': 'total_amount', 'old_value': 298274.24, 'new_value': 304601.24}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1473}]
2025-05-20 12:01:19,941 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-20 12:01:20,396 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-20 12:01:20,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22398.07, 'new_value': 23298.07}, {'field': 'total_amount', 'old_value': 22398.07, 'new_value': 23298.07}, {'field': 'order_count', 'old_value': 2188, 'new_value': 2189}]
2025-05-20 12:01:20,396 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-20 12:01:20,867 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-20 12:01:20,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119059.62, 'new_value': 123964.56}, {'field': 'total_amount', 'old_value': 119059.62, 'new_value': 123964.56}, {'field': 'order_count', 'old_value': 207, 'new_value': 216}]
2025-05-20 12:01:20,867 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-20 12:01:21,335 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-20 12:01:21,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86256.98, 'new_value': 88613.98}, {'field': 'total_amount', 'old_value': 86256.98, 'new_value': 88613.98}, {'field': 'order_count', 'old_value': 745, 'new_value': 766}]
2025-05-20 12:01:21,336 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-20 12:01:21,750 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-20 12:01:21,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70208.6, 'new_value': 73784.68}, {'field': 'offline_amount', 'old_value': 192557.2, 'new_value': 198912.46}, {'field': 'total_amount', 'old_value': 262765.8, 'new_value': 272697.14}, {'field': 'order_count', 'old_value': 8567, 'new_value': 8941}]
2025-05-20 12:01:21,751 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-20 12:01:22,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-20 12:01:22,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41990.0, 'new_value': 46889.0}, {'field': 'total_amount', 'old_value': 41990.0, 'new_value': 46889.0}, {'field': 'order_count', 'old_value': 17601, 'new_value': 17602}]
2025-05-20 12:01:22,213 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-20 12:01:22,732 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-20 12:01:22,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4981100.0, 'new_value': 5016100.0}, {'field': 'total_amount', 'old_value': 4981100.0, 'new_value': 5016100.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-05-20 12:01:22,733 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-20 12:01:23,234 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-20 12:01:23,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39498.0, 'new_value': 49586.0}, {'field': 'total_amount', 'old_value': 46422.0, 'new_value': 56510.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-20 12:01:23,235 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-20 12:01:23,730 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-20 12:01:23,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36492.0, 'new_value': 37188.8}, {'field': 'total_amount', 'old_value': 37399.2, 'new_value': 38096.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 120}]
2025-05-20 12:01:23,730 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-20 12:01:24,191 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-20 12:01:24,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 229276.7, 'new_value': 241526.95}, {'field': 'offline_amount', 'old_value': 12906.7, 'new_value': 14048.85}, {'field': 'total_amount', 'old_value': 242183.4, 'new_value': 255575.8}, {'field': 'order_count', 'old_value': 9605, 'new_value': 10096}]
2025-05-20 12:01:24,191 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-20 12:01:24,633 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-20 12:01:24,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87891.4, 'new_value': 88023.4}, {'field': 'total_amount', 'old_value': 104460.9, 'new_value': 104592.9}, {'field': 'order_count', 'old_value': 143, 'new_value': 144}]
2025-05-20 12:01:24,634 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-20 12:01:25,056 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-20 12:01:25,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 369259.0, 'new_value': 372259.0}, {'field': 'total_amount', 'old_value': 378077.99, 'new_value': 381077.99}, {'field': 'order_count', 'old_value': 69, 'new_value': 70}]
2025-05-20 12:01:25,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-20 12:01:25,624 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-20 12:01:25,624 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94750.96, 'new_value': 98992.63}, {'field': 'offline_amount', 'old_value': 210158.7, 'new_value': 215613.96}, {'field': 'total_amount', 'old_value': 304909.66, 'new_value': 314606.59}, {'field': 'order_count', 'old_value': 3812, 'new_value': 3970}]
2025-05-20 12:01:25,624 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-20 12:01:26,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-20 12:01:26,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195186.0, 'new_value': 204432.0}, {'field': 'total_amount', 'old_value': 195186.0, 'new_value': 204432.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 94}]
2025-05-20 12:01:26,062 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-20 12:01:26,514 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-20 12:01:26,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64491.2, 'new_value': 65928.8}, {'field': 'total_amount', 'old_value': 64491.2, 'new_value': 65928.8}, {'field': 'order_count', 'old_value': 475, 'new_value': 487}]
2025-05-20 12:01:26,514 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-20 12:01:26,887 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-20 12:01:26,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 309778.0, 'new_value': 323141.0}, {'field': 'total_amount', 'old_value': 309778.0, 'new_value': 323141.0}, {'field': 'order_count', 'old_value': 8272, 'new_value': 8700}]
2025-05-20 12:01:26,888 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-20 12:01:27,311 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-20 12:01:27,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316371.25, 'new_value': 336371.25}, {'field': 'total_amount', 'old_value': 316371.25, 'new_value': 336371.25}, {'field': 'order_count', 'old_value': 601, 'new_value': 602}]
2025-05-20 12:01:27,312 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-20 12:01:27,737 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-20 12:01:27,737 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55422.37, 'new_value': 57717.68}, {'field': 'offline_amount', 'old_value': 197460.57, 'new_value': 204228.89}, {'field': 'total_amount', 'old_value': 252882.94, 'new_value': 261946.57}, {'field': 'order_count', 'old_value': 3253, 'new_value': 3316}]
2025-05-20 12:01:27,737 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-20 12:01:28,222 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-20 12:01:28,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53777.0, 'new_value': 55061.9}, {'field': 'total_amount', 'old_value': 53777.0, 'new_value': 55061.9}, {'field': 'order_count', 'old_value': 142, 'new_value': 145}]
2025-05-20 12:01:28,222 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-20 12:01:28,699 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-20 12:01:28,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10736.0, 'new_value': 14624.0}, {'field': 'total_amount', 'old_value': 10736.0, 'new_value': 14624.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-20 12:01:28,700 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-20 12:01:29,116 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-20 12:01:29,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23602.0, 'new_value': 24230.0}, {'field': 'total_amount', 'old_value': 23602.0, 'new_value': 24230.0}, {'field': 'order_count', 'old_value': 230, 'new_value': 238}]
2025-05-20 12:01:29,117 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-20 12:01:29,556 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-20 12:01:29,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32252.6, 'new_value': 33809.38}, {'field': 'total_amount', 'old_value': 32252.6, 'new_value': 33809.38}, {'field': 'order_count', 'old_value': 131, 'new_value': 136}]
2025-05-20 12:01:29,556 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-20 12:01:30,105 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-20 12:01:30,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61283.0, 'new_value': 61841.0}, {'field': 'total_amount', 'old_value': 65779.0, 'new_value': 66337.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-20 12:01:30,106 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-20 12:01:30,543 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-20 12:01:30,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83259.0, 'new_value': 85225.0}, {'field': 'total_amount', 'old_value': 104516.41, 'new_value': 106482.41}, {'field': 'order_count', 'old_value': 86, 'new_value': 88}]
2025-05-20 12:01:30,543 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-20 12:01:30,989 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-20 12:01:30,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58388.0, 'new_value': 59388.0}, {'field': 'total_amount', 'old_value': 58388.0, 'new_value': 59388.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-20 12:01:30,989 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-20 12:01:31,395 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-20 12:01:31,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24929.0, 'new_value': 25008.0}, {'field': 'total_amount', 'old_value': 24929.0, 'new_value': 25008.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 234}]
2025-05-20 12:01:31,395 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-20 12:01:31,927 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-20 12:01:31,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 550850.05, 'new_value': 560204.1}, {'field': 'total_amount', 'old_value': 550850.05, 'new_value': 560204.1}, {'field': 'order_count', 'old_value': 1451, 'new_value': 1482}]
2025-05-20 12:01:31,927 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-20 12:01:32,366 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-20 12:01:32,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 512363.0, 'new_value': 559290.0}, {'field': 'total_amount', 'old_value': 512363.0, 'new_value': 559290.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 81}]
2025-05-20 12:01:32,366 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-20 12:01:32,789 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-20 12:01:32,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63419.12, 'new_value': 65636.32}, {'field': 'offline_amount', 'old_value': 102210.21, 'new_value': 103012.01}, {'field': 'total_amount', 'old_value': 165629.33, 'new_value': 168648.33}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1690}]
2025-05-20 12:01:32,790 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-20 12:01:33,250 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-20 12:01:33,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1220900.59, 'new_value': 1281799.27}, {'field': 'total_amount', 'old_value': 1369755.89, 'new_value': 1430654.57}, {'field': 'order_count', 'old_value': 4713, 'new_value': 4969}]
2025-05-20 12:01:33,250 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-20 12:01:33,818 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-20 12:01:33,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23344.0, 'new_value': 24544.0}, {'field': 'total_amount', 'old_value': 24720.0, 'new_value': 25920.0}, {'field': 'order_count', 'old_value': 2599, 'new_value': 2600}]
2025-05-20 12:01:33,819 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-20 12:01:34,246 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-20 12:01:34,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19643.0, 'new_value': 20026.0}, {'field': 'total_amount', 'old_value': 19643.0, 'new_value': 20026.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-20 12:01:34,246 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-20 12:01:34,700 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-20 12:01:34,700 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11855.2, 'new_value': 11982.4}, {'field': 'offline_amount', 'old_value': 37979.0, 'new_value': 38108.9}, {'field': 'total_amount', 'old_value': 49834.2, 'new_value': 50091.3}, {'field': 'order_count', 'old_value': 136, 'new_value': 140}]
2025-05-20 12:01:34,700 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-20 12:01:35,351 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-20 12:01:35,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8445.0, 'new_value': 8659.0}, {'field': 'total_amount', 'old_value': 10451.0, 'new_value': 10665.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 100}]
2025-05-20 12:01:35,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-20 12:01:35,757 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-20 12:01:35,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 440.0, 'new_value': 900.0}, {'field': 'offline_amount', 'old_value': 443861.0, 'new_value': 452048.0}, {'field': 'total_amount', 'old_value': 444301.0, 'new_value': 452948.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 214}]
2025-05-20 12:01:35,757 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-20 12:01:36,257 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-20 12:01:36,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23055.0, 'new_value': 23572.0}, {'field': 'total_amount', 'old_value': 23055.0, 'new_value': 23572.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 103}]
2025-05-20 12:01:36,257 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-20 12:01:36,721 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-20 12:01:36,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76693.6, 'new_value': 77837.0}, {'field': 'offline_amount', 'old_value': 115853.1, 'new_value': 117702.2}, {'field': 'total_amount', 'old_value': 192546.7, 'new_value': 195539.2}, {'field': 'order_count', 'old_value': 3873, 'new_value': 3933}]
2025-05-20 12:01:36,721 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-20 12:01:37,185 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-20 12:01:37,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368993.58, 'new_value': 379735.75}, {'field': 'total_amount', 'old_value': 368993.58, 'new_value': 379735.75}, {'field': 'order_count', 'old_value': 4967, 'new_value': 5135}]
2025-05-20 12:01:37,185 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-20 12:01:37,676 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-20 12:01:37,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7655.3, 'new_value': 8005.3}, {'field': 'offline_amount', 'old_value': 22059.9, 'new_value': 22138.9}, {'field': 'total_amount', 'old_value': 29715.2, 'new_value': 30144.2}, {'field': 'order_count', 'old_value': 73, 'new_value': 75}]
2025-05-20 12:01:37,676 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-20 12:01:38,228 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-20 12:01:38,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148769.0, 'new_value': 156743.0}, {'field': 'total_amount', 'old_value': 148769.0, 'new_value': 156743.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 237}]
2025-05-20 12:01:38,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-20 12:01:38,688 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-20 12:01:38,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 875.09, 'new_value': 1238.49}, {'field': 'offline_amount', 'old_value': 428019.54, 'new_value': 449507.34}, {'field': 'total_amount', 'old_value': 428894.63, 'new_value': 450745.83}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1086}]
2025-05-20 12:01:38,688 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-20 12:01:39,171 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-20 12:01:39,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126990.0, 'new_value': 133250.0}, {'field': 'total_amount', 'old_value': 126991.0, 'new_value': 133251.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-20 12:01:39,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-20 12:01:39,596 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-20 12:01:39,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68104.18, 'new_value': 70392.22}, {'field': 'total_amount', 'old_value': 68104.18, 'new_value': 70392.22}, {'field': 'order_count', 'old_value': 2101, 'new_value': 2178}]
2025-05-20 12:01:39,596 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-20 12:01:40,041 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-20 12:01:40,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6942.9, 'new_value': 7320.75}, {'field': 'offline_amount', 'old_value': 23731.76, 'new_value': 24393.45}, {'field': 'total_amount', 'old_value': 30674.66, 'new_value': 31714.2}, {'field': 'order_count', 'old_value': 1076, 'new_value': 1109}]
2025-05-20 12:01:40,042 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-20 12:01:40,381 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-20 12:01:40,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213101.0, 'new_value': 235878.0}, {'field': 'total_amount', 'old_value': 213101.0, 'new_value': 235878.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 56}]
2025-05-20 12:01:40,381 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-20 12:01:40,819 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-20 12:01:40,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117833.45, 'new_value': 120643.44}, {'field': 'total_amount', 'old_value': 117833.45, 'new_value': 120643.44}, {'field': 'order_count', 'old_value': 2982, 'new_value': 3069}]
2025-05-20 12:01:40,819 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-20 12:01:41,245 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-20 12:01:41,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54000.0, 'new_value': 59400.0}, {'field': 'total_amount', 'old_value': 54000.0, 'new_value': 59400.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-20 12:01:41,246 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-20 12:01:41,738 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-20 12:01:41,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25280.72, 'new_value': 26444.96}, {'field': 'offline_amount', 'old_value': 251239.68, 'new_value': 255896.83}, {'field': 'total_amount', 'old_value': 276520.4, 'new_value': 282341.79}, {'field': 'order_count', 'old_value': 6525, 'new_value': 6674}]
2025-05-20 12:01:41,738 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-20 12:01:42,272 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-20 12:01:42,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254860.18, 'new_value': 261095.89}, {'field': 'total_amount', 'old_value': 254860.18, 'new_value': 261095.89}, {'field': 'order_count', 'old_value': 2320, 'new_value': 2408}]
2025-05-20 12:01:42,272 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-20 12:01:42,729 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-20 12:01:42,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338247.6, 'new_value': 347466.3}, {'field': 'total_amount', 'old_value': 338247.6, 'new_value': 347466.3}, {'field': 'order_count', 'old_value': 1690, 'new_value': 1734}]
2025-05-20 12:01:42,730 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-20 12:01:43,171 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-20 12:01:43,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104758.38, 'new_value': 107941.82}, {'field': 'offline_amount', 'old_value': 42743.85, 'new_value': 43826.58}, {'field': 'total_amount', 'old_value': 147502.23, 'new_value': 151768.4}, {'field': 'order_count', 'old_value': 9071, 'new_value': 9328}]
2025-05-20 12:01:43,172 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-20 12:01:43,626 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-20 12:01:43,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5130.0, 'new_value': 5560.0}, {'field': 'offline_amount', 'old_value': 25066.0, 'new_value': 26269.0}, {'field': 'total_amount', 'old_value': 30196.0, 'new_value': 31829.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 253}]
2025-05-20 12:01:43,626 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-20 12:01:44,058 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-20 12:01:44,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87367.21, 'new_value': 90867.21}, {'field': 'total_amount', 'old_value': 163439.65, 'new_value': 166939.65}, {'field': 'order_count', 'old_value': 6956, 'new_value': 6957}]
2025-05-20 12:01:44,058 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-20 12:01:44,542 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-20 12:01:44,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159337.0, 'new_value': 164789.0}, {'field': 'total_amount', 'old_value': 159337.0, 'new_value': 164789.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 193}]
2025-05-20 12:01:44,542 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-20 12:01:45,198 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-20 12:01:45,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 565737.81, 'new_value': 576783.44}, {'field': 'total_amount', 'old_value': 565737.81, 'new_value': 576783.44}, {'field': 'order_count', 'old_value': 10797, 'new_value': 11001}]
2025-05-20 12:01:45,199 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-20 12:01:45,567 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-20 12:01:45,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211300.71, 'new_value': 219476.38}, {'field': 'total_amount', 'old_value': 211300.71, 'new_value': 219476.38}, {'field': 'order_count', 'old_value': 8896, 'new_value': 9268}]
2025-05-20 12:01:45,567 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-20 12:01:46,143 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-20 12:01:46,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22533.78, 'new_value': 23864.98}, {'field': 'total_amount', 'old_value': 22533.78, 'new_value': 23864.98}, {'field': 'order_count', 'old_value': 829, 'new_value': 878}]
2025-05-20 12:01:46,144 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-20 12:01:46,727 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-20 12:01:46,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 549995.07, 'new_value': 570476.73}, {'field': 'total_amount', 'old_value': 549995.07, 'new_value': 570476.73}, {'field': 'order_count', 'old_value': 4025, 'new_value': 4213}]
2025-05-20 12:01:46,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-20 12:01:47,213 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-20 12:01:47,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164061.0, 'new_value': 169607.0}, {'field': 'total_amount', 'old_value': 164061.0, 'new_value': 169607.0}, {'field': 'order_count', 'old_value': 498, 'new_value': 518}]
2025-05-20 12:01:47,213 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-20 12:01:47,618 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-20 12:01:47,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44810.5, 'new_value': 46667.37}, {'field': 'offline_amount', 'old_value': 343057.58, 'new_value': 354827.66}, {'field': 'total_amount', 'old_value': 387868.08, 'new_value': 401495.03}, {'field': 'order_count', 'old_value': 1803, 'new_value': 1883}]
2025-05-20 12:01:47,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-20 12:01:48,080 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-20 12:01:48,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 542691.0, 'new_value': 577731.0}, {'field': 'total_amount', 'old_value': 542691.0, 'new_value': 577731.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 143}]
2025-05-20 12:01:48,081 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-20 12:01:48,593 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-20 12:01:48,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1352.65, 'new_value': 1395.7}, {'field': 'offline_amount', 'old_value': 19075.77, 'new_value': 19259.57}, {'field': 'total_amount', 'old_value': 20428.42, 'new_value': 20655.27}, {'field': 'order_count', 'old_value': 724, 'new_value': 731}]
2025-05-20 12:01:48,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-20 12:01:49,106 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-20 12:01:49,106 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4810.38, 'new_value': 4987.48}, {'field': 'offline_amount', 'old_value': 279768.64, 'new_value': 289013.44}, {'field': 'total_amount', 'old_value': 284579.02, 'new_value': 294000.92}, {'field': 'order_count', 'old_value': 13857, 'new_value': 14281}]
2025-05-20 12:01:49,106 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-20 12:01:49,478 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-20 12:01:49,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62826.0, 'new_value': 64161.0}, {'field': 'total_amount', 'old_value': 62826.0, 'new_value': 64161.0}, {'field': 'order_count', 'old_value': 156, 'new_value': 160}]
2025-05-20 12:01:49,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-20 12:01:49,953 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-20 12:01:49,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35670.42, 'new_value': 37743.32}, {'field': 'offline_amount', 'old_value': 256308.45, 'new_value': 268074.1}, {'field': 'total_amount', 'old_value': 291978.87, 'new_value': 305817.42}, {'field': 'order_count', 'old_value': 1831, 'new_value': 1924}]
2025-05-20 12:01:49,953 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-20 12:01:50,350 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-20 12:01:50,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153948.55, 'new_value': 157547.9}, {'field': 'total_amount', 'old_value': 153948.55, 'new_value': 157547.9}, {'field': 'order_count', 'old_value': 843, 'new_value': 866}]
2025-05-20 12:01:50,350 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-20 12:01:50,789 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-20 12:01:50,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46404.14, 'new_value': 48290.13}, {'field': 'offline_amount', 'old_value': 69490.67, 'new_value': 70872.66}, {'field': 'total_amount', 'old_value': 115894.81, 'new_value': 119162.79}, {'field': 'order_count', 'old_value': 5354, 'new_value': 5493}]
2025-05-20 12:01:50,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-20 12:01:51,231 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-20 12:01:51,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281259.86, 'new_value': 291021.93}, {'field': 'total_amount', 'old_value': 303422.98, 'new_value': 313185.05}, {'field': 'order_count', 'old_value': 12716, 'new_value': 13172}]
2025-05-20 12:01:51,232 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-20 12:01:51,646 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-20 12:01:51,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8546.2, 'new_value': 8720.0}, {'field': 'total_amount', 'old_value': 23846.2, 'new_value': 24020.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 136}]
2025-05-20 12:01:51,646 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-20 12:01:52,094 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-20 12:01:52,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21562.24, 'new_value': 23123.75}, {'field': 'offline_amount', 'old_value': 183796.94, 'new_value': 188157.54}, {'field': 'total_amount', 'old_value': 205359.18, 'new_value': 211281.29}, {'field': 'order_count', 'old_value': 6430, 'new_value': 6608}]
2025-05-20 12:01:52,094 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-20 12:01:52,526 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-20 12:01:52,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303872.75, 'new_value': 308372.75}, {'field': 'total_amount', 'old_value': 303872.75, 'new_value': 308372.75}, {'field': 'order_count', 'old_value': 2134, 'new_value': 2135}]
2025-05-20 12:01:52,526 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-20 12:01:53,023 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-20 12:01:53,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103292.9, 'new_value': 104560.1}, {'field': 'total_amount', 'old_value': 103292.9, 'new_value': 104560.1}, {'field': 'order_count', 'old_value': 194, 'new_value': 198}]
2025-05-20 12:01:53,023 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-20 12:01:53,644 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-20 12:01:53,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76530.0, 'new_value': 77129.0}, {'field': 'total_amount', 'old_value': 91734.0, 'new_value': 92333.0}, {'field': 'order_count', 'old_value': 2072, 'new_value': 2088}]
2025-05-20 12:01:53,644 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-20 12:01:54,135 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-20 12:01:54,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76319.0, 'new_value': 80319.0}, {'field': 'total_amount', 'old_value': 76319.0, 'new_value': 80319.0}, {'field': 'order_count', 'old_value': 515, 'new_value': 516}]
2025-05-20 12:01:54,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-20 12:01:54,608 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-20 12:01:54,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116103.0, 'new_value': 117190.0}, {'field': 'total_amount', 'old_value': 116103.0, 'new_value': 117190.0}, {'field': 'order_count', 'old_value': 3712, 'new_value': 3744}]
2025-05-20 12:01:54,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-20 12:01:55,022 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-20 12:01:55,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87770.46, 'new_value': 93712.32}, {'field': 'offline_amount', 'old_value': 292112.72, 'new_value': 294613.62}, {'field': 'total_amount', 'old_value': 379883.18, 'new_value': 388325.94}, {'field': 'order_count', 'old_value': 3128, 'new_value': 3260}]
2025-05-20 12:01:55,022 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-20 12:01:55,509 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-20 12:01:55,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 196861.36, 'new_value': 197810.86}, {'field': 'offline_amount', 'old_value': 78217.38, 'new_value': 85202.78}, {'field': 'total_amount', 'old_value': 275078.74, 'new_value': 283013.64}, {'field': 'order_count', 'old_value': 487, 'new_value': 503}]
2025-05-20 12:01:55,509 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-20 12:01:55,966 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-20 12:01:55,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184568.31, 'new_value': 189623.26}, {'field': 'total_amount', 'old_value': 203741.74, 'new_value': 208796.69}, {'field': 'order_count', 'old_value': 4251, 'new_value': 4320}]
2025-05-20 12:01:55,967 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-20 12:01:56,421 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-20 12:01:56,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86707.8, 'new_value': 88482.8}, {'field': 'offline_amount', 'old_value': 61390.76, 'new_value': 62371.56}, {'field': 'total_amount', 'old_value': 148098.56, 'new_value': 150854.36}, {'field': 'order_count', 'old_value': 985, 'new_value': 1007}]
2025-05-20 12:01:56,422 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-20 12:01:56,839 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-20 12:01:56,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273529.3, 'new_value': 287570.6}, {'field': 'total_amount', 'old_value': 273529.3, 'new_value': 287570.6}, {'field': 'order_count', 'old_value': 336, 'new_value': 352}]
2025-05-20 12:01:56,839 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-20 12:01:57,333 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-20 12:01:57,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 715828.0, 'new_value': 743513.0}, {'field': 'total_amount', 'old_value': 715828.0, 'new_value': 743513.0}, {'field': 'order_count', 'old_value': 787, 'new_value': 822}]
2025-05-20 12:01:57,333 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-20 12:01:57,815 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-20 12:01:57,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481711.0, 'new_value': 509707.0}, {'field': 'total_amount', 'old_value': 481711.0, 'new_value': 509707.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-20 12:01:57,816 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-20 12:01:58,336 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-20 12:01:58,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 255954.67, 'new_value': 263301.31}, {'field': 'offline_amount', 'old_value': 973020.19, 'new_value': 1001804.46}, {'field': 'total_amount', 'old_value': 1228974.86, 'new_value': 1265105.77}, {'field': 'order_count', 'old_value': 6129, 'new_value': 6341}]
2025-05-20 12:01:58,336 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-20 12:01:58,776 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-20 12:01:58,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61201.51, 'new_value': 62544.77}, {'field': 'total_amount', 'old_value': 61201.51, 'new_value': 62544.77}, {'field': 'order_count', 'old_value': 3465, 'new_value': 3547}]
2025-05-20 12:01:58,776 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-20 12:01:59,241 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-20 12:01:59,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11258.45, 'new_value': 11458.45}, {'field': 'offline_amount', 'old_value': 203007.0, 'new_value': 211774.0}, {'field': 'total_amount', 'old_value': 214265.45, 'new_value': 223232.45}, {'field': 'order_count', 'old_value': 1142, 'new_value': 1187}]
2025-05-20 12:01:59,241 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-20 12:01:59,680 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-20 12:01:59,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98905.2, 'new_value': 104626.9}, {'field': 'offline_amount', 'old_value': 82712.8, 'new_value': 86127.8}, {'field': 'total_amount', 'old_value': 181618.0, 'new_value': 190754.7}, {'field': 'order_count', 'old_value': 4287, 'new_value': 4487}]
2025-05-20 12:01:59,680 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-20 12:02:00,141 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-20 12:02:00,141 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-20 12:02:00,141 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-20 12:02:00,615 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-20 12:02:00,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26711.7, 'new_value': 27662.7}, {'field': 'total_amount', 'old_value': 26711.7, 'new_value': 27662.7}, {'field': 'order_count', 'old_value': 151, 'new_value': 158}]
2025-05-20 12:02:00,615 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-20 12:02:01,062 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-20 12:02:01,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223116.5, 'new_value': 225116.5}, {'field': 'total_amount', 'old_value': 223116.5, 'new_value': 225116.5}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-05-20 12:02:01,062 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-20 12:02:01,491 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-20 12:02:01,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199622.93, 'new_value': 205254.93}, {'field': 'total_amount', 'old_value': 199622.93, 'new_value': 205254.93}, {'field': 'order_count', 'old_value': 1213, 'new_value': 1253}]
2025-05-20 12:02:01,491 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-20 12:02:02,008 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-20 12:02:02,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238126.0, 'new_value': 251376.0}, {'field': 'total_amount', 'old_value': 251901.0, 'new_value': 265151.0}, {'field': 'order_count', 'old_value': 5218, 'new_value': 5558}]
2025-05-20 12:02:02,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-20 12:02:02,492 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-20 12:02:02,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66399.95, 'new_value': 68125.37}, {'field': 'offline_amount', 'old_value': 150739.43, 'new_value': 152966.49}, {'field': 'total_amount', 'old_value': 217139.38, 'new_value': 221091.86}, {'field': 'order_count', 'old_value': 3929, 'new_value': 4058}]
2025-05-20 12:02:02,493 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-20 12:02:02,920 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-20 12:02:02,920 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38697.4, 'new_value': 43003.85}, {'field': 'total_amount', 'old_value': 38697.4, 'new_value': 43003.85}, {'field': 'order_count', 'old_value': 146, 'new_value': 161}]
2025-05-20 12:02:02,920 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-20 12:02:03,388 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-20 12:02:03,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 559048.29, 'new_value': 574895.01}, {'field': 'total_amount', 'old_value': 559048.29, 'new_value': 574895.01}, {'field': 'order_count', 'old_value': 6447, 'new_value': 6678}]
2025-05-20 12:02:03,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-20 12:02:03,823 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-20 12:02:03,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390295.2, 'new_value': 395129.6}, {'field': 'total_amount', 'old_value': 564487.8, 'new_value': 569322.2}, {'field': 'order_count', 'old_value': 3843, 'new_value': 3847}]
2025-05-20 12:02:03,823 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-20 12:02:04,280 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-20 12:02:04,280 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166200.67, 'new_value': 174667.94}, {'field': 'offline_amount', 'old_value': 583728.12, 'new_value': 597086.45}, {'field': 'total_amount', 'old_value': 749928.79, 'new_value': 771754.39}, {'field': 'order_count', 'old_value': 4303, 'new_value': 4486}]
2025-05-20 12:02:04,281 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-20 12:02:04,732 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-20 12:02:04,732 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23244.83, 'new_value': 24668.75}, {'field': 'offline_amount', 'old_value': 273224.9, 'new_value': 279567.4}, {'field': 'total_amount', 'old_value': 296469.73, 'new_value': 304236.15}, {'field': 'order_count', 'old_value': 9297, 'new_value': 9357}]
2025-05-20 12:02:04,732 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-20 12:02:05,244 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-20 12:02:05,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151302.0, 'new_value': 153738.0}, {'field': 'total_amount', 'old_value': 151302.0, 'new_value': 153738.0}, {'field': 'order_count', 'old_value': 2524, 'new_value': 2567}]
2025-05-20 12:02:05,244 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-20 12:02:05,702 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-20 12:02:05,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144051.86, 'new_value': 147727.34}, {'field': 'total_amount', 'old_value': 144051.86, 'new_value': 147727.34}, {'field': 'order_count', 'old_value': 6053, 'new_value': 6214}]
2025-05-20 12:02:05,703 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-20 12:02:06,243 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-20 12:02:06,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179057.96, 'new_value': 183858.18}, {'field': 'total_amount', 'old_value': 179057.96, 'new_value': 183858.18}, {'field': 'order_count', 'old_value': 1348, 'new_value': 1399}]
2025-05-20 12:02:06,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-20 12:02:06,746 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-20 12:02:06,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71310.8, 'new_value': 72289.6}, {'field': 'total_amount', 'old_value': 73039.6, 'new_value': 74018.4}, {'field': 'order_count', 'old_value': 447, 'new_value': 456}]
2025-05-20 12:02:06,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-20 12:02:07,520 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-20 12:02:07,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37263.6, 'new_value': 41501.6}, {'field': 'total_amount', 'old_value': 120759.3, 'new_value': 124997.3}, {'field': 'order_count', 'old_value': 3473, 'new_value': 3558}]
2025-05-20 12:02:07,521 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-20 12:02:08,007 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-20 12:02:08,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146268.47, 'new_value': 155376.9}, {'field': 'offline_amount', 'old_value': 20795.2, 'new_value': 20993.6}, {'field': 'total_amount', 'old_value': 167063.67, 'new_value': 176370.5}, {'field': 'order_count', 'old_value': 8207, 'new_value': 8566}]
2025-05-20 12:02:08,008 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-20 12:02:08,421 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-20 12:02:08,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29126.0, 'new_value': 29684.0}, {'field': 'total_amount', 'old_value': 29126.0, 'new_value': 29684.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-20 12:02:08,422 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-20 12:02:08,922 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-20 12:02:08,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27270.46, 'new_value': 28479.16}, {'field': 'offline_amount', 'old_value': 37033.54, 'new_value': 39033.54}, {'field': 'total_amount', 'old_value': 64304.0, 'new_value': 67512.7}, {'field': 'order_count', 'old_value': 3016, 'new_value': 3234}]
2025-05-20 12:02:08,922 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-20 12:02:09,430 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-20 12:02:09,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16445.95, 'new_value': 17408.95}, {'field': 'offline_amount', 'old_value': 12268.56, 'new_value': 12622.16}, {'field': 'total_amount', 'old_value': 28714.51, 'new_value': 30031.11}, {'field': 'order_count', 'old_value': 1255, 'new_value': 1297}]
2025-05-20 12:02:09,430 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-20 12:02:09,904 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-20 12:02:09,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226980.0, 'new_value': 237102.0}, {'field': 'total_amount', 'old_value': 226980.0, 'new_value': 237102.0}, {'field': 'order_count', 'old_value': 343, 'new_value': 358}]
2025-05-20 12:02:09,904 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-20 12:02:10,262 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-20 12:02:10,262 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81066.06, 'new_value': 86553.09}, {'field': 'offline_amount', 'old_value': 161022.78, 'new_value': 167112.15}, {'field': 'total_amount', 'old_value': 242088.84, 'new_value': 253665.24}, {'field': 'order_count', 'old_value': 7182, 'new_value': 7609}]
2025-05-20 12:02:10,262 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-20 12:02:10,735 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-20 12:02:10,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52396.1, 'new_value': 53495.1}, {'field': 'total_amount', 'old_value': 53201.1, 'new_value': 54300.1}, {'field': 'order_count', 'old_value': 16279, 'new_value': 16280}]
2025-05-20 12:02:10,736 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-20 12:02:11,185 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-20 12:02:11,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99091.52, 'new_value': 106767.52}, {'field': 'total_amount', 'old_value': 99099.52, 'new_value': 106775.52}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-20 12:02:11,185 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-20 12:02:11,600 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-20 12:02:11,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127104.59, 'new_value': 133462.8}, {'field': 'offline_amount', 'old_value': 37503.71, 'new_value': 38348.77}, {'field': 'total_amount', 'old_value': 164608.3, 'new_value': 171811.57}, {'field': 'order_count', 'old_value': 9294, 'new_value': 9687}]
2025-05-20 12:02:11,600 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-20 12:02:12,026 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-20 12:02:12,026 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84440.1, 'new_value': 90227.8}, {'field': 'offline_amount', 'old_value': 25619.4, 'new_value': 26452.4}, {'field': 'total_amount', 'old_value': 110059.5, 'new_value': 116680.2}, {'field': 'order_count', 'old_value': 8969, 'new_value': 9487}]
2025-05-20 12:02:12,026 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-20 12:02:12,564 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-20 12:02:12,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204995.89, 'new_value': 211662.69}, {'field': 'total_amount', 'old_value': 227483.29, 'new_value': 234150.09}, {'field': 'order_count', 'old_value': 1221, 'new_value': 1267}]
2025-05-20 12:02:12,564 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-20 12:02:13,032 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-20 12:02:13,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115954.87, 'new_value': 122898.13}, {'field': 'offline_amount', 'old_value': 221520.69, 'new_value': 228896.69}, {'field': 'total_amount', 'old_value': 337475.56, 'new_value': 351794.82}, {'field': 'order_count', 'old_value': 2724, 'new_value': 2876}]
2025-05-20 12:02:13,032 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-20 12:02:13,516 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-20 12:02:13,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1555833.0, 'new_value': 1600431.0}, {'field': 'total_amount', 'old_value': 1555833.0, 'new_value': 1600431.0}, {'field': 'order_count', 'old_value': 5981, 'new_value': 6181}]
2025-05-20 12:02:13,516 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-20 12:02:13,917 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-20 12:02:13,917 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31074.18, 'new_value': 32181.56}, {'field': 'offline_amount', 'old_value': 20442.36, 'new_value': 20818.97}, {'field': 'total_amount', 'old_value': 51516.54, 'new_value': 53000.53}, {'field': 'order_count', 'old_value': 2226, 'new_value': 2306}]
2025-05-20 12:02:13,918 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-20 12:02:14,414 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-20 12:02:14,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11438.44, 'new_value': 11975.49}, {'field': 'offline_amount', 'old_value': 27569.2, 'new_value': 28066.4}, {'field': 'total_amount', 'old_value': 39007.64, 'new_value': 40041.89}, {'field': 'order_count', 'old_value': 1542, 'new_value': 1588}]
2025-05-20 12:02:14,415 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-20 12:02:14,895 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-20 12:02:14,895 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246867.06, 'new_value': 248302.16}, {'field': 'total_amount', 'old_value': 246867.06, 'new_value': 248302.16}, {'field': 'order_count', 'old_value': 1373, 'new_value': 1385}]
2025-05-20 12:02:14,896 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-20 12:02:15,394 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-20 12:02:15,394 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42372.29, 'new_value': 46832.73}, {'field': 'offline_amount', 'old_value': 36108.14, 'new_value': 36813.34}, {'field': 'total_amount', 'old_value': 78480.43, 'new_value': 83646.07}, {'field': 'order_count', 'old_value': 6520, 'new_value': 7082}]
2025-05-20 12:02:15,394 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-20 12:02:15,889 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-20 12:02:15,889 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275458.0, 'new_value': 284456.0}, {'field': 'total_amount', 'old_value': 275458.0, 'new_value': 284456.0}]
2025-05-20 12:02:15,889 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-20 12:02:16,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-20 12:02:16,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71455.56, 'new_value': 74742.56}, {'field': 'total_amount', 'old_value': 71455.56, 'new_value': 74742.56}, {'field': 'order_count', 'old_value': 3681, 'new_value': 3857}]
2025-05-20 12:02:16,337 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-20 12:02:16,811 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-20 12:02:16,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 423801.0, 'new_value': 464001.0}, {'field': 'total_amount', 'old_value': 423801.0, 'new_value': 464001.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 63}]
2025-05-20 12:02:16,811 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-20 12:02:17,239 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-20 12:02:17,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 476218.0, 'new_value': 501962.0}, {'field': 'total_amount', 'old_value': 476218.0, 'new_value': 501962.0}, {'field': 'order_count', 'old_value': 368, 'new_value': 384}]
2025-05-20 12:02:17,239 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-20 12:02:17,684 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-20 12:02:17,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 601309.11, 'new_value': 615450.01}, {'field': 'total_amount', 'old_value': 601309.11, 'new_value': 615450.01}, {'field': 'order_count', 'old_value': 4674, 'new_value': 4817}]
2025-05-20 12:02:17,685 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-20 12:02:18,154 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-20 12:02:18,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 428986.0, 'new_value': 443082.0}, {'field': 'total_amount', 'old_value': 428986.0, 'new_value': 443082.0}, {'field': 'order_count', 'old_value': 2734, 'new_value': 2845}]
2025-05-20 12:02:18,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-20 12:02:18,642 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-20 12:02:18,642 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2654.0, 'new_value': 2768.0}, {'field': 'offline_amount', 'old_value': 24038.2, 'new_value': 24468.2}, {'field': 'total_amount', 'old_value': 26692.2, 'new_value': 27236.2}, {'field': 'order_count', 'old_value': 971, 'new_value': 992}]
2025-05-20 12:02:18,642 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-20 12:02:19,108 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-20 12:02:19,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101904.0, 'new_value': 106362.0}, {'field': 'offline_amount', 'old_value': 74253.0, 'new_value': 76334.0}, {'field': 'total_amount', 'old_value': 176157.0, 'new_value': 182696.0}, {'field': 'order_count', 'old_value': 2202, 'new_value': 2318}]
2025-05-20 12:02:19,108 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-20 12:02:19,598 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-20 12:02:19,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77050.67, 'new_value': 82332.19}, {'field': 'total_amount', 'old_value': 84279.74, 'new_value': 89561.26}, {'field': 'order_count', 'old_value': 463, 'new_value': 487}]
2025-05-20 12:02:19,598 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-20 12:02:20,084 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-20 12:02:20,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4133.0, 'new_value': 4253.0}, {'field': 'offline_amount', 'old_value': 16161.0, 'new_value': 16946.0}, {'field': 'total_amount', 'old_value': 20294.0, 'new_value': 21199.0}, {'field': 'order_count', 'old_value': 164, 'new_value': 169}]
2025-05-20 12:02:20,084 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-20 12:02:20,576 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-20 12:02:20,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 818120.0, 'new_value': 838998.0}, {'field': 'total_amount', 'old_value': 818120.0, 'new_value': 838998.0}, {'field': 'order_count', 'old_value': 3570, 'new_value': 3664}]
2025-05-20 12:02:20,576 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-20 12:02:21,021 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-20 12:02:21,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10290615.0, 'new_value': 10542933.0}, {'field': 'total_amount', 'old_value': 10290615.0, 'new_value': 10542933.0}, {'field': 'order_count', 'old_value': 31456, 'new_value': 32375}]
2025-05-20 12:02:21,022 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-20 12:02:21,454 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-20 12:02:21,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2782122.71, 'new_value': 2881632.39}, {'field': 'total_amount', 'old_value': 2782122.71, 'new_value': 2881632.39}, {'field': 'order_count', 'old_value': 4826, 'new_value': 5005}]
2025-05-20 12:02:21,454 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-20 12:02:21,874 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-20 12:02:21,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111404.2, 'new_value': 120271.94}, {'field': 'total_amount', 'old_value': 118843.84, 'new_value': 127711.58}, {'field': 'order_count', 'old_value': 8272, 'new_value': 8887}]
2025-05-20 12:02:21,874 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-20 12:02:22,401 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-20 12:02:22,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219714.0, 'new_value': 232884.0}, {'field': 'total_amount', 'old_value': 219714.0, 'new_value': 232884.0}, {'field': 'order_count', 'old_value': 4469, 'new_value': 4809}]
2025-05-20 12:02:22,402 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-20 12:02:22,935 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-20 12:02:22,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171405.0, 'new_value': 173291.0}, {'field': 'total_amount', 'old_value': 171405.0, 'new_value': 173291.0}, {'field': 'order_count', 'old_value': 373, 'new_value': 386}]
2025-05-20 12:02:22,935 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-20 12:02:23,460 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-20 12:02:23,460 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 219136.72, 'new_value': 228800.11}, {'field': 'offline_amount', 'old_value': 160066.25, 'new_value': 164560.83}, {'field': 'total_amount', 'old_value': 379202.97, 'new_value': 393360.94}, {'field': 'order_count', 'old_value': 15183, 'new_value': 15794}]
2025-05-20 12:02:23,460 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-20 12:02:23,967 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-20 12:02:23,967 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34604.05, 'new_value': 36872.9}, {'field': 'offline_amount', 'old_value': 52444.44, 'new_value': 54164.44}, {'field': 'total_amount', 'old_value': 87048.49, 'new_value': 91037.34}, {'field': 'order_count', 'old_value': 1878, 'new_value': 1975}]
2025-05-20 12:02:23,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-20 12:02:24,407 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-20 12:02:24,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220251.0, 'new_value': 227056.0}, {'field': 'total_amount', 'old_value': 220251.0, 'new_value': 227056.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 279}]
2025-05-20 12:02:24,407 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-20 12:02:24,922 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-20 12:02:24,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262143.8, 'new_value': 265536.4}, {'field': 'total_amount', 'old_value': 262143.8, 'new_value': 265536.4}, {'field': 'order_count', 'old_value': 5699, 'new_value': 5769}]
2025-05-20 12:02:24,923 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-20 12:02:25,369 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-20 12:02:25,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76998.0, 'new_value': 79710.0}, {'field': 'total_amount', 'old_value': 76998.0, 'new_value': 79710.0}, {'field': 'order_count', 'old_value': 5210, 'new_value': 5391}]
2025-05-20 12:02:25,370 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-20 12:02:25,809 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-20 12:02:25,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63997.2, 'new_value': 65121.2}, {'field': 'total_amount', 'old_value': 64250.2, 'new_value': 65374.2}, {'field': 'order_count', 'old_value': 951, 'new_value': 966}]
2025-05-20 12:02:25,810 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-20 12:02:26,336 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-20 12:02:26,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3202.6, 'new_value': 3377.6}, {'field': 'offline_amount', 'old_value': 17088.0, 'new_value': 18151.0}, {'field': 'total_amount', 'old_value': 20290.6, 'new_value': 21528.6}, {'field': 'order_count', 'old_value': 483, 'new_value': 510}]
2025-05-20 12:02:26,336 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-20 12:02:26,710 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-20 12:02:26,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141095.17, 'new_value': 148252.77}, {'field': 'total_amount', 'old_value': 141095.17, 'new_value': 148252.77}, {'field': 'order_count', 'old_value': 9851, 'new_value': 10457}]
2025-05-20 12:02:26,711 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-20 12:02:27,146 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-20 12:02:27,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27588.7, 'new_value': 28754.83}, {'field': 'total_amount', 'old_value': 27588.7, 'new_value': 28754.83}, {'field': 'order_count', 'old_value': 1271, 'new_value': 1326}]
2025-05-20 12:02:27,146 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-20 12:02:27,695 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-20 12:02:27,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3739524.51, 'new_value': 3902780.51}, {'field': 'total_amount', 'old_value': 3739524.51, 'new_value': 3902780.51}, {'field': 'order_count', 'old_value': 76372, 'new_value': 80023}]
2025-05-20 12:02:27,696 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-20 12:02:28,178 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-20 12:02:28,179 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 5546.36}, {'field': 'offline_amount', 'old_value': 422261.05, 'new_value': 433469.26}, {'field': 'total_amount', 'old_value': 422261.05, 'new_value': 439015.62}, {'field': 'order_count', 'old_value': 4819, 'new_value': 4864}]
2025-05-20 12:02:28,179 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-20 12:02:28,709 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-20 12:02:28,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137788.7, 'new_value': 147781.06}, {'field': 'total_amount', 'old_value': 137788.7, 'new_value': 147781.06}, {'field': 'order_count', 'old_value': 2657, 'new_value': 2811}]
2025-05-20 12:02:28,709 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-20 12:02:29,204 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-20 12:02:29,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57187.08, 'new_value': 59891.06}, {'field': 'total_amount', 'old_value': 57187.08, 'new_value': 59891.06}, {'field': 'order_count', 'old_value': 5746, 'new_value': 6078}]
2025-05-20 12:02:29,204 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-20 12:02:29,662 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-20 12:02:29,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269751.0, 'new_value': 269803.0}, {'field': 'total_amount', 'old_value': 269751.0, 'new_value': 269803.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-05-20 12:02:29,662 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-20 12:02:30,133 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-20 12:02:30,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98881.9, 'new_value': 102876.59}, {'field': 'total_amount', 'old_value': 98881.9, 'new_value': 102876.59}, {'field': 'order_count', 'old_value': 2467, 'new_value': 2593}]
2025-05-20 12:02:30,133 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-20 12:02:30,570 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-20 12:02:30,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22101.0, 'new_value': 23389.0}, {'field': 'total_amount', 'old_value': 22101.0, 'new_value': 23389.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 78}]
2025-05-20 12:02:30,571 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-20 12:02:31,093 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-20 12:02:31,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73690.77, 'new_value': 77078.37}, {'field': 'offline_amount', 'old_value': 330663.1, 'new_value': 336644.9}, {'field': 'total_amount', 'old_value': 404353.87, 'new_value': 413723.27}, {'field': 'order_count', 'old_value': 2722, 'new_value': 2845}]
2025-05-20 12:02:31,093 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-20 12:02:31,576 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-20 12:02:31,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38667.81, 'new_value': 42388.89}, {'field': 'total_amount', 'old_value': 66110.53, 'new_value': 69831.61}, {'field': 'order_count', 'old_value': 4310, 'new_value': 4572}]
2025-05-20 12:02:31,576 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-20 12:02:32,163 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-20 12:02:32,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65358.63, 'new_value': 73142.68}, {'field': 'total_amount', 'old_value': 111775.44, 'new_value': 119559.49}, {'field': 'order_count', 'old_value': 7324, 'new_value': 7864}]
2025-05-20 12:02:32,163 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-20 12:02:32,676 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-20 12:02:32,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 865132.06, 'new_value': 904854.39}, {'field': 'total_amount', 'old_value': 865132.06, 'new_value': 904854.39}, {'field': 'order_count', 'old_value': 2551, 'new_value': 2650}]
2025-05-20 12:02:32,677 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-20 12:02:33,220 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-20 12:02:33,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 526921.52, 'new_value': 547590.94}, {'field': 'total_amount', 'old_value': 526921.52, 'new_value': 547590.94}, {'field': 'order_count', 'old_value': 2618, 'new_value': 2762}]
2025-05-20 12:02:33,220 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-20 12:02:33,716 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-20 12:02:33,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 802188.63, 'new_value': 832298.93}, {'field': 'total_amount', 'old_value': 802188.63, 'new_value': 832298.93}, {'field': 'order_count', 'old_value': 2822, 'new_value': 2922}]
2025-05-20 12:02:33,717 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-20 12:02:34,196 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-20 12:02:34,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 573873.3, 'new_value': 600085.4}, {'field': 'total_amount', 'old_value': 573873.3, 'new_value': 600085.4}, {'field': 'order_count', 'old_value': 1775, 'new_value': 1837}]
2025-05-20 12:02:34,197 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-20 12:02:34,613 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-20 12:02:34,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15456.0, 'new_value': 15560.0}, {'field': 'offline_amount', 'old_value': 17856.0, 'new_value': 17960.0}, {'field': 'total_amount', 'old_value': 33312.0, 'new_value': 33520.0}, {'field': 'order_count', 'old_value': 15494, 'new_value': 15598}]
2025-05-20 12:02:34,614 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-20 12:02:35,045 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-20 12:02:35,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1512.4, 'new_value': 1621.2}, {'field': 'offline_amount', 'old_value': 31018.42, 'new_value': 34227.62}, {'field': 'total_amount', 'old_value': 32530.82, 'new_value': 35848.82}, {'field': 'order_count', 'old_value': 243, 'new_value': 266}]
2025-05-20 12:02:35,046 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-20 12:02:35,538 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-20 12:02:35,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235554.0, 'new_value': 244594.0}, {'field': 'total_amount', 'old_value': 240278.0, 'new_value': 249318.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 71}]
2025-05-20 12:02:35,539 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-20 12:02:36,013 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-20 12:02:36,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12142.0, 'new_value': 14841.0}, {'field': 'offline_amount', 'old_value': 39485.0, 'new_value': 45815.0}, {'field': 'total_amount', 'old_value': 51627.0, 'new_value': 60656.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 91}]
2025-05-20 12:02:36,014 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-20 12:02:36,512 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-20 12:02:36,512 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94432.0, 'new_value': 101694.0}, {'field': 'offline_amount', 'old_value': 61680.0, 'new_value': 66029.0}, {'field': 'total_amount', 'old_value': 156112.0, 'new_value': 167723.0}, {'field': 'order_count', 'old_value': 6485, 'new_value': 6993}]
2025-05-20 12:02:36,512 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-20 12:02:36,927 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-20 12:02:36,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84652.0, 'new_value': 89061.0}, {'field': 'total_amount', 'old_value': 84652.0, 'new_value': 89061.0}, {'field': 'order_count', 'old_value': 417, 'new_value': 445}]
2025-05-20 12:02:36,928 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-20 12:02:37,430 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-20 12:02:37,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176685.0, 'new_value': 188385.0}, {'field': 'total_amount', 'old_value': 176685.0, 'new_value': 188385.0}, {'field': 'order_count', 'old_value': 426, 'new_value': 449}]
2025-05-20 12:02:37,430 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-20 12:02:37,934 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-20 12:02:37,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3291.0, 'new_value': 13152.0}, {'field': 'total_amount', 'old_value': 18879.0, 'new_value': 28740.0}]
2025-05-20 12:02:37,934 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-20 12:02:38,385 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-20 12:02:38,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130784.0, 'new_value': 142429.0}, {'field': 'total_amount', 'old_value': 130784.0, 'new_value': 142429.0}, {'field': 'order_count', 'old_value': 13677, 'new_value': 14922}]
2025-05-20 12:02:38,385 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-20 12:02:38,789 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-20 12:02:38,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94610.0, 'new_value': 96421.0}, {'field': 'total_amount', 'old_value': 94610.0, 'new_value': 96421.0}, {'field': 'order_count', 'old_value': 853, 'new_value': 876}]
2025-05-20 12:02:38,789 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-20 12:02:39,239 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-20 12:02:39,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122328.31, 'new_value': 124528.31}, {'field': 'total_amount', 'old_value': 122328.31, 'new_value': 124528.31}, {'field': 'order_count', 'old_value': 1035, 'new_value': 1036}]
2025-05-20 12:02:39,239 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-20 12:02:39,739 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-20 12:02:39,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29136.0, 'new_value': 34816.0}, {'field': 'total_amount', 'old_value': 29136.0, 'new_value': 34816.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-20 12:02:39,739 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-20 12:02:40,171 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-20 12:02:40,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31481.0, 'new_value': 33111.0}, {'field': 'offline_amount', 'old_value': 37748.4, 'new_value': 38406.4}, {'field': 'total_amount', 'old_value': 69229.4, 'new_value': 71517.4}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-05-20 12:02:40,171 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-20 12:02:40,552 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-20 12:02:40,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38154.76, 'new_value': 40312.76}, {'field': 'total_amount', 'old_value': 38154.76, 'new_value': 40312.76}, {'field': 'order_count', 'old_value': 641, 'new_value': 678}]
2025-05-20 12:02:40,552 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-20 12:02:40,986 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-20 12:02:40,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97768.27, 'new_value': 99351.87}, {'field': 'offline_amount', 'old_value': 489278.64, 'new_value': 516239.26}, {'field': 'total_amount', 'old_value': 587046.91, 'new_value': 615591.13}, {'field': 'order_count', 'old_value': 1384, 'new_value': 1436}]
2025-05-20 12:02:40,986 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-20 12:02:41,394 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-20 12:02:41,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58011.6, 'new_value': 64293.6}, {'field': 'total_amount', 'old_value': 58011.6, 'new_value': 64293.6}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-05-20 12:02:41,394 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-20 12:02:41,993 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-20 12:02:41,994 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61654.63, 'new_value': 65868.2}, {'field': 'offline_amount', 'old_value': 603148.88, 'new_value': 637565.99}, {'field': 'total_amount', 'old_value': 662929.18, 'new_value': 701559.86}, {'field': 'order_count', 'old_value': 3178, 'new_value': 3358}]
2025-05-20 12:02:41,994 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-20 12:02:42,442 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-20 12:02:42,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78406.0, 'new_value': 80376.0}, {'field': 'total_amount', 'old_value': 78406.0, 'new_value': 80376.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 274}]
2025-05-20 12:02:42,442 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-20 12:02:42,970 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-20 12:02:42,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53394.0, 'new_value': 60650.0}, {'field': 'total_amount', 'old_value': 58712.0, 'new_value': 65968.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 34}]
2025-05-20 12:02:42,970 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-20 12:02:43,414 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-20 12:02:43,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12762923.04, 'new_value': 13428070.9}, {'field': 'total_amount', 'old_value': 12762923.04, 'new_value': 13428070.9}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-20 12:02:43,414 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-20 12:02:43,830 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-20 12:02:43,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31428.36, 'new_value': 32531.36}, {'field': 'total_amount', 'old_value': 35427.36, 'new_value': 36530.36}, {'field': 'order_count', 'old_value': 2338, 'new_value': 2409}]
2025-05-20 12:02:43,830 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-20 12:02:44,276 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-20 12:02:44,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133859.41, 'new_value': 140604.13}, {'field': 'total_amount', 'old_value': 133859.41, 'new_value': 140604.13}, {'field': 'order_count', 'old_value': 13809, 'new_value': 14550}]
2025-05-20 12:02:44,277 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-20 12:02:44,733 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-20 12:02:44,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15820.0, 'new_value': 16127.0}, {'field': 'total_amount', 'old_value': 15820.0, 'new_value': 16127.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 102}]
2025-05-20 12:02:44,733 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-20 12:02:45,185 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-20 12:02:45,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32122.11, 'new_value': 33466.28}, {'field': 'total_amount', 'old_value': 32122.11, 'new_value': 33466.28}, {'field': 'order_count', 'old_value': 1526, 'new_value': 1616}]
2025-05-20 12:02:45,185 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-20 12:02:45,667 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-20 12:02:45,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6691.0, 'new_value': 20294.0}, {'field': 'total_amount', 'old_value': 6691.0, 'new_value': 20294.0}, {'field': 'order_count', 'old_value': 466, 'new_value': 1059}]
2025-05-20 12:02:45,667 - INFO - 日期 2025-05 处理完成 - 更新: 287 条，插入: 0 条，错误: 0 条
2025-05-20 12:02:45,667 - INFO - 数据同步完成！更新: 287 条，插入: 0 条，错误: 0 条
2025-05-20 12:02:45,669 - INFO - =================同步完成====================
2025-05-20 15:00:02,221 - INFO - =================使用默认全量同步=============
2025-05-20 15:00:03,617 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-20 15:00:03,617 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 15:00:03,646 - INFO - 开始处理日期: 2025-01
2025-05-20 15:00:03,649 - INFO - Request Parameters - Page 1:
2025-05-20 15:00:03,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:03,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:04,525 - INFO - Response - Page 1:
2025-05-20 15:00:04,725 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:00:04,725 - INFO - Request Parameters - Page 2:
2025-05-20 15:00:04,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:04,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:05,732 - INFO - Response - Page 2:
2025-05-20 15:00:05,933 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:00:05,933 - INFO - Request Parameters - Page 3:
2025-05-20 15:00:05,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:05,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:06,534 - INFO - Response - Page 3:
2025-05-20 15:00:06,734 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:00:06,734 - INFO - Request Parameters - Page 4:
2025-05-20 15:00:06,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:06,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:07,268 - INFO - Response - Page 4:
2025-05-20 15:00:07,468 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:00:07,468 - INFO - Request Parameters - Page 5:
2025-05-20 15:00:07,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:07,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:08,132 - INFO - Response - Page 5:
2025-05-20 15:00:08,332 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:00:08,332 - INFO - Request Parameters - Page 6:
2025-05-20 15:00:08,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:08,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:08,830 - INFO - Response - Page 6:
2025-05-20 15:00:09,031 - INFO - 第 6 页获取到 100 条记录
2025-05-20 15:00:09,031 - INFO - Request Parameters - Page 7:
2025-05-20 15:00:09,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:09,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:09,535 - INFO - Response - Page 7:
2025-05-20 15:00:09,735 - INFO - 第 7 页获取到 82 条记录
2025-05-20 15:00:09,735 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 15:00:09,736 - INFO - 获取到 682 条表单数据
2025-05-20 15:00:09,751 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 15:00:09,767 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 15:00:09,767 - INFO - 开始处理日期: 2025-02
2025-05-20 15:00:09,767 - INFO - Request Parameters - Page 1:
2025-05-20 15:00:09,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:09,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:10,308 - INFO - Response - Page 1:
2025-05-20 15:00:10,509 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:00:10,509 - INFO - Request Parameters - Page 2:
2025-05-20 15:00:10,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:10,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:11,115 - INFO - Response - Page 2:
2025-05-20 15:00:11,315 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:00:11,315 - INFO - Request Parameters - Page 3:
2025-05-20 15:00:11,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:11,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:11,904 - INFO - Response - Page 3:
2025-05-20 15:00:12,105 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:00:12,105 - INFO - Request Parameters - Page 4:
2025-05-20 15:00:12,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:12,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:12,607 - INFO - Response - Page 4:
2025-05-20 15:00:12,807 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:00:12,807 - INFO - Request Parameters - Page 5:
2025-05-20 15:00:12,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:12,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:13,370 - INFO - Response - Page 5:
2025-05-20 15:00:13,571 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:00:13,571 - INFO - Request Parameters - Page 6:
2025-05-20 15:00:13,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:13,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:14,033 - INFO - Response - Page 6:
2025-05-20 15:00:14,233 - INFO - 第 6 页获取到 100 条记录
2025-05-20 15:00:14,233 - INFO - Request Parameters - Page 7:
2025-05-20 15:00:14,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:14,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:14,688 - INFO - Response - Page 7:
2025-05-20 15:00:14,888 - INFO - 第 7 页获取到 70 条记录
2025-05-20 15:00:14,888 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 15:00:14,888 - INFO - 获取到 670 条表单数据
2025-05-20 15:00:14,905 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 15:00:14,921 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 15:00:14,922 - INFO - 开始处理日期: 2025-03
2025-05-20 15:00:14,922 - INFO - Request Parameters - Page 1:
2025-05-20 15:00:14,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:14,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:15,435 - INFO - Response - Page 1:
2025-05-20 15:00:15,635 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:00:15,635 - INFO - Request Parameters - Page 2:
2025-05-20 15:00:15,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:15,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:16,251 - INFO - Response - Page 2:
2025-05-20 15:00:16,451 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:00:16,451 - INFO - Request Parameters - Page 3:
2025-05-20 15:00:16,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:16,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:16,933 - INFO - Response - Page 3:
2025-05-20 15:00:17,133 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:00:17,133 - INFO - Request Parameters - Page 4:
2025-05-20 15:00:17,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:17,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:17,648 - INFO - Response - Page 4:
2025-05-20 15:00:17,848 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:00:17,848 - INFO - Request Parameters - Page 5:
2025-05-20 15:00:17,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:17,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:18,359 - INFO - Response - Page 5:
2025-05-20 15:00:18,560 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:00:18,560 - INFO - Request Parameters - Page 6:
2025-05-20 15:00:18,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:18,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:19,073 - INFO - Response - Page 6:
2025-05-20 15:00:19,274 - INFO - 第 6 页获取到 100 条记录
2025-05-20 15:00:19,274 - INFO - Request Parameters - Page 7:
2025-05-20 15:00:19,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:19,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:19,736 - INFO - Response - Page 7:
2025-05-20 15:00:19,936 - INFO - 第 7 页获取到 61 条记录
2025-05-20 15:00:19,936 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 15:00:19,936 - INFO - 获取到 661 条表单数据
2025-05-20 15:00:19,948 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 15:00:19,959 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 15:00:19,960 - INFO - 开始处理日期: 2025-04
2025-05-20 15:00:19,960 - INFO - Request Parameters - Page 1:
2025-05-20 15:00:19,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:19,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:20,595 - INFO - Response - Page 1:
2025-05-20 15:00:20,795 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:00:20,795 - INFO - Request Parameters - Page 2:
2025-05-20 15:00:20,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:20,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:21,277 - INFO - Response - Page 2:
2025-05-20 15:00:21,477 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:00:21,477 - INFO - Request Parameters - Page 3:
2025-05-20 15:00:21,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:21,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:22,021 - INFO - Response - Page 3:
2025-05-20 15:00:22,221 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:00:22,221 - INFO - Request Parameters - Page 4:
2025-05-20 15:00:22,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:22,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:22,736 - INFO - Response - Page 4:
2025-05-20 15:00:22,937 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:00:22,937 - INFO - Request Parameters - Page 5:
2025-05-20 15:00:22,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:22,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:23,446 - INFO - Response - Page 5:
2025-05-20 15:00:23,646 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:00:23,646 - INFO - Request Parameters - Page 6:
2025-05-20 15:00:23,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:23,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:24,134 - INFO - Response - Page 6:
2025-05-20 15:00:24,335 - INFO - 第 6 页获取到 100 条记录
2025-05-20 15:00:24,335 - INFO - Request Parameters - Page 7:
2025-05-20 15:00:24,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:24,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:24,799 - INFO - Response - Page 7:
2025-05-20 15:00:25,000 - INFO - 第 7 页获取到 56 条记录
2025-05-20 15:00:25,000 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 15:00:25,001 - INFO - 获取到 656 条表单数据
2025-05-20 15:00:25,013 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 15:00:25,025 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 15:00:25,025 - INFO - 开始处理日期: 2025-05
2025-05-20 15:00:25,026 - INFO - Request Parameters - Page 1:
2025-05-20 15:00:25,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:25,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:25,523 - INFO - Response - Page 1:
2025-05-20 15:00:25,723 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:00:25,723 - INFO - Request Parameters - Page 2:
2025-05-20 15:00:25,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:25,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:26,151 - INFO - Response - Page 2:
2025-05-20 15:00:26,352 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:00:26,352 - INFO - Request Parameters - Page 3:
2025-05-20 15:00:26,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:26,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:26,829 - INFO - Response - Page 3:
2025-05-20 15:00:27,030 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:00:27,030 - INFO - Request Parameters - Page 4:
2025-05-20 15:00:27,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:27,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:27,488 - INFO - Response - Page 4:
2025-05-20 15:00:27,689 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:00:27,689 - INFO - Request Parameters - Page 5:
2025-05-20 15:00:27,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:27,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:28,155 - INFO - Response - Page 5:
2025-05-20 15:00:28,356 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:00:28,356 - INFO - Request Parameters - Page 6:
2025-05-20 15:00:28,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:28,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:28,842 - INFO - Response - Page 6:
2025-05-20 15:00:29,042 - INFO - 第 6 页获取到 100 条记录
2025-05-20 15:00:29,042 - INFO - Request Parameters - Page 7:
2025-05-20 15:00:29,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:00:29,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:00:29,414 - INFO - Response - Page 7:
2025-05-20 15:00:29,615 - INFO - 第 7 页获取到 28 条记录
2025-05-20 15:00:29,615 - INFO - 查询完成，共获取到 628 条记录
2025-05-20 15:00:29,615 - INFO - 获取到 628 条表单数据
2025-05-20 15:00:29,628 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-20 15:00:29,629 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-20 15:00:30,107 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-20 15:00:30,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45255.83, 'new_value': 48611.17}, {'field': 'total_amount', 'old_value': 49844.55, 'new_value': 53199.89}, {'field': 'order_count', 'old_value': 1766, 'new_value': 1906}]
2025-05-20 15:00:30,108 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-20 15:00:30,529 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-20 15:00:30,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23839.03, 'new_value': 26339.93}, {'field': 'offline_amount', 'old_value': 67420.24, 'new_value': 69280.94}, {'field': 'total_amount', 'old_value': 91259.27, 'new_value': 95620.87}, {'field': 'order_count', 'old_value': 2083, 'new_value': 2186}]
2025-05-20 15:00:30,530 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-20 15:00:30,986 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-20 15:00:30,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55610.0, 'new_value': 56972.0}, {'field': 'total_amount', 'old_value': 55610.0, 'new_value': 56972.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 94}]
2025-05-20 15:00:30,987 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-20 15:00:31,446 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-20 15:00:31,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179999.21, 'new_value': 185174.11}, {'field': 'offline_amount', 'old_value': 13479.15, 'new_value': 14213.15}, {'field': 'total_amount', 'old_value': 193478.36, 'new_value': 199387.26}, {'field': 'order_count', 'old_value': 4013, 'new_value': 4218}]
2025-05-20 15:00:31,446 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-20 15:00:31,911 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-20 15:00:31,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73761.93, 'new_value': 76159.79}, {'field': 'total_amount', 'old_value': 73761.93, 'new_value': 76159.79}, {'field': 'order_count', 'old_value': 2777, 'new_value': 2873}]
2025-05-20 15:00:31,912 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-20 15:00:32,465 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-20 15:00:32,465 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71003.88, 'new_value': 75658.55}, {'field': 'offline_amount', 'old_value': 41672.95, 'new_value': 42361.97}, {'field': 'total_amount', 'old_value': 112676.83, 'new_value': 118020.52}, {'field': 'order_count', 'old_value': 6367, 'new_value': 6654}]
2025-05-20 15:00:32,466 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-20 15:00:32,919 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-20 15:00:32,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79667.51, 'new_value': 86018.7}, {'field': 'offline_amount', 'old_value': 66071.45, 'new_value': 71196.1}, {'field': 'total_amount', 'old_value': 145738.96, 'new_value': 157214.8}, {'field': 'order_count', 'old_value': 5212, 'new_value': 5656}]
2025-05-20 15:00:32,919 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-20 15:00:33,357 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-20 15:00:33,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52384.65, 'new_value': 55465.81}, {'field': 'total_amount', 'old_value': 52384.65, 'new_value': 55465.81}, {'field': 'order_count', 'old_value': 2399, 'new_value': 2537}]
2025-05-20 15:00:33,358 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-20 15:00:33,803 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-20 15:00:33,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157798.15, 'new_value': 168002.62}, {'field': 'total_amount', 'old_value': 164713.81, 'new_value': 174918.28}, {'field': 'order_count', 'old_value': 3388, 'new_value': 3636}]
2025-05-20 15:00:33,803 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-20 15:00:34,212 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-20 15:00:34,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 371679.53, 'new_value': 390182.79}, {'field': 'total_amount', 'old_value': 371679.53, 'new_value': 390182.79}, {'field': 'order_count', 'old_value': 3744, 'new_value': 3933}]
2025-05-20 15:00:34,214 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-20 15:00:34,689 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-20 15:00:34,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1993.35, 'new_value': 2033.66}, {'field': 'offline_amount', 'old_value': 18323.56, 'new_value': 18808.42}, {'field': 'total_amount', 'old_value': 20316.91, 'new_value': 20842.08}, {'field': 'order_count', 'old_value': 923, 'new_value': 947}]
2025-05-20 15:00:34,690 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-20 15:00:35,314 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-20 15:00:35,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 669281.16, 'new_value': 690123.16}, {'field': 'total_amount', 'old_value': 669281.16, 'new_value': 690123.16}, {'field': 'order_count', 'old_value': 2583, 'new_value': 2698}]
2025-05-20 15:00:35,315 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-20 15:00:35,806 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-20 15:00:35,807 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23764.5, 'new_value': 23914.0}, {'field': 'offline_amount', 'old_value': 193319.0, 'new_value': 197097.9}, {'field': 'total_amount', 'old_value': 217083.5, 'new_value': 221011.9}, {'field': 'order_count', 'old_value': 1712, 'new_value': 1758}]
2025-05-20 15:00:35,807 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-20 15:00:37,380 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-20 15:00:37,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101822.08, 'new_value': 107245.08}, {'field': 'total_amount', 'old_value': 101822.08, 'new_value': 107245.08}, {'field': 'order_count', 'old_value': 9248, 'new_value': 9699}]
2025-05-20 15:00:37,381 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-20 15:00:37,873 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-20 15:00:37,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43109.68, 'new_value': 44825.88}, {'field': 'offline_amount', 'old_value': 180966.03, 'new_value': 189225.63}, {'field': 'total_amount', 'old_value': 224075.71, 'new_value': 234051.51}, {'field': 'order_count', 'old_value': 6894, 'new_value': 7256}]
2025-05-20 15:00:37,874 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-20 15:00:38,358 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-20 15:00:38,358 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31208.3, 'new_value': 34473.3}, {'field': 'offline_amount', 'old_value': 22417.64, 'new_value': 23549.64}, {'field': 'total_amount', 'old_value': 53625.94, 'new_value': 58022.94}, {'field': 'order_count', 'old_value': 7371, 'new_value': 7712}]
2025-05-20 15:00:38,358 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-20 15:00:38,860 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-20 15:00:38,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82370.99, 'new_value': 85109.72}, {'field': 'offline_amount', 'old_value': 239174.62, 'new_value': 245166.42}, {'field': 'total_amount', 'old_value': 321545.61, 'new_value': 330276.14}, {'field': 'order_count', 'old_value': 3844, 'new_value': 4022}]
2025-05-20 15:00:38,861 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-20 15:00:39,347 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-20 15:00:39,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19920.87, 'new_value': 24342.2}, {'field': 'offline_amount', 'old_value': 185336.7, 'new_value': 188772.66}, {'field': 'total_amount', 'old_value': 205257.57, 'new_value': 213114.86}, {'field': 'order_count', 'old_value': 3034, 'new_value': 3340}]
2025-05-20 15:00:39,347 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-20 15:00:39,797 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-20 15:00:39,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32707.0, 'new_value': 32807.0}, {'field': 'total_amount', 'old_value': 32707.0, 'new_value': 32807.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 179}]
2025-05-20 15:00:39,797 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-20 15:00:40,306 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-20 15:00:40,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227741.93, 'new_value': 235396.74}, {'field': 'total_amount', 'old_value': 260822.14, 'new_value': 268476.95}, {'field': 'order_count', 'old_value': 10933, 'new_value': 11292}]
2025-05-20 15:00:40,307 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-20 15:00:40,838 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-20 15:00:40,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78027.19, 'new_value': 81821.05}, {'field': 'total_amount', 'old_value': 78027.19, 'new_value': 81821.05}, {'field': 'order_count', 'old_value': 2229, 'new_value': 2348}]
2025-05-20 15:00:40,838 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-20 15:00:41,392 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-20 15:00:41,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139115.46, 'new_value': 149104.59}, {'field': 'total_amount', 'old_value': 139115.46, 'new_value': 149104.59}, {'field': 'order_count', 'old_value': 5820, 'new_value': 6278}]
2025-05-20 15:00:41,393 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-20 15:00:41,825 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-20 15:00:41,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26285.09, 'new_value': 29001.7}, {'field': 'offline_amount', 'old_value': 104686.09, 'new_value': 106795.14}, {'field': 'total_amount', 'old_value': 130971.18, 'new_value': 135796.84}, {'field': 'order_count', 'old_value': 7341, 'new_value': 7616}]
2025-05-20 15:00:41,826 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-20 15:00:42,301 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-20 15:00:42,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25420.05, 'new_value': 26222.79}, {'field': 'offline_amount', 'old_value': 45938.54, 'new_value': 48783.86}, {'field': 'total_amount', 'old_value': 71358.59, 'new_value': 75006.65}, {'field': 'order_count', 'old_value': 607, 'new_value': 639}]
2025-05-20 15:00:42,302 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-20 15:00:42,754 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-20 15:00:42,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60750.0, 'new_value': 62390.0}, {'field': 'total_amount', 'old_value': 60750.0, 'new_value': 62390.0}, {'field': 'order_count', 'old_value': 429, 'new_value': 443}]
2025-05-20 15:00:42,755 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-20 15:00:43,260 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-20 15:00:43,261 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6045.9, 'new_value': 6338.87}, {'field': 'offline_amount', 'old_value': 93805.1, 'new_value': 98024.0}, {'field': 'total_amount', 'old_value': 99851.0, 'new_value': 104362.87}, {'field': 'order_count', 'old_value': 5439, 'new_value': 5637}]
2025-05-20 15:00:43,261 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-20 15:00:43,780 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-20 15:00:43,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12913.08, 'new_value': 13475.05}, {'field': 'offline_amount', 'old_value': 106032.17, 'new_value': 108402.41}, {'field': 'total_amount', 'old_value': 118945.25, 'new_value': 121877.46}, {'field': 'order_count', 'old_value': 3513, 'new_value': 3612}]
2025-05-20 15:00:43,781 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-20 15:00:44,378 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-20 15:00:44,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77280.4, 'new_value': 80570.5}, {'field': 'total_amount', 'old_value': 77280.4, 'new_value': 80570.5}, {'field': 'order_count', 'old_value': 3795, 'new_value': 3956}]
2025-05-20 15:00:44,379 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-20 15:00:44,880 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-20 15:00:44,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 672027.0, 'new_value': 708595.0}, {'field': 'total_amount', 'old_value': 672027.0, 'new_value': 708595.0}, {'field': 'order_count', 'old_value': 1443, 'new_value': 1525}]
2025-05-20 15:00:44,881 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-20 15:00:45,386 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-20 15:00:45,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182996.46, 'new_value': 205082.46}, {'field': 'total_amount', 'old_value': 182996.46, 'new_value': 205082.46}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-05-20 15:00:45,387 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-20 15:00:45,824 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-20 15:00:45,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73108.0, 'new_value': 77755.0}, {'field': 'total_amount', 'old_value': 73108.0, 'new_value': 77755.0}, {'field': 'order_count', 'old_value': 1749, 'new_value': 1880}]
2025-05-20 15:00:45,827 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-20 15:00:46,336 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-20 15:00:46,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79687.4, 'new_value': 82195.1}, {'field': 'total_amount', 'old_value': 79687.4, 'new_value': 82195.1}, {'field': 'order_count', 'old_value': 382, 'new_value': 395}]
2025-05-20 15:00:46,338 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-20 15:00:46,837 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-20 15:00:46,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87532.0, 'new_value': 94697.0}, {'field': 'total_amount', 'old_value': 87612.0, 'new_value': 94777.0}, {'field': 'order_count', 'old_value': 8340, 'new_value': 9155}]
2025-05-20 15:00:46,838 - INFO - 日期 2025-05 处理完成 - 更新: 33 条，插入: 0 条，错误: 0 条
2025-05-20 15:00:46,838 - INFO - 数据同步完成！更新: 33 条，插入: 0 条，错误: 0 条
2025-05-20 15:00:46,840 - INFO - =================同步完成====================
2025-05-20 18:00:02,122 - INFO - =================使用默认全量同步=============
2025-05-20 18:00:03,535 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-20 18:00:03,536 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 18:00:03,564 - INFO - 开始处理日期: 2025-01
2025-05-20 18:00:03,567 - INFO - Request Parameters - Page 1:
2025-05-20 18:00:03,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:03,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:04,932 - INFO - Response - Page 1:
2025-05-20 18:00:05,133 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:00:05,133 - INFO - Request Parameters - Page 2:
2025-05-20 18:00:05,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:05,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:05,828 - INFO - Response - Page 2:
2025-05-20 18:00:06,028 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:00:06,028 - INFO - Request Parameters - Page 3:
2025-05-20 18:00:06,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:06,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:06,584 - INFO - Response - Page 3:
2025-05-20 18:00:06,785 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:00:06,785 - INFO - Request Parameters - Page 4:
2025-05-20 18:00:06,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:06,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:07,334 - INFO - Response - Page 4:
2025-05-20 18:00:07,534 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:00:07,534 - INFO - Request Parameters - Page 5:
2025-05-20 18:00:07,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:07,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:08,050 - INFO - Response - Page 5:
2025-05-20 18:00:08,250 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:00:08,250 - INFO - Request Parameters - Page 6:
2025-05-20 18:00:08,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:08,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:08,793 - INFO - Response - Page 6:
2025-05-20 18:00:08,993 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:00:08,993 - INFO - Request Parameters - Page 7:
2025-05-20 18:00:08,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:08,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:09,486 - INFO - Response - Page 7:
2025-05-20 18:00:09,687 - INFO - 第 7 页获取到 82 条记录
2025-05-20 18:00:09,687 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 18:00:09,687 - INFO - 获取到 682 条表单数据
2025-05-20 18:00:09,700 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 18:00:09,712 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:00:09,712 - INFO - 开始处理日期: 2025-02
2025-05-20 18:00:09,713 - INFO - Request Parameters - Page 1:
2025-05-20 18:00:09,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:09,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:10,234 - INFO - Response - Page 1:
2025-05-20 18:00:10,435 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:00:10,435 - INFO - Request Parameters - Page 2:
2025-05-20 18:00:10,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:10,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:10,998 - INFO - Response - Page 2:
2025-05-20 18:00:11,199 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:00:11,199 - INFO - Request Parameters - Page 3:
2025-05-20 18:00:11,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:11,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:11,739 - INFO - Response - Page 3:
2025-05-20 18:00:11,939 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:00:11,939 - INFO - Request Parameters - Page 4:
2025-05-20 18:00:11,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:11,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:12,657 - INFO - Response - Page 4:
2025-05-20 18:00:12,857 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:00:12,857 - INFO - Request Parameters - Page 5:
2025-05-20 18:00:12,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:12,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:13,378 - INFO - Response - Page 5:
2025-05-20 18:00:13,578 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:00:13,578 - INFO - Request Parameters - Page 6:
2025-05-20 18:00:13,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:13,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:14,109 - INFO - Response - Page 6:
2025-05-20 18:00:14,309 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:00:14,309 - INFO - Request Parameters - Page 7:
2025-05-20 18:00:14,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:14,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:14,801 - INFO - Response - Page 7:
2025-05-20 18:00:15,001 - INFO - 第 7 页获取到 70 条记录
2025-05-20 18:00:15,001 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 18:00:15,001 - INFO - 获取到 670 条表单数据
2025-05-20 18:00:15,012 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 18:00:15,024 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:00:15,024 - INFO - 开始处理日期: 2025-03
2025-05-20 18:00:15,024 - INFO - Request Parameters - Page 1:
2025-05-20 18:00:15,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:15,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:15,610 - INFO - Response - Page 1:
2025-05-20 18:00:15,810 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:00:15,810 - INFO - Request Parameters - Page 2:
2025-05-20 18:00:15,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:15,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:16,358 - INFO - Response - Page 2:
2025-05-20 18:00:16,558 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:00:16,558 - INFO - Request Parameters - Page 3:
2025-05-20 18:00:16,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:16,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:17,072 - INFO - Response - Page 3:
2025-05-20 18:00:17,272 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:00:17,272 - INFO - Request Parameters - Page 4:
2025-05-20 18:00:17,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:17,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:17,838 - INFO - Response - Page 4:
2025-05-20 18:00:18,038 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:00:18,038 - INFO - Request Parameters - Page 5:
2025-05-20 18:00:18,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:18,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:18,551 - INFO - Response - Page 5:
2025-05-20 18:00:18,753 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:00:18,753 - INFO - Request Parameters - Page 6:
2025-05-20 18:00:18,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:18,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:19,270 - INFO - Response - Page 6:
2025-05-20 18:00:19,471 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:00:19,471 - INFO - Request Parameters - Page 7:
2025-05-20 18:00:19,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:19,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:19,899 - INFO - Response - Page 7:
2025-05-20 18:00:20,099 - INFO - 第 7 页获取到 61 条记录
2025-05-20 18:00:20,099 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 18:00:20,099 - INFO - 获取到 661 条表单数据
2025-05-20 18:00:20,112 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 18:00:20,124 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:00:20,124 - INFO - 开始处理日期: 2025-04
2025-05-20 18:00:20,124 - INFO - Request Parameters - Page 1:
2025-05-20 18:00:20,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:20,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:20,695 - INFO - Response - Page 1:
2025-05-20 18:00:20,895 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:00:20,895 - INFO - Request Parameters - Page 2:
2025-05-20 18:00:20,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:20,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:21,375 - INFO - Response - Page 2:
2025-05-20 18:00:21,575 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:00:21,575 - INFO - Request Parameters - Page 3:
2025-05-20 18:00:21,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:21,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:22,160 - INFO - Response - Page 3:
2025-05-20 18:00:22,360 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:00:22,360 - INFO - Request Parameters - Page 4:
2025-05-20 18:00:22,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:22,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:22,896 - INFO - Response - Page 4:
2025-05-20 18:00:23,096 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:00:23,096 - INFO - Request Parameters - Page 5:
2025-05-20 18:00:23,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:23,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:23,620 - INFO - Response - Page 5:
2025-05-20 18:00:23,821 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:00:23,821 - INFO - Request Parameters - Page 6:
2025-05-20 18:00:23,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:23,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:24,369 - INFO - Response - Page 6:
2025-05-20 18:00:24,569 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:00:24,569 - INFO - Request Parameters - Page 7:
2025-05-20 18:00:24,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:24,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:25,018 - INFO - Response - Page 7:
2025-05-20 18:00:25,218 - INFO - 第 7 页获取到 56 条记录
2025-05-20 18:00:25,218 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 18:00:25,218 - INFO - 获取到 656 条表单数据
2025-05-20 18:00:25,232 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 18:00:25,244 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:00:25,244 - INFO - 开始处理日期: 2025-05
2025-05-20 18:00:25,244 - INFO - Request Parameters - Page 1:
2025-05-20 18:00:25,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:25,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:25,753 - INFO - Response - Page 1:
2025-05-20 18:00:25,953 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:00:25,953 - INFO - Request Parameters - Page 2:
2025-05-20 18:00:25,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:25,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:26,585 - INFO - Response - Page 2:
2025-05-20 18:00:26,786 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:00:26,786 - INFO - Request Parameters - Page 3:
2025-05-20 18:00:26,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:26,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:27,333 - INFO - Response - Page 3:
2025-05-20 18:00:27,533 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:00:27,533 - INFO - Request Parameters - Page 4:
2025-05-20 18:00:27,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:27,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:28,060 - INFO - Response - Page 4:
2025-05-20 18:00:28,260 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:00:28,260 - INFO - Request Parameters - Page 5:
2025-05-20 18:00:28,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:28,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:28,739 - INFO - Response - Page 5:
2025-05-20 18:00:28,939 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:00:28,939 - INFO - Request Parameters - Page 6:
2025-05-20 18:00:28,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:28,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:29,503 - INFO - Response - Page 6:
2025-05-20 18:00:29,703 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:00:29,703 - INFO - Request Parameters - Page 7:
2025-05-20 18:00:29,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:00:29,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:00:30,075 - INFO - Response - Page 7:
2025-05-20 18:00:30,275 - INFO - 第 7 页获取到 28 条记录
2025-05-20 18:00:30,275 - INFO - 查询完成，共获取到 628 条记录
2025-05-20 18:00:30,275 - INFO - 获取到 628 条表单数据
2025-05-20 18:00:30,289 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-20 18:00:30,289 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-20 18:00:30,829 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-20 18:00:30,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19287.3, 'new_value': 21311.5}, {'field': 'total_amount', 'old_value': 19287.3, 'new_value': 21311.5}, {'field': 'order_count', 'old_value': 141, 'new_value': 151}]
2025-05-20 18:00:30,830 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-20 18:00:31,300 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-20 18:00:31,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3145.47, 'new_value': 3310.0}, {'field': 'offline_amount', 'old_value': 63325.59, 'new_value': 66253.51}, {'field': 'total_amount', 'old_value': 66471.06, 'new_value': 69563.51}, {'field': 'order_count', 'old_value': 2529, 'new_value': 2673}]
2025-05-20 18:00:31,301 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-20 18:00:31,757 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-20 18:00:31,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33707.35, 'new_value': 35969.02}, {'field': 'offline_amount', 'old_value': 40327.32, 'new_value': 41109.98}, {'field': 'total_amount', 'old_value': 74034.67, 'new_value': 77079.0}, {'field': 'order_count', 'old_value': 3764, 'new_value': 3977}]
2025-05-20 18:00:31,758 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-20 18:00:32,202 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-20 18:00:32,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7047.02, 'new_value': 7386.4}, {'field': 'offline_amount', 'old_value': 94779.77, 'new_value': 98344.34}, {'field': 'total_amount', 'old_value': 101826.79, 'new_value': 105730.74}, {'field': 'order_count', 'old_value': 1622, 'new_value': 1700}]
2025-05-20 18:00:32,204 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-20 18:00:32,694 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-20 18:00:32,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142919.0, 'new_value': 149832.0}, {'field': 'total_amount', 'old_value': 142919.0, 'new_value': 149832.0}, {'field': 'order_count', 'old_value': 10213, 'new_value': 10394}]
2025-05-20 18:00:32,696 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-20 18:00:33,153 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-20 18:00:33,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98361.1, 'new_value': 96957.1}, {'field': 'total_amount', 'old_value': 98361.1, 'new_value': 96957.1}, {'field': 'order_count', 'old_value': 978, 'new_value': 990}]
2025-05-20 18:00:33,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-20 18:00:33,570 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-20 18:00:33,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4882459.0, 'new_value': 4899527.0}, {'field': 'total_amount', 'old_value': 4882459.0, 'new_value': 4899527.0}, {'field': 'order_count', 'old_value': 78450, 'new_value': 82227}]
2025-05-20 18:00:33,570 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-20 18:00:34,028 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-20 18:00:34,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67983.74, 'new_value': 71299.74}, {'field': 'total_amount', 'old_value': 67983.74, 'new_value': 71299.74}, {'field': 'order_count', 'old_value': 1915, 'new_value': 1957}]
2025-05-20 18:00:34,028 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-20 18:00:34,524 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-20 18:00:34,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91742.0, 'new_value': 90927.0}, {'field': 'total_amount', 'old_value': 91742.0, 'new_value': 90927.0}, {'field': 'order_count', 'old_value': 3254, 'new_value': 3367}]
2025-05-20 18:00:34,524 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-20 18:00:34,931 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-20 18:00:34,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23298.07, 'new_value': 23576.07}, {'field': 'total_amount', 'old_value': 23298.07, 'new_value': 23576.07}, {'field': 'order_count', 'old_value': 2189, 'new_value': 2296}]
2025-05-20 18:00:34,932 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-20 18:00:35,354 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-20 18:00:35,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5016100.0, 'new_value': 5030100.0}, {'field': 'total_amount', 'old_value': 5016100.0, 'new_value': 5030100.0}]
2025-05-20 18:00:35,354 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-20 18:00:36,003 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-20 18:00:36,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35584.5, 'new_value': 37191.5}, {'field': 'total_amount', 'old_value': 35584.5, 'new_value': 37191.5}, {'field': 'order_count', 'old_value': 1797, 'new_value': 1879}]
2025-05-20 18:00:36,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-20 18:00:36,429 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-20 18:00:36,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372259.0, 'new_value': 375439.0}, {'field': 'total_amount', 'old_value': 381077.99, 'new_value': 384257.99}]
2025-05-20 18:00:36,430 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-20 18:00:36,900 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-20 18:00:36,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27468.0, 'new_value': 29948.0}, {'field': 'offline_amount', 'old_value': 207397.0, 'new_value': 219697.0}, {'field': 'total_amount', 'old_value': 234865.0, 'new_value': 249645.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 223}]
2025-05-20 18:00:36,901 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-20 18:00:37,414 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-20 18:00:37,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336371.25, 'new_value': 321664.79}, {'field': 'total_amount', 'old_value': 336371.25, 'new_value': 321664.79}, {'field': 'order_count', 'old_value': 602, 'new_value': 623}]
2025-05-20 18:00:37,415 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-20 18:00:37,972 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-20 18:00:37,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59388.0, 'new_value': 65086.0}, {'field': 'total_amount', 'old_value': 59388.0, 'new_value': 65086.0}]
2025-05-20 18:00:37,972 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-20 18:00:38,435 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-20 18:00:38,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24544.0, 'new_value': 24718.0}, {'field': 'total_amount', 'old_value': 25920.0, 'new_value': 26094.0}, {'field': 'order_count', 'old_value': 2600, 'new_value': 2725}]
2025-05-20 18:00:38,436 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-20 18:00:38,874 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-20 18:00:38,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90867.21, 'new_value': 93862.49}, {'field': 'total_amount', 'old_value': 166939.65, 'new_value': 169934.93}, {'field': 'order_count', 'old_value': 6957, 'new_value': 7256}]
2025-05-20 18:00:38,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-20 18:00:39,308 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-20 18:00:39,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308372.75, 'new_value': 312411.43}, {'field': 'total_amount', 'old_value': 308372.75, 'new_value': 312411.43}, {'field': 'order_count', 'old_value': 2135, 'new_value': 2220}]
2025-05-20 18:00:39,308 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-20 18:00:39,733 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-20 18:00:39,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80319.0, 'new_value': 78556.0}, {'field': 'total_amount', 'old_value': 80319.0, 'new_value': 78556.0}, {'field': 'order_count', 'old_value': 516, 'new_value': 534}]
2025-05-20 18:00:39,734 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-20 18:00:40,285 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-20 18:00:40,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140163.8, 'new_value': 144214.78}, {'field': 'total_amount', 'old_value': 140163.8, 'new_value': 144214.78}, {'field': 'order_count', 'old_value': 1804, 'new_value': 1858}]
2025-05-20 18:00:40,285 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-20 18:00:40,864 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-20 18:00:40,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18877.0, 'new_value': 19174.0}, {'field': 'total_amount', 'old_value': 18877.0, 'new_value': 19174.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-20 18:00:40,865 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-20 18:00:41,364 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-20 18:00:41,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225116.5, 'new_value': 245668.5}, {'field': 'total_amount', 'old_value': 225116.5, 'new_value': 245668.5}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-05-20 18:00:41,364 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-20 18:00:41,767 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-20 18:00:41,767 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9373.0, 'new_value': 11372.0}, {'field': 'offline_amount', 'old_value': 6210.0, 'new_value': 6709.0}, {'field': 'total_amount', 'old_value': 15583.0, 'new_value': 18081.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 63}]
2025-05-20 18:00:41,769 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-20 18:00:42,234 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-20 18:00:42,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18883.31, 'new_value': 19847.24}, {'field': 'offline_amount', 'old_value': 21646.08, 'new_value': 22343.38}, {'field': 'total_amount', 'old_value': 40529.39, 'new_value': 42190.62}, {'field': 'order_count', 'old_value': 1931, 'new_value': 2016}]
2025-05-20 18:00:42,235 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-20 18:00:42,698 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-20 18:00:42,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232884.0, 'new_value': 248394.0}, {'field': 'total_amount', 'old_value': 232884.0, 'new_value': 248394.0}, {'field': 'order_count', 'old_value': 4809, 'new_value': 5159}]
2025-05-20 18:00:42,700 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-20 18:00:43,166 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-20 18:00:43,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89061.0, 'new_value': 88124.0}, {'field': 'total_amount', 'old_value': 89061.0, 'new_value': 88124.0}, {'field': 'order_count', 'old_value': 445, 'new_value': 451}]
2025-05-20 18:00:43,166 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-20 18:00:43,617 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-20 18:00:43,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124528.31, 'new_value': 124508.16}, {'field': 'total_amount', 'old_value': 124528.31, 'new_value': 124508.16}, {'field': 'order_count', 'old_value': 1036, 'new_value': 1061}]
2025-05-20 18:00:43,618 - INFO - 日期 2025-05 处理完成 - 更新: 28 条，插入: 0 条，错误: 0 条
2025-05-20 18:00:43,618 - INFO - 数据同步完成！更新: 28 条，插入: 0 条，错误: 0 条
2025-05-20 18:00:43,620 - INFO - =================同步完成====================
2025-05-20 21:00:02,065 - INFO - =================使用默认全量同步=============
2025-05-20 21:00:03,486 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-20 21:00:03,486 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-20 21:00:03,513 - INFO - 开始处理日期: 2025-01
2025-05-20 21:00:03,516 - INFO - Request Parameters - Page 1:
2025-05-20 21:00:03,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:03,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:04,728 - INFO - Response - Page 1:
2025-05-20 21:00:04,928 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:00:04,928 - INFO - Request Parameters - Page 2:
2025-05-20 21:00:04,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:04,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:05,481 - INFO - Response - Page 2:
2025-05-20 21:00:05,681 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:00:05,681 - INFO - Request Parameters - Page 3:
2025-05-20 21:00:05,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:05,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:06,305 - INFO - Response - Page 3:
2025-05-20 21:00:06,506 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:00:06,506 - INFO - Request Parameters - Page 4:
2025-05-20 21:00:06,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:06,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:07,092 - INFO - Response - Page 4:
2025-05-20 21:00:07,293 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:00:07,293 - INFO - Request Parameters - Page 5:
2025-05-20 21:00:07,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:07,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:07,751 - INFO - Response - Page 5:
2025-05-20 21:00:07,951 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:00:07,951 - INFO - Request Parameters - Page 6:
2025-05-20 21:00:07,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:07,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:08,455 - INFO - Response - Page 6:
2025-05-20 21:00:08,656 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:00:08,656 - INFO - Request Parameters - Page 7:
2025-05-20 21:00:08,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:08,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:09,099 - INFO - Response - Page 7:
2025-05-20 21:00:09,300 - INFO - 第 7 页获取到 82 条记录
2025-05-20 21:00:09,300 - INFO - 查询完成，共获取到 682 条记录
2025-05-20 21:00:09,300 - INFO - 获取到 682 条表单数据
2025-05-20 21:00:09,312 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-20 21:00:09,322 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:00:09,322 - INFO - 开始处理日期: 2025-02
2025-05-20 21:00:09,322 - INFO - Request Parameters - Page 1:
2025-05-20 21:00:09,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:09,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:09,935 - INFO - Response - Page 1:
2025-05-20 21:00:10,136 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:00:10,136 - INFO - Request Parameters - Page 2:
2025-05-20 21:00:10,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:10,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:10,680 - INFO - Response - Page 2:
2025-05-20 21:00:10,881 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:00:10,881 - INFO - Request Parameters - Page 3:
2025-05-20 21:00:10,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:10,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:11,380 - INFO - Response - Page 3:
2025-05-20 21:00:11,581 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:00:11,581 - INFO - Request Parameters - Page 4:
2025-05-20 21:00:11,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:11,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:12,091 - INFO - Response - Page 4:
2025-05-20 21:00:12,291 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:00:12,291 - INFO - Request Parameters - Page 5:
2025-05-20 21:00:12,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:12,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:12,729 - INFO - Response - Page 5:
2025-05-20 21:00:12,929 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:00:12,929 - INFO - Request Parameters - Page 6:
2025-05-20 21:00:12,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:12,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:13,462 - INFO - Response - Page 6:
2025-05-20 21:00:13,663 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:00:13,663 - INFO - Request Parameters - Page 7:
2025-05-20 21:00:13,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:13,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:14,119 - INFO - Response - Page 7:
2025-05-20 21:00:14,320 - INFO - 第 7 页获取到 70 条记录
2025-05-20 21:00:14,320 - INFO - 查询完成，共获取到 670 条记录
2025-05-20 21:00:14,320 - INFO - 获取到 670 条表单数据
2025-05-20 21:00:14,332 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-20 21:00:14,343 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:00:14,343 - INFO - 开始处理日期: 2025-03
2025-05-20 21:00:14,343 - INFO - Request Parameters - Page 1:
2025-05-20 21:00:14,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:14,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:14,854 - INFO - Response - Page 1:
2025-05-20 21:00:15,054 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:00:15,054 - INFO - Request Parameters - Page 2:
2025-05-20 21:00:15,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:15,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:15,546 - INFO - Response - Page 2:
2025-05-20 21:00:15,747 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:00:15,747 - INFO - Request Parameters - Page 3:
2025-05-20 21:00:15,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:15,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:16,255 - INFO - Response - Page 3:
2025-05-20 21:00:16,455 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:00:16,455 - INFO - Request Parameters - Page 4:
2025-05-20 21:00:16,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:16,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:16,925 - INFO - Response - Page 4:
2025-05-20 21:00:17,125 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:00:17,125 - INFO - Request Parameters - Page 5:
2025-05-20 21:00:17,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:17,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:18,058 - INFO - Response - Page 5:
2025-05-20 21:00:18,258 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:00:18,258 - INFO - Request Parameters - Page 6:
2025-05-20 21:00:18,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:18,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:18,995 - INFO - Response - Page 6:
2025-05-20 21:00:19,195 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:00:19,195 - INFO - Request Parameters - Page 7:
2025-05-20 21:00:19,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:19,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:19,622 - INFO - Response - Page 7:
2025-05-20 21:00:19,823 - INFO - 第 7 页获取到 61 条记录
2025-05-20 21:00:19,823 - INFO - 查询完成，共获取到 661 条记录
2025-05-20 21:00:19,823 - INFO - 获取到 661 条表单数据
2025-05-20 21:00:19,836 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-20 21:00:19,847 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:00:19,847 - INFO - 开始处理日期: 2025-04
2025-05-20 21:00:19,847 - INFO - Request Parameters - Page 1:
2025-05-20 21:00:19,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:19,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:20,436 - INFO - Response - Page 1:
2025-05-20 21:00:20,637 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:00:20,637 - INFO - Request Parameters - Page 2:
2025-05-20 21:00:20,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:20,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:21,091 - INFO - Response - Page 2:
2025-05-20 21:00:21,291 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:00:21,291 - INFO - Request Parameters - Page 3:
2025-05-20 21:00:21,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:21,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:21,780 - INFO - Response - Page 3:
2025-05-20 21:00:21,980 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:00:21,980 - INFO - Request Parameters - Page 4:
2025-05-20 21:00:21,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:21,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:22,654 - INFO - Response - Page 4:
2025-05-20 21:00:22,855 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:00:22,855 - INFO - Request Parameters - Page 5:
2025-05-20 21:00:22,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:22,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:23,344 - INFO - Response - Page 5:
2025-05-20 21:00:23,544 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:00:23,544 - INFO - Request Parameters - Page 6:
2025-05-20 21:00:23,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:23,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:23,991 - INFO - Response - Page 6:
2025-05-20 21:00:24,191 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:00:24,191 - INFO - Request Parameters - Page 7:
2025-05-20 21:00:24,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:24,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:24,807 - INFO - Response - Page 7:
2025-05-20 21:00:25,007 - INFO - 第 7 页获取到 56 条记录
2025-05-20 21:00:25,007 - INFO - 查询完成，共获取到 656 条记录
2025-05-20 21:00:25,008 - INFO - 获取到 656 条表单数据
2025-05-20 21:00:25,022 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-20 21:00:25,034 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:00:25,034 - INFO - 开始处理日期: 2025-05
2025-05-20 21:00:25,034 - INFO - Request Parameters - Page 1:
2025-05-20 21:00:25,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:25,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:25,569 - INFO - Response - Page 1:
2025-05-20 21:00:25,769 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:00:25,769 - INFO - Request Parameters - Page 2:
2025-05-20 21:00:25,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:25,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:26,279 - INFO - Response - Page 2:
2025-05-20 21:00:26,479 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:00:26,479 - INFO - Request Parameters - Page 3:
2025-05-20 21:00:26,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:26,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:27,044 - INFO - Response - Page 3:
2025-05-20 21:00:27,245 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:00:27,245 - INFO - Request Parameters - Page 4:
2025-05-20 21:00:27,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:27,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:27,734 - INFO - Response - Page 4:
2025-05-20 21:00:27,934 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:00:27,934 - INFO - Request Parameters - Page 5:
2025-05-20 21:00:27,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:27,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:28,438 - INFO - Response - Page 5:
2025-05-20 21:00:28,639 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:00:28,639 - INFO - Request Parameters - Page 6:
2025-05-20 21:00:28,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:28,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:29,149 - INFO - Response - Page 6:
2025-05-20 21:00:29,351 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:00:29,351 - INFO - Request Parameters - Page 7:
2025-05-20 21:00:29,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:00:29,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:00:29,752 - INFO - Response - Page 7:
2025-05-20 21:00:29,952 - INFO - 第 7 页获取到 28 条记录
2025-05-20 21:00:29,952 - INFO - 查询完成，共获取到 628 条记录
2025-05-20 21:00:29,952 - INFO - 获取到 628 条表单数据
2025-05-20 21:00:29,964 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-20 21:00:29,975 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:00:29,975 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:00:29,977 - INFO - =================同步完成====================
