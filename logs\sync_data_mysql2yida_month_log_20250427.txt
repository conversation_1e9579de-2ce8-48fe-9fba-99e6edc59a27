2025-04-27 00:00:03,640 - INFO - =================使用默认全量同步=============
2025-04-27 00:00:04,766 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-27 00:00:04,766 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 00:00:04,797 - INFO - 开始处理日期: 2025-01
2025-04-27 00:00:04,797 - INFO - Request Parameters - Page 1:
2025-04-27 00:00:04,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:04,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:05,360 - INFO - Response - Page 1:
2025-04-27 00:00:05,564 - INFO - 第 1 页获取到 100 条记录
2025-04-27 00:00:05,564 - INFO - Request Parameters - Page 2:
2025-04-27 00:00:05,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:05,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:06,095 - INFO - Response - Page 2:
2025-04-27 00:00:06,299 - INFO - 第 2 页获取到 100 条记录
2025-04-27 00:00:06,299 - INFO - Request Parameters - Page 3:
2025-04-27 00:00:06,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:06,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:06,799 - INFO - Response - Page 3:
2025-04-27 00:00:07,003 - INFO - 第 3 页获取到 100 条记录
2025-04-27 00:00:07,003 - INFO - Request Parameters - Page 4:
2025-04-27 00:00:07,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:07,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:07,440 - INFO - Response - Page 4:
2025-04-27 00:00:07,644 - INFO - 第 4 页获取到 100 条记录
2025-04-27 00:00:07,644 - INFO - Request Parameters - Page 5:
2025-04-27 00:00:07,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:07,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:08,160 - INFO - Response - Page 5:
2025-04-27 00:00:08,363 - INFO - 第 5 页获取到 100 条记录
2025-04-27 00:00:08,363 - INFO - Request Parameters - Page 6:
2025-04-27 00:00:08,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:08,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:08,770 - INFO - Response - Page 6:
2025-04-27 00:00:08,973 - INFO - 第 6 页获取到 100 条记录
2025-04-27 00:00:08,973 - INFO - Request Parameters - Page 7:
2025-04-27 00:00:08,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:08,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:09,427 - INFO - Response - Page 7:
2025-04-27 00:00:09,630 - INFO - 第 7 页获取到 82 条记录
2025-04-27 00:00:09,630 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 00:00:09,630 - INFO - 获取到 682 条表单数据
2025-04-27 00:00:09,630 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 00:00:09,646 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 00:00:09,646 - INFO - 开始处理日期: 2025-02
2025-04-27 00:00:09,646 - INFO - Request Parameters - Page 1:
2025-04-27 00:00:09,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:09,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:10,193 - INFO - Response - Page 1:
2025-04-27 00:00:10,396 - INFO - 第 1 页获取到 100 条记录
2025-04-27 00:00:10,396 - INFO - Request Parameters - Page 2:
2025-04-27 00:00:10,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:10,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:10,897 - INFO - Response - Page 2:
2025-04-27 00:00:11,100 - INFO - 第 2 页获取到 100 条记录
2025-04-27 00:00:11,100 - INFO - Request Parameters - Page 3:
2025-04-27 00:00:11,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:11,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:11,553 - INFO - Response - Page 3:
2025-04-27 00:00:11,757 - INFO - 第 3 页获取到 100 条记录
2025-04-27 00:00:11,757 - INFO - Request Parameters - Page 4:
2025-04-27 00:00:11,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:11,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:12,226 - INFO - Response - Page 4:
2025-04-27 00:00:12,429 - INFO - 第 4 页获取到 100 条记录
2025-04-27 00:00:12,429 - INFO - Request Parameters - Page 5:
2025-04-27 00:00:12,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:12,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:12,852 - INFO - Response - Page 5:
2025-04-27 00:00:13,055 - INFO - 第 5 页获取到 100 条记录
2025-04-27 00:00:13,055 - INFO - Request Parameters - Page 6:
2025-04-27 00:00:13,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:13,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:13,571 - INFO - Response - Page 6:
2025-04-27 00:00:13,774 - INFO - 第 6 页获取到 100 条记录
2025-04-27 00:00:13,774 - INFO - Request Parameters - Page 7:
2025-04-27 00:00:13,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:13,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:14,212 - INFO - Response - Page 7:
2025-04-27 00:00:14,415 - INFO - 第 7 页获取到 70 条记录
2025-04-27 00:00:14,415 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 00:00:14,415 - INFO - 获取到 670 条表单数据
2025-04-27 00:00:14,415 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 00:00:14,431 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 00:00:14,431 - INFO - 开始处理日期: 2025-03
2025-04-27 00:00:14,431 - INFO - Request Parameters - Page 1:
2025-04-27 00:00:14,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:14,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:14,853 - INFO - Response - Page 1:
2025-04-27 00:00:15,057 - INFO - 第 1 页获取到 100 条记录
2025-04-27 00:00:15,057 - INFO - Request Parameters - Page 2:
2025-04-27 00:00:15,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:15,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:15,588 - INFO - Response - Page 2:
2025-04-27 00:00:15,792 - INFO - 第 2 页获取到 100 条记录
2025-04-27 00:00:15,792 - INFO - Request Parameters - Page 3:
2025-04-27 00:00:15,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:15,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:16,198 - INFO - Response - Page 3:
2025-04-27 00:00:16,402 - INFO - 第 3 页获取到 100 条记录
2025-04-27 00:00:16,402 - INFO - Request Parameters - Page 4:
2025-04-27 00:00:16,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:16,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:16,840 - INFO - Response - Page 4:
2025-04-27 00:00:17,043 - INFO - 第 4 页获取到 100 条记录
2025-04-27 00:00:17,043 - INFO - Request Parameters - Page 5:
2025-04-27 00:00:17,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:17,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:17,496 - INFO - Response - Page 5:
2025-04-27 00:00:17,700 - INFO - 第 5 页获取到 100 条记录
2025-04-27 00:00:17,700 - INFO - Request Parameters - Page 6:
2025-04-27 00:00:17,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:17,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:18,138 - INFO - Response - Page 6:
2025-04-27 00:00:18,341 - INFO - 第 6 页获取到 100 条记录
2025-04-27 00:00:18,341 - INFO - Request Parameters - Page 7:
2025-04-27 00:00:18,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:18,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:18,794 - INFO - Response - Page 7:
2025-04-27 00:00:18,998 - INFO - 第 7 页获取到 61 条记录
2025-04-27 00:00:18,998 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 00:00:18,998 - INFO - 获取到 661 条表单数据
2025-04-27 00:00:18,998 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 00:00:19,013 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 00:00:19,013 - INFO - 开始处理日期: 2025-04
2025-04-27 00:00:19,013 - INFO - Request Parameters - Page 1:
2025-04-27 00:00:19,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:19,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:19,467 - INFO - Response - Page 1:
2025-04-27 00:00:19,670 - INFO - 第 1 页获取到 100 条记录
2025-04-27 00:00:19,670 - INFO - Request Parameters - Page 2:
2025-04-27 00:00:19,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:19,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:20,108 - INFO - Response - Page 2:
2025-04-27 00:00:20,311 - INFO - 第 2 页获取到 100 条记录
2025-04-27 00:00:20,311 - INFO - Request Parameters - Page 3:
2025-04-27 00:00:20,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:20,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:20,749 - INFO - Response - Page 3:
2025-04-27 00:00:20,953 - INFO - 第 3 页获取到 100 条记录
2025-04-27 00:00:20,953 - INFO - Request Parameters - Page 4:
2025-04-27 00:00:20,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:20,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:21,516 - INFO - Response - Page 4:
2025-04-27 00:00:21,719 - INFO - 第 4 页获取到 100 条记录
2025-04-27 00:00:21,719 - INFO - Request Parameters - Page 5:
2025-04-27 00:00:21,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:21,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:22,219 - INFO - Response - Page 5:
2025-04-27 00:00:22,423 - INFO - 第 5 页获取到 100 条记录
2025-04-27 00:00:22,423 - INFO - Request Parameters - Page 6:
2025-04-27 00:00:22,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:22,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:22,954 - INFO - Response - Page 6:
2025-04-27 00:00:23,158 - INFO - 第 6 页获取到 100 条记录
2025-04-27 00:00:23,158 - INFO - Request Parameters - Page 7:
2025-04-27 00:00:23,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 00:00:23,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 00:00:23,455 - INFO - Response - Page 7:
2025-04-27 00:00:23,658 - INFO - 第 7 页获取到 25 条记录
2025-04-27 00:00:23,658 - INFO - 查询完成，共获取到 625 条记录
2025-04-27 00:00:23,658 - INFO - 获取到 625 条表单数据
2025-04-27 00:00:23,658 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-27 00:00:23,658 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-27 00:00:24,080 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M33
2025-04-27 00:00:24,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78625.81, 'new_value': 82293.75}, {'field': 'offline_amount', 'old_value': 207609.1, 'new_value': 221397.98}, {'field': 'total_amount', 'old_value': 286234.91, 'new_value': 303691.73}, {'field': 'order_count', 'old_value': 10177, 'new_value': 10740}]
2025-04-27 00:00:24,080 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-27 00:00:24,471 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-27 00:00:24,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11207.58, 'new_value': 11531.9}, {'field': 'offline_amount', 'old_value': 42934.94, 'new_value': 47958.64}, {'field': 'total_amount', 'old_value': 54142.52, 'new_value': 59490.54}, {'field': 'order_count', 'old_value': 1057, 'new_value': 1154}]
2025-04-27 00:00:24,471 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-27 00:00:24,909 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA4
2025-04-27 00:00:24,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79865.6, 'new_value': 93244.6}, {'field': 'total_amount', 'old_value': 79865.6, 'new_value': 93244.6}, {'field': 'order_count', 'old_value': 573, 'new_value': 602}]
2025-04-27 00:00:24,909 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-27 00:00:25,394 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG4
2025-04-27 00:00:25,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 909161.0, 'new_value': 939352.0}, {'field': 'total_amount', 'old_value': 909161.0, 'new_value': 939352.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 155}]
2025-04-27 00:00:25,394 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-27 00:00:25,769 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK4
2025-04-27 00:00:25,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59500.56, 'new_value': 67668.2}, {'field': 'offline_amount', 'old_value': 104533.32, 'new_value': 104709.32}, {'field': 'total_amount', 'old_value': 164033.88, 'new_value': 172377.52}, {'field': 'order_count', 'old_value': 5726, 'new_value': 5989}]
2025-04-27 00:00:25,769 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO4
2025-04-27 00:00:26,286 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO4
2025-04-27 00:00:26,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7675.0, 'new_value': 9251.0}, {'field': 'total_amount', 'old_value': 7675.0, 'new_value': 9251.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-04-27 00:00:26,286 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-27 00:00:26,739 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-04-27 00:00:26,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52630.43, 'new_value': 56945.59}, {'field': 'offline_amount', 'old_value': 546772.91, 'new_value': 595303.64}, {'field': 'total_amount', 'old_value': 599403.34, 'new_value': 652249.23}, {'field': 'order_count', 'old_value': 1960, 'new_value': 2113}]
2025-04-27 00:00:26,739 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-27 00:00:27,177 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN5
2025-04-27 00:00:27,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20714.95, 'new_value': 21639.86}, {'field': 'offline_amount', 'old_value': 217070.84, 'new_value': 240252.61}, {'field': 'total_amount', 'old_value': 237785.79, 'new_value': 261892.47}, {'field': 'order_count', 'old_value': 1137, 'new_value': 1259}]
2025-04-27 00:00:27,177 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-27 00:00:27,568 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M89
2025-04-27 00:00:27,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12999.56, 'new_value': 14595.3}, {'field': 'total_amount', 'old_value': 12999.56, 'new_value': 14595.3}, {'field': 'order_count', 'old_value': 143, 'new_value': 156}]
2025-04-27 00:00:27,568 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-27 00:00:27,990 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M99
2025-04-27 00:00:27,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36936.42, 'new_value': 39025.21}, {'field': 'offline_amount', 'old_value': 273212.5, 'new_value': 290623.1}, {'field': 'total_amount', 'old_value': 310148.92, 'new_value': 329648.31}, {'field': 'order_count', 'old_value': 2149, 'new_value': 2280}]
2025-04-27 00:00:27,990 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-27 00:00:28,428 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-27 00:00:28,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18674.65, 'new_value': 20856.34}, {'field': 'offline_amount', 'old_value': 35128.82, 'new_value': 37582.89}, {'field': 'total_amount', 'old_value': 53803.47, 'new_value': 58439.23}, {'field': 'order_count', 'old_value': 2171, 'new_value': 2383}]
2025-04-27 00:00:28,428 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-27 00:00:28,819 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-27 00:00:28,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166708.57, 'new_value': 179160.37}, {'field': 'total_amount', 'old_value': 166708.57, 'new_value': 179160.37}, {'field': 'order_count', 'old_value': 277, 'new_value': 297}]
2025-04-27 00:00:28,819 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-27 00:00:29,226 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJ9
2025-04-27 00:00:29,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235731.04, 'new_value': 279262.04}, {'field': 'total_amount', 'old_value': 235731.04, 'new_value': 279262.04}, {'field': 'order_count', 'old_value': 177, 'new_value': 187}]
2025-04-27 00:00:29,226 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-27 00:00:29,679 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-27 00:00:29,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 319862.15, 'new_value': 350214.84}, {'field': 'offline_amount', 'old_value': 978.0, 'new_value': 1187.0}, {'field': 'total_amount', 'old_value': 320840.15, 'new_value': 351401.84}, {'field': 'order_count', 'old_value': 3857, 'new_value': 4206}]
2025-04-27 00:00:29,679 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-27 00:00:30,117 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MT9
2025-04-27 00:00:30,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35426.62, 'new_value': 37520.27}, {'field': 'offline_amount', 'old_value': 480131.9, 'new_value': 510286.59}, {'field': 'total_amount', 'old_value': 515558.52, 'new_value': 547806.86}, {'field': 'order_count', 'old_value': 4520, 'new_value': 4824}]
2025-04-27 00:00:30,117 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-27 00:00:30,477 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAA
2025-04-27 00:00:30,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68623.87, 'new_value': 74018.66}, {'field': 'total_amount', 'old_value': 70205.21, 'new_value': 75600.0}, {'field': 'order_count', 'old_value': 2779, 'new_value': 2978}]
2025-04-27 00:00:30,477 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFA
2025-04-27 00:00:30,868 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFA
2025-04-27 00:00:30,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6720.0, 'new_value': 9720.0}, {'field': 'total_amount', 'old_value': 6720.0, 'new_value': 9720.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-04-27 00:00:30,868 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-04-27 00:00:31,290 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MJA
2025-04-27 00:00:31,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 370777.16, 'new_value': 379662.16}, {'field': 'total_amount', 'old_value': 370777.16, 'new_value': 379662.16}, {'field': 'order_count', 'old_value': 67, 'new_value': 69}]
2025-04-27 00:00:31,290 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-27 00:00:31,712 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLA
2025-04-27 00:00:31,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98421.54, 'new_value': 103355.55}, {'field': 'offline_amount', 'old_value': 282690.48, 'new_value': 304332.76}, {'field': 'total_amount', 'old_value': 381112.02, 'new_value': 407688.31}, {'field': 'order_count', 'old_value': 9894, 'new_value': 10461}]
2025-04-27 00:00:31,712 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-27 00:00:32,135 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MPA
2025-04-27 00:00:32,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285856.31, 'new_value': 310034.63}, {'field': 'total_amount', 'old_value': 285856.31, 'new_value': 310034.63}, {'field': 'order_count', 'old_value': 1464, 'new_value': 1582}]
2025-04-27 00:00:32,135 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-27 00:00:32,541 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUA
2025-04-27 00:00:32,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48417.3, 'new_value': 51736.5}, {'field': 'total_amount', 'old_value': 48417.3, 'new_value': 51736.5}, {'field': 'order_count', 'old_value': 403, 'new_value': 422}]
2025-04-27 00:00:32,541 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-27 00:00:32,917 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXA
2025-04-27 00:00:32,917 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5661.9, 'new_value': 6427.9}, {'field': 'offline_amount', 'old_value': 39868.0, 'new_value': 44986.0}, {'field': 'total_amount', 'old_value': 45529.9, 'new_value': 51413.9}, {'field': 'order_count', 'old_value': 55, 'new_value': 60}]
2025-04-27 00:00:32,917 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-27 00:00:33,386 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1B
2025-04-27 00:00:33,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66193.4, 'new_value': 70274.4}, {'field': 'total_amount', 'old_value': 66193.4, 'new_value': 70274.4}, {'field': 'order_count', 'old_value': 186, 'new_value': 195}]
2025-04-27 00:00:33,386 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-27 00:00:33,871 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7B
2025-04-27 00:00:33,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33246.0, 'new_value': 35010.0}, {'field': 'total_amount', 'old_value': 33246.0, 'new_value': 35010.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 118}]
2025-04-27 00:00:33,871 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-27 00:00:34,308 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MAB
2025-04-27 00:00:34,308 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57091.74, 'new_value': 61116.07}, {'field': 'offline_amount', 'old_value': 72383.12, 'new_value': 81274.43}, {'field': 'total_amount', 'old_value': 129474.86, 'new_value': 142390.5}, {'field': 'order_count', 'old_value': 6592, 'new_value': 6734}]
2025-04-27 00:00:34,308 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-27 00:00:34,746 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMB
2025-04-27 00:00:34,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25957.6, 'new_value': 26835.6}, {'field': 'total_amount', 'old_value': 25957.6, 'new_value': 26835.6}, {'field': 'order_count', 'old_value': 120, 'new_value': 125}]
2025-04-27 00:00:34,746 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-27 00:00:35,184 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNB
2025-04-27 00:00:35,184 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 12446.92}, {'field': 'offline_amount', 'old_value': 139556.0, 'new_value': 141246.0}, {'field': 'total_amount', 'old_value': 139556.0, 'new_value': 153692.92}, {'field': 'order_count', 'old_value': 102, 'new_value': 113}]
2025-04-27 00:00:35,184 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-27 00:00:35,544 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVB
2025-04-27 00:00:35,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51501.35, 'new_value': 54669.29}, {'field': 'offline_amount', 'old_value': 66641.87, 'new_value': 71365.04}, {'field': 'total_amount', 'old_value': 118143.22, 'new_value': 126034.33}, {'field': 'order_count', 'old_value': 4689, 'new_value': 4982}]
2025-04-27 00:00:35,544 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-04-27 00:00:35,935 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML5
2025-04-27 00:00:35,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68970.0, 'new_value': 74484.0}, {'field': 'total_amount', 'old_value': 68970.0, 'new_value': 74484.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-04-27 00:00:35,935 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-27 00:00:36,310 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN5
2025-04-27 00:00:36,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81811.96, 'new_value': 86100.66}, {'field': 'offline_amount', 'old_value': 154442.87, 'new_value': 169159.84}, {'field': 'total_amount', 'old_value': 236254.83, 'new_value': 255260.5}, {'field': 'order_count', 'old_value': 3443, 'new_value': 3661}]
2025-04-27 00:00:36,326 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-27 00:00:36,701 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS5
2025-04-27 00:00:36,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29070.0, 'new_value': 31858.0}, {'field': 'total_amount', 'old_value': 30222.0, 'new_value': 33010.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 70}]
2025-04-27 00:00:36,701 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-27 00:00:37,108 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW5
2025-04-27 00:00:37,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23891.0, 'new_value': 25980.0}, {'field': 'total_amount', 'old_value': 23891.0, 'new_value': 25980.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 238}]
2025-04-27 00:00:37,108 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-27 00:00:37,530 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX5
2025-04-27 00:00:37,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19956.41, 'new_value': 20535.98}, {'field': 'total_amount', 'old_value': 21856.41, 'new_value': 22435.98}, {'field': 'order_count', 'old_value': 421, 'new_value': 439}]
2025-04-27 00:00:37,530 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-27 00:00:37,921 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY5
2025-04-27 00:00:37,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205986.9, 'new_value': 225251.9}, {'field': 'total_amount', 'old_value': 211167.1, 'new_value': 230432.1}, {'field': 'order_count', 'old_value': 2418, 'new_value': 2645}]
2025-04-27 00:00:37,921 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-27 00:00:38,343 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M16
2025-04-27 00:00:38,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221631.0, 'new_value': 226261.0}, {'field': 'total_amount', 'old_value': 221631.0, 'new_value': 226261.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-04-27 00:00:38,343 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-27 00:00:38,750 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD6
2025-04-27 00:00:38,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51436.07, 'new_value': 53479.64}, {'field': 'offline_amount', 'old_value': 22882.89, 'new_value': 24242.03}, {'field': 'total_amount', 'old_value': 74318.96, 'new_value': 77721.67}, {'field': 'order_count', 'old_value': 2999, 'new_value': 3150}]
2025-04-27 00:00:38,750 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-27 00:00:39,172 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF6
2025-04-27 00:00:39,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5311.0, 'new_value': 5468.0}, {'field': 'offline_amount', 'old_value': 367671.0, 'new_value': 373352.0}, {'field': 'total_amount', 'old_value': 372982.0, 'new_value': 378820.0}, {'field': 'order_count', 'old_value': 202, 'new_value': 208}]
2025-04-27 00:00:39,172 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-27 00:00:39,579 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG6
2025-04-27 00:00:39,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59605.0, 'new_value': 63164.0}, {'field': 'total_amount', 'old_value': 59605.0, 'new_value': 63164.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 158}]
2025-04-27 00:00:39,579 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-27 00:00:40,032 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI6
2025-04-27 00:00:40,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 24875.0}, {'field': 'total_amount', 'old_value': 222027.0, 'new_value': 246902.0}, {'field': 'order_count', 'old_value': 5384, 'new_value': 5404}]
2025-04-27 00:00:40,032 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-27 00:00:40,408 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ6
2025-04-27 00:00:40,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7689.9, 'new_value': 8270.66}, {'field': 'offline_amount', 'old_value': 73124.08, 'new_value': 77170.88}, {'field': 'total_amount', 'old_value': 80813.98, 'new_value': 85441.54}, {'field': 'order_count', 'old_value': 2266, 'new_value': 2431}]
2025-04-27 00:00:40,408 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-27 00:00:40,861 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP6
2025-04-27 00:00:40,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36057.0, 'new_value': 37457.0}, {'field': 'total_amount', 'old_value': 36057.0, 'new_value': 37457.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 101}]
2025-04-27 00:00:40,861 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-27 00:00:41,283 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR6
2025-04-27 00:00:41,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54778.0, 'new_value': 59465.0}, {'field': 'total_amount', 'old_value': 54778.0, 'new_value': 59465.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 94}]
2025-04-27 00:00:41,283 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-27 00:00:41,737 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT6
2025-04-27 00:00:41,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181749.0, 'new_value': 192309.0}, {'field': 'total_amount', 'old_value': 181749.0, 'new_value': 192309.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 263}]
2025-04-27 00:00:41,737 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-27 00:00:42,159 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV6
2025-04-27 00:00:42,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373212.59, 'new_value': 405186.67}, {'field': 'total_amount', 'old_value': 534512.06, 'new_value': 566486.14}, {'field': 'order_count', 'old_value': 2445, 'new_value': 2553}]
2025-04-27 00:00:42,159 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-27 00:00:42,519 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M17
2025-04-27 00:00:42,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25384.0, 'new_value': 28850.6}, {'field': 'total_amount', 'old_value': 25384.0, 'new_value': 28850.6}, {'field': 'order_count', 'old_value': 289, 'new_value': 306}]
2025-04-27 00:00:42,519 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-27 00:00:42,863 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M27
2025-04-27 00:00:42,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85828.12, 'new_value': 89603.63}, {'field': 'offline_amount', 'old_value': 59636.93, 'new_value': 63054.4}, {'field': 'total_amount', 'old_value': 145465.05, 'new_value': 152658.03}, {'field': 'order_count', 'old_value': 7769, 'new_value': 8226}]
2025-04-27 00:00:42,863 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-27 00:00:43,301 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M37
2025-04-27 00:00:43,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39531.96, 'new_value': 40616.18}, {'field': 'offline_amount', 'old_value': 224611.82, 'new_value': 241403.36}, {'field': 'total_amount', 'old_value': 264143.78, 'new_value': 282019.54}, {'field': 'order_count', 'old_value': 6391, 'new_value': 6871}]
2025-04-27 00:00:43,301 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-27 00:00:43,661 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M47
2025-04-27 00:00:43,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8933.7, 'new_value': 9235.89}, {'field': 'offline_amount', 'old_value': 13155.25, 'new_value': 14088.95}, {'field': 'total_amount', 'old_value': 22088.95, 'new_value': 23324.84}, {'field': 'order_count', 'old_value': 1485, 'new_value': 1564}]
2025-04-27 00:00:43,661 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-27 00:00:44,067 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M57
2025-04-27 00:00:44,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261679.7, 'new_value': 276056.75}, {'field': 'total_amount', 'old_value': 277438.5, 'new_value': 291815.55}, {'field': 'order_count', 'old_value': 11683, 'new_value': 12248}]
2025-04-27 00:00:44,067 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-27 00:00:44,427 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME7
2025-04-27 00:00:44,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 670880.0, 'new_value': 701073.0}, {'field': 'total_amount', 'old_value': 670880.0, 'new_value': 701073.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-04-27 00:00:44,427 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-27 00:00:44,896 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MJ7
2025-04-27 00:00:44,896 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 268535.87, 'new_value': 282028.72}, {'field': 'offline_amount', 'old_value': 14956.46, 'new_value': 15616.66}, {'field': 'total_amount', 'old_value': 283492.33, 'new_value': 297645.38}, {'field': 'order_count', 'old_value': 10112, 'new_value': 10667}]
2025-04-27 00:00:44,896 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-27 00:00:45,240 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP7
2025-04-27 00:00:45,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123761.7, 'new_value': 130944.9}, {'field': 'total_amount', 'old_value': 123761.7, 'new_value': 130944.9}, {'field': 'order_count', 'old_value': 228, 'new_value': 244}]
2025-04-27 00:00:45,240 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-27 00:00:45,662 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS7
2025-04-27 00:00:45,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 520759.0, 'new_value': 567235.0}, {'field': 'total_amount', 'old_value': 520759.0, 'new_value': 567235.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 93}]
2025-04-27 00:00:45,662 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-27 00:00:46,069 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW7
2025-04-27 00:00:46,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53995.5, 'new_value': 68995.5}, {'field': 'total_amount', 'old_value': 78238.24, 'new_value': 93238.24}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-27 00:00:46,069 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-27 00:00:46,460 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX7
2025-04-27 00:00:46,460 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84355.8, 'new_value': 92945.8}, {'field': 'offline_amount', 'old_value': 49505.04, 'new_value': 53073.44}, {'field': 'total_amount', 'old_value': 133860.84, 'new_value': 146019.24}, {'field': 'order_count', 'old_value': 880, 'new_value': 965}]
2025-04-27 00:00:46,460 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-27 00:00:46,882 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M08
2025-04-27 00:00:46,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71807.88, 'new_value': 76885.57}, {'field': 'total_amount', 'old_value': 71807.88, 'new_value': 76885.57}, {'field': 'order_count', 'old_value': 2116, 'new_value': 2261}]
2025-04-27 00:00:46,882 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-27 00:00:47,336 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M98
2025-04-27 00:00:47,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31713.9, 'new_value': 41713.9}, {'field': 'offline_amount', 'old_value': 133246.99, 'new_value': 135532.29}, {'field': 'total_amount', 'old_value': 164960.89, 'new_value': 177246.19}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-04-27 00:00:47,336 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-27 00:00:47,742 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA8
2025-04-27 00:00:47,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70364.17, 'new_value': 74858.35}, {'field': 'offline_amount', 'old_value': 39726.1, 'new_value': 42031.0}, {'field': 'total_amount', 'old_value': 110090.27, 'new_value': 116889.35}, {'field': 'order_count', 'old_value': 5518, 'new_value': 5911}]
2025-04-27 00:00:47,742 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-27 00:00:48,118 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHH
2025-04-27 00:00:48,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220141.29, 'new_value': 236350.29}, {'field': 'total_amount', 'old_value': 220141.29, 'new_value': 236350.29}, {'field': 'order_count', 'old_value': 1354, 'new_value': 1439}]
2025-04-27 00:00:48,118 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-27 00:00:48,524 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIH
2025-04-27 00:00:48,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 214595.51, 'new_value': 227601.38}, {'field': 'offline_amount', 'old_value': 49975.88, 'new_value': 54367.98}, {'field': 'total_amount', 'old_value': 264571.39, 'new_value': 281969.36}, {'field': 'order_count', 'old_value': 1048, 'new_value': 1121}]
2025-04-27 00:00:48,524 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-27 00:00:48,915 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJH
2025-04-27 00:00:48,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182986.31, 'new_value': 193783.01}, {'field': 'total_amount', 'old_value': 190498.61, 'new_value': 201295.31}, {'field': 'order_count', 'old_value': 348, 'new_value': 373}]
2025-04-27 00:00:48,915 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-27 00:00:49,385 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNH
2025-04-27 00:00:49,385 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30870.47, 'new_value': 30541.15}, {'field': 'offline_amount', 'old_value': 176772.07, 'new_value': 192391.97}, {'field': 'total_amount', 'old_value': 207642.54, 'new_value': 222933.12}, {'field': 'order_count', 'old_value': 7062, 'new_value': 7565}]
2025-04-27 00:00:49,385 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-27 00:00:49,822 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXH
2025-04-27 00:00:49,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 500462.28, 'new_value': 522922.28}, {'field': 'total_amount', 'old_value': 506159.28, 'new_value': 528619.28}, {'field': 'order_count', 'old_value': 352, 'new_value': 362}]
2025-04-27 00:00:49,822 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-27 00:00:50,213 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4I
2025-04-27 00:00:50,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 541018.5, 'new_value': 572993.45}, {'field': 'total_amount', 'old_value': 541018.5, 'new_value': 572993.45}, {'field': 'order_count', 'old_value': 1828, 'new_value': 1945}]
2025-04-27 00:00:50,213 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-27 00:00:50,589 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-27 00:00:50,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129530.0, 'new_value': 139726.0}, {'field': 'offline_amount', 'old_value': 104503.0, 'new_value': 109176.0}, {'field': 'total_amount', 'old_value': 234033.0, 'new_value': 248902.0}, {'field': 'order_count', 'old_value': 8897, 'new_value': 9514}]
2025-04-27 00:00:50,589 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-27 00:00:51,058 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNI
2025-04-27 00:00:51,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25730.0, 'new_value': 27829.0}, {'field': 'total_amount', 'old_value': 25730.0, 'new_value': 27829.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 136}]
2025-04-27 00:00:51,058 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-27 00:00:51,496 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-04-27 00:00:51,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97931.11, 'new_value': 107029.81}, {'field': 'total_amount', 'old_value': 109448.11, 'new_value': 118546.81}, {'field': 'order_count', 'old_value': 877, 'new_value': 938}]
2025-04-27 00:00:51,496 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-27 00:00:51,965 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-27 00:00:51,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30685.24, 'new_value': 31376.24}, {'field': 'total_amount', 'old_value': 30785.44, 'new_value': 31476.44}, {'field': 'order_count', 'old_value': 259, 'new_value': 265}]
2025-04-27 00:00:51,981 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-27 00:00:52,372 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3J
2025-04-27 00:00:52,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11172.0, 'new_value': 11740.0}, {'field': 'total_amount', 'old_value': 11172.0, 'new_value': 11740.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-04-27 00:00:52,372 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-27 00:00:52,747 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6J
2025-04-27 00:00:52,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79021.4, 'new_value': 86635.4}, {'field': 'offline_amount', 'old_value': 142048.7, 'new_value': 158140.9}, {'field': 'total_amount', 'old_value': 221070.1, 'new_value': 244776.3}, {'field': 'order_count', 'old_value': 3916, 'new_value': 4390}]
2025-04-27 00:00:52,747 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-27 00:00:53,169 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJJ
2025-04-27 00:00:53,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82904.9, 'new_value': 86739.6}, {'field': 'total_amount', 'old_value': 82904.9, 'new_value': 86739.6}, {'field': 'order_count', 'old_value': 276, 'new_value': 287}]
2025-04-27 00:00:53,169 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-27 00:00:53,513 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-27 00:00:53,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46734.18, 'new_value': 48643.53}, {'field': 'offline_amount', 'old_value': 46801.0, 'new_value': 48947.62}, {'field': 'total_amount', 'old_value': 93535.18, 'new_value': 97591.15}, {'field': 'order_count', 'old_value': 2360, 'new_value': 2459}]
2025-04-27 00:00:53,513 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-04-27 00:00:53,967 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPJ
2025-04-27 00:00:53,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13663.0, 'new_value': 16716.0}, {'field': 'total_amount', 'old_value': 29934.0, 'new_value': 32987.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-04-27 00:00:53,967 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-27 00:00:54,342 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQJ
2025-04-27 00:00:54,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130011.0, 'new_value': 140752.0}, {'field': 'total_amount', 'old_value': 130011.0, 'new_value': 140752.0}, {'field': 'order_count', 'old_value': 2269, 'new_value': 2464}]
2025-04-27 00:00:54,342 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-27 00:00:54,733 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVJ
2025-04-27 00:00:54,733 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2751.76, 'new_value': 2872.01}, {'field': 'offline_amount', 'old_value': 13722.91, 'new_value': 14666.07}, {'field': 'total_amount', 'old_value': 16474.67, 'new_value': 17538.08}, {'field': 'order_count', 'old_value': 594, 'new_value': 633}]
2025-04-27 00:00:54,749 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-27 00:00:55,155 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MH2
2025-04-27 00:00:55,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 403378.04, 'new_value': 431005.68}, {'field': 'total_amount', 'old_value': 405861.62, 'new_value': 433489.26}, {'field': 'order_count', 'old_value': 6399, 'new_value': 6757}]
2025-04-27 00:00:55,155 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-27 00:00:55,546 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M16
2025-04-27 00:00:55,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67544.8, 'new_value': 72314.81}, {'field': 'total_amount', 'old_value': 67544.8, 'new_value': 72314.81}, {'field': 'order_count', 'old_value': 1126, 'new_value': 1201}]
2025-04-27 00:00:55,546 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-27 00:00:56,000 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M26
2025-04-27 00:00:56,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14935.07, 'new_value': 16817.58}, {'field': 'offline_amount', 'old_value': 31376.89, 'new_value': 33300.68}, {'field': 'total_amount', 'old_value': 46311.96, 'new_value': 50118.26}, {'field': 'order_count', 'old_value': 2165, 'new_value': 2336}]
2025-04-27 00:00:56,000 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-27 00:00:56,406 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M86
2025-04-27 00:00:56,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117667.96, 'new_value': 128453.17}, {'field': 'total_amount', 'old_value': 117667.96, 'new_value': 128453.17}, {'field': 'order_count', 'old_value': 3072, 'new_value': 3327}]
2025-04-27 00:00:56,406 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-27 00:00:56,797 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME7
2025-04-27 00:00:56,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132709.45, 'new_value': 140321.26}, {'field': 'total_amount', 'old_value': 132709.45, 'new_value': 140321.26}, {'field': 'order_count', 'old_value': 10697, 'new_value': 11191}]
2025-04-27 00:00:56,797 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-27 00:00:57,235 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI7
2025-04-27 00:00:57,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112560.51, 'new_value': 122103.62}, {'field': 'offline_amount', 'old_value': 215910.37, 'new_value': 231593.15}, {'field': 'total_amount', 'old_value': 328470.88, 'new_value': 353696.77}, {'field': 'order_count', 'old_value': 3915, 'new_value': 4181}]
2025-04-27 00:00:57,235 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-27 00:00:57,642 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MZ1
2025-04-27 00:00:57,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 210295.91, 'new_value': 225514.19}, {'field': 'offline_amount', 'old_value': 451741.05, 'new_value': 491741.05}, {'field': 'total_amount', 'old_value': 662036.96, 'new_value': 717255.24}, {'field': 'order_count', 'old_value': 1522, 'new_value': 1608}]
2025-04-27 00:00:57,658 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-27 00:00:58,049 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-27 00:00:58,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16506.0, 'new_value': 17814.0}, {'field': 'offline_amount', 'old_value': 133658.0, 'new_value': 137466.0}, {'field': 'total_amount', 'old_value': 150164.0, 'new_value': 155280.0}, {'field': 'order_count', 'old_value': 622, 'new_value': 657}]
2025-04-27 00:00:58,049 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-27 00:00:58,440 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-27 00:00:58,440 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66468.0, 'new_value': 70768.0}, {'field': 'offline_amount', 'old_value': 32692.0, 'new_value': 33420.0}, {'field': 'total_amount', 'old_value': 99160.0, 'new_value': 104188.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 183}]
2025-04-27 00:00:58,440 - INFO - 日期 2025-04 处理完成 - 更新: 84 条，插入: 0 条，错误: 0 条
2025-04-27 00:00:58,440 - INFO - 数据同步完成！更新: 84 条，插入: 0 条，错误: 0 条
2025-04-27 00:00:58,440 - INFO - =================同步完成====================
2025-04-27 03:00:03,811 - INFO - =================使用默认全量同步=============
2025-04-27 03:00:04,906 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-27 03:00:04,906 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 03:00:04,937 - INFO - 开始处理日期: 2025-01
2025-04-27 03:00:04,937 - INFO - Request Parameters - Page 1:
2025-04-27 03:00:04,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:04,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:06,032 - INFO - Response - Page 1:
2025-04-27 03:00:06,235 - INFO - 第 1 页获取到 100 条记录
2025-04-27 03:00:06,235 - INFO - Request Parameters - Page 2:
2025-04-27 03:00:06,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:06,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:06,751 - INFO - Response - Page 2:
2025-04-27 03:00:06,954 - INFO - 第 2 页获取到 100 条记录
2025-04-27 03:00:06,954 - INFO - Request Parameters - Page 3:
2025-04-27 03:00:06,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:06,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:07,486 - INFO - Response - Page 3:
2025-04-27 03:00:07,689 - INFO - 第 3 页获取到 100 条记录
2025-04-27 03:00:07,689 - INFO - Request Parameters - Page 4:
2025-04-27 03:00:07,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:07,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:08,190 - INFO - Response - Page 4:
2025-04-27 03:00:08,393 - INFO - 第 4 页获取到 100 条记录
2025-04-27 03:00:08,393 - INFO - Request Parameters - Page 5:
2025-04-27 03:00:08,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:08,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:08,894 - INFO - Response - Page 5:
2025-04-27 03:00:09,097 - INFO - 第 5 页获取到 100 条记录
2025-04-27 03:00:09,097 - INFO - Request Parameters - Page 6:
2025-04-27 03:00:09,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:09,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:09,582 - INFO - Response - Page 6:
2025-04-27 03:00:09,785 - INFO - 第 6 页获取到 100 条记录
2025-04-27 03:00:09,785 - INFO - Request Parameters - Page 7:
2025-04-27 03:00:09,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:09,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:10,223 - INFO - Response - Page 7:
2025-04-27 03:00:10,426 - INFO - 第 7 页获取到 82 条记录
2025-04-27 03:00:10,426 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 03:00:10,426 - INFO - 获取到 682 条表单数据
2025-04-27 03:00:10,426 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 03:00:10,442 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 03:00:10,442 - INFO - 开始处理日期: 2025-02
2025-04-27 03:00:10,442 - INFO - Request Parameters - Page 1:
2025-04-27 03:00:10,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:10,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:10,989 - INFO - Response - Page 1:
2025-04-27 03:00:11,192 - INFO - 第 1 页获取到 100 条记录
2025-04-27 03:00:11,192 - INFO - Request Parameters - Page 2:
2025-04-27 03:00:11,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:11,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:11,693 - INFO - Response - Page 2:
2025-04-27 03:00:11,896 - INFO - 第 2 页获取到 100 条记录
2025-04-27 03:00:11,896 - INFO - Request Parameters - Page 3:
2025-04-27 03:00:11,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:11,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:12,412 - INFO - Response - Page 3:
2025-04-27 03:00:12,616 - INFO - 第 3 页获取到 100 条记录
2025-04-27 03:00:12,616 - INFO - Request Parameters - Page 4:
2025-04-27 03:00:12,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:12,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:13,132 - INFO - Response - Page 4:
2025-04-27 03:00:13,335 - INFO - 第 4 页获取到 100 条记录
2025-04-27 03:00:13,335 - INFO - Request Parameters - Page 5:
2025-04-27 03:00:13,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:13,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:13,820 - INFO - Response - Page 5:
2025-04-27 03:00:14,023 - INFO - 第 5 页获取到 100 条记录
2025-04-27 03:00:14,023 - INFO - Request Parameters - Page 6:
2025-04-27 03:00:14,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:14,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:14,492 - INFO - Response - Page 6:
2025-04-27 03:00:14,696 - INFO - 第 6 页获取到 100 条记录
2025-04-27 03:00:14,696 - INFO - Request Parameters - Page 7:
2025-04-27 03:00:14,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:14,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:15,165 - INFO - Response - Page 7:
2025-04-27 03:00:15,368 - INFO - 第 7 页获取到 70 条记录
2025-04-27 03:00:15,368 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 03:00:15,368 - INFO - 获取到 670 条表单数据
2025-04-27 03:00:15,368 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 03:00:15,384 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 03:00:15,384 - INFO - 开始处理日期: 2025-03
2025-04-27 03:00:15,384 - INFO - Request Parameters - Page 1:
2025-04-27 03:00:15,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:15,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:15,853 - INFO - Response - Page 1:
2025-04-27 03:00:16,056 - INFO - 第 1 页获取到 100 条记录
2025-04-27 03:00:16,056 - INFO - Request Parameters - Page 2:
2025-04-27 03:00:16,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:16,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:16,588 - INFO - Response - Page 2:
2025-04-27 03:00:16,791 - INFO - 第 2 页获取到 100 条记录
2025-04-27 03:00:16,791 - INFO - Request Parameters - Page 3:
2025-04-27 03:00:16,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:16,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:17,276 - INFO - Response - Page 3:
2025-04-27 03:00:17,479 - INFO - 第 3 页获取到 100 条记录
2025-04-27 03:00:17,479 - INFO - Request Parameters - Page 4:
2025-04-27 03:00:17,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:17,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:18,214 - INFO - Response - Page 4:
2025-04-27 03:00:18,418 - INFO - 第 4 页获取到 100 条记录
2025-04-27 03:00:18,418 - INFO - Request Parameters - Page 5:
2025-04-27 03:00:18,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:18,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:18,949 - INFO - Response - Page 5:
2025-04-27 03:00:19,153 - INFO - 第 5 页获取到 100 条记录
2025-04-27 03:00:19,153 - INFO - Request Parameters - Page 6:
2025-04-27 03:00:19,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:19,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:19,669 - INFO - Response - Page 6:
2025-04-27 03:00:19,872 - INFO - 第 6 页获取到 100 条记录
2025-04-27 03:00:19,872 - INFO - Request Parameters - Page 7:
2025-04-27 03:00:19,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:19,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:20,294 - INFO - Response - Page 7:
2025-04-27 03:00:20,498 - INFO - 第 7 页获取到 61 条记录
2025-04-27 03:00:20,498 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 03:00:20,498 - INFO - 获取到 661 条表单数据
2025-04-27 03:00:20,498 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 03:00:20,513 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 03:00:20,513 - INFO - 开始处理日期: 2025-04
2025-04-27 03:00:20,513 - INFO - Request Parameters - Page 1:
2025-04-27 03:00:20,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:20,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:21,029 - INFO - Response - Page 1:
2025-04-27 03:00:21,233 - INFO - 第 1 页获取到 100 条记录
2025-04-27 03:00:21,233 - INFO - Request Parameters - Page 2:
2025-04-27 03:00:21,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:21,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:21,765 - INFO - Response - Page 2:
2025-04-27 03:00:21,968 - INFO - 第 2 页获取到 100 条记录
2025-04-27 03:00:21,968 - INFO - Request Parameters - Page 3:
2025-04-27 03:00:21,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:21,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:22,484 - INFO - Response - Page 3:
2025-04-27 03:00:22,687 - INFO - 第 3 页获取到 100 条记录
2025-04-27 03:00:22,687 - INFO - Request Parameters - Page 4:
2025-04-27 03:00:22,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:22,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:23,172 - INFO - Response - Page 4:
2025-04-27 03:00:23,375 - INFO - 第 4 页获取到 100 条记录
2025-04-27 03:00:23,375 - INFO - Request Parameters - Page 5:
2025-04-27 03:00:23,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:23,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:23,813 - INFO - Response - Page 5:
2025-04-27 03:00:24,017 - INFO - 第 5 页获取到 100 条记录
2025-04-27 03:00:24,017 - INFO - Request Parameters - Page 6:
2025-04-27 03:00:24,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:24,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:24,533 - INFO - Response - Page 6:
2025-04-27 03:00:24,736 - INFO - 第 6 页获取到 100 条记录
2025-04-27 03:00:24,736 - INFO - Request Parameters - Page 7:
2025-04-27 03:00:24,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 03:00:24,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 03:00:25,080 - INFO - Response - Page 7:
2025-04-27 03:00:25,283 - INFO - 第 7 页获取到 25 条记录
2025-04-27 03:00:25,283 - INFO - 查询完成，共获取到 625 条记录
2025-04-27 03:00:25,283 - INFO - 获取到 625 条表单数据
2025-04-27 03:00:25,283 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-27 03:00:25,299 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 03:00:25,299 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 03:00:25,299 - INFO - =================同步完成====================
2025-04-27 06:00:03,629 - INFO - =================使用默认全量同步=============
2025-04-27 06:00:04,723 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-27 06:00:04,723 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 06:00:04,755 - INFO - 开始处理日期: 2025-01
2025-04-27 06:00:04,755 - INFO - Request Parameters - Page 1:
2025-04-27 06:00:04,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:04,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:05,646 - INFO - Response - Page 1:
2025-04-27 06:00:05,849 - INFO - 第 1 页获取到 100 条记录
2025-04-27 06:00:05,849 - INFO - Request Parameters - Page 2:
2025-04-27 06:00:05,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:05,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:06,365 - INFO - Response - Page 2:
2025-04-27 06:00:06,569 - INFO - 第 2 页获取到 100 条记录
2025-04-27 06:00:06,569 - INFO - Request Parameters - Page 3:
2025-04-27 06:00:06,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:06,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:07,116 - INFO - Response - Page 3:
2025-04-27 06:00:07,335 - INFO - 第 3 页获取到 100 条记录
2025-04-27 06:00:07,335 - INFO - Request Parameters - Page 4:
2025-04-27 06:00:07,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:07,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:07,757 - INFO - Response - Page 4:
2025-04-27 06:00:07,961 - INFO - 第 4 页获取到 100 条记录
2025-04-27 06:00:07,961 - INFO - Request Parameters - Page 5:
2025-04-27 06:00:07,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:07,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:08,445 - INFO - Response - Page 5:
2025-04-27 06:00:08,649 - INFO - 第 5 页获取到 100 条记录
2025-04-27 06:00:08,649 - INFO - Request Parameters - Page 6:
2025-04-27 06:00:08,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:08,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:09,196 - INFO - Response - Page 6:
2025-04-27 06:00:09,399 - INFO - 第 6 页获取到 100 条记录
2025-04-27 06:00:09,399 - INFO - Request Parameters - Page 7:
2025-04-27 06:00:09,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:09,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:09,900 - INFO - Response - Page 7:
2025-04-27 06:00:10,103 - INFO - 第 7 页获取到 82 条记录
2025-04-27 06:00:10,103 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 06:00:10,103 - INFO - 获取到 682 条表单数据
2025-04-27 06:00:10,103 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 06:00:10,119 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 06:00:10,119 - INFO - 开始处理日期: 2025-02
2025-04-27 06:00:10,119 - INFO - Request Parameters - Page 1:
2025-04-27 06:00:10,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:10,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:10,666 - INFO - Response - Page 1:
2025-04-27 06:00:10,869 - INFO - 第 1 页获取到 100 条记录
2025-04-27 06:00:10,869 - INFO - Request Parameters - Page 2:
2025-04-27 06:00:10,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:10,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:11,370 - INFO - Response - Page 2:
2025-04-27 06:00:11,573 - INFO - 第 2 页获取到 100 条记录
2025-04-27 06:00:11,573 - INFO - Request Parameters - Page 3:
2025-04-27 06:00:11,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:11,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:12,042 - INFO - Response - Page 3:
2025-04-27 06:00:12,246 - INFO - 第 3 页获取到 100 条记录
2025-04-27 06:00:12,246 - INFO - Request Parameters - Page 4:
2025-04-27 06:00:12,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:12,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:12,730 - INFO - Response - Page 4:
2025-04-27 06:00:12,934 - INFO - 第 4 页获取到 100 条记录
2025-04-27 06:00:12,934 - INFO - Request Parameters - Page 5:
2025-04-27 06:00:12,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:12,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:13,403 - INFO - Response - Page 5:
2025-04-27 06:00:13,606 - INFO - 第 5 页获取到 100 条记录
2025-04-27 06:00:13,606 - INFO - Request Parameters - Page 6:
2025-04-27 06:00:13,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:13,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:14,091 - INFO - Response - Page 6:
2025-04-27 06:00:14,294 - INFO - 第 6 页获取到 100 条记录
2025-04-27 06:00:14,294 - INFO - Request Parameters - Page 7:
2025-04-27 06:00:14,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:14,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:14,748 - INFO - Response - Page 7:
2025-04-27 06:00:14,967 - INFO - 第 7 页获取到 70 条记录
2025-04-27 06:00:14,967 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 06:00:14,967 - INFO - 获取到 670 条表单数据
2025-04-27 06:00:14,967 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 06:00:14,983 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 06:00:14,983 - INFO - 开始处理日期: 2025-03
2025-04-27 06:00:14,983 - INFO - Request Parameters - Page 1:
2025-04-27 06:00:14,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:14,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:15,483 - INFO - Response - Page 1:
2025-04-27 06:00:15,686 - INFO - 第 1 页获取到 100 条记录
2025-04-27 06:00:15,686 - INFO - Request Parameters - Page 2:
2025-04-27 06:00:15,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:15,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:16,281 - INFO - Response - Page 2:
2025-04-27 06:00:16,484 - INFO - 第 2 页获取到 100 条记录
2025-04-27 06:00:16,484 - INFO - Request Parameters - Page 3:
2025-04-27 06:00:16,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:16,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:17,000 - INFO - Response - Page 3:
2025-04-27 06:00:17,203 - INFO - 第 3 页获取到 100 条记录
2025-04-27 06:00:17,203 - INFO - Request Parameters - Page 4:
2025-04-27 06:00:17,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:17,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:17,688 - INFO - Response - Page 4:
2025-04-27 06:00:17,891 - INFO - 第 4 页获取到 100 条记录
2025-04-27 06:00:17,891 - INFO - Request Parameters - Page 5:
2025-04-27 06:00:17,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:17,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:18,345 - INFO - Response - Page 5:
2025-04-27 06:00:18,548 - INFO - 第 5 页获取到 100 条记录
2025-04-27 06:00:18,548 - INFO - Request Parameters - Page 6:
2025-04-27 06:00:18,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:18,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:19,017 - INFO - Response - Page 6:
2025-04-27 06:00:19,221 - INFO - 第 6 页获取到 100 条记录
2025-04-27 06:00:19,221 - INFO - Request Parameters - Page 7:
2025-04-27 06:00:19,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:19,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:19,612 - INFO - Response - Page 7:
2025-04-27 06:00:19,815 - INFO - 第 7 页获取到 61 条记录
2025-04-27 06:00:19,815 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 06:00:19,815 - INFO - 获取到 661 条表单数据
2025-04-27 06:00:19,815 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 06:00:19,831 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 06:00:19,831 - INFO - 开始处理日期: 2025-04
2025-04-27 06:00:19,831 - INFO - Request Parameters - Page 1:
2025-04-27 06:00:19,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:19,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:20,300 - INFO - Response - Page 1:
2025-04-27 06:00:20,503 - INFO - 第 1 页获取到 100 条记录
2025-04-27 06:00:20,503 - INFO - Request Parameters - Page 2:
2025-04-27 06:00:20,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:20,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:21,004 - INFO - Response - Page 2:
2025-04-27 06:00:21,207 - INFO - 第 2 页获取到 100 条记录
2025-04-27 06:00:21,207 - INFO - Request Parameters - Page 3:
2025-04-27 06:00:21,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:21,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:21,645 - INFO - Response - Page 3:
2025-04-27 06:00:21,848 - INFO - 第 3 页获取到 100 条记录
2025-04-27 06:00:21,848 - INFO - Request Parameters - Page 4:
2025-04-27 06:00:21,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:21,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:22,317 - INFO - Response - Page 4:
2025-04-27 06:00:22,521 - INFO - 第 4 页获取到 100 条记录
2025-04-27 06:00:22,521 - INFO - Request Parameters - Page 5:
2025-04-27 06:00:22,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:22,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:23,099 - INFO - Response - Page 5:
2025-04-27 06:00:23,303 - INFO - 第 5 页获取到 100 条记录
2025-04-27 06:00:23,303 - INFO - Request Parameters - Page 6:
2025-04-27 06:00:23,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:23,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:23,834 - INFO - Response - Page 6:
2025-04-27 06:00:24,038 - INFO - 第 6 页获取到 100 条记录
2025-04-27 06:00:24,038 - INFO - Request Parameters - Page 7:
2025-04-27 06:00:24,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 06:00:24,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 06:00:24,429 - INFO - Response - Page 7:
2025-04-27 06:00:24,632 - INFO - 第 7 页获取到 25 条记录
2025-04-27 06:00:24,632 - INFO - 查询完成，共获取到 625 条记录
2025-04-27 06:00:24,632 - INFO - 获取到 625 条表单数据
2025-04-27 06:00:24,632 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-27 06:00:24,647 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-27 06:00:25,132 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS
2025-04-27 06:00:25,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103402.0, 'new_value': 109458.0}, {'field': 'total_amount', 'old_value': 105294.0, 'new_value': 111350.0}, {'field': 'order_count', 'old_value': 513, 'new_value': 538}]
2025-04-27 06:00:25,148 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-27 06:00:25,148 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-27 06:00:25,148 - INFO - =================同步完成====================
2025-04-27 09:00:03,492 - INFO - =================使用默认全量同步=============
2025-04-27 09:00:04,587 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-27 09:00:04,587 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 09:00:04,603 - INFO - 开始处理日期: 2025-01
2025-04-27 09:00:04,618 - INFO - Request Parameters - Page 1:
2025-04-27 09:00:04,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:04,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:05,369 - INFO - Response - Page 1:
2025-04-27 09:00:05,572 - INFO - 第 1 页获取到 100 条记录
2025-04-27 09:00:05,572 - INFO - Request Parameters - Page 2:
2025-04-27 09:00:05,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:05,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:06,073 - INFO - Response - Page 2:
2025-04-27 09:00:06,276 - INFO - 第 2 页获取到 100 条记录
2025-04-27 09:00:06,276 - INFO - Request Parameters - Page 3:
2025-04-27 09:00:06,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:06,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:06,761 - INFO - Response - Page 3:
2025-04-27 09:00:06,964 - INFO - 第 3 页获取到 100 条记录
2025-04-27 09:00:06,964 - INFO - Request Parameters - Page 4:
2025-04-27 09:00:06,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:06,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:07,449 - INFO - Response - Page 4:
2025-04-27 09:00:07,652 - INFO - 第 4 页获取到 100 条记录
2025-04-27 09:00:07,652 - INFO - Request Parameters - Page 5:
2025-04-27 09:00:07,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:07,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:08,106 - INFO - Response - Page 5:
2025-04-27 09:00:08,309 - INFO - 第 5 页获取到 100 条记录
2025-04-27 09:00:08,309 - INFO - Request Parameters - Page 6:
2025-04-27 09:00:08,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:08,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:08,810 - INFO - Response - Page 6:
2025-04-27 09:00:09,013 - INFO - 第 6 页获取到 100 条记录
2025-04-27 09:00:09,013 - INFO - Request Parameters - Page 7:
2025-04-27 09:00:09,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:09,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:09,529 - INFO - Response - Page 7:
2025-04-27 09:00:09,732 - INFO - 第 7 页获取到 82 条记录
2025-04-27 09:00:09,732 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 09:00:09,732 - INFO - 获取到 682 条表单数据
2025-04-27 09:00:09,732 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 09:00:09,748 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 09:00:09,748 - INFO - 开始处理日期: 2025-02
2025-04-27 09:00:09,748 - INFO - Request Parameters - Page 1:
2025-04-27 09:00:09,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:09,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:10,311 - INFO - Response - Page 1:
2025-04-27 09:00:10,514 - INFO - 第 1 页获取到 100 条记录
2025-04-27 09:00:10,514 - INFO - Request Parameters - Page 2:
2025-04-27 09:00:10,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:10,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:11,202 - INFO - Response - Page 2:
2025-04-27 09:00:11,406 - INFO - 第 2 页获取到 100 条记录
2025-04-27 09:00:11,406 - INFO - Request Parameters - Page 3:
2025-04-27 09:00:11,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:11,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:11,906 - INFO - Response - Page 3:
2025-04-27 09:00:12,109 - INFO - 第 3 页获取到 100 条记录
2025-04-27 09:00:12,109 - INFO - Request Parameters - Page 4:
2025-04-27 09:00:12,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:12,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:12,594 - INFO - Response - Page 4:
2025-04-27 09:00:12,797 - INFO - 第 4 页获取到 100 条记录
2025-04-27 09:00:12,797 - INFO - Request Parameters - Page 5:
2025-04-27 09:00:12,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:12,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:13,282 - INFO - Response - Page 5:
2025-04-27 09:00:13,486 - INFO - 第 5 页获取到 100 条记录
2025-04-27 09:00:13,486 - INFO - Request Parameters - Page 6:
2025-04-27 09:00:13,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:13,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:13,892 - INFO - Response - Page 6:
2025-04-27 09:00:14,096 - INFO - 第 6 页获取到 100 条记录
2025-04-27 09:00:14,096 - INFO - Request Parameters - Page 7:
2025-04-27 09:00:14,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:14,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:14,549 - INFO - Response - Page 7:
2025-04-27 09:00:14,752 - INFO - 第 7 页获取到 70 条记录
2025-04-27 09:00:14,752 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 09:00:14,752 - INFO - 获取到 670 条表单数据
2025-04-27 09:00:14,752 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 09:00:14,768 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 09:00:14,768 - INFO - 开始处理日期: 2025-03
2025-04-27 09:00:14,768 - INFO - Request Parameters - Page 1:
2025-04-27 09:00:14,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:14,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:15,284 - INFO - Response - Page 1:
2025-04-27 09:00:15,487 - INFO - 第 1 页获取到 100 条记录
2025-04-27 09:00:15,487 - INFO - Request Parameters - Page 2:
2025-04-27 09:00:15,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:15,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:15,988 - INFO - Response - Page 2:
2025-04-27 09:00:16,191 - INFO - 第 2 页获取到 100 条记录
2025-04-27 09:00:16,191 - INFO - Request Parameters - Page 3:
2025-04-27 09:00:16,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:16,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:16,676 - INFO - Response - Page 3:
2025-04-27 09:00:16,879 - INFO - 第 3 页获取到 100 条记录
2025-04-27 09:00:16,879 - INFO - Request Parameters - Page 4:
2025-04-27 09:00:16,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:16,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:17,380 - INFO - Response - Page 4:
2025-04-27 09:00:17,583 - INFO - 第 4 页获取到 100 条记录
2025-04-27 09:00:17,583 - INFO - Request Parameters - Page 5:
2025-04-27 09:00:17,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:17,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:18,052 - INFO - Response - Page 5:
2025-04-27 09:00:18,256 - INFO - 第 5 页获取到 100 条记录
2025-04-27 09:00:18,256 - INFO - Request Parameters - Page 6:
2025-04-27 09:00:18,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:18,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:18,678 - INFO - Response - Page 6:
2025-04-27 09:00:18,881 - INFO - 第 6 页获取到 100 条记录
2025-04-27 09:00:18,881 - INFO - Request Parameters - Page 7:
2025-04-27 09:00:18,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:18,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:19,288 - INFO - Response - Page 7:
2025-04-27 09:00:19,491 - INFO - 第 7 页获取到 61 条记录
2025-04-27 09:00:19,491 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 09:00:19,491 - INFO - 获取到 661 条表单数据
2025-04-27 09:00:19,491 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 09:00:19,507 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 09:00:19,507 - INFO - 开始处理日期: 2025-04
2025-04-27 09:00:19,507 - INFO - Request Parameters - Page 1:
2025-04-27 09:00:19,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:19,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:20,070 - INFO - Response - Page 1:
2025-04-27 09:00:20,273 - INFO - 第 1 页获取到 100 条记录
2025-04-27 09:00:20,273 - INFO - Request Parameters - Page 2:
2025-04-27 09:00:20,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:20,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:20,758 - INFO - Response - Page 2:
2025-04-27 09:00:20,961 - INFO - 第 2 页获取到 100 条记录
2025-04-27 09:00:20,961 - INFO - Request Parameters - Page 3:
2025-04-27 09:00:20,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:20,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:21,415 - INFO - Response - Page 3:
2025-04-27 09:00:21,618 - INFO - 第 3 页获取到 100 条记录
2025-04-27 09:00:21,618 - INFO - Request Parameters - Page 4:
2025-04-27 09:00:21,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:21,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:22,197 - INFO - Response - Page 4:
2025-04-27 09:00:22,400 - INFO - 第 4 页获取到 100 条记录
2025-04-27 09:00:22,400 - INFO - Request Parameters - Page 5:
2025-04-27 09:00:22,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:22,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:22,947 - INFO - Response - Page 5:
2025-04-27 09:00:23,151 - INFO - 第 5 页获取到 100 条记录
2025-04-27 09:00:23,151 - INFO - Request Parameters - Page 6:
2025-04-27 09:00:23,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:23,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:23,651 - INFO - Response - Page 6:
2025-04-27 09:00:23,854 - INFO - 第 6 页获取到 100 条记录
2025-04-27 09:00:23,854 - INFO - Request Parameters - Page 7:
2025-04-27 09:00:23,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 09:00:23,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 09:00:24,230 - INFO - Response - Page 7:
2025-04-27 09:00:24,433 - INFO - 第 7 页获取到 25 条记录
2025-04-27 09:00:24,433 - INFO - 查询完成，共获取到 625 条记录
2025-04-27 09:00:24,433 - INFO - 获取到 625 条表单数据
2025-04-27 09:00:24,433 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-27 09:00:24,433 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-27 09:00:24,902 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-27 09:00:24,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119419.0, 'new_value': 136097.0}, {'field': 'total_amount', 'old_value': 384372.9, 'new_value': 401050.9}]
2025-04-27 09:00:24,902 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-27 09:00:25,324 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M13
2025-04-27 09:00:25,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15298.18, 'new_value': 16024.47}, {'field': 'offline_amount', 'old_value': 41259.09, 'new_value': 42562.14}, {'field': 'total_amount', 'old_value': 56557.27, 'new_value': 58586.61}, {'field': 'order_count', 'old_value': 3003, 'new_value': 3115}]
2025-04-27 09:00:25,324 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-27 09:00:25,778 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M63
2025-04-27 09:00:25,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160000.0, 'new_value': 170000.0}, {'field': 'total_amount', 'old_value': 160000.0, 'new_value': 170000.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 246}]
2025-04-27 09:00:25,778 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-27 09:00:26,247 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M73
2025-04-27 09:00:26,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160000.0, 'new_value': 170000.0}, {'field': 'total_amount', 'old_value': 160000.0, 'new_value': 170000.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 150}]
2025-04-27 09:00:26,247 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-27 09:00:26,716 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-04-27 09:00:26,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1600000.0, 'new_value': 1700000.0}, {'field': 'total_amount', 'old_value': 1600000.0, 'new_value': 1700000.0}, {'field': 'order_count', 'old_value': 350, 'new_value': 351}]
2025-04-27 09:00:26,716 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-27 09:00:27,154 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M93
2025-04-27 09:00:27,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1700000.0, 'new_value': 1800000.0}, {'field': 'total_amount', 'old_value': 1700000.0, 'new_value': 1800000.0}, {'field': 'order_count', 'old_value': 493, 'new_value': 494}]
2025-04-27 09:00:27,154 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-27 09:00:27,639 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ME3
2025-04-27 09:00:27,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260917.0, 'new_value': 277159.0}, {'field': 'total_amount', 'old_value': 260917.0, 'new_value': 277159.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 98}]
2025-04-27 09:00:27,639 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-27 09:00:28,077 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9ML3
2025-04-27 09:00:28,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18787.9, 'new_value': 19489.9}, {'field': 'offline_amount', 'old_value': 27744.35, 'new_value': 29103.35}, {'field': 'total_amount', 'old_value': 46532.25, 'new_value': 48593.25}, {'field': 'order_count', 'old_value': 2228, 'new_value': 2324}]
2025-04-27 09:00:28,077 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-27 09:00:28,468 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MO3
2025-04-27 09:00:28,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85171.0, 'new_value': 92379.0}, {'field': 'total_amount', 'old_value': 85171.0, 'new_value': 92379.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 114}]
2025-04-27 09:00:28,468 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP3
2025-04-27 09:00:28,859 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP3
2025-04-27 09:00:28,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49616.0, 'new_value': 54116.0}, {'field': 'total_amount', 'old_value': 52316.0, 'new_value': 56816.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-04-27 09:00:28,859 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-04-27 09:00:29,328 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR3
2025-04-27 09:00:29,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8968.0, 'new_value': 9621.0}, {'field': 'total_amount', 'old_value': 9558.0, 'new_value': 10211.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 198}]
2025-04-27 09:00:29,328 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-27 09:00:29,719 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS3
2025-04-27 09:00:29,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102455.0, 'new_value': 106989.0}, {'field': 'total_amount', 'old_value': 102455.0, 'new_value': 106989.0}, {'field': 'order_count', 'old_value': 5681, 'new_value': 5908}]
2025-04-27 09:00:29,719 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-27 09:00:30,173 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT3
2025-04-27 09:00:30,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61843.62, 'new_value': 63686.08}, {'field': 'offline_amount', 'old_value': 121548.55, 'new_value': 126275.55}, {'field': 'total_amount', 'old_value': 183392.17, 'new_value': 189961.63}, {'field': 'order_count', 'old_value': 2011, 'new_value': 2079}]
2025-04-27 09:00:30,173 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-27 09:00:30,626 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ3
2025-04-27 09:00:30,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60841.0, 'new_value': 61414.0}, {'field': 'offline_amount', 'old_value': 116894.16, 'new_value': 116972.16}, {'field': 'total_amount', 'old_value': 177735.16, 'new_value': 178386.16}, {'field': 'order_count', 'old_value': 234, 'new_value': 238}]
2025-04-27 09:00:30,626 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M54
2025-04-27 09:00:31,142 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M54
2025-04-27 09:00:31,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12498.0, 'new_value': 23897.0}, {'field': 'total_amount', 'old_value': 12498.0, 'new_value': 23897.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-04-27 09:00:31,142 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-27 09:00:31,611 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M64
2025-04-27 09:00:31,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63205.0, 'new_value': 65263.0}, {'field': 'total_amount', 'old_value': 63205.0, 'new_value': 65263.0}, {'field': 'order_count', 'old_value': 1172, 'new_value': 1213}]
2025-04-27 09:00:31,611 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-27 09:00:32,143 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME4
2025-04-27 09:00:32,143 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134608.0, 'new_value': 147827.0}, {'field': 'offline_amount', 'old_value': 48521.3, 'new_value': 52758.66}, {'field': 'total_amount', 'old_value': 183129.3, 'new_value': 200585.66}, {'field': 'order_count', 'old_value': 1250, 'new_value': 1365}]
2025-04-27 09:00:32,143 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-27 09:00:32,628 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-27 09:00:32,628 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5669.8, 'new_value': 6136.8}, {'field': 'offline_amount', 'old_value': 40353.6, 'new_value': 43884.3}, {'field': 'total_amount', 'old_value': 46023.4, 'new_value': 50021.1}, {'field': 'order_count', 'old_value': 474, 'new_value': 510}]
2025-04-27 09:00:32,628 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-27 09:00:33,003 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MH4
2025-04-27 09:00:33,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8467.8, 'new_value': 8937.44}, {'field': 'offline_amount', 'old_value': 202283.8, 'new_value': 209649.15}, {'field': 'total_amount', 'old_value': 210751.6, 'new_value': 218586.59}, {'field': 'order_count', 'old_value': 1161, 'new_value': 1221}]
2025-04-27 09:00:33,003 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-27 09:00:33,519 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI4
2025-04-27 09:00:33,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96704.73, 'new_value': 100011.06}, {'field': 'total_amount', 'old_value': 96704.73, 'new_value': 100011.06}, {'field': 'order_count', 'old_value': 2592, 'new_value': 2660}]
2025-04-27 09:00:33,519 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-27 09:00:33,926 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ4
2025-04-27 09:00:33,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37481.0, 'new_value': 40384.0}, {'field': 'total_amount', 'old_value': 37481.0, 'new_value': 40384.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 121}]
2025-04-27 09:00:33,926 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-27 09:00:34,442 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ML4
2025-04-27 09:00:34,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36381.7, 'new_value': 37389.9}, {'field': 'total_amount', 'old_value': 36381.7, 'new_value': 37389.9}, {'field': 'order_count', 'old_value': 1342, 'new_value': 1389}]
2025-04-27 09:00:34,442 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-27 09:00:34,864 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM4
2025-04-27 09:00:34,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11654.01, 'new_value': 11938.69}, {'field': 'offline_amount', 'old_value': 176143.56, 'new_value': 184075.26}, {'field': 'total_amount', 'old_value': 187797.57, 'new_value': 196013.95}, {'field': 'order_count', 'old_value': 2021, 'new_value': 2099}]
2025-04-27 09:00:34,864 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-27 09:00:35,333 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MS4
2025-04-27 09:00:35,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179359.2, 'new_value': 188191.1}, {'field': 'offline_amount', 'old_value': 62358.74, 'new_value': 66540.02}, {'field': 'total_amount', 'old_value': 241717.94, 'new_value': 254731.12}, {'field': 'order_count', 'old_value': 1708, 'new_value': 1810}]
2025-04-27 09:00:35,333 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-27 09:00:35,818 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MZ4
2025-04-27 09:00:35,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82003.0, 'new_value': 86055.0}, {'field': 'total_amount', 'old_value': 82003.0, 'new_value': 86055.0}, {'field': 'order_count', 'old_value': 719, 'new_value': 756}]
2025-04-27 09:00:35,818 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-27 09:00:36,287 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-04-27 09:00:36,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1463.95, 'new_value': 1787.58}, {'field': 'offline_amount', 'old_value': 92995.88, 'new_value': 103122.25}, {'field': 'total_amount', 'old_value': 94459.83, 'new_value': 104909.83}, {'field': 'order_count', 'old_value': 2373, 'new_value': 2614}]
2025-04-27 09:00:36,287 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-27 09:00:36,757 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M95
2025-04-27 09:00:36,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25227.14, 'new_value': 26767.84}, {'field': 'offline_amount', 'old_value': 18783.7, 'new_value': 19708.7}, {'field': 'total_amount', 'old_value': 44010.84, 'new_value': 46476.54}, {'field': 'order_count', 'old_value': 220, 'new_value': 235}]
2025-04-27 09:00:36,757 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-27 09:00:37,320 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB5
2025-04-27 09:00:37,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20287.4, 'new_value': 21995.4}, {'field': 'offline_amount', 'old_value': 47204.47, 'new_value': 49757.07}, {'field': 'total_amount', 'old_value': 67491.87, 'new_value': 71752.47}, {'field': 'order_count', 'old_value': 795, 'new_value': 838}]
2025-04-27 09:00:37,320 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-27 09:00:37,773 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC5
2025-04-27 09:00:37,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133289.29, 'new_value': 140396.27}, {'field': 'total_amount', 'old_value': 133289.29, 'new_value': 140396.27}, {'field': 'order_count', 'old_value': 1566, 'new_value': 1647}]
2025-04-27 09:00:37,773 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-27 09:00:38,211 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9ME5
2025-04-27 09:00:38,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199755.0, 'new_value': 213721.9}, {'field': 'total_amount', 'old_value': 199755.0, 'new_value': 213721.9}, {'field': 'order_count', 'old_value': 2502, 'new_value': 2627}]
2025-04-27 09:00:38,211 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ5
2025-04-27 09:00:38,680 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ5
2025-04-27 09:00:38,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2356.0, 'new_value': 2755.0}, {'field': 'total_amount', 'old_value': 2356.0, 'new_value': 2755.0}, {'field': 'order_count', 'old_value': 402, 'new_value': 403}]
2025-04-27 09:00:38,680 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-27 09:00:39,102 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MO5
2025-04-27 09:00:39,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4015.0, 'new_value': 10703.0}, {'field': 'total_amount', 'old_value': 7682.0, 'new_value': 14370.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-04-27 09:00:39,102 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-27 09:00:39,744 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9M79
2025-04-27 09:00:39,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 439228.75, 'new_value': 466641.75}, {'field': 'total_amount', 'old_value': 439228.75, 'new_value': 466641.75}, {'field': 'order_count', 'old_value': 1958, 'new_value': 2093}]
2025-04-27 09:00:39,744 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-27 09:00:40,228 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MF9
2025-04-27 09:00:40,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114534.4, 'new_value': 120968.4}, {'field': 'total_amount', 'old_value': 140847.21, 'new_value': 147281.21}, {'field': 'order_count', 'old_value': 3449, 'new_value': 3604}]
2025-04-27 09:00:40,228 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-27 09:00:40,823 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MG9
2025-04-27 09:00:40,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24093.92, 'new_value': 24881.62}, {'field': 'total_amount', 'old_value': 24093.92, 'new_value': 24881.62}, {'field': 'order_count', 'old_value': 139, 'new_value': 144}]
2025-04-27 09:00:40,823 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-27 09:00:41,276 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MK9
2025-04-27 09:00:41,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158376.0, 'new_value': 171087.0}, {'field': 'total_amount', 'old_value': 158376.0, 'new_value': 171087.0}, {'field': 'order_count', 'old_value': 319, 'new_value': 344}]
2025-04-27 09:00:41,276 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-27 09:00:41,714 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-27 09:00:41,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129074.41, 'new_value': 137363.91}, {'field': 'offline_amount', 'old_value': 114571.72, 'new_value': 122740.94}, {'field': 'total_amount', 'old_value': 243646.13, 'new_value': 260104.85}, {'field': 'order_count', 'old_value': 789, 'new_value': 833}]
2025-04-27 09:00:41,714 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-27 09:00:42,152 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MN9
2025-04-27 09:00:42,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22252.0, 'new_value': 27788.0}, {'field': 'total_amount', 'old_value': 22252.0, 'new_value': 27788.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 115}]
2025-04-27 09:00:42,152 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-27 09:00:42,574 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-27 09:00:42,574 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92510.0, 'new_value': 98747.0}, {'field': 'offline_amount', 'old_value': 197488.0, 'new_value': 210300.0}, {'field': 'total_amount', 'old_value': 289998.0, 'new_value': 309047.0}, {'field': 'order_count', 'old_value': 256, 'new_value': 274}]
2025-04-27 09:00:42,574 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-27 09:00:43,028 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MR9
2025-04-27 09:00:43,028 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35752.8, 'new_value': 41778.8}, {'field': 'offline_amount', 'old_value': 391514.0, 'new_value': 458802.0}, {'field': 'total_amount', 'old_value': 427266.8, 'new_value': 500580.8}, {'field': 'order_count', 'old_value': 56, 'new_value': 66}]
2025-04-27 09:00:43,028 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-27 09:00:43,513 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MV9
2025-04-27 09:00:43,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251073.05, 'new_value': 265099.05}, {'field': 'total_amount', 'old_value': 251073.05, 'new_value': 265099.05}, {'field': 'order_count', 'old_value': 974, 'new_value': 1029}]
2025-04-27 09:00:43,513 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-27 09:00:43,935 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZ9
2025-04-27 09:00:43,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41305.45, 'new_value': 42856.61}, {'field': 'offline_amount', 'old_value': 35218.0, 'new_value': 36903.0}, {'field': 'total_amount', 'old_value': 76523.45, 'new_value': 79759.61}, {'field': 'order_count', 'old_value': 1012, 'new_value': 1062}]
2025-04-27 09:00:43,935 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-27 09:00:44,451 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0A
2025-04-27 09:00:44,451 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32672.22, 'new_value': 32757.02}, {'field': 'offline_amount', 'old_value': 75109.96, 'new_value': 81890.65}, {'field': 'total_amount', 'old_value': 107782.18, 'new_value': 114647.67}, {'field': 'order_count', 'old_value': 444, 'new_value': 468}]
2025-04-27 09:00:44,451 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-27 09:00:44,905 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2A
2025-04-27 09:00:44,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184146.0, 'new_value': 186334.0}, {'field': 'total_amount', 'old_value': 201243.0, 'new_value': 203431.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-04-27 09:00:44,905 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-27 09:00:45,327 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-27 09:00:45,327 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21615.75, 'new_value': 22556.42}, {'field': 'total_amount', 'old_value': 21615.75, 'new_value': 22556.42}, {'field': 'order_count', 'old_value': 222, 'new_value': 235}]
2025-04-27 09:00:45,327 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-04-27 09:00:45,780 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5A
2025-04-27 09:00:45,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18724.0, 'new_value': 19799.0}, {'field': 'total_amount', 'old_value': 18724.0, 'new_value': 19799.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 73}]
2025-04-27 09:00:45,780 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-27 09:00:46,218 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6A
2025-04-27 09:00:46,218 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 172342.41, 'new_value': 181243.3}, {'field': 'offline_amount', 'old_value': 143001.67, 'new_value': 147445.58}, {'field': 'total_amount', 'old_value': 315344.08, 'new_value': 328688.88}, {'field': 'order_count', 'old_value': 2736, 'new_value': 2851}]
2025-04-27 09:00:46,218 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-27 09:00:46,656 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCA
2025-04-27 09:00:46,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131298.67, 'new_value': 142404.05}, {'field': 'offline_amount', 'old_value': 27818.23, 'new_value': 29529.05}, {'field': 'total_amount', 'old_value': 159116.9, 'new_value': 171933.1}, {'field': 'order_count', 'old_value': 658, 'new_value': 712}]
2025-04-27 09:00:46,656 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-27 09:00:47,063 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDA
2025-04-27 09:00:47,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83043.41, 'new_value': 87169.21}, {'field': 'total_amount', 'old_value': 83043.41, 'new_value': 87169.21}, {'field': 'order_count', 'old_value': 339, 'new_value': 357}]
2025-04-27 09:00:47,063 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-27 09:00:47,532 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGA
2025-04-27 09:00:47,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50006.81, 'new_value': 52575.21}, {'field': 'offline_amount', 'old_value': 306584.94, 'new_value': 327712.2}, {'field': 'total_amount', 'old_value': 356591.75, 'new_value': 380287.41}, {'field': 'order_count', 'old_value': 2429, 'new_value': 2588}]
2025-04-27 09:00:47,532 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-27 09:00:47,954 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIA
2025-04-27 09:00:47,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19459.0, 'new_value': 21220.0}, {'field': 'total_amount', 'old_value': 19459.0, 'new_value': 21220.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-04-27 09:00:47,954 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-27 09:00:48,423 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MRA
2025-04-27 09:00:48,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65044.17, 'new_value': 68063.72}, {'field': 'total_amount', 'old_value': 65044.17, 'new_value': 68063.72}, {'field': 'order_count', 'old_value': 1785, 'new_value': 1877}]
2025-04-27 09:00:48,423 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-27 09:00:48,830 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MTA
2025-04-27 09:00:48,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 321345.2, 'new_value': 348246.6}, {'field': 'offline_amount', 'old_value': 19505.0, 'new_value': 20046.0}, {'field': 'total_amount', 'old_value': 340850.2, 'new_value': 368292.6}, {'field': 'order_count', 'old_value': 3331, 'new_value': 3519}]
2025-04-27 09:00:48,830 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-27 09:00:49,268 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWA
2025-04-27 09:00:49,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160360.3, 'new_value': 169103.42}, {'field': 'total_amount', 'old_value': 160360.3, 'new_value': 169103.42}, {'field': 'order_count', 'old_value': 738, 'new_value': 779}]
2025-04-27 09:00:49,268 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-04-27 09:00:49,721 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYA
2025-04-27 09:00:49,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16091.0, 'new_value': 20323.0}, {'field': 'total_amount', 'old_value': 16091.0, 'new_value': 20323.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-04-27 09:00:49,721 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-27 09:00:50,159 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MZA
2025-04-27 09:00:50,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48108.0, 'new_value': 51897.0}, {'field': 'total_amount', 'old_value': 48505.0, 'new_value': 52294.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 91}]
2025-04-27 09:00:50,175 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-27 09:00:50,535 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M0B
2025-04-27 09:00:50,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91895.93, 'new_value': 97976.3}, {'field': 'total_amount', 'old_value': 91895.93, 'new_value': 97976.3}, {'field': 'order_count', 'old_value': 479, 'new_value': 506}]
2025-04-27 09:00:50,535 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-27 09:00:50,973 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-04-27 09:00:50,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220488.0, 'new_value': 229776.0}, {'field': 'total_amount', 'old_value': 220488.0, 'new_value': 229776.0}, {'field': 'order_count', 'old_value': 18374, 'new_value': 19148}]
2025-04-27 09:00:50,973 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-27 09:00:51,473 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M5B
2025-04-27 09:00:51,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1407450.98, 'new_value': 1529460.48}, {'field': 'total_amount', 'old_value': 1407450.98, 'new_value': 1529460.48}, {'field': 'order_count', 'old_value': 3058, 'new_value': 3275}]
2025-04-27 09:00:51,473 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-27 09:00:52,020 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8B
2025-04-27 09:00:52,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7262.4, 'new_value': 7464.4}, {'field': 'offline_amount', 'old_value': 18590.55, 'new_value': 19251.15}, {'field': 'total_amount', 'old_value': 25852.95, 'new_value': 26715.55}, {'field': 'order_count', 'old_value': 844, 'new_value': 880}]
2025-04-27 09:00:52,020 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-27 09:00:52,536 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MCB
2025-04-27 09:00:52,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 443623.1, 'new_value': 460117.1}, {'field': 'total_amount', 'old_value': 553399.1, 'new_value': 569893.1}, {'field': 'order_count', 'old_value': 734, 'new_value': 755}]
2025-04-27 09:00:52,536 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-27 09:00:53,021 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEB
2025-04-27 09:00:53,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104206.1, 'new_value': 111206.1}, {'field': 'offline_amount', 'old_value': 15840.3, 'new_value': 16442.1}, {'field': 'total_amount', 'old_value': 120046.4, 'new_value': 127648.2}, {'field': 'order_count', 'old_value': 341, 'new_value': 361}]
2025-04-27 09:00:53,021 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-27 09:00:53,522 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-04-27 09:00:53,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74371.0, 'new_value': 79680.0}, {'field': 'total_amount', 'old_value': 76952.0, 'new_value': 82261.0}, {'field': 'order_count', 'old_value': 309, 'new_value': 330}]
2025-04-27 09:00:53,522 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-27 09:00:53,991 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MKB
2025-04-27 09:00:53,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22155.59, 'new_value': 23141.27}, {'field': 'offline_amount', 'old_value': 36347.94, 'new_value': 38066.54}, {'field': 'total_amount', 'old_value': 58503.53, 'new_value': 61207.81}, {'field': 'order_count', 'old_value': 2431, 'new_value': 2552}]
2025-04-27 09:00:53,991 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-27 09:00:54,444 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSB
2025-04-27 09:00:54,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6823.0, 'new_value': 7100.0}, {'field': 'offline_amount', 'old_value': 26760.1, 'new_value': 27979.3}, {'field': 'total_amount', 'old_value': 33583.1, 'new_value': 35079.3}, {'field': 'order_count', 'old_value': 1276, 'new_value': 1339}]
2025-04-27 09:00:54,444 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-27 09:00:54,867 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MUB
2025-04-27 09:00:54,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328785.28, 'new_value': 363569.58}, {'field': 'total_amount', 'old_value': 328786.28, 'new_value': 363570.58}, {'field': 'order_count', 'old_value': 593, 'new_value': 615}]
2025-04-27 09:00:54,867 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM5
2025-04-27 09:00:55,367 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM5
2025-04-27 09:00:55,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79845.0, 'new_value': 103133.0}, {'field': 'total_amount', 'old_value': 79845.0, 'new_value': 103133.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 34}]
2025-04-27 09:00:55,367 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-04-27 09:00:55,836 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO5
2025-04-27 09:00:55,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5055.1, 'new_value': 5516.4}, {'field': 'offline_amount', 'old_value': 21000.0, 'new_value': 25800.0}, {'field': 'total_amount', 'old_value': 26055.1, 'new_value': 31316.4}, {'field': 'order_count', 'old_value': 75, 'new_value': 83}]
2025-04-27 09:00:55,836 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-27 09:00:56,352 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MP5
2025-04-27 09:00:56,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142477.41, 'new_value': 147465.41}, {'field': 'total_amount', 'old_value': 142477.41, 'new_value': 147465.41}, {'field': 'order_count', 'old_value': 5035, 'new_value': 5206}]
2025-04-27 09:00:56,352 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-27 09:00:56,806 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR5
2025-04-27 09:00:56,806 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12790.66, 'new_value': 13204.06}, {'field': 'offline_amount', 'old_value': 454297.27, 'new_value': 484541.41}, {'field': 'total_amount', 'old_value': 467087.93, 'new_value': 497745.47}, {'field': 'order_count', 'old_value': 1865, 'new_value': 1998}]
2025-04-27 09:00:56,806 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-27 09:00:57,228 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MV5
2025-04-27 09:00:57,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43657.82, 'new_value': 49221.37}, {'field': 'total_amount', 'old_value': 43657.82, 'new_value': 49221.37}, {'field': 'order_count', 'old_value': 185, 'new_value': 212}]
2025-04-27 09:00:57,228 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-27 09:00:57,650 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M06
2025-04-27 09:00:57,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25920.49, 'new_value': 27449.55}, {'field': 'offline_amount', 'old_value': 48471.08, 'new_value': 50296.58}, {'field': 'total_amount', 'old_value': 74391.57, 'new_value': 77746.13}, {'field': 'order_count', 'old_value': 2664, 'new_value': 2781}]
2025-04-27 09:00:57,650 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-27 09:00:58,104 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M76
2025-04-27 09:00:58,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 595060.02, 'new_value': 623226.02}, {'field': 'total_amount', 'old_value': 595060.02, 'new_value': 623226.02}, {'field': 'order_count', 'old_value': 792, 'new_value': 827}]
2025-04-27 09:00:58,104 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB6
2025-04-27 09:00:58,526 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB6
2025-04-27 09:00:58,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6685.0, 'new_value': 7381.0}, {'field': 'total_amount', 'old_value': 6685.0, 'new_value': 7381.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 122}]
2025-04-27 09:00:58,526 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-27 09:00:58,980 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC6
2025-04-27 09:00:58,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114879.68, 'new_value': 122897.1}, {'field': 'total_amount', 'old_value': 114879.68, 'new_value': 122897.1}, {'field': 'order_count', 'old_value': 3338, 'new_value': 3573}]
2025-04-27 09:00:58,980 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH6
2025-04-27 09:00:59,465 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH6
2025-04-27 09:00:59,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16415.0, 'new_value': 17273.0}, {'field': 'total_amount', 'old_value': 16415.0, 'new_value': 17273.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-04-27 09:00:59,465 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-27 09:00:59,902 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM6
2025-04-27 09:00:59,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23747.0, 'new_value': 27199.0}, {'field': 'total_amount', 'old_value': 23747.0, 'new_value': 27199.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 46}]
2025-04-27 09:00:59,902 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-27 09:01:00,309 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ6
2025-04-27 09:01:00,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143423.83, 'new_value': 149979.8}, {'field': 'total_amount', 'old_value': 143423.83, 'new_value': 149979.8}, {'field': 'order_count', 'old_value': 5044, 'new_value': 5270}]
2025-04-27 09:01:00,309 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-27 09:01:00,731 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MW6
2025-04-27 09:01:00,731 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211493.41, 'new_value': 221367.56}, {'field': 'offline_amount', 'old_value': 501628.58, 'new_value': 540135.68}, {'field': 'total_amount', 'old_value': 713121.99, 'new_value': 761503.24}, {'field': 'order_count', 'old_value': 4419, 'new_value': 4627}]
2025-04-27 09:01:00,731 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-27 09:01:01,138 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M97
2025-04-27 09:01:01,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49957.35, 'new_value': 52247.45}, {'field': 'total_amount', 'old_value': 49957.35, 'new_value': 52247.45}, {'field': 'order_count', 'old_value': 2300, 'new_value': 2399}]
2025-04-27 09:01:01,138 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-27 09:01:01,732 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MD7
2025-04-27 09:01:01,732 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26216.8, 'new_value': 26339.8}, {'field': 'offline_amount', 'old_value': 38935.6, 'new_value': 45831.6}, {'field': 'total_amount', 'old_value': 65152.4, 'new_value': 72171.4}, {'field': 'order_count', 'old_value': 91, 'new_value': 95}]
2025-04-27 09:01:01,732 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-27 09:01:02,233 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MG7
2025-04-27 09:01:02,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35332.86, 'new_value': 39819.07}, {'field': 'total_amount', 'old_value': 35522.86, 'new_value': 40009.07}, {'field': 'order_count', 'old_value': 358, 'new_value': 400}]
2025-04-27 09:01:02,233 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-27 09:01:02,686 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MH7
2025-04-27 09:01:02,686 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117441.0, 'new_value': 124701.0}, {'field': 'offline_amount', 'old_value': 95267.0, 'new_value': 108266.0}, {'field': 'total_amount', 'old_value': 212708.0, 'new_value': 232967.0}, {'field': 'order_count', 'old_value': 9032, 'new_value': 9222}]
2025-04-27 09:01:02,686 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-27 09:01:03,124 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MM7
2025-04-27 09:01:03,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188476.7, 'new_value': 199513.31}, {'field': 'total_amount', 'old_value': 188476.7, 'new_value': 199513.31}, {'field': 'order_count', 'old_value': 614, 'new_value': 649}]
2025-04-27 09:01:03,124 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-27 09:01:03,578 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MQ7
2025-04-27 09:01:03,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273901.3, 'new_value': 289212.6}, {'field': 'total_amount', 'old_value': 273901.3, 'new_value': 289212.6}, {'field': 'order_count', 'old_value': 7933, 'new_value': 8342}]
2025-04-27 09:01:03,578 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-27 09:01:04,016 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-27 09:01:04,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108512.9, 'new_value': 118534.2}, {'field': 'total_amount', 'old_value': 213053.67, 'new_value': 223074.97}, {'field': 'order_count', 'old_value': 5858, 'new_value': 6128}]
2025-04-27 09:01:04,016 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-27 09:01:04,500 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY7
2025-04-27 09:01:04,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45449.1, 'new_value': 48051.8}, {'field': 'total_amount', 'old_value': 45449.1, 'new_value': 48051.8}, {'field': 'order_count', 'old_value': 60, 'new_value': 64}]
2025-04-27 09:01:04,500 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-27 09:01:04,938 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ7
2025-04-27 09:01:04,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43231.85, 'new_value': 46153.36}, {'field': 'offline_amount', 'old_value': 50802.08, 'new_value': 53935.9}, {'field': 'total_amount', 'old_value': 94033.93, 'new_value': 100089.26}, {'field': 'order_count', 'old_value': 3556, 'new_value': 3665}]
2025-04-27 09:01:04,938 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-27 09:01:05,439 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M18
2025-04-27 09:01:05,439 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76529.75, 'new_value': 79717.66}, {'field': 'offline_amount', 'old_value': 111480.99, 'new_value': 116579.99}, {'field': 'total_amount', 'old_value': 188010.74, 'new_value': 196297.65}, {'field': 'order_count', 'old_value': 7583, 'new_value': 7902}]
2025-04-27 09:01:05,439 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-27 09:01:05,861 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M88
2025-04-27 09:01:05,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11587.86, 'new_value': 38325.78}, {'field': 'total_amount', 'old_value': 393122.34, 'new_value': 419860.26}, {'field': 'order_count', 'old_value': 1618, 'new_value': 1730}]
2025-04-27 09:01:05,877 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC8
2025-04-27 09:01:06,393 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC8
2025-04-27 09:01:06,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9501.0, 'new_value': 10531.0}, {'field': 'total_amount', 'old_value': 20583.0, 'new_value': 21613.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 101}]
2025-04-27 09:01:06,393 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-27 09:01:06,862 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-27 09:01:06,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445999.35, 'new_value': 467595.09}, {'field': 'total_amount', 'old_value': 465062.85, 'new_value': 486658.59}, {'field': 'order_count', 'old_value': 1818, 'new_value': 1899}]
2025-04-27 09:01:06,862 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-27 09:01:07,253 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-27 09:01:07,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58740.0, 'new_value': 60608.0}, {'field': 'total_amount', 'old_value': 65159.0, 'new_value': 67027.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-04-27 09:01:07,253 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-04-27 09:01:07,691 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCH
2025-04-27 09:01:07,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104703.5, 'new_value': 109263.5}, {'field': 'total_amount', 'old_value': 107103.5, 'new_value': 111663.5}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-04-27 09:01:07,691 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-27 09:01:08,160 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOH
2025-04-27 09:01:08,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56243.0, 'new_value': 58073.0}, {'field': 'total_amount', 'old_value': 56243.0, 'new_value': 58073.0}, {'field': 'order_count', 'old_value': 3820, 'new_value': 3871}]
2025-04-27 09:01:08,160 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-27 09:01:08,598 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRH
2025-04-27 09:01:08,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15728.6, 'new_value': 16426.6}, {'field': 'total_amount', 'old_value': 15728.6, 'new_value': 16426.6}, {'field': 'order_count', 'old_value': 159, 'new_value': 165}]
2025-04-27 09:01:08,598 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-27 09:01:09,036 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0I
2025-04-27 09:01:09,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78024.98, 'new_value': 84171.98}, {'field': 'total_amount', 'old_value': 80322.98, 'new_value': 86469.98}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-04-27 09:01:09,036 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-27 09:01:09,458 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M1I
2025-04-27 09:01:09,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47027.52, 'new_value': 48664.15}, {'field': 'offline_amount', 'old_value': 36420.38, 'new_value': 39610.18}, {'field': 'total_amount', 'old_value': 83447.9, 'new_value': 88274.33}, {'field': 'order_count', 'old_value': 6361, 'new_value': 6714}]
2025-04-27 09:01:09,458 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-27 09:01:09,958 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2I
2025-04-27 09:01:09,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232122.11, 'new_value': 244682.11}, {'field': 'total_amount', 'old_value': 236888.11, 'new_value': 249448.11}, {'field': 'order_count', 'old_value': 3391, 'new_value': 3552}]
2025-04-27 09:01:09,958 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-27 09:01:10,381 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M3I
2025-04-27 09:01:10,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 619221.0, 'new_value': 639363.0}, {'field': 'total_amount', 'old_value': 619221.0, 'new_value': 639363.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 137}]
2025-04-27 09:01:10,381 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-27 09:01:10,819 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5I
2025-04-27 09:01:10,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28409.0, 'new_value': 29875.0}, {'field': 'total_amount', 'old_value': 28409.0, 'new_value': 29875.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 187}]
2025-04-27 09:01:10,819 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6I
2025-04-27 09:01:11,428 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M6I
2025-04-27 09:01:11,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105340.0, 'new_value': 110940.0}, {'field': 'total_amount', 'old_value': 135840.0, 'new_value': 141440.0}, {'field': 'order_count', 'old_value': 1430, 'new_value': 1431}]
2025-04-27 09:01:11,428 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-27 09:01:11,851 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7I
2025-04-27 09:01:11,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66474.78, 'new_value': 71488.78}, {'field': 'offline_amount', 'old_value': 1202342.62, 'new_value': 1292400.14}, {'field': 'total_amount', 'old_value': 1268817.4, 'new_value': 1363888.92}, {'field': 'order_count', 'old_value': 9072, 'new_value': 9482}]
2025-04-27 09:01:11,851 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-27 09:01:12,257 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFI
2025-04-27 09:01:12,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 552319.0, 'new_value': 566648.0}, {'field': 'total_amount', 'old_value': 563357.0, 'new_value': 577686.0}, {'field': 'order_count', 'old_value': 489, 'new_value': 497}]
2025-04-27 09:01:12,257 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-27 09:01:12,680 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLI
2025-04-27 09:01:12,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104895.56, 'new_value': 112280.26}, {'field': 'offline_amount', 'old_value': 108285.95, 'new_value': 112794.15}, {'field': 'total_amount', 'old_value': 213181.51, 'new_value': 225074.41}, {'field': 'order_count', 'old_value': 4881, 'new_value': 5161}]
2025-04-27 09:01:12,680 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-27 09:01:13,055 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSI
2025-04-27 09:01:13,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1605675.0, 'new_value': 1719490.0}, {'field': 'total_amount', 'old_value': 1605675.0, 'new_value': 1719490.0}, {'field': 'order_count', 'old_value': 6542, 'new_value': 7027}]
2025-04-27 09:01:13,055 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-27 09:01:13,462 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-04-27 09:01:13,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39788.18, 'new_value': 42038.38}, {'field': 'offline_amount', 'old_value': 1000486.12, 'new_value': 1055052.21}, {'field': 'total_amount', 'old_value': 1040274.3, 'new_value': 1097090.59}, {'field': 'order_count', 'old_value': 5103, 'new_value': 5372}]
2025-04-27 09:01:13,462 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-27 09:01:13,868 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZI
2025-04-27 09:01:13,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216937.99, 'new_value': 235613.2}, {'field': 'total_amount', 'old_value': 216937.99, 'new_value': 235613.2}, {'field': 'order_count', 'old_value': 866, 'new_value': 971}]
2025-04-27 09:01:13,868 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-27 09:01:14,275 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M0J
2025-04-27 09:01:14,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120685.3, 'new_value': 129569.02}, {'field': 'total_amount', 'old_value': 143952.68, 'new_value': 152836.4}, {'field': 'order_count', 'old_value': 11579, 'new_value': 12079}]
2025-04-27 09:01:14,275 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-27 09:01:14,760 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M5J
2025-04-27 09:01:14,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26398.0, 'new_value': 28792.0}, {'field': 'total_amount', 'old_value': 26398.0, 'new_value': 28792.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 147}]
2025-04-27 09:01:14,760 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-27 09:01:15,213 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBJ
2025-04-27 09:01:15,213 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73759.9, 'new_value': 77332.4}, {'field': 'total_amount', 'old_value': 73759.9, 'new_value': 77332.4}, {'field': 'order_count', 'old_value': 263, 'new_value': 275}]
2025-04-27 09:01:15,213 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-27 09:01:15,667 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEJ
2025-04-27 09:01:15,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 962684.0, 'new_value': 1004965.0}, {'field': 'total_amount', 'old_value': 962684.0, 'new_value': 1004965.0}, {'field': 'order_count', 'old_value': 4052, 'new_value': 4259}]
2025-04-27 09:01:15,667 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFJ
2025-04-27 09:01:16,105 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFJ
2025-04-27 09:01:16,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2037700.0, 'new_value': 2494500.0}, {'field': 'total_amount', 'old_value': 2037700.0, 'new_value': 2494500.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-04-27 09:01:16,105 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-27 09:01:16,574 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MIJ
2025-04-27 09:01:16,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 865164.9, 'new_value': 901663.2}, {'field': 'total_amount', 'old_value': 865164.9, 'new_value': 901663.2}, {'field': 'order_count', 'old_value': 1092, 'new_value': 1152}]
2025-04-27 09:01:16,574 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-27 09:01:17,121 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMJ
2025-04-27 09:01:17,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189666.38, 'new_value': 229487.38}, {'field': 'total_amount', 'old_value': 189666.38, 'new_value': 229487.38}, {'field': 'order_count', 'old_value': 39, 'new_value': 47}]
2025-04-27 09:01:17,121 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-27 09:01:17,575 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MNJ
2025-04-27 09:01:17,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94686.0, 'new_value': 101470.0}, {'field': 'total_amount', 'old_value': 94686.0, 'new_value': 101470.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 274}]
2025-04-27 09:01:17,575 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTJ
2025-04-27 09:01:17,997 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTJ
2025-04-27 09:01:17,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106102.0, 'new_value': 119502.0}, {'field': 'total_amount', 'old_value': 106102.0, 'new_value': 119502.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-04-27 09:01:17,997 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-27 09:01:18,591 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWJ
2025-04-27 09:01:18,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70072.6, 'new_value': 74778.8}, {'field': 'total_amount', 'old_value': 70072.6, 'new_value': 74778.8}, {'field': 'order_count', 'old_value': 2038, 'new_value': 2176}]
2025-04-27 09:01:18,591 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M3
2025-04-27 09:01:19,092 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M3
2025-04-27 09:01:19,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6820.0, 'new_value': 7224.0}, {'field': 'offline_amount', 'old_value': 2508.5, 'new_value': 3608.5}, {'field': 'total_amount', 'old_value': 9328.5, 'new_value': 10832.5}, {'field': 'order_count', 'old_value': 82, 'new_value': 94}]
2025-04-27 09:01:19,092 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-27 09:01:19,655 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M6
2025-04-27 09:01:19,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101662.0, 'new_value': 106628.1}, {'field': 'total_amount', 'old_value': 101662.0, 'new_value': 106628.1}, {'field': 'order_count', 'old_value': 486, 'new_value': 516}]
2025-04-27 09:01:19,655 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-27 09:01:20,139 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-27 09:01:20,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159743.8, 'new_value': 171925.5}, {'field': 'total_amount', 'old_value': 159743.8, 'new_value': 171925.5}, {'field': 'order_count', 'old_value': 756, 'new_value': 825}]
2025-04-27 09:01:20,139 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-27 09:01:20,593 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-04-27 09:01:20,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15037.76, 'new_value': 15763.86}, {'field': 'offline_amount', 'old_value': 289369.7, 'new_value': 306548.3}, {'field': 'total_amount', 'old_value': 304407.46, 'new_value': 322312.16}, {'field': 'order_count', 'old_value': 15667, 'new_value': 16470}]
2025-04-27 09:01:20,593 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-27 09:01:21,047 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC
2025-04-27 09:01:21,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158767.0, 'new_value': 165247.0}, {'field': 'total_amount', 'old_value': 158767.0, 'new_value': 165247.0}, {'field': 'order_count', 'old_value': 11324, 'new_value': 11811}]
2025-04-27 09:01:21,047 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-27 09:01:21,484 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME
2025-04-27 09:01:21,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45029.25, 'new_value': 47268.29}, {'field': 'offline_amount', 'old_value': 43399.73, 'new_value': 45706.8}, {'field': 'total_amount', 'old_value': 88428.98, 'new_value': 92975.09}, {'field': 'order_count', 'old_value': 4638, 'new_value': 4902}]
2025-04-27 09:01:21,484 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-27 09:01:22,000 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG
2025-04-27 09:01:22,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 983811.0, 'new_value': 1033956.0}, {'field': 'total_amount', 'old_value': 983976.0, 'new_value': 1034121.0}, {'field': 'order_count', 'old_value': 1147, 'new_value': 1201}]
2025-04-27 09:01:22,000 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-27 09:01:22,454 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH
2025-04-27 09:01:22,470 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11125.62, 'new_value': 11385.22}, {'field': 'offline_amount', 'old_value': 134188.0, 'new_value': 141543.0}, {'field': 'total_amount', 'old_value': 145313.62, 'new_value': 152928.22}, {'field': 'order_count', 'old_value': 77, 'new_value': 81}]
2025-04-27 09:01:22,470 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-27 09:01:22,908 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML
2025-04-27 09:01:22,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120254.0, 'new_value': 127723.0}, {'field': 'total_amount', 'old_value': 145425.0, 'new_value': 152894.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-04-27 09:01:22,908 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-27 09:01:23,361 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO
2025-04-27 09:01:23,361 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46368.7, 'new_value': 48135.0}, {'field': 'offline_amount', 'old_value': 42414.98, 'new_value': 44007.98}, {'field': 'total_amount', 'old_value': 88783.68, 'new_value': 92142.98}, {'field': 'order_count', 'old_value': 304, 'new_value': 319}]
2025-04-27 09:01:23,361 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-27 09:01:23,799 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP
2025-04-27 09:01:23,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77796.56, 'new_value': 84182.95}, {'field': 'total_amount', 'old_value': 80027.97, 'new_value': 86414.36}, {'field': 'order_count', 'old_value': 423, 'new_value': 457}]
2025-04-27 09:01:23,799 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-27 09:01:24,268 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ
2025-04-27 09:01:24,268 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 277258.24, 'new_value': 289444.11}, {'field': 'offline_amount', 'old_value': 1340948.91, 'new_value': 1422720.95}, {'field': 'total_amount', 'old_value': 1618207.15, 'new_value': 1712165.06}, {'field': 'order_count', 'old_value': 8306, 'new_value': 8756}]
2025-04-27 09:01:24,268 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-04-27 09:01:24,675 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR
2025-04-27 09:01:24,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48509.0, 'new_value': 51847.0}, {'field': 'total_amount', 'old_value': 48509.0, 'new_value': 51847.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 53}]
2025-04-27 09:01:24,675 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-27 09:01:25,175 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT
2025-04-27 09:01:25,175 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109221.59, 'new_value': 112219.78}, {'field': 'offline_amount', 'old_value': 123377.85, 'new_value': 127810.9}, {'field': 'total_amount', 'old_value': 232599.44, 'new_value': 240030.68}, {'field': 'order_count', 'old_value': 5998, 'new_value': 6174}]
2025-04-27 09:01:25,175 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV
2025-04-27 09:01:25,660 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV
2025-04-27 09:01:25,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6680.0}, {'field': 'total_amount', 'old_value': 85540.0, 'new_value': 92220.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-04-27 09:01:25,660 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-27 09:01:26,098 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ
2025-04-27 09:01:26,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36007.71, 'new_value': 38296.91}, {'field': 'offline_amount', 'old_value': 91433.0, 'new_value': 95647.0}, {'field': 'total_amount', 'old_value': 127440.71, 'new_value': 133943.91}, {'field': 'order_count', 'old_value': 1564, 'new_value': 1647}]
2025-04-27 09:01:26,098 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-27 09:01:26,551 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M01
2025-04-27 09:01:26,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1215.38, 'new_value': 1831.38}, {'field': 'offline_amount', 'old_value': 65956.8, 'new_value': 68429.31}, {'field': 'total_amount', 'old_value': 67172.18, 'new_value': 70260.69}, {'field': 'order_count', 'old_value': 580, 'new_value': 620}]
2025-04-27 09:01:26,551 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-27 09:01:27,005 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M11
2025-04-27 09:01:27,005 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11759.0, 'new_value': 11845.0}, {'field': 'offline_amount', 'old_value': 74775.0, 'new_value': 78554.0}, {'field': 'total_amount', 'old_value': 86534.0, 'new_value': 90399.0}, {'field': 'order_count', 'old_value': 643, 'new_value': 671}]
2025-04-27 09:01:27,005 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-27 09:01:27,474 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M31
2025-04-27 09:01:27,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 526694.5, 'new_value': 529393.5}, {'field': 'total_amount', 'old_value': 526694.5, 'new_value': 529393.5}, {'field': 'order_count', 'old_value': 171, 'new_value': 172}]
2025-04-27 09:01:27,474 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M41
2025-04-27 09:01:27,943 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M41
2025-04-27 09:01:27,943 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-04-27 09:01:27,943 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-27 09:01:28,381 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M51
2025-04-27 09:01:28,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 231122.7, 'new_value': 242042.7}, {'field': 'total_amount', 'old_value': 231122.7, 'new_value': 242042.7}, {'field': 'order_count', 'old_value': 1037, 'new_value': 1089}]
2025-04-27 09:01:28,381 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-27 09:01:28,757 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M61
2025-04-27 09:01:28,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137028.0, 'new_value': 144096.0}, {'field': 'offline_amount', 'old_value': 71071.0, 'new_value': 74861.0}, {'field': 'total_amount', 'old_value': 208099.0, 'new_value': 218957.0}, {'field': 'order_count', 'old_value': 477, 'new_value': 502}]
2025-04-27 09:01:28,757 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-27 09:01:29,179 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M91
2025-04-27 09:01:29,179 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152899.0, 'new_value': 158578.0}, {'field': 'offline_amount', 'old_value': 223727.0, 'new_value': 233429.0}, {'field': 'total_amount', 'old_value': 376626.0, 'new_value': 392007.0}, {'field': 'order_count', 'old_value': 840, 'new_value': 884}]
2025-04-27 09:01:29,179 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-27 09:01:29,632 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB1
2025-04-27 09:01:29,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189204.0, 'new_value': 201693.0}, {'field': 'total_amount', 'old_value': 205078.0, 'new_value': 217567.0}, {'field': 'order_count', 'old_value': 290, 'new_value': 306}]
2025-04-27 09:01:29,632 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-27 09:01:30,055 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MC1
2025-04-27 09:01:30,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11558.0, 'new_value': 11815.0}, {'field': 'offline_amount', 'old_value': 129218.0, 'new_value': 143850.0}, {'field': 'total_amount', 'old_value': 140776.0, 'new_value': 155665.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 265}]
2025-04-27 09:01:30,055 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-04-27 09:01:30,414 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD1
2025-04-27 09:01:30,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15310.0, 'new_value': 16310.0}, {'field': 'total_amount', 'old_value': 15310.0, 'new_value': 16310.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-04-27 09:01:30,414 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-27 09:01:30,884 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MG1
2025-04-27 09:01:30,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3160.0, 'new_value': 3420.0}, {'field': 'offline_amount', 'old_value': 41195.0, 'new_value': 42410.0}, {'field': 'total_amount', 'old_value': 44355.0, 'new_value': 45830.0}, {'field': 'order_count', 'old_value': 606, 'new_value': 624}]
2025-04-27 09:01:30,884 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-27 09:01:31,337 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-27 09:01:31,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22048.59, 'new_value': 23339.26}, {'field': 'total_amount', 'old_value': 22048.59, 'new_value': 23339.26}, {'field': 'order_count', 'old_value': 116, 'new_value': 121}]
2025-04-27 09:01:31,337 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-27 09:01:31,806 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MP1
2025-04-27 09:01:31,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327572.0, 'new_value': 343954.0}, {'field': 'total_amount', 'old_value': 338362.0, 'new_value': 354744.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 282}]
2025-04-27 09:01:31,806 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-27 09:01:32,228 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-04-27 09:01:32,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3533400.0, 'new_value': 3605940.0}, {'field': 'offline_amount', 'old_value': 857850.0, 'new_value': 857851.0}, {'field': 'total_amount', 'old_value': 4391250.0, 'new_value': 4463791.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-04-27 09:01:32,228 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-27 09:01:32,745 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MZ1
2025-04-27 09:01:32,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90053.0, 'new_value': 95530.0}, {'field': 'total_amount', 'old_value': 90053.0, 'new_value': 95530.0}, {'field': 'order_count', 'old_value': 6146, 'new_value': 6510}]
2025-04-27 09:01:32,745 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-27 09:01:33,214 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M02
2025-04-27 09:01:33,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115003.4, 'new_value': 121335.94}, {'field': 'total_amount', 'old_value': 115003.4, 'new_value': 121335.94}, {'field': 'order_count', 'old_value': 8116, 'new_value': 8608}]
2025-04-27 09:01:33,214 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-27 09:01:33,636 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M12
2025-04-27 09:01:33,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 575252.91, 'new_value': 615015.35}, {'field': 'total_amount', 'old_value': 575252.91, 'new_value': 615015.35}, {'field': 'order_count', 'old_value': 3320, 'new_value': 3555}]
2025-04-27 09:01:33,636 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-27 09:01:34,152 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M72
2025-04-27 09:01:34,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40350.0, 'new_value': 42567.0}, {'field': 'total_amount', 'old_value': 40350.0, 'new_value': 42567.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-04-27 09:01:34,168 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-04-27 09:01:34,590 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9M92
2025-04-27 09:01:34,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53212.0, 'new_value': 57219.0}, {'field': 'total_amount', 'old_value': 53212.0, 'new_value': 57219.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-04-27 09:01:34,590 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-27 09:01:35,012 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MB2
2025-04-27 09:01:35,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 323563.0, 'new_value': 337493.0}, {'field': 'total_amount', 'old_value': 323563.0, 'new_value': 337493.0}, {'field': 'order_count', 'old_value': 7156, 'new_value': 7491}]
2025-04-27 09:01:35,012 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-27 09:01:35,466 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MC2
2025-04-27 09:01:35,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280268.75, 'new_value': 293256.19}, {'field': 'total_amount', 'old_value': 280268.75, 'new_value': 293256.19}, {'field': 'order_count', 'old_value': 811, 'new_value': 849}]
2025-04-27 09:01:35,466 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-27 09:01:35,935 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MO2
2025-04-27 09:01:35,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9826.1, 'new_value': 10717.9}, {'field': 'offline_amount', 'old_value': 25804.98, 'new_value': 32809.42}, {'field': 'total_amount', 'old_value': 35631.08, 'new_value': 43527.32}, {'field': 'order_count', 'old_value': 430, 'new_value': 445}]
2025-04-27 09:01:35,935 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-27 09:01:36,514 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MS2
2025-04-27 09:01:36,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 362530.0, 'new_value': 369330.0}, {'field': 'total_amount', 'old_value': 362530.0, 'new_value': 369330.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 141}]
2025-04-27 09:01:36,514 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-27 09:01:36,967 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK6
2025-04-27 09:01:36,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293352.1, 'new_value': 296623.5}, {'field': 'total_amount', 'old_value': 293352.1, 'new_value': 296623.5}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-04-27 09:01:36,967 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-27 09:01:37,327 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN6
2025-04-27 09:01:37,327 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96697.0, 'new_value': 99936.0}, {'field': 'offline_amount', 'old_value': 1129977.0, 'new_value': 1183938.0}, {'field': 'total_amount', 'old_value': 1226674.0, 'new_value': 1283874.0}, {'field': 'order_count', 'old_value': 28503, 'new_value': 29716}]
2025-04-27 09:01:37,327 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-27 09:01:37,796 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M37
2025-04-27 09:01:37,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 318783.0, 'new_value': 361939.0}, {'field': 'total_amount', 'old_value': 329945.0, 'new_value': 373101.0}, {'field': 'order_count', 'old_value': 7313, 'new_value': 8374}]
2025-04-27 09:01:37,796 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-27 09:01:38,250 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-27 09:01:38,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45649.0, 'new_value': 50649.0}, {'field': 'total_amount', 'old_value': 45649.0, 'new_value': 50649.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-04-27 09:01:38,250 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-27 09:01:38,703 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-04-27 09:01:38,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151252.0, 'new_value': 157708.0}, {'field': 'total_amount', 'old_value': 153901.0, 'new_value': 160357.0}, {'field': 'order_count', 'old_value': 628, 'new_value': 657}]
2025-04-27 09:01:38,703 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-27 09:01:39,141 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M12
2025-04-27 09:01:39,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17547.52, 'new_value': 23870.15}, {'field': 'offline_amount', 'old_value': 333632.84, 'new_value': 341402.05}, {'field': 'total_amount', 'old_value': 351180.36, 'new_value': 365272.2}, {'field': 'order_count', 'old_value': 4049, 'new_value': 4221}]
2025-04-27 09:01:39,141 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-27 09:01:39,579 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-27 09:01:39,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151775.06, 'new_value': 174820.41}, {'field': 'offline_amount', 'old_value': 137193.59, 'new_value': 152114.03}, {'field': 'total_amount', 'old_value': 288968.65, 'new_value': 326934.44}, {'field': 'order_count', 'old_value': 966, 'new_value': 1079}]
2025-04-27 09:01:39,579 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-27 09:01:40,001 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-27 09:01:40,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54142.0, 'new_value': 78522.0}, {'field': 'total_amount', 'old_value': 54142.0, 'new_value': 78522.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-04-27 09:01:40,001 - INFO - 日期 2025-04 处理完成 - 更新: 165 条，插入: 0 条，错误: 0 条
2025-04-27 09:01:40,001 - INFO - 数据同步完成！更新: 165 条，插入: 0 条，错误: 0 条
2025-04-27 09:01:40,017 - INFO - =================同步完成====================
2025-04-27 12:00:03,499 - INFO - =================使用默认全量同步=============
2025-04-27 12:00:04,610 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-27 12:00:04,610 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 12:00:04,625 - INFO - 开始处理日期: 2025-01
2025-04-27 12:00:04,625 - INFO - Request Parameters - Page 1:
2025-04-27 12:00:04,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:04,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:05,438 - INFO - Response - Page 1:
2025-04-27 12:00:05,642 - INFO - 第 1 页获取到 100 条记录
2025-04-27 12:00:05,642 - INFO - Request Parameters - Page 2:
2025-04-27 12:00:05,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:05,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:06,330 - INFO - Response - Page 2:
2025-04-27 12:00:06,533 - INFO - 第 2 页获取到 100 条记录
2025-04-27 12:00:06,533 - INFO - Request Parameters - Page 3:
2025-04-27 12:00:06,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:06,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:07,018 - INFO - Response - Page 3:
2025-04-27 12:00:07,221 - INFO - 第 3 页获取到 100 条记录
2025-04-27 12:00:07,221 - INFO - Request Parameters - Page 4:
2025-04-27 12:00:07,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:07,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:07,784 - INFO - Response - Page 4:
2025-04-27 12:00:07,988 - INFO - 第 4 页获取到 100 条记录
2025-04-27 12:00:07,988 - INFO - Request Parameters - Page 5:
2025-04-27 12:00:07,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:07,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:08,488 - INFO - Response - Page 5:
2025-04-27 12:00:08,691 - INFO - 第 5 页获取到 100 条记录
2025-04-27 12:00:08,691 - INFO - Request Parameters - Page 6:
2025-04-27 12:00:08,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:08,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:09,192 - INFO - Response - Page 6:
2025-04-27 12:00:09,395 - INFO - 第 6 页获取到 100 条记录
2025-04-27 12:00:09,395 - INFO - Request Parameters - Page 7:
2025-04-27 12:00:09,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:09,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:09,880 - INFO - Response - Page 7:
2025-04-27 12:00:10,083 - INFO - 第 7 页获取到 82 条记录
2025-04-27 12:00:10,083 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 12:00:10,083 - INFO - 获取到 682 条表单数据
2025-04-27 12:00:10,083 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 12:00:10,099 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 12:00:10,099 - INFO - 开始处理日期: 2025-02
2025-04-27 12:00:10,099 - INFO - Request Parameters - Page 1:
2025-04-27 12:00:10,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:10,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:10,662 - INFO - Response - Page 1:
2025-04-27 12:00:10,865 - INFO - 第 1 页获取到 100 条记录
2025-04-27 12:00:10,865 - INFO - Request Parameters - Page 2:
2025-04-27 12:00:10,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:10,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:11,366 - INFO - Response - Page 2:
2025-04-27 12:00:11,569 - INFO - 第 2 页获取到 100 条记录
2025-04-27 12:00:11,569 - INFO - Request Parameters - Page 3:
2025-04-27 12:00:11,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:11,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:12,069 - INFO - Response - Page 3:
2025-04-27 12:00:12,273 - INFO - 第 3 页获取到 100 条记录
2025-04-27 12:00:12,273 - INFO - Request Parameters - Page 4:
2025-04-27 12:00:12,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:12,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:12,789 - INFO - Response - Page 4:
2025-04-27 12:00:12,992 - INFO - 第 4 页获取到 100 条记录
2025-04-27 12:00:12,992 - INFO - Request Parameters - Page 5:
2025-04-27 12:00:12,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:12,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:13,430 - INFO - Response - Page 5:
2025-04-27 12:00:13,633 - INFO - 第 5 页获取到 100 条记录
2025-04-27 12:00:13,633 - INFO - Request Parameters - Page 6:
2025-04-27 12:00:13,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:13,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:14,056 - INFO - Response - Page 6:
2025-04-27 12:00:14,259 - INFO - 第 6 页获取到 100 条记录
2025-04-27 12:00:14,259 - INFO - Request Parameters - Page 7:
2025-04-27 12:00:14,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:14,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:14,759 - INFO - Response - Page 7:
2025-04-27 12:00:14,963 - INFO - 第 7 页获取到 70 条记录
2025-04-27 12:00:14,963 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 12:00:14,963 - INFO - 获取到 670 条表单数据
2025-04-27 12:00:14,963 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 12:00:14,978 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 12:00:14,978 - INFO - 开始处理日期: 2025-03
2025-04-27 12:00:14,978 - INFO - Request Parameters - Page 1:
2025-04-27 12:00:14,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:14,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:15,447 - INFO - Response - Page 1:
2025-04-27 12:00:15,651 - INFO - 第 1 页获取到 100 条记录
2025-04-27 12:00:15,651 - INFO - Request Parameters - Page 2:
2025-04-27 12:00:15,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:15,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:16,183 - INFO - Response - Page 2:
2025-04-27 12:00:16,386 - INFO - 第 2 页获取到 100 条记录
2025-04-27 12:00:16,386 - INFO - Request Parameters - Page 3:
2025-04-27 12:00:16,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:16,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:16,902 - INFO - Response - Page 3:
2025-04-27 12:00:17,121 - INFO - 第 3 页获取到 100 条记录
2025-04-27 12:00:17,121 - INFO - Request Parameters - Page 4:
2025-04-27 12:00:17,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:17,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:17,637 - INFO - Response - Page 4:
2025-04-27 12:00:17,840 - INFO - 第 4 页获取到 100 条记录
2025-04-27 12:00:17,840 - INFO - Request Parameters - Page 5:
2025-04-27 12:00:17,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:17,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:18,309 - INFO - Response - Page 5:
2025-04-27 12:00:18,513 - INFO - 第 5 页获取到 100 条记录
2025-04-27 12:00:18,513 - INFO - Request Parameters - Page 6:
2025-04-27 12:00:18,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:18,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:19,091 - INFO - Response - Page 6:
2025-04-27 12:00:19,295 - INFO - 第 6 页获取到 100 条记录
2025-04-27 12:00:19,295 - INFO - Request Parameters - Page 7:
2025-04-27 12:00:19,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:19,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:19,701 - INFO - Response - Page 7:
2025-04-27 12:00:19,905 - INFO - 第 7 页获取到 61 条记录
2025-04-27 12:00:19,905 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 12:00:19,905 - INFO - 获取到 661 条表单数据
2025-04-27 12:00:19,905 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 12:00:19,920 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 12:00:19,920 - INFO - 开始处理日期: 2025-04
2025-04-27 12:00:19,920 - INFO - Request Parameters - Page 1:
2025-04-27 12:00:19,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:19,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:20,436 - INFO - Response - Page 1:
2025-04-27 12:00:20,640 - INFO - 第 1 页获取到 100 条记录
2025-04-27 12:00:20,640 - INFO - Request Parameters - Page 2:
2025-04-27 12:00:20,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:20,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:21,109 - INFO - Response - Page 2:
2025-04-27 12:00:21,312 - INFO - 第 2 页获取到 100 条记录
2025-04-27 12:00:21,312 - INFO - Request Parameters - Page 3:
2025-04-27 12:00:21,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:21,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:21,797 - INFO - Response - Page 3:
2025-04-27 12:00:22,000 - INFO - 第 3 页获取到 100 条记录
2025-04-27 12:00:22,000 - INFO - Request Parameters - Page 4:
2025-04-27 12:00:22,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:22,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:22,532 - INFO - Response - Page 4:
2025-04-27 12:00:22,735 - INFO - 第 4 页获取到 100 条记录
2025-04-27 12:00:22,735 - INFO - Request Parameters - Page 5:
2025-04-27 12:00:22,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:22,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:23,204 - INFO - Response - Page 5:
2025-04-27 12:00:23,408 - INFO - 第 5 页获取到 100 条记录
2025-04-27 12:00:23,408 - INFO - Request Parameters - Page 6:
2025-04-27 12:00:23,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:23,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:23,877 - INFO - Response - Page 6:
2025-04-27 12:00:24,080 - INFO - 第 6 页获取到 100 条记录
2025-04-27 12:00:24,080 - INFO - Request Parameters - Page 7:
2025-04-27 12:00:24,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 12:00:24,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 12:00:24,409 - INFO - Response - Page 7:
2025-04-27 12:00:24,612 - INFO - 第 7 页获取到 25 条记录
2025-04-27 12:00:24,612 - INFO - 查询完成，共获取到 625 条记录
2025-04-27 12:00:24,612 - INFO - 获取到 625 条表单数据
2025-04-27 12:00:24,612 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-27 12:00:24,612 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-27 12:00:25,112 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MZ2
2025-04-27 12:00:25,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37955.92, 'new_value': 39539.2}, {'field': 'total_amount', 'old_value': 38162.92, 'new_value': 39746.2}, {'field': 'order_count', 'old_value': 4821, 'new_value': 5038}]
2025-04-27 12:00:25,112 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-27 12:00:25,535 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M03
2025-04-27 12:00:25,535 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 558, 'new_value': 583}]
2025-04-27 12:00:25,535 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M23
2025-04-27 12:00:25,988 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M23
2025-04-27 12:00:25,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 350000.0}, {'field': 'total_amount', 'old_value': 2868000.0, 'new_value': 3218000.0}]
2025-04-27 12:00:25,988 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-27 12:00:26,473 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M43
2025-04-27 12:00:26,473 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126892.64, 'new_value': 136008.18}, {'field': 'total_amount', 'old_value': 126892.64, 'new_value': 136008.18}, {'field': 'order_count', 'old_value': 682, 'new_value': 722}]
2025-04-27 12:00:26,473 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-27 12:00:26,927 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-27 12:00:26,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24909.2, 'new_value': 25935.6}, {'field': 'total_amount', 'old_value': 24909.2, 'new_value': 25935.6}, {'field': 'order_count', 'old_value': 195, 'new_value': 208}]
2025-04-27 12:00:26,927 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-27 12:00:27,318 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-04-27 12:00:27,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27244.71, 'new_value': 28022.71}, {'field': 'offline_amount', 'old_value': 15021.98, 'new_value': 15367.98}, {'field': 'total_amount', 'old_value': 42266.69, 'new_value': 43390.69}, {'field': 'order_count', 'old_value': 1873, 'new_value': 1936}]
2025-04-27 12:00:27,318 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-27 12:00:27,755 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MD3
2025-04-27 12:00:27,755 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 352394.0, 'new_value': 358161.0}, {'field': 'offline_amount', 'old_value': 619630.0, 'new_value': 675197.0}, {'field': 'total_amount', 'old_value': 972024.0, 'new_value': 1033358.0}, {'field': 'order_count', 'old_value': 935, 'new_value': 972}]
2025-04-27 12:00:27,755 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-27 12:00:28,209 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MF3
2025-04-27 12:00:28,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21598.26, 'new_value': 23278.26}, {'field': 'total_amount', 'old_value': 21598.26, 'new_value': 23278.26}, {'field': 'order_count', 'old_value': 89, 'new_value': 93}]
2025-04-27 12:00:28,209 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-04-27 12:00:28,678 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MH3
2025-04-27 12:00:28,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53067.1, 'new_value': 65487.1}, {'field': 'total_amount', 'old_value': 53067.1, 'new_value': 65487.1}, {'field': 'order_count', 'old_value': 18, 'new_value': 27}]
2025-04-27 12:00:28,678 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-04-27 12:00:29,116 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MI3
2025-04-27 12:00:29,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236760.0, 'new_value': 237938.0}, {'field': 'total_amount', 'old_value': 236760.0, 'new_value': 237938.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-04-27 12:00:29,116 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-27 12:00:29,679 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MJ3
2025-04-27 12:00:29,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179754.0, 'new_value': 185334.0}, {'field': 'total_amount', 'old_value': 179754.0, 'new_value': 185334.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-04-27 12:00:29,679 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-27 12:00:30,117 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MM3
2025-04-27 12:00:30,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2004.73, 'new_value': 3987.73}, {'field': 'offline_amount', 'old_value': 20676.51, 'new_value': 23041.41}, {'field': 'total_amount', 'old_value': 22681.24, 'new_value': 27029.14}, {'field': 'order_count', 'old_value': 43, 'new_value': 2027}]
2025-04-27 12:00:30,117 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-27 12:00:30,617 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MN3
2025-04-27 12:00:30,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2980.0, 'new_value': 5480.0}, {'field': 'total_amount', 'old_value': 65940.0, 'new_value': 68440.0}, {'field': 'order_count', 'old_value': 679, 'new_value': 702}]
2025-04-27 12:00:30,617 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-27 12:00:31,055 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ3
2025-04-27 12:00:31,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 548731.0, 'new_value': 560889.0}, {'field': 'total_amount', 'old_value': 634924.0, 'new_value': 647082.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-04-27 12:00:31,055 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-27 12:00:31,603 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV3
2025-04-27 12:00:31,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3811.05, 'new_value': 4513.35}, {'field': 'offline_amount', 'old_value': 50363.7, 'new_value': 56020.7}, {'field': 'total_amount', 'old_value': 54174.75, 'new_value': 60534.05}, {'field': 'order_count', 'old_value': 3739, 'new_value': 3902}]
2025-04-27 12:00:31,603 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX3
2025-04-27 12:00:32,103 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX3
2025-04-27 12:00:32,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39109.9, 'new_value': 39159.8}, {'field': 'total_amount', 'old_value': 41589.9, 'new_value': 41639.8}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-04-27 12:00:32,103 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-27 12:00:32,572 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY3
2025-04-27 12:00:32,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41657.47, 'new_value': 44738.59}, {'field': 'total_amount', 'old_value': 41657.47, 'new_value': 44738.59}, {'field': 'order_count', 'old_value': 8333, 'new_value': 8950}]
2025-04-27 12:00:32,572 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-27 12:00:33,010 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-04-27 12:00:33,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53475.0, 'new_value': 63475.0}, {'field': 'offline_amount', 'old_value': 70942.0, 'new_value': 76181.0}, {'field': 'total_amount', 'old_value': 124417.0, 'new_value': 139656.0}, {'field': 'order_count', 'old_value': 1768, 'new_value': 1971}]
2025-04-27 12:00:33,010 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-27 12:00:33,495 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M14
2025-04-27 12:00:33,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129170.0, 'new_value': 135681.0}, {'field': 'total_amount', 'old_value': 180580.0, 'new_value': 187091.0}, {'field': 'order_count', 'old_value': 3616, 'new_value': 3719}]
2025-04-27 12:00:33,495 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-04-27 12:00:33,933 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M84
2025-04-27 12:00:33,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6152.05, 'new_value': 6812.55}, {'field': 'total_amount', 'old_value': 6152.05, 'new_value': 6812.55}, {'field': 'order_count', 'old_value': 927, 'new_value': 939}]
2025-04-27 12:00:33,933 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-04-27 12:00:34,402 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MB4
2025-04-27 12:00:34,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89793.0, 'new_value': 89858.0}, {'field': 'total_amount', 'old_value': 89793.0, 'new_value': 89858.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-04-27 12:00:34,402 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-27 12:00:34,809 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF4
2025-04-27 12:00:34,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6136.8, 'new_value': 6523.8}, {'field': 'offline_amount', 'old_value': 43884.3, 'new_value': 44350.0}, {'field': 'total_amount', 'old_value': 50021.1, 'new_value': 50873.8}, {'field': 'order_count', 'old_value': 510, 'new_value': 520}]
2025-04-27 12:00:34,809 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-27 12:00:35,278 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MN4
2025-04-27 12:00:35,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 607335.7, 'new_value': 642947.7}, {'field': 'total_amount', 'old_value': 607335.7, 'new_value': 642947.7}, {'field': 'order_count', 'old_value': 83, 'new_value': 85}]
2025-04-27 12:00:35,278 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-27 12:00:35,684 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ4
2025-04-27 12:00:35,684 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102836.9, 'new_value': 106038.9}, {'field': 'offline_amount', 'old_value': 160745.25, 'new_value': 166924.25}, {'field': 'total_amount', 'old_value': 263582.15, 'new_value': 272963.15}, {'field': 'order_count', 'old_value': 5737, 'new_value': 5937}]
2025-04-27 12:00:35,684 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-27 12:00:36,091 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-04-27 12:00:36,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82930.86, 'new_value': 86205.71}, {'field': 'offline_amount', 'old_value': 770642.28, 'new_value': 805390.78}, {'field': 'total_amount', 'old_value': 853573.14, 'new_value': 891596.49}, {'field': 'order_count', 'old_value': 3883, 'new_value': 4007}]
2025-04-27 12:00:36,091 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-27 12:00:36,482 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MT4
2025-04-27 12:00:36,482 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60907.13, 'new_value': 62860.93}, {'field': 'offline_amount', 'old_value': 636629.11, 'new_value': 662367.21}, {'field': 'total_amount', 'old_value': 697536.24, 'new_value': 725228.14}, {'field': 'order_count', 'old_value': 2861, 'new_value': 2972}]
2025-04-27 12:00:36,482 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-27 12:00:36,967 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MW4
2025-04-27 12:00:36,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75614.33, 'new_value': 81425.35}, {'field': 'total_amount', 'old_value': 78683.66, 'new_value': 84494.68}, {'field': 'order_count', 'old_value': 4581, 'new_value': 4991}]
2025-04-27 12:00:36,967 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-27 12:00:37,389 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-04-27 12:00:37,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 605322.77, 'new_value': 634907.33}, {'field': 'total_amount', 'old_value': 605322.77, 'new_value': 634907.33}, {'field': 'order_count', 'old_value': 4281, 'new_value': 4495}]
2025-04-27 12:00:37,389 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY4
2025-04-27 12:00:37,858 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MY4
2025-04-27 12:00:37,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30089.64, 'new_value': 32197.64}, {'field': 'total_amount', 'old_value': 30089.64, 'new_value': 32197.64}, {'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-04-27 12:00:37,858 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-27 12:00:38,343 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-04-27 12:00:38,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202471.66, 'new_value': 210851.96}, {'field': 'total_amount', 'old_value': 202471.66, 'new_value': 210851.96}, {'field': 'order_count', 'old_value': 1027, 'new_value': 1074}]
2025-04-27 12:00:38,343 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-27 12:00:38,781 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MI5
2025-04-27 12:00:38,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136542.29, 'new_value': 136642.29}, {'field': 'total_amount', 'old_value': 136542.29, 'new_value': 136642.29}, {'field': 'order_count', 'old_value': 243, 'new_value': 245}]
2025-04-27 12:00:38,781 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-27 12:00:39,219 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MK5
2025-04-27 12:00:39,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17817.0, 'new_value': 20736.0}, {'field': 'total_amount', 'old_value': 22964.0, 'new_value': 25883.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 162}]
2025-04-27 12:00:39,219 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-27 12:00:39,719 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-04-27 12:00:39,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188590.0, 'new_value': 201294.0}, {'field': 'total_amount', 'old_value': 201091.0, 'new_value': 213795.0}, {'field': 'order_count', 'old_value': 1098, 'new_value': 1166}]
2025-04-27 12:00:39,719 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-27 12:00:40,173 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MB9
2025-04-27 12:00:40,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91282.8, 'new_value': 101291.5}, {'field': 'total_amount', 'old_value': 91282.8, 'new_value': 101291.5}, {'field': 'order_count', 'old_value': 183, 'new_value': 198}]
2025-04-27 12:00:40,173 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-27 12:00:40,548 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MC9
2025-04-27 12:00:40,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1568988.93, 'new_value': 1637104.38}, {'field': 'total_amount', 'old_value': 1568988.93, 'new_value': 1637104.38}, {'field': 'order_count', 'old_value': 12308, 'new_value': 12891}]
2025-04-27 12:00:40,548 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-27 12:00:41,002 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MM9
2025-04-27 12:00:41,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137363.91, 'new_value': 142534.57}, {'field': 'offline_amount', 'old_value': 122740.94, 'new_value': 130121.98}, {'field': 'total_amount', 'old_value': 260104.85, 'new_value': 272656.55}, {'field': 'order_count', 'old_value': 833, 'new_value': 869}]
2025-04-27 12:00:41,002 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-04-27 12:00:41,455 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MS9
2025-04-27 12:00:41,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31352.35, 'new_value': 33834.5}, {'field': 'total_amount', 'old_value': 31400.35, 'new_value': 33882.5}, {'field': 'order_count', 'old_value': 43, 'new_value': 66}]
2025-04-27 12:00:41,455 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-04-27 12:00:41,909 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MW9
2025-04-27 12:00:41,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50510.0, 'new_value': 62566.0}, {'field': 'total_amount', 'old_value': 50510.0, 'new_value': 62566.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 242}]
2025-04-27 12:00:41,909 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-04-27 12:00:42,394 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-04-27 12:00:42,394 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163724.36, 'new_value': 168960.85}, {'field': 'offline_amount', 'old_value': 21790.16, 'new_value': 24161.16}, {'field': 'total_amount', 'old_value': 185514.52, 'new_value': 193122.01}, {'field': 'order_count', 'old_value': 5181, 'new_value': 5404}]
2025-04-27 12:00:42,394 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-27 12:00:42,847 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-27 12:00:42,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60202.99, 'new_value': 88513.99}, {'field': 'total_amount', 'old_value': 671707.06, 'new_value': 700018.06}, {'field': 'order_count', 'old_value': 2234, 'new_value': 2320}]
2025-04-27 12:00:42,847 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-27 12:00:43,332 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-04-27 12:00:43,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4233.6, 'new_value': 6682.6}, {'field': 'offline_amount', 'old_value': 90240.9, 'new_value': 99707.9}, {'field': 'total_amount', 'old_value': 94474.5, 'new_value': 106390.5}, {'field': 'order_count', 'old_value': 132, 'new_value': 139}]
2025-04-27 12:00:43,332 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-27 12:00:43,770 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3A
2025-04-27 12:00:43,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22556.42, 'new_value': 23424.42}, {'field': 'total_amount', 'old_value': 22556.42, 'new_value': 23424.42}, {'field': 'order_count', 'old_value': 235, 'new_value': 243}]
2025-04-27 12:00:43,770 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-27 12:00:44,208 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4A
2025-04-27 12:00:44,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6442.5, 'new_value': 6991.7}, {'field': 'offline_amount', 'old_value': 35645.0, 'new_value': 40529.0}, {'field': 'total_amount', 'old_value': 42087.5, 'new_value': 47520.7}, {'field': 'order_count', 'old_value': 489, 'new_value': 499}]
2025-04-27 12:00:44,208 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-04-27 12:00:44,614 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M7A
2025-04-27 12:00:44,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80272.0, 'new_value': 86017.0}, {'field': 'total_amount', 'old_value': 84881.27, 'new_value': 90626.27}, {'field': 'order_count', 'old_value': 72, 'new_value': 77}]
2025-04-27 12:00:44,614 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-27 12:00:45,177 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-04-27 12:00:45,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 152793.54, 'new_value': 158486.08}, {'field': 'offline_amount', 'old_value': 638230.84, 'new_value': 661277.84}, {'field': 'total_amount', 'old_value': 791024.38, 'new_value': 819763.92}, {'field': 'order_count', 'old_value': 1768, 'new_value': 1849}]
2025-04-27 12:00:45,177 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-27 12:00:45,694 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-04-27 12:00:45,694 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19070.08, 'new_value': 19769.0}, {'field': 'offline_amount', 'old_value': 350066.7, 'new_value': 368000.7}, {'field': 'total_amount', 'old_value': 369136.78, 'new_value': 387769.7}, {'field': 'order_count', 'old_value': 2678, 'new_value': 2791}]
2025-04-27 12:00:45,694 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-27 12:00:46,194 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MEA
2025-04-27 12:00:46,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 170552.97, 'new_value': 176981.66}, {'field': 'offline_amount', 'old_value': 332420.96, 'new_value': 360355.01}, {'field': 'total_amount', 'old_value': 502973.93, 'new_value': 537336.67}, {'field': 'order_count', 'old_value': 3804, 'new_value': 4031}]
2025-04-27 12:00:46,194 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-27 12:00:46,773 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHA
2025-04-27 12:00:46,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8458.5, 'new_value': 10066.5}, {'field': 'offline_amount', 'old_value': 13939.7, 'new_value': 14861.7}, {'field': 'total_amount', 'old_value': 22398.2, 'new_value': 24928.2}, {'field': 'order_count', 'old_value': 61, 'new_value': 74}]
2025-04-27 12:00:46,773 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-04-27 12:00:47,242 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MMA
2025-04-27 12:00:47,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85068.0, 'new_value': 111042.0}, {'field': 'offline_amount', 'old_value': 61461.0, 'new_value': 62253.0}, {'field': 'total_amount', 'old_value': 146529.0, 'new_value': 173295.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 71}]
2025-04-27 12:00:47,242 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNA
2025-04-27 12:00:47,695 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MNA
2025-04-27 12:00:47,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211483.0, 'new_value': 213883.0}, {'field': 'total_amount', 'old_value': 214798.0, 'new_value': 217198.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-04-27 12:00:47,695 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-27 12:00:48,102 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQA
2025-04-27 12:00:48,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37372.83, 'new_value': 39115.29}, {'field': 'total_amount', 'old_value': 37372.83, 'new_value': 39115.29}, {'field': 'order_count', 'old_value': 162, 'new_value': 170}]
2025-04-27 12:00:48,102 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-27 12:00:48,555 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MSA
2025-04-27 12:00:48,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235355.67, 'new_value': 251980.89}, {'field': 'total_amount', 'old_value': 235388.67, 'new_value': 252013.89}, {'field': 'order_count', 'old_value': 1761, 'new_value': 1881}]
2025-04-27 12:00:48,555 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-27 12:00:49,056 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MVA
2025-04-27 12:00:49,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113614.79, 'new_value': 121565.86}, {'field': 'offline_amount', 'old_value': 255109.33, 'new_value': 275908.95}, {'field': 'total_amount', 'old_value': 368724.12, 'new_value': 397474.81}, {'field': 'order_count', 'old_value': 3437, 'new_value': 3694}]
2025-04-27 12:00:49,056 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-04-27 12:00:49,572 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-04-27 12:00:49,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37030.48, 'new_value': 38617.48}, {'field': 'total_amount', 'old_value': 77557.15, 'new_value': 79144.15}, {'field': 'order_count', 'old_value': 2810, 'new_value': 2811}]
2025-04-27 12:00:49,572 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-27 12:00:50,135 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M4B
2025-04-27 12:00:50,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6936.72, 'new_value': 7960.87}, {'field': 'offline_amount', 'old_value': 14050.46, 'new_value': 14831.26}, {'field': 'total_amount', 'old_value': 20987.18, 'new_value': 22792.13}, {'field': 'order_count', 'old_value': 79, 'new_value': 85}]
2025-04-27 12:00:50,135 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-27 12:00:50,604 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M6B
2025-04-27 12:00:50,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38718.98, 'new_value': 40824.37}, {'field': 'offline_amount', 'old_value': 310486.09, 'new_value': 339280.73}, {'field': 'total_amount', 'old_value': 349205.07, 'new_value': 380105.1}, {'field': 'order_count', 'old_value': 3110, 'new_value': 3354}]
2025-04-27 12:00:50,604 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-04-27 12:00:51,058 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9B
2025-04-27 12:00:51,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77633.0, 'new_value': 79632.0}, {'field': 'total_amount', 'old_value': 77633.0, 'new_value': 79632.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-27 12:00:51,058 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-27 12:00:51,480 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MDB
2025-04-27 12:00:51,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41721.0, 'new_value': 45348.0}, {'field': 'total_amount', 'old_value': 42351.0, 'new_value': 45978.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 176}]
2025-04-27 12:00:51,480 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-27 12:00:52,012 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MFB
2025-04-27 12:00:52,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319174.51, 'new_value': 324402.15}, {'field': 'total_amount', 'old_value': 347435.29, 'new_value': 352662.93}, {'field': 'order_count', 'old_value': 647, 'new_value': 658}]
2025-04-27 12:00:52,012 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-27 12:00:52,481 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MHB
2025-04-27 12:00:52,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29172.2, 'new_value': 32202.2}, {'field': 'total_amount', 'old_value': 29172.2, 'new_value': 32202.2}, {'field': 'order_count', 'old_value': 125, 'new_value': 136}]
2025-04-27 12:00:52,481 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-27 12:00:52,950 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MIB
2025-04-27 12:00:52,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24350.9, 'new_value': 25020.9}, {'field': 'offline_amount', 'old_value': 53421.4, 'new_value': 55244.4}, {'field': 'total_amount', 'old_value': 77772.3, 'new_value': 80265.3}, {'field': 'order_count', 'old_value': 211, 'new_value': 223}]
2025-04-27 12:00:52,950 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-27 12:00:53,372 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MLB
2025-04-27 12:00:53,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 199.7}, {'field': 'offline_amount', 'old_value': 35784.7, 'new_value': 38382.7}, {'field': 'total_amount', 'old_value': 35784.7, 'new_value': 38582.4}, {'field': 'order_count', 'old_value': 108, 'new_value': 120}]
2025-04-27 12:00:53,372 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-27 12:00:53,826 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-04-27 12:00:53,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127501.84, 'new_value': 136445.28}, {'field': 'total_amount', 'old_value': 127501.84, 'new_value': 136445.28}, {'field': 'order_count', 'old_value': 115, 'new_value': 122}]
2025-04-27 12:00:53,826 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-27 12:00:54,326 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQB
2025-04-27 12:00:54,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48685.9, 'new_value': 53615.8}, {'field': 'total_amount', 'old_value': 48685.9, 'new_value': 53615.8}, {'field': 'order_count', 'old_value': 276, 'new_value': 290}]
2025-04-27 12:00:54,326 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWB
2025-04-27 12:00:54,764 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MWB
2025-04-27 12:00:54,764 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62235.0, 'new_value': 81245.0}, {'field': 'total_amount', 'old_value': 62235.0, 'new_value': 81245.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-04-27 12:00:54,764 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-04-27 12:00:55,265 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MXB
2025-04-27 12:00:55,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72735.05, 'new_value': 76014.03}, {'field': 'offline_amount', 'old_value': 107351.81, 'new_value': 109147.68}, {'field': 'total_amount', 'old_value': 180086.86, 'new_value': 185161.71}, {'field': 'order_count', 'old_value': 6254, 'new_value': 6390}]
2025-04-27 12:00:55,265 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-27 12:00:55,624 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MYB
2025-04-27 12:00:55,624 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 230111.0, 'new_value': 253976.97}, {'field': 'total_amount', 'old_value': 294760.9, 'new_value': 318626.87}, {'field': 'order_count', 'old_value': 2025, 'new_value': 2173}]
2025-04-27 12:00:55,624 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-27 12:00:56,109 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT5
2025-04-27 12:00:56,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13589.22, 'new_value': 14531.56}, {'field': 'offline_amount', 'old_value': 139783.21, 'new_value': 151902.52}, {'field': 'total_amount', 'old_value': 153372.43, 'new_value': 166434.08}, {'field': 'order_count', 'old_value': 3821, 'new_value': 4111}]
2025-04-27 12:00:56,109 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-04-27 12:00:56,641 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU5
2025-04-27 12:00:56,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54790.0, 'new_value': 60789.0}, {'field': 'total_amount', 'old_value': 54790.0, 'new_value': 60789.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-04-27 12:00:56,641 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-27 12:00:57,157 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M26
2025-04-27 12:00:57,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55906.75, 'new_value': 57066.75}, {'field': 'total_amount', 'old_value': 55906.75, 'new_value': 57066.75}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-27 12:00:57,157 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-27 12:00:57,673 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M46
2025-04-27 12:00:57,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208578.73, 'new_value': 238572.61}, {'field': 'total_amount', 'old_value': 286885.03, 'new_value': 316878.91}, {'field': 'order_count', 'old_value': 547, 'new_value': 604}]
2025-04-27 12:00:57,673 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-27 12:00:58,174 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M56
2025-04-27 12:00:58,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73642.48, 'new_value': 83742.48}, {'field': 'total_amount', 'old_value': 82642.98, 'new_value': 92742.98}, {'field': 'order_count', 'old_value': 29, 'new_value': 37}]
2025-04-27 12:00:58,174 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-27 12:00:58,564 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M66
2025-04-27 12:00:58,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110412.3, 'new_value': 116740.1}, {'field': 'total_amount', 'old_value': 113067.6, 'new_value': 119395.4}, {'field': 'order_count', 'old_value': 490, 'new_value': 532}]
2025-04-27 12:00:58,564 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-27 12:00:59,018 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M86
2025-04-27 12:00:59,018 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56732.37, 'new_value': 59914.62}, {'field': 'offline_amount', 'old_value': 64714.27, 'new_value': 68556.39}, {'field': 'total_amount', 'old_value': 121446.64, 'new_value': 128471.01}, {'field': 'order_count', 'old_value': 5243, 'new_value': 5593}]
2025-04-27 12:00:59,018 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-27 12:00:59,472 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-04-27 12:00:59,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 313374.42, 'new_value': 327867.69}, {'field': 'total_amount', 'old_value': 313374.42, 'new_value': 327867.69}, {'field': 'order_count', 'old_value': 8149, 'new_value': 8480}]
2025-04-27 12:00:59,472 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-04-27 12:00:59,956 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN6
2025-04-27 12:00:59,956 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33431.79, 'new_value': 35324.05}, {'field': 'offline_amount', 'old_value': 262125.8, 'new_value': 273377.8}, {'field': 'total_amount', 'old_value': 295557.59, 'new_value': 308701.85}, {'field': 'order_count', 'old_value': 8907, 'new_value': 9304}]
2025-04-27 12:00:59,956 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-27 12:01:00,488 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO6
2025-04-27 12:01:00,488 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6398.4, 'new_value': 7050.3}, {'field': 'offline_amount', 'old_value': 59550.0, 'new_value': 60488.1}, {'field': 'total_amount', 'old_value': 65948.4, 'new_value': 67538.4}, {'field': 'order_count', 'old_value': 54, 'new_value': 59}]
2025-04-27 12:01:00,488 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-04-27 12:01:00,989 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MS6
2025-04-27 12:01:00,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22271.36, 'new_value': 24764.3}, {'field': 'offline_amount', 'old_value': 49016.77, 'new_value': 55079.6}, {'field': 'total_amount', 'old_value': 71288.13, 'new_value': 79843.9}, {'field': 'order_count', 'old_value': 628, 'new_value': 703}]
2025-04-27 12:01:00,989 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-27 12:01:01,552 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-04-27 12:01:01,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 378753.0, 'new_value': 410644.0}, {'field': 'total_amount', 'old_value': 378753.0, 'new_value': 410644.0}, {'field': 'order_count', 'old_value': 10941, 'new_value': 11785}]
2025-04-27 12:01:01,552 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY6
2025-04-27 12:01:01,974 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MY6
2025-04-27 12:01:01,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6106.08, 'new_value': 6226.08}, {'field': 'total_amount', 'old_value': 6106.08, 'new_value': 6226.08}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-04-27 12:01:01,974 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-27 12:01:02,318 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ6
2025-04-27 12:01:02,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22508.1, 'new_value': 23345.1}, {'field': 'total_amount', 'old_value': 26009.2, 'new_value': 26846.2}, {'field': 'order_count', 'old_value': 307, 'new_value': 313}]
2025-04-27 12:01:02,318 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-04-27 12:01:02,740 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M07
2025-04-27 12:01:02,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86796.67, 'new_value': 90780.34}, {'field': 'total_amount', 'old_value': 93045.77, 'new_value': 97029.44}, {'field': 'order_count', 'old_value': 2319, 'new_value': 2412}]
2025-04-27 12:01:02,740 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-04-27 12:01:03,147 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M67
2025-04-27 12:01:03,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 516969.97, 'new_value': 543581.27}, {'field': 'total_amount', 'old_value': 516969.97, 'new_value': 543581.27}, {'field': 'order_count', 'old_value': 5225, 'new_value': 5476}]
2025-04-27 12:01:03,147 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-27 12:01:03,585 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M77
2025-04-27 12:01:03,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8053.0, 'new_value': 8233.0}, {'field': 'total_amount', 'old_value': 8054.0, 'new_value': 8234.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-04-27 12:01:03,585 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-27 12:01:04,085 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M87
2025-04-27 12:01:04,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35863.0, 'new_value': 38833.0}, {'field': 'total_amount', 'old_value': 39539.0, 'new_value': 42509.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 78}]
2025-04-27 12:01:04,085 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-04-27 12:01:04,492 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MA7
2025-04-27 12:01:04,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16042.27, 'new_value': 18832.27}, {'field': 'total_amount', 'old_value': 18832.27, 'new_value': 21622.27}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-04-27 12:01:04,492 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-27 12:01:04,930 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB7
2025-04-27 12:01:04,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105509.0, 'new_value': 118222.0}, {'field': 'total_amount', 'old_value': 105509.0, 'new_value': 118222.0}, {'field': 'order_count', 'old_value': 3358, 'new_value': 3770}]
2025-04-27 12:01:04,930 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-27 12:01:05,383 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MC7
2025-04-27 12:01:05,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 512576.61, 'new_value': 553496.7}, {'field': 'total_amount', 'old_value': 595445.12, 'new_value': 636365.21}, {'field': 'order_count', 'old_value': 4332, 'new_value': 4676}]
2025-04-27 12:01:05,383 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-27 12:01:06,009 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MI7
2025-04-27 12:01:06,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1058195.0, 'new_value': 1127237.0}, {'field': 'total_amount', 'old_value': 1058195.0, 'new_value': 1127237.0}, {'field': 'order_count', 'old_value': 1125, 'new_value': 1180}]
2025-04-27 12:01:06,009 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-27 12:01:06,431 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK7
2025-04-27 12:01:06,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8676.6, 'new_value': 9180.6}, {'field': 'total_amount', 'old_value': 17388.2, 'new_value': 17892.2}, {'field': 'order_count', 'old_value': 145, 'new_value': 154}]
2025-04-27 12:01:06,431 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-27 12:01:06,885 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ML7
2025-04-27 12:01:06,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174675.0, 'new_value': 191570.0}, {'field': 'total_amount', 'old_value': 174675.0, 'new_value': 191570.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 212}]
2025-04-27 12:01:06,885 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-27 12:01:07,401 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-04-27 12:01:07,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 518883.88, 'new_value': 556623.54}, {'field': 'total_amount', 'old_value': 518883.88, 'new_value': 556623.54}, {'field': 'order_count', 'old_value': 10249, 'new_value': 11061}]
2025-04-27 12:01:07,401 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-27 12:01:07,870 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MT7
2025-04-27 12:01:07,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39229.0, 'new_value': 40061.0}, {'field': 'total_amount', 'old_value': 39229.0, 'new_value': 40061.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 128}]
2025-04-27 12:01:07,870 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-27 12:01:08,323 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU7
2025-04-27 12:01:08,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118534.2, 'new_value': 126101.2}, {'field': 'total_amount', 'old_value': 223074.97, 'new_value': 230641.97}, {'field': 'order_count', 'old_value': 6128, 'new_value': 6343}]
2025-04-27 12:01:08,323 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-27 12:01:08,761 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M28
2025-04-27 12:01:08,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105746.0, 'new_value': 107129.0}, {'field': 'total_amount', 'old_value': 106493.0, 'new_value': 107876.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 50}]
2025-04-27 12:01:08,761 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-27 12:01:09,183 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-04-27 12:01:09,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146082.7, 'new_value': 151474.79}, {'field': 'offline_amount', 'old_value': 405827.2, 'new_value': 425408.17}, {'field': 'total_amount', 'old_value': 551909.9, 'new_value': 576882.96}, {'field': 'order_count', 'old_value': 2691, 'new_value': 2891}]
2025-04-27 12:01:09,183 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-27 12:01:09,590 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-04-27 12:01:09,590 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136523.7, 'new_value': 145157.99}, {'field': 'offline_amount', 'old_value': 105606.92, 'new_value': 114909.92}, {'field': 'total_amount', 'old_value': 242130.62, 'new_value': 260067.91}, {'field': 'order_count', 'old_value': 2466, 'new_value': 2607}]
2025-04-27 12:01:09,590 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-27 12:01:10,044 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M68
2025-04-27 12:01:10,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488330.2, 'new_value': 540512.6}, {'field': 'total_amount', 'old_value': 544217.3, 'new_value': 596399.7}, {'field': 'order_count', 'old_value': 1213, 'new_value': 1265}]
2025-04-27 12:01:10,044 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-27 12:01:10,497 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M78
2025-04-27 12:01:10,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187841.18, 'new_value': 205156.09}, {'field': 'total_amount', 'old_value': 187841.18, 'new_value': 205156.09}, {'field': 'order_count', 'old_value': 3907, 'new_value': 4315}]
2025-04-27 12:01:10,497 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-27 12:01:10,951 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8H
2025-04-27 12:01:10,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 467595.09, 'new_value': 487188.1}, {'field': 'total_amount', 'old_value': 486658.59, 'new_value': 506251.6}, {'field': 'order_count', 'old_value': 1899, 'new_value': 1974}]
2025-04-27 12:01:10,951 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-27 12:01:11,420 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9H
2025-04-27 12:01:11,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60608.0, 'new_value': 65094.0}, {'field': 'total_amount', 'old_value': 67027.0, 'new_value': 71513.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 46}]
2025-04-27 12:01:11,420 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-27 12:01:11,967 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAH
2025-04-27 12:01:11,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326354.89, 'new_value': 348515.78}, {'field': 'total_amount', 'old_value': 326354.89, 'new_value': 348515.78}, {'field': 'order_count', 'old_value': 14232, 'new_value': 15119}]
2025-04-27 12:01:11,967 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-27 12:01:12,499 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-04-27 12:01:12,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 559688.02, 'new_value': 596172.98}, {'field': 'total_amount', 'old_value': 559688.02, 'new_value': 596172.98}, {'field': 'order_count', 'old_value': 5962, 'new_value': 6307}]
2025-04-27 12:01:12,499 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-27 12:01:12,937 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDH
2025-04-27 12:01:12,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6983.25, 'new_value': 7840.57}, {'field': 'offline_amount', 'old_value': 32601.58, 'new_value': 34626.98}, {'field': 'total_amount', 'old_value': 39584.83, 'new_value': 42467.55}, {'field': 'order_count', 'old_value': 1775, 'new_value': 1896}]
2025-04-27 12:01:12,937 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-27 12:01:13,406 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEH
2025-04-27 12:01:13,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9007.13, 'new_value': 9304.88}, {'field': 'offline_amount', 'old_value': 58262.96, 'new_value': 61980.82}, {'field': 'total_amount', 'old_value': 67270.09, 'new_value': 71285.7}, {'field': 'order_count', 'old_value': 2724, 'new_value': 2886}]
2025-04-27 12:01:13,406 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-27 12:01:13,860 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-04-27 12:01:13,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 645379.87, 'new_value': 671481.29}, {'field': 'total_amount', 'old_value': 645910.08, 'new_value': 672011.5}, {'field': 'order_count', 'old_value': 1529, 'new_value': 1583}]
2025-04-27 12:01:13,860 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKH
2025-04-27 12:01:14,360 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKH
2025-04-27 12:01:14,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 78100.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 78100.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 9}]
2025-04-27 12:01:14,360 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-27 12:01:14,892 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMH
2025-04-27 12:01:14,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176662.0, 'new_value': 190151.0}, {'field': 'total_amount', 'old_value': 176662.0, 'new_value': 190151.0}, {'field': 'order_count', 'old_value': 553, 'new_value': 587}]
2025-04-27 12:01:14,892 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-27 12:01:15,345 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPH
2025-04-27 12:01:15,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99756.0, 'new_value': 104388.0}, {'field': 'total_amount', 'old_value': 103114.0, 'new_value': 107746.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 175}]
2025-04-27 12:01:15,345 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-27 12:01:15,799 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQH
2025-04-27 12:01:15,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281236.0, 'new_value': 320653.0}, {'field': 'total_amount', 'old_value': 281236.0, 'new_value': 320653.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 128}]
2025-04-27 12:01:15,799 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-27 12:01:16,237 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MTH
2025-04-27 12:01:16,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46402.48, 'new_value': 48672.27}, {'field': 'offline_amount', 'old_value': 406255.35, 'new_value': 432459.65}, {'field': 'total_amount', 'old_value': 452657.83, 'new_value': 481131.92}, {'field': 'order_count', 'old_value': 2049, 'new_value': 2172}]
2025-04-27 12:01:16,237 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-27 12:01:16,706 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MWH
2025-04-27 12:01:16,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225301.48, 'new_value': 237175.48}, {'field': 'total_amount', 'old_value': 225301.48, 'new_value': 237175.48}, {'field': 'order_count', 'old_value': 1249, 'new_value': 1314}]
2025-04-27 12:01:16,706 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-27 12:01:17,159 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MZH
2025-04-27 12:01:17,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24295.0, 'new_value': 24849.0}, {'field': 'total_amount', 'old_value': 25498.8, 'new_value': 26052.8}, {'field': 'order_count', 'old_value': 257, 'new_value': 267}]
2025-04-27 12:01:17,159 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-27 12:01:17,629 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-27 12:01:17,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254040.0, 'new_value': 281400.0}, {'field': 'total_amount', 'old_value': 254040.0, 'new_value': 281400.0}]
2025-04-27 12:01:17,629 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-27 12:01:18,098 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAI
2025-04-27 12:01:18,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90409.4, 'new_value': 93733.0}, {'field': 'offline_amount', 'old_value': 181052.05, 'new_value': 192122.41}, {'field': 'total_amount', 'old_value': 271461.45, 'new_value': 285855.41}, {'field': 'order_count', 'old_value': 8447, 'new_value': 8786}]
2025-04-27 12:01:18,098 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-27 12:01:18,489 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBI
2025-04-27 12:01:18,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 903.4, 'new_value': 1269.1}, {'field': 'offline_amount', 'old_value': 25102.28, 'new_value': 27039.8}, {'field': 'total_amount', 'old_value': 26005.68, 'new_value': 28308.9}, {'field': 'order_count', 'old_value': 1097, 'new_value': 1177}]
2025-04-27 12:01:18,489 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-27 12:01:18,958 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-04-27 12:01:18,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 775773.86, 'new_value': 815687.84}, {'field': 'total_amount', 'old_value': 775773.86, 'new_value': 815687.84}, {'field': 'order_count', 'old_value': 5308, 'new_value': 5561}]
2025-04-27 12:01:18,958 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-27 12:01:19,333 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDI
2025-04-27 12:01:19,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114286.0, 'new_value': 119908.0}, {'field': 'total_amount', 'old_value': 114286.0, 'new_value': 119908.0}, {'field': 'order_count', 'old_value': 511, 'new_value': 536}]
2025-04-27 12:01:19,333 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-27 12:01:19,740 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGI
2025-04-27 12:01:19,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45537.65, 'new_value': 47508.6}, {'field': 'offline_amount', 'old_value': 165775.42, 'new_value': 179631.72}, {'field': 'total_amount', 'old_value': 211313.07, 'new_value': 227140.32}, {'field': 'order_count', 'old_value': 4118, 'new_value': 4415}]
2025-04-27 12:01:19,740 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-27 12:01:20,178 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MJI
2025-04-27 12:01:20,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214735.53, 'new_value': 227298.77}, {'field': 'total_amount', 'old_value': 214735.53, 'new_value': 227298.77}, {'field': 'order_count', 'old_value': 1653, 'new_value': 1740}]
2025-04-27 12:01:20,178 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-04-27 12:01:20,678 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKI
2025-04-27 12:01:20,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12161.0, 'new_value': 12758.0}, {'field': 'total_amount', 'old_value': 12161.0, 'new_value': 12758.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-04-27 12:01:20,678 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-04-27 12:01:21,132 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MMI
2025-04-27 12:01:21,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27960.0, 'new_value': 35960.0}, {'field': 'total_amount', 'old_value': 27960.0, 'new_value': 35960.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-04-27 12:01:21,132 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-27 12:01:21,601 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOI
2025-04-27 12:01:21,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237864.4, 'new_value': 250325.7}, {'field': 'total_amount', 'old_value': 237864.4, 'new_value': 250325.7}, {'field': 'order_count', 'old_value': 4948, 'new_value': 5289}]
2025-04-27 12:01:21,601 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPI
2025-04-27 12:01:22,054 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MPI
2025-04-27 12:01:22,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29600.0, 'new_value': 49300.0}, {'field': 'total_amount', 'old_value': 29600.0, 'new_value': 49300.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-04-27 12:01:22,054 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-04-27 12:01:22,508 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MQI
2025-04-27 12:01:22,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213033.85, 'new_value': 217215.85}, {'field': 'total_amount', 'old_value': 213033.85, 'new_value': 217215.85}, {'field': 'order_count', 'old_value': 9288, 'new_value': 9456}]
2025-04-27 12:01:22,508 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUI
2025-04-27 12:01:22,930 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUI
2025-04-27 12:01:22,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61000.0, 'new_value': 71000.0}, {'field': 'total_amount', 'old_value': 61000.0, 'new_value': 71000.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-04-27 12:01:22,930 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-27 12:01:23,462 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVI
2025-04-27 12:01:23,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62540.0, 'new_value': 63600.0}, {'field': 'total_amount', 'old_value': 62550.0, 'new_value': 63610.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 240}]
2025-04-27 12:01:23,462 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-27 12:01:23,869 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M2J
2025-04-27 12:01:23,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125727.2, 'new_value': 127308.81}, {'field': 'total_amount', 'old_value': 125727.2, 'new_value': 127308.81}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-04-27 12:01:23,869 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7J
2025-04-27 12:01:24,369 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M7J
2025-04-27 12:01:24,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88051.0, 'new_value': 90708.0}, {'field': 'total_amount', 'old_value': 88051.0, 'new_value': 90708.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-04-27 12:01:24,369 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-27 12:01:24,838 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8J
2025-04-27 12:01:24,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4732.0, 'new_value': 4757.0}, {'field': 'offline_amount', 'old_value': 21669.0, 'new_value': 22469.0}, {'field': 'total_amount', 'old_value': 26401.0, 'new_value': 27226.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 134}]
2025-04-27 12:01:24,838 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-27 12:01:25,260 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9J
2025-04-27 12:01:25,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44047.73, 'new_value': 45556.64}, {'field': 'total_amount', 'old_value': 44047.73, 'new_value': 45556.64}, {'field': 'order_count', 'old_value': 716, 'new_value': 734}]
2025-04-27 12:01:25,260 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-27 12:01:25,667 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MAJ
2025-04-27 12:01:25,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155520.99, 'new_value': 166213.56}, {'field': 'total_amount', 'old_value': 155520.99, 'new_value': 166213.56}, {'field': 'order_count', 'old_value': 6770, 'new_value': 7254}]
2025-04-27 12:01:25,667 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-27 12:01:26,152 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCJ
2025-04-27 12:01:26,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80080.0, 'new_value': 82098.0}, {'field': 'offline_amount', 'old_value': 24206.0, 'new_value': 28283.0}, {'field': 'total_amount', 'old_value': 104286.0, 'new_value': 110381.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 145}]
2025-04-27 12:01:26,152 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGJ
2025-04-27 12:01:26,637 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MGJ
2025-04-27 12:01:26,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153381.0, 'new_value': 157901.0}, {'field': 'total_amount', 'old_value': 153381.0, 'new_value': 157901.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 33}]
2025-04-27 12:01:26,637 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-27 12:01:27,090 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHJ
2025-04-27 12:01:27,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12240000.0, 'new_value': 12640000.0}, {'field': 'total_amount', 'old_value': 12240001.0, 'new_value': 12640001.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-04-27 12:01:27,090 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-27 12:01:27,669 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MKJ
2025-04-27 12:01:27,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10156.06, 'new_value': 10590.92}, {'field': 'offline_amount', 'old_value': 368721.83, 'new_value': 396664.13}, {'field': 'total_amount', 'old_value': 378877.89, 'new_value': 407255.05}, {'field': 'order_count', 'old_value': 15931, 'new_value': 17065}]
2025-04-27 12:01:27,669 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-27 12:01:28,107 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-04-27 12:01:28,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200230.33, 'new_value': 210148.45}, {'field': 'offline_amount', 'old_value': 41700.17, 'new_value': 42416.57}, {'field': 'total_amount', 'old_value': 241930.5, 'new_value': 252565.02}, {'field': 'order_count', 'old_value': 11794, 'new_value': 12236}]
2025-04-27 12:01:28,107 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-27 12:01:28,545 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-04-27 12:01:28,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1536140.71, 'new_value': 1622362.12}, {'field': 'offline_amount', 'old_value': 281778.0, 'new_value': 282962.0}, {'field': 'total_amount', 'old_value': 1817918.71, 'new_value': 1905324.12}, {'field': 'order_count', 'old_value': 9059, 'new_value': 9346}]
2025-04-27 12:01:28,545 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-27 12:01:28,983 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXJ
2025-04-27 12:01:28,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41146.0, 'new_value': 46374.0}, {'field': 'total_amount', 'old_value': 41146.0, 'new_value': 46374.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 110}]
2025-04-27 12:01:28,983 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYJ
2025-04-27 12:01:29,592 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYJ
2025-04-27 12:01:29,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4840.9, 'new_value': 5141.4}, {'field': 'total_amount', 'old_value': 6166.9, 'new_value': 6467.4}, {'field': 'order_count', 'old_value': 74, 'new_value': 84}]
2025-04-27 12:01:29,592 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-27 12:01:30,062 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M2
2025-04-27 12:01:30,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251107.04, 'new_value': 268653.34}, {'field': 'total_amount', 'old_value': 251107.04, 'new_value': 268653.34}, {'field': 'order_count', 'old_value': 1793, 'new_value': 1906}]
2025-04-27 12:01:30,062 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-27 12:01:30,578 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M4
2025-04-27 12:01:30,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88127.84, 'new_value': 98001.24}, {'field': 'total_amount', 'old_value': 127149.68, 'new_value': 137023.08}, {'field': 'order_count', 'old_value': 3685, 'new_value': 3949}]
2025-04-27 12:01:30,578 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-27 12:01:31,016 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M7
2025-04-27 12:01:31,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167029.54, 'new_value': 179545.06}, {'field': 'total_amount', 'old_value': 167029.54, 'new_value': 179545.06}, {'field': 'order_count', 'old_value': 696, 'new_value': 737}]
2025-04-27 12:01:31,016 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-27 12:01:31,500 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M8
2025-04-27 12:01:31,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32180.92, 'new_value': 36837.92}, {'field': 'total_amount', 'old_value': 64817.3, 'new_value': 69474.3}, {'field': 'order_count', 'old_value': 2351, 'new_value': 2559}]
2025-04-27 12:01:31,500 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-27 12:01:31,907 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M9
2025-04-27 12:01:31,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171925.5, 'new_value': 178579.7}, {'field': 'total_amount', 'old_value': 171925.5, 'new_value': 178579.7}, {'field': 'order_count', 'old_value': 825, 'new_value': 854}]
2025-04-27 12:01:31,907 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA
2025-04-27 12:01:32,329 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA
2025-04-27 12:01:32,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78588.0, 'new_value': 86796.0}, {'field': 'total_amount', 'old_value': 78588.0, 'new_value': 86796.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-04-27 12:01:32,329 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-27 12:01:32,830 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MD
2025-04-27 12:01:32,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52519.0, 'new_value': 66179.0}, {'field': 'total_amount', 'old_value': 52519.0, 'new_value': 66179.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-04-27 12:01:32,830 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-27 12:01:33,299 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI
2025-04-27 12:01:33,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430935.77, 'new_value': 454820.8}, {'field': 'total_amount', 'old_value': 430935.77, 'new_value': 454820.8}, {'field': 'order_count', 'old_value': 3364, 'new_value': 3489}]
2025-04-27 12:01:33,299 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-27 12:01:33,768 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-04-27 12:01:33,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69988.63, 'new_value': 74572.28}, {'field': 'offline_amount', 'old_value': 878491.74, 'new_value': 913349.8}, {'field': 'total_amount', 'old_value': 924467.17, 'new_value': 963908.88}, {'field': 'order_count', 'old_value': 3763, 'new_value': 3916}]
2025-04-27 12:01:33,768 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-27 12:01:34,222 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM
2025-04-27 12:01:34,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47855.9, 'new_value': 49995.4}, {'field': 'total_amount', 'old_value': 47855.9, 'new_value': 49995.4}, {'field': 'order_count', 'old_value': 263, 'new_value': 275}]
2025-04-27 12:01:34,222 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-27 12:01:34,644 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN
2025-04-27 12:01:34,644 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29178.82, 'new_value': 30319.92}, {'field': 'total_amount', 'old_value': 29178.82, 'new_value': 30319.92}, {'field': 'order_count', 'old_value': 903, 'new_value': 947}]
2025-04-27 12:01:34,660 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-27 12:01:35,113 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU
2025-04-27 12:01:35,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3502.0, 'new_value': 3690.0}, {'field': 'offline_amount', 'old_value': 18775.8, 'new_value': 20202.8}, {'field': 'total_amount', 'old_value': 22277.8, 'new_value': 23892.8}, {'field': 'order_count', 'old_value': 815, 'new_value': 876}]
2025-04-27 12:01:35,113 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-27 12:01:35,567 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MY
2025-04-27 12:01:35,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214444.0, 'new_value': 238245.0}, {'field': 'total_amount', 'old_value': 214445.0, 'new_value': 238246.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 389}]
2025-04-27 12:01:35,567 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-27 12:01:36,067 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M21
2025-04-27 12:01:36,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70695.0, 'new_value': 74015.0}, {'field': 'offline_amount', 'old_value': 303544.0, 'new_value': 315821.0}, {'field': 'total_amount', 'old_value': 374239.0, 'new_value': 389836.0}, {'field': 'order_count', 'old_value': 1477, 'new_value': 1547}]
2025-04-27 12:01:36,067 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-27 12:01:36,552 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-04-27 12:01:36,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77114.79, 'new_value': 82747.2}, {'field': 'offline_amount', 'old_value': 231894.91, 'new_value': 247962.55}, {'field': 'total_amount', 'old_value': 309009.7, 'new_value': 330709.75}, {'field': 'order_count', 'old_value': 2101, 'new_value': 2246}]
2025-04-27 12:01:36,552 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M81
2025-04-27 12:01:37,021 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M81
2025-04-27 12:01:37,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44537.06, 'new_value': 47429.17}, {'field': 'total_amount', 'old_value': 44537.06, 'new_value': 47429.17}, {'field': 'order_count', 'old_value': 2486, 'new_value': 2643}]
2025-04-27 12:01:37,021 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-27 12:01:37,506 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MA1
2025-04-27 12:01:37,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199575.21, 'new_value': 208045.93}, {'field': 'offline_amount', 'old_value': 41463.02, 'new_value': 43607.52}, {'field': 'total_amount', 'old_value': 241038.23, 'new_value': 251653.45}, {'field': 'order_count', 'old_value': 12771, 'new_value': 13394}]
2025-04-27 12:01:37,506 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-04-27 12:01:37,928 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ME1
2025-04-27 12:01:37,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153519.9, 'new_value': 155642.9}, {'field': 'total_amount', 'old_value': 153519.9, 'new_value': 155642.9}, {'field': 'order_count', 'old_value': 16354, 'new_value': 16558}]
2025-04-27 12:01:37,928 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-27 12:01:38,350 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ1
2025-04-27 12:01:38,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4734.0, 'new_value': 5005.0}, {'field': 'offline_amount', 'old_value': 25355.0, 'new_value': 26872.0}, {'field': 'total_amount', 'old_value': 30089.0, 'new_value': 31877.0}, {'field': 'order_count', 'old_value': 727, 'new_value': 776}]
2025-04-27 12:01:38,350 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-27 12:01:38,788 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MK1
2025-04-27 12:01:38,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81653.04, 'new_value': 88682.96}, {'field': 'total_amount', 'old_value': 81653.04, 'new_value': 88682.96}, {'field': 'order_count', 'old_value': 7411, 'new_value': 7933}]
2025-04-27 12:01:38,788 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-27 12:01:39,179 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9ML1
2025-04-27 12:01:39,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23339.26, 'new_value': 24371.9}, {'field': 'total_amount', 'old_value': 23339.26, 'new_value': 24371.9}, {'field': 'order_count', 'old_value': 121, 'new_value': 126}]
2025-04-27 12:01:39,179 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-27 12:01:39,617 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MM1
2025-04-27 12:01:39,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178297.85, 'new_value': 194369.26}, {'field': 'total_amount', 'old_value': 178297.85, 'new_value': 194369.26}, {'field': 'order_count', 'old_value': 13117, 'new_value': 14290}]
2025-04-27 12:01:39,617 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-27 12:01:40,118 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MN1
2025-04-27 12:01:40,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43193.8, 'new_value': 46393.8}, {'field': 'total_amount', 'old_value': 49198.8, 'new_value': 52398.8}, {'field': 'order_count', 'old_value': 90, 'new_value': 95}]
2025-04-27 12:01:40,118 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-27 12:01:40,618 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MO1
2025-04-27 12:01:40,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220658.0, 'new_value': 225115.0}, {'field': 'total_amount', 'old_value': 220658.0, 'new_value': 225115.0}, {'field': 'order_count', 'old_value': 23893, 'new_value': 24262}]
2025-04-27 12:01:40,618 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-27 12:01:41,056 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MQ1
2025-04-27 12:01:41,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198019.01, 'new_value': 213227.5}, {'field': 'total_amount', 'old_value': 203020.02, 'new_value': 218228.51}, {'field': 'order_count', 'old_value': 3495, 'new_value': 3776}]
2025-04-27 12:01:41,056 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-27 12:01:41,510 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MS1
2025-04-27 12:01:41,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18543.7, 'new_value': 20339.87}, {'field': 'offline_amount', 'old_value': 27853.14, 'new_value': 30672.26}, {'field': 'total_amount', 'old_value': 46396.84, 'new_value': 51012.13}, {'field': 'order_count', 'old_value': 2280, 'new_value': 2513}]
2025-04-27 12:01:41,510 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-27 12:01:41,932 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MT1
2025-04-27 12:01:41,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179152.0, 'new_value': 192429.0}, {'field': 'total_amount', 'old_value': 182670.0, 'new_value': 195947.0}, {'field': 'order_count', 'old_value': 28905, 'new_value': 29214}]
2025-04-27 12:01:41,932 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-27 12:01:42,385 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MU1
2025-04-27 12:01:42,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 705977.41, 'new_value': 714365.41}, {'field': 'total_amount', 'old_value': 705977.41, 'new_value': 714365.41}, {'field': 'order_count', 'old_value': 602, 'new_value': 630}]
2025-04-27 12:01:42,385 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-27 12:01:42,964 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV1
2025-04-27 12:01:42,964 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12827.32, 'new_value': 12962.62}, {'field': 'offline_amount', 'old_value': 30559.0, 'new_value': 31195.0}, {'field': 'total_amount', 'old_value': 43386.32, 'new_value': 44157.62}, {'field': 'order_count', 'old_value': 255, 'new_value': 266}]
2025-04-27 12:01:42,964 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-04-27 12:01:43,339 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M32
2025-04-27 12:01:43,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167683.8, 'new_value': 190780.46}, {'field': 'total_amount', 'old_value': 170882.1, 'new_value': 193978.76}, {'field': 'order_count', 'old_value': 69, 'new_value': 74}]
2025-04-27 12:01:43,339 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-27 12:01:43,777 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M62
2025-04-27 12:01:43,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98868.95, 'new_value': 105218.92}, {'field': 'total_amount', 'old_value': 170448.46, 'new_value': 176798.43}, {'field': 'order_count', 'old_value': 11129, 'new_value': 11558}]
2025-04-27 12:01:43,777 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-27 12:01:44,325 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M82
2025-04-27 12:01:44,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50360.34, 'new_value': 53555.4}, {'field': 'total_amount', 'old_value': 86329.36, 'new_value': 89524.42}, {'field': 'order_count', 'old_value': 5939, 'new_value': 6150}]
2025-04-27 12:01:44,325 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MA2
2025-04-27 12:01:44,794 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MA2
2025-04-27 12:01:44,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24350.0, 'new_value': 29100.0}, {'field': 'total_amount', 'old_value': 24350.0, 'new_value': 29100.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-04-27 12:01:44,794 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-27 12:01:45,263 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MD2
2025-04-27 12:01:45,263 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24914.6, 'new_value': 25831.7}, {'field': 'offline_amount', 'old_value': 68796.2, 'new_value': 72666.1}, {'field': 'total_amount', 'old_value': 93710.8, 'new_value': 98497.8}, {'field': 'order_count', 'old_value': 3926, 'new_value': 4146}]
2025-04-27 12:01:45,263 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-27 12:01:45,701 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-04-27 12:01:45,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13349036.0, 'new_value': 14331702.0}, {'field': 'total_amount', 'old_value': 13349036.0, 'new_value': 14331702.0}, {'field': 'order_count', 'old_value': 40016, 'new_value': 42798}]
2025-04-27 12:01:45,701 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-27 12:01:46,123 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ML2
2025-04-27 12:01:46,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1065444.53, 'new_value': 1111368.1}, {'field': 'total_amount', 'old_value': 1065444.53, 'new_value': 1111368.1}, {'field': 'order_count', 'old_value': 3247, 'new_value': 3394}]
2025-04-27 12:01:46,123 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-27 12:01:46,592 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MP2
2025-04-27 12:01:46,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 692967.49, 'new_value': 719517.53}, {'field': 'total_amount', 'old_value': 692967.49, 'new_value': 719517.53}, {'field': 'order_count', 'old_value': 3320, 'new_value': 3468}]
2025-04-27 12:01:46,592 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-27 12:01:47,186 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MT2
2025-04-27 12:01:47,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305717.0, 'new_value': 322138.0}, {'field': 'total_amount', 'old_value': 305717.0, 'new_value': 322138.0}, {'field': 'order_count', 'old_value': 6147, 'new_value': 6497}]
2025-04-27 12:01:47,186 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-27 12:01:47,640 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M36
2025-04-27 12:01:47,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198011.02, 'new_value': 206390.7}, {'field': 'total_amount', 'old_value': 198011.02, 'new_value': 206390.7}, {'field': 'order_count', 'old_value': 14024, 'new_value': 14599}]
2025-04-27 12:01:47,640 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-27 12:01:48,078 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M56
2025-04-27 12:01:48,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22687.41, 'new_value': 23539.55}, {'field': 'offline_amount', 'old_value': 17328.35, 'new_value': 18086.55}, {'field': 'total_amount', 'old_value': 40015.76, 'new_value': 41626.1}, {'field': 'order_count', 'old_value': 2099, 'new_value': 2184}]
2025-04-27 12:01:48,078 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-27 12:01:48,485 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-04-27 12:01:48,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284891.0, 'new_value': 294927.0}, {'field': 'total_amount', 'old_value': 284891.0, 'new_value': 294927.0}, {'field': 'order_count', 'old_value': 12198, 'new_value': 12337}]
2025-04-27 12:01:48,485 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-27 12:01:48,922 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M76
2025-04-27 12:01:48,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 677677.41, 'new_value': 730099.19}, {'field': 'total_amount', 'old_value': 677677.41, 'new_value': 730099.19}, {'field': 'order_count', 'old_value': 5609, 'new_value': 5990}]
2025-04-27 12:01:48,922 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-27 12:01:49,360 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB6
2025-04-27 12:01:49,360 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-04-27 12:01:49,360 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-27 12:01:49,814 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-04-27 12:01:49,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169911.57, 'new_value': 177469.17}, {'field': 'total_amount', 'old_value': 169911.57, 'new_value': 177469.17}, {'field': 'order_count', 'old_value': 18791, 'new_value': 19610}]
2025-04-27 12:01:49,814 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-27 12:01:50,314 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME6
2025-04-27 12:01:50,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4293568.95, 'new_value': 4550156.95}, {'field': 'total_amount', 'old_value': 4293568.95, 'new_value': 4550156.95}, {'field': 'order_count', 'old_value': 132110, 'new_value': 137322}]
2025-04-27 12:01:50,314 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-27 12:01:50,752 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF6
2025-04-27 12:01:50,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1180874.0, 'new_value': 1268130.0}, {'field': 'total_amount', 'old_value': 1180874.0, 'new_value': 1268130.0}, {'field': 'order_count', 'old_value': 4922, 'new_value': 5302}]
2025-04-27 12:01:50,752 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-27 12:01:51,159 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG6
2025-04-27 12:01:51,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42961.0, 'new_value': 46572.0}, {'field': 'total_amount', 'old_value': 42961.0, 'new_value': 46572.0}, {'field': 'order_count', 'old_value': 276, 'new_value': 294}]
2025-04-27 12:01:51,159 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-27 12:01:51,628 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH6
2025-04-27 12:01:51,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248041.0, 'new_value': 250637.0}, {'field': 'total_amount', 'old_value': 248044.0, 'new_value': 250640.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 64}]
2025-04-27 12:01:51,628 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-27 12:01:52,097 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI6
2025-04-27 12:01:52,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 723601.27, 'new_value': 750059.17}, {'field': 'total_amount', 'old_value': 759670.25, 'new_value': 786128.15}, {'field': 'order_count', 'old_value': 1936, 'new_value': 2003}]
2025-04-27 12:01:52,097 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-27 12:01:52,519 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML6
2025-04-27 12:01:52,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358982.0, 'new_value': 368082.0}, {'field': 'total_amount', 'old_value': 358982.0, 'new_value': 368082.0}, {'field': 'order_count', 'old_value': 643, 'new_value': 662}]
2025-04-27 12:01:52,519 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-27 12:01:53,004 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-04-27 12:01:53,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57101.2, 'new_value': 60096.11}, {'field': 'total_amount', 'old_value': 57101.2, 'new_value': 60096.11}, {'field': 'order_count', 'old_value': 959, 'new_value': 1009}]
2025-04-27 12:01:53,004 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-27 12:01:53,442 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-04-27 12:01:53,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16188.08, 'new_value': 17422.08}, {'field': 'offline_amount', 'old_value': 231511.0, 'new_value': 246733.0}, {'field': 'total_amount', 'old_value': 247699.08, 'new_value': 264155.08}, {'field': 'order_count', 'old_value': 1228, 'new_value': 1309}]
2025-04-27 12:01:53,442 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ6
2025-04-27 12:01:53,880 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ6
2025-04-27 12:01:53,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35915.0, 'new_value': 52159.0}, {'field': 'total_amount', 'old_value': 56647.0, 'new_value': 72891.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 31}]
2025-04-27 12:01:53,880 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-27 12:01:54,349 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MT6
2025-04-27 12:01:54,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86408.05, 'new_value': 91229.74}, {'field': 'offline_amount', 'old_value': 316676.74, 'new_value': 342014.64}, {'field': 'total_amount', 'old_value': 403084.79, 'new_value': 433244.38}, {'field': 'order_count', 'old_value': 2987, 'new_value': 3167}]
2025-04-27 12:01:54,349 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-04-27 12:01:54,834 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU6
2025-04-27 12:01:54,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139123.23, 'new_value': 141498.96}, {'field': 'total_amount', 'old_value': 139123.23, 'new_value': 141498.96}, {'field': 'order_count', 'old_value': 3302, 'new_value': 3303}]
2025-04-27 12:01:54,834 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-27 12:01:55,319 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW6
2025-04-27 12:01:55,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 553834.85, 'new_value': 587515.35}, {'field': 'total_amount', 'old_value': 553834.85, 'new_value': 587515.35}, {'field': 'order_count', 'old_value': 4628, 'new_value': 4828}]
2025-04-27 12:01:55,319 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-04-27 12:01:55,757 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX6
2025-04-27 12:01:55,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20448.9, 'new_value': 22393.9}, {'field': 'offline_amount', 'old_value': 99441.0, 'new_value': 106283.0}, {'field': 'total_amount', 'old_value': 119889.9, 'new_value': 128676.9}, {'field': 'order_count', 'old_value': 2413, 'new_value': 2617}]
2025-04-27 12:01:55,757 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-27 12:01:56,195 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY6
2025-04-27 12:01:56,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266147.22, 'new_value': 278118.91}, {'field': 'total_amount', 'old_value': 266147.22, 'new_value': 278118.91}, {'field': 'order_count', 'old_value': 5616, 'new_value': 5875}]
2025-04-27 12:01:56,195 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M07
2025-04-27 12:01:56,711 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M07
2025-04-27 12:01:56,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51559.0, 'new_value': 55269.0}, {'field': 'total_amount', 'old_value': 59022.0, 'new_value': 62732.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 45}]
2025-04-27 12:01:56,711 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-27 12:01:57,149 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M17
2025-04-27 12:01:57,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209712.22, 'new_value': 217024.43}, {'field': 'total_amount', 'old_value': 209712.22, 'new_value': 217024.43}, {'field': 'order_count', 'old_value': 7766, 'new_value': 8054}]
2025-04-27 12:01:57,149 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-04-27 12:01:57,586 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M27
2025-04-27 12:01:57,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163540.0, 'new_value': 164540.0}, {'field': 'total_amount', 'old_value': 262512.0, 'new_value': 263512.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 66}]
2025-04-27 12:01:57,586 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-27 12:01:58,009 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M47
2025-04-27 12:01:58,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127579.87, 'new_value': 133141.31}, {'field': 'total_amount', 'old_value': 127579.87, 'new_value': 133141.31}, {'field': 'order_count', 'old_value': 3012, 'new_value': 3132}]
2025-04-27 12:01:58,009 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M57
2025-04-27 12:01:58,494 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M57
2025-04-27 12:01:58,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62978.0, 'new_value': 65594.0}, {'field': 'total_amount', 'old_value': 62978.0, 'new_value': 65594.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-04-27 12:01:58,494 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-27 12:01:59,103 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M67
2025-04-27 12:01:59,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95317.8, 'new_value': 103939.5}, {'field': 'offline_amount', 'old_value': 25524.8, 'new_value': 27818.4}, {'field': 'total_amount', 'old_value': 120842.6, 'new_value': 131757.9}, {'field': 'order_count', 'old_value': 10418, 'new_value': 11375}]
2025-04-27 12:01:59,103 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-27 12:01:59,541 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M87
2025-04-27 12:01:59,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56935.85, 'new_value': 65910.85}, {'field': 'total_amount', 'old_value': 56935.85, 'new_value': 65910.85}, {'field': 'order_count', 'old_value': 155, 'new_value': 163}]
2025-04-27 12:01:59,541 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-27 12:01:59,995 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M97
2025-04-27 12:01:59,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 328390.8, 'new_value': 360160.8}, {'field': 'total_amount', 'old_value': 328390.8, 'new_value': 360160.8}, {'field': 'order_count', 'old_value': 1359, 'new_value': 1457}]
2025-04-27 12:01:59,995 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-27 12:02:00,480 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-04-27 12:02:00,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3368533.79, 'new_value': 3514527.51}, {'field': 'total_amount', 'old_value': 3368533.79, 'new_value': 3514527.51}, {'field': 'order_count', 'old_value': 5815, 'new_value': 6075}]
2025-04-27 12:02:00,480 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-27 12:02:01,043 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-04-27 12:02:01,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37965.0, 'new_value': 41387.92}, {'field': 'total_amount', 'old_value': 37965.0, 'new_value': 41387.92}, {'field': 'order_count', 'old_value': 1767, 'new_value': 1918}]
2025-04-27 12:02:01,043 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-27 12:02:01,465 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC7
2025-04-27 12:02:01,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50649.0, 'new_value': 51064.0}, {'field': 'total_amount', 'old_value': 50649.0, 'new_value': 51064.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-04-27 12:02:01,465 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-27 12:02:01,950 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD7
2025-04-27 12:02:01,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20693.0, 'new_value': 21999.0}, {'field': 'total_amount', 'old_value': 20693.0, 'new_value': 21999.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 211}]
2025-04-27 12:02:01,950 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-04-27 12:02:02,497 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF7
2025-04-27 12:02:02,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39193.9, 'new_value': 46847.9}, {'field': 'total_amount', 'old_value': 39989.9, 'new_value': 47643.9}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-04-27 12:02:02,497 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-27 12:02:02,935 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-04-27 12:02:02,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76800.0, 'new_value': 79194.0}, {'field': 'offline_amount', 'old_value': 136531.0, 'new_value': 143408.0}, {'field': 'total_amount', 'old_value': 213331.0, 'new_value': 222602.0}, {'field': 'order_count', 'old_value': 5139, 'new_value': 5356}]
2025-04-27 12:02:02,935 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-04-27 12:02:03,420 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML7
2025-04-27 12:02:03,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137685.0, 'new_value': 162591.0}, {'field': 'total_amount', 'old_value': 140505.0, 'new_value': 165411.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 61}]
2025-04-27 12:02:03,420 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-04-27 12:02:04,061 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO7
2025-04-27 12:02:04,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341310.0, 'new_value': 355520.0}, {'field': 'total_amount', 'old_value': 341310.0, 'new_value': 355520.0}, {'field': 'order_count', 'old_value': 8392, 'new_value': 8812}]
2025-04-27 12:02:04,061 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-04-27 12:02:04,530 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MQ7
2025-04-27 12:02:04,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77218.0, 'new_value': 80010.0}, {'field': 'total_amount', 'old_value': 77218.0, 'new_value': 80010.0}, {'field': 'order_count', 'old_value': 1346, 'new_value': 1417}]
2025-04-27 12:02:04,530 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-27 12:02:04,984 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-27 12:02:04,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44014.4, 'new_value': 47080.2}, {'field': 'total_amount', 'old_value': 44014.4, 'new_value': 47080.2}, {'field': 'order_count', 'old_value': 463, 'new_value': 515}]
2025-04-27 12:02:04,984 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-04-27 12:02:05,437 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MF8
2025-04-27 12:02:05,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1332000.0, 'new_value': 1392000.0}, {'field': 'total_amount', 'old_value': 1332000.0, 'new_value': 1392000.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-27 12:02:05,437 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-27 12:02:05,922 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-04-27 12:02:05,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105782.0, 'new_value': 124311.0}, {'field': 'total_amount', 'old_value': 105782.0, 'new_value': 124311.0}, {'field': 'order_count', 'old_value': 1080, 'new_value': 1223}]
2025-04-27 12:02:05,922 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-27 12:02:06,391 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-27 12:02:06,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137466.0, 'new_value': 141989.0}, {'field': 'total_amount', 'old_value': 155280.0, 'new_value': 159803.0}, {'field': 'order_count', 'old_value': 657, 'new_value': 686}]
2025-04-27 12:02:06,391 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-27 12:02:06,892 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-27 12:02:06,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115131.0, 'new_value': 121864.0}, {'field': 'total_amount', 'old_value': 115131.0, 'new_value': 121864.0}, {'field': 'order_count', 'old_value': 399, 'new_value': 421}]
2025-04-27 12:02:06,892 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-04-27 12:02:07,345 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M52
2025-04-27 12:02:07,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6294.3, 'new_value': 8050.8}, {'field': 'offline_amount', 'old_value': 40422.6, 'new_value': 43295.5}, {'field': 'total_amount', 'old_value': 46716.9, 'new_value': 51346.3}, {'field': 'order_count', 'old_value': 422, 'new_value': 492}]
2025-04-27 12:02:07,345 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-27 12:02:07,768 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-04-27 12:02:07,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13787310.75, 'new_value': 14338803.18}, {'field': 'total_amount', 'old_value': 13787310.75, 'new_value': 14338803.18}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-27 12:02:07,768 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-27 12:02:08,237 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MA2
2025-04-27 12:02:08,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174820.41, 'new_value': 188521.71}, {'field': 'offline_amount', 'old_value': 152114.03, 'new_value': 162750.09}, {'field': 'total_amount', 'old_value': 326934.44, 'new_value': 351271.8}, {'field': 'order_count', 'old_value': 1079, 'new_value': 1166}]
2025-04-27 12:02:08,237 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MB2
2025-04-27 12:02:08,675 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MB2
2025-04-27 12:02:08,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23495.0, 'new_value': 26894.0}, {'field': 'total_amount', 'old_value': 23495.0, 'new_value': 26894.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-04-27 12:02:08,675 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-27 12:02:09,222 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MC2
2025-04-27 12:02:09,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18118.0, 'new_value': 33551.0}, {'field': 'offline_amount', 'old_value': 70980.0, 'new_value': 82880.0}, {'field': 'total_amount', 'old_value': 89098.0, 'new_value': 116431.0}, {'field': 'order_count', 'old_value': 565, 'new_value': 776}]
2025-04-27 12:02:09,222 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-27 12:02:09,722 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MD2
2025-04-27 12:02:09,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78522.0, 'new_value': 97511.0}, {'field': 'total_amount', 'old_value': 78522.0, 'new_value': 97511.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-04-27 12:02:09,722 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-27 12:02:10,270 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-04-27 12:02:10,270 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6701.0, 'new_value': 22211.0}, {'field': 'total_amount', 'old_value': 6701.0, 'new_value': 22211.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-04-27 12:02:10,270 - INFO - 开始批量插入 1 条新记录
2025-04-27 12:02:10,426 - INFO - 批量插入响应状态码: 200
2025-04-27 12:02:10,426 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Apr 2025 04:00:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AABB18E7-5A91-7790-90E3-EA3A233F294F', 'x-acs-trace-id': 'ede60394e9cd0a5869bc5217d39bcdc3', 'etag': '6Zai5pmaDc0OcuPth3hk2Kg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-27 12:02:10,426 - INFO - 批量插入响应体: {'result': ['FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1']}
2025-04-27 12:02:10,426 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-04-27 12:02:10,426 - INFO - 成功插入的数据ID: ['FINST-NLF66581Q4VURCQVEAQGU8UJUISX2V9JF4Z9MPF1']
2025-04-27 12:02:13,445 - INFO - 批量插入完成，共 1 条记录
2025-04-27 12:02:13,445 - INFO - 日期 2025-04 处理完成 - 更新: 227 条，插入: 1 条，错误: 0 条
2025-04-27 12:02:13,445 - INFO - 数据同步完成！更新: 227 条，插入: 1 条，错误: 0 条
2025-04-27 12:02:13,445 - INFO - =================同步完成====================
2025-04-27 15:00:01,861 - INFO - =================使用默认全量同步=============
2025-04-27 15:00:02,986 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-27 15:00:02,986 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 15:00:03,001 - INFO - 开始处理日期: 2025-01
2025-04-27 15:00:03,001 - INFO - Request Parameters - Page 1:
2025-04-27 15:00:03,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:03,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:03,986 - INFO - Response - Page 1:
2025-04-27 15:00:04,189 - INFO - 第 1 页获取到 100 条记录
2025-04-27 15:00:04,189 - INFO - Request Parameters - Page 2:
2025-04-27 15:00:04,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:04,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:04,673 - INFO - Response - Page 2:
2025-04-27 15:00:04,876 - INFO - 第 2 页获取到 100 条记录
2025-04-27 15:00:04,876 - INFO - Request Parameters - Page 3:
2025-04-27 15:00:04,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:04,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:05,439 - INFO - Response - Page 3:
2025-04-27 15:00:05,642 - INFO - 第 3 页获取到 100 条记录
2025-04-27 15:00:05,642 - INFO - Request Parameters - Page 4:
2025-04-27 15:00:05,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:05,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:06,142 - INFO - Response - Page 4:
2025-04-27 15:00:06,345 - INFO - 第 4 页获取到 100 条记录
2025-04-27 15:00:06,345 - INFO - Request Parameters - Page 5:
2025-04-27 15:00:06,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:06,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:06,845 - INFO - Response - Page 5:
2025-04-27 15:00:07,048 - INFO - 第 5 页获取到 100 条记录
2025-04-27 15:00:07,048 - INFO - Request Parameters - Page 6:
2025-04-27 15:00:07,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:07,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:07,501 - INFO - Response - Page 6:
2025-04-27 15:00:07,704 - INFO - 第 6 页获取到 100 条记录
2025-04-27 15:00:07,704 - INFO - Request Parameters - Page 7:
2025-04-27 15:00:07,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:07,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:08,220 - INFO - Response - Page 7:
2025-04-27 15:00:08,423 - INFO - 第 7 页获取到 82 条记录
2025-04-27 15:00:08,423 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 15:00:08,423 - INFO - 获取到 682 条表单数据
2025-04-27 15:00:08,423 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 15:00:08,439 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 15:00:08,439 - INFO - 开始处理日期: 2025-02
2025-04-27 15:00:08,439 - INFO - Request Parameters - Page 1:
2025-04-27 15:00:08,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:08,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:08,861 - INFO - Response - Page 1:
2025-04-27 15:00:09,064 - INFO - 第 1 页获取到 100 条记录
2025-04-27 15:00:09,064 - INFO - Request Parameters - Page 2:
2025-04-27 15:00:09,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:09,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:09,595 - INFO - Response - Page 2:
2025-04-27 15:00:09,798 - INFO - 第 2 页获取到 100 条记录
2025-04-27 15:00:09,798 - INFO - Request Parameters - Page 3:
2025-04-27 15:00:09,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:09,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:10,392 - INFO - Response - Page 3:
2025-04-27 15:00:10,595 - INFO - 第 3 页获取到 100 条记录
2025-04-27 15:00:10,595 - INFO - Request Parameters - Page 4:
2025-04-27 15:00:10,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:10,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:11,204 - INFO - Response - Page 4:
2025-04-27 15:00:11,407 - INFO - 第 4 页获取到 100 条记录
2025-04-27 15:00:11,407 - INFO - Request Parameters - Page 5:
2025-04-27 15:00:11,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:11,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:11,970 - INFO - Response - Page 5:
2025-04-27 15:00:12,173 - INFO - 第 5 页获取到 100 条记录
2025-04-27 15:00:12,173 - INFO - Request Parameters - Page 6:
2025-04-27 15:00:12,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:12,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:12,673 - INFO - Response - Page 6:
2025-04-27 15:00:12,876 - INFO - 第 6 页获取到 100 条记录
2025-04-27 15:00:12,876 - INFO - Request Parameters - Page 7:
2025-04-27 15:00:12,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:12,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:13,345 - INFO - Response - Page 7:
2025-04-27 15:00:13,548 - INFO - 第 7 页获取到 70 条记录
2025-04-27 15:00:13,548 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 15:00:13,548 - INFO - 获取到 670 条表单数据
2025-04-27 15:00:13,548 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 15:00:13,564 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 15:00:13,564 - INFO - 开始处理日期: 2025-03
2025-04-27 15:00:13,564 - INFO - Request Parameters - Page 1:
2025-04-27 15:00:13,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:13,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:14,064 - INFO - Response - Page 1:
2025-04-27 15:00:14,267 - INFO - 第 1 页获取到 100 条记录
2025-04-27 15:00:14,267 - INFO - Request Parameters - Page 2:
2025-04-27 15:00:14,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:14,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:14,736 - INFO - Response - Page 2:
2025-04-27 15:00:14,939 - INFO - 第 2 页获取到 100 条记录
2025-04-27 15:00:14,939 - INFO - Request Parameters - Page 3:
2025-04-27 15:00:14,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:14,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:15,376 - INFO - Response - Page 3:
2025-04-27 15:00:15,579 - INFO - 第 3 页获取到 100 条记录
2025-04-27 15:00:15,579 - INFO - Request Parameters - Page 4:
2025-04-27 15:00:15,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:15,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:16,095 - INFO - Response - Page 4:
2025-04-27 15:00:16,298 - INFO - 第 4 页获取到 100 条记录
2025-04-27 15:00:16,298 - INFO - Request Parameters - Page 5:
2025-04-27 15:00:16,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:16,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:16,861 - INFO - Response - Page 5:
2025-04-27 15:00:17,064 - INFO - 第 5 页获取到 100 条记录
2025-04-27 15:00:17,064 - INFO - Request Parameters - Page 6:
2025-04-27 15:00:17,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:17,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:17,610 - INFO - Response - Page 6:
2025-04-27 15:00:17,814 - INFO - 第 6 页获取到 100 条记录
2025-04-27 15:00:17,814 - INFO - Request Parameters - Page 7:
2025-04-27 15:00:17,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:17,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:18,282 - INFO - Response - Page 7:
2025-04-27 15:00:18,486 - INFO - 第 7 页获取到 61 条记录
2025-04-27 15:00:18,486 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 15:00:18,486 - INFO - 获取到 661 条表单数据
2025-04-27 15:00:18,486 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 15:00:18,501 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 15:00:18,501 - INFO - 开始处理日期: 2025-04
2025-04-27 15:00:18,501 - INFO - Request Parameters - Page 1:
2025-04-27 15:00:18,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:18,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:19,001 - INFO - Response - Page 1:
2025-04-27 15:00:19,204 - INFO - 第 1 页获取到 100 条记录
2025-04-27 15:00:19,204 - INFO - Request Parameters - Page 2:
2025-04-27 15:00:19,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:19,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:19,751 - INFO - Response - Page 2:
2025-04-27 15:00:19,954 - INFO - 第 2 页获取到 100 条记录
2025-04-27 15:00:19,954 - INFO - Request Parameters - Page 3:
2025-04-27 15:00:19,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:19,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:20,407 - INFO - Response - Page 3:
2025-04-27 15:00:20,611 - INFO - 第 3 页获取到 100 条记录
2025-04-27 15:00:20,611 - INFO - Request Parameters - Page 4:
2025-04-27 15:00:20,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:20,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:21,079 - INFO - Response - Page 4:
2025-04-27 15:00:21,282 - INFO - 第 4 页获取到 100 条记录
2025-04-27 15:00:21,282 - INFO - Request Parameters - Page 5:
2025-04-27 15:00:21,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:21,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:21,736 - INFO - Response - Page 5:
2025-04-27 15:00:21,939 - INFO - 第 5 页获取到 100 条记录
2025-04-27 15:00:21,939 - INFO - Request Parameters - Page 6:
2025-04-27 15:00:21,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:21,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:22,392 - INFO - Response - Page 6:
2025-04-27 15:00:22,595 - INFO - 第 6 页获取到 100 条记录
2025-04-27 15:00:22,595 - INFO - Request Parameters - Page 7:
2025-04-27 15:00:22,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 15:00:22,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 15:00:22,923 - INFO - Response - Page 7:
2025-04-27 15:00:23,126 - INFO - 第 7 页获取到 26 条记录
2025-04-27 15:00:23,126 - INFO - 查询完成，共获取到 626 条记录
2025-04-27 15:00:23,126 - INFO - 获取到 626 条表单数据
2025-04-27 15:00:23,126 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-27 15:00:23,142 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-27 15:00:23,626 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M9I
2025-04-27 15:00:23,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281400.0, 'new_value': 303000.0}, {'field': 'total_amount', 'old_value': 281400.0, 'new_value': 303000.0}]
2025-04-27 15:00:23,626 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-27 15:00:24,110 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MJ2
2025-04-27 15:00:24,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202024.0, 'new_value': 209005.0}, {'field': 'total_amount', 'old_value': 202024.0, 'new_value': 209005.0}, {'field': 'order_count', 'old_value': 7200, 'new_value': 7420}]
2025-04-27 15:00:24,110 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-27 15:00:24,579 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M02
2025-04-27 15:00:24,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141989.0, 'new_value': 151416.0}, {'field': 'total_amount', 'old_value': 159803.0, 'new_value': 169230.0}, {'field': 'order_count', 'old_value': 686, 'new_value': 711}]
2025-04-27 15:00:24,579 - INFO - 日期 2025-04 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-04-27 15:00:24,579 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-04-27 15:00:24,579 - INFO - =================同步完成====================
2025-04-27 18:00:01,872 - INFO - =================使用默认全量同步=============
2025-04-27 18:00:02,981 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-27 18:00:02,981 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 18:00:02,997 - INFO - 开始处理日期: 2025-01
2025-04-27 18:00:02,997 - INFO - Request Parameters - Page 1:
2025-04-27 18:00:02,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:02,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:03,747 - INFO - Response - Page 1:
2025-04-27 18:00:03,950 - INFO - 第 1 页获取到 100 条记录
2025-04-27 18:00:03,950 - INFO - Request Parameters - Page 2:
2025-04-27 18:00:03,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:03,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:04,950 - INFO - Response - Page 2:
2025-04-27 18:00:05,153 - INFO - 第 2 页获取到 100 条记录
2025-04-27 18:00:05,153 - INFO - Request Parameters - Page 3:
2025-04-27 18:00:05,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:05,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:05,669 - INFO - Response - Page 3:
2025-04-27 18:00:05,872 - INFO - 第 3 页获取到 100 条记录
2025-04-27 18:00:05,872 - INFO - Request Parameters - Page 4:
2025-04-27 18:00:05,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:05,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:06,325 - INFO - Response - Page 4:
2025-04-27 18:00:06,528 - INFO - 第 4 页获取到 100 条记录
2025-04-27 18:00:06,528 - INFO - Request Parameters - Page 5:
2025-04-27 18:00:06,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:06,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:07,044 - INFO - Response - Page 5:
2025-04-27 18:00:07,247 - INFO - 第 5 页获取到 100 条记录
2025-04-27 18:00:07,247 - INFO - Request Parameters - Page 6:
2025-04-27 18:00:07,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:07,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:07,794 - INFO - Response - Page 6:
2025-04-27 18:00:07,997 - INFO - 第 6 页获取到 100 条记录
2025-04-27 18:00:07,997 - INFO - Request Parameters - Page 7:
2025-04-27 18:00:07,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:07,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:08,497 - INFO - Response - Page 7:
2025-04-27 18:00:08,700 - INFO - 第 7 页获取到 82 条记录
2025-04-27 18:00:08,700 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 18:00:08,700 - INFO - 获取到 682 条表单数据
2025-04-27 18:00:08,700 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 18:00:08,715 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 18:00:08,715 - INFO - 开始处理日期: 2025-02
2025-04-27 18:00:08,715 - INFO - Request Parameters - Page 1:
2025-04-27 18:00:08,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:08,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:09,200 - INFO - Response - Page 1:
2025-04-27 18:00:09,403 - INFO - 第 1 页获取到 100 条记录
2025-04-27 18:00:09,403 - INFO - Request Parameters - Page 2:
2025-04-27 18:00:09,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:09,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:09,919 - INFO - Response - Page 2:
2025-04-27 18:00:10,122 - INFO - 第 2 页获取到 100 条记录
2025-04-27 18:00:10,122 - INFO - Request Parameters - Page 3:
2025-04-27 18:00:10,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:10,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:10,700 - INFO - Response - Page 3:
2025-04-27 18:00:10,903 - INFO - 第 3 页获取到 100 条记录
2025-04-27 18:00:10,903 - INFO - Request Parameters - Page 4:
2025-04-27 18:00:10,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:10,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:11,559 - INFO - Response - Page 4:
2025-04-27 18:00:11,762 - INFO - 第 4 页获取到 100 条记录
2025-04-27 18:00:11,762 - INFO - Request Parameters - Page 5:
2025-04-27 18:00:11,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:11,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:12,262 - INFO - Response - Page 5:
2025-04-27 18:00:12,465 - INFO - 第 5 页获取到 100 条记录
2025-04-27 18:00:12,465 - INFO - Request Parameters - Page 6:
2025-04-27 18:00:12,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:12,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:12,997 - INFO - Response - Page 6:
2025-04-27 18:00:13,200 - INFO - 第 6 页获取到 100 条记录
2025-04-27 18:00:13,200 - INFO - Request Parameters - Page 7:
2025-04-27 18:00:13,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:13,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:13,622 - INFO - Response - Page 7:
2025-04-27 18:00:13,825 - INFO - 第 7 页获取到 70 条记录
2025-04-27 18:00:13,825 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 18:00:13,825 - INFO - 获取到 670 条表单数据
2025-04-27 18:00:13,825 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 18:00:13,840 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 18:00:13,840 - INFO - 开始处理日期: 2025-03
2025-04-27 18:00:13,840 - INFO - Request Parameters - Page 1:
2025-04-27 18:00:13,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:13,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:14,294 - INFO - Response - Page 1:
2025-04-27 18:00:14,497 - INFO - 第 1 页获取到 100 条记录
2025-04-27 18:00:14,497 - INFO - Request Parameters - Page 2:
2025-04-27 18:00:14,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:14,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:14,997 - INFO - Response - Page 2:
2025-04-27 18:00:15,200 - INFO - 第 2 页获取到 100 条记录
2025-04-27 18:00:15,200 - INFO - Request Parameters - Page 3:
2025-04-27 18:00:15,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:15,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:15,731 - INFO - Response - Page 3:
2025-04-27 18:00:15,934 - INFO - 第 3 页获取到 100 条记录
2025-04-27 18:00:15,934 - INFO - Request Parameters - Page 4:
2025-04-27 18:00:15,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:15,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:16,419 - INFO - Response - Page 4:
2025-04-27 18:00:16,622 - INFO - 第 4 页获取到 100 条记录
2025-04-27 18:00:16,622 - INFO - Request Parameters - Page 5:
2025-04-27 18:00:16,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:16,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:17,106 - INFO - Response - Page 5:
2025-04-27 18:00:17,309 - INFO - 第 5 页获取到 100 条记录
2025-04-27 18:00:17,309 - INFO - Request Parameters - Page 6:
2025-04-27 18:00:17,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:17,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:17,762 - INFO - Response - Page 6:
2025-04-27 18:00:17,965 - INFO - 第 6 页获取到 100 条记录
2025-04-27 18:00:17,965 - INFO - Request Parameters - Page 7:
2025-04-27 18:00:17,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:17,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:18,387 - INFO - Response - Page 7:
2025-04-27 18:00:18,590 - INFO - 第 7 页获取到 61 条记录
2025-04-27 18:00:18,590 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 18:00:18,590 - INFO - 获取到 661 条表单数据
2025-04-27 18:00:18,590 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 18:00:18,606 - INFO - 开始更新记录 - 表单实例ID: FINST-MKF66PA1NLVUAQBD8ANV446UQLIS3X2JFMW9MS3
2025-04-27 18:00:19,106 - INFO - 更新表单数据成功: FINST-MKF66PA1NLVUAQBD8ANV446UQLIS3X2JFMW9MS3
2025-04-27 18:00:19,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 384331.23, 'new_value': 419517.23}, {'field': 'total_amount', 'old_value': 384331.23, 'new_value': 419517.23}, {'field': 'order_count', 'old_value': 7520, 'new_value': 9490}]
2025-04-27 18:00:19,106 - INFO - 开始更新记录 - 表单实例ID: FINST-MKF66PA1NLVUAQBD8ANV446UQLIS3X2JFMW9MU3
2025-04-27 18:00:19,544 - INFO - 更新表单数据成功: FINST-MKF66PA1NLVUAQBD8ANV446UQLIS3X2JFMW9MU3
2025-04-27 18:00:19,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13474390.22, 'new_value': 13473281.22}, {'field': 'total_amount', 'old_value': 13474390.22, 'new_value': 13473281.22}]
2025-04-27 18:00:19,544 - INFO - 开始更新记录 - 表单实例ID: FINST-MKF66PA1NLVUAQBD8ANV446UQLIS3X2JFMW9M34
2025-04-27 18:00:19,997 - INFO - 更新表单数据成功: FINST-MKF66PA1NLVUAQBD8ANV446UQLIS3X2JFMW9M34
2025-04-27 18:00:19,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1219161.0, 'new_value': 1221544.0}, {'field': 'total_amount', 'old_value': 1219161.0, 'new_value': 1221544.0}]
2025-04-27 18:00:20,012 - INFO - 日期 2025-03 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-04-27 18:00:20,012 - INFO - 开始处理日期: 2025-04
2025-04-27 18:00:20,012 - INFO - Request Parameters - Page 1:
2025-04-27 18:00:20,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:20,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:20,559 - INFO - Response - Page 1:
2025-04-27 18:00:20,762 - INFO - 第 1 页获取到 100 条记录
2025-04-27 18:00:20,762 - INFO - Request Parameters - Page 2:
2025-04-27 18:00:20,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:20,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:21,215 - INFO - Response - Page 2:
2025-04-27 18:00:21,419 - INFO - 第 2 页获取到 100 条记录
2025-04-27 18:00:21,419 - INFO - Request Parameters - Page 3:
2025-04-27 18:00:21,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:21,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:21,903 - INFO - Response - Page 3:
2025-04-27 18:00:22,106 - INFO - 第 3 页获取到 100 条记录
2025-04-27 18:00:22,106 - INFO - Request Parameters - Page 4:
2025-04-27 18:00:22,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:22,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:22,700 - INFO - Response - Page 4:
2025-04-27 18:00:22,903 - INFO - 第 4 页获取到 100 条记录
2025-04-27 18:00:22,903 - INFO - Request Parameters - Page 5:
2025-04-27 18:00:22,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:22,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:23,372 - INFO - Response - Page 5:
2025-04-27 18:00:23,575 - INFO - 第 5 页获取到 100 条记录
2025-04-27 18:00:23,575 - INFO - Request Parameters - Page 6:
2025-04-27 18:00:23,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:23,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:24,044 - INFO - Response - Page 6:
2025-04-27 18:00:24,247 - INFO - 第 6 页获取到 100 条记录
2025-04-27 18:00:24,247 - INFO - Request Parameters - Page 7:
2025-04-27 18:00:24,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 18:00:24,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 18:00:24,590 - INFO - Response - Page 7:
2025-04-27 18:00:24,793 - INFO - 第 7 页获取到 26 条记录
2025-04-27 18:00:24,793 - INFO - 查询完成，共获取到 626 条记录
2025-04-27 18:00:24,793 - INFO - 获取到 626 条表单数据
2025-04-27 18:00:24,793 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-27 18:00:24,793 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-27 18:00:25,215 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MA3
2025-04-27 18:00:25,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25935.6, 'new_value': 28328.3}, {'field': 'total_amount', 'old_value': 25935.6, 'new_value': 28328.3}, {'field': 'order_count', 'old_value': 208, 'new_value': 220}]
2025-04-27 18:00:25,215 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MB3
2025-04-27 18:00:25,668 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MB3
2025-04-27 18:00:25,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7498.0, 'new_value': 8698.0}, {'field': 'total_amount', 'old_value': 11091.0, 'new_value': 12291.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 30}]
2025-04-27 18:00:25,668 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M24
2025-04-27 18:00:26,137 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M24
2025-04-27 18:00:26,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5853.59, 'new_value': 6204.68}, {'field': 'offline_amount', 'old_value': 67982.61, 'new_value': 76538.83}, {'field': 'total_amount', 'old_value': 73836.2, 'new_value': 82743.51}, {'field': 'order_count', 'old_value': 2908, 'new_value': 3239}]
2025-04-27 18:00:26,137 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD4
2025-04-27 18:00:26,559 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MD4
2025-04-27 18:00:26,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82625.74, 'new_value': 89004.08}, {'field': 'total_amount', 'old_value': 82625.74, 'new_value': 89004.08}, {'field': 'order_count', 'old_value': 3781, 'new_value': 4078}]
2025-04-27 18:00:26,575 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV4
2025-04-27 18:00:26,965 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MV4
2025-04-27 18:00:26,965 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29849.0, 'new_value': 33275.6}, {'field': 'offline_amount', 'old_value': 191280.8, 'new_value': 213812.3}, {'field': 'total_amount', 'old_value': 221129.8, 'new_value': 247087.9}, {'field': 'order_count', 'old_value': 2019, 'new_value': 2217}]
2025-04-27 18:00:26,965 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-04-27 18:00:27,434 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M15
2025-04-27 18:00:27,434 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67432.14, 'new_value': 78714.97}, {'field': 'offline_amount', 'old_value': 27685.89, 'new_value': 29435.99}, {'field': 'total_amount', 'old_value': 95118.03, 'new_value': 108150.96}, {'field': 'order_count', 'old_value': 4967, 'new_value': 5694}]
2025-04-27 18:00:27,434 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M55
2025-04-27 18:00:27,903 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M55
2025-04-27 18:00:27,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41580.0, 'new_value': 45459.0}, {'field': 'total_amount', 'old_value': 41580.0, 'new_value': 45459.0}, {'field': 'order_count', 'old_value': 2201, 'new_value': 2416}]
2025-04-27 18:00:27,903 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP5
2025-04-27 18:00:28,465 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MP5
2025-04-27 18:00:28,465 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46910.19, 'new_value': 53018.41}, {'field': 'offline_amount', 'old_value': 179964.85, 'new_value': 205177.46}, {'field': 'total_amount', 'old_value': 226875.04, 'new_value': 258195.87}, {'field': 'order_count', 'old_value': 3416, 'new_value': 3822}]
2025-04-27 18:00:28,465 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBA
2025-04-27 18:00:28,918 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MBA
2025-04-27 18:00:28,918 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8568.01, 'new_value': 8869.13}, {'field': 'offline_amount', 'old_value': 74330.35, 'new_value': 85053.54}, {'field': 'total_amount', 'old_value': 82898.36, 'new_value': 93922.67}, {'field': 'order_count', 'old_value': 1805, 'new_value': 2007}]
2025-04-27 18:00:28,918 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-04-27 18:00:29,340 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-04-27 18:00:29,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31764.0, 'new_value': 34256.0}, {'field': 'total_amount', 'old_value': 31764.0, 'new_value': 34256.0}, {'field': 'order_count', 'old_value': 177, 'new_value': 190}]
2025-04-27 18:00:29,340 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-04-27 18:00:29,778 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-04-27 18:00:29,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198080.5, 'new_value': 204079.5}, {'field': 'total_amount', 'old_value': 302258.5, 'new_value': 308257.5}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-04-27 18:00:29,778 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-04-27 18:00:30,215 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9ME6
2025-04-27 18:00:30,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153795.9, 'new_value': 158795.9}, {'field': 'total_amount', 'old_value': 153795.9, 'new_value': 158795.9}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-04-27 18:00:30,215 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU6
2025-04-27 18:00:30,653 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU6
2025-04-27 18:00:30,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10.1, 'new_value': 48.5}, {'field': 'offline_amount', 'old_value': 19356.0, 'new_value': 20599.5}, {'field': 'total_amount', 'old_value': 19366.1, 'new_value': 20648.0}, {'field': 'order_count', 'old_value': 939, 'new_value': 997}]
2025-04-27 18:00:30,653 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF7
2025-04-27 18:00:31,106 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MF7
2025-04-27 18:00:31,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94984.01, 'new_value': 107226.36}, {'field': 'total_amount', 'old_value': 112485.44, 'new_value': 124727.79}, {'field': 'order_count', 'old_value': 2537, 'new_value': 2805}]
2025-04-27 18:00:31,106 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN7
2025-04-27 18:00:31,543 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN7
2025-04-27 18:00:31,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68840.0, 'new_value': 77050.0}, {'field': 'total_amount', 'old_value': 68840.0, 'new_value': 77050.0}, {'field': 'order_count', 'old_value': 503, 'new_value': 558}]
2025-04-27 18:00:31,543 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR7
2025-04-27 18:00:31,981 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MR7
2025-04-27 18:00:31,981 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31991.99, 'new_value': 34778.96}, {'field': 'offline_amount', 'old_value': 173635.3, 'new_value': 193967.72}, {'field': 'total_amount', 'old_value': 205627.29, 'new_value': 228746.68}, {'field': 'order_count', 'old_value': 3887, 'new_value': 4267}]
2025-04-27 18:00:31,981 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB8
2025-04-27 18:00:32,450 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB8
2025-04-27 18:00:32,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17338.43, 'new_value': 18679.4}, {'field': 'offline_amount', 'old_value': 93492.56, 'new_value': 104820.36}, {'field': 'total_amount', 'old_value': 110830.99, 'new_value': 123499.76}, {'field': 'order_count', 'old_value': 3439, 'new_value': 3819}]
2025-04-27 18:00:32,450 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSH
2025-04-27 18:00:32,934 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSH
2025-04-27 18:00:32,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21895.27, 'new_value': 26936.27}, {'field': 'offline_amount', 'old_value': 73307.35, 'new_value': 78653.02}, {'field': 'total_amount', 'old_value': 95202.62, 'new_value': 105589.29}, {'field': 'order_count', 'old_value': 4561, 'new_value': 5213}]
2025-04-27 18:00:32,934 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-04-27 18:00:33,356 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-04-27 18:00:33,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128396.82, 'new_value': 142239.14}, {'field': 'total_amount', 'old_value': 128396.82, 'new_value': 142239.14}, {'field': 'order_count', 'old_value': 5003, 'new_value': 5500}]
2025-04-27 18:00:33,356 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDJ
2025-04-27 18:00:33,856 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MDJ
2025-04-27 18:00:33,856 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7615.72, 'new_value': 8723.92}, {'field': 'offline_amount', 'old_value': 118692.97, 'new_value': 131222.77}, {'field': 'total_amount', 'old_value': 126308.69, 'new_value': 139946.69}, {'field': 'order_count', 'old_value': 5018, 'new_value': 5541}]
2025-04-27 18:00:33,856 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRJ
2025-04-27 18:00:34,325 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRJ
2025-04-27 18:00:34,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12303.0, 'new_value': 12907.0}, {'field': 'total_amount', 'old_value': 12303.0, 'new_value': 12907.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 63}]
2025-04-27 18:00:34,340 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUJ
2025-04-27 18:00:34,825 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MUJ
2025-04-27 18:00:34,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216562.62, 'new_value': 245987.17}, {'field': 'total_amount', 'old_value': 216562.62, 'new_value': 245987.17}, {'field': 'order_count', 'old_value': 1903, 'new_value': 2144}]
2025-04-27 18:00:34,825 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-27 18:00:35,293 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MF1
2025-04-27 18:00:35,293 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52614.16, 'new_value': 54305.07}, {'field': 'offline_amount', 'old_value': 63763.12, 'new_value': 65520.61}, {'field': 'total_amount', 'old_value': 116377.28, 'new_value': 119825.68}, {'field': 'order_count', 'old_value': 5864, 'new_value': 6051}]
2025-04-27 18:00:35,293 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR1
2025-04-27 18:00:35,809 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MR1
2025-04-27 18:00:35,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174202.53, 'new_value': 193465.73}, {'field': 'total_amount', 'old_value': 180177.03, 'new_value': 199440.23}, {'field': 'order_count', 'old_value': 7859, 'new_value': 8651}]
2025-04-27 18:00:35,809 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M22
2025-04-27 18:00:36,387 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M22
2025-04-27 18:00:36,387 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37730.0, 'new_value': 42682.0}, {'field': 'offline_amount', 'old_value': 269380.0, 'new_value': 298720.0}, {'field': 'total_amount', 'old_value': 307110.0, 'new_value': 341402.0}, {'field': 'order_count', 'old_value': 224, 'new_value': 254}]
2025-04-27 18:00:36,387 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M42
2025-04-27 18:00:36,918 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M42
2025-04-27 18:00:36,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 762198.0, 'new_value': 825978.0}, {'field': 'total_amount', 'old_value': 762198.0, 'new_value': 825978.0}, {'field': 'order_count', 'old_value': 37239, 'new_value': 37370}]
2025-04-27 18:00:36,918 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M52
2025-04-27 18:00:37,372 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M52
2025-04-27 18:00:37,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 539564.0, 'new_value': 609522.0}, {'field': 'total_amount', 'old_value': 539564.0, 'new_value': 609522.0}, {'field': 'order_count', 'old_value': 2460, 'new_value': 2690}]
2025-04-27 18:00:37,372 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ME2
2025-04-27 18:00:37,825 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9ME2
2025-04-27 18:00:37,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125513.0, 'new_value': 145195.0}, {'field': 'total_amount', 'old_value': 125672.0, 'new_value': 145354.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 57}]
2025-04-27 18:00:37,825 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M96
2025-04-27 18:00:38,309 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M96
2025-04-27 18:00:38,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83508.4, 'new_value': 91558.0}, {'field': 'total_amount', 'old_value': 83508.4, 'new_value': 91558.0}, {'field': 'order_count', 'old_value': 4403, 'new_value': 4815}]
2025-04-27 18:00:38,309 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-04-27 18:00:38,809 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV6
2025-04-27 18:00:38,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77077.76, 'new_value': 86593.12}, {'field': 'total_amount', 'old_value': 77077.76, 'new_value': 86593.12}, {'field': 'order_count', 'old_value': 3150, 'new_value': 3544}]
2025-04-27 18:00:38,809 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-27 18:00:39,293 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-27 18:00:39,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28523.0, 'new_value': 29799.0}, {'field': 'total_amount', 'old_value': 28523.0, 'new_value': 29799.0}, {'field': 'order_count', 'old_value': 2663, 'new_value': 2788}]
2025-04-27 18:00:39,293 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-27 18:00:39,778 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-27 18:00:39,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94369.0, 'new_value': 98704.0}, {'field': 'total_amount', 'old_value': 94369.0, 'new_value': 98704.0}, {'field': 'order_count', 'old_value': 627, 'new_value': 654}]
2025-04-27 18:00:39,778 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR7
2025-04-27 18:00:40,200 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR7
2025-04-27 18:00:40,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32468.0, 'new_value': 37267.0}, {'field': 'total_amount', 'old_value': 32468.0, 'new_value': 37267.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-04-27 18:00:40,200 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-04-27 18:00:40,653 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-04-27 18:00:40,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15786.27, 'new_value': 18361.59}, {'field': 'offline_amount', 'old_value': 55415.5, 'new_value': 60262.98}, {'field': 'total_amount', 'old_value': 71201.77, 'new_value': 78624.57}, {'field': 'order_count', 'old_value': 3373, 'new_value': 3748}]
2025-04-27 18:00:40,653 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-04-27 18:00:41,200 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-04-27 18:00:41,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 524000.0, 'new_value': 608000.0}, {'field': 'total_amount', 'old_value': 524000.0, 'new_value': 608000.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-04-27 18:00:41,200 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-04-27 18:00:41,793 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-04-27 18:00:41,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172928.0, 'new_value': 186728.0}, {'field': 'total_amount', 'old_value': 172928.0, 'new_value': 186728.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-04-27 18:00:41,793 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW7
2025-04-27 18:00:42,215 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW7
2025-04-27 18:00:42,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3100.0, 'new_value': 3399.0}, {'field': 'total_amount', 'old_value': 3100.0, 'new_value': 3399.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-04-27 18:00:42,231 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-27 18:00:42,653 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-27 18:00:42,653 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13016.9, 'new_value': 16144.7}, {'field': 'total_amount', 'old_value': 13016.9, 'new_value': 16144.7}, {'field': 'order_count', 'old_value': 43, 'new_value': 60}]
2025-04-27 18:00:42,653 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ7
2025-04-27 18:00:43,153 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ7
2025-04-27 18:00:43,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301515.09, 'new_value': 328340.36}, {'field': 'total_amount', 'old_value': 301515.09, 'new_value': 328340.36}, {'field': 'order_count', 'old_value': 12761, 'new_value': 13902}]
2025-04-27 18:00:43,153 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-04-27 18:00:43,622 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-04-27 18:00:43,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374736.32, 'new_value': 428472.32}, {'field': 'total_amount', 'old_value': 374736.32, 'new_value': 428472.32}, {'field': 'order_count', 'old_value': 69, 'new_value': 77}]
2025-04-27 18:00:43,622 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M18
2025-04-27 18:00:44,106 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M18
2025-04-27 18:00:44,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133402.28, 'new_value': 140913.08}, {'field': 'total_amount', 'old_value': 133402.28, 'new_value': 140913.08}, {'field': 'order_count', 'old_value': 2191, 'new_value': 2302}]
2025-04-27 18:00:44,106 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-04-27 18:00:44,575 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M28
2025-04-27 18:00:44,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29924.68, 'new_value': 31129.68}, {'field': 'total_amount', 'old_value': 29924.68, 'new_value': 31129.68}, {'field': 'order_count', 'old_value': 2599, 'new_value': 2717}]
2025-04-27 18:00:44,575 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-04-27 18:00:45,043 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M38
2025-04-27 18:00:45,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245698.22, 'new_value': 271357.27}, {'field': 'total_amount', 'old_value': 248722.22, 'new_value': 274381.27}, {'field': 'order_count', 'old_value': 548, 'new_value': 576}]
2025-04-27 18:00:45,043 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M48
2025-04-27 18:00:45,481 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M48
2025-04-27 18:00:45,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62000.0, 'new_value': 65000.0}, {'field': 'total_amount', 'old_value': 62000.0, 'new_value': 65000.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-04-27 18:00:45,481 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M58
2025-04-27 18:00:45,903 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M58
2025-04-27 18:00:45,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22679.05, 'new_value': 24508.1}, {'field': 'total_amount', 'old_value': 22679.05, 'new_value': 24508.1}, {'field': 'order_count', 'old_value': 625, 'new_value': 708}]
2025-04-27 18:00:45,903 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-27 18:00:46,356 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-27 18:00:46,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113868.0, 'new_value': 119609.0}, {'field': 'total_amount', 'old_value': 113868.0, 'new_value': 119609.0}, {'field': 'order_count', 'old_value': 4064, 'new_value': 4278}]
2025-04-27 18:00:46,356 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-27 18:00:46,871 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-27 18:00:46,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161161.4, 'new_value': 168728.5}, {'field': 'total_amount', 'old_value': 167556.4, 'new_value': 175123.5}, {'field': 'order_count', 'old_value': 966, 'new_value': 1008}]
2025-04-27 18:00:46,871 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA8
2025-04-27 18:00:47,356 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA8
2025-04-27 18:00:47,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74414.0, 'new_value': 76954.0}, {'field': 'total_amount', 'old_value': 74414.0, 'new_value': 76954.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-04-27 18:00:47,356 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB8
2025-04-27 18:00:47,809 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB8
2025-04-27 18:00:47,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9358.0, 'new_value': 11247.0}, {'field': 'total_amount', 'old_value': 9358.0, 'new_value': 11247.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-04-27 18:00:47,809 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD8
2025-04-27 18:00:48,293 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD8
2025-04-27 18:00:48,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56898.0, 'new_value': 62656.0}, {'field': 'total_amount', 'old_value': 56899.0, 'new_value': 62657.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-04-27 18:00:48,293 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME8
2025-04-27 18:00:48,668 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ME8
2025-04-27 18:00:48,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155095.0, 'new_value': 225295.0}, {'field': 'total_amount', 'old_value': 155095.0, 'new_value': 225295.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-04-27 18:00:48,668 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-04-27 18:00:49,153 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MH8
2025-04-27 18:00:49,153 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28691.2, 'new_value': 32412.1}, {'field': 'offline_amount', 'old_value': 32194.06, 'new_value': 34302.95}, {'field': 'total_amount', 'old_value': 60885.26, 'new_value': 66715.05}, {'field': 'order_count', 'old_value': 4801, 'new_value': 5284}]
2025-04-27 18:00:49,153 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-04-27 18:00:49,575 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MI8
2025-04-27 18:00:49,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5754418.0, 'new_value': 6076408.0}, {'field': 'total_amount', 'old_value': 5754418.0, 'new_value': 6076408.0}, {'field': 'order_count', 'old_value': 102611, 'new_value': 107763}]
2025-04-27 18:00:49,575 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ8
2025-04-27 18:00:50,012 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MJ8
2025-04-27 18:00:50,012 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15069.0, 'new_value': 15297.0}, {'field': 'offline_amount', 'old_value': 7543.0, 'new_value': 7771.0}, {'field': 'total_amount', 'old_value': 22612.0, 'new_value': 23068.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 93}]
2025-04-27 18:00:50,012 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK8
2025-04-27 18:00:50,450 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MK8
2025-04-27 18:00:50,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43720.0, 'new_value': 47520.0}, {'field': 'total_amount', 'old_value': 43720.0, 'new_value': 47520.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-04-27 18:00:50,450 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-27 18:00:50,903 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-27 18:00:50,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145979.49, 'new_value': 154714.77}, {'field': 'total_amount', 'old_value': 145979.49, 'new_value': 154714.77}, {'field': 'order_count', 'old_value': 12442, 'new_value': 13489}]
2025-04-27 18:00:50,903 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-04-27 18:00:51,309 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM8
2025-04-27 18:00:51,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23610.0, 'new_value': 26110.0}, {'field': 'total_amount', 'old_value': 23610.0, 'new_value': 26110.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-04-27 18:00:51,309 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN8
2025-04-27 18:00:51,731 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MN8
2025-04-27 18:00:51,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56144.0, 'new_value': 62542.0}, {'field': 'total_amount', 'old_value': 56144.0, 'new_value': 62542.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-04-27 18:00:51,731 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-04-27 18:00:52,262 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-04-27 18:00:52,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52357.0, 'new_value': 55839.0}, {'field': 'total_amount', 'old_value': 52357.0, 'new_value': 55839.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 55}]
2025-04-27 18:00:52,262 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP8
2025-04-27 18:00:52,700 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP8
2025-04-27 18:00:52,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91000.0, 'new_value': 98000.0}, {'field': 'total_amount', 'old_value': 91000.0, 'new_value': 98000.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-04-27 18:00:52,700 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR8
2025-04-27 18:00:53,106 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MR8
2025-04-27 18:00:53,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8430.51, 'new_value': 8784.51}, {'field': 'total_amount', 'old_value': 9870.51, 'new_value': 10224.51}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-04-27 18:00:53,106 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-27 18:00:53,575 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-04-27 18:00:53,575 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 284497.45, 'new_value': 296799.85}, {'field': 'offline_amount', 'old_value': 235045.23, 'new_value': 252537.33}, {'field': 'total_amount', 'old_value': 519542.68, 'new_value': 549337.18}, {'field': 'order_count', 'old_value': 14626, 'new_value': 15269}]
2025-04-27 18:00:53,575 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-27 18:00:54,043 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-27 18:00:54,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11599.33, 'new_value': 12215.33}, {'field': 'total_amount', 'old_value': 11599.33, 'new_value': 12215.33}, {'field': 'order_count', 'old_value': 304, 'new_value': 326}]
2025-04-27 18:00:54,043 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M92
2025-04-27 18:00:54,465 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M92
2025-04-27 18:00:54,465 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11.0, 'new_value': 12.0}, {'field': 'offline_amount', 'old_value': 1011.0, 'new_value': 1012.0}, {'field': 'total_amount', 'old_value': 1022.0, 'new_value': 1024.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 19}]
2025-04-27 18:00:54,465 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-04-27 18:00:54,965 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-04-27 18:00:54,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25941.9, 'new_value': 39793.5}, {'field': 'total_amount', 'old_value': 25941.9, 'new_value': 39793.5}, {'field': 'order_count', 'old_value': 310, 'new_value': 450}]
2025-04-27 18:00:54,965 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-04-27 18:00:55,371 - INFO - 更新表单数据成功: FINST-OJ966381ZKVUYUYGD0EVH8PCR91L20Q91PX9MY3
2025-04-27 18:00:55,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49000.0, 'new_value': 1023000.0}, {'field': 'total_amount', 'old_value': 49000.0, 'new_value': 1023000.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 31}]
2025-04-27 18:00:55,371 - INFO - 日期 2025-04 处理完成 - 更新: 66 条，插入: 0 条，错误: 0 条
2025-04-27 18:00:55,371 - INFO - 数据同步完成！更新: 69 条，插入: 0 条，错误: 0 条
2025-04-27 18:00:55,387 - INFO - =================同步完成====================
2025-04-27 21:00:01,992 - INFO - =================使用默认全量同步=============
2025-04-27 21:00:03,102 - INFO - MySQL查询成功，共获取 2639 条记录
2025-04-27 21:00:03,102 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-27 21:00:03,117 - INFO - 开始处理日期: 2025-01
2025-04-27 21:00:03,117 - INFO - Request Parameters - Page 1:
2025-04-27 21:00:03,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:03,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:04,086 - INFO - Response - Page 1:
2025-04-27 21:00:04,289 - INFO - 第 1 页获取到 100 条记录
2025-04-27 21:00:04,289 - INFO - Request Parameters - Page 2:
2025-04-27 21:00:04,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:04,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:04,727 - INFO - Response - Page 2:
2025-04-27 21:00:04,930 - INFO - 第 2 页获取到 100 条记录
2025-04-27 21:00:04,930 - INFO - Request Parameters - Page 3:
2025-04-27 21:00:04,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:04,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:05,367 - INFO - Response - Page 3:
2025-04-27 21:00:05,570 - INFO - 第 3 页获取到 100 条记录
2025-04-27 21:00:05,570 - INFO - Request Parameters - Page 4:
2025-04-27 21:00:05,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:05,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:06,024 - INFO - Response - Page 4:
2025-04-27 21:00:06,227 - INFO - 第 4 页获取到 100 条记录
2025-04-27 21:00:06,227 - INFO - Request Parameters - Page 5:
2025-04-27 21:00:06,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:06,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:06,820 - INFO - Response - Page 5:
2025-04-27 21:00:07,024 - INFO - 第 5 页获取到 100 条记录
2025-04-27 21:00:07,024 - INFO - Request Parameters - Page 6:
2025-04-27 21:00:07,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:07,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:07,524 - INFO - Response - Page 6:
2025-04-27 21:00:07,727 - INFO - 第 6 页获取到 100 条记录
2025-04-27 21:00:07,727 - INFO - Request Parameters - Page 7:
2025-04-27 21:00:07,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:07,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:08,180 - INFO - Response - Page 7:
2025-04-27 21:00:08,383 - INFO - 第 7 页获取到 82 条记录
2025-04-27 21:00:08,383 - INFO - 查询完成，共获取到 682 条记录
2025-04-27 21:00:08,383 - INFO - 获取到 682 条表单数据
2025-04-27 21:00:08,383 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-27 21:00:08,399 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 21:00:08,399 - INFO - 开始处理日期: 2025-02
2025-04-27 21:00:08,399 - INFO - Request Parameters - Page 1:
2025-04-27 21:00:08,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:08,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:08,883 - INFO - Response - Page 1:
2025-04-27 21:00:09,086 - INFO - 第 1 页获取到 100 条记录
2025-04-27 21:00:09,086 - INFO - Request Parameters - Page 2:
2025-04-27 21:00:09,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:09,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:09,570 - INFO - Response - Page 2:
2025-04-27 21:00:09,773 - INFO - 第 2 页获取到 100 条记录
2025-04-27 21:00:09,773 - INFO - Request Parameters - Page 3:
2025-04-27 21:00:09,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:09,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:10,258 - INFO - Response - Page 3:
2025-04-27 21:00:10,461 - INFO - 第 3 页获取到 100 条记录
2025-04-27 21:00:10,461 - INFO - Request Parameters - Page 4:
2025-04-27 21:00:10,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:10,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:10,961 - INFO - Response - Page 4:
2025-04-27 21:00:11,164 - INFO - 第 4 页获取到 100 条记录
2025-04-27 21:00:11,164 - INFO - Request Parameters - Page 5:
2025-04-27 21:00:11,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:11,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:11,695 - INFO - Response - Page 5:
2025-04-27 21:00:11,898 - INFO - 第 5 页获取到 100 条记录
2025-04-27 21:00:11,898 - INFO - Request Parameters - Page 6:
2025-04-27 21:00:11,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:11,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:12,352 - INFO - Response - Page 6:
2025-04-27 21:00:12,555 - INFO - 第 6 页获取到 100 条记录
2025-04-27 21:00:12,555 - INFO - Request Parameters - Page 7:
2025-04-27 21:00:12,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:12,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:12,961 - INFO - Response - Page 7:
2025-04-27 21:00:13,164 - INFO - 第 7 页获取到 70 条记录
2025-04-27 21:00:13,164 - INFO - 查询完成，共获取到 670 条记录
2025-04-27 21:00:13,164 - INFO - 获取到 670 条表单数据
2025-04-27 21:00:13,164 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-27 21:00:13,180 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 21:00:13,180 - INFO - 开始处理日期: 2025-03
2025-04-27 21:00:13,180 - INFO - Request Parameters - Page 1:
2025-04-27 21:00:13,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:13,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:13,664 - INFO - Response - Page 1:
2025-04-27 21:00:13,867 - INFO - 第 1 页获取到 100 条记录
2025-04-27 21:00:13,867 - INFO - Request Parameters - Page 2:
2025-04-27 21:00:13,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:13,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:14,398 - INFO - Response - Page 2:
2025-04-27 21:00:14,602 - INFO - 第 2 页获取到 100 条记录
2025-04-27 21:00:14,602 - INFO - Request Parameters - Page 3:
2025-04-27 21:00:14,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:14,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:15,133 - INFO - Response - Page 3:
2025-04-27 21:00:15,336 - INFO - 第 3 页获取到 100 条记录
2025-04-27 21:00:15,336 - INFO - Request Parameters - Page 4:
2025-04-27 21:00:15,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:15,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:15,758 - INFO - Response - Page 4:
2025-04-27 21:00:15,961 - INFO - 第 4 页获取到 100 条记录
2025-04-27 21:00:15,961 - INFO - Request Parameters - Page 5:
2025-04-27 21:00:15,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:15,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:16,445 - INFO - Response - Page 5:
2025-04-27 21:00:16,648 - INFO - 第 5 页获取到 100 条记录
2025-04-27 21:00:16,648 - INFO - Request Parameters - Page 6:
2025-04-27 21:00:16,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:16,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:17,164 - INFO - Response - Page 6:
2025-04-27 21:00:17,367 - INFO - 第 6 页获取到 100 条记录
2025-04-27 21:00:17,367 - INFO - Request Parameters - Page 7:
2025-04-27 21:00:17,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:17,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:17,867 - INFO - Response - Page 7:
2025-04-27 21:00:18,070 - INFO - 第 7 页获取到 61 条记录
2025-04-27 21:00:18,070 - INFO - 查询完成，共获取到 661 条记录
2025-04-27 21:00:18,070 - INFO - 获取到 661 条表单数据
2025-04-27 21:00:18,070 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-27 21:00:18,086 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-27 21:00:18,086 - INFO - 开始处理日期: 2025-04
2025-04-27 21:00:18,086 - INFO - Request Parameters - Page 1:
2025-04-27 21:00:18,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:18,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:18,633 - INFO - Response - Page 1:
2025-04-27 21:00:18,836 - INFO - 第 1 页获取到 100 条记录
2025-04-27 21:00:18,836 - INFO - Request Parameters - Page 2:
2025-04-27 21:00:18,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:18,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:19,273 - INFO - Response - Page 2:
2025-04-27 21:00:19,492 - INFO - 第 2 页获取到 100 条记录
2025-04-27 21:00:19,492 - INFO - Request Parameters - Page 3:
2025-04-27 21:00:19,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:19,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:20,039 - INFO - Response - Page 3:
2025-04-27 21:00:20,242 - INFO - 第 3 页获取到 100 条记录
2025-04-27 21:00:20,242 - INFO - Request Parameters - Page 4:
2025-04-27 21:00:20,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:20,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:20,758 - INFO - Response - Page 4:
2025-04-27 21:00:20,961 - INFO - 第 4 页获取到 100 条记录
2025-04-27 21:00:20,961 - INFO - Request Parameters - Page 5:
2025-04-27 21:00:20,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:20,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:21,445 - INFO - Response - Page 5:
2025-04-27 21:00:21,648 - INFO - 第 5 页获取到 100 条记录
2025-04-27 21:00:21,648 - INFO - Request Parameters - Page 6:
2025-04-27 21:00:21,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:21,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:22,102 - INFO - Response - Page 6:
2025-04-27 21:00:22,305 - INFO - 第 6 页获取到 100 条记录
2025-04-27 21:00:22,305 - INFO - Request Parameters - Page 7:
2025-04-27 21:00:22,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-27 21:00:22,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-27 21:00:22,711 - INFO - Response - Page 7:
2025-04-27 21:00:22,914 - INFO - 第 7 页获取到 26 条记录
2025-04-27 21:00:22,914 - INFO - 查询完成，共获取到 626 条记录
2025-04-27 21:00:22,914 - INFO - 获取到 626 条表单数据
2025-04-27 21:00:22,914 - INFO - 当前日期 2025-04 有 626 条MySQL数据需要处理
2025-04-27 21:00:22,914 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-27 21:00:23,336 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MU3
2025-04-27 21:00:23,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11531.9, 'new_value': 11658.62}, {'field': 'offline_amount', 'old_value': 47958.64, 'new_value': 49184.04}, {'field': 'total_amount', 'old_value': 59490.54, 'new_value': 60842.66}, {'field': 'order_count', 'old_value': 1154, 'new_value': 1179}]
2025-04-27 21:00:23,352 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-27 21:00:23,773 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MD9
2025-04-27 21:00:23,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20856.34, 'new_value': 22825.42}, {'field': 'offline_amount', 'old_value': 37582.89, 'new_value': 39656.67}, {'field': 'total_amount', 'old_value': 58439.23, 'new_value': 62482.09}, {'field': 'order_count', 'old_value': 2383, 'new_value': 2564}]
2025-04-27 21:00:23,773 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-27 21:00:24,211 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MI9
2025-04-27 21:00:24,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179160.37, 'new_value': 191315.27}, {'field': 'total_amount', 'old_value': 179160.37, 'new_value': 191315.27}, {'field': 'order_count', 'old_value': 297, 'new_value': 311}]
2025-04-27 21:00:24,211 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-27 21:00:24,742 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MP9
2025-04-27 21:00:24,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98747.0, 'new_value': 82504.0}, {'field': 'total_amount', 'old_value': 309047.0, 'new_value': 292804.0}]
2025-04-27 21:00:24,742 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-27 21:00:25,180 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-04-27 21:00:25,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 350214.84, 'new_value': 382423.1}, {'field': 'offline_amount', 'old_value': 1187.0, 'new_value': 1243.0}, {'field': 'total_amount', 'old_value': 351401.84, 'new_value': 383666.1}, {'field': 'order_count', 'old_value': 4206, 'new_value': 4617}]
2025-04-27 21:00:25,180 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-27 21:00:25,664 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MEI
2025-04-27 21:00:25,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139726.0, 'new_value': 154797.0}, {'field': 'offline_amount', 'old_value': 109176.0, 'new_value': 116103.0}, {'field': 'total_amount', 'old_value': 248902.0, 'new_value': 270900.0}, {'field': 'order_count', 'old_value': 9514, 'new_value': 10356}]
2025-04-27 21:00:25,664 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-27 21:00:26,211 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MYI
2025-04-27 21:00:26,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31376.24, 'new_value': 34975.24}, {'field': 'total_amount', 'old_value': 31476.44, 'new_value': 35075.44}, {'field': 'order_count', 'old_value': 265, 'new_value': 283}]
2025-04-27 21:00:26,211 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-27 21:00:26,633 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-27 21:00:26,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92655.0, 'new_value': 96631.0}, {'field': 'offline_amount', 'old_value': 111364.0, 'new_value': 115461.0}, {'field': 'total_amount', 'old_value': 204019.0, 'new_value': 212092.0}, {'field': 'order_count', 'old_value': 5205, 'new_value': 5433}]
2025-04-27 21:00:26,648 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-27 21:00:27,102 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-04-27 21:00:27,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48643.53, 'new_value': 54869.44}, {'field': 'offline_amount', 'old_value': 48947.62, 'new_value': 52967.55}, {'field': 'total_amount', 'old_value': 97591.15, 'new_value': 107836.99}, {'field': 'order_count', 'old_value': 2459, 'new_value': 2688}]
2025-04-27 21:00:27,102 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-27 21:00:27,570 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MN2
2025-04-27 21:00:27,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 944263.37, 'new_value': 1056739.47}, {'field': 'total_amount', 'old_value': 944263.37, 'new_value': 1056739.47}, {'field': 'order_count', 'old_value': 3271, 'new_value': 3657}]
2025-04-27 21:00:27,586 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-27 21:00:27,992 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M32
2025-04-27 21:00:27,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70768.0, 'new_value': 74332.0}, {'field': 'offline_amount', 'old_value': 33420.0, 'new_value': 38595.0}, {'field': 'total_amount', 'old_value': 104188.0, 'new_value': 112927.0}, {'field': 'order_count', 'old_value': 183, 'new_value': 193}]
2025-04-27 21:00:27,992 - INFO - 日期 2025-04 处理完成 - 更新: 11 条，插入: 0 条，错误: 0 条
2025-04-27 21:00:27,992 - INFO - 数据同步完成！更新: 11 条，插入: 0 条，错误: 0 条
2025-04-27 21:00:27,992 - INFO - =================同步完成====================
