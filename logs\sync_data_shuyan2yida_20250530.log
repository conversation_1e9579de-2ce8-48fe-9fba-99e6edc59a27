2025-05-30 08:00:03,578 - INFO - ==================================================
2025-05-30 08:00:03,579 - INFO - 程序启动 - 版本 v1.0.0
2025-05-30 08:00:03,579 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250530.log
2025-05-30 08:00:03,579 - INFO - ==================================================
2025-05-30 08:00:03,580 - INFO - 程序入口点: __main__
2025-05-30 08:00:03,580 - INFO - ==================================================
2025-05-30 08:00:03,580 - INFO - 程序启动 - 版本 v1.0.1
2025-05-30 08:00:03,580 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250530.log
2025-05-30 08:00:03,580 - INFO - ==================================================
2025-05-30 08:00:03,895 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-30 08:00:03,896 - INFO - sales_data表已存在，无需创建
2025-05-30 08:00:03,897 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-30 08:00:03,897 - INFO - DataSyncManager初始化完成
2025-05-30 08:00:03,897 - INFO - 未提供日期参数，使用默认值
2025-05-30 08:00:03,897 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-30 08:00:03,898 - INFO - 开始综合数据同步流程...
2025-05-30 08:00:03,898 - INFO - 正在获取数衍平台日销售数据...
2025-05-30 08:00:03,898 - INFO - 查询数衍平台数据，时间段为: 2025-03-30, 2025-05-29
2025-05-30 08:00:03,898 - INFO - 正在获取********至********的数据
2025-05-30 08:00:03,898 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:03,898 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AF4161F2E869E841B2E2C4A814EBF4DB'}
2025-05-30 08:00:09,588 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:09,601 - INFO - 过滤后保留 1522 条记录
2025-05-30 08:00:11,603 - INFO - 正在获取********至********的数据
2025-05-30 08:00:11,603 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:11,604 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'ADB25A2A326DD3E794B2975059EFDC39'}
2025-05-30 08:00:14,742 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:14,755 - INFO - 过滤后保留 1496 条记录
2025-05-30 08:00:16,755 - INFO - 正在获取********至********的数据
2025-05-30 08:00:16,755 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:16,756 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DFA615C5E4AAD245E49DDD1638AD1C24'}
2025-05-30 08:00:19,535 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:19,549 - INFO - 过滤后保留 1509 条记录
2025-05-30 08:00:21,550 - INFO - 正在获取********至********的数据
2025-05-30 08:00:21,550 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:21,551 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A7952B782798E6EEE39B61470AE7F824'}
2025-05-30 08:00:24,086 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:24,098 - INFO - 过滤后保留 1482 条记录
2025-05-30 08:00:26,099 - INFO - 正在获取********至********的数据
2025-05-30 08:00:26,099 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:26,100 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7A6757AC42E0692501BD8A9919122EE6'}
2025-05-30 08:00:29,992 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:30,005 - INFO - 过滤后保留 1492 条记录
2025-05-30 08:00:32,006 - INFO - 正在获取********至********的数据
2025-05-30 08:00:32,006 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:32,007 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EA2FE62C493076C4D6C82717CD26F863'}
2025-05-30 08:00:34,628 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:34,640 - INFO - 过滤后保留 1470 条记录
2025-05-30 08:00:36,642 - INFO - 正在获取********至********的数据
2025-05-30 08:00:36,642 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:36,643 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '525B1A4BF419CD920510618011965C5C'}
2025-05-30 08:00:38,990 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:39,003 - INFO - 过滤后保留 1483 条记录
2025-05-30 08:00:41,005 - INFO - 正在获取********至********的数据
2025-05-30 08:00:41,005 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:41,005 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B0A43A78C3626817B25DEB14C0E367F8'}
2025-05-30 08:00:43,217 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:43,230 - INFO - 过滤后保留 1477 条记录
2025-05-30 08:00:45,232 - INFO - 正在获取********至********的数据
2025-05-30 08:00:45,232 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-30 08:00:45,233 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8DD1A0AEB14A1FBACD312C41EFB91F4C'}
2025-05-30 08:00:46,974 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-30 08:00:46,984 - INFO - 过滤后保留 1030 条记录
2025-05-30 08:00:48,985 - INFO - 开始保存数据到SQLite数据库，共 12961 条记录待处理
2025-05-30 08:00:49,475 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-01
2025-05-30 08:00:49,476 - INFO - 变更字段: amount: 48654 -> 48714, count: 295 -> 296, instore_amount: 31941.0 -> 32001.0, instore_count: 169 -> 170
2025-05-30 08:00:49,479 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-02
2025-05-30 08:00:49,480 - INFO - 变更字段: recommend_amount: 0.0 -> 18470.89, daily_bill_amount: 0.0 -> 18470.89
2025-05-30 08:00:49,480 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-01
2025-05-30 08:00:49,480 - INFO - 变更字段: recommend_amount: 0.0 -> 24548.58, daily_bill_amount: 0.0 -> 24548.58
2025-05-30 08:00:49,538 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-10
2025-05-30 08:00:49,539 - INFO - 变更字段: amount: 42931 -> 43016, count: 299 -> 300, instore_amount: 21580.9 -> 21665.9, instore_count: 107 -> 108
2025-05-30 08:00:49,542 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-08
2025-05-30 08:00:49,543 - INFO - 变更字段: recommend_amount: 0.0 -> 8399.56, daily_bill_amount: 0.0 -> 8399.56
2025-05-30 08:00:49,543 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-06
2025-05-30 08:00:49,543 - INFO - 变更字段: recommend_amount: 0.0 -> 5781.47, daily_bill_amount: 0.0 -> 5781.47
2025-05-30 08:00:49,544 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-05
2025-05-30 08:00:49,544 - INFO - 变更字段: recommend_amount: 0.0 -> 15002.42, daily_bill_amount: 0.0 -> 15002.42
2025-05-30 08:00:49,600 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-16
2025-05-30 08:00:49,600 - INFO - 变更字段: recommend_amount: 0.0 -> 23348.63, daily_bill_amount: 0.0 -> 23348.63
2025-05-30 08:00:49,601 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-12
2025-05-30 08:00:49,602 - INFO - 变更字段: recommend_amount: 0.0 -> 16837.53, daily_bill_amount: 0.0 -> 16837.53
2025-05-30 08:00:49,605 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-17
2025-05-30 08:00:49,605 - INFO - 变更字段: recommend_amount: 0.0 -> 19822.37, daily_bill_amount: 0.0 -> 19822.37
2025-05-30 08:00:49,605 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-16
2025-05-30 08:00:49,605 - INFO - 变更字段: recommend_amount: 9505.8 -> 9473.7, daily_bill_amount: 9505.8 -> 9473.7
2025-05-30 08:00:49,606 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-15
2025-05-30 08:00:49,606 - INFO - 变更字段: recommend_amount: 0.0 -> 8720.62, daily_bill_amount: 0.0 -> 8720.62
2025-05-30 08:00:49,606 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-14
2025-05-30 08:00:49,606 - INFO - 变更字段: recommend_amount: 0.0 -> 7846.66, daily_bill_amount: 0.0 -> 7846.66
2025-05-30 08:00:49,606 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-13
2025-05-30 08:00:49,607 - INFO - 变更字段: recommend_amount: 0.0 -> 8228.69, daily_bill_amount: 0.0 -> 8228.69
2025-05-30 08:00:49,607 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-12
2025-05-30 08:00:49,607 - INFO - 变更字段: recommend_amount: 0.0 -> 6631.72, daily_bill_amount: 0.0 -> 6631.72
2025-05-30 08:00:49,607 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-11
2025-05-30 08:00:49,607 - INFO - 变更字段: recommend_amount: 0.0 -> 25387.37, daily_bill_amount: 0.0 -> 25387.37
2025-05-30 08:00:49,665 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-24
2025-05-30 08:00:49,665 - INFO - 变更字段: amount: 43138 -> 43541, count: 298 -> 300, instore_amount: 26291.8 -> 26694.8, instore_count: 135 -> 137
2025-05-30 08:00:49,669 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-24
2025-05-30 08:00:49,669 - INFO - 变更字段: recommend_amount: 0.0 -> 19724.04, daily_bill_amount: 0.0 -> 19724.04
2025-05-30 08:00:49,670 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-23
2025-05-30 08:00:49,670 - INFO - 变更字段: recommend_amount: 0.0 -> 9752.12, daily_bill_amount: 0.0 -> 9752.12
2025-05-30 08:00:49,670 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-22
2025-05-30 08:00:49,670 - INFO - 变更字段: recommend_amount: 0.0 -> 8012.1, daily_bill_amount: 0.0 -> 8012.1
2025-05-30 08:00:49,670 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-21
2025-05-30 08:00:49,671 - INFO - 变更字段: recommend_amount: 0.0 -> 9317.01, daily_bill_amount: 0.0 -> 9317.01
2025-05-30 08:00:49,671 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-20
2025-05-30 08:00:49,671 - INFO - 变更字段: recommend_amount: 0.0 -> 8579.37, daily_bill_amount: 0.0 -> 8579.37
2025-05-30 08:00:49,671 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-19
2025-05-30 08:00:49,671 - INFO - 变更字段: recommend_amount: 0.0 -> 9093.63, daily_bill_amount: 0.0 -> 9093.63
2025-05-30 08:00:49,672 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-18
2025-05-30 08:00:49,672 - INFO - 变更字段: recommend_amount: 0.0 -> 20910.2, daily_bill_amount: 0.0 -> 20910.2
2025-05-30 08:00:49,699 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-28
2025-05-30 08:00:49,699 - INFO - 变更字段: recommend_amount: 0.0 -> 2439.0, daily_bill_amount: 0.0 -> 2439.0
2025-05-30 08:00:49,713 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-28
2025-05-30 08:00:49,714 - INFO - 变更字段: recommend_amount: 0.0 -> 6378.5, daily_bill_amount: 0.0 -> 6378.5
2025-05-30 08:00:49,716 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=6D1F9FC749FA44C6B70CA818C3E7FB77, sale_time=2025-05-28
2025-05-30 08:00:49,716 - INFO - 变更字段: recommend_amount: 0.0 -> 269.0, daily_bill_amount: 0.0 -> 269.0
2025-05-30 08:00:49,719 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-28
2025-05-30 08:00:49,719 - INFO - 变更字段: recommend_amount: 2583.37 -> 2595.97, amount: 2583 -> 2595, count: 143 -> 144, online_amount: 2120.41 -> 2133.01, online_count: 120 -> 121
2025-05-30 08:00:49,725 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-28
2025-05-30 08:00:49,725 - INFO - 变更字段: recommend_amount: 0.0 -> 19166.95, daily_bill_amount: 0.0 -> 19166.95
2025-05-30 08:00:49,731 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-28
2025-05-30 08:00:49,731 - INFO - 变更字段: amount: 369 -> 5372, count: 21 -> 268, instore_amount: 93.3 -> 1533.78, instore_count: 5 -> 70, online_amount: 276.5 -> 4008.4, online_count: 16 -> 198
2025-05-30 08:00:49,731 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-27
2025-05-30 08:00:49,731 - INFO - 变更字段: recommend_amount: 0.0 -> 7731.59, daily_bill_amount: 0.0 -> 7731.59, amount: 2949 -> 5589, count: 143 -> 266, instore_amount: 861.5 -> 1625.1, instore_count: 49 -> 79, online_amount: 2088.32 -> 4072.52, online_count: 94 -> 187
2025-05-30 08:00:49,732 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-26
2025-05-30 08:00:49,732 - INFO - 变更字段: recommend_amount: 0.0 -> 6951.83, daily_bill_amount: 0.0 -> 6951.83
2025-05-30 08:00:49,732 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-05-25
2025-05-30 08:00:49,732 - INFO - 变更字段: recommend_amount: 0.0 -> 19479.24, daily_bill_amount: 0.0 -> 19479.24
2025-05-30 08:00:49,944 - INFO - SQLite数据保存完成，统计信息：
2025-05-30 08:00:49,944 - INFO - - 总记录数: 12961
2025-05-30 08:00:49,945 - INFO - - 成功插入: 204
2025-05-30 08:00:49,945 - INFO - - 成功更新: 33
2025-05-30 08:00:49,945 - INFO - - 无需更新: 12724
2025-05-30 08:00:49,945 - INFO - - 处理失败: 0
2025-05-30 08:00:55,437 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250530.xlsx
2025-05-30 08:00:55,445 - INFO - 成功获取数衍平台数据，共 12961 条记录
2025-05-30 08:00:55,446 - INFO - 正在更新SQLite月度汇总数据...
2025-05-30 08:00:55,453 - INFO - 月度数据sqllite清空完成
2025-05-30 08:00:55,702 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-30 08:00:55,702 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-30 08:00:55,703 - INFO - 正在获取宜搭日销售表单数据...
2025-05-30 08:00:55,703 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-30 00:00:00 至 2025-05-29 23:59:59
2025-05-30 08:00:55,703 - INFO - 查询分段 1: 2025-03-30 至 2025-04-05
2025-05-30 08:00:55,703 - INFO - 查询日期范围: 2025-03-30 至 2025-04-05，使用分页查询，每页 100 条记录
2025-05-30 08:00:55,703 - INFO - Request Parameters - Page 1:
2025-05-30 08:00:55,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:00:55,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:03,818 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 54146375-BD83-7575-B82C-D870EECB2A99 Response: {'code': 'ServiceUnavailable', 'requestid': '54146375-BD83-7575-B82C-D870EECB2A99', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-30 08:01:03,819 - ERROR - 服务不可用，将等待后重试
2025-05-30 08:01:03,820 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 54146375-BD83-7575-B82C-D870EECB2A99 Response: {'code': 'ServiceUnavailable', 'requestid': '54146375-BD83-7575-B82C-D870EECB2A99', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-30 08:01:03,820 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-05-30 08:01:09,821 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-05-30 08:01:09,821 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 54146375-BD83-7575-B82C-D870EECB2A99 Response: {'code': 'ServiceUnavailable', 'requestid': '54146375-BD83-7575-B82C-D870EECB2A99', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-05-30 08:01:19,823 - INFO - Request Parameters - Page 1:
2025-05-30 08:01:19,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:19,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:20,690 - INFO - API请求耗时: 866ms
2025-05-30 08:01:20,691 - INFO - Response - Page 1
2025-05-30 08:01:20,691 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:01:21,191 - INFO - Request Parameters - Page 2:
2025-05-30 08:01:21,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:21,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:21,994 - INFO - API请求耗时: 802ms
2025-05-30 08:01:21,994 - INFO - Response - Page 2
2025-05-30 08:01:21,995 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:01:22,495 - INFO - Request Parameters - Page 3:
2025-05-30 08:01:22,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:22,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:23,375 - INFO - API请求耗时: 879ms
2025-05-30 08:01:23,375 - INFO - Response - Page 3
2025-05-30 08:01:23,376 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:01:23,877 - INFO - Request Parameters - Page 4:
2025-05-30 08:01:23,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:23,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:24,653 - INFO - API请求耗时: 773ms
2025-05-30 08:01:24,653 - INFO - Response - Page 4
2025-05-30 08:01:24,654 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:01:25,154 - INFO - Request Parameters - Page 5:
2025-05-30 08:01:25,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:25,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:25,872 - INFO - API请求耗时: 718ms
2025-05-30 08:01:25,872 - INFO - Response - Page 5
2025-05-30 08:01:25,873 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:01:26,374 - INFO - Request Parameters - Page 6:
2025-05-30 08:01:26,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:26,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:27,138 - INFO - API请求耗时: 763ms
2025-05-30 08:01:27,138 - INFO - Response - Page 6
2025-05-30 08:01:27,139 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:01:27,640 - INFO - Request Parameters - Page 7:
2025-05-30 08:01:27,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:27,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:28,371 - INFO - API请求耗时: 730ms
2025-05-30 08:01:28,372 - INFO - Response - Page 7
2025-05-30 08:01:28,372 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:01:28,873 - INFO - Request Parameters - Page 8:
2025-05-30 08:01:28,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:28,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:29,730 - INFO - API请求耗时: 855ms
2025-05-30 08:01:29,730 - INFO - Response - Page 8
2025-05-30 08:01:29,731 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:01:30,231 - INFO - Request Parameters - Page 9:
2025-05-30 08:01:30,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:30,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:31,025 - INFO - API请求耗时: 793ms
2025-05-30 08:01:31,025 - INFO - Response - Page 9
2025-05-30 08:01:31,026 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:01:31,527 - INFO - Request Parameters - Page 10:
2025-05-30 08:01:31,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:31,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:32,402 - INFO - API请求耗时: 874ms
2025-05-30 08:01:32,403 - INFO - Response - Page 10
2025-05-30 08:01:32,403 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:01:32,904 - INFO - Request Parameters - Page 11:
2025-05-30 08:01:32,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:32,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:33,648 - INFO - API请求耗时: 742ms
2025-05-30 08:01:33,648 - INFO - Response - Page 11
2025-05-30 08:01:33,649 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:01:34,149 - INFO - Request Parameters - Page 12:
2025-05-30 08:01:34,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:34,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:34,902 - INFO - API请求耗时: 752ms
2025-05-30 08:01:34,903 - INFO - Response - Page 12
2025-05-30 08:01:34,903 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:01:35,405 - INFO - Request Parameters - Page 13:
2025-05-30 08:01:35,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:35,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:36,260 - INFO - API请求耗时: 854ms
2025-05-30 08:01:36,261 - INFO - Response - Page 13
2025-05-30 08:01:36,262 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:01:36,763 - INFO - Request Parameters - Page 14:
2025-05-30 08:01:36,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:36,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:37,537 - INFO - API请求耗时: 773ms
2025-05-30 08:01:37,537 - INFO - Response - Page 14
2025-05-30 08:01:37,538 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:01:38,039 - INFO - Request Parameters - Page 15:
2025-05-30 08:01:38,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:38,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:38,757 - INFO - API请求耗时: 717ms
2025-05-30 08:01:38,757 - INFO - Response - Page 15
2025-05-30 08:01:38,758 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:01:39,258 - INFO - Request Parameters - Page 16:
2025-05-30 08:01:39,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:39,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:39,979 - INFO - API请求耗时: 720ms
2025-05-30 08:01:39,980 - INFO - Response - Page 16
2025-05-30 08:01:39,981 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:01:40,482 - INFO - Request Parameters - Page 17:
2025-05-30 08:01:40,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:40,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:41,268 - INFO - API请求耗时: 784ms
2025-05-30 08:01:41,268 - INFO - Response - Page 17
2025-05-30 08:01:41,269 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:01:41,770 - INFO - Request Parameters - Page 18:
2025-05-30 08:01:41,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:41,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:42,578 - INFO - API请求耗时: 808ms
2025-05-30 08:01:42,578 - INFO - Response - Page 18
2025-05-30 08:01:42,579 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:01:43,080 - INFO - Request Parameters - Page 19:
2025-05-30 08:01:43,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:43,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:43,763 - INFO - API请求耗时: 682ms
2025-05-30 08:01:43,763 - INFO - Response - Page 19
2025-05-30 08:01:43,764 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:01:44,265 - INFO - Request Parameters - Page 20:
2025-05-30 08:01:44,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:44,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:44,964 - INFO - API请求耗时: 698ms
2025-05-30 08:01:44,965 - INFO - Response - Page 20
2025-05-30 08:01:44,965 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:01:45,466 - INFO - Request Parameters - Page 21:
2025-05-30 08:01:45,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:45,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:46,137 - INFO - API请求耗时: 670ms
2025-05-30 08:01:46,137 - INFO - Response - Page 21
2025-05-30 08:01:46,138 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:01:46,638 - INFO - Request Parameters - Page 22:
2025-05-30 08:01:46,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:46,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:47,387 - INFO - API请求耗时: 748ms
2025-05-30 08:01:47,388 - INFO - Response - Page 22
2025-05-30 08:01:47,388 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:01:47,888 - INFO - Request Parameters - Page 23:
2025-05-30 08:01:47,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:47,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:48,665 - INFO - API请求耗时: 776ms
2025-05-30 08:01:48,665 - INFO - Response - Page 23
2025-05-30 08:01:48,666 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:01:49,168 - INFO - Request Parameters - Page 24:
2025-05-30 08:01:49,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:49,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:49,897 - INFO - API请求耗时: 728ms
2025-05-30 08:01:49,897 - INFO - Response - Page 24
2025-05-30 08:01:49,898 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:01:50,399 - INFO - Request Parameters - Page 25:
2025-05-30 08:01:50,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:50,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:51,227 - INFO - API请求耗时: 827ms
2025-05-30 08:01:51,228 - INFO - Response - Page 25
2025-05-30 08:01:51,228 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:01:51,730 - INFO - Request Parameters - Page 26:
2025-05-30 08:01:51,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:51,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:52,488 - INFO - API请求耗时: 758ms
2025-05-30 08:01:52,488 - INFO - Response - Page 26
2025-05-30 08:01:52,489 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:01:52,989 - INFO - Request Parameters - Page 27:
2025-05-30 08:01:52,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:52,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:53,689 - INFO - API请求耗时: 698ms
2025-05-30 08:01:53,690 - INFO - Response - Page 27
2025-05-30 08:01:53,690 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:01:54,191 - INFO - Request Parameters - Page 28:
2025-05-30 08:01:54,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:54,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:55,124 - INFO - API请求耗时: 933ms
2025-05-30 08:01:55,124 - INFO - Response - Page 28
2025-05-30 08:01:55,125 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:01:55,626 - INFO - Request Parameters - Page 29:
2025-05-30 08:01:55,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:55,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:56,338 - INFO - API请求耗时: 710ms
2025-05-30 08:01:56,338 - INFO - Response - Page 29
2025-05-30 08:01:56,339 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:01:56,839 - INFO - Request Parameters - Page 30:
2025-05-30 08:01:56,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:56,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:57,514 - INFO - API请求耗时: 674ms
2025-05-30 08:01:57,515 - INFO - Response - Page 30
2025-05-30 08:01:57,515 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:01:58,015 - INFO - Request Parameters - Page 31:
2025-05-30 08:01:58,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:58,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:01:58,745 - INFO - API请求耗时: 729ms
2025-05-30 08:01:58,746 - INFO - Response - Page 31
2025-05-30 08:01:58,747 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:01:59,248 - INFO - Request Parameters - Page 32:
2025-05-30 08:01:59,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:01:59,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:00,213 - INFO - API请求耗时: 964ms
2025-05-30 08:02:00,213 - INFO - Response - Page 32
2025-05-30 08:02:00,214 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:02:00,715 - INFO - Request Parameters - Page 33:
2025-05-30 08:02:00,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:00,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:01,564 - INFO - API请求耗时: 848ms
2025-05-30 08:02:01,564 - INFO - Response - Page 33
2025-05-30 08:02:01,565 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:02:02,066 - INFO - Request Parameters - Page 34:
2025-05-30 08:02:02,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:02,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:02,805 - INFO - API请求耗时: 738ms
2025-05-30 08:02:02,805 - INFO - Response - Page 34
2025-05-30 08:02:02,806 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:02:03,307 - INFO - Request Parameters - Page 35:
2025-05-30 08:02:03,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:03,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:04,024 - INFO - API请求耗时: 716ms
2025-05-30 08:02:04,024 - INFO - Response - Page 35
2025-05-30 08:02:04,025 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:02:04,526 - INFO - Request Parameters - Page 36:
2025-05-30 08:02:04,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:04,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:05,251 - INFO - API请求耗时: 725ms
2025-05-30 08:02:05,252 - INFO - Response - Page 36
2025-05-30 08:02:05,252 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:02:05,753 - INFO - Request Parameters - Page 37:
2025-05-30 08:02:05,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:05,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:06,475 - INFO - API请求耗时: 720ms
2025-05-30 08:02:06,475 - INFO - Response - Page 37
2025-05-30 08:02:06,476 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:02:06,977 - INFO - Request Parameters - Page 38:
2025-05-30 08:02:06,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:06,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:07,749 - INFO - API请求耗时: 771ms
2025-05-30 08:02:07,749 - INFO - Response - Page 38
2025-05-30 08:02:07,750 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:02:08,251 - INFO - Request Parameters - Page 39:
2025-05-30 08:02:08,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:08,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:09,045 - INFO - API请求耗时: 793ms
2025-05-30 08:02:09,045 - INFO - Response - Page 39
2025-05-30 08:02:09,046 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:02:09,547 - INFO - Request Parameters - Page 40:
2025-05-30 08:02:09,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:09,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:10,289 - INFO - API请求耗时: 741ms
2025-05-30 08:02:10,289 - INFO - Response - Page 40
2025-05-30 08:02:10,290 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:02:10,791 - INFO - Request Parameters - Page 41:
2025-05-30 08:02:10,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:10,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:11,479 - INFO - API请求耗时: 687ms
2025-05-30 08:02:11,479 - INFO - Response - Page 41
2025-05-30 08:02:11,480 - INFO - 第 41 页获取到 100 条记录
2025-05-30 08:02:11,981 - INFO - Request Parameters - Page 42:
2025-05-30 08:02:11,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:11,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:12,717 - INFO - API请求耗时: 735ms
2025-05-30 08:02:12,717 - INFO - Response - Page 42
2025-05-30 08:02:12,718 - INFO - 第 42 页获取到 100 条记录
2025-05-30 08:02:13,219 - INFO - Request Parameters - Page 43:
2025-05-30 08:02:13,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:13,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:13,936 - INFO - API请求耗时: 716ms
2025-05-30 08:02:13,936 - INFO - Response - Page 43
2025-05-30 08:02:13,937 - INFO - 第 43 页获取到 100 条记录
2025-05-30 08:02:14,438 - INFO - Request Parameters - Page 44:
2025-05-30 08:02:14,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:14,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000703, 1743782400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:15,003 - INFO - API请求耗时: 564ms
2025-05-30 08:02:15,004 - INFO - Response - Page 44
2025-05-30 08:02:15,004 - INFO - 第 44 页获取到 29 条记录
2025-05-30 08:02:15,004 - INFO - 查询完成，共获取到 4329 条记录
2025-05-30 08:02:15,005 - INFO - 分段 1 查询成功，获取到 4329 条记录
2025-05-30 08:02:16,006 - INFO - 查询分段 2: 2025-04-06 至 2025-04-12
2025-05-30 08:02:16,006 - INFO - 查询日期范围: 2025-04-06 至 2025-04-12，使用分页查询，每页 100 条记录
2025-05-30 08:02:16,007 - INFO - Request Parameters - Page 1:
2025-05-30 08:02:16,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:16,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:16,738 - INFO - API请求耗时: 730ms
2025-05-30 08:02:16,739 - INFO - Response - Page 1
2025-05-30 08:02:16,740 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:02:17,241 - INFO - Request Parameters - Page 2:
2025-05-30 08:02:17,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:17,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:17,964 - INFO - API请求耗时: 722ms
2025-05-30 08:02:17,965 - INFO - Response - Page 2
2025-05-30 08:02:17,965 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:02:18,465 - INFO - Request Parameters - Page 3:
2025-05-30 08:02:18,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:18,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:19,260 - INFO - API请求耗时: 794ms
2025-05-30 08:02:19,261 - INFO - Response - Page 3
2025-05-30 08:02:19,261 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:02:19,763 - INFO - Request Parameters - Page 4:
2025-05-30 08:02:19,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:19,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:20,543 - INFO - API请求耗时: 779ms
2025-05-30 08:02:20,543 - INFO - Response - Page 4
2025-05-30 08:02:20,544 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:02:21,044 - INFO - Request Parameters - Page 5:
2025-05-30 08:02:21,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:21,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:21,779 - INFO - API请求耗时: 734ms
2025-05-30 08:02:21,780 - INFO - Response - Page 5
2025-05-30 08:02:21,780 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:02:22,281 - INFO - Request Parameters - Page 6:
2025-05-30 08:02:22,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:22,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:23,013 - INFO - API请求耗时: 730ms
2025-05-30 08:02:23,013 - INFO - Response - Page 6
2025-05-30 08:02:23,014 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:02:23,515 - INFO - Request Parameters - Page 7:
2025-05-30 08:02:23,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:23,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:24,187 - INFO - API请求耗时: 671ms
2025-05-30 08:02:24,187 - INFO - Response - Page 7
2025-05-30 08:02:24,187 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:02:24,688 - INFO - Request Parameters - Page 8:
2025-05-30 08:02:24,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:24,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:25,745 - INFO - API请求耗时: 1056ms
2025-05-30 08:02:25,745 - INFO - Response - Page 8
2025-05-30 08:02:25,746 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:02:26,247 - INFO - Request Parameters - Page 9:
2025-05-30 08:02:26,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:26,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:27,029 - INFO - API请求耗时: 781ms
2025-05-30 08:02:27,030 - INFO - Response - Page 9
2025-05-30 08:02:27,030 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:02:27,532 - INFO - Request Parameters - Page 10:
2025-05-30 08:02:27,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:27,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:28,297 - INFO - API请求耗时: 764ms
2025-05-30 08:02:28,297 - INFO - Response - Page 10
2025-05-30 08:02:28,298 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:02:28,799 - INFO - Request Parameters - Page 11:
2025-05-30 08:02:28,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:28,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:29,546 - INFO - API请求耗时: 746ms
2025-05-30 08:02:29,546 - INFO - Response - Page 11
2025-05-30 08:02:29,547 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:02:30,047 - INFO - Request Parameters - Page 12:
2025-05-30 08:02:30,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:30,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:30,854 - INFO - API请求耗时: 807ms
2025-05-30 08:02:30,855 - INFO - Response - Page 12
2025-05-30 08:02:30,855 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:02:31,357 - INFO - Request Parameters - Page 13:
2025-05-30 08:02:31,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:31,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:32,156 - INFO - API请求耗时: 798ms
2025-05-30 08:02:32,156 - INFO - Response - Page 13
2025-05-30 08:02:32,156 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:02:32,657 - INFO - Request Parameters - Page 14:
2025-05-30 08:02:32,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:32,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:33,365 - INFO - API请求耗时: 707ms
2025-05-30 08:02:33,366 - INFO - Response - Page 14
2025-05-30 08:02:33,366 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:02:33,866 - INFO - Request Parameters - Page 15:
2025-05-30 08:02:33,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:33,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:34,634 - INFO - API请求耗时: 767ms
2025-05-30 08:02:34,634 - INFO - Response - Page 15
2025-05-30 08:02:34,635 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:02:35,136 - INFO - Request Parameters - Page 16:
2025-05-30 08:02:35,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:35,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:35,931 - INFO - API请求耗时: 794ms
2025-05-30 08:02:35,931 - INFO - Response - Page 16
2025-05-30 08:02:35,932 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:02:36,433 - INFO - Request Parameters - Page 17:
2025-05-30 08:02:36,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:36,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:37,514 - INFO - API请求耗时: 1080ms
2025-05-30 08:02:37,514 - INFO - Response - Page 17
2025-05-30 08:02:37,515 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:02:38,015 - INFO - Request Parameters - Page 18:
2025-05-30 08:02:38,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:38,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:38,739 - INFO - API请求耗时: 723ms
2025-05-30 08:02:38,739 - INFO - Response - Page 18
2025-05-30 08:02:38,740 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:02:39,241 - INFO - Request Parameters - Page 19:
2025-05-30 08:02:39,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:39,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:39,948 - INFO - API请求耗时: 706ms
2025-05-30 08:02:39,948 - INFO - Response - Page 19
2025-05-30 08:02:39,949 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:02:40,449 - INFO - Request Parameters - Page 20:
2025-05-30 08:02:40,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:40,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:41,150 - INFO - API请求耗时: 700ms
2025-05-30 08:02:41,150 - INFO - Response - Page 20
2025-05-30 08:02:41,151 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:02:41,651 - INFO - Request Parameters - Page 21:
2025-05-30 08:02:41,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:41,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:42,376 - INFO - API请求耗时: 724ms
2025-05-30 08:02:42,376 - INFO - Response - Page 21
2025-05-30 08:02:42,377 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:02:42,878 - INFO - Request Parameters - Page 22:
2025-05-30 08:02:42,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:42,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:43,564 - INFO - API请求耗时: 686ms
2025-05-30 08:02:43,564 - INFO - Response - Page 22
2025-05-30 08:02:43,565 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:02:44,065 - INFO - Request Parameters - Page 23:
2025-05-30 08:02:44,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:44,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:44,757 - INFO - API请求耗时: 690ms
2025-05-30 08:02:44,758 - INFO - Response - Page 23
2025-05-30 08:02:44,758 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:02:45,258 - INFO - Request Parameters - Page 24:
2025-05-30 08:02:45,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:45,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:46,028 - INFO - API请求耗时: 770ms
2025-05-30 08:02:46,028 - INFO - Response - Page 24
2025-05-30 08:02:46,029 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:02:46,530 - INFO - Request Parameters - Page 25:
2025-05-30 08:02:46,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:46,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:47,296 - INFO - API请求耗时: 765ms
2025-05-30 08:02:47,296 - INFO - Response - Page 25
2025-05-30 08:02:47,297 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:02:47,797 - INFO - Request Parameters - Page 26:
2025-05-30 08:02:47,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:47,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:48,553 - INFO - API请求耗时: 755ms
2025-05-30 08:02:48,554 - INFO - Response - Page 26
2025-05-30 08:02:48,555 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:02:49,055 - INFO - Request Parameters - Page 27:
2025-05-30 08:02:49,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:49,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:49,784 - INFO - API请求耗时: 727ms
2025-05-30 08:02:49,784 - INFO - Response - Page 27
2025-05-30 08:02:49,785 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:02:50,286 - INFO - Request Parameters - Page 28:
2025-05-30 08:02:50,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:50,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:51,103 - INFO - API请求耗时: 816ms
2025-05-30 08:02:51,103 - INFO - Response - Page 28
2025-05-30 08:02:51,104 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:02:51,605 - INFO - Request Parameters - Page 29:
2025-05-30 08:02:51,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:51,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:52,280 - INFO - API请求耗时: 674ms
2025-05-30 08:02:52,280 - INFO - Response - Page 29
2025-05-30 08:02:52,281 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:02:52,782 - INFO - Request Parameters - Page 30:
2025-05-30 08:02:52,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:52,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:53,490 - INFO - API请求耗时: 707ms
2025-05-30 08:02:53,491 - INFO - Response - Page 30
2025-05-30 08:02:53,491 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:02:53,993 - INFO - Request Parameters - Page 31:
2025-05-30 08:02:53,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:53,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:54,824 - INFO - API请求耗时: 830ms
2025-05-30 08:02:54,824 - INFO - Response - Page 31
2025-05-30 08:02:54,825 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:02:55,326 - INFO - Request Parameters - Page 32:
2025-05-30 08:02:55,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:55,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:56,049 - INFO - API请求耗时: 722ms
2025-05-30 08:02:56,050 - INFO - Response - Page 32
2025-05-30 08:02:56,050 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:02:56,550 - INFO - Request Parameters - Page 33:
2025-05-30 08:02:56,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:56,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:57,300 - INFO - API请求耗时: 749ms
2025-05-30 08:02:57,300 - INFO - Response - Page 33
2025-05-30 08:02:57,301 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:02:57,802 - INFO - Request Parameters - Page 34:
2025-05-30 08:02:57,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:57,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:58,598 - INFO - API请求耗时: 796ms
2025-05-30 08:02:58,599 - INFO - Response - Page 34
2025-05-30 08:02:58,599 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:02:59,101 - INFO - Request Parameters - Page 35:
2025-05-30 08:02:59,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:02:59,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:02:59,850 - INFO - API请求耗时: 748ms
2025-05-30 08:02:59,850 - INFO - Response - Page 35
2025-05-30 08:02:59,851 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:03:00,352 - INFO - Request Parameters - Page 36:
2025-05-30 08:03:00,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:00,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:01,504 - INFO - API请求耗时: 1150ms
2025-05-30 08:03:01,504 - INFO - Response - Page 36
2025-05-30 08:03:01,505 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:03:02,006 - INFO - Request Parameters - Page 37:
2025-05-30 08:03:02,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:02,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:02,844 - INFO - API请求耗时: 838ms
2025-05-30 08:03:02,845 - INFO - Response - Page 37
2025-05-30 08:03:02,845 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:03:03,346 - INFO - Request Parameters - Page 38:
2025-05-30 08:03:03,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:03,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:04,016 - INFO - API请求耗时: 669ms
2025-05-30 08:03:04,017 - INFO - Response - Page 38
2025-05-30 08:03:04,017 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:03:04,519 - INFO - Request Parameters - Page 39:
2025-05-30 08:03:04,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:04,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:05,250 - INFO - API请求耗时: 729ms
2025-05-30 08:03:05,250 - INFO - Response - Page 39
2025-05-30 08:03:05,251 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:03:05,751 - INFO - Request Parameters - Page 40:
2025-05-30 08:03:05,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:05,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:06,515 - INFO - API请求耗时: 764ms
2025-05-30 08:03:06,516 - INFO - Response - Page 40
2025-05-30 08:03:06,516 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:03:07,017 - INFO - Request Parameters - Page 41:
2025-05-30 08:03:07,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:07,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:07,744 - INFO - API请求耗时: 725ms
2025-05-30 08:03:07,744 - INFO - Response - Page 41
2025-05-30 08:03:07,745 - INFO - 第 41 页获取到 100 条记录
2025-05-30 08:03:08,245 - INFO - Request Parameters - Page 42:
2025-05-30 08:03:08,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:08,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:08,962 - INFO - API请求耗时: 716ms
2025-05-30 08:03:08,962 - INFO - Response - Page 42
2025-05-30 08:03:08,963 - INFO - 第 42 页获取到 100 条记录
2025-05-30 08:03:09,464 - INFO - Request Parameters - Page 43:
2025-05-30 08:03:09,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:09,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800703, 1744387200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:10,075 - INFO - API请求耗时: 610ms
2025-05-30 08:03:10,076 - INFO - Response - Page 43
2025-05-30 08:03:10,076 - INFO - 第 43 页获取到 79 条记录
2025-05-30 08:03:10,077 - INFO - 查询完成，共获取到 4279 条记录
2025-05-30 08:03:10,077 - INFO - 分段 2 查询成功，获取到 4279 条记录
2025-05-30 08:03:11,078 - INFO - 查询分段 3: 2025-04-13 至 2025-04-19
2025-05-30 08:03:11,078 - INFO - 查询日期范围: 2025-04-13 至 2025-04-19，使用分页查询，每页 100 条记录
2025-05-30 08:03:11,079 - INFO - Request Parameters - Page 1:
2025-05-30 08:03:11,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:11,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:11,762 - INFO - API请求耗时: 684ms
2025-05-30 08:03:11,763 - INFO - Response - Page 1
2025-05-30 08:03:11,764 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:03:12,265 - INFO - Request Parameters - Page 2:
2025-05-30 08:03:12,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:12,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:12,978 - INFO - API请求耗时: 712ms
2025-05-30 08:03:12,979 - INFO - Response - Page 2
2025-05-30 08:03:12,979 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:03:13,479 - INFO - Request Parameters - Page 3:
2025-05-30 08:03:13,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:13,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:14,180 - INFO - API请求耗时: 700ms
2025-05-30 08:03:14,181 - INFO - Response - Page 3
2025-05-30 08:03:14,181 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:03:14,682 - INFO - Request Parameters - Page 4:
2025-05-30 08:03:14,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:14,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:15,725 - INFO - API请求耗时: 1042ms
2025-05-30 08:03:15,726 - INFO - Response - Page 4
2025-05-30 08:03:15,726 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:03:16,227 - INFO - Request Parameters - Page 5:
2025-05-30 08:03:16,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:16,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:17,006 - INFO - API请求耗时: 778ms
2025-05-30 08:03:17,006 - INFO - Response - Page 5
2025-05-30 08:03:17,007 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:03:17,508 - INFO - Request Parameters - Page 6:
2025-05-30 08:03:17,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:17,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:18,207 - INFO - API请求耗时: 697ms
2025-05-30 08:03:18,208 - INFO - Response - Page 6
2025-05-30 08:03:18,208 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:03:18,710 - INFO - Request Parameters - Page 7:
2025-05-30 08:03:18,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:18,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:19,531 - INFO - API请求耗时: 820ms
2025-05-30 08:03:19,532 - INFO - Response - Page 7
2025-05-30 08:03:19,532 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:03:20,033 - INFO - Request Parameters - Page 8:
2025-05-30 08:03:20,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:20,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:20,756 - INFO - API请求耗时: 723ms
2025-05-30 08:03:20,756 - INFO - Response - Page 8
2025-05-30 08:03:20,757 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:03:21,258 - INFO - Request Parameters - Page 9:
2025-05-30 08:03:21,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:21,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:22,199 - INFO - API请求耗时: 938ms
2025-05-30 08:03:22,199 - INFO - Response - Page 9
2025-05-30 08:03:22,200 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:03:22,701 - INFO - Request Parameters - Page 10:
2025-05-30 08:03:22,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:22,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:23,411 - INFO - API请求耗时: 709ms
2025-05-30 08:03:23,412 - INFO - Response - Page 10
2025-05-30 08:03:23,412 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:03:23,912 - INFO - Request Parameters - Page 11:
2025-05-30 08:03:23,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:23,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:24,633 - INFO - API请求耗时: 721ms
2025-05-30 08:03:24,633 - INFO - Response - Page 11
2025-05-30 08:03:24,634 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:03:25,134 - INFO - Request Parameters - Page 12:
2025-05-30 08:03:25,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:25,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:25,867 - INFO - API请求耗时: 731ms
2025-05-30 08:03:25,867 - INFO - Response - Page 12
2025-05-30 08:03:25,868 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:03:26,369 - INFO - Request Parameters - Page 13:
2025-05-30 08:03:26,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:26,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:27,096 - INFO - API请求耗时: 727ms
2025-05-30 08:03:27,097 - INFO - Response - Page 13
2025-05-30 08:03:27,097 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:03:27,599 - INFO - Request Parameters - Page 14:
2025-05-30 08:03:27,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:27,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:28,365 - INFO - API请求耗时: 765ms
2025-05-30 08:03:28,366 - INFO - Response - Page 14
2025-05-30 08:03:28,366 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:03:28,867 - INFO - Request Parameters - Page 15:
2025-05-30 08:03:28,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:28,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:29,622 - INFO - API请求耗时: 754ms
2025-05-30 08:03:29,623 - INFO - Response - Page 15
2025-05-30 08:03:29,623 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:03:30,123 - INFO - Request Parameters - Page 16:
2025-05-30 08:03:30,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:30,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:30,868 - INFO - API请求耗时: 744ms
2025-05-30 08:03:30,869 - INFO - Response - Page 16
2025-05-30 08:03:30,869 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:03:31,370 - INFO - Request Parameters - Page 17:
2025-05-30 08:03:31,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:31,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:32,110 - INFO - API请求耗时: 739ms
2025-05-30 08:03:32,110 - INFO - Response - Page 17
2025-05-30 08:03:32,111 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:03:32,612 - INFO - Request Parameters - Page 18:
2025-05-30 08:03:32,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:32,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:33,374 - INFO - API请求耗时: 761ms
2025-05-30 08:03:33,374 - INFO - Response - Page 18
2025-05-30 08:03:33,375 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:03:33,875 - INFO - Request Parameters - Page 19:
2025-05-30 08:03:33,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:33,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:34,619 - INFO - API请求耗时: 742ms
2025-05-30 08:03:34,620 - INFO - Response - Page 19
2025-05-30 08:03:34,620 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:03:35,122 - INFO - Request Parameters - Page 20:
2025-05-30 08:03:35,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:35,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:35,882 - INFO - API请求耗时: 760ms
2025-05-30 08:03:35,883 - INFO - Response - Page 20
2025-05-30 08:03:35,883 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:03:36,384 - INFO - Request Parameters - Page 21:
2025-05-30 08:03:36,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:36,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:37,223 - INFO - API请求耗时: 838ms
2025-05-30 08:03:37,223 - INFO - Response - Page 21
2025-05-30 08:03:37,224 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:03:37,725 - INFO - Request Parameters - Page 22:
2025-05-30 08:03:37,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:37,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:38,493 - INFO - API请求耗时: 767ms
2025-05-30 08:03:38,493 - INFO - Response - Page 22
2025-05-30 08:03:38,494 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:03:38,995 - INFO - Request Parameters - Page 23:
2025-05-30 08:03:38,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:38,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:39,749 - INFO - API请求耗时: 754ms
2025-05-30 08:03:39,750 - INFO - Response - Page 23
2025-05-30 08:03:39,750 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:03:40,251 - INFO - Request Parameters - Page 24:
2025-05-30 08:03:40,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:40,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:41,058 - INFO - API请求耗时: 806ms
2025-05-30 08:03:41,059 - INFO - Response - Page 24
2025-05-30 08:03:41,059 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:03:41,561 - INFO - Request Parameters - Page 25:
2025-05-30 08:03:41,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:41,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:42,326 - INFO - API请求耗时: 763ms
2025-05-30 08:03:42,326 - INFO - Response - Page 25
2025-05-30 08:03:42,327 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:03:42,828 - INFO - Request Parameters - Page 26:
2025-05-30 08:03:42,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:42,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:43,658 - INFO - API请求耗时: 829ms
2025-05-30 08:03:43,658 - INFO - Response - Page 26
2025-05-30 08:03:43,659 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:03:44,160 - INFO - Request Parameters - Page 27:
2025-05-30 08:03:44,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:44,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:44,982 - INFO - API请求耗时: 821ms
2025-05-30 08:03:44,982 - INFO - Response - Page 27
2025-05-30 08:03:44,983 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:03:45,484 - INFO - Request Parameters - Page 28:
2025-05-30 08:03:45,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:45,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:46,174 - INFO - API请求耗时: 689ms
2025-05-30 08:03:46,174 - INFO - Response - Page 28
2025-05-30 08:03:46,175 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:03:46,676 - INFO - Request Parameters - Page 29:
2025-05-30 08:03:46,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:46,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:47,422 - INFO - API请求耗时: 744ms
2025-05-30 08:03:47,422 - INFO - Response - Page 29
2025-05-30 08:03:47,423 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:03:47,924 - INFO - Request Parameters - Page 30:
2025-05-30 08:03:47,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:47,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:48,691 - INFO - API请求耗时: 765ms
2025-05-30 08:03:48,691 - INFO - Response - Page 30
2025-05-30 08:03:48,692 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:03:49,193 - INFO - Request Parameters - Page 31:
2025-05-30 08:03:49,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:49,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:49,955 - INFO - API请求耗时: 762ms
2025-05-30 08:03:49,955 - INFO - Response - Page 31
2025-05-30 08:03:49,956 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:03:50,457 - INFO - Request Parameters - Page 32:
2025-05-30 08:03:50,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:50,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:51,252 - INFO - API请求耗时: 794ms
2025-05-30 08:03:51,253 - INFO - Response - Page 32
2025-05-30 08:03:51,254 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:03:51,754 - INFO - Request Parameters - Page 33:
2025-05-30 08:03:51,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:51,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:52,531 - INFO - API请求耗时: 775ms
2025-05-30 08:03:52,532 - INFO - Response - Page 33
2025-05-30 08:03:52,532 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:03:53,034 - INFO - Request Parameters - Page 34:
2025-05-30 08:03:53,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:53,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:53,743 - INFO - API请求耗时: 708ms
2025-05-30 08:03:53,744 - INFO - Response - Page 34
2025-05-30 08:03:53,744 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:03:54,244 - INFO - Request Parameters - Page 35:
2025-05-30 08:03:54,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:54,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:55,011 - INFO - API请求耗时: 766ms
2025-05-30 08:03:55,012 - INFO - Response - Page 35
2025-05-30 08:03:55,012 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:03:55,514 - INFO - Request Parameters - Page 36:
2025-05-30 08:03:55,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:55,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:56,242 - INFO - API请求耗时: 727ms
2025-05-30 08:03:56,242 - INFO - Response - Page 36
2025-05-30 08:03:56,243 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:03:56,743 - INFO - Request Parameters - Page 37:
2025-05-30 08:03:56,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:56,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:57,450 - INFO - API请求耗时: 704ms
2025-05-30 08:03:57,450 - INFO - Response - Page 37
2025-05-30 08:03:57,451 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:03:57,952 - INFO - Request Parameters - Page 38:
2025-05-30 08:03:57,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:57,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:03:58,948 - INFO - API请求耗时: 995ms
2025-05-30 08:03:58,948 - INFO - Response - Page 38
2025-05-30 08:03:58,949 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:03:59,450 - INFO - Request Parameters - Page 39:
2025-05-30 08:03:59,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:03:59,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:00,185 - INFO - API请求耗时: 734ms
2025-05-30 08:04:00,185 - INFO - Response - Page 39
2025-05-30 08:04:00,186 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:04:00,686 - INFO - Request Parameters - Page 40:
2025-05-30 08:04:00,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:00,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:01,512 - INFO - API请求耗时: 825ms
2025-05-30 08:04:01,512 - INFO - Response - Page 40
2025-05-30 08:04:01,513 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:04:02,014 - INFO - Request Parameters - Page 41:
2025-05-30 08:04:02,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:02,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600703, 1744992000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:02,841 - INFO - API请求耗时: 826ms
2025-05-30 08:04:02,842 - INFO - Response - Page 41
2025-05-30 08:04:02,842 - INFO - 第 41 页获取到 81 条记录
2025-05-30 08:04:02,843 - INFO - 查询完成，共获取到 4081 条记录
2025-05-30 08:04:02,843 - INFO - 分段 3 查询成功，获取到 4081 条记录
2025-05-30 08:04:03,845 - INFO - 查询分段 4: 2025-04-20 至 2025-04-26
2025-05-30 08:04:03,845 - INFO - 查询日期范围: 2025-04-20 至 2025-04-26，使用分页查询，每页 100 条记录
2025-05-30 08:04:03,846 - INFO - Request Parameters - Page 1:
2025-05-30 08:04:03,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:03,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:04,670 - INFO - API请求耗时: 824ms
2025-05-30 08:04:04,671 - INFO - Response - Page 1
2025-05-30 08:04:04,672 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:04:05,172 - INFO - Request Parameters - Page 2:
2025-05-30 08:04:05,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:05,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:06,058 - INFO - API请求耗时: 885ms
2025-05-30 08:04:06,058 - INFO - Response - Page 2
2025-05-30 08:04:06,059 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:04:06,560 - INFO - Request Parameters - Page 3:
2025-05-30 08:04:06,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:06,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:07,295 - INFO - API请求耗时: 733ms
2025-05-30 08:04:07,295 - INFO - Response - Page 3
2025-05-30 08:04:07,295 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:04:07,796 - INFO - Request Parameters - Page 4:
2025-05-30 08:04:07,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:07,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:08,506 - INFO - API请求耗时: 709ms
2025-05-30 08:04:08,507 - INFO - Response - Page 4
2025-05-30 08:04:08,507 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:04:09,009 - INFO - Request Parameters - Page 5:
2025-05-30 08:04:09,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:09,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:09,794 - INFO - API请求耗时: 784ms
2025-05-30 08:04:09,794 - INFO - Response - Page 5
2025-05-30 08:04:09,795 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:04:10,296 - INFO - Request Parameters - Page 6:
2025-05-30 08:04:10,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:10,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:10,993 - INFO - API请求耗时: 696ms
2025-05-30 08:04:10,993 - INFO - Response - Page 6
2025-05-30 08:04:10,994 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:04:11,495 - INFO - Request Parameters - Page 7:
2025-05-30 08:04:11,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:11,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:12,217 - INFO - API请求耗时: 721ms
2025-05-30 08:04:12,217 - INFO - Response - Page 7
2025-05-30 08:04:12,218 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:04:12,718 - INFO - Request Parameters - Page 8:
2025-05-30 08:04:12,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:12,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:13,513 - INFO - API请求耗时: 794ms
2025-05-30 08:04:13,514 - INFO - Response - Page 8
2025-05-30 08:04:13,514 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:04:14,016 - INFO - Request Parameters - Page 9:
2025-05-30 08:04:14,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:14,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:14,804 - INFO - API请求耗时: 787ms
2025-05-30 08:04:14,805 - INFO - Response - Page 9
2025-05-30 08:04:14,805 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:04:15,306 - INFO - Request Parameters - Page 10:
2025-05-30 08:04:15,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:15,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:16,026 - INFO - API请求耗时: 719ms
2025-05-30 08:04:16,027 - INFO - Response - Page 10
2025-05-30 08:04:16,027 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:04:16,528 - INFO - Request Parameters - Page 11:
2025-05-30 08:04:16,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:16,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:17,279 - INFO - API请求耗时: 751ms
2025-05-30 08:04:17,279 - INFO - Response - Page 11
2025-05-30 08:04:17,280 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:04:17,781 - INFO - Request Parameters - Page 12:
2025-05-30 08:04:17,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:17,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:18,597 - INFO - API请求耗时: 814ms
2025-05-30 08:04:18,597 - INFO - Response - Page 12
2025-05-30 08:04:18,598 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:04:19,099 - INFO - Request Parameters - Page 13:
2025-05-30 08:04:19,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:19,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:19,847 - INFO - API请求耗时: 746ms
2025-05-30 08:04:19,847 - INFO - Response - Page 13
2025-05-30 08:04:19,848 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:04:20,349 - INFO - Request Parameters - Page 14:
2025-05-30 08:04:20,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:20,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:21,008 - INFO - API请求耗时: 658ms
2025-05-30 08:04:21,009 - INFO - Response - Page 14
2025-05-30 08:04:21,009 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:04:21,510 - INFO - Request Parameters - Page 15:
2025-05-30 08:04:21,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:21,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:22,230 - INFO - API请求耗时: 718ms
2025-05-30 08:04:22,230 - INFO - Response - Page 15
2025-05-30 08:04:22,231 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:04:22,731 - INFO - Request Parameters - Page 16:
2025-05-30 08:04:22,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:22,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:23,446 - INFO - API请求耗时: 715ms
2025-05-30 08:04:23,447 - INFO - Response - Page 16
2025-05-30 08:04:23,448 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:04:23,949 - INFO - Request Parameters - Page 17:
2025-05-30 08:04:23,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:23,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:24,708 - INFO - API请求耗时: 757ms
2025-05-30 08:04:24,708 - INFO - Response - Page 17
2025-05-30 08:04:24,709 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:04:25,209 - INFO - Request Parameters - Page 18:
2025-05-30 08:04:25,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:25,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:25,965 - INFO - API请求耗时: 754ms
2025-05-30 08:04:25,965 - INFO - Response - Page 18
2025-05-30 08:04:25,966 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:04:26,466 - INFO - Request Parameters - Page 19:
2025-05-30 08:04:26,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:26,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:27,193 - INFO - API请求耗时: 726ms
2025-05-30 08:04:27,193 - INFO - Response - Page 19
2025-05-30 08:04:27,193 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:04:27,693 - INFO - Request Parameters - Page 20:
2025-05-30 08:04:27,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:27,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:28,450 - INFO - API请求耗时: 756ms
2025-05-30 08:04:28,451 - INFO - Response - Page 20
2025-05-30 08:04:28,452 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:04:28,953 - INFO - Request Parameters - Page 21:
2025-05-30 08:04:28,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:28,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:29,706 - INFO - API请求耗时: 752ms
2025-05-30 08:04:29,706 - INFO - Response - Page 21
2025-05-30 08:04:29,707 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:04:30,208 - INFO - Request Parameters - Page 22:
2025-05-30 08:04:30,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:30,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:30,936 - INFO - API请求耗时: 727ms
2025-05-30 08:04:30,936 - INFO - Response - Page 22
2025-05-30 08:04:30,937 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:04:31,438 - INFO - Request Parameters - Page 23:
2025-05-30 08:04:31,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:31,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:32,133 - INFO - API请求耗时: 694ms
2025-05-30 08:04:32,133 - INFO - Response - Page 23
2025-05-30 08:04:32,134 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:04:32,635 - INFO - Request Parameters - Page 24:
2025-05-30 08:04:32,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:32,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:33,368 - INFO - API请求耗时: 732ms
2025-05-30 08:04:33,369 - INFO - Response - Page 24
2025-05-30 08:04:33,369 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:04:33,869 - INFO - Request Parameters - Page 25:
2025-05-30 08:04:33,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:33,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:34,849 - INFO - API请求耗时: 978ms
2025-05-30 08:04:34,849 - INFO - Response - Page 25
2025-05-30 08:04:34,850 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:04:35,351 - INFO - Request Parameters - Page 26:
2025-05-30 08:04:35,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:35,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:36,155 - INFO - API请求耗时: 804ms
2025-05-30 08:04:36,156 - INFO - Response - Page 26
2025-05-30 08:04:36,156 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:04:36,657 - INFO - Request Parameters - Page 27:
2025-05-30 08:04:36,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:36,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:37,437 - INFO - API请求耗时: 778ms
2025-05-30 08:04:37,437 - INFO - Response - Page 27
2025-05-30 08:04:37,438 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:04:37,939 - INFO - Request Parameters - Page 28:
2025-05-30 08:04:37,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:37,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:38,660 - INFO - API请求耗时: 721ms
2025-05-30 08:04:38,660 - INFO - Response - Page 28
2025-05-30 08:04:38,661 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:04:39,161 - INFO - Request Parameters - Page 29:
2025-05-30 08:04:39,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:39,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:39,905 - INFO - API请求耗时: 743ms
2025-05-30 08:04:39,906 - INFO - Response - Page 29
2025-05-30 08:04:39,906 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:04:40,408 - INFO - Request Parameters - Page 30:
2025-05-30 08:04:40,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:40,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:41,099 - INFO - API请求耗时: 689ms
2025-05-30 08:04:41,100 - INFO - Response - Page 30
2025-05-30 08:04:41,100 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:04:41,602 - INFO - Request Parameters - Page 31:
2025-05-30 08:04:41,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:41,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:42,456 - INFO - API请求耗时: 854ms
2025-05-30 08:04:42,457 - INFO - Response - Page 31
2025-05-30 08:04:42,457 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:04:42,959 - INFO - Request Parameters - Page 32:
2025-05-30 08:04:42,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:42,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:43,642 - INFO - API请求耗时: 682ms
2025-05-30 08:04:43,642 - INFO - Response - Page 32
2025-05-30 08:04:43,643 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:04:44,144 - INFO - Request Parameters - Page 33:
2025-05-30 08:04:44,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:44,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:44,888 - INFO - API请求耗时: 742ms
2025-05-30 08:04:44,888 - INFO - Response - Page 33
2025-05-30 08:04:44,889 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:04:45,389 - INFO - Request Parameters - Page 34:
2025-05-30 08:04:45,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:45,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:46,098 - INFO - API请求耗时: 708ms
2025-05-30 08:04:46,099 - INFO - Response - Page 34
2025-05-30 08:04:46,099 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:04:46,600 - INFO - Request Parameters - Page 35:
2025-05-30 08:04:46,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:46,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:47,321 - INFO - API请求耗时: 721ms
2025-05-30 08:04:47,321 - INFO - Response - Page 35
2025-05-30 08:04:47,322 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:04:47,823 - INFO - Request Parameters - Page 36:
2025-05-30 08:04:47,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:47,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:48,550 - INFO - API请求耗时: 726ms
2025-05-30 08:04:48,550 - INFO - Response - Page 36
2025-05-30 08:04:48,551 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:04:49,052 - INFO - Request Parameters - Page 37:
2025-05-30 08:04:49,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:49,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:49,818 - INFO - API请求耗时: 766ms
2025-05-30 08:04:49,819 - INFO - Response - Page 37
2025-05-30 08:04:49,819 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:04:50,321 - INFO - Request Parameters - Page 38:
2025-05-30 08:04:50,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:50,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:51,069 - INFO - API请求耗时: 747ms
2025-05-30 08:04:51,069 - INFO - Response - Page 38
2025-05-30 08:04:51,070 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:04:51,571 - INFO - Request Parameters - Page 39:
2025-05-30 08:04:51,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:51,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:52,549 - INFO - API请求耗时: 977ms
2025-05-30 08:04:52,549 - INFO - Response - Page 39
2025-05-30 08:04:52,550 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:04:53,051 - INFO - Request Parameters - Page 40:
2025-05-30 08:04:53,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:53,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:53,809 - INFO - API请求耗时: 757ms
2025-05-30 08:04:53,809 - INFO - Response - Page 40
2025-05-30 08:04:53,810 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:04:54,310 - INFO - Request Parameters - Page 41:
2025-05-30 08:04:54,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:54,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:55,212 - INFO - API请求耗时: 899ms
2025-05-30 08:04:55,212 - INFO - Response - Page 41
2025-05-30 08:04:55,213 - INFO - 第 41 页获取到 100 条记录
2025-05-30 08:04:55,714 - INFO - Request Parameters - Page 42:
2025-05-30 08:04:55,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:55,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:56,395 - INFO - API请求耗时: 681ms
2025-05-30 08:04:56,396 - INFO - Response - Page 42
2025-05-30 08:04:56,396 - INFO - 第 42 页获取到 100 条记录
2025-05-30 08:04:56,898 - INFO - Request Parameters - Page 43:
2025-05-30 08:04:56,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:56,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400703, 1745596800703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:57,471 - INFO - API请求耗时: 571ms
2025-05-30 08:04:57,472 - INFO - Response - Page 43
2025-05-30 08:04:57,472 - INFO - 第 43 页获取到 24 条记录
2025-05-30 08:04:57,472 - INFO - 查询完成，共获取到 4224 条记录
2025-05-30 08:04:57,473 - INFO - 分段 4 查询成功，获取到 4224 条记录
2025-05-30 08:04:58,474 - INFO - 查询分段 5: 2025-04-27 至 2025-05-03
2025-05-30 08:04:58,474 - INFO - 查询日期范围: 2025-04-27 至 2025-05-03，使用分页查询，每页 100 条记录
2025-05-30 08:04:58,475 - INFO - Request Parameters - Page 1:
2025-05-30 08:04:58,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:58,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:04:59,135 - INFO - API请求耗时: 660ms
2025-05-30 08:04:59,135 - INFO - Response - Page 1
2025-05-30 08:04:59,136 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:04:59,638 - INFO - Request Parameters - Page 2:
2025-05-30 08:04:59,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:04:59,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:00,404 - INFO - API请求耗时: 766ms
2025-05-30 08:05:00,404 - INFO - Response - Page 2
2025-05-30 08:05:00,405 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:05:00,906 - INFO - Request Parameters - Page 3:
2025-05-30 08:05:00,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:00,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:01,694 - INFO - API请求耗时: 787ms
2025-05-30 08:05:01,696 - INFO - Response - Page 3
2025-05-30 08:05:01,698 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:05:02,199 - INFO - Request Parameters - Page 4:
2025-05-30 08:05:02,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:02,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:02,926 - INFO - API请求耗时: 726ms
2025-05-30 08:05:02,926 - INFO - Response - Page 4
2025-05-30 08:05:02,927 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:05:03,427 - INFO - Request Parameters - Page 5:
2025-05-30 08:05:03,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:03,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:04,139 - INFO - API请求耗时: 711ms
2025-05-30 08:05:04,139 - INFO - Response - Page 5
2025-05-30 08:05:04,140 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:05:04,640 - INFO - Request Parameters - Page 6:
2025-05-30 08:05:04,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:04,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:05,508 - INFO - API请求耗时: 866ms
2025-05-30 08:05:05,508 - INFO - Response - Page 6
2025-05-30 08:05:05,509 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:05:06,010 - INFO - Request Parameters - Page 7:
2025-05-30 08:05:06,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:06,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:06,697 - INFO - API请求耗时: 687ms
2025-05-30 08:05:06,697 - INFO - Response - Page 7
2025-05-30 08:05:06,698 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:05:07,199 - INFO - Request Parameters - Page 8:
2025-05-30 08:05:07,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:07,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:08,020 - INFO - API请求耗时: 820ms
2025-05-30 08:05:08,021 - INFO - Response - Page 8
2025-05-30 08:05:08,021 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:05:08,523 - INFO - Request Parameters - Page 9:
2025-05-30 08:05:08,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:08,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:09,257 - INFO - API请求耗时: 732ms
2025-05-30 08:05:09,257 - INFO - Response - Page 9
2025-05-30 08:05:09,258 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:05:09,758 - INFO - Request Parameters - Page 10:
2025-05-30 08:05:09,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:09,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:10,477 - INFO - API请求耗时: 719ms
2025-05-30 08:05:10,478 - INFO - Response - Page 10
2025-05-30 08:05:10,478 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:05:10,979 - INFO - Request Parameters - Page 11:
2025-05-30 08:05:10,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:10,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:11,641 - INFO - API请求耗时: 661ms
2025-05-30 08:05:11,641 - INFO - Response - Page 11
2025-05-30 08:05:11,642 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:05:12,143 - INFO - Request Parameters - Page 12:
2025-05-30 08:05:12,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:12,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:12,835 - INFO - API请求耗时: 690ms
2025-05-30 08:05:12,836 - INFO - Response - Page 12
2025-05-30 08:05:12,836 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:05:13,337 - INFO - Request Parameters - Page 13:
2025-05-30 08:05:13,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:13,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:14,036 - INFO - API请求耗时: 698ms
2025-05-30 08:05:14,036 - INFO - Response - Page 13
2025-05-30 08:05:14,037 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:05:14,538 - INFO - Request Parameters - Page 14:
2025-05-30 08:05:14,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:14,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:15,464 - INFO - API请求耗时: 925ms
2025-05-30 08:05:15,465 - INFO - Response - Page 14
2025-05-30 08:05:15,465 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:05:15,967 - INFO - Request Parameters - Page 15:
2025-05-30 08:05:15,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:15,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:16,759 - INFO - API请求耗时: 791ms
2025-05-30 08:05:16,760 - INFO - Response - Page 15
2025-05-30 08:05:16,760 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:05:17,262 - INFO - Request Parameters - Page 16:
2025-05-30 08:05:17,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:17,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:17,929 - INFO - API请求耗时: 667ms
2025-05-30 08:05:17,930 - INFO - Response - Page 16
2025-05-30 08:05:17,930 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:05:18,431 - INFO - Request Parameters - Page 17:
2025-05-30 08:05:18,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:18,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:19,135 - INFO - API请求耗时: 702ms
2025-05-30 08:05:19,135 - INFO - Response - Page 17
2025-05-30 08:05:19,136 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:05:19,636 - INFO - Request Parameters - Page 18:
2025-05-30 08:05:19,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:19,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:20,486 - INFO - API请求耗时: 849ms
2025-05-30 08:05:20,487 - INFO - Response - Page 18
2025-05-30 08:05:20,487 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:05:20,989 - INFO - Request Parameters - Page 19:
2025-05-30 08:05:20,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:20,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:21,695 - INFO - API请求耗时: 705ms
2025-05-30 08:05:21,696 - INFO - Response - Page 19
2025-05-30 08:05:21,696 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:05:22,198 - INFO - Request Parameters - Page 20:
2025-05-30 08:05:22,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:22,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:22,876 - INFO - API请求耗时: 677ms
2025-05-30 08:05:22,876 - INFO - Response - Page 20
2025-05-30 08:05:22,876 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:05:23,377 - INFO - Request Parameters - Page 21:
2025-05-30 08:05:23,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:23,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:24,094 - INFO - API请求耗时: 717ms
2025-05-30 08:05:24,094 - INFO - Response - Page 21
2025-05-30 08:05:24,095 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:05:24,596 - INFO - Request Parameters - Page 22:
2025-05-30 08:05:24,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:24,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:25,358 - INFO - API请求耗时: 761ms
2025-05-30 08:05:25,359 - INFO - Response - Page 22
2025-05-30 08:05:25,359 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:05:25,860 - INFO - Request Parameters - Page 23:
2025-05-30 08:05:25,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:25,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:26,751 - INFO - API请求耗时: 890ms
2025-05-30 08:05:26,752 - INFO - Response - Page 23
2025-05-30 08:05:26,752 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:05:27,254 - INFO - Request Parameters - Page 24:
2025-05-30 08:05:27,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:27,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:27,965 - INFO - API请求耗时: 710ms
2025-05-30 08:05:27,965 - INFO - Response - Page 24
2025-05-30 08:05:27,966 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:05:28,467 - INFO - Request Parameters - Page 25:
2025-05-30 08:05:28,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:28,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:29,238 - INFO - API请求耗时: 770ms
2025-05-30 08:05:29,239 - INFO - Response - Page 25
2025-05-30 08:05:29,239 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:05:29,741 - INFO - Request Parameters - Page 26:
2025-05-30 08:05:29,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:29,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:30,515 - INFO - API请求耗时: 773ms
2025-05-30 08:05:30,515 - INFO - Response - Page 26
2025-05-30 08:05:30,516 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:05:31,017 - INFO - Request Parameters - Page 27:
2025-05-30 08:05:31,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:31,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:31,900 - INFO - API请求耗时: 882ms
2025-05-30 08:05:31,900 - INFO - Response - Page 27
2025-05-30 08:05:31,901 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:05:32,402 - INFO - Request Parameters - Page 28:
2025-05-30 08:05:32,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:32,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:33,199 - INFO - API请求耗时: 796ms
2025-05-30 08:05:33,200 - INFO - Response - Page 28
2025-05-30 08:05:33,200 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:05:33,701 - INFO - Request Parameters - Page 29:
2025-05-30 08:05:33,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:33,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:34,445 - INFO - API请求耗时: 743ms
2025-05-30 08:05:34,446 - INFO - Response - Page 29
2025-05-30 08:05:34,447 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:05:34,948 - INFO - Request Parameters - Page 30:
2025-05-30 08:05:34,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:34,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:35,711 - INFO - API请求耗时: 762ms
2025-05-30 08:05:35,711 - INFO - Response - Page 30
2025-05-30 08:05:35,712 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:05:36,213 - INFO - Request Parameters - Page 31:
2025-05-30 08:05:36,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:36,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:36,987 - INFO - API请求耗时: 773ms
2025-05-30 08:05:36,987 - INFO - Response - Page 31
2025-05-30 08:05:36,988 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:05:37,489 - INFO - Request Parameters - Page 32:
2025-05-30 08:05:37,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:37,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:38,229 - INFO - API请求耗时: 739ms
2025-05-30 08:05:38,229 - INFO - Response - Page 32
2025-05-30 08:05:38,230 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:05:38,731 - INFO - Request Parameters - Page 33:
2025-05-30 08:05:38,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:38,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:39,408 - INFO - API请求耗时: 676ms
2025-05-30 08:05:39,408 - INFO - Response - Page 33
2025-05-30 08:05:39,409 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:05:39,910 - INFO - Request Parameters - Page 34:
2025-05-30 08:05:39,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:39,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:40,666 - INFO - API请求耗时: 755ms
2025-05-30 08:05:40,666 - INFO - Response - Page 34
2025-05-30 08:05:40,667 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:05:41,167 - INFO - Request Parameters - Page 35:
2025-05-30 08:05:41,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:41,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:41,905 - INFO - API请求耗时: 737ms
2025-05-30 08:05:41,905 - INFO - Response - Page 35
2025-05-30 08:05:41,905 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:05:42,406 - INFO - Request Parameters - Page 36:
2025-05-30 08:05:42,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:42,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:43,151 - INFO - API请求耗时: 744ms
2025-05-30 08:05:43,151 - INFO - Response - Page 36
2025-05-30 08:05:43,152 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:05:43,653 - INFO - Request Parameters - Page 37:
2025-05-30 08:05:43,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:43,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:44,412 - INFO - API请求耗时: 758ms
2025-05-30 08:05:44,413 - INFO - Response - Page 37
2025-05-30 08:05:44,413 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:05:44,913 - INFO - Request Parameters - Page 38:
2025-05-30 08:05:44,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:44,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:45,681 - INFO - API请求耗时: 768ms
2025-05-30 08:05:45,681 - INFO - Response - Page 38
2025-05-30 08:05:45,682 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:05:46,183 - INFO - Request Parameters - Page 39:
2025-05-30 08:05:46,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:46,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:46,896 - INFO - API请求耗时: 712ms
2025-05-30 08:05:46,896 - INFO - Response - Page 39
2025-05-30 08:05:46,897 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:05:47,398 - INFO - Request Parameters - Page 40:
2025-05-30 08:05:47,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:47,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:48,128 - INFO - API请求耗时: 729ms
2025-05-30 08:05:48,128 - INFO - Response - Page 40
2025-05-30 08:05:48,128 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:05:48,630 - INFO - Request Parameters - Page 41:
2025-05-30 08:05:48,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:48,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:49,375 - INFO - API请求耗时: 743ms
2025-05-30 08:05:49,376 - INFO - Response - Page 41
2025-05-30 08:05:49,376 - INFO - 第 41 页获取到 100 条记录
2025-05-30 08:05:49,877 - INFO - Request Parameters - Page 42:
2025-05-30 08:05:49,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:49,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:50,613 - INFO - API请求耗时: 735ms
2025-05-30 08:05:50,613 - INFO - Response - Page 42
2025-05-30 08:05:50,614 - INFO - 第 42 页获取到 100 条记录
2025-05-30 08:05:51,115 - INFO - Request Parameters - Page 43:
2025-05-30 08:05:51,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:51,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:51,804 - INFO - API请求耗时: 688ms
2025-05-30 08:05:51,805 - INFO - Response - Page 43
2025-05-30 08:05:51,805 - INFO - 第 43 页获取到 100 条记录
2025-05-30 08:05:52,307 - INFO - Request Parameters - Page 44:
2025-05-30 08:05:52,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:52,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:53,005 - INFO - API请求耗时: 696ms
2025-05-30 08:05:53,005 - INFO - Response - Page 44
2025-05-30 08:05:53,005 - INFO - 第 44 页获取到 100 条记录
2025-05-30 08:05:53,506 - INFO - Request Parameters - Page 45:
2025-05-30 08:05:53,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:53,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:54,195 - INFO - API请求耗时: 687ms
2025-05-30 08:05:54,195 - INFO - Response - Page 45
2025-05-30 08:05:54,196 - INFO - 第 45 页获取到 100 条记录
2025-05-30 08:05:54,696 - INFO - Request Parameters - Page 46:
2025-05-30 08:05:54,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:54,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:55,385 - INFO - API请求耗时: 688ms
2025-05-30 08:05:55,385 - INFO - Response - Page 46
2025-05-30 08:05:55,386 - INFO - 第 46 页获取到 100 条记录
2025-05-30 08:05:55,886 - INFO - Request Parameters - Page 47:
2025-05-30 08:05:55,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:55,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:56,615 - INFO - API请求耗时: 728ms
2025-05-30 08:05:56,616 - INFO - Response - Page 47
2025-05-30 08:05:56,616 - INFO - 第 47 页获取到 100 条记录
2025-05-30 08:05:57,117 - INFO - Request Parameters - Page 48:
2025-05-30 08:05:57,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:57,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:57,990 - INFO - API请求耗时: 872ms
2025-05-30 08:05:57,991 - INFO - Response - Page 48
2025-05-30 08:05:57,991 - INFO - 第 48 页获取到 100 条记录
2025-05-30 08:05:58,491 - INFO - Request Parameters - Page 49:
2025-05-30 08:05:58,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:58,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200703, 1746201600703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:05:58,946 - INFO - API请求耗时: 454ms
2025-05-30 08:05:58,946 - INFO - Response - Page 49
2025-05-30 08:05:58,947 - INFO - 第 49 页获取到 5 条记录
2025-05-30 08:05:58,947 - INFO - 查询完成，共获取到 4805 条记录
2025-05-30 08:05:58,948 - INFO - 分段 5 查询成功，获取到 4805 条记录
2025-05-30 08:05:59,949 - INFO - 查询分段 6: 2025-05-04 至 2025-05-10
2025-05-30 08:05:59,949 - INFO - 查询日期范围: 2025-05-04 至 2025-05-10，使用分页查询，每页 100 条记录
2025-05-30 08:05:59,950 - INFO - Request Parameters - Page 1:
2025-05-30 08:05:59,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:05:59,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:00,668 - INFO - API请求耗时: 718ms
2025-05-30 08:06:00,669 - INFO - Response - Page 1
2025-05-30 08:06:00,669 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:06:01,171 - INFO - Request Parameters - Page 2:
2025-05-30 08:06:01,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:01,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:02,149 - INFO - API请求耗时: 977ms
2025-05-30 08:06:02,150 - INFO - Response - Page 2
2025-05-30 08:06:02,150 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:06:02,652 - INFO - Request Parameters - Page 3:
2025-05-30 08:06:02,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:02,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:03,385 - INFO - API请求耗时: 731ms
2025-05-30 08:06:03,385 - INFO - Response - Page 3
2025-05-30 08:06:03,386 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:06:03,887 - INFO - Request Parameters - Page 4:
2025-05-30 08:06:03,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:03,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:04,589 - INFO - API请求耗时: 701ms
2025-05-30 08:06:04,590 - INFO - Response - Page 4
2025-05-30 08:06:04,590 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:06:05,092 - INFO - Request Parameters - Page 5:
2025-05-30 08:06:05,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:05,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:06,022 - INFO - API请求耗时: 929ms
2025-05-30 08:06:06,023 - INFO - Response - Page 5
2025-05-30 08:06:06,023 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:06:06,525 - INFO - Request Parameters - Page 6:
2025-05-30 08:06:06,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:06,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:07,283 - INFO - API请求耗时: 757ms
2025-05-30 08:06:07,284 - INFO - Response - Page 6
2025-05-30 08:06:07,284 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:06:07,785 - INFO - Request Parameters - Page 7:
2025-05-30 08:06:07,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:07,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:08,554 - INFO - API请求耗时: 769ms
2025-05-30 08:06:08,555 - INFO - Response - Page 7
2025-05-30 08:06:08,555 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:06:09,055 - INFO - Request Parameters - Page 8:
2025-05-30 08:06:09,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:09,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:09,823 - INFO - API请求耗时: 767ms
2025-05-30 08:06:09,824 - INFO - Response - Page 8
2025-05-30 08:06:09,824 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:06:10,326 - INFO - Request Parameters - Page 9:
2025-05-30 08:06:10,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:10,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:11,037 - INFO - API请求耗时: 709ms
2025-05-30 08:06:11,037 - INFO - Response - Page 9
2025-05-30 08:06:11,038 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:06:11,538 - INFO - Request Parameters - Page 10:
2025-05-30 08:06:11,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:11,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:12,282 - INFO - API请求耗时: 742ms
2025-05-30 08:06:12,282 - INFO - Response - Page 10
2025-05-30 08:06:12,283 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:06:12,784 - INFO - Request Parameters - Page 11:
2025-05-30 08:06:12,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:12,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:13,507 - INFO - API请求耗时: 723ms
2025-05-30 08:06:13,508 - INFO - Response - Page 11
2025-05-30 08:06:13,508 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:06:14,010 - INFO - Request Parameters - Page 12:
2025-05-30 08:06:14,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:14,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:14,700 - INFO - API请求耗时: 688ms
2025-05-30 08:06:14,701 - INFO - Response - Page 12
2025-05-30 08:06:14,702 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:06:15,203 - INFO - Request Parameters - Page 13:
2025-05-30 08:06:15,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:15,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:15,883 - INFO - API请求耗时: 680ms
2025-05-30 08:06:15,884 - INFO - Response - Page 13
2025-05-30 08:06:15,884 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:06:16,385 - INFO - Request Parameters - Page 14:
2025-05-30 08:06:16,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:16,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:17,170 - INFO - API请求耗时: 784ms
2025-05-30 08:06:17,170 - INFO - Response - Page 14
2025-05-30 08:06:17,171 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:06:17,672 - INFO - Request Parameters - Page 15:
2025-05-30 08:06:17,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:17,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:18,494 - INFO - API请求耗时: 820ms
2025-05-30 08:06:18,494 - INFO - Response - Page 15
2025-05-30 08:06:18,495 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:06:18,995 - INFO - Request Parameters - Page 16:
2025-05-30 08:06:18,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:18,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:19,915 - INFO - API请求耗时: 919ms
2025-05-30 08:06:19,915 - INFO - Response - Page 16
2025-05-30 08:06:19,916 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:06:20,417 - INFO - Request Parameters - Page 17:
2025-05-30 08:06:20,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:20,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:21,168 - INFO - API请求耗时: 750ms
2025-05-30 08:06:21,168 - INFO - Response - Page 17
2025-05-30 08:06:21,169 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:06:21,670 - INFO - Request Parameters - Page 18:
2025-05-30 08:06:21,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:21,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:22,367 - INFO - API请求耗时: 695ms
2025-05-30 08:06:22,368 - INFO - Response - Page 18
2025-05-30 08:06:22,368 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:06:22,869 - INFO - Request Parameters - Page 19:
2025-05-30 08:06:22,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:22,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:23,547 - INFO - API请求耗时: 677ms
2025-05-30 08:06:23,547 - INFO - Response - Page 19
2025-05-30 08:06:23,548 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:06:24,048 - INFO - Request Parameters - Page 20:
2025-05-30 08:06:24,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:24,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:24,763 - INFO - API请求耗时: 714ms
2025-05-30 08:06:24,764 - INFO - Response - Page 20
2025-05-30 08:06:24,764 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:06:25,264 - INFO - Request Parameters - Page 21:
2025-05-30 08:06:25,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:25,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:25,959 - INFO - API请求耗时: 694ms
2025-05-30 08:06:25,959 - INFO - Response - Page 21
2025-05-30 08:06:25,959 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:06:26,461 - INFO - Request Parameters - Page 22:
2025-05-30 08:06:26,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:26,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:27,175 - INFO - API请求耗时: 713ms
2025-05-30 08:06:27,175 - INFO - Response - Page 22
2025-05-30 08:06:27,176 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:06:27,677 - INFO - Request Parameters - Page 23:
2025-05-30 08:06:27,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:27,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:28,410 - INFO - API请求耗时: 732ms
2025-05-30 08:06:28,410 - INFO - Response - Page 23
2025-05-30 08:06:28,411 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:06:28,912 - INFO - Request Parameters - Page 24:
2025-05-30 08:06:28,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:28,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:29,597 - INFO - API请求耗时: 684ms
2025-05-30 08:06:29,598 - INFO - Response - Page 24
2025-05-30 08:06:29,598 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:06:30,099 - INFO - Request Parameters - Page 25:
2025-05-30 08:06:30,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:30,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:30,843 - INFO - API请求耗时: 743ms
2025-05-30 08:06:30,844 - INFO - Response - Page 25
2025-05-30 08:06:30,844 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:06:31,346 - INFO - Request Parameters - Page 26:
2025-05-30 08:06:31,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:31,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:32,102 - INFO - API请求耗时: 755ms
2025-05-30 08:06:32,102 - INFO - Response - Page 26
2025-05-30 08:06:32,103 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:06:32,604 - INFO - Request Parameters - Page 27:
2025-05-30 08:06:32,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:32,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:33,306 - INFO - API请求耗时: 701ms
2025-05-30 08:06:33,306 - INFO - Response - Page 27
2025-05-30 08:06:33,307 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:06:33,807 - INFO - Request Parameters - Page 28:
2025-05-30 08:06:33,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:33,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:34,522 - INFO - API请求耗时: 714ms
2025-05-30 08:06:34,523 - INFO - Response - Page 28
2025-05-30 08:06:34,523 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:06:35,024 - INFO - Request Parameters - Page 29:
2025-05-30 08:06:35,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:35,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:35,887 - INFO - API请求耗时: 862ms
2025-05-30 08:06:35,887 - INFO - Response - Page 29
2025-05-30 08:06:35,888 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:06:36,389 - INFO - Request Parameters - Page 30:
2025-05-30 08:06:36,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:36,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:37,111 - INFO - API请求耗时: 721ms
2025-05-30 08:06:37,112 - INFO - Response - Page 30
2025-05-30 08:06:37,112 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:06:37,614 - INFO - Request Parameters - Page 31:
2025-05-30 08:06:37,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:37,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:38,307 - INFO - API请求耗时: 692ms
2025-05-30 08:06:38,307 - INFO - Response - Page 31
2025-05-30 08:06:38,308 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:06:38,809 - INFO - Request Parameters - Page 32:
2025-05-30 08:06:38,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:38,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:39,778 - INFO - API请求耗时: 968ms
2025-05-30 08:06:39,779 - INFO - Response - Page 32
2025-05-30 08:06:39,779 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:06:40,279 - INFO - Request Parameters - Page 33:
2025-05-30 08:06:40,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:40,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:40,945 - INFO - API请求耗时: 665ms
2025-05-30 08:06:40,946 - INFO - Response - Page 33
2025-05-30 08:06:40,946 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:06:41,446 - INFO - Request Parameters - Page 34:
2025-05-30 08:06:41,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:41,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:42,188 - INFO - API请求耗时: 741ms
2025-05-30 08:06:42,189 - INFO - Response - Page 34
2025-05-30 08:06:42,189 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:06:42,690 - INFO - Request Parameters - Page 35:
2025-05-30 08:06:42,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:42,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:43,365 - INFO - API请求耗时: 675ms
2025-05-30 08:06:43,366 - INFO - Response - Page 35
2025-05-30 08:06:43,366 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:06:43,866 - INFO - Request Parameters - Page 36:
2025-05-30 08:06:43,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:43,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:44,589 - INFO - API请求耗时: 722ms
2025-05-30 08:06:44,590 - INFO - Response - Page 36
2025-05-30 08:06:44,590 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:06:45,090 - INFO - Request Parameters - Page 37:
2025-05-30 08:06:45,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:45,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:45,777 - INFO - API请求耗时: 686ms
2025-05-30 08:06:45,778 - INFO - Response - Page 37
2025-05-30 08:06:45,778 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:06:46,278 - INFO - Request Parameters - Page 38:
2025-05-30 08:06:46,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:46,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:46,961 - INFO - API请求耗时: 682ms
2025-05-30 08:06:46,962 - INFO - Response - Page 38
2025-05-30 08:06:46,962 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:06:47,463 - INFO - Request Parameters - Page 39:
2025-05-30 08:06:47,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:47,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:48,205 - INFO - API请求耗时: 740ms
2025-05-30 08:06:48,205 - INFO - Response - Page 39
2025-05-30 08:06:48,206 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:06:48,707 - INFO - Request Parameters - Page 40:
2025-05-30 08:06:48,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:48,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:49,389 - INFO - API请求耗时: 681ms
2025-05-30 08:06:49,390 - INFO - Response - Page 40
2025-05-30 08:06:49,390 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:06:49,892 - INFO - Request Parameters - Page 41:
2025-05-30 08:06:49,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:49,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:50,615 - INFO - API请求耗时: 723ms
2025-05-30 08:06:50,615 - INFO - Response - Page 41
2025-05-30 08:06:50,616 - INFO - 第 41 页获取到 100 条记录
2025-05-30 08:06:51,117 - INFO - Request Parameters - Page 42:
2025-05-30 08:06:51,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:51,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000703, 1746806400703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:51,795 - INFO - API请求耗时: 677ms
2025-05-30 08:06:51,795 - INFO - Response - Page 42
2025-05-30 08:06:51,796 - INFO - 第 42 页获取到 85 条记录
2025-05-30 08:06:51,796 - INFO - 查询完成，共获取到 4185 条记录
2025-05-30 08:06:51,797 - INFO - 分段 6 查询成功，获取到 4185 条记录
2025-05-30 08:06:52,798 - INFO - 查询分段 7: 2025-05-11 至 2025-05-17
2025-05-30 08:06:52,798 - INFO - 查询日期范围: 2025-05-11 至 2025-05-17，使用分页查询，每页 100 条记录
2025-05-30 08:06:52,799 - INFO - Request Parameters - Page 1:
2025-05-30 08:06:52,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:52,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:53,658 - INFO - API请求耗时: 859ms
2025-05-30 08:06:53,659 - INFO - Response - Page 1
2025-05-30 08:06:53,659 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:06:54,160 - INFO - Request Parameters - Page 2:
2025-05-30 08:06:54,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:54,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:54,830 - INFO - API请求耗时: 669ms
2025-05-30 08:06:54,830 - INFO - Response - Page 2
2025-05-30 08:06:54,831 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:06:55,331 - INFO - Request Parameters - Page 3:
2025-05-30 08:06:55,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:55,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:56,100 - INFO - API请求耗时: 768ms
2025-05-30 08:06:56,100 - INFO - Response - Page 3
2025-05-30 08:06:56,101 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:06:56,602 - INFO - Request Parameters - Page 4:
2025-05-30 08:06:56,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:56,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:57,329 - INFO - API请求耗时: 727ms
2025-05-30 08:06:57,329 - INFO - Response - Page 4
2025-05-30 08:06:57,330 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:06:57,830 - INFO - Request Parameters - Page 5:
2025-05-30 08:06:57,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:57,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:58,611 - INFO - API请求耗时: 780ms
2025-05-30 08:06:58,612 - INFO - Response - Page 5
2025-05-30 08:06:58,612 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:06:59,114 - INFO - Request Parameters - Page 6:
2025-05-30 08:06:59,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:06:59,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:06:59,866 - INFO - API请求耗时: 751ms
2025-05-30 08:06:59,866 - INFO - Response - Page 6
2025-05-30 08:06:59,867 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:07:00,368 - INFO - Request Parameters - Page 7:
2025-05-30 08:07:00,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:00,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:01,084 - INFO - API请求耗时: 715ms
2025-05-30 08:07:01,084 - INFO - Response - Page 7
2025-05-30 08:07:01,085 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:07:01,585 - INFO - Request Parameters - Page 8:
2025-05-30 08:07:01,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:01,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:02,406 - INFO - API请求耗时: 820ms
2025-05-30 08:07:02,407 - INFO - Response - Page 8
2025-05-30 08:07:02,407 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:07:02,908 - INFO - Request Parameters - Page 9:
2025-05-30 08:07:02,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:02,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:03,643 - INFO - API请求耗时: 732ms
2025-05-30 08:07:03,643 - INFO - Response - Page 9
2025-05-30 08:07:03,644 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:07:04,144 - INFO - Request Parameters - Page 10:
2025-05-30 08:07:04,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:04,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:04,938 - INFO - API请求耗时: 793ms
2025-05-30 08:07:04,938 - INFO - Response - Page 10
2025-05-30 08:07:04,939 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:07:05,439 - INFO - Request Parameters - Page 11:
2025-05-30 08:07:05,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:05,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:06,105 - INFO - API请求耗时: 665ms
2025-05-30 08:07:06,105 - INFO - Response - Page 11
2025-05-30 08:07:06,106 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:07:06,606 - INFO - Request Parameters - Page 12:
2025-05-30 08:07:06,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:06,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:07,316 - INFO - API请求耗时: 709ms
2025-05-30 08:07:07,316 - INFO - Response - Page 12
2025-05-30 08:07:07,317 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:07:07,818 - INFO - Request Parameters - Page 13:
2025-05-30 08:07:07,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:07,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:08,500 - INFO - API请求耗时: 681ms
2025-05-30 08:07:08,501 - INFO - Response - Page 13
2025-05-30 08:07:08,502 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:07:09,003 - INFO - Request Parameters - Page 14:
2025-05-30 08:07:09,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:09,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:09,790 - INFO - API请求耗时: 786ms
2025-05-30 08:07:09,790 - INFO - Response - Page 14
2025-05-30 08:07:09,791 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:07:10,292 - INFO - Request Parameters - Page 15:
2025-05-30 08:07:10,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:10,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:11,032 - INFO - API请求耗时: 739ms
2025-05-30 08:07:11,033 - INFO - Response - Page 15
2025-05-30 08:07:11,033 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:07:11,534 - INFO - Request Parameters - Page 16:
2025-05-30 08:07:11,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:11,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:12,315 - INFO - API请求耗时: 780ms
2025-05-30 08:07:12,315 - INFO - Response - Page 16
2025-05-30 08:07:12,316 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:07:12,817 - INFO - Request Parameters - Page 17:
2025-05-30 08:07:12,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:12,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:13,563 - INFO - API请求耗时: 744ms
2025-05-30 08:07:13,563 - INFO - Response - Page 17
2025-05-30 08:07:13,563 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:07:14,065 - INFO - Request Parameters - Page 18:
2025-05-30 08:07:14,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:14,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:15,085 - INFO - API请求耗时: 1019ms
2025-05-30 08:07:15,086 - INFO - Response - Page 18
2025-05-30 08:07:15,086 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:07:15,587 - INFO - Request Parameters - Page 19:
2025-05-30 08:07:15,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:15,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:16,338 - INFO - API请求耗时: 750ms
2025-05-30 08:07:16,338 - INFO - Response - Page 19
2025-05-30 08:07:16,339 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:07:16,840 - INFO - Request Parameters - Page 20:
2025-05-30 08:07:16,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:16,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:17,674 - INFO - API请求耗时: 833ms
2025-05-30 08:07:17,675 - INFO - Response - Page 20
2025-05-30 08:07:17,675 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:07:18,175 - INFO - Request Parameters - Page 21:
2025-05-30 08:07:18,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:18,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:18,904 - INFO - API请求耗时: 728ms
2025-05-30 08:07:18,905 - INFO - Response - Page 21
2025-05-30 08:07:18,905 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:07:19,406 - INFO - Request Parameters - Page 22:
2025-05-30 08:07:19,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:19,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:20,055 - INFO - API请求耗时: 647ms
2025-05-30 08:07:20,055 - INFO - Response - Page 22
2025-05-30 08:07:20,056 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:07:20,557 - INFO - Request Parameters - Page 23:
2025-05-30 08:07:20,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:20,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:21,324 - INFO - API请求耗时: 767ms
2025-05-30 08:07:21,324 - INFO - Response - Page 23
2025-05-30 08:07:21,325 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:07:21,825 - INFO - Request Parameters - Page 24:
2025-05-30 08:07:21,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:21,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:22,551 - INFO - API请求耗时: 725ms
2025-05-30 08:07:22,551 - INFO - Response - Page 24
2025-05-30 08:07:22,552 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:07:23,052 - INFO - Request Parameters - Page 25:
2025-05-30 08:07:23,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:23,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:23,787 - INFO - API请求耗时: 734ms
2025-05-30 08:07:23,788 - INFO - Response - Page 25
2025-05-30 08:07:23,789 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:07:24,290 - INFO - Request Parameters - Page 26:
2025-05-30 08:07:24,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:24,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:25,020 - INFO - API请求耗时: 729ms
2025-05-30 08:07:25,021 - INFO - Response - Page 26
2025-05-30 08:07:25,021 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:07:25,521 - INFO - Request Parameters - Page 27:
2025-05-30 08:07:25,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:25,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:26,280 - INFO - API请求耗时: 758ms
2025-05-30 08:07:26,280 - INFO - Response - Page 27
2025-05-30 08:07:26,281 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:07:26,781 - INFO - Request Parameters - Page 28:
2025-05-30 08:07:26,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:26,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:27,423 - INFO - API请求耗时: 641ms
2025-05-30 08:07:27,424 - INFO - Response - Page 28
2025-05-30 08:07:27,424 - INFO - 第 28 页获取到 100 条记录
2025-05-30 08:07:27,925 - INFO - Request Parameters - Page 29:
2025-05-30 08:07:27,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:27,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:28,641 - INFO - API请求耗时: 716ms
2025-05-30 08:07:28,642 - INFO - Response - Page 29
2025-05-30 08:07:28,642 - INFO - 第 29 页获取到 100 条记录
2025-05-30 08:07:29,143 - INFO - Request Parameters - Page 30:
2025-05-30 08:07:29,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:29,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:29,833 - INFO - API请求耗时: 688ms
2025-05-30 08:07:29,834 - INFO - Response - Page 30
2025-05-30 08:07:29,834 - INFO - 第 30 页获取到 100 条记录
2025-05-30 08:07:30,335 - INFO - Request Parameters - Page 31:
2025-05-30 08:07:30,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:30,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:31,085 - INFO - API请求耗时: 749ms
2025-05-30 08:07:31,085 - INFO - Response - Page 31
2025-05-30 08:07:31,086 - INFO - 第 31 页获取到 100 条记录
2025-05-30 08:07:31,586 - INFO - Request Parameters - Page 32:
2025-05-30 08:07:31,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:31,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:32,339 - INFO - API请求耗时: 752ms
2025-05-30 08:07:32,339 - INFO - Response - Page 32
2025-05-30 08:07:32,340 - INFO - 第 32 页获取到 100 条记录
2025-05-30 08:07:32,841 - INFO - Request Parameters - Page 33:
2025-05-30 08:07:32,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:32,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:33,554 - INFO - API请求耗时: 712ms
2025-05-30 08:07:33,554 - INFO - Response - Page 33
2025-05-30 08:07:33,555 - INFO - 第 33 页获取到 100 条记录
2025-05-30 08:07:34,056 - INFO - Request Parameters - Page 34:
2025-05-30 08:07:34,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:34,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:34,790 - INFO - API请求耗时: 733ms
2025-05-30 08:07:34,791 - INFO - Response - Page 34
2025-05-30 08:07:34,791 - INFO - 第 34 页获取到 100 条记录
2025-05-30 08:07:35,292 - INFO - Request Parameters - Page 35:
2025-05-30 08:07:35,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:35,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:36,014 - INFO - API请求耗时: 721ms
2025-05-30 08:07:36,014 - INFO - Response - Page 35
2025-05-30 08:07:36,014 - INFO - 第 35 页获取到 100 条记录
2025-05-30 08:07:36,515 - INFO - Request Parameters - Page 36:
2025-05-30 08:07:36,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:36,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:37,185 - INFO - API请求耗时: 669ms
2025-05-30 08:07:37,185 - INFO - Response - Page 36
2025-05-30 08:07:37,186 - INFO - 第 36 页获取到 100 条记录
2025-05-30 08:07:37,687 - INFO - Request Parameters - Page 37:
2025-05-30 08:07:37,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:37,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:38,394 - INFO - API请求耗时: 705ms
2025-05-30 08:07:38,394 - INFO - Response - Page 37
2025-05-30 08:07:38,395 - INFO - 第 37 页获取到 100 条记录
2025-05-30 08:07:38,896 - INFO - Request Parameters - Page 38:
2025-05-30 08:07:38,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:38,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:39,617 - INFO - API请求耗时: 720ms
2025-05-30 08:07:39,618 - INFO - Response - Page 38
2025-05-30 08:07:39,618 - INFO - 第 38 页获取到 100 条记录
2025-05-30 08:07:40,119 - INFO - Request Parameters - Page 39:
2025-05-30 08:07:40,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:40,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:40,774 - INFO - API请求耗时: 654ms
2025-05-30 08:07:40,774 - INFO - Response - Page 39
2025-05-30 08:07:40,775 - INFO - 第 39 页获取到 100 条记录
2025-05-30 08:07:41,276 - INFO - Request Parameters - Page 40:
2025-05-30 08:07:41,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:41,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:42,010 - INFO - API请求耗时: 731ms
2025-05-30 08:07:42,010 - INFO - Response - Page 40
2025-05-30 08:07:42,010 - INFO - 第 40 页获取到 100 条记录
2025-05-30 08:07:42,512 - INFO - Request Parameters - Page 41:
2025-05-30 08:07:42,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:42,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:43,319 - INFO - API请求耗时: 807ms
2025-05-30 08:07:43,319 - INFO - Response - Page 41
2025-05-30 08:07:43,320 - INFO - 第 41 页获取到 100 条记录
2025-05-30 08:07:43,820 - INFO - Request Parameters - Page 42:
2025-05-30 08:07:43,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:43,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800703, 1747411200703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:44,541 - INFO - API请求耗时: 719ms
2025-05-30 08:07:44,541 - INFO - Response - Page 42
2025-05-30 08:07:44,542 - INFO - 第 42 页获取到 76 条记录
2025-05-30 08:07:44,542 - INFO - 查询完成，共获取到 4176 条记录
2025-05-30 08:07:44,542 - INFO - 分段 7 查询成功，获取到 4176 条记录
2025-05-30 08:07:45,542 - INFO - 查询分段 8: 2025-05-18 至 2025-05-24
2025-05-30 08:07:45,543 - INFO - 查询日期范围: 2025-05-18 至 2025-05-24，使用分页查询，每页 100 条记录
2025-05-30 08:07:45,543 - INFO - Request Parameters - Page 1:
2025-05-30 08:07:45,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:45,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:46,241 - INFO - API请求耗时: 697ms
2025-05-30 08:07:46,242 - INFO - Response - Page 1
2025-05-30 08:07:46,242 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:07:46,744 - INFO - Request Parameters - Page 2:
2025-05-30 08:07:46,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:46,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:47,560 - INFO - API请求耗时: 815ms
2025-05-30 08:07:47,560 - INFO - Response - Page 2
2025-05-30 08:07:47,561 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:07:48,062 - INFO - Request Parameters - Page 3:
2025-05-30 08:07:48,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:48,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:48,761 - INFO - API请求耗时: 697ms
2025-05-30 08:07:48,761 - INFO - Response - Page 3
2025-05-30 08:07:48,762 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:07:49,262 - INFO - Request Parameters - Page 4:
2025-05-30 08:07:49,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:49,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:49,997 - INFO - API请求耗时: 733ms
2025-05-30 08:07:49,997 - INFO - Response - Page 4
2025-05-30 08:07:49,998 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:07:50,498 - INFO - Request Parameters - Page 5:
2025-05-30 08:07:50,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:50,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:51,233 - INFO - API请求耗时: 734ms
2025-05-30 08:07:51,233 - INFO - Response - Page 5
2025-05-30 08:07:51,234 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:07:51,734 - INFO - Request Parameters - Page 6:
2025-05-30 08:07:51,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:51,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:52,663 - INFO - API请求耗时: 928ms
2025-05-30 08:07:52,663 - INFO - Response - Page 6
2025-05-30 08:07:52,664 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:07:53,164 - INFO - Request Parameters - Page 7:
2025-05-30 08:07:53,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:53,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:53,951 - INFO - API请求耗时: 786ms
2025-05-30 08:07:53,952 - INFO - Response - Page 7
2025-05-30 08:07:53,952 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:07:54,452 - INFO - Request Parameters - Page 8:
2025-05-30 08:07:54,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:54,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:55,199 - INFO - API请求耗时: 746ms
2025-05-30 08:07:55,199 - INFO - Response - Page 8
2025-05-30 08:07:55,200 - INFO - 第 8 页获取到 100 条记录
2025-05-30 08:07:55,701 - INFO - Request Parameters - Page 9:
2025-05-30 08:07:55,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:55,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:56,492 - INFO - API请求耗时: 790ms
2025-05-30 08:07:56,492 - INFO - Response - Page 9
2025-05-30 08:07:56,493 - INFO - 第 9 页获取到 100 条记录
2025-05-30 08:07:56,993 - INFO - Request Parameters - Page 10:
2025-05-30 08:07:56,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:56,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:57,731 - INFO - API请求耗时: 737ms
2025-05-30 08:07:57,731 - INFO - Response - Page 10
2025-05-30 08:07:57,732 - INFO - 第 10 页获取到 100 条记录
2025-05-30 08:07:58,233 - INFO - Request Parameters - Page 11:
2025-05-30 08:07:58,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:58,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:07:58,966 - INFO - API请求耗时: 732ms
2025-05-30 08:07:58,966 - INFO - Response - Page 11
2025-05-30 08:07:58,967 - INFO - 第 11 页获取到 100 条记录
2025-05-30 08:07:59,467 - INFO - Request Parameters - Page 12:
2025-05-30 08:07:59,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:07:59,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:00,214 - INFO - API请求耗时: 745ms
2025-05-30 08:08:00,214 - INFO - Response - Page 12
2025-05-30 08:08:00,215 - INFO - 第 12 页获取到 100 条记录
2025-05-30 08:08:00,715 - INFO - Request Parameters - Page 13:
2025-05-30 08:08:00,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:00,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:01,458 - INFO - API请求耗时: 741ms
2025-05-30 08:08:01,458 - INFO - Response - Page 13
2025-05-30 08:08:01,459 - INFO - 第 13 页获取到 100 条记录
2025-05-30 08:08:01,960 - INFO - Request Parameters - Page 14:
2025-05-30 08:08:01,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:01,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:02,741 - INFO - API请求耗时: 780ms
2025-05-30 08:08:02,742 - INFO - Response - Page 14
2025-05-30 08:08:02,742 - INFO - 第 14 页获取到 100 条记录
2025-05-30 08:08:03,243 - INFO - Request Parameters - Page 15:
2025-05-30 08:08:03,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:03,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:03,927 - INFO - API请求耗时: 684ms
2025-05-30 08:08:03,928 - INFO - Response - Page 15
2025-05-30 08:08:03,928 - INFO - 第 15 页获取到 100 条记录
2025-05-30 08:08:04,429 - INFO - Request Parameters - Page 16:
2025-05-30 08:08:04,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:04,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:05,194 - INFO - API请求耗时: 764ms
2025-05-30 08:08:05,194 - INFO - Response - Page 16
2025-05-30 08:08:05,195 - INFO - 第 16 页获取到 100 条记录
2025-05-30 08:08:05,696 - INFO - Request Parameters - Page 17:
2025-05-30 08:08:05,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:05,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:06,482 - INFO - API请求耗时: 785ms
2025-05-30 08:08:06,482 - INFO - Response - Page 17
2025-05-30 08:08:06,483 - INFO - 第 17 页获取到 100 条记录
2025-05-30 08:08:06,983 - INFO - Request Parameters - Page 18:
2025-05-30 08:08:06,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:06,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:07,713 - INFO - API请求耗时: 729ms
2025-05-30 08:08:07,713 - INFO - Response - Page 18
2025-05-30 08:08:07,713 - INFO - 第 18 页获取到 100 条记录
2025-05-30 08:08:08,214 - INFO - Request Parameters - Page 19:
2025-05-30 08:08:08,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:08,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:08,985 - INFO - API请求耗时: 770ms
2025-05-30 08:08:08,985 - INFO - Response - Page 19
2025-05-30 08:08:08,986 - INFO - 第 19 页获取到 100 条记录
2025-05-30 08:08:09,487 - INFO - Request Parameters - Page 20:
2025-05-30 08:08:09,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:09,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:10,192 - INFO - API请求耗时: 704ms
2025-05-30 08:08:10,193 - INFO - Response - Page 20
2025-05-30 08:08:10,194 - INFO - 第 20 页获取到 100 条记录
2025-05-30 08:08:10,694 - INFO - Request Parameters - Page 21:
2025-05-30 08:08:10,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:10,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:11,463 - INFO - API请求耗时: 768ms
2025-05-30 08:08:11,463 - INFO - Response - Page 21
2025-05-30 08:08:11,463 - INFO - 第 21 页获取到 100 条记录
2025-05-30 08:08:11,965 - INFO - Request Parameters - Page 22:
2025-05-30 08:08:11,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:11,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:12,716 - INFO - API请求耗时: 750ms
2025-05-30 08:08:12,716 - INFO - Response - Page 22
2025-05-30 08:08:12,717 - INFO - 第 22 页获取到 100 条记录
2025-05-30 08:08:13,217 - INFO - Request Parameters - Page 23:
2025-05-30 08:08:13,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:13,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:13,915 - INFO - API请求耗时: 696ms
2025-05-30 08:08:13,915 - INFO - Response - Page 23
2025-05-30 08:08:13,916 - INFO - 第 23 页获取到 100 条记录
2025-05-30 08:08:14,417 - INFO - Request Parameters - Page 24:
2025-05-30 08:08:14,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:14,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:15,169 - INFO - API请求耗时: 751ms
2025-05-30 08:08:15,169 - INFO - Response - Page 24
2025-05-30 08:08:15,169 - INFO - 第 24 页获取到 100 条记录
2025-05-30 08:08:15,670 - INFO - Request Parameters - Page 25:
2025-05-30 08:08:15,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:15,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:16,420 - INFO - API请求耗时: 749ms
2025-05-30 08:08:16,420 - INFO - Response - Page 25
2025-05-30 08:08:16,421 - INFO - 第 25 页获取到 100 条记录
2025-05-30 08:08:16,921 - INFO - Request Parameters - Page 26:
2025-05-30 08:08:16,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:16,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:17,627 - INFO - API请求耗时: 705ms
2025-05-30 08:08:17,627 - INFO - Response - Page 26
2025-05-30 08:08:17,628 - INFO - 第 26 页获取到 100 条记录
2025-05-30 08:08:18,129 - INFO - Request Parameters - Page 27:
2025-05-30 08:08:18,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:18,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:18,889 - INFO - API请求耗时: 759ms
2025-05-30 08:08:18,889 - INFO - Response - Page 27
2025-05-30 08:08:18,890 - INFO - 第 27 页获取到 100 条记录
2025-05-30 08:08:19,390 - INFO - Request Parameters - Page 28:
2025-05-30 08:08:19,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:19,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600703, 1748016000703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:19,847 - INFO - API请求耗时: 456ms
2025-05-30 08:08:19,848 - INFO - Response - Page 28
2025-05-30 08:08:19,848 - INFO - 第 28 页获取到 7 条记录
2025-05-30 08:08:19,849 - INFO - 查询完成，共获取到 2707 条记录
2025-05-30 08:08:19,849 - INFO - 分段 8 查询成功，获取到 2707 条记录
2025-05-30 08:08:20,850 - INFO - 查询分段 9: 2025-05-25 至 2025-05-29
2025-05-30 08:08:20,850 - INFO - 查询日期范围: 2025-05-25 至 2025-05-29，使用分页查询，每页 100 条记录
2025-05-30 08:08:20,851 - INFO - Request Parameters - Page 1:
2025-05-30 08:08:20,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:20,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:21,570 - INFO - API请求耗时: 718ms
2025-05-30 08:08:21,570 - INFO - Response - Page 1
2025-05-30 08:08:21,571 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:08:22,072 - INFO - Request Parameters - Page 2:
2025-05-30 08:08:22,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:22,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:22,837 - INFO - API请求耗时: 765ms
2025-05-30 08:08:22,838 - INFO - Response - Page 2
2025-05-30 08:08:22,838 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:08:23,340 - INFO - Request Parameters - Page 3:
2025-05-30 08:08:23,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:23,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:24,074 - INFO - API请求耗时: 732ms
2025-05-30 08:08:24,074 - INFO - Response - Page 3
2025-05-30 08:08:24,075 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:08:24,576 - INFO - Request Parameters - Page 4:
2025-05-30 08:08:24,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:24,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:25,244 - INFO - API请求耗时: 667ms
2025-05-30 08:08:25,245 - INFO - Response - Page 4
2025-05-30 08:08:25,245 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:08:25,747 - INFO - Request Parameters - Page 5:
2025-05-30 08:08:25,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:25,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:26,503 - INFO - API请求耗时: 755ms
2025-05-30 08:08:26,503 - INFO - Response - Page 5
2025-05-30 08:08:26,504 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:08:27,005 - INFO - Request Parameters - Page 6:
2025-05-30 08:08:27,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:27,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:27,747 - INFO - API请求耗时: 740ms
2025-05-30 08:08:27,748 - INFO - Response - Page 6
2025-05-30 08:08:27,748 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:08:28,249 - INFO - Request Parameters - Page 7:
2025-05-30 08:08:28,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:08:28,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400703, 1748534399703], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:08:28,720 - INFO - API请求耗时: 471ms
2025-05-30 08:08:28,721 - INFO - Response - Page 7
2025-05-30 08:08:28,721 - INFO - 第 7 页获取到 17 条记录
2025-05-30 08:08:28,721 - INFO - 查询完成，共获取到 617 条记录
2025-05-30 08:08:28,722 - INFO - 分段 9 查询成功，获取到 617 条记录
2025-05-30 08:08:29,723 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 33403 条记录，失败 0 次
2025-05-30 08:08:29,723 - INFO - 成功获取宜搭日销售表单数据，共 33403 条记录
2025-05-30 08:08:29,724 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-30 08:08:29,724 - INFO - 开始对比和同步日销售数据...
2025-05-30 08:08:30,629 - INFO - 成功创建宜搭日销售数据索引，共 10813 条记录
2025-05-30 08:08:30,629 - INFO - 开始处理数衍数据，共 12961 条记录
2025-05-30 08:08:31,270 - INFO - 更新表单数据成功: FINST-WBF66B811JZUKRLX6BARY5WL121J2VP3SG7AMUN
2025-05-30 08:08:31,271 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250501, 变更字段: [{'field': 'amount', 'old_value': 48654.799999999996, 'new_value': 48714.799999999996}, {'field': 'count', 'old_value': 295, 'new_value': 296}, {'field': 'instoreAmount', 'old_value': 31941.0, 'new_value': 32001.0}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 170}]
2025-05-30 08:08:31,822 - INFO - 更新表单数据成功: FINST-80B66291G8GVMH1AA8AMN9RWS6B23LE879PAME5
2025-05-30 08:08:31,822 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250502, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 18470.89}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 18470.89}]
2025-05-30 08:08:32,340 - INFO - 更新表单数据成功: FINST-X0G66U81T7ZUX4MYD0RNX4B56T793C48C16AMZL
2025-05-30 08:08:32,340 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 24548.58}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 24548.58}]
2025-05-30 08:08:32,790 - INFO - 更新表单数据成功: FINST-U1B66W91LBMVO9NWCCI424DEVPM03KPR3MYAMMB
2025-05-30 08:08:32,790 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 42931.9, 'new_value': 43016.9}, {'field': 'count', 'old_value': 299, 'new_value': 300}, {'field': 'instoreAmount', 'old_value': 21580.9, 'new_value': 21665.9}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 108}]
2025-05-30 08:08:33,266 - INFO - 更新表单数据成功: FINST-U1B66W91FWFVNLDQF9RE29DDTPHT3NP989PAM73
2025-05-30 08:08:33,266 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250508, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8399.56}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8399.56}]
2025-05-30 08:08:33,747 - INFO - 更新表单数据成功: FINST-U1B66W91FWFVNLDQF9RE29DDTPHT3NP989PAM93
2025-05-30 08:08:33,748 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250506, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5781.47}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5781.47}]
2025-05-30 08:08:34,231 - INFO - 更新表单数据成功: FINST-U1B66W91FWFVNLDQF9RE29DDTPHT3NP989PAMA3
2025-05-30 08:08:34,232 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250505, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15002.42}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15002.42}]
2025-05-30 08:08:34,742 - INFO - 更新表单数据成功: FINST-6AG66W81TBLVR8MMB5TYA59YG2953BNRN6XAM75
2025-05-30 08:08:34,742 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 23348.63}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 23348.63}]
2025-05-30 08:08:35,230 - INFO - 更新表单数据成功: FINST-W4G66DA1CYFVU7R6E7XYM9LI10RK3YD599PAMI3
2025-05-30 08:08:35,231 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16837.53}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16837.53}]
2025-05-30 08:08:35,674 - INFO - 更新表单数据成功: FINST-RNA66D71UCTVGA7UAZUJ5989QIKW215V8M8BM59
2025-05-30 08:08:35,674 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19822.37}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19822.37}]
2025-05-30 08:08:36,084 - INFO - 更新表单数据成功: FINST-737662B1A9TVH1MN9B7T3AIZKOI83AVP8M8BMH9
2025-05-30 08:08:36,084 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 9505.8, 'new_value': 9473.7}, {'field': 'dailyBillAmount', 'old_value': 9505.8, 'new_value': 9473.7}]
2025-05-30 08:08:36,628 - INFO - 更新表单数据成功: FINST-737662B1A9TVH1MN9B7T3AIZKOI83AVP8M8BMI9
2025-05-30 08:08:36,628 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8720.62}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8720.62}]
2025-05-30 08:08:37,077 - INFO - 更新表单数据成功: FINST-LLF66B713GPVIVLO7VYWB5FT38SK2BESWB4BMYE
2025-05-30 08:08:37,077 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7846.66}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7846.66}]
2025-05-30 08:08:37,535 - INFO - 更新表单数据成功: FINST-W4G66DA1CYFVU7R6E7XYM9LI10RK3ZD599PAMW4
2025-05-30 08:08:37,535 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250513, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8228.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8228.69}]
2025-05-30 08:08:37,999 - INFO - 更新表单数据成功: FINST-W4G66DA1CYFVU7R6E7XYM9LI10RK3ZD599PAMX4
2025-05-30 08:08:37,999 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6631.72}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6631.72}]
2025-05-30 08:08:38,459 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3H3JDR5BM31
2025-05-30 08:08:38,459 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 43138.3, 'new_value': 43541.3}, {'field': 'count', 'old_value': 298, 'new_value': 300}, {'field': 'instoreAmount', 'old_value': 26291.8, 'new_value': 26694.8}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 137}]
2025-05-30 08:08:38,908 - INFO - 更新表单数据成功: FINST-ACB66071T7TVDNGCA9CYF9DQXK573B9VV89BMPF
2025-05-30 08:08:38,908 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19724.04}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19724.04}]
2025-05-30 08:08:39,350 - INFO - 更新表单数据成功: FINST-8SG66JA1H9TVMGE16K08F9OXRW953HPNYB9BMGK
2025-05-30 08:08:39,350 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250523, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9752.12}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9752.12}]
2025-05-30 08:08:39,787 - INFO - 更新表单数据成功: FINST-RNA66D71UCTVGA7UAZUJ5989QIKW215V8M8BM09
2025-05-30 08:08:39,787 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250522, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8012.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8012.1}]
2025-05-30 08:08:40,319 - INFO - 更新表单数据成功: FINST-8SG66JA1H9TVMGE16K08F9OXRW953GPNYB9BM5K
2025-05-30 08:08:40,320 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9317.01}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9317.01}]
2025-05-30 08:08:40,797 - INFO - 更新表单数据成功: FINST-RNA66D71UCTVGA7UAZUJ5989QIKW215V8M8BM29
2025-05-30 08:08:40,797 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250520, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8579.37}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8579.37}]
2025-05-30 08:08:41,224 - INFO - 更新表单数据成功: FINST-RNA66D71UCTVGA7UAZUJ5989QIKW215V8M8BM39
2025-05-30 08:08:41,224 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9093.63}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9093.63}]
2025-05-30 08:08:41,651 - INFO - 更新表单数据成功: FINST-1PF66KA17FSVXEY0B18X8CIV43PP2TSX8M8BMS3
2025-05-30 08:08:41,652 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250528, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2439.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2439.0}]
2025-05-30 08:08:42,121 - INFO - 更新表单数据成功: FINST-487664C1WETVM1796SERK7NFX1AV3QE09M8BMC8
2025-05-30 08:08:42,121 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250528, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6378.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6378.5}]
2025-05-30 08:08:42,625 - INFO - 更新表单数据成功: FINST-487664C1WETVM1796SERK7NFX1AV3QE09M8BMY8
2025-05-30 08:08:42,625 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_20250528, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 269.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 269.0}]
2025-05-30 08:08:43,097 - INFO - 更新表单数据成功: FINST-6AG66W810GTV4L2FFMZHZ6FBQ77Q2W239M8BMTJ
2025-05-30 08:08:43,098 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250528, 变更字段: [{'field': 'recommendAmount', 'old_value': 2583.37, 'new_value': 2595.97}, {'field': 'amount', 'old_value': 2583.37, 'new_value': 2595.97}, {'field': 'count', 'old_value': 143, 'new_value': 144}, {'field': 'onlineAmount', 'old_value': 2120.41, 'new_value': 2133.01}, {'field': 'onlineCount', 'old_value': 120, 'new_value': 121}]
2025-05-30 08:08:43,553 - INFO - 更新表单数据成功: FINST-6AG66W810GTV4L2FFMZHZ6FBQ77Q2X239M8BMCL
2025-05-30 08:08:43,554 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250528, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19166.95}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19166.95}]
2025-05-30 08:08:44,039 - INFO - 更新表单数据成功: FINST-ACB66071T7TVDNGCA9CYF9DQXK573B9VV89BMLF
2025-05-30 08:08:44,039 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250528, 变更字段: [{'field': 'amount', 'old_value': 369.8, 'new_value': 5372.08}, {'field': 'count', 'old_value': 21, 'new_value': 268}, {'field': 'instoreAmount', 'old_value': 93.3, 'new_value': 1533.78}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 70}, {'field': 'onlineAmount', 'old_value': 276.5, 'new_value': 4008.4}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 198}]
2025-05-30 08:08:44,496 - INFO - 更新表单数据成功: FINST-ACB66071T7TVDNGCA9CYF9DQXK573B9VV89BMMF
2025-05-30 08:08:44,497 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250527, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7731.59}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7731.59}, {'field': 'amount', 'old_value': 2949.82, 'new_value': 5589.5199999999995}, {'field': 'count', 'old_value': 143, 'new_value': 266}, {'field': 'instoreAmount', 'old_value': 861.5, 'new_value': 1625.1}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 79}, {'field': 'onlineAmount', 'old_value': 2088.32, 'new_value': 4072.52}, {'field': 'onlineCount', 'old_value': 94, 'new_value': 187}]
2025-05-30 08:08:44,932 - INFO - 更新表单数据成功: FINST-ACB66071T7TVDNGCA9CYF9DQXK573B9VV89BMNF
2025-05-30 08:08:44,932 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6951.83}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6951.83}]
2025-05-30 08:08:44,989 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-30 08:08:45,452 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-30 08:08:48,455 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-30 08:08:48,916 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-30 08:08:51,919 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-30 08:08:52,285 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-30 08:08:55,289 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-30 08:08:55,803 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-30 08:08:58,806 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-30 08:08:59,282 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-30 08:09:02,285 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-30 08:09:02,773 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-30 08:09:05,777 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-30 08:09:06,208 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-30 08:09:09,212 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-30 08:09:09,623 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-30 08:09:12,626 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-30 08:09:13,043 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-30 08:09:16,045 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-30 08:09:16,538 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-30 08:09:19,541 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-30 08:09:20,007 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-30 08:09:23,011 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-30 08:09:23,560 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-30 08:09:26,563 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-30 08:09:26,956 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-30 08:09:29,959 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-30 08:09:30,313 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-30 08:09:33,315 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-30 08:09:33,700 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-30 08:09:36,703 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-30 08:09:37,190 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-30 08:09:40,193 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-30 08:09:40,662 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-30 08:09:43,666 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-30 08:09:44,184 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-30 08:09:47,186 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-30 08:09:47,592 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-30 08:09:50,596 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-30 08:09:51,022 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-30 08:09:54,026 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-30 08:09:54,448 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-30 08:09:57,450 - INFO - 正在批量插入每日数据，批次 22/22，共 48 条记录
2025-05-30 08:09:57,703 - INFO - 批量插入每日数据成功，批次 22，48 条记录
2025-05-30 08:10:00,704 - INFO - 批量插入每日数据完成: 总计 2148 条，成功 2148 条，失败 0 条
2025-05-30 08:10:00,708 - INFO - 批量插入日销售数据完成，共 2148 条记录
2025-05-30 08:10:00,708 - INFO - 日销售数据同步完成！更新: 30 条，插入: 2148 条，错误: 0 条，跳过: 10783 条
2025-05-30 08:10:00,708 - INFO - 正在获取宜搭月销售表单数据...
2025-05-30 08:10:00,708 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-30 08:10:00,708 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-30 08:10:00,709 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:00,709 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:00,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:00,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:01,473 - INFO - API请求耗时: 764ms
2025-05-30 08:10:01,474 - INFO - Response - Page 1
2025-05-30 08:10:01,474 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:01,474 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:01,475 - WARNING - 月度分段 1 查询返回空数据
2025-05-30 08:10:01,475 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-30 08:10:01,475 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:01,475 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:01,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:01,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:01,885 - INFO - API请求耗时: 408ms
2025-05-30 08:10:01,885 - INFO - Response - Page 1
2025-05-30 08:10:01,886 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:01,886 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:01,886 - WARNING - 单月查询返回空数据: 2024-05
2025-05-30 08:10:02,387 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-30 08:10:02,387 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:02,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:02,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:02,623 - INFO - API请求耗时: 235ms
2025-05-30 08:10:02,623 - INFO - Response - Page 1
2025-05-30 08:10:02,624 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:02,624 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:02,624 - WARNING - 单月查询返回空数据: 2024-06
2025-05-30 08:10:03,125 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:03,125 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:03,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:03,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:03,350 - INFO - API请求耗时: 224ms
2025-05-30 08:10:03,351 - INFO - Response - Page 1
2025-05-30 08:10:03,351 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:03,351 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:03,351 - WARNING - 单月查询返回空数据: 2024-07
2025-05-30 08:10:04,853 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-30 08:10:04,853 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:04,854 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:04,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:04,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:05,067 - INFO - API请求耗时: 212ms
2025-05-30 08:10:05,067 - INFO - Response - Page 1
2025-05-30 08:10:05,068 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:05,068 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:05,068 - WARNING - 月度分段 2 查询返回空数据
2025-05-30 08:10:05,068 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-30 08:10:05,069 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:05,069 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:05,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:05,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:05,295 - INFO - API请求耗时: 226ms
2025-05-30 08:10:05,296 - INFO - Response - Page 1
2025-05-30 08:10:05,297 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:05,297 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:05,297 - WARNING - 单月查询返回空数据: 2024-08
2025-05-30 08:10:05,798 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-30 08:10:05,798 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:05,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:05,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:06,085 - INFO - API请求耗时: 286ms
2025-05-30 08:10:06,085 - INFO - Response - Page 1
2025-05-30 08:10:06,086 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:06,086 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:06,086 - WARNING - 单月查询返回空数据: 2024-09
2025-05-30 08:10:06,586 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:06,586 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:06,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:06,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:06,827 - INFO - API请求耗时: 240ms
2025-05-30 08:10:06,827 - INFO - Response - Page 1
2025-05-30 08:10:06,828 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-30 08:10:06,828 - INFO - 查询完成，共获取到 0 条记录
2025-05-30 08:10:06,828 - WARNING - 单月查询返回空数据: 2024-10
2025-05-30 08:10:08,329 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-30 08:10:08,329 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:08,330 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:08,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:08,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:08,843 - INFO - API请求耗时: 513ms
2025-05-30 08:10:08,843 - INFO - Response - Page 1
2025-05-30 08:10:08,844 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:10:09,345 - INFO - Request Parameters - Page 2:
2025-05-30 08:10:09,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:09,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:09,929 - INFO - API请求耗时: 584ms
2025-05-30 08:10:09,930 - INFO - Response - Page 2
2025-05-30 08:10:09,930 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:10:10,431 - INFO - Request Parameters - Page 3:
2025-05-30 08:10:10,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:10,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:10,862 - INFO - API请求耗时: 430ms
2025-05-30 08:10:10,863 - INFO - Response - Page 3
2025-05-30 08:10:10,864 - INFO - 第 3 页获取到 48 条记录
2025-05-30 08:10:10,864 - INFO - 查询完成，共获取到 248 条记录
2025-05-30 08:10:10,864 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-30 08:10:11,865 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-30 08:10:11,865 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-30 08:10:11,866 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:11,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:11,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:12,413 - INFO - API请求耗时: 547ms
2025-05-30 08:10:12,414 - INFO - Response - Page 1
2025-05-30 08:10:12,414 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:10:12,916 - INFO - Request Parameters - Page 2:
2025-05-30 08:10:12,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:12,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:13,470 - INFO - API请求耗时: 554ms
2025-05-30 08:10:13,471 - INFO - Response - Page 2
2025-05-30 08:10:13,471 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:10:13,972 - INFO - Request Parameters - Page 3:
2025-05-30 08:10:13,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:13,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:14,559 - INFO - API请求耗时: 586ms
2025-05-30 08:10:14,560 - INFO - Response - Page 3
2025-05-30 08:10:14,560 - INFO - 第 3 页获取到 100 条记录
2025-05-30 08:10:15,062 - INFO - Request Parameters - Page 4:
2025-05-30 08:10:15,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:15,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:15,663 - INFO - API请求耗时: 600ms
2025-05-30 08:10:15,664 - INFO - Response - Page 4
2025-05-30 08:10:15,665 - INFO - 第 4 页获取到 100 条记录
2025-05-30 08:10:16,166 - INFO - Request Parameters - Page 5:
2025-05-30 08:10:16,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:16,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:16,730 - INFO - API请求耗时: 563ms
2025-05-30 08:10:16,730 - INFO - Response - Page 5
2025-05-30 08:10:16,731 - INFO - 第 5 页获取到 100 条记录
2025-05-30 08:10:17,232 - INFO - Request Parameters - Page 6:
2025-05-30 08:10:17,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:17,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:17,810 - INFO - API请求耗时: 577ms
2025-05-30 08:10:17,810 - INFO - Response - Page 6
2025-05-30 08:10:17,811 - INFO - 第 6 页获取到 100 条记录
2025-05-30 08:10:18,312 - INFO - Request Parameters - Page 7:
2025-05-30 08:10:18,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:18,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:18,883 - INFO - API请求耗时: 569ms
2025-05-30 08:10:18,884 - INFO - Response - Page 7
2025-05-30 08:10:18,884 - INFO - 第 7 页获取到 100 条记录
2025-05-30 08:10:19,384 - INFO - Request Parameters - Page 8:
2025-05-30 08:10:19,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:19,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:19,743 - INFO - API请求耗时: 358ms
2025-05-30 08:10:19,744 - INFO - Response - Page 8
2025-05-30 08:10:19,745 - INFO - 第 8 页获取到 16 条记录
2025-05-30 08:10:19,745 - INFO - 查询完成，共获取到 716 条记录
2025-05-30 08:10:19,745 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-30 08:10:20,746 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-30 08:10:20,746 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-30 08:10:20,747 - INFO - Request Parameters - Page 1:
2025-05-30 08:10:20,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:20,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:21,248 - INFO - API请求耗时: 500ms
2025-05-30 08:10:21,248 - INFO - Response - Page 1
2025-05-30 08:10:21,248 - INFO - 第 1 页获取到 100 条记录
2025-05-30 08:10:21,750 - INFO - Request Parameters - Page 2:
2025-05-30 08:10:21,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:21,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:22,306 - INFO - API请求耗时: 556ms
2025-05-30 08:10:22,306 - INFO - Response - Page 2
2025-05-30 08:10:22,307 - INFO - 第 2 页获取到 100 条记录
2025-05-30 08:10:22,808 - INFO - Request Parameters - Page 3:
2025-05-30 08:10:22,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-30 08:10:22,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-30 08:10:23,261 - INFO - API请求耗时: 452ms
2025-05-30 08:10:23,262 - INFO - Response - Page 3
2025-05-30 08:10:23,262 - INFO - 第 3 页获取到 28 条记录
2025-05-30 08:10:23,263 - INFO - 查询完成，共获取到 228 条记录
2025-05-30 08:10:23,263 - INFO - 月度分段 5 查询成功，获取到 228 条记录
2025-05-30 08:10:24,263 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1192 条记录，失败 0 次
2025-05-30 08:10:24,263 - INFO - 成功获取宜搭月销售表单数据，共 1192 条记录
2025-05-30 08:10:24,264 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-30 08:10:24,264 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-30 08:10:24,274 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-30 08:10:24,335 - INFO - 成功创建宜搭月销售数据索引，共 1192 条记录
2025-05-30 08:10:24,336 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:24,849 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-30 08:10:24,849 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 196949.25, 'new_value': 201704.87}, {'field': 'dailyBillAmount', 'old_value': 196949.25, 'new_value': 201704.87}, {'field': 'amount', 'old_value': 5683.1, 'new_value': 5883.7}, {'field': 'count', 'old_value': 80, 'new_value': 82}, {'field': 'onlineAmount', 'old_value': 5759.67, 'new_value': 5960.2699999999995}, {'field': 'onlineCount', 'old_value': 80, 'new_value': 82}]
2025-05-30 08:10:24,850 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:25,473 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-30 08:10:25,474 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 499970.44, 'new_value': 513284.85}, {'field': 'dailyBillAmount', 'old_value': 499970.44, 'new_value': 513284.85}, {'field': 'amount', 'old_value': 268423.1, 'new_value': 277573.6}, {'field': 'count', 'old_value': 2542, 'new_value': 2634}, {'field': 'instoreAmount', 'old_value': 109980.7, 'new_value': 113340.3}, {'field': 'instoreCount', 'old_value': 869, 'new_value': 897}, {'field': 'onlineAmount', 'old_value': 158795.0, 'new_value': 164585.9}, {'field': 'onlineCount', 'old_value': 1673, 'new_value': 1737}]
2025-05-30 08:10:25,475 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:25,929 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-30 08:10:25,929 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 336631.68, 'new_value': 343099.45}, {'field': 'dailyBillAmount', 'old_value': 336631.68, 'new_value': 343099.45}, {'field': 'amount', 'old_value': 339837.33, 'new_value': 346386.73}, {'field': 'count', 'old_value': 2260, 'new_value': 2301}, {'field': 'instoreAmount', 'old_value': 321851.98, 'new_value': 327904.88}, {'field': 'instoreCount', 'old_value': 1993, 'new_value': 2027}, {'field': 'onlineAmount', 'old_value': 18312.45, 'new_value': 18808.95}, {'field': 'onlineCount', 'old_value': 267, 'new_value': 274}]
2025-05-30 08:10:25,930 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:26,386 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-30 08:10:26,387 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 725083.37, 'new_value': 749325.81}, {'field': 'dailyBillAmount', 'old_value': 725083.37, 'new_value': 749325.81}, {'field': 'amount', 'old_value': 536437.18, 'new_value': 552079.18}, {'field': 'count', 'old_value': 2597, 'new_value': 2674}, {'field': 'instoreAmount', 'old_value': 536437.18, 'new_value': 552079.18}, {'field': 'instoreCount', 'old_value': 2597, 'new_value': 2674}]
2025-05-30 08:10:26,387 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:26,915 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-30 08:10:26,915 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 572219.06, 'new_value': 579998.0}, {'field': 'dailyBillAmount', 'old_value': 572219.06, 'new_value': 579998.0}, {'field': 'amount', 'old_value': 944413.0, 'new_value': 960120.0}, {'field': 'count', 'old_value': 3309, 'new_value': 3381}, {'field': 'instoreAmount', 'old_value': 945522.0, 'new_value': 961229.0}, {'field': 'instoreCount', 'old_value': 3305, 'new_value': 3377}]
2025-05-30 08:10:26,916 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:27,333 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-30 08:10:27,333 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65545.1, 'new_value': 65743.0}, {'field': 'dailyBillAmount', 'old_value': 65545.1, 'new_value': 65743.0}, {'field': 'amount', 'old_value': 88643.61, 'new_value': 88927.31}, {'field': 'count', 'old_value': 355, 'new_value': 361}, {'field': 'onlineAmount', 'old_value': 49884.92, 'new_value': 50274.520000000004}, {'field': 'onlineCount', 'old_value': 307, 'new_value': 313}]
2025-05-30 08:10:27,334 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:27,764 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-30 08:10:27,764 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 224456.9, 'new_value': 231676.9}, {'field': 'amount', 'old_value': 224456.9, 'new_value': 231676.9}, {'field': 'count', 'old_value': 137, 'new_value': 142}, {'field': 'instoreAmount', 'old_value': 224456.9, 'new_value': 231676.9}, {'field': 'instoreCount', 'old_value': 137, 'new_value': 142}]
2025-05-30 08:10:27,765 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:28,258 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-30 08:10:28,258 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 816215.51, 'new_value': 842690.42}, {'field': 'dailyBillAmount', 'old_value': 816215.51, 'new_value': 842690.42}, {'field': 'amount', 'old_value': 711499.0, 'new_value': 726369.8}, {'field': 'count', 'old_value': 5074, 'new_value': 5183}, {'field': 'instoreAmount', 'old_value': 588810.81, 'new_value': 602517.71}, {'field': 'instoreCount', 'old_value': 2557, 'new_value': 2619}, {'field': 'onlineAmount', 'old_value': 126862.57, 'new_value': 128408.77}, {'field': 'onlineCount', 'old_value': 2517, 'new_value': 2564}]
2025-05-30 08:10:28,259 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:28,737 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-30 08:10:28,738 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 801221.9, 'new_value': 822319.27}, {'field': 'dailyBillAmount', 'old_value': 801221.9, 'new_value': 822319.27}, {'field': 'amount', 'old_value': 216081.03, 'new_value': 219798.62}, {'field': 'count', 'old_value': 1221, 'new_value': 1249}, {'field': 'instoreAmount', 'old_value': 216081.03, 'new_value': 219798.62}, {'field': 'instoreCount', 'old_value': 1221, 'new_value': 1249}]
2025-05-30 08:10:28,743 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:29,170 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-30 08:10:29,170 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 123608.1, 'new_value': 129374.1}, {'field': 'count', 'old_value': 322, 'new_value': 329}, {'field': 'instoreAmount', 'old_value': 123609.8, 'new_value': 129375.8}, {'field': 'instoreCount', 'old_value': 322, 'new_value': 329}]
2025-05-30 08:10:29,171 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:29,653 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-05-30 08:10:29,654 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34866.9, 'new_value': 34892.8}, {'field': 'amount', 'old_value': 34866.9, 'new_value': 34892.8}, {'field': 'count', 'old_value': 31, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 36262.9, 'new_value': 36288.8}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 32}]
2025-05-30 08:10:29,654 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:30,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-30 08:10:30,241 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1085083.53, 'new_value': 1105593.3599999999}, {'field': 'dailyBillAmount', 'old_value': 1085083.53, 'new_value': 1105593.3599999999}, {'field': 'amount', 'old_value': -389696.9, 'new_value': -380790.57}, {'field': 'count', 'old_value': 1204, 'new_value': 1250}, {'field': 'instoreAmount', 'old_value': 698063.4, 'new_value': 718573.23}, {'field': 'instoreCount', 'old_value': 1204, 'new_value': 1250}]
2025-05-30 08:10:30,241 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:30,664 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-30 08:10:30,664 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 467526.0, 'new_value': 483906.0}, {'field': 'amount', 'old_value': 467526.0, 'new_value': 483906.0}, {'field': 'count', 'old_value': 1558, 'new_value': 1612}, {'field': 'instoreAmount', 'old_value': 467526.0, 'new_value': 483906.0}, {'field': 'instoreCount', 'old_value': 1558, 'new_value': 1612}]
2025-05-30 08:10:30,665 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:31,185 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-30 08:10:31,185 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 436808.76, 'new_value': 453599.41000000003}, {'field': 'dailyBillAmount', 'old_value': 342474.66, 'new_value': 357958.71}, {'field': 'amount', 'old_value': 436808.76, 'new_value': 453599.41000000003}, {'field': 'count', 'old_value': 1475, 'new_value': 1523}, {'field': 'instoreAmount', 'old_value': 436808.76, 'new_value': 453599.41000000003}, {'field': 'instoreCount', 'old_value': 1475, 'new_value': 1523}]
2025-05-30 08:10:31,186 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:31,666 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-30 08:10:31,667 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 226497.91, 'new_value': 232876.41}, {'field': 'dailyBillAmount', 'old_value': 226497.91, 'new_value': 232876.41}]
2025-05-30 08:10:31,667 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:32,091 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-30 08:10:32,091 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 113181.36, 'new_value': 115719.22}, {'field': 'dailyBillAmount', 'old_value': 113181.36, 'new_value': 115719.22}, {'field': 'amount', 'old_value': 68326.48, 'new_value': 70160.76}, {'field': 'count', 'old_value': 1029, 'new_value': 1063}, {'field': 'instoreAmount', 'old_value': 70292.38, 'new_value': 72126.66}, {'field': 'instoreCount', 'old_value': 1029, 'new_value': 1063}]
2025-05-30 08:10:32,092 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:32,560 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-30 08:10:32,561 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 167232.7, 'new_value': 174276.81}, {'field': 'dailyBillAmount', 'old_value': 93140.53, 'new_value': 99761.42}, {'field': 'amount', 'old_value': 167231.84, 'new_value': 174275.95}, {'field': 'count', 'old_value': 5742, 'new_value': 5991}, {'field': 'instoreAmount', 'old_value': 145648.78, 'new_value': 151879.38}, {'field': 'instoreCount', 'old_value': 5193, 'new_value': 5418}, {'field': 'onlineAmount', 'old_value': 21583.92, 'new_value': 22397.43}, {'field': 'onlineCount', 'old_value': 549, 'new_value': 573}]
2025-05-30 08:10:32,561 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:33,142 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-30 08:10:33,142 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 342137.22, 'new_value': 352443.22}, {'field': 'dailyBillAmount', 'old_value': 336747.0, 'new_value': 347053.0}, {'field': 'amount', 'old_value': 282877.01, 'new_value': 291032.01}, {'field': 'count', 'old_value': 263, 'new_value': 272}, {'field': 'instoreAmount', 'old_value': 282644.0, 'new_value': 290799.0}, {'field': 'instoreCount', 'old_value': 260, 'new_value': 269}]
2025-05-30 08:10:33,143 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:33,597 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-30 08:10:33,597 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 637380.4, 'new_value': 681942.3}, {'field': 'dailyBillAmount', 'old_value': 636875.85, 'new_value': 681437.75}, {'field': 'amount', 'old_value': 637380.4, 'new_value': 681942.3}, {'field': 'count', 'old_value': 573, 'new_value': 600}, {'field': 'instoreAmount', 'old_value': 637381.4, 'new_value': 681943.3}, {'field': 'instoreCount', 'old_value': 573, 'new_value': 600}]
2025-05-30 08:10:33,598 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:34,095 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-30 08:10:34,096 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 41229.0, 'new_value': 43699.0}, {'field': 'count', 'old_value': 71, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 41229.0, 'new_value': 43699.0}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 73}]
2025-05-30 08:10:34,096 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:34,553 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-30 08:10:34,553 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117920.0, 'new_value': 120923.8}, {'field': 'dailyBillAmount', 'old_value': 117920.0, 'new_value': 120923.8}, {'field': 'amount', 'old_value': 128839.3, 'new_value': 131981.1}, {'field': 'count', 'old_value': 343, 'new_value': 351}, {'field': 'instoreAmount', 'old_value': 128845.2, 'new_value': 131987.0}, {'field': 'instoreCount', 'old_value': 343, 'new_value': 351}]
2025-05-30 08:10:34,554 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:35,005 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-30 08:10:35,005 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 188033.7, 'new_value': 191638.7}, {'field': 'amount', 'old_value': 188033.7, 'new_value': 191638.7}, {'field': 'count', 'old_value': 218, 'new_value': 224}, {'field': 'instoreAmount', 'old_value': 188160.7, 'new_value': 191765.7}, {'field': 'instoreCount', 'old_value': 218, 'new_value': 224}]
2025-05-30 08:10:35,005 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:35,499 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-30 08:10:35,499 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 251246.18, 'new_value': 256571.18}, {'field': 'dailyBillAmount', 'old_value': 251246.18, 'new_value': 256571.18}, {'field': 'amount', 'old_value': 265161.35, 'new_value': 272318.75}, {'field': 'count', 'old_value': 1759, 'new_value': 1805}, {'field': 'instoreAmount', 'old_value': 266370.35, 'new_value': 273527.75}, {'field': 'instoreCount', 'old_value': 1759, 'new_value': 1805}]
2025-05-30 08:10:35,500 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:35,919 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-30 08:10:35,920 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 168179.39, 'new_value': 176363.37}, {'field': 'dailyBillAmount', 'old_value': 168179.39, 'new_value': 176363.37}, {'field': 'amount', 'old_value': 16553.53, 'new_value': 17611.98}, {'field': 'count', 'old_value': 1541, 'new_value': 1632}, {'field': 'instoreAmount', 'old_value': 22079.69, 'new_value': 23313.54}, {'field': 'instoreCount', 'old_value': 1541, 'new_value': 1632}]
2025-05-30 08:10:35,920 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:36,395 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-30 08:10:36,395 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335321.12, 'new_value': 352028.5}, {'field': 'amount', 'old_value': 335316.03, 'new_value': 352023.41000000003}, {'field': 'count', 'old_value': 7320, 'new_value': 7627}, {'field': 'instoreAmount', 'old_value': 327707.77, 'new_value': 347616.95}, {'field': 'instoreCount', 'old_value': 7058, 'new_value': 7355}, {'field': 'onlineAmount', 'old_value': 12311.93, 'new_value': 13293.13}, {'field': 'onlineCount', 'old_value': 262, 'new_value': 272}]
2025-05-30 08:10:36,396 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:36,829 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-30 08:10:36,829 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 262962.7, 'new_value': 270206.0}, {'field': 'dailyBillAmount', 'old_value': 262962.7, 'new_value': 270206.0}, {'field': 'amount', 'old_value': 262962.7, 'new_value': 270206.0}, {'field': 'count', 'old_value': 783, 'new_value': 804}, {'field': 'instoreAmount', 'old_value': 262962.7, 'new_value': 270206.0}, {'field': 'instoreCount', 'old_value': 783, 'new_value': 804}]
2025-05-30 08:10:36,830 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:37,356 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-30 08:10:37,356 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 232176.58, 'new_value': 233325.58}, {'field': 'amount', 'old_value': 76175.2, 'new_value': 77324.2}, {'field': 'count', 'old_value': 177, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 76175.2, 'new_value': 77324.2}, {'field': 'instoreCount', 'old_value': 177, 'new_value': 180}]
2025-05-30 08:10:37,357 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:37,818 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-30 08:10:37,818 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 458020.59, 'new_value': 466559.54}, {'field': 'dailyBillAmount', 'old_value': 458020.59, 'new_value': 466559.54}, {'field': 'amount', 'old_value': 189517.9, 'new_value': 193717.2}, {'field': 'count', 'old_value': 711, 'new_value': 726}, {'field': 'instoreAmount', 'old_value': 189518.16, 'new_value': 193717.46}, {'field': 'instoreCount', 'old_value': 711, 'new_value': 726}]
2025-05-30 08:10:37,819 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:38,359 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-30 08:10:38,359 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102772.89, 'new_value': 106721.97}, {'field': 'dailyBillAmount', 'old_value': 102772.89, 'new_value': 106721.97}, {'field': 'amount', 'old_value': 30627.45, 'new_value': 31553.06}, {'field': 'count', 'old_value': 1122, 'new_value': 1156}, {'field': 'instoreAmount', 'old_value': 7171.54, 'new_value': 7387.42}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 198}, {'field': 'onlineAmount', 'old_value': 23794.82, 'new_value': 24504.55}, {'field': 'onlineCount', 'old_value': 932, 'new_value': 958}]
2025-05-30 08:10:38,360 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:38,771 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-30 08:10:38,772 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162844.55, 'new_value': 168423.28}, {'field': 'dailyBillAmount', 'old_value': 162844.55, 'new_value': 168423.28}, {'field': 'amount', 'old_value': 26713.17, 'new_value': 27585.14}, {'field': 'count', 'old_value': 659, 'new_value': 678}, {'field': 'instoreAmount', 'old_value': 23243.94, 'new_value': 24097.38}, {'field': 'instoreCount', 'old_value': 587, 'new_value': 605}, {'field': 'onlineAmount', 'old_value': 3469.92, 'new_value': 3488.45}, {'field': 'onlineCount', 'old_value': 72, 'new_value': 73}]
2025-05-30 08:10:38,772 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:39,217 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-30 08:10:39,217 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22521.93, 'new_value': 22889.93}, {'field': 'dailyBillAmount', 'old_value': 22521.93, 'new_value': 22889.93}, {'field': 'amount', 'old_value': 17660.08, 'new_value': 17828.08}, {'field': 'count', 'old_value': 635, 'new_value': 648}, {'field': 'instoreAmount', 'old_value': 18027.68, 'new_value': 18195.68}, {'field': 'instoreCount', 'old_value': 635, 'new_value': 648}]
2025-05-30 08:10:39,218 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:39,724 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-30 08:10:39,725 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50536.47, 'new_value': 51918.72}, {'field': 'dailyBillAmount', 'old_value': 50536.47, 'new_value': 51918.72}, {'field': 'amount', 'old_value': 34027.98, 'new_value': 34808.69}, {'field': 'count', 'old_value': 1734, 'new_value': 1778}, {'field': 'instoreAmount', 'old_value': 17848.52, 'new_value': 18127.52}, {'field': 'instoreCount', 'old_value': 686, 'new_value': 700}, {'field': 'onlineAmount', 'old_value': 17160.09, 'new_value': 17661.8}, {'field': 'onlineCount', 'old_value': 1048, 'new_value': 1078}]
2025-05-30 08:10:39,725 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:40,194 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-30 08:10:40,194 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 365403.0, 'new_value': 375284.07}, {'field': 'dailyBillAmount', 'old_value': 365403.0, 'new_value': 375284.07}, {'field': 'amount', 'old_value': 171601.33, 'new_value': 177229.45}, {'field': 'count', 'old_value': 724, 'new_value': 745}, {'field': 'instoreAmount', 'old_value': 177058.9, 'new_value': 182704.9}, {'field': 'instoreCount', 'old_value': 724, 'new_value': 745}]
2025-05-30 08:10:40,195 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:40,653 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-30 08:10:40,654 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 19656.37, 'new_value': 20377.99}, {'field': 'count', 'old_value': 177, 'new_value': 183}, {'field': 'instoreAmount', 'old_value': 19731.11, 'new_value': 20452.73}, {'field': 'instoreCount', 'old_value': 177, 'new_value': 183}]
2025-05-30 08:10:40,654 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:41,055 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-30 08:10:41,055 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 222818.86, 'new_value': 230665.03}, {'field': 'dailyBillAmount', 'old_value': 222818.86, 'new_value': 230665.03}, {'field': 'amount', 'old_value': 109955.45, 'new_value': 114214.8}, {'field': 'count', 'old_value': 4705, 'new_value': 4856}, {'field': 'instoreAmount', 'old_value': 112317.15, 'new_value': 116597.8}, {'field': 'instoreCount', 'old_value': 4705, 'new_value': 4856}]
2025-05-30 08:10:41,056 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:41,527 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-30 08:10:41,528 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 529274.0, 'new_value': 544009.0}, {'field': 'dailyBillAmount', 'old_value': 529274.0, 'new_value': 544009.0}, {'field': 'amount', 'old_value': 529274.0, 'new_value': 544009.0}, {'field': 'count', 'old_value': 666, 'new_value': 687}, {'field': 'instoreAmount', 'old_value': 529274.0, 'new_value': 544009.0}, {'field': 'instoreCount', 'old_value': 666, 'new_value': 687}]
2025-05-30 08:10:41,528 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:41,938 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-30 08:10:41,938 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228583.73, 'new_value': 231750.83}, {'field': 'dailyBillAmount', 'old_value': 228583.73, 'new_value': 231750.83}, {'field': 'amount', 'old_value': 133076.35, 'new_value': 134417.15}, {'field': 'count', 'old_value': 351, 'new_value': 355}, {'field': 'instoreAmount', 'old_value': 134492.95, 'new_value': 135833.75}, {'field': 'instoreCount', 'old_value': 351, 'new_value': 355}]
2025-05-30 08:10:41,939 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:42,445 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-30 08:10:42,445 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60056.0, 'new_value': 62176.0}, {'field': 'dailyBillAmount', 'old_value': 60056.0, 'new_value': 62176.0}, {'field': 'amount', 'old_value': 60056.0, 'new_value': 62176.0}, {'field': 'count', 'old_value': 1170, 'new_value': 1214}, {'field': 'instoreAmount', 'old_value': 60095.0, 'new_value': 62215.0}, {'field': 'instoreCount', 'old_value': 1170, 'new_value': 1214}]
2025-05-30 08:10:42,446 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:42,859 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-30 08:10:42,859 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 101138.02, 'new_value': 105231.04}, {'field': 'dailyBillAmount', 'old_value': 101138.02, 'new_value': 105231.04}, {'field': 'amount', 'old_value': 104319.78, 'new_value': 108482.57}, {'field': 'count', 'old_value': 5507, 'new_value': 5711}, {'field': 'instoreAmount', 'old_value': 50791.94, 'new_value': 53313.5}, {'field': 'instoreCount', 'old_value': 2549, 'new_value': 2650}, {'field': 'onlineAmount', 'old_value': 54909.27, 'new_value': 56572.0}, {'field': 'onlineCount', 'old_value': 2958, 'new_value': 3061}]
2025-05-30 08:10:42,860 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:43,366 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-30 08:10:43,366 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36461.74, 'new_value': 37360.95}, {'field': 'dailyBillAmount', 'old_value': 36461.74, 'new_value': 37360.95}, {'field': 'amount', 'old_value': 49307.29, 'new_value': 50491.9}, {'field': 'count', 'old_value': 1433, 'new_value': 1470}, {'field': 'instoreAmount', 'old_value': 45547.64, 'new_value': 46732.25}, {'field': 'instoreCount', 'old_value': 1282, 'new_value': 1319}]
2025-05-30 08:10:43,367 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:43,812 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-30 08:10:43,813 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'amount', 'old_value': 74330.04, 'new_value': 76975.4}, {'field': 'count', 'old_value': 2894, 'new_value': 3000}, {'field': 'instoreAmount', 'old_value': 48610.08, 'new_value': 50416.52}, {'field': 'instoreCount', 'old_value': 1734, 'new_value': 1801}, {'field': 'onlineAmount', 'old_value': 26042.79, 'new_value': 26881.71}, {'field': 'onlineCount', 'old_value': 1160, 'new_value': 1199}]
2025-05-30 08:10:43,813 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:44,250 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-30 08:10:44,250 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 70777.26, 'new_value': 73337.27}, {'field': 'count', 'old_value': 885, 'new_value': 918}, {'field': 'instoreAmount', 'old_value': 71306.16, 'new_value': 73866.17}, {'field': 'instoreCount', 'old_value': 885, 'new_value': 918}]
2025-05-30 08:10:44,251 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:44,697 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-30 08:10:44,698 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77218.7, 'new_value': 78652.8}, {'field': 'amount', 'old_value': 77218.2, 'new_value': 78652.3}, {'field': 'count', 'old_value': 1992, 'new_value': 2037}, {'field': 'instoreAmount', 'old_value': 78326.1, 'new_value': 79805.8}, {'field': 'instoreCount', 'old_value': 1992, 'new_value': 2037}]
2025-05-30 08:10:44,698 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:45,145 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-30 08:10:45,146 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 354870.42, 'new_value': 363542.42}, {'field': 'dailyBillAmount', 'old_value': 354870.42, 'new_value': 363542.42}, {'field': 'amount', 'old_value': 113684.82, 'new_value': 117874.82}, {'field': 'count', 'old_value': 393, 'new_value': 405}, {'field': 'instoreAmount', 'old_value': 113684.82, 'new_value': 117974.82}, {'field': 'instoreCount', 'old_value': 393, 'new_value': 405}]
2025-05-30 08:10:45,146 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:45,592 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-30 08:10:45,592 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93528.46, 'new_value': 95739.56}, {'field': 'dailyBillAmount', 'old_value': 93528.46, 'new_value': 95739.56}, {'field': 'amount', 'old_value': 94714.26, 'new_value': 96835.36}, {'field': 'count', 'old_value': 351, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 96848.89, 'new_value': 98969.99}, {'field': 'instoreCount', 'old_value': 351, 'new_value': 360}]
2025-05-30 08:10:45,593 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:46,076 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-30 08:10:46,076 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58510.0, 'new_value': 59562.0}, {'field': 'dailyBillAmount', 'old_value': 58510.0, 'new_value': 59562.0}, {'field': 'amount', 'old_value': 70963.0, 'new_value': 72891.0}, {'field': 'count', 'old_value': 134, 'new_value': 138}, {'field': 'instoreAmount', 'old_value': 76315.0, 'new_value': 78243.0}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 138}]
2025-05-30 08:10:46,077 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:46,510 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-30 08:10:46,511 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104839.35, 'new_value': 109356.45}, {'field': 'dailyBillAmount', 'old_value': 97452.85, 'new_value': 101969.95}, {'field': 'amount', 'old_value': 104836.75, 'new_value': 109353.85}, {'field': 'count', 'old_value': 325, 'new_value': 343}, {'field': 'instoreAmount', 'old_value': 118124.85, 'new_value': 123152.45}, {'field': 'instoreCount', 'old_value': 325, 'new_value': 343}]
2025-05-30 08:10:46,511 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:46,999 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-30 08:10:46,999 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 130446.4, 'new_value': 133300.0}, {'field': 'dailyBillAmount', 'old_value': 130290.8, 'new_value': 133144.4}, {'field': 'amount', 'old_value': 74444.93000000001, 'new_value': 76311.48}, {'field': 'count', 'old_value': 2014, 'new_value': 2064}, {'field': 'instoreAmount', 'old_value': 64569.71, 'new_value': 66001.65}, {'field': 'instoreCount', 'old_value': 1697, 'new_value': 1735}, {'field': 'onlineAmount', 'old_value': 11012.43, 'new_value': 11447.039999999999}, {'field': 'onlineCount', 'old_value': 317, 'new_value': 329}]
2025-05-30 08:10:47,000 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:47,432 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-30 08:10:47,433 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 193586.56, 'new_value': 199763.45}, {'field': 'dailyBillAmount', 'old_value': 188183.3, 'new_value': 194281.05}, {'field': 'amount', 'old_value': 193586.36000000002, 'new_value': 199763.25}, {'field': 'count', 'old_value': 2387, 'new_value': 2472}, {'field': 'instoreAmount', 'old_value': 185010.35, 'new_value': 190855.35}, {'field': 'instoreCount', 'old_value': 2286, 'new_value': 2366}, {'field': 'onlineAmount', 'old_value': 8635.45, 'new_value': 8967.34}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 106}]
2025-05-30 08:10:47,433 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:47,923 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-30 08:10:47,924 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82912.25, 'new_value': 83999.17}, {'field': 'dailyBillAmount', 'old_value': 82912.25, 'new_value': 83999.17}, {'field': 'amount', 'old_value': 111402.54, 'new_value': 112499.9}, {'field': 'count', 'old_value': 505, 'new_value': 509}, {'field': 'instoreAmount', 'old_value': 107443.08, 'new_value': 108498.14}, {'field': 'instoreCount', 'old_value': 451, 'new_value': 454}, {'field': 'onlineAmount', 'old_value': 4117.46, 'new_value': 4159.76}, {'field': 'onlineCount', 'old_value': 54, 'new_value': 55}]
2025-05-30 08:10:47,925 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:48,368 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-30 08:10:48,368 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 212173.3, 'new_value': 219849.0}, {'field': 'dailyBillAmount', 'old_value': 212173.3, 'new_value': 219849.0}, {'field': 'amount', 'old_value': 217806.9, 'new_value': 225235.6}, {'field': 'count', 'old_value': 802, 'new_value': 825}, {'field': 'instoreAmount', 'old_value': 221567.8, 'new_value': 228996.5}, {'field': 'instoreCount', 'old_value': 802, 'new_value': 825}]
2025-05-30 08:10:48,369 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:48,833 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-30 08:10:48,833 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48792.0, 'new_value': 52171.0}, {'field': 'dailyBillAmount', 'old_value': 48792.0, 'new_value': 52171.0}, {'field': 'amount', 'old_value': 45990.0, 'new_value': 49369.0}, {'field': 'count', 'old_value': 115, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 46583.0, 'new_value': 50211.0}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 120}]
2025-05-30 08:10:48,834 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:49,244 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-30 08:10:49,244 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 22278.76, 'new_value': 22560.16}, {'field': 'count', 'old_value': 39, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 22560.16, 'new_value': 22841.56}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 40}]
2025-05-30 08:10:49,245 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:49,811 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-30 08:10:49,811 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25859.56, 'new_value': 29073.13}, {'field': 'dailyBillAmount', 'old_value': 25859.56, 'new_value': 29073.13}, {'field': 'amount', 'old_value': 44194.04, 'new_value': 47167.64}, {'field': 'count', 'old_value': 247, 'new_value': 264}, {'field': 'instoreAmount', 'old_value': 42022.200000000004, 'new_value': 44969.200000000004}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 206}, {'field': 'onlineAmount', 'old_value': 3053.05, 'new_value': 3079.65}, {'field': 'onlineCount', 'old_value': 57, 'new_value': 58}]
2025-05-30 08:10:49,812 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:50,357 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-30 08:10:50,357 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55106.71, 'new_value': 57510.74}, {'field': 'amount', 'old_value': 55105.07, 'new_value': 57509.1}, {'field': 'count', 'old_value': 2758, 'new_value': 2872}, {'field': 'instoreAmount', 'old_value': 63269.11, 'new_value': 65726.73}, {'field': 'instoreCount', 'old_value': 2758, 'new_value': 2872}]
2025-05-30 08:10:50,358 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:50,815 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-30 08:10:50,815 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149255.32, 'new_value': 152679.92}, {'field': 'dailyBillAmount', 'old_value': 149255.32, 'new_value': 152679.92}, {'field': 'amount', 'old_value': 118982.2, 'new_value': 121218.2}, {'field': 'count', 'old_value': 485, 'new_value': 495}, {'field': 'instoreAmount', 'old_value': 118929.9, 'new_value': 121165.9}, {'field': 'instoreCount', 'old_value': 482, 'new_value': 492}]
2025-05-30 08:10:50,816 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:51,295 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-30 08:10:51,296 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 416684.82, 'new_value': 430120.92}, {'field': 'dailyBillAmount', 'old_value': 416684.82, 'new_value': 430120.92}, {'field': 'amount', 'old_value': 239786.85, 'new_value': 244906.41999999998}, {'field': 'count', 'old_value': 2723, 'new_value': 2783}, {'field': 'instoreAmount', 'old_value': 110042.51, 'new_value': 112147.41}, {'field': 'instoreCount', 'old_value': 1197, 'new_value': 1219}, {'field': 'onlineAmount', 'old_value': 129747.81, 'new_value': 132762.48}, {'field': 'onlineCount', 'old_value': 1526, 'new_value': 1564}]
2025-05-30 08:10:51,296 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:51,740 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-30 08:10:51,741 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 232130.33, 'new_value': 240977.07}, {'field': 'dailyBillAmount', 'old_value': 232130.33, 'new_value': 240977.07}, {'field': 'amount', 'old_value': 249417.1, 'new_value': 259761.3}, {'field': 'count', 'old_value': 1530, 'new_value': 1584}, {'field': 'instoreAmount', 'old_value': 250197.0, 'new_value': 260541.19999999998}, {'field': 'instoreCount', 'old_value': 1530, 'new_value': 1584}]
2025-05-30 08:10:51,742 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:52,187 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-30 08:10:52,188 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172139.41, 'new_value': 178565.23}, {'field': 'dailyBillAmount', 'old_value': 172139.41, 'new_value': 178565.23}, {'field': 'amount', 'old_value': 108568.36, 'new_value': 112220.23}, {'field': 'count', 'old_value': 1212, 'new_value': 1247}, {'field': 'instoreAmount', 'old_value': 99261.32, 'new_value': 102738.19}, {'field': 'instoreCount', 'old_value': 880, 'new_value': 908}, {'field': 'onlineAmount', 'old_value': 11098.21, 'new_value': 11273.21}, {'field': 'onlineCount', 'old_value': 332, 'new_value': 339}]
2025-05-30 08:10:52,188 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:52,660 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-30 08:10:52,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2893.05, 'new_value': 2929.8}, {'field': 'count', 'old_value': 35, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 2893.05, 'new_value': 2929.8}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 36}]
2025-05-30 08:10:52,662 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:53,152 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-30 08:10:53,152 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 219371.24, 'new_value': 225595.74}, {'field': 'dailyBillAmount', 'old_value': 214565.49, 'new_value': 220789.99}, {'field': 'amount', 'old_value': 219371.24, 'new_value': 225595.74}, {'field': 'count', 'old_value': 925, 'new_value': 957}, {'field': 'instoreAmount', 'old_value': 219371.24, 'new_value': 225595.74}, {'field': 'instoreCount', 'old_value': 925, 'new_value': 957}]
2025-05-30 08:10:53,153 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:53,588 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-30 08:10:53,588 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27321.02, 'new_value': 28571.39}, {'field': 'dailyBillAmount', 'old_value': 27321.02, 'new_value': 28571.39}, {'field': 'amount', 'old_value': 32827.020000000004, 'new_value': 34147.090000000004}, {'field': 'count', 'old_value': 968, 'new_value': 1013}, {'field': 'instoreAmount', 'old_value': 32846.82, 'new_value': 34166.89}, {'field': 'instoreCount', 'old_value': 968, 'new_value': 1013}]
2025-05-30 08:10:53,589 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:54,028 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-30 08:10:54,029 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 333527.8, 'new_value': 346233.8}, {'field': 'amount', 'old_value': 333527.8, 'new_value': 346233.8}, {'field': 'count', 'old_value': 521, 'new_value': 546}, {'field': 'instoreAmount', 'old_value': 333527.8, 'new_value': 346233.8}, {'field': 'instoreCount', 'old_value': 521, 'new_value': 546}]
2025-05-30 08:10:54,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:54,601 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-30 08:10:54,601 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48316.29, 'new_value': 52501.1}, {'field': 'amount', 'old_value': 48316.29, 'new_value': 52501.1}, {'field': 'count', 'old_value': 407, 'new_value': 422}, {'field': 'instoreAmount', 'old_value': 48316.29, 'new_value': 52501.1}, {'field': 'instoreCount', 'old_value': 407, 'new_value': 422}]
2025-05-30 08:10:54,602 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:55,034 - INFO - 更新表单数据成功: FINST-OPC666D1DEUV15H0FNQXAAZ3JUDL2OCUYB9BM41
2025-05-30 08:10:55,035 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69643.8, 'new_value': 79972.59999999999}, {'field': 'dailyBillAmount', 'old_value': 69643.8, 'new_value': 79972.59999999999}, {'field': 'amount', 'old_value': 69643.8, 'new_value': 79972.59999999999}, {'field': 'count', 'old_value': 412, 'new_value': 475}, {'field': 'instoreAmount', 'old_value': 69643.8, 'new_value': 79972.59999999999}, {'field': 'instoreCount', 'old_value': 412, 'new_value': 475}]
2025-05-30 08:10:55,036 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:55,464 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-30 08:10:55,464 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 56358.19, 'new_value': 57307.79}, {'field': 'count', 'old_value': 573, 'new_value': 588}, {'field': 'instoreAmount', 'old_value': 56358.19, 'new_value': 57307.79}, {'field': 'instoreCount', 'old_value': 573, 'new_value': 588}]
2025-05-30 08:10:55,465 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:55,933 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-30 08:10:55,933 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 491769.48, 'new_value': 509628.48}, {'field': 'dailyBillAmount', 'old_value': 491769.48, 'new_value': 509628.48}, {'field': 'amount', 'old_value': 498940.48, 'new_value': 516799.48}, {'field': 'count', 'old_value': 1601, 'new_value': 1660}, {'field': 'instoreAmount', 'old_value': 498940.48, 'new_value': 516799.48}, {'field': 'instoreCount', 'old_value': 1601, 'new_value': 1660}]
2025-05-30 08:10:55,934 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:56,393 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-30 08:10:56,393 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1141383.25, 'new_value': 1184396.04}, {'field': 'count', 'old_value': 1436, 'new_value': 1492}, {'field': 'instoreAmount', 'old_value': 1141383.42, 'new_value': 1184396.21}, {'field': 'instoreCount', 'old_value': 1436, 'new_value': 1492}]
2025-05-30 08:10:56,394 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:56,886 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-30 08:10:56,886 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162687.9, 'new_value': 165126.9}, {'field': 'dailyBillAmount', 'old_value': 162687.9, 'new_value': 165126.9}, {'field': 'amount', 'old_value': 34161.5, 'new_value': 34329.5}, {'field': 'count', 'old_value': 132, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 34163.0, 'new_value': 34331.0}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 133}]
2025-05-30 08:10:56,892 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:57,339 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-30 08:10:57,339 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 208703.27, 'new_value': 210659.15}, {'field': 'amount', 'old_value': 208699.84, 'new_value': 210655.72}, {'field': 'count', 'old_value': 2208, 'new_value': 2234}, {'field': 'instoreAmount', 'old_value': 131948.37, 'new_value': 132683.16}, {'field': 'instoreCount', 'old_value': 1222, 'new_value': 1231}, {'field': 'onlineAmount', 'old_value': 82310.46, 'new_value': 83611.45}, {'field': 'onlineCount', 'old_value': 986, 'new_value': 1003}]
2025-05-30 08:10:57,340 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:57,850 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-30 08:10:57,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 367231.67, 'new_value': 377642.98}, {'field': 'dailyBillAmount', 'old_value': 367231.67, 'new_value': 377642.98}, {'field': 'amount', 'old_value': 31656.91, 'new_value': 32360.81}, {'field': 'count', 'old_value': 967, 'new_value': 988}, {'field': 'instoreAmount', 'old_value': 36994.18, 'new_value': 38037.58}, {'field': 'instoreCount', 'old_value': 967, 'new_value': 988}]
2025-05-30 08:10:57,851 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:58,375 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-30 08:10:58,375 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 327331.23, 'new_value': 335795.18}, {'field': 'dailyBillAmount', 'old_value': 327331.23, 'new_value': 335795.18}, {'field': 'amount', 'old_value': 171293.69, 'new_value': 173213.36}, {'field': 'count', 'old_value': 3808, 'new_value': 3855}, {'field': 'instoreAmount', 'old_value': 141664.13, 'new_value': 143727.8}, {'field': 'instoreCount', 'old_value': 3149, 'new_value': 3194}, {'field': 'onlineAmount', 'old_value': 31877.47, 'new_value': 32018.87}, {'field': 'onlineCount', 'old_value': 659, 'new_value': 661}]
2025-05-30 08:10:58,376 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:58,800 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-30 08:10:58,800 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 296832.9, 'new_value': 300027.9}, {'field': 'amount', 'old_value': 296830.4, 'new_value': 300025.4}, {'field': 'count', 'old_value': 1175, 'new_value': 1190}, {'field': 'instoreAmount', 'old_value': 301634.7, 'new_value': 305377.5}, {'field': 'instoreCount', 'old_value': 1175, 'new_value': 1190}]
2025-05-30 08:10:58,801 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:59,309 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-30 08:10:59,309 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 463862.11, 'new_value': 468077.55}, {'field': 'count', 'old_value': 8737, 'new_value': 8820}, {'field': 'instoreAmount', 'old_value': 431819.71, 'new_value': 435468.58}, {'field': 'instoreCount', 'old_value': 8118, 'new_value': 8190}, {'field': 'onlineAmount', 'old_value': 33816.12, 'new_value': 34382.69}, {'field': 'onlineCount', 'old_value': 619, 'new_value': 630}]
2025-05-30 08:10:59,310 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:10:59,754 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-30 08:10:59,755 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 439566.97000000003, 'new_value': 455049.67}, {'field': 'amount', 'old_value': 407281.6, 'new_value': 422764.3}, {'field': 'count', 'old_value': 9553, 'new_value': 9939}, {'field': 'instoreAmount', 'old_value': 307072.6, 'new_value': 312244.0}, {'field': 'instoreCount', 'old_value': 6620, 'new_value': 6730}, {'field': 'onlineAmount', 'old_value': 100368.9, 'new_value': 110680.2}, {'field': 'onlineCount', 'old_value': 2933, 'new_value': 3209}]
2025-05-30 08:10:59,756 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:00,203 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-30 08:11:00,203 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30373.579999999998, 'new_value': 30365.579999999998}, {'field': 'count', 'old_value': 844, 'new_value': 848}, {'field': 'instoreAmount', 'old_value': 2665.0, 'new_value': 2720.0}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 45142.729999999996, 'new_value': 45175.729999999996}, {'field': 'onlineCount', 'old_value': 801, 'new_value': 803}]
2025-05-30 08:11:00,204 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:00,658 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-30 08:11:00,659 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114983.87, 'new_value': 120395.92}, {'field': 'dailyBillAmount', 'old_value': 114983.87, 'new_value': 120395.92}, {'field': 'amount', 'old_value': 183724.38, 'new_value': 188812.68}, {'field': 'count', 'old_value': 12523, 'new_value': 12837}, {'field': 'instoreAmount', 'old_value': 147207.28, 'new_value': 150214.31}, {'field': 'instoreCount', 'old_value': 9749, 'new_value': 9919}, {'field': 'onlineAmount', 'old_value': 40715.090000000004, 'new_value': 42854.18}, {'field': 'onlineCount', 'old_value': 2774, 'new_value': 2918}]
2025-05-30 08:11:00,659 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:01,133 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-30 08:11:01,133 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287629.37, 'new_value': 293657.9}, {'field': 'dailyBillAmount', 'old_value': 287629.37, 'new_value': 293657.9}, {'field': 'amount', 'old_value': 279994.96, 'new_value': 284696.37}, {'field': 'count', 'old_value': 8235, 'new_value': 8366}, {'field': 'instoreAmount', 'old_value': 281696.77, 'new_value': 286495.48}, {'field': 'instoreCount', 'old_value': 8235, 'new_value': 8366}]
2025-05-30 08:11:01,134 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:01,596 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-30 08:11:01,596 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87041.31, 'new_value': 89303.74}, {'field': 'amount', 'old_value': 87038.03, 'new_value': 89300.46}, {'field': 'count', 'old_value': 4730, 'new_value': 4873}, {'field': 'instoreAmount', 'old_value': 47318.28, 'new_value': 48265.909999999996}, {'field': 'instoreCount', 'old_value': 2799, 'new_value': 2883}, {'field': 'onlineAmount', 'old_value': 39723.03, 'new_value': 41037.83}, {'field': 'onlineCount', 'old_value': 1931, 'new_value': 1990}]
2025-05-30 08:11:01,597 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:02,039 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-30 08:11:02,039 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 152271.43, 'new_value': 155949.53}, {'field': 'dailyBillAmount', 'old_value': 152271.43, 'new_value': 155949.53}, {'field': 'amount', 'old_value': 31323.670000000002, 'new_value': 31947.440000000002}, {'field': 'count', 'old_value': 1126, 'new_value': 1156}, {'field': 'instoreAmount', 'old_value': 32427.48, 'new_value': 33107.08}, {'field': 'instoreCount', 'old_value': 1126, 'new_value': 1156}]
2025-05-30 08:11:02,040 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:02,525 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-30 08:11:02,526 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 102711.15, 'new_value': 105082.41}, {'field': 'count', 'old_value': 5084, 'new_value': 5184}, {'field': 'instoreAmount', 'old_value': 21732.04, 'new_value': 22108.9}, {'field': 'instoreCount', 'old_value': 1546, 'new_value': 1557}, {'field': 'onlineAmount', 'old_value': 82574.84, 'new_value': 84766.24}, {'field': 'onlineCount', 'old_value': 3538, 'new_value': 3627}]
2025-05-30 08:11:02,526 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:03,020 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-30 08:11:03,020 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118583.14, 'new_value': 121688.42}, {'field': 'amount', 'old_value': 118581.73, 'new_value': 121687.01}, {'field': 'count', 'old_value': 3027, 'new_value': 3104}, {'field': 'instoreAmount', 'old_value': 112484.55, 'new_value': 115311.43}, {'field': 'instoreCount', 'old_value': 2936, 'new_value': 3008}, {'field': 'onlineAmount', 'old_value': 7174.01, 'new_value': 7452.41}, {'field': 'onlineCount', 'old_value': 91, 'new_value': 96}]
2025-05-30 08:11:03,021 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:03,480 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-30 08:11:03,481 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 184133.05, 'new_value': 193479.46}, {'field': 'count', 'old_value': 7652, 'new_value': 8022}, {'field': 'instoreAmount', 'old_value': 187612.15, 'new_value': 197213.01}, {'field': 'instoreCount', 'old_value': 7583, 'new_value': 7949}, {'field': 'onlineAmount', 'old_value': 2494.0099999999998, 'new_value': 2532.81}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 73}]
2025-05-30 08:11:03,481 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:03,976 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-30 08:11:03,977 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194436.65, 'new_value': 199376.06}, {'field': 'dailyBillAmount', 'old_value': 194436.65, 'new_value': 199376.06}, {'field': 'amount', 'old_value': 129666.84, 'new_value': 133652.19}, {'field': 'count', 'old_value': 10406, 'new_value': 10634}, {'field': 'instoreAmount', 'old_value': 9751.26, 'new_value': 9999.06}, {'field': 'instoreCount', 'old_value': 565, 'new_value': 587}, {'field': 'onlineAmount', 'old_value': 125425.84, 'new_value': 129289.39}, {'field': 'onlineCount', 'old_value': 9841, 'new_value': 10047}]
2025-05-30 08:11:03,977 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:04,453 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-30 08:11:04,454 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 173058.44, 'new_value': 176198.13999999998}, {'field': 'dailyBillAmount', 'old_value': 173058.44, 'new_value': 176198.13999999998}, {'field': 'amount', 'old_value': 148243.48, 'new_value': 152014.86}, {'field': 'count', 'old_value': 4870, 'new_value': 4957}, {'field': 'instoreAmount', 'old_value': 80323.6, 'new_value': 82532.28}, {'field': 'instoreCount', 'old_value': 3491, 'new_value': 3548}, {'field': 'onlineAmount', 'old_value': 77495.03, 'new_value': 79149.73}, {'field': 'onlineCount', 'old_value': 1379, 'new_value': 1409}]
2025-05-30 08:11:04,454 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:04,873 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-30 08:11:04,874 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 135501.45, 'new_value': 137281.45}, {'field': 'amount', 'old_value': 135500.92, 'new_value': 137280.92}, {'field': 'count', 'old_value': 94, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 135501.45, 'new_value': 137281.45}, {'field': 'instoreCount', 'old_value': 94, 'new_value': 95}]
2025-05-30 08:11:04,874 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:05,372 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-30 08:11:05,372 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63392.24, 'new_value': 65524.6}, {'field': 'dailyBillAmount', 'old_value': 63392.24, 'new_value': 65524.6}, {'field': 'amount', 'old_value': 83381.56999999999, 'new_value': 85915.44}, {'field': 'count', 'old_value': 3274, 'new_value': 3371}, {'field': 'instoreAmount', 'old_value': 26469.36, 'new_value': 27036.49}, {'field': 'instoreCount', 'old_value': 1133, 'new_value': 1163}, {'field': 'onlineAmount', 'old_value': 58051.38, 'new_value': 60018.12}, {'field': 'onlineCount', 'old_value': 2141, 'new_value': 2208}]
2025-05-30 08:11:05,373 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:05,825 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-30 08:11:05,825 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104066.01, 'new_value': 105464.26}, {'field': 'dailyBillAmount', 'old_value': 104066.01, 'new_value': 105464.26}, {'field': 'amount', 'old_value': 107364.93, 'new_value': 108854.11}, {'field': 'count', 'old_value': 3793, 'new_value': 3855}, {'field': 'instoreAmount', 'old_value': 107237.53, 'new_value': 108726.70999999999}, {'field': 'instoreCount', 'old_value': 3787, 'new_value': 3849}]
2025-05-30 08:11:05,826 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:06,264 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-30 08:11:06,265 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 366681.0, 'new_value': 369024.0}, {'field': 'count', 'old_value': 294, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 399716.0, 'new_value': 403095.0}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 297}]
2025-05-30 08:11:06,265 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:06,701 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-30 08:11:06,701 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 243632.35, 'new_value': 248304.15}, {'field': 'dailyBillAmount', 'old_value': 243632.35, 'new_value': 248304.15}, {'field': 'amount', 'old_value': 248019.71, 'new_value': 252701.51}, {'field': 'count', 'old_value': 490, 'new_value': 503}, {'field': 'instoreAmount', 'old_value': 251489.21, 'new_value': 256171.01}, {'field': 'instoreCount', 'old_value': 490, 'new_value': 503}]
2025-05-30 08:11:06,702 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:07,148 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-05-30 08:11:07,149 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53543.0, 'new_value': 54765.0}, {'field': 'amount', 'old_value': 53543.0, 'new_value': 54765.0}, {'field': 'count', 'old_value': 122, 'new_value': 126}, {'field': 'instoreAmount', 'old_value': 53543.0, 'new_value': 54765.0}, {'field': 'instoreCount', 'old_value': 122, 'new_value': 126}]
2025-05-30 08:11:07,149 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:07,735 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-30 08:11:07,735 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83580.0, 'new_value': 86124.0}, {'field': 'dailyBillAmount', 'old_value': 83580.0, 'new_value': 86124.0}]
2025-05-30 08:11:07,736 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:08,180 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-30 08:11:08,180 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80728.0, 'new_value': 83737.0}, {'field': 'dailyBillAmount', 'old_value': 63856.0, 'new_value': 66865.0}, {'field': 'amount', 'old_value': 63861.0, 'new_value': 66391.0}, {'field': 'count', 'old_value': 88, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 63861.0, 'new_value': 66391.0}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 92}]
2025-05-30 08:11:08,181 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:08,756 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-30 08:11:08,756 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73826.9, 'new_value': 75139.9}, {'field': 'amount', 'old_value': 73824.7, 'new_value': 75137.7}, {'field': 'count', 'old_value': 196, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 74315.6, 'new_value': 75628.6}, {'field': 'instoreCount', 'old_value': 196, 'new_value': 200}]
2025-05-30 08:11:08,757 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:09,203 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-30 08:11:09,203 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 667079.0, 'new_value': 682777.0}, {'field': 'dailyBillAmount', 'old_value': 667079.0, 'new_value': 682777.0}, {'field': 'amount', 'old_value': 701110.0, 'new_value': 716808.0}, {'field': 'count', 'old_value': 86, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 701110.0, 'new_value': 716808.0}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 88}]
2025-05-30 08:11:09,204 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:09,687 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-30 08:11:09,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104400.0, 'new_value': 106027.0}, {'field': 'amount', 'old_value': 104400.0, 'new_value': 106027.0}, {'field': 'count', 'old_value': 29, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 104400.0, 'new_value': 106027.0}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 30}]
2025-05-30 08:11:09,689 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:10,162 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-30 08:11:10,163 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28573.0, 'new_value': 29626.0}, {'field': 'amount', 'old_value': 28573.0, 'new_value': 29626.0}, {'field': 'count', 'old_value': 43, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 28573.0, 'new_value': 29626.0}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 44}]
2025-05-30 08:11:10,163 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:10,609 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-30 08:11:10,610 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73054.0, 'new_value': 73853.0}, {'field': 'amount', 'old_value': 73054.0, 'new_value': 73853.0}, {'field': 'count', 'old_value': 83, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 73054.0, 'new_value': 73853.0}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 84}]
2025-05-30 08:11:10,610 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:10,985 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-30 08:11:10,985 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 275223.0, 'new_value': 281164.1}, {'field': 'dailyBillAmount', 'old_value': 275223.0, 'new_value': 281164.1}, {'field': 'amount', 'old_value': 363292.1, 'new_value': 369233.2}, {'field': 'count', 'old_value': 453, 'new_value': 459}, {'field': 'instoreAmount', 'old_value': 377444.96, 'new_value': 383386.06}, {'field': 'instoreCount', 'old_value': 453, 'new_value': 459}]
2025-05-30 08:11:10,986 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:11,498 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-30 08:11:11,499 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115151.24, 'new_value': 117173.54}, {'field': 'dailyBillAmount', 'old_value': 115151.24, 'new_value': 117173.54}, {'field': 'amount', 'old_value': 55032.4, 'new_value': 56101.0}, {'field': 'count', 'old_value': 560, 'new_value': 570}, {'field': 'instoreAmount', 'old_value': 53693.38, 'new_value': 54633.08}, {'field': 'instoreCount', 'old_value': 497, 'new_value': 506}, {'field': 'onlineAmount', 'old_value': 3901.12, 'new_value': 4030.02}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 64}]
2025-05-30 08:11:11,499 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:11,944 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-30 08:11:11,945 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16031.0, 'new_value': 16328.0}, {'field': 'amount', 'old_value': 16031.0, 'new_value': 16328.0}, {'field': 'count', 'old_value': 42, 'new_value': 44}, {'field': 'instoreAmount', 'old_value': 16031.0, 'new_value': 16328.0}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 44}]
2025-05-30 08:11:11,945 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:12,412 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-30 08:11:12,413 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39557.0, 'new_value': 40577.0}, {'field': 'dailyBillAmount', 'old_value': 39557.0, 'new_value': 40577.0}, {'field': 'amount', 'old_value': 45640.0, 'new_value': 46660.0}, {'field': 'count', 'old_value': 143, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 45640.0, 'new_value': 46660.0}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 146}]
2025-05-30 08:11:12,414 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:12,873 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-30 08:11:12,873 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35956.7, 'new_value': 36858.7}, {'field': 'amount', 'old_value': 35956.7, 'new_value': 36858.7}, {'field': 'count', 'old_value': 219, 'new_value': 225}, {'field': 'instoreAmount', 'old_value': 36294.7, 'new_value': 37196.7}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 225}]
2025-05-30 08:11:12,874 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:13,410 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-30 08:11:13,410 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 8963.0, 'new_value': 9092.0}, {'field': 'dailyBillAmount', 'old_value': 8963.0, 'new_value': 9092.0}, {'field': 'amount', 'old_value': 43090.0, 'new_value': 43219.0}, {'field': 'count', 'old_value': 138, 'new_value': 139}, {'field': 'instoreAmount', 'old_value': 43865.0, 'new_value': 43994.0}, {'field': 'instoreCount', 'old_value': 138, 'new_value': 139}]
2025-05-30 08:11:13,411 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:13,858 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-30 08:11:13,858 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 865730.67, 'new_value': 879584.61}, {'field': 'dailyBillAmount', 'old_value': 865730.67, 'new_value': 879584.61}, {'field': 'amount', 'old_value': 53946.58, 'new_value': 55654.56}, {'field': 'count', 'old_value': 546, 'new_value': 565}, {'field': 'instoreAmount', 'old_value': 42870.77, 'new_value': 44081.77}, {'field': 'instoreCount', 'old_value': 387, 'new_value': 396}, {'field': 'onlineAmount', 'old_value': 12100.92, 'new_value': 12597.9}, {'field': 'onlineCount', 'old_value': 159, 'new_value': 169}]
2025-05-30 08:11:13,859 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:14,301 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-30 08:11:14,301 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84987.5, 'new_value': 86174.5}, {'field': 'amount', 'old_value': 84787.5, 'new_value': 85974.5}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 87334.0, 'new_value': 88521.0}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 114}]
2025-05-30 08:11:14,302 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:14,759 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-30 08:11:14,759 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17283.0, 'new_value': 17522.0}, {'field': 'amount', 'old_value': 17283.0, 'new_value': 17522.0}, {'field': 'count', 'old_value': 31, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 17283.0, 'new_value': 17522.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 33}]
2025-05-30 08:11:14,760 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:15,222 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-30 08:11:15,223 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27232.52, 'new_value': 27383.57}, {'field': 'amount', 'old_value': 27231.09, 'new_value': 27382.14}, {'field': 'count', 'old_value': 109, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 27232.52, 'new_value': 27383.57}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 110}]
2025-05-30 08:11:15,224 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:15,645 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-30 08:11:15,645 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48524.0, 'new_value': 51267.0}, {'field': 'dailyBillAmount', 'old_value': 48524.0, 'new_value': 51267.0}, {'field': 'amount', 'old_value': 48740.0, 'new_value': 51483.0}, {'field': 'count', 'old_value': 116, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 49986.0, 'new_value': 53400.0}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 121}]
2025-05-30 08:11:15,646 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:16,097 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-30 08:11:16,097 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 365526.08999999997, 'new_value': 367044.93}, {'field': 'dailyBillAmount', 'old_value': 337738.1, 'new_value': 339113.1}, {'field': 'amount', 'old_value': 362944.8, 'new_value': 364463.64}, {'field': 'count', 'old_value': 976, 'new_value': 988}, {'field': 'instoreAmount', 'old_value': 366992.04, 'new_value': 368510.88}, {'field': 'instoreCount', 'old_value': 976, 'new_value': 988}]
2025-05-30 08:11:16,098 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:16,585 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-30 08:11:16,585 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74409.0, 'new_value': 74766.0}, {'field': 'amount', 'old_value': 74409.0, 'new_value': 74766.0}, {'field': 'count', 'old_value': 320, 'new_value': 322}, {'field': 'instoreAmount', 'old_value': 75511.0, 'new_value': 75868.0}, {'field': 'instoreCount', 'old_value': 320, 'new_value': 322}]
2025-05-30 08:11:16,586 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:17,030 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-30 08:11:17,030 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 113764.79, 'new_value': 117299.79}, {'field': 'dailyBillAmount', 'old_value': 113764.79, 'new_value': 117299.79}, {'field': 'amount', 'old_value': 116711.31, 'new_value': 120246.31}, {'field': 'count', 'old_value': 719, 'new_value': 743}, {'field': 'instoreAmount', 'old_value': 116711.31, 'new_value': 120246.31}, {'field': 'instoreCount', 'old_value': 719, 'new_value': 743}]
2025-05-30 08:11:17,031 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:17,493 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-30 08:11:17,494 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 110965.43, 'new_value': 112566.47}, {'field': 'dailyBillAmount', 'old_value': 110965.43, 'new_value': 112566.47}, {'field': 'amount', 'old_value': 38619.42, 'new_value': 39129.62}, {'field': 'count', 'old_value': 3820, 'new_value': 3886}, {'field': 'instoreAmount', 'old_value': 41113.21, 'new_value': 41643.409999999996}, {'field': 'instoreCount', 'old_value': 3820, 'new_value': 3886}]
2025-05-30 08:11:17,495 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:17,941 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-30 08:11:17,942 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 662946.9, 'new_value': 679009.0599999999}, {'field': 'dailyBillAmount', 'old_value': 662946.9, 'new_value': 679009.0599999999}, {'field': 'amount', 'old_value': 676551.27, 'new_value': 692531.74}, {'field': 'count', 'old_value': 6907, 'new_value': 7147}, {'field': 'instoreAmount', 'old_value': 511987.39, 'new_value': 522406.8}, {'field': 'instoreCount', 'old_value': 2604, 'new_value': 2666}, {'field': 'onlineAmount', 'old_value': 170281.56, 'new_value': 175929.82}, {'field': 'onlineCount', 'old_value': 4303, 'new_value': 4481}]
2025-05-30 08:11:17,942 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:18,380 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-30 08:11:18,380 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 205394.78, 'new_value': 207922.68}, {'field': 'amount', 'old_value': 205394.78, 'new_value': 207922.68}, {'field': 'count', 'old_value': 1387, 'new_value': 1402}, {'field': 'instoreAmount', 'old_value': 205829.78, 'new_value': 208357.68}, {'field': 'instoreCount', 'old_value': 1387, 'new_value': 1402}]
2025-05-30 08:11:18,381 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:18,866 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMP71
2025-05-30 08:11:18,867 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 83132.39, 'new_value': 351491.77}, {'field': 'dailyBillAmount', 'old_value': 83132.39, 'new_value': 351491.77}, {'field': 'amount', 'old_value': 182780.55, 'new_value': 196095.24}, {'field': 'count', 'old_value': 7602, 'new_value': 8228}, {'field': 'instoreAmount', 'old_value': 71059.18, 'new_value': 74295.26}, {'field': 'instoreCount', 'old_value': 3169, 'new_value': 3293}, {'field': 'onlineAmount', 'old_value': 114687.34, 'new_value': 125091.45}, {'field': 'onlineCount', 'old_value': 4433, 'new_value': 4935}]
2025-05-30 08:11:18,867 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:19,304 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-30 08:11:19,304 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99378.28, 'new_value': 101789.98}, {'field': 'dailyBillAmount', 'old_value': 99378.28, 'new_value': 101789.98}, {'field': 'amount', 'old_value': 124039.11, 'new_value': 126838.36}, {'field': 'count', 'old_value': 5856, 'new_value': 5969}, {'field': 'instoreAmount', 'old_value': 62439.02, 'new_value': 63064.17}, {'field': 'instoreCount', 'old_value': 3311, 'new_value': 3340}, {'field': 'onlineAmount', 'old_value': 62923.24, 'new_value': 65109.34}, {'field': 'onlineCount', 'old_value': 2545, 'new_value': 2629}]
2025-05-30 08:11:19,305 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:19,710 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-30 08:11:19,711 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82687.0, 'new_value': 86733.0}, {'field': 'amount', 'old_value': 82687.0, 'new_value': 86733.0}, {'field': 'count', 'old_value': 39, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 82687.0, 'new_value': 86733.0}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 41}]
2025-05-30 08:11:19,711 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:20,245 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-30 08:11:20,246 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127581.66, 'new_value': 129034.11}, {'field': 'dailyBillAmount', 'old_value': 127581.66, 'new_value': 129034.11}, {'field': 'amount', 'old_value': 61728.36, 'new_value': 62533.76}, {'field': 'count', 'old_value': 4420, 'new_value': 4474}, {'field': 'instoreAmount', 'old_value': 8350.04, 'new_value': 8406.84}, {'field': 'instoreCount', 'old_value': 367, 'new_value': 373}, {'field': 'onlineAmount', 'old_value': 53378.32, 'new_value': 54126.92}, {'field': 'onlineCount', 'old_value': 4053, 'new_value': 4101}]
2025-05-30 08:11:20,247 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:20,700 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-30 08:11:20,701 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 376204.57, 'new_value': 386990.68}, {'field': 'dailyBillAmount', 'old_value': 376204.57, 'new_value': 386990.68}, {'field': 'amount', 'old_value': 353022.88, 'new_value': 364341.66}, {'field': 'count', 'old_value': 3157, 'new_value': 3296}, {'field': 'instoreAmount', 'old_value': 256677.3, 'new_value': 265342.38}, {'field': 'instoreCount', 'old_value': 1344, 'new_value': 1425}, {'field': 'onlineAmount', 'old_value': 96349.08, 'new_value': 99002.78}, {'field': 'onlineCount', 'old_value': 1813, 'new_value': 1871}]
2025-05-30 08:11:20,701 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:21,193 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-30 08:11:21,193 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 423707.13, 'new_value': 432175.85}, {'field': 'dailyBillAmount', 'old_value': 423707.13, 'new_value': 432175.85}, {'field': 'amount', 'old_value': 430270.27, 'new_value': 436822.97}, {'field': 'count', 'old_value': 2625, 'new_value': 2665}, {'field': 'instoreAmount', 'old_value': 393229.57, 'new_value': 399782.27}, {'field': 'instoreCount', 'old_value': 2230, 'new_value': 2270}]
2025-05-30 08:11:21,194 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:21,748 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-30 08:11:21,749 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1074485.26, 'new_value': 1099450.02}, {'field': 'dailyBillAmount', 'old_value': 1074485.26, 'new_value': 1099450.02}, {'field': 'amount', 'old_value': 1183149.08, 'new_value': 1209745.82}, {'field': 'count', 'old_value': 6707, 'new_value': 6920}, {'field': 'instoreAmount', 'old_value': 886443.13, 'new_value': 904903.13}, {'field': 'instoreCount', 'old_value': 3599, 'new_value': 3687}, {'field': 'onlineAmount', 'old_value': 305978.02, 'new_value': 314469.46}, {'field': 'onlineCount', 'old_value': 3108, 'new_value': 3233}]
2025-05-30 08:11:21,749 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:22,195 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-30 08:11:22,195 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 344581.87, 'new_value': 348468.89}, {'field': 'dailyBillAmount', 'old_value': 344581.87, 'new_value': 348468.89}, {'field': 'amount', 'old_value': 490133.01, 'new_value': 495703.45}, {'field': 'count', 'old_value': 2337, 'new_value': 2379}, {'field': 'instoreAmount', 'old_value': 459008.06, 'new_value': 464003.1}, {'field': 'instoreCount', 'old_value': 1852, 'new_value': 1878}, {'field': 'onlineAmount', 'old_value': 31885.15, 'new_value': 32460.55}, {'field': 'onlineCount', 'old_value': 485, 'new_value': 501}]
2025-05-30 08:11:22,196 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:22,614 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-30 08:11:22,614 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 404939.01, 'new_value': 410713.44}, {'field': 'dailyBillAmount', 'old_value': 404939.01, 'new_value': 410713.44}, {'field': 'amount', 'old_value': 379689.1, 'new_value': 384490.4}, {'field': 'count', 'old_value': 1719, 'new_value': 1749}, {'field': 'instoreAmount', 'old_value': 386376.7, 'new_value': 391616.9}, {'field': 'instoreCount', 'old_value': 1719, 'new_value': 1749}]
2025-05-30 08:11:22,615 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:23,066 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-30 08:11:23,067 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 853467.12, 'new_value': 870699.02}, {'field': 'amount', 'old_value': 853466.02, 'new_value': 870697.92}, {'field': 'count', 'old_value': 6935, 'new_value': 7101}, {'field': 'instoreAmount', 'old_value': 853467.12, 'new_value': 870699.02}, {'field': 'instoreCount', 'old_value': 6935, 'new_value': 7101}]
2025-05-30 08:11:23,067 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:23,570 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-30 08:11:23,570 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 715299.47, 'new_value': 776122.04}, {'field': 'dailyBillAmount', 'old_value': 715299.47, 'new_value': 776122.04}, {'field': 'amount', 'old_value': 881711.08, 'new_value': 904984.39}, {'field': 'count', 'old_value': 6163, 'new_value': 6355}, {'field': 'instoreAmount', 'old_value': 480341.0, 'new_value': 489999.10000000003}, {'field': 'instoreCount', 'old_value': 2531, 'new_value': 2595}, {'field': 'onlineAmount', 'old_value': 412498.2, 'new_value': 427030.9}, {'field': 'onlineCount', 'old_value': 3632, 'new_value': 3760}]
2025-05-30 08:11:23,571 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:24,059 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-30 08:11:24,059 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 448021.29, 'new_value': 457442.29}, {'field': 'dailyBillAmount', 'old_value': 448021.29, 'new_value': 457442.29}, {'field': 'amount', 'old_value': 515901.66000000003, 'new_value': 524245.89}, {'field': 'count', 'old_value': 5768, 'new_value': 5901}, {'field': 'instoreAmount', 'old_value': 349668.12, 'new_value': 352448.02}, {'field': 'instoreCount', 'old_value': 2424, 'new_value': 2451}, {'field': 'onlineAmount', 'old_value': 168442.4, 'new_value': 174095.18}, {'field': 'onlineCount', 'old_value': 3344, 'new_value': 3450}]
2025-05-30 08:11:24,060 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:24,525 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-30 08:11:24,526 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 563301.33, 'new_value': 576846.48}, {'field': 'dailyBillAmount', 'old_value': 563301.33, 'new_value': 576846.48}, {'field': 'amount', 'old_value': 570964.42, 'new_value': 584238.12}, {'field': 'count', 'old_value': 5469, 'new_value': 5646}, {'field': 'instoreAmount', 'old_value': 496919.94, 'new_value': 506872.21}, {'field': 'instoreCount', 'old_value': 2895, 'new_value': 2960}, {'field': 'onlineAmount', 'old_value': 75189.65, 'new_value': 78511.08}, {'field': 'onlineCount', 'old_value': 2574, 'new_value': 2686}]
2025-05-30 08:11:24,527 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:24,994 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-30 08:11:24,995 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129345.8, 'new_value': 134471.8}, {'field': 'amount', 'old_value': 129345.3, 'new_value': 134471.3}, {'field': 'count', 'old_value': 615, 'new_value': 624}, {'field': 'instoreAmount', 'old_value': 129345.8, 'new_value': 134471.8}, {'field': 'instoreCount', 'old_value': 615, 'new_value': 624}]
2025-05-30 08:11:24,995 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:25,444 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-30 08:11:25,444 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 375308.88, 'new_value': 387736.95}, {'field': 'dailyBillAmount', 'old_value': 375308.88, 'new_value': 387736.95}, {'field': 'amount', 'old_value': -297523.28, 'new_value': -302468.48}, {'field': 'count', 'old_value': 1027, 'new_value': 1044}, {'field': 'instoreAmount', 'old_value': 7486.0, 'new_value': 7506.0}, {'field': 'instoreCount', 'old_value': 341, 'new_value': 343}, {'field': 'onlineAmount', 'old_value': 21139.57, 'new_value': 21568.37}, {'field': 'onlineCount', 'old_value': 686, 'new_value': 701}]
2025-05-30 08:11:25,445 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:25,847 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-30 08:11:25,847 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 661132.66, 'new_value': 680299.61}, {'field': 'dailyBillAmount', 'old_value': 661132.66, 'new_value': 680299.61}, {'field': 'amount', 'old_value': 482847.27, 'new_value': 492345.03}, {'field': 'count', 'old_value': 2043, 'new_value': 2085}, {'field': 'instoreAmount', 'old_value': 482847.27, 'new_value': 492345.03}, {'field': 'instoreCount', 'old_value': 2043, 'new_value': 2085}]
2025-05-30 08:11:25,848 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:26,280 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-30 08:11:26,281 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 436308.21, 'new_value': 452439.17}, {'field': 'dailyBillAmount', 'old_value': 436308.21, 'new_value': 452439.17}, {'field': 'amount', 'old_value': 177963.7, 'new_value': 183258.2}, {'field': 'count', 'old_value': 732, 'new_value': 744}, {'field': 'instoreAmount', 'old_value': 183827.1, 'new_value': 188823.0}, {'field': 'instoreCount', 'old_value': 707, 'new_value': 718}, {'field': 'onlineAmount', 'old_value': 1751.8, 'new_value': 2050.4}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 26}]
2025-05-30 08:11:26,281 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:26,768 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-30 08:11:26,768 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 329686.03, 'new_value': 336600.18}, {'field': 'dailyBillAmount', 'old_value': 329686.03, 'new_value': 336600.18}, {'field': 'amount', 'old_value': 319611.97000000003, 'new_value': 326223.42}, {'field': 'count', 'old_value': 2182, 'new_value': 2245}, {'field': 'instoreAmount', 'old_value': 299416.76, 'new_value': 305023.26}, {'field': 'instoreCount', 'old_value': 1626, 'new_value': 1654}, {'field': 'onlineAmount', 'old_value': 20359.36, 'new_value': 21364.31}, {'field': 'onlineCount', 'old_value': 556, 'new_value': 591}]
2025-05-30 08:11:26,769 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:27,231 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-30 08:11:27,232 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 359501.25, 'new_value': 366921.42}, {'field': 'dailyBillAmount', 'old_value': 359501.25, 'new_value': 366921.42}, {'field': 'amount', 'old_value': 153691.4, 'new_value': 157751.32}, {'field': 'count', 'old_value': 2633, 'new_value': 2735}, {'field': 'instoreAmount', 'old_value': 87689.78, 'new_value': 90090.28}, {'field': 'instoreCount', 'old_value': 691, 'new_value': 725}, {'field': 'onlineAmount', 'old_value': 66004.93, 'new_value': 67664.35}, {'field': 'onlineCount', 'old_value': 1942, 'new_value': 2010}]
2025-05-30 08:11:27,233 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:27,636 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-30 08:11:27,637 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 58364.0, 'new_value': 60391.0}, {'field': 'count', 'old_value': 33, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 58364.0, 'new_value': 60391.0}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 35}]
2025-05-30 08:11:27,637 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:28,040 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-30 08:11:28,040 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 153979.21, 'new_value': 159842.32}, {'field': 'amount', 'old_value': 153965.7, 'new_value': 159828.81}, {'field': 'count', 'old_value': 7148, 'new_value': 7427}, {'field': 'instoreAmount', 'old_value': 53860.19, 'new_value': 55083.5}, {'field': 'instoreCount', 'old_value': 2135, 'new_value': 2196}, {'field': 'onlineAmount', 'old_value': 107814.7, 'new_value': 112611.2}, {'field': 'onlineCount', 'old_value': 5013, 'new_value': 5231}]
2025-05-30 08:11:28,041 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:28,548 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-30 08:11:28,549 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47570.9, 'new_value': 48317.9}, {'field': 'amount', 'old_value': 47570.9, 'new_value': 48317.9}, {'field': 'count', 'old_value': 211, 'new_value': 215}, {'field': 'instoreAmount', 'old_value': 47570.9, 'new_value': 48317.9}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 215}]
2025-05-30 08:11:28,549 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:28,967 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-30 08:11:28,967 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 429445.84, 'new_value': 440580.74}, {'field': 'dailyBillAmount', 'old_value': 429445.84, 'new_value': 440580.74}, {'field': 'amount', 'old_value': 171695.9, 'new_value': 177287.7}, {'field': 'count', 'old_value': 3233, 'new_value': 3340}, {'field': 'instoreAmount', 'old_value': 172991.8, 'new_value': 178589.5}, {'field': 'instoreCount', 'old_value': 3233, 'new_value': 3340}]
2025-05-30 08:11:28,968 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:29,517 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-30 08:11:29,517 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172371.36000000002, 'new_value': 175392.44}, {'field': 'amount', 'old_value': 172370.16, 'new_value': 175391.24}, {'field': 'count', 'old_value': 4131, 'new_value': 4205}, {'field': 'instoreAmount', 'old_value': 172628.84, 'new_value': 175649.92}, {'field': 'instoreCount', 'old_value': 4131, 'new_value': 4205}]
2025-05-30 08:11:29,518 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:29,990 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-30 08:11:29,991 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34429.12, 'new_value': 35383.9}, {'field': 'amount', 'old_value': 34422.49, 'new_value': 35377.27}, {'field': 'count', 'old_value': 2155, 'new_value': 2230}, {'field': 'instoreAmount', 'old_value': 17011.97, 'new_value': 17265.17}, {'field': 'instoreCount', 'old_value': 851, 'new_value': 869}, {'field': 'onlineAmount', 'old_value': 18020.58, 'new_value': 18722.16}, {'field': 'onlineCount', 'old_value': 1304, 'new_value': 1361}]
2025-05-30 08:11:29,991 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:30,424 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-30 08:11:30,425 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60899.5, 'new_value': 63006.7}, {'field': 'amount', 'old_value': 60897.2, 'new_value': 63004.4}, {'field': 'count', 'old_value': 151, 'new_value': 157}, {'field': 'instoreAmount', 'old_value': 60899.5, 'new_value': 63006.7}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 157}]
2025-05-30 08:11:30,427 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:30,876 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-30 08:11:30,877 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 208988.31, 'new_value': 213658.0}, {'field': 'dailyBillAmount', 'old_value': 172661.0, 'new_value': 176349.7}, {'field': 'amount', 'old_value': 208987.63, 'new_value': 213657.32}, {'field': 'count', 'old_value': 3009, 'new_value': 3098}, {'field': 'instoreAmount', 'old_value': 199114.6, 'new_value': 203293.1}, {'field': 'instoreCount', 'old_value': 2607, 'new_value': 2674}, {'field': 'onlineAmount', 'old_value': 10126.11, 'new_value': 10617.3}, {'field': 'onlineCount', 'old_value': 402, 'new_value': 424}]
2025-05-30 08:11:30,877 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:31,291 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-30 08:11:31,291 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32125.4, 'new_value': 33028.7}, {'field': 'amount', 'old_value': 32124.6, 'new_value': 33027.9}, {'field': 'count', 'old_value': 1374, 'new_value': 1419}, {'field': 'instoreAmount', 'old_value': 26740.3, 'new_value': 27480.2}, {'field': 'instoreCount', 'old_value': 1219, 'new_value': 1259}, {'field': 'onlineAmount', 'old_value': 5470.8, 'new_value': 5634.2}, {'field': 'onlineCount', 'old_value': 155, 'new_value': 160}]
2025-05-30 08:11:31,292 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:31,733 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-30 08:11:31,733 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 424016.31, 'new_value': 433639.58}, {'field': 'dailyBillAmount', 'old_value': 424016.31, 'new_value': 433639.58}, {'field': 'amount', 'old_value': 550429.53, 'new_value': 564169.8}, {'field': 'count', 'old_value': 5715, 'new_value': 5900}, {'field': 'instoreAmount', 'old_value': 516930.47000000003, 'new_value': 529329.85}, {'field': 'instoreCount', 'old_value': 3926, 'new_value': 4049}, {'field': 'onlineAmount', 'old_value': 43719.41, 'new_value': 45060.3}, {'field': 'onlineCount', 'old_value': 1789, 'new_value': 1851}]
2025-05-30 08:11:31,734 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:32,199 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-30 08:11:32,200 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'amount', 'old_value': 41463.65, 'new_value': 42628.19}, {'field': 'count', 'old_value': 706, 'new_value': 744}, {'field': 'instoreAmount', 'old_value': 27143.45, 'new_value': 28040.2}, {'field': 'instoreCount', 'old_value': 379, 'new_value': 403}, {'field': 'onlineAmount', 'old_value': 15228.52, 'new_value': 15496.31}, {'field': 'onlineCount', 'old_value': 327, 'new_value': 341}]
2025-05-30 08:11:32,200 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:32,659 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-30 08:11:32,659 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 169405.39, 'new_value': 175920.79}, {'field': 'dailyBillAmount', 'old_value': 154516.65, 'new_value': 161096.88}, {'field': 'amount', 'old_value': 169402.6, 'new_value': 175918.0}, {'field': 'count', 'old_value': 9588, 'new_value': 9977}, {'field': 'instoreAmount', 'old_value': 103077.58, 'new_value': 106906.76}, {'field': 'instoreCount', 'old_value': 5701, 'new_value': 5911}, {'field': 'onlineAmount', 'old_value': 68437.7, 'new_value': 71385.2}, {'field': 'onlineCount', 'old_value': 3887, 'new_value': 4066}]
2025-05-30 08:11:32,660 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:33,076 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-30 08:11:33,077 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86808.68000000001, 'new_value': 89222.59}, {'field': 'amount', 'old_value': 86795.95, 'new_value': 89209.86}, {'field': 'count', 'old_value': 5579, 'new_value': 5729}, {'field': 'instoreAmount', 'old_value': 38399.83, 'new_value': 39431.48}, {'field': 'instoreCount', 'old_value': 2241, 'new_value': 2296}, {'field': 'onlineAmount', 'old_value': 51165.840000000004, 'new_value': 52598.58}, {'field': 'onlineCount', 'old_value': 3338, 'new_value': 3433}]
2025-05-30 08:11:33,077 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:33,506 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-30 08:11:33,507 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 174168.17, 'new_value': 178466.03}, {'field': 'count', 'old_value': 1783, 'new_value': 1839}, {'field': 'instoreAmount', 'old_value': 174472.05, 'new_value': 178769.91}, {'field': 'instoreCount', 'old_value': 1783, 'new_value': 1839}]
2025-05-30 08:11:33,508 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:33,948 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-30 08:11:33,948 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 140595.84, 'new_value': 144824.53}, {'field': 'dailyBillAmount', 'old_value': 145656.17, 'new_value': 150083.94}, {'field': 'amount', 'old_value': 140590.03, 'new_value': 144818.72}, {'field': 'count', 'old_value': 2990, 'new_value': 3112}, {'field': 'instoreAmount', 'old_value': 133845.45, 'new_value': 137616.03}, {'field': 'instoreCount', 'old_value': 2456, 'new_value': 2548}, {'field': 'onlineAmount', 'old_value': 6872.21, 'new_value': 7330.32}, {'field': 'onlineCount', 'old_value': 534, 'new_value': 564}]
2025-05-30 08:11:33,951 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:34,368 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-30 08:11:34,368 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 239513.21, 'new_value': 249872.86}, {'field': 'dailyBillAmount', 'old_value': 239513.21, 'new_value': 249872.86}, {'field': 'amount', 'old_value': 31144.48, 'new_value': 32482.71}, {'field': 'count', 'old_value': 1174, 'new_value': 1231}, {'field': 'instoreAmount', 'old_value': 35116.27, 'new_value': 36648.9}, {'field': 'instoreCount', 'old_value': 1174, 'new_value': 1231}]
2025-05-30 08:11:34,369 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:34,855 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-30 08:11:34,856 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 592283.86, 'new_value': 610272.84}, {'field': 'dailyBillAmount', 'old_value': 592283.86, 'new_value': 610272.84}, {'field': 'amount', 'old_value': 61542.36, 'new_value': 63771.159999999996}, {'field': 'count', 'old_value': 300, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 61769.16, 'new_value': 64266.86}, {'field': 'instoreCount', 'old_value': 300, 'new_value': 314}]
2025-05-30 08:11:34,856 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:35,283 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-30 08:11:35,284 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 19142.67, 'new_value': 20101.55}, {'field': 'count', 'old_value': 987, 'new_value': 1036}, {'field': 'onlineAmount', 'old_value': 19364.329999999998, 'new_value': 20323.21}, {'field': 'onlineCount', 'old_value': 987, 'new_value': 1036}]
2025-05-30 08:11:35,284 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:35,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-30 08:11:35,688 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 355599.36, 'new_value': 370641.34}, {'field': 'amount', 'old_value': 355445.58, 'new_value': 370487.56}, {'field': 'count', 'old_value': 3696, 'new_value': 3866}, {'field': 'instoreAmount', 'old_value': 338305.8, 'new_value': 352661.1}, {'field': 'instoreCount', 'old_value': 3113, 'new_value': 3254}, {'field': 'onlineAmount', 'old_value': 23283.190000000002, 'new_value': 24181.32}, {'field': 'onlineCount', 'old_value': 583, 'new_value': 612}]
2025-05-30 08:11:35,689 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:36,098 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-30 08:11:36,098 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 198878.48, 'new_value': 206606.41999999998}, {'field': 'dailyBillAmount', 'old_value': 195078.01, 'new_value': 202805.95}, {'field': 'amount', 'old_value': 148067.11, 'new_value': 153422.99}, {'field': 'count', 'old_value': 5333, 'new_value': 5517}, {'field': 'instoreAmount', 'old_value': 58674.16, 'new_value': 60431.36}, {'field': 'instoreCount', 'old_value': 2003, 'new_value': 2032}, {'field': 'onlineAmount', 'old_value': 91322.35, 'new_value': 94991.23}, {'field': 'onlineCount', 'old_value': 3330, 'new_value': 3485}]
2025-05-30 08:11:36,099 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:36,588 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-30 08:11:36,588 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 6859.11, 'new_value': 6952.43}, {'field': 'count', 'old_value': 312, 'new_value': 315}, {'field': 'instoreAmount', 'old_value': 6859.85, 'new_value': 6953.17}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 315}]
2025-05-30 08:11:36,589 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:37,018 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-30 08:11:37,019 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 7026.18, 'new_value': 7333.76}, {'field': 'count', 'old_value': 302, 'new_value': 315}, {'field': 'onlineAmount', 'old_value': 7026.18, 'new_value': 7333.76}, {'field': 'onlineCount', 'old_value': 302, 'new_value': 315}]
2025-05-30 08:11:37,019 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:37,477 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-30 08:11:37,477 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114624.99, 'new_value': 117100.65}, {'field': 'dailyBillAmount', 'old_value': 55618.24, 'new_value': 56780.840000000004}, {'field': 'amount', 'old_value': 114624.4, 'new_value': 117100.06}, {'field': 'count', 'old_value': 2874, 'new_value': 2951}, {'field': 'instoreAmount', 'old_value': 60663.55, 'new_value': 61813.95}, {'field': 'instoreCount', 'old_value': 1498, 'new_value': 1534}, {'field': 'onlineAmount', 'old_value': 57024.37, 'new_value': 58349.63}, {'field': 'onlineCount', 'old_value': 1376, 'new_value': 1417}]
2025-05-30 08:11:37,478 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:37,973 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-30 08:11:37,973 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67030.27, 'new_value': 71624.49}, {'field': 'amount', 'old_value': 67030.27, 'new_value': 71624.49}, {'field': 'count', 'old_value': 2706, 'new_value': 2938}, {'field': 'instoreAmount', 'old_value': 68071.16, 'new_value': 72799.36}, {'field': 'instoreCount', 'old_value': 2706, 'new_value': 2938}]
2025-05-30 08:11:37,974 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:38,352 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-30 08:11:38,353 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57780.159999999996, 'new_value': 60488.49}, {'field': 'dailyBillAmount', 'old_value': 57780.159999999996, 'new_value': 60488.49}, {'field': 'amount', 'old_value': 43728.74, 'new_value': 45359.65}, {'field': 'count', 'old_value': 2096, 'new_value': 2203}, {'field': 'instoreAmount', 'old_value': 22135.41, 'new_value': 22380.41}, {'field': 'instoreCount', 'old_value': 759, 'new_value': 772}, {'field': 'onlineAmount', 'old_value': 21685.23, 'new_value': 23080.58}, {'field': 'onlineCount', 'old_value': 1337, 'new_value': 1431}]
2025-05-30 08:11:38,353 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:38,870 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-30 08:11:38,871 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90383.06, 'new_value': 92823.22}, {'field': 'amount', 'old_value': 90383.06, 'new_value': 92823.22}, {'field': 'count', 'old_value': 2753, 'new_value': 2810}, {'field': 'instoreAmount', 'old_value': 37071.69, 'new_value': 37735.49}, {'field': 'instoreCount', 'old_value': 1403, 'new_value': 1437}, {'field': 'onlineAmount', 'old_value': 53412.86, 'new_value': 55189.22}, {'field': 'onlineCount', 'old_value': 1350, 'new_value': 1373}]
2025-05-30 08:11:38,871 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:39,373 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-30 08:11:39,373 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55326.24, 'new_value': 56577.92}, {'field': 'amount', 'old_value': 55325.340000000004, 'new_value': 56577.020000000004}, {'field': 'count', 'old_value': 1332, 'new_value': 1366}, {'field': 'instoreAmount', 'old_value': 43014.72, 'new_value': 43992.82}, {'field': 'instoreCount', 'old_value': 1068, 'new_value': 1095}, {'field': 'onlineAmount', 'old_value': 12829.53, 'new_value': 13103.11}, {'field': 'onlineCount', 'old_value': 264, 'new_value': 271}]
2025-05-30 08:11:39,374 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:39,787 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-30 08:11:39,787 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 260816.22, 'new_value': 271011.07}, {'field': 'dailyBillAmount', 'old_value': 260816.22, 'new_value': 271011.07}, {'field': 'amount', 'old_value': 174571.23, 'new_value': 181577.66}, {'field': 'count', 'old_value': 4420, 'new_value': 4579}, {'field': 'instoreAmount', 'old_value': 111569.76, 'new_value': 114816.16}, {'field': 'instoreCount', 'old_value': 2205, 'new_value': 2272}, {'field': 'onlineAmount', 'old_value': 78394.31, 'new_value': 82843.65}, {'field': 'onlineCount', 'old_value': 2215, 'new_value': 2307}]
2025-05-30 08:11:39,788 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:40,238 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-30 08:11:40,238 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 757504.87, 'new_value': 775937.61}, {'field': 'dailyBillAmount', 'old_value': 757504.87, 'new_value': 775937.61}, {'field': 'amount', 'old_value': 734185.0, 'new_value': 748254.7}, {'field': 'count', 'old_value': 4413, 'new_value': 4495}, {'field': 'instoreAmount', 'old_value': 517357.4, 'new_value': 524441.5}, {'field': 'instoreCount', 'old_value': 3396, 'new_value': 3446}, {'field': 'onlineAmount', 'old_value': 216830.5, 'new_value': 223816.1}, {'field': 'onlineCount', 'old_value': 1017, 'new_value': 1049}]
2025-05-30 08:11:40,239 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:40,681 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-30 08:11:40,681 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1171204.72, 'new_value': 1207340.53}, {'field': 'amount', 'old_value': 1171204.22, 'new_value': 1207340.03}, {'field': 'count', 'old_value': 4103, 'new_value': 4217}, {'field': 'instoreAmount', 'old_value': 1171204.72, 'new_value': 1207340.53}, {'field': 'instoreCount', 'old_value': 4103, 'new_value': 4217}]
2025-05-30 08:11:40,682 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:41,121 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-30 08:11:41,121 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 667148.32, 'new_value': 691725.44}, {'field': 'dailyBillAmount', 'old_value': 589416.1, 'new_value': 610987.0599999999}, {'field': 'amount', 'old_value': 667148.32, 'new_value': 691725.44}, {'field': 'count', 'old_value': 4199, 'new_value': 4362}, {'field': 'instoreAmount', 'old_value': 606758.6, 'new_value': 629155.49}, {'field': 'instoreCount', 'old_value': 2587, 'new_value': 2672}, {'field': 'onlineAmount', 'old_value': 60761.7, 'new_value': 62971.74}, {'field': 'onlineCount', 'old_value': 1612, 'new_value': 1690}]
2025-05-30 08:11:41,122 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:41,624 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-30 08:11:41,624 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 638179.83, 'new_value': 652906.55}, {'field': 'dailyBillAmount', 'old_value': 620754.67, 'new_value': 635360.72}, {'field': 'amount', 'old_value': 638173.24, 'new_value': 652899.96}, {'field': 'count', 'old_value': 1594, 'new_value': 1643}, {'field': 'instoreAmount', 'old_value': 592675.4, 'new_value': 605272.4}, {'field': 'instoreCount', 'old_value': 1236, 'new_value': 1274}, {'field': 'onlineAmount', 'old_value': 45631.71, 'new_value': 47761.43}, {'field': 'onlineCount', 'old_value': 358, 'new_value': 369}]
2025-05-30 08:11:41,625 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:42,049 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-30 08:11:42,049 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 810438.45, 'new_value': 831331.97}, {'field': 'amount', 'old_value': 810437.13, 'new_value': 831330.65}, {'field': 'count', 'old_value': 4416, 'new_value': 4580}, {'field': 'instoreAmount', 'old_value': 758011.91, 'new_value': 775524.91}, {'field': 'instoreCount', 'old_value': 2810, 'new_value': 2868}, {'field': 'onlineAmount', 'old_value': 52571.340000000004, 'new_value': 55951.86}, {'field': 'onlineCount', 'old_value': 1606, 'new_value': 1712}]
2025-05-30 08:11:42,050 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:42,535 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-30 08:11:42,535 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 893477.58, 'new_value': 923443.3}, {'field': 'dailyBillAmount', 'old_value': 893477.58, 'new_value': 923443.3}, {'field': 'amount', 'old_value': 823222.14, 'new_value': 848597.1}, {'field': 'count', 'old_value': 4094, 'new_value': 4224}, {'field': 'instoreAmount', 'old_value': 755338.78, 'new_value': 779068.26}, {'field': 'instoreCount', 'old_value': 3375, 'new_value': 3483}, {'field': 'onlineAmount', 'old_value': 68904.4, 'new_value': 70549.88}, {'field': 'onlineCount', 'old_value': 719, 'new_value': 741}]
2025-05-30 08:11:42,536 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:42,985 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-30 08:11:42,985 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 226048.08, 'new_value': 230708.08}, {'field': 'dailyBillAmount', 'old_value': 224641.53, 'new_value': 229301.53}, {'field': 'amount', 'old_value': 221129.9, 'new_value': 225789.9}, {'field': 'count', 'old_value': 321, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 221129.9, 'new_value': 225789.9}, {'field': 'instoreCount', 'old_value': 321, 'new_value': 331}]
2025-05-30 08:11:42,986 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:43,427 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-30 08:11:43,427 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 179598.73, 'new_value': 181412.73}, {'field': 'dailyBillAmount', 'old_value': 179598.73, 'new_value': 181412.73}, {'field': 'amount', 'old_value': 158944.95, 'new_value': 159592.95}, {'field': 'count', 'old_value': 263, 'new_value': 265}, {'field': 'instoreAmount', 'old_value': 156002.9, 'new_value': 156650.9}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 245}]
2025-05-30 08:11:43,428 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:43,958 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-30 08:11:43,959 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23709.29, 'new_value': 24341.5}, {'field': 'amount', 'old_value': 23709.29, 'new_value': 24341.5}, {'field': 'count', 'old_value': 500, 'new_value': 511}, {'field': 'instoreAmount', 'old_value': 23709.29, 'new_value': 24341.5}, {'field': 'instoreCount', 'old_value': 500, 'new_value': 511}]
2025-05-30 08:11:43,959 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:44,430 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-30 08:11:44,430 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 103235.4, 'new_value': 106686.1}, {'field': 'amount', 'old_value': 103235.4, 'new_value': 106686.1}, {'field': 'count', 'old_value': 877, 'new_value': 913}, {'field': 'instoreAmount', 'old_value': 103786.24, 'new_value': 107236.94}, {'field': 'instoreCount', 'old_value': 877, 'new_value': 913}]
2025-05-30 08:11:44,431 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:44,844 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-30 08:11:44,845 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 352971.32, 'new_value': 365477.01}, {'field': 'dailyBillAmount', 'old_value': 352971.32, 'new_value': 365477.01}, {'field': 'amount', 'old_value': 372882.4, 'new_value': 386189.92}, {'field': 'count', 'old_value': 10297, 'new_value': 10658}, {'field': 'instoreAmount', 'old_value': 351182.03, 'new_value': 363419.93}, {'field': 'instoreCount', 'old_value': 9204, 'new_value': 9515}, {'field': 'onlineAmount', 'old_value': 27134.23, 'new_value': 28340.35}, {'field': 'onlineCount', 'old_value': 1093, 'new_value': 1143}]
2025-05-30 08:11:44,846 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:45,265 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-30 08:11:45,266 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 884180.88, 'new_value': 902939.44}, {'field': 'dailyBillAmount', 'old_value': 884180.88, 'new_value': 902939.44}, {'field': 'amount', 'old_value': 790357.19, 'new_value': 809688.23}, {'field': 'count', 'old_value': 2132, 'new_value': 2216}, {'field': 'instoreAmount', 'old_value': 829672.14, 'new_value': 848430.7}, {'field': 'instoreCount', 'old_value': 1757, 'new_value': 1821}, {'field': 'onlineAmount', 'old_value': 8883.26, 'new_value': 9455.74}, {'field': 'onlineCount', 'old_value': 375, 'new_value': 395}]
2025-05-30 08:11:45,266 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:45,721 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-30 08:11:45,721 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1373496.74, 'new_value': 1413170.3}, {'field': 'amount', 'old_value': 1373496.54, 'new_value': 1413170.1}, {'field': 'count', 'old_value': 4420, 'new_value': 4566}, {'field': 'instoreAmount', 'old_value': 1374707.74, 'new_value': 1414381.3}, {'field': 'instoreCount', 'old_value': 4420, 'new_value': 4566}]
2025-05-30 08:11:45,722 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:46,172 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-30 08:11:46,173 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1109238.96, 'new_value': 1161854.04}, {'field': 'dailyBillAmount', 'old_value': 1109238.96, 'new_value': 1161854.04}, {'field': 'amount', 'old_value': 879643.59, 'new_value': 920620.94}, {'field': 'count', 'old_value': 3115, 'new_value': 3251}, {'field': 'instoreAmount', 'old_value': 858379.49, 'new_value': 898061.14}, {'field': 'instoreCount', 'old_value': 1931, 'new_value': 2019}, {'field': 'onlineAmount', 'old_value': 36260.42, 'new_value': 37725.72}, {'field': 'onlineCount', 'old_value': 1184, 'new_value': 1232}]
2025-05-30 08:11:46,173 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:46,715 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-30 08:11:46,715 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 2027401.85, 'new_value': 2096028.7}, {'field': 'dailyBillAmount', 'old_value': 2027401.85, 'new_value': 2096028.7}, {'field': 'amount', 'old_value': 2056921.0, 'new_value': 2115022.0}, {'field': 'count', 'old_value': 5304, 'new_value': 5450}, {'field': 'instoreAmount', 'old_value': 2063927.0, 'new_value': 2125823.0}, {'field': 'instoreCount', 'old_value': 5304, 'new_value': 5450}]
2025-05-30 08:11:46,716 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:47,162 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-30 08:11:47,163 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 324031.57, 'new_value': 336346.28}, {'field': 'dailyBillAmount', 'old_value': 324031.57, 'new_value': 336346.28}, {'field': 'amount', 'old_value': 327908.76, 'new_value': 340188.47000000003}, {'field': 'count', 'old_value': 1778, 'new_value': 1848}, {'field': 'instoreAmount', 'old_value': 318145.8, 'new_value': 329923.9}, {'field': 'instoreCount', 'old_value': 1502, 'new_value': 1563}, {'field': 'onlineAmount', 'old_value': 17031.24, 'new_value': 17574.85}, {'field': 'onlineCount', 'old_value': 276, 'new_value': 285}]
2025-05-30 08:11:47,163 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:47,636 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-30 08:11:47,636 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1084609.49, 'new_value': 1128344.42}, {'field': 'dailyBillAmount', 'old_value': 1084609.49, 'new_value': 1128344.42}, {'field': 'amount', 'old_value': 1142786.56, 'new_value': 1186555.47}, {'field': 'count', 'old_value': 4828, 'new_value': 4997}, {'field': 'instoreAmount', 'old_value': 1142787.21, 'new_value': 1186556.12}, {'field': 'instoreCount', 'old_value': 4828, 'new_value': 4997}]
2025-05-30 08:11:47,637 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:48,086 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-30 08:11:48,087 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 476181.99, 'new_value': 488686.23}, {'field': 'dailyBillAmount', 'old_value': 476181.99, 'new_value': 488686.23}, {'field': 'amount', 'old_value': 774995.2999999999, 'new_value': 800743.6}, {'field': 'count', 'old_value': 1315, 'new_value': 1378}, {'field': 'instoreAmount', 'old_value': 769610.6, 'new_value': 795282.6}, {'field': 'instoreCount', 'old_value': 1276, 'new_value': 1338}, {'field': 'onlineAmount', 'old_value': 5718.4, 'new_value': 5794.7}, {'field': 'onlineCount', 'old_value': 39, 'new_value': 40}]
2025-05-30 08:11:48,087 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:48,520 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-30 08:11:48,520 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 277575.19, 'new_value': 284847.12}, {'field': 'dailyBillAmount', 'old_value': 277575.19, 'new_value': 284847.12}, {'field': 'amount', 'old_value': 312731.3, 'new_value': 320463.3}, {'field': 'count', 'old_value': 2200, 'new_value': 2270}, {'field': 'instoreAmount', 'old_value': 316766.3, 'new_value': 324498.3}, {'field': 'instoreCount', 'old_value': 2200, 'new_value': 2270}]
2025-05-30 08:11:48,523 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:48,948 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-30 08:11:48,949 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 196806.2, 'new_value': 203628.82}, {'field': 'dailyBillAmount', 'old_value': 196806.2, 'new_value': 203628.82}, {'field': 'amount', 'old_value': 162636.66999999998, 'new_value': 169200.66999999998}, {'field': 'count', 'old_value': 1090, 'new_value': 1146}, {'field': 'instoreAmount', 'old_value': 163192.0, 'new_value': 169920.0}, {'field': 'instoreCount', 'old_value': 1027, 'new_value': 1081}, {'field': 'onlineAmount', 'old_value': 3039.67, 'new_value': 3065.67}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 65}]
2025-05-30 08:11:48,950 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:49,477 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-30 08:11:49,478 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 165573.14, 'new_value': 168901.0}, {'field': 'count', 'old_value': 7980, 'new_value': 8166}, {'field': 'instoreAmount', 'old_value': 85414.33, 'new_value': 86128.68}, {'field': 'instoreCount', 'old_value': 4318, 'new_value': 4378}, {'field': 'onlineAmount', 'old_value': 84688.06, 'new_value': 87341.66}, {'field': 'onlineCount', 'old_value': 3662, 'new_value': 3788}]
2025-05-30 08:11:49,478 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:49,998 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-30 08:11:49,999 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247874.63, 'new_value': 258400.95}, {'field': 'amount', 'old_value': 247863.16, 'new_value': 258389.48}, {'field': 'count', 'old_value': 4678, 'new_value': 4867}, {'field': 'instoreAmount', 'old_value': 223943.17, 'new_value': 232488.06}, {'field': 'instoreCount', 'old_value': 4196, 'new_value': 4351}, {'field': 'onlineAmount', 'old_value': 23931.46, 'new_value': 25912.89}, {'field': 'onlineCount', 'old_value': 482, 'new_value': 516}]
2025-05-30 08:11:49,999 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:50,518 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-30 08:11:50,519 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37035.1, 'new_value': 37854.5}, {'field': 'amount', 'old_value': 37035.1, 'new_value': 37854.5}, {'field': 'count', 'old_value': 253, 'new_value': 261}, {'field': 'instoreAmount', 'old_value': 37035.1, 'new_value': 37854.5}, {'field': 'instoreCount', 'old_value': 253, 'new_value': 261}]
2025-05-30 08:11:50,519 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:50,967 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-30 08:11:50,968 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67427.7, 'new_value': 67623.7}, {'field': 'dailyBillAmount', 'old_value': 67427.7, 'new_value': 67623.7}, {'field': 'amount', 'old_value': 52774.200000000004, 'new_value': 54658.0}, {'field': 'count', 'old_value': 477, 'new_value': 490}, {'field': 'instoreAmount', 'old_value': 53121.6, 'new_value': 55005.4}, {'field': 'instoreCount', 'old_value': 477, 'new_value': 490}]
2025-05-30 08:11:50,968 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:51,400 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-30 08:11:51,400 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59557.0, 'new_value': 64854.0}, {'field': 'dailyBillAmount', 'old_value': 59557.0, 'new_value': 64854.0}]
2025-05-30 08:11:51,401 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:51,801 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-30 08:11:51,801 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 305698.9, 'new_value': 343754.1}, {'field': 'dailyBillAmount', 'old_value': 305698.9, 'new_value': 343754.1}, {'field': 'amount', 'old_value': 224032.74, 'new_value': 247538.34}, {'field': 'count', 'old_value': 5785, 'new_value': 5993}, {'field': 'instoreAmount', 'old_value': 219651.38999999998, 'new_value': 243222.09}, {'field': 'instoreCount', 'old_value': 5580, 'new_value': 5782}, {'field': 'onlineAmount', 'old_value': 8660.76, 'new_value': 9267.26}, {'field': 'onlineCount', 'old_value': 205, 'new_value': 211}]
2025-05-30 08:11:51,802 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:52,241 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-30 08:11:52,241 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57178.5, 'new_value': 58237.4}, {'field': 'dailyBillAmount', 'old_value': 57178.5, 'new_value': 58237.4}, {'field': 'amount', 'old_value': 57153.0, 'new_value': 58211.9}, {'field': 'count', 'old_value': 339, 'new_value': 346}, {'field': 'instoreAmount', 'old_value': 59872.9, 'new_value': 61081.7}, {'field': 'instoreCount', 'old_value': 335, 'new_value': 342}]
2025-05-30 08:11:52,242 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:52,826 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-30 08:11:52,826 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81686.54, 'new_value': 84899.58}, {'field': 'dailyBillAmount', 'old_value': 81686.54, 'new_value': 84899.58}]
2025-05-30 08:11:52,827 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:53,345 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-30 08:11:53,345 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56195.3, 'new_value': 58066.15}, {'field': 'amount', 'old_value': 56194.4, 'new_value': 58065.25}, {'field': 'count', 'old_value': 3296, 'new_value': 3406}, {'field': 'instoreAmount', 'old_value': 57109.61, 'new_value': 59028.91}, {'field': 'instoreCount', 'old_value': 3296, 'new_value': 3406}]
2025-05-30 08:11:53,346 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:53,754 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-30 08:11:53,754 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85817.29, 'new_value': 88823.75}, {'field': 'dailyBillAmount', 'old_value': 85817.29, 'new_value': 88823.75}, {'field': 'amount', 'old_value': 88283.27, 'new_value': 91267.2}, {'field': 'count', 'old_value': 4372, 'new_value': 4538}, {'field': 'instoreAmount', 'old_value': 81952.3, 'new_value': 84632.3}, {'field': 'instoreCount', 'old_value': 4091, 'new_value': 4239}, {'field': 'onlineAmount', 'old_value': 6465.43, 'new_value': 6769.36}, {'field': 'onlineCount', 'old_value': 281, 'new_value': 299}]
2025-05-30 08:11:53,755 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:54,249 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-30 08:11:54,250 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59735.22, 'new_value': 61436.99}, {'field': 'amount', 'old_value': 59735.22, 'new_value': 61436.99}, {'field': 'count', 'old_value': 2921, 'new_value': 3012}, {'field': 'instoreAmount', 'old_value': 36826.7, 'new_value': 37745.65}, {'field': 'instoreCount', 'old_value': 1910, 'new_value': 1967}, {'field': 'onlineAmount', 'old_value': 23032.67, 'new_value': 23815.49}, {'field': 'onlineCount', 'old_value': 1011, 'new_value': 1045}]
2025-05-30 08:11:54,250 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:54,661 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-30 08:11:54,662 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42749.46, 'new_value': 44455.53}, {'field': 'dailyBillAmount', 'old_value': 42749.46, 'new_value': 44455.53}, {'field': 'amount', 'old_value': 29757.43, 'new_value': 31037.85}, {'field': 'count', 'old_value': 1197, 'new_value': 1253}, {'field': 'instoreAmount', 'old_value': 30043.43, 'new_value': 31323.85}, {'field': 'instoreCount', 'old_value': 1197, 'new_value': 1253}]
2025-05-30 08:11:54,662 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:55,106 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-30 08:11:55,106 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76830.2, 'new_value': 79442.46}, {'field': 'amount', 'old_value': 76820.78, 'new_value': 79432.07}, {'field': 'count', 'old_value': 4548, 'new_value': 4682}, {'field': 'instoreAmount', 'old_value': 19719.05, 'new_value': 20289.29}, {'field': 'instoreCount', 'old_value': 1117, 'new_value': 1145}, {'field': 'onlineAmount', 'old_value': 59251.18, 'new_value': 61330.8}, {'field': 'onlineCount', 'old_value': 3431, 'new_value': 3537}]
2025-05-30 08:11:55,107 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:55,640 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-30 08:11:55,641 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149897.25, 'new_value': 155672.06}, {'field': 'dailyBillAmount', 'old_value': 149897.25, 'new_value': 155672.06}, {'field': 'amount', 'old_value': 124625.48, 'new_value': 129262.48}, {'field': 'count', 'old_value': 1206, 'new_value': 1249}, {'field': 'instoreAmount', 'old_value': 124749.48, 'new_value': 129386.48}, {'field': 'instoreCount', 'old_value': 1206, 'new_value': 1249}]
2025-05-30 08:11:55,641 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:56,106 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-30 08:11:56,107 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115428.15, 'new_value': 118228.25}, {'field': 'dailyBillAmount', 'old_value': 115428.15, 'new_value': 118228.25}, {'field': 'amount', 'old_value': 133647.8, 'new_value': 137027.8}, {'field': 'count', 'old_value': 567, 'new_value': 580}, {'field': 'instoreAmount', 'old_value': 133647.8, 'new_value': 137027.8}, {'field': 'instoreCount', 'old_value': 567, 'new_value': 580}]
2025-05-30 08:11:56,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:56,520 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-30 08:11:56,521 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 60171.49, 'new_value': 61590.49}, {'field': 'count', 'old_value': 322, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 61608.49, 'new_value': 63027.49}, {'field': 'instoreCount', 'old_value': 322, 'new_value': 330}]
2025-05-30 08:11:56,521 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:56,943 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-30 08:11:56,944 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 143841.0, 'new_value': 148338.0}, {'field': 'amount', 'old_value': 143841.0, 'new_value': 148338.0}, {'field': 'count', 'old_value': 1478, 'new_value': 1527}, {'field': 'instoreAmount', 'old_value': 143841.0, 'new_value': 148338.0}, {'field': 'instoreCount', 'old_value': 1478, 'new_value': 1527}]
2025-05-30 08:11:56,944 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:57,363 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-30 08:11:57,364 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37411.409999999996, 'new_value': 38914.98}, {'field': 'dailyBillAmount', 'old_value': 37411.409999999996, 'new_value': 38914.98}, {'field': 'amount', 'old_value': 5241.1, 'new_value': 5816.0}, {'field': 'count', 'old_value': 211, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 5750.33, 'new_value': 6325.23}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 234}]
2025-05-30 08:11:57,364 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:57,771 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-30 08:11:57,771 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49936.4, 'new_value': 51257.9}, {'field': 'dailyBillAmount', 'old_value': 49936.4, 'new_value': 51257.9}, {'field': 'amount', 'old_value': 74152.5, 'new_value': 75913.5}, {'field': 'count', 'old_value': 300, 'new_value': 307}, {'field': 'instoreAmount', 'old_value': 74341.5, 'new_value': 76102.5}, {'field': 'instoreCount', 'old_value': 299, 'new_value': 306}]
2025-05-30 08:11:57,772 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:58,218 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-30 08:11:58,219 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46363.0, 'new_value': 49703.0}, {'field': 'dailyBillAmount', 'old_value': 46363.0, 'new_value': 49703.0}, {'field': 'amount', 'old_value': 49894.0, 'new_value': 52780.0}, {'field': 'count', 'old_value': 278, 'new_value': 294}, {'field': 'instoreAmount', 'old_value': 49908.0, 'new_value': 52794.0}, {'field': 'instoreCount', 'old_value': 278, 'new_value': 294}]
2025-05-30 08:11:58,219 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:58,628 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-30 08:11:58,628 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82433.75, 'new_value': 85555.05}, {'field': 'dailyBillAmount', 'old_value': 82433.75, 'new_value': 85555.05}, {'field': 'amount', 'old_value': 73733.72, 'new_value': 76555.38}, {'field': 'count', 'old_value': 2500, 'new_value': 2600}, {'field': 'instoreAmount', 'old_value': 67185.15, 'new_value': 69850.81}, {'field': 'instoreCount', 'old_value': 2184, 'new_value': 2275}, {'field': 'onlineAmount', 'old_value': 6585.01, 'new_value': 6741.01}, {'field': 'onlineCount', 'old_value': 316, 'new_value': 325}]
2025-05-30 08:11:58,629 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:59,078 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-30 08:11:59,079 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55765.4, 'new_value': 61329.04}, {'field': 'dailyBillAmount', 'old_value': 55765.4, 'new_value': 61329.04}, {'field': 'amount', 'old_value': 60707.62, 'new_value': 66189.62}, {'field': 'count', 'old_value': 393, 'new_value': 408}, {'field': 'instoreAmount', 'old_value': 60654.31, 'new_value': 66136.31}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 331}]
2025-05-30 08:11:59,079 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-05-30 08:11:59,539 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-30 08:11:59,539 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 233118.2, 'new_value': 239126.08}, {'field': 'dailyBillAmount', 'old_value': 233118.2, 'new_value': 239126.08}, {'field': 'amount', 'old_value': 240684.9, 'new_value': 247136.1}, {'field': 'count', 'old_value': 1577, 'new_value': 1621}, {'field': 'instoreAmount', 'old_value': 233530.7, 'new_value': 239743.7}, {'field': 'instoreCount', 'old_value': 1412, 'new_value': 1453}, {'field': 'onlineAmount', 'old_value': 11464.0, 'new_value': 11705.2}, {'field': 'onlineCount', 'old_value': 165, 'new_value': 168}]
2025-05-30 08:11:59,540 - INFO - 月销售数据同步完成！更新: 205 条，插入: 0 条，错误: 0 条，跳过: 987 条
2025-05-30 08:11:59,540 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-30 08:12:00,113 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250530.xlsx
2025-05-30 08:12:00,113 - INFO - 综合数据同步流程完成！
2025-05-30 08:12:00,196 - INFO - 综合数据同步完成
2025-05-30 08:12:00,197 - INFO - ==================================================
2025-05-30 08:12:00,197 - INFO - 程序退出
2025-05-30 08:12:00,198 - INFO - ==================================================
