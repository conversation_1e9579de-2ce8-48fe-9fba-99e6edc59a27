import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any
import json
import time  # 添加 time 模块导入
from get_shuyandata import call_sale_query_api
from get_formdata_1 import YidaFormDataClient
from update_formdata import Sample as UpdateFormSample
from insert_save_formdata import Sample as InsertFormSample
from get_token import token
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_tea_openapi import models as open_api_models

# 检查是否已经配置了日志处理器
if not logging.getLogger().handlers:
    today = datetime.now().strftime('%Y%m%d')
    logging.basicConfig(
        filename=f'sync_data_{today}.log',
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        encoding='utf-8'
    )

class DataSyncManager:
    def __init__(self):
        self.shuyan_config = {
            'appId': 'a5274b7e5d9a41939346c33c2c3443db',
            'appKey': '2c9a5a628e7dab16018f5b055f3d0002',
            'apiSecret': '07F77244AD915AC2BB3EECE8EF7AE4DB',
            'method': 'gogo.open.auto.routing',
            'lowerMethod': 'com.gooagoo.open.api.salequery',
            'url': 'http://api.gooagoo.com/oapi/rest'
        }
        self.yida_config = {
            'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
            'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
            'USER_ID': 'hexuepeng',
            'LANGUAGE': 'zh_CN',
            'FORM_UUID': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P'
        }
        self.shop_mapping = {
            "1ETDLFB9DIMQME7Q2OVD93ISAI00189O": "广州VT101维多利广场",
            "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE": "武汉国金天地",
            "1HFLOR99TBR11L6UBHOUTGCK1C001A3F": "广州悦汇城",
            "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV": "悦汇广场·南海",
            "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU": "广州IFC国金天地",
            "1HRIS7255PESAA7AV8LHQQGIH8001KNH": "广州ICC环贸天地",
            "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D": "武汉星汇维港"
        }
        self.access_token = token.get_token()

    def get_shuyan_data(self, start_date=None, end_date=None) -> List[Dict]:
        """
        获取数衍平台数据并保存到Excel
        :param start_date: 开始日期，格式：YYYYMMDD，默认为30天前
        :param end_date: 结束日期，格式：YYYYMMDD，默认为昨天
        """
        try:
            if end_date is None:
                end_date = datetime.now() - timedelta(days=1)
            else:
                try:
                    end_date = datetime.strptime(end_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"结束日期格式错误: {end_date}, {str(e)}")
                    return []
            
            if start_date is None:
                start_date = end_date - timedelta(days=60)  # 默认获取31天数据
            else:
                try:
                    start_date = datetime.strptime(start_date, '%Y%m%d')
                except ValueError as e:
                    logging.error(f"开始日期格式错误: {start_date}, {str(e)}")
                    return []
            
            all_data = []
            current_date = start_date
            logging.info(f"查询数衍平台数据，时间段为: {start_date}, {end_date}")
            # 将时间段分成多个7天的批次
            while current_date <= end_date:  # 修改循环条件，使用 <= 而不是 <
                batch_end_date = min(current_date + timedelta(days=6), end_date)
                
                business_data = {
                    "fromDate": current_date.strftime("%Y%m%d"),
                    "toDate": batch_end_date.strftime("%Y%m%d"),
                    "shopIds": list(self.shop_mapping.keys())
                }
                
                logging.info(f"正在获取{business_data['fromDate']}至{business_data['toDate']}的数据")
                
                try:
                    result = call_sale_query_api(
                        self.shuyan_config['appId'],
                        self.shuyan_config['appKey'],
                        self.shuyan_config['apiSecret'],
                        self.shuyan_config['method'],
                        self.shuyan_config['lowerMethod'],
                        self.shuyan_config['url'],
                        business_data
                    )
                    
                    # 优化日志输出，避免显示过大的数据内容
                    if 'rescode' in result and 'resmsg' in result:
                        logging.info(f"Response: {{'rescode': '{result['rescode']}', 'resmsg': '{result['resmsg']}'}}")
                    else:
                        logging.error(f"获取{business_data['fromDate']}至{business_data['toDate']}的数据失败: 响应格式错误")
                        continue
                    
                    if 'data' in result and isinstance(result['data'], list):
                        # 过滤掉所有金额和笔数都为0的记录
                        filtered_data = []
                        for item in result['data']:
                            # 检查所有金额和笔数字段
                            amount_fields = ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']
                            count_fields = ['count', 'instoreCount', 'onlineCount']
                            
                            # 检查是否有任何金额或笔数不为0
                            has_non_zero_amount = any(float(item.get(field, 0)) > 0 for field in amount_fields)
                            has_non_zero_count = any(float(item.get(field, 0)) > 0 for field in count_fields)
                            
                            if has_non_zero_amount or has_non_zero_count:
                                filtered_data.append(item)
                            else:
                                logging.debug(f"过滤掉全零记录: {item}")
                        
                        all_data.extend(filtered_data)
                        logging.info(f"过滤后保留 {len(filtered_data)} 条记录")
                    else:
                        logging.error(f"获取{business_data['fromDate']}至{business_data['toDate']}的数据失败: {result}")
                    
                    # 添加延时避免请求过于频繁
                    time.sleep(2)
                    current_date = batch_end_date + timedelta(days=1)  # 修改为使用 batch_end_date 而不是 end_date
                except Exception as e:
                    logging.error(f"调用数衍平台API失败: {str(e)}")
                    continue
            
            # 生成Excel文件
            if all_data:
                try:
                    import pandas as pd
                    from pathlib import Path
                    
                    # 准备数据
                    excel_data = []
                    for item in all_data:
                        try:
                            shop_id = item.get('shopId', '')
                            shop_entity_id = item.get('shopEntityId', '')
                            project_name = self.shop_mapping.get(shop_id, '')
                            
                            if not project_name:
                                logging.warning(f"警告：机构ID {shop_id} 未找到对应的项目名称")
                            
                            excel_data.append({
                                '项目名称': project_name,
                                '数衍平台机构ID': shop_id,
                                '数衍平台店铺ID': shop_entity_id,
                                '店铺名称': item.get('shopEntityName', ''),
                                '销售日期': item.get('saleTime', ''),
                                '推荐金额': round(float(item.get('recommendAmount', 0)), 2),
                                '日结金额': round(float(item.get('dailyBillAmount', 0)), 2),
                                '净销售额': round(float(item.get('amount', 0)), 2),
                                '总销售笔数': int(float(item.get('count', 0))),
                                '店内净销售额': round(float(item.get('instoreAmount', 0)), 2),
                                '店内销售笔数': int(float(item.get('instoreCount', 0))),
                                '线上净销售额': round(float(item.get('onlineAmount', 0)), 2),
                                '线上销售笔数': int(float(item.get('onlineCount', 0)))
                            })
                        except Exception as e:
                            logging.error(f"处理数据项失败: {item}, {str(e)}")
                            continue
                    
                    # 创建DataFrame
                    df = pd.DataFrame(excel_data)
                    
                    # 生成文件名
                    today = datetime.now().strftime('%Y%m%d')
                    file_path = Path(f'数衍平台数据导出_{today}.xlsx')
                    
                    # 保存Excel文件
                    df.to_excel(file_path, index=False, engine='openpyxl')
                    logging.info(f"数据已保存到Excel文件: {file_path}")
                except Exception as e:
                    logging.error(f"生成Excel文件失败: {str(e)}")
            
            return all_data
        except Exception as e:
            logging.error(f"获取数衍平台数据失败: {str(e)}")
            return []

    def get_yida_data(self, start_date=None, end_date=None) -> List[Dict]:
        """获取宜搭表单数据"""
        try:
            client = YidaFormDataClient()
            
            # 构建日期筛选条件
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=61)).strftime('%Y%m%d')
            if end_date is None:
                end_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
            logging.info(f"查询宜搭表单数据，时间段为: {start_date}, {end_date}")
            # 将日期转换为时间戳（毫秒）
            try:
                start_timestamp = int(datetime.strptime(start_date, '%Y%m%d').timestamp() * 1000)
                end_timestamp = int(datetime.strptime(end_date, '%Y%m%d').timestamp() * 1000) + 86399000  # 加上23:59:59的毫秒数
            except ValueError as e:
                logging.error(f"日期格式转换错误: {start_date}, {end_date}, {str(e)}")
                return []
            
            # 构建筛选条件
            search_condition = [{
                "key": "dateField_m9dkdkoz",  # 销售日期字段
                "value": [start_timestamp, end_timestamp],
                "type": "DOUBLE",
                "operator": "between",
                "componentName": "DateField"
            }]
            
            # 获取表单数据
            return client.get_form_data(search_condition=search_condition)
        except Exception as e:
            logging.error(f"获取宜搭表单数据失败: {str(e)}")
            return []

    def sync_data(self, start_date=None, end_date=None):
        """
        同步数据主流程
        :param start_date: 开始日期，格式：YYYYMMDD，默认为7天前
        :param end_date: 结束日期，格式：YYYYMMDD，默认为昨天
        """
        try:
            logging.info("开始数据同步流程...")
            
            # 1. 获取数衍平台数据
            logging.info("正在获取数衍平台数据...")
            shuyan_data = self.get_shuyan_data(start_date, end_date)
            if not shuyan_data:
                logging.error("未获取到数衍平台数据，同步流程终止")
                return
            logging.info(f"成功获取数衍平台数据，共 {len(shuyan_data)} 条记录")
            
            # 2. 获取宜搭表单数据
            logging.info("正在获取宜搭表单数据...")
            try:
                yida_data = self.get_yida_data(start_date, end_date)
                logging.info(f"成功获取宜搭表单数据，共 {len(yida_data)} 条记录")
            except Exception as e:
                logging.error(f"获取宜搭表单数据失败: {str(e)}")
                return
            
            # 创建宜搭数据索引
            logging.info("正在处理宜搭数据索引...")
            yida_data_dict = {}
            for item in yida_data:
                try:
                    standard_form_data = convert_yida_to_standard(item['formData'])
                    key = self.create_key(standard_form_data)
                    yida_data_dict[key] = {
                        'form_instance_id': item['formInstanceId'],
                        'form_data': standard_form_data
                    }
                except Exception as e:
                    logging.error(f"处理宜搭数据项失败: {item}, {str(e)}")
                    continue
            logging.info(f"数据索引处理完成，共 {len(yida_data_dict)} 条记录")
            
            # 对比并同步数据
            logging.info("开始数据对比和同步...")
            update_count = 0
            insert_count = 0
            error_count = 0
            insert_data_list = []
            
            # 定义需要比较的字段
            compare_fields = [
                'recommendAmount',    # 推荐金额
                'dailyBillAmount',    # 日结金额
                'amount',             # 净销售额
                'count',              # 总销售笔数
                'instoreAmount',      # 店内净销售额
                'instoreCount',       # 店内销售笔数
                'onlineAmount',       # 线上净销售额
                'onlineCount',        # 线上销售笔数
                'shopEntityName'      # 数衍平台店铺名称
            ]
            
            for shuyan_item in shuyan_data:
                try:
                    key = self.create_key(shuyan_item)
                    
                    if key in yida_data_dict:
                        yida_item = yida_data_dict[key]['form_data']
                        # 逐个字段比较
                        need_update = False
                        changed_fields = []
                        
                        for field in compare_fields:
                            shuyan_value = shuyan_item.get(field, 0)
                            yida_value = yida_item.get(field, 0)
                            
                            # 对于笔数字段，确保比较整数
                            if field in ['count', 'instoreCount', 'onlineCount']:
                                shuyan_value = int(shuyan_value) if shuyan_value else 0
                                yida_value = int(yida_value) if yida_value else 0
                            
                            if shuyan_value != yida_value:
                                need_update = True
                                changed_fields.append({
                                    'field': field,
                                    'old_value': yida_value,
                                    'new_value': shuyan_value
                                })
                        
                        if need_update:
                            try:
                                yida_format_data = convert_standard_to_yida(shuyan_item, self.shop_mapping)
                                form_instance_id = yida_data_dict[key]['form_instance_id']
                                
                                if not form_instance_id or not form_instance_id.startswith('FINST-'):
                                    logging.error(f"无效的表单实例ID: {form_instance_id}")
                                    error_count += 1
                                    continue
                                
                                if not all(key in yida_format_data for key in ['textField_m9dkdkpg', 'textField_m9dkdkph', 'dateField_m9dkdkoz']):
                                    logging.error(f"转换后的数据缺少必要字段: {yida_format_data}")
                                    error_count += 1
                                    continue
                                
                                self.update_form_data(form_instance_id, yida_format_data)
                                update_count += 1
                                logging.info(f"更新记录成功，变更字段: {changed_fields}")
                            except Exception as e:
                                logging.error(f"更新记录时发生错误: {str(e)}")
                                error_count += 1
                    else:
                        try:
                            yida_format_data = convert_standard_to_yida(shuyan_item, self.shop_mapping)
                            insert_data_list.append(yida_format_data)
                            insert_count += 1
                        except Exception as e:
                            logging.error(f"转换数据格式失败: {str(e)}")
                            error_count += 1
                except Exception as e:
                    logging.error(f"处理数据项失败: {shuyan_item}, {str(e)}")
                    error_count += 1
                    continue
            
            # 批量插入新数据
            if insert_data_list:
                try:
                    self.batch_insert_form_data(insert_data_list)
                    logging.info(f"批量插入完成，共 {len(insert_data_list)} 条记录")
                except Exception as e:
                    logging.error(f"批量插入数据失败: {str(e)}")
                    error_count += len(insert_data_list)
            
            logging.info(f"数据同步完成！更新: {update_count} 条，插入: {insert_count} 条，错误: {error_count} 条")
        except Exception as e:
            logging.error(f"数据同步流程失败: {str(e)}")
            raise

    def create_key(self, data: Dict) -> str:
        """创建数据唯一标识"""
        return f"{data.get('shopId')}_{data.get('shopEntityId')}_{data.get('saleTime')}"

    def update_form_data(self, form_instance_id: str, new_data: Dict):
        """更新宜搭表单数据"""
        try:
            client = UpdateFormSample.create_client()
            headers = dingtalkyida__2__0_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            request = dingtalkyida__2__0_models.UpdateFormDataRequest()
            request.app_type = self.yida_config['APP_TYPE']
            request.system_token = self.yida_config['SYSTEM_TOKEN']
            request.user_id = self.yida_config['USER_ID']
            request.language = self.yida_config['LANGUAGE']
            request.form_instance_id = form_instance_id
            request.form_uuid = self.yida_config['FORM_UUID']
            request.update_form_data_json = json.dumps(new_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新表单数据失败: {str(e)}")
            raise

    def insert_form_data(self, new_data: Dict):
        """插入宜搭表单数据"""
        try:
            client = InsertFormSample.create_client()
            headers = dingtalkyida__2__0_models.SaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            request = dingtalkyida__2__0_models.SaveFormDataRequest()
            request.app_type = self.yida_config['APP_TYPE']
            request.system_token = self.yida_config['SYSTEM_TOKEN']
            request.user_id = self.yida_config['USER_ID']
            request.language = self.yida_config['LANGUAGE']
            request.form_uuid = self.yida_config['FORM_UUID']
            request.form_data_json = json.dumps(new_data, ensure_ascii=False)
            request.use_alias = False
            
            response = client.save_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"插入表单数据成功")
            return response
            
        except Exception as e:
            logging.error(f"插入表单数据失败: {str(e)}")
            raise

    def batch_insert_form_data(self, data_list: List[Dict], batch_size: int = 100):
        """批量插入宜搭表单数据"""
        try:
            client = dingtalkyida_1_0Client(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 将数据列表分成多个批次
            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in batch_data]
                
                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=self.yida_config['FORM_UUID'],
                    app_type=self.yida_config['APP_TYPE'],
                    asynchronous_execution=True,
                    system_token=self.yida_config['SYSTEM_TOKEN'],
                    keep_running_after_exception=True,
                    user_id=self.yida_config['USER_ID'],
                    form_data_json_list=form_data_json_list
                )
                
                try:
                    response = client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())
                    if response.status_code == 200:
                        logging.info(f"批量插入表单数据成功，批次 {i//batch_size + 1}，共 {len(batch_data)} 条记录")
                    else:
                        logging.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {response.status_code}, {response.body}")
                except Exception as e:
                    logging.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {str(e)}")
                    continue
                
                # 添加延时避免请求过于频繁
                time.sleep(5)
            
        except Exception as e:
            logging.error(f"批量插入表单数据失败: {str(e)}")
            raise

def convert_yida_to_standard(yida_data: Dict) -> Dict:
    """将宜搭数据格式转换为标准格式"""
    logging.debug(f"开始转换宜搭数据到标准格式: {yida_data}")
    mapping = {
        'numberField_m9dkdkp8': 'recommendAmount',    # 推荐金额
        'numberField_m9dkdkp9': 'dailyBillAmount',    # 日结金额
        'numberField_m9dkdkpa': 'amount',             # 净销售额
        'numberField_m9dkdkpb': 'count',              # 总销售笔数
        'numberField_m9dkdkpc': 'instoreAmount',      # 店内净销售额
        'numberField_m9dkdkpd': 'instoreCount',       # 店内销售笔数
        'numberField_m9dkdkpe': 'onlineAmount',       # 线上净销售额
        'numberField_m9dkdkpf': 'onlineCount',        # 线上销售笔数
        'textField_m9dkdkpg': 'shopId',              # 数衍平台机构ID
        'textField_m9dkdkph': 'shopEntityId',        # 数衍平台店铺ID
        'textField_m9dkdkpi': 'shopEntityName',      # 数衍平台店铺名称
        'textField_m9dkdkox': 'projectName',         # 项目名称
        'dateField_m9dkdkoz': 'saleTime'             # 销售日期
    }
    
    result = {}
    for yida_key, standard_key in mapping.items():
        if yida_key in yida_data:
            value = yida_data[yida_key]
            # 处理日期字段
            if yida_key == 'dateField_m9dkdkoz':
                timestamp = int(value) / 1000  # 毫秒转秒
                value = datetime.fromtimestamp(timestamp).strftime('%Y%m%d')
            # 处理数值字段（使用不带_value后缀的数值版本）
            elif yida_key.startswith('numberField_'):
                value = float(value) if value else 0.0
            result[standard_key] = value
            
    return result

def convert_standard_to_yida(standard_data: Dict, shop_mapping: Dict) -> Dict:
    """将标准格式转换为宜搭数据格式"""
    logging.debug(f"开始转换标准数据到宜搭格式: {standard_data}")
    # 修正映射关系的获取方式
    mapping = {
        'recommendAmount': 'numberField_m9dkdkp8',    # 推荐金额 - 商户推荐的销售金额
        'dailyBillAmount': 'numberField_m9dkdkp9',    # 日结金额 - 每日结算的销售总额
        'amount': 'numberField_m9dkdkpa',             # 净销售额 - 扣除退款后的实际销售金额
        'count': 'numberField_m9dkdkpb',              # 总销售笔数 - 包含所有销售交易的笔数
        'instoreAmount': 'numberField_m9dkdkpc',      # 店内净销售额 - 实体店铺内的销售金额
        'instoreCount': 'numberField_m9dkdkpd',       # 店内销售笔数 - 实体店铺内的销售交易笔数
        'onlineAmount': 'numberField_m9dkdkpe',       # 线上净销售额 - 线上渠道的销售金额
        'onlineCount': 'numberField_m9dkdkpf',        # 线上销售笔数 - 线上渠道的销售交易笔数
        'shopId': 'textField_m9dkdkpg',              # 数衍平台机构ID - 商户在数衍平台的唯一标识
        'shopEntityId': 'textField_m9dkdkph',        # 数衍平台店铺ID - 店铺在数衍平台的唯一标识
        'shopEntityName': 'textField_m9dkdkpi',      # 数衍平台店铺名称 - 店铺的显示名称
        'projectName': 'textField_m9dkdkox',         # 项目名称 - 所属项目的名称
        'saleTime': 'dateField_m9dkdkoz'             # 销售日期 - 销售数据统计的日期
    }
    result = {}
    
    # 获取项目名称
    shop_id = standard_data.get('shopId', '')
    project_name = shop_mapping.get(shop_id, '')
    if not project_name:
        logging.warning(f"警告：机构ID {shop_id} 未找到对应的项目名称")
    standard_data['projectName'] = project_name
    
    for standard_key, value in standard_data.items():
        if standard_key in mapping:
            yida_key = mapping[standard_key]
            # 处理日期字段
            if yida_key == 'dateField_m9dkdkoz':
                try:
                    dt = datetime.strptime(str(value), '%Y%m%d')
                    value = int(dt.timestamp() * 1000)  # 秒转毫秒
                except ValueError as e:
                    logging.error(f"日期格式转换错误: {value}, {str(e)}")
                    continue
            # 处理数值字段
            elif yida_key.startswith('numberField_'):
                try:
                    # 保持原始值，不进行类型转换
                    if value is None or value == '':
                        value = 0
                    # 对于笔数字段，确保是整数
                    if standard_key in ['count', 'instoreCount', 'onlineCount']:
                        value = int(value) if value else 0
                    # 对于金额字段，保持原始类型
                    elif standard_key in ['recommendAmount', 'dailyBillAmount', 'amount', 'instoreAmount', 'onlineAmount']:
                        if value == 0:
                            value = 0  # 保持为整数0
                        elif isinstance(value, (int, float)):
                            value = value  # 保持原始数值类型
                    result[f"{yida_key}_value"] = str(value)  # 添加字符串版本
                except ValueError as e:
                    logging.error(f"数值转换错误: {value}, {str(e)}")
                    continue
            result[yida_key] = value
            
    return result

def main():
    try:
        sync_manager = DataSyncManager()
        
        # 处理命令行参数
        import sys
        if len(sys.argv) > 1:
            # 如果只有一个参数，则作为开始日期
            if len(sys.argv) == 2:
                start_date = sys.argv[1]
                end_date = None
            # 如果有两个参数，则分别作为开始日期和结束日期
            elif len(sys.argv) == 3:
                start_date = sys.argv[1]
                end_date = sys.argv[2]
            else:
                logging.error("参数格式错误，请使用以下格式：python sync_data.py [开始日期] [结束日期]")
                return
        else:
            start_date = None
            end_date = None
        
        sync_manager.sync_data(start_date, end_date)
    except Exception as e:
        logging.error(f"数据同步失败: {str(e)}")

if __name__ == '__main__':
    main()