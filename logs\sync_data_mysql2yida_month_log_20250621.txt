2025-06-21 00:00:03,091 - INFO - =================使用默认全量同步=============
2025-06-21 00:00:04,888 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 00:00:04,888 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 00:00:04,919 - INFO - 开始处理日期: 2025-01
2025-06-21 00:00:04,919 - INFO - Request Parameters - Page 1:
2025-06-21 00:00:04,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:04,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:06,341 - INFO - Response - Page 1:
2025-06-21 00:00:06,544 - INFO - 第 1 页获取到 100 条记录
2025-06-21 00:00:06,544 - INFO - Request Parameters - Page 2:
2025-06-21 00:00:06,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:06,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:07,154 - INFO - Response - Page 2:
2025-06-21 00:00:07,357 - INFO - 第 2 页获取到 100 条记录
2025-06-21 00:00:07,357 - INFO - Request Parameters - Page 3:
2025-06-21 00:00:07,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:07,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:08,216 - INFO - Response - Page 3:
2025-06-21 00:00:08,420 - INFO - 第 3 页获取到 100 条记录
2025-06-21 00:00:08,420 - INFO - Request Parameters - Page 4:
2025-06-21 00:00:08,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:08,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:08,920 - INFO - Response - Page 4:
2025-06-21 00:00:09,123 - INFO - 第 4 页获取到 100 条记录
2025-06-21 00:00:09,123 - INFO - Request Parameters - Page 5:
2025-06-21 00:00:09,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:09,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:09,701 - INFO - Response - Page 5:
2025-06-21 00:00:09,904 - INFO - 第 5 页获取到 100 条记录
2025-06-21 00:00:09,904 - INFO - Request Parameters - Page 6:
2025-06-21 00:00:09,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:09,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:10,404 - INFO - Response - Page 6:
2025-06-21 00:00:10,607 - INFO - 第 6 页获取到 100 条记录
2025-06-21 00:00:10,607 - INFO - Request Parameters - Page 7:
2025-06-21 00:00:10,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:10,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:11,092 - INFO - Response - Page 7:
2025-06-21 00:00:11,295 - INFO - 第 7 页获取到 82 条记录
2025-06-21 00:00:11,295 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 00:00:11,295 - INFO - 获取到 682 条表单数据
2025-06-21 00:00:11,295 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 00:00:11,310 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 00:00:11,310 - INFO - 开始处理日期: 2025-02
2025-06-21 00:00:11,310 - INFO - Request Parameters - Page 1:
2025-06-21 00:00:11,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:11,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:11,826 - INFO - Response - Page 1:
2025-06-21 00:00:12,029 - INFO - 第 1 页获取到 100 条记录
2025-06-21 00:00:12,029 - INFO - Request Parameters - Page 2:
2025-06-21 00:00:12,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:12,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:12,592 - INFO - Response - Page 2:
2025-06-21 00:00:12,795 - INFO - 第 2 页获取到 100 条记录
2025-06-21 00:00:12,795 - INFO - Request Parameters - Page 3:
2025-06-21 00:00:12,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:12,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:13,326 - INFO - Response - Page 3:
2025-06-21 00:00:13,529 - INFO - 第 3 页获取到 100 条记录
2025-06-21 00:00:13,529 - INFO - Request Parameters - Page 4:
2025-06-21 00:00:13,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:13,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:14,029 - INFO - Response - Page 4:
2025-06-21 00:00:14,232 - INFO - 第 4 页获取到 100 条记录
2025-06-21 00:00:14,232 - INFO - Request Parameters - Page 5:
2025-06-21 00:00:14,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:14,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:14,732 - INFO - Response - Page 5:
2025-06-21 00:00:14,936 - INFO - 第 5 页获取到 100 条记录
2025-06-21 00:00:14,936 - INFO - Request Parameters - Page 6:
2025-06-21 00:00:14,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:14,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:15,482 - INFO - Response - Page 6:
2025-06-21 00:00:15,686 - INFO - 第 6 页获取到 100 条记录
2025-06-21 00:00:15,686 - INFO - Request Parameters - Page 7:
2025-06-21 00:00:15,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:15,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:16,139 - INFO - Response - Page 7:
2025-06-21 00:00:16,342 - INFO - 第 7 页获取到 70 条记录
2025-06-21 00:00:16,342 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 00:00:16,342 - INFO - 获取到 670 条表单数据
2025-06-21 00:00:16,342 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 00:00:16,358 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 00:00:16,358 - INFO - 开始处理日期: 2025-03
2025-06-21 00:00:16,358 - INFO - Request Parameters - Page 1:
2025-06-21 00:00:16,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:16,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:16,826 - INFO - Response - Page 1:
2025-06-21 00:00:17,029 - INFO - 第 1 页获取到 100 条记录
2025-06-21 00:00:17,029 - INFO - Request Parameters - Page 2:
2025-06-21 00:00:17,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:17,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:17,608 - INFO - Response - Page 2:
2025-06-21 00:00:17,811 - INFO - 第 2 页获取到 100 条记录
2025-06-21 00:00:17,811 - INFO - Request Parameters - Page 3:
2025-06-21 00:00:17,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:17,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:18,311 - INFO - Response - Page 3:
2025-06-21 00:00:18,514 - INFO - 第 3 页获取到 100 条记录
2025-06-21 00:00:18,514 - INFO - Request Parameters - Page 4:
2025-06-21 00:00:18,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:18,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:19,061 - INFO - Response - Page 4:
2025-06-21 00:00:19,264 - INFO - 第 4 页获取到 100 条记录
2025-06-21 00:00:19,264 - INFO - Request Parameters - Page 5:
2025-06-21 00:00:19,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:19,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:19,811 - INFO - Response - Page 5:
2025-06-21 00:00:20,014 - INFO - 第 5 页获取到 100 条记录
2025-06-21 00:00:20,014 - INFO - Request Parameters - Page 6:
2025-06-21 00:00:20,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:20,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:20,545 - INFO - Response - Page 6:
2025-06-21 00:00:20,748 - INFO - 第 6 页获取到 100 条记录
2025-06-21 00:00:20,748 - INFO - Request Parameters - Page 7:
2025-06-21 00:00:20,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:20,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:21,280 - INFO - Response - Page 7:
2025-06-21 00:00:21,483 - INFO - 第 7 页获取到 61 条记录
2025-06-21 00:00:21,483 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 00:00:21,483 - INFO - 获取到 661 条表单数据
2025-06-21 00:00:21,483 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 00:00:21,498 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 00:00:21,498 - INFO - 开始处理日期: 2025-04
2025-06-21 00:00:21,498 - INFO - Request Parameters - Page 1:
2025-06-21 00:00:21,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:21,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:22,061 - INFO - Response - Page 1:
2025-06-21 00:00:22,264 - INFO - 第 1 页获取到 100 条记录
2025-06-21 00:00:22,264 - INFO - Request Parameters - Page 2:
2025-06-21 00:00:22,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:22,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:22,795 - INFO - Response - Page 2:
2025-06-21 00:00:22,999 - INFO - 第 2 页获取到 100 条记录
2025-06-21 00:00:22,999 - INFO - Request Parameters - Page 3:
2025-06-21 00:00:22,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:22,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:23,624 - INFO - Response - Page 3:
2025-06-21 00:00:23,827 - INFO - 第 3 页获取到 100 条记录
2025-06-21 00:00:23,827 - INFO - Request Parameters - Page 4:
2025-06-21 00:00:23,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:23,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:24,374 - INFO - Response - Page 4:
2025-06-21 00:00:24,577 - INFO - 第 4 页获取到 100 条记录
2025-06-21 00:00:24,577 - INFO - Request Parameters - Page 5:
2025-06-21 00:00:24,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:24,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:25,077 - INFO - Response - Page 5:
2025-06-21 00:00:25,280 - INFO - 第 5 页获取到 100 条记录
2025-06-21 00:00:25,280 - INFO - Request Parameters - Page 6:
2025-06-21 00:00:25,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:25,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:25,811 - INFO - Response - Page 6:
2025-06-21 00:00:26,014 - INFO - 第 6 页获取到 100 条记录
2025-06-21 00:00:26,014 - INFO - Request Parameters - Page 7:
2025-06-21 00:00:26,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:26,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:26,421 - INFO - Response - Page 7:
2025-06-21 00:00:26,624 - INFO - 第 7 页获取到 56 条记录
2025-06-21 00:00:26,624 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 00:00:26,624 - INFO - 获取到 656 条表单数据
2025-06-21 00:00:26,624 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 00:00:26,639 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 00:00:26,639 - INFO - 开始处理日期: 2025-05
2025-06-21 00:00:26,639 - INFO - Request Parameters - Page 1:
2025-06-21 00:00:26,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:26,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:27,186 - INFO - Response - Page 1:
2025-06-21 00:00:27,389 - INFO - 第 1 页获取到 100 条记录
2025-06-21 00:00:27,389 - INFO - Request Parameters - Page 2:
2025-06-21 00:00:27,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:27,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:27,889 - INFO - Response - Page 2:
2025-06-21 00:00:28,093 - INFO - 第 2 页获取到 100 条记录
2025-06-21 00:00:28,093 - INFO - Request Parameters - Page 3:
2025-06-21 00:00:28,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:28,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:28,608 - INFO - Response - Page 3:
2025-06-21 00:00:28,811 - INFO - 第 3 页获取到 100 条记录
2025-06-21 00:00:28,811 - INFO - Request Parameters - Page 4:
2025-06-21 00:00:28,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:28,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:29,343 - INFO - Response - Page 4:
2025-06-21 00:00:29,546 - INFO - 第 4 页获取到 100 条记录
2025-06-21 00:00:29,546 - INFO - Request Parameters - Page 5:
2025-06-21 00:00:29,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:29,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:30,062 - INFO - Response - Page 5:
2025-06-21 00:00:30,265 - INFO - 第 5 页获取到 100 条记录
2025-06-21 00:00:30,265 - INFO - Request Parameters - Page 6:
2025-06-21 00:00:30,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:30,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:30,780 - INFO - Response - Page 6:
2025-06-21 00:00:30,983 - INFO - 第 6 页获取到 100 条记录
2025-06-21 00:00:30,983 - INFO - Request Parameters - Page 7:
2025-06-21 00:00:30,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:30,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:31,437 - INFO - Response - Page 7:
2025-06-21 00:00:31,640 - INFO - 第 7 页获取到 65 条记录
2025-06-21 00:00:31,640 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 00:00:31,640 - INFO - 获取到 665 条表单数据
2025-06-21 00:00:31,640 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 00:00:31,655 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 00:00:31,655 - INFO - 开始处理日期: 2025-06
2025-06-21 00:00:31,655 - INFO - Request Parameters - Page 1:
2025-06-21 00:00:31,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:31,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:32,280 - INFO - Response - Page 1:
2025-06-21 00:00:32,484 - INFO - 第 1 页获取到 100 条记录
2025-06-21 00:00:32,484 - INFO - Request Parameters - Page 2:
2025-06-21 00:00:32,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:32,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:32,999 - INFO - Response - Page 2:
2025-06-21 00:00:33,202 - INFO - 第 2 页获取到 100 条记录
2025-06-21 00:00:33,202 - INFO - Request Parameters - Page 3:
2025-06-21 00:00:33,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:33,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:33,655 - INFO - Response - Page 3:
2025-06-21 00:00:33,859 - INFO - 第 3 页获取到 100 条记录
2025-06-21 00:00:33,859 - INFO - Request Parameters - Page 4:
2025-06-21 00:00:33,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:33,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:34,374 - INFO - Response - Page 4:
2025-06-21 00:00:34,577 - INFO - 第 4 页获取到 100 条记录
2025-06-21 00:00:34,577 - INFO - Request Parameters - Page 5:
2025-06-21 00:00:34,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:34,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:35,078 - INFO - Response - Page 5:
2025-06-21 00:00:35,281 - INFO - 第 5 页获取到 100 条记录
2025-06-21 00:00:35,281 - INFO - Request Parameters - Page 6:
2025-06-21 00:00:35,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:35,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:35,765 - INFO - Response - Page 6:
2025-06-21 00:00:35,968 - INFO - 第 6 页获取到 100 条记录
2025-06-21 00:00:35,968 - INFO - Request Parameters - Page 7:
2025-06-21 00:00:35,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 00:00:35,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 00:00:36,328 - INFO - Response - Page 7:
2025-06-21 00:00:36,531 - INFO - 第 7 页获取到 23 条记录
2025-06-21 00:00:36,531 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 00:00:36,531 - INFO - 获取到 623 条表单数据
2025-06-21 00:00:36,531 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 00:00:36,531 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-21 00:00:37,015 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-21 00:00:37,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38082.3, 'new_value': 38588.4}, {'field': 'total_amount', 'old_value': 38082.3, 'new_value': 38588.4}, {'field': 'order_count', 'old_value': 374, 'new_value': 387}]
2025-06-21 00:00:37,015 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-21 00:00:37,421 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-21 00:00:37,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 654243.0, 'new_value': 685837.0}, {'field': 'total_amount', 'old_value': 654243.0, 'new_value': 685837.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 146}]
2025-06-21 00:00:37,421 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-21 00:00:37,859 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-21 00:00:37,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1085944.76, 'new_value': 1144564.38}, {'field': 'total_amount', 'old_value': 1085944.76, 'new_value': 1144564.38}, {'field': 'order_count', 'old_value': 12056, 'new_value': 12709}]
2025-06-21 00:00:37,859 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-21 00:00:38,281 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-21 00:00:38,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46823.51, 'new_value': 49294.63}, {'field': 'offline_amount', 'old_value': 68143.36, 'new_value': 71504.61}, {'field': 'total_amount', 'old_value': 114966.87, 'new_value': 120799.24}, {'field': 'order_count', 'old_value': 3842, 'new_value': 4052}]
2025-06-21 00:00:38,281 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-21 00:00:38,718 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-21 00:00:38,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4101.0, 'new_value': 4696.0}, {'field': 'total_amount', 'old_value': 4101.0, 'new_value': 4696.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-06-21 00:00:38,734 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-21 00:00:39,062 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-21 00:00:39,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 434029.0, 'new_value': 451451.0}, {'field': 'total_amount', 'old_value': 435217.0, 'new_value': 452639.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 140}]
2025-06-21 00:00:39,062 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-21 00:00:39,531 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-21 00:00:39,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16736.3, 'new_value': 17770.3}, {'field': 'total_amount', 'old_value': 16736.3, 'new_value': 17770.3}, {'field': 'order_count', 'old_value': 92, 'new_value': 95}]
2025-06-21 00:00:39,531 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-21 00:00:39,937 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-21 00:00:39,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34617.0, 'new_value': 37032.07}, {'field': 'offline_amount', 'old_value': 515963.74, 'new_value': 550260.24}, {'field': 'total_amount', 'old_value': 550580.74, 'new_value': 587292.31}, {'field': 'order_count', 'old_value': 2322, 'new_value': 2475}]
2025-06-21 00:00:39,937 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-21 00:00:40,422 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-21 00:00:40,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16676.0, 'new_value': 18546.0}, {'field': 'total_amount', 'old_value': 16676.0, 'new_value': 18546.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 76}]
2025-06-21 00:00:40,422 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-21 00:00:40,859 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-21 00:00:40,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 386181.0, 'new_value': 401019.0}, {'field': 'total_amount', 'old_value': 390097.0, 'new_value': 404935.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-06-21 00:00:40,859 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-21 00:00:41,312 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-21 00:00:41,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153061.5, 'new_value': 155813.5}, {'field': 'total_amount', 'old_value': 153061.5, 'new_value': 155813.5}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-06-21 00:00:41,312 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-21 00:00:41,890 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-21 00:00:41,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9877.33, 'new_value': 10730.67}, {'field': 'total_amount', 'old_value': 16249.33, 'new_value': 17102.67}, {'field': 'order_count', 'old_value': 76, 'new_value': 80}]
2025-06-21 00:00:41,890 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-21 00:00:42,344 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-21 00:00:42,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23781.0, 'new_value': 25778.0}, {'field': 'total_amount', 'old_value': 24258.0, 'new_value': 26255.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 58}]
2025-06-21 00:00:42,344 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-21 00:00:42,765 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-21 00:00:42,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260833.2, 'new_value': 271949.24}, {'field': 'total_amount', 'old_value': 260833.2, 'new_value': 271949.24}, {'field': 'order_count', 'old_value': 1291, 'new_value': 1352}]
2025-06-21 00:00:42,765 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-21 00:00:43,187 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-21 00:00:43,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 283.0, 'new_value': 4498.05}, {'field': 'total_amount', 'old_value': 66996.0, 'new_value': 71211.05}]
2025-06-21 00:00:43,187 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-21 00:00:43,625 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-21 00:00:43,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115801.0, 'new_value': 135067.0}, {'field': 'total_amount', 'old_value': 176492.55, 'new_value': 195758.55}, {'field': 'order_count', 'old_value': 77, 'new_value': 81}]
2025-06-21 00:00:43,640 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-21 00:00:44,125 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-21 00:00:44,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7405.0, 'new_value': 7582.0}, {'field': 'total_amount', 'old_value': 54703.0, 'new_value': 54880.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-06-21 00:00:44,125 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-21 00:00:44,578 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-21 00:00:44,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41298.0, 'new_value': 44694.0}, {'field': 'total_amount', 'old_value': 41298.0, 'new_value': 44694.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 123}]
2025-06-21 00:00:44,578 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-21 00:00:45,016 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-21 00:00:45,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15338.3, 'new_value': 17258.1}, {'field': 'offline_amount', 'old_value': 81319.7, 'new_value': 84359.7}, {'field': 'total_amount', 'old_value': 96658.0, 'new_value': 101617.8}, {'field': 'order_count', 'old_value': 114, 'new_value': 119}]
2025-06-21 00:00:45,016 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-21 00:00:45,469 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-21 00:00:45,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39581.47, 'new_value': 42194.46}, {'field': 'offline_amount', 'old_value': 346332.36, 'new_value': 360887.75}, {'field': 'total_amount', 'old_value': 385913.83, 'new_value': 403082.21}, {'field': 'order_count', 'old_value': 3208, 'new_value': 3448}]
2025-06-21 00:00:45,469 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-21 00:00:46,047 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-21 00:00:46,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177968.53, 'new_value': 181212.53}, {'field': 'total_amount', 'old_value': 177968.53, 'new_value': 181212.53}, {'field': 'order_count', 'old_value': 104, 'new_value': 108}]
2025-06-21 00:00:46,047 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-21 00:00:46,500 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-21 00:00:46,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13264.41, 'new_value': 14268.36}, {'field': 'total_amount', 'old_value': 14480.78, 'new_value': 15484.73}, {'field': 'order_count', 'old_value': 281, 'new_value': 295}]
2025-06-21 00:00:46,500 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-21 00:00:46,875 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-21 00:00:46,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60434.2, 'new_value': 67980.9}, {'field': 'total_amount', 'old_value': 60434.2, 'new_value': 67980.9}, {'field': 'order_count', 'old_value': 220, 'new_value': 233}]
2025-06-21 00:00:46,875 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-21 00:00:47,266 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-21 00:00:47,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39419.74, 'new_value': 41230.79}, {'field': 'offline_amount', 'old_value': 234686.11, 'new_value': 243411.71}, {'field': 'total_amount', 'old_value': 274105.85, 'new_value': 284642.5}, {'field': 'order_count', 'old_value': 2044, 'new_value': 2168}]
2025-06-21 00:00:47,266 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-21 00:00:47,719 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-21 00:00:47,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57356.92, 'new_value': 60333.02}, {'field': 'total_amount', 'old_value': 57356.92, 'new_value': 60333.02}, {'field': 'order_count', 'old_value': 1820, 'new_value': 1908}]
2025-06-21 00:00:47,719 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-21 00:00:48,125 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-21 00:00:48,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40270.0, 'new_value': 40576.0}, {'field': 'total_amount', 'old_value': 40270.0, 'new_value': 40576.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 96}]
2025-06-21 00:00:48,125 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-21 00:00:48,625 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-21 00:00:48,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22520.2, 'new_value': 23819.7}, {'field': 'total_amount', 'old_value': 22520.2, 'new_value': 23819.7}, {'field': 'order_count', 'old_value': 141, 'new_value': 146}]
2025-06-21 00:00:48,625 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-21 00:00:49,094 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-21 00:00:49,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218764.2, 'new_value': 234081.6}, {'field': 'total_amount', 'old_value': 218764.2, 'new_value': 234081.6}, {'field': 'order_count', 'old_value': 2194, 'new_value': 2345}]
2025-06-21 00:00:49,094 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-21 00:00:49,532 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-21 00:00:49,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47105.7, 'new_value': 49274.7}, {'field': 'total_amount', 'old_value': 53731.1, 'new_value': 55900.1}, {'field': 'order_count', 'old_value': 132, 'new_value': 138}]
2025-06-21 00:00:49,532 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-21 00:00:49,953 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-21 00:00:49,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117833.0, 'new_value': 120826.0}, {'field': 'total_amount', 'old_value': 117833.0, 'new_value': 120826.0}, {'field': 'order_count', 'old_value': 455, 'new_value': 468}]
2025-06-21 00:00:49,953 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-21 00:00:50,297 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-21 00:00:50,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 668758.0, 'new_value': 702589.0}, {'field': 'total_amount', 'old_value': 741350.0, 'new_value': 775181.0}, {'field': 'order_count', 'old_value': 770, 'new_value': 802}]
2025-06-21 00:00:50,297 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-21 00:00:50,750 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-21 00:00:50,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76742.68, 'new_value': 80365.68}, {'field': 'total_amount', 'old_value': 76742.68, 'new_value': 80365.68}, {'field': 'order_count', 'old_value': 6549, 'new_value': 6914}]
2025-06-21 00:00:50,750 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-21 00:00:51,188 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-21 00:00:51,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73006.2, 'new_value': 77811.92}, {'field': 'offline_amount', 'old_value': 37594.5, 'new_value': 39278.0}, {'field': 'total_amount', 'old_value': 110600.7, 'new_value': 117089.92}, {'field': 'order_count', 'old_value': 6472, 'new_value': 6822}]
2025-06-21 00:00:51,188 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-21 00:00:51,641 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-21 00:00:51,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75794.48, 'new_value': 79209.09}, {'field': 'offline_amount', 'old_value': 269451.77, 'new_value': 282451.77}, {'field': 'total_amount', 'old_value': 345246.25, 'new_value': 361660.86}, {'field': 'order_count', 'old_value': 1116, 'new_value': 1168}]
2025-06-21 00:00:51,641 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-21 00:00:52,141 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-21 00:00:52,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102917.0, 'new_value': 109817.0}, {'field': 'total_amount', 'old_value': 102918.0, 'new_value': 109818.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-21 00:00:52,141 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-21 00:00:52,610 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-21 00:00:52,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43358.33, 'new_value': 45803.19}, {'field': 'offline_amount', 'old_value': 58225.47, 'new_value': 61080.44}, {'field': 'total_amount', 'old_value': 101583.8, 'new_value': 106883.63}, {'field': 'order_count', 'old_value': 3973, 'new_value': 4191}]
2025-06-21 00:00:52,610 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-21 00:00:53,157 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-21 00:00:53,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50469.27, 'new_value': 54101.74}, {'field': 'offline_amount', 'old_value': 62598.44, 'new_value': 68877.76}, {'field': 'total_amount', 'old_value': 113067.71, 'new_value': 122979.5}, {'field': 'order_count', 'old_value': 5735, 'new_value': 6180}]
2025-06-21 00:00:53,157 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-21 00:00:53,610 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-21 00:00:53,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66209.98, 'new_value': 69015.48}, {'field': 'total_amount', 'old_value': 66209.98, 'new_value': 69015.48}, {'field': 'order_count', 'old_value': 2462, 'new_value': 2561}]
2025-06-21 00:00:53,610 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-21 00:00:54,079 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-21 00:00:54,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295833.08, 'new_value': 306843.18}, {'field': 'total_amount', 'old_value': 295833.08, 'new_value': 306843.18}, {'field': 'order_count', 'old_value': 1633, 'new_value': 1686}]
2025-06-21 00:00:54,079 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-21 00:00:54,501 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-21 00:00:54,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16752.2, 'new_value': 17739.2}, {'field': 'total_amount', 'old_value': 17252.2, 'new_value': 18239.2}, {'field': 'order_count', 'old_value': 83, 'new_value': 89}]
2025-06-21 00:00:54,501 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-21 00:00:54,969 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-21 00:00:54,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30644.92, 'new_value': 32317.57}, {'field': 'offline_amount', 'old_value': 15187.65, 'new_value': 15861.85}, {'field': 'total_amount', 'old_value': 45832.57, 'new_value': 48179.42}, {'field': 'order_count', 'old_value': 1960, 'new_value': 2070}]
2025-06-21 00:00:54,969 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-21 00:00:55,407 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-21 00:00:55,407 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12634.07, 'new_value': 13266.49}, {'field': 'offline_amount', 'old_value': 8369.09, 'new_value': 8690.79}, {'field': 'total_amount', 'old_value': 21003.16, 'new_value': 21957.28}, {'field': 'order_count', 'old_value': 1675, 'new_value': 1761}]
2025-06-21 00:00:55,407 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-21 00:00:55,844 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-21 00:00:55,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 465714.08, 'new_value': 492255.45}, {'field': 'total_amount', 'old_value': 465714.08, 'new_value': 492255.45}, {'field': 'order_count', 'old_value': 7754, 'new_value': 8204}]
2025-06-21 00:00:55,844 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-21 00:00:56,313 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-21 00:00:56,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32910.75, 'new_value': 33865.77}, {'field': 'offline_amount', 'old_value': 199179.03, 'new_value': 209261.08}, {'field': 'total_amount', 'old_value': 232089.78, 'new_value': 243126.85}, {'field': 'order_count', 'old_value': 23595, 'new_value': 34632}]
2025-06-21 00:00:56,313 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-21 00:00:56,751 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-21 00:00:56,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272022.0, 'new_value': 280374.0}, {'field': 'total_amount', 'old_value': 272022.0, 'new_value': 280374.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 76}]
2025-06-21 00:00:56,751 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-21 00:00:57,204 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-21 00:00:57,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20401.45, 'new_value': 21533.34}, {'field': 'offline_amount', 'old_value': 181026.12, 'new_value': 192222.97}, {'field': 'total_amount', 'old_value': 201427.57, 'new_value': 213756.31}, {'field': 'order_count', 'old_value': 961, 'new_value': 1018}]
2025-06-21 00:00:57,204 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-21 00:00:57,610 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-21 00:00:57,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104981.91, 'new_value': 110155.51}, {'field': 'total_amount', 'old_value': 104981.91, 'new_value': 110155.51}, {'field': 'order_count', 'old_value': 246, 'new_value': 259}]
2025-06-21 00:00:57,610 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-21 00:00:58,048 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-21 00:00:58,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199576.28, 'new_value': 209443.29}, {'field': 'offline_amount', 'old_value': 57061.56, 'new_value': 59127.86}, {'field': 'total_amount', 'old_value': 256637.84, 'new_value': 268571.15}, {'field': 'order_count', 'old_value': 1583, 'new_value': 1667}]
2025-06-21 00:00:58,048 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-21 00:00:58,391 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-21 00:00:58,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95615.0, 'new_value': 100213.0}, {'field': 'total_amount', 'old_value': 100182.0, 'new_value': 104780.0}, {'field': 'order_count', 'old_value': 4294, 'new_value': 4303}]
2025-06-21 00:00:58,391 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-21 00:00:58,813 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-21 00:00:58,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59032.61, 'new_value': 62451.8}, {'field': 'offline_amount', 'old_value': 242618.87, 'new_value': 253048.87}, {'field': 'total_amount', 'old_value': 301651.48, 'new_value': 315500.67}, {'field': 'order_count', 'old_value': 3547, 'new_value': 3655}]
2025-06-21 00:00:58,813 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-21 00:00:59,345 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-21 00:00:59,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7770.54, 'new_value': 8196.54}, {'field': 'offline_amount', 'old_value': 22487.79, 'new_value': 23661.69}, {'field': 'total_amount', 'old_value': 30258.33, 'new_value': 31858.23}, {'field': 'order_count', 'old_value': 1052, 'new_value': 1106}]
2025-06-21 00:00:59,345 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-21 00:00:59,876 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-21 00:00:59,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37159.0, 'new_value': 37544.0}, {'field': 'offline_amount', 'old_value': 101546.46, 'new_value': 109689.46}, {'field': 'total_amount', 'old_value': 138705.46, 'new_value': 147233.46}, {'field': 'order_count', 'old_value': 196, 'new_value': 210}]
2025-06-21 00:00:59,876 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-21 00:01:00,313 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-21 00:01:00,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355829.23, 'new_value': 372133.51}, {'field': 'total_amount', 'old_value': 355829.23, 'new_value': 372133.51}, {'field': 'order_count', 'old_value': 5197, 'new_value': 5443}]
2025-06-21 00:01:00,313 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-21 00:01:00,751 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-21 00:01:00,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 478302.0, 'new_value': 498406.0}, {'field': 'total_amount', 'old_value': 478302.0, 'new_value': 498406.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 50}]
2025-06-21 00:01:00,751 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-21 00:01:01,188 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-21 00:01:01,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141839.63, 'new_value': 152041.11}, {'field': 'total_amount', 'old_value': 193563.72, 'new_value': 203765.2}, {'field': 'order_count', 'old_value': 8475, 'new_value': 8953}]
2025-06-21 00:01:01,188 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-21 00:01:01,626 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-21 00:01:01,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82625.4, 'new_value': 88373.4}, {'field': 'offline_amount', 'old_value': 172256.8, 'new_value': 176717.4}, {'field': 'total_amount', 'old_value': 254882.2, 'new_value': 265090.8}, {'field': 'order_count', 'old_value': 3226, 'new_value': 3584}]
2025-06-21 00:01:01,626 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-21 00:01:02,064 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-21 00:01:02,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4315.71, 'new_value': 4486.41}, {'field': 'total_amount', 'old_value': 22055.71, 'new_value': 22226.41}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-06-21 00:01:02,064 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-21 00:01:02,579 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-21 00:01:02,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9092.58, 'new_value': 9593.06}, {'field': 'offline_amount', 'old_value': 22792.72, 'new_value': 23825.32}, {'field': 'total_amount', 'old_value': 31885.3, 'new_value': 33418.38}, {'field': 'order_count', 'old_value': 1551, 'new_value': 1618}]
2025-06-21 00:01:02,579 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-21 00:01:03,001 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-21 00:01:03,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27645.95, 'new_value': 28888.21}, {'field': 'total_amount', 'old_value': 27645.95, 'new_value': 28888.21}, {'field': 'order_count', 'old_value': 119, 'new_value': 124}]
2025-06-21 00:01:03,001 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-21 00:01:03,423 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-21 00:01:03,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12603.0, 'new_value': 12802.0}, {'field': 'total_amount', 'old_value': 12603.0, 'new_value': 12802.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 144}]
2025-06-21 00:01:03,423 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-21 00:01:03,782 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-21 00:01:03,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45739.61, 'new_value': 47443.09}, {'field': 'offline_amount', 'old_value': 163359.42, 'new_value': 170030.08}, {'field': 'total_amount', 'old_value': 209099.03, 'new_value': 217473.17}, {'field': 'order_count', 'old_value': 4740, 'new_value': 4950}]
2025-06-21 00:01:03,782 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-21 00:01:04,251 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-21 00:01:04,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347403.0, 'new_value': 360808.0}, {'field': 'total_amount', 'old_value': 347403.0, 'new_value': 360808.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 79}]
2025-06-21 00:01:04,251 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-21 00:01:04,704 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-21 00:01:04,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74520.7, 'new_value': 77876.7}, {'field': 'offline_amount', 'old_value': 41583.6, 'new_value': 43513.1}, {'field': 'total_amount', 'old_value': 116104.3, 'new_value': 121389.8}, {'field': 'order_count', 'old_value': 759, 'new_value': 799}]
2025-06-21 00:01:04,704 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-21 00:01:05,142 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-21 00:01:05,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17988.0, 'new_value': 18917.0}, {'field': 'total_amount', 'old_value': 17988.0, 'new_value': 18917.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 79}]
2025-06-21 00:01:05,142 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-21 00:01:05,611 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-21 00:01:05,611 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22134.17, 'new_value': 23453.4}, {'field': 'offline_amount', 'old_value': 152908.7, 'new_value': 159807.7}, {'field': 'total_amount', 'old_value': 175042.87, 'new_value': 183261.1}, {'field': 'order_count', 'old_value': 5628, 'new_value': 5898}]
2025-06-21 00:01:05,611 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-21 00:01:06,048 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-21 00:01:06,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200787.28, 'new_value': 211093.01}, {'field': 'offline_amount', 'old_value': 507004.71, 'new_value': 534160.49}, {'field': 'total_amount', 'old_value': 707791.99, 'new_value': 745253.5}, {'field': 'order_count', 'old_value': 4447, 'new_value': 4685}]
2025-06-21 00:01:06,048 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-21 00:01:06,548 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-21 00:01:06,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84592.43, 'new_value': 89016.48}, {'field': 'offline_amount', 'old_value': 176046.8, 'new_value': 184834.82}, {'field': 'total_amount', 'old_value': 260639.23, 'new_value': 273851.3}, {'field': 'order_count', 'old_value': 9388, 'new_value': 9853}]
2025-06-21 00:01:06,548 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-21 00:01:07,033 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-21 00:01:07,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121997.38, 'new_value': 130227.57}, {'field': 'offline_amount', 'old_value': 163808.71, 'new_value': 171808.71}, {'field': 'total_amount', 'old_value': 285806.09, 'new_value': 302036.28}, {'field': 'order_count', 'old_value': 931, 'new_value': 985}]
2025-06-21 00:01:07,033 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-21 00:01:07,455 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-21 00:01:07,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132805.0, 'new_value': 138112.0}, {'field': 'total_amount', 'old_value': 132805.0, 'new_value': 138112.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 97}]
2025-06-21 00:01:07,455 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-21 00:01:07,908 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-21 00:01:07,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 554907.28, 'new_value': 584956.13}, {'field': 'total_amount', 'old_value': 554907.28, 'new_value': 584956.13}, {'field': 'order_count', 'old_value': 3679, 'new_value': 3915}]
2025-06-21 00:01:07,908 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-21 00:01:08,330 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-21 00:01:08,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28915.99, 'new_value': 30199.62}, {'field': 'offline_amount', 'old_value': 31426.58, 'new_value': 33003.84}, {'field': 'total_amount', 'old_value': 60342.57, 'new_value': 63203.46}, {'field': 'order_count', 'old_value': 5169, 'new_value': 5409}]
2025-06-21 00:01:08,330 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-21 00:01:08,783 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-21 00:01:08,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9533.0, 'new_value': 9732.0}, {'field': 'total_amount', 'old_value': 9533.0, 'new_value': 9732.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-21 00:01:08,783 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-21 00:01:09,361 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-21 00:01:09,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304083.33, 'new_value': 317885.33}, {'field': 'total_amount', 'old_value': 304083.33, 'new_value': 317885.33}, {'field': 'order_count', 'old_value': 12407, 'new_value': 12997}]
2025-06-21 00:01:09,361 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-21 00:01:09,830 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-21 00:01:09,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35000.0, 'new_value': 40000.0}, {'field': 'total_amount', 'old_value': 35000.0, 'new_value': 40000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-21 00:01:09,830 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-21 00:01:10,298 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-21 00:01:10,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21524.0, 'new_value': 21880.0}, {'field': 'total_amount', 'old_value': 21524.0, 'new_value': 21880.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-21 00:01:10,314 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-21 00:01:10,783 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-21 00:01:10,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219362.49, 'new_value': 222881.17}, {'field': 'total_amount', 'old_value': 233357.49, 'new_value': 236876.17}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-06-21 00:01:10,783 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-21 00:01:11,205 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-21 00:01:11,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8400000.0, 'new_value': 8600000.0}, {'field': 'total_amount', 'old_value': 8400000.0, 'new_value': 8600000.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-06-21 00:01:11,205 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-21 00:01:11,689 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-21 00:01:11,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 182023.18, 'new_value': 193123.34}, {'field': 'offline_amount', 'old_value': 228667.64, 'new_value': 239785.55}, {'field': 'total_amount', 'old_value': 410690.82, 'new_value': 432908.89}, {'field': 'order_count', 'old_value': 13081, 'new_value': 13849}]
2025-06-21 00:01:11,689 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-21 00:01:12,189 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-21 00:01:12,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117983.41, 'new_value': 125607.85}, {'field': 'total_amount', 'old_value': 147178.42, 'new_value': 154802.86}, {'field': 'order_count', 'old_value': 8359, 'new_value': 8772}]
2025-06-21 00:01:12,189 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-21 00:01:12,595 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-21 00:01:12,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55953.25, 'new_value': 57611.93}, {'field': 'offline_amount', 'old_value': 70362.81, 'new_value': 72783.21}, {'field': 'total_amount', 'old_value': 126316.06, 'new_value': 130395.14}, {'field': 'order_count', 'old_value': 1456, 'new_value': 1516}]
2025-06-21 00:01:12,595 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-21 00:01:12,971 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-21 00:01:12,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62801.0, 'new_value': 66763.0}, {'field': 'offline_amount', 'old_value': 127737.0, 'new_value': 133132.0}, {'field': 'total_amount', 'old_value': 190538.0, 'new_value': 199895.0}, {'field': 'order_count', 'old_value': 3749, 'new_value': 3970}]
2025-06-21 00:01:12,986 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-21 00:01:13,377 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-21 00:01:13,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8651.49, 'new_value': 8956.94}, {'field': 'offline_amount', 'old_value': 58820.23, 'new_value': 62138.95}, {'field': 'total_amount', 'old_value': 67471.72, 'new_value': 71095.89}, {'field': 'order_count', 'old_value': 2077, 'new_value': 2193}]
2025-06-21 00:01:13,377 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR1
2025-06-21 00:01:13,814 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR1
2025-06-21 00:01:13,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4004.8, 'new_value': 4226.8}, {'field': 'total_amount', 'old_value': 4004.8, 'new_value': 4226.8}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-21 00:01:13,814 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-21 00:01:14,236 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-21 00:01:14,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32605.2, 'new_value': 33070.4}, {'field': 'total_amount', 'old_value': 32605.2, 'new_value': 33070.4}, {'field': 'order_count', 'old_value': 277, 'new_value': 280}]
2025-06-21 00:01:14,236 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-21 00:01:14,783 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-21 00:01:14,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35196.0, 'new_value': 35419.0}, {'field': 'offline_amount', 'old_value': 304173.0, 'new_value': 306158.0}, {'field': 'total_amount', 'old_value': 339369.0, 'new_value': 341577.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 94}]
2025-06-21 00:01:14,783 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-21 00:01:15,283 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-21 00:01:15,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18329.2, 'new_value': 19883.1}, {'field': 'total_amount', 'old_value': 18329.2, 'new_value': 19883.1}, {'field': 'order_count', 'old_value': 299, 'new_value': 348}]
2025-06-21 00:01:15,283 - INFO - 日期 2025-06 处理完成 - 更新: 86 条，插入: 0 条，错误: 0 条
2025-06-21 00:01:15,283 - INFO - 数据同步完成！更新: 86 条，插入: 0 条，错误: 0 条
2025-06-21 00:01:15,299 - INFO - =================同步完成====================
2025-06-21 03:00:02,746 - INFO - =================使用默认全量同步=============
2025-06-21 03:00:04,496 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 03:00:04,496 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 03:00:04,528 - INFO - 开始处理日期: 2025-01
2025-06-21 03:00:04,528 - INFO - Request Parameters - Page 1:
2025-06-21 03:00:04,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:04,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:05,496 - INFO - Response - Page 1:
2025-06-21 03:00:05,700 - INFO - 第 1 页获取到 100 条记录
2025-06-21 03:00:05,700 - INFO - Request Parameters - Page 2:
2025-06-21 03:00:05,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:05,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:06,621 - INFO - Response - Page 2:
2025-06-21 03:00:06,825 - INFO - 第 2 页获取到 100 条记录
2025-06-21 03:00:06,825 - INFO - Request Parameters - Page 3:
2025-06-21 03:00:06,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:06,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:07,293 - INFO - Response - Page 3:
2025-06-21 03:00:07,497 - INFO - 第 3 页获取到 100 条记录
2025-06-21 03:00:07,497 - INFO - Request Parameters - Page 4:
2025-06-21 03:00:07,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:07,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:08,012 - INFO - Response - Page 4:
2025-06-21 03:00:08,215 - INFO - 第 4 页获取到 100 条记录
2025-06-21 03:00:08,215 - INFO - Request Parameters - Page 5:
2025-06-21 03:00:08,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:08,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:08,747 - INFO - Response - Page 5:
2025-06-21 03:00:08,950 - INFO - 第 5 页获取到 100 条记录
2025-06-21 03:00:08,950 - INFO - Request Parameters - Page 6:
2025-06-21 03:00:08,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:08,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:09,497 - INFO - Response - Page 6:
2025-06-21 03:00:09,700 - INFO - 第 6 页获取到 100 条记录
2025-06-21 03:00:09,700 - INFO - Request Parameters - Page 7:
2025-06-21 03:00:09,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:09,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:10,153 - INFO - Response - Page 7:
2025-06-21 03:00:10,356 - INFO - 第 7 页获取到 82 条记录
2025-06-21 03:00:10,356 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 03:00:10,356 - INFO - 获取到 682 条表单数据
2025-06-21 03:00:10,372 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 03:00:10,372 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:10,372 - INFO - 开始处理日期: 2025-02
2025-06-21 03:00:10,372 - INFO - Request Parameters - Page 1:
2025-06-21 03:00:10,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:10,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:10,872 - INFO - Response - Page 1:
2025-06-21 03:00:11,075 - INFO - 第 1 页获取到 100 条记录
2025-06-21 03:00:11,075 - INFO - Request Parameters - Page 2:
2025-06-21 03:00:11,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:11,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:11,544 - INFO - Response - Page 2:
2025-06-21 03:00:11,747 - INFO - 第 2 页获取到 100 条记录
2025-06-21 03:00:11,747 - INFO - Request Parameters - Page 3:
2025-06-21 03:00:11,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:11,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:12,247 - INFO - Response - Page 3:
2025-06-21 03:00:12,450 - INFO - 第 3 页获取到 100 条记录
2025-06-21 03:00:12,450 - INFO - Request Parameters - Page 4:
2025-06-21 03:00:12,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:12,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:12,934 - INFO - Response - Page 4:
2025-06-21 03:00:13,138 - INFO - 第 4 页获取到 100 条记录
2025-06-21 03:00:13,138 - INFO - Request Parameters - Page 5:
2025-06-21 03:00:13,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:13,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:13,669 - INFO - Response - Page 5:
2025-06-21 03:00:13,872 - INFO - 第 5 页获取到 100 条记录
2025-06-21 03:00:13,872 - INFO - Request Parameters - Page 6:
2025-06-21 03:00:13,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:13,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:14,372 - INFO - Response - Page 6:
2025-06-21 03:00:14,575 - INFO - 第 6 页获取到 100 条记录
2025-06-21 03:00:14,575 - INFO - Request Parameters - Page 7:
2025-06-21 03:00:14,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:14,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:15,044 - INFO - Response - Page 7:
2025-06-21 03:00:15,247 - INFO - 第 7 页获取到 70 条记录
2025-06-21 03:00:15,247 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 03:00:15,247 - INFO - 获取到 670 条表单数据
2025-06-21 03:00:15,247 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 03:00:15,263 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:15,263 - INFO - 开始处理日期: 2025-03
2025-06-21 03:00:15,263 - INFO - Request Parameters - Page 1:
2025-06-21 03:00:15,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:15,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:15,716 - INFO - Response - Page 1:
2025-06-21 03:00:15,919 - INFO - 第 1 页获取到 100 条记录
2025-06-21 03:00:15,919 - INFO - Request Parameters - Page 2:
2025-06-21 03:00:15,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:15,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:16,466 - INFO - Response - Page 2:
2025-06-21 03:00:16,669 - INFO - 第 2 页获取到 100 条记录
2025-06-21 03:00:16,669 - INFO - Request Parameters - Page 3:
2025-06-21 03:00:16,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:16,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:17,169 - INFO - Response - Page 3:
2025-06-21 03:00:17,372 - INFO - 第 3 页获取到 100 条记录
2025-06-21 03:00:17,372 - INFO - Request Parameters - Page 4:
2025-06-21 03:00:17,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:17,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:17,903 - INFO - Response - Page 4:
2025-06-21 03:00:18,107 - INFO - 第 4 页获取到 100 条记录
2025-06-21 03:00:18,107 - INFO - Request Parameters - Page 5:
2025-06-21 03:00:18,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:18,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:18,653 - INFO - Response - Page 5:
2025-06-21 03:00:18,857 - INFO - 第 5 页获取到 100 条记录
2025-06-21 03:00:18,857 - INFO - Request Parameters - Page 6:
2025-06-21 03:00:18,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:18,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:19,341 - INFO - Response - Page 6:
2025-06-21 03:00:19,544 - INFO - 第 6 页获取到 100 条记录
2025-06-21 03:00:19,544 - INFO - Request Parameters - Page 7:
2025-06-21 03:00:19,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:19,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:19,982 - INFO - Response - Page 7:
2025-06-21 03:00:20,185 - INFO - 第 7 页获取到 61 条记录
2025-06-21 03:00:20,185 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 03:00:20,185 - INFO - 获取到 661 条表单数据
2025-06-21 03:00:20,185 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 03:00:20,200 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:20,200 - INFO - 开始处理日期: 2025-04
2025-06-21 03:00:20,200 - INFO - Request Parameters - Page 1:
2025-06-21 03:00:20,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:20,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:20,732 - INFO - Response - Page 1:
2025-06-21 03:00:20,935 - INFO - 第 1 页获取到 100 条记录
2025-06-21 03:00:20,935 - INFO - Request Parameters - Page 2:
2025-06-21 03:00:20,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:20,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:21,388 - INFO - Response - Page 2:
2025-06-21 03:00:21,591 - INFO - 第 2 页获取到 100 条记录
2025-06-21 03:00:21,591 - INFO - Request Parameters - Page 3:
2025-06-21 03:00:21,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:21,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:22,138 - INFO - Response - Page 3:
2025-06-21 03:00:22,341 - INFO - 第 3 页获取到 100 条记录
2025-06-21 03:00:22,341 - INFO - Request Parameters - Page 4:
2025-06-21 03:00:22,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:22,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:22,873 - INFO - Response - Page 4:
2025-06-21 03:00:23,076 - INFO - 第 4 页获取到 100 条记录
2025-06-21 03:00:23,076 - INFO - Request Parameters - Page 5:
2025-06-21 03:00:23,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:23,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:23,591 - INFO - Response - Page 5:
2025-06-21 03:00:23,794 - INFO - 第 5 页获取到 100 条记录
2025-06-21 03:00:23,794 - INFO - Request Parameters - Page 6:
2025-06-21 03:00:23,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:23,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:24,279 - INFO - Response - Page 6:
2025-06-21 03:00:24,482 - INFO - 第 6 页获取到 100 条记录
2025-06-21 03:00:24,482 - INFO - Request Parameters - Page 7:
2025-06-21 03:00:24,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:24,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:24,951 - INFO - Response - Page 7:
2025-06-21 03:00:25,154 - INFO - 第 7 页获取到 56 条记录
2025-06-21 03:00:25,154 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 03:00:25,154 - INFO - 获取到 656 条表单数据
2025-06-21 03:00:25,154 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 03:00:25,170 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:25,170 - INFO - 开始处理日期: 2025-05
2025-06-21 03:00:25,170 - INFO - Request Parameters - Page 1:
2025-06-21 03:00:25,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:25,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:25,670 - INFO - Response - Page 1:
2025-06-21 03:00:25,873 - INFO - 第 1 页获取到 100 条记录
2025-06-21 03:00:25,873 - INFO - Request Parameters - Page 2:
2025-06-21 03:00:25,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:25,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:26,341 - INFO - Response - Page 2:
2025-06-21 03:00:26,545 - INFO - 第 2 页获取到 100 条记录
2025-06-21 03:00:26,545 - INFO - Request Parameters - Page 3:
2025-06-21 03:00:26,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:26,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:27,029 - INFO - Response - Page 3:
2025-06-21 03:00:27,232 - INFO - 第 3 页获取到 100 条记录
2025-06-21 03:00:27,232 - INFO - Request Parameters - Page 4:
2025-06-21 03:00:27,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:27,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:27,732 - INFO - Response - Page 4:
2025-06-21 03:00:27,935 - INFO - 第 4 页获取到 100 条记录
2025-06-21 03:00:27,935 - INFO - Request Parameters - Page 5:
2025-06-21 03:00:27,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:27,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:28,435 - INFO - Response - Page 5:
2025-06-21 03:00:28,639 - INFO - 第 5 页获取到 100 条记录
2025-06-21 03:00:28,639 - INFO - Request Parameters - Page 6:
2025-06-21 03:00:28,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:28,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:29,185 - INFO - Response - Page 6:
2025-06-21 03:00:29,389 - INFO - 第 6 页获取到 100 条记录
2025-06-21 03:00:29,389 - INFO - Request Parameters - Page 7:
2025-06-21 03:00:29,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:29,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:29,842 - INFO - Response - Page 7:
2025-06-21 03:00:30,045 - INFO - 第 7 页获取到 65 条记录
2025-06-21 03:00:30,045 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 03:00:30,045 - INFO - 获取到 665 条表单数据
2025-06-21 03:00:30,045 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 03:00:30,061 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:30,061 - INFO - 开始处理日期: 2025-06
2025-06-21 03:00:30,061 - INFO - Request Parameters - Page 1:
2025-06-21 03:00:30,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:30,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:30,529 - INFO - Response - Page 1:
2025-06-21 03:00:30,732 - INFO - 第 1 页获取到 100 条记录
2025-06-21 03:00:30,732 - INFO - Request Parameters - Page 2:
2025-06-21 03:00:30,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:30,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:31,186 - INFO - Response - Page 2:
2025-06-21 03:00:31,389 - INFO - 第 2 页获取到 100 条记录
2025-06-21 03:00:31,389 - INFO - Request Parameters - Page 3:
2025-06-21 03:00:31,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:31,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:31,842 - INFO - Response - Page 3:
2025-06-21 03:00:32,045 - INFO - 第 3 页获取到 100 条记录
2025-06-21 03:00:32,045 - INFO - Request Parameters - Page 4:
2025-06-21 03:00:32,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:32,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:32,561 - INFO - Response - Page 4:
2025-06-21 03:00:32,764 - INFO - 第 4 页获取到 100 条记录
2025-06-21 03:00:32,764 - INFO - Request Parameters - Page 5:
2025-06-21 03:00:32,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:32,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:33,201 - INFO - Response - Page 5:
2025-06-21 03:00:33,404 - INFO - 第 5 页获取到 100 条记录
2025-06-21 03:00:33,404 - INFO - Request Parameters - Page 6:
2025-06-21 03:00:33,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:33,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:33,904 - INFO - Response - Page 6:
2025-06-21 03:00:34,108 - INFO - 第 6 页获取到 100 条记录
2025-06-21 03:00:34,108 - INFO - Request Parameters - Page 7:
2025-06-21 03:00:34,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 03:00:34,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 03:00:34,498 - INFO - Response - Page 7:
2025-06-21 03:00:34,701 - INFO - 第 7 页获取到 23 条记录
2025-06-21 03:00:34,701 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 03:00:34,701 - INFO - 获取到 623 条表单数据
2025-06-21 03:00:34,701 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 03:00:34,701 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-21 03:00:35,170 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-21 03:00:35,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133293.0, 'new_value': 137533.0}, {'field': 'total_amount', 'old_value': 133293.0, 'new_value': 137533.0}, {'field': 'order_count', 'old_value': 2116, 'new_value': 2208}]
2025-06-21 03:00:35,186 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-21 03:00:35,670 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-21 03:00:35,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218555.0, 'new_value': 227259.0}, {'field': 'total_amount', 'old_value': 253555.0, 'new_value': 262259.0}, {'field': 'order_count', 'old_value': 6378, 'new_value': 6488}]
2025-06-21 03:00:35,670 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-21 03:00:36,139 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-21 03:00:36,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158830.98, 'new_value': 166763.77}, {'field': 'total_amount', 'old_value': 158830.98, 'new_value': 166763.77}, {'field': 'order_count', 'old_value': 1262, 'new_value': 1346}]
2025-06-21 03:00:36,139 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-21 03:00:36,639 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-21 03:00:36,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192893.71, 'new_value': 203063.65}, {'field': 'total_amount', 'old_value': 192893.71, 'new_value': 203063.65}, {'field': 'order_count', 'old_value': 4073, 'new_value': 4249}]
2025-06-21 03:00:36,639 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-21 03:00:37,108 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-21 03:00:37,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3081.0, 'new_value': 3107.0}, {'field': 'offline_amount', 'old_value': 19905.4, 'new_value': 20343.4}, {'field': 'total_amount', 'old_value': 22986.4, 'new_value': 23450.4}, {'field': 'order_count', 'old_value': 772, 'new_value': 791}]
2025-06-21 03:00:37,108 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-21 03:00:37,623 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-21 03:00:37,623 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105635.29, 'new_value': 111784.53}, {'field': 'offline_amount', 'old_value': 153491.69, 'new_value': 161108.81}, {'field': 'total_amount', 'old_value': 259126.98, 'new_value': 272893.34}, {'field': 'order_count', 'old_value': 8649, 'new_value': 9136}]
2025-06-21 03:00:37,623 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-21 03:00:38,045 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-21 03:00:38,045 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51557.7, 'new_value': 55557.7}, {'field': 'offline_amount', 'old_value': 77812.19, 'new_value': 78432.21}, {'field': 'total_amount', 'old_value': 129369.89, 'new_value': 133989.91}, {'field': 'order_count', 'old_value': 3050, 'new_value': 3161}]
2025-06-21 03:00:38,045 - INFO - 日期 2025-06 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:38,045 - INFO - 数据同步完成！更新: 7 条，插入: 0 条，错误: 0 条
2025-06-21 03:00:38,045 - INFO - =================同步完成====================
2025-06-21 06:00:02,909 - INFO - =================使用默认全量同步=============
2025-06-21 06:00:04,644 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 06:00:04,644 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 06:00:04,675 - INFO - 开始处理日期: 2025-01
2025-06-21 06:00:04,675 - INFO - Request Parameters - Page 1:
2025-06-21 06:00:04,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:04,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:05,815 - INFO - Response - Page 1:
2025-06-21 06:00:06,019 - INFO - 第 1 页获取到 100 条记录
2025-06-21 06:00:06,019 - INFO - Request Parameters - Page 2:
2025-06-21 06:00:06,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:06,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:07,019 - INFO - Response - Page 2:
2025-06-21 06:00:07,222 - INFO - 第 2 页获取到 100 条记录
2025-06-21 06:00:07,222 - INFO - Request Parameters - Page 3:
2025-06-21 06:00:07,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:07,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:07,800 - INFO - Response - Page 3:
2025-06-21 06:00:08,003 - INFO - 第 3 页获取到 100 条记录
2025-06-21 06:00:08,003 - INFO - Request Parameters - Page 4:
2025-06-21 06:00:08,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:08,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:08,597 - INFO - Response - Page 4:
2025-06-21 06:00:08,800 - INFO - 第 4 页获取到 100 条记录
2025-06-21 06:00:08,800 - INFO - Request Parameters - Page 5:
2025-06-21 06:00:08,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:08,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:09,425 - INFO - Response - Page 5:
2025-06-21 06:00:09,628 - INFO - 第 5 页获取到 100 条记录
2025-06-21 06:00:09,628 - INFO - Request Parameters - Page 6:
2025-06-21 06:00:09,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:09,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:10,112 - INFO - Response - Page 6:
2025-06-21 06:00:10,315 - INFO - 第 6 页获取到 100 条记录
2025-06-21 06:00:10,315 - INFO - Request Parameters - Page 7:
2025-06-21 06:00:10,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:10,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:10,815 - INFO - Response - Page 7:
2025-06-21 06:00:11,018 - INFO - 第 7 页获取到 82 条记录
2025-06-21 06:00:11,018 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 06:00:11,018 - INFO - 获取到 682 条表单数据
2025-06-21 06:00:11,018 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 06:00:11,034 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:11,034 - INFO - 开始处理日期: 2025-02
2025-06-21 06:00:11,034 - INFO - Request Parameters - Page 1:
2025-06-21 06:00:11,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:11,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:11,581 - INFO - Response - Page 1:
2025-06-21 06:00:11,784 - INFO - 第 1 页获取到 100 条记录
2025-06-21 06:00:11,784 - INFO - Request Parameters - Page 2:
2025-06-21 06:00:11,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:11,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:12,284 - INFO - Response - Page 2:
2025-06-21 06:00:12,487 - INFO - 第 2 页获取到 100 条记录
2025-06-21 06:00:12,487 - INFO - Request Parameters - Page 3:
2025-06-21 06:00:12,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:12,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:12,987 - INFO - Response - Page 3:
2025-06-21 06:00:13,190 - INFO - 第 3 页获取到 100 条记录
2025-06-21 06:00:13,190 - INFO - Request Parameters - Page 4:
2025-06-21 06:00:13,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:13,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:13,690 - INFO - Response - Page 4:
2025-06-21 06:00:13,893 - INFO - 第 4 页获取到 100 条记录
2025-06-21 06:00:13,893 - INFO - Request Parameters - Page 5:
2025-06-21 06:00:13,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:13,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:14,487 - INFO - Response - Page 5:
2025-06-21 06:00:14,690 - INFO - 第 5 页获取到 100 条记录
2025-06-21 06:00:14,690 - INFO - Request Parameters - Page 6:
2025-06-21 06:00:14,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:14,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:15,175 - INFO - Response - Page 6:
2025-06-21 06:00:15,378 - INFO - 第 6 页获取到 100 条记录
2025-06-21 06:00:15,378 - INFO - Request Parameters - Page 7:
2025-06-21 06:00:15,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:15,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:15,862 - INFO - Response - Page 7:
2025-06-21 06:00:16,065 - INFO - 第 7 页获取到 70 条记录
2025-06-21 06:00:16,065 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 06:00:16,065 - INFO - 获取到 670 条表单数据
2025-06-21 06:00:16,065 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 06:00:16,081 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:16,081 - INFO - 开始处理日期: 2025-03
2025-06-21 06:00:16,081 - INFO - Request Parameters - Page 1:
2025-06-21 06:00:16,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:16,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:16,659 - INFO - Response - Page 1:
2025-06-21 06:00:16,862 - INFO - 第 1 页获取到 100 条记录
2025-06-21 06:00:16,862 - INFO - Request Parameters - Page 2:
2025-06-21 06:00:16,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:16,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:17,362 - INFO - Response - Page 2:
2025-06-21 06:00:17,565 - INFO - 第 2 页获取到 100 条记录
2025-06-21 06:00:17,565 - INFO - Request Parameters - Page 3:
2025-06-21 06:00:17,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:17,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:18,065 - INFO - Response - Page 3:
2025-06-21 06:00:18,268 - INFO - 第 3 页获取到 100 条记录
2025-06-21 06:00:18,268 - INFO - Request Parameters - Page 4:
2025-06-21 06:00:18,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:18,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:18,800 - INFO - Response - Page 4:
2025-06-21 06:00:19,003 - INFO - 第 4 页获取到 100 条记录
2025-06-21 06:00:19,003 - INFO - Request Parameters - Page 5:
2025-06-21 06:00:19,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:19,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:19,534 - INFO - Response - Page 5:
2025-06-21 06:00:19,737 - INFO - 第 5 页获取到 100 条记录
2025-06-21 06:00:19,737 - INFO - Request Parameters - Page 6:
2025-06-21 06:00:19,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:19,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:20,222 - INFO - Response - Page 6:
2025-06-21 06:00:20,425 - INFO - 第 6 页获取到 100 条记录
2025-06-21 06:00:20,425 - INFO - Request Parameters - Page 7:
2025-06-21 06:00:20,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:20,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:20,847 - INFO - Response - Page 7:
2025-06-21 06:00:21,050 - INFO - 第 7 页获取到 61 条记录
2025-06-21 06:00:21,050 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 06:00:21,050 - INFO - 获取到 661 条表单数据
2025-06-21 06:00:21,050 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 06:00:21,065 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:21,065 - INFO - 开始处理日期: 2025-04
2025-06-21 06:00:21,065 - INFO - Request Parameters - Page 1:
2025-06-21 06:00:21,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:21,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:21,550 - INFO - Response - Page 1:
2025-06-21 06:00:21,753 - INFO - 第 1 页获取到 100 条记录
2025-06-21 06:00:21,753 - INFO - Request Parameters - Page 2:
2025-06-21 06:00:21,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:21,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:22,206 - INFO - Response - Page 2:
2025-06-21 06:00:22,409 - INFO - 第 2 页获取到 100 条记录
2025-06-21 06:00:22,409 - INFO - Request Parameters - Page 3:
2025-06-21 06:00:22,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:22,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:22,909 - INFO - Response - Page 3:
2025-06-21 06:00:23,112 - INFO - 第 3 页获取到 100 条记录
2025-06-21 06:00:23,112 - INFO - Request Parameters - Page 4:
2025-06-21 06:00:23,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:23,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:23,628 - INFO - Response - Page 4:
2025-06-21 06:00:23,831 - INFO - 第 4 页获取到 100 条记录
2025-06-21 06:00:23,831 - INFO - Request Parameters - Page 5:
2025-06-21 06:00:23,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:23,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:24,378 - INFO - Response - Page 5:
2025-06-21 06:00:24,581 - INFO - 第 5 页获取到 100 条记录
2025-06-21 06:00:24,581 - INFO - Request Parameters - Page 6:
2025-06-21 06:00:24,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:24,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:25,081 - INFO - Response - Page 6:
2025-06-21 06:00:25,284 - INFO - 第 6 页获取到 100 条记录
2025-06-21 06:00:25,284 - INFO - Request Parameters - Page 7:
2025-06-21 06:00:25,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:25,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:25,722 - INFO - Response - Page 7:
2025-06-21 06:00:25,925 - INFO - 第 7 页获取到 56 条记录
2025-06-21 06:00:25,925 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 06:00:25,925 - INFO - 获取到 656 条表单数据
2025-06-21 06:00:25,925 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 06:00:25,940 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:25,940 - INFO - 开始处理日期: 2025-05
2025-06-21 06:00:25,940 - INFO - Request Parameters - Page 1:
2025-06-21 06:00:25,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:25,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:26,472 - INFO - Response - Page 1:
2025-06-21 06:00:26,675 - INFO - 第 1 页获取到 100 条记录
2025-06-21 06:00:26,675 - INFO - Request Parameters - Page 2:
2025-06-21 06:00:26,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:26,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:27,300 - INFO - Response - Page 2:
2025-06-21 06:00:27,503 - INFO - 第 2 页获取到 100 条记录
2025-06-21 06:00:27,503 - INFO - Request Parameters - Page 3:
2025-06-21 06:00:27,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:27,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:27,987 - INFO - Response - Page 3:
2025-06-21 06:00:28,190 - INFO - 第 3 页获取到 100 条记录
2025-06-21 06:00:28,190 - INFO - Request Parameters - Page 4:
2025-06-21 06:00:28,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:28,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:28,690 - INFO - Response - Page 4:
2025-06-21 06:00:28,893 - INFO - 第 4 页获取到 100 条记录
2025-06-21 06:00:28,893 - INFO - Request Parameters - Page 5:
2025-06-21 06:00:28,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:28,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:29,409 - INFO - Response - Page 5:
2025-06-21 06:00:29,612 - INFO - 第 5 页获取到 100 条记录
2025-06-21 06:00:29,612 - INFO - Request Parameters - Page 6:
2025-06-21 06:00:29,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:29,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:30,143 - INFO - Response - Page 6:
2025-06-21 06:00:30,346 - INFO - 第 6 页获取到 100 条记录
2025-06-21 06:00:30,346 - INFO - Request Parameters - Page 7:
2025-06-21 06:00:30,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:30,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:30,831 - INFO - Response - Page 7:
2025-06-21 06:00:31,034 - INFO - 第 7 页获取到 65 条记录
2025-06-21 06:00:31,034 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 06:00:31,034 - INFO - 获取到 665 条表单数据
2025-06-21 06:00:31,034 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 06:00:31,050 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:31,050 - INFO - 开始处理日期: 2025-06
2025-06-21 06:00:31,050 - INFO - Request Parameters - Page 1:
2025-06-21 06:00:31,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:31,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:31,581 - INFO - Response - Page 1:
2025-06-21 06:00:31,784 - INFO - 第 1 页获取到 100 条记录
2025-06-21 06:00:31,784 - INFO - Request Parameters - Page 2:
2025-06-21 06:00:31,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:31,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:32,268 - INFO - Response - Page 2:
2025-06-21 06:00:32,471 - INFO - 第 2 页获取到 100 条记录
2025-06-21 06:00:32,471 - INFO - Request Parameters - Page 3:
2025-06-21 06:00:32,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:32,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:32,971 - INFO - Response - Page 3:
2025-06-21 06:00:33,175 - INFO - 第 3 页获取到 100 条记录
2025-06-21 06:00:33,175 - INFO - Request Parameters - Page 4:
2025-06-21 06:00:33,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:33,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:33,628 - INFO - Response - Page 4:
2025-06-21 06:00:33,831 - INFO - 第 4 页获取到 100 条记录
2025-06-21 06:00:33,831 - INFO - Request Parameters - Page 5:
2025-06-21 06:00:33,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:33,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:34,300 - INFO - Response - Page 5:
2025-06-21 06:00:34,503 - INFO - 第 5 页获取到 100 条记录
2025-06-21 06:00:34,503 - INFO - Request Parameters - Page 6:
2025-06-21 06:00:34,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:34,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:35,081 - INFO - Response - Page 6:
2025-06-21 06:00:35,284 - INFO - 第 6 页获取到 100 条记录
2025-06-21 06:00:35,284 - INFO - Request Parameters - Page 7:
2025-06-21 06:00:35,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 06:00:35,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 06:00:35,628 - INFO - Response - Page 7:
2025-06-21 06:00:35,831 - INFO - 第 7 页获取到 23 条记录
2025-06-21 06:00:35,831 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 06:00:35,831 - INFO - 获取到 623 条表单数据
2025-06-21 06:00:35,831 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 06:00:35,846 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:35,846 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 06:00:35,846 - INFO - =================同步完成====================
2025-06-21 09:00:03,148 - INFO - =================使用默认全量同步=============
2025-06-21 09:00:04,929 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 09:00:04,929 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 09:00:04,961 - INFO - 开始处理日期: 2025-01
2025-06-21 09:00:04,961 - INFO - Request Parameters - Page 1:
2025-06-21 09:00:04,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:04,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:06,507 - INFO - Response - Page 1:
2025-06-21 09:00:06,711 - INFO - 第 1 页获取到 100 条记录
2025-06-21 09:00:06,711 - INFO - Request Parameters - Page 2:
2025-06-21 09:00:06,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:06,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:07,257 - INFO - Response - Page 2:
2025-06-21 09:00:07,461 - INFO - 第 2 页获取到 100 条记录
2025-06-21 09:00:07,461 - INFO - Request Parameters - Page 3:
2025-06-21 09:00:07,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:07,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:07,976 - INFO - Response - Page 3:
2025-06-21 09:00:08,179 - INFO - 第 3 页获取到 100 条记录
2025-06-21 09:00:08,179 - INFO - Request Parameters - Page 4:
2025-06-21 09:00:08,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:08,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:08,804 - INFO - Response - Page 4:
2025-06-21 09:00:09,007 - INFO - 第 4 页获取到 100 条记录
2025-06-21 09:00:09,007 - INFO - Request Parameters - Page 5:
2025-06-21 09:00:09,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:09,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:09,492 - INFO - Response - Page 5:
2025-06-21 09:00:09,695 - INFO - 第 5 页获取到 100 条记录
2025-06-21 09:00:09,695 - INFO - Request Parameters - Page 6:
2025-06-21 09:00:09,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:09,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:10,211 - INFO - Response - Page 6:
2025-06-21 09:00:10,414 - INFO - 第 6 页获取到 100 条记录
2025-06-21 09:00:10,414 - INFO - Request Parameters - Page 7:
2025-06-21 09:00:10,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:10,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:10,945 - INFO - Response - Page 7:
2025-06-21 09:00:11,148 - INFO - 第 7 页获取到 82 条记录
2025-06-21 09:00:11,148 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 09:00:11,148 - INFO - 获取到 682 条表单数据
2025-06-21 09:00:11,148 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 09:00:11,164 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 09:00:11,164 - INFO - 开始处理日期: 2025-02
2025-06-21 09:00:11,164 - INFO - Request Parameters - Page 1:
2025-06-21 09:00:11,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:11,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:11,664 - INFO - Response - Page 1:
2025-06-21 09:00:11,867 - INFO - 第 1 页获取到 100 条记录
2025-06-21 09:00:11,867 - INFO - Request Parameters - Page 2:
2025-06-21 09:00:11,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:11,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:12,414 - INFO - Response - Page 2:
2025-06-21 09:00:12,617 - INFO - 第 2 页获取到 100 条记录
2025-06-21 09:00:12,617 - INFO - Request Parameters - Page 3:
2025-06-21 09:00:12,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:12,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:13,117 - INFO - Response - Page 3:
2025-06-21 09:00:13,320 - INFO - 第 3 页获取到 100 条记录
2025-06-21 09:00:13,320 - INFO - Request Parameters - Page 4:
2025-06-21 09:00:13,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:13,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:13,867 - INFO - Response - Page 4:
2025-06-21 09:00:14,070 - INFO - 第 4 页获取到 100 条记录
2025-06-21 09:00:14,070 - INFO - Request Parameters - Page 5:
2025-06-21 09:00:14,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:14,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:14,585 - INFO - Response - Page 5:
2025-06-21 09:00:14,789 - INFO - 第 5 页获取到 100 条记录
2025-06-21 09:00:14,789 - INFO - Request Parameters - Page 6:
2025-06-21 09:00:14,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:14,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:15,273 - INFO - Response - Page 6:
2025-06-21 09:00:15,476 - INFO - 第 6 页获取到 100 条记录
2025-06-21 09:00:15,476 - INFO - Request Parameters - Page 7:
2025-06-21 09:00:15,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:15,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:15,976 - INFO - Response - Page 7:
2025-06-21 09:00:16,179 - INFO - 第 7 页获取到 70 条记录
2025-06-21 09:00:16,179 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 09:00:16,179 - INFO - 获取到 670 条表单数据
2025-06-21 09:00:16,179 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 09:00:16,195 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 09:00:16,195 - INFO - 开始处理日期: 2025-03
2025-06-21 09:00:16,195 - INFO - Request Parameters - Page 1:
2025-06-21 09:00:16,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:16,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:16,710 - INFO - Response - Page 1:
2025-06-21 09:00:16,914 - INFO - 第 1 页获取到 100 条记录
2025-06-21 09:00:16,914 - INFO - Request Parameters - Page 2:
2025-06-21 09:00:16,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:16,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:17,492 - INFO - Response - Page 2:
2025-06-21 09:00:17,695 - INFO - 第 2 页获取到 100 条记录
2025-06-21 09:00:17,695 - INFO - Request Parameters - Page 3:
2025-06-21 09:00:17,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:17,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:18,195 - INFO - Response - Page 3:
2025-06-21 09:00:18,414 - INFO - 第 3 页获取到 100 条记录
2025-06-21 09:00:18,414 - INFO - Request Parameters - Page 4:
2025-06-21 09:00:18,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:18,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:18,976 - INFO - Response - Page 4:
2025-06-21 09:00:19,179 - INFO - 第 4 页获取到 100 条记录
2025-06-21 09:00:19,179 - INFO - Request Parameters - Page 5:
2025-06-21 09:00:19,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:19,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:19,710 - INFO - Response - Page 5:
2025-06-21 09:00:19,914 - INFO - 第 5 页获取到 100 条记录
2025-06-21 09:00:19,914 - INFO - Request Parameters - Page 6:
2025-06-21 09:00:19,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:19,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:20,429 - INFO - Response - Page 6:
2025-06-21 09:00:20,632 - INFO - 第 6 页获取到 100 条记录
2025-06-21 09:00:20,632 - INFO - Request Parameters - Page 7:
2025-06-21 09:00:20,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:20,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:21,070 - INFO - Response - Page 7:
2025-06-21 09:00:21,273 - INFO - 第 7 页获取到 61 条记录
2025-06-21 09:00:21,273 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 09:00:21,273 - INFO - 获取到 661 条表单数据
2025-06-21 09:00:21,289 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 09:00:21,304 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 09:00:21,304 - INFO - 开始处理日期: 2025-04
2025-06-21 09:00:21,304 - INFO - Request Parameters - Page 1:
2025-06-21 09:00:21,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:21,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:21,898 - INFO - Response - Page 1:
2025-06-21 09:00:22,101 - INFO - 第 1 页获取到 100 条记录
2025-06-21 09:00:22,101 - INFO - Request Parameters - Page 2:
2025-06-21 09:00:22,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:22,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:22,632 - INFO - Response - Page 2:
2025-06-21 09:00:22,835 - INFO - 第 2 页获取到 100 条记录
2025-06-21 09:00:22,835 - INFO - Request Parameters - Page 3:
2025-06-21 09:00:22,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:22,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:23,351 - INFO - Response - Page 3:
2025-06-21 09:00:23,554 - INFO - 第 3 页获取到 100 条记录
2025-06-21 09:00:23,554 - INFO - Request Parameters - Page 4:
2025-06-21 09:00:23,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:23,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:24,070 - INFO - Response - Page 4:
2025-06-21 09:00:24,273 - INFO - 第 4 页获取到 100 条记录
2025-06-21 09:00:24,273 - INFO - Request Parameters - Page 5:
2025-06-21 09:00:24,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:24,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:24,726 - INFO - Response - Page 5:
2025-06-21 09:00:24,929 - INFO - 第 5 页获取到 100 条记录
2025-06-21 09:00:24,929 - INFO - Request Parameters - Page 6:
2025-06-21 09:00:24,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:24,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:25,398 - INFO - Response - Page 6:
2025-06-21 09:00:25,601 - INFO - 第 6 页获取到 100 条记录
2025-06-21 09:00:25,601 - INFO - Request Parameters - Page 7:
2025-06-21 09:00:25,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:25,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:26,085 - INFO - Response - Page 7:
2025-06-21 09:00:26,289 - INFO - 第 7 页获取到 56 条记录
2025-06-21 09:00:26,289 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 09:00:26,289 - INFO - 获取到 656 条表单数据
2025-06-21 09:00:26,289 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 09:00:26,304 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 09:00:26,304 - INFO - 开始处理日期: 2025-05
2025-06-21 09:00:26,304 - INFO - Request Parameters - Page 1:
2025-06-21 09:00:26,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:26,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:26,804 - INFO - Response - Page 1:
2025-06-21 09:00:27,007 - INFO - 第 1 页获取到 100 条记录
2025-06-21 09:00:27,007 - INFO - Request Parameters - Page 2:
2025-06-21 09:00:27,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:27,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:27,492 - INFO - Response - Page 2:
2025-06-21 09:00:27,695 - INFO - 第 2 页获取到 100 条记录
2025-06-21 09:00:27,695 - INFO - Request Parameters - Page 3:
2025-06-21 09:00:27,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:27,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:28,335 - INFO - Response - Page 3:
2025-06-21 09:00:28,539 - INFO - 第 3 页获取到 100 条记录
2025-06-21 09:00:28,539 - INFO - Request Parameters - Page 4:
2025-06-21 09:00:28,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:28,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:28,976 - INFO - Response - Page 4:
2025-06-21 09:00:29,179 - INFO - 第 4 页获取到 100 条记录
2025-06-21 09:00:29,179 - INFO - Request Parameters - Page 5:
2025-06-21 09:00:29,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:29,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:29,710 - INFO - Response - Page 5:
2025-06-21 09:00:29,914 - INFO - 第 5 页获取到 100 条记录
2025-06-21 09:00:29,914 - INFO - Request Parameters - Page 6:
2025-06-21 09:00:29,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:29,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:30,460 - INFO - Response - Page 6:
2025-06-21 09:00:30,663 - INFO - 第 6 页获取到 100 条记录
2025-06-21 09:00:30,663 - INFO - Request Parameters - Page 7:
2025-06-21 09:00:30,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:30,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:31,164 - INFO - Response - Page 7:
2025-06-21 09:00:31,367 - INFO - 第 7 页获取到 65 条记录
2025-06-21 09:00:31,367 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 09:00:31,367 - INFO - 获取到 665 条表单数据
2025-06-21 09:00:31,382 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 09:00:31,382 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 09:00:31,382 - INFO - 开始处理日期: 2025-06
2025-06-21 09:00:31,382 - INFO - Request Parameters - Page 1:
2025-06-21 09:00:31,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:31,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:31,976 - INFO - Response - Page 1:
2025-06-21 09:00:32,179 - INFO - 第 1 页获取到 100 条记录
2025-06-21 09:00:32,179 - INFO - Request Parameters - Page 2:
2025-06-21 09:00:32,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:32,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:32,726 - INFO - Response - Page 2:
2025-06-21 09:00:32,929 - INFO - 第 2 页获取到 100 条记录
2025-06-21 09:00:32,929 - INFO - Request Parameters - Page 3:
2025-06-21 09:00:32,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:32,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:33,398 - INFO - Response - Page 3:
2025-06-21 09:00:33,601 - INFO - 第 3 页获取到 100 条记录
2025-06-21 09:00:33,601 - INFO - Request Parameters - Page 4:
2025-06-21 09:00:33,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:33,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:34,148 - INFO - Response - Page 4:
2025-06-21 09:00:34,351 - INFO - 第 4 页获取到 100 条记录
2025-06-21 09:00:34,351 - INFO - Request Parameters - Page 5:
2025-06-21 09:00:34,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:34,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:34,882 - INFO - Response - Page 5:
2025-06-21 09:00:35,085 - INFO - 第 5 页获取到 100 条记录
2025-06-21 09:00:35,085 - INFO - Request Parameters - Page 6:
2025-06-21 09:00:35,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:35,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:35,570 - INFO - Response - Page 6:
2025-06-21 09:00:35,773 - INFO - 第 6 页获取到 100 条记录
2025-06-21 09:00:35,773 - INFO - Request Parameters - Page 7:
2025-06-21 09:00:35,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 09:00:35,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 09:00:36,132 - INFO - Response - Page 7:
2025-06-21 09:00:36,335 - INFO - 第 7 页获取到 23 条记录
2025-06-21 09:00:36,335 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 09:00:36,335 - INFO - 获取到 623 条表单数据
2025-06-21 09:00:36,335 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 09:00:36,335 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-21 09:00:36,788 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-21 09:00:36,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157400.0, 'new_value': 167800.0}, {'field': 'total_amount', 'old_value': 157400.0, 'new_value': 167800.0}]
2025-06-21 09:00:36,788 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-21 09:00:37,257 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-21 09:00:37,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209610.0, 'new_value': 239130.0}, {'field': 'total_amount', 'old_value': 231790.0, 'new_value': 261310.0}, {'field': 'order_count', 'old_value': 163, 'new_value': 178}]
2025-06-21 09:00:37,257 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-21 09:00:37,726 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-21 09:00:37,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145320.0, 'new_value': 155280.0}, {'field': 'total_amount', 'old_value': 145320.0, 'new_value': 155280.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-21 09:00:37,726 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-21 09:00:38,195 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-21 09:00:38,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5670.0, 'new_value': 5958.0}, {'field': 'offline_amount', 'old_value': 64770.0, 'new_value': 69665.0}, {'field': 'total_amount', 'old_value': 70440.0, 'new_value': 75623.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 86}]
2025-06-21 09:00:38,195 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-21 09:00:38,663 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-21 09:00:38,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153377.0, 'new_value': 155307.0}, {'field': 'total_amount', 'old_value': 153377.0, 'new_value': 155307.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-21 09:00:38,663 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-21 09:00:39,148 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-21 09:00:39,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204121.9, 'new_value': 208013.8}, {'field': 'total_amount', 'old_value': 204121.9, 'new_value': 208013.8}, {'field': 'order_count', 'old_value': 3013, 'new_value': 3125}]
2025-06-21 09:00:39,148 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-21 09:00:39,648 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-21 09:00:39,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3320.0, 'new_value': 3405.0}, {'field': 'offline_amount', 'old_value': 43194.47, 'new_value': 43778.47}, {'field': 'total_amount', 'old_value': 46514.47, 'new_value': 47183.47}, {'field': 'order_count', 'old_value': 388, 'new_value': 397}]
2025-06-21 09:00:39,648 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-21 09:00:40,117 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-21 09:00:40,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36732.77, 'new_value': 38930.17}, {'field': 'offline_amount', 'old_value': 86093.62, 'new_value': 90652.62}, {'field': 'total_amount', 'old_value': 122826.39, 'new_value': 129582.79}, {'field': 'order_count', 'old_value': 1399, 'new_value': 1489}]
2025-06-21 09:00:40,117 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-21 09:00:40,554 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-21 09:00:40,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26695.0, 'new_value': 27663.0}, {'field': 'offline_amount', 'old_value': 47793.08, 'new_value': 47915.08}, {'field': 'total_amount', 'old_value': 74488.08, 'new_value': 75578.08}, {'field': 'order_count', 'old_value': 99, 'new_value': 103}]
2025-06-21 09:00:40,554 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-21 09:00:41,023 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-21 09:00:41,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47134.0, 'new_value': 49358.0}, {'field': 'total_amount', 'old_value': 47134.0, 'new_value': 49358.0}, {'field': 'order_count', 'old_value': 885, 'new_value': 935}]
2025-06-21 09:00:41,023 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-21 09:00:41,492 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-21 09:00:41,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109935.0, 'new_value': 115225.0}, {'field': 'offline_amount', 'old_value': 39751.54, 'new_value': 41127.54}, {'field': 'total_amount', 'old_value': 149686.54, 'new_value': 156352.54}, {'field': 'order_count', 'old_value': 1009, 'new_value': 1053}]
2025-06-21 09:00:41,492 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-21 09:00:41,945 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-21 09:00:41,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12992.0, 'new_value': 15972.0}, {'field': 'total_amount', 'old_value': 13560.2, 'new_value': 16540.2}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-21 09:00:41,945 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-21 09:00:42,492 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-21 09:00:42,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4215.0, 'new_value': 4463.0}, {'field': 'total_amount', 'old_value': 8249.0, 'new_value': 8497.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 147}]
2025-06-21 09:00:42,492 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-21 09:00:42,960 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-21 09:00:42,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19130.38, 'new_value': 19993.32}, {'field': 'offline_amount', 'old_value': 21295.13, 'new_value': 22449.72}, {'field': 'total_amount', 'old_value': 40425.51, 'new_value': 42443.04}, {'field': 'order_count', 'old_value': 1959, 'new_value': 2073}]
2025-06-21 09:00:42,960 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-21 09:00:43,460 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-21 09:00:43,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85655.5, 'new_value': 85754.5}, {'field': 'total_amount', 'old_value': 85655.5, 'new_value': 85754.5}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-06-21 09:00:43,460 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-21 09:00:43,945 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-21 09:00:43,945 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 68, 'new_value': 5228}]
2025-06-21 09:00:43,945 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-21 09:00:44,367 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-21 09:00:44,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136538.0, 'new_value': 140842.0}, {'field': 'total_amount', 'old_value': 136538.0, 'new_value': 140842.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 257}]
2025-06-21 09:00:44,367 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-21 09:00:44,835 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-21 09:00:44,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8060.39, 'new_value': 8438.46}, {'field': 'offline_amount', 'old_value': 121556.37, 'new_value': 127158.39}, {'field': 'total_amount', 'old_value': 129616.76, 'new_value': 135596.85}, {'field': 'order_count', 'old_value': 1453, 'new_value': 1529}]
2025-06-21 09:00:44,835 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-21 09:00:45,304 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-21 09:00:45,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58343.0, 'new_value': 58859.0}, {'field': 'total_amount', 'old_value': 64803.4, 'new_value': 65319.4}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-06-21 09:00:45,304 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-21 09:00:45,710 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-21 09:00:45,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7349.76, 'new_value': 7479.26}, {'field': 'total_amount', 'old_value': 7349.76, 'new_value': 7479.26}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-21 09:00:45,710 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-21 09:00:46,210 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-21 09:00:46,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102692.89, 'new_value': 107028.97}, {'field': 'total_amount', 'old_value': 102692.89, 'new_value': 107028.97}, {'field': 'order_count', 'old_value': 516, 'new_value': 537}]
2025-06-21 09:00:46,210 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-21 09:00:46,632 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-21 09:00:46,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21929.0, 'new_value': 22499.0}, {'field': 'total_amount', 'old_value': 21929.0, 'new_value': 22499.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 105}]
2025-06-21 09:00:46,632 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-21 09:00:47,054 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-21 09:00:47,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139818.0, 'new_value': 148448.0}, {'field': 'total_amount', 'old_value': 139818.0, 'new_value': 148448.0}, {'field': 'order_count', 'old_value': 5384, 'new_value': 5733}]
2025-06-21 09:00:47,054 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-21 09:00:47,617 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-21 09:00:47,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121803.74, 'new_value': 128332.74}, {'field': 'total_amount', 'old_value': 121803.74, 'new_value': 128332.74}, {'field': 'order_count', 'old_value': 492, 'new_value': 509}]
2025-06-21 09:00:47,617 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-21 09:00:48,054 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-21 09:00:48,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97382.0, 'new_value': 103047.0}, {'field': 'offline_amount', 'old_value': 39952.28, 'new_value': 41734.18}, {'field': 'total_amount', 'old_value': 137334.28, 'new_value': 144781.18}, {'field': 'order_count', 'old_value': 972, 'new_value': 1013}]
2025-06-21 09:00:48,054 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-21 09:00:48,570 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-21 09:00:48,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106398.44, 'new_value': 109934.49}, {'field': 'total_amount', 'old_value': 106398.44, 'new_value': 109934.49}, {'field': 'order_count', 'old_value': 3740, 'new_value': 3879}]
2025-06-21 09:00:48,570 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-21 09:00:48,992 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-21 09:00:48,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40198.0, 'new_value': 43987.0}, {'field': 'offline_amount', 'old_value': 145350.0, 'new_value': 150368.0}, {'field': 'total_amount', 'old_value': 185548.0, 'new_value': 194355.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 146}]
2025-06-21 09:00:48,992 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-21 09:00:49,445 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-21 09:00:49,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12463.9, 'new_value': 13753.79}, {'field': 'offline_amount', 'old_value': 284710.66, 'new_value': 292443.88}, {'field': 'total_amount', 'old_value': 297174.56, 'new_value': 306197.67}, {'field': 'order_count', 'old_value': 1473, 'new_value': 1536}]
2025-06-21 09:00:49,445 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-21 09:00:49,898 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-21 09:00:49,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100327.08, 'new_value': 104379.25}, {'field': 'total_amount', 'old_value': 100327.08, 'new_value': 104379.25}, {'field': 'order_count', 'old_value': 1272, 'new_value': 1328}]
2025-06-21 09:00:49,898 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-21 09:00:50,320 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-21 09:00:50,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55395.96, 'new_value': 57584.06}, {'field': 'total_amount', 'old_value': 55395.96, 'new_value': 57584.06}, {'field': 'order_count', 'old_value': 231, 'new_value': 240}]
2025-06-21 09:00:50,320 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-21 09:00:50,773 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-21 09:00:50,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9683.29, 'new_value': 10082.29}, {'field': 'offline_amount', 'old_value': 18635.83, 'new_value': 19416.83}, {'field': 'total_amount', 'old_value': 28319.12, 'new_value': 29499.12}, {'field': 'order_count', 'old_value': 971, 'new_value': 1016}]
2025-06-21 09:00:50,773 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-21 09:00:51,210 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-21 09:00:51,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 296234.5, 'new_value': 307864.1}, {'field': 'offline_amount', 'old_value': 68910.9, 'new_value': 72065.7}, {'field': 'total_amount', 'old_value': 365145.4, 'new_value': 379929.8}, {'field': 'order_count', 'old_value': 461, 'new_value': 481}]
2025-06-21 09:00:51,210 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-21 09:00:51,632 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-21 09:00:51,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77500.74, 'new_value': 81945.19}, {'field': 'total_amount', 'old_value': 77500.74, 'new_value': 81945.19}, {'field': 'order_count', 'old_value': 2214, 'new_value': 2343}]
2025-06-21 09:00:51,648 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-21 09:00:52,101 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-21 09:00:52,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141619.71, 'new_value': 143318.71}, {'field': 'total_amount', 'old_value': 141619.71, 'new_value': 143318.71}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-06-21 09:00:52,101 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-21 09:00:52,570 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-21 09:00:52,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81620.0, 'new_value': 85100.0}, {'field': 'total_amount', 'old_value': 100700.0, 'new_value': 104180.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-21 09:00:52,570 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-21 09:00:53,038 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-21 09:00:53,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 276735.51, 'new_value': 291938.79}, {'field': 'total_amount', 'old_value': 276735.51, 'new_value': 291938.79}, {'field': 'order_count', 'old_value': 887, 'new_value': 933}]
2025-06-21 09:00:53,038 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-21 09:00:53,476 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-21 09:00:53,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31033.0, 'new_value': 31338.0}, {'field': 'total_amount', 'old_value': 31033.0, 'new_value': 31338.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-06-21 09:00:53,476 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-21 09:00:53,882 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-21 09:00:53,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52705.4, 'new_value': 54286.5}, {'field': 'total_amount', 'old_value': 71651.7, 'new_value': 73232.8}, {'field': 'order_count', 'old_value': 256, 'new_value': 262}]
2025-06-21 09:00:53,882 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-21 09:00:54,351 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-21 09:00:54,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55863.45, 'new_value': 57056.05}, {'field': 'total_amount', 'old_value': 67449.45, 'new_value': 68642.05}, {'field': 'order_count', 'old_value': 667, 'new_value': 687}]
2025-06-21 09:00:54,351 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-21 09:00:54,882 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-21 09:00:54,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3897.0, 'new_value': 4038.0}, {'field': 'offline_amount', 'old_value': 20757.6, 'new_value': 21975.5}, {'field': 'total_amount', 'old_value': 24654.6, 'new_value': 26013.5}, {'field': 'order_count', 'old_value': 888, 'new_value': 948}]
2025-06-21 09:00:54,882 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-21 09:00:55,320 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-21 09:00:55,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17619.92, 'new_value': 19143.91}, {'field': 'offline_amount', 'old_value': 35648.38, 'new_value': 37715.84}, {'field': 'total_amount', 'old_value': 53268.3, 'new_value': 56859.75}, {'field': 'order_count', 'old_value': 1956, 'new_value': 2097}]
2025-06-21 09:00:55,320 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-21 09:00:55,820 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-21 09:00:55,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14267.0, 'new_value': 14876.0}, {'field': 'total_amount', 'old_value': 14267.0, 'new_value': 14876.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 81}]
2025-06-21 09:00:55,820 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-21 09:00:56,288 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-21 09:00:56,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64838.24, 'new_value': 68015.96}, {'field': 'offline_amount', 'old_value': 110218.85, 'new_value': 117444.03}, {'field': 'total_amount', 'old_value': 175057.09, 'new_value': 185459.99}, {'field': 'order_count', 'old_value': 1092, 'new_value': 1157}]
2025-06-21 09:00:56,288 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-21 09:00:56,710 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-21 09:00:56,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57846.76, 'new_value': 60254.76}, {'field': 'total_amount', 'old_value': 57846.76, 'new_value': 60254.76}, {'field': 'order_count', 'old_value': 1532, 'new_value': 1597}]
2025-06-21 09:00:56,710 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-21 09:00:57,163 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-21 09:00:57,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47229.62, 'new_value': 49284.32}, {'field': 'total_amount', 'old_value': 74004.79, 'new_value': 76059.49}, {'field': 'order_count', 'old_value': 434, 'new_value': 442}]
2025-06-21 09:00:57,163 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-21 09:00:57,726 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-21 09:00:57,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11952.0, 'new_value': 12196.0}, {'field': 'total_amount', 'old_value': 11952.0, 'new_value': 12196.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-06-21 09:00:57,726 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-21 09:00:58,195 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-21 09:00:58,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29705.0, 'new_value': 31192.1}, {'field': 'total_amount', 'old_value': 29705.0, 'new_value': 31192.1}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-21 09:00:58,195 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-21 09:00:58,616 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-21 09:00:58,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38688.0, 'new_value': 41033.0}, {'field': 'total_amount', 'old_value': 38688.0, 'new_value': 41033.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 67}]
2025-06-21 09:00:58,616 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-21 09:00:59,210 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-21 09:00:59,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119828.97, 'new_value': 126748.68}, {'field': 'offline_amount', 'old_value': 114309.75, 'new_value': 116608.75}, {'field': 'total_amount', 'old_value': 234138.72, 'new_value': 243357.43}, {'field': 'order_count', 'old_value': 2130, 'new_value': 2239}]
2025-06-21 09:00:59,210 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-21 09:00:59,710 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-21 09:00:59,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26173.0, 'new_value': 30553.0}, {'field': 'total_amount', 'old_value': 26173.0, 'new_value': 30553.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-21 09:00:59,710 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-21 09:01:00,179 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-21 09:01:00,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97551.18, 'new_value': 102092.63}, {'field': 'total_amount', 'old_value': 97551.18, 'new_value': 102092.63}, {'field': 'order_count', 'old_value': 2784, 'new_value': 2918}]
2025-06-21 09:01:00,179 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-21 09:01:00,648 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-21 09:01:00,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20617.3, 'new_value': 21156.3}, {'field': 'total_amount', 'old_value': 20617.3, 'new_value': 21156.3}, {'field': 'order_count', 'old_value': 195, 'new_value': 197}]
2025-06-21 09:01:00,648 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-21 09:01:01,101 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-21 09:01:01,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35025.0, 'new_value': 37057.44}, {'field': 'total_amount', 'old_value': 35034.0, 'new_value': 37066.44}, {'field': 'order_count', 'old_value': 1442, 'new_value': 1526}]
2025-06-21 09:01:01,101 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-21 09:01:01,616 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-21 09:01:01,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274408.88, 'new_value': 287320.92}, {'field': 'total_amount', 'old_value': 310392.88, 'new_value': 323304.92}, {'field': 'order_count', 'old_value': 1643, 'new_value': 1695}]
2025-06-21 09:01:01,616 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-21 09:01:02,054 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-21 09:01:02,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59942.0, 'new_value': 61932.0}, {'field': 'total_amount', 'old_value': 62757.0, 'new_value': 64747.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 246}]
2025-06-21 09:01:02,054 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-21 09:01:02,507 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-21 09:01:02,507 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148217.18, 'new_value': 153695.16}, {'field': 'offline_amount', 'old_value': 29962.79, 'new_value': 31949.41}, {'field': 'total_amount', 'old_value': 178179.97, 'new_value': 185644.57}, {'field': 'order_count', 'old_value': 737, 'new_value': 767}]
2025-06-21 09:01:02,507 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-21 09:01:02,913 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-21 09:01:02,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48851.0, 'new_value': 52440.0}, {'field': 'total_amount', 'old_value': 48851.0, 'new_value': 52440.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 35}]
2025-06-21 09:01:02,929 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-21 09:01:03,413 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-21 09:01:03,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24816.1, 'new_value': 25264.1}, {'field': 'total_amount', 'old_value': 24816.1, 'new_value': 25264.1}, {'field': 'order_count', 'old_value': 70, 'new_value': 72}]
2025-06-21 09:01:03,413 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-21 09:01:03,866 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-21 09:01:03,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82219.0, 'new_value': 84154.0}, {'field': 'offline_amount', 'old_value': 338340.0, 'new_value': 346723.0}, {'field': 'total_amount', 'old_value': 420559.0, 'new_value': 430877.0}, {'field': 'order_count', 'old_value': 534, 'new_value': 550}]
2025-06-21 09:01:03,866 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-21 09:01:04,320 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-21 09:01:04,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1217154.01, 'new_value': 1275013.61}, {'field': 'total_amount', 'old_value': 1275632.71, 'new_value': 1333492.31}, {'field': 'order_count', 'old_value': 2441, 'new_value': 2565}]
2025-06-21 09:01:04,320 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-21 09:01:04,726 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-21 09:01:04,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23657.23, 'new_value': 24555.73}, {'field': 'total_amount', 'old_value': 23657.23, 'new_value': 24555.73}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-06-21 09:01:04,726 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-21 09:01:05,179 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-21 09:01:05,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94176.0, 'new_value': 97575.0}, {'field': 'total_amount', 'old_value': 94176.0, 'new_value': 97575.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-21 09:01:05,179 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-21 09:01:05,679 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-21 09:01:05,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 547076.0, 'new_value': 562846.0}, {'field': 'total_amount', 'old_value': 547076.0, 'new_value': 562846.0}, {'field': 'order_count', 'old_value': 2566, 'new_value': 2651}]
2025-06-21 09:01:05,679 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-21 09:01:06,195 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-21 09:01:06,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360890.0, 'new_value': 388552.0}, {'field': 'total_amount', 'old_value': 360890.0, 'new_value': 388552.0}, {'field': 'order_count', 'old_value': 372, 'new_value': 396}]
2025-06-21 09:01:06,195 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-21 09:01:06,695 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-21 09:01:06,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21670.7, 'new_value': 23049.67}, {'field': 'offline_amount', 'old_value': 20453.1, 'new_value': 20992.76}, {'field': 'total_amount', 'old_value': 42123.8, 'new_value': 44042.43}, {'field': 'order_count', 'old_value': 1929, 'new_value': 2032}]
2025-06-21 09:01:06,695 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-21 09:01:07,085 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-21 09:01:07,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104105.97, 'new_value': 110511.62}, {'field': 'total_amount', 'old_value': 112066.38, 'new_value': 118472.03}, {'field': 'order_count', 'old_value': 5633, 'new_value': 5933}]
2025-06-21 09:01:07,085 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-21 09:01:07,554 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-21 09:01:07,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41982.72, 'new_value': 44497.06}, {'field': 'offline_amount', 'old_value': 28271.47, 'new_value': 29584.92}, {'field': 'total_amount', 'old_value': 70254.19, 'new_value': 74081.98}, {'field': 'order_count', 'old_value': 4126, 'new_value': 4372}]
2025-06-21 09:01:07,554 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-21 09:01:08,007 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-21 09:01:08,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67535.58, 'new_value': 71902.91}, {'field': 'offline_amount', 'old_value': 73423.31, 'new_value': 76273.01}, {'field': 'total_amount', 'old_value': 140958.89, 'new_value': 148175.92}, {'field': 'order_count', 'old_value': 5811, 'new_value': 6076}]
2025-06-21 09:01:08,007 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-21 09:01:08,460 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-21 09:01:08,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20211.79, 'new_value': 21190.79}, {'field': 'total_amount', 'old_value': 20276.39, 'new_value': 21255.39}, {'field': 'order_count', 'old_value': 109, 'new_value': 114}]
2025-06-21 09:01:08,460 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-21 09:01:08,991 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-21 09:01:08,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 186432.0, 'new_value': 197892.0}, {'field': 'total_amount', 'old_value': 186432.0, 'new_value': 197892.0}, {'field': 'order_count', 'old_value': 15536, 'new_value': 16491}]
2025-06-21 09:01:08,991 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-21 09:01:09,429 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-21 09:01:09,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166216.45, 'new_value': 172969.15}, {'field': 'total_amount', 'old_value': 166216.45, 'new_value': 172969.15}, {'field': 'order_count', 'old_value': 598, 'new_value': 626}]
2025-06-21 09:01:09,429 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-21 09:01:09,882 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-21 09:01:09,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70896.1, 'new_value': 72874.0}, {'field': 'total_amount', 'old_value': 70896.1, 'new_value': 72874.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 156}]
2025-06-21 09:01:09,882 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-21 09:01:10,366 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-21 09:01:10,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260725.16, 'new_value': 270867.82}, {'field': 'total_amount', 'old_value': 260725.16, 'new_value': 270867.82}, {'field': 'order_count', 'old_value': 906, 'new_value': 936}]
2025-06-21 09:01:10,366 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-21 09:01:10,741 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-21 09:01:10,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120381.0, 'new_value': 128246.0}, {'field': 'total_amount', 'old_value': 129000.0, 'new_value': 136865.0}, {'field': 'order_count', 'old_value': 9286, 'new_value': 9866}]
2025-06-21 09:01:10,741 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-21 09:01:11,382 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-21 09:01:11,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2156.0, 'new_value': 2231.0}, {'field': 'total_amount', 'old_value': 9417.0, 'new_value': 9492.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-06-21 09:01:11,382 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-21 09:01:11,804 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-21 09:01:11,804 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112164.0, 'new_value': 121558.0}, {'field': 'offline_amount', 'old_value': 65996.0, 'new_value': 70243.0}, {'field': 'total_amount', 'old_value': 178160.0, 'new_value': 191801.0}, {'field': 'order_count', 'old_value': 2699, 'new_value': 2861}]
2025-06-21 09:01:11,804 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-21 09:01:12,273 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-21 09:01:12,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 200407.19, 'new_value': 212699.29}, {'field': 'offline_amount', 'old_value': 1033702.0, 'new_value': 1078020.6}, {'field': 'total_amount', 'old_value': 1234109.19, 'new_value': 1290719.89}, {'field': 'order_count', 'old_value': 5949, 'new_value': 6224}]
2025-06-21 09:01:12,273 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-21 09:01:12,679 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-21 09:01:12,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21789.94, 'new_value': 22488.94}, {'field': 'offline_amount', 'old_value': 24291.42, 'new_value': 25687.42}, {'field': 'total_amount', 'old_value': 46081.36, 'new_value': 48176.36}, {'field': 'order_count', 'old_value': 6649, 'new_value': 6660}]
2025-06-21 09:01:12,679 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-21 09:01:13,116 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-21 09:01:13,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120304.8, 'new_value': 137547.9}, {'field': 'total_amount', 'old_value': 231603.8, 'new_value': 248846.9}, {'field': 'order_count', 'old_value': 6250, 'new_value': 6712}]
2025-06-21 09:01:13,116 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-21 09:01:13,601 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-21 09:01:13,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374529.25, 'new_value': 392935.25}, {'field': 'total_amount', 'old_value': 374529.25, 'new_value': 392935.25}, {'field': 'order_count', 'old_value': 1435, 'new_value': 1522}]
2025-06-21 09:01:13,601 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-21 09:01:14,038 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-21 09:01:14,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253165.2, 'new_value': 266757.3}, {'field': 'total_amount', 'old_value': 253165.2, 'new_value': 266757.3}, {'field': 'order_count', 'old_value': 279, 'new_value': 293}]
2025-06-21 09:01:14,038 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-21 09:01:14,491 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-21 09:01:14,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1150237.0, 'new_value': 1193821.0}, {'field': 'total_amount', 'old_value': 1150237.0, 'new_value': 1193821.0}, {'field': 'order_count', 'old_value': 5317, 'new_value': 5531}]
2025-06-21 09:01:14,491 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-21 09:01:14,960 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-21 09:01:14,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80241.0, 'new_value': 85109.0}, {'field': 'total_amount', 'old_value': 80241.0, 'new_value': 85109.0}, {'field': 'order_count', 'old_value': 198, 'new_value': 211}]
2025-06-21 09:01:14,960 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-21 09:01:15,523 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-21 09:01:15,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98487.2, 'new_value': 102939.4}, {'field': 'offline_amount', 'old_value': 73125.0, 'new_value': 77187.9}, {'field': 'total_amount', 'old_value': 171612.2, 'new_value': 180127.3}, {'field': 'order_count', 'old_value': 4157, 'new_value': 4362}]
2025-06-21 09:01:15,523 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-21 09:01:15,944 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-21 09:01:15,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 605871.0, 'new_value': 633712.0}, {'field': 'total_amount', 'old_value': 605871.0, 'new_value': 633712.0}, {'field': 'order_count', 'old_value': 790, 'new_value': 821}]
2025-06-21 09:01:15,944 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-21 09:01:16,413 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-21 09:01:16,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71226.6, 'new_value': 75201.4}, {'field': 'total_amount', 'old_value': 71226.6, 'new_value': 75201.4}, {'field': 'order_count', 'old_value': 324, 'new_value': 341}]
2025-06-21 09:01:16,413 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-21 09:01:16,866 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-21 09:01:16,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30143.49, 'new_value': 32194.71}, {'field': 'offline_amount', 'old_value': 34551.57, 'new_value': 36436.44}, {'field': 'total_amount', 'old_value': 64695.06, 'new_value': 68631.15}, {'field': 'order_count', 'old_value': 3375, 'new_value': 3566}]
2025-06-21 09:01:16,866 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-21 09:01:17,351 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-21 09:01:17,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103043.1, 'new_value': 107196.1}, {'field': 'total_amount', 'old_value': 103043.1, 'new_value': 107196.1}, {'field': 'order_count', 'old_value': 493, 'new_value': 513}]
2025-06-21 09:01:17,351 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-21 09:01:17,788 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-21 09:01:17,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13354.87, 'new_value': 15046.89}, {'field': 'offline_amount', 'old_value': 101239.0, 'new_value': 104099.0}, {'field': 'total_amount', 'old_value': 114593.87, 'new_value': 119145.89}, {'field': 'order_count', 'old_value': 62, 'new_value': 66}]
2025-06-21 09:01:17,788 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-21 09:01:18,288 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-21 09:01:18,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35562.0, 'new_value': 35820.0}, {'field': 'total_amount', 'old_value': 35562.0, 'new_value': 35820.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-06-21 09:01:18,288 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-21 09:01:18,632 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-21 09:01:18,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112238.5, 'new_value': 121946.5}, {'field': 'total_amount', 'old_value': 112238.5, 'new_value': 121946.5}, {'field': 'order_count', 'old_value': 3132, 'new_value': 3389}]
2025-06-21 09:01:18,632 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-21 09:01:19,069 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-21 09:01:19,069 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5322.63, 'new_value': 5566.63}, {'field': 'offline_amount', 'old_value': 13899.37, 'new_value': 14036.65}, {'field': 'total_amount', 'old_value': 19222.0, 'new_value': 19603.28}, {'field': 'order_count', 'old_value': 195, 'new_value': 198}]
2025-06-21 09:01:19,069 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-21 09:01:19,554 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-21 09:01:19,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223153.07, 'new_value': 236426.2}, {'field': 'total_amount', 'old_value': 275225.13, 'new_value': 288498.26}, {'field': 'order_count', 'old_value': 4082, 'new_value': 4245}]
2025-06-21 09:01:19,554 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-21 09:01:20,023 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-21 09:01:20,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247670.0, 'new_value': 268450.0}, {'field': 'total_amount', 'old_value': 261228.0, 'new_value': 282008.0}, {'field': 'order_count', 'old_value': 209, 'new_value': 229}]
2025-06-21 09:01:20,023 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-21 09:01:20,569 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-21 09:01:20,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61795.0, 'new_value': 62812.0}, {'field': 'total_amount', 'old_value': 74382.0, 'new_value': 75399.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 54}]
2025-06-21 09:01:20,569 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-21 09:01:21,038 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-21 09:01:21,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46795.01, 'new_value': 50081.01}, {'field': 'total_amount', 'old_value': 48532.01, 'new_value': 51818.01}, {'field': 'order_count', 'old_value': 306, 'new_value': 321}]
2025-06-21 09:01:21,038 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-21 09:01:21,601 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-21 09:01:21,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255932.1, 'new_value': 275961.1}, {'field': 'total_amount', 'old_value': 255932.1, 'new_value': 275961.1}, {'field': 'order_count', 'old_value': 323, 'new_value': 340}]
2025-06-21 09:01:21,601 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-21 09:01:22,069 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-21 09:01:22,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51403.0, 'new_value': 54307.0}, {'field': 'total_amount', 'old_value': 51403.0, 'new_value': 54307.0}, {'field': 'order_count', 'old_value': 2342, 'new_value': 2455}]
2025-06-21 09:01:22,069 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-21 09:01:22,538 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-21 09:01:22,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186573.61, 'new_value': 192273.13}, {'field': 'total_amount', 'old_value': 186573.61, 'new_value': 192273.13}, {'field': 'order_count', 'old_value': 871, 'new_value': 905}]
2025-06-21 09:01:22,538 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-21 09:01:22,976 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-21 09:01:22,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201507.0, 'new_value': 215765.0}, {'field': 'total_amount', 'old_value': 201507.0, 'new_value': 215765.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 268}]
2025-06-21 09:01:22,976 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-21 09:01:23,398 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-21 09:01:23,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142445.74, 'new_value': 146178.54}, {'field': 'total_amount', 'old_value': 142445.74, 'new_value': 146178.54}, {'field': 'order_count', 'old_value': 612, 'new_value': 629}]
2025-06-21 09:01:23,398 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-21 09:01:23,882 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-21 09:01:23,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131849.43, 'new_value': 146291.93}, {'field': 'total_amount', 'old_value': 213564.63, 'new_value': 228007.13}, {'field': 'order_count', 'old_value': 353, 'new_value': 370}]
2025-06-21 09:01:23,882 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-21 09:01:24,351 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-21 09:01:24,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1875.0, 'new_value': 2075.0}, {'field': 'offline_amount', 'old_value': 24885.0, 'new_value': 26185.0}, {'field': 'total_amount', 'old_value': 26760.0, 'new_value': 28260.0}, {'field': 'order_count', 'old_value': 344, 'new_value': 359}]
2025-06-21 09:01:24,351 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-21 09:01:24,851 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-21 09:01:24,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111845.0, 'new_value': 115041.0}, {'field': 'offline_amount', 'old_value': 126924.0, 'new_value': 134850.0}, {'field': 'total_amount', 'old_value': 238769.0, 'new_value': 249891.0}, {'field': 'order_count', 'old_value': 168197, 'new_value': 179319}]
2025-06-21 09:01:24,851 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-21 09:01:25,273 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-21 09:01:25,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48451.3, 'new_value': 52027.54}, {'field': 'offline_amount', 'old_value': 53812.87, 'new_value': 57856.96}, {'field': 'total_amount', 'old_value': 102264.17, 'new_value': 109884.5}, {'field': 'order_count', 'old_value': 5282, 'new_value': 5697}]
2025-06-21 09:01:25,273 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-21 09:01:25,866 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-21 09:01:25,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6352.01, 'new_value': 6565.01}, {'field': 'offline_amount', 'old_value': 18813.68, 'new_value': 19812.68}, {'field': 'total_amount', 'old_value': 25165.69, 'new_value': 26377.69}, {'field': 'order_count', 'old_value': 84, 'new_value': 89}]
2025-06-21 09:01:25,866 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-21 09:01:26,398 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-21 09:01:26,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72369.0, 'new_value': 75600.0}, {'field': 'total_amount', 'old_value': 72369.0, 'new_value': 75600.0}, {'field': 'order_count', 'old_value': 7158, 'new_value': 7402}]
2025-06-21 09:01:26,398 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-21 09:01:26,835 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-21 09:01:26,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139214.0, 'new_value': 143632.0}, {'field': 'offline_amount', 'old_value': 876169.0, 'new_value': 913551.0}, {'field': 'total_amount', 'old_value': 1015383.0, 'new_value': 1057183.0}, {'field': 'order_count', 'old_value': 26009, 'new_value': 27346}]
2025-06-21 09:01:26,835 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-21 09:01:27,319 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-21 09:01:27,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233084.0, 'new_value': 244722.0}, {'field': 'total_amount', 'old_value': 233084.0, 'new_value': 244722.0}, {'field': 'order_count', 'old_value': 5420, 'new_value': 5655}]
2025-06-21 09:01:27,319 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-21 09:01:27,882 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-21 09:01:27,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5500.5, 'new_value': 5764.5}, {'field': 'total_amount', 'old_value': 11923.5, 'new_value': 12187.5}, {'field': 'order_count', 'old_value': 103, 'new_value': 110}]
2025-06-21 09:01:27,882 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-21 09:01:28,335 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-21 09:01:28,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34915.45, 'new_value': 35402.25}, {'field': 'total_amount', 'old_value': 34915.45, 'new_value': 35402.25}, {'field': 'order_count', 'old_value': 949, 'new_value': 970}]
2025-06-21 09:01:28,335 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-21 09:01:28,835 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-21 09:01:28,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 173285.9, 'new_value': 184289.9}, {'field': 'offline_amount', 'old_value': 10962.5, 'new_value': 11001.5}, {'field': 'total_amount', 'old_value': 184248.4, 'new_value': 195291.4}, {'field': 'order_count', 'old_value': 1846, 'new_value': 1989}]
2025-06-21 09:01:28,835 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-21 09:01:29,382 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-21 09:01:29,382 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25229.1, 'new_value': 26629.5}, {'field': 'offline_amount', 'old_value': 42426.3, 'new_value': 44139.3}, {'field': 'total_amount', 'old_value': 67655.4, 'new_value': 70768.8}, {'field': 'order_count', 'old_value': 2768, 'new_value': 2875}]
2025-06-21 09:01:29,382 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-21 09:01:29,835 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-21 09:01:29,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26286.79, 'new_value': 27030.09}, {'field': 'total_amount', 'old_value': 27052.79, 'new_value': 27796.09}, {'field': 'order_count', 'old_value': 257, 'new_value': 263}]
2025-06-21 09:01:29,835 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-21 09:01:30,288 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-21 09:01:30,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28570.93, 'new_value': 29991.6}, {'field': 'total_amount', 'old_value': 28570.93, 'new_value': 29991.6}, {'field': 'order_count', 'old_value': 76, 'new_value': 82}]
2025-06-21 09:01:30,288 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-21 09:01:30,788 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-21 09:01:30,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67199.0, 'new_value': 69235.0}, {'field': 'total_amount', 'old_value': 67199.0, 'new_value': 69235.0}, {'field': 'order_count', 'old_value': 345, 'new_value': 355}]
2025-06-21 09:01:30,788 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-21 09:01:31,210 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-21 09:01:31,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125130.02, 'new_value': 129049.84}, {'field': 'offline_amount', 'old_value': 33179.28, 'new_value': 35428.56}, {'field': 'total_amount', 'old_value': 158309.3, 'new_value': 164478.4}, {'field': 'order_count', 'old_value': 673, 'new_value': 702}]
2025-06-21 09:01:31,226 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-21 09:01:31,632 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-21 09:01:31,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41804.0, 'new_value': 45875.0}, {'field': 'total_amount', 'old_value': 41804.0, 'new_value': 45875.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-21 09:01:31,632 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-21 09:01:32,054 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-21 09:01:32,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89936.0, 'new_value': 91204.0}, {'field': 'total_amount', 'old_value': 89936.0, 'new_value': 91204.0}, {'field': 'order_count', 'old_value': 403, 'new_value': 413}]
2025-06-21 09:01:32,054 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-21 09:01:32,476 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-21 09:01:32,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84479.0, 'new_value': 90244.0}, {'field': 'total_amount', 'old_value': 84479.0, 'new_value': 90244.0}, {'field': 'order_count', 'old_value': 4758, 'new_value': 5112}]
2025-06-21 09:01:32,476 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-21 09:01:32,929 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-21 09:01:32,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167705.9, 'new_value': 177002.9}, {'field': 'total_amount', 'old_value': 167705.9, 'new_value': 177002.9}, {'field': 'order_count', 'old_value': 5759, 'new_value': 6143}]
2025-06-21 09:01:32,944 - INFO - 日期 2025-06 处理完成 - 更新: 121 条，插入: 0 条，错误: 0 条
2025-06-21 09:01:32,944 - INFO - 数据同步完成！更新: 121 条，插入: 0 条，错误: 0 条
2025-06-21 09:01:32,944 - INFO - =================同步完成====================
2025-06-21 12:00:03,184 - INFO - =================使用默认全量同步=============
2025-06-21 12:00:04,934 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 12:00:04,934 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 12:00:04,965 - INFO - 开始处理日期: 2025-01
2025-06-21 12:00:04,965 - INFO - Request Parameters - Page 1:
2025-06-21 12:00:04,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:04,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:05,981 - INFO - Response - Page 1:
2025-06-21 12:00:06,184 - INFO - 第 1 页获取到 100 条记录
2025-06-21 12:00:06,184 - INFO - Request Parameters - Page 2:
2025-06-21 12:00:06,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:06,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:06,715 - INFO - Response - Page 2:
2025-06-21 12:00:06,918 - INFO - 第 2 页获取到 100 条记录
2025-06-21 12:00:06,918 - INFO - Request Parameters - Page 3:
2025-06-21 12:00:06,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:06,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:08,168 - INFO - Response - Page 3:
2025-06-21 12:00:08,371 - INFO - 第 3 页获取到 100 条记录
2025-06-21 12:00:08,371 - INFO - Request Parameters - Page 4:
2025-06-21 12:00:08,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:08,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:08,981 - INFO - Response - Page 4:
2025-06-21 12:00:09,184 - INFO - 第 4 页获取到 100 条记录
2025-06-21 12:00:09,184 - INFO - Request Parameters - Page 5:
2025-06-21 12:00:09,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:09,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:09,621 - INFO - Response - Page 5:
2025-06-21 12:00:09,825 - INFO - 第 5 页获取到 100 条记录
2025-06-21 12:00:09,825 - INFO - Request Parameters - Page 6:
2025-06-21 12:00:09,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:09,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:10,403 - INFO - Response - Page 6:
2025-06-21 12:00:10,606 - INFO - 第 6 页获取到 100 条记录
2025-06-21 12:00:10,606 - INFO - Request Parameters - Page 7:
2025-06-21 12:00:10,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:10,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:11,075 - INFO - Response - Page 7:
2025-06-21 12:00:11,278 - INFO - 第 7 页获取到 82 条记录
2025-06-21 12:00:11,278 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 12:00:11,278 - INFO - 获取到 682 条表单数据
2025-06-21 12:00:11,278 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 12:00:11,293 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 12:00:11,293 - INFO - 开始处理日期: 2025-02
2025-06-21 12:00:11,293 - INFO - Request Parameters - Page 1:
2025-06-21 12:00:11,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:11,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:11,825 - INFO - Response - Page 1:
2025-06-21 12:00:12,028 - INFO - 第 1 页获取到 100 条记录
2025-06-21 12:00:12,028 - INFO - Request Parameters - Page 2:
2025-06-21 12:00:12,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:12,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:12,559 - INFO - Response - Page 2:
2025-06-21 12:00:12,762 - INFO - 第 2 页获取到 100 条记录
2025-06-21 12:00:12,762 - INFO - Request Parameters - Page 3:
2025-06-21 12:00:12,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:12,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:13,278 - INFO - Response - Page 3:
2025-06-21 12:00:13,481 - INFO - 第 3 页获取到 100 条记录
2025-06-21 12:00:13,481 - INFO - Request Parameters - Page 4:
2025-06-21 12:00:13,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:13,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:13,996 - INFO - Response - Page 4:
2025-06-21 12:00:14,200 - INFO - 第 4 页获取到 100 条记录
2025-06-21 12:00:14,200 - INFO - Request Parameters - Page 5:
2025-06-21 12:00:14,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:14,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:14,809 - INFO - Response - Page 5:
2025-06-21 12:00:15,012 - INFO - 第 5 页获取到 100 条记录
2025-06-21 12:00:15,012 - INFO - Request Parameters - Page 6:
2025-06-21 12:00:15,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:15,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:15,559 - INFO - Response - Page 6:
2025-06-21 12:00:15,762 - INFO - 第 6 页获取到 100 条记录
2025-06-21 12:00:15,762 - INFO - Request Parameters - Page 7:
2025-06-21 12:00:15,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:15,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:16,199 - INFO - Response - Page 7:
2025-06-21 12:00:16,403 - INFO - 第 7 页获取到 70 条记录
2025-06-21 12:00:16,403 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 12:00:16,403 - INFO - 获取到 670 条表单数据
2025-06-21 12:00:16,403 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 12:00:16,418 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 12:00:16,418 - INFO - 开始处理日期: 2025-03
2025-06-21 12:00:16,418 - INFO - Request Parameters - Page 1:
2025-06-21 12:00:16,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:16,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:16,996 - INFO - Response - Page 1:
2025-06-21 12:00:17,199 - INFO - 第 1 页获取到 100 条记录
2025-06-21 12:00:17,199 - INFO - Request Parameters - Page 2:
2025-06-21 12:00:17,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:17,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:17,699 - INFO - Response - Page 2:
2025-06-21 12:00:17,903 - INFO - 第 2 页获取到 100 条记录
2025-06-21 12:00:17,903 - INFO - Request Parameters - Page 3:
2025-06-21 12:00:17,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:17,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:18,434 - INFO - Response - Page 3:
2025-06-21 12:00:18,637 - INFO - 第 3 页获取到 100 条记录
2025-06-21 12:00:18,637 - INFO - Request Parameters - Page 4:
2025-06-21 12:00:18,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:18,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:19,090 - INFO - Response - Page 4:
2025-06-21 12:00:19,309 - INFO - 第 4 页获取到 100 条记录
2025-06-21 12:00:19,309 - INFO - Request Parameters - Page 5:
2025-06-21 12:00:19,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:19,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:19,793 - INFO - Response - Page 5:
2025-06-21 12:00:19,996 - INFO - 第 5 页获取到 100 条记录
2025-06-21 12:00:19,996 - INFO - Request Parameters - Page 6:
2025-06-21 12:00:19,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:19,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:20,528 - INFO - Response - Page 6:
2025-06-21 12:00:20,731 - INFO - 第 6 页获取到 100 条记录
2025-06-21 12:00:20,731 - INFO - Request Parameters - Page 7:
2025-06-21 12:00:20,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:20,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:21,246 - INFO - Response - Page 7:
2025-06-21 12:00:21,449 - INFO - 第 7 页获取到 61 条记录
2025-06-21 12:00:21,449 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 12:00:21,449 - INFO - 获取到 661 条表单数据
2025-06-21 12:00:21,449 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 12:00:21,465 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 12:00:21,465 - INFO - 开始处理日期: 2025-04
2025-06-21 12:00:21,465 - INFO - Request Parameters - Page 1:
2025-06-21 12:00:21,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:21,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:22,559 - INFO - Response - Page 1:
2025-06-21 12:00:22,762 - INFO - 第 1 页获取到 100 条记录
2025-06-21 12:00:22,762 - INFO - Request Parameters - Page 2:
2025-06-21 12:00:22,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:22,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:23,184 - INFO - Response - Page 2:
2025-06-21 12:00:23,387 - INFO - 第 2 页获取到 100 条记录
2025-06-21 12:00:23,387 - INFO - Request Parameters - Page 3:
2025-06-21 12:00:23,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:23,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:23,871 - INFO - Response - Page 3:
2025-06-21 12:00:24,074 - INFO - 第 3 页获取到 100 条记录
2025-06-21 12:00:24,074 - INFO - Request Parameters - Page 4:
2025-06-21 12:00:24,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:24,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:24,699 - INFO - Response - Page 4:
2025-06-21 12:00:24,903 - INFO - 第 4 页获取到 100 条记录
2025-06-21 12:00:24,903 - INFO - Request Parameters - Page 5:
2025-06-21 12:00:24,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:24,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:25,434 - INFO - Response - Page 5:
2025-06-21 12:00:25,637 - INFO - 第 5 页获取到 100 条记录
2025-06-21 12:00:25,637 - INFO - Request Parameters - Page 6:
2025-06-21 12:00:25,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:25,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:26,090 - INFO - Response - Page 6:
2025-06-21 12:00:26,293 - INFO - 第 6 页获取到 100 条记录
2025-06-21 12:00:26,293 - INFO - Request Parameters - Page 7:
2025-06-21 12:00:26,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:26,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:26,762 - INFO - Response - Page 7:
2025-06-21 12:00:26,965 - INFO - 第 7 页获取到 56 条记录
2025-06-21 12:00:26,965 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 12:00:26,965 - INFO - 获取到 656 条表单数据
2025-06-21 12:00:26,981 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 12:00:26,996 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 12:00:26,996 - INFO - 开始处理日期: 2025-05
2025-06-21 12:00:26,996 - INFO - Request Parameters - Page 1:
2025-06-21 12:00:26,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:26,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:27,856 - INFO - Response - Page 1:
2025-06-21 12:00:28,059 - INFO - 第 1 页获取到 100 条记录
2025-06-21 12:00:28,059 - INFO - Request Parameters - Page 2:
2025-06-21 12:00:28,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:28,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:28,528 - INFO - Response - Page 2:
2025-06-21 12:00:28,731 - INFO - 第 2 页获取到 100 条记录
2025-06-21 12:00:28,731 - INFO - Request Parameters - Page 3:
2025-06-21 12:00:28,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:28,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:29,215 - INFO - Response - Page 3:
2025-06-21 12:00:29,418 - INFO - 第 3 页获取到 100 条记录
2025-06-21 12:00:29,418 - INFO - Request Parameters - Page 4:
2025-06-21 12:00:29,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:29,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:29,918 - INFO - Response - Page 4:
2025-06-21 12:00:30,121 - INFO - 第 4 页获取到 100 条记录
2025-06-21 12:00:30,121 - INFO - Request Parameters - Page 5:
2025-06-21 12:00:30,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:30,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:30,653 - INFO - Response - Page 5:
2025-06-21 12:00:30,856 - INFO - 第 5 页获取到 100 条记录
2025-06-21 12:00:30,856 - INFO - Request Parameters - Page 6:
2025-06-21 12:00:30,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:30,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:31,371 - INFO - Response - Page 6:
2025-06-21 12:00:31,590 - INFO - 第 6 页获取到 100 条记录
2025-06-21 12:00:31,590 - INFO - Request Parameters - Page 7:
2025-06-21 12:00:31,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:31,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:32,043 - INFO - Response - Page 7:
2025-06-21 12:00:32,246 - INFO - 第 7 页获取到 65 条记录
2025-06-21 12:00:32,246 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 12:00:32,246 - INFO - 获取到 665 条表单数据
2025-06-21 12:00:32,246 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 12:00:32,262 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 12:00:32,262 - INFO - 开始处理日期: 2025-06
2025-06-21 12:00:32,262 - INFO - Request Parameters - Page 1:
2025-06-21 12:00:32,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:32,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:32,809 - INFO - Response - Page 1:
2025-06-21 12:00:33,012 - INFO - 第 1 页获取到 100 条记录
2025-06-21 12:00:33,012 - INFO - Request Parameters - Page 2:
2025-06-21 12:00:33,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:33,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:33,527 - INFO - Response - Page 2:
2025-06-21 12:00:33,731 - INFO - 第 2 页获取到 100 条记录
2025-06-21 12:00:33,731 - INFO - Request Parameters - Page 3:
2025-06-21 12:00:33,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:33,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:34,559 - INFO - Response - Page 3:
2025-06-21 12:00:34,762 - INFO - 第 3 页获取到 100 条记录
2025-06-21 12:00:34,762 - INFO - Request Parameters - Page 4:
2025-06-21 12:00:34,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:34,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:35,324 - INFO - Response - Page 4:
2025-06-21 12:00:35,527 - INFO - 第 4 页获取到 100 条记录
2025-06-21 12:00:35,527 - INFO - Request Parameters - Page 5:
2025-06-21 12:00:35,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:35,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:35,981 - INFO - Response - Page 5:
2025-06-21 12:00:36,184 - INFO - 第 5 页获取到 100 条记录
2025-06-21 12:00:36,184 - INFO - Request Parameters - Page 6:
2025-06-21 12:00:36,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:36,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:36,637 - INFO - Response - Page 6:
2025-06-21 12:00:36,840 - INFO - 第 6 页获取到 100 条记录
2025-06-21 12:00:36,840 - INFO - Request Parameters - Page 7:
2025-06-21 12:00:36,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 12:00:36,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 12:00:37,168 - INFO - Response - Page 7:
2025-06-21 12:00:37,371 - INFO - 第 7 页获取到 23 条记录
2025-06-21 12:00:37,371 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 12:00:37,371 - INFO - 获取到 623 条表单数据
2025-06-21 12:00:37,371 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 12:00:37,371 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-21 12:00:37,887 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-21 12:00:37,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 546427.0, 'new_value': 572140.0}, {'field': 'total_amount', 'old_value': 546427.0, 'new_value': 572140.0}, {'field': 'order_count', 'old_value': 3807, 'new_value': 4006}]
2025-06-21 12:00:37,887 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-21 12:00:38,356 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-21 12:00:38,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102418.98, 'new_value': 106345.39}, {'field': 'total_amount', 'old_value': 102418.98, 'new_value': 106345.39}, {'field': 'order_count', 'old_value': 2694, 'new_value': 2810}]
2025-06-21 12:00:38,356 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-21 12:00:38,777 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-21 12:00:38,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67337.46, 'new_value': 71080.67}, {'field': 'offline_amount', 'old_value': 690289.95, 'new_value': 737207.98}, {'field': 'total_amount', 'old_value': 753634.92, 'new_value': 804296.16}, {'field': 'order_count', 'old_value': 3640, 'new_value': 3887}]
2025-06-21 12:00:38,777 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-21 12:00:39,262 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-21 12:00:39,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65610.0, 'new_value': 86760.0}, {'field': 'total_amount', 'old_value': 65610.0, 'new_value': 86760.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-06-21 12:00:39,262 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-21 12:00:39,746 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-21 12:00:39,746 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20822.86, 'new_value': 22000.86}, {'field': 'offline_amount', 'old_value': 11442.82, 'new_value': 12010.82}, {'field': 'total_amount', 'old_value': 32265.68, 'new_value': 34011.68}, {'field': 'order_count', 'old_value': 1343, 'new_value': 1424}]
2025-06-21 12:00:39,746 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-21 12:00:40,184 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-21 12:00:40,184 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 323136.0, 'new_value': 362009.0}, {'field': 'offline_amount', 'old_value': 133677.0, 'new_value': 138012.0}, {'field': 'total_amount', 'old_value': 456813.0, 'new_value': 500021.0}, {'field': 'order_count', 'old_value': 494, 'new_value': 518}]
2025-06-21 12:00:40,184 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-21 12:00:40,637 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-21 12:00:40,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21605.82, 'new_value': 21641.82}, {'field': 'total_amount', 'old_value': 21605.82, 'new_value': 21641.82}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-06-21 12:00:40,637 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-21 12:00:41,137 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-21 12:00:41,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6980.0, 'new_value': 9200.0}, {'field': 'total_amount', 'old_value': 46440.0, 'new_value': 48660.0}, {'field': 'order_count', 'old_value': 496, 'new_value': 519}]
2025-06-21 12:00:41,137 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-21 12:00:41,621 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-21 12:00:41,621 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177288.0, 'new_value': 186218.0}, {'field': 'total_amount', 'old_value': 177288.0, 'new_value': 186218.0}, {'field': 'order_count', 'old_value': 878, 'new_value': 938}]
2025-06-21 12:00:41,621 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-21 12:00:42,059 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-21 12:00:42,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76048.23, 'new_value': 84576.07}, {'field': 'total_amount', 'old_value': 76048.23, 'new_value': 84576.07}, {'field': 'order_count', 'old_value': 160, 'new_value': 172}]
2025-06-21 12:00:42,059 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-21 12:00:42,543 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-21 12:00:42,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120521.42, 'new_value': 127076.72}, {'field': 'total_amount', 'old_value': 120521.42, 'new_value': 127076.72}, {'field': 'order_count', 'old_value': 753, 'new_value': 797}]
2025-06-21 12:00:42,543 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-21 12:00:42,949 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-21 12:00:42,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 428560.84, 'new_value': 451255.57}, {'field': 'total_amount', 'old_value': 428560.84, 'new_value': 451255.57}, {'field': 'order_count', 'old_value': 2955, 'new_value': 3155}]
2025-06-21 12:00:42,949 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-21 12:00:43,527 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-21 12:00:43,527 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 139.0}, {'field': 'offline_amount', 'old_value': 8423.5, 'new_value': 8749.5}, {'field': 'total_amount', 'old_value': 8423.5, 'new_value': 8888.5}, {'field': 'order_count', 'old_value': 107, 'new_value': 111}]
2025-06-21 12:00:43,527 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-21 12:00:44,012 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-21 12:00:44,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 574083.1, 'new_value': 753689.7}, {'field': 'total_amount', 'old_value': 658439.4, 'new_value': 838046.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 69}]
2025-06-21 12:00:44,012 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-21 12:00:44,481 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-21 12:00:44,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116044.31, 'new_value': 120754.31}, {'field': 'total_amount', 'old_value': 142219.02, 'new_value': 146929.02}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-21 12:00:44,481 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-21 12:00:44,902 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-21 12:00:44,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78290.09, 'new_value': 81351.46}, {'field': 'total_amount', 'old_value': 79621.54, 'new_value': 82682.91}, {'field': 'order_count', 'old_value': 1852, 'new_value': 1926}]
2025-06-21 12:00:44,902 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-21 12:00:45,371 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-21 12:00:45,371 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96016.43, 'new_value': 99524.83}, {'field': 'offline_amount', 'old_value': 843998.74, 'new_value': 885690.35}, {'field': 'total_amount', 'old_value': 940015.17, 'new_value': 985215.18}, {'field': 'order_count', 'old_value': 7955, 'new_value': 8383}]
2025-06-21 12:00:45,371 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-21 12:00:45,762 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-21 12:00:45,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107506.0, 'new_value': 172506.0}, {'field': 'total_amount', 'old_value': 116238.0, 'new_value': 181238.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 50}]
2025-06-21 12:00:45,762 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-21 12:00:46,246 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-21 12:00:46,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43848.0, 'new_value': 44202.0}, {'field': 'total_amount', 'old_value': 48364.0, 'new_value': 48718.0}, {'field': 'order_count', 'old_value': 341, 'new_value': 344}]
2025-06-21 12:00:46,246 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-21 12:00:46,699 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-21 12:00:46,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97416.0, 'new_value': 101016.0}, {'field': 'total_amount', 'old_value': 97416.0, 'new_value': 101016.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-21 12:00:46,699 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-21 12:00:47,246 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-21 12:00:47,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119295.0, 'new_value': 124835.0}, {'field': 'total_amount', 'old_value': 119295.0, 'new_value': 124835.0}, {'field': 'order_count', 'old_value': 3106, 'new_value': 3259}]
2025-06-21 12:00:47,246 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-21 12:00:47,746 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-21 12:00:47,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450383.0, 'new_value': 453163.01}, {'field': 'total_amount', 'old_value': 543383.0, 'new_value': 546163.01}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-06-21 12:00:47,746 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-21 12:00:48,231 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-21 12:00:48,231 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6546.9, 'new_value': 7762.9}, {'field': 'offline_amount', 'old_value': 9159.0, 'new_value': 9435.0}, {'field': 'total_amount', 'old_value': 15705.9, 'new_value': 17197.9}, {'field': 'order_count', 'old_value': 46, 'new_value': 52}]
2025-06-21 12:00:48,231 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-21 12:00:48,684 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-21 12:00:48,684 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6750.12, 'new_value': 7459.25}, {'field': 'offline_amount', 'old_value': 145239.17, 'new_value': 153938.01}, {'field': 'total_amount', 'old_value': 151989.29, 'new_value': 161397.26}, {'field': 'order_count', 'old_value': 895, 'new_value': 939}]
2025-06-21 12:00:48,684 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-21 12:00:49,152 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-21 12:00:49,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143348.55, 'new_value': 152709.44}, {'field': 'offline_amount', 'old_value': 293556.77, 'new_value': 302957.29}, {'field': 'total_amount', 'old_value': 436905.32, 'new_value': 455666.73}, {'field': 'order_count', 'old_value': 3290, 'new_value': 3450}]
2025-06-21 12:00:49,152 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-21 12:00:49,668 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-21 12:00:49,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18678.75, 'new_value': 19948.75}, {'field': 'offline_amount', 'old_value': 32.0, 'new_value': 34.0}, {'field': 'total_amount', 'old_value': 18710.75, 'new_value': 19982.75}, {'field': 'order_count', 'old_value': 79, 'new_value': 82}]
2025-06-21 12:00:49,668 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-21 12:00:50,137 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-21 12:00:50,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20584.32, 'new_value': 20881.32}, {'field': 'total_amount', 'old_value': 20584.32, 'new_value': 20881.32}, {'field': 'order_count', 'old_value': 824, 'new_value': 862}]
2025-06-21 12:00:50,137 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-21 12:00:50,637 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-21 12:00:50,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64021.0, 'new_value': 66089.0}, {'field': 'total_amount', 'old_value': 64021.0, 'new_value': 66089.0}, {'field': 'order_count', 'old_value': 241, 'new_value': 249}]
2025-06-21 12:00:50,637 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-21 12:00:51,027 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-21 12:00:51,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 393579.61, 'new_value': 421343.61}, {'field': 'total_amount', 'old_value': 393579.61, 'new_value': 421343.61}, {'field': 'order_count', 'old_value': 3019, 'new_value': 3211}]
2025-06-21 12:00:51,027 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-21 12:00:51,465 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-21 12:00:51,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45000.0, 'new_value': 52000.0}, {'field': 'total_amount', 'old_value': 48800.0, 'new_value': 55800.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-21 12:00:51,465 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-21 12:00:51,902 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-21 12:00:51,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82079.28, 'new_value': 85817.58}, {'field': 'offline_amount', 'old_value': 403098.43, 'new_value': 423090.23}, {'field': 'total_amount', 'old_value': 485177.71, 'new_value': 508907.81}, {'field': 'order_count', 'old_value': 1307, 'new_value': 1376}]
2025-06-21 12:00:51,902 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-21 12:00:52,355 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-21 12:00:52,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38791.0, 'new_value': 38881.0}, {'field': 'total_amount', 'old_value': 38791.0, 'new_value': 38881.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-06-21 12:00:52,355 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-21 12:00:52,824 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-21 12:00:52,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11338.8, 'new_value': 11879.5}, {'field': 'offline_amount', 'old_value': 27787.8, 'new_value': 27881.8}, {'field': 'total_amount', 'old_value': 39126.6, 'new_value': 39761.3}, {'field': 'order_count', 'old_value': 129, 'new_value': 138}]
2025-06-21 12:00:52,824 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-21 12:00:53,324 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-21 12:00:53,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 135099.85, 'new_value': 154942.85}, {'field': 'offline_amount', 'old_value': 67256.15, 'new_value': 69920.15}, {'field': 'total_amount', 'old_value': 202356.0, 'new_value': 224863.0}, {'field': 'order_count', 'old_value': 892, 'new_value': 954}]
2025-06-21 12:00:53,324 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-21 12:00:53,809 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-21 12:00:53,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48884.0, 'new_value': 67879.0}, {'field': 'offline_amount', 'old_value': 242739.0, 'new_value': 247031.0}, {'field': 'total_amount', 'old_value': 291623.0, 'new_value': 314910.0}, {'field': 'order_count', 'old_value': 2218, 'new_value': 2350}]
2025-06-21 12:00:53,809 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-21 12:00:54,277 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-21 12:00:54,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97728.0, 'new_value': 106837.0}, {'field': 'total_amount', 'old_value': 97728.0, 'new_value': 106837.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 83}]
2025-06-21 12:00:54,277 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-21 12:00:54,777 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-21 12:00:54,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6287.9, 'new_value': 6404.7}, {'field': 'offline_amount', 'old_value': 51463.0, 'new_value': 53201.0}, {'field': 'total_amount', 'old_value': 57750.9, 'new_value': 59605.7}, {'field': 'order_count', 'old_value': 1192, 'new_value': 1279}]
2025-06-21 12:00:54,777 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-21 12:00:55,309 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-21 12:00:55,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18242.0, 'new_value': 19280.0}, {'field': 'total_amount', 'old_value': 18302.0, 'new_value': 19340.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 77}]
2025-06-21 12:00:55,309 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-21 12:00:55,777 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-21 12:00:55,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162810.72, 'new_value': 174373.72}, {'field': 'total_amount', 'old_value': 174535.43, 'new_value': 186098.43}, {'field': 'order_count', 'old_value': 1165, 'new_value': 1240}]
2025-06-21 12:00:55,777 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-21 12:00:56,293 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-21 12:00:56,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234101.51, 'new_value': 247759.41}, {'field': 'total_amount', 'old_value': 234101.51, 'new_value': 247759.41}, {'field': 'order_count', 'old_value': 6815, 'new_value': 7197}]
2025-06-21 12:00:56,293 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-21 12:00:56,762 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-21 12:00:56,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8715.0, 'new_value': 9075.0}, {'field': 'total_amount', 'old_value': 8715.0, 'new_value': 9075.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-06-21 12:00:56,762 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-21 12:00:57,246 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-21 12:00:57,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124279.0, 'new_value': 127465.0}, {'field': 'total_amount', 'old_value': 255613.0, 'new_value': 258799.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 138}]
2025-06-21 12:00:57,246 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-21 12:00:57,715 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-21 12:00:57,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62343.6, 'new_value': 64179.6}, {'field': 'total_amount', 'old_value': 62343.6, 'new_value': 64179.6}, {'field': 'order_count', 'old_value': 876, 'new_value': 877}]
2025-06-21 12:00:57,715 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-21 12:00:58,184 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-21 12:00:58,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32089.2, 'new_value': 36780.2}, {'field': 'total_amount', 'old_value': 32089.2, 'new_value': 36780.2}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-06-21 12:00:58,184 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-21 12:00:58,621 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-21 12:00:58,621 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24402.62, 'new_value': 24770.72}, {'field': 'offline_amount', 'old_value': 198417.45, 'new_value': 209043.95}, {'field': 'total_amount', 'old_value': 222820.07, 'new_value': 233814.67}, {'field': 'order_count', 'old_value': 12336, 'new_value': 12979}]
2025-06-21 12:00:58,621 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-21 12:00:59,043 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-21 12:00:59,043 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6455.9, 'new_value': 6966.5}, {'field': 'offline_amount', 'old_value': 51900.0, 'new_value': 53100.0}, {'field': 'total_amount', 'old_value': 58355.9, 'new_value': 60066.5}, {'field': 'order_count', 'old_value': 47, 'new_value': 50}]
2025-06-21 12:00:59,043 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-21 12:00:59,512 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-21 12:00:59,512 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48995.69, 'new_value': 49851.72}, {'field': 'offline_amount', 'old_value': 59007.15, 'new_value': 66395.15}, {'field': 'total_amount', 'old_value': 108002.84, 'new_value': 116246.87}, {'field': 'order_count', 'old_value': 87, 'new_value': 90}]
2025-06-21 12:00:59,512 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-21 12:00:59,980 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-21 12:00:59,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30938.88, 'new_value': 31683.27}, {'field': 'offline_amount', 'old_value': 22837.0, 'new_value': 23837.0}, {'field': 'total_amount', 'old_value': 53775.88, 'new_value': 55520.27}, {'field': 'order_count', 'old_value': 704, 'new_value': 739}]
2025-06-21 12:00:59,980 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-21 12:01:00,496 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-21 12:01:00,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60820.52, 'new_value': 61378.3}, {'field': 'total_amount', 'old_value': 60820.52, 'new_value': 61378.3}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-21 12:01:00,496 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-21 12:01:00,949 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-21 12:01:00,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165605.72, 'new_value': 174428.32}, {'field': 'total_amount', 'old_value': 165605.72, 'new_value': 174428.32}, {'field': 'order_count', 'old_value': 926, 'new_value': 976}]
2025-06-21 12:01:00,949 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-21 12:01:01,387 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-21 12:01:01,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237694.0, 'new_value': 262234.0}, {'field': 'total_amount', 'old_value': 237694.0, 'new_value': 262234.0}]
2025-06-21 12:01:01,387 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-21 12:01:01,918 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-21 12:01:01,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82721.1, 'new_value': 86960.0}, {'field': 'total_amount', 'old_value': 82721.1, 'new_value': 86960.0}, {'field': 'order_count', 'old_value': 210, 'new_value': 217}]
2025-06-21 12:01:01,934 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-21 12:01:02,527 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-21 12:01:02,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41593.05, 'new_value': 42844.95}, {'field': 'total_amount', 'old_value': 41593.05, 'new_value': 42844.95}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-21 12:01:02,527 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-21 12:01:03,105 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-21 12:01:03,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40242.2, 'new_value': 41742.2}, {'field': 'total_amount', 'old_value': 40242.2, 'new_value': 41742.2}, {'field': 'order_count', 'old_value': 242, 'new_value': 244}]
2025-06-21 12:01:03,105 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-21 12:01:03,621 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-21 12:01:03,621 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60930.73, 'new_value': 64693.07}, {'field': 'total_amount', 'old_value': 60930.73, 'new_value': 64693.07}, {'field': 'order_count', 'old_value': 305, 'new_value': 322}]
2025-06-21 12:01:03,621 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-21 12:01:04,090 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-21 12:01:04,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450910.37, 'new_value': 481132.0}, {'field': 'total_amount', 'old_value': 450910.37, 'new_value': 481132.0}, {'field': 'order_count', 'old_value': 1436, 'new_value': 1542}]
2025-06-21 12:01:04,090 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-21 12:01:04,559 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-21 12:01:04,559 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31310.0, 'new_value': 31609.0}, {'field': 'total_amount', 'old_value': 189344.0, 'new_value': 189643.0}]
2025-06-21 12:01:04,559 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-21 12:01:04,965 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-21 12:01:04,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124900.7, 'new_value': 131257.6}, {'field': 'total_amount', 'old_value': 124989.7, 'new_value': 131346.6}, {'field': 'order_count', 'old_value': 1530, 'new_value': 1604}]
2025-06-21 12:01:04,965 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-21 12:01:05,434 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-21 12:01:05,434 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 219.0, 'new_value': 728.0}, {'field': 'offline_amount', 'old_value': 27246.0, 'new_value': 27375.0}, {'field': 'total_amount', 'old_value': 27465.0, 'new_value': 28103.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-06-21 12:01:05,434 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-21 12:01:05,902 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-21 12:01:05,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40754.7, 'new_value': 43976.5}, {'field': 'offline_amount', 'old_value': 256920.36, 'new_value': 270914.51}, {'field': 'total_amount', 'old_value': 297675.06, 'new_value': 314891.01}, {'field': 'order_count', 'old_value': 1863, 'new_value': 1964}]
2025-06-21 12:01:05,902 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-21 12:01:06,371 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-21 12:01:06,371 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27035.85, 'new_value': 29032.96}, {'field': 'offline_amount', 'old_value': 26295.94, 'new_value': 27295.94}, {'field': 'total_amount', 'old_value': 53331.79, 'new_value': 56328.9}, {'field': 'order_count', 'old_value': 2499, 'new_value': 2633}]
2025-06-21 12:01:06,371 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-21 12:01:06,809 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-21 12:01:06,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59473.47, 'new_value': 62910.64}, {'field': 'offline_amount', 'old_value': 24121.35, 'new_value': 24909.15}, {'field': 'total_amount', 'old_value': 83594.82, 'new_value': 87819.79}, {'field': 'order_count', 'old_value': 4894, 'new_value': 5127}]
2025-06-21 12:01:06,809 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-21 12:01:07,293 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-21 12:01:07,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20483.0, 'new_value': 22083.0}, {'field': 'total_amount', 'old_value': 20483.0, 'new_value': 22083.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 132}]
2025-06-21 12:01:07,293 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-21 12:01:07,777 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-21 12:01:07,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29243.2, 'new_value': 30730.5}, {'field': 'total_amount', 'old_value': 30491.2, 'new_value': 31978.5}, {'field': 'order_count', 'old_value': 104, 'new_value': 110}]
2025-06-21 12:01:07,777 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-21 12:01:08,230 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-21 12:01:08,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107316.03, 'new_value': 114585.6}, {'field': 'offline_amount', 'old_value': 76452.0, 'new_value': 79049.0}, {'field': 'total_amount', 'old_value': 183768.03, 'new_value': 193634.6}, {'field': 'order_count', 'old_value': 1895, 'new_value': 2002}]
2025-06-21 12:01:08,230 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-21 12:01:08,684 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-21 12:01:08,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417933.76, 'new_value': 444523.76}, {'field': 'total_amount', 'old_value': 418249.12, 'new_value': 444839.12}, {'field': 'order_count', 'old_value': 1120, 'new_value': 1197}]
2025-06-21 12:01:08,684 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-21 12:01:09,137 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-21 12:01:09,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56900.99, 'new_value': 59966.44}, {'field': 'offline_amount', 'old_value': 49178.99, 'new_value': 51302.13}, {'field': 'total_amount', 'old_value': 106079.98, 'new_value': 111268.57}, {'field': 'order_count', 'old_value': 4993, 'new_value': 5239}]
2025-06-21 12:01:09,137 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-21 12:01:09,558 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-21 12:01:09,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211913.9, 'new_value': 219321.8}, {'field': 'total_amount', 'old_value': 211913.9, 'new_value': 219321.8}, {'field': 'order_count', 'old_value': 6368, 'new_value': 6613}]
2025-06-21 12:01:09,558 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-21 12:01:10,027 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-21 12:01:10,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31072.1, 'new_value': 31772.9}, {'field': 'total_amount', 'old_value': 31072.1, 'new_value': 31772.9}, {'field': 'order_count', 'old_value': 421, 'new_value': 433}]
2025-06-21 12:01:10,027 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-21 12:01:10,512 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-21 12:01:10,512 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211917.97, 'new_value': 220116.29}, {'field': 'total_amount', 'old_value': 274105.15, 'new_value': 282303.47}, {'field': 'order_count', 'old_value': 549, 'new_value': 563}]
2025-06-21 12:01:10,512 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAK
2025-06-21 12:01:10,934 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAK
2025-06-21 12:01:10,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18987.0, 'new_value': 23987.0}, {'field': 'total_amount', 'old_value': 18987.0, 'new_value': 23987.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-06-21 12:01:10,934 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-21 12:01:11,387 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-21 12:01:11,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160025.0, 'new_value': 172668.0}, {'field': 'total_amount', 'old_value': 160025.0, 'new_value': 172668.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 182}]
2025-06-21 12:01:11,387 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-21 12:01:11,887 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-21 12:01:11,887 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42329.3, 'new_value': 67106.9}, {'field': 'offline_amount', 'old_value': 105410.9, 'new_value': 108141.2}, {'field': 'total_amount', 'old_value': 147740.2, 'new_value': 175248.1}, {'field': 'order_count', 'old_value': 2877, 'new_value': 3058}]
2025-06-21 12:01:11,887 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-21 12:01:12,371 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-21 12:01:12,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170214.0, 'new_value': 180894.0}, {'field': 'total_amount', 'old_value': 170214.0, 'new_value': 180894.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-21 12:01:12,371 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-21 12:01:12,840 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-21 12:01:12,840 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4242.62, 'new_value': 4519.42}, {'field': 'offline_amount', 'old_value': 258245.57, 'new_value': 270517.37}, {'field': 'total_amount', 'old_value': 262488.19, 'new_value': 275036.79}, {'field': 'order_count', 'old_value': 12878, 'new_value': 13589}]
2025-06-21 12:01:12,840 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-21 12:01:13,308 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-21 12:01:13,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20203.0, 'new_value': 21891.0}, {'field': 'total_amount', 'old_value': 20203.0, 'new_value': 21891.0}, {'field': 'order_count', 'old_value': 191, 'new_value': 206}]
2025-06-21 12:01:13,308 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-21 12:01:13,715 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-21 12:01:13,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 412518.16, 'new_value': 431484.29}, {'field': 'total_amount', 'old_value': 412518.16, 'new_value': 431484.29}, {'field': 'order_count', 'old_value': 4648, 'new_value': 4885}]
2025-06-21 12:01:13,715 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-21 12:01:14,152 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-21 12:01:14,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91964.0, 'new_value': 93339.0}, {'field': 'total_amount', 'old_value': 91964.0, 'new_value': 93339.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 58}]
2025-06-21 12:01:14,152 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-21 12:01:14,527 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-21 12:01:14,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92049.0, 'new_value': 97313.0}, {'field': 'total_amount', 'old_value': 92049.0, 'new_value': 97313.0}, {'field': 'order_count', 'old_value': 338, 'new_value': 356}]
2025-06-21 12:01:14,527 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-21 12:01:14,980 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-21 12:01:14,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83455.84, 'new_value': 84329.15}, {'field': 'total_amount', 'old_value': 321971.26, 'new_value': 322844.57}]
2025-06-21 12:01:14,980 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-21 12:01:15,433 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-21 12:01:15,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109104.74, 'new_value': 116089.96}, {'field': 'offline_amount', 'old_value': 274487.07, 'new_value': 289178.56}, {'field': 'total_amount', 'old_value': 383591.81, 'new_value': 405268.52}, {'field': 'order_count', 'old_value': 3781, 'new_value': 4054}]
2025-06-21 12:01:15,433 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-21 12:01:15,902 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-21 12:01:15,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113170.3, 'new_value': 119425.89}, {'field': 'offline_amount', 'old_value': 229371.5, 'new_value': 235104.14}, {'field': 'total_amount', 'old_value': 342541.8, 'new_value': 354530.03}, {'field': 'order_count', 'old_value': 2806, 'new_value': 2969}]
2025-06-21 12:01:15,902 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-21 12:01:16,340 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-21 12:01:16,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42964.6, 'new_value': 43001.2}, {'field': 'total_amount', 'old_value': 42964.6, 'new_value': 43001.2}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-21 12:01:16,340 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-21 12:01:16,808 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-21 12:01:16,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27538.62, 'new_value': 29262.62}, {'field': 'offline_amount', 'old_value': 793330.61, 'new_value': 840580.23}, {'field': 'total_amount', 'old_value': 820869.23, 'new_value': 869842.85}, {'field': 'order_count', 'old_value': 3841, 'new_value': 4060}]
2025-06-21 12:01:16,808 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-21 12:01:17,215 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-21 12:01:17,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40896.0, 'new_value': 41184.0}, {'field': 'total_amount', 'old_value': 43508.0, 'new_value': 43796.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 104}]
2025-06-21 12:01:17,215 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-21 12:01:17,668 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-21 12:01:17,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112036.18, 'new_value': 116599.48}, {'field': 'total_amount', 'old_value': 112036.18, 'new_value': 116599.48}, {'field': 'order_count', 'old_value': 5056, 'new_value': 5261}]
2025-06-21 12:01:17,668 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-21 12:01:18,168 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-21 12:01:18,168 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61526.0, 'new_value': 64071.0}, {'field': 'total_amount', 'old_value': 61526.0, 'new_value': 64071.0}, {'field': 'order_count', 'old_value': 1842, 'new_value': 1920}]
2025-06-21 12:01:18,168 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-21 12:01:18,637 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-21 12:01:18,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46855.9, 'new_value': 47970.7}, {'field': 'total_amount', 'old_value': 47592.8, 'new_value': 48707.6}, {'field': 'order_count', 'old_value': 335, 'new_value': 344}]
2025-06-21 12:01:18,637 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-21 12:01:19,090 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-21 12:01:19,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28187.1, 'new_value': 28842.1}, {'field': 'total_amount', 'old_value': 28187.1, 'new_value': 28842.1}, {'field': 'order_count', 'old_value': 185, 'new_value': 194}]
2025-06-21 12:01:19,090 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-21 12:01:19,449 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-21 12:01:19,449 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67846.06, 'new_value': 70144.04}, {'field': 'offline_amount', 'old_value': 638867.45, 'new_value': 673385.35}, {'field': 'total_amount', 'old_value': 706713.51, 'new_value': 743529.39}, {'field': 'order_count', 'old_value': 3010, 'new_value': 3170}]
2025-06-21 12:01:19,449 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-21 12:01:19,840 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-21 12:01:19,840 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208590.52, 'new_value': 211390.52}, {'field': 'offline_amount', 'old_value': 182898.2, 'new_value': 186585.77}, {'field': 'total_amount', 'old_value': 391488.72, 'new_value': 397976.29}, {'field': 'order_count', 'old_value': 2595, 'new_value': 2658}]
2025-06-21 12:01:19,840 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-21 12:01:20,324 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-21 12:01:20,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64586.0, 'new_value': 68065.0}, {'field': 'total_amount', 'old_value': 64586.0, 'new_value': 68065.0}, {'field': 'order_count', 'old_value': 519, 'new_value': 549}]
2025-06-21 12:01:20,324 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-21 12:01:20,793 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-21 12:01:20,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22372.92, 'new_value': 24372.92}, {'field': 'total_amount', 'old_value': 22930.92, 'new_value': 24930.92}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-06-21 12:01:20,793 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-21 12:01:21,230 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-21 12:01:21,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1350000.0, 'new_value': 1400000.0}, {'field': 'total_amount', 'old_value': 1450000.0, 'new_value': 1500000.0}, {'field': 'order_count', 'old_value': 371, 'new_value': 372}]
2025-06-21 12:01:21,230 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-21 12:01:21,668 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-21 12:01:21,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111837.0, 'new_value': 114426.0}, {'field': 'total_amount', 'old_value': 111837.0, 'new_value': 114426.0}, {'field': 'order_count', 'old_value': 3558, 'new_value': 3640}]
2025-06-21 12:01:21,668 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-21 12:01:22,058 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-21 12:01:22,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187266.36, 'new_value': 199064.94}, {'field': 'total_amount', 'old_value': 187266.36, 'new_value': 199064.94}, {'field': 'order_count', 'old_value': 1145, 'new_value': 1218}]
2025-06-21 12:01:22,058 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-21 12:01:22,558 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-21 12:01:22,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1158434.94, 'new_value': 1200181.97}, {'field': 'offline_amount', 'old_value': 236653.0, 'new_value': 276368.0}, {'field': 'total_amount', 'old_value': 1395087.94, 'new_value': 1476549.97}, {'field': 'order_count', 'old_value': 5217, 'new_value': 5546}]
2025-06-21 12:01:22,558 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-21 12:01:23,027 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-21 12:01:23,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39180.36, 'new_value': 40810.13}, {'field': 'offline_amount', 'old_value': 341275.08, 'new_value': 358508.18}, {'field': 'total_amount', 'old_value': 380455.44, 'new_value': 399318.31}, {'field': 'order_count', 'old_value': 1697, 'new_value': 1773}]
2025-06-21 12:01:23,027 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-21 12:01:23,543 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-21 12:01:23,543 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91198.5, 'new_value': 96557.4}, {'field': 'offline_amount', 'old_value': 321303.9, 'new_value': 326303.9}, {'field': 'total_amount', 'old_value': 412502.4, 'new_value': 422861.3}, {'field': 'order_count', 'old_value': 2082, 'new_value': 2180}]
2025-06-21 12:01:23,543 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-21 12:01:23,996 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-21 12:01:23,996 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20080.15, 'new_value': 20915.15}, {'field': 'offline_amount', 'old_value': 21340.4, 'new_value': 21987.2}, {'field': 'total_amount', 'old_value': 41420.55, 'new_value': 42902.35}, {'field': 'order_count', 'old_value': 250, 'new_value': 257}]
2025-06-21 12:01:23,996 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-21 12:01:24,558 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-21 12:01:24,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244652.21, 'new_value': 257663.11}, {'field': 'total_amount', 'old_value': 244652.21, 'new_value': 257663.11}, {'field': 'order_count', 'old_value': 2305, 'new_value': 2433}]
2025-06-21 12:01:24,558 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-21 12:01:25,074 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-21 12:01:25,074 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42197.71, 'new_value': 43608.49}, {'field': 'offline_amount', 'old_value': 259384.69, 'new_value': 270621.59}, {'field': 'total_amount', 'old_value': 301582.4, 'new_value': 314230.08}, {'field': 'order_count', 'old_value': 2488, 'new_value': 2606}]
2025-06-21 12:01:25,074 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-21 12:01:25,558 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-21 12:01:25,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131290.98, 'new_value': 141642.23}, {'field': 'offline_amount', 'old_value': 196085.13, 'new_value': 207403.92}, {'field': 'total_amount', 'old_value': 327376.11, 'new_value': 349046.15}, {'field': 'order_count', 'old_value': 2951, 'new_value': 3140}]
2025-06-21 12:01:25,558 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-21 12:01:26,012 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-21 12:01:26,012 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 227944.28, 'new_value': 238804.18}, {'field': 'offline_amount', 'old_value': 18442.19, 'new_value': 19188.29}, {'field': 'total_amount', 'old_value': 246386.47, 'new_value': 257992.47}, {'field': 'order_count', 'old_value': 9421, 'new_value': 9825}]
2025-06-21 12:01:26,012 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-21 12:01:26,496 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-21 12:01:26,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97786.0, 'new_value': 102380.0}, {'field': 'total_amount', 'old_value': 97786.0, 'new_value': 102380.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 176}]
2025-06-21 12:01:26,496 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ01
2025-06-21 12:01:26,980 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ01
2025-06-21 12:01:26,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358000.0, 'new_value': 716000.0}, {'field': 'total_amount', 'old_value': 858000.0, 'new_value': 1216000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-21 12:01:26,980 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-21 12:01:27,465 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-21 12:01:27,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33287.5, 'new_value': 35362.5}, {'field': 'total_amount', 'old_value': 33287.5, 'new_value': 35362.5}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-06-21 12:01:27,465 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-21 12:01:27,918 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-21 12:01:27,918 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12436.64, 'new_value': 13161.05}, {'field': 'offline_amount', 'old_value': 204736.1, 'new_value': 218650.1}, {'field': 'total_amount', 'old_value': 217172.74, 'new_value': 231811.15}, {'field': 'order_count', 'old_value': 1156, 'new_value': 1231}]
2025-06-21 12:01:27,918 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-21 12:01:28,308 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-21 12:01:28,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24847.0, 'new_value': 26524.0}, {'field': 'total_amount', 'old_value': 24847.0, 'new_value': 26524.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 81}]
2025-06-21 12:01:28,308 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-21 12:01:28,746 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-21 12:01:28,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48636.54, 'new_value': 51586.54}, {'field': 'total_amount', 'old_value': 48636.54, 'new_value': 51586.54}, {'field': 'order_count', 'old_value': 840, 'new_value': 923}]
2025-06-21 12:01:28,746 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-21 12:01:29,215 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF11
2025-06-21 12:01:29,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13940.0, 'new_value': 18919.0}, {'field': 'total_amount', 'old_value': 13940.0, 'new_value': 18919.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-06-21 12:01:29,215 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-21 12:01:29,715 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-21 12:01:29,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167336.9, 'new_value': 174655.9}, {'field': 'total_amount', 'old_value': 177963.9, 'new_value': 185282.9}, {'field': 'order_count', 'old_value': 735, 'new_value': 769}]
2025-06-21 12:01:29,715 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-21 12:01:30,152 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-21 12:01:30,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27057.82, 'new_value': 28742.82}, {'field': 'total_amount', 'old_value': 27057.82, 'new_value': 28742.82}, {'field': 'order_count', 'old_value': 585, 'new_value': 613}]
2025-06-21 12:01:30,152 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-21 12:01:30,621 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-21 12:01:30,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6192.0, 'new_value': 11540.3}, {'field': 'total_amount', 'old_value': 177537.13, 'new_value': 182885.43}, {'field': 'order_count', 'old_value': 12921, 'new_value': 13130}]
2025-06-21 12:01:30,621 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-21 12:01:31,043 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-21 12:01:31,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92298.0, 'new_value': 98283.0}, {'field': 'total_amount', 'old_value': 92466.0, 'new_value': 98451.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 302}]
2025-06-21 12:01:31,043 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-21 12:01:31,511 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-21 12:01:31,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12813.0, 'new_value': 13734.0}, {'field': 'offline_amount', 'old_value': 19469.0, 'new_value': 21410.0}, {'field': 'total_amount', 'old_value': 32282.0, 'new_value': 35144.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 280}]
2025-06-21 12:01:31,511 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-21 12:01:32,074 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-21 12:01:32,074 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102252.49, 'new_value': 109120.37}, {'field': 'offline_amount', 'old_value': 35250.18, 'new_value': 36432.17}, {'field': 'total_amount', 'old_value': 137502.67, 'new_value': 145552.54}, {'field': 'order_count', 'old_value': 8117, 'new_value': 8611}]
2025-06-21 12:01:32,074 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-21 12:01:32,590 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-21 12:01:32,590 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105369.67, 'new_value': 110035.57}, {'field': 'total_amount', 'old_value': 105369.67, 'new_value': 110035.57}, {'field': 'order_count', 'old_value': 496, 'new_value': 524}]
2025-06-21 12:01:32,590 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-21 12:01:33,058 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-21 12:01:33,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56568.0, 'new_value': 58123.0}, {'field': 'offline_amount', 'old_value': 236682.0, 'new_value': 250664.0}, {'field': 'total_amount', 'old_value': 293250.0, 'new_value': 308787.0}, {'field': 'order_count', 'old_value': 1101, 'new_value': 1161}]
2025-06-21 12:01:33,058 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-21 12:01:33,574 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-21 12:01:33,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215765.0, 'new_value': 223804.0}, {'field': 'total_amount', 'old_value': 215765.0, 'new_value': 223804.0}, {'field': 'order_count', 'old_value': 268, 'new_value': 281}]
2025-06-21 12:01:33,574 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-21 12:01:34,058 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-21 12:01:34,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314330.29, 'new_value': 326893.19}, {'field': 'total_amount', 'old_value': 314330.29, 'new_value': 326893.19}, {'field': 'order_count', 'old_value': 6833, 'new_value': 7103}]
2025-06-21 12:01:34,058 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-21 12:01:34,527 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-21 12:01:34,527 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76623.62, 'new_value': 81659.75}, {'field': 'offline_amount', 'old_value': 98948.53, 'new_value': 104835.4}, {'field': 'total_amount', 'old_value': 175572.15, 'new_value': 186495.15}, {'field': 'order_count', 'old_value': 4598, 'new_value': 7916}]
2025-06-21 12:01:34,527 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-21 12:01:34,965 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-21 12:01:34,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65275.05, 'new_value': 68223.01}, {'field': 'total_amount', 'old_value': 65275.05, 'new_value': 68223.01}, {'field': 'order_count', 'old_value': 4184, 'new_value': 4396}]
2025-06-21 12:01:34,965 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-21 12:01:35,418 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-21 12:01:35,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174020.0, 'new_value': 184219.0}, {'field': 'total_amount', 'old_value': 174020.0, 'new_value': 184219.0}, {'field': 'order_count', 'old_value': 18302, 'new_value': 19496}]
2025-06-21 12:01:35,418 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-21 12:01:35,855 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-21 12:01:35,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64896.0, 'new_value': 68276.0}, {'field': 'total_amount', 'old_value': 64896.0, 'new_value': 68276.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-21 12:01:35,855 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-21 12:01:36,324 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-21 12:01:36,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92595.8, 'new_value': 97671.3}, {'field': 'total_amount', 'old_value': 116754.6, 'new_value': 121830.1}, {'field': 'order_count', 'old_value': 9094, 'new_value': 9480}]
2025-06-21 12:01:36,324 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-21 12:01:36,746 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-21 12:01:36,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28540.0, 'new_value': 36440.0}, {'field': 'total_amount', 'old_value': 28540.0, 'new_value': 36440.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-21 12:01:36,746 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-21 12:01:37,183 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-21 12:01:37,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37477.4, 'new_value': 39615.1}, {'field': 'total_amount', 'old_value': 37477.4, 'new_value': 39615.1}, {'field': 'order_count', 'old_value': 197, 'new_value': 207}]
2025-06-21 12:01:37,183 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-21 12:01:37,574 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-21 12:01:37,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 527524.3, 'new_value': 552965.6}, {'field': 'total_amount', 'old_value': 527524.3, 'new_value': 552965.6}, {'field': 'order_count', 'old_value': 4561, 'new_value': 4797}]
2025-06-21 12:01:37,574 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-21 12:01:38,011 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-21 12:01:38,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 540887.13, 'new_value': 576611.3}, {'field': 'total_amount', 'old_value': 540887.13, 'new_value': 576611.3}, {'field': 'order_count', 'old_value': 3485, 'new_value': 3710}]
2025-06-21 12:01:38,011 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-21 12:01:38,402 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-21 12:01:38,402 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28680.93, 'new_value': 29403.93}, {'field': 'offline_amount', 'old_value': 342517.81, 'new_value': 349017.81}, {'field': 'total_amount', 'old_value': 371198.74, 'new_value': 378421.74}, {'field': 'order_count', 'old_value': 432, 'new_value': 446}]
2025-06-21 12:01:38,402 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-21 12:01:38,933 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-21 12:01:38,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4067780.49, 'new_value': 4259030.49}, {'field': 'total_amount', 'old_value': 4067780.49, 'new_value': 4259030.49}, {'field': 'order_count', 'old_value': 82018, 'new_value': 86020}]
2025-06-21 12:01:38,933 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-21 12:01:39,340 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-21 12:01:39,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 789562.23, 'new_value': 836985.83}, {'field': 'total_amount', 'old_value': 789562.23, 'new_value': 836985.83}, {'field': 'order_count', 'old_value': 3473, 'new_value': 3611}]
2025-06-21 12:01:39,340 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-21 12:01:39,871 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-21 12:01:39,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 525314.39, 'new_value': 554214.54}, {'field': 'total_amount', 'old_value': 525314.39, 'new_value': 554214.54}, {'field': 'order_count', 'old_value': 1345, 'new_value': 1405}]
2025-06-21 12:01:39,871 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-21 12:01:40,324 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-21 12:01:40,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48513.83, 'new_value': 52868.37}, {'field': 'total_amount', 'old_value': 76910.95, 'new_value': 81265.49}, {'field': 'order_count', 'old_value': 5036, 'new_value': 5300}]
2025-06-21 12:01:40,324 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-21 12:01:40,715 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-21 12:01:40,715 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84741.04, 'new_value': 91748.95}, {'field': 'total_amount', 'old_value': 137216.87, 'new_value': 144224.78}, {'field': 'order_count', 'old_value': 9110, 'new_value': 9578}]
2025-06-21 12:01:40,715 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-21 12:01:41,293 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-21 12:01:41,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1679588.0, 'new_value': 1729588.0}, {'field': 'total_amount', 'old_value': 1679588.0, 'new_value': 1729588.0}, {'field': 'order_count', 'old_value': 3080, 'new_value': 3081}]
2025-06-21 12:01:41,293 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-21 12:01:41,761 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-21 12:01:41,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6000.0}, {'field': 'total_amount', 'old_value': 13000.0, 'new_value': 19000.0}]
2025-06-21 12:01:41,761 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-21 12:01:42,215 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-21 12:01:42,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145000.0, 'new_value': 150000.0}, {'field': 'total_amount', 'old_value': 145000.0, 'new_value': 150000.0}, {'field': 'order_count', 'old_value': 793, 'new_value': 794}]
2025-06-21 12:01:42,215 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-21 12:01:42,715 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-21 12:01:42,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145000.0, 'new_value': 150000.0}, {'field': 'total_amount', 'old_value': 155000.0, 'new_value': 160000.0}, {'field': 'order_count', 'old_value': 922, 'new_value': 923}]
2025-06-21 12:01:42,715 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-21 12:01:43,183 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-21 12:01:43,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78938.0, 'new_value': 100220.0}, {'field': 'total_amount', 'old_value': 78938.0, 'new_value': 100220.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-21 12:01:43,183 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-21 12:01:43,668 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-21 12:01:43,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 815747.34, 'new_value': 841635.67}, {'field': 'total_amount', 'old_value': 815747.34, 'new_value': 841635.67}, {'field': 'order_count', 'old_value': 3579, 'new_value': 3680}]
2025-06-21 12:01:43,668 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-21 12:01:44,183 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-21 12:01:44,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 351529.0, 'new_value': 368067.86}, {'field': 'total_amount', 'old_value': 351529.0, 'new_value': 368067.86}, {'field': 'order_count', 'old_value': 2400, 'new_value': 2446}]
2025-06-21 12:01:44,183 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-21 12:01:44,636 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-21 12:01:44,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33345.3, 'new_value': 33423.3}, {'field': 'total_amount', 'old_value': 38733.15, 'new_value': 38811.15}, {'field': 'order_count', 'old_value': 290, 'new_value': 291}]
2025-06-21 12:01:44,636 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-21 12:01:45,011 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-21 12:01:45,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179023.73, 'new_value': 188786.98}, {'field': 'total_amount', 'old_value': 179023.73, 'new_value': 188786.98}, {'field': 'order_count', 'old_value': 5594, 'new_value': 5826}]
2025-06-21 12:01:45,027 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-21 12:01:45,480 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-21 12:01:45,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105847.86, 'new_value': 112527.76}, {'field': 'total_amount', 'old_value': 105847.86, 'new_value': 112527.76}, {'field': 'order_count', 'old_value': 7539, 'new_value': 7977}]
2025-06-21 12:01:45,480 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-21 12:01:45,918 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-21 12:01:45,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200100.0, 'new_value': 210800.0}, {'field': 'total_amount', 'old_value': 200100.0, 'new_value': 210800.0}, {'field': 'order_count', 'old_value': 472, 'new_value': 497}]
2025-06-21 12:01:45,918 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-21 12:01:46,355 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-21 12:01:46,355 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23246.5, 'new_value': 25099.9}, {'field': 'offline_amount', 'old_value': 91890.0, 'new_value': 95671.0}, {'field': 'total_amount', 'old_value': 115136.5, 'new_value': 120770.9}, {'field': 'order_count', 'old_value': 1433, 'new_value': 1506}]
2025-06-21 12:01:46,355 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-21 12:01:46,808 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-21 12:01:46,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145000.0, 'new_value': 150000.0}, {'field': 'total_amount', 'old_value': 145000.0, 'new_value': 150000.0}, {'field': 'order_count', 'old_value': 588, 'new_value': 589}]
2025-06-21 12:01:46,808 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-21 12:01:47,214 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-21 12:01:47,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155388.04, 'new_value': 166787.72}, {'field': 'total_amount', 'old_value': 155388.04, 'new_value': 166787.72}, {'field': 'order_count', 'old_value': 2520, 'new_value': 2535}]
2025-06-21 12:01:47,214 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-21 12:01:47,746 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-21 12:01:47,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 442145.9, 'new_value': 449744.9}, {'field': 'total_amount', 'old_value': 442145.9, 'new_value': 449744.9}, {'field': 'order_count', 'old_value': 404, 'new_value': 415}]
2025-06-21 12:01:47,746 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-21 12:01:48,199 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-21 12:01:48,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 331320.0, 'new_value': 339920.0}, {'field': 'total_amount', 'old_value': 331320.0, 'new_value': 339920.0}, {'field': 'order_count', 'old_value': 7520, 'new_value': 7715}]
2025-06-21 12:01:48,199 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-21 12:01:48,621 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-21 12:01:48,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101633.35, 'new_value': 106958.35}, {'field': 'total_amount', 'old_value': 101633.35, 'new_value': 106958.35}, {'field': 'order_count', 'old_value': 2596, 'new_value': 2732}]
2025-06-21 12:01:48,621 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-21 12:01:49,136 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-21 12:01:49,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131805.02, 'new_value': 141694.07}, {'field': 'total_amount', 'old_value': 221671.57, 'new_value': 231560.62}, {'field': 'order_count', 'old_value': 1740, 'new_value': 1827}]
2025-06-21 12:01:49,136 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-21 12:01:49,574 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-21 12:01:49,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64888.0, 'new_value': 67271.0}, {'field': 'total_amount', 'old_value': 64888.0, 'new_value': 67271.0}, {'field': 'order_count', 'old_value': 585, 'new_value': 610}]
2025-06-21 12:01:49,574 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-21 12:01:50,027 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-21 12:01:50,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66929.0, 'new_value': 69654.0}, {'field': 'total_amount', 'old_value': 66929.0, 'new_value': 69654.0}, {'field': 'order_count', 'old_value': 1017, 'new_value': 1056}]
2025-06-21 12:01:50,027 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-21 12:01:50,402 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-21 12:01:50,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59182.4, 'new_value': 64584.4}, {'field': 'total_amount', 'old_value': 59182.4, 'new_value': 64584.4}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-06-21 12:01:50,402 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-21 12:01:50,886 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-21 12:01:50,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50350.0, 'new_value': 54590.0}, {'field': 'total_amount', 'old_value': 53795.0, 'new_value': 58035.0}, {'field': 'order_count', 'old_value': 203, 'new_value': 219}]
2025-06-21 12:01:50,886 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-21 12:01:51,324 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-21 12:01:51,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34257.54, 'new_value': 36679.52}, {'field': 'total_amount', 'old_value': 34257.54, 'new_value': 36679.52}, {'field': 'order_count', 'old_value': 4356, 'new_value': 4666}]
2025-06-21 12:01:51,324 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-21 12:01:51,793 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-21 12:01:51,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233409.77, 'new_value': 247005.0}, {'field': 'total_amount', 'old_value': 233409.77, 'new_value': 247005.0}, {'field': 'order_count', 'old_value': 645, 'new_value': 685}]
2025-06-21 12:01:51,793 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-21 12:01:52,746 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-21 12:01:52,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237952.0, 'new_value': 252821.0}, {'field': 'total_amount', 'old_value': 237952.0, 'new_value': 252821.0}, {'field': 'order_count', 'old_value': 5486, 'new_value': 5783}]
2025-06-21 12:01:52,746 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-21 12:01:53,261 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-21 12:01:53,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214473.0, 'new_value': 228873.0}, {'field': 'total_amount', 'old_value': 214473.0, 'new_value': 228873.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-06-21 12:01:53,261 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-21 12:01:53,746 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-21 12:01:53,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2367307.03, 'new_value': 2498048.29}, {'field': 'total_amount', 'old_value': 2367307.03, 'new_value': 2498048.29}, {'field': 'order_count', 'old_value': 4405, 'new_value': 4634}]
2025-06-21 12:01:53,746 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-21 12:01:54,136 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-21 12:01:54,136 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3768.0, 'new_value': 3816.0}, {'field': 'offline_amount', 'old_value': 16840.0, 'new_value': 17420.0}, {'field': 'total_amount', 'old_value': 20608.0, 'new_value': 21236.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-06-21 12:01:54,136 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-21 12:01:54,746 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-21 12:01:54,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600999.0, 'new_value': 625762.0}, {'field': 'total_amount', 'old_value': 600999.0, 'new_value': 625762.0}, {'field': 'order_count', 'old_value': 3279, 'new_value': 3416}]
2025-06-21 12:01:54,746 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-21 12:01:55,214 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-21 12:01:55,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7097870.73, 'new_value': 7409971.73}, {'field': 'total_amount', 'old_value': 7097870.73, 'new_value': 7409971.73}, {'field': 'order_count', 'old_value': 26385, 'new_value': 27585}]
2025-06-21 12:01:55,214 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-21 12:01:55,699 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-21 12:01:55,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124906.4, 'new_value': 132576.84}, {'field': 'total_amount', 'old_value': 124906.4, 'new_value': 132576.84}, {'field': 'order_count', 'old_value': 13527, 'new_value': 14389}]
2025-06-21 12:01:55,699 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-21 12:01:56,152 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-21 12:01:56,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148309.54, 'new_value': 157883.48}, {'field': 'offline_amount', 'old_value': 123881.36, 'new_value': 133067.7}, {'field': 'total_amount', 'old_value': 272190.9, 'new_value': 290951.18}, {'field': 'order_count', 'old_value': 11855, 'new_value': 12597}]
2025-06-21 12:01:56,152 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-21 12:01:56,605 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-21 12:01:56,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153598.85, 'new_value': 170958.85}, {'field': 'total_amount', 'old_value': 187800.95, 'new_value': 205160.95}, {'field': 'order_count', 'old_value': 592, 'new_value': 636}]
2025-06-21 12:01:56,605 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-21 12:01:57,043 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-21 12:01:57,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10061876.0, 'new_value': 12577345.0}, {'field': 'total_amount', 'old_value': 10061876.0, 'new_value': 12577345.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 20}]
2025-06-21 12:01:57,043 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-21 12:01:57,543 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-21 12:01:57,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143935.0, 'new_value': 145278.0}, {'field': 'total_amount', 'old_value': 143935.0, 'new_value': 145278.0}, {'field': 'order_count', 'old_value': 623, 'new_value': 639}]
2025-06-21 12:01:57,543 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-21 12:01:57,964 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-21 12:01:57,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41046.0, 'new_value': 42546.0}, {'field': 'total_amount', 'old_value': 45592.0, 'new_value': 47092.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-06-21 12:01:57,964 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-21 12:01:58,449 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-21 12:01:58,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257413.0, 'new_value': 270613.0}, {'field': 'total_amount', 'old_value': 257413.0, 'new_value': 270613.0}, {'field': 'order_count', 'old_value': 7607, 'new_value': 7987}]
2025-06-21 12:01:58,449 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ2
2025-06-21 12:01:58,902 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ2
2025-06-21 12:01:58,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12080.0, 'new_value': 16080.0}, {'field': 'total_amount', 'old_value': 12080.0, 'new_value': 16080.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-21 12:01:58,902 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-21 12:01:59,449 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-21 12:01:59,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104220.0, 'new_value': 107548.0}, {'field': 'total_amount', 'old_value': 104220.0, 'new_value': 107548.0}, {'field': 'order_count', 'old_value': 2395, 'new_value': 2396}]
2025-06-21 12:01:59,449 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-21 12:01:59,871 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-21 12:01:59,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214441.84, 'new_value': 216941.84}, {'field': 'total_amount', 'old_value': 214441.84, 'new_value': 216941.84}, {'field': 'order_count', 'old_value': 80, 'new_value': 81}]
2025-06-21 12:01:59,886 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-21 12:02:00,339 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-21 12:02:00,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30600.0, 'new_value': 32934.0}, {'field': 'total_amount', 'old_value': 30600.0, 'new_value': 32934.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-21 12:02:00,339 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-21 12:02:00,808 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-21 12:02:00,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6533.5, 'new_value': 6985.5}, {'field': 'total_amount', 'old_value': 6533.5, 'new_value': 6985.5}, {'field': 'order_count', 'old_value': 43, 'new_value': 46}]
2025-06-21 12:02:00,808 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-21 12:02:01,261 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-21 12:02:01,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145956.0, 'new_value': 219203.0}, {'field': 'total_amount', 'old_value': 145956.0, 'new_value': 219203.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 270}]
2025-06-21 12:02:01,261 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-21 12:02:01,730 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-21 12:02:01,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28802.53, 'new_value': 30336.85}, {'field': 'total_amount', 'old_value': 28802.53, 'new_value': 30336.85}, {'field': 'order_count', 'old_value': 1161, 'new_value': 1220}]
2025-06-21 12:02:01,730 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-21 12:02:02,167 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-21 12:02:02,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132804.49, 'new_value': 141810.26}, {'field': 'total_amount', 'old_value': 132804.49, 'new_value': 141810.26}, {'field': 'order_count', 'old_value': 10038, 'new_value': 10748}]
2025-06-21 12:02:02,167 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-21 12:02:02,652 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-21 12:02:02,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162396.28, 'new_value': 173472.24}, {'field': 'total_amount', 'old_value': 162396.28, 'new_value': 173472.24}, {'field': 'order_count', 'old_value': 12621, 'new_value': 12843}]
2025-06-21 12:02:02,652 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-21 12:02:03,089 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-21 12:02:03,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 386000.0, 'new_value': 401000.0}, {'field': 'total_amount', 'old_value': 386000.0, 'new_value': 401000.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-21 12:02:03,089 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-21 12:02:03,574 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-21 12:02:03,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24724.32, 'new_value': 29839.32}, {'field': 'total_amount', 'old_value': 24724.32, 'new_value': 29839.32}, {'field': 'order_count', 'old_value': 146, 'new_value': 179}]
2025-06-21 12:02:03,574 - INFO - 日期 2025-06 处理完成 - 更新: 184 条，插入: 0 条，错误: 0 条
2025-06-21 12:02:03,574 - INFO - 数据同步完成！更新: 184 条，插入: 0 条，错误: 0 条
2025-06-21 12:02:03,574 - INFO - =================同步完成====================
2025-06-21 15:00:02,876 - INFO - =================使用默认全量同步=============
2025-06-21 15:00:04,641 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 15:00:04,641 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 15:00:04,672 - INFO - 开始处理日期: 2025-01
2025-06-21 15:00:04,688 - INFO - Request Parameters - Page 1:
2025-06-21 15:00:04,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:04,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:06,235 - INFO - Response - Page 1:
2025-06-21 15:00:06,438 - INFO - 第 1 页获取到 100 条记录
2025-06-21 15:00:06,438 - INFO - Request Parameters - Page 2:
2025-06-21 15:00:06,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:06,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:07,188 - INFO - Response - Page 2:
2025-06-21 15:00:07,391 - INFO - 第 2 页获取到 100 条记录
2025-06-21 15:00:07,391 - INFO - Request Parameters - Page 3:
2025-06-21 15:00:07,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:07,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:07,954 - INFO - Response - Page 3:
2025-06-21 15:00:08,157 - INFO - 第 3 页获取到 100 条记录
2025-06-21 15:00:08,157 - INFO - Request Parameters - Page 4:
2025-06-21 15:00:08,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:08,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:08,829 - INFO - Response - Page 4:
2025-06-21 15:00:09,032 - INFO - 第 4 页获取到 100 条记录
2025-06-21 15:00:09,032 - INFO - Request Parameters - Page 5:
2025-06-21 15:00:09,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:09,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:09,688 - INFO - Response - Page 5:
2025-06-21 15:00:09,891 - INFO - 第 5 页获取到 100 条记录
2025-06-21 15:00:09,891 - INFO - Request Parameters - Page 6:
2025-06-21 15:00:09,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:09,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:10,438 - INFO - Response - Page 6:
2025-06-21 15:00:10,641 - INFO - 第 6 页获取到 100 条记录
2025-06-21 15:00:10,641 - INFO - Request Parameters - Page 7:
2025-06-21 15:00:10,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:10,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:11,157 - INFO - Response - Page 7:
2025-06-21 15:00:11,360 - INFO - 第 7 页获取到 82 条记录
2025-06-21 15:00:11,360 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 15:00:11,360 - INFO - 获取到 682 条表单数据
2025-06-21 15:00:11,360 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 15:00:11,376 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:11,376 - INFO - 开始处理日期: 2025-02
2025-06-21 15:00:11,376 - INFO - Request Parameters - Page 1:
2025-06-21 15:00:11,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:11,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:11,891 - INFO - Response - Page 1:
2025-06-21 15:00:12,094 - INFO - 第 1 页获取到 100 条记录
2025-06-21 15:00:12,094 - INFO - Request Parameters - Page 2:
2025-06-21 15:00:12,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:12,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:12,594 - INFO - Response - Page 2:
2025-06-21 15:00:12,797 - INFO - 第 2 页获取到 100 条记录
2025-06-21 15:00:12,797 - INFO - Request Parameters - Page 3:
2025-06-21 15:00:12,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:12,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:13,344 - INFO - Response - Page 3:
2025-06-21 15:00:13,547 - INFO - 第 3 页获取到 100 条记录
2025-06-21 15:00:13,547 - INFO - Request Parameters - Page 4:
2025-06-21 15:00:13,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:13,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:14,016 - INFO - Response - Page 4:
2025-06-21 15:00:14,219 - INFO - 第 4 页获取到 100 条记录
2025-06-21 15:00:14,219 - INFO - Request Parameters - Page 5:
2025-06-21 15:00:14,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:14,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:14,688 - INFO - Response - Page 5:
2025-06-21 15:00:14,891 - INFO - 第 5 页获取到 100 条记录
2025-06-21 15:00:14,891 - INFO - Request Parameters - Page 6:
2025-06-21 15:00:14,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:14,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:15,407 - INFO - Response - Page 6:
2025-06-21 15:00:15,610 - INFO - 第 6 页获取到 100 条记录
2025-06-21 15:00:15,610 - INFO - Request Parameters - Page 7:
2025-06-21 15:00:15,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:15,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:16,047 - INFO - Response - Page 7:
2025-06-21 15:00:16,251 - INFO - 第 7 页获取到 70 条记录
2025-06-21 15:00:16,251 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 15:00:16,251 - INFO - 获取到 670 条表单数据
2025-06-21 15:00:16,251 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 15:00:16,266 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:16,266 - INFO - 开始处理日期: 2025-03
2025-06-21 15:00:16,266 - INFO - Request Parameters - Page 1:
2025-06-21 15:00:16,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:16,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:16,797 - INFO - Response - Page 1:
2025-06-21 15:00:17,001 - INFO - 第 1 页获取到 100 条记录
2025-06-21 15:00:17,001 - INFO - Request Parameters - Page 2:
2025-06-21 15:00:17,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:17,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:17,501 - INFO - Response - Page 2:
2025-06-21 15:00:17,704 - INFO - 第 2 页获取到 100 条记录
2025-06-21 15:00:17,704 - INFO - Request Parameters - Page 3:
2025-06-21 15:00:17,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:17,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:18,251 - INFO - Response - Page 3:
2025-06-21 15:00:18,454 - INFO - 第 3 页获取到 100 条记录
2025-06-21 15:00:18,454 - INFO - Request Parameters - Page 4:
2025-06-21 15:00:18,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:18,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:18,954 - INFO - Response - Page 4:
2025-06-21 15:00:19,157 - INFO - 第 4 页获取到 100 条记录
2025-06-21 15:00:19,157 - INFO - Request Parameters - Page 5:
2025-06-21 15:00:19,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:19,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:19,626 - INFO - Response - Page 5:
2025-06-21 15:00:19,829 - INFO - 第 5 页获取到 100 条记录
2025-06-21 15:00:19,829 - INFO - Request Parameters - Page 6:
2025-06-21 15:00:19,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:19,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:20,829 - INFO - Response - Page 6:
2025-06-21 15:00:21,032 - INFO - 第 6 页获取到 100 条记录
2025-06-21 15:00:21,032 - INFO - Request Parameters - Page 7:
2025-06-21 15:00:21,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:21,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:21,500 - INFO - Response - Page 7:
2025-06-21 15:00:21,704 - INFO - 第 7 页获取到 61 条记录
2025-06-21 15:00:21,704 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 15:00:21,704 - INFO - 获取到 661 条表单数据
2025-06-21 15:00:21,704 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 15:00:21,719 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:21,719 - INFO - 开始处理日期: 2025-04
2025-06-21 15:00:21,719 - INFO - Request Parameters - Page 1:
2025-06-21 15:00:21,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:21,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:22,344 - INFO - Response - Page 1:
2025-06-21 15:00:22,547 - INFO - 第 1 页获取到 100 条记录
2025-06-21 15:00:22,547 - INFO - Request Parameters - Page 2:
2025-06-21 15:00:22,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:22,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:23,157 - INFO - Response - Page 2:
2025-06-21 15:00:23,360 - INFO - 第 2 页获取到 100 条记录
2025-06-21 15:00:23,360 - INFO - Request Parameters - Page 3:
2025-06-21 15:00:23,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:23,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:23,907 - INFO - Response - Page 3:
2025-06-21 15:00:24,110 - INFO - 第 3 页获取到 100 条记录
2025-06-21 15:00:24,110 - INFO - Request Parameters - Page 4:
2025-06-21 15:00:24,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:24,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:24,594 - INFO - Response - Page 4:
2025-06-21 15:00:24,797 - INFO - 第 4 页获取到 100 条记录
2025-06-21 15:00:24,797 - INFO - Request Parameters - Page 5:
2025-06-21 15:00:24,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:24,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:25,297 - INFO - Response - Page 5:
2025-06-21 15:00:25,500 - INFO - 第 5 页获取到 100 条记录
2025-06-21 15:00:25,500 - INFO - Request Parameters - Page 6:
2025-06-21 15:00:25,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:25,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:26,000 - INFO - Response - Page 6:
2025-06-21 15:00:26,204 - INFO - 第 6 页获取到 100 条记录
2025-06-21 15:00:26,204 - INFO - Request Parameters - Page 7:
2025-06-21 15:00:26,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:26,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:26,657 - INFO - Response - Page 7:
2025-06-21 15:00:26,860 - INFO - 第 7 页获取到 56 条记录
2025-06-21 15:00:26,860 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 15:00:26,860 - INFO - 获取到 656 条表单数据
2025-06-21 15:00:26,875 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 15:00:26,875 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:26,875 - INFO - 开始处理日期: 2025-05
2025-06-21 15:00:26,875 - INFO - Request Parameters - Page 1:
2025-06-21 15:00:26,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:26,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:27,391 - INFO - Response - Page 1:
2025-06-21 15:00:27,594 - INFO - 第 1 页获取到 100 条记录
2025-06-21 15:00:27,594 - INFO - Request Parameters - Page 2:
2025-06-21 15:00:27,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:27,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:28,094 - INFO - Response - Page 2:
2025-06-21 15:00:28,297 - INFO - 第 2 页获取到 100 条记录
2025-06-21 15:00:28,297 - INFO - Request Parameters - Page 3:
2025-06-21 15:00:28,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:28,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:28,750 - INFO - Response - Page 3:
2025-06-21 15:00:28,954 - INFO - 第 3 页获取到 100 条记录
2025-06-21 15:00:28,954 - INFO - Request Parameters - Page 4:
2025-06-21 15:00:28,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:28,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:29,438 - INFO - Response - Page 4:
2025-06-21 15:00:29,641 - INFO - 第 4 页获取到 100 条记录
2025-06-21 15:00:29,641 - INFO - Request Parameters - Page 5:
2025-06-21 15:00:29,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:29,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:30,235 - INFO - Response - Page 5:
2025-06-21 15:00:30,438 - INFO - 第 5 页获取到 100 条记录
2025-06-21 15:00:30,438 - INFO - Request Parameters - Page 6:
2025-06-21 15:00:30,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:30,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:30,922 - INFO - Response - Page 6:
2025-06-21 15:00:31,125 - INFO - 第 6 页获取到 100 条记录
2025-06-21 15:00:31,125 - INFO - Request Parameters - Page 7:
2025-06-21 15:00:31,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:31,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:31,610 - INFO - Response - Page 7:
2025-06-21 15:00:31,813 - INFO - 第 7 页获取到 65 条记录
2025-06-21 15:00:31,813 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 15:00:31,813 - INFO - 获取到 665 条表单数据
2025-06-21 15:00:31,813 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 15:00:31,829 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:31,829 - INFO - 开始处理日期: 2025-06
2025-06-21 15:00:31,829 - INFO - Request Parameters - Page 1:
2025-06-21 15:00:31,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:31,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:32,375 - INFO - Response - Page 1:
2025-06-21 15:00:32,579 - INFO - 第 1 页获取到 100 条记录
2025-06-21 15:00:32,579 - INFO - Request Parameters - Page 2:
2025-06-21 15:00:32,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:32,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:33,125 - INFO - Response - Page 2:
2025-06-21 15:00:33,329 - INFO - 第 2 页获取到 100 条记录
2025-06-21 15:00:33,329 - INFO - Request Parameters - Page 3:
2025-06-21 15:00:33,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:33,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:33,875 - INFO - Response - Page 3:
2025-06-21 15:00:34,079 - INFO - 第 3 页获取到 100 条记录
2025-06-21 15:00:34,079 - INFO - Request Parameters - Page 4:
2025-06-21 15:00:34,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:34,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:34,657 - INFO - Response - Page 4:
2025-06-21 15:00:34,860 - INFO - 第 4 页获取到 100 条记录
2025-06-21 15:00:34,860 - INFO - Request Parameters - Page 5:
2025-06-21 15:00:34,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:34,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:35,344 - INFO - Response - Page 5:
2025-06-21 15:00:35,547 - INFO - 第 5 页获取到 100 条记录
2025-06-21 15:00:35,547 - INFO - Request Parameters - Page 6:
2025-06-21 15:00:35,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:35,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:36,063 - INFO - Response - Page 6:
2025-06-21 15:00:36,266 - INFO - 第 6 页获取到 100 条记录
2025-06-21 15:00:36,266 - INFO - Request Parameters - Page 7:
2025-06-21 15:00:36,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 15:00:36,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 15:00:36,594 - INFO - Response - Page 7:
2025-06-21 15:00:36,797 - INFO - 第 7 页获取到 23 条记录
2025-06-21 15:00:36,797 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 15:00:36,797 - INFO - 获取到 623 条表单数据
2025-06-21 15:00:36,797 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 15:00:36,813 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-21 15:00:37,360 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-21 15:00:37,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100113.0, 'new_value': 108328.0}, {'field': 'total_amount', 'old_value': 158305.0, 'new_value': 166520.0}, {'field': 'order_count', 'old_value': 3599, 'new_value': 3798}]
2025-06-21 15:00:37,360 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7L
2025-06-21 15:00:37,797 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7L
2025-06-21 15:00:37,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168819.4, 'new_value': 172334.0}, {'field': 'total_amount', 'old_value': 168819.4, 'new_value': 172334.0}, {'field': 'order_count', 'old_value': 1798, 'new_value': 1822}]
2025-06-21 15:00:37,797 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-21 15:00:38,282 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-21 15:00:38,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15238.46, 'new_value': 16197.27}, {'field': 'offline_amount', 'old_value': 8827.84, 'new_value': 9477.84}, {'field': 'total_amount', 'old_value': 24066.3, 'new_value': 25675.11}, {'field': 'order_count', 'old_value': 904, 'new_value': 956}]
2025-06-21 15:00:38,282 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-21 15:00:38,719 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-21 15:00:38,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9933.0, 'new_value': 11032.0}, {'field': 'total_amount', 'old_value': 12132.0, 'new_value': 13231.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-21 15:00:38,719 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-21 15:00:39,157 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-21 15:00:39,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 899000.0, 'new_value': 949000.0}, {'field': 'total_amount', 'old_value': 899000.0, 'new_value': 949000.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 54}]
2025-06-21 15:00:39,157 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-21 15:00:39,657 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-21 15:00:39,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17650.0, 'new_value': 18249.0}, {'field': 'total_amount', 'old_value': 17650.0, 'new_value': 18249.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-21 15:00:39,657 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-21 15:00:40,047 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-21 15:00:40,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73287.0, 'new_value': 77771.0}, {'field': 'total_amount', 'old_value': 73287.0, 'new_value': 77771.0}, {'field': 'order_count', 'old_value': 548, 'new_value': 580}]
2025-06-21 15:00:40,047 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-21 15:00:40,485 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-21 15:00:40,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56984.0, 'new_value': 60283.0}, {'field': 'total_amount', 'old_value': 56984.0, 'new_value': 60283.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-06-21 15:00:40,485 - INFO - 日期 2025-06 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:40,485 - INFO - 数据同步完成！更新: 8 条，插入: 0 条，错误: 0 条
2025-06-21 15:00:40,485 - INFO - =================同步完成====================
2025-06-21 18:00:02,349 - INFO - =================使用默认全量同步=============
2025-06-21 18:00:04,052 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 18:00:04,052 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 18:00:04,083 - INFO - 开始处理日期: 2025-01
2025-06-21 18:00:04,083 - INFO - Request Parameters - Page 1:
2025-06-21 18:00:04,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:04,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:05,067 - INFO - Response - Page 1:
2025-06-21 18:00:05,271 - INFO - 第 1 页获取到 100 条记录
2025-06-21 18:00:05,271 - INFO - Request Parameters - Page 2:
2025-06-21 18:00:05,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:05,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:06,239 - INFO - Response - Page 2:
2025-06-21 18:00:06,442 - INFO - 第 2 页获取到 100 条记录
2025-06-21 18:00:06,442 - INFO - Request Parameters - Page 3:
2025-06-21 18:00:06,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:06,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:07,021 - INFO - Response - Page 3:
2025-06-21 18:00:07,224 - INFO - 第 3 页获取到 100 条记录
2025-06-21 18:00:07,224 - INFO - Request Parameters - Page 4:
2025-06-21 18:00:07,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:07,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:07,771 - INFO - Response - Page 4:
2025-06-21 18:00:07,974 - INFO - 第 4 页获取到 100 条记录
2025-06-21 18:00:07,974 - INFO - Request Parameters - Page 5:
2025-06-21 18:00:07,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:07,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:08,521 - INFO - Response - Page 5:
2025-06-21 18:00:08,724 - INFO - 第 5 页获取到 100 条记录
2025-06-21 18:00:08,724 - INFO - Request Parameters - Page 6:
2025-06-21 18:00:08,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:08,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:09,224 - INFO - Response - Page 6:
2025-06-21 18:00:09,427 - INFO - 第 6 页获取到 100 条记录
2025-06-21 18:00:09,427 - INFO - Request Parameters - Page 7:
2025-06-21 18:00:09,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:09,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:09,927 - INFO - Response - Page 7:
2025-06-21 18:00:10,130 - INFO - 第 7 页获取到 82 条记录
2025-06-21 18:00:10,130 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 18:00:10,130 - INFO - 获取到 682 条表单数据
2025-06-21 18:00:10,130 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 18:00:10,146 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:10,146 - INFO - 开始处理日期: 2025-02
2025-06-21 18:00:10,146 - INFO - Request Parameters - Page 1:
2025-06-21 18:00:10,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:10,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:10,708 - INFO - Response - Page 1:
2025-06-21 18:00:10,911 - INFO - 第 1 页获取到 100 条记录
2025-06-21 18:00:10,911 - INFO - Request Parameters - Page 2:
2025-06-21 18:00:10,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:10,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:11,396 - INFO - Response - Page 2:
2025-06-21 18:00:11,599 - INFO - 第 2 页获取到 100 条记录
2025-06-21 18:00:11,599 - INFO - Request Parameters - Page 3:
2025-06-21 18:00:11,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:11,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:12,208 - INFO - Response - Page 3:
2025-06-21 18:00:12,411 - INFO - 第 3 页获取到 100 条记录
2025-06-21 18:00:12,411 - INFO - Request Parameters - Page 4:
2025-06-21 18:00:12,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:12,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:13,052 - INFO - Response - Page 4:
2025-06-21 18:00:13,255 - INFO - 第 4 页获取到 100 条记录
2025-06-21 18:00:13,255 - INFO - Request Parameters - Page 5:
2025-06-21 18:00:13,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:13,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:13,755 - INFO - Response - Page 5:
2025-06-21 18:00:13,958 - INFO - 第 5 页获取到 100 条记录
2025-06-21 18:00:13,958 - INFO - Request Parameters - Page 6:
2025-06-21 18:00:13,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:13,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:14,646 - INFO - Response - Page 6:
2025-06-21 18:00:14,849 - INFO - 第 6 页获取到 100 条记录
2025-06-21 18:00:14,849 - INFO - Request Parameters - Page 7:
2025-06-21 18:00:14,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:14,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:15,349 - INFO - Response - Page 7:
2025-06-21 18:00:15,552 - INFO - 第 7 页获取到 70 条记录
2025-06-21 18:00:15,552 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 18:00:15,552 - INFO - 获取到 670 条表单数据
2025-06-21 18:00:15,552 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 18:00:15,567 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:15,567 - INFO - 开始处理日期: 2025-03
2025-06-21 18:00:15,567 - INFO - Request Parameters - Page 1:
2025-06-21 18:00:15,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:15,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:16,130 - INFO - Response - Page 1:
2025-06-21 18:00:16,333 - INFO - 第 1 页获取到 100 条记录
2025-06-21 18:00:16,333 - INFO - Request Parameters - Page 2:
2025-06-21 18:00:16,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:16,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:16,833 - INFO - Response - Page 2:
2025-06-21 18:00:17,036 - INFO - 第 2 页获取到 100 条记录
2025-06-21 18:00:17,036 - INFO - Request Parameters - Page 3:
2025-06-21 18:00:17,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:17,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:17,552 - INFO - Response - Page 3:
2025-06-21 18:00:17,755 - INFO - 第 3 页获取到 100 条记录
2025-06-21 18:00:17,755 - INFO - Request Parameters - Page 4:
2025-06-21 18:00:17,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:17,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:18,224 - INFO - Response - Page 4:
2025-06-21 18:00:18,427 - INFO - 第 4 页获取到 100 条记录
2025-06-21 18:00:18,427 - INFO - Request Parameters - Page 5:
2025-06-21 18:00:18,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:18,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:18,942 - INFO - Response - Page 5:
2025-06-21 18:00:19,145 - INFO - 第 5 页获取到 100 条记录
2025-06-21 18:00:19,145 - INFO - Request Parameters - Page 6:
2025-06-21 18:00:19,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:19,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:19,677 - INFO - Response - Page 6:
2025-06-21 18:00:19,880 - INFO - 第 6 页获取到 100 条记录
2025-06-21 18:00:19,880 - INFO - Request Parameters - Page 7:
2025-06-21 18:00:19,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:19,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:20,380 - INFO - Response - Page 7:
2025-06-21 18:00:20,599 - INFO - 第 7 页获取到 61 条记录
2025-06-21 18:00:20,599 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 18:00:20,599 - INFO - 获取到 661 条表单数据
2025-06-21 18:00:20,599 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 18:00:20,614 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:20,614 - INFO - 开始处理日期: 2025-04
2025-06-21 18:00:20,614 - INFO - Request Parameters - Page 1:
2025-06-21 18:00:20,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:20,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:21,099 - INFO - Response - Page 1:
2025-06-21 18:00:21,302 - INFO - 第 1 页获取到 100 条记录
2025-06-21 18:00:21,302 - INFO - Request Parameters - Page 2:
2025-06-21 18:00:21,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:21,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:21,864 - INFO - Response - Page 2:
2025-06-21 18:00:22,067 - INFO - 第 2 页获取到 100 条记录
2025-06-21 18:00:22,067 - INFO - Request Parameters - Page 3:
2025-06-21 18:00:22,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:22,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:22,599 - INFO - Response - Page 3:
2025-06-21 18:00:22,802 - INFO - 第 3 页获取到 100 条记录
2025-06-21 18:00:22,802 - INFO - Request Parameters - Page 4:
2025-06-21 18:00:22,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:22,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:23,317 - INFO - Response - Page 4:
2025-06-21 18:00:23,520 - INFO - 第 4 页获取到 100 条记录
2025-06-21 18:00:23,520 - INFO - Request Parameters - Page 5:
2025-06-21 18:00:23,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:23,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:23,974 - INFO - Response - Page 5:
2025-06-21 18:00:24,177 - INFO - 第 5 页获取到 100 条记录
2025-06-21 18:00:24,177 - INFO - Request Parameters - Page 6:
2025-06-21 18:00:24,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:24,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:24,802 - INFO - Response - Page 6:
2025-06-21 18:00:25,005 - INFO - 第 6 页获取到 100 条记录
2025-06-21 18:00:25,005 - INFO - Request Parameters - Page 7:
2025-06-21 18:00:25,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:25,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:25,458 - INFO - Response - Page 7:
2025-06-21 18:00:25,677 - INFO - 第 7 页获取到 56 条记录
2025-06-21 18:00:25,677 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 18:00:25,677 - INFO - 获取到 656 条表单数据
2025-06-21 18:00:25,692 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 18:00:25,708 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:25,708 - INFO - 开始处理日期: 2025-05
2025-06-21 18:00:25,708 - INFO - Request Parameters - Page 1:
2025-06-21 18:00:25,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:25,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:26,224 - INFO - Response - Page 1:
2025-06-21 18:00:26,427 - INFO - 第 1 页获取到 100 条记录
2025-06-21 18:00:26,427 - INFO - Request Parameters - Page 2:
2025-06-21 18:00:26,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:26,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:26,911 - INFO - Response - Page 2:
2025-06-21 18:00:27,114 - INFO - 第 2 页获取到 100 条记录
2025-06-21 18:00:27,114 - INFO - Request Parameters - Page 3:
2025-06-21 18:00:27,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:27,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:27,645 - INFO - Response - Page 3:
2025-06-21 18:00:27,849 - INFO - 第 3 页获取到 100 条记录
2025-06-21 18:00:27,849 - INFO - Request Parameters - Page 4:
2025-06-21 18:00:27,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:27,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:28,380 - INFO - Response - Page 4:
2025-06-21 18:00:28,583 - INFO - 第 4 页获取到 100 条记录
2025-06-21 18:00:28,583 - INFO - Request Parameters - Page 5:
2025-06-21 18:00:28,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:28,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:29,145 - INFO - Response - Page 5:
2025-06-21 18:00:29,349 - INFO - 第 5 页获取到 100 条记录
2025-06-21 18:00:29,349 - INFO - Request Parameters - Page 6:
2025-06-21 18:00:29,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:29,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:29,833 - INFO - Response - Page 6:
2025-06-21 18:00:30,036 - INFO - 第 6 页获取到 100 条记录
2025-06-21 18:00:30,036 - INFO - Request Parameters - Page 7:
2025-06-21 18:00:30,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:30,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:30,474 - INFO - Response - Page 7:
2025-06-21 18:00:30,677 - INFO - 第 7 页获取到 65 条记录
2025-06-21 18:00:30,677 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 18:00:30,677 - INFO - 获取到 665 条表单数据
2025-06-21 18:00:30,677 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 18:00:30,692 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:30,692 - INFO - 开始处理日期: 2025-06
2025-06-21 18:00:30,692 - INFO - Request Parameters - Page 1:
2025-06-21 18:00:30,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:30,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:31,224 - INFO - Response - Page 1:
2025-06-21 18:00:31,427 - INFO - 第 1 页获取到 100 条记录
2025-06-21 18:00:31,427 - INFO - Request Parameters - Page 2:
2025-06-21 18:00:31,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:31,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:31,958 - INFO - Response - Page 2:
2025-06-21 18:00:32,161 - INFO - 第 2 页获取到 100 条记录
2025-06-21 18:00:32,161 - INFO - Request Parameters - Page 3:
2025-06-21 18:00:32,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:32,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:32,770 - INFO - Response - Page 3:
2025-06-21 18:00:32,974 - INFO - 第 3 页获取到 100 条记录
2025-06-21 18:00:32,974 - INFO - Request Parameters - Page 4:
2025-06-21 18:00:32,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:32,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:33,505 - INFO - Response - Page 4:
2025-06-21 18:00:33,708 - INFO - 第 4 页获取到 100 条记录
2025-06-21 18:00:33,708 - INFO - Request Parameters - Page 5:
2025-06-21 18:00:33,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:33,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:34,286 - INFO - Response - Page 5:
2025-06-21 18:00:34,489 - INFO - 第 5 页获取到 100 条记录
2025-06-21 18:00:34,489 - INFO - Request Parameters - Page 6:
2025-06-21 18:00:34,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:34,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:34,958 - INFO - Response - Page 6:
2025-06-21 18:00:35,161 - INFO - 第 6 页获取到 100 条记录
2025-06-21 18:00:35,161 - INFO - Request Parameters - Page 7:
2025-06-21 18:00:35,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 18:00:35,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 18:00:35,473 - INFO - Response - Page 7:
2025-06-21 18:00:35,677 - INFO - 第 7 页获取到 23 条记录
2025-06-21 18:00:35,677 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 18:00:35,677 - INFO - 获取到 623 条表单数据
2025-06-21 18:00:35,677 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 18:00:35,692 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-21 18:00:36,192 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-21 18:00:36,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116896.0, 'new_value': 123582.0}, {'field': 'total_amount', 'old_value': 116896.0, 'new_value': 123582.0}, {'field': 'order_count', 'old_value': 10376, 'new_value': 10975}]
2025-06-21 18:00:36,192 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-21 18:00:36,645 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-21 18:00:36,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106872.0, 'new_value': 115672.0}, {'field': 'total_amount', 'old_value': 106872.0, 'new_value': 115672.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-21 18:00:36,645 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM23
2025-06-21 18:00:37,145 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM23
2025-06-21 18:00:37,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24419.9, 'new_value': 24449.8}, {'field': 'total_amount', 'old_value': 24419.9, 'new_value': 24449.8}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-21 18:00:37,145 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-21 18:00:37,598 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-21 18:00:37,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95360.0, 'new_value': 100155.3}, {'field': 'total_amount', 'old_value': 95360.0, 'new_value': 100155.3}, {'field': 'order_count', 'old_value': 3985, 'new_value': 4083}]
2025-06-21 18:00:37,598 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-21 18:00:38,036 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-21 18:00:38,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106319.79, 'new_value': 112844.79}, {'field': 'total_amount', 'old_value': 106319.79, 'new_value': 112844.79}, {'field': 'order_count', 'old_value': 791, 'new_value': 825}]
2025-06-21 18:00:38,036 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-21 18:00:38,489 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-21 18:00:38,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25575.5, 'new_value': 27164.5}, {'field': 'total_amount', 'old_value': 25575.5, 'new_value': 27164.5}, {'field': 'order_count', 'old_value': 2357, 'new_value': 2472}]
2025-06-21 18:00:38,489 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-21 18:00:38,989 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-21 18:00:38,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4324828.0, 'new_value': 4539687.0}, {'field': 'total_amount', 'old_value': 4324828.0, 'new_value': 4539687.0}, {'field': 'order_count', 'old_value': 78675, 'new_value': 82670}]
2025-06-21 18:00:38,989 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-21 18:00:39,458 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-21 18:00:39,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185111.2, 'new_value': 186545.22}, {'field': 'total_amount', 'old_value': 185111.2, 'new_value': 186545.22}, {'field': 'order_count', 'old_value': 352, 'new_value': 360}]
2025-06-21 18:00:39,458 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-21 18:00:39,880 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-21 18:00:39,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4161160.0, 'new_value': 4348760.0}, {'field': 'total_amount', 'old_value': 4161160.0, 'new_value': 4348760.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 64}]
2025-06-21 18:00:39,880 - INFO - 日期 2025-06 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:39,880 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-06-21 18:00:39,880 - INFO - =================同步完成====================
2025-06-21 21:00:03,261 - INFO - =================使用默认全量同步=============
2025-06-21 21:00:04,964 - INFO - MySQL查询成功，共获取 3957 条记录
2025-06-21 21:00:04,964 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-21 21:00:04,995 - INFO - 开始处理日期: 2025-01
2025-06-21 21:00:04,995 - INFO - Request Parameters - Page 1:
2025-06-21 21:00:04,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:04,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:06,542 - INFO - Response - Page 1:
2025-06-21 21:00:06,746 - INFO - 第 1 页获取到 100 条记录
2025-06-21 21:00:06,746 - INFO - Request Parameters - Page 2:
2025-06-21 21:00:06,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:06,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:07,230 - INFO - Response - Page 2:
2025-06-21 21:00:07,433 - INFO - 第 2 页获取到 100 条记录
2025-06-21 21:00:07,433 - INFO - Request Parameters - Page 3:
2025-06-21 21:00:07,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:07,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:07,886 - INFO - Response - Page 3:
2025-06-21 21:00:08,090 - INFO - 第 3 页获取到 100 条记录
2025-06-21 21:00:08,090 - INFO - Request Parameters - Page 4:
2025-06-21 21:00:08,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:08,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:08,558 - INFO - Response - Page 4:
2025-06-21 21:00:08,762 - INFO - 第 4 页获取到 100 条记录
2025-06-21 21:00:08,762 - INFO - Request Parameters - Page 5:
2025-06-21 21:00:08,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:08,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:09,230 - INFO - Response - Page 5:
2025-06-21 21:00:09,434 - INFO - 第 5 页获取到 100 条记录
2025-06-21 21:00:09,434 - INFO - Request Parameters - Page 6:
2025-06-21 21:00:09,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:09,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:09,934 - INFO - Response - Page 6:
2025-06-21 21:00:10,137 - INFO - 第 6 页获取到 100 条记录
2025-06-21 21:00:10,137 - INFO - Request Parameters - Page 7:
2025-06-21 21:00:10,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:10,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:10,606 - INFO - Response - Page 7:
2025-06-21 21:00:10,809 - INFO - 第 7 页获取到 82 条记录
2025-06-21 21:00:10,809 - INFO - 查询完成，共获取到 682 条记录
2025-06-21 21:00:10,809 - INFO - 获取到 682 条表单数据
2025-06-21 21:00:10,809 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-21 21:00:10,824 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:10,824 - INFO - 开始处理日期: 2025-02
2025-06-21 21:00:10,824 - INFO - Request Parameters - Page 1:
2025-06-21 21:00:10,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:10,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:11,434 - INFO - Response - Page 1:
2025-06-21 21:00:11,637 - INFO - 第 1 页获取到 100 条记录
2025-06-21 21:00:11,637 - INFO - Request Parameters - Page 2:
2025-06-21 21:00:11,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:11,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:12,153 - INFO - Response - Page 2:
2025-06-21 21:00:12,356 - INFO - 第 2 页获取到 100 条记录
2025-06-21 21:00:12,356 - INFO - Request Parameters - Page 3:
2025-06-21 21:00:12,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:12,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:12,887 - INFO - Response - Page 3:
2025-06-21 21:00:13,090 - INFO - 第 3 页获取到 100 条记录
2025-06-21 21:00:13,090 - INFO - Request Parameters - Page 4:
2025-06-21 21:00:13,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:13,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:13,606 - INFO - Response - Page 4:
2025-06-21 21:00:13,809 - INFO - 第 4 页获取到 100 条记录
2025-06-21 21:00:13,809 - INFO - Request Parameters - Page 5:
2025-06-21 21:00:13,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:13,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:14,263 - INFO - Response - Page 5:
2025-06-21 21:00:14,466 - INFO - 第 5 页获取到 100 条记录
2025-06-21 21:00:14,466 - INFO - Request Parameters - Page 6:
2025-06-21 21:00:14,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:14,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:14,935 - INFO - Response - Page 6:
2025-06-21 21:00:15,138 - INFO - 第 6 页获取到 100 条记录
2025-06-21 21:00:15,138 - INFO - Request Parameters - Page 7:
2025-06-21 21:00:15,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:15,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:15,638 - INFO - Response - Page 7:
2025-06-21 21:00:15,841 - INFO - 第 7 页获取到 70 条记录
2025-06-21 21:00:15,841 - INFO - 查询完成，共获取到 670 条记录
2025-06-21 21:00:15,841 - INFO - 获取到 670 条表单数据
2025-06-21 21:00:15,841 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-21 21:00:15,857 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:15,857 - INFO - 开始处理日期: 2025-03
2025-06-21 21:00:15,857 - INFO - Request Parameters - Page 1:
2025-06-21 21:00:15,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:15,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:16,372 - INFO - Response - Page 1:
2025-06-21 21:00:16,575 - INFO - 第 1 页获取到 100 条记录
2025-06-21 21:00:16,575 - INFO - Request Parameters - Page 2:
2025-06-21 21:00:16,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:16,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:17,060 - INFO - Response - Page 2:
2025-06-21 21:00:17,263 - INFO - 第 2 页获取到 100 条记录
2025-06-21 21:00:17,263 - INFO - Request Parameters - Page 3:
2025-06-21 21:00:17,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:17,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:17,748 - INFO - Response - Page 3:
2025-06-21 21:00:17,951 - INFO - 第 3 页获取到 100 条记录
2025-06-21 21:00:17,951 - INFO - Request Parameters - Page 4:
2025-06-21 21:00:17,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:17,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:18,404 - INFO - Response - Page 4:
2025-06-21 21:00:18,607 - INFO - 第 4 页获取到 100 条记录
2025-06-21 21:00:18,607 - INFO - Request Parameters - Page 5:
2025-06-21 21:00:18,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:18,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:19,123 - INFO - Response - Page 5:
2025-06-21 21:00:19,326 - INFO - 第 5 页获取到 100 条记录
2025-06-21 21:00:19,326 - INFO - Request Parameters - Page 6:
2025-06-21 21:00:19,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:19,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:19,889 - INFO - Response - Page 6:
2025-06-21 21:00:20,092 - INFO - 第 6 页获取到 100 条记录
2025-06-21 21:00:20,092 - INFO - Request Parameters - Page 7:
2025-06-21 21:00:20,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:20,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:20,545 - INFO - Response - Page 7:
2025-06-21 21:00:20,748 - INFO - 第 7 页获取到 61 条记录
2025-06-21 21:00:20,748 - INFO - 查询完成，共获取到 661 条记录
2025-06-21 21:00:20,748 - INFO - 获取到 661 条表单数据
2025-06-21 21:00:20,748 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-21 21:00:20,764 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:20,764 - INFO - 开始处理日期: 2025-04
2025-06-21 21:00:20,764 - INFO - Request Parameters - Page 1:
2025-06-21 21:00:20,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:20,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:21,483 - INFO - Response - Page 1:
2025-06-21 21:00:21,686 - INFO - 第 1 页获取到 100 条记录
2025-06-21 21:00:21,686 - INFO - Request Parameters - Page 2:
2025-06-21 21:00:21,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:21,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:22,123 - INFO - Response - Page 2:
2025-06-21 21:00:22,327 - INFO - 第 2 页获取到 100 条记录
2025-06-21 21:00:22,327 - INFO - Request Parameters - Page 3:
2025-06-21 21:00:22,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:22,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:22,764 - INFO - Response - Page 3:
2025-06-21 21:00:22,967 - INFO - 第 3 页获取到 100 条记录
2025-06-21 21:00:22,967 - INFO - Request Parameters - Page 4:
2025-06-21 21:00:22,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:22,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:23,452 - INFO - Response - Page 4:
2025-06-21 21:00:23,655 - INFO - 第 4 页获取到 100 条记录
2025-06-21 21:00:23,655 - INFO - Request Parameters - Page 5:
2025-06-21 21:00:23,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:23,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:24,217 - INFO - Response - Page 5:
2025-06-21 21:00:24,421 - INFO - 第 5 页获取到 100 条记录
2025-06-21 21:00:24,421 - INFO - Request Parameters - Page 6:
2025-06-21 21:00:24,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:24,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:24,905 - INFO - Response - Page 6:
2025-06-21 21:00:25,108 - INFO - 第 6 页获取到 100 条记录
2025-06-21 21:00:25,108 - INFO - Request Parameters - Page 7:
2025-06-21 21:00:25,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:25,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:25,530 - INFO - Response - Page 7:
2025-06-21 21:00:25,733 - INFO - 第 7 页获取到 56 条记录
2025-06-21 21:00:25,733 - INFO - 查询完成，共获取到 656 条记录
2025-06-21 21:00:25,733 - INFO - 获取到 656 条表单数据
2025-06-21 21:00:25,733 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-21 21:00:25,749 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:25,749 - INFO - 开始处理日期: 2025-05
2025-06-21 21:00:25,749 - INFO - Request Parameters - Page 1:
2025-06-21 21:00:25,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:25,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:26,249 - INFO - Response - Page 1:
2025-06-21 21:00:26,452 - INFO - 第 1 页获取到 100 条记录
2025-06-21 21:00:26,452 - INFO - Request Parameters - Page 2:
2025-06-21 21:00:26,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:26,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:26,921 - INFO - Response - Page 2:
2025-06-21 21:00:27,124 - INFO - 第 2 页获取到 100 条记录
2025-06-21 21:00:27,124 - INFO - Request Parameters - Page 3:
2025-06-21 21:00:27,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:27,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:27,593 - INFO - Response - Page 3:
2025-06-21 21:00:27,796 - INFO - 第 3 页获取到 100 条记录
2025-06-21 21:00:27,796 - INFO - Request Parameters - Page 4:
2025-06-21 21:00:27,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:27,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:28,234 - INFO - Response - Page 4:
2025-06-21 21:00:28,437 - INFO - 第 4 页获取到 100 条记录
2025-06-21 21:00:28,437 - INFO - Request Parameters - Page 5:
2025-06-21 21:00:28,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:28,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:28,921 - INFO - Response - Page 5:
2025-06-21 21:00:29,125 - INFO - 第 5 页获取到 100 条记录
2025-06-21 21:00:29,125 - INFO - Request Parameters - Page 6:
2025-06-21 21:00:29,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:29,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:29,593 - INFO - Response - Page 6:
2025-06-21 21:00:29,797 - INFO - 第 6 页获取到 100 条记录
2025-06-21 21:00:29,797 - INFO - Request Parameters - Page 7:
2025-06-21 21:00:29,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:29,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:30,281 - INFO - Response - Page 7:
2025-06-21 21:00:30,484 - INFO - 第 7 页获取到 65 条记录
2025-06-21 21:00:30,484 - INFO - 查询完成，共获取到 665 条记录
2025-06-21 21:00:30,484 - INFO - 获取到 665 条表单数据
2025-06-21 21:00:30,484 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-21 21:00:30,500 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:30,500 - INFO - 开始处理日期: 2025-06
2025-06-21 21:00:30,500 - INFO - Request Parameters - Page 1:
2025-06-21 21:00:30,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:30,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:30,969 - INFO - Response - Page 1:
2025-06-21 21:00:31,172 - INFO - 第 1 页获取到 100 条记录
2025-06-21 21:00:31,172 - INFO - Request Parameters - Page 2:
2025-06-21 21:00:31,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:31,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:31,688 - INFO - Response - Page 2:
2025-06-21 21:00:31,891 - INFO - 第 2 页获取到 100 条记录
2025-06-21 21:00:31,891 - INFO - Request Parameters - Page 3:
2025-06-21 21:00:31,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:31,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:32,406 - INFO - Response - Page 3:
2025-06-21 21:00:32,610 - INFO - 第 3 页获取到 100 条记录
2025-06-21 21:00:32,610 - INFO - Request Parameters - Page 4:
2025-06-21 21:00:32,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:32,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:33,282 - INFO - Response - Page 4:
2025-06-21 21:00:33,485 - INFO - 第 4 页获取到 100 条记录
2025-06-21 21:00:33,485 - INFO - Request Parameters - Page 5:
2025-06-21 21:00:33,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:33,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:33,985 - INFO - Response - Page 5:
2025-06-21 21:00:34,188 - INFO - 第 5 页获取到 100 条记录
2025-06-21 21:00:34,188 - INFO - Request Parameters - Page 6:
2025-06-21 21:00:34,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:34,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:34,672 - INFO - Response - Page 6:
2025-06-21 21:00:34,876 - INFO - 第 6 页获取到 100 条记录
2025-06-21 21:00:34,876 - INFO - Request Parameters - Page 7:
2025-06-21 21:00:34,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-21 21:00:34,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-21 21:00:35,219 - INFO - Response - Page 7:
2025-06-21 21:00:35,423 - INFO - 第 7 页获取到 23 条记录
2025-06-21 21:00:35,423 - INFO - 查询完成，共获取到 623 条记录
2025-06-21 21:00:35,423 - INFO - 获取到 623 条表单数据
2025-06-21 21:00:35,423 - INFO - 当前日期 2025-06 有 623 条MySQL数据需要处理
2025-06-21 21:00:35,423 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-21 21:00:35,845 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-21 21:00:35,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86760.0, 'new_value': 113560.0}, {'field': 'total_amount', 'old_value': 86760.0, 'new_value': 113560.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-06-21 21:00:35,860 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:35,860 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-21 21:00:35,860 - INFO - =================同步完成====================
