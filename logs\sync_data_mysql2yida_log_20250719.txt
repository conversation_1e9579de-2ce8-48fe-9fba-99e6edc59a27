2025-07-19 01:30:34,045 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 01:30:34,046 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 01:30:34,047 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 01:30:34,136 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 0 条记录
2025-07-19 01:30:34,136 - ERROR - 未获取到MySQL数据
2025-07-19 01:31:34,136 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 01:31:34,136 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 01:31:34,136 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 01:31:34,282 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 58 条记录
2025-07-19 01:31:34,283 - INFO - 获取到 1 个日期需要处理: ['2025-07-18']
2025-07-19 01:31:34,283 - INFO - 开始处理日期: 2025-07-18
2025-07-19 01:31:34,286 - INFO - Request Parameters - Page 1:
2025-07-19 01:31:34,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 01:31:34,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 01:31:41,821 - INFO - Response - Page 1:
2025-07-19 01:31:41,822 - INFO - 第 1 页获取到 49 条记录
2025-07-19 01:31:42,323 - INFO - 查询完成，共获取到 49 条记录
2025-07-19 01:31:42,323 - INFO - 获取到 49 条表单数据
2025-07-19 01:31:42,324 - INFO - 当前日期 2025-07-18 有 58 条MySQL数据需要处理
2025-07-19 01:31:42,326 - INFO - 开始批量插入 9 条新记录
2025-07-19 01:31:42,489 - INFO - 批量插入响应状态码: 200
2025-07-19 01:31:42,490 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 18 Jul 2025 17:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '962BE7F6-A59B-7601-8D95-E784AC70A0D2', 'x-acs-trace-id': 'ee46d6fd9977fac2e72e189287b60886', 'etag': '4rOG08XSEerN+9zKVTX6t0w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 01:31:42,490 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMIB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMJB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMKB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMLB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMMB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMNB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMOB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMPB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMQB']}
2025-07-19 01:31:42,490 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-07-19 01:31:42,490 - INFO - 成功插入的数据ID: ['FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMIB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMJB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMKB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMLB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMMB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMNB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMOB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMPB', 'FINST-3PF66271S49XXBHE8A8QQ6SCOJG72SK0J39DMQB']
2025-07-19 01:31:47,491 - INFO - 批量插入完成，共 9 条记录
2025-07-19 01:31:47,491 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-07-19 01:31:47,491 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-07-19 01:31:47,491 - INFO - 同步完成
2025-07-19 04:30:34,004 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 04:30:34,004 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 04:30:34,004 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 04:30:34,151 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 10 条记录
2025-07-19 04:30:34,152 - INFO - 获取到 1 个日期需要处理: ['2025-07-18']
2025-07-19 04:30:34,152 - INFO - 开始处理日期: 2025-07-18
2025-07-19 04:30:34,154 - INFO - Request Parameters - Page 1:
2025-07-19 04:30:34,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 04:30:34,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 04:30:42,286 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 13A46E31-09DA-73A3-8D22-7DD5E8ED7004 Response: {'code': 'ServiceUnavailable', 'requestid': '13A46E31-09DA-73A3-8D22-7DD5E8ED7004', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 13A46E31-09DA-73A3-8D22-7DD5E8ED7004)
2025-07-19 04:30:42,286 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-19 04:31:42,286 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 04:31:42,286 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 04:31:42,286 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 04:31:42,435 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 119 条记录
2025-07-19 04:31:42,435 - INFO - 获取到 1 个日期需要处理: ['2025-07-18']
2025-07-19 04:31:42,437 - INFO - 开始处理日期: 2025-07-18
2025-07-19 04:31:42,437 - INFO - Request Parameters - Page 1:
2025-07-19 04:31:42,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 04:31:42,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 04:31:43,274 - INFO - Response - Page 1:
2025-07-19 04:31:43,275 - INFO - 第 1 页获取到 50 条记录
2025-07-19 04:31:43,776 - INFO - Request Parameters - Page 2:
2025-07-19 04:31:43,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 04:31:43,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 04:31:44,340 - INFO - Response - Page 2:
2025-07-19 04:31:44,341 - INFO - 第 2 页获取到 8 条记录
2025-07-19 04:31:44,842 - INFO - 查询完成，共获取到 58 条记录
2025-07-19 04:31:44,842 - INFO - 获取到 58 条表单数据
2025-07-19 04:31:44,843 - INFO - 当前日期 2025-07-18 有 115 条MySQL数据需要处理
2025-07-19 04:31:44,845 - INFO - 开始批量插入 57 条新记录
2025-07-19 04:31:45,093 - INFO - 批量插入响应状态码: 200
2025-07-19 04:31:45,093 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 18 Jul 2025 20:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6BC1A83D-81B1-7C75-9161-1A22DF117E7E', 'x-acs-trace-id': 'cf14cdbc86186d4b86752a62ecff7669', 'etag': '2GRQMmCozW3MmQZov9h/9Zw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 04:31:45,093 - INFO - 批量插入响应体: {'result': ['FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM99', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMA9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMB9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMC9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMD9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DME9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMF9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMG9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMH9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMI9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMJ9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMK9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DML9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMM9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMN9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMO9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMP9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMQ9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMR9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMS9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMT9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMU9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMV9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMW9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMX9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMY9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMZ9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM0A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM1A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM2A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM3A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM4A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM5A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM6A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM7A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM8A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM9A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMAA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMBA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMCA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMDA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMEA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMFA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMGA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMHA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMIA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMJA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMKA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMLA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMMA']}
2025-07-19 04:31:45,093 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-19 04:31:45,093 - INFO - 成功插入的数据ID: ['FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM99', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMA9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMB9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMC9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMD9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DME9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMF9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMG9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMH9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMI9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMJ9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMK9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DML9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMM9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMN9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMO9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMP9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMQ9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMR9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMS9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMT9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMU9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMV9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMW9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMX9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMY9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMZ9', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM0A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM1A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM2A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM3A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM4A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM5A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM6A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM7A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM8A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DM9A', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMAA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMBA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMCA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMDA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMEA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMFA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMGA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMHA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMIA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMJA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMKA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMLA', 'FINST-OPC666D1R97XBOCJF7JR4DKP63N82WYJY99DMMA']
2025-07-19 04:31:50,253 - INFO - 批量插入响应状态码: 200
2025-07-19 04:31:50,253 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 18 Jul 2025 20:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D8439CAF-03A5-78CD-B740-35EF9FE3C7B0', 'x-acs-trace-id': '90f896f4233e18fae5113ab31ec9cd2f', 'etag': '3cNGCJkFe6Os2jYLdt2uW0w8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 04:31:50,253 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMB6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMC6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMD6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DME6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMF6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMG6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMH6']}
2025-07-19 04:31:50,253 - INFO - 批量插入表单数据成功，批次 2，共 7 条记录
2025-07-19 04:31:50,253 - INFO - 成功插入的数据ID: ['FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMB6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMC6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMD6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DME6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMF6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMG6', 'FINST-K7666JC1P49XEFFW9KDBK9NO9SX93BYNY99DMH6']
2025-07-19 04:31:55,254 - INFO - 批量插入完成，共 57 条记录
2025-07-19 04:31:55,254 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 57 条，错误: 0 条
2025-07-19 04:31:55,254 - INFO - 数据同步完成！更新: 0 条，插入: 57 条，错误: 0 条
2025-07-19 04:31:55,254 - INFO - 同步完成
2025-07-19 07:30:34,145 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 07:30:34,145 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 07:30:34,145 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 07:30:34,295 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 18 条记录
2025-07-19 07:30:34,295 - INFO - 获取到 1 个日期需要处理: ['2025-07-18']
2025-07-19 07:30:34,295 - INFO - 开始处理日期: 2025-07-18
2025-07-19 07:30:34,298 - INFO - Request Parameters - Page 1:
2025-07-19 07:30:34,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 07:30:34,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 07:30:42,424 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 023E9519-119C-77C4-9626-FCF980774A64 Response: {'code': 'ServiceUnavailable', 'requestid': '023E9519-119C-77C4-9626-FCF980774A64', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 023E9519-119C-77C4-9626-FCF980774A64)
2025-07-19 07:30:42,425 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-19 07:31:42,426 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 07:31:42,426 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 07:31:42,426 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 07:31:42,574 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 129 条记录
2025-07-19 07:31:42,575 - INFO - 获取到 1 个日期需要处理: ['2025-07-18']
2025-07-19 07:31:42,576 - INFO - 开始处理日期: 2025-07-18
2025-07-19 07:31:42,576 - INFO - Request Parameters - Page 1:
2025-07-19 07:31:42,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 07:31:42,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 07:31:50,086 - INFO - Response - Page 1:
2025-07-19 07:31:50,087 - INFO - 第 1 页获取到 50 条记录
2025-07-19 07:31:50,588 - INFO - Request Parameters - Page 2:
2025-07-19 07:31:50,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 07:31:50,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 07:31:51,334 - INFO - Response - Page 2:
2025-07-19 07:31:51,334 - INFO - 第 2 页获取到 50 条记录
2025-07-19 07:31:51,834 - INFO - Request Parameters - Page 3:
2025-07-19 07:31:51,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 07:31:51,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 07:31:52,376 - INFO - Response - Page 3:
2025-07-19 07:31:52,376 - INFO - 第 3 页获取到 15 条记录
2025-07-19 07:31:52,877 - INFO - 查询完成，共获取到 115 条记录
2025-07-19 07:31:52,877 - INFO - 获取到 115 条表单数据
2025-07-19 07:31:52,879 - INFO - 当前日期 2025-07-18 有 124 条MySQL数据需要处理
2025-07-19 07:31:52,882 - INFO - 开始批量插入 9 条新记录
2025-07-19 07:31:53,053 - INFO - 批量插入响应状态码: 200
2025-07-19 07:31:53,053 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 18 Jul 2025 23:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5869014B-859B-7D2E-88AA-B64C3427AFA6', 'x-acs-trace-id': '08c85dc722dc41c0df84ade9d648c53d', 'etag': '4Q+yByK6tRIOQK8zgWvNbnQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 07:31:53,053 - INFO - 批量插入响应体: {'result': ['FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DMZ7', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM08', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM18', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM28', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM38', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM48', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM58', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM68', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM78']}
2025-07-19 07:31:53,053 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-07-19 07:31:53,053 - INFO - 成功插入的数据ID: ['FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DMZ7', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM08', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM18', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM28', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM38', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM48', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM58', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM68', 'FINST-6PF66691J07XRTKG94QB25IPNJMO31I7EG9DM78']
2025-07-19 07:31:58,054 - INFO - 批量插入完成，共 9 条记录
2025-07-19 07:31:58,054 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-07-19 07:31:58,054 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-07-19 07:31:58,054 - INFO - 同步完成
2025-07-19 10:30:33,825 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 10:30:33,825 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 10:30:33,825 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 10:30:33,978 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 101 条记录
2025-07-19 10:30:33,978 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-19 10:30:33,979 - INFO - 开始处理日期: 2025-07-18
2025-07-19 10:30:33,983 - INFO - Request Parameters - Page 1:
2025-07-19 10:30:33,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 10:30:33,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 10:30:42,117 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F8FB96B1-38F1-70FC-85E7-C5E98B079C6C Response: {'code': 'ServiceUnavailable', 'requestid': 'F8FB96B1-38F1-70FC-85E7-C5E98B079C6C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F8FB96B1-38F1-70FC-85E7-C5E98B079C6C)
2025-07-19 10:30:42,118 - INFO - 开始处理日期: 2025-07-19
2025-07-19 10:30:42,118 - INFO - Request Parameters - Page 1:
2025-07-19 10:30:42,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 10:30:42,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 10:30:50,229 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CD770D65-EE31-72F7-955D-41C77A28EA13 Response: {'code': 'ServiceUnavailable', 'requestid': 'CD770D65-EE31-72F7-955D-41C77A28EA13', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CD770D65-EE31-72F7-955D-41C77A28EA13)
2025-07-19 10:30:50,230 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-19 10:31:50,231 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 10:31:50,231 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 10:31:50,231 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 10:31:50,394 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 398 条记录
2025-07-19 10:31:50,394 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-19 10:31:50,398 - INFO - 开始处理日期: 2025-07-18
2025-07-19 10:31:50,398 - INFO - Request Parameters - Page 1:
2025-07-19 10:31:50,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 10:31:50,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 10:31:51,272 - INFO - Response - Page 1:
2025-07-19 10:31:51,273 - INFO - 第 1 页获取到 50 条记录
2025-07-19 10:31:51,773 - INFO - Request Parameters - Page 2:
2025-07-19 10:31:51,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 10:31:51,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 10:31:59,151 - INFO - Response - Page 2:
2025-07-19 10:31:59,151 - INFO - 第 2 页获取到 50 条记录
2025-07-19 10:31:59,652 - INFO - Request Parameters - Page 3:
2025-07-19 10:31:59,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 10:31:59,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 10:32:00,317 - INFO - Response - Page 3:
2025-07-19 10:32:00,317 - INFO - 第 3 页获取到 24 条记录
2025-07-19 10:32:00,818 - INFO - 查询完成，共获取到 124 条记录
2025-07-19 10:32:00,818 - INFO - 获取到 124 条表单数据
2025-07-19 10:32:00,820 - INFO - 当前日期 2025-07-18 有 382 条MySQL数据需要处理
2025-07-19 10:32:00,825 - INFO - 开始批量插入 258 条新记录
2025-07-19 10:32:01,052 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:01,052 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E1CEE7C4-86E6-7FBB-9A3F-1DF3493C18F2', 'x-acs-trace-id': '33ef5c33a3548ab891952bb1efc8866b', 'etag': '2vBQIXliw96i7E0KP0T6AWA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:01,053 - INFO - 批量插入响应体: {'result': ['FINST-B1D66U61398XBEW97GGVSCWO65YP2C2VTM9DMID', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMJD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMKD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMLD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMMD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMND', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMOD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMPD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMQD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMRD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMSD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMTD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMUD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMVD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMWD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMXD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMYD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMZD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM0E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM1E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM2E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM3E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM4E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM5E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM6E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM7E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM8E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM9E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMAE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMBE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMCE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMDE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMEE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMFE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMGE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMHE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMIE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMJE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMKE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMLE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMME', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMNE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMOE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMPE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMQE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMRE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMSE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMTE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMUE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMVE']}
2025-07-19 10:32:01,053 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-19 10:32:01,053 - INFO - 成功插入的数据ID: ['FINST-B1D66U61398XBEW97GGVSCWO65YP2C2VTM9DMID', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMJD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMKD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMLD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMMD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMND', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMOD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMPD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMQD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMRD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMSD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMTD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMUD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMVD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMWD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMXD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMYD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMZD', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM0E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM1E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM2E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM3E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM4E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM5E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM6E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM7E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM8E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DM9E', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMAE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMBE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMCE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMDE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMEE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMFE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMGE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMHE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMIE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMJE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMKE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMLE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMME', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMNE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMOE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMPE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMQE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMRE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMSE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMTE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMUE', 'FINST-B1D66U61398XBEW97GGVSCWO65YP2D2VTM9DMVE']
2025-07-19 10:32:06,317 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:06,317 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '13305149-718A-72BE-8B9B-C174D49DEA27', 'x-acs-trace-id': '37e20ea65f702750ced7b9895c4aedb0', 'etag': '2+6svBlXCGceGVNqiCywaOg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:06,317 - INFO - 批量插入响应体: {'result': ['FINST-HXD667B1J97XJB4BBSGCRCXRU0F82F4ZTM9DMTD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMUD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMVD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMWD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMXD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMYD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMZD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM0E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM1E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM2E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM3E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM4E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM5E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM6E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM7E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM8E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM9E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMAE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMBE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMCE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMDE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMEE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMFE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMGE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMHE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMIE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMJE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMKE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMLE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMME', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMNE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMOE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMPE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMQE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMRE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMSE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMTE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMUE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMVE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMWE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMXE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMYE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMZE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM0F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM1F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM2F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM3F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM4F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM5F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM6F']}
2025-07-19 10:32:06,317 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-19 10:32:06,317 - INFO - 成功插入的数据ID: ['FINST-HXD667B1J97XJB4BBSGCRCXRU0F82F4ZTM9DMTD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMUD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMVD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMWD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMXD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMYD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMZD', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM0E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM1E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM2E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM3E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM4E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM5E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM6E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM7E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM8E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM9E', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMAE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMBE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMCE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMDE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMEE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMFE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMGE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMHE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMIE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMJE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMKE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMLE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMME', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMNE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMOE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMPE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMQE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMRE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMSE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMTE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMUE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMVE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMWE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMXE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMYE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DMZE', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM0F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM1F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM2F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM3F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM4F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM5F', 'FINST-HXD667B1J97XJB4BBSGCRCXRU0F82G4ZTM9DM6F']
2025-07-19 10:32:11,539 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:11,539 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '405D8DFB-D267-7B95-BF00-100A7A1EC550', 'x-acs-trace-id': '22a2325ccb860f1b443b2dadd1bf53d7', 'etag': '20R3lRYfWKE/sT8AVybcaUA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:11,540 - INFO - 批量插入响应体: {'result': ['FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM34', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM44', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM54', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM64', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM74', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM84', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM94', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMA4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMB4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMC4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMD4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DME4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMF4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMG4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMH4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMI4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMJ4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMK4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DML4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMM4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMN4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMO4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMP4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMQ4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMR4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMS4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMT4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMU4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMV4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMW4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMX4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMY4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMZ4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM05', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM15', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM25', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM35', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM45', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM55', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM65', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM75', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM85', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM95', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMA5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMB5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMC5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMD5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DME5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMF5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMG5']}
2025-07-19 10:32:11,540 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-19 10:32:11,540 - INFO - 成功插入的数据ID: ['FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM34', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM44', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM54', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM64', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM74', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM84', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DM94', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMA4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMB4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMC4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMD4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DME4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMF4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMG4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMH4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMI4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMJ4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DMK4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13M53UM9DML4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMM4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMN4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMO4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMP4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMQ4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMR4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMS4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMT4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMU4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMV4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMW4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMX4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMY4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMZ4', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM05', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM15', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM25', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM35', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM45', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM55', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM65', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM75', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM85', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DM95', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMA5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMB5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMC5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMD5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DME5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMF5', 'FINST-ACB66071788XY0DW70HF29L1TFJ13N53UM9DMG5']
2025-07-19 10:32:16,780 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:16,780 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A433C2BE-78D1-76C3-AAAD-E8F3E1F1BC80', 'x-acs-trace-id': '696ea85112bd0f3bfe8d923d073c666f', 'etag': '2mLXncPnjGq64wyWQeydqRQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:16,780 - INFO - 批量插入响应体: {'result': ['FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMUF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMVF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMWF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMXF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMYF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMZF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM0G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM1G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM2G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM3G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM4G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM5G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM6G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM7G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM8G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM9G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMAG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMBG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMCG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMDG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMEG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMFG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMGG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMHG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMIG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMJG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMKG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMLG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMMG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMNG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMOG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMPG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMQG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMRG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMSG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMTG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMUG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMVG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMWG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMXG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMYG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMZG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM0H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM1H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM2H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM3H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM4H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM5H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM6H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM7H']}
2025-07-19 10:32:16,780 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-19 10:32:16,781 - INFO - 成功插入的数据ID: ['FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMUF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMVF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMWF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMXF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMYF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMZF', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM0G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM1G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM2G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM3G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM4G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM5G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM6G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM7G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM8G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM9G', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMAG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMBG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMCG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMDG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMEG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMFG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMGG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMHG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMIG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMJG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMKG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMLG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMMG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMNG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMOG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMPG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMQG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMRG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMSG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMTG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMUG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMVG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMWG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMXG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMYG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DMZG', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM0H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM1H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM2H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM3H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM4H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM5H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM6H', 'FINST-LOG66Q61ZF9X73OYA48QY3OSBRQX2877UM9DM7H']
2025-07-19 10:32:22,024 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:22,024 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A9CFF591-16F1-765A-9C53-BD8A2E902501', 'x-acs-trace-id': '58c318ec976b551a672179b34748d742', 'etag': '2VdXZ1U2g1sAY0p0QQQZtMw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:22,024 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM74', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM84', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM94', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMA4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMB4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMC4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMD4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DME4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMF4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMG4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMH4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMI4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMJ4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMK4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DML4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMM4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMN4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMO4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMP4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMQ4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMR4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMS4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMT4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMU4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMV4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMW4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMX4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMY4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMZ4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM05', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM15', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM25', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM35', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM45', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM55', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM65', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM75', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM85', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM95', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMA5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMB5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMC5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMD5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DME5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMF5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMG5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMH5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMI5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMJ5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMK5']}
2025-07-19 10:32:22,024 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-19 10:32:22,024 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM74', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM84', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM94', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMA4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMB4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMC4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMD4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DME4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMF4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMG4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMH4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMI4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMJ4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMK4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DML4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMM4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMN4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMO4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMP4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMQ4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMR4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMS4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMT4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMU4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMV4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMW4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMX4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMY4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMZ4', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM05', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM15', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM25', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM35', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM45', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM55', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM65', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM75', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM85', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DM95', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMA5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMB5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMC5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMD5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DME5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMF5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMG5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMH5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMI5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMJ5', 'FINST-OIF66BA1F59XN7E0B7X73DJXN3OL3U8BUM9DMK5']
2025-07-19 10:32:27,174 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:27,174 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4A2D8E12-6D0B-73CA-8548-BC1603AEB998', 'x-acs-trace-id': 'e2a3d935d11a05eb959e4ee63a19c03f', 'etag': '3cGyJ9NRrgsXYRcn5PUgvCA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:27,175 - INFO - 批量插入响应体: {'result': ['FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM1C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM2C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM3C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM4C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM5C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM6C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM7C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM8C']}
2025-07-19 10:32:27,175 - INFO - 批量插入表单数据成功，批次 6，共 8 条记录
2025-07-19 10:32:27,175 - INFO - 成功插入的数据ID: ['FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM1C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM2C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM3C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM4C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM5C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM6C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM7C', 'FINST-6AG66W81759XDJF18GLLL6BETABB308FUM9DM8C']
2025-07-19 10:32:32,176 - INFO - 批量插入完成，共 258 条记录
2025-07-19 10:32:32,176 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 258 条，错误: 0 条
2025-07-19 10:32:32,176 - INFO - 开始处理日期: 2025-07-19
2025-07-19 10:32:32,176 - INFO - Request Parameters - Page 1:
2025-07-19 10:32:32,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 10:32:32,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 10:32:32,661 - INFO - Response - Page 1:
2025-07-19 10:32:32,661 - INFO - 查询完成，共获取到 0 条记录
2025-07-19 10:32:32,661 - INFO - 获取到 0 条表单数据
2025-07-19 10:32:32,661 - INFO - 当前日期 2025-07-19 有 1 条MySQL数据需要处理
2025-07-19 10:32:32,662 - INFO - 开始批量插入 1 条新记录
2025-07-19 10:32:32,801 - INFO - 批量插入响应状态码: 200
2025-07-19 10:32:32,801 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 02:32:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5384B8EB-89B1-71AB-B79E-D780AC380AC4', 'x-acs-trace-id': '6d5f3bab43ceabfd7b189206cc490e76', 'etag': '6SUIU3VHNqQbwa/mhOp96Lg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 10:32:32,801 - INFO - 批量插入响应体: {'result': ['FINST-X2F66HC1L97XU19A8P4NA92IW0CJ39KJUM9DMMC']}
2025-07-19 10:32:32,801 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-19 10:32:32,801 - INFO - 成功插入的数据ID: ['FINST-X2F66HC1L97XU19A8P4NA92IW0CJ39KJUM9DMMC']
2025-07-19 10:32:37,802 - INFO - 批量插入完成，共 1 条记录
2025-07-19 10:32:37,802 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-19 10:32:37,802 - INFO - 数据同步完成！更新: 0 条，插入: 259 条，错误: 0 条
2025-07-19 10:32:37,802 - INFO - 同步完成
2025-07-19 13:30:34,019 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 13:30:34,020 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 13:30:34,020 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 13:30:34,175 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 132 条记录
2025-07-19 13:30:34,175 - INFO - 获取到 3 个日期需要处理: ['2025-07-17', '2025-07-18', '2025-07-19']
2025-07-19 13:30:34,176 - INFO - 开始处理日期: 2025-07-17
2025-07-19 13:30:34,179 - INFO - Request Parameters - Page 1:
2025-07-19 13:30:34,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:34,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:41,995 - INFO - Response - Page 1:
2025-07-19 13:30:41,996 - INFO - 第 1 页获取到 50 条记录
2025-07-19 13:30:42,497 - INFO - Request Parameters - Page 2:
2025-07-19 13:30:42,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:42,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:50,625 - ERROR - 处理日期 2025-07-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9E42E560-E183-766E-AED7-B6FA56198D6F Response: {'code': 'ServiceUnavailable', 'requestid': '9E42E560-E183-766E-AED7-B6FA56198D6F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9E42E560-E183-766E-AED7-B6FA56198D6F)
2025-07-19 13:30:50,625 - INFO - 开始处理日期: 2025-07-18
2025-07-19 13:30:50,625 - INFO - Request Parameters - Page 1:
2025-07-19 13:30:50,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:50,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:54,978 - INFO - Response - Page 1:
2025-07-19 13:30:54,978 - INFO - 第 1 页获取到 50 条记录
2025-07-19 13:30:55,479 - INFO - Request Parameters - Page 2:
2025-07-19 13:30:55,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:55,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:56,194 - INFO - Response - Page 2:
2025-07-19 13:30:56,195 - INFO - 第 2 页获取到 50 条记录
2025-07-19 13:30:56,696 - INFO - Request Parameters - Page 3:
2025-07-19 13:30:56,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:56,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:57,427 - INFO - Response - Page 3:
2025-07-19 13:30:57,428 - INFO - 第 3 页获取到 50 条记录
2025-07-19 13:30:57,929 - INFO - Request Parameters - Page 4:
2025-07-19 13:30:57,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:57,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:58,647 - INFO - Response - Page 4:
2025-07-19 13:30:58,647 - INFO - 第 4 页获取到 50 条记录
2025-07-19 13:30:59,149 - INFO - Request Parameters - Page 5:
2025-07-19 13:30:59,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:30:59,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:30:59,886 - INFO - Response - Page 5:
2025-07-19 13:30:59,886 - INFO - 第 5 页获取到 50 条记录
2025-07-19 13:31:00,387 - INFO - Request Parameters - Page 6:
2025-07-19 13:31:00,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:31:00,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:31:01,228 - INFO - Response - Page 6:
2025-07-19 13:31:01,229 - INFO - 第 6 页获取到 50 条记录
2025-07-19 13:31:01,730 - INFO - Request Parameters - Page 7:
2025-07-19 13:31:01,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:31:01,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:31:02,447 - INFO - Response - Page 7:
2025-07-19 13:31:02,448 - INFO - 第 7 页获取到 50 条记录
2025-07-19 13:31:02,948 - INFO - Request Parameters - Page 8:
2025-07-19 13:31:02,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:31:02,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:31:03,615 - INFO - Response - Page 8:
2025-07-19 13:31:03,615 - INFO - 第 8 页获取到 32 条记录
2025-07-19 13:31:04,116 - INFO - 查询完成，共获取到 382 条记录
2025-07-19 13:31:04,116 - INFO - 获取到 382 条表单数据
2025-07-19 13:31:04,123 - INFO - 当前日期 2025-07-18 有 124 条MySQL数据需要处理
2025-07-19 13:31:04,126 - INFO - 开始批量插入 29 条新记录
2025-07-19 13:31:04,380 - INFO - 批量插入响应状态码: 200
2025-07-19 13:31:04,381 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 05:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1404', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F0F143D5-F49E-72C1-8478-5AA0FA8AE3F6', 'x-acs-trace-id': '6072fc158babf97a4e59e7e58a735380', 'etag': '1gdKe4xddJNfAAmO+xoTrWA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 13:31:04,381 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM01', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM11', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM21', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM31', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM41', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM51', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM61', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM71', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM81', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM91', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMA1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMB1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMC1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMD1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DME1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMF1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMG1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMH1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMI1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMJ1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMK1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DML1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMM1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMN1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMO1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMP1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMQ1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMR1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMS1']}
2025-07-19 13:31:04,381 - INFO - 批量插入表单数据成功，批次 1，共 29 条记录
2025-07-19 13:31:04,381 - INFO - 成功插入的数据ID: ['FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM01', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM11', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM21', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM31', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM41', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM51', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM61', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM71', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM81', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DM91', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMA1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMB1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMC1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMD1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DME1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMF1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMG1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMH1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMI1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMJ1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMK1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DML1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMM1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMN1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMO1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMP1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMQ1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMR1', 'FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMS1']
2025-07-19 13:31:09,382 - INFO - 批量插入完成，共 29 条记录
2025-07-19 13:31:09,382 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 29 条，错误: 0 条
2025-07-19 13:31:09,382 - INFO - 开始处理日期: 2025-07-19
2025-07-19 13:31:09,382 - INFO - Request Parameters - Page 1:
2025-07-19 13:31:09,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:31:09,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:31:09,862 - INFO - Response - Page 1:
2025-07-19 13:31:09,862 - INFO - 第 1 页获取到 1 条记录
2025-07-19 13:31:10,362 - INFO - 查询完成，共获取到 1 条记录
2025-07-19 13:31:10,362 - INFO - 获取到 1 条表单数据
2025-07-19 13:31:10,362 - INFO - 当前日期 2025-07-19 有 2 条MySQL数据需要处理
2025-07-19 13:31:10,363 - INFO - 开始批量插入 1 条新记录
2025-07-19 13:31:10,543 - INFO - 批量插入响应状态码: 200
2025-07-19 13:31:10,543 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 05:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F7CD4A06-45F6-7EB0-BEC7-A905ED389551', 'x-acs-trace-id': 'ce497410e4c7b620027d7326c43f34bb', 'etag': '6WsaYlRNksLgW8bm5s9AkeA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 13:31:10,543 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q81GA9X8SQT97EQCCB4MKBR2YG98T9DMOB']}
2025-07-19 13:31:10,543 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-19 13:31:10,543 - INFO - 成功插入的数据ID: ['FINST-MUC66Q81GA9X8SQT97EQCCB4MKBR2YG98T9DMOB']
2025-07-19 13:31:15,544 - INFO - 批量插入完成，共 1 条记录
2025-07-19 13:31:15,544 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-19 13:31:15,544 - INFO - 数据同步完成！更新: 0 条，插入: 30 条，错误: 1 条
2025-07-19 13:32:15,545 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 13:32:15,545 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 13:32:15,545 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 13:32:15,706 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 443 条记录
2025-07-19 13:32:15,706 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-19 13:32:15,710 - INFO - 开始处理日期: 2025-07-18
2025-07-19 13:32:15,711 - INFO - Request Parameters - Page 1:
2025-07-19 13:32:15,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:15,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:16,463 - INFO - Response - Page 1:
2025-07-19 13:32:16,464 - INFO - 第 1 页获取到 50 条记录
2025-07-19 13:32:16,965 - INFO - Request Parameters - Page 2:
2025-07-19 13:32:16,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:16,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:17,783 - INFO - Response - Page 2:
2025-07-19 13:32:17,783 - INFO - 第 2 页获取到 50 条记录
2025-07-19 13:32:18,284 - INFO - Request Parameters - Page 3:
2025-07-19 13:32:18,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:18,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:18,965 - INFO - Response - Page 3:
2025-07-19 13:32:18,965 - INFO - 第 3 页获取到 50 条记录
2025-07-19 13:32:19,466 - INFO - Request Parameters - Page 4:
2025-07-19 13:32:19,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:19,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:20,222 - INFO - Response - Page 4:
2025-07-19 13:32:20,222 - INFO - 第 4 页获取到 50 条记录
2025-07-19 13:32:20,722 - INFO - Request Parameters - Page 5:
2025-07-19 13:32:20,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:20,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:21,491 - INFO - Response - Page 5:
2025-07-19 13:32:21,491 - INFO - 第 5 页获取到 50 条记录
2025-07-19 13:32:21,992 - INFO - Request Parameters - Page 6:
2025-07-19 13:32:21,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:21,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:22,723 - INFO - Response - Page 6:
2025-07-19 13:32:22,723 - INFO - 第 6 页获取到 50 条记录
2025-07-19 13:32:23,223 - INFO - Request Parameters - Page 7:
2025-07-19 13:32:23,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:23,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:23,921 - INFO - Response - Page 7:
2025-07-19 13:32:23,921 - INFO - 第 7 页获取到 50 条记录
2025-07-19 13:32:24,422 - INFO - Request Parameters - Page 8:
2025-07-19 13:32:24,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:24,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:25,112 - INFO - Response - Page 8:
2025-07-19 13:32:25,112 - INFO - 第 8 页获取到 50 条记录
2025-07-19 13:32:25,613 - INFO - Request Parameters - Page 9:
2025-07-19 13:32:25,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:25,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:26,236 - INFO - Response - Page 9:
2025-07-19 13:32:26,237 - INFO - 第 9 页获取到 11 条记录
2025-07-19 13:32:26,738 - INFO - 查询完成，共获取到 411 条记录
2025-07-19 13:32:26,738 - INFO - 获取到 411 条表单数据
2025-07-19 13:32:26,746 - INFO - 当前日期 2025-07-18 有 426 条MySQL数据需要处理
2025-07-19 13:32:26,756 - INFO - 开始批量插入 15 条新记录
2025-07-19 13:32:26,959 - INFO - 批量插入响应状态码: 200
2025-07-19 13:32:26,960 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 05:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-12AF-7883-8B50-83E18F5CB3AB', 'x-acs-trace-id': '0a39c2299df26483de7d0f755f810ebc', 'etag': '7Sboz5e5j3gGwwxJNB2TjZg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 13:32:26,960 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMKT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMLT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMMT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMNT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMOT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMPT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMQT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMRT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMST', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMTT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMUT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMVT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMWT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMXT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMYT']}
2025-07-19 13:32:26,960 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-07-19 13:32:26,960 - INFO - 成功插入的数据ID: ['FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMKT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMLT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMMT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMNT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMOT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMPT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMQT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMRT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMST', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMTT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMUT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMVT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMWT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMXT', 'FINST-8PF66V71UA8X477MANKQFAJ8GZEF2TFW9T9DMYT']
2025-07-19 13:32:31,961 - INFO - 批量插入完成，共 15 条记录
2025-07-19 13:32:31,961 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 15 条，错误: 0 条
2025-07-19 13:32:31,961 - INFO - 开始处理日期: 2025-07-19
2025-07-19 13:32:31,961 - INFO - Request Parameters - Page 1:
2025-07-19 13:32:31,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 13:32:31,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 13:32:32,458 - INFO - Response - Page 1:
2025-07-19 13:32:32,458 - INFO - 第 1 页获取到 2 条记录
2025-07-19 13:32:32,959 - INFO - 查询完成，共获取到 2 条记录
2025-07-19 13:32:32,959 - INFO - 获取到 2 条表单数据
2025-07-19 13:32:32,960 - INFO - 当前日期 2025-07-19 有 2 条MySQL数据需要处理
2025-07-19 13:32:32,960 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 13:32:32,960 - INFO - 数据同步完成！更新: 0 条，插入: 15 条，错误: 0 条
2025-07-19 13:32:32,960 - INFO - 同步完成
2025-07-19 16:30:33,520 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 16:30:33,520 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 16:30:33,520 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 16:30:33,675 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 147 条记录
2025-07-19 16:30:33,676 - INFO - 获取到 3 个日期需要处理: ['2025-07-17', '2025-07-18', '2025-07-19']
2025-07-19 16:30:33,677 - INFO - 开始处理日期: 2025-07-17
2025-07-19 16:30:33,682 - INFO - Request Parameters - Page 1:
2025-07-19 16:30:33,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:33,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:41,480 - INFO - Response - Page 1:
2025-07-19 16:30:41,480 - INFO - 第 1 页获取到 50 条记录
2025-07-19 16:30:41,980 - INFO - Request Parameters - Page 2:
2025-07-19 16:30:41,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:41,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:50,097 - ERROR - 处理日期 2025-07-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FA7E3FD1-0AEE-7E32-B0AD-453A30524E2D Response: {'code': 'ServiceUnavailable', 'requestid': 'FA7E3FD1-0AEE-7E32-B0AD-453A30524E2D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FA7E3FD1-0AEE-7E32-B0AD-453A30524E2D)
2025-07-19 16:30:50,098 - INFO - 开始处理日期: 2025-07-18
2025-07-19 16:30:50,098 - INFO - Request Parameters - Page 1:
2025-07-19 16:30:50,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:50,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:50,789 - INFO - Response - Page 1:
2025-07-19 16:30:50,789 - INFO - 第 1 页获取到 50 条记录
2025-07-19 16:30:51,289 - INFO - Request Parameters - Page 2:
2025-07-19 16:30:51,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:51,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:54,438 - INFO - Response - Page 2:
2025-07-19 16:30:54,439 - INFO - 第 2 页获取到 50 条记录
2025-07-19 16:30:54,939 - INFO - Request Parameters - Page 3:
2025-07-19 16:30:54,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:54,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:55,656 - INFO - Response - Page 3:
2025-07-19 16:30:55,656 - INFO - 第 3 页获取到 50 条记录
2025-07-19 16:30:56,157 - INFO - Request Parameters - Page 4:
2025-07-19 16:30:56,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:56,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:56,935 - INFO - Response - Page 4:
2025-07-19 16:30:56,935 - INFO - 第 4 页获取到 50 条记录
2025-07-19 16:30:57,435 - INFO - Request Parameters - Page 5:
2025-07-19 16:30:57,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:57,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:58,141 - INFO - Response - Page 5:
2025-07-19 16:30:58,141 - INFO - 第 5 页获取到 50 条记录
2025-07-19 16:30:58,642 - INFO - Request Parameters - Page 6:
2025-07-19 16:30:58,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:58,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:30:59,361 - INFO - Response - Page 6:
2025-07-19 16:30:59,361 - INFO - 第 6 页获取到 50 条记录
2025-07-19 16:30:59,862 - INFO - Request Parameters - Page 7:
2025-07-19 16:30:59,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:30:59,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:31:00,548 - INFO - Response - Page 7:
2025-07-19 16:31:00,548 - INFO - 第 7 页获取到 50 条记录
2025-07-19 16:31:01,048 - INFO - Request Parameters - Page 8:
2025-07-19 16:31:01,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:31:01,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:31:01,742 - INFO - Response - Page 8:
2025-07-19 16:31:01,742 - INFO - 第 8 页获取到 50 条记录
2025-07-19 16:31:02,243 - INFO - Request Parameters - Page 9:
2025-07-19 16:31:02,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:31:02,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:31:02,870 - INFO - Response - Page 9:
2025-07-19 16:31:02,870 - INFO - 第 9 页获取到 26 条记录
2025-07-19 16:31:03,371 - INFO - 查询完成，共获取到 426 条记录
2025-07-19 16:31:03,371 - INFO - 获取到 426 条表单数据
2025-07-19 16:31:03,379 - INFO - 当前日期 2025-07-18 有 138 条MySQL数据需要处理
2025-07-19 16:31:03,383 - INFO - 开始更新记录 - 表单实例ID: FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMI1
2025-07-19 16:31:04,008 - INFO - 更新表单数据成功: FINST-QUA66S7124AXIZR37JQ4068ENWM62ZP48T9DMI1
2025-07-19 16:31:04,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16990.0, 'new_value': 12988.9}, {'field': 'total_amount', 'old_value': 16990.0, 'new_value': 12988.9}, {'field': 'order_count', 'old_value': 46, 'new_value': 14}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/aa6bba7553204a648ae089b21f52c6d0.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=LiKhANVYgmEkOyga32DhjDvuQMY%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/3fc472fb4a824ced8d6fbcc6f7af43a3.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Tiw7PSMbNnuEy46FONOmkqVucXc%3D'}]
2025-07-19 16:31:04,009 - INFO - 开始批量插入 14 条新记录
2025-07-19 16:31:04,191 - INFO - 批量插入响应状态码: 200
2025-07-19 16:31:04,192 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 08:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5FD30210-B540-7D58-8731-3B3A8E47D714', 'x-acs-trace-id': '9592177b12a83ab97220e048b72502f4', 'etag': '6fKJYPnKx+zLYq3WrmQaQAw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 16:31:04,192 - INFO - 批量插入响应体: {'result': ['FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMTB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMUB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMVB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMWB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMXB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMYB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMZB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM0C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM1C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM2C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM3C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM4C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM5C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM6C']}
2025-07-19 16:31:04,192 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-07-19 16:31:04,192 - INFO - 成功插入的数据ID: ['FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMTB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMUB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMVB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMWB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMXB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMYB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DMZB', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM0C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM1C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM2C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM3C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM4C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM5C', 'FINST-3ME66E81O98XWMG5FWWKF5EPEM0T3QYLNZ9DM6C']
2025-07-19 16:31:09,193 - INFO - 批量插入完成，共 14 条记录
2025-07-19 16:31:09,193 - INFO - 日期 2025-07-18 处理完成 - 更新: 1 条，插入: 14 条，错误: 0 条
2025-07-19 16:31:09,193 - INFO - 开始处理日期: 2025-07-19
2025-07-19 16:31:09,193 - INFO - Request Parameters - Page 1:
2025-07-19 16:31:09,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:31:09,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:31:09,750 - INFO - Response - Page 1:
2025-07-19 16:31:09,750 - INFO - 第 1 页获取到 2 条记录
2025-07-19 16:31:10,251 - INFO - 查询完成，共获取到 2 条记录
2025-07-19 16:31:10,251 - INFO - 获取到 2 条表单数据
2025-07-19 16:31:10,252 - INFO - 当前日期 2025-07-19 有 3 条MySQL数据需要处理
2025-07-19 16:31:10,252 - INFO - 开始批量插入 1 条新记录
2025-07-19 16:31:10,408 - INFO - 批量插入响应状态码: 200
2025-07-19 16:31:10,408 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 08:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9263CC7C-CB9A-79F7-A339-6A5FC4AFED97', 'x-acs-trace-id': '04182b45649e000bd72f2ff78e0b7c6e', 'etag': '6nLDnhTz36b5sge9YhDdy2w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 16:31:10,408 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71G39XIEBMAGDH8CQEZJHV2KRQNZ9DMIE']}
2025-07-19 16:31:10,408 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-19 16:31:10,408 - INFO - 成功插入的数据ID: ['FINST-L5766E71G39XIEBMAGDH8CQEZJHV2KRQNZ9DMIE']
2025-07-19 16:31:15,409 - INFO - 批量插入完成，共 1 条记录
2025-07-19 16:31:15,409 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-19 16:31:15,409 - INFO - 数据同步完成！更新: 1 条，插入: 15 条，错误: 1 条
2025-07-19 16:32:15,409 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 16:32:15,409 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 16:32:15,409 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 16:32:15,575 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 480 条记录
2025-07-19 16:32:15,575 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-19 16:32:15,579 - INFO - 开始处理日期: 2025-07-18
2025-07-19 16:32:15,580 - INFO - Request Parameters - Page 1:
2025-07-19 16:32:15,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:15,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:16,363 - INFO - Response - Page 1:
2025-07-19 16:32:16,363 - INFO - 第 1 页获取到 50 条记录
2025-07-19 16:32:16,865 - INFO - Request Parameters - Page 2:
2025-07-19 16:32:16,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:16,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:17,662 - INFO - Response - Page 2:
2025-07-19 16:32:17,662 - INFO - 第 2 页获取到 50 条记录
2025-07-19 16:32:18,163 - INFO - Request Parameters - Page 3:
2025-07-19 16:32:18,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:18,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:18,885 - INFO - Response - Page 3:
2025-07-19 16:32:18,886 - INFO - 第 3 页获取到 50 条记录
2025-07-19 16:32:19,386 - INFO - Request Parameters - Page 4:
2025-07-19 16:32:19,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:19,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:20,110 - INFO - Response - Page 4:
2025-07-19 16:32:20,110 - INFO - 第 4 页获取到 50 条记录
2025-07-19 16:32:20,610 - INFO - Request Parameters - Page 5:
2025-07-19 16:32:20,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:20,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:21,353 - INFO - Response - Page 5:
2025-07-19 16:32:21,353 - INFO - 第 5 页获取到 50 条记录
2025-07-19 16:32:21,854 - INFO - Request Parameters - Page 6:
2025-07-19 16:32:21,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:21,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:22,586 - INFO - Response - Page 6:
2025-07-19 16:32:22,586 - INFO - 第 6 页获取到 50 条记录
2025-07-19 16:32:23,087 - INFO - Request Parameters - Page 7:
2025-07-19 16:32:23,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:23,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:23,823 - INFO - Response - Page 7:
2025-07-19 16:32:23,823 - INFO - 第 7 页获取到 50 条记录
2025-07-19 16:32:24,323 - INFO - Request Parameters - Page 8:
2025-07-19 16:32:24,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:24,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:24,990 - INFO - Response - Page 8:
2025-07-19 16:32:24,990 - INFO - 第 8 页获取到 50 条记录
2025-07-19 16:32:25,491 - INFO - Request Parameters - Page 9:
2025-07-19 16:32:25,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:25,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:26,179 - INFO - Response - Page 9:
2025-07-19 16:32:26,180 - INFO - 第 9 页获取到 40 条记录
2025-07-19 16:32:26,681 - INFO - 查询完成，共获取到 440 条记录
2025-07-19 16:32:26,681 - INFO - 获取到 440 条表单数据
2025-07-19 16:32:26,691 - INFO - 当前日期 2025-07-18 有 462 条MySQL数据需要处理
2025-07-19 16:32:26,703 - INFO - 开始批量插入 22 条新记录
2025-07-19 16:32:26,936 - INFO - 批量插入响应状态码: 200
2025-07-19 16:32:26,936 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 08:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1068', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7E934A6F-2413-71A6-B064-F7A724367CB6', 'x-acs-trace-id': '0479d4faa6c0134177075355061e9d5c', 'etag': '1wNy3H+lccQQdoKtkRG1BEA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 16:32:26,936 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMI2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMJ2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMK2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DML2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMM2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMN2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMO2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMP2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMQ2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMR2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMS2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMT2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMU2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMV2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMW2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMX2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMY2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMZ2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM03', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM13', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM23', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM33']}
2025-07-19 16:32:26,936 - INFO - 批量插入表单数据成功，批次 1，共 22 条记录
2025-07-19 16:32:26,936 - INFO - 成功插入的数据ID: ['FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMI2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMJ2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMK2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DML2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMM2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMN2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMO2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMP2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMQ2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMR2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMS2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMT2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMU2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMV2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMW2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMX2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMY2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DMZ2', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM03', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM13', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM23', 'FINST-TQB66671K2AXVRQKFEXZA4DAQO9Y24TDPZ9DM33']
2025-07-19 16:32:31,937 - INFO - 批量插入完成，共 22 条记录
2025-07-19 16:32:31,937 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 22 条，错误: 0 条
2025-07-19 16:32:31,937 - INFO - 开始处理日期: 2025-07-19
2025-07-19 16:32:31,937 - INFO - Request Parameters - Page 1:
2025-07-19 16:32:31,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 16:32:31,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 16:32:32,423 - INFO - Response - Page 1:
2025-07-19 16:32:32,423 - INFO - 第 1 页获取到 3 条记录
2025-07-19 16:32:32,924 - INFO - 查询完成，共获取到 3 条记录
2025-07-19 16:32:32,924 - INFO - 获取到 3 条表单数据
2025-07-19 16:32:32,925 - INFO - 当前日期 2025-07-19 有 3 条MySQL数据需要处理
2025-07-19 16:32:32,925 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 16:32:32,925 - INFO - 数据同步完成！更新: 0 条，插入: 22 条，错误: 0 条
2025-07-19 16:32:32,925 - INFO - 同步完成
2025-07-19 19:30:34,413 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 19:30:34,414 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 19:30:34,414 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 19:30:34,570 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 151 条记录
2025-07-19 19:30:34,570 - INFO - 获取到 3 个日期需要处理: ['2025-07-17', '2025-07-18', '2025-07-19']
2025-07-19 19:30:34,571 - INFO - 开始处理日期: 2025-07-17
2025-07-19 19:30:34,574 - INFO - Request Parameters - Page 1:
2025-07-19 19:30:34,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:34,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:42,050 - INFO - Response - Page 1:
2025-07-19 19:30:42,050 - INFO - 第 1 页获取到 50 条记录
2025-07-19 19:30:42,552 - INFO - Request Parameters - Page 2:
2025-07-19 19:30:42,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:42,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:43,260 - INFO - Response - Page 2:
2025-07-19 19:30:43,260 - INFO - 第 2 页获取到 50 条记录
2025-07-19 19:30:43,762 - INFO - Request Parameters - Page 3:
2025-07-19 19:30:43,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:43,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:44,418 - INFO - Response - Page 3:
2025-07-19 19:30:44,418 - INFO - 第 3 页获取到 50 条记录
2025-07-19 19:30:44,920 - INFO - Request Parameters - Page 4:
2025-07-19 19:30:44,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:44,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:45,634 - INFO - Response - Page 4:
2025-07-19 19:30:45,634 - INFO - 第 4 页获取到 50 条记录
2025-07-19 19:30:46,136 - INFO - Request Parameters - Page 5:
2025-07-19 19:30:46,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:46,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:46,819 - INFO - Response - Page 5:
2025-07-19 19:30:46,819 - INFO - 第 5 页获取到 50 条记录
2025-07-19 19:30:47,320 - INFO - Request Parameters - Page 6:
2025-07-19 19:30:47,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:47,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:48,026 - INFO - Response - Page 6:
2025-07-19 19:30:48,026 - INFO - 第 6 页获取到 50 条记录
2025-07-19 19:30:48,527 - INFO - Request Parameters - Page 7:
2025-07-19 19:30:48,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:48,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:49,249 - INFO - Response - Page 7:
2025-07-19 19:30:49,249 - INFO - 第 7 页获取到 50 条记录
2025-07-19 19:30:49,749 - INFO - Request Parameters - Page 8:
2025-07-19 19:30:49,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:49,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:50,514 - INFO - Response - Page 8:
2025-07-19 19:30:50,514 - INFO - 第 8 页获取到 50 条记录
2025-07-19 19:30:51,015 - INFO - Request Parameters - Page 9:
2025-07-19 19:30:51,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:51,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:51,708 - INFO - Response - Page 9:
2025-07-19 19:30:51,708 - INFO - 第 9 页获取到 50 条记录
2025-07-19 19:30:52,210 - INFO - Request Parameters - Page 10:
2025-07-19 19:30:52,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:52,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:30:52,881 - INFO - Response - Page 10:
2025-07-19 19:30:52,881 - INFO - 第 10 页获取到 40 条记录
2025-07-19 19:30:53,382 - INFO - 查询完成，共获取到 490 条记录
2025-07-19 19:30:53,382 - INFO - 获取到 490 条表单数据
2025-07-19 19:30:53,390 - INFO - 当前日期 2025-07-17 有 1 条MySQL数据需要处理
2025-07-19 19:30:53,390 - INFO - 开始批量插入 1 条新记录
2025-07-19 19:30:53,568 - INFO - 批量插入响应状态码: 200
2025-07-19 19:30:53,568 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 11:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CB9A9B6E-068F-7C0A-9713-BB853CD3CC73', 'x-acs-trace-id': 'a223f95e0a343d7edd5fc0f1b82b76fb', 'etag': '5CK+ucwMC3HmIcNS5QsULXg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 19:30:53,568 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA14HAXL3V891YKCCVKPYC42MWU26ADME']}
2025-07-19 19:30:53,568 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-19 19:30:53,569 - INFO - 成功插入的数据ID: ['FINST-OIF66BA14HAXL3V891YKCCVKPYC42MWU26ADME']
2025-07-19 19:30:58,572 - INFO - 批量插入完成，共 1 条记录
2025-07-19 19:30:58,572 - INFO - 日期 2025-07-17 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-19 19:30:58,572 - INFO - 开始处理日期: 2025-07-18
2025-07-19 19:30:58,572 - INFO - Request Parameters - Page 1:
2025-07-19 19:30:58,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:30:58,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:31:06,686 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 665EE103-427F-7573-9A75-7E0E04E0CB6C Response: {'code': 'ServiceUnavailable', 'requestid': '665EE103-427F-7573-9A75-7E0E04E0CB6C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 665EE103-427F-7573-9A75-7E0E04E0CB6C)
2025-07-19 19:31:06,686 - INFO - 开始处理日期: 2025-07-19
2025-07-19 19:31:06,686 - INFO - Request Parameters - Page 1:
2025-07-19 19:31:06,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:31:06,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:31:07,166 - INFO - Response - Page 1:
2025-07-19 19:31:07,166 - INFO - 第 1 页获取到 3 条记录
2025-07-19 19:31:07,667 - INFO - 查询完成，共获取到 3 条记录
2025-07-19 19:31:07,667 - INFO - 获取到 3 条表单数据
2025-07-19 19:31:07,668 - INFO - 当前日期 2025-07-19 有 3 条MySQL数据需要处理
2025-07-19 19:31:07,668 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 19:31:07,668 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-07-19 19:32:07,692 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 19:32:07,692 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 19:32:07,692 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 19:32:07,855 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 485 条记录
2025-07-19 19:32:07,855 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-19 19:32:07,859 - INFO - 开始处理日期: 2025-07-18
2025-07-19 19:32:07,859 - INFO - Request Parameters - Page 1:
2025-07-19 19:32:07,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:07,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:08,624 - INFO - Response - Page 1:
2025-07-19 19:32:08,624 - INFO - 第 1 页获取到 50 条记录
2025-07-19 19:32:09,126 - INFO - Request Parameters - Page 2:
2025-07-19 19:32:09,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:09,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:09,871 - INFO - Response - Page 2:
2025-07-19 19:32:09,871 - INFO - 第 2 页获取到 50 条记录
2025-07-19 19:32:10,373 - INFO - Request Parameters - Page 3:
2025-07-19 19:32:10,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:10,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:11,183 - INFO - Response - Page 3:
2025-07-19 19:32:11,183 - INFO - 第 3 页获取到 50 条记录
2025-07-19 19:32:11,684 - INFO - Request Parameters - Page 4:
2025-07-19 19:32:11,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:11,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:12,544 - INFO - Response - Page 4:
2025-07-19 19:32:12,544 - INFO - 第 4 页获取到 50 条记录
2025-07-19 19:32:13,045 - INFO - Request Parameters - Page 5:
2025-07-19 19:32:13,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:13,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:13,722 - INFO - Response - Page 5:
2025-07-19 19:32:13,722 - INFO - 第 5 页获取到 50 条记录
2025-07-19 19:32:14,222 - INFO - Request Parameters - Page 6:
2025-07-19 19:32:14,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:14,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:14,977 - INFO - Response - Page 6:
2025-07-19 19:32:14,977 - INFO - 第 6 页获取到 50 条记录
2025-07-19 19:32:15,478 - INFO - Request Parameters - Page 7:
2025-07-19 19:32:15,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:15,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:16,221 - INFO - Response - Page 7:
2025-07-19 19:32:16,221 - INFO - 第 7 页获取到 50 条记录
2025-07-19 19:32:16,721 - INFO - Request Parameters - Page 8:
2025-07-19 19:32:16,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:16,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:17,544 - INFO - Response - Page 8:
2025-07-19 19:32:17,544 - INFO - 第 8 页获取到 50 条记录
2025-07-19 19:32:18,045 - INFO - Request Parameters - Page 9:
2025-07-19 19:32:18,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:18,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:18,754 - INFO - Response - Page 9:
2025-07-19 19:32:18,754 - INFO - 第 9 页获取到 50 条记录
2025-07-19 19:32:19,255 - INFO - Request Parameters - Page 10:
2025-07-19 19:32:19,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:19,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:19,824 - INFO - Response - Page 10:
2025-07-19 19:32:19,824 - INFO - 第 10 页获取到 12 条记录
2025-07-19 19:32:20,325 - INFO - 查询完成，共获取到 462 条记录
2025-07-19 19:32:20,325 - INFO - 获取到 462 条表单数据
2025-07-19 19:32:20,334 - INFO - 当前日期 2025-07-18 有 466 条MySQL数据需要处理
2025-07-19 19:32:20,345 - INFO - 开始批量插入 4 条新记录
2025-07-19 19:32:20,507 - INFO - 批量插入响应状态码: 200
2025-07-19 19:32:20,507 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 11:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '200', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E4BF65D9-9F1F-722F-A4FA-F2853D211854', 'x-acs-trace-id': 'e9a07a533098124ba8c623f2bc3bea79', 'etag': '2dOTnH1rWK+2Y1ZkkuBvzQg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 19:32:20,507 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMG', 'FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMH', 'FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMI', 'FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMJ']}
2025-07-19 19:32:20,507 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-07-19 19:32:20,507 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMG', 'FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMH', 'FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMI', 'FINST-IQE66ZC18HAX0J20F2ONF6D3QR952LYP46ADMJ']
2025-07-19 19:32:25,510 - INFO - 批量插入完成，共 4 条记录
2025-07-19 19:32:25,510 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-07-19 19:32:25,510 - INFO - 开始处理日期: 2025-07-19
2025-07-19 19:32:25,510 - INFO - Request Parameters - Page 1:
2025-07-19 19:32:25,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 19:32:25,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 19:32:26,022 - INFO - Response - Page 1:
2025-07-19 19:32:26,022 - INFO - 第 1 页获取到 3 条记录
2025-07-19 19:32:26,523 - INFO - 查询完成，共获取到 3 条记录
2025-07-19 19:32:26,523 - INFO - 获取到 3 条表单数据
2025-07-19 19:32:26,524 - INFO - 当前日期 2025-07-19 有 4 条MySQL数据需要处理
2025-07-19 19:32:26,524 - INFO - 开始批量插入 1 条新记录
2025-07-19 19:32:26,670 - INFO - 批量插入响应状态码: 200
2025-07-19 19:32:26,670 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 11:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6661F424-E37F-70FD-985F-CF7F035CEA9E', 'x-acs-trace-id': '5c87316c41a1f40bd0796ffa3e09050c', 'etag': '5Ysp1y8moaxp7PoJITnk0Vw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 19:32:26,670 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC15IAXGMDQDDF5O6RQQURE2UPU46ADM5']}
2025-07-19 19:32:26,670 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-19 19:32:26,670 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC15IAXGMDQDDF5O6RQQURE2UPU46ADM5']
2025-07-19 19:32:31,673 - INFO - 批量插入完成，共 1 条记录
2025-07-19 19:32:31,673 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-19 19:32:31,673 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 0 条
2025-07-19 19:32:31,673 - INFO - 同步完成
2025-07-19 22:30:35,633 - INFO - 使用默认增量同步（当天更新数据）
2025-07-19 22:30:35,633 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-19 22:30:35,633 - INFO - 查询参数: ('2025-07-19',)
2025-07-19 22:30:35,795 - INFO - MySQL查询成功，增量数据（日期: 2025-07-19），共获取 248 条记录
2025-07-19 22:30:35,796 - INFO - 获取到 3 个日期需要处理: ['2025-07-17', '2025-07-18', '2025-07-19']
2025-07-19 22:30:35,798 - INFO - 开始处理日期: 2025-07-17
2025-07-19 22:30:35,801 - INFO - Request Parameters - Page 1:
2025-07-19 22:30:35,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:35,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752681600000, 1752767999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:30:43,919 - ERROR - 处理日期 2025-07-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F96E00F0-8DE1-7BD7-B35F-D5E0CC581A17 Response: {'code': 'ServiceUnavailable', 'requestid': 'F96E00F0-8DE1-7BD7-B35F-D5E0CC581A17', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F96E00F0-8DE1-7BD7-B35F-D5E0CC581A17)
2025-07-19 22:30:43,919 - INFO - 开始处理日期: 2025-07-18
2025-07-19 22:30:43,920 - INFO - Request Parameters - Page 1:
2025-07-19 22:30:43,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:43,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:30:47,667 - INFO - Response - Page 1:
2025-07-19 22:30:47,667 - INFO - 第 1 页获取到 50 条记录
2025-07-19 22:30:48,167 - INFO - Request Parameters - Page 2:
2025-07-19 22:30:48,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:48,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:30:55,622 - INFO - Response - Page 2:
2025-07-19 22:30:55,623 - INFO - 第 2 页获取到 50 条记录
2025-07-19 22:30:56,124 - INFO - Request Parameters - Page 3:
2025-07-19 22:30:56,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:56,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:30:56,859 - INFO - Response - Page 3:
2025-07-19 22:30:56,860 - INFO - 第 3 页获取到 50 条记录
2025-07-19 22:30:57,361 - INFO - Request Parameters - Page 4:
2025-07-19 22:30:57,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:57,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:30:58,069 - INFO - Response - Page 4:
2025-07-19 22:30:58,069 - INFO - 第 4 页获取到 50 条记录
2025-07-19 22:30:58,571 - INFO - Request Parameters - Page 5:
2025-07-19 22:30:58,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:58,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:30:59,330 - INFO - Response - Page 5:
2025-07-19 22:30:59,331 - INFO - 第 5 页获取到 50 条记录
2025-07-19 22:30:59,832 - INFO - Request Parameters - Page 6:
2025-07-19 22:30:59,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:30:59,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:31:00,469 - INFO - Response - Page 6:
2025-07-19 22:31:00,469 - INFO - 第 6 页获取到 50 条记录
2025-07-19 22:31:00,969 - INFO - Request Parameters - Page 7:
2025-07-19 22:31:00,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:31:00,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:31:01,613 - INFO - Response - Page 7:
2025-07-19 22:31:01,613 - INFO - 第 7 页获取到 50 条记录
2025-07-19 22:31:02,114 - INFO - Request Parameters - Page 8:
2025-07-19 22:31:02,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:31:02,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:31:02,829 - INFO - Response - Page 8:
2025-07-19 22:31:02,829 - INFO - 第 8 页获取到 50 条记录
2025-07-19 22:31:03,330 - INFO - Request Parameters - Page 9:
2025-07-19 22:31:03,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:31:03,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:31:04,037 - INFO - Response - Page 9:
2025-07-19 22:31:04,037 - INFO - 第 9 页获取到 50 条记录
2025-07-19 22:31:04,537 - INFO - Request Parameters - Page 10:
2025-07-19 22:31:04,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:31:04,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:31:05,107 - INFO - Response - Page 10:
2025-07-19 22:31:05,107 - INFO - 第 10 页获取到 16 条记录
2025-07-19 22:31:05,608 - INFO - 查询完成，共获取到 466 条记录
2025-07-19 22:31:05,608 - INFO - 获取到 466 条表单数据
2025-07-19 22:31:05,616 - INFO - 当前日期 2025-07-18 有 142 条MySQL数据需要处理
2025-07-19 22:31:05,620 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 22:31:05,620 - INFO - 开始处理日期: 2025-07-19
2025-07-19 22:31:05,620 - INFO - Request Parameters - Page 1:
2025-07-19 22:31:05,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:31:05,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:31:06,130 - INFO - Response - Page 1:
2025-07-19 22:31:06,130 - INFO - 第 1 页获取到 4 条记录
2025-07-19 22:31:06,631 - INFO - 查询完成，共获取到 4 条记录
2025-07-19 22:31:06,631 - INFO - 获取到 4 条表单数据
2025-07-19 22:31:06,632 - INFO - 当前日期 2025-07-19 有 97 条MySQL数据需要处理
2025-07-19 22:31:06,633 - INFO - 开始批量插入 93 条新记录
2025-07-19 22:31:06,891 - INFO - 批量插入响应状态码: 200
2025-07-19 22:31:06,891 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 14:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0EBFED4F-5F41-76DF-87E9-6F137462ED6A', 'x-acs-trace-id': '34a9ec78965904aa68e5ccc9be636f7a', 'etag': '25F6xvNsoNXz+qRDs1bv98g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 22:31:06,891 - INFO - 批量插入响应体: {'result': ['FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM61', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM71', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM81', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM91', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMA1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMB1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMC1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMD1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADME1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMF1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMG1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMH1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMI1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMJ1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMK1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADML1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMM1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMN1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMO1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMP1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMQ1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMR1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMS1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMT1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMU1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMV1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMW1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMX1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMY1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMZ1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM02', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM12', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM22', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM32', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM42', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM52', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM62', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM72', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM82', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM92', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMA2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMB2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMC2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMD2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADME2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMF2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMG2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMH2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMI2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMJ2']}
2025-07-19 22:31:06,891 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-19 22:31:06,891 - INFO - 成功插入的数据ID: ['FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM61', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM71', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM81', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADM91', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMA1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMB1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMC1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMD1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADME1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMF1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMG1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMH1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMI1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMJ1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMK1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADML1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMM1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3F7JICADMN1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMO1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMP1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMQ1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMR1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMS1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMT1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMU1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMV1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMW1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMX1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMY1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMZ1', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM02', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM12', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM22', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM32', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM42', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM52', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM62', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM72', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM82', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADM92', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMA2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMB2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMC2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMD2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADME2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMF2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMG2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMH2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMI2', 'FINST-74766M715JAXFAX2EISXX4TAXAKH3G7JICADMJ2']
2025-07-19 22:31:12,119 - INFO - 批量插入响应状态码: 200
2025-07-19 22:31:12,119 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2076', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E8DCC0F6-0C55-7E87-9EDC-AF419B69FF44', 'x-acs-trace-id': '9f3b2afda30522c86bda74ac4eed9a87', 'etag': '26dhf1OXmj3zfQdMPNE6I8Q6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-19 22:31:12,119 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMS3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMT3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMU3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMV3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMW3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMX3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMY3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMZ3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM04', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM14', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM24', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM34', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM44', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM54', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM64', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM74', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM84', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM94', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMA4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMB4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMC4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMD4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADME4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMF4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMG4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMH4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMI4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMJ4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMK4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADML4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMM4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMN4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMO4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMP4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMQ4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMR4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMS4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMT4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMU4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMV4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMW4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMX4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMY4']}
2025-07-19 22:31:12,119 - INFO - 批量插入表单数据成功，批次 2，共 43 条记录
2025-07-19 22:31:12,119 - INFO - 成功插入的数据ID: ['FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMS3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMT3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMU3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMV3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMW3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2T8NICADMX3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMY3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMZ3', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM04', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM14', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM24', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM34', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM44', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM54', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM64', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM74', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM84', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADM94', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMA4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMB4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMC4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMD4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADME4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMF4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMG4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMH4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMI4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMJ4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMK4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADML4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMM4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMN4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMO4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMP4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMQ4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMR4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMS4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMT4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMU4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMV4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMW4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMX4', 'FINST-RNA66D71GHAXQX61F1AWE4PI74TE2U8NICADMY4']
2025-07-19 22:31:17,122 - INFO - 批量插入完成，共 93 条记录
2025-07-19 22:31:17,122 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 93 条，错误: 0 条
2025-07-19 22:31:17,122 - INFO - 数据同步完成！更新: 0 条，插入: 93 条，错误: 1 条
2025-07-19 22:32:17,147 - INFO - 开始同步昨天与今天的销售数据: 2025-07-18 至 2025-07-19
2025-07-19 22:32:17,147 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-19 22:32:17,147 - INFO - 查询参数: ('2025-07-18', '2025-07-19')
2025-07-19 22:32:17,314 - INFO - MySQL查询成功，时间段: 2025-07-18 至 2025-07-19，共获取 581 条记录
2025-07-19 22:32:17,314 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-19 22:32:17,320 - INFO - 开始处理日期: 2025-07-18
2025-07-19 22:32:17,320 - INFO - Request Parameters - Page 1:
2025-07-19 22:32:17,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:17,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:18,111 - INFO - Response - Page 1:
2025-07-19 22:32:18,111 - INFO - 第 1 页获取到 50 条记录
2025-07-19 22:32:18,612 - INFO - Request Parameters - Page 2:
2025-07-19 22:32:18,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:18,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:19,426 - INFO - Response - Page 2:
2025-07-19 22:32:19,426 - INFO - 第 2 页获取到 50 条记录
2025-07-19 22:32:19,926 - INFO - Request Parameters - Page 3:
2025-07-19 22:32:19,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:19,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:20,731 - INFO - Response - Page 3:
2025-07-19 22:32:20,732 - INFO - 第 3 页获取到 50 条记录
2025-07-19 22:32:21,233 - INFO - Request Parameters - Page 4:
2025-07-19 22:32:21,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:21,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:22,054 - INFO - Response - Page 4:
2025-07-19 22:32:22,054 - INFO - 第 4 页获取到 50 条记录
2025-07-19 22:32:22,554 - INFO - Request Parameters - Page 5:
2025-07-19 22:32:22,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:22,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:23,213 - INFO - Response - Page 5:
2025-07-19 22:32:23,213 - INFO - 第 5 页获取到 50 条记录
2025-07-19 22:32:23,714 - INFO - Request Parameters - Page 6:
2025-07-19 22:32:23,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:23,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:24,428 - INFO - Response - Page 6:
2025-07-19 22:32:24,428 - INFO - 第 6 页获取到 50 条记录
2025-07-19 22:32:24,929 - INFO - Request Parameters - Page 7:
2025-07-19 22:32:24,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:24,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:25,612 - INFO - Response - Page 7:
2025-07-19 22:32:25,612 - INFO - 第 7 页获取到 50 条记录
2025-07-19 22:32:26,112 - INFO - Request Parameters - Page 8:
2025-07-19 22:32:26,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:26,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:26,814 - INFO - Response - Page 8:
2025-07-19 22:32:26,814 - INFO - 第 8 页获取到 50 条记录
2025-07-19 22:32:27,315 - INFO - Request Parameters - Page 9:
2025-07-19 22:32:27,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:27,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:27,972 - INFO - Response - Page 9:
2025-07-19 22:32:27,972 - INFO - 第 9 页获取到 50 条记录
2025-07-19 22:32:28,473 - INFO - Request Parameters - Page 10:
2025-07-19 22:32:28,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:28,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:29,031 - INFO - Response - Page 10:
2025-07-19 22:32:29,032 - INFO - 第 10 页获取到 16 条记录
2025-07-19 22:32:29,533 - INFO - 查询完成，共获取到 466 条记录
2025-07-19 22:32:29,533 - INFO - 获取到 466 条表单数据
2025-07-19 22:32:29,541 - INFO - 当前日期 2025-07-18 有 466 条MySQL数据需要处理
2025-07-19 22:32:29,553 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 22:32:29,553 - INFO - 开始处理日期: 2025-07-19
2025-07-19 22:32:29,553 - INFO - Request Parameters - Page 1:
2025-07-19 22:32:29,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:29,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:30,257 - INFO - Response - Page 1:
2025-07-19 22:32:30,258 - INFO - 第 1 页获取到 50 条记录
2025-07-19 22:32:30,759 - INFO - Request Parameters - Page 2:
2025-07-19 22:32:30,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-19 22:32:30,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-19 22:32:31,439 - INFO - Response - Page 2:
2025-07-19 22:32:31,440 - INFO - 第 2 页获取到 47 条记录
2025-07-19 22:32:31,942 - INFO - 查询完成，共获取到 97 条记录
2025-07-19 22:32:31,942 - INFO - 获取到 97 条表单数据
2025-07-19 22:32:31,944 - INFO - 当前日期 2025-07-19 有 97 条MySQL数据需要处理
2025-07-19 22:32:31,946 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 22:32:31,946 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-19 22:32:31,947 - INFO - 同步完成
