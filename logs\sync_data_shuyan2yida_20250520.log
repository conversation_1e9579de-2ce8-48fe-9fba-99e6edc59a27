2025-05-20 08:00:03,606 - INFO - ==================================================
2025-05-20 08:00:03,606 - INFO - 程序启动 - 版本 v1.0.0
2025-05-20 08:00:03,607 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250520.log
2025-05-20 08:00:03,607 - INFO - ==================================================
2025-05-20 08:00:03,607 - INFO - 程序入口点: __main__
2025-05-20 08:00:03,608 - INFO - ==================================================
2025-05-20 08:00:03,608 - INFO - 程序启动 - 版本 v1.0.1
2025-05-20 08:00:03,608 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250520.log
2025-05-20 08:00:03,608 - INFO - ==================================================
2025-05-20 08:00:03,936 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-20 08:00:03,938 - INFO - sales_data表已存在，无需创建
2025-05-20 08:00:03,938 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-20 08:00:03,939 - INFO - DataSyncManager初始化完成
2025-05-20 08:00:03,939 - INFO - 未提供日期参数，使用默认值
2025-05-20 08:00:03,939 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-20 08:00:03,939 - INFO - 开始综合数据同步流程...
2025-05-20 08:00:03,939 - INFO - 正在获取数衍平台日销售数据...
2025-05-20 08:00:03,940 - INFO - 查询数衍平台数据，时间段为: 2025-03-20, 2025-05-19
2025-05-20 08:00:03,940 - INFO - 正在获取********至********的数据
2025-05-20 08:00:03,940 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:03,941 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E728652C2663657AC1357BA3F6B6B47A'}
2025-05-20 08:00:08,457 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:08,469 - INFO - 过滤后保留 1568 条记录
2025-05-20 08:00:10,471 - INFO - 正在获取********至********的数据
2025-05-20 08:00:10,471 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:10,472 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E83EB778C95AC5BFD60B34FD83EB9939'}
2025-05-20 08:00:13,640 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:13,653 - INFO - 过滤后保留 1542 条记录
2025-05-20 08:00:15,654 - INFO - 正在获取********至********的数据
2025-05-20 08:00:15,655 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:15,655 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4AA9DDD6C06A45D25C129E0885A8525F'}
2025-05-20 08:00:18,231 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:18,246 - INFO - 过滤后保留 1503 条记录
2025-05-20 08:00:20,248 - INFO - 正在获取********至********的数据
2025-05-20 08:00:20,248 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:20,249 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9A48C66A6A50D5D4725B727D70881AD6'}
2025-05-20 08:00:22,857 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:22,870 - INFO - 过滤后保留 1501 条记录
2025-05-20 08:00:24,871 - INFO - 正在获取********至********的数据
2025-05-20 08:00:24,871 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:24,872 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D0156E449C633B7288B1C77423414CA5'}
2025-05-20 08:00:27,507 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:27,520 - INFO - 过滤后保留 1499 条记录
2025-05-20 08:00:29,521 - INFO - 正在获取********至********的数据
2025-05-20 08:00:29,521 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:29,522 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F0CE65E3DFA06FEC90FB3D0F11C4D633'}
2025-05-20 08:00:31,730 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:31,742 - INFO - 过滤后保留 1491 条记录
2025-05-20 08:00:33,743 - INFO - 正在获取********至********的数据
2025-05-20 08:00:33,743 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:33,744 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0F06E4F8D0C5096299C73A130E7EF8F9'}
2025-05-20 08:00:36,177 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:36,190 - INFO - 过滤后保留 1466 条记录
2025-05-20 08:00:38,192 - INFO - 正在获取********至********的数据
2025-05-20 08:00:38,192 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:38,193 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '186C53275E1D88CBDA9B8351E33A5B40'}
2025-05-20 08:00:40,227 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:40,240 - INFO - 过滤后保留 1487 条记录
2025-05-20 08:00:42,241 - INFO - 正在获取********至********的数据
2025-05-20 08:00:42,241 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-20 08:00:42,242 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AF19CA6CE7858CFEF4AFB118C8539312'}
2025-05-20 08:00:43,795 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-20 08:00:43,804 - INFO - 过滤后保留 1052 条记录
2025-05-20 08:00:45,806 - INFO - 开始保存数据到SQLite数据库，共 13109 条记录待处理
2025-05-20 08:00:46,278 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-04-23
2025-05-20 08:00:46,278 - INFO - 变更字段: recommend_amount: 10889.38 -> 11206.38, amount: 10889 -> 11206, count: 48 -> 49, instore_amount: 18032.92 -> 18349.92, instore_count: 45 -> 46
2025-05-20 08:00:46,339 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-04-29
2025-05-20 08:00:46,339 - INFO - 变更字段: amount: 16402 -> 16729, count: 73 -> 74, instore_amount: 38094.5 -> 38421.3, instore_count: 73 -> 74
2025-05-20 08:00:46,358 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-04-24
2025-05-20 08:00:46,358 - INFO - 变更字段: recommend_amount: 5173.12 -> 5642.12, amount: 5173 -> 5642, count: 12 -> 13, instore_amount: 5173.12 -> 5642.12, instore_count: 12 -> 13
2025-05-20 08:00:46,437 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS, sale_time=2025-05-04
2025-05-20 08:00:46,438 - INFO - 变更字段: amount: 3477 -> 3935, count: 7 -> 8, online_amount: 257.6 -> 715.6, online_count: 4 -> 5
2025-05-20 08:00:46,518 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-18
2025-05-20 08:00:46,519 - INFO - 变更字段: recommend_amount: 0.0 -> 4155.6, daily_bill_amount: 0.0 -> 4155.6, amount: 386 -> 554, count: 1 -> 2, instore_amount: 386.0 -> 554.0, instore_count: 1 -> 2
2025-05-20 08:00:46,527 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE29HIJ7QK7Q2OV4FVC7F40014BL, sale_time=2025-05-18
2025-05-20 08:00:46,527 - INFO - 变更字段: amount: 1596 -> 1821, count: 10 -> 11, instore_amount: 1596.4 -> 1821.6, instore_count: 10 -> 11
2025-05-20 08:00:46,532 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-05-18
2025-05-20 08:00:46,532 - INFO - 变更字段: amount: 4425 -> 4458, count: 167 -> 170, instore_amount: 4446.08 -> 4479.2, instore_count: 167 -> 170
2025-05-20 08:00:46,534 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-05-18
2025-05-20 08:00:46,535 - INFO - 变更字段: count: 32 -> 33, online_count: 26 -> 27
2025-05-20 08:00:46,536 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDJ5HVP5F47Q2OV4FVC77O001449, sale_time=2025-05-16
2025-05-20 08:00:46,536 - INFO - 变更字段: amount: 3809 -> 5379, count: 9 -> 10, instore_amount: 3809.0 -> 5379.0, instore_count: 9 -> 10
2025-05-20 08:00:46,537 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-05-18
2025-05-20 08:00:46,538 - INFO - 变更字段: instore_amount: 12246.7 -> 12208.7, instore_count: 316 -> 314, online_amount: 732.5 -> 770.5, online_count: 18 -> 20
2025-05-20 08:00:46,539 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G, sale_time=2025-05-18
2025-05-20 08:00:46,539 - INFO - 变更字段: recommend_amount: 0.0 -> 16565.9, daily_bill_amount: 0.0 -> 16565.9
2025-05-20 08:00:46,540 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-05-18
2025-05-20 08:00:46,540 - INFO - 变更字段: amount: 4553 -> 4726, count: 14 -> 15, instore_amount: 4553.8 -> 4726.7, instore_count: 14 -> 15
2025-05-20 08:00:46,542 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-18
2025-05-20 08:00:46,542 - INFO - 变更字段: recommend_amount: 0.0 -> 4408.6, daily_bill_amount: 0.0 -> 4408.6, amount: 509 -> 1333, count: 2 -> 3, instore_amount: 509.0 -> 1333.0, instore_count: 2 -> 3
2025-05-20 08:00:46,547 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-18
2025-05-20 08:00:46,547 - INFO - 变更字段: recommend_amount: 3118.82 -> 3125.42, amount: 3118 -> 3125, count: 176 -> 177, online_amount: 2289.06 -> 2295.66, online_count: 103 -> 104
2025-05-20 08:00:46,550 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-05-18
2025-05-20 08:00:46,550 - INFO - 变更字段: amount: 10599 -> 10619, count: 234 -> 235, online_amount: 449.9 -> 469.2, online_count: 9 -> 10
2025-05-20 08:00:46,550 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-05-18
2025-05-20 08:00:46,551 - INFO - 变更字段: amount: 2383 -> 2901, count: 24 -> 25, instore_amount: 2383.0 -> 2901.0, instore_count: 24 -> 25
2025-05-20 08:00:46,551 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-18
2025-05-20 08:00:46,552 - INFO - 变更字段: recommend_amount: 11410.18 -> 11439.98, amount: 11410 -> 11439, count: 192 -> 193, online_amount: 1017.54 -> 1047.34, online_count: 16 -> 17
2025-05-20 08:00:46,552 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-18
2025-05-20 08:00:46,552 - INFO - 变更字段: amount: 7224 -> 7277, count: 329 -> 331, instore_amount: 4004.02 -> 4010.77, instore_count: 186 -> 187, online_amount: 3305.5 -> 3351.6, online_count: 143 -> 144
2025-05-20 08:00:46,554 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-18
2025-05-20 08:00:46,554 - INFO - 变更字段: recommend_amount: 1796.27 -> 1812.22, amount: 1796 -> 1812, count: 116 -> 117, online_amount: 782.02 -> 797.97, online_count: 61 -> 62
2025-05-20 08:00:46,554 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-17
2025-05-20 08:00:46,554 - INFO - 变更字段: recommend_amount: 1554.18 -> 1554.63, online_amount: 818.05 -> 818.5
2025-05-20 08:00:46,556 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-18
2025-05-20 08:00:46,557 - INFO - 变更字段: recommend_amount: 5121.07 -> 5181.97, amount: 5121 -> 5181, count: 268 -> 270, instore_amount: 1709.89 -> 1756.19, instore_count: 90 -> 91, online_amount: 3559.18 -> 3573.78, online_count: 178 -> 179
2025-05-20 08:00:46,557 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HLFG4AKMT96P82UAQ9ONTBKHO001HHS, sale_time=2025-05-18
2025-05-20 08:00:46,558 - INFO - 变更字段: recommend_amount: 0.0 -> 696.0, daily_bill_amount: 0.0 -> 696.0
2025-05-20 08:00:46,558 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-05-18
2025-05-20 08:00:46,558 - INFO - 变更字段: amount: 7942 -> 7943, count: 134 -> 135, instore_amount: 4385.95 -> 4387.08, instore_count: 26 -> 27
2025-05-20 08:00:46,560 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-18
2025-05-20 08:00:46,560 - INFO - 变更字段: recommend_amount: 0.0 -> 15213.55, daily_bill_amount: 0.0 -> 15213.55
2025-05-20 08:00:46,561 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-18
2025-05-20 08:00:46,561 - INFO - 变更字段: amount: 28924 -> 29159, count: 295 -> 296, instore_amount: 22473.2 -> 22708.2, instore_count: 147 -> 148
2025-05-20 08:00:46,562 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-05-18
2025-05-20 08:00:46,562 - INFO - 变更字段: amount: 42282 -> 44590, count: 277 -> 285, instore_amount: 27815.7 -> 30123.7, instore_count: 146 -> 154
2025-05-20 08:00:46,564 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-18
2025-05-20 08:00:46,564 - INFO - 变更字段: amount: 62700 -> 62738, count: 356 -> 358, online_amount: 15080.4 -> 15118.6, online_count: 155 -> 157
2025-05-20 08:00:46,565 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-05-18
2025-05-20 08:00:46,565 - INFO - 变更字段: amount: 18392 -> 18576, count: 166 -> 169, online_amount: 5133.22 -> 5317.12, online_count: 105 -> 108
2025-05-20 08:00:46,566 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0VODGC6J0I86N3H2U1QP001F3V, sale_time=2025-05-18
2025-05-20 08:00:46,567 - INFO - 变更字段: recommend_amount: 129.0 -> 258.0, amount: 129 -> 258, count: 9 -> 10, instore_amount: 129.0 -> 258.0, instore_count: 9 -> 10
2025-05-20 08:00:46,568 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-18
2025-05-20 08:00:46,568 - INFO - 变更字段: amount: 5439 -> 5450, count: 319 -> 321, instore_amount: 3296.32 -> 3307.32, instore_count: 202 -> 204
2025-05-20 08:00:46,569 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-18
2025-05-20 08:00:46,569 - INFO - 变更字段: amount: 239 -> 1743, count: 23 -> 181, instore_amount: 240.5 -> 1823.1, instore_count: 23 -> 181
2025-05-20 08:00:46,570 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-17
2025-05-20 08:00:46,570 - INFO - 变更字段: amount: 485 -> 1527, count: 45 -> 146, instore_amount: 522.9 -> 1580.9, instore_count: 45 -> 146
2025-05-20 08:00:46,572 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-18
2025-05-20 08:00:46,572 - INFO - 变更字段: amount: 2640 -> 2704, count: 28 -> 29, instore_amount: 2481.99 -> 2545.69, instore_count: 26 -> 27
2025-05-20 08:00:46,573 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ, sale_time=2025-05-18
2025-05-20 08:00:46,573 - INFO - 变更字段: recommend_amount: 1905.0 -> 2083.0, amount: 1905 -> 2083, count: 13 -> 14, instore_amount: 1905.0 -> 2083.0, instore_count: 13 -> 14
2025-05-20 08:00:46,574 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDF8HFHI690I86N3H2U1H9001EQF, sale_time=2025-05-18
2025-05-20 08:00:46,574 - INFO - 变更字段: amount: 2030 -> 2325, count: 6 -> 7, instore_amount: 2030.0 -> 2325.0, instore_count: 6 -> 7
2025-05-20 08:00:46,576 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9G31FV3GL0I86N3H2U190001EI6, sale_time=2025-05-18
2025-05-20 08:00:46,577 - INFO - 变更字段: amount: 28597 -> 42795, count: 3 -> 4, instore_amount: 28597.0 -> 42795.0, instore_count: 3 -> 4
2025-05-20 08:00:46,578 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-05-18
2025-05-20 08:00:46,578 - INFO - 变更字段: recommend_amount: 0.0 -> 4318.0, daily_bill_amount: 0.0 -> 4318.0
2025-05-20 08:00:46,579 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-05-18
2025-05-20 08:00:46,579 - INFO - 变更字段: recommend_amount: 0.0 -> 8100.1, daily_bill_amount: 0.0 -> 8100.1
2025-05-20 08:00:46,580 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-05-18
2025-05-20 08:00:46,580 - INFO - 变更字段: amount: 31409 -> 33565, count: 22 -> 24, instore_amount: 37208.0 -> 39364.0, instore_count: 22 -> 24
2025-05-20 08:00:46,581 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDVBEGSM760I86N3H2U12H001EBN, sale_time=2025-05-18
2025-05-20 08:00:46,581 - INFO - 变更字段: recommend_amount: 0.0 -> 20896.0, amount: 0 -> 20896, count: 2 -> 12, instore_amount: 0.0 -> 20896.0, instore_count: 2 -> 12
2025-05-20 08:00:46,582 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-18
2025-05-20 08:00:46,582 - INFO - 变更字段: amount: 4948 -> 5000, count: 322 -> 327, online_amount: 4710.16 -> 4761.86, online_count: 288 -> 293
2025-05-20 08:00:46,583 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDTTV5HD0Q0I86N3H2U11P001EAV, sale_time=2025-05-18
2025-05-20 08:00:46,583 - INFO - 变更字段: amount: 7297 -> 7301, count: 938 -> 940, instore_amount: 8719.69 -> 8730.49, instore_count: 928 -> 930
2025-05-20 08:00:46,583 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-05-18
2025-05-20 08:00:46,583 - INFO - 变更字段: amount: 3485 -> 3512, count: 188 -> 189, online_amount: 2781.51 -> 2808.11, online_count: 130 -> 131
2025-05-20 08:00:46,584 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDRR6FJ7A60I86N3H2U10L001E9R, sale_time=2025-05-18
2025-05-20 08:00:46,584 - INFO - 变更字段: recommend_amount: 5733.8 -> 5800.8, amount: 5733 -> 5800, count: 263 -> 265, online_amount: 2710.1 -> 2777.1, online_count: 98 -> 100
2025-05-20 08:00:46,586 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQQG9THS10I86N3H2U108001E9E, sale_time=2025-05-18
2025-05-20 08:00:46,586 - INFO - 变更字段: amount: 15893 -> 16099, count: 468 -> 469, instore_amount: 15949.1 -> 16155.4, instore_count: 468 -> 469
2025-05-20 08:00:46,587 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-18
2025-05-20 08:00:46,587 - INFO - 变更字段: amount: 7157 -> 7141, count: 516 -> 517, instore_amount: 5931.22 -> 6014.89, instore_count: 398 -> 409, online_amount: 1661.58 -> 1583.51, online_count: 118 -> 108
2025-05-20 08:00:46,588 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-18
2025-05-20 08:00:46,588 - INFO - 变更字段: amount: 23941 -> 24018, count: 466 -> 467, online_amount: 0.0 -> 76.65, online_count: 0 -> 1
2025-05-20 08:00:46,590 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDMFCJQF4F0I86N3H2U1UC001E7I, sale_time=2025-05-18
2025-05-20 08:00:46,590 - INFO - 变更字段: recommend_amount: 12684.17 -> 12734.57, amount: 12684 -> 12734, count: 156 -> 157, online_amount: 5261.74 -> 5312.14, online_count: 62 -> 63
2025-05-20 08:00:46,590 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=3F059827C9E04DEAA6B50797867EC52B, sale_time=2025-05-16
2025-05-20 08:00:46,591 - INFO - 变更字段: amount: 3694 -> 4400, count: 29 -> 30, instore_amount: 3694.0 -> 4400.0, instore_count: 29 -> 30
2025-05-20 08:00:46,591 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=3F059827C9E04DEAA6B50797867EC52B, sale_time=2025-05-15
2025-05-20 08:00:46,591 - INFO - 变更字段: amount: 5660 -> 5699, count: 31 -> 32, instore_amount: 5660.0 -> 5699.0, instore_count: 31 -> 32
2025-05-20 08:00:46,593 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-05-18
2025-05-20 08:00:46,593 - INFO - 变更字段: amount: 11548 -> 11856, count: 89 -> 90, instore_amount: 5224.8 -> 5532.8, instore_count: 33 -> 34
2025-05-20 08:00:46,594 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-18
2025-05-20 08:00:46,597 - INFO - 变更字段: recommend_amount: 4393.31 -> 4371.91, amount: 4393 -> 4371
2025-05-20 08:00:46,598 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-18
2025-05-20 08:00:46,598 - INFO - 变更字段: amount: 24628 -> 25840, count: 254 -> 259, instore_amount: 22634.64 -> 23846.62, instore_count: 166 -> 171
2025-05-20 08:00:46,600 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-18
2025-05-20 08:00:46,600 - INFO - 变更字段: recommend_amount: 8730.0 -> 20956.0, daily_bill_amount: 8730.0 -> 20956.0
2025-05-20 08:00:46,601 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-18
2025-05-20 08:00:46,602 - INFO - 变更字段: recommend_amount: 32985.9 -> 32929.42, daily_bill_amount: 32985.9 -> 32929.42
2025-05-20 08:00:46,602 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-17
2025-05-20 08:00:46,602 - INFO - 变更字段: amount: 26319 -> 26889, count: 91 -> 92, instore_amount: 26553.6 -> 27124.04, instore_count: 64 -> 65
2025-05-20 08:00:46,602 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-15
2025-05-20 08:00:46,603 - INFO - 变更字段: amount: 34664 -> 35369, count: 98 -> 99, instore_amount: 33903.35 -> 34608.35, instore_count: 69 -> 70
2025-05-20 08:00:46,605 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-18
2025-05-20 08:00:46,606 - INFO - 变更字段: amount: 29051 -> 29077, count: 164 -> 165, instore_amount: 25624.4 -> 25650.9, instore_count: 133 -> 134
2025-05-20 08:00:46,608 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRBVE2CQT7AV8LHQQGID9001EJI, sale_time=2025-05-18
2025-05-20 08:00:46,609 - INFO - 变更字段: amount: 30092 -> 30338, count: 174 -> 175, instore_amount: 17648.0 -> 17894.0, instore_count: 116 -> 117
2025-05-20 08:00:46,612 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-18
2025-05-20 08:00:46,613 - INFO - 变更字段: amount: 1058 -> 1051
2025-05-20 08:00:46,823 - INFO - SQLite数据保存完成，统计信息：
2025-05-20 08:00:46,823 - INFO - - 总记录数: 13109
2025-05-20 08:00:46,824 - INFO - - 成功插入: 207
2025-05-20 08:00:46,824 - INFO - - 成功更新: 60
2025-05-20 08:00:46,824 - INFO - - 无需更新: 12842
2025-05-20 08:00:46,824 - INFO - - 处理失败: 0
2025-05-20 08:00:52,209 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250520.xlsx
2025-05-20 08:00:52,217 - INFO - 成功获取数衍平台数据，共 13109 条记录
2025-05-20 08:00:52,217 - INFO - 正在更新SQLite月度汇总数据...
2025-05-20 08:00:52,225 - INFO - 月度数据sqllite清空完成
2025-05-20 08:00:52,468 - INFO - 月度汇总数据更新完成，处理了 1190 条汇总记录
2025-05-20 08:00:52,469 - INFO - 成功更新月度汇总数据，共 1190 条记录
2025-05-20 08:00:52,469 - INFO - 正在获取宜搭日销售表单数据...
2025-05-20 08:00:52,470 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-20 00:00:00 至 2025-05-19 23:59:59
2025-05-20 08:00:52,470 - INFO - 查询分段 1: 2025-03-20 至 2025-03-26
2025-05-20 08:00:52,470 - INFO - 查询日期范围: 2025-03-20 至 2025-03-26，使用分页查询，每页 100 条记录
2025-05-20 08:00:52,470 - INFO - Request Parameters - Page 1:
2025-05-20 08:00:52,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:00:52,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:00:57,319 - INFO - API请求耗时: 4848ms
2025-05-20 08:00:57,319 - INFO - Response - Page 1
2025-05-20 08:00:57,320 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:00:57,821 - INFO - Request Parameters - Page 2:
2025-05-20 08:00:57,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:00:57,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:00:58,558 - INFO - API请求耗时: 736ms
2025-05-20 08:00:58,559 - INFO - Response - Page 2
2025-05-20 08:00:58,559 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:00:59,061 - INFO - Request Parameters - Page 3:
2025-05-20 08:00:59,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:00:59,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:00:59,785 - INFO - API请求耗时: 723ms
2025-05-20 08:00:59,785 - INFO - Response - Page 3
2025-05-20 08:00:59,786 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:01:00,287 - INFO - Request Parameters - Page 4:
2025-05-20 08:01:00,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:00,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:00,915 - INFO - API请求耗时: 628ms
2025-05-20 08:01:00,916 - INFO - Response - Page 4
2025-05-20 08:01:00,916 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:01:01,418 - INFO - Request Parameters - Page 5:
2025-05-20 08:01:01,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:01,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:02,134 - INFO - API请求耗时: 715ms
2025-05-20 08:01:02,135 - INFO - Response - Page 5
2025-05-20 08:01:02,135 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:01:02,637 - INFO - Request Parameters - Page 6:
2025-05-20 08:01:02,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:02,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:03,274 - INFO - API请求耗时: 636ms
2025-05-20 08:01:03,274 - INFO - Response - Page 6
2025-05-20 08:01:03,275 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:01:03,776 - INFO - Request Parameters - Page 7:
2025-05-20 08:01:03,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:03,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:06,953 - INFO - API请求耗时: 3176ms
2025-05-20 08:01:06,953 - INFO - Response - Page 7
2025-05-20 08:01:06,953 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:01:07,453 - INFO - Request Parameters - Page 8:
2025-05-20 08:01:07,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:07,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:08,194 - INFO - API请求耗时: 739ms
2025-05-20 08:01:08,194 - INFO - Response - Page 8
2025-05-20 08:01:08,195 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:01:08,696 - INFO - Request Parameters - Page 9:
2025-05-20 08:01:08,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:08,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:09,327 - INFO - API请求耗时: 631ms
2025-05-20 08:01:09,327 - INFO - Response - Page 9
2025-05-20 08:01:09,328 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:01:09,829 - INFO - Request Parameters - Page 10:
2025-05-20 08:01:09,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:09,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:10,766 - INFO - API请求耗时: 936ms
2025-05-20 08:01:10,767 - INFO - Response - Page 10
2025-05-20 08:01:10,767 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:01:11,269 - INFO - Request Parameters - Page 11:
2025-05-20 08:01:11,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:11,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:11,931 - INFO - API请求耗时: 660ms
2025-05-20 08:01:11,931 - INFO - Response - Page 11
2025-05-20 08:01:11,931 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:01:12,431 - INFO - Request Parameters - Page 12:
2025-05-20 08:01:12,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:12,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:13,070 - INFO - API请求耗时: 638ms
2025-05-20 08:01:13,071 - INFO - Response - Page 12
2025-05-20 08:01:13,071 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:01:13,573 - INFO - Request Parameters - Page 13:
2025-05-20 08:01:13,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:13,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:14,409 - INFO - API请求耗时: 836ms
2025-05-20 08:01:14,409 - INFO - Response - Page 13
2025-05-20 08:01:14,410 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:01:14,910 - INFO - Request Parameters - Page 14:
2025-05-20 08:01:14,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:14,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:15,616 - INFO - API请求耗时: 704ms
2025-05-20 08:01:15,617 - INFO - Response - Page 14
2025-05-20 08:01:15,617 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:01:16,117 - INFO - Request Parameters - Page 15:
2025-05-20 08:01:16,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:16,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:16,775 - INFO - API请求耗时: 657ms
2025-05-20 08:01:16,775 - INFO - Response - Page 15
2025-05-20 08:01:16,776 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:01:17,276 - INFO - Request Parameters - Page 16:
2025-05-20 08:01:17,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:17,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:17,914 - INFO - API请求耗时: 638ms
2025-05-20 08:01:17,914 - INFO - Response - Page 16
2025-05-20 08:01:17,915 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:01:18,416 - INFO - Request Parameters - Page 17:
2025-05-20 08:01:18,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:18,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:19,058 - INFO - API请求耗时: 641ms
2025-05-20 08:01:19,058 - INFO - Response - Page 17
2025-05-20 08:01:19,059 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:01:19,560 - INFO - Request Parameters - Page 18:
2025-05-20 08:01:19,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:19,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:20,215 - INFO - API请求耗时: 654ms
2025-05-20 08:01:20,216 - INFO - Response - Page 18
2025-05-20 08:01:20,216 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:01:20,717 - INFO - Request Parameters - Page 19:
2025-05-20 08:01:20,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:20,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:21,385 - INFO - API请求耗时: 667ms
2025-05-20 08:01:21,385 - INFO - Response - Page 19
2025-05-20 08:01:21,386 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:01:21,887 - INFO - Request Parameters - Page 20:
2025-05-20 08:01:21,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:21,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:22,556 - INFO - API请求耗时: 668ms
2025-05-20 08:01:22,556 - INFO - Response - Page 20
2025-05-20 08:01:22,557 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:01:23,058 - INFO - Request Parameters - Page 21:
2025-05-20 08:01:23,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:23,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:23,758 - INFO - API请求耗时: 699ms
2025-05-20 08:01:23,759 - INFO - Response - Page 21
2025-05-20 08:01:23,759 - INFO - 第 21 页获取到 100 条记录
2025-05-20 08:01:24,261 - INFO - Request Parameters - Page 22:
2025-05-20 08:01:24,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:24,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:24,952 - INFO - API请求耗时: 690ms
2025-05-20 08:01:24,952 - INFO - Response - Page 22
2025-05-20 08:01:24,953 - INFO - 第 22 页获取到 100 条记录
2025-05-20 08:01:25,453 - INFO - Request Parameters - Page 23:
2025-05-20 08:01:25,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:25,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000470, 1742918400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:25,920 - INFO - API请求耗时: 466ms
2025-05-20 08:01:25,921 - INFO - Response - Page 23
2025-05-20 08:01:25,921 - INFO - 第 23 页获取到 37 条记录
2025-05-20 08:01:25,921 - INFO - 查询完成，共获取到 2237 条记录
2025-05-20 08:01:25,922 - INFO - 分段 1 查询成功，获取到 2237 条记录
2025-05-20 08:01:26,923 - INFO - 查询分段 2: 2025-03-27 至 2025-04-02
2025-05-20 08:01:26,923 - INFO - 查询日期范围: 2025-03-27 至 2025-04-02，使用分页查询，每页 100 条记录
2025-05-20 08:01:26,924 - INFO - Request Parameters - Page 1:
2025-05-20 08:01:26,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:26,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:27,569 - INFO - API请求耗时: 645ms
2025-05-20 08:01:27,569 - INFO - Response - Page 1
2025-05-20 08:01:27,570 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:01:28,071 - INFO - Request Parameters - Page 2:
2025-05-20 08:01:28,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:28,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:28,865 - INFO - API请求耗时: 794ms
2025-05-20 08:01:28,866 - INFO - Response - Page 2
2025-05-20 08:01:28,866 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:01:29,367 - INFO - Request Parameters - Page 3:
2025-05-20 08:01:29,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:29,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:30,021 - INFO - API请求耗时: 653ms
2025-05-20 08:01:30,022 - INFO - Response - Page 3
2025-05-20 08:01:30,022 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:01:30,522 - INFO - Request Parameters - Page 4:
2025-05-20 08:01:30,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:30,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:31,186 - INFO - API请求耗时: 663ms
2025-05-20 08:01:31,186 - INFO - Response - Page 4
2025-05-20 08:01:31,187 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:01:31,688 - INFO - Request Parameters - Page 5:
2025-05-20 08:01:31,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:31,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:32,398 - INFO - API请求耗时: 709ms
2025-05-20 08:01:32,399 - INFO - Response - Page 5
2025-05-20 08:01:32,399 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:01:32,901 - INFO - Request Parameters - Page 6:
2025-05-20 08:01:32,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:32,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:33,535 - INFO - API请求耗时: 634ms
2025-05-20 08:01:33,536 - INFO - Response - Page 6
2025-05-20 08:01:33,537 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:01:34,038 - INFO - Request Parameters - Page 7:
2025-05-20 08:01:34,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:34,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:34,737 - INFO - API请求耗时: 697ms
2025-05-20 08:01:34,738 - INFO - Response - Page 7
2025-05-20 08:01:34,738 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:01:35,240 - INFO - Request Parameters - Page 8:
2025-05-20 08:01:35,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:35,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:35,889 - INFO - API请求耗时: 648ms
2025-05-20 08:01:35,889 - INFO - Response - Page 8
2025-05-20 08:01:35,890 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:01:36,390 - INFO - Request Parameters - Page 9:
2025-05-20 08:01:36,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:36,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:37,137 - INFO - API请求耗时: 746ms
2025-05-20 08:01:37,138 - INFO - Response - Page 9
2025-05-20 08:01:37,138 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:01:37,640 - INFO - Request Parameters - Page 10:
2025-05-20 08:01:37,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:37,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:38,269 - INFO - API请求耗时: 628ms
2025-05-20 08:01:38,270 - INFO - Response - Page 10
2025-05-20 08:01:38,271 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:01:38,772 - INFO - Request Parameters - Page 11:
2025-05-20 08:01:38,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:38,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:39,477 - INFO - API请求耗时: 704ms
2025-05-20 08:01:39,477 - INFO - Response - Page 11
2025-05-20 08:01:39,478 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:01:39,979 - INFO - Request Parameters - Page 12:
2025-05-20 08:01:39,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:39,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:40,584 - INFO - API请求耗时: 604ms
2025-05-20 08:01:40,584 - INFO - Response - Page 12
2025-05-20 08:01:40,585 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:01:41,085 - INFO - Request Parameters - Page 13:
2025-05-20 08:01:41,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:41,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:41,799 - INFO - API请求耗时: 713ms
2025-05-20 08:01:41,799 - INFO - Response - Page 13
2025-05-20 08:01:41,800 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:01:42,301 - INFO - Request Parameters - Page 14:
2025-05-20 08:01:42,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:42,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:42,989 - INFO - API请求耗时: 688ms
2025-05-20 08:01:42,990 - INFO - Response - Page 14
2025-05-20 08:01:42,990 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:01:43,490 - INFO - Request Parameters - Page 15:
2025-05-20 08:01:43,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:43,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:44,164 - INFO - API请求耗时: 673ms
2025-05-20 08:01:44,164 - INFO - Response - Page 15
2025-05-20 08:01:44,165 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:01:44,666 - INFO - Request Parameters - Page 16:
2025-05-20 08:01:44,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:44,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:45,402 - INFO - API请求耗时: 735ms
2025-05-20 08:01:45,403 - INFO - Response - Page 16
2025-05-20 08:01:45,403 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:01:45,904 - INFO - Request Parameters - Page 17:
2025-05-20 08:01:45,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:45,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:46,516 - INFO - API请求耗时: 610ms
2025-05-20 08:01:46,517 - INFO - Response - Page 17
2025-05-20 08:01:46,517 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:01:47,017 - INFO - Request Parameters - Page 18:
2025-05-20 08:01:47,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:47,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:47,611 - INFO - API请求耗时: 594ms
2025-05-20 08:01:47,611 - INFO - Response - Page 18
2025-05-20 08:01:47,612 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:01:48,112 - INFO - Request Parameters - Page 19:
2025-05-20 08:01:48,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:48,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:48,775 - INFO - API请求耗时: 661ms
2025-05-20 08:01:48,775 - INFO - Response - Page 19
2025-05-20 08:01:48,775 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:01:49,275 - INFO - Request Parameters - Page 20:
2025-05-20 08:01:49,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:49,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743004800470, 1743523200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:49,895 - INFO - API请求耗时: 618ms
2025-05-20 08:01:49,895 - INFO - Response - Page 20
2025-05-20 08:01:49,896 - INFO - 第 20 页获取到 68 条记录
2025-05-20 08:01:49,896 - INFO - 查询完成，共获取到 1968 条记录
2025-05-20 08:01:49,897 - INFO - 分段 2 查询成功，获取到 1968 条记录
2025-05-20 08:01:50,897 - INFO - 查询分段 3: 2025-04-03 至 2025-04-09
2025-05-20 08:01:50,897 - INFO - 查询日期范围: 2025-04-03 至 2025-04-09，使用分页查询，每页 100 条记录
2025-05-20 08:01:50,898 - INFO - Request Parameters - Page 1:
2025-05-20 08:01:50,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:50,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:51,488 - INFO - API请求耗时: 590ms
2025-05-20 08:01:51,489 - INFO - Response - Page 1
2025-05-20 08:01:51,489 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:01:51,990 - INFO - Request Parameters - Page 2:
2025-05-20 08:01:51,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:51,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:52,728 - INFO - API请求耗时: 736ms
2025-05-20 08:01:52,728 - INFO - Response - Page 2
2025-05-20 08:01:52,729 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:01:53,230 - INFO - Request Parameters - Page 3:
2025-05-20 08:01:53,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:53,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:53,938 - INFO - API请求耗时: 707ms
2025-05-20 08:01:53,938 - INFO - Response - Page 3
2025-05-20 08:01:53,939 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:01:54,440 - INFO - Request Parameters - Page 4:
2025-05-20 08:01:54,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:54,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:55,194 - INFO - API请求耗时: 754ms
2025-05-20 08:01:55,195 - INFO - Response - Page 4
2025-05-20 08:01:55,196 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:01:55,697 - INFO - Request Parameters - Page 5:
2025-05-20 08:01:55,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:55,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:56,351 - INFO - API请求耗时: 652ms
2025-05-20 08:01:56,351 - INFO - Response - Page 5
2025-05-20 08:01:56,352 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:01:56,852 - INFO - Request Parameters - Page 6:
2025-05-20 08:01:56,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:56,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:57,719 - INFO - API请求耗时: 866ms
2025-05-20 08:01:57,720 - INFO - Response - Page 6
2025-05-20 08:01:57,720 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:01:58,222 - INFO - Request Parameters - Page 7:
2025-05-20 08:01:58,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:58,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:01:58,913 - INFO - API请求耗时: 691ms
2025-05-20 08:01:58,914 - INFO - Response - Page 7
2025-05-20 08:01:58,914 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:01:59,415 - INFO - Request Parameters - Page 8:
2025-05-20 08:01:59,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:01:59,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:00,309 - INFO - API请求耗时: 893ms
2025-05-20 08:02:00,309 - INFO - Response - Page 8
2025-05-20 08:02:00,310 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:02:00,811 - INFO - Request Parameters - Page 9:
2025-05-20 08:02:00,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:00,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:01,585 - INFO - API请求耗时: 773ms
2025-05-20 08:02:01,585 - INFO - Response - Page 9
2025-05-20 08:02:01,586 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:02:02,087 - INFO - Request Parameters - Page 10:
2025-05-20 08:02:02,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:02,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:02,712 - INFO - API请求耗时: 624ms
2025-05-20 08:02:02,712 - INFO - Response - Page 10
2025-05-20 08:02:02,713 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:02:03,214 - INFO - Request Parameters - Page 11:
2025-05-20 08:02:03,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:03,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:03,823 - INFO - API请求耗时: 608ms
2025-05-20 08:02:03,824 - INFO - Response - Page 11
2025-05-20 08:02:03,824 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:02:04,325 - INFO - Request Parameters - Page 12:
2025-05-20 08:02:04,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:04,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:04,970 - INFO - API请求耗时: 644ms
2025-05-20 08:02:04,971 - INFO - Response - Page 12
2025-05-20 08:02:04,971 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:02:05,473 - INFO - Request Parameters - Page 13:
2025-05-20 08:02:05,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:05,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:06,118 - INFO - API请求耗时: 645ms
2025-05-20 08:02:06,118 - INFO - Response - Page 13
2025-05-20 08:02:06,119 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:02:06,620 - INFO - Request Parameters - Page 14:
2025-05-20 08:02:06,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:06,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:07,294 - INFO - API请求耗时: 673ms
2025-05-20 08:02:07,294 - INFO - Response - Page 14
2025-05-20 08:02:07,295 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:02:07,796 - INFO - Request Parameters - Page 15:
2025-05-20 08:02:07,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:07,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:08,421 - INFO - API请求耗时: 624ms
2025-05-20 08:02:08,422 - INFO - Response - Page 15
2025-05-20 08:02:08,422 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:02:08,922 - INFO - Request Parameters - Page 16:
2025-05-20 08:02:08,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:08,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:09,572 - INFO - API请求耗时: 649ms
2025-05-20 08:02:09,572 - INFO - Response - Page 16
2025-05-20 08:02:09,573 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:02:10,074 - INFO - Request Parameters - Page 17:
2025-05-20 08:02:10,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:10,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:10,751 - INFO - API请求耗时: 676ms
2025-05-20 08:02:10,751 - INFO - Response - Page 17
2025-05-20 08:02:10,752 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:02:11,252 - INFO - Request Parameters - Page 18:
2025-05-20 08:02:11,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:11,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:12,002 - INFO - API请求耗时: 749ms
2025-05-20 08:02:12,003 - INFO - Response - Page 18
2025-05-20 08:02:12,004 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:02:12,505 - INFO - Request Parameters - Page 19:
2025-05-20 08:02:12,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:12,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:13,217 - INFO - API请求耗时: 711ms
2025-05-20 08:02:13,217 - INFO - Response - Page 19
2025-05-20 08:02:13,218 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:02:13,719 - INFO - Request Parameters - Page 20:
2025-05-20 08:02:13,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:13,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:14,351 - INFO - API请求耗时: 632ms
2025-05-20 08:02:14,353 - INFO - Response - Page 20
2025-05-20 08:02:14,353 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:02:14,854 - INFO - Request Parameters - Page 21:
2025-05-20 08:02:14,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:14,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:15,530 - INFO - API请求耗时: 675ms
2025-05-20 08:02:15,531 - INFO - Response - Page 21
2025-05-20 08:02:15,531 - INFO - 第 21 页获取到 100 条记录
2025-05-20 08:02:16,031 - INFO - Request Parameters - Page 22:
2025-05-20 08:02:16,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:16,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600470, 1744128000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:16,552 - INFO - API请求耗时: 520ms
2025-05-20 08:02:16,552 - INFO - Response - Page 22
2025-05-20 08:02:16,552 - INFO - 第 22 页获取到 39 条记录
2025-05-20 08:02:16,552 - INFO - 查询完成，共获取到 2139 条记录
2025-05-20 08:02:16,553 - INFO - 分段 3 查询成功，获取到 2139 条记录
2025-05-20 08:02:17,554 - INFO - 查询分段 4: 2025-04-10 至 2025-04-16
2025-05-20 08:02:17,554 - INFO - 查询日期范围: 2025-04-10 至 2025-04-16，使用分页查询，每页 100 条记录
2025-05-20 08:02:17,555 - INFO - Request Parameters - Page 1:
2025-05-20 08:02:17,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:17,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:18,162 - INFO - API请求耗时: 606ms
2025-05-20 08:02:18,162 - INFO - Response - Page 1
2025-05-20 08:02:18,163 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:02:18,664 - INFO - Request Parameters - Page 2:
2025-05-20 08:02:18,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:18,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:19,266 - INFO - API请求耗时: 601ms
2025-05-20 08:02:19,267 - INFO - Response - Page 2
2025-05-20 08:02:19,267 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:02:19,767 - INFO - Request Parameters - Page 3:
2025-05-20 08:02:19,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:19,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:20,421 - INFO - API请求耗时: 652ms
2025-05-20 08:02:20,421 - INFO - Response - Page 3
2025-05-20 08:02:20,422 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:02:20,922 - INFO - Request Parameters - Page 4:
2025-05-20 08:02:20,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:20,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:21,554 - INFO - API请求耗时: 631ms
2025-05-20 08:02:21,554 - INFO - Response - Page 4
2025-05-20 08:02:21,555 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:02:22,056 - INFO - Request Parameters - Page 5:
2025-05-20 08:02:22,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:22,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:22,750 - INFO - API请求耗时: 693ms
2025-05-20 08:02:22,750 - INFO - Response - Page 5
2025-05-20 08:02:22,751 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:02:23,252 - INFO - Request Parameters - Page 6:
2025-05-20 08:02:23,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:23,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:23,967 - INFO - API请求耗时: 714ms
2025-05-20 08:02:23,967 - INFO - Response - Page 6
2025-05-20 08:02:23,968 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:02:24,468 - INFO - Request Parameters - Page 7:
2025-05-20 08:02:24,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:24,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:25,297 - INFO - API请求耗时: 828ms
2025-05-20 08:02:25,298 - INFO - Response - Page 7
2025-05-20 08:02:25,298 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:02:25,799 - INFO - Request Parameters - Page 8:
2025-05-20 08:02:25,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:25,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:26,449 - INFO - API请求耗时: 649ms
2025-05-20 08:02:26,449 - INFO - Response - Page 8
2025-05-20 08:02:26,450 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:02:26,951 - INFO - Request Parameters - Page 9:
2025-05-20 08:02:26,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:26,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:27,622 - INFO - API请求耗时: 671ms
2025-05-20 08:02:27,623 - INFO - Response - Page 9
2025-05-20 08:02:27,623 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:02:28,125 - INFO - Request Parameters - Page 10:
2025-05-20 08:02:28,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:28,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:28,758 - INFO - API请求耗时: 632ms
2025-05-20 08:02:28,759 - INFO - Response - Page 10
2025-05-20 08:02:28,760 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:02:29,261 - INFO - Request Parameters - Page 11:
2025-05-20 08:02:29,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:29,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:30,088 - INFO - API请求耗时: 825ms
2025-05-20 08:02:30,088 - INFO - Response - Page 11
2025-05-20 08:02:30,089 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:02:30,590 - INFO - Request Parameters - Page 12:
2025-05-20 08:02:30,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:30,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:31,255 - INFO - API请求耗时: 664ms
2025-05-20 08:02:31,255 - INFO - Response - Page 12
2025-05-20 08:02:31,256 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:02:31,757 - INFO - Request Parameters - Page 13:
2025-05-20 08:02:31,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:31,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:32,374 - INFO - API请求耗时: 616ms
2025-05-20 08:02:32,375 - INFO - Response - Page 13
2025-05-20 08:02:32,375 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:02:32,876 - INFO - Request Parameters - Page 14:
2025-05-20 08:02:32,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:32,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:33,555 - INFO - API请求耗时: 678ms
2025-05-20 08:02:33,555 - INFO - Response - Page 14
2025-05-20 08:02:33,555 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:02:34,055 - INFO - Request Parameters - Page 15:
2025-05-20 08:02:34,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:34,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:34,743 - INFO - API请求耗时: 687ms
2025-05-20 08:02:34,743 - INFO - Response - Page 15
2025-05-20 08:02:34,744 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:02:35,244 - INFO - Request Parameters - Page 16:
2025-05-20 08:02:35,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:35,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:35,997 - INFO - API请求耗时: 752ms
2025-05-20 08:02:35,997 - INFO - Response - Page 16
2025-05-20 08:02:35,998 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:02:36,499 - INFO - Request Parameters - Page 17:
2025-05-20 08:02:36,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:36,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:37,289 - INFO - API请求耗时: 789ms
2025-05-20 08:02:37,290 - INFO - Response - Page 17
2025-05-20 08:02:37,290 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:02:37,792 - INFO - Request Parameters - Page 18:
2025-05-20 08:02:37,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:37,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:38,513 - INFO - API请求耗时: 720ms
2025-05-20 08:02:38,513 - INFO - Response - Page 18
2025-05-20 08:02:38,513 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:02:39,013 - INFO - Request Parameters - Page 19:
2025-05-20 08:02:39,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:39,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:39,676 - INFO - API请求耗时: 661ms
2025-05-20 08:02:39,677 - INFO - Response - Page 19
2025-05-20 08:02:39,677 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:02:40,178 - INFO - Request Parameters - Page 20:
2025-05-20 08:02:40,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:40,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:40,783 - INFO - API请求耗时: 603ms
2025-05-20 08:02:40,783 - INFO - Response - Page 20
2025-05-20 08:02:40,783 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:02:41,285 - INFO - Request Parameters - Page 21:
2025-05-20 08:02:41,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:41,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:42,032 - INFO - API请求耗时: 746ms
2025-05-20 08:02:42,032 - INFO - Response - Page 21
2025-05-20 08:02:42,033 - INFO - 第 21 页获取到 100 条记录
2025-05-20 08:02:42,534 - INFO - Request Parameters - Page 22:
2025-05-20 08:02:42,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:42,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744214400470, 1744732800470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:43,037 - INFO - API请求耗时: 502ms
2025-05-20 08:02:43,038 - INFO - Response - Page 22
2025-05-20 08:02:43,038 - INFO - 第 22 页获取到 47 条记录
2025-05-20 08:02:43,039 - INFO - 查询完成，共获取到 2147 条记录
2025-05-20 08:02:43,039 - INFO - 分段 4 查询成功，获取到 2147 条记录
2025-05-20 08:02:44,040 - INFO - 查询分段 5: 2025-04-17 至 2025-04-23
2025-05-20 08:02:44,040 - INFO - 查询日期范围: 2025-04-17 至 2025-04-23，使用分页查询，每页 100 条记录
2025-05-20 08:02:44,041 - INFO - Request Parameters - Page 1:
2025-05-20 08:02:44,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:44,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:44,622 - INFO - API请求耗时: 581ms
2025-05-20 08:02:44,623 - INFO - Response - Page 1
2025-05-20 08:02:44,623 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:02:45,123 - INFO - Request Parameters - Page 2:
2025-05-20 08:02:45,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:45,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:45,778 - INFO - API请求耗时: 654ms
2025-05-20 08:02:45,779 - INFO - Response - Page 2
2025-05-20 08:02:45,779 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:02:46,281 - INFO - Request Parameters - Page 3:
2025-05-20 08:02:46,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:46,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:46,913 - INFO - API请求耗时: 631ms
2025-05-20 08:02:46,914 - INFO - Response - Page 3
2025-05-20 08:02:46,915 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:02:47,415 - INFO - Request Parameters - Page 4:
2025-05-20 08:02:47,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:47,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:48,102 - INFO - API请求耗时: 687ms
2025-05-20 08:02:48,103 - INFO - Response - Page 4
2025-05-20 08:02:48,103 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:02:48,604 - INFO - Request Parameters - Page 5:
2025-05-20 08:02:48,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:48,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:49,353 - INFO - API请求耗时: 748ms
2025-05-20 08:02:49,353 - INFO - Response - Page 5
2025-05-20 08:02:49,354 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:02:49,854 - INFO - Request Parameters - Page 6:
2025-05-20 08:02:49,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:49,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:50,561 - INFO - API请求耗时: 706ms
2025-05-20 08:02:50,561 - INFO - Response - Page 6
2025-05-20 08:02:50,562 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:02:51,063 - INFO - Request Parameters - Page 7:
2025-05-20 08:02:51,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:51,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:51,705 - INFO - API请求耗时: 641ms
2025-05-20 08:02:51,705 - INFO - Response - Page 7
2025-05-20 08:02:51,706 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:02:52,207 - INFO - Request Parameters - Page 8:
2025-05-20 08:02:52,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:52,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:52,850 - INFO - API请求耗时: 642ms
2025-05-20 08:02:52,850 - INFO - Response - Page 8
2025-05-20 08:02:52,851 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:02:53,352 - INFO - Request Parameters - Page 9:
2025-05-20 08:02:53,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:53,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:54,148 - INFO - API请求耗时: 795ms
2025-05-20 08:02:54,148 - INFO - Response - Page 9
2025-05-20 08:02:54,149 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:02:54,649 - INFO - Request Parameters - Page 10:
2025-05-20 08:02:54,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:54,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:55,281 - INFO - API请求耗时: 631ms
2025-05-20 08:02:55,281 - INFO - Response - Page 10
2025-05-20 08:02:55,282 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:02:55,783 - INFO - Request Parameters - Page 11:
2025-05-20 08:02:55,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:55,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:56,445 - INFO - API请求耗时: 661ms
2025-05-20 08:02:56,446 - INFO - Response - Page 11
2025-05-20 08:02:56,446 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:02:56,947 - INFO - Request Parameters - Page 12:
2025-05-20 08:02:56,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:56,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:57,624 - INFO - API请求耗时: 676ms
2025-05-20 08:02:57,625 - INFO - Response - Page 12
2025-05-20 08:02:57,625 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:02:58,126 - INFO - Request Parameters - Page 13:
2025-05-20 08:02:58,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:58,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:02:58,839 - INFO - API请求耗时: 712ms
2025-05-20 08:02:58,840 - INFO - Response - Page 13
2025-05-20 08:02:58,840 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:02:59,341 - INFO - Request Parameters - Page 14:
2025-05-20 08:02:59,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:02:59,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:00,056 - INFO - API请求耗时: 714ms
2025-05-20 08:03:00,056 - INFO - Response - Page 14
2025-05-20 08:03:00,057 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:03:00,558 - INFO - Request Parameters - Page 15:
2025-05-20 08:03:00,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:00,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:01,400 - INFO - API请求耗时: 841ms
2025-05-20 08:03:01,401 - INFO - Response - Page 15
2025-05-20 08:03:01,401 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:03:01,902 - INFO - Request Parameters - Page 16:
2025-05-20 08:03:01,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:01,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:02,613 - INFO - API请求耗时: 710ms
2025-05-20 08:03:02,613 - INFO - Response - Page 16
2025-05-20 08:03:02,613 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:03:03,115 - INFO - Request Parameters - Page 17:
2025-05-20 08:03:03,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:03,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:03,755 - INFO - API请求耗时: 640ms
2025-05-20 08:03:03,755 - INFO - Response - Page 17
2025-05-20 08:03:03,756 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:03:04,256 - INFO - Request Parameters - Page 18:
2025-05-20 08:03:04,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:04,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:04,863 - INFO - API请求耗时: 605ms
2025-05-20 08:03:04,863 - INFO - Response - Page 18
2025-05-20 08:03:04,864 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:03:05,365 - INFO - Request Parameters - Page 19:
2025-05-20 08:03:05,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:05,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:06,325 - INFO - API请求耗时: 959ms
2025-05-20 08:03:06,325 - INFO - Response - Page 19
2025-05-20 08:03:06,326 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:03:06,827 - INFO - Request Parameters - Page 20:
2025-05-20 08:03:06,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:06,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:07,516 - INFO - API请求耗时: 688ms
2025-05-20 08:03:07,517 - INFO - Response - Page 20
2025-05-20 08:03:07,518 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:03:08,019 - INFO - Request Parameters - Page 21:
2025-05-20 08:03:08,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:08,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:08,696 - INFO - API请求耗时: 676ms
2025-05-20 08:03:08,696 - INFO - Response - Page 21
2025-05-20 08:03:08,697 - INFO - 第 21 页获取到 100 条记录
2025-05-20 08:03:09,197 - INFO - Request Parameters - Page 22:
2025-05-20 08:03:09,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:09,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200470, 1745337600470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:09,662 - INFO - API请求耗时: 464ms
2025-05-20 08:03:09,662 - INFO - Response - Page 22
2025-05-20 08:03:09,663 - INFO - 第 22 页获取到 27 条记录
2025-05-20 08:03:09,663 - INFO - 查询完成，共获取到 2127 条记录
2025-05-20 08:03:09,664 - INFO - 分段 5 查询成功，获取到 2127 条记录
2025-05-20 08:03:10,665 - INFO - 查询分段 6: 2025-04-24 至 2025-04-30
2025-05-20 08:03:10,665 - INFO - 查询日期范围: 2025-04-24 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-20 08:03:10,666 - INFO - Request Parameters - Page 1:
2025-05-20 08:03:10,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:10,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:11,338 - INFO - API请求耗时: 673ms
2025-05-20 08:03:11,339 - INFO - Response - Page 1
2025-05-20 08:03:11,339 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:03:11,841 - INFO - Request Parameters - Page 2:
2025-05-20 08:03:11,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:11,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:12,553 - INFO - API请求耗时: 711ms
2025-05-20 08:03:12,554 - INFO - Response - Page 2
2025-05-20 08:03:12,554 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:03:13,055 - INFO - Request Parameters - Page 3:
2025-05-20 08:03:13,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:13,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:13,866 - INFO - API请求耗时: 810ms
2025-05-20 08:03:13,866 - INFO - Response - Page 3
2025-05-20 08:03:13,867 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:03:14,368 - INFO - Request Parameters - Page 4:
2025-05-20 08:03:14,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:14,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:15,063 - INFO - API请求耗时: 694ms
2025-05-20 08:03:15,063 - INFO - Response - Page 4
2025-05-20 08:03:15,064 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:03:15,565 - INFO - Request Parameters - Page 5:
2025-05-20 08:03:15,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:15,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:16,246 - INFO - API请求耗时: 679ms
2025-05-20 08:03:16,247 - INFO - Response - Page 5
2025-05-20 08:03:16,247 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:03:16,748 - INFO - Request Parameters - Page 6:
2025-05-20 08:03:16,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:16,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:17,382 - INFO - API请求耗时: 634ms
2025-05-20 08:03:17,382 - INFO - Response - Page 6
2025-05-20 08:03:17,383 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:03:17,883 - INFO - Request Parameters - Page 7:
2025-05-20 08:03:17,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:17,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:18,535 - INFO - API请求耗时: 650ms
2025-05-20 08:03:18,535 - INFO - Response - Page 7
2025-05-20 08:03:18,536 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:03:19,037 - INFO - Request Parameters - Page 8:
2025-05-20 08:03:19,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:19,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:19,686 - INFO - API请求耗时: 648ms
2025-05-20 08:03:19,687 - INFO - Response - Page 8
2025-05-20 08:03:19,687 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:03:20,188 - INFO - Request Parameters - Page 9:
2025-05-20 08:03:20,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:20,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:20,865 - INFO - API请求耗时: 676ms
2025-05-20 08:03:20,866 - INFO - Response - Page 9
2025-05-20 08:03:20,866 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:03:21,368 - INFO - Request Parameters - Page 10:
2025-05-20 08:03:21,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:21,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:22,045 - INFO - API请求耗时: 677ms
2025-05-20 08:03:22,045 - INFO - Response - Page 10
2025-05-20 08:03:22,046 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:03:22,546 - INFO - Request Parameters - Page 11:
2025-05-20 08:03:22,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:22,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:23,203 - INFO - API请求耗时: 655ms
2025-05-20 08:03:23,203 - INFO - Response - Page 11
2025-05-20 08:03:23,204 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:03:23,704 - INFO - Request Parameters - Page 12:
2025-05-20 08:03:23,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:23,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:24,489 - INFO - API请求耗时: 784ms
2025-05-20 08:03:24,489 - INFO - Response - Page 12
2025-05-20 08:03:24,490 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:03:24,991 - INFO - Request Parameters - Page 13:
2025-05-20 08:03:24,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:24,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:25,624 - INFO - API请求耗时: 633ms
2025-05-20 08:03:25,625 - INFO - Response - Page 13
2025-05-20 08:03:25,625 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:03:26,126 - INFO - Request Parameters - Page 14:
2025-05-20 08:03:26,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:26,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:26,883 - INFO - API请求耗时: 755ms
2025-05-20 08:03:26,883 - INFO - Response - Page 14
2025-05-20 08:03:26,884 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:03:27,385 - INFO - Request Parameters - Page 15:
2025-05-20 08:03:27,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:27,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:28,136 - INFO - API请求耗时: 750ms
2025-05-20 08:03:28,137 - INFO - Response - Page 15
2025-05-20 08:03:28,137 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:03:28,638 - INFO - Request Parameters - Page 16:
2025-05-20 08:03:28,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:28,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:29,290 - INFO - API请求耗时: 650ms
2025-05-20 08:03:29,290 - INFO - Response - Page 16
2025-05-20 08:03:29,291 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:03:29,791 - INFO - Request Parameters - Page 17:
2025-05-20 08:03:29,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:29,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:30,559 - INFO - API请求耗时: 767ms
2025-05-20 08:03:30,559 - INFO - Response - Page 17
2025-05-20 08:03:30,560 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:03:31,061 - INFO - Request Parameters - Page 18:
2025-05-20 08:03:31,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:31,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:31,731 - INFO - API请求耗时: 669ms
2025-05-20 08:03:31,732 - INFO - Response - Page 18
2025-05-20 08:03:31,732 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:03:32,232 - INFO - Request Parameters - Page 19:
2025-05-20 08:03:32,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:32,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:32,886 - INFO - API请求耗时: 653ms
2025-05-20 08:03:32,886 - INFO - Response - Page 19
2025-05-20 08:03:32,886 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:03:33,388 - INFO - Request Parameters - Page 20:
2025-05-20 08:03:33,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:33,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:34,053 - INFO - API请求耗时: 664ms
2025-05-20 08:03:34,053 - INFO - Response - Page 20
2025-05-20 08:03:34,054 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:03:34,555 - INFO - Request Parameters - Page 21:
2025-05-20 08:03:34,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:34,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:35,248 - INFO - API请求耗时: 692ms
2025-05-20 08:03:35,248 - INFO - Response - Page 21
2025-05-20 08:03:35,249 - INFO - 第 21 页获取到 100 条记录
2025-05-20 08:03:35,750 - INFO - Request Parameters - Page 22:
2025-05-20 08:03:35,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:35,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745424000470, 1745942400470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:36,373 - INFO - API请求耗时: 622ms
2025-05-20 08:03:36,373 - INFO - Response - Page 22
2025-05-20 08:03:36,374 - INFO - 第 22 页获取到 35 条记录
2025-05-20 08:03:36,374 - INFO - 查询完成，共获取到 2135 条记录
2025-05-20 08:03:36,374 - INFO - 分段 6 查询成功，获取到 2135 条记录
2025-05-20 08:03:37,375 - INFO - 查询分段 7: 2025-05-01 至 2025-05-07
2025-05-20 08:03:37,375 - INFO - 查询日期范围: 2025-05-01 至 2025-05-07，使用分页查询，每页 100 条记录
2025-05-20 08:03:37,376 - INFO - Request Parameters - Page 1:
2025-05-20 08:03:37,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:37,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:37,969 - INFO - API请求耗时: 592ms
2025-05-20 08:03:37,969 - INFO - Response - Page 1
2025-05-20 08:03:37,970 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:03:38,470 - INFO - Request Parameters - Page 2:
2025-05-20 08:03:38,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:38,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:39,120 - INFO - API请求耗时: 648ms
2025-05-20 08:03:39,120 - INFO - Response - Page 2
2025-05-20 08:03:39,121 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:03:39,621 - INFO - Request Parameters - Page 3:
2025-05-20 08:03:39,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:39,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:40,313 - INFO - API请求耗时: 690ms
2025-05-20 08:03:40,313 - INFO - Response - Page 3
2025-05-20 08:03:40,314 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:03:40,815 - INFO - Request Parameters - Page 4:
2025-05-20 08:03:40,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:40,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:41,494 - INFO - API请求耗时: 678ms
2025-05-20 08:03:41,495 - INFO - Response - Page 4
2025-05-20 08:03:41,495 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:03:41,995 - INFO - Request Parameters - Page 5:
2025-05-20 08:03:41,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:41,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:42,602 - INFO - API请求耗时: 606ms
2025-05-20 08:03:42,602 - INFO - Response - Page 5
2025-05-20 08:03:42,603 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:03:43,103 - INFO - Request Parameters - Page 6:
2025-05-20 08:03:43,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:43,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:43,797 - INFO - API请求耗时: 693ms
2025-05-20 08:03:43,798 - INFO - Response - Page 6
2025-05-20 08:03:43,799 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:03:44,300 - INFO - Request Parameters - Page 7:
2025-05-20 08:03:44,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:44,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:44,994 - INFO - API请求耗时: 692ms
2025-05-20 08:03:44,994 - INFO - Response - Page 7
2025-05-20 08:03:44,994 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:03:45,495 - INFO - Request Parameters - Page 8:
2025-05-20 08:03:45,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:45,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:46,230 - INFO - API请求耗时: 732ms
2025-05-20 08:03:46,230 - INFO - Response - Page 8
2025-05-20 08:03:46,231 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:03:46,732 - INFO - Request Parameters - Page 9:
2025-05-20 08:03:46,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:46,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:47,450 - INFO - API请求耗时: 717ms
2025-05-20 08:03:47,451 - INFO - Response - Page 9
2025-05-20 08:03:47,451 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:03:47,952 - INFO - Request Parameters - Page 10:
2025-05-20 08:03:47,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:47,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:48,638 - INFO - API请求耗时: 685ms
2025-05-20 08:03:48,638 - INFO - Response - Page 10
2025-05-20 08:03:48,639 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:03:49,140 - INFO - Request Parameters - Page 11:
2025-05-20 08:03:49,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:49,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:49,897 - INFO - API请求耗时: 756ms
2025-05-20 08:03:49,898 - INFO - Response - Page 11
2025-05-20 08:03:49,898 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:03:50,399 - INFO - Request Parameters - Page 12:
2025-05-20 08:03:50,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:50,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:51,158 - INFO - API请求耗时: 758ms
2025-05-20 08:03:51,158 - INFO - Response - Page 12
2025-05-20 08:03:51,159 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:03:51,659 - INFO - Request Parameters - Page 13:
2025-05-20 08:03:51,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:51,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:52,354 - INFO - API请求耗时: 693ms
2025-05-20 08:03:52,355 - INFO - Response - Page 13
2025-05-20 08:03:52,355 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:03:52,857 - INFO - Request Parameters - Page 14:
2025-05-20 08:03:52,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:52,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:53,460 - INFO - API请求耗时: 603ms
2025-05-20 08:03:53,460 - INFO - Response - Page 14
2025-05-20 08:03:53,461 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:03:53,962 - INFO - Request Parameters - Page 15:
2025-05-20 08:03:53,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:53,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:54,655 - INFO - API请求耗时: 691ms
2025-05-20 08:03:54,655 - INFO - Response - Page 15
2025-05-20 08:03:54,656 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:03:55,157 - INFO - Request Parameters - Page 16:
2025-05-20 08:03:55,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:55,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:55,805 - INFO - API请求耗时: 647ms
2025-05-20 08:03:55,805 - INFO - Response - Page 16
2025-05-20 08:03:55,806 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:03:56,307 - INFO - Request Parameters - Page 17:
2025-05-20 08:03:56,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:56,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:56,974 - INFO - API请求耗时: 666ms
2025-05-20 08:03:56,974 - INFO - Response - Page 17
2025-05-20 08:03:56,975 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:03:57,475 - INFO - Request Parameters - Page 18:
2025-05-20 08:03:57,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:57,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:58,110 - INFO - API请求耗时: 634ms
2025-05-20 08:03:58,111 - INFO - Response - Page 18
2025-05-20 08:03:58,111 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:03:58,612 - INFO - Request Parameters - Page 19:
2025-05-20 08:03:58,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:58,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:03:59,319 - INFO - API请求耗时: 706ms
2025-05-20 08:03:59,319 - INFO - Response - Page 19
2025-05-20 08:03:59,320 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:03:59,820 - INFO - Request Parameters - Page 20:
2025-05-20 08:03:59,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:03:59,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:00,581 - INFO - API请求耗时: 761ms
2025-05-20 08:04:00,582 - INFO - Response - Page 20
2025-05-20 08:04:00,582 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:04:01,083 - INFO - Request Parameters - Page 21:
2025-05-20 08:04:01,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:01,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800470, 1746547200470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:01,791 - INFO - API请求耗时: 707ms
2025-05-20 08:04:01,791 - INFO - Response - Page 21
2025-05-20 08:04:01,792 - INFO - 第 21 页获取到 75 条记录
2025-05-20 08:04:01,792 - INFO - 查询完成，共获取到 2075 条记录
2025-05-20 08:04:01,792 - INFO - 分段 7 查询成功，获取到 2075 条记录
2025-05-20 08:04:02,793 - INFO - 查询分段 8: 2025-05-08 至 2025-05-14
2025-05-20 08:04:02,794 - INFO - 查询日期范围: 2025-05-08 至 2025-05-14，使用分页查询，每页 100 条记录
2025-05-20 08:04:02,794 - INFO - Request Parameters - Page 1:
2025-05-20 08:04:02,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:02,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:03,479 - INFO - API请求耗时: 684ms
2025-05-20 08:04:03,480 - INFO - Response - Page 1
2025-05-20 08:04:03,480 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:04:03,981 - INFO - Request Parameters - Page 2:
2025-05-20 08:04:03,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:03,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:04,615 - INFO - API请求耗时: 634ms
2025-05-20 08:04:04,615 - INFO - Response - Page 2
2025-05-20 08:04:04,616 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:04:05,117 - INFO - Request Parameters - Page 3:
2025-05-20 08:04:05,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:05,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:05,789 - INFO - API请求耗时: 671ms
2025-05-20 08:04:05,789 - INFO - Response - Page 3
2025-05-20 08:04:05,790 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:04:06,291 - INFO - Request Parameters - Page 4:
2025-05-20 08:04:06,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:06,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:07,038 - INFO - API请求耗时: 746ms
2025-05-20 08:04:07,038 - INFO - Response - Page 4
2025-05-20 08:04:07,039 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:04:07,539 - INFO - Request Parameters - Page 5:
2025-05-20 08:04:07,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:07,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:08,246 - INFO - API请求耗时: 706ms
2025-05-20 08:04:08,246 - INFO - Response - Page 5
2025-05-20 08:04:08,247 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:04:08,748 - INFO - Request Parameters - Page 6:
2025-05-20 08:04:08,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:08,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:09,355 - INFO - API请求耗时: 604ms
2025-05-20 08:04:09,355 - INFO - Response - Page 6
2025-05-20 08:04:09,356 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:04:09,857 - INFO - Request Parameters - Page 7:
2025-05-20 08:04:09,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:09,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:10,521 - INFO - API请求耗时: 663ms
2025-05-20 08:04:10,521 - INFO - Response - Page 7
2025-05-20 08:04:10,522 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:04:11,023 - INFO - Request Parameters - Page 8:
2025-05-20 08:04:11,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:11,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:11,678 - INFO - API请求耗时: 654ms
2025-05-20 08:04:11,679 - INFO - Response - Page 8
2025-05-20 08:04:11,680 - INFO - 第 8 页获取到 100 条记录
2025-05-20 08:04:12,181 - INFO - Request Parameters - Page 9:
2025-05-20 08:04:12,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:12,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:12,859 - INFO - API请求耗时: 677ms
2025-05-20 08:04:12,859 - INFO - Response - Page 9
2025-05-20 08:04:12,860 - INFO - 第 9 页获取到 100 条记录
2025-05-20 08:04:13,360 - INFO - Request Parameters - Page 10:
2025-05-20 08:04:13,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:13,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:13,974 - INFO - API请求耗时: 613ms
2025-05-20 08:04:13,974 - INFO - Response - Page 10
2025-05-20 08:04:13,975 - INFO - 第 10 页获取到 100 条记录
2025-05-20 08:04:14,475 - INFO - Request Parameters - Page 11:
2025-05-20 08:04:14,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:14,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:15,151 - INFO - API请求耗时: 675ms
2025-05-20 08:04:15,151 - INFO - Response - Page 11
2025-05-20 08:04:15,152 - INFO - 第 11 页获取到 100 条记录
2025-05-20 08:04:15,652 - INFO - Request Parameters - Page 12:
2025-05-20 08:04:15,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:15,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:16,345 - INFO - API请求耗时: 692ms
2025-05-20 08:04:16,345 - INFO - Response - Page 12
2025-05-20 08:04:16,346 - INFO - 第 12 页获取到 100 条记录
2025-05-20 08:04:16,847 - INFO - Request Parameters - Page 13:
2025-05-20 08:04:16,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:16,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:17,507 - INFO - API请求耗时: 659ms
2025-05-20 08:04:17,508 - INFO - Response - Page 13
2025-05-20 08:04:17,509 - INFO - 第 13 页获取到 100 条记录
2025-05-20 08:04:18,010 - INFO - Request Parameters - Page 14:
2025-05-20 08:04:18,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:18,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:18,660 - INFO - API请求耗时: 649ms
2025-05-20 08:04:18,661 - INFO - Response - Page 14
2025-05-20 08:04:18,662 - INFO - 第 14 页获取到 100 条记录
2025-05-20 08:04:19,163 - INFO - Request Parameters - Page 15:
2025-05-20 08:04:19,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:19,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:19,743 - INFO - API请求耗时: 579ms
2025-05-20 08:04:19,743 - INFO - Response - Page 15
2025-05-20 08:04:19,744 - INFO - 第 15 页获取到 100 条记录
2025-05-20 08:04:20,245 - INFO - Request Parameters - Page 16:
2025-05-20 08:04:20,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:20,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:20,965 - INFO - API请求耗时: 719ms
2025-05-20 08:04:20,966 - INFO - Response - Page 16
2025-05-20 08:04:20,966 - INFO - 第 16 页获取到 100 条记录
2025-05-20 08:04:21,467 - INFO - Request Parameters - Page 17:
2025-05-20 08:04:21,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:21,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:22,085 - INFO - API请求耗时: 617ms
2025-05-20 08:04:22,086 - INFO - Response - Page 17
2025-05-20 08:04:22,086 - INFO - 第 17 页获取到 100 条记录
2025-05-20 08:04:22,587 - INFO - Request Parameters - Page 18:
2025-05-20 08:04:22,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:22,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:23,288 - INFO - API请求耗时: 660ms
2025-05-20 08:04:23,288 - INFO - Response - Page 18
2025-05-20 08:04:23,290 - INFO - 第 18 页获取到 100 条记录
2025-05-20 08:04:23,790 - INFO - Request Parameters - Page 19:
2025-05-20 08:04:23,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:23,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:24,426 - INFO - API请求耗时: 635ms
2025-05-20 08:04:24,427 - INFO - Response - Page 19
2025-05-20 08:04:24,427 - INFO - 第 19 页获取到 100 条记录
2025-05-20 08:04:24,928 - INFO - Request Parameters - Page 20:
2025-05-20 08:04:24,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:24,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:25,626 - INFO - API请求耗时: 697ms
2025-05-20 08:04:25,627 - INFO - Response - Page 20
2025-05-20 08:04:25,627 - INFO - 第 20 页获取到 100 条记录
2025-05-20 08:04:26,129 - INFO - Request Parameters - Page 21:
2025-05-20 08:04:26,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:26,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:26,816 - INFO - API请求耗时: 687ms
2025-05-20 08:04:26,817 - INFO - Response - Page 21
2025-05-20 08:04:26,817 - INFO - 第 21 页获取到 100 条记录
2025-05-20 08:04:27,317 - INFO - Request Parameters - Page 22:
2025-05-20 08:04:27,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:27,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746633600470, 1747152000470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:27,752 - INFO - API请求耗时: 434ms
2025-05-20 08:04:27,752 - INFO - Response - Page 22
2025-05-20 08:04:27,753 - INFO - 第 22 页获取到 15 条记录
2025-05-20 08:04:27,753 - INFO - 查询完成，共获取到 2115 条记录
2025-05-20 08:04:27,753 - INFO - 分段 8 查询成功，获取到 2115 条记录
2025-05-20 08:04:28,754 - INFO - 查询分段 9: 2025-05-15 至 2025-05-19
2025-05-20 08:04:28,754 - INFO - 查询日期范围: 2025-05-15 至 2025-05-19，使用分页查询，每页 100 条记录
2025-05-20 08:04:28,755 - INFO - Request Parameters - Page 1:
2025-05-20 08:04:28,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:28,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400470, 1747670399470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:29,497 - INFO - API请求耗时: 742ms
2025-05-20 08:04:29,498 - INFO - Response - Page 1
2025-05-20 08:04:29,498 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:04:30,000 - INFO - Request Parameters - Page 2:
2025-05-20 08:04:30,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:30,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400470, 1747670399470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:30,643 - INFO - API请求耗时: 642ms
2025-05-20 08:04:30,643 - INFO - Response - Page 2
2025-05-20 08:04:30,644 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:04:31,145 - INFO - Request Parameters - Page 3:
2025-05-20 08:04:31,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:31,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400470, 1747670399470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:31,798 - INFO - API请求耗时: 652ms
2025-05-20 08:04:31,798 - INFO - Response - Page 3
2025-05-20 08:04:31,799 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:04:32,300 - INFO - Request Parameters - Page 4:
2025-05-20 08:04:32,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:32,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400470, 1747670399470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:33,137 - INFO - API请求耗时: 835ms
2025-05-20 08:04:33,137 - INFO - Response - Page 4
2025-05-20 08:04:33,138 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:04:33,639 - INFO - Request Parameters - Page 5:
2025-05-20 08:04:33,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:33,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400470, 1747670399470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:34,338 - INFO - API请求耗时: 698ms
2025-05-20 08:04:34,338 - INFO - Response - Page 5
2025-05-20 08:04:34,339 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:04:34,840 - INFO - Request Parameters - Page 6:
2025-05-20 08:04:34,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:04:34,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400470, 1747670399470], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:04:35,486 - INFO - API请求耗时: 646ms
2025-05-20 08:04:35,487 - INFO - Response - Page 6
2025-05-20 08:04:35,488 - INFO - 第 6 页获取到 96 条记录
2025-05-20 08:04:35,488 - INFO - 查询完成，共获取到 596 条记录
2025-05-20 08:04:35,488 - INFO - 分段 9 查询成功，获取到 596 条记录
2025-05-20 08:04:36,488 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 17539 条记录，失败 0 次
2025-05-20 08:04:36,489 - INFO - 成功获取宜搭日销售表单数据，共 17539 条记录
2025-05-20 08:04:36,489 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-20 08:04:36,489 - INFO - 开始对比和同步日销售数据...
2025-05-20 08:04:37,032 - INFO - 成功创建宜搭日销售数据索引，共 10922 条记录
2025-05-20 08:04:37,032 - INFO - 开始处理数衍数据，共 13109 条记录
2025-05-20 08:04:37,556 - INFO - 更新表单数据成功: FINST-HXD667B1HFFVFL2X90WYNBIM55X12VEZ59PAMKF
2025-05-20 08:04:37,556 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 10889.38, 'new_value': 11206.38}, {'field': 'amount', 'old_value': 10889.379999999997, 'new_value': 11206.379999999997}, {'field': 'count', 'old_value': 48, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 18032.92, 'new_value': 18349.92}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 46}]
2025-05-20 08:04:38,028 - INFO - 更新表单数据成功: FINST-3PF66V712BGVGNGF76TKW5B720FM2SI079PAM84
2025-05-20 08:04:38,029 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_20250429, 变更字段: [{'field': 'amount', 'old_value': 16402.74, 'new_value': 16729.540000000005}, {'field': 'count', 'old_value': 73, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 38094.5, 'new_value': 38421.3}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 74}]
2025-05-20 08:04:38,524 - INFO - 更新表单数据成功: FINST-SWC66P91F9FVTV4N910BM7P27IWT2IDG79PAMZ2
2025-05-20 08:04:38,524 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_********, 变更字段: [{'field': 'amount', 'old_value': 5776.3, 'new_value': 5058.3}]
2025-05-20 08:04:38,963 - INFO - 更新表单数据成功: FINST-SWC66P91F9FVTV4N910BM7P27IWT2IDG79PAMZ3
2025-05-20 08:04:38,964 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4768.87, 'new_value': 4758.97}, {'field': 'amount', 'old_value': 4768.87, 'new_value': 4758.97}, {'field': 'instoreAmount', 'old_value': 4432.86, 'new_value': 4422.96}]
2025-05-20 08:04:39,361 - INFO - 更新表单数据成功: FINST-FPB66VB10FHV7MZQFLBFO9L5TET52PHCAWSAMZD
2025-05-20 08:04:39,361 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250429, 变更字段: [{'field': 'recommendAmount', 'old_value': 4879.23, 'new_value': 4849.33}, {'field': 'amount', 'old_value': 4879.23, 'new_value': 4849.33}, {'field': 'instoreAmount', 'old_value': 4542.54, 'new_value': 4512.64}]
2025-05-20 08:04:39,775 - INFO - 更新表单数据成功: FINST-6AG66W81PFFVII8NBSO518E6VPMT36XZC1QAM5M
2025-05-20 08:04:39,776 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250427, 变更字段: [{'field': 'recommendAmount', 'old_value': 6807.81, 'new_value': 5607.33}, {'field': 'amount', 'old_value': 6807.81, 'new_value': 5607.33}, {'field': 'instoreAmount', 'old_value': 6560.96, 'new_value': 5360.48}]
2025-05-20 08:04:40,241 - INFO - 更新表单数据成功: FINST-1MD668B12VFVXKDR9KMIMCRMJBIS3M0J79PAMY8
2025-05-20 08:04:40,242 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2807.74, 'new_value': 2705.41}, {'field': 'amount', 'old_value': 2807.7400000000002, 'new_value': 2705.4100000000003}, {'field': 'count', 'old_value': 170, 'new_value': 165}, {'field': 'instoreAmount', 'old_value': 1329.73, 'new_value': 1227.4}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 62}]
2025-05-20 08:04:40,702 - INFO - 更新表单数据成功: FINST-FPB66VB10FHV7MZQFLBFO9L5TET52PHCAWSAM1E
2025-05-20 08:04:40,702 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250429, 变更字段: [{'field': 'recommendAmount', 'old_value': 2465.12, 'new_value': 2303.4}, {'field': 'amount', 'old_value': 2465.12, 'new_value': 2303.3999999999996}, {'field': 'count', 'old_value': 163, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 1293.62, 'new_value': 1131.9}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 75}]
2025-05-20 08:04:41,121 - INFO - 更新表单数据成功: FINST-8LC66GC1YEHV8RYUBF8H6BRU8HIT22FUTGRAMHC
2025-05-20 08:04:41,121 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250428, 变更字段: [{'field': 'recommendAmount', 'old_value': 2249.21, 'new_value': 2182.12}, {'field': 'amount', 'old_value': 2249.21, 'new_value': 2182.12}, {'field': 'count', 'old_value': 153, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 1232.19, 'new_value': 1165.1}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 71}]
2025-05-20 08:04:41,580 - INFO - 更新表单数据成功: FINST-6AG66W81PFFVII8NBSO518E6VPMT36XZC1QAM7M
2025-05-20 08:04:41,581 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250427, 变更字段: [{'field': 'recommendAmount', 'old_value': 2188.66, 'new_value': 2032.53}, {'field': 'amount', 'old_value': 2188.6600000000003, 'new_value': 2032.5300000000002}, {'field': 'count', 'old_value': 173, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 817.33, 'new_value': 661.2}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 80}]
2025-05-20 08:04:42,077 - INFO - 更新表单数据成功: FINST-X8D66N81GZFVKKRM94YJOD14IFW03RMH89PAM87
2025-05-20 08:04:42,077 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_20250504, 变更字段: [{'field': 'amount', 'old_value': 3477.4, 'new_value': 3935.4}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'onlineAmount', 'old_value': 257.6, 'new_value': 715.6}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 5}]
2025-05-20 08:04:42,559 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAMV
2025-05-20 08:04:42,560 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4550.69, 'new_value': 4523.79}, {'field': 'amount', 'old_value': 4550.69, 'new_value': 4523.79}, {'field': 'instoreAmount', 'old_value': 4384.84, 'new_value': 4357.94}]
2025-05-20 08:04:43,024 - INFO - 更新表单数据成功: FINST-RNA66D718CHV37BIEYQMM41S5MC73WRZTGRAMIK
2025-05-20 08:04:43,025 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250505, 变更字段: [{'field': 'recommendAmount', 'old_value': 6194.99, 'new_value': 6133.49}, {'field': 'amount', 'old_value': 6194.99, 'new_value': 6133.49}, {'field': 'instoreAmount', 'old_value': 5907.81, 'new_value': 5846.31}]
2025-05-20 08:04:43,487 - INFO - 更新表单数据成功: FINST-LLF66F71LFFV6KOC788B149S4LOP2885D1QAMGI
2025-05-20 08:04:43,487 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250504, 变更字段: [{'field': 'recommendAmount', 'old_value': 5079.06, 'new_value': 5059.16}, {'field': 'amount', 'old_value': 5079.06, 'new_value': 5059.160000000001}, {'field': 'instoreAmount', 'old_value': 4978.15, 'new_value': 4958.25}]
2025-05-20 08:04:43,918 - INFO - 更新表单数据成功: FINST-OJ966381SBGVT0PJEVQX2CURV3QD3VAK89PAM91
2025-05-20 08:04:43,918 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2398.47, 'new_value': 2356.09}, {'field': 'amount', 'old_value': 2398.4700000000003, 'new_value': 2356.09}, {'field': 'count', 'old_value': 178, 'new_value': 176}, {'field': 'instoreAmount', 'old_value': 872.78, 'new_value': 830.4}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 61}]
2025-05-20 08:04:44,473 - INFO - 更新表单数据成功: FINST-SED66Q617FHV83DJALKM26VLZ7WU21PHAWSAM6I
2025-05-20 08:04:44,474 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250506, 变更字段: [{'field': 'recommendAmount', 'old_value': 2706.92, 'new_value': 2674.54}, {'field': 'amount', 'old_value': 2706.92, 'new_value': 2674.54}, {'field': 'count', 'old_value': 199, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 1010.18, 'new_value': 977.8}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 83}]
2025-05-20 08:04:44,921 - INFO - 更新表单数据成功: FINST-RNA66D718CHV37BIEYQMM41S5MC73WRZTGRAMKK
2025-05-20 08:04:44,922 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250505, 变更字段: [{'field': 'recommendAmount', 'old_value': 3699.32, 'new_value': 3604.15}, {'field': 'amount', 'old_value': 3699.3199999999997, 'new_value': 3604.1499999999996}, {'field': 'count', 'old_value': 233, 'new_value': 229}, {'field': 'instoreAmount', 'old_value': 1854.07, 'new_value': 1758.9}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 120}]
2025-05-20 08:04:45,345 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAMZ2
2025-05-20 08:04:45,346 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250513, 变更字段: [{'field': 'recommendAmount', 'old_value': 4595.21, 'new_value': 4591.41}, {'field': 'amount', 'old_value': 4595.21, 'new_value': 4591.41}, {'field': 'instoreAmount', 'old_value': 4436.27, 'new_value': 4432.47}]
2025-05-20 08:04:45,865 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMDH
2025-05-20 08:04:45,866 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 3485.07, 'new_value': 3481.27}, {'field': 'amount', 'old_value': 3485.07, 'new_value': 3481.27}, {'field': 'instoreAmount', 'old_value': 3232.17, 'new_value': 3228.37}]
2025-05-20 08:04:46,316 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMH7
2025-05-20 08:04:46,317 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 6249.31, 'new_value': 6245.51}, {'field': 'amount', 'old_value': 6249.31, 'new_value': 6245.51}, {'field': 'instoreAmount', 'old_value': 6088.99, 'new_value': 6085.19}]
2025-05-20 08:04:46,751 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAMS2
2025-05-20 08:04:46,752 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2418.4, 'new_value': 2347.27}, {'field': 'amount', 'old_value': 2418.4, 'new_value': 2347.27}, {'field': 'count', 'old_value': 155, 'new_value': 151}, {'field': 'instoreAmount', 'old_value': 1166.53, 'new_value': 1095.4}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 66}]
2025-05-20 08:04:47,193 - INFO - 更新表单数据成功: FINST-NS866I91J1IVWW2PCXDO49OU89ZP35ASAWSAM33
2025-05-20 08:04:47,193 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250513, 变更字段: [{'field': 'recommendAmount', 'old_value': 2815.96, 'new_value': 2668.19}, {'field': 'amount', 'old_value': 2815.96, 'new_value': 2668.19}, {'field': 'count', 'old_value': 185, 'new_value': 176}, {'field': 'instoreAmount', 'old_value': 1250.07, 'new_value': 1102.3}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 67}]
2025-05-20 08:04:47,665 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMHH
2025-05-20 08:04:47,666 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 1823.66, 'new_value': 1775.79}, {'field': 'amount', 'old_value': 1823.66, 'new_value': 1775.79}, {'field': 'count', 'old_value': 115, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 723.78, 'new_value': 675.91}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 47}]
2025-05-20 08:04:48,018 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3UYFD1QAML7
2025-05-20 08:04:48,018 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250511, 变更字段: [{'field': 'recommendAmount', 'old_value': 3130.87, 'new_value': 3010.62}, {'field': 'amount', 'old_value': 3130.87, 'new_value': 3010.62}, {'field': 'count', 'old_value': 186, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 1626.79, 'new_value': 1506.54}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 92}]
2025-05-20 08:04:48,505 - INFO - 更新表单数据成功: FINST-6PF66691UBHVRYF3EOUJHC43R95U2MLUQBUAMFS
2025-05-20 08:04:48,505 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4155.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4155.6}, {'field': 'amount', 'old_value': 386.0, 'new_value': 554.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 386.0, 'new_value': 554.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-05-20 08:04:48,949 - INFO - 更新表单数据成功: FINST-6PF66691UBHVRYF3EOUJHC43R95U2MLUQBUAMWT
2025-05-20 08:04:48,950 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_20250518, 变更字段: [{'field': 'amount', 'old_value': 1596.4, 'new_value': 1821.6}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 1596.4, 'new_value': 1821.6}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-05-20 08:04:49,572 - INFO - 更新表单数据成功: FINST-6PF66691UBHVRYF3EOUJHC43R95U2MLUQBUAMNU
2025-05-20 08:04:49,572 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_20250518, 变更字段: [{'field': 'amount', 'old_value': 4425.08, 'new_value': 4458.2}, {'field': 'count', 'old_value': 167, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 4446.08, 'new_value': 4479.2}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 170}]
2025-05-20 08:04:50,066 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3N8XQBUAMLH
2025-05-20 08:04:50,066 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_20250518, 变更字段: [{'field': 'amount', 'old_value': 880.03, 'new_value': 880.04}, {'field': 'count', 'old_value': 32, 'new_value': 33}, {'field': 'onlineAmount', 'old_value': 754.73, 'new_value': 754.74}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 27}]
2025-05-20 08:04:50,528 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMW6
2025-05-20 08:04:50,529 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_20250516, 变更字段: [{'field': 'amount', 'old_value': 3809.0, 'new_value': 5379.0}, {'field': 'count', 'old_value': 9, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 3809.0, 'new_value': 5379.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-05-20 08:04:50,949 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3N8XQBUAMTH
2025-05-20 08:04:50,949 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_20250518, 变更字段: [{'field': 'instoreAmount', 'old_value': 12246.7, 'new_value': 12208.7}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 314}, {'field': 'onlineAmount', 'old_value': 732.5, 'new_value': 770.5}, {'field': 'onlineCount', 'old_value': 18, 'new_value': 20}]
2025-05-20 08:04:51,359 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3N8XQBUAMYH
2025-05-20 08:04:51,359 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16565.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16565.9}]
2025-05-20 08:04:51,781 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3N8XQBUAM2I
2025-05-20 08:04:51,781 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_20250518, 变更字段: [{'field': 'amount', 'old_value': 4553.8, 'new_value': 4726.7}, {'field': 'count', 'old_value': 14, 'new_value': 15}, {'field': 'instoreAmount', 'old_value': 4553.8, 'new_value': 4726.7}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-05-20 08:04:52,257 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3N8XQBUAMDI
2025-05-20 08:04:52,257 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4408.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4408.6}, {'field': 'amount', 'old_value': 509.0, 'new_value': 1333.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 509.0, 'new_value': 1333.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-05-20 08:04:52,733 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3O8XQBUAMGJ
2025-05-20 08:04:52,734 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 3118.82, 'new_value': 3125.42}, {'field': 'amount', 'old_value': 3118.8199999999997, 'new_value': 3125.4199999999996}, {'field': 'count', 'old_value': 176, 'new_value': 177}, {'field': 'onlineAmount', 'old_value': 2289.06, 'new_value': 2295.66}, {'field': 'onlineCount', 'old_value': 103, 'new_value': 104}]
2025-05-20 08:04:53,197 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3O8XQBUAMVJ
2025-05-20 08:04:53,198 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250518, 变更字段: [{'field': 'amount', 'old_value': 10599.99, 'new_value': 10619.29}, {'field': 'count', 'old_value': 234, 'new_value': 235}, {'field': 'onlineAmount', 'old_value': 449.9, 'new_value': 469.2}, {'field': 'onlineCount', 'old_value': 9, 'new_value': 10}]
2025-05-20 08:04:53,652 - INFO - 更新表单数据成功: FINST-OIF66RB166JVHL8L82N5R9Q6PWVG3O8XQBUAMZJ
2025-05-20 08:04:53,653 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_20250518, 变更字段: [{'field': 'amount', 'old_value': 2383.0, 'new_value': 2901.0}, {'field': 'count', 'old_value': 24, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 2383.0, 'new_value': 2901.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 25}]
2025-05-20 08:04:54,132 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3HYZQBUAMNU
2025-05-20 08:04:54,132 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 11410.18, 'new_value': 11439.98}, {'field': 'amount', 'old_value': 11410.18, 'new_value': 11439.98}, {'field': 'count', 'old_value': 192, 'new_value': 193}, {'field': 'onlineAmount', 'old_value': 1017.54, 'new_value': 1047.34}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 17}]
2025-05-20 08:04:54,613 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3HYZQBUAMPU
2025-05-20 08:04:54,614 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250518, 变更字段: [{'field': 'amount', 'old_value': 7224.620000000001, 'new_value': 7277.47}, {'field': 'count', 'old_value': 329, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 4004.02, 'new_value': 4010.77}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 187}, {'field': 'onlineAmount', 'old_value': 3305.5, 'new_value': 3351.6}, {'field': 'onlineCount', 'old_value': 143, 'new_value': 144}]
2025-05-20 08:04:55,067 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3HYZQBUAMXU
2025-05-20 08:04:55,068 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 1796.27, 'new_value': 1812.22}, {'field': 'amount', 'old_value': 1796.27, 'new_value': 1812.22}, {'field': 'count', 'old_value': 116, 'new_value': 117}, {'field': 'onlineAmount', 'old_value': 782.02, 'new_value': 797.97}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 62}]
2025-05-20 08:04:55,493 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAM6V
2025-05-20 08:04:55,493 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 5121.07, 'new_value': 5181.97}, {'field': 'amount', 'old_value': 5121.07, 'new_value': 5181.97}, {'field': 'count', 'old_value': 268, 'new_value': 270}, {'field': 'instoreAmount', 'old_value': 1709.89, 'new_value': 1756.19}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 91}, {'field': 'onlineAmount', 'old_value': 3559.18, 'new_value': 3573.78}, {'field': 'onlineCount', 'old_value': 178, 'new_value': 179}]
2025-05-20 08:04:55,957 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAM9V
2025-05-20 08:04:55,957 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 696.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 696.0}]
2025-05-20 08:04:56,561 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMBV
2025-05-20 08:04:56,561 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_20250518, 变更字段: [{'field': 'amount', 'old_value': 7942.4, 'new_value': 7943.53}, {'field': 'count', 'old_value': 134, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 4385.95, 'new_value': 4387.08}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-05-20 08:04:57,261 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMHV
2025-05-20 08:04:57,261 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15213.55}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15213.55}]
2025-05-20 08:04:57,698 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMQV
2025-05-20 08:04:57,698 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_20250518, 变更字段: [{'field': 'amount', 'old_value': 28924.01, 'new_value': 29159.01}, {'field': 'count', 'old_value': 295, 'new_value': 296}, {'field': 'instoreAmount', 'old_value': 22473.2, 'new_value': 22708.2}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 148}]
2025-05-20 08:04:58,158 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMTV
2025-05-20 08:04:58,158 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_20250518, 变更字段: [{'field': 'amount', 'old_value': 42282.09, 'new_value': 44590.09}, {'field': 'count', 'old_value': 277, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 27815.7, 'new_value': 30123.7}, {'field': 'instoreCount', 'old_value': 146, 'new_value': 154}]
2025-05-20 08:04:58,624 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAM2W
2025-05-20 08:04:58,625 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250518, 变更字段: [{'field': 'amount', 'old_value': 62700.57, 'new_value': 62738.770000000004}, {'field': 'count', 'old_value': 356, 'new_value': 358}, {'field': 'onlineAmount', 'old_value': 15080.4, 'new_value': 15118.6}, {'field': 'onlineCount', 'old_value': 155, 'new_value': 157}]
2025-05-20 08:04:59,077 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAM7W
2025-05-20 08:04:59,077 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_20250518, 变更字段: [{'field': 'amount', 'old_value': 18392.32, 'new_value': 18576.22}, {'field': 'count', 'old_value': 166, 'new_value': 169}, {'field': 'onlineAmount', 'old_value': 5133.22, 'new_value': 5317.12}, {'field': 'onlineCount', 'old_value': 105, 'new_value': 108}]
2025-05-20 08:04:59,498 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMDW
2025-05-20 08:04:59,498 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 129.0, 'new_value': 258.0}, {'field': 'amount', 'old_value': 129.0, 'new_value': 258.0}, {'field': 'count', 'old_value': 9, 'new_value': 10}, {'field': 'instoreAmount', 'old_value': 129.0, 'new_value': 258.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 10}]
2025-05-20 08:05:00,027 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMEW
2025-05-20 08:05:00,028 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250518, 变更字段: [{'field': 'amount', 'old_value': 5439.62, 'new_value': 5450.62}, {'field': 'count', 'old_value': 319, 'new_value': 321}, {'field': 'instoreAmount', 'old_value': 3296.32, 'new_value': 3307.32}, {'field': 'instoreCount', 'old_value': 202, 'new_value': 204}]
2025-05-20 08:05:00,600 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMNW
2025-05-20 08:05:00,601 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250518, 变更字段: [{'field': 'amount', 'old_value': 239.9, 'new_value': 1743.79}, {'field': 'count', 'old_value': 23, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 240.5, 'new_value': 1823.1}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 181}]
2025-05-20 08:05:01,197 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAM6X
2025-05-20 08:05:01,197 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250518, 变更字段: [{'field': 'amount', 'old_value': 2640.95, 'new_value': 2704.65}, {'field': 'count', 'old_value': 28, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 2481.99, 'new_value': 2545.69}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-05-20 08:05:01,653 - INFO - 更新表单数据成功: FINST-OLF665813CHVCFU16O00B4PIBE3X3IYZQBUAMBX
2025-05-20 08:05:01,654 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 1905.0, 'new_value': 2083.0}, {'field': 'amount', 'old_value': 1905.0, 'new_value': 2083.0}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 1905.0, 'new_value': 2083.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 14}]
2025-05-20 08:05:02,137 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU925O2RBUAMC3
2025-05-20 08:05:02,137 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_20250518, 变更字段: [{'field': 'amount', 'old_value': 2030.0, 'new_value': 2325.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 2030.0, 'new_value': 2325.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-05-20 08:05:02,583 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMP3
2025-05-20 08:05:02,584 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_20250518, 变更字段: [{'field': 'amount', 'old_value': 28597.0, 'new_value': 42795.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 28597.0, 'new_value': 42795.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-05-20 08:05:03,098 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMV3
2025-05-20 08:05:03,100 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4318.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4318.0}]
2025-05-20 08:05:03,507 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMY3
2025-05-20 08:05:03,508 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8100.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8100.1}]
2025-05-20 08:05:04,085 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAM14
2025-05-20 08:05:04,086 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_20250518, 变更字段: [{'field': 'amount', 'old_value': 31409.0, 'new_value': 33565.0}, {'field': 'count', 'old_value': 22, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 37208.0, 'new_value': 39364.0}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 24}]
2025-05-20 08:05:04,544 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAM74
2025-05-20 08:05:04,544 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 20896.0}, {'field': 'amount', 'old_value': 0.0, 'new_value': 20896.0}, {'field': 'count', 'old_value': 2, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 20896.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 12}]
2025-05-20 08:05:05,013 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMC4
2025-05-20 08:05:05,014 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250518, 变更字段: [{'field': 'amount', 'old_value': 4948.86, 'new_value': 5000.5599999999995}, {'field': 'count', 'old_value': 322, 'new_value': 327}, {'field': 'onlineAmount', 'old_value': 4710.16, 'new_value': 4761.86}, {'field': 'onlineCount', 'old_value': 288, 'new_value': 293}]
2025-05-20 08:05:05,514 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAME4
2025-05-20 08:05:05,515 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_20250518, 变更字段: [{'field': 'amount', 'old_value': 7297.549999999999, 'new_value': 7301.650000000001}, {'field': 'count', 'old_value': 938, 'new_value': 940}, {'field': 'instoreAmount', 'old_value': 8719.69, 'new_value': 8730.49}, {'field': 'instoreCount', 'old_value': 928, 'new_value': 930}]
2025-05-20 08:05:05,985 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMJ4
2025-05-20 08:05:05,986 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_20250518, 变更字段: [{'field': 'amount', 'old_value': 3485.46, 'new_value': 3512.06}, {'field': 'count', 'old_value': 188, 'new_value': 189}, {'field': 'onlineAmount', 'old_value': 2781.51, 'new_value': 2808.11}, {'field': 'onlineCount', 'old_value': 130, 'new_value': 131}]
2025-05-20 08:05:06,430 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMO4
2025-05-20 08:05:06,431 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 5733.8, 'new_value': 5800.8}, {'field': 'amount', 'old_value': 5733.8, 'new_value': 5800.8}, {'field': 'count', 'old_value': 263, 'new_value': 265}, {'field': 'onlineAmount', 'old_value': 2710.1, 'new_value': 2777.1}, {'field': 'onlineCount', 'old_value': 98, 'new_value': 100}]
2025-05-20 08:05:07,090 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMR4
2025-05-20 08:05:07,091 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_20250518, 变更字段: [{'field': 'amount', 'old_value': 15893.5, 'new_value': 16099.8}, {'field': 'count', 'old_value': 468, 'new_value': 469}, {'field': 'instoreAmount', 'old_value': 15949.1, 'new_value': 16155.4}, {'field': 'instoreCount', 'old_value': 468, 'new_value': 469}]
2025-05-20 08:05:07,602 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMT4
2025-05-20 08:05:07,602 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250518, 变更字段: [{'field': 'amount', 'old_value': 7157.77, 'new_value': 7141.7699999999995}, {'field': 'count', 'old_value': 516, 'new_value': 517}, {'field': 'instoreAmount', 'old_value': 5931.22, 'new_value': 6014.89}, {'field': 'instoreCount', 'old_value': 398, 'new_value': 409}, {'field': 'onlineAmount', 'old_value': 1661.58, 'new_value': 1583.51}, {'field': 'onlineCount', 'old_value': 118, 'new_value': 108}]
2025-05-20 08:05:08,092 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMZ4
2025-05-20 08:05:08,092 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250518, 变更字段: [{'field': 'amount', 'old_value': 23941.48, 'new_value': 24018.13}, {'field': 'count', 'old_value': 466, 'new_value': 467}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 76.65}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-05-20 08:05:08,545 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAM85
2025-05-20 08:05:08,545 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 12684.17, 'new_value': 12734.57}, {'field': 'amount', 'old_value': 12684.169999999998, 'new_value': 12734.57}, {'field': 'count', 'old_value': 156, 'new_value': 157}, {'field': 'onlineAmount', 'old_value': 5261.74, 'new_value': 5312.14}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 63}]
2025-05-20 08:05:09,004 - INFO - 更新表单数据成功: FINST-OLF66581UBHV59FJ6IZNV5BSOF733TJAUGRAMQG
2025-05-20 08:05:09,005 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_20250516, 变更字段: [{'field': 'amount', 'old_value': 3694.0, 'new_value': 4400.0}, {'field': 'count', 'old_value': 29, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 3694.0, 'new_value': 4400.0}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 30}]
2025-05-20 08:05:09,436 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU926O2RBUAMT5
2025-05-20 08:05:09,436 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_20250518, 变更字段: [{'field': 'amount', 'old_value': 11548.5, 'new_value': 11856.5}, {'field': 'count', 'old_value': 89, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 5224.8, 'new_value': 5532.8}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 34}]
2025-05-20 08:05:09,941 - INFO - 更新表单数据成功: FINST-I3F66991OGJVUUR27XIT86QE9EU927O2RBUAM16
2025-05-20 08:05:09,942 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 4393.31, 'new_value': 4371.91}, {'field': 'amount', 'old_value': 4393.3099999999995, 'new_value': 4371.91}]
2025-05-20 08:05:10,340 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAM5C
2025-05-20 08:05:10,340 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250518, 变更字段: [{'field': 'amount', 'old_value': 24628.85, 'new_value': 25840.83}, {'field': 'count', 'old_value': 254, 'new_value': 259}, {'field': 'instoreAmount', 'old_value': 22634.64, 'new_value': 23846.62}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 171}]
2025-05-20 08:05:10,808 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAMGC
2025-05-20 08:05:10,809 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 8730.0, 'new_value': 20956.0}, {'field': 'dailyBillAmount', 'old_value': 8730.0, 'new_value': 20956.0}]
2025-05-20 08:05:11,256 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAMOC
2025-05-20 08:05:11,256 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250518, 变更字段: [{'field': 'recommendAmount', 'old_value': 32985.9, 'new_value': 32929.42}, {'field': 'dailyBillAmount', 'old_value': 32985.9, 'new_value': 32929.42}]
2025-05-20 08:05:11,699 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAM6D
2025-05-20 08:05:11,699 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250518, 变更字段: [{'field': 'amount', 'old_value': 29051.3, 'new_value': 29077.8}, {'field': 'count', 'old_value': 164, 'new_value': 165}, {'field': 'instoreAmount', 'old_value': 25624.4, 'new_value': 25650.9}, {'field': 'instoreCount', 'old_value': 133, 'new_value': 134}]
2025-05-20 08:05:12,183 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAMGD
2025-05-20 08:05:12,184 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_20250518, 变更字段: [{'field': 'amount', 'old_value': 30092.3, 'new_value': 30338.3}, {'field': 'count', 'old_value': 174, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 17648.0, 'new_value': 17894.0}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 117}]
2025-05-20 08:05:12,658 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAM9E
2025-05-20 08:05:12,658 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_20250518, 变更字段: [{'field': 'amount', 'old_value': 1058.94, 'new_value': 1051.04}]
2025-05-20 08:05:12,733 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-20 08:05:13,243 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-20 08:05:16,247 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-20 08:05:16,731 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-20 08:05:19,735 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-20 08:05:20,208 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-20 08:05:23,211 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-20 08:05:23,568 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-20 08:05:26,571 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-20 08:05:26,978 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-20 08:05:29,981 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-20 08:05:30,470 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-20 08:05:33,474 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-20 08:05:33,877 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-20 08:05:36,880 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-20 08:05:37,272 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-20 08:05:40,275 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-20 08:05:40,663 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-20 08:05:43,666 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-20 08:05:44,090 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-20 08:05:47,093 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-20 08:05:47,486 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-20 08:05:50,489 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-20 08:05:50,950 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-20 08:05:53,953 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-20 08:05:54,367 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-20 08:05:57,371 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-20 08:05:57,788 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-20 08:06:00,791 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-20 08:06:01,255 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-20 08:06:04,258 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-20 08:06:04,730 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-20 08:06:07,734 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-20 08:06:08,172 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-20 08:06:11,175 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-20 08:06:11,573 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-20 08:06:14,576 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-20 08:06:14,994 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-20 08:06:17,997 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-20 08:06:18,407 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-20 08:06:21,410 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-20 08:06:21,841 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-20 08:06:24,844 - INFO - 正在批量插入每日数据，批次 22/22，共 87 条记录
2025-05-20 08:06:25,246 - INFO - 批量插入每日数据成功，批次 22，87 条记录
2025-05-20 08:06:28,246 - INFO - 批量插入每日数据完成: 总计 2187 条，成功 2187 条，失败 0 条
2025-05-20 08:06:28,250 - INFO - 批量插入日销售数据完成，共 2187 条记录
2025-05-20 08:06:28,250 - INFO - 日销售数据同步完成！更新: 75 条，插入: 2187 条，错误: 0 条，跳过: 10847 条
2025-05-20 08:06:28,250 - INFO - 正在获取宜搭月销售表单数据...
2025-05-20 08:06:28,250 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-20 08:06:28,250 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-20 08:06:28,251 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:28,251 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:28,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:28,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:29,059 - INFO - API请求耗时: 807ms
2025-05-20 08:06:29,059 - INFO - Response - Page 1
2025-05-20 08:06:29,060 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:29,060 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:29,060 - WARNING - 月度分段 1 查询返回空数据
2025-05-20 08:06:29,060 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-20 08:06:29,060 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:29,060 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:29,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:29,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:29,299 - INFO - API请求耗时: 238ms
2025-05-20 08:06:29,300 - INFO - Response - Page 1
2025-05-20 08:06:29,300 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:29,301 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:29,301 - WARNING - 单月查询返回空数据: 2024-05
2025-05-20 08:06:29,801 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-20 08:06:29,801 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:29,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:29,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:30,009 - INFO - API请求耗时: 207ms
2025-05-20 08:06:30,010 - INFO - Response - Page 1
2025-05-20 08:06:30,010 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:30,010 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:30,011 - WARNING - 单月查询返回空数据: 2024-06
2025-05-20 08:06:30,511 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:30,511 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:30,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:30,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:30,736 - INFO - API请求耗时: 224ms
2025-05-20 08:06:30,736 - INFO - Response - Page 1
2025-05-20 08:06:30,737 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:30,737 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:30,738 - WARNING - 单月查询返回空数据: 2024-07
2025-05-20 08:06:32,239 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-20 08:06:32,239 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:32,240 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:32,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:32,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:32,458 - INFO - API请求耗时: 219ms
2025-05-20 08:06:32,458 - INFO - Response - Page 1
2025-05-20 08:06:32,459 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:32,459 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:32,459 - WARNING - 月度分段 2 查询返回空数据
2025-05-20 08:06:32,459 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-20 08:06:32,460 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:32,460 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:32,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:32,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:32,692 - INFO - API请求耗时: 231ms
2025-05-20 08:06:32,692 - INFO - Response - Page 1
2025-05-20 08:06:32,693 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:32,693 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:32,693 - WARNING - 单月查询返回空数据: 2024-08
2025-05-20 08:06:33,193 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-20 08:06:33,193 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:33,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:33,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:33,425 - INFO - API请求耗时: 231ms
2025-05-20 08:06:33,426 - INFO - Response - Page 1
2025-05-20 08:06:33,426 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:33,427 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:33,427 - WARNING - 单月查询返回空数据: 2024-09
2025-05-20 08:06:33,928 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:33,928 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:33,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:33,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:34,139 - INFO - API请求耗时: 210ms
2025-05-20 08:06:34,140 - INFO - Response - Page 1
2025-05-20 08:06:34,140 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-20 08:06:34,140 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 08:06:34,141 - WARNING - 单月查询返回空数据: 2024-10
2025-05-20 08:06:35,642 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-20 08:06:35,642 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:35,643 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:35,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:35,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:36,255 - INFO - API请求耗时: 611ms
2025-05-20 08:06:36,256 - INFO - Response - Page 1
2025-05-20 08:06:36,257 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:06:36,758 - INFO - Request Parameters - Page 2:
2025-05-20 08:06:36,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:36,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:37,289 - INFO - API请求耗时: 530ms
2025-05-20 08:06:37,290 - INFO - Response - Page 2
2025-05-20 08:06:37,290 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:06:37,790 - INFO - Request Parameters - Page 3:
2025-05-20 08:06:37,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:37,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:38,264 - INFO - API请求耗时: 473ms
2025-05-20 08:06:38,264 - INFO - Response - Page 3
2025-05-20 08:06:38,265 - INFO - 第 3 页获取到 48 条记录
2025-05-20 08:06:38,265 - INFO - 查询完成，共获取到 248 条记录
2025-05-20 08:06:38,265 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-20 08:06:39,265 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-20 08:06:39,265 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-20 08:06:39,266 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:39,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:39,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:39,847 - INFO - API请求耗时: 581ms
2025-05-20 08:06:39,848 - INFO - Response - Page 1
2025-05-20 08:06:39,848 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:06:40,349 - INFO - Request Parameters - Page 2:
2025-05-20 08:06:40,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:40,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:40,899 - INFO - API请求耗时: 549ms
2025-05-20 08:06:40,900 - INFO - Response - Page 2
2025-05-20 08:06:40,901 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:06:41,402 - INFO - Request Parameters - Page 3:
2025-05-20 08:06:41,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:41,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:42,160 - INFO - API请求耗时: 758ms
2025-05-20 08:06:42,161 - INFO - Response - Page 3
2025-05-20 08:06:42,161 - INFO - 第 3 页获取到 100 条记录
2025-05-20 08:06:42,662 - INFO - Request Parameters - Page 4:
2025-05-20 08:06:42,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:42,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:43,226 - INFO - API请求耗时: 562ms
2025-05-20 08:06:43,226 - INFO - Response - Page 4
2025-05-20 08:06:43,227 - INFO - 第 4 页获取到 100 条记录
2025-05-20 08:06:43,728 - INFO - Request Parameters - Page 5:
2025-05-20 08:06:43,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:43,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:44,331 - INFO - API请求耗时: 603ms
2025-05-20 08:06:44,331 - INFO - Response - Page 5
2025-05-20 08:06:44,332 - INFO - 第 5 页获取到 100 条记录
2025-05-20 08:06:44,833 - INFO - Request Parameters - Page 6:
2025-05-20 08:06:44,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:44,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:45,375 - INFO - API请求耗时: 541ms
2025-05-20 08:06:45,376 - INFO - Response - Page 6
2025-05-20 08:06:45,377 - INFO - 第 6 页获取到 100 条记录
2025-05-20 08:06:45,878 - INFO - Request Parameters - Page 7:
2025-05-20 08:06:45,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:45,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:46,444 - INFO - API请求耗时: 565ms
2025-05-20 08:06:46,444 - INFO - Response - Page 7
2025-05-20 08:06:46,445 - INFO - 第 7 页获取到 100 条记录
2025-05-20 08:06:46,946 - INFO - Request Parameters - Page 8:
2025-05-20 08:06:46,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:46,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:47,277 - INFO - API请求耗时: 329ms
2025-05-20 08:06:47,277 - INFO - Response - Page 8
2025-05-20 08:06:47,278 - INFO - 第 8 页获取到 16 条记录
2025-05-20 08:06:47,278 - INFO - 查询完成，共获取到 716 条记录
2025-05-20 08:06:47,278 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-20 08:06:48,279 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-20 08:06:48,279 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-20 08:06:48,280 - INFO - Request Parameters - Page 1:
2025-05-20 08:06:48,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:48,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:48,751 - INFO - API请求耗时: 469ms
2025-05-20 08:06:48,751 - INFO - Response - Page 1
2025-05-20 08:06:48,751 - INFO - 第 1 页获取到 100 条记录
2025-05-20 08:06:49,253 - INFO - Request Parameters - Page 2:
2025-05-20 08:06:49,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:49,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:49,786 - INFO - API请求耗时: 532ms
2025-05-20 08:06:49,787 - INFO - Response - Page 2
2025-05-20 08:06:49,787 - INFO - 第 2 页获取到 100 条记录
2025-05-20 08:06:50,289 - INFO - Request Parameters - Page 3:
2025-05-20 08:06:50,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:06:50,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:06:50,649 - INFO - API请求耗时: 358ms
2025-05-20 08:06:50,650 - INFO - Response - Page 3
2025-05-20 08:06:50,650 - INFO - 第 3 页获取到 24 条记录
2025-05-20 08:06:50,651 - INFO - 查询完成，共获取到 224 条记录
2025-05-20 08:06:50,651 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-20 08:06:51,651 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-20 08:06:51,651 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-20 08:06:51,651 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-20 08:06:51,651 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-20 08:06:51,661 - INFO - 成功获取SQLite月度汇总数据，共 1190 条记录
2025-05-20 08:06:51,719 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-20 08:06:52,274 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-20 08:06:52,275 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126426.29, 'new_value': 131746.76}, {'field': 'dailyBillAmount', 'old_value': 126426.29, 'new_value': 131746.76}, {'field': 'amount', 'old_value': 3834.7, 'new_value': 4133.0}, {'field': 'count', 'old_value': 51, 'new_value': 56}, {'field': 'onlineAmount', 'old_value': 3910.7, 'new_value': 4209.0}, {'field': 'onlineCount', 'old_value': 51, 'new_value': 56}]
2025-05-20 08:06:52,694 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-20 08:06:52,694 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 336616.61, 'new_value': 350741.47}, {'field': 'dailyBillAmount', 'old_value': 336616.61, 'new_value': 350741.47}, {'field': 'amount', 'old_value': 180628.0, 'new_value': 191938.0}, {'field': 'count', 'old_value': 1635, 'new_value': 1755}, {'field': 'instoreAmount', 'old_value': 72067.7, 'new_value': 77769.1}, {'field': 'instoreCount', 'old_value': 515, 'new_value': 571}, {'field': 'onlineAmount', 'old_value': 108851.7, 'new_value': 114460.8}, {'field': 'onlineCount', 'old_value': 1120, 'new_value': 1184}]
2025-05-20 08:06:53,210 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-20 08:06:53,210 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 487720.62, 'new_value': 516183.18}, {'field': 'dailyBillAmount', 'old_value': 487720.62, 'new_value': 516183.18}, {'field': 'amount', 'old_value': 363813.9, 'new_value': 381282.72}, {'field': 'count', 'old_value': 1760, 'new_value': 1840}, {'field': 'instoreAmount', 'old_value': 363813.9, 'new_value': 381282.72}, {'field': 'instoreCount', 'old_value': 1760, 'new_value': 1840}]
2025-05-20 08:06:53,661 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-20 08:06:53,661 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 394501.24, 'new_value': 405950.18}, {'field': 'dailyBillAmount', 'old_value': 394501.24, 'new_value': 405950.18}, {'field': 'amount', 'old_value': 625509.0, 'new_value': 653436.0}, {'field': 'count', 'old_value': 2147, 'new_value': 2236}, {'field': 'instoreAmount', 'old_value': 626759.0, 'new_value': 654686.0}, {'field': 'instoreCount', 'old_value': 2147, 'new_value': 2236}]
2025-05-20 08:06:54,112 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-20 08:06:54,112 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49834.2, 'new_value': 50091.3}, {'field': 'dailyBillAmount', 'old_value': 49834.2, 'new_value': 50091.3}, {'field': 'amount', 'old_value': 65932.71, 'new_value': 66779.11}, {'field': 'count', 'old_value': 210, 'new_value': 218}, {'field': 'onlineAmount', 'old_value': 30948.72, 'new_value': 31795.52}, {'field': 'onlineCount', 'old_value': 174, 'new_value': 182}]
2025-05-20 08:06:54,561 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-20 08:06:54,562 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 191425.9, 'new_value': 193305.9}, {'field': 'amount', 'old_value': 191425.9, 'new_value': 193305.9}, {'field': 'count', 'old_value': 97, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 191425.9, 'new_value': 193305.9}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 100}]
2025-05-20 08:06:55,054 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-20 08:06:55,054 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 540814.56, 'new_value': 564971.13}, {'field': 'dailyBillAmount', 'old_value': 540814.56, 'new_value': 564971.13}, {'field': 'amount', 'old_value': 515692.76, 'new_value': 530679.61}, {'field': 'count', 'old_value': 3703, 'new_value': 3830}, {'field': 'instoreAmount', 'old_value': 414437.26, 'new_value': 426993.01}, {'field': 'instoreCount', 'old_value': 1761, 'new_value': 1827}, {'field': 'onlineAmount', 'old_value': 104530.17, 'new_value': 107021.17}, {'field': 'onlineCount', 'old_value': 1942, 'new_value': 2003}]
2025-05-20 08:06:55,506 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-20 08:06:55,507 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 549995.07, 'new_value': 570476.73}, {'field': 'dailyBillAmount', 'old_value': 549995.07, 'new_value': 570476.73}, {'field': 'amount', 'old_value': 120642.89, 'new_value': 130948.76}, {'field': 'count', 'old_value': 620, 'new_value': 691}, {'field': 'instoreAmount', 'old_value': 120642.89, 'new_value': 130948.76}, {'field': 'instoreCount', 'old_value': 620, 'new_value': 691}]
2025-05-20 08:06:56,071 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MOH
2025-05-20 08:06:56,072 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 1536608.72, 'new_value': 1536925.72}, {'field': 'amount', 'old_value': 1052325.28, 'new_value': 1052968.54}, {'field': 'count', 'old_value': 2489, 'new_value': 2491}, {'field': 'instoreAmount', 'old_value': 1350874.31, 'new_value': 1351518.11}, {'field': 'instoreCount', 'old_value': 2486, 'new_value': 2488}]
2025-05-20 08:06:56,497 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-20 08:06:56,498 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 17137.0, 'new_value': 19100.0}, {'field': 'count', 'old_value': 24, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 17137.0, 'new_value': 19100.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 26}]
2025-05-20 08:06:56,945 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-20 08:06:56,946 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 85776.3, 'new_value': 87308.3}, {'field': 'count', 'old_value': 240, 'new_value': 246}, {'field': 'instoreAmount', 'old_value': 85777.1, 'new_value': 87309.1}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 246}]
2025-05-20 08:06:57,372 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-20 08:06:57,372 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 764399.5, 'new_value': 783136.36}, {'field': 'dailyBillAmount', 'old_value': 764399.5, 'new_value': 783136.36}, {'field': 'amount', 'old_value': -285788.57, 'new_value': -277940.96}, {'field': 'count', 'old_value': 806, 'new_value': 853}, {'field': 'instoreAmount', 'old_value': 509776.22, 'new_value': 528513.08}, {'field': 'instoreCount', 'old_value': 806, 'new_value': 853}]
2025-05-20 08:06:57,806 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-20 08:06:57,806 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 269672.0, 'new_value': 288045.0}, {'field': 'amount', 'old_value': 269672.0, 'new_value': 288045.0}, {'field': 'count', 'old_value': 1013, 'new_value': 1054}, {'field': 'instoreAmount', 'old_value': 269672.0, 'new_value': 288045.0}, {'field': 'instoreCount', 'old_value': 1013, 'new_value': 1054}]
2025-05-20 08:06:58,297 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-20 08:06:58,297 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 305361.24, 'new_value': 318871.72000000003}, {'field': 'amount', 'old_value': 305361.24, 'new_value': 318871.72000000003}, {'field': 'count', 'old_value': 1064, 'new_value': 1102}, {'field': 'instoreAmount', 'old_value': 305361.24, 'new_value': 318871.72000000003}, {'field': 'instoreCount', 'old_value': 1064, 'new_value': 1102}]
2025-05-20 08:06:58,743 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-20 08:06:58,743 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 144744.25, 'new_value': 149152.85}, {'field': 'dailyBillAmount', 'old_value': 144744.25, 'new_value': 149152.85}, {'field': 'amount', 'old_value': 10209.3, 'new_value': 11033.3}, {'field': 'count', 'old_value': 78, 'new_value': 79}, {'field': 'instoreAmount', 'old_value': 12103.2, 'new_value': 12927.2}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 79}]
2025-05-20 08:06:59,249 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-20 08:06:59,249 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78682.02, 'new_value': 81721.92}, {'field': 'dailyBillAmount', 'old_value': 78682.02, 'new_value': 81721.92}, {'field': 'amount', 'old_value': 50113.12, 'new_value': 51966.97}, {'field': 'count', 'old_value': 735, 'new_value': 758}, {'field': 'instoreAmount', 'old_value': 51951.22, 'new_value': 53864.87}, {'field': 'instoreCount', 'old_value': 735, 'new_value': 758}]
2025-05-20 08:06:59,685 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-20 08:06:59,685 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105619.56999999999, 'new_value': 111629.83}, {'field': 'dailyBillAmount', 'old_value': 60614.67, 'new_value': 66428.73}, {'field': 'amount', 'old_value': 105618.70999999999, 'new_value': 111628.97}, {'field': 'count', 'old_value': 3669, 'new_value': 3884}, {'field': 'instoreAmount', 'old_value': 93120.18, 'new_value': 98269.74}, {'field': 'instoreCount', 'old_value': 3323, 'new_value': 3521}, {'field': 'onlineAmount', 'old_value': 12499.39, 'new_value': 13360.09}, {'field': 'onlineCount', 'old_value': 346, 'new_value': 363}]
2025-05-20 08:07:00,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-20 08:07:00,240 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 176313.22, 'new_value': 219988.22}, {'field': 'dailyBillAmount', 'old_value': 172855.0, 'new_value': 214802.0}, {'field': 'amount', 'old_value': 139740.22, 'new_value': 183415.22}, {'field': 'count', 'old_value': 152, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 139611.0, 'new_value': 183286.0}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 173}]
2025-05-20 08:07:00,637 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-20 08:07:00,637 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 406236.34, 'new_value': 429365.68}, {'field': 'dailyBillAmount', 'old_value': 405731.79, 'new_value': 428861.13}, {'field': 'amount', 'old_value': 406236.34, 'new_value': 429365.68}, {'field': 'count', 'old_value': 358, 'new_value': 376}, {'field': 'instoreAmount', 'old_value': 406237.34, 'new_value': 429366.68}, {'field': 'instoreCount', 'old_value': 358, 'new_value': 376}]
2025-05-20 08:07:01,157 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-20 08:07:01,157 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 33395.0, 'new_value': 33655.0}, {'field': 'count', 'old_value': 51, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 33395.0, 'new_value': 33655.0}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 53}]
2025-05-20 08:07:01,493 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-20 08:07:01,493 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81269.5, 'new_value': 83777.2}, {'field': 'dailyBillAmount', 'old_value': 81269.5, 'new_value': 83777.2}, {'field': 'amount', 'old_value': 90615.0, 'new_value': 93914.9}, {'field': 'count', 'old_value': 234, 'new_value': 244}, {'field': 'instoreAmount', 'old_value': 90620.2, 'new_value': 93920.8}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 244}]
2025-05-20 08:07:01,959 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-20 08:07:01,960 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122435.87, 'new_value': 129023.87}, {'field': 'amount', 'old_value': 122435.87, 'new_value': 129023.87}, {'field': 'count', 'old_value': 144, 'new_value': 152}, {'field': 'instoreAmount', 'old_value': 122562.87, 'new_value': 129150.87}, {'field': 'instoreCount', 'old_value': 144, 'new_value': 152}]
2025-05-20 08:07:02,350 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-20 08:07:02,350 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 157467.91999999998, 'new_value': 180394.72}, {'field': 'dailyBillAmount', 'old_value': 157467.91999999998, 'new_value': 180394.72}, {'field': 'amount', 'old_value': 183474.95, 'new_value': 189982.85}, {'field': 'count', 'old_value': 1213, 'new_value': 1263}, {'field': 'instoreAmount', 'old_value': 184683.95, 'new_value': 191191.85}, {'field': 'instoreCount', 'old_value': 1213, 'new_value': 1263}]
2025-05-20 08:07:02,819 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-20 08:07:02,820 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 107079.86, 'new_value': 115366.87}, {'field': 'dailyBillAmount', 'old_value': 107079.86, 'new_value': 115366.87}, {'field': 'amount', 'old_value': 9734.44, 'new_value': 10857.75}, {'field': 'count', 'old_value': 910, 'new_value': 995}, {'field': 'instoreAmount', 'old_value': 13444.1, 'new_value': 14596.21}, {'field': 'instoreCount', 'old_value': 910, 'new_value': 995}]
2025-05-20 08:07:03,328 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-20 08:07:03,329 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 189883.86, 'new_value': 205489.52}, {'field': 'amount', 'old_value': 189880.47, 'new_value': 205486.13}, {'field': 'count', 'old_value': 4797, 'new_value': 5035}, {'field': 'instoreAmount', 'old_value': 184506.01, 'new_value': 199927.47}, {'field': 'instoreCount', 'old_value': 4624, 'new_value': 4855}, {'field': 'onlineAmount', 'old_value': 8310.73, 'new_value': 8524.03}, {'field': 'onlineCount', 'old_value': 173, 'new_value': 180}]
2025-05-20 08:07:03,776 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-20 08:07:03,777 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174825.29, 'new_value': 181810.09}, {'field': 'dailyBillAmount', 'old_value': 174825.29, 'new_value': 181810.09}, {'field': 'amount', 'old_value': 174825.29, 'new_value': 181810.09}, {'field': 'count', 'old_value': 535, 'new_value': 560}, {'field': 'instoreAmount', 'old_value': 174825.29, 'new_value': 181810.09}, {'field': 'instoreCount', 'old_value': 535, 'new_value': 560}]
2025-05-20 08:07:04,178 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-20 08:07:04,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 152216.62, 'new_value': 157925.82}, {'field': 'dailyBillAmount', 'old_value': 152216.62, 'new_value': 157925.82}, {'field': 'amount', 'old_value': 50124.2, 'new_value': 54721.2}, {'field': 'count', 'old_value': 119, 'new_value': 127}, {'field': 'instoreAmount', 'old_value': 50124.2, 'new_value': 54721.2}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 127}]
2025-05-20 08:07:04,660 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-20 08:07:04,660 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 303869.9, 'new_value': 319452.04}, {'field': 'dailyBillAmount', 'old_value': 303869.9, 'new_value': 319452.04}, {'field': 'amount', 'old_value': 124803.9, 'new_value': 131327.4}, {'field': 'count', 'old_value': 473, 'new_value': 494}, {'field': 'instoreAmount', 'old_value': 124804.16, 'new_value': 131327.66}, {'field': 'instoreCount', 'old_value': 473, 'new_value': 494}]
2025-05-20 08:07:05,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFK
2025-05-20 08:07:05,115 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17831.0, 'new_value': 18685.0}, {'field': 'amount', 'old_value': 17831.0, 'new_value': 18685.0}, {'field': 'count', 'old_value': 15, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 17831.0, 'new_value': 18685.0}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-05-20 08:07:05,601 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-20 08:07:05,602 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64359.67, 'new_value': 67877.89}, {'field': 'dailyBillAmount', 'old_value': 64359.67, 'new_value': 67877.89}, {'field': 'amount', 'old_value': 19599.08, 'new_value': 20657.94}, {'field': 'count', 'old_value': 715, 'new_value': 757}, {'field': 'instoreAmount', 'old_value': 4727.71, 'new_value': 5006.73}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 127}, {'field': 'onlineAmount', 'old_value': 15125.89, 'new_value': 15905.73}, {'field': 'onlineCount', 'old_value': 595, 'new_value': 630}]
2025-05-20 08:07:06,081 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-20 08:07:06,081 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104561.35, 'new_value': 110508.19}, {'field': 'dailyBillAmount', 'old_value': 104561.35, 'new_value': 110508.19}, {'field': 'amount', 'old_value': 16402.82, 'new_value': 18046.28}, {'field': 'count', 'old_value': 408, 'new_value': 440}, {'field': 'instoreAmount', 'old_value': 13863.31, 'new_value': 15348.51}, {'field': 'instoreCount', 'old_value': 359, 'new_value': 387}, {'field': 'onlineAmount', 'old_value': 2540.2, 'new_value': 2698.46}, {'field': 'onlineCount', 'old_value': 49, 'new_value': 53}]
2025-05-20 08:07:06,580 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-20 08:07:06,581 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17032.03, 'new_value': 17457.03}, {'field': 'dailyBillAmount', 'old_value': 17032.03, 'new_value': 17457.03}, {'field': 'amount', 'old_value': 14460.18, 'new_value': 14587.18}, {'field': 'count', 'old_value': 469, 'new_value': 483}, {'field': 'instoreAmount', 'old_value': 14827.78, 'new_value': 14954.78}, {'field': 'instoreCount', 'old_value': 469, 'new_value': 483}]
2025-05-20 08:07:07,033 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-20 08:07:07,033 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34123.57, 'new_value': 35651.32}, {'field': 'dailyBillAmount', 'old_value': 34123.57, 'new_value': 35651.32}, {'field': 'amount', 'old_value': 21583.09, 'new_value': 22707.76}, {'field': 'count', 'old_value': 1187, 'new_value': 1236}, {'field': 'instoreAmount', 'old_value': 10865.55, 'new_value': 11624.02}, {'field': 'instoreCount', 'old_value': 476, 'new_value': 504}, {'field': 'onlineAmount', 'old_value': 11193.52, 'new_value': 11597.72}, {'field': 'onlineCount', 'old_value': 711, 'new_value': 732}]
2025-05-20 08:07:07,493 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-20 08:07:07,493 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 234153.5, 'new_value': 246411.63999999998}, {'field': 'dailyBillAmount', 'old_value': 234153.5, 'new_value': 246411.63999999998}, {'field': 'amount', 'old_value': 108827.56, 'new_value': 113573.56}, {'field': 'count', 'old_value': 440, 'new_value': 463}, {'field': 'instoreAmount', 'old_value': 112632.52, 'new_value': 117645.52}, {'field': 'instoreCount', 'old_value': 440, 'new_value': 463}]
2025-05-20 08:07:08,002 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-20 08:07:08,002 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 12147.73, 'new_value': 12707.69}, {'field': 'count', 'old_value': 99, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 12221.97, 'new_value': 12781.93}, {'field': 'instoreCount', 'old_value': 99, 'new_value': 110}]
2025-05-20 08:07:08,358 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-20 08:07:08,358 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149668.63, 'new_value': 156295.82}, {'field': 'dailyBillAmount', 'old_value': 149668.63, 'new_value': 156295.82}, {'field': 'amount', 'old_value': 71977.04, 'new_value': 74903.0}, {'field': 'count', 'old_value': 3032, 'new_value': 3162}, {'field': 'instoreAmount', 'old_value': 73409.26, 'new_value': 76366.62}, {'field': 'instoreCount', 'old_value': 3032, 'new_value': 3162}]
2025-05-20 08:07:08,786 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-20 08:07:08,787 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 329732.8, 'new_value': 346058.0}, {'field': 'dailyBillAmount', 'old_value': 329732.8, 'new_value': 346058.0}, {'field': 'amount', 'old_value': 329732.8, 'new_value': 346058.0}, {'field': 'count', 'old_value': 412, 'new_value': 431}, {'field': 'instoreAmount', 'old_value': 329732.8, 'new_value': 346058.0}, {'field': 'instoreCount', 'old_value': 412, 'new_value': 431}]
2025-05-20 08:07:09,203 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-20 08:07:09,203 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 161723.91, 'new_value': 166787.26}, {'field': 'dailyBillAmount', 'old_value': 161723.91, 'new_value': 166787.26}, {'field': 'amount', 'old_value': 93820.70999999999, 'new_value': 96297.01}, {'field': 'count', 'old_value': 243, 'new_value': 250}, {'field': 'instoreAmount', 'old_value': 95237.31, 'new_value': 97713.61}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 250}]
2025-05-20 08:07:09,637 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-20 08:07:09,637 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36301.0, 'new_value': 38593.0}, {'field': 'dailyBillAmount', 'old_value': 36301.0, 'new_value': 38593.0}, {'field': 'amount', 'old_value': 36301.0, 'new_value': 38593.0}, {'field': 'count', 'old_value': 710, 'new_value': 752}, {'field': 'instoreAmount', 'old_value': 36340.0, 'new_value': 38632.0}, {'field': 'instoreCount', 'old_value': 710, 'new_value': 752}]
2025-05-20 08:07:10,094 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-20 08:07:10,095 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64012.45, 'new_value': 68006.71}, {'field': 'dailyBillAmount', 'old_value': 64012.45, 'new_value': 68006.71}, {'field': 'amount', 'old_value': 66215.88, 'new_value': 70012.8}, {'field': 'count', 'old_value': 3500, 'new_value': 3729}, {'field': 'instoreAmount', 'old_value': 31385.62, 'new_value': 33377.33}, {'field': 'instoreCount', 'old_value': 1581, 'new_value': 1693}, {'field': 'onlineAmount', 'old_value': 35727.28, 'new_value': 37606.99}, {'field': 'onlineCount', 'old_value': 1919, 'new_value': 2036}]
2025-05-20 08:07:10,554 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-20 08:07:10,555 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22536.78, 'new_value': 23867.98}, {'field': 'dailyBillAmount', 'old_value': 22536.78, 'new_value': 23867.98}, {'field': 'amount', 'old_value': 31236.49, 'new_value': 33004.37}, {'field': 'count', 'old_value': 909, 'new_value': 961}, {'field': 'instoreAmount', 'old_value': 28112.68, 'new_value': 29837.68}, {'field': 'instoreCount', 'old_value': 786, 'new_value': 834}, {'field': 'onlineAmount', 'old_value': 3146.61, 'new_value': 3222.86}, {'field': 'onlineCount', 'old_value': 123, 'new_value': 127}]
2025-05-20 08:07:10,992 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-20 08:07:10,993 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46468.65, 'new_value': 49352.11}, {'field': 'dailyBillAmount', 'old_value': 46468.65, 'new_value': 49352.11}, {'field': 'amount', 'old_value': 46374.8, 'new_value': 49295.94}, {'field': 'count', 'old_value': 1797, 'new_value': 1919}, {'field': 'instoreAmount', 'old_value': 29785.02, 'new_value': 31718.02}, {'field': 'instoreCount', 'old_value': 1056, 'new_value': 1132}, {'field': 'onlineAmount', 'old_value': 16714.84, 'new_value': 17754.98}, {'field': 'onlineCount', 'old_value': 741, 'new_value': 787}]
2025-05-20 08:07:11,417 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-20 08:07:11,417 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 50486.73, 'new_value': 52947.32}, {'field': 'count', 'old_value': 602, 'new_value': 628}, {'field': 'instoreAmount', 'old_value': 50913.63, 'new_value': 53374.22}, {'field': 'instoreCount', 'old_value': 602, 'new_value': 628}]
2025-05-20 08:07:11,839 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-20 08:07:11,839 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54647.7, 'new_value': 56292.6}, {'field': 'amount', 'old_value': 54647.2, 'new_value': 56292.1}, {'field': 'count', 'old_value': 1350, 'new_value': 1396}, {'field': 'instoreAmount', 'old_value': 55325.6, 'new_value': 57049.6}, {'field': 'instoreCount', 'old_value': 1350, 'new_value': 1396}]
2025-05-20 08:07:12,244 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-20 08:07:12,245 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 249519.42, 'new_value': 258320.42}, {'field': 'dailyBillAmount', 'old_value': 249519.42, 'new_value': 258320.42}, {'field': 'amount', 'old_value': 73731.82, 'new_value': 79181.82}, {'field': 'count', 'old_value': 264, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 73731.82, 'new_value': 79181.82}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 284}]
2025-05-20 08:07:12,681 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-20 08:07:12,682 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77506.52, 'new_value': 78997.52}, {'field': 'dailyBillAmount', 'old_value': 77506.52, 'new_value': 78997.52}, {'field': 'amount', 'old_value': 74808.72, 'new_value': 76299.72}, {'field': 'count', 'old_value': 251, 'new_value': 261}, {'field': 'instoreAmount', 'old_value': 76943.35, 'new_value': 78434.35}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 261}]
2025-05-20 08:07:13,229 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-20 08:07:13,229 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40829.0, 'new_value': 41108.0}, {'field': 'dailyBillAmount', 'old_value': 40829.0, 'new_value': 41108.0}, {'field': 'amount', 'old_value': 50262.0, 'new_value': 50541.0}, {'field': 'count', 'old_value': 93, 'new_value': 94}, {'field': 'instoreAmount', 'old_value': 54360.0, 'new_value': 54639.0}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 94}]
2025-05-20 08:07:13,703 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-20 08:07:13,703 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61681.75, 'new_value': 64847.549999999996}, {'field': 'dailyBillAmount', 'old_value': 59188.549999999996, 'new_value': 62354.35}, {'field': 'amount', 'old_value': 61454.85, 'new_value': 64845.25}, {'field': 'count', 'old_value': 187, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 69393.35, 'new_value': 73008.65}, {'field': 'instoreCount', 'old_value': 187, 'new_value': 201}]
2025-05-20 08:07:14,224 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-20 08:07:14,224 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90833.92, 'new_value': 94589.69}, {'field': 'dailyBillAmount', 'old_value': 90833.92, 'new_value': 94589.69}, {'field': 'amount', 'old_value': 50216.21, 'new_value': 52683.0}, {'field': 'count', 'old_value': 1382, 'new_value': 1436}, {'field': 'instoreAmount', 'old_value': 43365.909999999996, 'new_value': 46021.68}, {'field': 'instoreCount', 'old_value': 1176, 'new_value': 1219}, {'field': 'onlineAmount', 'old_value': 7171.38, 'new_value': 7606.6}, {'field': 'onlineCount', 'old_value': 206, 'new_value': 217}]
2025-05-20 08:07:14,705 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-20 08:07:14,705 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124854.36, 'new_value': 132459.33000000002}, {'field': 'dailyBillAmount', 'old_value': 120394.51, 'new_value': 127823.94}, {'field': 'amount', 'old_value': 124854.36, 'new_value': 132459.33000000002}, {'field': 'count', 'old_value': 1524, 'new_value': 1623}, {'field': 'instoreAmount', 'old_value': 119118.45, 'new_value': 126429.45}, {'field': 'instoreCount', 'old_value': 1462, 'new_value': 1557}, {'field': 'onlineAmount', 'old_value': 5735.91, 'new_value': 6029.88}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 66}]
2025-05-20 08:07:15,162 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-20 08:07:15,163 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57811.65, 'new_value': 59561.43}, {'field': 'dailyBillAmount', 'old_value': 57811.65, 'new_value': 59561.43}, {'field': 'amount', 'old_value': 78117.21, 'new_value': 79888.99}, {'field': 'count', 'old_value': 329, 'new_value': 338}, {'field': 'instoreAmount', 'old_value': 75248.63, 'new_value': 77020.41}, {'field': 'instoreCount', 'old_value': 295, 'new_value': 304}]
2025-05-20 08:07:15,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-20 08:07:15,541 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 157757.4, 'new_value': 164889.6}, {'field': 'dailyBillAmount', 'old_value': 157757.4, 'new_value': 164889.6}, {'field': 'amount', 'old_value': 160842.7, 'new_value': 168333.0}, {'field': 'count', 'old_value': 585, 'new_value': 616}, {'field': 'instoreAmount', 'old_value': 163023.6, 'new_value': 171217.9}, {'field': 'instoreCount', 'old_value': 585, 'new_value': 616}]
2025-05-20 08:07:16,063 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-20 08:07:16,063 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38170.0, 'new_value': 38602.0}, {'field': 'dailyBillAmount', 'old_value': 38170.0, 'new_value': 38602.0}, {'field': 'amount', 'old_value': 35368.0, 'new_value': 35800.0}, {'field': 'count', 'old_value': 84, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 35724.0, 'new_value': 36393.0}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 85}]
2025-05-20 08:07:16,565 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-20 08:07:16,565 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 15294.2, 'new_value': 16557.14}, {'field': 'count', 'old_value': 31, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 15575.6, 'new_value': 16838.54}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 33}]
2025-05-20 08:07:17,004 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-20 08:07:17,004 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19509.74, 'new_value': 20931.8}, {'field': 'dailyBillAmount', 'old_value': 19509.74, 'new_value': 20931.8}, {'field': 'amount', 'old_value': 36468.590000000004, 'new_value': 37402.23}, {'field': 'count', 'old_value': 193, 'new_value': 202}, {'field': 'instoreAmount', 'old_value': 35156.8, 'new_value': 35878.0}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 155}, {'field': 'onlineAmount', 'old_value': 2152.54, 'new_value': 2364.98}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 47}]
2025-05-20 08:07:17,444 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-20 08:07:17,445 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18921.6, 'new_value': 19777.2}, {'field': 'dailyBillAmount', 'old_value': 18921.6, 'new_value': 19777.2}, {'field': 'amount', 'old_value': 14604.71, 'new_value': 15155.61}, {'field': 'count', 'old_value': 654, 'new_value': 677}, {'field': 'instoreAmount', 'old_value': 14773.16, 'new_value': 15348.66}, {'field': 'instoreCount', 'old_value': 654, 'new_value': 677}]
2025-05-20 08:07:17,936 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-20 08:07:17,936 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30607.41, 'new_value': 33650.39}, {'field': 'amount', 'old_value': 30605.940000000002, 'new_value': 33648.92}, {'field': 'count', 'old_value': 1631, 'new_value': 1744}, {'field': 'instoreAmount', 'old_value': 36246.33, 'new_value': 39412.8}, {'field': 'instoreCount', 'old_value': 1631, 'new_value': 1744}]
2025-05-20 08:07:18,354 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-20 08:07:18,355 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98769.7, 'new_value': 101583.87}, {'field': 'dailyBillAmount', 'old_value': 98769.7, 'new_value': 101583.87}, {'field': 'amount', 'old_value': 77463.3, 'new_value': 79159.3}, {'field': 'count', 'old_value': 313, 'new_value': 323}, {'field': 'instoreAmount', 'old_value': 77463.3, 'new_value': 79159.3}, {'field': 'instoreCount', 'old_value': 313, 'new_value': 323}]
2025-05-20 08:07:18,809 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-20 08:07:18,810 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 280131.72, 'new_value': 291932.07}, {'field': 'dailyBillAmount', 'old_value': 280131.72, 'new_value': 291932.07}, {'field': 'amount', 'old_value': 168044.41, 'new_value': 174282.75}, {'field': 'count', 'old_value': 1964, 'new_value': 2039}, {'field': 'instoreAmount', 'old_value': 68138.17, 'new_value': 72125.01}, {'field': 'instoreCount', 'old_value': 803, 'new_value': 849}, {'field': 'onlineAmount', 'old_value': 99906.24, 'new_value': 102157.74}, {'field': 'onlineCount', 'old_value': 1161, 'new_value': 1190}]
2025-05-20 08:07:19,259 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-20 08:07:19,260 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 175987.82, 'new_value': 180091.82}, {'field': 'dailyBillAmount', 'old_value': 175987.82, 'new_value': 180091.82}, {'field': 'amount', 'old_value': 182276.6, 'new_value': 186067.6}, {'field': 'count', 'old_value': 1088, 'new_value': 1108}, {'field': 'instoreAmount', 'old_value': 183056.5, 'new_value': 186847.5}, {'field': 'instoreCount', 'old_value': 1088, 'new_value': 1108}]
2025-05-20 08:07:19,709 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-20 08:07:19,710 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61795.89, 'new_value': 62074.89}, {'field': 'amount', 'old_value': 61795.89, 'new_value': 62074.89}, {'field': 'count', 'old_value': 26, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 61795.89, 'new_value': 62074.89}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-05-20 08:07:20,104 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-20 08:07:20,104 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122031.76, 'new_value': 129082.51}, {'field': 'dailyBillAmount', 'old_value': 122031.76, 'new_value': 129082.51}, {'field': 'amount', 'old_value': 78809.37, 'new_value': 82450.5}, {'field': 'count', 'old_value': 904, 'new_value': 942}, {'field': 'instoreAmount', 'old_value': 69882.14, 'new_value': 73428.07}, {'field': 'instoreCount', 'old_value': 613, 'new_value': 647}, {'field': 'onlineAmount', 'old_value': 9718.4, 'new_value': 9813.6}, {'field': 'onlineCount', 'old_value': 291, 'new_value': 295}]
2025-05-20 08:07:20,574 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-20 08:07:20,575 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1839.6, 'new_value': 1888.6}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 1839.6, 'new_value': 1888.6}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-05-20 08:07:20,993 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-20 08:07:20,994 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137526.5, 'new_value': 144337.1}, {'field': 'dailyBillAmount', 'old_value': 132720.75, 'new_value': 139531.35}, {'field': 'amount', 'old_value': 137526.5, 'new_value': 144337.1}, {'field': 'count', 'old_value': 577, 'new_value': 605}, {'field': 'instoreAmount', 'old_value': 137526.5, 'new_value': 144337.1}, {'field': 'instoreCount', 'old_value': 577, 'new_value': 605}]
2025-05-20 08:07:21,449 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-20 08:07:21,449 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16651.27, 'new_value': 17474.66}, {'field': 'dailyBillAmount', 'old_value': 16651.27, 'new_value': 17474.66}, {'field': 'amount', 'old_value': 19506.27, 'new_value': 20329.66}, {'field': 'count', 'old_value': 588, 'new_value': 623}, {'field': 'instoreAmount', 'old_value': 19506.27, 'new_value': 20349.46}, {'field': 'instoreCount', 'old_value': 588, 'new_value': 623}]
2025-05-20 08:07:21,881 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-20 08:07:21,882 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 210735.2, 'new_value': 221035.2}, {'field': 'amount', 'old_value': 210735.2, 'new_value': 221035.2}, {'field': 'count', 'old_value': 316, 'new_value': 335}, {'field': 'instoreAmount', 'old_value': 210735.2, 'new_value': 221035.2}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 335}]
2025-05-20 08:07:22,332 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-20 08:07:22,333 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37203.53, 'new_value': 39103.28}, {'field': 'amount', 'old_value': 37203.53, 'new_value': 39103.28}, {'field': 'count', 'old_value': 301, 'new_value': 315}, {'field': 'instoreAmount', 'old_value': 37203.53, 'new_value': 39103.28}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 315}]
2025-05-20 08:07:22,764 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-20 08:07:22,764 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 251464.0, 'new_value': 261107.0}, {'field': 'amount', 'old_value': 251464.0, 'new_value': 261107.0}, {'field': 'count', 'old_value': 50, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 251464.0, 'new_value': 261107.0}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 57}]
2025-05-20 08:07:23,185 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-20 08:07:23,185 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30654.100000000002, 'new_value': 31929.4}, {'field': 'count', 'old_value': 400, 'new_value': 420}, {'field': 'instoreAmount', 'old_value': 30654.100000000002, 'new_value': 31929.4}, {'field': 'instoreCount', 'old_value': 400, 'new_value': 420}]
2025-05-20 08:07:23,667 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-20 08:07:23,668 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34846.0, 'new_value': 35644.0}, {'field': 'dailyBillAmount', 'old_value': 34846.0, 'new_value': 35644.0}, {'field': 'amount', 'old_value': 34846.0, 'new_value': 35644.0}, {'field': 'count', 'old_value': 41, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 34846.0, 'new_value': 35644.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 42}]
2025-05-20 08:07:24,161 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-20 08:07:24,162 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 330307.36, 'new_value': 340622.36}, {'field': 'dailyBillAmount', 'old_value': 330307.36, 'new_value': 340622.36}, {'field': 'amount', 'old_value': 342938.36, 'new_value': 353253.36}, {'field': 'count', 'old_value': 1083, 'new_value': 1116}, {'field': 'instoreAmount', 'old_value': 342938.36, 'new_value': 353253.36}, {'field': 'instoreCount', 'old_value': 1083, 'new_value': 1116}]
2025-05-20 08:07:24,576 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-20 08:07:24,577 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 291286.0, 'new_value': 325017.0}, {'field': 'dailyBillAmount', 'old_value': 291286.0, 'new_value': 325017.0}, {'field': 'amount', 'old_value': 734793.32, 'new_value': 768796.28}, {'field': 'count', 'old_value': 950, 'new_value': 990}, {'field': 'instoreAmount', 'old_value': 734793.49, 'new_value': 768796.45}, {'field': 'instoreCount', 'old_value': 950, 'new_value': 990}]
2025-05-20 08:07:24,993 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMH01
2025-05-20 08:07:24,993 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-05, 变更字段: [{'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-05-20 08:07:25,463 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-20 08:07:25,464 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111222.4, 'new_value': 115378.0}, {'field': 'dailyBillAmount', 'old_value': 111222.4, 'new_value': 115378.0}, {'field': 'amount', 'old_value': 23418.2, 'new_value': 24335.1}, {'field': 'count', 'old_value': 90, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 23419.7, 'new_value': 24336.6}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 93}]
2025-05-20 08:07:25,958 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9M0B
2025-05-20 08:07:25,959 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 357017.3, 'new_value': 357486.3}, {'field': 'amount', 'old_value': 350292.8, 'new_value': 350761.68}, {'field': 'count', 'old_value': 754, 'new_value': 755}, {'field': 'instoreAmount', 'old_value': 352878.83, 'new_value': 353347.83}, {'field': 'instoreCount', 'old_value': 754, 'new_value': 755}]
2025-05-20 08:07:26,388 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-20 08:07:26,389 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162223.06, 'new_value': 165354.36}, {'field': 'amount', 'old_value': 162222.41, 'new_value': 165353.13999999998}, {'field': 'count', 'old_value': 1636, 'new_value': 1678}, {'field': 'instoreAmount', 'old_value': 107257.52, 'new_value': 108191.98999999999}, {'field': 'instoreCount', 'old_value': 959, 'new_value': 974}, {'field': 'onlineAmount', 'old_value': 59046.26, 'new_value': 61337.19}, {'field': 'onlineCount', 'old_value': 677, 'new_value': 704}]
2025-05-20 08:07:26,794 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-20 08:07:26,795 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 241963.02, 'new_value': 253539.42}, {'field': 'dailyBillAmount', 'old_value': 241963.02, 'new_value': 253539.42}, {'field': 'amount', 'old_value': 23224.3, 'new_value': 24213.41}, {'field': 'count', 'old_value': 725, 'new_value': 748}, {'field': 'instoreAmount', 'old_value': 26673.41, 'new_value': 27707.53}, {'field': 'instoreCount', 'old_value': 725, 'new_value': 748}]
2025-05-20 08:07:27,267 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-20 08:07:27,267 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 242779.29, 'new_value': 247430.54}, {'field': 'dailyBillAmount', 'old_value': 242779.29, 'new_value': 247430.54}, {'field': 'amount', 'old_value': 122835.02, 'new_value': 125703.46}, {'field': 'count', 'old_value': 2758, 'new_value': 2837}, {'field': 'instoreAmount', 'old_value': 104634.33, 'new_value': 106497.46}, {'field': 'instoreCount', 'old_value': 2351, 'new_value': 2408}, {'field': 'onlineAmount', 'old_value': 20185.21, 'new_value': 21190.52}, {'field': 'onlineCount', 'old_value': 407, 'new_value': 429}]
2025-05-20 08:07:27,712 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-20 08:07:27,712 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228229.5, 'new_value': 234306.3}, {'field': 'amount', 'old_value': 228227.9, 'new_value': 234304.7}, {'field': 'count', 'old_value': 903, 'new_value': 931}, {'field': 'instoreAmount', 'old_value': 231040.1, 'new_value': 237116.9}, {'field': 'instoreCount', 'old_value': 903, 'new_value': 931}]
2025-05-20 08:07:28,181 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-20 08:07:28,181 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 378280.02, 'new_value': 382587.67}, {'field': 'count', 'old_value': 7189, 'new_value': 7274}, {'field': 'instoreAmount', 'old_value': 358126.76, 'new_value': 360222.63}, {'field': 'instoreCount', 'old_value': 6796, 'new_value': 6841}, {'field': 'onlineAmount', 'old_value': 21571.97, 'new_value': 23783.88}, {'field': 'onlineCount', 'old_value': 393, 'new_value': 433}]
2025-05-20 08:07:28,629 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-20 08:07:28,629 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 212822.77, 'new_value': 230316.67}, {'field': 'amount', 'old_value': 180537.5, 'new_value': 198031.4}, {'field': 'count', 'old_value': 4345, 'new_value': 4787}, {'field': 'instoreAmount', 'old_value': 163970.1, 'new_value': 177496.9}, {'field': 'instoreCount', 'old_value': 3582, 'new_value': 3898}, {'field': 'onlineAmount', 'old_value': 16727.2, 'new_value': 20694.3}, {'field': 'onlineCount', 'old_value': 763, 'new_value': 889}]
2025-05-20 08:07:29,098 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-20 08:07:29,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 32233.309999999998, 'new_value': 32153.309999999998}, {'field': 'count', 'old_value': 820, 'new_value': 824}, {'field': 'instoreAmount', 'old_value': 1756.0, 'new_value': 1877.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 37}, {'field': 'onlineAmount', 'old_value': 44842.729999999996, 'new_value': 44876.729999999996}, {'field': 'onlineCount', 'old_value': 785, 'new_value': 787}]
2025-05-20 08:07:29,523 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-20 08:07:29,523 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 118514.24, 'new_value': 124568.61}, {'field': 'count', 'old_value': 8165, 'new_value': 8530}, {'field': 'instoreAmount', 'old_value': 98702.65, 'new_value': 103507.42}, {'field': 'instoreCount', 'old_value': 6623, 'new_value': 6901}, {'field': 'onlineAmount', 'old_value': 22830.94, 'new_value': 24243.71}, {'field': 'onlineCount', 'old_value': 1542, 'new_value': 1629}]
2025-05-20 08:07:29,938 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-20 08:07:29,938 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 210610.01, 'new_value': 216532.12}, {'field': 'dailyBillAmount', 'old_value': 210610.01, 'new_value': 216532.12}, {'field': 'amount', 'old_value': 203464.54, 'new_value': 208419.34}, {'field': 'count', 'old_value': 5942, 'new_value': 6083}, {'field': 'instoreAmount', 'old_value': 204771.45, 'new_value': 209735.05}, {'field': 'instoreCount', 'old_value': 5942, 'new_value': 6083}]
2025-05-20 08:07:30,433 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-20 08:07:30,433 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60222.770000000004, 'new_value': 61867.55}, {'field': 'amount', 'old_value': 60221.89, 'new_value': 61865.87}, {'field': 'count', 'old_value': 3234, 'new_value': 3320}, {'field': 'instoreAmount', 'old_value': 37068.51, 'new_value': 37570.79}, {'field': 'instoreCount', 'old_value': 2090, 'new_value': 2129}, {'field': 'onlineAmount', 'old_value': 23154.26, 'new_value': 24296.76}, {'field': 'onlineCount', 'old_value': 1144, 'new_value': 1191}]
2025-05-20 08:07:30,893 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-20 08:07:30,893 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105648.54, 'new_value': 109463.39}, {'field': 'dailyBillAmount', 'old_value': 105648.54, 'new_value': 109463.39}, {'field': 'amount', 'old_value': 21779.670000000002, 'new_value': 22753.79}, {'field': 'count', 'old_value': 758, 'new_value': 793}, {'field': 'instoreAmount', 'old_value': 22455.46, 'new_value': 23429.58}, {'field': 'instoreCount', 'old_value': 758, 'new_value': 793}]
2025-05-20 08:07:31,482 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-20 08:07:31,482 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84515.75, 'new_value': 87783.73}, {'field': 'dailyBillAmount', 'old_value': 84515.75, 'new_value': 87783.73}, {'field': 'amount', 'old_value': 70214.01, 'new_value': 72986.67}, {'field': 'count', 'old_value': 3543, 'new_value': 3657}, {'field': 'instoreAmount', 'old_value': 16284.69, 'new_value': 16822.510000000002}, {'field': 'instoreCount', 'old_value': 1163, 'new_value': 1194}, {'field': 'onlineAmount', 'old_value': 54943.06, 'new_value': 57198.36}, {'field': 'onlineCount', 'old_value': 2380, 'new_value': 2463}]
2025-05-20 08:07:31,986 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-20 08:07:31,986 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 79400.6, 'new_value': 82202.81999999999}, {'field': 'amount', 'old_value': 79399.72, 'new_value': 82201.94}, {'field': 'count', 'old_value': 2103, 'new_value': 2161}, {'field': 'instoreAmount', 'old_value': 76615.11, 'new_value': 79514.82}, {'field': 'instoreCount', 'old_value': 2051, 'new_value': 2109}]
2025-05-20 08:07:32,415 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-20 08:07:32,416 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 111312.53, 'new_value': 119187.7}, {'field': 'count', 'old_value': 4716, 'new_value': 4978}, {'field': 'instoreAmount', 'old_value': 113995.63, 'new_value': 121897.55}, {'field': 'instoreCount', 'old_value': 4674, 'new_value': 4934}, {'field': 'onlineAmount', 'old_value': 1585.51, 'new_value': 1696.31}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 44}]
2025-05-20 08:07:32,926 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-20 08:07:32,927 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 89527.7, 'new_value': 92336.33}, {'field': 'count', 'old_value': 7614, 'new_value': 7834}, {'field': 'instoreAmount', 'old_value': 6381.91, 'new_value': 6642.55}, {'field': 'instoreCount', 'old_value': 344, 'new_value': 360}, {'field': 'onlineAmount', 'old_value': 87733.62, 'new_value': 90305.27}, {'field': 'onlineCount', 'old_value': 7270, 'new_value': 7474}]
2025-05-20 08:07:33,449 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-20 08:07:33,449 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122533.37, 'new_value': 126771.7}, {'field': 'dailyBillAmount', 'old_value': 122533.37, 'new_value': 126771.7}, {'field': 'amount', 'old_value': 103243.85, 'new_value': 106928.44}, {'field': 'count', 'old_value': 3555, 'new_value': 3643}, {'field': 'instoreAmount', 'old_value': 59502.0, 'new_value': 61230.49}, {'field': 'instoreCount', 'old_value': 2626, 'new_value': 2679}, {'field': 'onlineAmount', 'old_value': 50685.21, 'new_value': 53220.31}, {'field': 'onlineCount', 'old_value': 929, 'new_value': 964}]
2025-05-20 08:07:34,043 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-20 08:07:34,043 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62269.53, 'new_value': 86053.53}, {'field': 'amount', 'old_value': 62269.0, 'new_value': 86053.0}, {'field': 'count', 'old_value': 38, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 62269.53, 'new_value': 86053.53}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 50}]
2025-05-20 08:07:34,464 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-20 08:07:34,465 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42257.64, 'new_value': 43741.63}, {'field': 'dailyBillAmount', 'old_value': 42257.64, 'new_value': 43741.63}, {'field': 'amount', 'old_value': 57816.14, 'new_value': 59733.65}, {'field': 'count', 'old_value': 2262, 'new_value': 2340}, {'field': 'instoreAmount', 'old_value': 19841.02, 'new_value': 20293.51}, {'field': 'instoreCount', 'old_value': 846, 'new_value': 863}, {'field': 'onlineAmount', 'old_value': 38820.75, 'new_value': 40285.77}, {'field': 'onlineCount', 'old_value': 1416, 'new_value': 1477}]
2025-05-20 08:07:34,889 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-20 08:07:34,889 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76509.35, 'new_value': 78283.15}, {'field': 'dailyBillAmount', 'old_value': 76509.35, 'new_value': 78283.15}, {'field': 'amount', 'old_value': 78725.78, 'new_value': 80567.08}, {'field': 'count', 'old_value': 2791, 'new_value': 2858}, {'field': 'instoreAmount', 'old_value': 78725.78, 'new_value': 80567.08}, {'field': 'instoreCount', 'old_value': 2791, 'new_value': 2858}]
2025-05-20 08:07:35,430 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-20 08:07:35,430 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 280409.0, 'new_value': 296690.0}, {'field': 'count', 'old_value': 230, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 306708.0, 'new_value': 322989.0}, {'field': 'instoreCount', 'old_value': 230, 'new_value': 240}]
2025-05-20 08:07:35,887 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-20 08:07:35,887 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 187356.0, 'new_value': 195456.1}, {'field': 'dailyBillAmount', 'old_value': 187356.0, 'new_value': 195456.1}, {'field': 'amount', 'old_value': 199807.06, 'new_value': 204453.46}, {'field': 'count', 'old_value': 374, 'new_value': 386}, {'field': 'instoreAmount', 'old_value': 201722.76, 'new_value': 206369.16}, {'field': 'instoreCount', 'old_value': 374, 'new_value': 386}]
2025-05-20 08:07:36,299 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-20 08:07:36,300 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57762.0, 'new_value': 63415.0}, {'field': 'dailyBillAmount', 'old_value': 57762.0, 'new_value': 63415.0}, {'field': 'amount', 'old_value': 30604.0, 'new_value': 31242.0}, {'field': 'count', 'old_value': 81, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 31767.0, 'new_value': 32405.0}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 83}]
2025-05-20 08:07:36,742 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-20 08:07:36,742 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54808.0, 'new_value': 55307.0}, {'field': 'amount', 'old_value': 51176.0, 'new_value': 51675.0}, {'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 51176.0, 'new_value': 51675.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 67}]
2025-05-20 08:07:37,159 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-20 08:07:37,159 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54546.5, 'new_value': 55831.4}, {'field': 'amount', 'old_value': 54544.3, 'new_value': 55829.2}, {'field': 'count', 'old_value': 147, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 54546.5, 'new_value': 55831.4}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 150}]
2025-05-20 08:07:37,670 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-20 08:07:37,670 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 413721.0, 'new_value': 441717.0}, {'field': 'dailyBillAmount', 'old_value': 413721.0, 'new_value': 441717.0}, {'field': 'amount', 'old_value': 476349.0, 'new_value': 504345.0}, {'field': 'count', 'old_value': 59, 'new_value': 62}, {'field': 'instoreAmount', 'old_value': 476349.0, 'new_value': 504345.0}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 62}]
2025-05-20 08:07:38,171 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-20 08:07:38,172 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 74816.0, 'new_value': 76387.0}, {'field': 'amount', 'old_value': 74816.0, 'new_value': 76387.0}, {'field': 'count', 'old_value': 19, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 74816.0, 'new_value': 76387.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 20}]
2025-05-20 08:07:38,801 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-20 08:07:38,801 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19716.0, 'new_value': 20274.0}, {'field': 'amount', 'old_value': 19716.0, 'new_value': 20274.0}, {'field': 'count', 'old_value': 29, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 19716.0, 'new_value': 20274.0}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 31}]
2025-05-20 08:07:39,260 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-20 08:07:39,260 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56409.0, 'new_value': 56778.0}, {'field': 'amount', 'old_value': 56409.0, 'new_value': 56778.0}, {'field': 'count', 'old_value': 63, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 56409.0, 'new_value': 56778.0}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 64}]
2025-05-20 08:07:39,669 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-20 08:07:39,670 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 171747.8, 'new_value': 185789.1}, {'field': 'dailyBillAmount', 'old_value': 171747.8, 'new_value': 185789.1}, {'field': 'amount', 'old_value': 263703.8, 'new_value': 276222.9}, {'field': 'count', 'old_value': 328, 'new_value': 342}, {'field': 'instoreAmount', 'old_value': 271852.26, 'new_value': 285877.26}, {'field': 'instoreCount', 'old_value': 328, 'new_value': 342}]
2025-05-20 08:07:40,110 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-20 08:07:40,110 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86223.59, 'new_value': 88581.09}, {'field': 'dailyBillAmount', 'old_value': 86223.59, 'new_value': 88581.09}, {'field': 'amount', 'old_value': 21795.24, 'new_value': 24496.74}, {'field': 'count', 'old_value': 211, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 21533.44, 'new_value': 24009.94}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 190}, {'field': 'onlineAmount', 'old_value': 2405.1, 'new_value': 2630.1}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 45}]
2025-05-20 08:07:40,576 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-20 08:07:40,576 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9619.0, 'new_value': 10195.0}, {'field': 'amount', 'old_value': 9619.0, 'new_value': 10195.0}, {'field': 'count', 'old_value': 25, 'new_value': 28}, {'field': 'instoreAmount', 'old_value': 9619.0, 'new_value': 10195.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 28}]
2025-05-20 08:07:41,092 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-20 08:07:41,092 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27515.0, 'new_value': 27924.0}, {'field': 'dailyBillAmount', 'old_value': 27515.0, 'new_value': 27924.0}, {'field': 'amount', 'old_value': 31333.0, 'new_value': 31858.0}, {'field': 'count', 'old_value': 101, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 31333.0, 'new_value': 31858.0}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 104}]
2025-05-20 08:07:41,554 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-20 08:07:41,555 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26889.7, 'new_value': 28018.7}, {'field': 'amount', 'old_value': 26889.7, 'new_value': 28018.7}, {'field': 'count', 'old_value': 155, 'new_value': 163}, {'field': 'instoreAmount', 'old_value': 27227.7, 'new_value': 28356.7}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 163}]
2025-05-20 08:07:42,002 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-20 08:07:42,002 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 6429.0, 'new_value': 6528.0}, {'field': 'dailyBillAmount', 'old_value': 6429.0, 'new_value': 6528.0}, {'field': 'amount', 'old_value': 32560.0, 'new_value': 32659.0}, {'field': 'count', 'old_value': 98, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 33335.0, 'new_value': 33434.0}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 99}]
2025-05-20 08:07:42,500 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-20 08:07:42,500 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 634834.59, 'new_value': 650329.29}, {'field': 'dailyBillAmount', 'old_value': 634834.59, 'new_value': 650329.29}, {'field': 'amount', 'old_value': 40061.79, 'new_value': 40827.33}, {'field': 'count', 'old_value': 380, 'new_value': 387}, {'field': 'instoreAmount', 'old_value': 32983.8, 'new_value': 33582.0}, {'field': 'instoreCount', 'old_value': 270, 'new_value': 274}, {'field': 'onlineAmount', 'old_value': 8100.37, 'new_value': 8268.36}, {'field': 'onlineCount', 'old_value': 110, 'new_value': 113}]
2025-05-20 08:07:42,996 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-20 08:07:42,996 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'count', 'old_value': 75, 'new_value': 76}, {'field': 'instoreCount', 'old_value': 75, 'new_value': 76}]
2025-05-20 08:07:43,447 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-20 08:07:43,447 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18671.77, 'new_value': 19399.57}, {'field': 'amount', 'old_value': 18671.07, 'new_value': 19398.87}, {'field': 'count', 'old_value': 72, 'new_value': 75}, {'field': 'instoreAmount', 'old_value': 18671.77, 'new_value': 19399.57}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 75}]
2025-05-20 08:07:43,918 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-20 08:07:43,918 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 288942.02999999997, 'new_value': 297594.93}, {'field': 'dailyBillAmount', 'old_value': 270020.52, 'new_value': 278053.92}, {'field': 'amount', 'old_value': 287164.52, 'new_value': 295817.42}, {'field': 'count', 'old_value': 561, 'new_value': 577}, {'field': 'instoreAmount', 'old_value': 289197.62, 'new_value': 297850.52}, {'field': 'instoreCount', 'old_value': 561, 'new_value': 577}]
2025-05-20 08:07:44,509 - INFO - 更新表单数据成功: FINST-VRA66VA1RMZU72KJ6T3JJ8YBFM4G3QAM6RBAM032
2025-05-20 08:07:44,510 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29979.0, 'new_value': 33192.0}, {'field': 'amount', 'old_value': 29979.0, 'new_value': 33192.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 29979.0, 'new_value': 33192.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-05-20 08:07:44,966 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-20 08:07:44,966 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46100.979999999996, 'new_value': 51732.979999999996}, {'field': 'dailyBillAmount', 'old_value': 46100.979999999996, 'new_value': 51732.979999999996}, {'field': 'amount', 'old_value': 49363.5, 'new_value': 54995.5}, {'field': 'count', 'old_value': 287, 'new_value': 327}, {'field': 'instoreAmount', 'old_value': 49363.5, 'new_value': 54995.5}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 327}]
2025-05-20 08:07:45,399 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-20 08:07:45,400 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65943.58, 'new_value': 67062.1}, {'field': 'dailyBillAmount', 'old_value': 65943.58, 'new_value': 67062.1}, {'field': 'amount', 'old_value': 26577.57, 'new_value': 29381.94}, {'field': 'count', 'old_value': 2566, 'new_value': 2857}, {'field': 'instoreAmount', 'old_value': 28379.02, 'new_value': 31365.52}, {'field': 'instoreCount', 'old_value': 2566, 'new_value': 2857}]
2025-05-20 08:07:45,874 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-20 08:07:45,874 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 470492.19, 'new_value': 484732.5}, {'field': 'dailyBillAmount', 'old_value': 470492.19, 'new_value': 484732.5}, {'field': 'amount', 'old_value': 486106.04, 'new_value': 500243.11}, {'field': 'count', 'old_value': 4443, 'new_value': 4654}, {'field': 'instoreAmount', 'old_value': 373560.21, 'new_value': 382387.4}, {'field': 'instoreCount', 'old_value': 1795, 'new_value': 1855}, {'field': 'onlineAmount', 'old_value': 116486.45999999999, 'new_value': 121833.5}, {'field': 'onlineCount', 'old_value': 2648, 'new_value': 2799}]
2025-05-20 08:07:46,290 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-20 08:07:46,291 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 148207.56, 'new_value': 150963.36}, {'field': 'amount', 'old_value': 148207.56, 'new_value': 150963.36}, {'field': 'count', 'old_value': 986, 'new_value': 1008}, {'field': 'instoreAmount', 'old_value': 148316.56, 'new_value': 151072.36}, {'field': 'instoreCount', 'old_value': 986, 'new_value': 1008}]
2025-05-20 08:07:46,757 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-20 08:07:46,758 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70682.06999999999, 'new_value': 73969.93}, {'field': 'dailyBillAmount', 'old_value': 70682.06999999999, 'new_value': 73969.93}, {'field': 'amount', 'old_value': 82353.45, 'new_value': 86153.02}, {'field': 'count', 'old_value': 3749, 'new_value': 3926}, {'field': 'instoreAmount', 'old_value': 43342.51, 'new_value': 44714.71}, {'field': 'instoreCount', 'old_value': 2232, 'new_value': 2318}, {'field': 'onlineAmount', 'old_value': 39790.66, 'new_value': 42302.48}, {'field': 'onlineCount', 'old_value': 1517, 'new_value': 1608}]
2025-05-20 08:07:47,112 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-05-20 08:07:47,113 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 2040.0, 'new_value': 2169.0}, {'field': 'amount', 'old_value': 2040.0, 'new_value': 2169.0}, {'field': 'count', 'old_value': 109, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 2040.0, 'new_value': 2169.0}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 110}]
2025-05-20 08:07:47,502 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-20 08:07:47,503 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96496.56, 'new_value': 98856.24}, {'field': 'dailyBillAmount', 'old_value': 96496.56, 'new_value': 98856.24}, {'field': 'amount', 'old_value': 45723.16, 'new_value': 47080.16}, {'field': 'count', 'old_value': 2909, 'new_value': 3037}, {'field': 'instoreAmount', 'old_value': 7045.3, 'new_value': 7148.8}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 294}, {'field': 'onlineAmount', 'old_value': 38677.86, 'new_value': 39931.36}, {'field': 'onlineCount', 'old_value': 2622, 'new_value': 2743}]
2025-05-20 08:07:47,977 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-20 08:07:47,978 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 258700.3, 'new_value': 268338.67}, {'field': 'dailyBillAmount', 'old_value': 258700.3, 'new_value': 268338.67}, {'field': 'amount', 'old_value': 242218.78, 'new_value': 250982.4}, {'field': 'count', 'old_value': 2058, 'new_value': 2162}, {'field': 'instoreAmount', 'old_value': 178843.05, 'new_value': 184168.09}, {'field': 'instoreCount', 'old_value': 860, 'new_value': 890}, {'field': 'onlineAmount', 'old_value': 63376.729999999996, 'new_value': 66815.53}, {'field': 'onlineCount', 'old_value': 1198, 'new_value': 1272}]
2025-05-20 08:07:48,493 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-20 08:07:48,493 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 300941.0, 'new_value': 308707.42}, {'field': 'dailyBillAmount', 'old_value': 300941.0, 'new_value': 308707.42}, {'field': 'amount', 'old_value': 308237.51, 'new_value': 316355.31}, {'field': 'count', 'old_value': 1838, 'new_value': 1899}, {'field': 'instoreAmount', 'old_value': 282427.71, 'new_value': 288868.21}, {'field': 'instoreCount', 'old_value': 1561, 'new_value': 1599}, {'field': 'onlineAmount', 'old_value': 30790.0, 'new_value': 32750.2}, {'field': 'onlineCount', 'old_value': 277, 'new_value': 300}]
2025-05-20 08:07:48,959 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-20 08:07:48,959 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 765690.55, 'new_value': 787516.15}, {'field': 'dailyBillAmount', 'old_value': 765690.55, 'new_value': 787516.15}, {'field': 'amount', 'old_value': 843190.43, 'new_value': 866797.08}, {'field': 'count', 'old_value': 4450, 'new_value': 4640}, {'field': 'instoreAmount', 'old_value': 639878.44, 'new_value': 654132.56}, {'field': 'instoreCount', 'old_value': 2526, 'new_value': 2595}, {'field': 'onlineAmount', 'old_value': 209381.24, 'new_value': 218991.14}, {'field': 'onlineCount', 'old_value': 1924, 'new_value': 2045}]
2025-05-20 08:07:49,398 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-20 08:07:49,398 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 237369.49, 'new_value': 246937.5}, {'field': 'dailyBillAmount', 'old_value': 237369.49, 'new_value': 246937.5}, {'field': 'amount', 'old_value': 336119.91, 'new_value': 350739.01}, {'field': 'count', 'old_value': 1588, 'new_value': 1652}, {'field': 'instoreAmount', 'old_value': 316094.62, 'new_value': 329561.52}, {'field': 'instoreCount', 'old_value': 1277, 'new_value': 1324}, {'field': 'onlineAmount', 'old_value': 20281.79, 'new_value': 21479.19}, {'field': 'onlineCount', 'old_value': 311, 'new_value': 328}]
2025-05-20 08:07:49,840 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-20 08:07:49,841 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 297985.24, 'new_value': 304312.24}, {'field': 'dailyBillAmount', 'old_value': 297985.24, 'new_value': 304312.24}, {'field': 'amount', 'old_value': 281960.6, 'new_value': 287857.3}, {'field': 'count', 'old_value': 1230, 'new_value': 1265}, {'field': 'instoreAmount', 'old_value': 286475.3, 'new_value': 292372.0}, {'field': 'instoreCount', 'old_value': 1230, 'new_value': 1265}]
2025-05-20 08:07:50,304 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-20 08:07:50,304 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 611076.72, 'new_value': 625345.52}, {'field': 'amount', 'old_value': 611076.72, 'new_value': 625345.52}, {'field': 'count', 'old_value': 4725, 'new_value': 4869}, {'field': 'instoreAmount', 'old_value': 611076.72, 'new_value': 625345.52}, {'field': 'instoreCount', 'old_value': 4725, 'new_value': 4869}]
2025-05-20 08:07:50,719 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-20 08:07:50,720 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 468019.19, 'new_value': 485053.27}, {'field': 'dailyBillAmount', 'old_value': 468019.19, 'new_value': 485053.27}, {'field': 'amount', 'old_value': 590801.83, 'new_value': 612850.12}, {'field': 'count', 'old_value': 4033, 'new_value': 4212}, {'field': 'instoreAmount', 'old_value': 332027.7, 'new_value': 341792.4}, {'field': 'instoreCount', 'old_value': 1718, 'new_value': 1778}, {'field': 'onlineAmount', 'old_value': 266785.8, 'new_value': 279339.4}, {'field': 'onlineCount', 'old_value': 2315, 'new_value': 2434}]
2025-05-20 08:07:51,143 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-20 08:07:51,144 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 307305.05, 'new_value': 315747.81}, {'field': 'dailyBillAmount', 'old_value': 307305.05, 'new_value': 315747.81}, {'field': 'amount', 'old_value': 379730.35, 'new_value': 387996.37}, {'field': 'count', 'old_value': 3994, 'new_value': 4129}, {'field': 'instoreAmount', 'old_value': 270281.12, 'new_value': 273847.62}, {'field': 'instoreCount', 'old_value': 1801, 'new_value': 1837}, {'field': 'onlineAmount', 'old_value': 110745.09, 'new_value': 115644.72}, {'field': 'onlineCount', 'old_value': 2193, 'new_value': 2292}]
2025-05-20 08:07:51,577 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-20 08:07:51,578 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 387868.08, 'new_value': 401495.04}, {'field': 'dailyBillAmount', 'old_value': 387868.08, 'new_value': 401495.04}, {'field': 'amount', 'old_value': 393128.66, 'new_value': 406702.39}, {'field': 'count', 'old_value': 3644, 'new_value': 3798}, {'field': 'instoreAmount', 'old_value': 344473.76, 'new_value': 356024.85}, {'field': 'instoreCount', 'old_value': 1906, 'new_value': 1988}, {'field': 'onlineAmount', 'old_value': 49571.56, 'new_value': 51622.71}, {'field': 'onlineCount', 'old_value': 1738, 'new_value': 1810}]
2025-05-20 08:07:51,980 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-20 08:07:51,980 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102910.8, 'new_value': 105881.8}, {'field': 'amount', 'old_value': 102910.3, 'new_value': 105881.3}, {'field': 'count', 'old_value': 455, 'new_value': 471}, {'field': 'instoreAmount', 'old_value': 102910.8, 'new_value': 105881.8}, {'field': 'instoreCount', 'old_value': 455, 'new_value': 471}]
2025-05-20 08:07:52,398 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-20 08:07:52,399 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 280524.29, 'new_value': 287191.09}, {'field': 'dailyBillAmount', 'old_value': 280524.29, 'new_value': 287191.09}, {'field': 'amount', 'old_value': -209428.18, 'new_value': -215800.98}, {'field': 'count', 'old_value': 754, 'new_value': 783}, {'field': 'instoreAmount', 'old_value': 5548.3, 'new_value': 5739.6}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 262}, {'field': 'onlineAmount', 'old_value': 15724.52, 'new_value': 16329.42}, {'field': 'onlineCount', 'old_value': 504, 'new_value': 521}]
2025-05-20 08:07:52,834 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-20 08:07:52,835 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 466266.27999999997, 'new_value': 481479.83}, {'field': 'dailyBillAmount', 'old_value': 466266.27999999997, 'new_value': 481479.83}, {'field': 'amount', 'old_value': 368510.49, 'new_value': 374957.91000000003}, {'field': 'count', 'old_value': 1521, 'new_value': 1552}, {'field': 'instoreAmount', 'old_value': 368510.49, 'new_value': 374957.91000000003}, {'field': 'instoreCount', 'old_value': 1521, 'new_value': 1552}]
2025-05-20 08:07:53,285 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-20 08:07:53,285 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'amount', 'old_value': 125856.2, 'new_value': 130903.9}, {'field': 'count', 'old_value': 525, 'new_value': 540}, {'field': 'instoreAmount', 'old_value': 130944.9, 'new_value': 135945.1}, {'field': 'instoreCount', 'old_value': 509, 'new_value': 523}, {'field': 'onlineAmount', 'old_value': 1199.8999999999999, 'new_value': 1247.3999999999999}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 17}]
2025-05-20 08:07:53,710 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-20 08:07:53,712 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 246498.52, 'new_value': 251800.91}, {'field': 'dailyBillAmount', 'old_value': 246498.52, 'new_value': 251800.91}, {'field': 'amount', 'old_value': 239269.84, 'new_value': 244172.41}, {'field': 'count', 'old_value': 1522, 'new_value': 1567}, {'field': 'instoreAmount', 'old_value': 226823.07, 'new_value': 230996.34}, {'field': 'instoreCount', 'old_value': 1202, 'new_value': 1227}, {'field': 'onlineAmount', 'old_value': 12589.53, 'new_value': 13318.83}, {'field': 'onlineCount', 'old_value': 320, 'new_value': 340}]
2025-05-20 08:07:54,193 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-20 08:07:54,193 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 255975.03999999998, 'new_value': 262210.75}, {'field': 'dailyBillAmount', 'old_value': 255975.03999999998, 'new_value': 262210.75}, {'field': 'amount', 'old_value': 112331.97, 'new_value': 115497.79}, {'field': 'count', 'old_value': 1749, 'new_value': 1835}, {'field': 'instoreAmount', 'old_value': 66065.69, 'new_value': 67540.43000000001}, {'field': 'instoreCount', 'old_value': 477, 'new_value': 493}, {'field': 'onlineAmount', 'old_value': 46268.4, 'new_value': 47960.01}, {'field': 'onlineCount', 'old_value': 1272, 'new_value': 1342}]
2025-05-20 08:07:54,697 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-20 08:07:54,698 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20433.0, 'new_value': 21129.0}, {'field': 'dailyBillAmount', 'old_value': 20433.0, 'new_value': 21129.0}]
2025-05-20 08:07:55,154 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-20 08:07:55,155 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 100431.19, 'new_value': 104193.17}, {'field': 'amount', 'old_value': 100424.69, 'new_value': 104185.7}, {'field': 'count', 'old_value': 4532, 'new_value': 4699}, {'field': 'instoreAmount', 'old_value': 37928.28, 'new_value': 38894.06}, {'field': 'instoreCount', 'old_value': 1513, 'new_value': 1551}, {'field': 'onlineAmount', 'old_value': 66911.77, 'new_value': 69731.37}, {'field': 'onlineCount', 'old_value': 3019, 'new_value': 3148}]
2025-05-20 08:07:55,682 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-20 08:07:55,683 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34261.9, 'new_value': 34965.9}, {'field': 'amount', 'old_value': 34261.9, 'new_value': 34965.9}, {'field': 'count', 'old_value': 158, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 34261.9, 'new_value': 34965.9}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 162}]
2025-05-20 08:07:56,158 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-20 08:07:56,158 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 305331.52999999997, 'new_value': 314753.43}, {'field': 'dailyBillAmount', 'old_value': 305331.52999999997, 'new_value': 314753.43}, {'field': 'amount', 'old_value': 119732.1, 'new_value': 124746.4}, {'field': 'count', 'old_value': 2231, 'new_value': 2330}, {'field': 'instoreAmount', 'old_value': 120855.9, 'new_value': 125878.4}, {'field': 'instoreCount', 'old_value': 2231, 'new_value': 2330}]
2025-05-20 08:07:56,563 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-20 08:07:56,563 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127759.76, 'new_value': 131002.36}, {'field': 'amount', 'old_value': 127759.76, 'new_value': 131002.36}, {'field': 'count', 'old_value': 3000, 'new_value': 3087}, {'field': 'instoreAmount', 'old_value': 127759.76, 'new_value': 131002.36}, {'field': 'instoreCount', 'old_value': 3000, 'new_value': 3087}]
2025-05-20 08:07:57,100 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-20 08:07:57,100 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21415.19, 'new_value': 22317.2}, {'field': 'amount', 'old_value': 21412.7, 'new_value': 22314.04}, {'field': 'count', 'old_value': 1262, 'new_value': 1323}, {'field': 'instoreAmount', 'old_value': 12168.59, 'new_value': 12303.59}, {'field': 'instoreCount', 'old_value': 618, 'new_value': 627}, {'field': 'onlineAmount', 'old_value': 9698.1, 'new_value': 10465.11}, {'field': 'onlineCount', 'old_value': 644, 'new_value': 696}]
2025-05-20 08:07:57,552 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-20 08:07:57,553 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37190.2, 'new_value': 37887.0}, {'field': 'amount', 'old_value': 37190.2, 'new_value': 37887.0}, {'field': 'count', 'old_value': 96, 'new_value': 98}, {'field': 'instoreAmount', 'old_value': 37190.2, 'new_value': 37887.0}, {'field': 'instoreCount', 'old_value': 96, 'new_value': 98}]
2025-05-20 08:07:58,020 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG81
2025-05-20 08:07:58,020 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58717.76, 'new_value': 61289.26}, {'field': 'dailyBillAmount', 'old_value': 58717.76, 'new_value': 61289.26}, {'field': 'amount', 'old_value': 65667.33, 'new_value': 68238.83}, {'field': 'count', 'old_value': 2530, 'new_value': 2638}, {'field': 'instoreAmount', 'old_value': 65996.23, 'new_value': 68660.63}, {'field': 'instoreCount', 'old_value': 2530, 'new_value': 2638}]
2025-05-20 08:07:58,493 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-20 08:07:58,493 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 139817.71, 'new_value': 144068.02}, {'field': 'dailyBillAmount', 'old_value': 117925.1, 'new_value': 120824.8}, {'field': 'amount', 'old_value': 139817.03, 'new_value': 144067.34}, {'field': 'count', 'old_value': 1935, 'new_value': 2011}, {'field': 'instoreAmount', 'old_value': 134911.8, 'new_value': 138565.6}, {'field': 'instoreCount', 'old_value': 1702, 'new_value': 1755}, {'field': 'onlineAmount', 'old_value': 5115.03, 'new_value': 5729.54}, {'field': 'onlineCount', 'old_value': 233, 'new_value': 256}]
2025-05-20 08:07:58,952 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-20 08:07:58,952 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21653.78, 'new_value': 22205.54}, {'field': 'amount', 'old_value': 21652.98, 'new_value': 22204.74}, {'field': 'count', 'old_value': 919, 'new_value': 943}, {'field': 'instoreAmount', 'old_value': 18202.78, 'new_value': 18687.64}, {'field': 'instoreCount', 'old_value': 823, 'new_value': 846}, {'field': 'onlineAmount', 'old_value': 3491.2, 'new_value': 3558.1}, {'field': 'onlineCount', 'old_value': 96, 'new_value': 97}]
2025-05-20 08:07:59,390 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-20 08:07:59,390 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 293545.65, 'new_value': 302276.18}, {'field': 'dailyBillAmount', 'old_value': 293545.65, 'new_value': 302276.18}, {'field': 'amount', 'old_value': 373657.97, 'new_value': 387681.66}, {'field': 'count', 'old_value': 3807, 'new_value': 3988}, {'field': 'instoreAmount', 'old_value': 352350.98, 'new_value': 365199.96}, {'field': 'instoreCount', 'old_value': 2636, 'new_value': 2760}, {'field': 'onlineAmount', 'old_value': 29195.29, 'new_value': 30386.8}, {'field': 'onlineCount', 'old_value': 1171, 'new_value': 1228}]
2025-05-20 08:07:59,848 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-20 08:07:59,848 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117293.58, 'new_value': 119473.43000000001}, {'field': 'dailyBillAmount', 'old_value': 117293.58, 'new_value': 119473.43000000001}, {'field': 'amount', 'old_value': 28238.31, 'new_value': 29380.68}, {'field': 'count', 'old_value': 456, 'new_value': 478}, {'field': 'instoreAmount', 'old_value': 17741.69, 'new_value': 18528.37}, {'field': 'instoreCount', 'old_value': 232, 'new_value': 241}, {'field': 'onlineAmount', 'old_value': 11359.95, 'new_value': 11715.64}, {'field': 'onlineCount', 'old_value': 224, 'new_value': 237}]
2025-05-20 08:08:00,335 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-20 08:08:00,336 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 112607.64, 'new_value': 118001.68}, {'field': 'dailyBillAmount', 'old_value': 100130.38, 'new_value': 105474.06999999999}, {'field': 'amount', 'old_value': 112605.76, 'new_value': 117999.8}, {'field': 'count', 'old_value': 6379, 'new_value': 6670}, {'field': 'instoreAmount', 'old_value': 70791.59, 'new_value': 74279.67}, {'field': 'instoreCount', 'old_value': 3948, 'new_value': 4122}, {'field': 'onlineAmount', 'old_value': 43358.01, 'new_value': 45285.47}, {'field': 'onlineCount', 'old_value': 2431, 'new_value': 2548}]
2025-05-20 08:08:00,764 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-20 08:08:00,764 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59393.46, 'new_value': 61998.41}, {'field': 'amount', 'old_value': 59386.08, 'new_value': 61990.12}, {'field': 'count', 'old_value': 3665, 'new_value': 3878}, {'field': 'instoreAmount', 'old_value': 27850.94, 'new_value': 28564.44}, {'field': 'instoreCount', 'old_value': 1579, 'new_value': 1618}, {'field': 'onlineAmount', 'old_value': 32920.77, 'new_value': 35101.74}, {'field': 'onlineCount', 'old_value': 2086, 'new_value': 2260}]
2025-05-20 08:08:01,207 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-20 08:08:01,207 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 117228.6, 'new_value': 118944.16}, {'field': 'count', 'old_value': 1145, 'new_value': 1174}, {'field': 'instoreAmount', 'old_value': 117343.48, 'new_value': 119065.84}, {'field': 'instoreCount', 'old_value': 1145, 'new_value': 1174}]
2025-05-20 08:08:01,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-20 08:08:01,689 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98574.5, 'new_value': 102380.79}, {'field': 'dailyBillAmount', 'old_value': 101826.79, 'new_value': 105730.74}, {'field': 'amount', 'old_value': 98568.69, 'new_value': 102374.98}, {'field': 'count', 'old_value': 1907, 'new_value': 2006}, {'field': 'instoreAmount', 'old_value': 94779.77, 'new_value': 98344.34}, {'field': 'instoreCount', 'old_value': 1622, 'new_value': 1700}, {'field': 'onlineAmount', 'old_value': 3880.17, 'new_value': 4121.89}, {'field': 'onlineCount', 'old_value': 285, 'new_value': 306}]
2025-05-20 08:08:02,170 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-20 08:08:02,170 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137489.57, 'new_value': 148315.83}, {'field': 'dailyBillAmount', 'old_value': 137489.57, 'new_value': 148315.83}, {'field': 'amount', 'old_value': 17934.59, 'new_value': 19211.2}, {'field': 'count', 'old_value': 691, 'new_value': 748}, {'field': 'instoreAmount', 'old_value': 20779.57, 'new_value': 22148.59}, {'field': 'instoreCount', 'old_value': 691, 'new_value': 748}]
2025-05-20 08:08:02,663 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-20 08:08:02,663 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 371679.53, 'new_value': 390182.79}, {'field': 'dailyBillAmount', 'old_value': 371679.53, 'new_value': 390182.79}, {'field': 'amount', 'old_value': 36398.16, 'new_value': 38657.86}, {'field': 'count', 'old_value': 180, 'new_value': 189}, {'field': 'instoreAmount', 'old_value': 36623.96, 'new_value': 38883.66}, {'field': 'instoreCount', 'old_value': 180, 'new_value': 189}]
2025-05-20 08:08:03,133 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-20 08:08:03,133 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 12515.63, 'new_value': 13315.76}, {'field': 'count', 'old_value': 640, 'new_value': 683}, {'field': 'onlineAmount', 'old_value': 12601.89, 'new_value': 13402.02}, {'field': 'onlineCount', 'old_value': 640, 'new_value': 683}]
2025-05-20 08:08:03,607 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-20 08:08:03,608 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 203200.81, 'new_value': 221705.49}, {'field': 'amount', 'old_value': 203047.03, 'new_value': 221551.71}, {'field': 'count', 'old_value': 2107, 'new_value': 2307}, {'field': 'instoreAmount', 'old_value': 192578.7, 'new_value': 209977.6}, {'field': 'instoreCount', 'old_value': 1783, 'new_value': 1961}, {'field': 'onlineAmount', 'old_value': 13145.550000000001, 'new_value': 14334.33}, {'field': 'onlineCount', 'old_value': 324, 'new_value': 346}]
2025-05-20 08:08:04,071 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-20 08:08:04,072 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126963.94, 'new_value': 132872.84}, {'field': 'dailyBillAmount', 'old_value': 123163.47, 'new_value': 129072.37}, {'field': 'amount', 'old_value': 100597.82, 'new_value': 104851.41}, {'field': 'count', 'old_value': 3666, 'new_value': 3809}, {'field': 'instoreAmount', 'old_value': 46678.14, 'new_value': 47757.49}, {'field': 'instoreCount', 'old_value': 1643, 'new_value': 1680}, {'field': 'onlineAmount', 'old_value': 55048.82, 'new_value': 58290.22}, {'field': 'onlineCount', 'old_value': 2023, 'new_value': 2129}]
2025-05-20 08:08:04,522 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-20 08:08:04,523 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36745.0, 'new_value': 41392.46}, {'field': 'dailyBillAmount', 'old_value': 36745.0, 'new_value': 41392.46}, {'field': 'amount', 'old_value': 2412.94, 'new_value': 2889.84}, {'field': 'count', 'old_value': 109, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 2412.94, 'new_value': 2889.84}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 130}]
2025-05-20 08:08:05,090 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-20 08:08:05,090 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 4471.08, 'new_value': 4632.78}, {'field': 'count', 'old_value': 190, 'new_value': 198}, {'field': 'onlineAmount', 'old_value': 4471.08, 'new_value': 4632.78}, {'field': 'onlineCount', 'old_value': 190, 'new_value': 198}]
2025-05-20 08:08:05,494 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-20 08:08:05,494 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78563.47, 'new_value': 82412.82}, {'field': 'dailyBillAmount', 'old_value': 40768.82, 'new_value': 42223.87}, {'field': 'amount', 'old_value': 78562.88, 'new_value': 82412.23}, {'field': 'count', 'old_value': 1931, 'new_value': 2025}, {'field': 'instoreAmount', 'old_value': 43167.85, 'new_value': 44750.7}, {'field': 'instoreCount', 'old_value': 1041, 'new_value': 1088}, {'field': 'onlineAmount', 'old_value': 37361.46, 'new_value': 39627.96}, {'field': 'onlineCount', 'old_value': 890, 'new_value': 937}]
2025-05-20 08:08:05,900 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-20 08:08:05,900 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28424.93, 'new_value': 32686.53}, {'field': 'amount', 'old_value': 28424.93, 'new_value': 32686.53}, {'field': 'count', 'old_value': 1054, 'new_value': 1218}, {'field': 'instoreAmount', 'old_value': 28790.38, 'new_value': 33063.98}, {'field': 'instoreCount', 'old_value': 1054, 'new_value': 1218}]
2025-05-20 08:08:06,410 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-20 08:08:06,410 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37065.28, 'new_value': 39039.41}, {'field': 'dailyBillAmount', 'old_value': 37065.28, 'new_value': 39039.41}, {'field': 'amount', 'old_value': 30656.41, 'new_value': 31787.48}, {'field': 'count', 'old_value': 1352, 'new_value': 1402}, {'field': 'instoreAmount', 'old_value': 18349.61, 'new_value': 18626.97}, {'field': 'instoreCount', 'old_value': 627, 'new_value': 634}, {'field': 'onlineAmount', 'old_value': 12351.28, 'new_value': 13204.99}, {'field': 'onlineCount', 'old_value': 725, 'new_value': 768}]
2025-05-20 08:08:06,827 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-20 08:08:06,828 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63301.15, 'new_value': 66148.15}, {'field': 'amount', 'old_value': 63301.15, 'new_value': 66148.15}, {'field': 'count', 'old_value': 1859, 'new_value': 1949}, {'field': 'instoreAmount', 'old_value': 24958.0, 'new_value': 25716.94}, {'field': 'instoreCount', 'old_value': 887, 'new_value': 917}, {'field': 'onlineAmount', 'old_value': 38376.15, 'new_value': 40498.91}, {'field': 'onlineCount', 'old_value': 972, 'new_value': 1032}]
2025-05-20 08:08:07,275 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-20 08:08:07,275 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37658.75, 'new_value': 39687.09}, {'field': 'amount', 'old_value': 37657.85, 'new_value': 39686.19}, {'field': 'count', 'old_value': 875, 'new_value': 916}, {'field': 'instoreAmount', 'old_value': 30037.9, 'new_value': 31110.4}, {'field': 'instoreCount', 'old_value': 708, 'new_value': 739}, {'field': 'onlineAmount', 'old_value': 7953.38, 'new_value': 8909.22}, {'field': 'onlineCount', 'old_value': 167, 'new_value': 177}]
2025-05-20 08:08:07,688 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-20 08:08:07,689 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174534.0, 'new_value': 183800.49}, {'field': 'dailyBillAmount', 'old_value': 174534.0, 'new_value': 183800.49}, {'field': 'amount', 'old_value': 114983.37, 'new_value': 121401.53}, {'field': 'count', 'old_value': 2855, 'new_value': 3020}, {'field': 'instoreAmount', 'old_value': 73175.12, 'new_value': 76941.97}, {'field': 'instoreCount', 'old_value': 1436, 'new_value': 1503}, {'field': 'onlineAmount', 'old_value': 50786.93, 'new_value': 54012.5}, {'field': 'onlineCount', 'old_value': 1419, 'new_value': 1517}]
2025-05-20 08:08:08,183 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-20 08:08:08,184 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 542822.07, 'new_value': 562043.78}, {'field': 'dailyBillAmount', 'old_value': 542822.07, 'new_value': 562043.78}, {'field': 'amount', 'old_value': 502402.0, 'new_value': 517225.9}, {'field': 'count', 'old_value': 2954, 'new_value': 3055}, {'field': 'instoreAmount', 'old_value': 362599.0, 'new_value': 369858.4}, {'field': 'instoreCount', 'old_value': 2305, 'new_value': 2370}, {'field': 'onlineAmount', 'old_value': 139805.1, 'new_value': 147369.9}, {'field': 'onlineCount', 'old_value': 649, 'new_value': 685}]
2025-05-20 08:08:08,618 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-20 08:08:08,618 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 781493.03, 'new_value': 811603.33}, {'field': 'amount', 'old_value': 781492.53, 'new_value': 811602.83}, {'field': 'count', 'old_value': 2737, 'new_value': 2837}, {'field': 'instoreAmount', 'old_value': 781493.03, 'new_value': 811603.33}, {'field': 'instoreCount', 'old_value': 2737, 'new_value': 2837}]
2025-05-20 08:08:09,034 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-20 08:08:09,035 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 451049.72, 'new_value': 465601.32}, {'field': 'dailyBillAmount', 'old_value': 402167.37, 'new_value': 413704.57}, {'field': 'amount', 'old_value': 451049.72, 'new_value': 465601.32}, {'field': 'count', 'old_value': 2720, 'new_value': 2863}, {'field': 'instoreAmount', 'old_value': 412285.73, 'new_value': 424268.04000000004}, {'field': 'instoreCount', 'old_value': 1762, 'new_value': 1819}, {'field': 'onlineAmount', 'old_value': 38980.64, 'new_value': 41580.76}, {'field': 'onlineCount', 'old_value': 958, 'new_value': 1044}]
2025-05-20 08:08:09,534 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-20 08:08:09,534 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 467415.19, 'new_value': 482877.17}, {'field': 'dailyBillAmount', 'old_value': 444261.37, 'new_value': 458976.68}, {'field': 'amount', 'old_value': 467415.19, 'new_value': 482877.17}, {'field': 'count', 'old_value': 1081, 'new_value': 1121}, {'field': 'instoreAmount', 'old_value': 438737.2, 'new_value': 452869.2}, {'field': 'instoreCount', 'old_value': 838, 'new_value': 868}, {'field': 'onlineAmount', 'old_value': 28805.27, 'new_value': 30135.25}, {'field': 'onlineCount', 'old_value': 243, 'new_value': 253}]
2025-05-20 08:08:09,944 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-20 08:08:09,945 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 525969.82, 'new_value': 546629.3}, {'field': 'amount', 'old_value': 525969.14, 'new_value': 546628.62}, {'field': 'count', 'old_value': 2645, 'new_value': 2789}, {'field': 'instoreAmount', 'old_value': 497887.79, 'new_value': 516252.79}, {'field': 'instoreCount', 'old_value': 1845, 'new_value': 1909}, {'field': 'onlineAmount', 'old_value': 28127.600000000002, 'new_value': 30447.690000000002}, {'field': 'onlineCount', 'old_value': 800, 'new_value': 880}]
2025-05-20 08:08:10,421 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-20 08:08:10,421 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 624870.43, 'new_value': 653184.51}, {'field': 'dailyBillAmount', 'old_value': 624870.43, 'new_value': 653184.51}, {'field': 'amount', 'old_value': 561750.37, 'new_value': 583857.31}, {'field': 'count', 'old_value': 2776, 'new_value': 2901}, {'field': 'instoreAmount', 'old_value': 514842.86, 'new_value': 534491.36}, {'field': 'instoreCount', 'old_value': 2312, 'new_value': 2408}, {'field': 'onlineAmount', 'old_value': 47238.52, 'new_value': 49745.26}, {'field': 'onlineCount', 'old_value': 464, 'new_value': 493}]
2025-05-20 08:08:10,924 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-20 08:08:10,925 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 136451.24, 'new_value': 149295.24}, {'field': 'dailyBillAmount', 'old_value': 135044.69, 'new_value': 147888.69}, {'field': 'amount', 'old_value': 134092.66, 'new_value': 146172.66}, {'field': 'count', 'old_value': 209, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 134092.66, 'new_value': 146172.66}, {'field': 'instoreCount', 'old_value': 209, 'new_value': 219}]
2025-05-20 08:08:11,386 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-20 08:08:11,386 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118505.21, 'new_value': 123424.02}, {'field': 'dailyBillAmount', 'old_value': 118505.21, 'new_value': 123424.02}, {'field': 'amount', 'old_value': 102102.13, 'new_value': 105073.23}, {'field': 'count', 'old_value': 180, 'new_value': 189}, {'field': 'instoreAmount', 'old_value': 99702.0, 'new_value': 102289.0}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 173}, {'field': 'onlineAmount', 'old_value': 2400.84, 'new_value': 2848.25}, {'field': 'onlineCount', 'old_value': 14, 'new_value': 16}]
2025-05-20 08:08:11,823 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-20 08:08:11,823 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17863.05, 'new_value': 18221.74}, {'field': 'amount', 'old_value': 17863.05, 'new_value': 18221.74}, {'field': 'count', 'old_value': 358, 'new_value': 370}, {'field': 'instoreAmount', 'old_value': 17863.05, 'new_value': 18221.74}, {'field': 'instoreCount', 'old_value': 358, 'new_value': 370}]
2025-05-20 08:08:12,240 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-20 08:08:12,240 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 71358.59, 'new_value': 75006.65}, {'field': 'amount', 'old_value': 71358.59, 'new_value': 75006.65}, {'field': 'count', 'old_value': 595, 'new_value': 625}, {'field': 'instoreAmount', 'old_value': 71543.15, 'new_value': 75191.21}, {'field': 'instoreCount', 'old_value': 595, 'new_value': 625}]
2025-05-20 08:08:12,679 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-20 08:08:12,680 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 223370.12, 'new_value': 233205.32}, {'field': 'dailyBillAmount', 'old_value': 223370.12, 'new_value': 233205.32}, {'field': 'amount', 'old_value': 241657.11000000002, 'new_value': 252849.16}, {'field': 'count', 'old_value': 6397, 'new_value': 6732}, {'field': 'instoreAmount', 'old_value': 230087.25, 'new_value': 240475.84}, {'field': 'instoreCount', 'old_value': 5816, 'new_value': 6109}, {'field': 'onlineAmount', 'old_value': 15516.05, 'new_value': 16522.51}, {'field': 'onlineCount', 'old_value': 581, 'new_value': 623}]
2025-05-20 08:08:13,184 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-20 08:08:13,184 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59762.42, 'new_value': 65707.1}, {'field': 'dailyBillAmount', 'old_value': 59762.42, 'new_value': 65707.1}, {'field': 'amount', 'old_value': 60390.42, 'new_value': 66335.1}, {'field': 'count', 'old_value': 57, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 60390.42, 'new_value': 66335.1}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 59}]
2025-05-20 08:08:13,650 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-20 08:08:13,651 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 587046.91, 'new_value': 615591.13}, {'field': 'dailyBillAmount', 'old_value': 587046.91, 'new_value': 615591.13}, {'field': 'amount', 'old_value': 534838.18, 'new_value': 559815.79}, {'field': 'count', 'old_value': 1382, 'new_value': 1448}, {'field': 'instoreAmount', 'old_value': 556575.63, 'new_value': 582807.55}, {'field': 'instoreCount', 'old_value': 1156, 'new_value': 1204}, {'field': 'onlineAmount', 'old_value': 5064.1, 'new_value': 5459.79}, {'field': 'onlineCount', 'old_value': 226, 'new_value': 244}]
2025-05-20 08:08:14,127 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-20 08:08:14,128 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 909995.96, 'new_value': 954178.29}, {'field': 'amount', 'old_value': 909995.96, 'new_value': 954178.29}, {'field': 'count', 'old_value': 2942, 'new_value': 3060}, {'field': 'instoreAmount', 'old_value': 911206.96, 'new_value': 955389.29}, {'field': 'instoreCount', 'old_value': 2942, 'new_value': 3060}]
2025-05-20 08:08:14,595 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-20 08:08:14,596 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 664833.51, 'new_value': 664777.03}, {'field': 'dailyBillAmount', 'old_value': 664833.51, 'new_value': 664777.03}, {'field': 'amount', 'old_value': 529009.18, 'new_value': 557728.21}, {'field': 'count', 'old_value': 1958, 'new_value': 2050}, {'field': 'instoreAmount', 'old_value': 515663.83, 'new_value': 543470.05}, {'field': 'instoreCount', 'old_value': 1173, 'new_value': 1222}, {'field': 'onlineAmount', 'old_value': 24022.52, 'new_value': 25294.32}, {'field': 'onlineCount', 'old_value': 785, 'new_value': 828}]
2025-05-20 08:08:15,014 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-20 08:08:15,014 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1336786.29, 'new_value': 1393115.29}, {'field': 'dailyBillAmount', 'old_value': 1336786.29, 'new_value': 1393115.29}, {'field': 'amount', 'old_value': 1380991.0, 'new_value': 1439272.0}, {'field': 'count', 'old_value': 3857, 'new_value': 3991}, {'field': 'instoreAmount', 'old_value': 1380991.0, 'new_value': 1439272.0}, {'field': 'instoreCount', 'old_value': 3857, 'new_value': 3991}]
2025-05-20 08:08:15,494 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-20 08:08:15,495 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 214753.96, 'new_value': 223722.51}, {'field': 'dailyBillAmount', 'old_value': 214753.96, 'new_value': 223722.51}, {'field': 'amount', 'old_value': 212206.83000000002, 'new_value': 220981.38}, {'field': 'count', 'old_value': 1145, 'new_value': 1190}, {'field': 'instoreAmount', 'old_value': 204952.8, 'new_value': 213750.4}, {'field': 'instoreCount', 'old_value': 961, 'new_value': 1001}, {'field': 'onlineAmount', 'old_value': 11436.91, 'new_value': 11637.86}, {'field': 'onlineCount', 'old_value': 184, 'new_value': 189}]
2025-05-20 08:08:15,923 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-20 08:08:15,923 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 702627.11, 'new_value': 730556.53}, {'field': 'dailyBillAmount', 'old_value': 702627.11, 'new_value': 730556.53}, {'field': 'amount', 'old_value': 749693.73, 'new_value': 787578.05}, {'field': 'count', 'old_value': 3090, 'new_value': 3229}, {'field': 'instoreAmount', 'old_value': 749694.18, 'new_value': 787578.5}, {'field': 'instoreCount', 'old_value': 3090, 'new_value': 3229}]
2025-05-20 08:08:16,399 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-20 08:08:16,399 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 288540.04, 'new_value': 312585.04}, {'field': 'dailyBillAmount', 'old_value': 288540.04, 'new_value': 312585.04}, {'field': 'amount', 'old_value': 490993.4, 'new_value': 515191.2}, {'field': 'count', 'old_value': 822, 'new_value': 864}, {'field': 'instoreAmount', 'old_value': 487488.28, 'new_value': 511225.28}, {'field': 'instoreCount', 'old_value': 792, 'new_value': 833}, {'field': 'onlineAmount', 'old_value': 3770.6, 'new_value': 4231.4}, {'field': 'onlineCount', 'old_value': 30, 'new_value': 31}]
2025-05-20 08:08:16,854 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-20 08:08:16,855 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 189863.99, 'new_value': 194630.85}, {'field': 'dailyBillAmount', 'old_value': 189863.99, 'new_value': 194630.85}, {'field': 'amount', 'old_value': 221500.3, 'new_value': 226453.3}, {'field': 'count', 'old_value': 1552, 'new_value': 1588}, {'field': 'instoreAmount', 'old_value': 224845.3, 'new_value': 229798.3}, {'field': 'instoreCount', 'old_value': 1552, 'new_value': 1588}]
2025-05-20 08:08:17,228 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-20 08:08:17,229 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111135.82, 'new_value': 116361.88}, {'field': 'dailyBillAmount', 'old_value': 111135.82, 'new_value': 116361.88}, {'field': 'amount', 'old_value': 86490.95999999999, 'new_value': 91974.95999999999}, {'field': 'count', 'old_value': 561, 'new_value': 601}, {'field': 'instoreAmount', 'old_value': 85674.0, 'new_value': 91376.0}, {'field': 'instoreCount', 'old_value': 516, 'new_value': 556}]
2025-05-20 08:08:17,651 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-20 08:08:17,652 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31652.8, 'new_value': 35432.6}, {'field': 'dailyBillAmount', 'old_value': 31652.8, 'new_value': 35432.6}]
2025-05-20 08:08:18,078 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-20 08:08:18,078 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 114558.38, 'new_value': 119347.95}, {'field': 'count', 'old_value': 5428, 'new_value': 5693}, {'field': 'instoreAmount', 'old_value': 61657.33, 'new_value': 64561.96}, {'field': 'instoreCount', 'old_value': 3133, 'new_value': 3281}, {'field': 'onlineAmount', 'old_value': 56130.63, 'new_value': 58114.24}, {'field': 'onlineCount', 'old_value': 2295, 'new_value': 2412}]
2025-05-20 08:08:18,560 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-20 08:08:18,561 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 158033.02, 'new_value': 165527.86}, {'field': 'amount', 'old_value': 158027.34, 'new_value': 165521.2}, {'field': 'count', 'old_value': 2896, 'new_value': 3070}, {'field': 'instoreAmount', 'old_value': 149856.95, 'new_value': 155454.86}, {'field': 'instoreCount', 'old_value': 2740, 'new_value': 2877}, {'field': 'onlineAmount', 'old_value': 8176.07, 'new_value': 10073.0}, {'field': 'onlineCount', 'old_value': 156, 'new_value': 193}]
2025-05-20 08:08:18,978 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-20 08:08:18,978 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22013.6, 'new_value': 23547.2}, {'field': 'amount', 'old_value': 22013.6, 'new_value': 23547.2}, {'field': 'count', 'old_value': 159, 'new_value': 166}, {'field': 'instoreAmount', 'old_value': 22013.6, 'new_value': 23547.2}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 166}]
2025-05-20 08:08:19,466 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-20 08:08:19,466 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28682.2, 'new_value': 34728.2}, {'field': 'dailyBillAmount', 'old_value': 28682.2, 'new_value': 34728.2}, {'field': 'amount', 'old_value': 39537.6, 'new_value': 41472.1}, {'field': 'count', 'old_value': 355, 'new_value': 373}, {'field': 'instoreAmount', 'old_value': 39757.8, 'new_value': 41692.3}, {'field': 'instoreCount', 'old_value': 355, 'new_value': 373}]
2025-05-20 08:08:19,925 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-20 08:08:19,926 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40826.0, 'new_value': 42721.0}, {'field': 'dailyBillAmount', 'old_value': 40826.0, 'new_value': 42721.0}]
2025-05-20 08:08:20,392 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-20 08:08:20,392 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129132.3, 'new_value': 144820.5}, {'field': 'dailyBillAmount', 'old_value': 129132.3, 'new_value': 144820.5}, {'field': 'amount', 'old_value': 125669.92, 'new_value': 134300.83}, {'field': 'count', 'old_value': 3709, 'new_value': 3877}, {'field': 'instoreAmount', 'old_value': 121944.3, 'new_value': 130482.9}, {'field': 'instoreCount', 'old_value': 3562, 'new_value': 3727}, {'field': 'onlineAmount', 'old_value': 5616.12, 'new_value': 5708.72}, {'field': 'onlineCount', 'old_value': 147, 'new_value': 150}]
2025-05-20 08:08:20,842 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-20 08:08:20,842 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38422.1, 'new_value': 41156.2}, {'field': 'dailyBillAmount', 'old_value': 38422.1, 'new_value': 41156.2}, {'field': 'amount', 'old_value': 38227.6, 'new_value': 40961.7}, {'field': 'count', 'old_value': 215, 'new_value': 231}, {'field': 'instoreAmount', 'old_value': 40604.3, 'new_value': 43767.9}, {'field': 'instoreCount', 'old_value': 214, 'new_value': 230}]
2025-05-20 08:08:21,309 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-20 08:08:21,310 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48786.13, 'new_value': 51490.11}, {'field': 'dailyBillAmount', 'old_value': 48786.13, 'new_value': 51490.11}]
2025-05-20 08:08:21,687 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-20 08:08:21,688 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34951.36, 'new_value': 36842.0}, {'field': 'amount', 'old_value': 34951.12, 'new_value': 36841.76}, {'field': 'count', 'old_value': 2009, 'new_value': 2123}, {'field': 'instoreAmount', 'old_value': 35568.62, 'new_value': 37484.24}, {'field': 'instoreCount', 'old_value': 2009, 'new_value': 2123}]
2025-05-20 08:08:22,148 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-20 08:08:22,148 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56262.46, 'new_value': 59503.729999999996}, {'field': 'dailyBillAmount', 'old_value': 56262.46, 'new_value': 59503.729999999996}, {'field': 'amount', 'old_value': 58035.81, 'new_value': 61344.22}, {'field': 'count', 'old_value': 2814, 'new_value': 2967}, {'field': 'instoreAmount', 'old_value': 53961.5, 'new_value': 57100.4}, {'field': 'instoreCount', 'old_value': 2643, 'new_value': 2788}, {'field': 'onlineAmount', 'old_value': 4139.56, 'new_value': 4309.07}, {'field': 'onlineCount', 'old_value': 171, 'new_value': 179}]
2025-05-20 08:08:22,686 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-20 08:08:22,686 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40016.01, 'new_value': 41660.08}, {'field': 'amount', 'old_value': 40016.01, 'new_value': 41660.08}, {'field': 'count', 'old_value': 1930, 'new_value': 2014}, {'field': 'instoreAmount', 'old_value': 25268.13, 'new_value': 26139.64}, {'field': 'instoreCount', 'old_value': 1289, 'new_value': 1335}, {'field': 'onlineAmount', 'old_value': 14808.88, 'new_value': 15581.44}, {'field': 'onlineCount', 'old_value': 641, 'new_value': 679}]
2025-05-20 08:08:23,251 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-20 08:08:23,251 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28231.62, 'new_value': 29397.75}, {'field': 'dailyBillAmount', 'old_value': 28231.62, 'new_value': 29397.75}, {'field': 'amount', 'old_value': 20050.33, 'new_value': 21008.97}, {'field': 'count', 'old_value': 811, 'new_value': 850}, {'field': 'instoreAmount', 'old_value': 20239.73, 'new_value': 21198.37}, {'field': 'instoreCount', 'old_value': 811, 'new_value': 850}]
2025-05-20 08:08:23,702 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-20 08:08:23,702 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52676.46, 'new_value': 54682.99}, {'field': 'amount', 'old_value': 52671.24, 'new_value': 54677.35}, {'field': 'count', 'old_value': 3210, 'new_value': 3341}, {'field': 'instoreAmount', 'old_value': 13638.51, 'new_value': 14187.91}, {'field': 'instoreCount', 'old_value': 861, 'new_value': 897}, {'field': 'onlineAmount', 'old_value': 40192.75, 'new_value': 41652.46}, {'field': 'onlineCount', 'old_value': 2349, 'new_value': 2444}]
2025-05-20 08:08:24,073 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-20 08:08:24,073 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92885.47, 'new_value': 100359.05}, {'field': 'dailyBillAmount', 'old_value': 92885.47, 'new_value': 100359.05}, {'field': 'amount', 'old_value': 77356.04, 'new_value': 83797.34}, {'field': 'count', 'old_value': 768, 'new_value': 824}, {'field': 'instoreAmount', 'old_value': 77356.04, 'new_value': 83797.34}, {'field': 'instoreCount', 'old_value': 768, 'new_value': 824}]
2025-05-20 08:08:24,540 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-20 08:08:24,540 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 79222.49, 'new_value': 81481.49}, {'field': 'dailyBillAmount', 'old_value': 79222.49, 'new_value': 81481.49}, {'field': 'amount', 'old_value': 88033.8, 'new_value': 90705.8}, {'field': 'count', 'old_value': 378, 'new_value': 392}, {'field': 'instoreAmount', 'old_value': 88033.8, 'new_value': 90705.8}, {'field': 'instoreCount', 'old_value': 378, 'new_value': 392}]
2025-05-20 08:08:24,982 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-20 08:08:24,982 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51929.7, 'new_value': 53201.7}, {'field': 'dailyBillAmount', 'old_value': 51929.7, 'new_value': 53201.7}, {'field': 'amount', 'old_value': 44327.65, 'new_value': 45599.65}, {'field': 'count', 'old_value': 234, 'new_value': 241}, {'field': 'instoreAmount', 'old_value': 45764.65, 'new_value': 47036.65}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 241}]
2025-05-20 08:08:25,407 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-20 08:08:25,407 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96631.0, 'new_value': 102093.0}, {'field': 'amount', 'old_value': 96631.0, 'new_value': 102093.0}, {'field': 'count', 'old_value': 1005, 'new_value': 1053}, {'field': 'instoreAmount', 'old_value': 96631.0, 'new_value': 102093.0}, {'field': 'instoreCount', 'old_value': 1005, 'new_value': 1053}]
2025-05-20 08:08:25,920 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-20 08:08:25,921 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20131.98, 'new_value': 22551.52}, {'field': 'dailyBillAmount', 'old_value': 20131.98, 'new_value': 22551.52}, {'field': 'amount', 'old_value': 2388.58, 'new_value': 2452.28}, {'field': 'count', 'old_value': 130, 'new_value': 134}, {'field': 'instoreAmount', 'old_value': 2801.2599999999998, 'new_value': 2880.2599999999998}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 134}]
2025-05-20 08:08:26,368 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-20 08:08:26,369 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16878.97, 'new_value': 17942.16}, {'field': 'dailyBillAmount', 'old_value': 16878.97, 'new_value': 17942.16}, {'field': 'amount', 'old_value': 17514.97, 'new_value': 18606.94}, {'field': 'count', 'old_value': 467, 'new_value': 495}, {'field': 'instoreAmount', 'old_value': 17557.11, 'new_value': 18620.3}, {'field': 'instoreCount', 'old_value': 466, 'new_value': 493}, {'field': 'onlineAmount', 'old_value': 26.38, 'new_value': 55.16}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-05-20 08:08:26,837 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-20 08:08:26,837 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37997.4, 'new_value': 39334.6}, {'field': 'dailyBillAmount', 'old_value': 37997.4, 'new_value': 39334.6}, {'field': 'amount', 'old_value': 51564.6, 'new_value': 53119.6}, {'field': 'count', 'old_value': 203, 'new_value': 210}, {'field': 'instoreAmount', 'old_value': 51816.6, 'new_value': 53308.6}, {'field': 'instoreCount', 'old_value': 203, 'new_value': 209}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 63.0}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-05-20 08:08:27,300 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-20 08:08:27,300 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29688.0, 'new_value': 31068.0}, {'field': 'dailyBillAmount', 'old_value': 29688.0, 'new_value': 31068.0}, {'field': 'amount', 'old_value': 33163.0, 'new_value': 34369.0}, {'field': 'count', 'old_value': 172, 'new_value': 180}, {'field': 'instoreAmount', 'old_value': 33177.0, 'new_value': 34383.0}, {'field': 'instoreCount', 'old_value': 172, 'new_value': 180}]
2025-05-20 08:08:27,674 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-20 08:08:27,674 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56826.38, 'new_value': 59605.25}, {'field': 'dailyBillAmount', 'old_value': 56826.38, 'new_value': 59605.25}, {'field': 'amount', 'old_value': 50294.74, 'new_value': 52866.35}, {'field': 'count', 'old_value': 1685, 'new_value': 1775}, {'field': 'instoreAmount', 'old_value': 46020.78, 'new_value': 48349.19}, {'field': 'instoreCount', 'old_value': 1490, 'new_value': 1567}, {'field': 'onlineAmount', 'old_value': 4310.4, 'new_value': 4553.6}, {'field': 'onlineCount', 'old_value': 195, 'new_value': 208}]
2025-05-20 08:08:28,199 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-20 08:08:28,199 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23036.74, 'new_value': 25609.74}, {'field': 'dailyBillAmount', 'old_value': 23036.74, 'new_value': 25609.74}, {'field': 'amount', 'old_value': 26725.31, 'new_value': 29318.31}, {'field': 'count', 'old_value': 163, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 26697.11, 'new_value': 29290.11}, {'field': 'instoreCount', 'old_value': 157, 'new_value': 169}]
2025-05-20 08:08:28,713 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-20 08:08:28,713 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 145965.08000000002, 'new_value': 154672.1}, {'field': 'dailyBillAmount', 'old_value': 145965.08000000002, 'new_value': 154672.1}, {'field': 'amount', 'old_value': 152884.6, 'new_value': 161771.6}, {'field': 'count', 'old_value': 1069, 'new_value': 1112}, {'field': 'instoreAmount', 'old_value': 146653.7, 'new_value': 155131.7}, {'field': 'instoreCount', 'old_value': 951, 'new_value': 990}, {'field': 'onlineAmount', 'old_value': 7603.9, 'new_value': 8012.9}, {'field': 'onlineCount', 'old_value': 118, 'new_value': 122}]
2025-05-20 08:08:28,714 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-20 08:08:28,714 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-20 08:08:28,714 - INFO - 正在批量插入月度数据，批次 1/1，共 2 条记录
2025-05-20 08:08:28,880 - INFO - 批量插入月度数据成功，批次 1，2 条记录
2025-05-20 08:08:31,882 - INFO - 批量插入月度数据完成: 总计 2 条，成功 2 条，失败 0 条
2025-05-20 08:08:31,882 - INFO - 批量插入月销售数据完成，共 2 条记录
2025-05-20 08:08:31,883 - INFO - 月销售数据同步完成！更新: 211 条，插入: 2 条，错误: 0 条，跳过: 977 条
2025-05-20 08:08:31,883 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-20 08:08:32,366 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250520.xlsx
2025-05-20 08:08:32,367 - INFO - 综合数据同步流程完成！
2025-05-20 08:08:32,428 - INFO - 综合数据同步完成
2025-05-20 08:08:32,428 - INFO - ==================================================
2025-05-20 08:08:32,428 - INFO - 程序退出
2025-05-20 08:08:32,429 - INFO - ==================================================
