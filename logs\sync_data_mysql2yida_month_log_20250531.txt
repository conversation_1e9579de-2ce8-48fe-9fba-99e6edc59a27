2025-05-31 00:00:02,029 - INFO - =================使用默认全量同步=============
2025-05-31 00:00:03,562 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-31 00:00:03,563 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 00:00:03,590 - INFO - 开始处理日期: 2025-01
2025-05-31 00:00:03,593 - INFO - Request Parameters - Page 1:
2025-05-31 00:00:03,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:03,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:04,885 - INFO - Response - Page 1:
2025-05-31 00:00:05,085 - INFO - 第 1 页获取到 100 条记录
2025-05-31 00:00:05,087 - INFO - Request Parameters - Page 2:
2025-05-31 00:00:05,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:05,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:05,609 - INFO - Response - Page 2:
2025-05-31 00:00:05,809 - INFO - 第 2 页获取到 100 条记录
2025-05-31 00:00:05,809 - INFO - Request Parameters - Page 3:
2025-05-31 00:00:05,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:05,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:06,308 - INFO - Response - Page 3:
2025-05-31 00:00:06,508 - INFO - 第 3 页获取到 100 条记录
2025-05-31 00:00:06,508 - INFO - Request Parameters - Page 4:
2025-05-31 00:00:06,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:06,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:07,046 - INFO - Response - Page 4:
2025-05-31 00:00:07,247 - INFO - 第 4 页获取到 100 条记录
2025-05-31 00:00:07,247 - INFO - Request Parameters - Page 5:
2025-05-31 00:00:07,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:07,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:07,783 - INFO - Response - Page 5:
2025-05-31 00:00:07,983 - INFO - 第 5 页获取到 100 条记录
2025-05-31 00:00:07,983 - INFO - Request Parameters - Page 6:
2025-05-31 00:00:07,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:07,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:08,579 - INFO - Response - Page 6:
2025-05-31 00:00:08,779 - INFO - 第 6 页获取到 100 条记录
2025-05-31 00:00:08,779 - INFO - Request Parameters - Page 7:
2025-05-31 00:00:08,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:08,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:09,231 - INFO - Response - Page 7:
2025-05-31 00:00:09,432 - INFO - 第 7 页获取到 82 条记录
2025-05-31 00:00:09,432 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 00:00:09,432 - INFO - 获取到 682 条表单数据
2025-05-31 00:00:09,444 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 00:00:09,456 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 00:00:09,456 - INFO - 开始处理日期: 2025-02
2025-05-31 00:00:09,456 - INFO - Request Parameters - Page 1:
2025-05-31 00:00:09,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:09,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:09,947 - INFO - Response - Page 1:
2025-05-31 00:00:10,147 - INFO - 第 1 页获取到 100 条记录
2025-05-31 00:00:10,147 - INFO - Request Parameters - Page 2:
2025-05-31 00:00:10,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:10,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:10,606 - INFO - Response - Page 2:
2025-05-31 00:00:10,806 - INFO - 第 2 页获取到 100 条记录
2025-05-31 00:00:10,806 - INFO - Request Parameters - Page 3:
2025-05-31 00:00:10,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:10,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:11,277 - INFO - Response - Page 3:
2025-05-31 00:00:11,478 - INFO - 第 3 页获取到 100 条记录
2025-05-31 00:00:11,478 - INFO - Request Parameters - Page 4:
2025-05-31 00:00:11,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:11,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:11,983 - INFO - Response - Page 4:
2025-05-31 00:00:12,183 - INFO - 第 4 页获取到 100 条记录
2025-05-31 00:00:12,183 - INFO - Request Parameters - Page 5:
2025-05-31 00:00:12,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:12,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:12,664 - INFO - Response - Page 5:
2025-05-31 00:00:12,864 - INFO - 第 5 页获取到 100 条记录
2025-05-31 00:00:12,864 - INFO - Request Parameters - Page 6:
2025-05-31 00:00:12,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:12,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:13,402 - INFO - Response - Page 6:
2025-05-31 00:00:13,602 - INFO - 第 6 页获取到 100 条记录
2025-05-31 00:00:13,602 - INFO - Request Parameters - Page 7:
2025-05-31 00:00:13,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:13,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:14,060 - INFO - Response - Page 7:
2025-05-31 00:00:14,260 - INFO - 第 7 页获取到 70 条记录
2025-05-31 00:00:14,260 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 00:00:14,260 - INFO - 获取到 670 条表单数据
2025-05-31 00:00:14,273 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 00:00:14,286 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 00:00:14,286 - INFO - 开始处理日期: 2025-03
2025-05-31 00:00:14,286 - INFO - Request Parameters - Page 1:
2025-05-31 00:00:14,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:14,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:14,845 - INFO - Response - Page 1:
2025-05-31 00:00:15,045 - INFO - 第 1 页获取到 100 条记录
2025-05-31 00:00:15,045 - INFO - Request Parameters - Page 2:
2025-05-31 00:00:15,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:15,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:15,606 - INFO - Response - Page 2:
2025-05-31 00:00:15,806 - INFO - 第 2 页获取到 100 条记录
2025-05-31 00:00:15,806 - INFO - Request Parameters - Page 3:
2025-05-31 00:00:15,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:15,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:16,467 - INFO - Response - Page 3:
2025-05-31 00:00:16,668 - INFO - 第 3 页获取到 100 条记录
2025-05-31 00:00:16,668 - INFO - Request Parameters - Page 4:
2025-05-31 00:00:16,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:16,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:17,159 - INFO - Response - Page 4:
2025-05-31 00:00:17,359 - INFO - 第 4 页获取到 100 条记录
2025-05-31 00:00:17,359 - INFO - Request Parameters - Page 5:
2025-05-31 00:00:17,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:17,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:17,974 - INFO - Response - Page 5:
2025-05-31 00:00:18,174 - INFO - 第 5 页获取到 100 条记录
2025-05-31 00:00:18,174 - INFO - Request Parameters - Page 6:
2025-05-31 00:00:18,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:18,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:18,726 - INFO - Response - Page 6:
2025-05-31 00:00:18,926 - INFO - 第 6 页获取到 100 条记录
2025-05-31 00:00:18,926 - INFO - Request Parameters - Page 7:
2025-05-31 00:00:18,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:18,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:19,359 - INFO - Response - Page 7:
2025-05-31 00:00:19,559 - INFO - 第 7 页获取到 61 条记录
2025-05-31 00:00:19,559 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 00:00:19,559 - INFO - 获取到 661 条表单数据
2025-05-31 00:00:19,571 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 00:00:19,583 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 00:00:19,583 - INFO - 开始处理日期: 2025-04
2025-05-31 00:00:19,583 - INFO - Request Parameters - Page 1:
2025-05-31 00:00:19,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:19,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:20,080 - INFO - Response - Page 1:
2025-05-31 00:00:20,280 - INFO - 第 1 页获取到 100 条记录
2025-05-31 00:00:20,280 - INFO - Request Parameters - Page 2:
2025-05-31 00:00:20,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:20,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:20,791 - INFO - Response - Page 2:
2025-05-31 00:00:20,992 - INFO - 第 2 页获取到 100 条记录
2025-05-31 00:00:20,992 - INFO - Request Parameters - Page 3:
2025-05-31 00:00:20,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:20,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:21,600 - INFO - Response - Page 3:
2025-05-31 00:00:21,800 - INFO - 第 3 页获取到 100 条记录
2025-05-31 00:00:21,800 - INFO - Request Parameters - Page 4:
2025-05-31 00:00:21,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:21,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:22,323 - INFO - Response - Page 4:
2025-05-31 00:00:22,523 - INFO - 第 4 页获取到 100 条记录
2025-05-31 00:00:22,523 - INFO - Request Parameters - Page 5:
2025-05-31 00:00:22,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:22,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:23,060 - INFO - Response - Page 5:
2025-05-31 00:00:23,260 - INFO - 第 5 页获取到 100 条记录
2025-05-31 00:00:23,260 - INFO - Request Parameters - Page 6:
2025-05-31 00:00:23,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:23,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:23,790 - INFO - Response - Page 6:
2025-05-31 00:00:23,990 - INFO - 第 6 页获取到 100 条记录
2025-05-31 00:00:23,990 - INFO - Request Parameters - Page 7:
2025-05-31 00:00:23,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:23,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:24,384 - INFO - Response - Page 7:
2025-05-31 00:00:24,585 - INFO - 第 7 页获取到 56 条记录
2025-05-31 00:00:24,585 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 00:00:24,585 - INFO - 获取到 656 条表单数据
2025-05-31 00:00:24,597 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 00:00:24,609 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 00:00:24,609 - INFO - 开始处理日期: 2025-05
2025-05-31 00:00:24,610 - INFO - Request Parameters - Page 1:
2025-05-31 00:00:24,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:24,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:25,111 - INFO - Response - Page 1:
2025-05-31 00:00:25,311 - INFO - 第 1 页获取到 100 条记录
2025-05-31 00:00:25,311 - INFO - Request Parameters - Page 2:
2025-05-31 00:00:25,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:25,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:25,749 - INFO - Response - Page 2:
2025-05-31 00:00:25,949 - INFO - 第 2 页获取到 100 条记录
2025-05-31 00:00:25,949 - INFO - Request Parameters - Page 3:
2025-05-31 00:00:25,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:25,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:26,464 - INFO - Response - Page 3:
2025-05-31 00:00:26,664 - INFO - 第 3 页获取到 100 条记录
2025-05-31 00:00:26,664 - INFO - Request Parameters - Page 4:
2025-05-31 00:00:26,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:26,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:27,187 - INFO - Response - Page 4:
2025-05-31 00:00:27,387 - INFO - 第 4 页获取到 100 条记录
2025-05-31 00:00:27,387 - INFO - Request Parameters - Page 5:
2025-05-31 00:00:27,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:27,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:27,978 - INFO - Response - Page 5:
2025-05-31 00:00:28,178 - INFO - 第 5 页获取到 100 条记录
2025-05-31 00:00:28,178 - INFO - Request Parameters - Page 6:
2025-05-31 00:00:28,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:28,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:28,668 - INFO - Response - Page 6:
2025-05-31 00:00:28,868 - INFO - 第 6 页获取到 100 条记录
2025-05-31 00:00:28,868 - INFO - Request Parameters - Page 7:
2025-05-31 00:00:28,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 00:00:28,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 00:00:29,217 - INFO - Response - Page 7:
2025-05-31 00:00:29,419 - INFO - 第 7 页获取到 35 条记录
2025-05-31 00:00:29,419 - INFO - 查询完成，共获取到 635 条记录
2025-05-31 00:00:29,419 - INFO - 获取到 635 条表单数据
2025-05-31 00:00:29,430 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-31 00:00:29,431 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-31 00:00:29,881 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-31 00:00:29,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132865.0, 'new_value': 135922.0}, {'field': 'offline_amount', 'old_value': 159119.28, 'new_value': 165378.28}, {'field': 'total_amount', 'old_value': 291984.28, 'new_value': 301300.28}, {'field': 'order_count', 'old_value': 6223, 'new_value': 6416}]
2025-05-31 00:00:29,883 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-31 00:00:30,362 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-31 00:00:30,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95702.0, 'new_value': 98713.0}, {'field': 'offline_amount', 'old_value': 122335.0, 'new_value': 127312.0}, {'field': 'total_amount', 'old_value': 218037.0, 'new_value': 226025.0}, {'field': 'order_count', 'old_value': 4883, 'new_value': 5041}]
2025-05-31 00:00:30,363 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-31 00:00:30,831 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-31 00:00:30,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33902.2, 'new_value': 35234.2}, {'field': 'offline_amount', 'old_value': 193697.2, 'new_value': 203614.02}, {'field': 'total_amount', 'old_value': 227599.4, 'new_value': 238848.22}, {'field': 'order_count', 'old_value': 301, 'new_value': 313}]
2025-05-31 00:00:30,832 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-31 00:00:31,291 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-31 00:00:31,291 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59156.26, 'new_value': 61589.44}, {'field': 'offline_amount', 'old_value': 797156.03, 'new_value': 834082.18}, {'field': 'total_amount', 'old_value': 856312.29, 'new_value': 895671.62}, {'field': 'order_count', 'old_value': 3544, 'new_value': 3686}]
2025-05-31 00:00:31,291 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-31 00:00:31,770 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-31 00:00:31,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6814.16, 'new_value': 7089.16}, {'field': 'offline_amount', 'old_value': 68336.0, 'new_value': 73612.0}, {'field': 'total_amount', 'old_value': 75150.16, 'new_value': 80701.16}, {'field': 'order_count', 'old_value': 468, 'new_value': 471}]
2025-05-31 00:00:31,771 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-31 00:00:32,210 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-31 00:00:32,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69164.21, 'new_value': 71004.16}, {'field': 'offline_amount', 'old_value': 114185.18, 'new_value': 116847.13}, {'field': 'total_amount', 'old_value': 183349.39, 'new_value': 187851.29}, {'field': 'order_count', 'old_value': 6365, 'new_value': 6539}]
2025-05-31 00:00:32,210 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-31 00:00:32,624 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-31 00:00:32,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 369064.0, 'new_value': 379253.0}, {'field': 'total_amount', 'old_value': 369064.0, 'new_value': 379253.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 243}]
2025-05-31 00:00:32,625 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-31 00:00:33,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-31 00:00:33,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25071.79, 'new_value': 25274.85}, {'field': 'total_amount', 'old_value': 25137.34, 'new_value': 25340.4}, {'field': 'order_count', 'old_value': 232, 'new_value': 234}]
2025-05-31 00:00:33,119 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-31 00:00:33,512 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-31 00:00:33,512 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168608.0, 'new_value': 182155.0}, {'field': 'total_amount', 'old_value': 207292.17, 'new_value': 220839.17}, {'field': 'order_count', 'old_value': 40, 'new_value': 43}]
2025-05-31 00:00:33,512 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-31 00:00:33,921 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-31 00:00:33,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62660.0, 'new_value': 64427.0}, {'field': 'total_amount', 'old_value': 63008.0, 'new_value': 64775.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 137}]
2025-05-31 00:00:33,921 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-31 00:00:34,269 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-31 00:00:34,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 954114.0, 'new_value': 966128.0}, {'field': 'total_amount', 'old_value': 954114.0, 'new_value': 966128.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 181}]
2025-05-31 00:00:34,270 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-31 00:00:34,720 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-31 00:00:34,720 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 272355.96, 'new_value': 282756.39}, {'field': 'offline_amount', 'old_value': 114243.94, 'new_value': 116608.84}, {'field': 'total_amount', 'old_value': 386599.9, 'new_value': 399365.23}, {'field': 'order_count', 'old_value': 1608, 'new_value': 1670}]
2025-05-31 00:00:34,720 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-31 00:00:35,143 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-31 00:00:35,143 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25318.13, 'new_value': 26146.02}, {'field': 'offline_amount', 'old_value': 323150.76, 'new_value': 331419.0}, {'field': 'total_amount', 'old_value': 348468.89, 'new_value': 357565.02}, {'field': 'order_count', 'old_value': 1644, 'new_value': 1691}]
2025-05-31 00:00:35,143 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-31 00:00:35,617 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-31 00:00:35,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45170.02, 'new_value': 46554.22}, {'field': 'offline_amount', 'old_value': 609722.38, 'new_value': 626902.18}, {'field': 'total_amount', 'old_value': 654892.4, 'new_value': 673456.4}, {'field': 'order_count', 'old_value': 3437, 'new_value': 3517}]
2025-05-31 00:00:35,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-31 00:00:36,081 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-31 00:00:36,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320374.0, 'new_value': 330996.0}, {'field': 'total_amount', 'old_value': 320374.0, 'new_value': 330996.0}, {'field': 'order_count', 'old_value': 270, 'new_value': 278}]
2025-05-31 00:00:36,081 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-31 00:00:36,527 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-31 00:00:36,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 346853.4, 'new_value': 360270.2}, {'field': 'total_amount', 'old_value': 346853.4, 'new_value': 360270.2}, {'field': 'order_count', 'old_value': 3565, 'new_value': 3688}]
2025-05-31 00:00:36,527 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-31 00:00:36,959 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-31 00:00:36,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1966310.73, 'new_value': 2021132.53}, {'field': 'total_amount', 'old_value': 1966310.73, 'new_value': 2021132.53}, {'field': 'order_count', 'old_value': 17698, 'new_value': 18263}]
2025-05-31 00:00:36,960 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-31 00:00:37,383 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-31 00:00:37,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247697.56, 'new_value': 252320.26}, {'field': 'total_amount', 'old_value': 247703.56, 'new_value': 252326.26}, {'field': 'order_count', 'old_value': 464, 'new_value': 474}]
2025-05-31 00:00:37,384 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-31 00:00:37,816 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-31 00:00:37,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150770.24, 'new_value': 154634.3}, {'field': 'offline_amount', 'old_value': 528201.0, 'new_value': 543201.0}, {'field': 'total_amount', 'old_value': 678971.24, 'new_value': 697835.3}, {'field': 'order_count', 'old_value': 2770, 'new_value': 2930}]
2025-05-31 00:00:37,817 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-31 00:00:38,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-31 00:00:38,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150058.0, 'new_value': 158558.0}, {'field': 'total_amount', 'old_value': 150058.0, 'new_value': 158558.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-31 00:00:38,337 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-31 00:00:38,804 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-31 00:00:38,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211633.0, 'new_value': 214828.0}, {'field': 'total_amount', 'old_value': 211766.0, 'new_value': 214961.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 156}]
2025-05-31 00:00:38,804 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-31 00:00:39,236 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-31 00:00:39,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114370.03, 'new_value': 117973.52}, {'field': 'total_amount', 'old_value': 119981.55, 'new_value': 123585.04}, {'field': 'order_count', 'old_value': 11033, 'new_value': 11248}]
2025-05-31 00:00:39,237 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-31 00:00:39,642 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-31 00:00:39,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84748.0, 'new_value': 85586.0}, {'field': 'total_amount', 'old_value': 84748.0, 'new_value': 85586.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 139}]
2025-05-31 00:00:39,642 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-31 00:00:40,065 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-31 00:00:40,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36340.0, 'new_value': 43590.0}, {'field': 'total_amount', 'old_value': 36340.0, 'new_value': 43590.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-31 00:00:40,067 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-31 00:00:40,489 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-31 00:00:40,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91430.28, 'new_value': 94765.78}, {'field': 'offline_amount', 'old_value': 275453.71, 'new_value': 281935.29}, {'field': 'total_amount', 'old_value': 366883.99, 'new_value': 376701.07}, {'field': 'order_count', 'old_value': 4276, 'new_value': 4366}]
2025-05-31 00:00:40,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-31 00:00:40,891 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-31 00:00:40,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74351.1, 'new_value': 76658.9}, {'field': 'total_amount', 'old_value': 74351.1, 'new_value': 76658.9}, {'field': 'order_count', 'old_value': 1652, 'new_value': 1658}]
2025-05-31 00:00:40,892 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-31 00:00:41,324 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-31 00:00:41,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 731777.0, 'new_value': 744916.0}, {'field': 'total_amount', 'old_value': 731777.0, 'new_value': 744916.0}, {'field': 'order_count', 'old_value': 2126, 'new_value': 2163}]
2025-05-31 00:00:41,325 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-31 00:00:41,826 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-31 00:00:41,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 932878.0, 'new_value': 945677.0}, {'field': 'total_amount', 'old_value': 932878.0, 'new_value': 945677.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 133}]
2025-05-31 00:00:41,826 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-31 00:00:42,303 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-31 00:00:42,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 637824.0, 'new_value': 651619.0}, {'field': 'total_amount', 'old_value': 640296.0, 'new_value': 654091.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 306}]
2025-05-31 00:00:42,304 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-31 00:00:42,743 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-31 00:00:42,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216034.0, 'new_value': 220296.0}, {'field': 'total_amount', 'old_value': 216034.0, 'new_value': 220296.0}, {'field': 'order_count', 'old_value': 3389, 'new_value': 3393}]
2025-05-31 00:00:42,744 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-31 00:00:43,278 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-31 00:00:43,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326011.0, 'new_value': 344668.0}, {'field': 'total_amount', 'old_value': 326011.0, 'new_value': 344668.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 97}]
2025-05-31 00:00:43,279 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-31 00:00:43,695 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-31 00:00:43,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 772916.0, 'new_value': 781641.0}, {'field': 'total_amount', 'old_value': 772916.0, 'new_value': 781641.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 193}]
2025-05-31 00:00:43,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-31 00:00:44,161 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-31 00:00:44,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 821115.97, 'new_value': 849349.67}, {'field': 'total_amount', 'old_value': 821115.97, 'new_value': 849349.67}, {'field': 'order_count', 'old_value': 5773, 'new_value': 5999}]
2025-05-31 00:00:44,162 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-31 00:00:44,593 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-31 00:00:44,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86940.0, 'new_value': 89906.0}, {'field': 'total_amount', 'old_value': 86940.0, 'new_value': 89906.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 252}]
2025-05-31 00:00:44,594 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-31 00:00:45,049 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-31 00:00:45,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10443.71, 'new_value': 10590.51}, {'field': 'total_amount', 'old_value': 31743.71, 'new_value': 31890.51}, {'field': 'order_count', 'old_value': 162, 'new_value': 164}]
2025-05-31 00:00:45,050 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-31 00:00:45,514 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-31 00:00:45,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140993.0, 'new_value': 143663.7}, {'field': 'total_amount', 'old_value': 140993.0, 'new_value': 143663.7}, {'field': 'order_count', 'old_value': 280, 'new_value': 284}]
2025-05-31 00:00:45,515 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-31 00:00:45,975 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-31 00:00:45,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126229.6, 'new_value': 128032.6}, {'field': 'offline_amount', 'old_value': 81583.68, 'new_value': 82497.98}, {'field': 'total_amount', 'old_value': 207813.28, 'new_value': 210530.58}, {'field': 'order_count', 'old_value': 1400, 'new_value': 1420}]
2025-05-31 00:00:45,975 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-31 00:00:46,411 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-31 00:00:46,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382705.6, 'new_value': 389009.5}, {'field': 'total_amount', 'old_value': 382705.6, 'new_value': 389009.5}, {'field': 'order_count', 'old_value': 470, 'new_value': 481}]
2025-05-31 00:00:46,411 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-31 00:00:46,811 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-31 00:00:46,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1017109.0, 'new_value': 1042308.0}, {'field': 'total_amount', 'old_value': 1017109.0, 'new_value': 1042308.0}, {'field': 'order_count', 'old_value': 51133, 'new_value': 51161}]
2025-05-31 00:00:46,811 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-31 00:00:47,242 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-31 00:00:47,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 750767.0, 'new_value': 790561.0}, {'field': 'total_amount', 'old_value': 750767.0, 'new_value': 790561.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 91}]
2025-05-31 00:00:47,243 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-31 00:00:47,678 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-31 00:00:47,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 348513.65, 'new_value': 361124.88}, {'field': 'offline_amount', 'old_value': 1402936.15, 'new_value': 1455169.94}, {'field': 'total_amount', 'old_value': 1751449.8, 'new_value': 1816294.82}, {'field': 'order_count', 'old_value': 8763, 'new_value': 9082}]
2025-05-31 00:00:47,679 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-31 00:00:48,062 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-31 00:00:48,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151294.8, 'new_value': 156015.4}, {'field': 'offline_amount', 'old_value': 125611.5, 'new_value': 129287.2}, {'field': 'total_amount', 'old_value': 276906.3, 'new_value': 285302.6}, {'field': 'order_count', 'old_value': 6515, 'new_value': 6718}]
2025-05-31 00:00:48,063 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-31 00:00:48,514 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-31 00:00:48,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270838.74, 'new_value': 280860.74}, {'field': 'total_amount', 'old_value': 270838.74, 'new_value': 280860.74}, {'field': 'order_count', 'old_value': 1670, 'new_value': 1725}]
2025-05-31 00:00:48,515 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-31 00:00:48,963 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-31 00:00:48,964 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 273722.74, 'new_value': 284472.73}, {'field': 'offline_amount', 'old_value': 811339.47, 'new_value': 836513.71}, {'field': 'total_amount', 'old_value': 1085062.21, 'new_value': 1120986.44}, {'field': 'order_count', 'old_value': 6586, 'new_value': 6814}]
2025-05-31 00:00:48,964 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-31 00:00:49,425 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-31 00:00:49,425 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39657.11, 'new_value': 42044.81}, {'field': 'offline_amount', 'old_value': 388350.35, 'new_value': 400997.85}, {'field': 'total_amount', 'old_value': 428007.46, 'new_value': 443042.66}, {'field': 'order_count', 'old_value': 10235, 'new_value': 10360}]
2025-05-31 00:00:49,426 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-31 00:00:49,869 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-31 00:00:49,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2043384.0, 'new_value': 2088296.0}, {'field': 'total_amount', 'old_value': 2043384.0, 'new_value': 2088296.0}, {'field': 'order_count', 'old_value': 8214, 'new_value': 8451}]
2025-05-31 00:00:49,870 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-31 00:00:50,372 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-31 00:00:50,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287465.42, 'new_value': 290188.65}, {'field': 'total_amount', 'old_value': 287465.42, 'new_value': 290188.65}, {'field': 'order_count', 'old_value': 1642, 'new_value': 1658}]
2025-05-31 00:00:50,372 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-31 00:00:50,787 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-31 00:00:50,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63805.54, 'new_value': 65281.17}, {'field': 'offline_amount', 'old_value': 48769.21, 'new_value': 49840.27}, {'field': 'total_amount', 'old_value': 112574.75, 'new_value': 115121.44}, {'field': 'order_count', 'old_value': 9533, 'new_value': 9752}]
2025-05-31 00:00:50,787 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-31 00:00:51,209 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-31 00:00:51,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399076.9, 'new_value': 428317.9}, {'field': 'total_amount', 'old_value': 399076.9, 'new_value': 428317.9}]
2025-05-31 00:00:51,209 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-31 00:00:51,715 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-31 00:00:51,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 632079.0, 'new_value': 689758.66}, {'field': 'total_amount', 'old_value': 692079.0, 'new_value': 749758.66}, {'field': 'order_count', 'old_value': 103, 'new_value': 109}]
2025-05-31 00:00:51,715 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-31 00:00:52,123 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-31 00:00:52,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 663811.15, 'new_value': 672942.15}, {'field': 'total_amount', 'old_value': 663811.15, 'new_value': 672942.15}, {'field': 'order_count', 'old_value': 582, 'new_value': 596}]
2025-05-31 00:00:52,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-31 00:00:52,561 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-31 00:00:52,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 324557.0, 'new_value': 343843.0}, {'field': 'total_amount', 'old_value': 324557.0, 'new_value': 343843.0}, {'field': 'order_count', 'old_value': 404, 'new_value': 424}]
2025-05-31 00:00:52,562 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-31 00:00:53,088 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-31 00:00:53,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 497527.0, 'new_value': 519511.0}, {'field': 'total_amount', 'old_value': 503484.0, 'new_value': 525468.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 115}]
2025-05-31 00:00:53,089 - INFO - 日期 2025-05 处理完成 - 更新: 53 条，插入: 0 条，错误: 0 条
2025-05-31 00:00:53,089 - INFO - 数据同步完成！更新: 53 条，插入: 0 条，错误: 0 条
2025-05-31 00:00:53,091 - INFO - =================同步完成====================
2025-05-31 03:00:01,987 - INFO - =================使用默认全量同步=============
2025-05-31 03:00:03,509 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-31 03:00:03,510 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 03:00:03,538 - INFO - 开始处理日期: 2025-01
2025-05-31 03:00:03,540 - INFO - Request Parameters - Page 1:
2025-05-31 03:00:03,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:03,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:04,665 - INFO - Response - Page 1:
2025-05-31 03:00:04,866 - INFO - 第 1 页获取到 100 条记录
2025-05-31 03:00:04,866 - INFO - Request Parameters - Page 2:
2025-05-31 03:00:04,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:04,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:05,642 - INFO - Response - Page 2:
2025-05-31 03:00:05,842 - INFO - 第 2 页获取到 100 条记录
2025-05-31 03:00:05,842 - INFO - Request Parameters - Page 3:
2025-05-31 03:00:05,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:05,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:06,364 - INFO - Response - Page 3:
2025-05-31 03:00:06,564 - INFO - 第 3 页获取到 100 条记录
2025-05-31 03:00:06,564 - INFO - Request Parameters - Page 4:
2025-05-31 03:00:06,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:06,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:07,148 - INFO - Response - Page 4:
2025-05-31 03:00:07,348 - INFO - 第 4 页获取到 100 条记录
2025-05-31 03:00:07,348 - INFO - Request Parameters - Page 5:
2025-05-31 03:00:07,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:07,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:07,819 - INFO - Response - Page 5:
2025-05-31 03:00:08,019 - INFO - 第 5 页获取到 100 条记录
2025-05-31 03:00:08,019 - INFO - Request Parameters - Page 6:
2025-05-31 03:00:08,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:08,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:08,637 - INFO - Response - Page 6:
2025-05-31 03:00:08,837 - INFO - 第 6 页获取到 100 条记录
2025-05-31 03:00:08,837 - INFO - Request Parameters - Page 7:
2025-05-31 03:00:08,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:08,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:09,469 - INFO - Response - Page 7:
2025-05-31 03:00:09,669 - INFO - 第 7 页获取到 82 条记录
2025-05-31 03:00:09,669 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 03:00:09,669 - INFO - 获取到 682 条表单数据
2025-05-31 03:00:09,681 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 03:00:09,693 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 03:00:09,693 - INFO - 开始处理日期: 2025-02
2025-05-31 03:00:09,693 - INFO - Request Parameters - Page 1:
2025-05-31 03:00:09,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:09,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:10,154 - INFO - Response - Page 1:
2025-05-31 03:00:10,354 - INFO - 第 1 页获取到 100 条记录
2025-05-31 03:00:10,354 - INFO - Request Parameters - Page 2:
2025-05-31 03:00:10,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:10,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:10,886 - INFO - Response - Page 2:
2025-05-31 03:00:11,087 - INFO - 第 2 页获取到 100 条记录
2025-05-31 03:00:11,087 - INFO - Request Parameters - Page 3:
2025-05-31 03:00:11,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:11,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:11,617 - INFO - Response - Page 3:
2025-05-31 03:00:11,817 - INFO - 第 3 页获取到 100 条记录
2025-05-31 03:00:11,817 - INFO - Request Parameters - Page 4:
2025-05-31 03:00:11,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:11,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:12,367 - INFO - Response - Page 4:
2025-05-31 03:00:12,567 - INFO - 第 4 页获取到 100 条记录
2025-05-31 03:00:12,567 - INFO - Request Parameters - Page 5:
2025-05-31 03:00:12,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:12,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:13,092 - INFO - Response - Page 5:
2025-05-31 03:00:13,294 - INFO - 第 5 页获取到 100 条记录
2025-05-31 03:00:13,294 - INFO - Request Parameters - Page 6:
2025-05-31 03:00:13,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:13,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:13,800 - INFO - Response - Page 6:
2025-05-31 03:00:14,001 - INFO - 第 6 页获取到 100 条记录
2025-05-31 03:00:14,001 - INFO - Request Parameters - Page 7:
2025-05-31 03:00:14,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:14,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:14,470 - INFO - Response - Page 7:
2025-05-31 03:00:14,670 - INFO - 第 7 页获取到 70 条记录
2025-05-31 03:00:14,670 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 03:00:14,670 - INFO - 获取到 670 条表单数据
2025-05-31 03:00:14,682 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 03:00:14,694 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 03:00:14,694 - INFO - 开始处理日期: 2025-03
2025-05-31 03:00:14,694 - INFO - Request Parameters - Page 1:
2025-05-31 03:00:14,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:14,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:15,234 - INFO - Response - Page 1:
2025-05-31 03:00:15,434 - INFO - 第 1 页获取到 100 条记录
2025-05-31 03:00:15,434 - INFO - Request Parameters - Page 2:
2025-05-31 03:00:15,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:15,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:15,894 - INFO - Response - Page 2:
2025-05-31 03:00:16,094 - INFO - 第 2 页获取到 100 条记录
2025-05-31 03:00:16,094 - INFO - Request Parameters - Page 3:
2025-05-31 03:00:16,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:16,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:16,618 - INFO - Response - Page 3:
2025-05-31 03:00:16,819 - INFO - 第 3 页获取到 100 条记录
2025-05-31 03:00:16,819 - INFO - Request Parameters - Page 4:
2025-05-31 03:00:16,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:16,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:17,303 - INFO - Response - Page 4:
2025-05-31 03:00:17,504 - INFO - 第 4 页获取到 100 条记录
2025-05-31 03:00:17,504 - INFO - Request Parameters - Page 5:
2025-05-31 03:00:17,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:17,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:18,004 - INFO - Response - Page 5:
2025-05-31 03:00:18,204 - INFO - 第 5 页获取到 100 条记录
2025-05-31 03:00:18,204 - INFO - Request Parameters - Page 6:
2025-05-31 03:00:18,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:18,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:18,787 - INFO - Response - Page 6:
2025-05-31 03:00:18,987 - INFO - 第 6 页获取到 100 条记录
2025-05-31 03:00:18,987 - INFO - Request Parameters - Page 7:
2025-05-31 03:00:18,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:18,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:19,448 - INFO - Response - Page 7:
2025-05-31 03:00:19,649 - INFO - 第 7 页获取到 61 条记录
2025-05-31 03:00:19,649 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 03:00:19,649 - INFO - 获取到 661 条表单数据
2025-05-31 03:00:19,661 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 03:00:19,672 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 03:00:19,672 - INFO - 开始处理日期: 2025-04
2025-05-31 03:00:19,672 - INFO - Request Parameters - Page 1:
2025-05-31 03:00:19,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:19,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:20,236 - INFO - Response - Page 1:
2025-05-31 03:00:20,436 - INFO - 第 1 页获取到 100 条记录
2025-05-31 03:00:20,436 - INFO - Request Parameters - Page 2:
2025-05-31 03:00:20,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:20,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:20,906 - INFO - Response - Page 2:
2025-05-31 03:00:21,106 - INFO - 第 2 页获取到 100 条记录
2025-05-31 03:00:21,106 - INFO - Request Parameters - Page 3:
2025-05-31 03:00:21,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:21,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:21,613 - INFO - Response - Page 3:
2025-05-31 03:00:21,813 - INFO - 第 3 页获取到 100 条记录
2025-05-31 03:00:21,813 - INFO - Request Parameters - Page 4:
2025-05-31 03:00:21,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:21,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:22,377 - INFO - Response - Page 4:
2025-05-31 03:00:22,577 - INFO - 第 4 页获取到 100 条记录
2025-05-31 03:00:22,577 - INFO - Request Parameters - Page 5:
2025-05-31 03:00:22,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:22,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:23,148 - INFO - Response - Page 5:
2025-05-31 03:00:23,349 - INFO - 第 5 页获取到 100 条记录
2025-05-31 03:00:23,349 - INFO - Request Parameters - Page 6:
2025-05-31 03:00:23,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:23,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:23,827 - INFO - Response - Page 6:
2025-05-31 03:00:24,027 - INFO - 第 6 页获取到 100 条记录
2025-05-31 03:00:24,027 - INFO - Request Parameters - Page 7:
2025-05-31 03:00:24,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:24,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:24,443 - INFO - Response - Page 7:
2025-05-31 03:00:24,643 - INFO - 第 7 页获取到 56 条记录
2025-05-31 03:00:24,643 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 03:00:24,643 - INFO - 获取到 656 条表单数据
2025-05-31 03:00:24,656 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 03:00:24,667 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 03:00:24,667 - INFO - 开始处理日期: 2025-05
2025-05-31 03:00:24,668 - INFO - Request Parameters - Page 1:
2025-05-31 03:00:24,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:24,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:25,144 - INFO - Response - Page 1:
2025-05-31 03:00:25,345 - INFO - 第 1 页获取到 100 条记录
2025-05-31 03:00:25,345 - INFO - Request Parameters - Page 2:
2025-05-31 03:00:25,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:25,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:25,822 - INFO - Response - Page 2:
2025-05-31 03:00:26,022 - INFO - 第 2 页获取到 100 条记录
2025-05-31 03:00:26,022 - INFO - Request Parameters - Page 3:
2025-05-31 03:00:26,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:26,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:26,505 - INFO - Response - Page 3:
2025-05-31 03:00:26,705 - INFO - 第 3 页获取到 100 条记录
2025-05-31 03:00:26,705 - INFO - Request Parameters - Page 4:
2025-05-31 03:00:26,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:26,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:27,187 - INFO - Response - Page 4:
2025-05-31 03:00:27,387 - INFO - 第 4 页获取到 100 条记录
2025-05-31 03:00:27,387 - INFO - Request Parameters - Page 5:
2025-05-31 03:00:27,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:27,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:27,869 - INFO - Response - Page 5:
2025-05-31 03:00:28,069 - INFO - 第 5 页获取到 100 条记录
2025-05-31 03:00:28,069 - INFO - Request Parameters - Page 6:
2025-05-31 03:00:28,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:28,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:28,649 - INFO - Response - Page 6:
2025-05-31 03:00:28,849 - INFO - 第 6 页获取到 100 条记录
2025-05-31 03:00:28,849 - INFO - Request Parameters - Page 7:
2025-05-31 03:00:28,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 03:00:28,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 03:00:29,219 - INFO - Response - Page 7:
2025-05-31 03:00:29,420 - INFO - 第 7 页获取到 35 条记录
2025-05-31 03:00:29,420 - INFO - 查询完成，共获取到 635 条记录
2025-05-31 03:00:29,420 - INFO - 获取到 635 条表单数据
2025-05-31 03:00:29,432 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-31 03:00:29,433 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-31 03:00:29,852 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-31 03:00:29,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53421.1, 'new_value': 53500.9}, {'field': 'total_amount', 'old_value': 57381.1, 'new_value': 57460.9}, {'field': 'order_count', 'old_value': 461, 'new_value': 483}]
2025-05-31 03:00:29,854 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-31 03:00:30,220 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-31 03:00:30,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16328.0, 'new_value': 16964.0}, {'field': 'total_amount', 'old_value': 16328.0, 'new_value': 16964.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-31 03:00:30,220 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-31 03:00:30,550 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-31 03:00:30,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31446.47, 'new_value': 32486.47}, {'field': 'total_amount', 'old_value': 31446.47, 'new_value': 32486.47}, {'field': 'order_count', 'old_value': 188, 'new_value': 195}]
2025-05-31 03:00:30,550 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-31 03:00:30,988 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-31 03:00:30,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7575.8, 'new_value': 7851.8}, {'field': 'offline_amount', 'old_value': 194911.8, 'new_value': 199572.8}, {'field': 'total_amount', 'old_value': 202487.6, 'new_value': 207424.6}, {'field': 'order_count', 'old_value': 31, 'new_value': 34}]
2025-05-31 03:00:30,989 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-31 03:00:31,446 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-31 03:00:31,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54110.99, 'new_value': 55958.0}, {'field': 'offline_amount', 'old_value': 101903.83, 'new_value': 104631.85}, {'field': 'total_amount', 'old_value': 156014.82, 'new_value': 160589.85}, {'field': 'order_count', 'old_value': 5771, 'new_value': 5945}]
2025-05-31 03:00:31,447 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-31 03:00:31,881 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-31 03:00:31,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105514.26, 'new_value': 108034.51}, {'field': 'total_amount', 'old_value': 105514.26, 'new_value': 108034.51}, {'field': 'order_count', 'old_value': 4036, 'new_value': 4147}]
2025-05-31 03:00:31,882 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-31 03:00:32,304 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-31 03:00:32,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16727.33, 'new_value': 17480.13}, {'field': 'total_amount', 'old_value': 28019.31, 'new_value': 28772.11}, {'field': 'order_count', 'old_value': 120, 'new_value': 123}]
2025-05-31 03:00:32,304 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-31 03:00:32,880 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-31 03:00:32,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112932.3, 'new_value': 115866.8}, {'field': 'total_amount', 'old_value': 112932.3, 'new_value': 115866.8}, {'field': 'order_count', 'old_value': 353, 'new_value': 367}]
2025-05-31 03:00:32,880 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-31 03:00:33,347 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-31 03:00:33,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 234336.69, 'new_value': 244079.69}, {'field': 'offline_amount', 'old_value': 399088.99, 'new_value': 410075.99}, {'field': 'total_amount', 'old_value': 633425.68, 'new_value': 654155.68}, {'field': 'order_count', 'old_value': 18180, 'new_value': 18872}]
2025-05-31 03:00:33,347 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-31 03:00:33,948 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-31 03:00:33,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47374.0, 'new_value': 50629.0}, {'field': 'total_amount', 'old_value': 47374.0, 'new_value': 50629.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 138}]
2025-05-31 03:00:33,948 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-31 03:00:34,409 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-31 03:00:34,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40577.0, 'new_value': 40768.0}, {'field': 'total_amount', 'old_value': 40577.0, 'new_value': 40768.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 124}]
2025-05-31 03:00:34,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-31 03:00:34,847 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-31 03:00:34,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7734.0, 'new_value': 7932.0}, {'field': 'total_amount', 'old_value': 7734.0, 'new_value': 7932.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-31 03:00:34,847 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-31 03:00:35,309 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-31 03:00:35,309 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15886.97, 'new_value': 16399.89}, {'field': 'offline_amount', 'old_value': 103060.22, 'new_value': 107796.73}, {'field': 'total_amount', 'old_value': 118947.19, 'new_value': 124196.62}, {'field': 'order_count', 'old_value': 3148, 'new_value': 3231}]
2025-05-31 03:00:35,309 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-31 03:00:35,760 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-31 03:00:35,760 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13264.78, 'new_value': 13766.13}, {'field': 'offline_amount', 'old_value': 13522.51, 'new_value': 13785.11}, {'field': 'total_amount', 'old_value': 26787.29, 'new_value': 27551.24}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2286}]
2025-05-31 03:00:35,760 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-31 03:00:36,286 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-31 03:00:36,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65653.37, 'new_value': 66948.57}, {'field': 'offline_amount', 'old_value': 426101.54, 'new_value': 438467.66}, {'field': 'total_amount', 'old_value': 491754.91, 'new_value': 505416.23}, {'field': 'order_count', 'old_value': 4111, 'new_value': 4234}]
2025-05-31 03:00:36,286 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-31 03:00:36,669 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-31 03:00:36,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97859.57, 'new_value': 101381.33}, {'field': 'offline_amount', 'old_value': 41751.81, 'new_value': 42816.91}, {'field': 'total_amount', 'old_value': 139611.38, 'new_value': 144198.24}, {'field': 'order_count', 'old_value': 8615, 'new_value': 8914}]
2025-05-31 03:00:36,669 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-31 03:00:37,145 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-31 03:00:37,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113457.78, 'new_value': 117255.94}, {'field': 'offline_amount', 'old_value': 280803.65, 'new_value': 289574.5}, {'field': 'total_amount', 'old_value': 394261.43, 'new_value': 406830.44}, {'field': 'order_count', 'old_value': 13395, 'new_value': 13851}]
2025-05-31 03:00:37,145 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-31 03:00:37,556 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-31 03:00:37,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 353401.07, 'new_value': 370611.71}, {'field': 'offline_amount', 'old_value': 22430.54, 'new_value': 22787.94}, {'field': 'total_amount', 'old_value': 375831.61, 'new_value': 393399.65}, {'field': 'order_count', 'old_value': 14942, 'new_value': 15552}]
2025-05-31 03:00:37,556 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-31 03:00:38,038 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-31 03:00:38,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126263.8, 'new_value': 128007.5}, {'field': 'total_amount', 'old_value': 147917.7, 'new_value': 149661.4}, {'field': 'order_count', 'old_value': 202, 'new_value': 207}]
2025-05-31 03:00:38,039 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-31 03:00:38,601 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-31 03:00:38,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83912.65, 'new_value': 85312.65}, {'field': 'total_amount', 'old_value': 83912.65, 'new_value': 85312.65}, {'field': 'order_count', 'old_value': 616, 'new_value': 622}]
2025-05-31 03:00:38,601 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-31 03:00:39,052 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-31 03:00:39,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30612.0, 'new_value': 31027.0}, {'field': 'total_amount', 'old_value': 30612.0, 'new_value': 31027.0}, {'field': 'order_count', 'old_value': 326, 'new_value': 334}]
2025-05-31 03:00:39,053 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-31 03:00:39,536 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-31 03:00:39,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88798.08, 'new_value': 91437.72}, {'field': 'offline_amount', 'old_value': 125535.49, 'new_value': 128046.49}, {'field': 'total_amount', 'old_value': 214333.57, 'new_value': 219484.21}, {'field': 'order_count', 'old_value': 2250, 'new_value': 2308}]
2025-05-31 03:00:39,536 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-31 03:00:40,003 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-31 03:00:40,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12013.0, 'new_value': 12655.0}, {'field': 'total_amount', 'old_value': 14021.0, 'new_value': 14663.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 141}]
2025-05-31 03:00:40,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-31 03:00:40,349 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-31 03:00:40,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30669.0, 'new_value': 30788.0}, {'field': 'total_amount', 'old_value': 30669.0, 'new_value': 30788.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 142}]
2025-05-31 03:00:40,350 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-31 03:00:40,830 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-31 03:00:40,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126333.6, 'new_value': 135157.3}, {'field': 'offline_amount', 'old_value': 154390.9, 'new_value': 159913.0}, {'field': 'total_amount', 'old_value': 280724.5, 'new_value': 295070.3}, {'field': 'order_count', 'old_value': 5663, 'new_value': 5950}]
2025-05-31 03:00:40,830 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-31 03:00:41,287 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-31 03:00:41,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 523440.75, 'new_value': 540449.87}, {'field': 'total_amount', 'old_value': 523440.75, 'new_value': 540449.87}, {'field': 'order_count', 'old_value': 7275, 'new_value': 7520}]
2025-05-31 03:00:41,287 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-31 03:00:41,722 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-31 03:00:41,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99173.36, 'new_value': 102614.68}, {'field': 'total_amount', 'old_value': 99173.36, 'new_value': 102614.68}, {'field': 'order_count', 'old_value': 3107, 'new_value': 3200}]
2025-05-31 03:00:41,722 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-31 03:00:42,173 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-31 03:00:42,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11153.2, 'new_value': 11510.9}, {'field': 'offline_amount', 'old_value': 35148.74, 'new_value': 36161.21}, {'field': 'total_amount', 'old_value': 46301.94, 'new_value': 47672.11}, {'field': 'order_count', 'old_value': 1618, 'new_value': 1665}]
2025-05-31 03:00:42,173 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-31 03:00:42,638 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-31 03:00:42,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40946.57, 'new_value': 43071.57}, {'field': 'offline_amount', 'old_value': 346679.07, 'new_value': 357430.73}, {'field': 'total_amount', 'old_value': 387625.64, 'new_value': 400502.3}, {'field': 'order_count', 'old_value': 8894, 'new_value': 9156}]
2025-05-31 03:00:42,639 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-31 03:00:43,088 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-31 03:00:43,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 452441.6, 'new_value': 467442.7}, {'field': 'total_amount', 'old_value': 452441.6, 'new_value': 467442.7}, {'field': 'order_count', 'old_value': 2228, 'new_value': 2285}]
2025-05-31 03:00:43,089 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-31 03:00:43,536 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-31 03:00:43,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148162.25, 'new_value': 156158.1}, {'field': 'offline_amount', 'old_value': 60089.21, 'new_value': 62003.46}, {'field': 'total_amount', 'old_value': 208251.46, 'new_value': 218161.56}, {'field': 'order_count', 'old_value': 12707, 'new_value': 13172}]
2025-05-31 03:00:43,536 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-31 03:00:43,987 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-31 03:00:43,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 231365.0, 'new_value': 237488.0}, {'field': 'total_amount', 'old_value': 231365.0, 'new_value': 237488.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 267}]
2025-05-31 03:00:43,987 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-31 03:00:44,435 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-31 03:00:44,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 764074.87, 'new_value': 788417.06}, {'field': 'total_amount', 'old_value': 764074.87, 'new_value': 788417.06}, {'field': 'order_count', 'old_value': 14518, 'new_value': 14982}]
2025-05-31 03:00:44,435 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-31 03:00:44,954 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-31 03:00:44,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302489.94, 'new_value': 313681.16}, {'field': 'total_amount', 'old_value': 314540.91, 'new_value': 325732.13}, {'field': 'order_count', 'old_value': 13644, 'new_value': 14142}]
2025-05-31 03:00:44,954 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-31 03:00:45,347 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-31 03:00:45,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1983.82, 'new_value': 2010.1}, {'field': 'offline_amount', 'old_value': 23507.89, 'new_value': 23789.13}, {'field': 'total_amount', 'old_value': 25491.71, 'new_value': 25799.23}, {'field': 'order_count', 'old_value': 915, 'new_value': 928}]
2025-05-31 03:00:45,347 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-31 03:00:45,889 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-31 03:00:45,889 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7890.13, 'new_value': 8487.23}, {'field': 'offline_amount', 'old_value': 413983.84, 'new_value': 430559.14}, {'field': 'total_amount', 'old_value': 421873.97, 'new_value': 439046.37}, {'field': 'order_count', 'old_value': 20213, 'new_value': 20921}]
2025-05-31 03:00:45,889 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-31 03:00:46,321 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-31 03:00:46,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73739.7, 'new_value': 75971.6}, {'field': 'offline_amount', 'old_value': 90469.71, 'new_value': 92527.44}, {'field': 'total_amount', 'old_value': 164209.41, 'new_value': 168499.04}, {'field': 'order_count', 'old_value': 7581, 'new_value': 7786}]
2025-05-31 03:00:46,322 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-31 03:00:46,715 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-31 03:00:46,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417352.0, 'new_value': 435636.89}, {'field': 'total_amount', 'old_value': 439515.12, 'new_value': 457800.01}, {'field': 'order_count', 'old_value': 18786, 'new_value': 19439}]
2025-05-31 03:00:46,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-31 03:00:47,116 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-31 03:00:47,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261457.39, 'new_value': 271756.81}, {'field': 'total_amount', 'old_value': 280630.82, 'new_value': 290930.24}, {'field': 'order_count', 'old_value': 5807, 'new_value': 6009}]
2025-05-31 03:00:47,116 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-31 03:00:47,547 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-31 03:00:47,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37253.7, 'new_value': 37718.7}, {'field': 'total_amount', 'old_value': 37253.7, 'new_value': 37718.7}, {'field': 'order_count', 'old_value': 221, 'new_value': 225}]
2025-05-31 03:00:47,548 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-31 03:00:48,017 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-31 03:00:48,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194440.0, 'new_value': 202468.0}, {'field': 'total_amount', 'old_value': 194440.0, 'new_value': 202468.0}, {'field': 'order_count', 'old_value': 3269, 'new_value': 3388}]
2025-05-31 03:00:48,017 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-31 03:00:48,481 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-31 03:00:48,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200130.75, 'new_value': 205995.32}, {'field': 'total_amount', 'old_value': 200130.75, 'new_value': 205995.32}, {'field': 'order_count', 'old_value': 8548, 'new_value': 8773}]
2025-05-31 03:00:48,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-31 03:00:48,884 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-31 03:00:48,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262749.7, 'new_value': 271935.9}, {'field': 'total_amount', 'old_value': 262749.7, 'new_value': 271935.9}, {'field': 'order_count', 'old_value': 2044, 'new_value': 2131}]
2025-05-31 03:00:48,885 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-31 03:00:49,302 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-31 03:00:49,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41310.07, 'new_value': 43556.75}, {'field': 'offline_amount', 'old_value': 53243.85, 'new_value': 54243.85}, {'field': 'total_amount', 'old_value': 94553.92, 'new_value': 97800.6}, {'field': 'order_count', 'old_value': 4634, 'new_value': 4795}]
2025-05-31 03:00:49,302 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-31 03:00:49,733 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-31 03:00:49,733 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140525.58, 'new_value': 144650.1}, {'field': 'offline_amount', 'old_value': 237243.22, 'new_value': 243668.64}, {'field': 'total_amount', 'old_value': 377768.8, 'new_value': 388318.74}, {'field': 'order_count', 'old_value': 11784, 'new_value': 12148}]
2025-05-31 03:00:49,734 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-31 03:00:50,191 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-31 03:00:50,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47709.15, 'new_value': 49613.43}, {'field': 'offline_amount', 'old_value': 27074.35, 'new_value': 27621.69}, {'field': 'total_amount', 'old_value': 74783.5, 'new_value': 77235.12}, {'field': 'order_count', 'old_value': 3321, 'new_value': 3416}]
2025-05-31 03:00:50,191 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-31 03:00:50,616 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-31 03:00:50,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16494.4, 'new_value': 16870.51}, {'field': 'offline_amount', 'old_value': 38260.4, 'new_value': 39287.4}, {'field': 'total_amount', 'old_value': 54754.8, 'new_value': 56157.91}, {'field': 'order_count', 'old_value': 2178, 'new_value': 2245}]
2025-05-31 03:00:50,617 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-31 03:00:51,014 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-31 03:00:51,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 555770.0, 'new_value': 567666.0}, {'field': 'total_amount', 'old_value': 555770.0, 'new_value': 567666.0}, {'field': 'order_count', 'old_value': 4057, 'new_value': 4181}]
2025-05-31 03:00:51,015 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-31 03:00:51,416 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-31 03:00:51,416 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4194.0, 'new_value': 4282.0}, {'field': 'offline_amount', 'old_value': 30735.8, 'new_value': 31687.8}, {'field': 'total_amount', 'old_value': 34929.8, 'new_value': 35969.8}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1305}]
2025-05-31 03:00:51,418 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-31 03:00:51,871 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-31 03:00:51,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68235.28, 'new_value': 74654.9}, {'field': 'total_amount', 'old_value': 79091.72, 'new_value': 85511.34}, {'field': 'order_count', 'old_value': 4798, 'new_value': 5135}]
2025-05-31 03:00:51,871 - INFO - 日期 2025-05 处理完成 - 更新: 50 条，插入: 0 条，错误: 0 条
2025-05-31 03:00:51,871 - INFO - 数据同步完成！更新: 50 条，插入: 0 条，错误: 0 条
2025-05-31 03:00:51,873 - INFO - =================同步完成====================
2025-05-31 06:00:01,898 - INFO - =================使用默认全量同步=============
2025-05-31 06:00:03,427 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-31 06:00:03,428 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 06:00:03,456 - INFO - 开始处理日期: 2025-01
2025-05-31 06:00:03,459 - INFO - Request Parameters - Page 1:
2025-05-31 06:00:03,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:03,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:04,516 - INFO - Response - Page 1:
2025-05-31 06:00:04,716 - INFO - 第 1 页获取到 100 条记录
2025-05-31 06:00:04,716 - INFO - Request Parameters - Page 2:
2025-05-31 06:00:04,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:04,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:05,592 - INFO - Response - Page 2:
2025-05-31 06:00:05,792 - INFO - 第 2 页获取到 100 条记录
2025-05-31 06:00:05,792 - INFO - Request Parameters - Page 3:
2025-05-31 06:00:05,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:05,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:06,318 - INFO - Response - Page 3:
2025-05-31 06:00:06,518 - INFO - 第 3 页获取到 100 条记录
2025-05-31 06:00:06,518 - INFO - Request Parameters - Page 4:
2025-05-31 06:00:06,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:06,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:07,037 - INFO - Response - Page 4:
2025-05-31 06:00:07,238 - INFO - 第 4 页获取到 100 条记录
2025-05-31 06:00:07,238 - INFO - Request Parameters - Page 5:
2025-05-31 06:00:07,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:07,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:07,780 - INFO - Response - Page 5:
2025-05-31 06:00:07,980 - INFO - 第 5 页获取到 100 条记录
2025-05-31 06:00:07,980 - INFO - Request Parameters - Page 6:
2025-05-31 06:00:07,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:07,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:08,524 - INFO - Response - Page 6:
2025-05-31 06:00:08,724 - INFO - 第 6 页获取到 100 条记录
2025-05-31 06:00:08,724 - INFO - Request Parameters - Page 7:
2025-05-31 06:00:08,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:08,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:09,153 - INFO - Response - Page 7:
2025-05-31 06:00:09,354 - INFO - 第 7 页获取到 82 条记录
2025-05-31 06:00:09,354 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 06:00:09,354 - INFO - 获取到 682 条表单数据
2025-05-31 06:00:09,366 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 06:00:09,378 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 06:00:09,378 - INFO - 开始处理日期: 2025-02
2025-05-31 06:00:09,379 - INFO - Request Parameters - Page 1:
2025-05-31 06:00:09,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:09,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:09,907 - INFO - Response - Page 1:
2025-05-31 06:00:10,108 - INFO - 第 1 页获取到 100 条记录
2025-05-31 06:00:10,108 - INFO - Request Parameters - Page 2:
2025-05-31 06:00:10,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:10,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:10,673 - INFO - Response - Page 2:
2025-05-31 06:00:10,873 - INFO - 第 2 页获取到 100 条记录
2025-05-31 06:00:10,873 - INFO - Request Parameters - Page 3:
2025-05-31 06:00:10,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:10,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:11,433 - INFO - Response - Page 3:
2025-05-31 06:00:11,633 - INFO - 第 3 页获取到 100 条记录
2025-05-31 06:00:11,633 - INFO - Request Parameters - Page 4:
2025-05-31 06:00:11,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:11,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:12,213 - INFO - Response - Page 4:
2025-05-31 06:00:12,413 - INFO - 第 4 页获取到 100 条记录
2025-05-31 06:00:12,413 - INFO - Request Parameters - Page 5:
2025-05-31 06:00:12,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:12,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:12,939 - INFO - Response - Page 5:
2025-05-31 06:00:13,139 - INFO - 第 5 页获取到 100 条记录
2025-05-31 06:00:13,139 - INFO - Request Parameters - Page 6:
2025-05-31 06:00:13,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:13,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:13,697 - INFO - Response - Page 6:
2025-05-31 06:00:13,898 - INFO - 第 6 页获取到 100 条记录
2025-05-31 06:00:13,898 - INFO - Request Parameters - Page 7:
2025-05-31 06:00:13,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:13,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:14,343 - INFO - Response - Page 7:
2025-05-31 06:00:14,544 - INFO - 第 7 页获取到 70 条记录
2025-05-31 06:00:14,544 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 06:00:14,544 - INFO - 获取到 670 条表单数据
2025-05-31 06:00:14,556 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 06:00:14,568 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 06:00:14,568 - INFO - 开始处理日期: 2025-03
2025-05-31 06:00:14,568 - INFO - Request Parameters - Page 1:
2025-05-31 06:00:14,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:14,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:15,098 - INFO - Response - Page 1:
2025-05-31 06:00:15,298 - INFO - 第 1 页获取到 100 条记录
2025-05-31 06:00:15,298 - INFO - Request Parameters - Page 2:
2025-05-31 06:00:15,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:15,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:15,822 - INFO - Response - Page 2:
2025-05-31 06:00:16,023 - INFO - 第 2 页获取到 100 条记录
2025-05-31 06:00:16,023 - INFO - Request Parameters - Page 3:
2025-05-31 06:00:16,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:16,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:16,490 - INFO - Response - Page 3:
2025-05-31 06:00:16,691 - INFO - 第 3 页获取到 100 条记录
2025-05-31 06:00:16,691 - INFO - Request Parameters - Page 4:
2025-05-31 06:00:16,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:16,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:17,167 - INFO - Response - Page 4:
2025-05-31 06:00:17,367 - INFO - 第 4 页获取到 100 条记录
2025-05-31 06:00:17,367 - INFO - Request Parameters - Page 5:
2025-05-31 06:00:17,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:17,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:17,936 - INFO - Response - Page 5:
2025-05-31 06:00:18,136 - INFO - 第 5 页获取到 100 条记录
2025-05-31 06:00:18,136 - INFO - Request Parameters - Page 6:
2025-05-31 06:00:18,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:18,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:18,676 - INFO - Response - Page 6:
2025-05-31 06:00:18,876 - INFO - 第 6 页获取到 100 条记录
2025-05-31 06:00:18,876 - INFO - Request Parameters - Page 7:
2025-05-31 06:00:18,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:18,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:19,301 - INFO - Response - Page 7:
2025-05-31 06:00:19,501 - INFO - 第 7 页获取到 61 条记录
2025-05-31 06:00:19,501 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 06:00:19,501 - INFO - 获取到 661 条表单数据
2025-05-31 06:00:19,514 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 06:00:19,525 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 06:00:19,525 - INFO - 开始处理日期: 2025-04
2025-05-31 06:00:19,525 - INFO - Request Parameters - Page 1:
2025-05-31 06:00:19,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:19,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:20,130 - INFO - Response - Page 1:
2025-05-31 06:00:20,330 - INFO - 第 1 页获取到 100 条记录
2025-05-31 06:00:20,330 - INFO - Request Parameters - Page 2:
2025-05-31 06:00:20,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:20,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:20,819 - INFO - Response - Page 2:
2025-05-31 06:00:21,021 - INFO - 第 2 页获取到 100 条记录
2025-05-31 06:00:21,021 - INFO - Request Parameters - Page 3:
2025-05-31 06:00:21,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:21,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:21,520 - INFO - Response - Page 3:
2025-05-31 06:00:21,720 - INFO - 第 3 页获取到 100 条记录
2025-05-31 06:00:21,720 - INFO - Request Parameters - Page 4:
2025-05-31 06:00:21,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:21,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:22,256 - INFO - Response - Page 4:
2025-05-31 06:00:22,456 - INFO - 第 4 页获取到 100 条记录
2025-05-31 06:00:22,456 - INFO - Request Parameters - Page 5:
2025-05-31 06:00:22,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:22,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:22,994 - INFO - Response - Page 5:
2025-05-31 06:00:23,194 - INFO - 第 5 页获取到 100 条记录
2025-05-31 06:00:23,194 - INFO - Request Parameters - Page 6:
2025-05-31 06:00:23,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:23,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:23,795 - INFO - Response - Page 6:
2025-05-31 06:00:23,995 - INFO - 第 6 页获取到 100 条记录
2025-05-31 06:00:23,995 - INFO - Request Parameters - Page 7:
2025-05-31 06:00:23,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:23,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:24,476 - INFO - Response - Page 7:
2025-05-31 06:00:24,676 - INFO - 第 7 页获取到 56 条记录
2025-05-31 06:00:24,676 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 06:00:24,676 - INFO - 获取到 656 条表单数据
2025-05-31 06:00:24,688 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 06:00:24,699 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 06:00:24,699 - INFO - 开始处理日期: 2025-05
2025-05-31 06:00:24,699 - INFO - Request Parameters - Page 1:
2025-05-31 06:00:24,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:24,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:25,266 - INFO - Response - Page 1:
2025-05-31 06:00:25,466 - INFO - 第 1 页获取到 100 条记录
2025-05-31 06:00:25,466 - INFO - Request Parameters - Page 2:
2025-05-31 06:00:25,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:25,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:26,018 - INFO - Response - Page 2:
2025-05-31 06:00:26,218 - INFO - 第 2 页获取到 100 条记录
2025-05-31 06:00:26,218 - INFO - Request Parameters - Page 3:
2025-05-31 06:00:26,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:26,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:26,677 - INFO - Response - Page 3:
2025-05-31 06:00:26,877 - INFO - 第 3 页获取到 100 条记录
2025-05-31 06:00:26,877 - INFO - Request Parameters - Page 4:
2025-05-31 06:00:26,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:26,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:27,380 - INFO - Response - Page 4:
2025-05-31 06:00:27,580 - INFO - 第 4 页获取到 100 条记录
2025-05-31 06:00:27,580 - INFO - Request Parameters - Page 5:
2025-05-31 06:00:27,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:27,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:28,018 - INFO - Response - Page 5:
2025-05-31 06:00:28,218 - INFO - 第 5 页获取到 100 条记录
2025-05-31 06:00:28,218 - INFO - Request Parameters - Page 6:
2025-05-31 06:00:28,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:28,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:28,754 - INFO - Response - Page 6:
2025-05-31 06:00:28,954 - INFO - 第 6 页获取到 100 条记录
2025-05-31 06:00:28,954 - INFO - Request Parameters - Page 7:
2025-05-31 06:00:28,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 06:00:28,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 06:00:29,384 - INFO - Response - Page 7:
2025-05-31 06:00:29,584 - INFO - 第 7 页获取到 35 条记录
2025-05-31 06:00:29,584 - INFO - 查询完成，共获取到 635 条记录
2025-05-31 06:00:29,584 - INFO - 获取到 635 条表单数据
2025-05-31 06:00:29,596 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-31 06:00:29,606 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-31 06:00:30,047 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-31 06:00:30,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97323.0, 'new_value': 100062.0}, {'field': 'total_amount', 'old_value': 99173.0, 'new_value': 101912.0}, {'field': 'order_count', 'old_value': 553, 'new_value': 571}]
2025-05-31 06:00:30,048 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-31 06:00:30,048 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-31 06:00:30,050 - INFO - =================同步完成====================
2025-05-31 09:00:01,915 - INFO - =================使用默认全量同步=============
2025-05-31 09:00:03,431 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-31 09:00:03,432 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 09:00:03,460 - INFO - 开始处理日期: 2025-01
2025-05-31 09:00:03,463 - INFO - Request Parameters - Page 1:
2025-05-31 09:00:03,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:03,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:04,781 - INFO - Response - Page 1:
2025-05-31 09:00:04,981 - INFO - 第 1 页获取到 100 条记录
2025-05-31 09:00:04,981 - INFO - Request Parameters - Page 2:
2025-05-31 09:00:04,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:04,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:05,527 - INFO - Response - Page 2:
2025-05-31 09:00:05,727 - INFO - 第 2 页获取到 100 条记录
2025-05-31 09:00:05,727 - INFO - Request Parameters - Page 3:
2025-05-31 09:00:05,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:05,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:06,306 - INFO - Response - Page 3:
2025-05-31 09:00:06,506 - INFO - 第 3 页获取到 100 条记录
2025-05-31 09:00:06,506 - INFO - Request Parameters - Page 4:
2025-05-31 09:00:06,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:06,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:07,065 - INFO - Response - Page 4:
2025-05-31 09:00:07,266 - INFO - 第 4 页获取到 100 条记录
2025-05-31 09:00:07,266 - INFO - Request Parameters - Page 5:
2025-05-31 09:00:07,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:07,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:07,837 - INFO - Response - Page 5:
2025-05-31 09:00:08,039 - INFO - 第 5 页获取到 100 条记录
2025-05-31 09:00:08,039 - INFO - Request Parameters - Page 6:
2025-05-31 09:00:08,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:08,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:08,594 - INFO - Response - Page 6:
2025-05-31 09:00:08,795 - INFO - 第 6 页获取到 100 条记录
2025-05-31 09:00:08,795 - INFO - Request Parameters - Page 7:
2025-05-31 09:00:08,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:08,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:09,242 - INFO - Response - Page 7:
2025-05-31 09:00:09,442 - INFO - 第 7 页获取到 82 条记录
2025-05-31 09:00:09,442 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 09:00:09,442 - INFO - 获取到 682 条表单数据
2025-05-31 09:00:09,455 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 09:00:09,466 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 09:00:09,466 - INFO - 开始处理日期: 2025-02
2025-05-31 09:00:09,466 - INFO - Request Parameters - Page 1:
2025-05-31 09:00:09,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:09,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:09,966 - INFO - Response - Page 1:
2025-05-31 09:00:10,166 - INFO - 第 1 页获取到 100 条记录
2025-05-31 09:00:10,166 - INFO - Request Parameters - Page 2:
2025-05-31 09:00:10,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:10,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:10,676 - INFO - Response - Page 2:
2025-05-31 09:00:10,877 - INFO - 第 2 页获取到 100 条记录
2025-05-31 09:00:10,877 - INFO - Request Parameters - Page 3:
2025-05-31 09:00:10,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:10,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:11,497 - INFO - Response - Page 3:
2025-05-31 09:00:11,698 - INFO - 第 3 页获取到 100 条记录
2025-05-31 09:00:11,698 - INFO - Request Parameters - Page 4:
2025-05-31 09:00:11,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:11,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:12,199 - INFO - Response - Page 4:
2025-05-31 09:00:12,399 - INFO - 第 4 页获取到 100 条记录
2025-05-31 09:00:12,399 - INFO - Request Parameters - Page 5:
2025-05-31 09:00:12,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:12,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:12,948 - INFO - Response - Page 5:
2025-05-31 09:00:13,148 - INFO - 第 5 页获取到 100 条记录
2025-05-31 09:00:13,148 - INFO - Request Parameters - Page 6:
2025-05-31 09:00:13,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:13,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:13,808 - INFO - Response - Page 6:
2025-05-31 09:00:14,008 - INFO - 第 6 页获取到 100 条记录
2025-05-31 09:00:14,008 - INFO - Request Parameters - Page 7:
2025-05-31 09:00:14,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:14,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:14,510 - INFO - Response - Page 7:
2025-05-31 09:00:14,710 - INFO - 第 7 页获取到 70 条记录
2025-05-31 09:00:14,710 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 09:00:14,710 - INFO - 获取到 670 条表单数据
2025-05-31 09:00:14,725 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 09:00:14,737 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 09:00:14,737 - INFO - 开始处理日期: 2025-03
2025-05-31 09:00:14,737 - INFO - Request Parameters - Page 1:
2025-05-31 09:00:14,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:14,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:15,216 - INFO - Response - Page 1:
2025-05-31 09:00:15,416 - INFO - 第 1 页获取到 100 条记录
2025-05-31 09:00:15,416 - INFO - Request Parameters - Page 2:
2025-05-31 09:00:15,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:15,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:15,984 - INFO - Response - Page 2:
2025-05-31 09:00:16,184 - INFO - 第 2 页获取到 100 条记录
2025-05-31 09:00:16,184 - INFO - Request Parameters - Page 3:
2025-05-31 09:00:16,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:16,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:16,711 - INFO - Response - Page 3:
2025-05-31 09:00:16,911 - INFO - 第 3 页获取到 100 条记录
2025-05-31 09:00:16,911 - INFO - Request Parameters - Page 4:
2025-05-31 09:00:16,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:16,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:17,445 - INFO - Response - Page 4:
2025-05-31 09:00:17,645 - INFO - 第 4 页获取到 100 条记录
2025-05-31 09:00:17,645 - INFO - Request Parameters - Page 5:
2025-05-31 09:00:17,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:17,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:18,166 - INFO - Response - Page 5:
2025-05-31 09:00:18,367 - INFO - 第 5 页获取到 100 条记录
2025-05-31 09:00:18,367 - INFO - Request Parameters - Page 6:
2025-05-31 09:00:18,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:18,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:18,863 - INFO - Response - Page 6:
2025-05-31 09:00:19,064 - INFO - 第 6 页获取到 100 条记录
2025-05-31 09:00:19,064 - INFO - Request Parameters - Page 7:
2025-05-31 09:00:19,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:19,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:19,516 - INFO - Response - Page 7:
2025-05-31 09:00:19,716 - INFO - 第 7 页获取到 61 条记录
2025-05-31 09:00:19,716 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 09:00:19,716 - INFO - 获取到 661 条表单数据
2025-05-31 09:00:19,728 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 09:00:19,740 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 09:00:19,740 - INFO - 开始处理日期: 2025-04
2025-05-31 09:00:19,740 - INFO - Request Parameters - Page 1:
2025-05-31 09:00:19,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:19,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:20,318 - INFO - Response - Page 1:
2025-05-31 09:00:20,518 - INFO - 第 1 页获取到 100 条记录
2025-05-31 09:00:20,518 - INFO - Request Parameters - Page 2:
2025-05-31 09:00:20,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:20,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:21,022 - INFO - Response - Page 2:
2025-05-31 09:00:21,222 - INFO - 第 2 页获取到 100 条记录
2025-05-31 09:00:21,222 - INFO - Request Parameters - Page 3:
2025-05-31 09:00:21,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:21,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:21,711 - INFO - Response - Page 3:
2025-05-31 09:00:21,911 - INFO - 第 3 页获取到 100 条记录
2025-05-31 09:00:21,911 - INFO - Request Parameters - Page 4:
2025-05-31 09:00:21,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:21,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:22,399 - INFO - Response - Page 4:
2025-05-31 09:00:22,599 - INFO - 第 4 页获取到 100 条记录
2025-05-31 09:00:22,599 - INFO - Request Parameters - Page 5:
2025-05-31 09:00:22,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:22,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:23,136 - INFO - Response - Page 5:
2025-05-31 09:00:23,337 - INFO - 第 5 页获取到 100 条记录
2025-05-31 09:00:23,337 - INFO - Request Parameters - Page 6:
2025-05-31 09:00:23,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:23,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:23,815 - INFO - Response - Page 6:
2025-05-31 09:00:24,015 - INFO - 第 6 页获取到 100 条记录
2025-05-31 09:00:24,015 - INFO - Request Parameters - Page 7:
2025-05-31 09:00:24,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:24,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:24,466 - INFO - Response - Page 7:
2025-05-31 09:00:24,667 - INFO - 第 7 页获取到 56 条记录
2025-05-31 09:00:24,667 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 09:00:24,667 - INFO - 获取到 656 条表单数据
2025-05-31 09:00:24,680 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 09:00:24,691 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 09:00:24,691 - INFO - 开始处理日期: 2025-05
2025-05-31 09:00:24,692 - INFO - Request Parameters - Page 1:
2025-05-31 09:00:24,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:24,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:25,299 - INFO - Response - Page 1:
2025-05-31 09:00:25,499 - INFO - 第 1 页获取到 100 条记录
2025-05-31 09:00:25,499 - INFO - Request Parameters - Page 2:
2025-05-31 09:00:25,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:25,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:26,044 - INFO - Response - Page 2:
2025-05-31 09:00:26,244 - INFO - 第 2 页获取到 100 条记录
2025-05-31 09:00:26,244 - INFO - Request Parameters - Page 3:
2025-05-31 09:00:26,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:26,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:26,688 - INFO - Response - Page 3:
2025-05-31 09:00:26,889 - INFO - 第 3 页获取到 100 条记录
2025-05-31 09:00:26,889 - INFO - Request Parameters - Page 4:
2025-05-31 09:00:26,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:26,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:27,380 - INFO - Response - Page 4:
2025-05-31 09:00:27,580 - INFO - 第 4 页获取到 100 条记录
2025-05-31 09:00:27,580 - INFO - Request Parameters - Page 5:
2025-05-31 09:00:27,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:27,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:28,146 - INFO - Response - Page 5:
2025-05-31 09:00:28,346 - INFO - 第 5 页获取到 100 条记录
2025-05-31 09:00:28,346 - INFO - Request Parameters - Page 6:
2025-05-31 09:00:28,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:28,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:28,891 - INFO - Response - Page 6:
2025-05-31 09:00:29,091 - INFO - 第 6 页获取到 100 条记录
2025-05-31 09:00:29,091 - INFO - Request Parameters - Page 7:
2025-05-31 09:00:29,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 09:00:29,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 09:00:29,456 - INFO - Response - Page 7:
2025-05-31 09:00:29,657 - INFO - 第 7 页获取到 35 条记录
2025-05-31 09:00:29,657 - INFO - 查询完成，共获取到 635 条记录
2025-05-31 09:00:29,657 - INFO - 获取到 635 条表单数据
2025-05-31 09:00:29,668 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-31 09:00:29,676 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-31 09:00:30,125 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-31 09:00:30,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2430000.0, 'new_value': 2480000.0}, {'field': 'total_amount', 'old_value': 2430000.0, 'new_value': 2480000.0}, {'field': 'order_count', 'old_value': 286, 'new_value': 287}]
2025-05-31 09:00:30,126 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-31 09:00:30,597 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-31 09:00:30,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102229.56, 'new_value': 105094.56}, {'field': 'total_amount', 'old_value': 102229.56, 'new_value': 105094.56}, {'field': 'order_count', 'old_value': 5260, 'new_value': 5374}]
2025-05-31 09:00:30,600 - INFO - 开始更新记录 - 表单实例ID: FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B
2025-05-31 09:00:31,066 - INFO - 更新表单数据成功: FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B
2025-05-31 09:00:31,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4356.0, 'new_value': 12090.0}, {'field': 'offline_amount', 'old_value': 222.0, 'new_value': 321.0}, {'field': 'total_amount', 'old_value': 4578.0, 'new_value': 12411.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 594}]
2025-05-31 09:00:31,067 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-31 09:00:31,067 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-05-31 09:00:31,068 - INFO - =================同步完成====================
2025-05-31 12:00:01,932 - INFO - =================使用默认全量同步=============
2025-05-31 12:00:03,435 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-31 12:00:03,436 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 12:00:03,465 - INFO - 开始处理日期: 2025-01
2025-05-31 12:00:03,468 - INFO - Request Parameters - Page 1:
2025-05-31 12:00:03,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:03,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:04,498 - INFO - Response - Page 1:
2025-05-31 12:00:04,698 - INFO - 第 1 页获取到 100 条记录
2025-05-31 12:00:04,698 - INFO - Request Parameters - Page 2:
2025-05-31 12:00:04,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:04,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:05,253 - INFO - Response - Page 2:
2025-05-31 12:00:05,453 - INFO - 第 2 页获取到 100 条记录
2025-05-31 12:00:05,453 - INFO - Request Parameters - Page 3:
2025-05-31 12:00:05,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:05,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:06,165 - INFO - Response - Page 3:
2025-05-31 12:00:06,365 - INFO - 第 3 页获取到 100 条记录
2025-05-31 12:00:06,365 - INFO - Request Parameters - Page 4:
2025-05-31 12:00:06,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:06,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:06,932 - INFO - Response - Page 4:
2025-05-31 12:00:07,132 - INFO - 第 4 页获取到 100 条记录
2025-05-31 12:00:07,132 - INFO - Request Parameters - Page 5:
2025-05-31 12:00:07,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:07,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:07,624 - INFO - Response - Page 5:
2025-05-31 12:00:07,824 - INFO - 第 5 页获取到 100 条记录
2025-05-31 12:00:07,824 - INFO - Request Parameters - Page 6:
2025-05-31 12:00:07,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:07,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:08,375 - INFO - Response - Page 6:
2025-05-31 12:00:08,576 - INFO - 第 6 页获取到 100 条记录
2025-05-31 12:00:08,576 - INFO - Request Parameters - Page 7:
2025-05-31 12:00:08,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:08,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:09,333 - INFO - Response - Page 7:
2025-05-31 12:00:09,533 - INFO - 第 7 页获取到 82 条记录
2025-05-31 12:00:09,533 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 12:00:09,533 - INFO - 获取到 682 条表单数据
2025-05-31 12:00:09,548 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 12:00:09,565 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 12:00:09,566 - INFO - 开始处理日期: 2025-02
2025-05-31 12:00:09,566 - INFO - Request Parameters - Page 1:
2025-05-31 12:00:09,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:09,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:10,048 - INFO - Response - Page 1:
2025-05-31 12:00:10,248 - INFO - 第 1 页获取到 100 条记录
2025-05-31 12:00:10,248 - INFO - Request Parameters - Page 2:
2025-05-31 12:00:10,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:10,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:10,850 - INFO - Response - Page 2:
2025-05-31 12:00:11,051 - INFO - 第 2 页获取到 100 条记录
2025-05-31 12:00:11,051 - INFO - Request Parameters - Page 3:
2025-05-31 12:00:11,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:11,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:11,557 - INFO - Response - Page 3:
2025-05-31 12:00:11,758 - INFO - 第 3 页获取到 100 条记录
2025-05-31 12:00:11,758 - INFO - Request Parameters - Page 4:
2025-05-31 12:00:11,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:11,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:12,298 - INFO - Response - Page 4:
2025-05-31 12:00:12,498 - INFO - 第 4 页获取到 100 条记录
2025-05-31 12:00:12,498 - INFO - Request Parameters - Page 5:
2025-05-31 12:00:12,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:12,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:13,063 - INFO - Response - Page 5:
2025-05-31 12:00:13,264 - INFO - 第 5 页获取到 100 条记录
2025-05-31 12:00:13,264 - INFO - Request Parameters - Page 6:
2025-05-31 12:00:13,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:13,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:13,827 - INFO - Response - Page 6:
2025-05-31 12:00:14,027 - INFO - 第 6 页获取到 100 条记录
2025-05-31 12:00:14,027 - INFO - Request Parameters - Page 7:
2025-05-31 12:00:14,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:14,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:14,496 - INFO - Response - Page 7:
2025-05-31 12:00:14,697 - INFO - 第 7 页获取到 70 条记录
2025-05-31 12:00:14,697 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 12:00:14,697 - INFO - 获取到 670 条表单数据
2025-05-31 12:00:14,711 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 12:00:14,723 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 12:00:14,723 - INFO - 开始处理日期: 2025-03
2025-05-31 12:00:14,723 - INFO - Request Parameters - Page 1:
2025-05-31 12:00:14,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:14,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:15,332 - INFO - Response - Page 1:
2025-05-31 12:00:15,532 - INFO - 第 1 页获取到 100 条记录
2025-05-31 12:00:15,532 - INFO - Request Parameters - Page 2:
2025-05-31 12:00:15,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:15,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:16,024 - INFO - Response - Page 2:
2025-05-31 12:00:16,225 - INFO - 第 2 页获取到 100 条记录
2025-05-31 12:00:16,225 - INFO - Request Parameters - Page 3:
2025-05-31 12:00:16,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:16,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:16,735 - INFO - Response - Page 3:
2025-05-31 12:00:16,935 - INFO - 第 3 页获取到 100 条记录
2025-05-31 12:00:16,935 - INFO - Request Parameters - Page 4:
2025-05-31 12:00:16,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:16,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:17,453 - INFO - Response - Page 4:
2025-05-31 12:00:17,654 - INFO - 第 4 页获取到 100 条记录
2025-05-31 12:00:17,654 - INFO - Request Parameters - Page 5:
2025-05-31 12:00:17,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:17,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:18,140 - INFO - Response - Page 5:
2025-05-31 12:00:18,341 - INFO - 第 5 页获取到 100 条记录
2025-05-31 12:00:18,341 - INFO - Request Parameters - Page 6:
2025-05-31 12:00:18,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:18,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:18,881 - INFO - Response - Page 6:
2025-05-31 12:00:19,081 - INFO - 第 6 页获取到 100 条记录
2025-05-31 12:00:19,081 - INFO - Request Parameters - Page 7:
2025-05-31 12:00:19,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:19,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:19,540 - INFO - Response - Page 7:
2025-05-31 12:00:19,740 - INFO - 第 7 页获取到 61 条记录
2025-05-31 12:00:19,740 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 12:00:19,740 - INFO - 获取到 661 条表单数据
2025-05-31 12:00:19,752 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 12:00:19,764 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 12:00:19,764 - INFO - 开始处理日期: 2025-04
2025-05-31 12:00:19,764 - INFO - Request Parameters - Page 1:
2025-05-31 12:00:19,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:19,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:20,314 - INFO - Response - Page 1:
2025-05-31 12:00:20,514 - INFO - 第 1 页获取到 100 条记录
2025-05-31 12:00:20,514 - INFO - Request Parameters - Page 2:
2025-05-31 12:00:20,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:20,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:21,083 - INFO - Response - Page 2:
2025-05-31 12:00:21,283 - INFO - 第 2 页获取到 100 条记录
2025-05-31 12:00:21,283 - INFO - Request Parameters - Page 3:
2025-05-31 12:00:21,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:21,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:21,841 - INFO - Response - Page 3:
2025-05-31 12:00:22,041 - INFO - 第 3 页获取到 100 条记录
2025-05-31 12:00:22,041 - INFO - Request Parameters - Page 4:
2025-05-31 12:00:22,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:22,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:22,687 - INFO - Response - Page 4:
2025-05-31 12:00:22,888 - INFO - 第 4 页获取到 100 条记录
2025-05-31 12:00:22,888 - INFO - Request Parameters - Page 5:
2025-05-31 12:00:22,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:22,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:23,440 - INFO - Response - Page 5:
2025-05-31 12:00:23,640 - INFO - 第 5 页获取到 100 条记录
2025-05-31 12:00:23,640 - INFO - Request Parameters - Page 6:
2025-05-31 12:00:23,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:23,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:24,310 - INFO - Response - Page 6:
2025-05-31 12:00:24,512 - INFO - 第 6 页获取到 100 条记录
2025-05-31 12:00:24,512 - INFO - Request Parameters - Page 7:
2025-05-31 12:00:24,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:24,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:24,971 - INFO - Response - Page 7:
2025-05-31 12:00:25,173 - INFO - 第 7 页获取到 56 条记录
2025-05-31 12:00:25,173 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 12:00:25,173 - INFO - 获取到 656 条表单数据
2025-05-31 12:00:25,185 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 12:00:25,197 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 12:00:25,197 - INFO - 开始处理日期: 2025-05
2025-05-31 12:00:25,197 - INFO - Request Parameters - Page 1:
2025-05-31 12:00:25,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:25,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:25,684 - INFO - Response - Page 1:
2025-05-31 12:00:25,885 - INFO - 第 1 页获取到 100 条记录
2025-05-31 12:00:25,885 - INFO - Request Parameters - Page 2:
2025-05-31 12:00:25,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:25,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:26,402 - INFO - Response - Page 2:
2025-05-31 12:00:26,602 - INFO - 第 2 页获取到 100 条记录
2025-05-31 12:00:26,602 - INFO - Request Parameters - Page 3:
2025-05-31 12:00:26,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:26,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:27,138 - INFO - Response - Page 3:
2025-05-31 12:00:27,340 - INFO - 第 3 页获取到 100 条记录
2025-05-31 12:00:27,340 - INFO - Request Parameters - Page 4:
2025-05-31 12:00:27,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:27,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:27,897 - INFO - Response - Page 4:
2025-05-31 12:00:28,097 - INFO - 第 4 页获取到 100 条记录
2025-05-31 12:00:28,097 - INFO - Request Parameters - Page 5:
2025-05-31 12:00:28,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:28,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:28,670 - INFO - Response - Page 5:
2025-05-31 12:00:28,870 - INFO - 第 5 页获取到 100 条记录
2025-05-31 12:00:28,870 - INFO - Request Parameters - Page 6:
2025-05-31 12:00:28,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:28,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:29,660 - INFO - Response - Page 6:
2025-05-31 12:00:29,860 - INFO - 第 6 页获取到 100 条记录
2025-05-31 12:00:29,860 - INFO - Request Parameters - Page 7:
2025-05-31 12:00:29,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 12:00:29,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 12:00:30,263 - INFO - Response - Page 7:
2025-05-31 12:00:30,464 - INFO - 第 7 页获取到 35 条记录
2025-05-31 12:00:30,464 - INFO - 查询完成，共获取到 635 条记录
2025-05-31 12:00:30,464 - INFO - 获取到 635 条表单数据
2025-05-31 12:00:30,477 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-31 12:00:30,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-31 12:00:31,004 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-31 12:00:31,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 323200.0, 'new_value': 336400.0}, {'field': 'total_amount', 'old_value': 323200.0, 'new_value': 336400.0}]
2025-05-31 12:00:31,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-31 12:00:31,461 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-31 12:00:31,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2427.0, 'new_value': 2637.0}, {'field': 'offline_amount', 'old_value': 47143.0, 'new_value': 48343.0}, {'field': 'total_amount', 'old_value': 49570.0, 'new_value': 50980.0}, {'field': 'order_count', 'old_value': 684, 'new_value': 699}]
2025-05-31 12:00:31,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-31 12:00:31,913 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-31 12:00:31,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 419865.0, 'new_value': 427293.0}, {'field': 'total_amount', 'old_value': 419865.0, 'new_value': 427293.0}, {'field': 'order_count', 'old_value': 313, 'new_value': 321}]
2025-05-31 12:00:31,914 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-31 12:00:32,426 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-31 12:00:32,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11700080.0, 'new_value': 12100080.0}, {'field': 'total_amount', 'old_value': 11800080.0, 'new_value': 12200080.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-05-31 12:00:32,427 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-31 12:00:32,796 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-31 12:00:32,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60685.0, 'new_value': 62275.0}, {'field': 'total_amount', 'old_value': 62275.0, 'new_value': 63865.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 241}]
2025-05-31 12:00:32,796 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-31 12:00:33,249 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-31 12:00:33,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 842233.98, 'new_value': 869622.98}, {'field': 'total_amount', 'old_value': 842233.98, 'new_value': 869622.98}, {'field': 'order_count', 'old_value': 2653, 'new_value': 2735}]
2025-05-31 12:00:33,250 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-31 12:00:33,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-31 12:00:33,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126395.0, 'new_value': 132220.0}, {'field': 'total_amount', 'old_value': 126395.0, 'new_value': 132220.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-31 12:00:33,697 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-31 12:00:34,135 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-31 12:00:34,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94778.0, 'new_value': 99139.0}, {'field': 'total_amount', 'old_value': 97675.0, 'new_value': 102036.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-05-31 12:00:34,136 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-31 12:00:34,617 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-31 12:00:34,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74786.83, 'new_value': 77472.83}, {'field': 'total_amount', 'old_value': 81093.34, 'new_value': 83779.34}, {'field': 'order_count', 'old_value': 2951, 'new_value': 2971}]
2025-05-31 12:00:34,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-31 12:00:35,093 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-31 12:00:35,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39543.72, 'new_value': 40660.0}, {'field': 'offline_amount', 'old_value': 18283.84, 'new_value': 18600.64}, {'field': 'total_amount', 'old_value': 57827.56, 'new_value': 59260.64}, {'field': 'order_count', 'old_value': 2876, 'new_value': 2948}]
2025-05-31 12:00:35,093 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-31 12:00:35,592 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-31 12:00:35,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58797.78, 'new_value': 58887.78}, {'field': 'total_amount', 'old_value': 58797.78, 'new_value': 58887.78}, {'field': 'order_count', 'old_value': 138, 'new_value': 139}]
2025-05-31 12:00:35,592 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-31 12:00:36,077 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-31 12:00:36,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 461340.0, 'new_value': 486200.0}, {'field': 'total_amount', 'old_value': 461340.0, 'new_value': 486200.0}, {'field': 'order_count', 'old_value': 282, 'new_value': 299}]
2025-05-31 12:00:36,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-31 12:00:36,510 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-31 12:00:36,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 486633.0, 'new_value': 506204.0}, {'field': 'offline_amount', 'old_value': 380676.0, 'new_value': 387659.0}, {'field': 'total_amount', 'old_value': 867309.0, 'new_value': 893863.0}, {'field': 'order_count', 'old_value': 942, 'new_value': 972}]
2025-05-31 12:00:36,511 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-31 12:00:36,997 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-31 12:00:36,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126330.0, 'new_value': 128536.0}, {'field': 'total_amount', 'old_value': 126330.0, 'new_value': 128536.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 146}]
2025-05-31 12:00:36,998 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-31 12:00:37,608 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-31 12:00:37,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112010.84, 'new_value': 132313.0}, {'field': 'total_amount', 'old_value': 112010.84, 'new_value': 132313.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 22}]
2025-05-31 12:00:37,608 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-31 12:00:38,124 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-31 12:00:38,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60620.0, 'new_value': 62460.0}, {'field': 'total_amount', 'old_value': 64740.0, 'new_value': 66580.0}, {'field': 'order_count', 'old_value': 628, 'new_value': 647}]
2025-05-31 12:00:38,125 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-31 12:00:38,528 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-31 12:00:38,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31681.8, 'new_value': 36166.8}, {'field': 'total_amount', 'old_value': 37976.4, 'new_value': 42461.4}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-05-31 12:00:38,529 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-31 12:00:38,961 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-31 12:00:38,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295317.0, 'new_value': 322798.0}, {'field': 'total_amount', 'old_value': 295317.0, 'new_value': 322798.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 62}]
2025-05-31 12:00:38,961 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-31 12:00:39,383 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-31 12:00:39,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66371.43, 'new_value': 68034.97}, {'field': 'offline_amount', 'old_value': 131117.35, 'new_value': 135299.35}, {'field': 'total_amount', 'old_value': 197488.78, 'new_value': 203334.32}, {'field': 'order_count', 'old_value': 2266, 'new_value': 2331}]
2025-05-31 12:00:39,383 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-31 12:00:39,820 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-31 12:00:39,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24055.77, 'new_value': 24773.9}, {'field': 'offline_amount', 'old_value': 29080.57, 'new_value': 30097.82}, {'field': 'total_amount', 'old_value': 53136.34, 'new_value': 54871.72}, {'field': 'order_count', 'old_value': 2634, 'new_value': 2738}]
2025-05-31 12:00:39,821 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-31 12:00:40,245 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-31 12:00:40,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 324586.3, 'new_value': 332991.8}, {'field': 'total_amount', 'old_value': 439606.0, 'new_value': 448011.5}, {'field': 'order_count', 'old_value': 3750, 'new_value': 3873}]
2025-05-31 12:00:40,245 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-31 12:00:40,647 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-31 12:00:40,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121035.4, 'new_value': 125662.7}, {'field': 'total_amount', 'old_value': 121035.4, 'new_value': 125662.7}, {'field': 'order_count', 'old_value': 6687, 'new_value': 6948}]
2025-05-31 12:00:40,648 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-31 12:00:41,101 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-31 12:00:41,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78204.0, 'new_value': 82004.0}, {'field': 'total_amount', 'old_value': 78503.92, 'new_value': 82303.92}, {'field': 'order_count', 'old_value': 121, 'new_value': 126}]
2025-05-31 12:00:41,101 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-31 12:00:41,549 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-31 12:00:41,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 484790.71, 'new_value': 509246.71}, {'field': 'total_amount', 'old_value': 517973.71, 'new_value': 542429.71}, {'field': 'order_count', 'old_value': 499, 'new_value': 511}]
2025-05-31 12:00:41,549 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-31 12:00:42,051 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-31 12:00:42,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163736.11, 'new_value': 169908.26}, {'field': 'total_amount', 'old_value': 163736.11, 'new_value': 169908.26}, {'field': 'order_count', 'old_value': 1928, 'new_value': 2009}]
2025-05-31 12:00:42,051 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-31 12:00:42,505 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-31 12:00:42,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1353062.0, 'new_value': 1403040.0}, {'field': 'offline_amount', 'old_value': 397427.0, 'new_value': 408417.0}, {'field': 'total_amount', 'old_value': 1750489.0, 'new_value': 1811457.0}, {'field': 'order_count', 'old_value': 2000, 'new_value': 2069}]
2025-05-31 12:00:42,505 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-31 12:00:43,007 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-31 12:00:43,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14541.54, 'new_value': 15253.88}, {'field': 'offline_amount', 'old_value': 42156.21, 'new_value': 42823.41}, {'field': 'total_amount', 'old_value': 56697.75, 'new_value': 58077.29}, {'field': 'order_count', 'old_value': 1054, 'new_value': 1086}]
2025-05-31 12:00:43,007 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-31 12:00:43,569 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-31 12:00:43,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73934.0, 'new_value': 75533.0}, {'field': 'total_amount', 'old_value': 111534.0, 'new_value': 113133.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-31 12:00:43,569 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-31 12:00:43,988 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-31 12:00:43,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7624.8, 'new_value': 7804.5}, {'field': 'offline_amount', 'old_value': 86081.7, 'new_value': 86275.7}, {'field': 'total_amount', 'old_value': 93706.5, 'new_value': 94080.2}, {'field': 'order_count', 'old_value': 2124, 'new_value': 2128}]
2025-05-31 12:00:43,988 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-31 12:00:44,472 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-31 12:00:44,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 288605.0, 'new_value': 294405.0}, {'field': 'total_amount', 'old_value': 318605.0, 'new_value': 324405.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-31 12:00:44,472 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-31 12:00:44,965 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-31 12:00:44,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 346521.89, 'new_value': 401268.89}, {'field': 'total_amount', 'old_value': 386521.89, 'new_value': 441268.89}, {'field': 'order_count', 'old_value': 66, 'new_value': 72}]
2025-05-31 12:00:44,966 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-31 12:00:45,410 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-31 12:00:45,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317395.13, 'new_value': 352730.13}, {'field': 'total_amount', 'old_value': 386755.13, 'new_value': 422090.13}, {'field': 'order_count', 'old_value': 57, 'new_value': 62}]
2025-05-31 12:00:45,411 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-31 12:00:45,866 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-31 12:00:45,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1010625.1, 'new_value': 1042526.5}, {'field': 'total_amount', 'old_value': 1070321.1, 'new_value': 1102222.5}, {'field': 'order_count', 'old_value': 104, 'new_value': 106}]
2025-05-31 12:00:45,867 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-31 12:00:46,267 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-31 12:00:46,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60115.6, 'new_value': 61115.6}, {'field': 'total_amount', 'old_value': 76038.3, 'new_value': 77038.3}, {'field': 'order_count', 'old_value': 747, 'new_value': 748}]
2025-05-31 12:00:46,268 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-31 12:00:46,710 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-31 12:00:46,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195517.0, 'new_value': 199570.0}, {'field': 'total_amount', 'old_value': 195517.0, 'new_value': 199570.0}, {'field': 'order_count', 'old_value': 405, 'new_value': 422}]
2025-05-31 12:00:46,710 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-31 12:00:47,173 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-31 12:00:47,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199343.87, 'new_value': 202881.04}, {'field': 'total_amount', 'old_value': 199343.87, 'new_value': 202881.04}, {'field': 'order_count', 'old_value': 314, 'new_value': 324}]
2025-05-31 12:00:47,173 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-31 12:00:47,643 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-31 12:00:47,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91575.0, 'new_value': 94834.0}, {'field': 'total_amount', 'old_value': 91575.0, 'new_value': 94834.0}, {'field': 'order_count', 'old_value': 779, 'new_value': 807}]
2025-05-31 12:00:47,644 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-31 12:00:48,044 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-31 12:00:48,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50243.98, 'new_value': 50990.62}, {'field': 'total_amount', 'old_value': 50243.98, 'new_value': 50990.62}, {'field': 'order_count', 'old_value': 3673, 'new_value': 3741}]
2025-05-31 12:00:48,045 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-31 12:00:48,429 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-31 12:00:48,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195644.83, 'new_value': 202277.9}, {'field': 'offline_amount', 'old_value': 32547.38, 'new_value': 33769.52}, {'field': 'total_amount', 'old_value': 228192.21, 'new_value': 236047.42}, {'field': 'order_count', 'old_value': 846, 'new_value': 877}]
2025-05-31 12:00:48,429 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-31 12:00:48,880 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-31 12:00:48,880 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 187361.0, 'new_value': 191609.0}, {'field': 'offline_amount', 'old_value': 70738.65, 'new_value': 73073.63}, {'field': 'total_amount', 'old_value': 258099.65, 'new_value': 264682.63}, {'field': 'order_count', 'old_value': 1697, 'new_value': 1740}]
2025-05-31 12:00:48,880 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-31 12:00:49,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-31 12:00:49,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6376.0, 'new_value': 10145.0}, {'field': 'offline_amount', 'old_value': 46530.0, 'new_value': 48601.0}, {'field': 'total_amount', 'old_value': 52906.0, 'new_value': 58746.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 273}]
2025-05-31 12:00:49,360 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-31 12:00:49,770 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-31 12:00:49,770 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1493.0, 'new_value': 2293.0}, {'field': 'offline_amount', 'old_value': 10447.0, 'new_value': 10744.0}, {'field': 'total_amount', 'old_value': 11940.0, 'new_value': 13037.0}, {'field': 'order_count', 'old_value': 247, 'new_value': 259}]
2025-05-31 12:00:49,770 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-31 12:00:50,197 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-31 12:00:50,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106694.27, 'new_value': 109426.28}, {'field': 'total_amount', 'old_value': 106694.27, 'new_value': 109426.28}, {'field': 'order_count', 'old_value': 2908, 'new_value': 2987}]
2025-05-31 12:00:50,197 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-31 12:00:50,678 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-31 12:00:50,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78490.1, 'new_value': 81551.6}, {'field': 'total_amount', 'old_value': 78490.1, 'new_value': 81551.6}, {'field': 'order_count', 'old_value': 145, 'new_value': 151}]
2025-05-31 12:00:50,678 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-31 12:00:51,100 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-31 12:00:51,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12861.0, 'new_value': 12862.0}, {'field': 'total_amount', 'old_value': 12861.0, 'new_value': 12862.0}, {'field': 'order_count', 'old_value': 309, 'new_value': 989}]
2025-05-31 12:00:51,101 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-31 12:00:51,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-31 12:00:51,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246405.3, 'new_value': 249561.3}, {'field': 'total_amount', 'old_value': 246405.3, 'new_value': 249561.3}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-05-31 12:00:51,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-31 12:00:52,031 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-31 12:00:52,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 178261.0, 'new_value': 183737.0}, {'field': 'offline_amount', 'old_value': 78310.18, 'new_value': 79837.18}, {'field': 'total_amount', 'old_value': 256571.18, 'new_value': 263574.18}, {'field': 'order_count', 'old_value': 1817, 'new_value': 1862}]
2025-05-31 12:00:52,032 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-31 12:00:52,471 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-31 12:00:52,472 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63005.9, 'new_value': 64850.95}, {'field': 'offline_amount', 'old_value': 610987.06, 'new_value': 633894.42}, {'field': 'total_amount', 'old_value': 673992.96, 'new_value': 698745.37}, {'field': 'order_count', 'old_value': 2166, 'new_value': 2248}]
2025-05-31 12:00:52,472 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-31 12:00:52,925 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-31 12:00:52,925 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12809.12, 'new_value': 13376.31}, {'field': 'offline_amount', 'old_value': 199883.8, 'new_value': 207433.65}, {'field': 'total_amount', 'old_value': 212692.92, 'new_value': 220809.96}, {'field': 'order_count', 'old_value': 2346, 'new_value': 2440}]
2025-05-31 12:00:52,925 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-31 12:00:53,389 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-31 12:00:53,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58156.6, 'new_value': 58983.7}, {'field': 'total_amount', 'old_value': 58237.4, 'new_value': 59064.5}, {'field': 'order_count', 'old_value': 349, 'new_value': 353}]
2025-05-31 12:00:53,389 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-31 12:00:53,835 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-31 12:00:53,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99877.0, 'new_value': 101119.0}, {'field': 'total_amount', 'old_value': 301225.0, 'new_value': 302467.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 91}]
2025-05-31 12:00:53,836 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-31 12:00:54,257 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-31 12:00:54,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35746.0, 'new_value': 36443.0}, {'field': 'total_amount', 'old_value': 37446.0, 'new_value': 38143.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 144}]
2025-05-31 12:00:54,257 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-31 12:00:54,668 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-31 12:00:54,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8051.83, 'new_value': 8140.22}, {'field': 'offline_amount', 'old_value': 118936.62, 'new_value': 126934.22}, {'field': 'total_amount', 'old_value': 126988.45, 'new_value': 135074.44}, {'field': 'order_count', 'old_value': 3093, 'new_value': 3261}]
2025-05-31 12:00:54,669 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-31 12:00:55,169 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-31 12:00:55,170 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 253936.0, 'new_value': 265699.0}, {'field': 'total_amount', 'old_value': 253936.0, 'new_value': 265699.0}, {'field': 'order_count', 'old_value': 1343, 'new_value': 1399}]
2025-05-31 12:00:55,170 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-31 12:00:55,724 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-31 12:00:55,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238052.38, 'new_value': 244148.19}, {'field': 'total_amount', 'old_value': 238052.38, 'new_value': 244148.19}, {'field': 'order_count', 'old_value': 815, 'new_value': 842}]
2025-05-31 12:00:55,724 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-31 12:00:56,085 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-31 12:00:56,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101875.51, 'new_value': 105504.57}, {'field': 'offline_amount', 'old_value': 50312.81, 'new_value': 51905.31}, {'field': 'total_amount', 'old_value': 152188.32, 'new_value': 157409.88}, {'field': 'order_count', 'old_value': 5313, 'new_value': 5506}]
2025-05-31 12:00:56,085 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-31 12:00:56,586 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-31 12:00:56,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168411.0, 'new_value': 174923.0}, {'field': 'total_amount', 'old_value': 168411.0, 'new_value': 174923.0}, {'field': 'order_count', 'old_value': 4274, 'new_value': 4448}]
2025-05-31 12:00:56,586 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-31 12:00:57,102 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-31 12:00:57,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99040.92, 'new_value': 102328.17}, {'field': 'offline_amount', 'old_value': 1007077.65, 'new_value': 1044168.85}, {'field': 'total_amount', 'old_value': 1106118.57, 'new_value': 1146497.02}, {'field': 'order_count', 'old_value': 3474, 'new_value': 3604}]
2025-05-31 12:00:57,102 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-31 12:00:57,533 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-31 12:00:57,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 197458.0, 'new_value': 204944.0}, {'field': 'total_amount', 'old_value': 197458.0, 'new_value': 204944.0}, {'field': 'order_count', 'old_value': 7460, 'new_value': 7751}]
2025-05-31 12:00:57,533 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-31 12:00:58,002 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-31 12:00:58,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 802235.28, 'new_value': 828646.5}, {'field': 'total_amount', 'old_value': 802235.28, 'new_value': 828646.5}, {'field': 'order_count', 'old_value': 4798, 'new_value': 5040}]
2025-05-31 12:00:58,003 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-31 12:00:58,443 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-31 12:00:58,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64854.0, 'new_value': 66348.0}, {'field': 'total_amount', 'old_value': 64854.0, 'new_value': 66348.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 80}]
2025-05-31 12:00:58,444 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-31 12:00:58,889 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-31 12:00:58,889 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34346.0, 'new_value': 35032.0}, {'field': 'offline_amount', 'old_value': 549993.2, 'new_value': 553941.2}, {'field': 'total_amount', 'old_value': 584339.2, 'new_value': 588973.2}, {'field': 'order_count', 'old_value': 99, 'new_value': 100}]
2025-05-31 12:00:58,889 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-31 12:00:59,375 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-31 12:00:59,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98153.0, 'new_value': 100453.0}, {'field': 'total_amount', 'old_value': 98153.0, 'new_value': 100453.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-31 12:00:59,375 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-31 12:00:59,833 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-31 12:00:59,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162625.23, 'new_value': 169757.09}, {'field': 'total_amount', 'old_value': 162625.23, 'new_value': 169757.09}, {'field': 'order_count', 'old_value': 5921, 'new_value': 6152}]
2025-05-31 12:00:59,833 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-31 12:01:00,330 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-31 12:01:00,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166296.0, 'new_value': 177692.0}, {'field': 'total_amount', 'old_value': 247201.0, 'new_value': 258597.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 95}]
2025-05-31 12:01:00,330 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-31 12:01:00,779 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-31 12:01:00,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42815.59, 'new_value': 42825.27}, {'field': 'total_amount', 'old_value': 76383.81, 'new_value': 76393.49}, {'field': 'order_count', 'old_value': 72, 'new_value': 74}]
2025-05-31 12:01:00,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-31 12:01:01,180 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-31 12:01:01,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22557.16, 'new_value': 23259.4}, {'field': 'total_amount', 'old_value': 22557.16, 'new_value': 23259.4}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-31 12:01:01,180 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-31 12:01:01,618 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-31 12:01:01,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 358300.95, 'new_value': 371102.08}, {'field': 'offline_amount', 'old_value': 887.0, 'new_value': 1111.0}, {'field': 'total_amount', 'old_value': 359187.95, 'new_value': 372213.08}, {'field': 'order_count', 'old_value': 4148, 'new_value': 4298}]
2025-05-31 12:01:01,619 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-31 12:01:02,102 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-31 12:01:02,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157548.07, 'new_value': 160998.85}, {'field': 'total_amount', 'old_value': 162450.18, 'new_value': 165900.96}, {'field': 'order_count', 'old_value': 753, 'new_value': 772}]
2025-05-31 12:01:02,103 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-31 12:01:02,595 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-31 12:01:02,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53319.0, 'new_value': 53927.0}, {'field': 'total_amount', 'old_value': 53319.0, 'new_value': 53927.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 120}]
2025-05-31 12:01:02,595 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-31 12:01:03,053 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-31 12:01:03,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153147.22, 'new_value': 161545.68}, {'field': 'offline_amount', 'old_value': 592049.17, 'new_value': 619267.17}, {'field': 'total_amount', 'old_value': 745196.39, 'new_value': 780812.85}, {'field': 'order_count', 'old_value': 966, 'new_value': 1004}]
2025-05-31 12:01:03,053 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-31 12:01:03,553 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-31 12:01:03,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207254.16, 'new_value': 215215.92}, {'field': 'total_amount', 'old_value': 207254.16, 'new_value': 215215.92}, {'field': 'order_count', 'old_value': 1190, 'new_value': 1244}]
2025-05-31 12:01:03,553 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-31 12:01:04,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-31 12:01:04,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 233074.63, 'new_value': 241804.37}, {'field': 'offline_amount', 'old_value': 468890.31, 'new_value': 479257.22}, {'field': 'total_amount', 'old_value': 701964.94, 'new_value': 721061.59}, {'field': 'order_count', 'old_value': 5290, 'new_value': 5471}]
2025-05-31 12:01:04,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-31 12:01:04,703 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-31 12:01:04,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7489.74, 'new_value': 8066.71}, {'field': 'offline_amount', 'old_value': 231636.34, 'new_value': 242469.0}, {'field': 'total_amount', 'old_value': 239126.08, 'new_value': 250535.71}, {'field': 'order_count', 'old_value': 1503, 'new_value': 1574}]
2025-05-31 12:01:04,703 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-31 12:01:05,149 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-31 12:01:05,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208564.0, 'new_value': 211211.0}, {'field': 'total_amount', 'old_value': 208564.0, 'new_value': 211211.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-31 12:01:05,149 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-31 12:01:05,515 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-31 12:01:05,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1905867.78, 'new_value': 1987248.38}, {'field': 'total_amount', 'old_value': 1959312.88, 'new_value': 2040693.48}, {'field': 'order_count', 'old_value': 3613, 'new_value': 3760}]
2025-05-31 12:01:05,516 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-31 12:01:05,955 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-31 12:01:05,956 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 284736.45, 'new_value': 302254.45}, {'field': 'offline_amount', 'old_value': 139858.0, 'new_value': 143189.0}, {'field': 'total_amount', 'old_value': 424594.45, 'new_value': 445443.45}, {'field': 'order_count', 'old_value': 2166, 'new_value': 2235}]
2025-05-31 12:01:05,956 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-31 12:01:06,413 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-31 12:01:06,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9201.0, 'new_value': 9300.0}, {'field': 'total_amount', 'old_value': 9201.0, 'new_value': 9300.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-31 12:01:06,413 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-31 12:01:06,843 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-31 12:01:06,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41508.9, 'new_value': 41665.9}, {'field': 'total_amount', 'old_value': 41508.9, 'new_value': 41665.9}, {'field': 'order_count', 'old_value': 185, 'new_value': 186}]
2025-05-31 12:01:06,843 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-31 12:01:07,274 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-31 12:01:07,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74397.0, 'new_value': 75100.0}, {'field': 'total_amount', 'old_value': 103043.0, 'new_value': 103746.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-31 12:01:07,274 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-31 12:01:07,718 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-31 12:01:07,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21585.0, 'new_value': 21817.0}, {'field': 'total_amount', 'old_value': 21585.0, 'new_value': 21817.0}, {'field': 'order_count', 'old_value': 372, 'new_value': 376}]
2025-05-31 12:01:07,718 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-31 12:01:08,206 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-31 12:01:08,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129012.0, 'new_value': 132098.0}, {'field': 'total_amount', 'old_value': 129015.0, 'new_value': 132101.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-05-31 12:01:08,207 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-31 12:01:08,752 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-31 12:01:08,752 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9987.95, 'new_value': 10307.95}, {'field': 'offline_amount', 'old_value': 20047.41, 'new_value': 20930.47}, {'field': 'total_amount', 'old_value': 30035.36, 'new_value': 31238.42}, {'field': 'order_count', 'old_value': 1009, 'new_value': 1048}]
2025-05-31 12:01:08,752 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-31 12:01:09,245 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-31 12:01:09,245 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 207474.36, 'new_value': 215045.33}, {'field': 'offline_amount', 'old_value': 167761.81, 'new_value': 172104.31}, {'field': 'total_amount', 'old_value': 375236.17, 'new_value': 387149.64}, {'field': 'order_count', 'old_value': 3415, 'new_value': 3525}]
2025-05-31 12:01:09,245 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-31 12:01:09,676 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-31 12:01:09,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56077.0, 'new_value': 65193.0}, {'field': 'total_amount', 'old_value': 56077.0, 'new_value': 65193.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-31 12:01:09,676 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-31 12:01:10,221 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-31 12:01:10,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 298621.37, 'new_value': 320374.33}, {'field': 'total_amount', 'old_value': 298644.47, 'new_value': 320397.43}, {'field': 'order_count', 'old_value': 61, 'new_value': 66}]
2025-05-31 12:01:10,221 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-31 12:01:10,713 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-31 12:01:10,713 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 422992.9, 'new_value': 440578.9}, {'field': 'offline_amount', 'old_value': 118632.1, 'new_value': 120604.1}, {'field': 'total_amount', 'old_value': 541625.0, 'new_value': 561183.0}, {'field': 'order_count', 'old_value': 685, 'new_value': 20243}]
2025-05-31 12:01:10,713 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-31 12:01:11,191 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-31 12:01:11,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31797.0, 'new_value': 34704.0}, {'field': 'total_amount', 'old_value': 31797.0, 'new_value': 34704.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 91}]
2025-05-31 12:01:11,191 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-31 12:01:11,601 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-31 12:01:11,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69895.0, 'new_value': 74703.0}, {'field': 'total_amount', 'old_value': 70644.0, 'new_value': 75452.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 64}]
2025-05-31 12:01:11,602 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-31 12:01:12,075 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-31 12:01:12,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81121.49, 'new_value': 81221.49}, {'field': 'total_amount', 'old_value': 84890.59, 'new_value': 84990.59}, {'field': 'order_count', 'old_value': 421, 'new_value': 423}]
2025-05-31 12:01:12,076 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-31 12:01:12,603 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-31 12:01:12,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190615.0, 'new_value': 199929.0}, {'field': 'total_amount', 'old_value': 262434.0, 'new_value': 271748.0}, {'field': 'order_count', 'old_value': 5871, 'new_value': 6085}]
2025-05-31 12:01:12,603 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-31 12:01:13,052 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-31 12:01:13,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15924.57, 'new_value': 16367.25}, {'field': 'offline_amount', 'old_value': 327846.88, 'new_value': 337982.28}, {'field': 'total_amount', 'old_value': 343771.45, 'new_value': 354349.53}, {'field': 'order_count', 'old_value': 2360, 'new_value': 2436}]
2025-05-31 12:01:13,052 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-31 12:01:13,492 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-31 12:01:13,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125364.64, 'new_value': 127568.64}, {'field': 'total_amount', 'old_value': 130704.64, 'new_value': 132908.64}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-31 12:01:13,492 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-31 12:01:13,991 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-31 12:01:13,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174998.36, 'new_value': 181430.68}, {'field': 'offline_amount', 'old_value': 126303.45, 'new_value': 128857.45}, {'field': 'total_amount', 'old_value': 301301.81, 'new_value': 310288.13}, {'field': 'order_count', 'old_value': 3001, 'new_value': 3110}]
2025-05-31 12:01:13,991 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-31 12:01:14,468 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-31 12:01:14,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 536052.04, 'new_value': 539352.04}, {'field': 'offline_amount', 'old_value': 248379.9, 'new_value': 251102.9}, {'field': 'total_amount', 'old_value': 784431.94, 'new_value': 790454.94}, {'field': 'order_count', 'old_value': 6845, 'new_value': 6869}]
2025-05-31 12:01:14,468 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-31 12:01:14,896 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-31 12:01:14,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43557.5, 'new_value': 44142.5}, {'field': 'offline_amount', 'old_value': 579.0, 'new_value': 580.0}, {'field': 'total_amount', 'old_value': 44136.5, 'new_value': 44722.5}, {'field': 'order_count', 'old_value': 180, 'new_value': 182}]
2025-05-31 12:01:14,897 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-31 12:01:15,365 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-31 12:01:15,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95587.7, 'new_value': 100327.6}, {'field': 'offline_amount', 'old_value': 11583.65, 'new_value': 12549.05}, {'field': 'total_amount', 'old_value': 107171.35, 'new_value': 112876.65}, {'field': 'order_count', 'old_value': 334, 'new_value': 345}]
2025-05-31 12:01:15,365 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-31 12:01:15,837 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-31 12:01:15,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78783.61, 'new_value': 80908.41}, {'field': 'total_amount', 'old_value': 78783.61, 'new_value': 80908.41}, {'field': 'order_count', 'old_value': 2285, 'new_value': 2352}]
2025-05-31 12:01:15,837 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-31 12:01:16,310 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-31 12:01:16,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11310.2, 'new_value': 11517.0}, {'field': 'offline_amount', 'old_value': 57512.1, 'new_value': 57650.1}, {'field': 'total_amount', 'old_value': 68822.3, 'new_value': 69167.1}, {'field': 'order_count', 'old_value': 87, 'new_value': 89}]
2025-05-31 12:01:16,310 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-31 12:01:16,771 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-31 12:01:16,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 746147.0, 'new_value': 841147.0}, {'field': 'total_amount', 'old_value': 746147.0, 'new_value': 841147.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-05-31 12:01:16,771 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-31 12:01:17,301 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-31 12:01:17,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161852.65, 'new_value': 168704.09}, {'field': 'offline_amount', 'old_value': 491183.67, 'new_value': 514628.66}, {'field': 'total_amount', 'old_value': 653036.32, 'new_value': 683332.75}, {'field': 'order_count', 'old_value': 3101, 'new_value': 3171}]
2025-05-31 12:01:17,301 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-31 12:01:17,806 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-31 12:01:17,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 478784.13, 'new_value': 492727.83}, {'field': 'total_amount', 'old_value': 478784.13, 'new_value': 492727.83}, {'field': 'order_count', 'old_value': 606, 'new_value': 622}]
2025-05-31 12:01:17,807 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-31 12:01:18,230 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-31 12:01:18,231 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19979.43, 'new_value': 21298.14}, {'field': 'offline_amount', 'old_value': 503831.58, 'new_value': 518060.64}, {'field': 'total_amount', 'old_value': 523811.01, 'new_value': 539358.78}, {'field': 'order_count', 'old_value': 2108, 'new_value': 2171}]
2025-05-31 12:01:18,231 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-31 12:01:18,779 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-31 12:01:18,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96975.0, 'new_value': 98886.0}, {'field': 'offline_amount', 'old_value': 96942.23, 'new_value': 99121.23}, {'field': 'total_amount', 'old_value': 193917.23, 'new_value': 198007.23}, {'field': 'order_count', 'old_value': 229, 'new_value': 239}]
2025-05-31 12:01:18,779 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-31 12:01:19,277 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-31 12:01:19,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183507.6, 'new_value': 191974.2}, {'field': 'total_amount', 'old_value': 183507.6, 'new_value': 191974.2}, {'field': 'order_count', 'old_value': 429, 'new_value': 440}]
2025-05-31 12:01:19,277 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-31 12:01:19,789 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-31 12:01:19,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 215993.94, 'new_value': 224669.49}, {'field': 'total_amount', 'old_value': 336428.78, 'new_value': 345104.33}, {'field': 'order_count', 'old_value': 3515, 'new_value': 3662}]
2025-05-31 12:01:19,790 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-31 12:01:20,238 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-31 12:01:20,239 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108121.12, 'new_value': 114817.15}, {'field': 'offline_amount', 'old_value': 1359162.22, 'new_value': 1416283.64}, {'field': 'total_amount', 'old_value': 1467283.34, 'new_value': 1531100.79}, {'field': 'order_count', 'old_value': 11837, 'new_value': 12276}]
2025-05-31 12:01:20,239 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-31 12:01:20,763 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-31 12:01:20,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51267.0, 'new_value': 51861.0}, {'field': 'total_amount', 'old_value': 51267.0, 'new_value': 51861.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 102}]
2025-05-31 12:01:20,763 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-31 12:01:21,205 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-31 12:01:21,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90236.59, 'new_value': 93477.59}, {'field': 'total_amount', 'old_value': 90236.59, 'new_value': 93477.59}, {'field': 'order_count', 'old_value': 515, 'new_value': 536}]
2025-05-31 12:01:21,206 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-31 12:01:21,649 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-31 12:01:21,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9546.54, 'new_value': 9769.69}, {'field': 'offline_amount', 'old_value': 36872.0, 'new_value': 37388.0}, {'field': 'total_amount', 'old_value': 46418.54, 'new_value': 47157.69}, {'field': 'order_count', 'old_value': 238, 'new_value': 246}]
2025-05-31 12:01:21,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-31 12:01:22,082 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-31 12:01:22,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52546.67, 'new_value': 53564.31}, {'field': 'total_amount', 'old_value': 53068.27, 'new_value': 54085.91}, {'field': 'order_count', 'old_value': 446, 'new_value': 460}]
2025-05-31 12:01:22,083 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-31 12:01:22,565 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-31 12:01:22,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6739.0, 'new_value': 6999.0}, {'field': 'offline_amount', 'old_value': 30693.6, 'new_value': 32005.5}, {'field': 'total_amount', 'old_value': 37432.6, 'new_value': 39004.5}, {'field': 'order_count', 'old_value': 1432, 'new_value': 1480}]
2025-05-31 12:01:22,566 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-31 12:01:22,988 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-31 12:01:22,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96835.36, 'new_value': 98084.36}, {'field': 'total_amount', 'old_value': 96835.36, 'new_value': 98084.36}, {'field': 'order_count', 'old_value': 367, 'new_value': 373}]
2025-05-31 12:01:22,989 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-31 12:01:23,454 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-31 12:01:23,454 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31998.5, 'new_value': 33091.4}, {'field': 'offline_amount', 'old_value': 24010.5, 'new_value': 24481.5}, {'field': 'total_amount', 'old_value': 56009.0, 'new_value': 57572.9}, {'field': 'order_count', 'old_value': 304, 'new_value': 314}]
2025-05-31 12:01:23,454 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-31 12:01:23,893 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-31 12:01:23,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23467.76, 'new_value': 25664.9}, {'field': 'offline_amount', 'old_value': 37031.97, 'new_value': 38394.4}, {'field': 'total_amount', 'old_value': 60499.73, 'new_value': 64059.3}, {'field': 'order_count', 'old_value': 2849, 'new_value': 3008}]
2025-05-31 12:01:23,894 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-31 12:01:24,314 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-31 12:01:24,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214936.0, 'new_value': 224774.6}, {'field': 'total_amount', 'old_value': 214936.0, 'new_value': 224774.6}, {'field': 'order_count', 'old_value': 796, 'new_value': 825}]
2025-05-31 12:01:24,315 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-31 12:01:24,771 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-31 12:01:24,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57846.0, 'new_value': 58738.0}, {'field': 'total_amount', 'old_value': 61000.0, 'new_value': 61892.0}, {'field': 'order_count', 'old_value': 232, 'new_value': 236}]
2025-05-31 12:01:24,771 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-31 12:01:25,183 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-31 12:01:25,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19963.45, 'new_value': 20589.5}, {'field': 'offline_amount', 'old_value': 314500.24, 'new_value': 327604.04}, {'field': 'total_amount', 'old_value': 334463.69, 'new_value': 348193.54}, {'field': 'order_count', 'old_value': 18604, 'new_value': 19319}]
2025-05-31 12:01:25,184 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-31 12:01:25,606 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-31 12:01:25,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339057.86, 'new_value': 349095.07}, {'field': 'total_amount', 'old_value': 339057.86, 'new_value': 349095.07}, {'field': 'order_count', 'old_value': 9465, 'new_value': 9773}]
2025-05-31 12:01:25,606 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-31 12:01:26,045 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-31 12:01:26,045 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57176.6, 'new_value': 59252.18}, {'field': 'offline_amount', 'old_value': 39656.0, 'new_value': 41496.0}, {'field': 'total_amount', 'old_value': 96832.6, 'new_value': 100748.18}, {'field': 'order_count', 'old_value': 1203, 'new_value': 1247}]
2025-05-31 12:01:26,045 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-31 12:01:26,482 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-31 12:01:26,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169373.02, 'new_value': 173987.92}, {'field': 'total_amount', 'old_value': 169373.02, 'new_value': 173987.92}, {'field': 'order_count', 'old_value': 845, 'new_value': 871}]
2025-05-31 12:01:26,483 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-31 12:01:26,932 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-31 12:01:26,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26843.11, 'new_value': 27438.94}, {'field': 'offline_amount', 'old_value': 50188.08, 'new_value': 52428.88}, {'field': 'total_amount', 'old_value': 77031.19, 'new_value': 79867.82}, {'field': 'order_count', 'old_value': 2780, 'new_value': 2886}]
2025-05-31 12:01:26,932 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-31 12:01:27,376 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-31 12:01:27,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86379.0, 'new_value': 90513.0}, {'field': 'total_amount', 'old_value': 88787.0, 'new_value': 92921.0}, {'field': 'order_count', 'old_value': 367, 'new_value': 384}]
2025-05-31 12:01:27,377 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-31 12:01:27,808 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-31 12:01:27,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21049.2, 'new_value': 21783.2}, {'field': 'offline_amount', 'old_value': 68635.04, 'new_value': 69641.38}, {'field': 'total_amount', 'old_value': 89684.24, 'new_value': 91424.58}, {'field': 'order_count', 'old_value': 1015, 'new_value': 1037}]
2025-05-31 12:01:27,808 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-31 12:01:28,254 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-31 12:01:28,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110960.7, 'new_value': 113745.1}, {'field': 'offline_amount', 'old_value': 159274.3, 'new_value': 165254.3}, {'field': 'total_amount', 'old_value': 270235.0, 'new_value': 278999.4}, {'field': 'order_count', 'old_value': 1705, 'new_value': 1761}]
2025-05-31 12:01:28,255 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-31 12:01:28,702 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-31 12:01:28,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112255.26, 'new_value': 116437.07}, {'field': 'offline_amount', 'old_value': 82553.24, 'new_value': 85979.69}, {'field': 'total_amount', 'old_value': 194808.5, 'new_value': 202416.76}, {'field': 'order_count', 'old_value': 8338, 'new_value': 8648}]
2025-05-31 12:01:28,703 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-31 12:01:29,253 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-31 12:01:29,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28002.01, 'new_value': 29194.67}, {'field': 'offline_amount', 'old_value': 50055.08, 'new_value': 51744.45}, {'field': 'total_amount', 'old_value': 78057.09, 'new_value': 80939.12}, {'field': 'order_count', 'old_value': 4105, 'new_value': 4249}]
2025-05-31 12:01:29,253 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-31 12:01:29,660 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-31 12:01:29,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128170.0, 'new_value': 135960.0}, {'field': 'total_amount', 'old_value': 128170.0, 'new_value': 135960.0}, {'field': 'order_count', 'old_value': 6498, 'new_value': 6685}]
2025-05-31 12:01:29,660 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-31 12:01:30,111 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-31 12:01:30,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84303.37, 'new_value': 85138.37}, {'field': 'total_amount', 'old_value': 116178.27, 'new_value': 117013.27}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-31 12:01:30,111 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-31 12:01:30,737 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-31 12:01:30,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59213.0, 'new_value': 61656.0}, {'field': 'total_amount', 'old_value': 59562.0, 'new_value': 62005.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 115}]
2025-05-31 12:01:30,738 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-31 12:01:31,259 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-31 12:01:31,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55650.0, 'new_value': 56502.0}, {'field': 'total_amount', 'old_value': 55650.0, 'new_value': 56502.0}, {'field': 'order_count', 'old_value': 401, 'new_value': 407}]
2025-05-31 12:01:31,259 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-31 12:01:31,715 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-31 12:01:31,715 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48025.98, 'new_value': 48109.98}, {'field': 'offline_amount', 'old_value': 527045.5, 'new_value': 542513.5}, {'field': 'total_amount', 'old_value': 575071.48, 'new_value': 590623.48}, {'field': 'order_count', 'old_value': 4550, 'new_value': 4696}]
2025-05-31 12:01:31,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-31 12:01:32,199 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-31 12:01:32,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134031.33, 'new_value': 140015.79}, {'field': 'total_amount', 'old_value': 134031.33, 'new_value': 140015.79}, {'field': 'order_count', 'old_value': 3928, 'new_value': 4084}]
2025-05-31 12:01:32,200 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-31 12:01:32,677 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-31 12:01:32,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49461.2, 'new_value': 51941.2}, {'field': 'total_amount', 'old_value': 49655.2, 'new_value': 52135.2}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-05-31 12:01:32,678 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-31 12:01:33,151 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-31 12:01:33,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 307892.49, 'new_value': 319455.84}, {'field': 'total_amount', 'old_value': 446360.83, 'new_value': 457924.18}, {'field': 'order_count', 'old_value': 5234, 'new_value': 5386}]
2025-05-31 12:01:33,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-31 12:01:33,617 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-31 12:01:33,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41920.08, 'new_value': 44050.08}, {'field': 'total_amount', 'old_value': 41920.08, 'new_value': 44050.08}, {'field': 'order_count', 'old_value': 40, 'new_value': 42}]
2025-05-31 12:01:33,617 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-31 12:01:33,978 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-31 12:01:33,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 411002.44, 'new_value': 423345.79}, {'field': 'total_amount', 'old_value': 411002.44, 'new_value': 423345.79}, {'field': 'order_count', 'old_value': 2071, 'new_value': 2145}]
2025-05-31 12:01:33,979 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-31 12:01:34,443 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-31 12:01:34,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181951.43, 'new_value': 191754.43}, {'field': 'total_amount', 'old_value': 181951.43, 'new_value': 191754.43}, {'field': 'order_count', 'old_value': 313, 'new_value': 327}]
2025-05-31 12:01:34,444 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-31 12:01:34,902 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-31 12:01:34,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 261552.0, 'new_value': 272952.0}, {'field': 'total_amount', 'old_value': 261552.0, 'new_value': 272952.0}, {'field': 'order_count', 'old_value': 21796, 'new_value': 22746}]
2025-05-31 12:01:34,903 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-31 12:01:35,329 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-31 12:01:35,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119700.98, 'new_value': 123883.98}, {'field': 'total_amount', 'old_value': 119700.98, 'new_value': 123883.98}, {'field': 'order_count', 'old_value': 1096, 'new_value': 1121}]
2025-05-31 12:01:35,330 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-31 12:01:35,757 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-31 12:01:35,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 386839.0, 'new_value': 417506.0}, {'field': 'total_amount', 'old_value': 386839.0, 'new_value': 417506.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-31 12:01:35,757 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-31 12:01:36,269 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-31 12:01:36,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49661.0, 'new_value': 51085.5}, {'field': 'total_amount', 'old_value': 49661.0, 'new_value': 51085.5}, {'field': 'order_count', 'old_value': 73, 'new_value': 76}]
2025-05-31 12:01:36,269 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-31 12:01:36,943 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-31 12:01:36,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106022.8, 'new_value': 106398.8}, {'field': 'total_amount', 'old_value': 113372.8, 'new_value': 113748.8}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-05-31 12:01:36,944 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-31 12:01:37,367 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-31 12:01:37,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56837.9, 'new_value': 59997.5}, {'field': 'total_amount', 'old_value': 59138.2, 'new_value': 62297.8}, {'field': 'order_count', 'old_value': 183, 'new_value': 192}]
2025-05-31 12:01:37,367 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-31 12:01:37,840 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-31 12:01:37,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52013.1, 'new_value': 54044.2}, {'field': 'total_amount', 'old_value': 52013.1, 'new_value': 54044.2}, {'field': 'order_count', 'old_value': 2326, 'new_value': 2406}]
2025-05-31 12:01:37,841 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-31 12:01:38,403 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-31 12:01:38,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300227.81, 'new_value': 317341.91}, {'field': 'total_amount', 'old_value': 449449.39, 'new_value': 466563.49}, {'field': 'order_count', 'old_value': 5607, 'new_value': 6020}]
2025-05-31 12:01:38,403 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-31 12:01:38,887 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-31 12:01:38,887 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155841.7, 'new_value': 167825.5}, {'field': 'total_amount', 'old_value': 287306.05, 'new_value': 299289.85}, {'field': 'order_count', 'old_value': 7670, 'new_value': 7967}]
2025-05-31 12:01:38,887 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-31 12:01:39,317 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-31 12:01:39,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354806.4, 'new_value': 376382.4}, {'field': 'total_amount', 'old_value': 354806.4, 'new_value': 376382.4}, {'field': 'order_count', 'old_value': 155, 'new_value': 160}]
2025-05-31 12:01:39,318 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-31 12:01:39,824 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-31 12:01:39,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32268.0, 'new_value': 32988.0}, {'field': 'total_amount', 'old_value': 32268.0, 'new_value': 32988.0}, {'field': 'order_count', 'old_value': 311, 'new_value': 319}]
2025-05-31 12:01:39,824 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-31 12:01:40,276 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-31 12:01:40,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50601.56, 'new_value': 51961.17}, {'field': 'total_amount', 'old_value': 50601.56, 'new_value': 51961.17}, {'field': 'order_count', 'old_value': 203, 'new_value': 208}]
2025-05-31 12:01:40,277 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-31 12:01:40,748 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-31 12:01:40,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66008.24, 'new_value': 68338.32}, {'field': 'offline_amount', 'old_value': 39222.8, 'new_value': 40384.92}, {'field': 'total_amount', 'old_value': 105231.04, 'new_value': 108723.24}, {'field': 'order_count', 'old_value': 5703, 'new_value': 5921}]
2025-05-31 12:01:40,748 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-31 12:01:41,261 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-31 12:01:41,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96293.0, 'new_value': 96808.0}, {'field': 'total_amount', 'old_value': 106204.0, 'new_value': 106719.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-05-31 12:01:41,261 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-31 12:01:41,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-31 12:01:41,716 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33052.54, 'new_value': 36191.62}, {'field': 'offline_amount', 'old_value': 116011.48, 'new_value': 121608.48}, {'field': 'total_amount', 'old_value': 149064.02, 'new_value': 157800.1}, {'field': 'order_count', 'old_value': 118, 'new_value': 123}]
2025-05-31 12:01:41,717 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-31 12:01:42,207 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-31 12:01:42,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23428.08, 'new_value': 23605.08}, {'field': 'total_amount', 'old_value': 23428.08, 'new_value': 23605.08}, {'field': 'order_count', 'old_value': 201, 'new_value': 207}]
2025-05-31 12:01:42,207 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-31 12:01:42,673 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-31 12:01:42,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 466559.54, 'new_value': 479046.68}, {'field': 'total_amount', 'old_value': 466559.54, 'new_value': 479046.68}, {'field': 'order_count', 'old_value': 1665, 'new_value': 1708}]
2025-05-31 12:01:42,673 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-31 12:01:43,145 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-31 12:01:43,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 357376.8, 'new_value': 368400.7}, {'field': 'total_amount', 'old_value': 368653.0, 'new_value': 379676.9}, {'field': 'order_count', 'old_value': 9188, 'new_value': 9598}]
2025-05-31 12:01:43,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-31 12:01:43,648 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-31 12:01:43,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45878.95, 'new_value': 47209.37}, {'field': 'total_amount', 'old_value': 45878.95, 'new_value': 47209.37}, {'field': 'order_count', 'old_value': 5937, 'new_value': 6113}]
2025-05-31 12:01:43,648 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-31 12:01:44,106 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-31 12:01:44,106 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29769.08, 'new_value': 30631.35}, {'field': 'offline_amount', 'old_value': 37695.15, 'new_value': 39402.5}, {'field': 'total_amount', 'old_value': 67464.23, 'new_value': 70033.85}, {'field': 'order_count', 'old_value': 3058, 'new_value': 3171}]
2025-05-31 12:01:44,106 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-31 12:01:44,701 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-31 12:01:44,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106311.0, 'new_value': 108837.0}, {'field': 'total_amount', 'old_value': 111512.0, 'new_value': 114038.0}, {'field': 'order_count', 'old_value': 323, 'new_value': 332}]
2025-05-31 12:01:44,701 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-31 12:01:45,092 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-31 12:01:45,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1913862.36, 'new_value': 1979351.61}, {'field': 'offline_amount', 'old_value': 252688.59, 'new_value': 263908.59}, {'field': 'total_amount', 'old_value': 2166550.95, 'new_value': 2243260.2}, {'field': 'order_count', 'old_value': 7595, 'new_value': 7890}]
2025-05-31 12:01:45,093 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-31 12:01:45,535 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-31 12:01:45,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321171.1, 'new_value': 321299.1}, {'field': 'total_amount', 'old_value': 321171.1, 'new_value': 321299.1}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-05-31 12:01:45,536 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-31 12:01:45,989 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-31 12:01:45,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13150.0, 'new_value': 13605.0}, {'field': 'total_amount', 'old_value': 13150.0, 'new_value': 13605.0}, {'field': 'order_count', 'old_value': 689, 'new_value': 693}]
2025-05-31 12:01:45,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-31 12:01:46,483 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-31 12:01:46,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18793.1, 'new_value': 19484.5}, {'field': 'offline_amount', 'old_value': 46949.9, 'new_value': 47224.9}, {'field': 'total_amount', 'old_value': 65743.0, 'new_value': 66709.4}, {'field': 'order_count', 'old_value': 232, 'new_value': 244}]
2025-05-31 12:01:46,484 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-31 12:01:46,908 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-31 12:01:46,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 522410.48, 'new_value': 539914.48}, {'field': 'total_amount', 'old_value': 522410.48, 'new_value': 539914.48}, {'field': 'order_count', 'old_value': 2692, 'new_value': 2782}]
2025-05-31 12:01:46,909 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-31 12:01:47,495 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-31 12:01:47,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12000.0, 'new_value': 12029.7}, {'field': 'total_amount', 'old_value': 40507.9, 'new_value': 40537.6}, {'field': 'order_count', 'old_value': 109, 'new_value': 112}]
2025-05-31 12:01:47,496 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-31 12:01:47,917 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-31 12:01:47,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 698092.72, 'new_value': 726234.12}, {'field': 'total_amount', 'old_value': 700005.77, 'new_value': 728147.17}, {'field': 'order_count', 'old_value': 1692, 'new_value': 1762}]
2025-05-31 12:01:47,918 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-31 12:01:48,368 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-31 12:01:48,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199430.0, 'new_value': 200825.0}, {'field': 'total_amount', 'old_value': 199431.0, 'new_value': 200826.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-31 12:01:48,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-31 12:01:48,878 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-31 12:01:48,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1088852.0, 'new_value': 1137320.0}, {'field': 'total_amount', 'old_value': 1088852.0, 'new_value': 1137320.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 138}]
2025-05-31 12:01:48,878 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-31 12:01:49,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-31 12:01:49,295 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48739.42, 'new_value': 48769.68}, {'field': 'offline_amount', 'old_value': 59513.27, 'new_value': 62165.37}, {'field': 'total_amount', 'old_value': 108252.69, 'new_value': 110935.05}, {'field': 'order_count', 'old_value': 379, 'new_value': 391}]
2025-05-31 12:01:49,295 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-31 12:01:49,805 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-31 12:01:49,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160393.23, 'new_value': 164663.59}, {'field': 'total_amount', 'old_value': 160393.23, 'new_value': 164663.59}, {'field': 'order_count', 'old_value': 4180, 'new_value': 4290}]
2025-05-31 12:01:49,805 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-31 12:01:50,245 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-31 12:01:50,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112760.0, 'new_value': 124460.0}, {'field': 'total_amount', 'old_value': 112760.0, 'new_value': 124460.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 17}]
2025-05-31 12:01:50,245 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-31 12:01:50,705 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-31 12:01:50,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365554.65, 'new_value': 376374.46}, {'field': 'total_amount', 'old_value': 365554.65, 'new_value': 376374.46}, {'field': 'order_count', 'old_value': 3591, 'new_value': 3702}]
2025-05-31 12:01:50,705 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-31 12:01:51,200 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-31 12:01:51,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79967.0, 'new_value': 83076.0}, {'field': 'total_amount', 'old_value': 79967.0, 'new_value': 83076.0}, {'field': 'order_count', 'old_value': 369, 'new_value': 377}]
2025-05-31 12:01:51,201 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-31 12:01:51,678 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-31 12:01:51,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 350721.92, 'new_value': 362205.92}, {'field': 'offline_amount', 'old_value': 12720.5, 'new_value': 13503.5}, {'field': 'total_amount', 'old_value': 363442.42, 'new_value': 375709.42}, {'field': 'order_count', 'old_value': 3131, 'new_value': 3241}]
2025-05-31 12:01:51,679 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-31 12:01:52,122 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-31 12:01:52,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24337.0, 'new_value': 25034.0}, {'field': 'total_amount', 'old_value': 24337.0, 'new_value': 25034.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 140}]
2025-05-31 12:01:52,123 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-31 12:01:52,635 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-31 12:01:52,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31085.8, 'new_value': 31651.8}, {'field': 'total_amount', 'old_value': 31108.3, 'new_value': 31674.3}, {'field': 'order_count', 'old_value': 871, 'new_value': 911}]
2025-05-31 12:01:52,635 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-31 12:01:53,174 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-31 12:01:53,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12358.9, 'new_value': 12408.9}, {'field': 'total_amount', 'old_value': 12787.9, 'new_value': 12837.9}, {'field': 'order_count', 'old_value': 169, 'new_value': 170}]
2025-05-31 12:01:53,174 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-31 12:01:53,635 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-31 12:01:53,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7679.0, 'new_value': 7944.0}, {'field': 'offline_amount', 'old_value': 55716.49, 'new_value': 57325.19}, {'field': 'total_amount', 'old_value': 63395.49, 'new_value': 65269.19}, {'field': 'order_count', 'old_value': 591, 'new_value': 607}]
2025-05-31 12:01:53,636 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-31 12:01:54,166 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-31 12:01:54,166 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8160.0, 'new_value': 8325.0}, {'field': 'offline_amount', 'old_value': 36525.0, 'new_value': 37159.0}, {'field': 'total_amount', 'old_value': 44685.0, 'new_value': 45484.0}, {'field': 'order_count', 'old_value': 340, 'new_value': 347}]
2025-05-31 12:01:54,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-31 12:01:54,673 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-31 12:01:54,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16741.6, 'new_value': 17301.6}, {'field': 'total_amount', 'old_value': 66913.4, 'new_value': 67473.4}, {'field': 'order_count', 'old_value': 110, 'new_value': 111}]
2025-05-31 12:01:54,674 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-31 12:01:55,127 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-31 12:01:55,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106728.1, 'new_value': 110046.05}, {'field': 'offline_amount', 'old_value': 262179.75, 'new_value': 270141.74}, {'field': 'total_amount', 'old_value': 368907.85, 'new_value': 380187.79}, {'field': 'order_count', 'old_value': 18258, 'new_value': 18826}]
2025-05-31 12:01:55,128 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-31 12:01:55,565 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-31 12:01:55,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37357.95, 'new_value': 38588.85}, {'field': 'total_amount', 'old_value': 37357.95, 'new_value': 38588.85}, {'field': 'order_count', 'old_value': 1395, 'new_value': 1448}]
2025-05-31 12:01:55,565 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-31 12:01:56,028 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-31 12:01:56,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 822254.41, 'new_value': 846317.7}, {'field': 'total_amount', 'old_value': 822254.41, 'new_value': 846317.7}, {'field': 'order_count', 'old_value': 6239, 'new_value': 6420}]
2025-05-31 12:01:56,028 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-31 12:01:56,470 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-31 12:01:56,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229308.0, 'new_value': 233211.0}, {'field': 'total_amount', 'old_value': 229308.0, 'new_value': 233211.0}, {'field': 'order_count', 'old_value': 731, 'new_value': 741}]
2025-05-31 12:01:56,471 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-31 12:01:56,938 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-31 12:01:56,939 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72137.19, 'new_value': 74777.18}, {'field': 'offline_amount', 'old_value': 504709.38, 'new_value': 520576.14}, {'field': 'total_amount', 'old_value': 576846.57, 'new_value': 595353.32}, {'field': 'order_count', 'old_value': 2820, 'new_value': 2913}]
2025-05-31 12:01:56,939 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-31 12:01:57,407 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-31 12:01:57,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 925126.0, 'new_value': 946878.0}, {'field': 'total_amount', 'old_value': 925126.0, 'new_value': 946878.0}, {'field': 'order_count', 'old_value': 4192, 'new_value': 4303}]
2025-05-31 12:01:57,407 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-31 12:01:57,865 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-31 12:01:57,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43251.0, 'new_value': 51231.0}, {'field': 'total_amount', 'old_value': 43251.0, 'new_value': 51231.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-31 12:01:57,865 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-31 12:01:58,467 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-31 12:01:58,467 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57800.43, 'new_value': 60365.33}, {'field': 'offline_amount', 'old_value': 388601.25, 'new_value': 404363.93}, {'field': 'total_amount', 'old_value': 446401.68, 'new_value': 464729.26}, {'field': 'order_count', 'old_value': 2863, 'new_value': 2974}]
2025-05-31 12:01:58,467 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-31 12:01:58,924 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-31 12:01:58,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246229.74, 'new_value': 256361.83}, {'field': 'total_amount', 'old_value': 246229.74, 'new_value': 256361.83}, {'field': 'order_count', 'old_value': 1367, 'new_value': 1422}]
2025-05-31 12:01:58,924 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-31 12:01:59,466 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-31 12:01:59,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77695.0, 'new_value': 80560.0}, {'field': 'total_amount', 'old_value': 77695.0, 'new_value': 80560.0}, {'field': 'order_count', 'old_value': 2285, 'new_value': 2371}]
2025-05-31 12:01:59,467 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-31 12:01:59,844 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-31 12:01:59,844 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33145.93, 'new_value': 34297.88}, {'field': 'offline_amount', 'old_value': 260261.14, 'new_value': 266373.84}, {'field': 'total_amount', 'old_value': 293407.07, 'new_value': 300671.72}, {'field': 'order_count', 'old_value': 9229, 'new_value': 9467}]
2025-05-31 12:01:59,845 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-31 12:02:00,282 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-31 12:02:00,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153733.0, 'new_value': 155700.0}, {'field': 'total_amount', 'old_value': 153733.0, 'new_value': 155700.0}, {'field': 'order_count', 'old_value': 4914, 'new_value': 4977}]
2025-05-31 12:02:00,282 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-31 12:02:00,748 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-31 12:02:00,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141377.82, 'new_value': 146597.43}, {'field': 'offline_amount', 'old_value': 388488.28, 'new_value': 396507.28}, {'field': 'total_amount', 'old_value': 529866.1, 'new_value': 543104.71}, {'field': 'order_count', 'old_value': 4651, 'new_value': 4805}]
2025-05-31 12:02:00,748 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-31 12:02:01,222 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-31 12:02:01,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110064.35, 'new_value': 114594.38}, {'field': 'offline_amount', 'old_value': 120614.36, 'new_value': 125298.1}, {'field': 'total_amount', 'old_value': 230678.71, 'new_value': 239892.48}, {'field': 'order_count', 'old_value': 9434, 'new_value': 9822}]
2025-05-31 12:02:01,223 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-31 12:02:01,615 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-31 12:02:01,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 229588.42, 'new_value': 235485.94}, {'field': 'offline_amount', 'old_value': 116989.9, 'new_value': 117841.62}, {'field': 'total_amount', 'old_value': 346578.32, 'new_value': 353327.56}, {'field': 'order_count', 'old_value': 638, 'new_value': 651}]
2025-05-31 12:02:01,616 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-31 12:02:02,017 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-31 12:02:02,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61114.0, 'new_value': 61343.0}, {'field': 'total_amount', 'old_value': 61114.0, 'new_value': 61343.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 136}]
2025-05-31 12:02:02,017 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-31 12:02:02,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-31 12:02:02,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 788126.72, 'new_value': 819045.72}, {'field': 'total_amount', 'old_value': 788126.72, 'new_value': 819045.72}, {'field': 'order_count', 'old_value': 6114, 'new_value': 6413}]
2025-05-31 12:02:02,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-31 12:02:02,855 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-31 12:02:02,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32181.0, 'new_value': 33060.0}, {'field': 'total_amount', 'old_value': 32181.0, 'new_value': 33060.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 200}]
2025-05-31 12:02:02,855 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-31 12:02:03,343 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-31 12:02:03,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87067.96, 'new_value': 89728.18}, {'field': 'total_amount', 'old_value': 87067.96, 'new_value': 89728.18}, {'field': 'order_count', 'old_value': 5076, 'new_value': 5244}]
2025-05-31 12:02:03,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-31 12:02:03,762 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-31 12:02:03,762 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64748.97, 'new_value': 66625.41}, {'field': 'offline_amount', 'old_value': 50970.35, 'new_value': 53236.75}, {'field': 'total_amount', 'old_value': 115719.32, 'new_value': 119862.16}, {'field': 'order_count', 'old_value': 2336, 'new_value': 2415}]
2025-05-31 12:02:03,762 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-31 12:02:04,233 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-31 12:02:04,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106800.0, 'new_value': 116800.0}, {'field': 'total_amount', 'old_value': 106800.0, 'new_value': 116800.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-31 12:02:04,233 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-31 12:02:04,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-31 12:02:04,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17343.45, 'new_value': 17891.45}, {'field': 'offline_amount', 'old_value': 327808.0, 'new_value': 337154.0}, {'field': 'total_amount', 'old_value': 345151.45, 'new_value': 355045.45}, {'field': 'order_count', 'old_value': 1844, 'new_value': 1903}]
2025-05-31 12:02:04,906 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-31 12:02:05,398 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-31 12:02:05,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216200.0, 'new_value': 221800.0}, {'field': 'total_amount', 'old_value': 216200.0, 'new_value': 221800.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-31 12:02:05,398 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-31 12:02:05,877 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-31 12:02:05,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212254.5, 'new_value': 218160.3}, {'field': 'total_amount', 'old_value': 212254.5, 'new_value': 218160.3}, {'field': 'order_count', 'old_value': 2749, 'new_value': 2820}]
2025-05-31 12:02:05,878 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-31 12:02:06,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-31 12:02:06,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97758.53, 'new_value': 98338.53}, {'field': 'total_amount', 'old_value': 97758.53, 'new_value': 98338.53}, {'field': 'order_count', 'old_value': 129, 'new_value': 130}]
2025-05-31 12:02:06,343 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-31 12:02:06,815 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-31 12:02:06,815 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49533.65, 'new_value': 50691.25}, {'field': 'offline_amount', 'old_value': 1238290.24, 'new_value': 1294667.69}, {'field': 'total_amount', 'old_value': 1287823.89, 'new_value': 1345358.94}, {'field': 'order_count', 'old_value': 6408, 'new_value': 6674}]
2025-05-31 12:02:06,815 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-31 12:02:07,307 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-31 12:02:07,308 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85451.89, 'new_value': 87412.86}, {'field': 'offline_amount', 'old_value': 217642.34, 'new_value': 224195.33}, {'field': 'total_amount', 'old_value': 303094.23, 'new_value': 311608.19}, {'field': 'order_count', 'old_value': 5920, 'new_value': 6127}]
2025-05-31 12:02:07,308 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-31 12:02:07,878 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-31 12:02:07,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347706.0, 'new_value': 348646.0}, {'field': 'total_amount', 'old_value': 352506.0, 'new_value': 353446.0}, {'field': 'order_count', 'old_value': 258, 'new_value': 262}]
2025-05-31 12:02:07,878 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-31 12:02:08,325 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-31 12:02:08,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61417.95, 'new_value': 64418.85}, {'field': 'total_amount', 'old_value': 64858.75, 'new_value': 67859.65}, {'field': 'order_count', 'old_value': 241, 'new_value': 253}]
2025-05-31 12:02:08,326 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-31 12:02:08,803 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-31 12:02:08,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115824.0, 'new_value': 123532.0}, {'field': 'total_amount', 'old_value': 115824.0, 'new_value': 123532.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-31 12:02:08,803 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-31 12:02:09,257 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-31 12:02:09,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 779083.3, 'new_value': 803497.42}, {'field': 'total_amount', 'old_value': 779083.3, 'new_value': 803497.42}, {'field': 'order_count', 'old_value': 9156, 'new_value': 9409}]
2025-05-31 12:02:09,257 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-31 12:02:09,746 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-31 12:02:09,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 478027.2, 'new_value': 497973.2}, {'field': 'total_amount', 'old_value': 697159.1, 'new_value': 717105.1}, {'field': 'order_count', 'old_value': 4950, 'new_value': 4969}]
2025-05-31 12:02:09,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-31 12:02:10,213 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-31 12:02:10,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 681722.0, 'new_value': 695096.0}, {'field': 'total_amount', 'old_value': 681722.0, 'new_value': 695096.0}, {'field': 'order_count', 'old_value': 603, 'new_value': 617}]
2025-05-31 12:02:10,214 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-31 12:02:10,647 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-31 12:02:10,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92518.6, 'new_value': 94051.2}, {'field': 'total_amount', 'old_value': 94982.2, 'new_value': 96514.8}, {'field': 'order_count', 'old_value': 600, 'new_value': 610}]
2025-05-31 12:02:10,648 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-31 12:02:11,096 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-31 12:02:11,096 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70082.63, 'new_value': 71307.88}, {'field': 'offline_amount', 'old_value': 104110.73, 'new_value': 108110.73}, {'field': 'total_amount', 'old_value': 174193.36, 'new_value': 179418.61}, {'field': 'order_count', 'old_value': 4837, 'new_value': 4963}]
2025-05-31 12:02:11,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-31 12:02:11,559 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-31 12:02:11,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53604.0, 'new_value': 57274.0}, {'field': 'total_amount', 'old_value': 53604.0, 'new_value': 57274.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 136}]
2025-05-31 12:02:11,559 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-31 12:02:12,038 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-31 12:02:12,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 540000.0, 'new_value': 545000.0}, {'field': 'total_amount', 'old_value': 540000.0, 'new_value': 545000.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 154}]
2025-05-31 12:02:12,038 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-31 12:02:12,552 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-31 12:02:12,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 520000.0, 'new_value': 525000.0}, {'field': 'total_amount', 'old_value': 520000.0, 'new_value': 525000.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 153}]
2025-05-31 12:02:12,552 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-31 12:02:13,220 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-31 12:02:13,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3548674.0, 'new_value': 3598674.0}, {'field': 'total_amount', 'old_value': 3548674.0, 'new_value': 3598674.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 307}]
2025-05-31 12:02:13,221 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-31 12:02:13,686 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-31 12:02:13,686 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112527.0, 'new_value': 118514.0}, {'field': 'offline_amount', 'old_value': 1324670.0, 'new_value': 1376983.0}, {'field': 'total_amount', 'old_value': 1437197.0, 'new_value': 1495497.0}, {'field': 'order_count', 'old_value': 35633, 'new_value': 37021}]
2025-05-31 12:02:13,686 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-31 12:02:14,126 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-31 12:02:14,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 440075.84, 'new_value': 455159.63}, {'field': 'total_amount', 'old_value': 453586.32, 'new_value': 468670.11}, {'field': 'order_count', 'old_value': 1449, 'new_value': 1492}]
2025-05-31 12:02:14,127 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-31 12:02:14,595 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-31 12:02:14,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62035.0, 'new_value': 64081.0}, {'field': 'offline_amount', 'old_value': 285018.0, 'new_value': 290396.0}, {'field': 'total_amount', 'old_value': 347053.0, 'new_value': 354477.0}, {'field': 'order_count', 'old_value': 308, 'new_value': 314}]
2025-05-31 12:02:14,596 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-31 12:02:15,073 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-31 12:02:15,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39077.0, 'new_value': 39705.0}, {'field': 'total_amount', 'old_value': 39436.0, 'new_value': 40064.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-05-31 12:02:15,074 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-31 12:02:15,538 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-31 12:02:15,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37878.0, 'new_value': 55486.0}, {'field': 'total_amount', 'old_value': 37878.0, 'new_value': 55486.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-31 12:02:15,538 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-31 12:02:16,002 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-31 12:02:16,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26371.68, 'new_value': 27334.82}, {'field': 'offline_amount', 'old_value': 17400.94, 'new_value': 18039.94}, {'field': 'total_amount', 'old_value': 43772.62, 'new_value': 45374.76}, {'field': 'order_count', 'old_value': 1821, 'new_value': 1884}]
2025-05-31 12:02:16,002 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-31 12:02:16,487 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-31 12:02:16,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334251.0, 'new_value': 343540.0}, {'field': 'total_amount', 'old_value': 334251.0, 'new_value': 343540.0}, {'field': 'order_count', 'old_value': 505, 'new_value': 517}]
2025-05-31 12:02:16,487 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-31 12:02:17,004 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-31 12:02:17,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37686.5, 'new_value': 43036.5}, {'field': 'total_amount', 'old_value': 37686.5, 'new_value': 43036.5}, {'field': 'order_count', 'old_value': 187, 'new_value': 196}]
2025-05-31 12:02:17,005 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-31 12:02:17,476 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-31 12:02:17,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23177.55, 'new_value': 23762.5}, {'field': 'offline_amount', 'old_value': 55552.8, 'new_value': 58608.7}, {'field': 'total_amount', 'old_value': 78730.35, 'new_value': 82371.2}, {'field': 'order_count', 'old_value': 668, 'new_value': 683}]
2025-05-31 12:02:17,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-31 12:02:17,892 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-31 12:02:17,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121875.0, 'new_value': 125099.0}, {'field': 'total_amount', 'old_value': 121875.0, 'new_value': 125099.0}, {'field': 'order_count', 'old_value': 516, 'new_value': 531}]
2025-05-31 12:02:17,892 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-31 12:02:18,339 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-31 12:02:18,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26514.0, 'new_value': 29938.0}, {'field': 'total_amount', 'old_value': 26514.0, 'new_value': 29938.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-31 12:02:18,340 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-31 12:02:18,808 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-31 12:02:18,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131986.1, 'new_value': 132644.1}, {'field': 'total_amount', 'old_value': 132791.1, 'new_value': 133449.1}, {'field': 'order_count', 'old_value': 16322, 'new_value': 16325}]
2025-05-31 12:02:18,808 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-31 12:02:19,300 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-31 12:02:19,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 192906.85, 'new_value': 199958.37}, {'field': 'offline_amount', 'old_value': 57046.16, 'new_value': 58693.9}, {'field': 'total_amount', 'old_value': 249953.01, 'new_value': 258652.27}, {'field': 'order_count', 'old_value': 14346, 'new_value': 14830}]
2025-05-31 12:02:19,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-31 12:02:19,756 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-31 12:02:19,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295814.73, 'new_value': 302300.13}, {'field': 'total_amount', 'old_value': 318302.13, 'new_value': 324787.53}, {'field': 'order_count', 'old_value': 1797, 'new_value': 1837}]
2025-05-31 12:02:19,756 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-31 12:02:20,230 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-31 12:02:20,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400946.92, 'new_value': 415923.03}, {'field': 'total_amount', 'old_value': 635361.06, 'new_value': 650337.17}, {'field': 'order_count', 'old_value': 1642, 'new_value': 1662}]
2025-05-31 12:02:20,230 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-31 12:02:20,736 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-31 12:02:20,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192616.58, 'new_value': 199678.48}, {'field': 'total_amount', 'old_value': 192616.58, 'new_value': 199678.48}, {'field': 'order_count', 'old_value': 9749, 'new_value': 10049}]
2025-05-31 12:02:20,737 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-31 12:02:21,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-31 12:02:21,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176131.0, 'new_value': 179438.7}, {'field': 'total_amount', 'old_value': 176131.0, 'new_value': 179438.7}, {'field': 'order_count', 'old_value': 799, 'new_value': 816}]
2025-05-31 12:02:21,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-31 12:02:21,594 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-31 12:02:21,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141783.6, 'new_value': 147022.0}, {'field': 'total_amount', 'old_value': 141783.6, 'new_value': 147022.0}, {'field': 'order_count', 'old_value': 3916, 'new_value': 4054}]
2025-05-31 12:02:21,595 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-31 12:02:22,049 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-31 12:02:22,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10956.0, 'new_value': 12713.0}, {'field': 'total_amount', 'old_value': 22430.0, 'new_value': 24187.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 138}]
2025-05-31 12:02:22,049 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-31 12:02:22,573 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-31 12:02:22,574 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 178050.19, 'new_value': 185039.69}, {'field': 'offline_amount', 'old_value': 334592.41, 'new_value': 347856.27}, {'field': 'total_amount', 'old_value': 512642.6, 'new_value': 532895.96}, {'field': 'order_count', 'old_value': 4304, 'new_value': 4470}]
2025-05-31 12:02:22,574 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-31 12:02:23,036 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-31 12:02:23,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120719.6, 'new_value': 124731.8}, {'field': 'total_amount', 'old_value': 120719.6, 'new_value': 124731.8}, {'field': 'order_count', 'old_value': 572, 'new_value': 590}]
2025-05-31 12:02:23,037 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-31 12:02:23,572 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-31 12:02:23,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125955.0, 'new_value': 129009.0}, {'field': 'total_amount', 'old_value': 125955.0, 'new_value': 129009.0}, {'field': 'order_count', 'old_value': 1699, 'new_value': 1700}]
2025-05-31 12:02:23,572 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-31 12:02:24,090 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-31 12:02:24,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89204.14, 'new_value': 94280.44}, {'field': 'total_amount', 'old_value': 97991.48, 'new_value': 103067.78}, {'field': 'order_count', 'old_value': 467, 'new_value': 488}]
2025-05-31 12:02:24,090 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-31 12:02:24,599 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-31 12:02:24,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 858896.29, 'new_value': 883530.99}, {'field': 'total_amount', 'old_value': 858896.29, 'new_value': 883530.99}, {'field': 'order_count', 'old_value': 7038, 'new_value': 7256}]
2025-05-31 12:02:24,599 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-31 12:02:25,050 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-31 12:02:25,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26628.8, 'new_value': 29452.5}, {'field': 'offline_amount', 'old_value': 209845.7, 'new_value': 232914.1}, {'field': 'total_amount', 'old_value': 236474.5, 'new_value': 262366.6}, {'field': 'order_count', 'old_value': 7577, 'new_value': 8284}]
2025-05-31 12:02:25,050 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-31 12:02:25,488 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-31 12:02:25,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5655.0, 'new_value': 5806.0}, {'field': 'total_amount', 'old_value': 13418.0, 'new_value': 13569.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 147}]
2025-05-31 12:02:25,488 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-31 12:02:25,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-31 12:02:25,997 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62253.79, 'new_value': 64384.48}, {'field': 'offline_amount', 'old_value': 65089.03, 'new_value': 67462.94}, {'field': 'total_amount', 'old_value': 127342.82, 'new_value': 131847.42}, {'field': 'order_count', 'old_value': 6341, 'new_value': 6582}]
2025-05-31 12:02:25,997 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-31 12:02:26,415 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-31 12:02:26,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122792.05, 'new_value': 127605.61}, {'field': 'offline_amount', 'old_value': 133857.43, 'new_value': 138035.96}, {'field': 'total_amount', 'old_value': 256649.48, 'new_value': 265641.57}, {'field': 'order_count', 'old_value': 6523, 'new_value': 6743}]
2025-05-31 12:02:26,416 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-31 12:02:26,869 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-31 12:02:26,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96668.0, 'new_value': 104668.0}, {'field': 'total_amount', 'old_value': 96668.0, 'new_value': 104668.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-31 12:02:26,870 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-31 12:02:27,291 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-31 12:02:27,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107620.0, 'new_value': 112280.0}, {'field': 'total_amount', 'old_value': 107620.0, 'new_value': 112280.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-31 12:02:27,292 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-31 12:02:27,783 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-31 12:02:27,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1135808.0, 'new_value': 1182982.0}, {'field': 'total_amount', 'old_value': 1135808.0, 'new_value': 1182982.0}, {'field': 'order_count', 'old_value': 1306, 'new_value': 1352}]
2025-05-31 12:02:27,784 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-31 12:02:28,215 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-31 12:02:28,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243915.4, 'new_value': 251387.4}, {'field': 'total_amount', 'old_value': 249865.7, 'new_value': 257337.7}, {'field': 'order_count', 'old_value': 468, 'new_value': 487}]
2025-05-31 12:02:28,215 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-31 12:02:28,676 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-31 12:02:28,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136722.0, 'new_value': 158322.0}, {'field': 'total_amount', 'old_value': 136722.0, 'new_value': 158322.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-05-31 12:02:28,676 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-31 12:02:29,195 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-31 12:02:29,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49035.65, 'new_value': 50611.95}, {'field': 'offline_amount', 'old_value': 146302.0, 'new_value': 153930.0}, {'field': 'total_amount', 'old_value': 195337.65, 'new_value': 204541.95}, {'field': 'order_count', 'old_value': 2138, 'new_value': 2220}]
2025-05-31 12:02:29,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-31 12:02:29,747 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-31 12:02:29,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 157633.0, 'new_value': 162456.0}, {'field': 'offline_amount', 'old_value': 105627.0, 'new_value': 109551.0}, {'field': 'total_amount', 'old_value': 263260.0, 'new_value': 272007.0}, {'field': 'order_count', 'old_value': 3567, 'new_value': 3686}]
2025-05-31 12:02:29,747 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-31 12:02:30,223 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-31 12:02:30,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8267.7, 'new_value': 8801.7}, {'field': 'offline_amount', 'old_value': 25676.9, 'new_value': 25921.6}, {'field': 'total_amount', 'old_value': 33944.6, 'new_value': 34723.3}, {'field': 'order_count', 'old_value': 340, 'new_value': 347}]
2025-05-31 12:02:30,223 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-31 12:02:30,685 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-31 12:02:30,686 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12433.92, 'new_value': 12963.54}, {'field': 'offline_amount', 'old_value': 195458.0, 'new_value': 195846.0}, {'field': 'total_amount', 'old_value': 207891.92, 'new_value': 208809.54}, {'field': 'order_count', 'old_value': 88, 'new_value': 91}]
2025-05-31 12:02:30,686 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-31 12:02:31,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-31 12:02:31,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140823.58, 'new_value': 146580.0}, {'field': 'total_amount', 'old_value': 148052.65, 'new_value': 153809.07}, {'field': 'order_count', 'old_value': 784, 'new_value': 818}]
2025-05-31 12:02:31,182 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-31 12:02:31,647 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-31 12:02:31,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39558.66, 'new_value': 40703.66}, {'field': 'offline_amount', 'old_value': 37013.12, 'new_value': 38476.12}, {'field': 'total_amount', 'old_value': 76571.78, 'new_value': 79179.78}, {'field': 'order_count', 'old_value': 335, 'new_value': 347}]
2025-05-31 12:02:31,648 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-31 12:02:32,117 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-31 12:02:32,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8238.0, 'new_value': 9259.0}, {'field': 'offline_amount', 'old_value': 59514.7, 'new_value': 61637.7}, {'field': 'total_amount', 'old_value': 67752.7, 'new_value': 70896.7}, {'field': 'order_count', 'old_value': 547, 'new_value': 569}]
2025-05-31 12:02:32,117 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-31 12:02:32,522 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-31 12:02:32,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253606.5, 'new_value': 264764.5}, {'field': 'total_amount', 'old_value': 253606.5, 'new_value': 264764.5}, {'field': 'order_count', 'old_value': 1231, 'new_value': 1282}]
2025-05-31 12:02:32,522 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-31 12:02:32,976 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-31 12:02:32,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5668.0, 'new_value': 5898.0}, {'field': 'offline_amount', 'old_value': 23697.0, 'new_value': 24283.0}, {'field': 'total_amount', 'old_value': 29365.0, 'new_value': 30181.0}, {'field': 'order_count', 'old_value': 225, 'new_value': 229}]
2025-05-31 12:02:32,976 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-31 12:02:33,490 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-31 12:02:33,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6666.04, 'new_value': 6819.04}, {'field': 'offline_amount', 'old_value': 60762.78, 'new_value': 63510.3}, {'field': 'total_amount', 'old_value': 67428.82, 'new_value': 70329.34}, {'field': 'order_count', 'old_value': 635, 'new_value': 671}]
2025-05-31 12:02:33,491 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-31 12:02:33,921 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-31 12:02:33,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225620.74, 'new_value': 235888.34}, {'field': 'total_amount', 'old_value': 225620.74, 'new_value': 235888.34}, {'field': 'order_count', 'old_value': 876, 'new_value': 919}]
2025-05-31 12:02:33,921 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-31 12:02:34,377 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-31 12:02:34,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15702.0, 'new_value': 15901.0}, {'field': 'total_amount', 'old_value': 15702.0, 'new_value': 15901.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-31 12:02:34,378 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-31 12:02:34,799 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-31 12:02:34,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72811.0, 'new_value': 75046.0}, {'field': 'offline_amount', 'old_value': 363910.0, 'new_value': 385033.0}, {'field': 'total_amount', 'old_value': 436721.0, 'new_value': 460079.0}, {'field': 'order_count', 'old_value': 1735, 'new_value': 1810}]
2025-05-31 12:02:34,800 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-31 12:02:35,273 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-31 12:02:35,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1129787.0, 'new_value': 1201666.0}, {'field': 'total_amount', 'old_value': 1129787.0, 'new_value': 1201666.0}, {'field': 'order_count', 'old_value': 5003, 'new_value': 5308}]
2025-05-31 12:02:35,273 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-31 12:02:35,727 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-31 12:02:35,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13830101.0, 'new_value': 14154606.0}, {'field': 'total_amount', 'old_value': 13830101.0, 'new_value': 14154606.0}, {'field': 'order_count', 'old_value': 44549, 'new_value': 45869}]
2025-05-31 12:02:35,727 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-31 12:02:36,246 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-31 12:02:36,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4115848.14, 'new_value': 4227508.51}, {'field': 'total_amount', 'old_value': 4115848.14, 'new_value': 4227508.51}, {'field': 'order_count', 'old_value': 7101, 'new_value': 7314}]
2025-05-31 12:02:36,247 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-31 12:02:36,715 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-31 12:02:36,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187771.92, 'new_value': 195133.04}, {'field': 'total_amount', 'old_value': 195211.56, 'new_value': 202572.68}, {'field': 'order_count', 'old_value': 13735, 'new_value': 14326}]
2025-05-31 12:02:36,716 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-31 12:02:37,173 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-31 12:02:37,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 322784.03, 'new_value': 334665.92}, {'field': 'offline_amount', 'old_value': 228275.36, 'new_value': 237346.45}, {'field': 'total_amount', 'old_value': 551059.39, 'new_value': 572012.37}, {'field': 'order_count', 'old_value': 22423, 'new_value': 23271}]
2025-05-31 12:02:37,173 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-31 12:02:37,608 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-31 12:02:37,608 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52503.38, 'new_value': 55115.15}, {'field': 'offline_amount', 'old_value': 75917.31, 'new_value': 77259.9}, {'field': 'total_amount', 'old_value': 128420.69, 'new_value': 132375.05}, {'field': 'order_count', 'old_value': 2747, 'new_value': 2835}]
2025-05-31 12:02:37,608 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-31 12:02:38,064 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-31 12:02:38,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85994.0, 'new_value': 91974.0}, {'field': 'total_amount', 'old_value': 93974.0, 'new_value': 99954.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-31 12:02:38,064 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-31 12:02:38,509 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-31 12:02:38,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 204455.6, 'new_value': 220534.6}, {'field': 'total_amount', 'old_value': 364671.38, 'new_value': 380750.38}, {'field': 'order_count', 'old_value': 268, 'new_value': 292}]
2025-05-31 12:02:38,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-31 12:02:38,974 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-31 12:02:38,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336433.0, 'new_value': 703941.0}, {'field': 'total_amount', 'old_value': 336433.0, 'new_value': 703941.0}, {'field': 'order_count', 'old_value': 7351, 'new_value': 8018}]
2025-05-31 12:02:38,974 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-31 12:02:39,461 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-31 12:02:39,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60558.9, 'new_value': 62717.9}, {'field': 'total_amount', 'old_value': 60558.9, 'new_value': 62717.9}, {'field': 'order_count', 'old_value': 332, 'new_value': 342}]
2025-05-31 12:02:39,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-31 12:02:39,909 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-31 12:02:39,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 222981.0, 'new_value': 226860.0}, {'field': 'offline_amount', 'old_value': 198624.0, 'new_value': 205571.0}, {'field': 'total_amount', 'old_value': 421605.0, 'new_value': 432431.0}, {'field': 'order_count', 'old_value': 1189, 'new_value': 1237}]
2025-05-31 12:02:39,910 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-31 12:02:40,402 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-31 12:02:40,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 868960.51, 'new_value': 887164.18}, {'field': 'total_amount', 'old_value': 868960.51, 'new_value': 887164.18}, {'field': 'order_count', 'old_value': 4623, 'new_value': 4752}]
2025-05-31 12:02:40,403 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-31 12:02:40,937 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-31 12:02:40,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153905.78, 'new_value': 158501.4}, {'field': 'total_amount', 'old_value': 153905.78, 'new_value': 158501.4}, {'field': 'order_count', 'old_value': 10764, 'new_value': 11121}]
2025-05-31 12:02:40,938 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-31 12:02:41,411 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-31 12:02:41,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 467081.0, 'new_value': 489081.0}, {'field': 'total_amount', 'old_value': 467081.0, 'new_value': 489081.0}, {'field': 'order_count', 'old_value': 10606, 'new_value': 11073}]
2025-05-31 12:02:41,412 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-31 12:02:41,838 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-31 12:02:41,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110634.0, 'new_value': 114437.0}, {'field': 'total_amount', 'old_value': 110634.0, 'new_value': 114437.0}, {'field': 'order_count', 'old_value': 7485, 'new_value': 7689}]
2025-05-31 12:02:41,838 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-31 12:02:42,318 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-31 12:02:42,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170349.0, 'new_value': 178771.0}, {'field': 'total_amount', 'old_value': 175088.0, 'new_value': 183510.0}, {'field': 'order_count', 'old_value': 13036, 'new_value': 13630}]
2025-05-31 12:02:42,318 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-31 12:02:42,816 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-31 12:02:42,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37661.0, 'new_value': 37837.0}, {'field': 'total_amount', 'old_value': 37661.0, 'new_value': 37837.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-05-31 12:02:42,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-31 12:02:43,237 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-31 12:02:43,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79779.1, 'new_value': 80372.1}, {'field': 'total_amount', 'old_value': 80058.9, 'new_value': 80651.9}, {'field': 'order_count', 'old_value': 1180, 'new_value': 1190}]
2025-05-31 12:02:43,237 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-31 12:02:43,684 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-31 12:02:43,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2684405.75, 'new_value': 3043405.75}, {'field': 'total_amount', 'old_value': 4249655.75, 'new_value': 4608655.75}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-31 12:02:43,684 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-31 12:02:44,108 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-31 12:02:44,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31902.3, 'new_value': 33638.1}, {'field': 'offline_amount', 'old_value': 72149.3, 'new_value': 75687.6}, {'field': 'total_amount', 'old_value': 104051.6, 'new_value': 109325.7}, {'field': 'order_count', 'old_value': 3931, 'new_value': 4116}]
2025-05-31 12:02:44,109 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-31 12:02:44,622 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-31 12:02:44,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73919.08, 'new_value': 76766.3}, {'field': 'total_amount', 'old_value': 81788.13, 'new_value': 84635.35}, {'field': 'order_count', 'old_value': 1516, 'new_value': 1546}]
2025-05-31 12:02:44,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-31 12:02:45,081 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-31 12:02:45,081 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5813884.67, 'new_value': 6096951.17}, {'field': 'total_amount', 'old_value': 5813884.67, 'new_value': 6096951.17}, {'field': 'order_count', 'old_value': 119655, 'new_value': 124058}]
2025-05-31 12:02:45,081 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-31 12:02:45,570 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-31 12:02:45,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28148.88, 'new_value': 29483.52}, {'field': 'total_amount', 'old_value': 28148.88, 'new_value': 29483.52}, {'field': 'order_count', 'old_value': 130, 'new_value': 136}]
2025-05-31 12:02:45,570 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-31 12:02:46,014 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-31 12:02:46,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 597375.39, 'new_value': 614143.39}, {'field': 'total_amount', 'old_value': 602921.75, 'new_value': 619689.75}, {'field': 'order_count', 'old_value': 6114, 'new_value': 6239}]
2025-05-31 12:02:46,014 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-31 12:02:46,445 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-31 12:02:46,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235962.11, 'new_value': 245131.85}, {'field': 'total_amount', 'old_value': 235962.11, 'new_value': 245131.85}, {'field': 'order_count', 'old_value': 4282, 'new_value': 4418}]
2025-05-31 12:02:46,445 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-31 12:02:46,903 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-31 12:02:46,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 391762.0, 'new_value': 410149.0}, {'field': 'total_amount', 'old_value': 391762.0, 'new_value': 410149.0}, {'field': 'order_count', 'old_value': 8450, 'new_value': 8803}]
2025-05-31 12:02:46,903 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-31 12:02:47,427 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-31 12:02:47,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53.8, 'new_value': 81.7}, {'field': 'total_amount', 'old_value': 34892.8, 'new_value': 34920.7}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-31 12:02:47,428 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-31 12:02:47,860 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-31 12:02:47,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 593028.0, 'new_value': 609270.0}, {'field': 'total_amount', 'old_value': 593028.0, 'new_value': 609270.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 109}]
2025-05-31 12:02:47,860 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-31 12:02:48,267 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-31 12:02:48,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146730.1, 'new_value': 152506.5}, {'field': 'total_amount', 'old_value': 151698.3, 'new_value': 157474.7}, {'field': 'order_count', 'old_value': 3841, 'new_value': 3999}]
2025-05-31 12:02:48,268 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-05-31 12:02:48,678 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-05-31 12:02:48,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6976.0, 'new_value': 11206.0}, {'field': 'total_amount', 'old_value': 6976.0, 'new_value': 11206.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-31 12:02:48,678 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-31 12:02:49,116 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-31 12:02:49,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40689.0, 'new_value': 41897.0}, {'field': 'total_amount', 'old_value': 40689.0, 'new_value': 41897.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 133}]
2025-05-31 12:02:49,116 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-31 12:02:49,587 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-31 12:02:49,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117251.14, 'new_value': 122167.24}, {'field': 'offline_amount', 'old_value': 439740.8, 'new_value': 457722.9}, {'field': 'total_amount', 'old_value': 556991.94, 'new_value': 579890.14}, {'field': 'order_count', 'old_value': 4160, 'new_value': 4321}]
2025-05-31 12:02:49,588 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-31 12:02:50,070 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-31 12:02:50,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64995.41, 'new_value': 69652.49}, {'field': 'total_amount', 'old_value': 105472.8, 'new_value': 110129.88}, {'field': 'order_count', 'old_value': 6944, 'new_value': 7278}]
2025-05-31 12:02:50,070 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-31 12:02:50,507 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-31 12:02:50,508 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113329.28, 'new_value': 121258.63}, {'field': 'total_amount', 'old_value': 187101.46, 'new_value': 195030.81}, {'field': 'order_count', 'old_value': 12350, 'new_value': 12924}]
2025-05-31 12:02:50,508 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-31 12:02:50,952 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-31 12:02:50,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1338069.09, 'new_value': 1383755.89}, {'field': 'total_amount', 'old_value': 1338069.09, 'new_value': 1383755.89}, {'field': 'order_count', 'old_value': 3956, 'new_value': 4095}]
2025-05-31 12:02:50,952 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-31 12:02:51,408 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-31 12:02:51,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223428.8, 'new_value': 232697.8}, {'field': 'total_amount', 'old_value': 223428.8, 'new_value': 232697.8}, {'field': 'order_count', 'old_value': 7816, 'new_value': 8146}]
2025-05-31 12:02:51,408 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-31 12:02:51,895 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-31 12:02:51,896 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-31 12:02:51,896 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-31 12:02:52,414 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-31 12:02:52,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 832884.25, 'new_value': 867011.99}, {'field': 'total_amount', 'old_value': 832884.25, 'new_value': 867011.99}, {'field': 'order_count', 'old_value': 4581, 'new_value': 4781}]
2025-05-31 12:02:52,414 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-31 12:02:52,913 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-31 12:02:52,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1228944.13, 'new_value': 1273205.21}, {'field': 'total_amount', 'old_value': 1228944.13, 'new_value': 1273205.21}, {'field': 'order_count', 'old_value': 4309, 'new_value': 4456}]
2025-05-31 12:02:52,914 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-31 12:02:53,393 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-31 12:02:53,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 933984.51, 'new_value': 960773.91}, {'field': 'total_amount', 'old_value': 933984.51, 'new_value': 960773.91}, {'field': 'order_count', 'old_value': 2616, 'new_value': 2679}]
2025-05-31 12:02:53,393 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-31 12:02:53,878 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-31 12:02:53,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53496.8, 'new_value': 55408.4}, {'field': 'total_amount', 'old_value': 56340.8, 'new_value': 58252.4}, {'field': 'order_count', 'old_value': 417, 'new_value': 433}]
2025-05-31 12:02:53,878 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-31 12:02:54,427 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-31 12:02:54,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342536.51, 'new_value': 358253.26}, {'field': 'total_amount', 'old_value': 342536.51, 'new_value': 358253.26}, {'field': 'order_count', 'old_value': 933, 'new_value': 976}]
2025-05-31 12:02:54,427 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-31 12:02:54,818 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-31 12:02:54,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139450.0, 'new_value': 141046.0}, {'field': 'total_amount', 'old_value': 139530.0, 'new_value': 141126.0}, {'field': 'order_count', 'old_value': 13992, 'new_value': 14092}]
2025-05-31 12:02:54,818 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-31 12:02:55,281 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-31 12:02:55,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66513.0, 'new_value': 69929.0}, {'field': 'total_amount', 'old_value': 88714.0, 'new_value': 92130.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 159}]
2025-05-31 12:02:55,281 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-31 12:02:55,768 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-31 12:02:55,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159266.0, 'new_value': 165297.0}, {'field': 'offline_amount', 'old_value': 111451.0, 'new_value': 116506.0}, {'field': 'total_amount', 'old_value': 270717.0, 'new_value': 281803.0}, {'field': 'order_count', 'old_value': 11082, 'new_value': 11522}]
2025-05-31 12:02:55,768 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-31 12:02:56,223 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-31 12:02:56,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143929.0, 'new_value': 150028.0}, {'field': 'total_amount', 'old_value': 143929.0, 'new_value': 150028.0}, {'field': 'order_count', 'old_value': 693, 'new_value': 708}]
2025-05-31 12:02:56,223 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-31 12:02:56,673 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-31 12:02:56,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160341.0, 'new_value': 164012.0}, {'field': 'total_amount', 'old_value': 174698.0, 'new_value': 178369.0}, {'field': 'order_count', 'old_value': 659, 'new_value': 673}]
2025-05-31 12:02:56,673 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-31 12:02:57,153 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-31 12:02:57,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329185.0, 'new_value': 453585.0}, {'field': 'total_amount', 'old_value': 329185.0, 'new_value': 453585.0}, {'field': 'order_count', 'old_value': 774, 'new_value': 804}]
2025-05-31 12:02:57,154 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-31 12:02:57,661 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-31 12:02:57,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23016.0, 'new_value': 29916.0}, {'field': 'total_amount', 'old_value': 38604.0, 'new_value': 45504.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-31 12:02:57,662 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-31 12:02:58,092 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-31 12:02:58,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62176.0, 'new_value': 65262.0}, {'field': 'total_amount', 'old_value': 62176.0, 'new_value': 65262.0}, {'field': 'order_count', 'old_value': 1194, 'new_value': 1251}]
2025-05-31 12:02:58,093 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-31 12:02:58,522 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-31 12:02:58,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240651.0, 'new_value': 251221.0}, {'field': 'total_amount', 'old_value': 240651.0, 'new_value': 251221.0}, {'field': 'order_count', 'old_value': 25308, 'new_value': 26422}]
2025-05-31 12:02:58,522 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-31 12:02:58,970 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-31 12:02:58,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129263.0, 'new_value': 132337.0}, {'field': 'total_amount', 'old_value': 129263.0, 'new_value': 132337.0}, {'field': 'order_count', 'old_value': 1353, 'new_value': 1399}]
2025-05-31 12:02:58,970 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-31 12:02:59,404 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-31 12:02:59,404 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44709.0, 'new_value': 48989.0}, {'field': 'total_amount', 'old_value': 84834.4, 'new_value': 89114.4}, {'field': 'order_count', 'old_value': 2810, 'new_value': 2811}]
2025-05-31 12:02:59,404 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-31 12:02:59,846 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-31 12:02:59,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55809.66, 'new_value': 57746.66}, {'field': 'total_amount', 'old_value': 55809.66, 'new_value': 57746.66}, {'field': 'order_count', 'old_value': 951, 'new_value': 977}]
2025-05-31 12:02:59,846 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-31 12:03:00,207 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-31 12:03:00,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141001.77, 'new_value': 148680.37}, {'field': 'offline_amount', 'old_value': 761937.67, 'new_value': 789572.91}, {'field': 'total_amount', 'old_value': 902939.44, 'new_value': 938253.28}, {'field': 'order_count', 'old_value': 2141, 'new_value': 2227}]
2025-05-31 12:03:00,207 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-31 12:03:00,573 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-31 12:03:00,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87678.6, 'new_value': 91441.6}, {'field': 'total_amount', 'old_value': 87678.6, 'new_value': 91441.6}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-05-31 12:03:00,573 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-31 12:03:01,018 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-31 12:03:01,018 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104949.04, 'new_value': 108505.62}, {'field': 'offline_amount', 'old_value': 1056931.48, 'new_value': 1094453.88}, {'field': 'total_amount', 'old_value': 1160006.19, 'new_value': 1201085.17}, {'field': 'order_count', 'old_value': 5448, 'new_value': 5652}]
2025-05-31 12:03:01,019 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-31 12:03:01,458 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-31 12:03:01,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136002.0, 'new_value': 138471.0}, {'field': 'total_amount', 'old_value': 136002.0, 'new_value': 138471.0}, {'field': 'order_count', 'old_value': 417, 'new_value': 428}]
2025-05-31 12:03:01,458 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-31 12:03:01,901 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-31 12:03:01,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95897.0, 'new_value': 116697.0}, {'field': 'total_amount', 'old_value': 101215.0, 'new_value': 122015.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 53}]
2025-05-31 12:03:01,901 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-31 12:03:02,387 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-31 12:03:02,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20079549.5, 'new_value': 20744697.36}, {'field': 'total_amount', 'old_value': 20079549.5, 'new_value': 20744697.36}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-31 12:03:02,387 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-31 12:03:02,868 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-31 12:03:02,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209129.02, 'new_value': 215679.34}, {'field': 'total_amount', 'old_value': 209129.02, 'new_value': 215679.34}, {'field': 'order_count', 'old_value': 22116, 'new_value': 22838}]
2025-05-31 12:03:02,868 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-31 12:03:03,344 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-31 12:03:03,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63252.77, 'new_value': 71038.29}, {'field': 'offline_amount', 'old_value': 63372.77, 'new_value': 69674.97}, {'field': 'total_amount', 'old_value': 126625.54, 'new_value': 140713.26}, {'field': 'order_count', 'old_value': 578, 'new_value': 634}]
2025-05-31 12:03:03,345 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-31 12:03:04,075 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-31 12:03:04,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5785.55, 'new_value': 7142.95}, {'field': 'total_amount', 'old_value': 5785.55, 'new_value': 7142.95}, {'field': 'order_count', 'old_value': 327, 'new_value': 399}]
2025-05-31 12:03:04,075 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-31 12:03:04,534 - INFO - 更新表单数据成功: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-31 12:03:04,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40000.0, 'new_value': 45000.0}, {'field': 'total_amount', 'old_value': 40000.0, 'new_value': 45000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-31 12:03:04,534 - INFO - 日期 2025-05 处理完成 - 更新: 329 条，插入: 0 条，错误: 0 条
2025-05-31 12:03:04,534 - INFO - 数据同步完成！更新: 329 条，插入: 0 条，错误: 0 条
2025-05-31 12:03:04,537 - INFO - =================同步完成====================
2025-05-31 15:00:01,897 - INFO - =================使用默认全量同步=============
2025-05-31 15:00:03,421 - INFO - MySQL查询成功，共获取 3305 条记录
2025-05-31 15:00:03,422 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 15:00:03,451 - INFO - 开始处理日期: 2025-01
2025-05-31 15:00:03,454 - INFO - Request Parameters - Page 1:
2025-05-31 15:00:03,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:03,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:04,630 - INFO - Response - Page 1:
2025-05-31 15:00:04,830 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:00:04,830 - INFO - Request Parameters - Page 2:
2025-05-31 15:00:04,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:04,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:05,672 - INFO - Response - Page 2:
2025-05-31 15:00:05,872 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:00:05,872 - INFO - Request Parameters - Page 3:
2025-05-31 15:00:05,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:05,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:06,432 - INFO - Response - Page 3:
2025-05-31 15:00:06,632 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:00:06,632 - INFO - Request Parameters - Page 4:
2025-05-31 15:00:06,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:06,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:07,221 - INFO - Response - Page 4:
2025-05-31 15:00:07,421 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:00:07,421 - INFO - Request Parameters - Page 5:
2025-05-31 15:00:07,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:07,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:07,976 - INFO - Response - Page 5:
2025-05-31 15:00:08,176 - INFO - 第 5 页获取到 100 条记录
2025-05-31 15:00:08,176 - INFO - Request Parameters - Page 6:
2025-05-31 15:00:08,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:08,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:08,703 - INFO - Response - Page 6:
2025-05-31 15:00:08,904 - INFO - 第 6 页获取到 100 条记录
2025-05-31 15:00:08,904 - INFO - Request Parameters - Page 7:
2025-05-31 15:00:08,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:08,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:09,455 - INFO - Response - Page 7:
2025-05-31 15:00:09,655 - INFO - 第 7 页获取到 82 条记录
2025-05-31 15:00:09,655 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 15:00:09,655 - INFO - 获取到 682 条表单数据
2025-05-31 15:00:09,667 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 15:00:09,679 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:00:09,679 - INFO - 开始处理日期: 2025-02
2025-05-31 15:00:09,679 - INFO - Request Parameters - Page 1:
2025-05-31 15:00:09,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:09,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:10,187 - INFO - Response - Page 1:
2025-05-31 15:00:10,387 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:00:10,387 - INFO - Request Parameters - Page 2:
2025-05-31 15:00:10,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:10,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:10,889 - INFO - Response - Page 2:
2025-05-31 15:00:11,089 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:00:11,089 - INFO - Request Parameters - Page 3:
2025-05-31 15:00:11,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:11,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:11,573 - INFO - Response - Page 3:
2025-05-31 15:00:11,774 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:00:11,774 - INFO - Request Parameters - Page 4:
2025-05-31 15:00:11,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:11,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:12,286 - INFO - Response - Page 4:
2025-05-31 15:00:12,487 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:00:12,487 - INFO - Request Parameters - Page 5:
2025-05-31 15:00:12,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:12,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:13,010 - INFO - Response - Page 5:
2025-05-31 15:00:13,210 - INFO - 第 5 页获取到 100 条记录
2025-05-31 15:00:13,210 - INFO - Request Parameters - Page 6:
2025-05-31 15:00:13,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:13,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:13,792 - INFO - Response - Page 6:
2025-05-31 15:00:13,992 - INFO - 第 6 页获取到 100 条记录
2025-05-31 15:00:13,992 - INFO - Request Parameters - Page 7:
2025-05-31 15:00:13,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:13,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:14,482 - INFO - Response - Page 7:
2025-05-31 15:00:14,682 - INFO - 第 7 页获取到 70 条记录
2025-05-31 15:00:14,682 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 15:00:14,682 - INFO - 获取到 670 条表单数据
2025-05-31 15:00:14,694 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 15:00:14,706 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:00:14,706 - INFO - 开始处理日期: 2025-03
2025-05-31 15:00:14,706 - INFO - Request Parameters - Page 1:
2025-05-31 15:00:14,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:14,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:15,168 - INFO - Response - Page 1:
2025-05-31 15:00:15,368 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:00:15,368 - INFO - Request Parameters - Page 2:
2025-05-31 15:00:15,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:15,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:15,851 - INFO - Response - Page 2:
2025-05-31 15:00:16,051 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:00:16,051 - INFO - Request Parameters - Page 3:
2025-05-31 15:00:16,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:16,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:16,638 - INFO - Response - Page 3:
2025-05-31 15:00:16,838 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:00:16,838 - INFO - Request Parameters - Page 4:
2025-05-31 15:00:16,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:16,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:17,383 - INFO - Response - Page 4:
2025-05-31 15:00:17,583 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:00:17,583 - INFO - Request Parameters - Page 5:
2025-05-31 15:00:17,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:17,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:18,366 - INFO - Response - Page 5:
2025-05-31 15:00:18,567 - INFO - 第 5 页获取到 100 条记录
2025-05-31 15:00:18,567 - INFO - Request Parameters - Page 6:
2025-05-31 15:00:18,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:18,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:19,046 - INFO - Response - Page 6:
2025-05-31 15:00:19,246 - INFO - 第 6 页获取到 100 条记录
2025-05-31 15:00:19,246 - INFO - Request Parameters - Page 7:
2025-05-31 15:00:19,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:19,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:19,742 - INFO - Response - Page 7:
2025-05-31 15:00:19,943 - INFO - 第 7 页获取到 61 条记录
2025-05-31 15:00:19,943 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 15:00:19,943 - INFO - 获取到 661 条表单数据
2025-05-31 15:00:19,954 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 15:00:19,966 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:00:19,966 - INFO - 开始处理日期: 2025-04
2025-05-31 15:00:19,966 - INFO - Request Parameters - Page 1:
2025-05-31 15:00:19,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:19,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:20,492 - INFO - Response - Page 1:
2025-05-31 15:00:20,692 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:00:20,692 - INFO - Request Parameters - Page 2:
2025-05-31 15:00:20,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:20,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:21,273 - INFO - Response - Page 2:
2025-05-31 15:00:21,474 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:00:21,474 - INFO - Request Parameters - Page 3:
2025-05-31 15:00:21,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:21,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:21,934 - INFO - Response - Page 3:
2025-05-31 15:00:22,134 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:00:22,134 - INFO - Request Parameters - Page 4:
2025-05-31 15:00:22,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:22,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:22,655 - INFO - Response - Page 4:
2025-05-31 15:00:22,857 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:00:22,857 - INFO - Request Parameters - Page 5:
2025-05-31 15:00:22,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:22,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:23,321 - INFO - Response - Page 5:
2025-05-31 15:00:23,522 - INFO - 第 5 页获取到 100 条记录
2025-05-31 15:00:23,522 - INFO - Request Parameters - Page 6:
2025-05-31 15:00:23,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:23,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:23,987 - INFO - Response - Page 6:
2025-05-31 15:00:24,188 - INFO - 第 6 页获取到 100 条记录
2025-05-31 15:00:24,188 - INFO - Request Parameters - Page 7:
2025-05-31 15:00:24,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:24,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:24,616 - INFO - Response - Page 7:
2025-05-31 15:00:24,816 - INFO - 第 7 页获取到 56 条记录
2025-05-31 15:00:24,816 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 15:00:24,816 - INFO - 获取到 656 条表单数据
2025-05-31 15:00:24,828 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 15:00:24,840 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 15:00:24,840 - INFO - 开始处理日期: 2025-05
2025-05-31 15:00:24,840 - INFO - Request Parameters - Page 1:
2025-05-31 15:00:24,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:24,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:25,423 - INFO - Response - Page 1:
2025-05-31 15:00:25,623 - INFO - 第 1 页获取到 100 条记录
2025-05-31 15:00:25,623 - INFO - Request Parameters - Page 2:
2025-05-31 15:00:25,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:25,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:26,121 - INFO - Response - Page 2:
2025-05-31 15:00:26,322 - INFO - 第 2 页获取到 100 条记录
2025-05-31 15:00:26,322 - INFO - Request Parameters - Page 3:
2025-05-31 15:00:26,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:26,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:26,816 - INFO - Response - Page 3:
2025-05-31 15:00:27,018 - INFO - 第 3 页获取到 100 条记录
2025-05-31 15:00:27,018 - INFO - Request Parameters - Page 4:
2025-05-31 15:00:27,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:27,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:27,485 - INFO - Response - Page 4:
2025-05-31 15:00:27,686 - INFO - 第 4 页获取到 100 条记录
2025-05-31 15:00:27,686 - INFO - Request Parameters - Page 5:
2025-05-31 15:00:27,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:27,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:28,206 - INFO - Response - Page 5:
2025-05-31 15:00:28,406 - INFO - 第 5 页获取到 100 条记录
2025-05-31 15:00:28,406 - INFO - Request Parameters - Page 6:
2025-05-31 15:00:28,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:28,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:29,005 - INFO - Response - Page 6:
2025-05-31 15:00:29,206 - INFO - 第 6 页获取到 100 条记录
2025-05-31 15:00:29,206 - INFO - Request Parameters - Page 7:
2025-05-31 15:00:29,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 15:00:29,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 15:00:29,615 - INFO - Response - Page 7:
2025-05-31 15:00:29,815 - INFO - 第 7 页获取到 35 条记录
2025-05-31 15:00:29,815 - INFO - 查询完成，共获取到 635 条记录
2025-05-31 15:00:29,815 - INFO - 获取到 635 条表单数据
2025-05-31 15:00:29,828 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-05-31 15:00:29,831 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-31 15:00:30,314 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-31 15:00:30,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385081.8, 'new_value': 445880.8}, {'field': 'total_amount', 'old_value': 411623.8, 'new_value': 472422.8}, {'field': 'order_count', 'old_value': 99, 'new_value': 103}]
2025-05-31 15:00:30,320 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-31 15:00:30,792 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-31 15:00:30,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356134.0, 'new_value': 371345.0}, {'field': 'total_amount', 'old_value': 356134.0, 'new_value': 371345.0}, {'field': 'order_count', 'old_value': 7022, 'new_value': 7355}]
2025-05-31 15:00:30,793 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-31 15:00:31,227 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-31 15:00:31,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 703941.0, 'new_value': 367508.0}, {'field': 'total_amount', 'old_value': 703941.0, 'new_value': 367508.0}]
2025-05-31 15:00:31,228 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-31 15:00:31,646 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-31 15:00:31,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 453585.0, 'new_value': 341585.0}, {'field': 'total_amount', 'old_value': 453585.0, 'new_value': 341585.0}]
2025-05-31 15:00:31,647 - INFO - 开始批量插入 1 条新记录
2025-05-31 15:00:31,800 - INFO - 批量插入响应状态码: 200
2025-05-31 15:00:31,800 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 31 May 2025 07:00:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AD633E47-2448-784C-9CF3-DF8046CA0E30', 'x-acs-trace-id': '1532d5645a00ef54becea326105bab18', 'etag': '61g3kxc8hEnOM38DcUEv5zg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-31 15:00:31,800 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA1XITVHDFID4U3U5UO4PEC3YIDUVBBM4G']}
2025-05-31 15:00:31,800 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-31 15:00:31,800 - INFO - 成功插入的数据ID: ['FINST-I6E66WA1XITVHDFID4U3U5UO4PEC3YIDUVBBM4G']
2025-05-31 15:00:34,801 - INFO - 批量插入完成，共 1 条记录
2025-05-31 15:00:34,801 - INFO - 日期 2025-05 处理完成 - 更新: 4 条，插入: 1 条，错误: 0 条
2025-05-31 15:00:34,801 - INFO - 数据同步完成！更新: 4 条，插入: 1 条，错误: 0 条
2025-05-31 15:00:34,803 - INFO - =================同步完成====================
2025-05-31 18:00:01,902 - INFO - =================使用默认全量同步=============
2025-05-31 18:00:03,433 - INFO - MySQL查询成功，共获取 3305 条记录
2025-05-31 18:00:03,433 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 18:00:03,465 - INFO - 开始处理日期: 2025-01
2025-05-31 18:00:03,465 - INFO - Request Parameters - Page 1:
2025-05-31 18:00:03,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:03,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:04,636 - INFO - Response - Page 1:
2025-05-31 18:00:04,840 - INFO - 第 1 页获取到 100 条记录
2025-05-31 18:00:04,840 - INFO - Request Parameters - Page 2:
2025-05-31 18:00:04,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:04,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:05,418 - INFO - Response - Page 2:
2025-05-31 18:00:05,621 - INFO - 第 2 页获取到 100 条记录
2025-05-31 18:00:05,621 - INFO - Request Parameters - Page 3:
2025-05-31 18:00:05,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:05,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:06,105 - INFO - Response - Page 3:
2025-05-31 18:00:06,308 - INFO - 第 3 页获取到 100 条记录
2025-05-31 18:00:06,308 - INFO - Request Parameters - Page 4:
2025-05-31 18:00:06,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:06,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:06,777 - INFO - Response - Page 4:
2025-05-31 18:00:06,980 - INFO - 第 4 页获取到 100 条记录
2025-05-31 18:00:06,980 - INFO - Request Parameters - Page 5:
2025-05-31 18:00:06,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:06,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:07,543 - INFO - Response - Page 5:
2025-05-31 18:00:07,746 - INFO - 第 5 页获取到 100 条记录
2025-05-31 18:00:07,746 - INFO - Request Parameters - Page 6:
2025-05-31 18:00:07,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:07,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:08,418 - INFO - Response - Page 6:
2025-05-31 18:00:08,621 - INFO - 第 6 页获取到 100 条记录
2025-05-31 18:00:08,621 - INFO - Request Parameters - Page 7:
2025-05-31 18:00:08,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:08,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:09,058 - INFO - Response - Page 7:
2025-05-31 18:00:09,277 - INFO - 第 7 页获取到 82 条记录
2025-05-31 18:00:09,277 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 18:00:09,277 - INFO - 获取到 682 条表单数据
2025-05-31 18:00:09,277 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 18:00:09,293 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:00:09,293 - INFO - 开始处理日期: 2025-02
2025-05-31 18:00:09,293 - INFO - Request Parameters - Page 1:
2025-05-31 18:00:09,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:09,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:09,871 - INFO - Response - Page 1:
2025-05-31 18:00:10,074 - INFO - 第 1 页获取到 100 条记录
2025-05-31 18:00:10,074 - INFO - Request Parameters - Page 2:
2025-05-31 18:00:10,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:10,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:10,652 - INFO - Response - Page 2:
2025-05-31 18:00:10,855 - INFO - 第 2 页获取到 100 条记录
2025-05-31 18:00:10,855 - INFO - Request Parameters - Page 3:
2025-05-31 18:00:10,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:10,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:11,339 - INFO - Response - Page 3:
2025-05-31 18:00:11,543 - INFO - 第 3 页获取到 100 条记录
2025-05-31 18:00:11,543 - INFO - Request Parameters - Page 4:
2025-05-31 18:00:11,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:11,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:12,074 - INFO - Response - Page 4:
2025-05-31 18:00:12,277 - INFO - 第 4 页获取到 100 条记录
2025-05-31 18:00:12,277 - INFO - Request Parameters - Page 5:
2025-05-31 18:00:12,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:12,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:12,886 - INFO - Response - Page 5:
2025-05-31 18:00:13,089 - INFO - 第 5 页获取到 100 条记录
2025-05-31 18:00:13,089 - INFO - Request Parameters - Page 6:
2025-05-31 18:00:13,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:13,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:13,605 - INFO - Response - Page 6:
2025-05-31 18:00:13,808 - INFO - 第 6 页获取到 100 条记录
2025-05-31 18:00:13,808 - INFO - Request Parameters - Page 7:
2025-05-31 18:00:13,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:13,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:14,230 - INFO - Response - Page 7:
2025-05-31 18:00:14,433 - INFO - 第 7 页获取到 70 条记录
2025-05-31 18:00:14,433 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 18:00:14,433 - INFO - 获取到 670 条表单数据
2025-05-31 18:00:14,433 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 18:00:14,449 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:00:14,449 - INFO - 开始处理日期: 2025-03
2025-05-31 18:00:14,449 - INFO - Request Parameters - Page 1:
2025-05-31 18:00:14,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:14,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:15,011 - INFO - Response - Page 1:
2025-05-31 18:00:15,214 - INFO - 第 1 页获取到 100 条记录
2025-05-31 18:00:15,214 - INFO - Request Parameters - Page 2:
2025-05-31 18:00:15,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:15,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:15,714 - INFO - Response - Page 2:
2025-05-31 18:00:15,918 - INFO - 第 2 页获取到 100 条记录
2025-05-31 18:00:15,918 - INFO - Request Parameters - Page 3:
2025-05-31 18:00:15,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:15,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:16,464 - INFO - Response - Page 3:
2025-05-31 18:00:16,668 - INFO - 第 3 页获取到 100 条记录
2025-05-31 18:00:16,668 - INFO - Request Parameters - Page 4:
2025-05-31 18:00:16,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:16,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:17,199 - INFO - Response - Page 4:
2025-05-31 18:00:17,402 - INFO - 第 4 页获取到 100 条记录
2025-05-31 18:00:17,402 - INFO - Request Parameters - Page 5:
2025-05-31 18:00:17,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:17,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:17,886 - INFO - Response - Page 5:
2025-05-31 18:00:18,089 - INFO - 第 5 页获取到 100 条记录
2025-05-31 18:00:18,089 - INFO - Request Parameters - Page 6:
2025-05-31 18:00:18,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:18,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:18,574 - INFO - Response - Page 6:
2025-05-31 18:00:18,777 - INFO - 第 6 页获取到 100 条记录
2025-05-31 18:00:18,777 - INFO - Request Parameters - Page 7:
2025-05-31 18:00:18,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:18,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:19,261 - INFO - Response - Page 7:
2025-05-31 18:00:19,464 - INFO - 第 7 页获取到 61 条记录
2025-05-31 18:00:19,464 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 18:00:19,464 - INFO - 获取到 661 条表单数据
2025-05-31 18:00:19,464 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 18:00:19,480 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:00:19,480 - INFO - 开始处理日期: 2025-04
2025-05-31 18:00:19,480 - INFO - Request Parameters - Page 1:
2025-05-31 18:00:19,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:19,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:20,058 - INFO - Response - Page 1:
2025-05-31 18:00:20,261 - INFO - 第 1 页获取到 100 条记录
2025-05-31 18:00:20,261 - INFO - Request Parameters - Page 2:
2025-05-31 18:00:20,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:20,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:20,918 - INFO - Response - Page 2:
2025-05-31 18:00:21,121 - INFO - 第 2 页获取到 100 条记录
2025-05-31 18:00:21,121 - INFO - Request Parameters - Page 3:
2025-05-31 18:00:21,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:21,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:21,652 - INFO - Response - Page 3:
2025-05-31 18:00:21,855 - INFO - 第 3 页获取到 100 条记录
2025-05-31 18:00:21,855 - INFO - Request Parameters - Page 4:
2025-05-31 18:00:21,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:21,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:22,386 - INFO - Response - Page 4:
2025-05-31 18:00:22,589 - INFO - 第 4 页获取到 100 条记录
2025-05-31 18:00:22,589 - INFO - Request Parameters - Page 5:
2025-05-31 18:00:22,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:22,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:23,058 - INFO - Response - Page 5:
2025-05-31 18:00:23,261 - INFO - 第 5 页获取到 100 条记录
2025-05-31 18:00:23,261 - INFO - Request Parameters - Page 6:
2025-05-31 18:00:23,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:23,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:23,808 - INFO - Response - Page 6:
2025-05-31 18:00:24,011 - INFO - 第 6 页获取到 100 条记录
2025-05-31 18:00:24,011 - INFO - Request Parameters - Page 7:
2025-05-31 18:00:24,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:24,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:24,464 - INFO - Response - Page 7:
2025-05-31 18:00:24,668 - INFO - 第 7 页获取到 56 条记录
2025-05-31 18:00:24,668 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 18:00:24,668 - INFO - 获取到 656 条表单数据
2025-05-31 18:00:24,668 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 18:00:24,683 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 18:00:24,683 - INFO - 开始处理日期: 2025-05
2025-05-31 18:00:24,683 - INFO - Request Parameters - Page 1:
2025-05-31 18:00:24,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:24,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:25,214 - INFO - Response - Page 1:
2025-05-31 18:00:25,418 - INFO - 第 1 页获取到 100 条记录
2025-05-31 18:00:25,418 - INFO - Request Parameters - Page 2:
2025-05-31 18:00:25,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:25,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:25,949 - INFO - Response - Page 2:
2025-05-31 18:00:26,152 - INFO - 第 2 页获取到 100 条记录
2025-05-31 18:00:26,152 - INFO - Request Parameters - Page 3:
2025-05-31 18:00:26,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:26,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:26,730 - INFO - Response - Page 3:
2025-05-31 18:00:26,933 - INFO - 第 3 页获取到 100 条记录
2025-05-31 18:00:26,933 - INFO - Request Parameters - Page 4:
2025-05-31 18:00:26,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:26,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:27,449 - INFO - Response - Page 4:
2025-05-31 18:00:27,652 - INFO - 第 4 页获取到 100 条记录
2025-05-31 18:00:27,652 - INFO - Request Parameters - Page 5:
2025-05-31 18:00:27,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:27,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:28,261 - INFO - Response - Page 5:
2025-05-31 18:00:28,464 - INFO - 第 5 页获取到 100 条记录
2025-05-31 18:00:28,464 - INFO - Request Parameters - Page 6:
2025-05-31 18:00:28,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:28,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:28,933 - INFO - Response - Page 6:
2025-05-31 18:00:29,136 - INFO - 第 6 页获取到 100 条记录
2025-05-31 18:00:29,136 - INFO - Request Parameters - Page 7:
2025-05-31 18:00:29,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 18:00:29,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 18:00:29,542 - INFO - Response - Page 7:
2025-05-31 18:00:29,746 - INFO - 第 7 页获取到 36 条记录
2025-05-31 18:00:29,746 - INFO - 查询完成，共获取到 636 条记录
2025-05-31 18:00:29,746 - INFO - 获取到 636 条表单数据
2025-05-31 18:00:29,746 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-05-31 18:00:29,746 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-31 18:00:30,230 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-31 18:00:30,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116859.14, 'new_value': 122300.48}, {'field': 'total_amount', 'old_value': 116859.14, 'new_value': 122300.48}, {'field': 'order_count', 'old_value': 855, 'new_value': 900}]
2025-05-31 18:00:30,230 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-31 18:00:30,730 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-31 18:00:30,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1468000.0, 'new_value': 1513000.0}, {'field': 'total_amount', 'old_value': 1468000.0, 'new_value': 1513000.0}, {'field': 'order_count', 'old_value': 348, 'new_value': 349}]
2025-05-31 18:00:30,730 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-31 18:00:31,167 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-31 18:00:31,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148900.0, 'new_value': 154315.0}, {'field': 'total_amount', 'old_value': 148900.0, 'new_value': 154315.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-31 18:00:31,167 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-31 18:00:31,652 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-31 18:00:31,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51278.0, 'new_value': 51628.0}, {'field': 'total_amount', 'old_value': 51278.0, 'new_value': 51628.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-31 18:00:31,652 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-31 18:00:32,121 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-31 18:00:32,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7289927.0, 'new_value': 7562088.0}, {'field': 'total_amount', 'old_value': 7289927.0, 'new_value': 7562088.0}, {'field': 'order_count', 'old_value': 123744, 'new_value': 128047}]
2025-05-31 18:00:32,121 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-31 18:00:32,574 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-31 18:00:32,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77109.0, 'new_value': 81046.0}, {'field': 'total_amount', 'old_value': 77109.0, 'new_value': 81046.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 84}]
2025-05-31 18:00:32,574 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-31 18:00:33,074 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-31 18:00:33,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140014.0, 'new_value': 147208.0}, {'field': 'total_amount', 'old_value': 140014.0, 'new_value': 147208.0}, {'field': 'order_count', 'old_value': 5111, 'new_value': 5435}]
2025-05-31 18:00:33,074 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-31 18:00:33,527 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-31 18:00:33,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36136.07, 'new_value': 37547.07}, {'field': 'total_amount', 'old_value': 36136.07, 'new_value': 37547.07}, {'field': 'order_count', 'old_value': 3444, 'new_value': 3579}]
2025-05-31 18:00:33,527 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-31 18:00:33,980 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-31 18:00:33,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7858000.0, 'new_value': 7923000.0}, {'field': 'total_amount', 'old_value': 7858000.0, 'new_value': 7923000.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-05-31 18:00:33,980 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-31 18:00:34,464 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-31 18:00:34,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 448715.0, 'new_value': 462295.0}, {'field': 'total_amount', 'old_value': 457533.99, 'new_value': 471113.99}, {'field': 'order_count', 'old_value': 82, 'new_value': 84}]
2025-05-31 18:00:34,464 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-31 18:00:34,917 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-31 18:00:34,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 497940.33, 'new_value': 498831.23}, {'field': 'total_amount', 'old_value': 497940.33, 'new_value': 498831.23}, {'field': 'order_count', 'old_value': 904, 'new_value': 915}]
2025-05-31 18:00:34,917 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-31 18:00:35,402 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-31 18:00:35,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100796.0, 'new_value': 107894.0}, {'field': 'total_amount', 'old_value': 100796.0, 'new_value': 107894.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-31 18:00:35,402 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-31 18:00:35,855 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-31 18:00:35,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39017.0, 'new_value': 40613.0}, {'field': 'total_amount', 'old_value': 40393.0, 'new_value': 41989.0}, {'field': 'order_count', 'old_value': 4038, 'new_value': 4207}]
2025-05-31 18:00:35,855 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-31 18:00:36,339 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-31 18:00:36,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17249.0, 'new_value': 18629.0}, {'field': 'total_amount', 'old_value': 17249.0, 'new_value': 18629.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-31 18:00:36,339 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-31 18:00:36,933 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-31 18:00:36,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91174.0, 'new_value': 102814.0}, {'field': 'total_amount', 'old_value': 91174.0, 'new_value': 102814.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-31 18:00:36,933 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-31 18:00:37,402 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-31 18:00:37,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97791.0, 'new_value': 110368.0}, {'field': 'total_amount', 'old_value': 112995.0, 'new_value': 125572.0}, {'field': 'order_count', 'old_value': 2625, 'new_value': 2915}]
2025-05-31 18:00:37,402 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-31 18:00:37,886 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-31 18:00:37,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115268.0, 'new_value': 119198.0}, {'field': 'total_amount', 'old_value': 115268.0, 'new_value': 119198.0}, {'field': 'order_count', 'old_value': 780, 'new_value': 803}]
2025-05-31 18:00:37,886 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-31 18:00:38,371 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-31 18:00:38,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31272.0, 'new_value': 31860.0}, {'field': 'total_amount', 'old_value': 31272.0, 'new_value': 31860.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-31 18:00:38,371 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-31 18:00:38,933 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-31 18:00:38,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24814.0, 'new_value': 25113.0}, {'field': 'total_amount', 'old_value': 24814.0, 'new_value': 25113.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-31 18:00:38,933 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-31 18:00:39,402 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-31 18:00:39,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1563900.0, 'new_value': 1572900.0}, {'field': 'total_amount', 'old_value': 1563900.0, 'new_value': 1572900.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-31 18:00:39,402 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-31 18:00:39,871 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-31 18:00:39,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14332.67, 'new_value': 14868.67}, {'field': 'total_amount', 'old_value': 14332.67, 'new_value': 14868.67}, {'field': 'order_count', 'old_value': 409, 'new_value': 416}]
2025-05-31 18:00:39,871 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-31 18:00:40,371 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-31 18:00:40,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89854.0, 'new_value': 94394.0}, {'field': 'total_amount', 'old_value': 89854.0, 'new_value': 94394.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-31 18:00:40,371 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-31 18:00:40,824 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-31 18:00:40,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 366475.0, 'new_value': 382064.0}, {'field': 'total_amount', 'old_value': 393565.0, 'new_value': 409154.0}, {'field': 'order_count', 'old_value': 8592, 'new_value': 8927}]
2025-05-31 18:00:40,824 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-31 18:00:41,313 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-31 18:00:41,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84000.0, 'new_value': 91000.0}, {'field': 'total_amount', 'old_value': 84000.0, 'new_value': 91000.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-31 18:00:41,314 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-31 18:00:41,761 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-31 18:00:41,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175628.82, 'new_value': 182153.82}, {'field': 'total_amount', 'old_value': 175628.82, 'new_value': 182153.82}, {'field': 'order_count', 'old_value': 1485, 'new_value': 1528}]
2025-05-31 18:00:41,761 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-31 18:00:42,230 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-31 18:00:42,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20034.0, 'new_value': 20241.0}, {'field': 'total_amount', 'old_value': 20034.0, 'new_value': 20241.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 131}]
2025-05-31 18:00:42,230 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-31 18:00:42,730 - INFO - 更新表单数据成功: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-31 18:00:42,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227266.0, 'new_value': 229010.0}, {'field': 'total_amount', 'old_value': 227266.0, 'new_value': 229010.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 192}]
2025-05-31 18:00:42,730 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-31 18:00:43,230 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-31 18:00:43,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24023.19, 'new_value': 25784.46}, {'field': 'total_amount', 'old_value': 24023.19, 'new_value': 25784.46}, {'field': 'order_count', 'old_value': 1006, 'new_value': 1073}]
2025-05-31 18:00:43,230 - INFO - 日期 2025-05 处理完成 - 更新: 28 条，插入: 0 条，错误: 0 条
2025-05-31 18:00:43,230 - INFO - 数据同步完成！更新: 28 条，插入: 0 条，错误: 0 条
2025-05-31 18:00:43,230 - INFO - =================同步完成====================
2025-05-31 21:00:02,669 - INFO - =================使用默认全量同步=============
2025-05-31 21:00:04,169 - INFO - MySQL查询成功，共获取 3305 条记录
2025-05-31 21:00:04,169 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-31 21:00:04,200 - INFO - 开始处理日期: 2025-01
2025-05-31 21:00:04,200 - INFO - Request Parameters - Page 1:
2025-05-31 21:00:04,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:04,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:05,200 - INFO - Response - Page 1:
2025-05-31 21:00:05,404 - INFO - 第 1 页获取到 100 条记录
2025-05-31 21:00:05,404 - INFO - Request Parameters - Page 2:
2025-05-31 21:00:05,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:05,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:06,154 - INFO - Response - Page 2:
2025-05-31 21:00:06,357 - INFO - 第 2 页获取到 100 条记录
2025-05-31 21:00:06,357 - INFO - Request Parameters - Page 3:
2025-05-31 21:00:06,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:06,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:06,904 - INFO - Response - Page 3:
2025-05-31 21:00:07,107 - INFO - 第 3 页获取到 100 条记录
2025-05-31 21:00:07,107 - INFO - Request Parameters - Page 4:
2025-05-31 21:00:07,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:07,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:07,607 - INFO - Response - Page 4:
2025-05-31 21:00:07,810 - INFO - 第 4 页获取到 100 条记录
2025-05-31 21:00:07,810 - INFO - Request Parameters - Page 5:
2025-05-31 21:00:07,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:07,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:08,341 - INFO - Response - Page 5:
2025-05-31 21:00:08,544 - INFO - 第 5 页获取到 100 条记录
2025-05-31 21:00:08,544 - INFO - Request Parameters - Page 6:
2025-05-31 21:00:08,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:08,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:09,013 - INFO - Response - Page 6:
2025-05-31 21:00:09,216 - INFO - 第 6 页获取到 100 条记录
2025-05-31 21:00:09,216 - INFO - Request Parameters - Page 7:
2025-05-31 21:00:09,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:09,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:09,779 - INFO - Response - Page 7:
2025-05-31 21:00:09,982 - INFO - 第 7 页获取到 82 条记录
2025-05-31 21:00:09,982 - INFO - 查询完成，共获取到 682 条记录
2025-05-31 21:00:09,982 - INFO - 获取到 682 条表单数据
2025-05-31 21:00:09,982 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-31 21:00:09,997 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:00:09,997 - INFO - 开始处理日期: 2025-02
2025-05-31 21:00:09,997 - INFO - Request Parameters - Page 1:
2025-05-31 21:00:09,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:09,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:10,482 - INFO - Response - Page 1:
2025-05-31 21:00:10,685 - INFO - 第 1 页获取到 100 条记录
2025-05-31 21:00:10,685 - INFO - Request Parameters - Page 2:
2025-05-31 21:00:10,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:10,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:11,247 - INFO - Response - Page 2:
2025-05-31 21:00:11,450 - INFO - 第 2 页获取到 100 条记录
2025-05-31 21:00:11,450 - INFO - Request Parameters - Page 3:
2025-05-31 21:00:11,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:11,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:11,950 - INFO - Response - Page 3:
2025-05-31 21:00:12,154 - INFO - 第 3 页获取到 100 条记录
2025-05-31 21:00:12,154 - INFO - Request Parameters - Page 4:
2025-05-31 21:00:12,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:12,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:12,669 - INFO - Response - Page 4:
2025-05-31 21:00:12,872 - INFO - 第 4 页获取到 100 条记录
2025-05-31 21:00:12,872 - INFO - Request Parameters - Page 5:
2025-05-31 21:00:12,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:12,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:13,357 - INFO - Response - Page 5:
2025-05-31 21:00:13,560 - INFO - 第 5 页获取到 100 条记录
2025-05-31 21:00:13,560 - INFO - Request Parameters - Page 6:
2025-05-31 21:00:13,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:13,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:14,138 - INFO - Response - Page 6:
2025-05-31 21:00:14,341 - INFO - 第 6 页获取到 100 条记录
2025-05-31 21:00:14,341 - INFO - Request Parameters - Page 7:
2025-05-31 21:00:14,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:14,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:14,794 - INFO - Response - Page 7:
2025-05-31 21:00:14,997 - INFO - 第 7 页获取到 70 条记录
2025-05-31 21:00:14,997 - INFO - 查询完成，共获取到 670 条记录
2025-05-31 21:00:14,997 - INFO - 获取到 670 条表单数据
2025-05-31 21:00:14,997 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-31 21:00:15,013 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:00:15,013 - INFO - 开始处理日期: 2025-03
2025-05-31 21:00:15,013 - INFO - Request Parameters - Page 1:
2025-05-31 21:00:15,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:15,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:15,450 - INFO - Response - Page 1:
2025-05-31 21:00:15,654 - INFO - 第 1 页获取到 100 条记录
2025-05-31 21:00:15,654 - INFO - Request Parameters - Page 2:
2025-05-31 21:00:15,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:15,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:16,200 - INFO - Response - Page 2:
2025-05-31 21:00:16,404 - INFO - 第 2 页获取到 100 条记录
2025-05-31 21:00:16,404 - INFO - Request Parameters - Page 3:
2025-05-31 21:00:16,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:16,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:16,919 - INFO - Response - Page 3:
2025-05-31 21:00:17,122 - INFO - 第 3 页获取到 100 条记录
2025-05-31 21:00:17,122 - INFO - Request Parameters - Page 4:
2025-05-31 21:00:17,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:17,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:17,544 - INFO - Response - Page 4:
2025-05-31 21:00:17,747 - INFO - 第 4 页获取到 100 条记录
2025-05-31 21:00:17,747 - INFO - Request Parameters - Page 5:
2025-05-31 21:00:17,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:17,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:18,247 - INFO - Response - Page 5:
2025-05-31 21:00:18,450 - INFO - 第 5 页获取到 100 条记录
2025-05-31 21:00:18,450 - INFO - Request Parameters - Page 6:
2025-05-31 21:00:18,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:18,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:18,904 - INFO - Response - Page 6:
2025-05-31 21:00:19,107 - INFO - 第 6 页获取到 100 条记录
2025-05-31 21:00:19,107 - INFO - Request Parameters - Page 7:
2025-05-31 21:00:19,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:19,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:19,622 - INFO - Response - Page 7:
2025-05-31 21:00:19,825 - INFO - 第 7 页获取到 61 条记录
2025-05-31 21:00:19,825 - INFO - 查询完成，共获取到 661 条记录
2025-05-31 21:00:19,825 - INFO - 获取到 661 条表单数据
2025-05-31 21:00:19,825 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-31 21:00:19,841 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:00:19,841 - INFO - 开始处理日期: 2025-04
2025-05-31 21:00:19,841 - INFO - Request Parameters - Page 1:
2025-05-31 21:00:19,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:19,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:20,412 - INFO - Response - Page 1:
2025-05-31 21:00:20,612 - INFO - 第 1 页获取到 100 条记录
2025-05-31 21:00:20,612 - INFO - Request Parameters - Page 2:
2025-05-31 21:00:20,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:20,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:21,200 - INFO - Response - Page 2:
2025-05-31 21:00:21,401 - INFO - 第 2 页获取到 100 条记录
2025-05-31 21:00:21,401 - INFO - Request Parameters - Page 3:
2025-05-31 21:00:21,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:21,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:21,917 - INFO - Response - Page 3:
2025-05-31 21:00:22,120 - INFO - 第 3 页获取到 100 条记录
2025-05-31 21:00:22,120 - INFO - Request Parameters - Page 4:
2025-05-31 21:00:22,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:22,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:22,636 - INFO - Response - Page 4:
2025-05-31 21:00:22,839 - INFO - 第 4 页获取到 100 条记录
2025-05-31 21:00:22,839 - INFO - Request Parameters - Page 5:
2025-05-31 21:00:22,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:22,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:23,339 - INFO - Response - Page 5:
2025-05-31 21:00:23,542 - INFO - 第 5 页获取到 100 条记录
2025-05-31 21:00:23,542 - INFO - Request Parameters - Page 6:
2025-05-31 21:00:23,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:23,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:24,058 - INFO - Response - Page 6:
2025-05-31 21:00:24,261 - INFO - 第 6 页获取到 100 条记录
2025-05-31 21:00:24,261 - INFO - Request Parameters - Page 7:
2025-05-31 21:00:24,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:24,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:24,667 - INFO - Response - Page 7:
2025-05-31 21:00:24,870 - INFO - 第 7 页获取到 56 条记录
2025-05-31 21:00:24,870 - INFO - 查询完成，共获取到 656 条记录
2025-05-31 21:00:24,870 - INFO - 获取到 656 条表单数据
2025-05-31 21:00:24,870 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-31 21:00:24,886 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:00:24,886 - INFO - 开始处理日期: 2025-05
2025-05-31 21:00:24,886 - INFO - Request Parameters - Page 1:
2025-05-31 21:00:24,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:24,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:25,495 - INFO - Response - Page 1:
2025-05-31 21:00:25,698 - INFO - 第 1 页获取到 100 条记录
2025-05-31 21:00:25,698 - INFO - Request Parameters - Page 2:
2025-05-31 21:00:25,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:25,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:26,261 - INFO - Response - Page 2:
2025-05-31 21:00:26,464 - INFO - 第 2 页获取到 100 条记录
2025-05-31 21:00:26,464 - INFO - Request Parameters - Page 3:
2025-05-31 21:00:26,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:26,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:26,901 - INFO - Response - Page 3:
2025-05-31 21:00:27,102 - INFO - 第 3 页获取到 100 条记录
2025-05-31 21:00:27,102 - INFO - Request Parameters - Page 4:
2025-05-31 21:00:27,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:27,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:27,616 - INFO - Response - Page 4:
2025-05-31 21:00:27,819 - INFO - 第 4 页获取到 100 条记录
2025-05-31 21:00:27,819 - INFO - Request Parameters - Page 5:
2025-05-31 21:00:27,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:27,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:28,319 - INFO - Response - Page 5:
2025-05-31 21:00:28,520 - INFO - 第 5 页获取到 100 条记录
2025-05-31 21:00:28,520 - INFO - Request Parameters - Page 6:
2025-05-31 21:00:28,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:28,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:29,021 - INFO - Response - Page 6:
2025-05-31 21:00:29,221 - INFO - 第 6 页获取到 100 条记录
2025-05-31 21:00:29,221 - INFO - Request Parameters - Page 7:
2025-05-31 21:00:29,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-31 21:00:29,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-31 21:00:29,626 - INFO - Response - Page 7:
2025-05-31 21:00:29,828 - INFO - 第 7 页获取到 36 条记录
2025-05-31 21:00:29,828 - INFO - 查询完成，共获取到 636 条记录
2025-05-31 21:00:29,828 - INFO - 获取到 636 条表单数据
2025-05-31 21:00:29,828 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-05-31 21:00:29,844 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:00:29,844 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-31 21:00:29,844 - INFO - =================同步完成====================
