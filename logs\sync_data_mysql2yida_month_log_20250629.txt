2025-06-29 00:00:03,295 - INFO - =================使用默认全量同步=============
2025-06-29 00:00:05,139 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-29 00:00:05,139 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 00:00:05,186 - INFO - 开始处理日期: 2025-01
2025-06-29 00:00:05,186 - INFO - Request Parameters - Page 1:
2025-06-29 00:00:05,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:05,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:06,873 - INFO - Response - Page 1:
2025-06-29 00:00:07,076 - INFO - 第 1 页获取到 100 条记录
2025-06-29 00:00:07,076 - INFO - Request Parameters - Page 2:
2025-06-29 00:00:07,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:07,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:07,561 - INFO - Response - Page 2:
2025-06-29 00:00:07,764 - INFO - 第 2 页获取到 100 条记录
2025-06-29 00:00:07,764 - INFO - Request Parameters - Page 3:
2025-06-29 00:00:07,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:07,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:08,264 - INFO - Response - Page 3:
2025-06-29 00:00:08,467 - INFO - 第 3 页获取到 100 条记录
2025-06-29 00:00:08,467 - INFO - Request Parameters - Page 4:
2025-06-29 00:00:08,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:08,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:08,951 - INFO - Response - Page 4:
2025-06-29 00:00:09,155 - INFO - 第 4 页获取到 100 条记录
2025-06-29 00:00:09,155 - INFO - Request Parameters - Page 5:
2025-06-29 00:00:09,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:09,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:09,655 - INFO - Response - Page 5:
2025-06-29 00:00:09,858 - INFO - 第 5 页获取到 100 条记录
2025-06-29 00:00:09,858 - INFO - Request Parameters - Page 6:
2025-06-29 00:00:09,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:09,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:10,373 - INFO - Response - Page 6:
2025-06-29 00:00:10,576 - INFO - 第 6 页获取到 100 条记录
2025-06-29 00:00:10,576 - INFO - Request Parameters - Page 7:
2025-06-29 00:00:10,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:10,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:11,061 - INFO - Response - Page 7:
2025-06-29 00:00:11,264 - INFO - 第 7 页获取到 82 条记录
2025-06-29 00:00:11,264 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 00:00:11,264 - INFO - 获取到 682 条表单数据
2025-06-29 00:00:11,264 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 00:00:11,280 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 00:00:11,280 - INFO - 开始处理日期: 2025-02
2025-06-29 00:00:11,280 - INFO - Request Parameters - Page 1:
2025-06-29 00:00:11,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:11,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:11,780 - INFO - Response - Page 1:
2025-06-29 00:00:11,983 - INFO - 第 1 页获取到 100 条记录
2025-06-29 00:00:11,983 - INFO - Request Parameters - Page 2:
2025-06-29 00:00:11,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:11,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:12,389 - INFO - Response - Page 2:
2025-06-29 00:00:12,592 - INFO - 第 2 页获取到 100 条记录
2025-06-29 00:00:12,592 - INFO - Request Parameters - Page 3:
2025-06-29 00:00:12,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:12,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:13,014 - INFO - Response - Page 3:
2025-06-29 00:00:13,217 - INFO - 第 3 页获取到 100 条记录
2025-06-29 00:00:13,217 - INFO - Request Parameters - Page 4:
2025-06-29 00:00:13,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:13,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:13,701 - INFO - Response - Page 4:
2025-06-29 00:00:13,905 - INFO - 第 4 页获取到 100 条记录
2025-06-29 00:00:13,905 - INFO - Request Parameters - Page 5:
2025-06-29 00:00:13,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:13,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:14,389 - INFO - Response - Page 5:
2025-06-29 00:00:14,592 - INFO - 第 5 页获取到 100 条记录
2025-06-29 00:00:14,592 - INFO - Request Parameters - Page 6:
2025-06-29 00:00:14,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:14,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:15,154 - INFO - Response - Page 6:
2025-06-29 00:00:15,358 - INFO - 第 6 页获取到 100 条记录
2025-06-29 00:00:15,358 - INFO - Request Parameters - Page 7:
2025-06-29 00:00:15,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:15,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:15,842 - INFO - Response - Page 7:
2025-06-29 00:00:16,045 - INFO - 第 7 页获取到 70 条记录
2025-06-29 00:00:16,045 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 00:00:16,045 - INFO - 获取到 670 条表单数据
2025-06-29 00:00:16,045 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 00:00:16,061 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 00:00:16,061 - INFO - 开始处理日期: 2025-03
2025-06-29 00:00:16,061 - INFO - Request Parameters - Page 1:
2025-06-29 00:00:16,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:16,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:16,561 - INFO - Response - Page 1:
2025-06-29 00:00:16,764 - INFO - 第 1 页获取到 100 条记录
2025-06-29 00:00:16,764 - INFO - Request Parameters - Page 2:
2025-06-29 00:00:16,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:16,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:17,264 - INFO - Response - Page 2:
2025-06-29 00:00:17,467 - INFO - 第 2 页获取到 100 条记录
2025-06-29 00:00:17,467 - INFO - Request Parameters - Page 3:
2025-06-29 00:00:17,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:17,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:17,951 - INFO - Response - Page 3:
2025-06-29 00:00:18,154 - INFO - 第 3 页获取到 100 条记录
2025-06-29 00:00:18,154 - INFO - Request Parameters - Page 4:
2025-06-29 00:00:18,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:18,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:18,748 - INFO - Response - Page 4:
2025-06-29 00:00:18,951 - INFO - 第 4 页获取到 100 条记录
2025-06-29 00:00:18,951 - INFO - Request Parameters - Page 5:
2025-06-29 00:00:18,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:18,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:19,420 - INFO - Response - Page 5:
2025-06-29 00:00:19,623 - INFO - 第 5 页获取到 100 条记录
2025-06-29 00:00:19,623 - INFO - Request Parameters - Page 6:
2025-06-29 00:00:19,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:19,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:20,123 - INFO - Response - Page 6:
2025-06-29 00:00:20,326 - INFO - 第 6 页获取到 100 条记录
2025-06-29 00:00:20,326 - INFO - Request Parameters - Page 7:
2025-06-29 00:00:20,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:20,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:20,717 - INFO - Response - Page 7:
2025-06-29 00:00:20,920 - INFO - 第 7 页获取到 61 条记录
2025-06-29 00:00:20,920 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 00:00:20,920 - INFO - 获取到 661 条表单数据
2025-06-29 00:00:20,920 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 00:00:20,936 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 00:00:20,936 - INFO - 开始处理日期: 2025-04
2025-06-29 00:00:20,936 - INFO - Request Parameters - Page 1:
2025-06-29 00:00:20,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:20,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:21,451 - INFO - Response - Page 1:
2025-06-29 00:00:21,654 - INFO - 第 1 页获取到 100 条记录
2025-06-29 00:00:21,654 - INFO - Request Parameters - Page 2:
2025-06-29 00:00:21,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:21,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:22,154 - INFO - Response - Page 2:
2025-06-29 00:00:22,358 - INFO - 第 2 页获取到 100 条记录
2025-06-29 00:00:22,358 - INFO - Request Parameters - Page 3:
2025-06-29 00:00:22,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:22,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:22,889 - INFO - Response - Page 3:
2025-06-29 00:00:23,092 - INFO - 第 3 页获取到 100 条记录
2025-06-29 00:00:23,092 - INFO - Request Parameters - Page 4:
2025-06-29 00:00:23,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:23,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:23,639 - INFO - Response - Page 4:
2025-06-29 00:00:23,842 - INFO - 第 4 页获取到 100 条记录
2025-06-29 00:00:23,842 - INFO - Request Parameters - Page 5:
2025-06-29 00:00:23,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:23,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:24,389 - INFO - Response - Page 5:
2025-06-29 00:00:24,592 - INFO - 第 5 页获取到 100 条记录
2025-06-29 00:00:24,592 - INFO - Request Parameters - Page 6:
2025-06-29 00:00:24,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:24,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:25,076 - INFO - Response - Page 6:
2025-06-29 00:00:25,279 - INFO - 第 6 页获取到 100 条记录
2025-06-29 00:00:25,279 - INFO - Request Parameters - Page 7:
2025-06-29 00:00:25,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:25,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:25,748 - INFO - Response - Page 7:
2025-06-29 00:00:25,951 - INFO - 第 7 页获取到 56 条记录
2025-06-29 00:00:25,951 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 00:00:25,951 - INFO - 获取到 656 条表单数据
2025-06-29 00:00:25,951 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 00:00:25,967 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 00:00:25,967 - INFO - 开始处理日期: 2025-05
2025-06-29 00:00:25,967 - INFO - Request Parameters - Page 1:
2025-06-29 00:00:25,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:25,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:26,451 - INFO - Response - Page 1:
2025-06-29 00:00:26,654 - INFO - 第 1 页获取到 100 条记录
2025-06-29 00:00:26,654 - INFO - Request Parameters - Page 2:
2025-06-29 00:00:26,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:26,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:27,201 - INFO - Response - Page 2:
2025-06-29 00:00:27,404 - INFO - 第 2 页获取到 100 条记录
2025-06-29 00:00:27,404 - INFO - Request Parameters - Page 3:
2025-06-29 00:00:27,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:27,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:27,967 - INFO - Response - Page 3:
2025-06-29 00:00:28,170 - INFO - 第 3 页获取到 100 条记录
2025-06-29 00:00:28,170 - INFO - Request Parameters - Page 4:
2025-06-29 00:00:28,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:28,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:28,654 - INFO - Response - Page 4:
2025-06-29 00:00:28,858 - INFO - 第 4 页获取到 100 条记录
2025-06-29 00:00:28,858 - INFO - Request Parameters - Page 5:
2025-06-29 00:00:28,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:28,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:29,436 - INFO - Response - Page 5:
2025-06-29 00:00:29,639 - INFO - 第 5 页获取到 100 条记录
2025-06-29 00:00:29,639 - INFO - Request Parameters - Page 6:
2025-06-29 00:00:29,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:29,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:30,092 - INFO - Response - Page 6:
2025-06-29 00:00:30,311 - INFO - 第 6 页获取到 100 条记录
2025-06-29 00:00:30,311 - INFO - Request Parameters - Page 7:
2025-06-29 00:00:30,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:30,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:30,733 - INFO - Response - Page 7:
2025-06-29 00:00:30,936 - INFO - 第 7 页获取到 65 条记录
2025-06-29 00:00:30,936 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 00:00:30,936 - INFO - 获取到 665 条表单数据
2025-06-29 00:00:30,936 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 00:00:30,951 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 00:00:30,951 - INFO - 开始处理日期: 2025-06
2025-06-29 00:00:30,951 - INFO - Request Parameters - Page 1:
2025-06-29 00:00:30,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:30,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:31,483 - INFO - Response - Page 1:
2025-06-29 00:00:31,686 - INFO - 第 1 页获取到 100 条记录
2025-06-29 00:00:31,686 - INFO - Request Parameters - Page 2:
2025-06-29 00:00:31,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:31,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:32,170 - INFO - Response - Page 2:
2025-06-29 00:00:32,373 - INFO - 第 2 页获取到 100 条记录
2025-06-29 00:00:32,373 - INFO - Request Parameters - Page 3:
2025-06-29 00:00:32,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:32,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:32,811 - INFO - Response - Page 3:
2025-06-29 00:00:33,014 - INFO - 第 3 页获取到 100 条记录
2025-06-29 00:00:33,014 - INFO - Request Parameters - Page 4:
2025-06-29 00:00:33,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:33,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:33,482 - INFO - Response - Page 4:
2025-06-29 00:00:33,686 - INFO - 第 4 页获取到 100 条记录
2025-06-29 00:00:33,686 - INFO - Request Parameters - Page 5:
2025-06-29 00:00:33,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:33,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:34,123 - INFO - Response - Page 5:
2025-06-29 00:00:34,326 - INFO - 第 5 页获取到 100 条记录
2025-06-29 00:00:34,326 - INFO - Request Parameters - Page 6:
2025-06-29 00:00:34,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:34,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:34,795 - INFO - Response - Page 6:
2025-06-29 00:00:34,998 - INFO - 第 6 页获取到 100 条记录
2025-06-29 00:00:34,998 - INFO - Request Parameters - Page 7:
2025-06-29 00:00:34,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 00:00:34,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 00:00:35,436 - INFO - Response - Page 7:
2025-06-29 00:00:35,639 - INFO - 第 7 页获取到 29 条记录
2025-06-29 00:00:35,639 - INFO - 查询完成，共获取到 629 条记录
2025-06-29 00:00:35,639 - INFO - 获取到 629 条表单数据
2025-06-29 00:00:35,639 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-29 00:00:35,639 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-29 00:00:36,123 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-29 00:00:36,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161960.0, 'new_value': 186960.0}, {'field': 'total_amount', 'old_value': 161960.0, 'new_value': 186960.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-29 00:00:36,123 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-29 00:00:36,498 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-29 00:00:36,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51286.2, 'new_value': 53736.5}, {'field': 'total_amount', 'old_value': 51286.2, 'new_value': 53736.5}, {'field': 'order_count', 'old_value': 456, 'new_value': 463}]
2025-06-29 00:00:36,514 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-29 00:00:36,904 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-29 00:00:36,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 894299.0, 'new_value': 933241.0}, {'field': 'total_amount', 'old_value': 894299.0, 'new_value': 933241.0}, {'field': 'order_count', 'old_value': 189, 'new_value': 202}]
2025-06-29 00:00:36,904 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-29 00:00:37,357 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-29 00:00:37,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144151.84, 'new_value': 149759.18}, {'field': 'total_amount', 'old_value': 144151.84, 'new_value': 149759.18}, {'field': 'order_count', 'old_value': 5142, 'new_value': 5345}]
2025-06-29 00:00:37,357 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-29 00:00:37,779 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-29 00:00:37,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1488073.9, 'new_value': 1555769.57}, {'field': 'total_amount', 'old_value': 1488073.9, 'new_value': 1555769.57}, {'field': 'order_count', 'old_value': 16613, 'new_value': 17282}]
2025-06-29 00:00:37,779 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-29 00:00:38,201 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-29 00:00:38,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68634.44, 'new_value': 70358.05}, {'field': 'offline_amount', 'old_value': 95900.77, 'new_value': 101409.93}, {'field': 'total_amount', 'old_value': 164535.21, 'new_value': 171767.98}, {'field': 'order_count', 'old_value': 5560, 'new_value': 5797}]
2025-06-29 00:00:38,201 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-29 00:00:38,607 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-29 00:00:38,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5985.0, 'new_value': 6161.0}, {'field': 'total_amount', 'old_value': 5985.0, 'new_value': 6161.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-29 00:00:38,607 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-29 00:00:39,014 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-29 00:00:39,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 601289.0, 'new_value': 610176.0}, {'field': 'total_amount', 'old_value': 648874.0, 'new_value': 657761.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 213}]
2025-06-29 00:00:39,014 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-29 00:00:39,404 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-29 00:00:39,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12976.0, 'new_value': 13551.0}, {'field': 'total_amount', 'old_value': 12977.0, 'new_value': 13552.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 32}]
2025-06-29 00:00:39,404 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-29 00:00:39,811 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-29 00:00:39,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22582.3, 'new_value': 23489.3}, {'field': 'total_amount', 'old_value': 22582.3, 'new_value': 23489.3}, {'field': 'order_count', 'old_value': 120, 'new_value': 125}]
2025-06-29 00:00:39,811 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-29 00:00:40,201 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-29 00:00:40,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46635.13, 'new_value': 48150.4}, {'field': 'offline_amount', 'old_value': 712309.88, 'new_value': 736582.15}, {'field': 'total_amount', 'old_value': 758945.01, 'new_value': 784732.55}, {'field': 'order_count', 'old_value': 3032, 'new_value': 3137}]
2025-06-29 00:00:40,201 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-29 00:00:40,654 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-29 00:00:40,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25261.0, 'new_value': 26480.0}, {'field': 'total_amount', 'old_value': 25261.0, 'new_value': 26480.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 108}]
2025-06-29 00:00:40,654 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-29 00:00:41,045 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-29 00:00:41,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 508147.0, 'new_value': 543887.0}, {'field': 'total_amount', 'old_value': 512063.0, 'new_value': 547803.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 89}]
2025-06-29 00:00:41,045 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-29 00:00:41,451 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-29 00:00:41,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220917.5, 'new_value': 226025.5}, {'field': 'total_amount', 'old_value': 220917.5, 'new_value': 226025.5}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-06-29 00:00:41,451 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-29 00:00:41,842 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-29 00:00:41,842 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12404.97, 'new_value': 14991.01}, {'field': 'offline_amount', 'old_value': 10225.0, 'new_value': 10464.0}, {'field': 'total_amount', 'old_value': 22629.97, 'new_value': 25455.01}, {'field': 'order_count', 'old_value': 106, 'new_value': 115}]
2025-06-29 00:00:41,842 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-29 00:00:42,217 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-29 00:00:42,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34734.0, 'new_value': 35871.0}, {'field': 'total_amount', 'old_value': 35211.0, 'new_value': 36348.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 84}]
2025-06-29 00:00:42,217 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-29 00:00:42,639 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-29 00:00:42,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4498.05, 'new_value': 5332.11}, {'field': 'total_amount', 'old_value': 115213.05, 'new_value': 116047.11}, {'field': 'order_count', 'old_value': 5614, 'new_value': 5615}]
2025-06-29 00:00:42,639 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-29 00:00:43,076 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-29 00:00:43,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188353.0, 'new_value': 191400.0}, {'field': 'total_amount', 'old_value': 257284.55, 'new_value': 260331.55}, {'field': 'order_count', 'old_value': 98, 'new_value': 100}]
2025-06-29 00:00:43,076 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-29 00:00:43,529 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-29 00:00:43,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9696.0, 'new_value': 9984.0}, {'field': 'total_amount', 'old_value': 62841.0, 'new_value': 63129.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-06-29 00:00:43,529 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-29 00:00:43,936 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-29 00:00:43,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66832.0, 'new_value': 70269.0}, {'field': 'total_amount', 'old_value': 66832.0, 'new_value': 70269.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 187}]
2025-06-29 00:00:43,936 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-29 00:00:44,342 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-29 00:00:44,342 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27409.0, 'new_value': 28915.9}, {'field': 'offline_amount', 'old_value': 107952.6, 'new_value': 110883.6}, {'field': 'total_amount', 'old_value': 135361.6, 'new_value': 139799.5}, {'field': 'order_count', 'old_value': 170, 'new_value': 177}]
2025-06-29 00:00:44,342 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-29 00:00:44,670 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-29 00:00:44,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265680.53, 'new_value': 270423.53}, {'field': 'total_amount', 'old_value': 265680.53, 'new_value': 270423.53}, {'field': 'order_count', 'old_value': 158, 'new_value': 163}]
2025-06-29 00:00:44,670 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-29 00:00:45,139 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-29 00:00:45,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18849.68, 'new_value': 20007.51}, {'field': 'total_amount', 'old_value': 21066.05, 'new_value': 22223.88}, {'field': 'order_count', 'old_value': 417, 'new_value': 440}]
2025-06-29 00:00:45,139 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-29 00:00:45,607 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-29 00:00:45,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49776.0, 'new_value': 50621.0}, {'field': 'total_amount', 'old_value': 49776.0, 'new_value': 50621.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 42}]
2025-06-29 00:00:45,607 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-29 00:00:46,029 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-29 00:00:46,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92199.1, 'new_value': 95313.3}, {'field': 'total_amount', 'old_value': 92199.1, 'new_value': 95313.3}, {'field': 'order_count', 'old_value': 302, 'new_value': 314}]
2025-06-29 00:00:46,029 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-29 00:00:46,436 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-29 00:00:46,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79874.22, 'new_value': 84622.59}, {'field': 'total_amount', 'old_value': 79874.22, 'new_value': 84622.59}, {'field': 'order_count', 'old_value': 2591, 'new_value': 2750}]
2025-06-29 00:00:46,451 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYQ
2025-06-29 00:00:46,842 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYQ
2025-06-29 00:00:46,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2598.0, 'new_value': 5978.0}, {'field': 'total_amount', 'old_value': 49486.0, 'new_value': 52866.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-29 00:00:46,842 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-29 00:00:47,264 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-29 00:00:47,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46762.0, 'new_value': 48237.0}, {'field': 'total_amount', 'old_value': 46762.0, 'new_value': 48237.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 118}]
2025-06-29 00:00:47,264 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-29 00:00:47,732 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-29 00:00:47,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34966.19, 'new_value': 37164.69}, {'field': 'total_amount', 'old_value': 34966.19, 'new_value': 37164.69}, {'field': 'order_count', 'old_value': 209, 'new_value': 218}]
2025-06-29 00:00:47,732 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-29 00:00:48,107 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-29 00:00:48,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63894.5, 'new_value': 67693.5}, {'field': 'total_amount', 'old_value': 70519.9, 'new_value': 74318.9}, {'field': 'order_count', 'old_value': 173, 'new_value': 180}]
2025-06-29 00:00:48,123 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-29 00:00:48,561 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-29 00:00:48,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5095900.0, 'new_value': 5895700.0}, {'field': 'total_amount', 'old_value': 5095900.0, 'new_value': 5895700.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-06-29 00:00:48,561 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-29 00:00:48,904 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-29 00:00:48,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162488.0, 'new_value': 171086.0}, {'field': 'total_amount', 'old_value': 162488.0, 'new_value': 171086.0}, {'field': 'order_count', 'old_value': 609, 'new_value': 644}]
2025-06-29 00:00:48,904 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-29 00:00:49,326 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-29 00:00:49,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107696.86, 'new_value': 113521.69}, {'field': 'offline_amount', 'old_value': 52972.52, 'new_value': 56136.27}, {'field': 'total_amount', 'old_value': 160669.38, 'new_value': 169657.96}, {'field': 'order_count', 'old_value': 9269, 'new_value': 9810}]
2025-06-29 00:00:49,326 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-29 00:00:49,857 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-29 00:00:49,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63123.54, 'new_value': 65717.65}, {'field': 'offline_amount', 'old_value': 80791.74, 'new_value': 86133.47}, {'field': 'total_amount', 'old_value': 143915.28, 'new_value': 151851.12}, {'field': 'order_count', 'old_value': 5705, 'new_value': 5994}]
2025-06-29 00:00:49,857 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-29 00:00:50,248 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-29 00:00:50,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75564.08, 'new_value': 79445.54}, {'field': 'offline_amount', 'old_value': 96272.14, 'new_value': 100032.3}, {'field': 'total_amount', 'old_value': 171836.22, 'new_value': 179477.84}, {'field': 'order_count', 'old_value': 8848, 'new_value': 9261}]
2025-06-29 00:00:50,248 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-29 00:00:50,639 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-29 00:00:50,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92021.01, 'new_value': 98370.47}, {'field': 'total_amount', 'old_value': 92021.01, 'new_value': 98370.47}, {'field': 'order_count', 'old_value': 3455, 'new_value': 3695}]
2025-06-29 00:00:50,639 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-29 00:00:51,107 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-29 00:00:51,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401856.88, 'new_value': 425599.68}, {'field': 'total_amount', 'old_value': 401856.88, 'new_value': 425599.68}, {'field': 'order_count', 'old_value': 2097, 'new_value': 2212}]
2025-06-29 00:00:51,107 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-29 00:00:51,482 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-29 00:00:51,482 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28249.19, 'new_value': 28857.19}, {'field': 'total_amount', 'old_value': 28749.19, 'new_value': 29357.19}, {'field': 'order_count', 'old_value': 126, 'new_value': 132}]
2025-06-29 00:00:51,482 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-29 00:00:51,857 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-29 00:00:51,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46352.21, 'new_value': 48376.07}, {'field': 'offline_amount', 'old_value': 20144.43, 'new_value': 21305.71}, {'field': 'total_amount', 'old_value': 66496.64, 'new_value': 69681.78}, {'field': 'order_count', 'old_value': 2988, 'new_value': 3153}]
2025-06-29 00:00:51,857 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-29 00:00:52,264 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-29 00:00:52,264 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19294.13, 'new_value': 20129.69}, {'field': 'offline_amount', 'old_value': 11412.79, 'new_value': 12208.69}, {'field': 'total_amount', 'old_value': 30706.92, 'new_value': 32338.38}, {'field': 'order_count', 'old_value': 2500, 'new_value': 2596}]
2025-06-29 00:00:52,264 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-29 00:00:52,670 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-29 00:00:52,670 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44234.65, 'new_value': 46289.49}, {'field': 'offline_amount', 'old_value': 283834.39, 'new_value': 302670.23}, {'field': 'total_amount', 'old_value': 328069.04, 'new_value': 348959.72}, {'field': 'order_count', 'old_value': 36618, 'new_value': 37130}]
2025-06-29 00:00:52,670 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-29 00:00:53,107 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-29 00:00:53,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 433860.0, 'new_value': 489653.0}, {'field': 'total_amount', 'old_value': 443660.0, 'new_value': 499453.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 106}]
2025-06-29 00:00:53,107 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-29 00:00:53,560 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-29 00:00:53,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153796.01, 'new_value': 166594.61}, {'field': 'total_amount', 'old_value': 153796.01, 'new_value': 166594.61}, {'field': 'order_count', 'old_value': 334, 'new_value': 359}]
2025-06-29 00:00:53,560 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-29 00:00:53,982 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-29 00:00:53,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131305.0, 'new_value': 145819.0}, {'field': 'total_amount', 'old_value': 135872.0, 'new_value': 150386.0}, {'field': 'order_count', 'old_value': 4359, 'new_value': 4376}]
2025-06-29 00:00:53,982 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-29 00:00:54,404 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-29 00:00:54,404 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84448.17, 'new_value': 86855.35}, {'field': 'offline_amount', 'old_value': 342341.05, 'new_value': 364799.49}, {'field': 'total_amount', 'old_value': 426789.22, 'new_value': 451654.84}, {'field': 'order_count', 'old_value': 4855, 'new_value': 5123}]
2025-06-29 00:00:54,404 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-29 00:00:54,826 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-29 00:00:54,826 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40795.93, 'new_value': 42797.22}, {'field': 'offline_amount', 'old_value': 35295.94, 'new_value': 37295.94}, {'field': 'total_amount', 'old_value': 76091.87, 'new_value': 80093.16}, {'field': 'order_count', 'old_value': 3683, 'new_value': 3885}]
2025-06-29 00:00:54,826 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-29 00:00:55,248 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-29 00:00:55,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85043.36, 'new_value': 90068.56}, {'field': 'offline_amount', 'old_value': 32910.85, 'new_value': 34198.05}, {'field': 'total_amount', 'old_value': 117954.21, 'new_value': 124266.61}, {'field': 'order_count', 'old_value': 6712, 'new_value': 7041}]
2025-06-29 00:00:55,248 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-29 00:00:55,685 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-29 00:00:55,685 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46958.0, 'new_value': 49351.0}, {'field': 'offline_amount', 'old_value': 156547.07, 'new_value': 160926.07}, {'field': 'total_amount', 'old_value': 203505.07, 'new_value': 210277.07}, {'field': 'order_count', 'old_value': 286, 'new_value': 298}]
2025-06-29 00:00:55,685 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-29 00:00:56,123 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-29 00:00:56,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 496495.01, 'new_value': 523678.5}, {'field': 'total_amount', 'old_value': 496495.01, 'new_value': 523678.5}, {'field': 'order_count', 'old_value': 7248, 'new_value': 7596}]
2025-06-29 00:00:56,123 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-29 00:00:56,514 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-29 00:00:56,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216579.35, 'new_value': 230054.01}, {'field': 'total_amount', 'old_value': 276260.26, 'new_value': 289734.92}, {'field': 'order_count', 'old_value': 12304, 'new_value': 12921}]
2025-06-29 00:00:56,514 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-29 00:00:56,889 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-29 00:00:56,889 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83894.62, 'new_value': 88126.87}, {'field': 'offline_amount', 'old_value': 66392.95, 'new_value': 69807.4}, {'field': 'total_amount', 'old_value': 150287.57, 'new_value': 157934.27}, {'field': 'order_count', 'old_value': 7004, 'new_value': 7365}]
2025-06-29 00:00:56,889 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-29 00:00:57,295 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-29 00:00:57,295 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13231.87, 'new_value': 13951.19}, {'field': 'offline_amount', 'old_value': 30944.4, 'new_value': 32817.8}, {'field': 'total_amount', 'old_value': 44176.27, 'new_value': 46768.99}, {'field': 'order_count', 'old_value': 2281, 'new_value': 2420}]
2025-06-29 00:00:57,295 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-29 00:00:57,685 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-29 00:00:57,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249047.0, 'new_value': 261298.0}, {'field': 'total_amount', 'old_value': 249047.0, 'new_value': 261298.0}, {'field': 'order_count', 'old_value': 251, 'new_value': 257}]
2025-06-29 00:00:57,685 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-29 00:00:58,123 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-29 00:00:58,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24062.0, 'new_value': 25593.0}, {'field': 'total_amount', 'old_value': 24062.0, 'new_value': 25593.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 111}]
2025-06-29 00:00:58,123 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-29 00:00:58,576 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-29 00:00:58,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124056.73, 'new_value': 129744.48}, {'field': 'offline_amount', 'old_value': 247586.68, 'new_value': 263886.92}, {'field': 'total_amount', 'old_value': 371643.41, 'new_value': 393631.4}, {'field': 'order_count', 'old_value': 13462, 'new_value': 14183}]
2025-06-29 00:00:58,576 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-29 00:00:58,982 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-29 00:00:58,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 189685.41, 'new_value': 204223.91}, {'field': 'offline_amount', 'old_value': 224308.71, 'new_value': 234308.71}, {'field': 'total_amount', 'old_value': 413994.12, 'new_value': 438532.62}, {'field': 'order_count', 'old_value': 1339, 'new_value': 1407}]
2025-06-29 00:00:58,982 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-29 00:00:59,389 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-29 00:00:59,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 779682.44, 'new_value': 834481.7}, {'field': 'total_amount', 'old_value': 779682.44, 'new_value': 834481.7}, {'field': 'order_count', 'old_value': 5251, 'new_value': 5648}]
2025-06-29 00:00:59,389 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-29 00:00:59,810 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-29 00:00:59,810 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11862.0, 'new_value': 12101.0}, {'field': 'total_amount', 'old_value': 11862.0, 'new_value': 12101.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-29 00:00:59,810 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-29 00:01:00,279 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-29 00:01:00,279 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103932.4, 'new_value': 105231.7}, {'field': 'total_amount', 'old_value': 103932.4, 'new_value': 105231.7}, {'field': 'order_count', 'old_value': 221, 'new_value': 224}]
2025-06-29 00:01:00,279 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-29 00:01:00,670 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-29 00:01:00,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 419428.45, 'new_value': 442688.97}, {'field': 'total_amount', 'old_value': 419428.45, 'new_value': 442688.97}, {'field': 'order_count', 'old_value': 17134, 'new_value': 18063}]
2025-06-29 00:01:00,670 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-29 00:01:01,092 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-29 00:01:01,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 655219.0, 'new_value': 676583.0}, {'field': 'total_amount', 'old_value': 655219.0, 'new_value': 676583.0}, {'field': 'order_count', 'old_value': 451, 'new_value': 494}]
2025-06-29 00:01:01,107 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-29 00:01:01,623 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-29 00:01:01,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'total_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-29 00:01:01,623 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-29 00:01:01,998 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-29 00:01:01,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11260000.0, 'new_value': 11660000.0}, {'field': 'total_amount', 'old_value': 11260000.0, 'new_value': 11660000.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-06-29 00:01:01,998 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-29 00:01:02,420 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-29 00:01:02,420 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161283.06, 'new_value': 168505.5}, {'field': 'offline_amount', 'old_value': 217025.07, 'new_value': 227400.99}, {'field': 'total_amount', 'old_value': 378308.13, 'new_value': 395906.49}, {'field': 'order_count', 'old_value': 12871, 'new_value': 13466}]
2025-06-29 00:01:02,420 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-29 00:01:02,857 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-29 00:01:02,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 266253.22, 'new_value': 275971.79}, {'field': 'offline_amount', 'old_value': 326932.74, 'new_value': 347843.77}, {'field': 'total_amount', 'old_value': 593185.96, 'new_value': 623815.56}, {'field': 'order_count', 'old_value': 18932, 'new_value': 19705}]
2025-06-29 00:01:02,857 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-29 00:01:03,264 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-29 00:01:03,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165197.72, 'new_value': 177263.29}, {'field': 'total_amount', 'old_value': 209204.63, 'new_value': 221270.2}, {'field': 'order_count', 'old_value': 11974, 'new_value': 12617}]
2025-06-29 00:01:03,264 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-29 00:01:03,685 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-29 00:01:03,685 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11827.11, 'new_value': 12128.41}, {'field': 'offline_amount', 'old_value': 86476.32, 'new_value': 92917.67}, {'field': 'total_amount', 'old_value': 98303.43, 'new_value': 105046.08}, {'field': 'order_count', 'old_value': 2996, 'new_value': 3180}]
2025-06-29 00:01:03,685 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-29 00:01:04,139 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-29 00:01:04,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38209.2, 'new_value': 41042.2}, {'field': 'total_amount', 'old_value': 38692.2, 'new_value': 41525.2}, {'field': 'order_count', 'old_value': 177, 'new_value': 178}]
2025-06-29 00:01:04,139 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-29 00:01:04,545 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-29 00:01:04,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176636.0, 'new_value': 189564.73}, {'field': 'offline_amount', 'old_value': 56222.7, 'new_value': 61624.66}, {'field': 'total_amount', 'old_value': 232858.7, 'new_value': 251189.39}, {'field': 'order_count', 'old_value': 1016, 'new_value': 1089}]
2025-06-29 00:01:04,545 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-29 00:01:04,936 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-29 00:01:04,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46275.5, 'new_value': 49608.6}, {'field': 'total_amount', 'old_value': 46275.5, 'new_value': 49608.6}, {'field': 'order_count', 'old_value': 372, 'new_value': 398}]
2025-06-29 00:01:04,936 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-29 00:01:05,342 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-29 00:01:05,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66525.32, 'new_value': 72581.32}, {'field': 'total_amount', 'old_value': 66525.32, 'new_value': 72581.32}, {'field': 'order_count', 'old_value': 393, 'new_value': 428}]
2025-06-29 00:01:05,342 - INFO - 开始更新记录 - 表单实例ID: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMJ9
2025-06-29 00:01:05,748 - INFO - 更新表单数据成功: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMJ9
2025-06-29 00:01:05,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4355.52, 'new_value': 11669.52}, {'field': 'total_amount', 'old_value': 9009.92, 'new_value': 16323.92}, {'field': 'order_count', 'old_value': 143, 'new_value': 248}]
2025-06-29 00:01:05,748 - INFO - 日期 2025-06 处理完成 - 更新: 72 条，插入: 0 条，错误: 0 条
2025-06-29 00:01:05,748 - INFO - 数据同步完成！更新: 72 条，插入: 0 条，错误: 0 条
2025-06-29 00:01:05,748 - INFO - =================同步完成====================
2025-06-29 03:00:03,316 - INFO - =================使用默认全量同步=============
2025-06-29 03:00:05,129 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-29 03:00:05,129 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 03:00:05,160 - INFO - 开始处理日期: 2025-01
2025-06-29 03:00:05,160 - INFO - Request Parameters - Page 1:
2025-06-29 03:00:05,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:05,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:06,676 - INFO - Response - Page 1:
2025-06-29 03:00:06,879 - INFO - 第 1 页获取到 100 条记录
2025-06-29 03:00:06,879 - INFO - Request Parameters - Page 2:
2025-06-29 03:00:06,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:06,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:07,613 - INFO - Response - Page 2:
2025-06-29 03:00:07,816 - INFO - 第 2 页获取到 100 条记录
2025-06-29 03:00:07,816 - INFO - Request Parameters - Page 3:
2025-06-29 03:00:07,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:07,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:08,332 - INFO - Response - Page 3:
2025-06-29 03:00:08,535 - INFO - 第 3 页获取到 100 条记录
2025-06-29 03:00:08,535 - INFO - Request Parameters - Page 4:
2025-06-29 03:00:08,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:08,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:09,019 - INFO - Response - Page 4:
2025-06-29 03:00:09,223 - INFO - 第 4 页获取到 100 条记录
2025-06-29 03:00:09,223 - INFO - Request Parameters - Page 5:
2025-06-29 03:00:09,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:09,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:09,769 - INFO - Response - Page 5:
2025-06-29 03:00:09,973 - INFO - 第 5 页获取到 100 条记录
2025-06-29 03:00:09,973 - INFO - Request Parameters - Page 6:
2025-06-29 03:00:09,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:09,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:10,504 - INFO - Response - Page 6:
2025-06-29 03:00:10,707 - INFO - 第 6 页获取到 100 条记录
2025-06-29 03:00:10,707 - INFO - Request Parameters - Page 7:
2025-06-29 03:00:10,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:10,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:11,223 - INFO - Response - Page 7:
2025-06-29 03:00:11,426 - INFO - 第 7 页获取到 82 条记录
2025-06-29 03:00:11,426 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 03:00:11,426 - INFO - 获取到 682 条表单数据
2025-06-29 03:00:11,426 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 03:00:11,441 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:11,441 - INFO - 开始处理日期: 2025-02
2025-06-29 03:00:11,441 - INFO - Request Parameters - Page 1:
2025-06-29 03:00:11,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:11,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:12,004 - INFO - Response - Page 1:
2025-06-29 03:00:12,207 - INFO - 第 1 页获取到 100 条记录
2025-06-29 03:00:12,207 - INFO - Request Parameters - Page 2:
2025-06-29 03:00:12,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:12,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:12,801 - INFO - Response - Page 2:
2025-06-29 03:00:13,004 - INFO - 第 2 页获取到 100 条记录
2025-06-29 03:00:13,004 - INFO - Request Parameters - Page 3:
2025-06-29 03:00:13,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:13,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:13,519 - INFO - Response - Page 3:
2025-06-29 03:00:13,723 - INFO - 第 3 页获取到 100 条记录
2025-06-29 03:00:13,723 - INFO - Request Parameters - Page 4:
2025-06-29 03:00:13,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:13,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:14,285 - INFO - Response - Page 4:
2025-06-29 03:00:14,488 - INFO - 第 4 页获取到 100 条记录
2025-06-29 03:00:14,488 - INFO - Request Parameters - Page 5:
2025-06-29 03:00:14,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:14,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:14,957 - INFO - Response - Page 5:
2025-06-29 03:00:15,160 - INFO - 第 5 页获取到 100 条记录
2025-06-29 03:00:15,160 - INFO - Request Parameters - Page 6:
2025-06-29 03:00:15,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:15,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:15,660 - INFO - Response - Page 6:
2025-06-29 03:00:15,863 - INFO - 第 6 页获取到 100 条记录
2025-06-29 03:00:15,863 - INFO - Request Parameters - Page 7:
2025-06-29 03:00:15,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:15,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:16,379 - INFO - Response - Page 7:
2025-06-29 03:00:16,582 - INFO - 第 7 页获取到 70 条记录
2025-06-29 03:00:16,582 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 03:00:16,582 - INFO - 获取到 670 条表单数据
2025-06-29 03:00:16,597 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 03:00:16,613 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:16,613 - INFO - 开始处理日期: 2025-03
2025-06-29 03:00:16,613 - INFO - Request Parameters - Page 1:
2025-06-29 03:00:16,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:16,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:17,097 - INFO - Response - Page 1:
2025-06-29 03:00:17,301 - INFO - 第 1 页获取到 100 条记录
2025-06-29 03:00:17,301 - INFO - Request Parameters - Page 2:
2025-06-29 03:00:17,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:17,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:17,863 - INFO - Response - Page 2:
2025-06-29 03:00:18,066 - INFO - 第 2 页获取到 100 条记录
2025-06-29 03:00:18,066 - INFO - Request Parameters - Page 3:
2025-06-29 03:00:18,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:18,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:18,551 - INFO - Response - Page 3:
2025-06-29 03:00:18,754 - INFO - 第 3 页获取到 100 条记录
2025-06-29 03:00:18,754 - INFO - Request Parameters - Page 4:
2025-06-29 03:00:18,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:18,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:19,207 - INFO - Response - Page 4:
2025-06-29 03:00:19,410 - INFO - 第 4 页获取到 100 条记录
2025-06-29 03:00:19,410 - INFO - Request Parameters - Page 5:
2025-06-29 03:00:19,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:19,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:19,910 - INFO - Response - Page 5:
2025-06-29 03:00:20,113 - INFO - 第 5 页获取到 100 条记录
2025-06-29 03:00:20,113 - INFO - Request Parameters - Page 6:
2025-06-29 03:00:20,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:20,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:20,660 - INFO - Response - Page 6:
2025-06-29 03:00:20,863 - INFO - 第 6 页获取到 100 条记录
2025-06-29 03:00:20,863 - INFO - Request Parameters - Page 7:
2025-06-29 03:00:20,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:20,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:21,285 - INFO - Response - Page 7:
2025-06-29 03:00:21,488 - INFO - 第 7 页获取到 61 条记录
2025-06-29 03:00:21,488 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 03:00:21,488 - INFO - 获取到 661 条表单数据
2025-06-29 03:00:21,488 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 03:00:21,504 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:21,504 - INFO - 开始处理日期: 2025-04
2025-06-29 03:00:21,504 - INFO - Request Parameters - Page 1:
2025-06-29 03:00:21,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:21,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:21,957 - INFO - Response - Page 1:
2025-06-29 03:00:22,160 - INFO - 第 1 页获取到 100 条记录
2025-06-29 03:00:22,160 - INFO - Request Parameters - Page 2:
2025-06-29 03:00:22,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:22,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:22,629 - INFO - Response - Page 2:
2025-06-29 03:00:22,847 - INFO - 第 2 页获取到 100 条记录
2025-06-29 03:00:22,847 - INFO - Request Parameters - Page 3:
2025-06-29 03:00:22,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:22,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:23,332 - INFO - Response - Page 3:
2025-06-29 03:00:23,535 - INFO - 第 3 页获取到 100 条记录
2025-06-29 03:00:23,535 - INFO - Request Parameters - Page 4:
2025-06-29 03:00:23,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:23,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:24,051 - INFO - Response - Page 4:
2025-06-29 03:00:24,254 - INFO - 第 4 页获取到 100 条记录
2025-06-29 03:00:24,254 - INFO - Request Parameters - Page 5:
2025-06-29 03:00:24,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:24,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:24,769 - INFO - Response - Page 5:
2025-06-29 03:00:24,972 - INFO - 第 5 页获取到 100 条记录
2025-06-29 03:00:24,972 - INFO - Request Parameters - Page 6:
2025-06-29 03:00:24,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:24,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:25,519 - INFO - Response - Page 6:
2025-06-29 03:00:25,722 - INFO - 第 6 页获取到 100 条记录
2025-06-29 03:00:25,722 - INFO - Request Parameters - Page 7:
2025-06-29 03:00:25,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:25,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:26,129 - INFO - Response - Page 7:
2025-06-29 03:00:26,332 - INFO - 第 7 页获取到 56 条记录
2025-06-29 03:00:26,332 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 03:00:26,332 - INFO - 获取到 656 条表单数据
2025-06-29 03:00:26,332 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 03:00:26,347 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:26,347 - INFO - 开始处理日期: 2025-05
2025-06-29 03:00:26,347 - INFO - Request Parameters - Page 1:
2025-06-29 03:00:26,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:26,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:26,879 - INFO - Response - Page 1:
2025-06-29 03:00:27,082 - INFO - 第 1 页获取到 100 条记录
2025-06-29 03:00:27,082 - INFO - Request Parameters - Page 2:
2025-06-29 03:00:27,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:27,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:27,551 - INFO - Response - Page 2:
2025-06-29 03:00:27,754 - INFO - 第 2 页获取到 100 条记录
2025-06-29 03:00:27,754 - INFO - Request Parameters - Page 3:
2025-06-29 03:00:27,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:27,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:28,301 - INFO - Response - Page 3:
2025-06-29 03:00:28,504 - INFO - 第 3 页获取到 100 条记录
2025-06-29 03:00:28,504 - INFO - Request Parameters - Page 4:
2025-06-29 03:00:28,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:28,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:29,004 - INFO - Response - Page 4:
2025-06-29 03:00:29,207 - INFO - 第 4 页获取到 100 条记录
2025-06-29 03:00:29,207 - INFO - Request Parameters - Page 5:
2025-06-29 03:00:29,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:29,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:29,738 - INFO - Response - Page 5:
2025-06-29 03:00:29,941 - INFO - 第 5 页获取到 100 条记录
2025-06-29 03:00:29,941 - INFO - Request Parameters - Page 6:
2025-06-29 03:00:29,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:29,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:30,488 - INFO - Response - Page 6:
2025-06-29 03:00:30,691 - INFO - 第 6 页获取到 100 条记录
2025-06-29 03:00:30,691 - INFO - Request Parameters - Page 7:
2025-06-29 03:00:30,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:30,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:31,129 - INFO - Response - Page 7:
2025-06-29 03:00:31,332 - INFO - 第 7 页获取到 65 条记录
2025-06-29 03:00:31,332 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 03:00:31,332 - INFO - 获取到 665 条表单数据
2025-06-29 03:00:31,332 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 03:00:31,347 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:31,347 - INFO - 开始处理日期: 2025-06
2025-06-29 03:00:31,347 - INFO - Request Parameters - Page 1:
2025-06-29 03:00:31,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:31,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:31,863 - INFO - Response - Page 1:
2025-06-29 03:00:32,066 - INFO - 第 1 页获取到 100 条记录
2025-06-29 03:00:32,066 - INFO - Request Parameters - Page 2:
2025-06-29 03:00:32,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:32,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:32,691 - INFO - Response - Page 2:
2025-06-29 03:00:32,894 - INFO - 第 2 页获取到 100 条记录
2025-06-29 03:00:32,894 - INFO - Request Parameters - Page 3:
2025-06-29 03:00:32,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:32,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:33,410 - INFO - Response - Page 3:
2025-06-29 03:00:33,613 - INFO - 第 3 页获取到 100 条记录
2025-06-29 03:00:33,613 - INFO - Request Parameters - Page 4:
2025-06-29 03:00:33,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:33,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:34,144 - INFO - Response - Page 4:
2025-06-29 03:00:34,347 - INFO - 第 4 页获取到 100 条记录
2025-06-29 03:00:34,347 - INFO - Request Parameters - Page 5:
2025-06-29 03:00:34,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:34,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:34,832 - INFO - Response - Page 5:
2025-06-29 03:00:35,035 - INFO - 第 5 页获取到 100 条记录
2025-06-29 03:00:35,035 - INFO - Request Parameters - Page 6:
2025-06-29 03:00:35,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:35,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:35,519 - INFO - Response - Page 6:
2025-06-29 03:00:35,722 - INFO - 第 6 页获取到 100 条记录
2025-06-29 03:00:35,722 - INFO - Request Parameters - Page 7:
2025-06-29 03:00:35,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 03:00:35,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 03:00:36,066 - INFO - Response - Page 7:
2025-06-29 03:00:36,269 - INFO - 第 7 页获取到 29 条记录
2025-06-29 03:00:36,269 - INFO - 查询完成，共获取到 629 条记录
2025-06-29 03:00:36,269 - INFO - 获取到 629 条表单数据
2025-06-29 03:00:36,269 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-29 03:00:36,285 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:36,285 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 03:00:36,285 - INFO - =================同步完成====================
2025-06-29 06:00:02,994 - INFO - =================使用默认全量同步=============
2025-06-29 06:00:04,759 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-29 06:00:04,759 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 06:00:04,791 - INFO - 开始处理日期: 2025-01
2025-06-29 06:00:04,791 - INFO - Request Parameters - Page 1:
2025-06-29 06:00:04,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:04,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:06,087 - INFO - Response - Page 1:
2025-06-29 06:00:06,291 - INFO - 第 1 页获取到 100 条记录
2025-06-29 06:00:06,291 - INFO - Request Parameters - Page 2:
2025-06-29 06:00:06,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:06,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:06,806 - INFO - Response - Page 2:
2025-06-29 06:00:07,009 - INFO - 第 2 页获取到 100 条记录
2025-06-29 06:00:07,009 - INFO - Request Parameters - Page 3:
2025-06-29 06:00:07,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:07,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:07,556 - INFO - Response - Page 3:
2025-06-29 06:00:07,759 - INFO - 第 3 页获取到 100 条记录
2025-06-29 06:00:07,759 - INFO - Request Parameters - Page 4:
2025-06-29 06:00:07,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:07,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:08,619 - INFO - Response - Page 4:
2025-06-29 06:00:08,822 - INFO - 第 4 页获取到 100 条记录
2025-06-29 06:00:08,822 - INFO - Request Parameters - Page 5:
2025-06-29 06:00:08,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:08,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:09,400 - INFO - Response - Page 5:
2025-06-29 06:00:09,603 - INFO - 第 5 页获取到 100 条记录
2025-06-29 06:00:09,603 - INFO - Request Parameters - Page 6:
2025-06-29 06:00:09,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:09,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:10,166 - INFO - Response - Page 6:
2025-06-29 06:00:10,369 - INFO - 第 6 页获取到 100 条记录
2025-06-29 06:00:10,369 - INFO - Request Parameters - Page 7:
2025-06-29 06:00:10,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:10,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:10,884 - INFO - Response - Page 7:
2025-06-29 06:00:11,087 - INFO - 第 7 页获取到 82 条记录
2025-06-29 06:00:11,087 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 06:00:11,087 - INFO - 获取到 682 条表单数据
2025-06-29 06:00:11,087 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 06:00:11,103 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:11,103 - INFO - 开始处理日期: 2025-02
2025-06-29 06:00:11,103 - INFO - Request Parameters - Page 1:
2025-06-29 06:00:11,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:11,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:11,603 - INFO - Response - Page 1:
2025-06-29 06:00:11,806 - INFO - 第 1 页获取到 100 条记录
2025-06-29 06:00:11,806 - INFO - Request Parameters - Page 2:
2025-06-29 06:00:11,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:11,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:12,244 - INFO - Response - Page 2:
2025-06-29 06:00:12,447 - INFO - 第 2 页获取到 100 条记录
2025-06-29 06:00:12,447 - INFO - Request Parameters - Page 3:
2025-06-29 06:00:12,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:12,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:12,994 - INFO - Response - Page 3:
2025-06-29 06:00:13,197 - INFO - 第 3 页获取到 100 条记录
2025-06-29 06:00:13,197 - INFO - Request Parameters - Page 4:
2025-06-29 06:00:13,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:13,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:13,697 - INFO - Response - Page 4:
2025-06-29 06:00:13,900 - INFO - 第 4 页获取到 100 条记录
2025-06-29 06:00:13,900 - INFO - Request Parameters - Page 5:
2025-06-29 06:00:13,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:13,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:14,400 - INFO - Response - Page 5:
2025-06-29 06:00:14,603 - INFO - 第 5 页获取到 100 条记录
2025-06-29 06:00:14,603 - INFO - Request Parameters - Page 6:
2025-06-29 06:00:14,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:14,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:15,166 - INFO - Response - Page 6:
2025-06-29 06:00:15,369 - INFO - 第 6 页获取到 100 条记录
2025-06-29 06:00:15,369 - INFO - Request Parameters - Page 7:
2025-06-29 06:00:15,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:15,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:15,806 - INFO - Response - Page 7:
2025-06-29 06:00:16,009 - INFO - 第 7 页获取到 70 条记录
2025-06-29 06:00:16,009 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 06:00:16,009 - INFO - 获取到 670 条表单数据
2025-06-29 06:00:16,009 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 06:00:16,025 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:16,025 - INFO - 开始处理日期: 2025-03
2025-06-29 06:00:16,025 - INFO - Request Parameters - Page 1:
2025-06-29 06:00:16,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:16,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:16,603 - INFO - Response - Page 1:
2025-06-29 06:00:16,806 - INFO - 第 1 页获取到 100 条记录
2025-06-29 06:00:16,806 - INFO - Request Parameters - Page 2:
2025-06-29 06:00:16,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:16,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:17,478 - INFO - Response - Page 2:
2025-06-29 06:00:17,681 - INFO - 第 2 页获取到 100 条记录
2025-06-29 06:00:17,681 - INFO - Request Parameters - Page 3:
2025-06-29 06:00:17,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:17,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:18,197 - INFO - Response - Page 3:
2025-06-29 06:00:18,400 - INFO - 第 3 页获取到 100 条记录
2025-06-29 06:00:18,400 - INFO - Request Parameters - Page 4:
2025-06-29 06:00:18,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:18,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:18,915 - INFO - Response - Page 4:
2025-06-29 06:00:19,119 - INFO - 第 4 页获取到 100 条记录
2025-06-29 06:00:19,119 - INFO - Request Parameters - Page 5:
2025-06-29 06:00:19,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:19,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:19,665 - INFO - Response - Page 5:
2025-06-29 06:00:19,869 - INFO - 第 5 页获取到 100 条记录
2025-06-29 06:00:19,869 - INFO - Request Parameters - Page 6:
2025-06-29 06:00:19,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:19,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:20,447 - INFO - Response - Page 6:
2025-06-29 06:00:20,650 - INFO - 第 6 页获取到 100 条记录
2025-06-29 06:00:20,650 - INFO - Request Parameters - Page 7:
2025-06-29 06:00:20,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:20,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:21,072 - INFO - Response - Page 7:
2025-06-29 06:00:21,275 - INFO - 第 7 页获取到 61 条记录
2025-06-29 06:00:21,275 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 06:00:21,275 - INFO - 获取到 661 条表单数据
2025-06-29 06:00:21,275 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 06:00:21,290 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:21,290 - INFO - 开始处理日期: 2025-04
2025-06-29 06:00:21,290 - INFO - Request Parameters - Page 1:
2025-06-29 06:00:21,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:21,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:21,884 - INFO - Response - Page 1:
2025-06-29 06:00:22,087 - INFO - 第 1 页获取到 100 条记录
2025-06-29 06:00:22,087 - INFO - Request Parameters - Page 2:
2025-06-29 06:00:22,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:22,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:22,587 - INFO - Response - Page 2:
2025-06-29 06:00:22,790 - INFO - 第 2 页获取到 100 条记录
2025-06-29 06:00:22,790 - INFO - Request Parameters - Page 3:
2025-06-29 06:00:22,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:22,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:23,290 - INFO - Response - Page 3:
2025-06-29 06:00:23,494 - INFO - 第 3 页获取到 100 条记录
2025-06-29 06:00:23,494 - INFO - Request Parameters - Page 4:
2025-06-29 06:00:23,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:23,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:23,994 - INFO - Response - Page 4:
2025-06-29 06:00:24,197 - INFO - 第 4 页获取到 100 条记录
2025-06-29 06:00:24,197 - INFO - Request Parameters - Page 5:
2025-06-29 06:00:24,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:24,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:24,681 - INFO - Response - Page 5:
2025-06-29 06:00:24,884 - INFO - 第 5 页获取到 100 条记录
2025-06-29 06:00:24,884 - INFO - Request Parameters - Page 6:
2025-06-29 06:00:24,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:24,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:25,384 - INFO - Response - Page 6:
2025-06-29 06:00:25,587 - INFO - 第 6 页获取到 100 条记录
2025-06-29 06:00:25,587 - INFO - Request Parameters - Page 7:
2025-06-29 06:00:25,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:25,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:26,025 - INFO - Response - Page 7:
2025-06-29 06:00:26,228 - INFO - 第 7 页获取到 56 条记录
2025-06-29 06:00:26,228 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 06:00:26,228 - INFO - 获取到 656 条表单数据
2025-06-29 06:00:26,228 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 06:00:26,244 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:26,244 - INFO - 开始处理日期: 2025-05
2025-06-29 06:00:26,244 - INFO - Request Parameters - Page 1:
2025-06-29 06:00:26,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:26,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:26,744 - INFO - Response - Page 1:
2025-06-29 06:00:26,947 - INFO - 第 1 页获取到 100 条记录
2025-06-29 06:00:26,947 - INFO - Request Parameters - Page 2:
2025-06-29 06:00:26,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:26,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:27,447 - INFO - Response - Page 2:
2025-06-29 06:00:27,650 - INFO - 第 2 页获取到 100 条记录
2025-06-29 06:00:27,650 - INFO - Request Parameters - Page 3:
2025-06-29 06:00:27,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:27,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:28,165 - INFO - Response - Page 3:
2025-06-29 06:00:28,369 - INFO - 第 3 页获取到 100 条记录
2025-06-29 06:00:28,369 - INFO - Request Parameters - Page 4:
2025-06-29 06:00:28,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:28,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:28,884 - INFO - Response - Page 4:
2025-06-29 06:00:29,087 - INFO - 第 4 页获取到 100 条记录
2025-06-29 06:00:29,087 - INFO - Request Parameters - Page 5:
2025-06-29 06:00:29,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:29,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:29,603 - INFO - Response - Page 5:
2025-06-29 06:00:29,806 - INFO - 第 5 页获取到 100 条记录
2025-06-29 06:00:29,806 - INFO - Request Parameters - Page 6:
2025-06-29 06:00:29,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:29,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:30,337 - INFO - Response - Page 6:
2025-06-29 06:00:30,540 - INFO - 第 6 页获取到 100 条记录
2025-06-29 06:00:30,540 - INFO - Request Parameters - Page 7:
2025-06-29 06:00:30,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:30,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:31,134 - INFO - Response - Page 7:
2025-06-29 06:00:31,337 - INFO - 第 7 页获取到 65 条记录
2025-06-29 06:00:31,337 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 06:00:31,337 - INFO - 获取到 665 条表单数据
2025-06-29 06:00:31,337 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 06:00:31,353 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:31,353 - INFO - 开始处理日期: 2025-06
2025-06-29 06:00:31,353 - INFO - Request Parameters - Page 1:
2025-06-29 06:00:31,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:31,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:31,869 - INFO - Response - Page 1:
2025-06-29 06:00:32,072 - INFO - 第 1 页获取到 100 条记录
2025-06-29 06:00:32,072 - INFO - Request Parameters - Page 2:
2025-06-29 06:00:32,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:32,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:32,603 - INFO - Response - Page 2:
2025-06-29 06:00:32,806 - INFO - 第 2 页获取到 100 条记录
2025-06-29 06:00:32,806 - INFO - Request Parameters - Page 3:
2025-06-29 06:00:32,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:32,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:33,306 - INFO - Response - Page 3:
2025-06-29 06:00:33,509 - INFO - 第 3 页获取到 100 条记录
2025-06-29 06:00:33,509 - INFO - Request Parameters - Page 4:
2025-06-29 06:00:33,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:33,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:33,962 - INFO - Response - Page 4:
2025-06-29 06:00:34,165 - INFO - 第 4 页获取到 100 条记录
2025-06-29 06:00:34,165 - INFO - Request Parameters - Page 5:
2025-06-29 06:00:34,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:34,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:34,697 - INFO - Response - Page 5:
2025-06-29 06:00:34,900 - INFO - 第 5 页获取到 100 条记录
2025-06-29 06:00:34,900 - INFO - Request Parameters - Page 6:
2025-06-29 06:00:34,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:34,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:35,494 - INFO - Response - Page 6:
2025-06-29 06:00:35,697 - INFO - 第 6 页获取到 100 条记录
2025-06-29 06:00:35,697 - INFO - Request Parameters - Page 7:
2025-06-29 06:00:35,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 06:00:35,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 06:00:36,103 - INFO - Response - Page 7:
2025-06-29 06:00:36,306 - INFO - 第 7 页获取到 29 条记录
2025-06-29 06:00:36,306 - INFO - 查询完成，共获取到 629 条记录
2025-06-29 06:00:36,306 - INFO - 获取到 629 条表单数据
2025-06-29 06:00:36,306 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-29 06:00:36,306 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-29 06:00:36,822 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-29 06:00:36,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168966.0, 'new_value': 181411.0}, {'field': 'total_amount', 'old_value': 168966.0, 'new_value': 181411.0}, {'field': 'order_count', 'old_value': 2811, 'new_value': 3054}]
2025-06-29 06:00:36,822 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-29 06:00:37,400 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-29 06:00:37,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143067.51, 'new_value': 152490.57}, {'field': 'total_amount', 'old_value': 143067.51, 'new_value': 152490.57}, {'field': 'order_count', 'old_value': 3773, 'new_value': 4010}]
2025-06-29 06:00:37,400 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-29 06:00:37,790 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-29 06:00:37,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158930.11, 'new_value': 164499.11}, {'field': 'total_amount', 'old_value': 185704.82, 'new_value': 191273.82}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-06-29 06:00:37,806 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-29 06:00:38,275 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-29 06:00:38,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300178.0, 'new_value': 318081.0}, {'field': 'total_amount', 'old_value': 335178.0, 'new_value': 353081.0}, {'field': 'order_count', 'old_value': 7457, 'new_value': 7644}]
2025-06-29 06:00:38,275 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-29 06:00:38,759 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-29 06:00:38,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 662399.4, 'new_value': 710810.94}, {'field': 'total_amount', 'old_value': 662399.4, 'new_value': 710810.94}, {'field': 'order_count', 'old_value': 11465, 'new_value': 12379}]
2025-06-29 06:00:38,759 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-29 06:00:39,181 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-29 06:00:39,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10738.27, 'new_value': 11307.27}, {'field': 'offline_amount', 'old_value': 33463.83, 'new_value': 36095.74}, {'field': 'total_amount', 'old_value': 44202.1, 'new_value': 47403.01}, {'field': 'order_count', 'old_value': 1543, 'new_value': 1665}]
2025-06-29 06:00:39,197 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-29 06:00:39,665 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-29 06:00:39,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129090.2, 'new_value': 140305.9}, {'field': 'offline_amount', 'old_value': 136007.4, 'new_value': 146648.5}, {'field': 'total_amount', 'old_value': 265097.6, 'new_value': 286954.4}, {'field': 'order_count', 'old_value': 4816, 'new_value': 5253}]
2025-06-29 06:00:39,665 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-29 06:00:40,118 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-29 06:00:40,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61484.82, 'new_value': 64106.09}, {'field': 'offline_amount', 'old_value': 227010.58, 'new_value': 240827.41}, {'field': 'total_amount', 'old_value': 288495.4, 'new_value': 304933.5}, {'field': 'order_count', 'old_value': 6602, 'new_value': 6924}]
2025-06-29 06:00:40,118 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-29 06:00:40,525 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-29 06:00:40,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6591.64, 'new_value': 7235.64}, {'field': 'offline_amount', 'old_value': 366976.77, 'new_value': 390466.27}, {'field': 'total_amount', 'old_value': 373568.41, 'new_value': 397701.91}, {'field': 'order_count', 'old_value': 18752, 'new_value': 19961}]
2025-06-29 06:00:40,525 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-29 06:00:40,978 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-29 06:00:40,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31865.83, 'new_value': 32212.54}, {'field': 'offline_amount', 'old_value': 215087.3, 'new_value': 229191.2}, {'field': 'total_amount', 'old_value': 246953.13, 'new_value': 261403.74}, {'field': 'order_count', 'old_value': 8022, 'new_value': 8552}]
2025-06-29 06:00:40,978 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-29 06:00:41,525 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-29 06:00:41,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158968.66, 'new_value': 168931.41}, {'field': 'total_amount', 'old_value': 158968.66, 'new_value': 168931.41}, {'field': 'order_count', 'old_value': 7082, 'new_value': 7528}]
2025-06-29 06:00:41,525 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-29 06:00:41,915 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-29 06:00:41,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229242.95, 'new_value': 242075.86}, {'field': 'total_amount', 'old_value': 229242.95, 'new_value': 242075.86}, {'field': 'order_count', 'old_value': 1864, 'new_value': 1965}]
2025-06-29 06:00:41,915 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-29 06:00:42,384 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-29 06:00:42,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 268327.39, 'new_value': 284398.33}, {'field': 'total_amount', 'old_value': 268327.39, 'new_value': 284398.33}, {'field': 'order_count', 'old_value': 5665, 'new_value': 6046}]
2025-06-29 06:00:42,384 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-29 06:00:42,837 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-29 06:00:42,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56264.36, 'new_value': 58324.57}, {'field': 'offline_amount', 'old_value': 370605.81, 'new_value': 398961.81}, {'field': 'total_amount', 'old_value': 426870.17, 'new_value': 457286.38}, {'field': 'order_count', 'old_value': 3553, 'new_value': 3785}]
2025-06-29 06:00:42,837 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-29 06:00:43,322 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-29 06:00:43,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 304792.55, 'new_value': 306039.55}, {'field': 'offline_amount', 'old_value': 30713.89, 'new_value': 42593.17}, {'field': 'total_amount', 'old_value': 335506.44, 'new_value': 348632.72}, {'field': 'order_count', 'old_value': 12616, 'new_value': 13182}]
2025-06-29 06:00:43,322 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-29 06:00:43,712 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-29 06:00:43,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136300.0, 'new_value': 143470.0}, {'field': 'total_amount', 'old_value': 136300.0, 'new_value': 143470.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 257}]
2025-06-29 06:00:43,712 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-29 06:00:44,134 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-29 06:00:44,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4070.0, 'new_value': 4224.0}, {'field': 'offline_amount', 'old_value': 25876.2, 'new_value': 27654.0}, {'field': 'total_amount', 'old_value': 29946.2, 'new_value': 31878.0}, {'field': 'order_count', 'old_value': 1025, 'new_value': 1087}]
2025-06-29 06:00:44,134 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-29 06:00:44,587 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-29 06:00:44,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153630.09, 'new_value': 160418.55}, {'field': 'offline_amount', 'old_value': 46759.15, 'new_value': 48886.71}, {'field': 'total_amount', 'old_value': 200389.24, 'new_value': 209305.26}, {'field': 'order_count', 'old_value': 11896, 'new_value': 12498}]
2025-06-29 06:00:44,603 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-29 06:00:45,103 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-29 06:00:45,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73220.34, 'new_value': 77041.05}, {'field': 'offline_amount', 'old_value': 96807.05, 'new_value': 104451.29}, {'field': 'total_amount', 'old_value': 170027.39, 'new_value': 181492.34}, {'field': 'order_count', 'old_value': 1994, 'new_value': 2125}]
2025-06-29 06:00:45,103 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR1
2025-06-29 06:00:45,572 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR1
2025-06-29 06:00:45,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4226.8, 'new_value': 4794.8}, {'field': 'total_amount', 'old_value': 4226.8, 'new_value': 4794.8}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-29 06:00:45,572 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-29 06:00:45,962 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-29 06:00:45,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140223.55, 'new_value': 146173.26}, {'field': 'total_amount', 'old_value': 140223.55, 'new_value': 146173.26}, {'field': 'order_count', 'old_value': 3608, 'new_value': 3775}]
2025-06-29 06:00:45,962 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-29 06:00:46,384 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-29 06:00:46,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114929.63, 'new_value': 125479.63}, {'field': 'total_amount', 'old_value': 181487.33, 'new_value': 192037.33}, {'field': 'order_count', 'old_value': 4371, 'new_value': 4664}]
2025-06-29 06:00:46,384 - INFO - 开始更新记录 - 表单实例ID: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMK9
2025-06-29 06:00:46,884 - INFO - 更新表单数据成功: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMK9
2025-06-29 06:00:46,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4062.96, 'new_value': 10551.66}, {'field': 'total_amount', 'old_value': 4062.96, 'new_value': 10551.66}, {'field': 'order_count', 'old_value': 192, 'new_value': 514}]
2025-06-29 06:00:46,884 - INFO - 日期 2025-06 处理完成 - 更新: 23 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:46,884 - INFO - 数据同步完成！更新: 23 条，插入: 0 条，错误: 0 条
2025-06-29 06:00:46,884 - INFO - =================同步完成====================
2025-06-29 09:00:03,077 - INFO - =================使用默认全量同步=============
2025-06-29 09:00:04,874 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-29 09:00:04,874 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 09:00:04,905 - INFO - 开始处理日期: 2025-01
2025-06-29 09:00:04,905 - INFO - Request Parameters - Page 1:
2025-06-29 09:00:04,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:04,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:06,327 - INFO - Response - Page 1:
2025-06-29 09:00:06,530 - INFO - 第 1 页获取到 100 条记录
2025-06-29 09:00:06,530 - INFO - Request Parameters - Page 2:
2025-06-29 09:00:06,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:06,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:07,092 - INFO - Response - Page 2:
2025-06-29 09:00:07,295 - INFO - 第 2 页获取到 100 条记录
2025-06-29 09:00:07,295 - INFO - Request Parameters - Page 3:
2025-06-29 09:00:07,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:07,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:07,780 - INFO - Response - Page 3:
2025-06-29 09:00:07,983 - INFO - 第 3 页获取到 100 条记录
2025-06-29 09:00:07,983 - INFO - Request Parameters - Page 4:
2025-06-29 09:00:07,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:07,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:08,499 - INFO - Response - Page 4:
2025-06-29 09:00:08,702 - INFO - 第 4 页获取到 100 条记录
2025-06-29 09:00:08,702 - INFO - Request Parameters - Page 5:
2025-06-29 09:00:08,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:08,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:09,186 - INFO - Response - Page 5:
2025-06-29 09:00:09,389 - INFO - 第 5 页获取到 100 条记录
2025-06-29 09:00:09,389 - INFO - Request Parameters - Page 6:
2025-06-29 09:00:09,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:09,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:09,920 - INFO - Response - Page 6:
2025-06-29 09:00:10,124 - INFO - 第 6 页获取到 100 条记录
2025-06-29 09:00:10,124 - INFO - Request Parameters - Page 7:
2025-06-29 09:00:10,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:10,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:10,561 - INFO - Response - Page 7:
2025-06-29 09:00:10,764 - INFO - 第 7 页获取到 82 条记录
2025-06-29 09:00:10,764 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 09:00:10,764 - INFO - 获取到 682 条表单数据
2025-06-29 09:00:10,764 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 09:00:10,780 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 09:00:10,780 - INFO - 开始处理日期: 2025-02
2025-06-29 09:00:10,780 - INFO - Request Parameters - Page 1:
2025-06-29 09:00:10,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:10,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:11,280 - INFO - Response - Page 1:
2025-06-29 09:00:11,483 - INFO - 第 1 页获取到 100 条记录
2025-06-29 09:00:11,483 - INFO - Request Parameters - Page 2:
2025-06-29 09:00:11,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:11,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:11,999 - INFO - Response - Page 2:
2025-06-29 09:00:12,202 - INFO - 第 2 页获取到 100 条记录
2025-06-29 09:00:12,202 - INFO - Request Parameters - Page 3:
2025-06-29 09:00:12,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:12,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:12,733 - INFO - Response - Page 3:
2025-06-29 09:00:12,936 - INFO - 第 3 页获取到 100 条记录
2025-06-29 09:00:12,936 - INFO - Request Parameters - Page 4:
2025-06-29 09:00:12,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:12,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:13,467 - INFO - Response - Page 4:
2025-06-29 09:00:13,670 - INFO - 第 4 页获取到 100 条记录
2025-06-29 09:00:13,670 - INFO - Request Parameters - Page 5:
2025-06-29 09:00:13,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:13,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:14,233 - INFO - Response - Page 5:
2025-06-29 09:00:14,436 - INFO - 第 5 页获取到 100 条记录
2025-06-29 09:00:14,436 - INFO - Request Parameters - Page 6:
2025-06-29 09:00:14,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:14,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:14,999 - INFO - Response - Page 6:
2025-06-29 09:00:15,202 - INFO - 第 6 页获取到 100 条记录
2025-06-29 09:00:15,202 - INFO - Request Parameters - Page 7:
2025-06-29 09:00:15,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:15,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:15,686 - INFO - Response - Page 7:
2025-06-29 09:00:15,889 - INFO - 第 7 页获取到 70 条记录
2025-06-29 09:00:15,889 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 09:00:15,889 - INFO - 获取到 670 条表单数据
2025-06-29 09:00:15,889 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 09:00:15,905 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 09:00:15,905 - INFO - 开始处理日期: 2025-03
2025-06-29 09:00:15,905 - INFO - Request Parameters - Page 1:
2025-06-29 09:00:15,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:15,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:16,514 - INFO - Response - Page 1:
2025-06-29 09:00:16,717 - INFO - 第 1 页获取到 100 条记录
2025-06-29 09:00:16,717 - INFO - Request Parameters - Page 2:
2025-06-29 09:00:16,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:16,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:17,264 - INFO - Response - Page 2:
2025-06-29 09:00:17,467 - INFO - 第 2 页获取到 100 条记录
2025-06-29 09:00:17,467 - INFO - Request Parameters - Page 3:
2025-06-29 09:00:17,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:17,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:17,936 - INFO - Response - Page 3:
2025-06-29 09:00:18,139 - INFO - 第 3 页获取到 100 条记录
2025-06-29 09:00:18,139 - INFO - Request Parameters - Page 4:
2025-06-29 09:00:18,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:18,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:18,702 - INFO - Response - Page 4:
2025-06-29 09:00:18,905 - INFO - 第 4 页获取到 100 条记录
2025-06-29 09:00:18,905 - INFO - Request Parameters - Page 5:
2025-06-29 09:00:18,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:18,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:19,420 - INFO - Response - Page 5:
2025-06-29 09:00:19,624 - INFO - 第 5 页获取到 100 条记录
2025-06-29 09:00:19,624 - INFO - Request Parameters - Page 6:
2025-06-29 09:00:19,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:19,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:20,108 - INFO - Response - Page 6:
2025-06-29 09:00:20,311 - INFO - 第 6 页获取到 100 条记录
2025-06-29 09:00:20,311 - INFO - Request Parameters - Page 7:
2025-06-29 09:00:20,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:20,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:20,733 - INFO - Response - Page 7:
2025-06-29 09:00:20,936 - INFO - 第 7 页获取到 61 条记录
2025-06-29 09:00:20,936 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 09:00:20,936 - INFO - 获取到 661 条表单数据
2025-06-29 09:00:20,936 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 09:00:20,952 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 09:00:20,952 - INFO - 开始处理日期: 2025-04
2025-06-29 09:00:20,952 - INFO - Request Parameters - Page 1:
2025-06-29 09:00:20,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:20,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:21,530 - INFO - Response - Page 1:
2025-06-29 09:00:21,733 - INFO - 第 1 页获取到 100 条记录
2025-06-29 09:00:21,733 - INFO - Request Parameters - Page 2:
2025-06-29 09:00:21,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:21,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:22,295 - INFO - Response - Page 2:
2025-06-29 09:00:22,499 - INFO - 第 2 页获取到 100 条记录
2025-06-29 09:00:22,499 - INFO - Request Parameters - Page 3:
2025-06-29 09:00:22,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:22,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:22,967 - INFO - Response - Page 3:
2025-06-29 09:00:23,170 - INFO - 第 3 页获取到 100 条记录
2025-06-29 09:00:23,170 - INFO - Request Parameters - Page 4:
2025-06-29 09:00:23,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:23,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:23,702 - INFO - Response - Page 4:
2025-06-29 09:00:23,905 - INFO - 第 4 页获取到 100 条记录
2025-06-29 09:00:23,905 - INFO - Request Parameters - Page 5:
2025-06-29 09:00:23,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:23,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:24,389 - INFO - Response - Page 5:
2025-06-29 09:00:24,592 - INFO - 第 5 页获取到 100 条记录
2025-06-29 09:00:24,592 - INFO - Request Parameters - Page 6:
2025-06-29 09:00:24,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:24,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:25,077 - INFO - Response - Page 6:
2025-06-29 09:00:25,280 - INFO - 第 6 页获取到 100 条记录
2025-06-29 09:00:25,280 - INFO - Request Parameters - Page 7:
2025-06-29 09:00:25,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:25,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:25,702 - INFO - Response - Page 7:
2025-06-29 09:00:25,905 - INFO - 第 7 页获取到 56 条记录
2025-06-29 09:00:25,905 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 09:00:25,905 - INFO - 获取到 656 条表单数据
2025-06-29 09:00:25,905 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 09:00:25,920 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 09:00:25,920 - INFO - 开始处理日期: 2025-05
2025-06-29 09:00:25,920 - INFO - Request Parameters - Page 1:
2025-06-29 09:00:25,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:25,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:26,467 - INFO - Response - Page 1:
2025-06-29 09:00:26,670 - INFO - 第 1 页获取到 100 条记录
2025-06-29 09:00:26,670 - INFO - Request Parameters - Page 2:
2025-06-29 09:00:26,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:26,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:27,248 - INFO - Response - Page 2:
2025-06-29 09:00:27,452 - INFO - 第 2 页获取到 100 条记录
2025-06-29 09:00:27,452 - INFO - Request Parameters - Page 3:
2025-06-29 09:00:27,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:27,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:27,936 - INFO - Response - Page 3:
2025-06-29 09:00:28,139 - INFO - 第 3 页获取到 100 条记录
2025-06-29 09:00:28,139 - INFO - Request Parameters - Page 4:
2025-06-29 09:00:28,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:28,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:28,686 - INFO - Response - Page 4:
2025-06-29 09:00:28,889 - INFO - 第 4 页获取到 100 条记录
2025-06-29 09:00:28,889 - INFO - Request Parameters - Page 5:
2025-06-29 09:00:28,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:28,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:29,498 - INFO - Response - Page 5:
2025-06-29 09:00:29,702 - INFO - 第 5 页获取到 100 条记录
2025-06-29 09:00:29,702 - INFO - Request Parameters - Page 6:
2025-06-29 09:00:29,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:29,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:30,233 - INFO - Response - Page 6:
2025-06-29 09:00:30,436 - INFO - 第 6 页获取到 100 条记录
2025-06-29 09:00:30,436 - INFO - Request Parameters - Page 7:
2025-06-29 09:00:30,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:30,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:30,967 - INFO - Response - Page 7:
2025-06-29 09:00:31,170 - INFO - 第 7 页获取到 65 条记录
2025-06-29 09:00:31,170 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 09:00:31,170 - INFO - 获取到 665 条表单数据
2025-06-29 09:00:31,170 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 09:00:31,186 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 09:00:31,186 - INFO - 开始处理日期: 2025-06
2025-06-29 09:00:31,186 - INFO - Request Parameters - Page 1:
2025-06-29 09:00:31,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:31,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:31,733 - INFO - Response - Page 1:
2025-06-29 09:00:31,936 - INFO - 第 1 页获取到 100 条记录
2025-06-29 09:00:31,936 - INFO - Request Parameters - Page 2:
2025-06-29 09:00:31,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:31,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:32,358 - INFO - Response - Page 2:
2025-06-29 09:00:32,561 - INFO - 第 2 页获取到 100 条记录
2025-06-29 09:00:32,561 - INFO - Request Parameters - Page 3:
2025-06-29 09:00:32,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:32,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:33,092 - INFO - Response - Page 3:
2025-06-29 09:00:33,295 - INFO - 第 3 页获取到 100 条记录
2025-06-29 09:00:33,295 - INFO - Request Parameters - Page 4:
2025-06-29 09:00:33,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:33,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:33,717 - INFO - Response - Page 4:
2025-06-29 09:00:33,920 - INFO - 第 4 页获取到 100 条记录
2025-06-29 09:00:33,920 - INFO - Request Parameters - Page 5:
2025-06-29 09:00:33,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:33,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:34,436 - INFO - Response - Page 5:
2025-06-29 09:00:34,639 - INFO - 第 5 页获取到 100 条记录
2025-06-29 09:00:34,639 - INFO - Request Parameters - Page 6:
2025-06-29 09:00:34,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:34,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:35,123 - INFO - Response - Page 6:
2025-06-29 09:00:35,327 - INFO - 第 6 页获取到 100 条记录
2025-06-29 09:00:35,327 - INFO - Request Parameters - Page 7:
2025-06-29 09:00:35,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 09:00:35,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 09:00:35,670 - INFO - Response - Page 7:
2025-06-29 09:00:35,873 - INFO - 第 7 页获取到 29 条记录
2025-06-29 09:00:35,873 - INFO - 查询完成，共获取到 629 条记录
2025-06-29 09:00:35,873 - INFO - 获取到 629 条表单数据
2025-06-29 09:00:35,873 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-29 09:00:35,873 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-29 09:00:36,311 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-29 09:00:36,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420670.0, 'new_value': 435670.0}, {'field': 'total_amount', 'old_value': 442850.0, 'new_value': 457850.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 308}]
2025-06-29 09:00:36,311 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-29 09:00:36,780 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-29 09:00:36,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26939.64, 'new_value': 27561.64}, {'field': 'total_amount', 'old_value': 26939.64, 'new_value': 27561.64}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-06-29 09:00:36,780 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-29 09:00:37,264 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-29 09:00:37,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53480.0, 'new_value': 56860.0}, {'field': 'total_amount', 'old_value': 65420.0, 'new_value': 68800.0}, {'field': 'order_count', 'old_value': 726, 'new_value': 766}]
2025-06-29 09:00:37,264 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-29 09:00:37,655 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-29 09:00:37,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37048.4, 'new_value': 37816.4}, {'field': 'total_amount', 'old_value': 37048.4, 'new_value': 37816.4}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-29 09:00:37,655 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-29 09:00:38,155 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-29 09:00:38,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6738.0, 'new_value': 7532.0}, {'field': 'offline_amount', 'old_value': 56693.0, 'new_value': 60647.0}, {'field': 'total_amount', 'old_value': 63431.0, 'new_value': 68179.0}, {'field': 'order_count', 'old_value': 523, 'new_value': 594}]
2025-06-29 09:00:38,155 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-29 09:00:38,545 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-29 09:00:38,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123400.0, 'new_value': 128888.0}, {'field': 'total_amount', 'old_value': 123400.0, 'new_value': 128888.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-06-29 09:00:38,545 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-29 09:00:39,092 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-29 09:00:39,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9652.9, 'new_value': 11006.9}, {'field': 'offline_amount', 'old_value': 10379.0, 'new_value': 10387.0}, {'field': 'total_amount', 'old_value': 20031.9, 'new_value': 21393.9}, {'field': 'order_count', 'old_value': 68, 'new_value': 75}]
2025-06-29 09:00:39,092 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-29 09:00:39,545 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-29 09:00:39,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24676.01, 'new_value': 25599.01}, {'field': 'offline_amount', 'old_value': 3548.0, 'new_value': 3552.0}, {'field': 'total_amount', 'old_value': 28224.01, 'new_value': 29151.01}, {'field': 'order_count', 'old_value': 119, 'new_value': 124}]
2025-06-29 09:00:39,545 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-29 09:00:40,045 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-29 09:00:40,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91705.0, 'new_value': 97401.0}, {'field': 'total_amount', 'old_value': 91705.0, 'new_value': 97401.0}, {'field': 'order_count', 'old_value': 334, 'new_value': 366}]
2025-06-29 09:00:40,045 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-29 09:00:40,514 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-29 09:00:40,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44935.0, 'new_value': 46049.0}, {'field': 'total_amount', 'old_value': 44935.0, 'new_value': 46049.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 68}]
2025-06-29 09:00:40,514 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-29 09:00:41,014 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-29 09:00:41,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148830.0, 'new_value': 159856.0}, {'field': 'total_amount', 'old_value': 148830.0, 'new_value': 159856.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 269}]
2025-06-29 09:00:41,014 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-29 09:00:41,452 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-29 09:00:41,452 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8325.65, 'new_value': 8924.0}, {'field': 'offline_amount', 'old_value': 60587.0, 'new_value': 65114.0}, {'field': 'total_amount', 'old_value': 68912.65, 'new_value': 74038.0}, {'field': 'order_count', 'old_value': 1600, 'new_value': 1804}]
2025-06-29 09:00:41,452 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-29 09:00:41,905 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-29 09:00:41,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24848.0, 'new_value': 26065.0}, {'field': 'total_amount', 'old_value': 24848.0, 'new_value': 26065.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 131}]
2025-06-29 09:00:41,905 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-29 09:00:42,373 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-29 09:00:42,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368839.22, 'new_value': 390132.48}, {'field': 'total_amount', 'old_value': 368839.22, 'new_value': 390132.48}, {'field': 'order_count', 'old_value': 1888, 'new_value': 2000}]
2025-06-29 09:00:42,373 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-29 09:00:42,842 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-29 09:00:42,842 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64124.15, 'new_value': 67928.6}, {'field': 'offline_amount', 'old_value': 474098.51, 'new_value': 497700.36}, {'field': 'total_amount', 'old_value': 538222.66, 'new_value': 565628.96}, {'field': 'order_count', 'old_value': 5368, 'new_value': 5734}]
2025-06-29 09:00:42,842 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-29 09:00:43,264 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-29 09:00:43,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59579.0, 'new_value': 61257.0}, {'field': 'total_amount', 'old_value': 59579.0, 'new_value': 61257.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 99}]
2025-06-29 09:00:43,264 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-29 09:00:43,701 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-29 09:00:43,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42575.0, 'new_value': 66375.0}, {'field': 'total_amount', 'old_value': 42575.0, 'new_value': 66375.0}, {'field': 'order_count', 'old_value': 1388, 'new_value': 1393}]
2025-06-29 09:00:43,701 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUD
2025-06-29 09:00:44,061 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUD
2025-06-29 09:00:44,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98912.0, 'new_value': 138912.0}, {'field': 'total_amount', 'old_value': 98912.0, 'new_value': 138912.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-29 09:00:44,061 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-29 09:00:44,561 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-29 09:00:44,561 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57554.57, 'new_value': 59631.6}, {'field': 'offline_amount', 'old_value': 329990.3, 'new_value': 350841.67}, {'field': 'total_amount', 'old_value': 387544.87, 'new_value': 410473.27}, {'field': 'order_count', 'old_value': 3097, 'new_value': 3261}]
2025-06-29 09:00:44,561 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0E
2025-06-29 09:00:44,983 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0E
2025-06-29 09:00:44,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121705.0, 'new_value': 139702.0}, {'field': 'total_amount', 'old_value': 121705.0, 'new_value': 139702.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-06-29 09:00:44,983 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-29 09:00:45,451 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-29 09:00:45,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101801.2, 'new_value': 105315.2}, {'field': 'total_amount', 'old_value': 101801.2, 'new_value': 105315.2}, {'field': 'order_count', 'old_value': 884, 'new_value': 885}]
2025-06-29 09:00:45,451 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-29 09:00:45,920 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-29 09:00:45,920 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8969.5, 'new_value': 9681.3}, {'field': 'offline_amount', 'old_value': 76895.9, 'new_value': 77922.9}, {'field': 'total_amount', 'old_value': 85865.4, 'new_value': 87604.2}, {'field': 'order_count', 'old_value': 72, 'new_value': 76}]
2025-06-29 09:00:45,920 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-29 09:00:46,326 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-29 09:00:46,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73240.15, 'new_value': 78530.25}, {'field': 'total_amount', 'old_value': 125007.08, 'new_value': 130297.18}, {'field': 'order_count', 'old_value': 102, 'new_value': 111}]
2025-06-29 09:00:46,326 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-29 09:00:46,842 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-29 09:00:46,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294388.0, 'new_value': 315596.2}, {'field': 'total_amount', 'old_value': 294388.0, 'new_value': 315596.2}, {'field': 'order_count', 'old_value': 3028, 'new_value': 3275}]
2025-06-29 09:00:46,842 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-29 09:00:47,280 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-29 09:00:47,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 874815.0, 'new_value': 921795.0}, {'field': 'total_amount', 'old_value': 947407.0, 'new_value': 994387.0}, {'field': 'order_count', 'old_value': 1014, 'new_value': 1079}]
2025-06-29 09:00:47,280 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-29 09:00:47,748 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-29 09:00:47,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109575.76, 'new_value': 116277.76}, {'field': 'total_amount', 'old_value': 109575.76, 'new_value': 116277.76}, {'field': 'order_count', 'old_value': 9762, 'new_value': 10686}]
2025-06-29 09:00:47,748 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-29 09:00:48,139 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-29 09:00:48,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373508.0, 'new_value': 395828.0}, {'field': 'total_amount', 'old_value': 373508.0, 'new_value': 395828.0}]
2025-06-29 09:00:48,139 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-29 09:00:48,608 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-29 09:00:48,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111916.0, 'new_value': 119922.2}, {'field': 'total_amount', 'old_value': 111916.0, 'new_value': 119922.2}, {'field': 'order_count', 'old_value': 279, 'new_value': 299}]
2025-06-29 09:00:48,608 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-29 09:00:49,014 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-29 09:00:49,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 415540.63, 'new_value': 448326.19}, {'field': 'total_amount', 'old_value': 499756.97, 'new_value': 532542.53}, {'field': 'order_count', 'old_value': 1584, 'new_value': 1704}]
2025-06-29 09:00:49,014 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-29 09:00:49,498 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-29 09:00:49,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107352.95, 'new_value': 115233.85}, {'field': 'total_amount', 'old_value': 107352.95, 'new_value': 115233.85}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-29 09:00:49,498 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-29 09:00:49,998 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-29 09:00:49,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53271.4, 'new_value': 55847.4}, {'field': 'total_amount', 'old_value': 53271.4, 'new_value': 55847.4}, {'field': 'order_count', 'old_value': 325, 'new_value': 365}]
2025-06-29 09:00:49,998 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-29 09:00:50,420 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-29 09:00:50,420 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27177.59, 'new_value': 28685.63}, {'field': 'offline_amount', 'old_value': 260467.99, 'new_value': 282042.33}, {'field': 'total_amount', 'old_value': 287645.58, 'new_value': 310727.96}, {'field': 'order_count', 'old_value': 1402, 'new_value': 1521}]
2025-06-29 09:00:50,436 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-29 09:00:50,951 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-29 09:00:50,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 281367.53, 'new_value': 296251.01}, {'field': 'offline_amount', 'old_value': 84509.71, 'new_value': 88008.82}, {'field': 'total_amount', 'old_value': 365877.24, 'new_value': 384259.83}, {'field': 'order_count', 'old_value': 2304, 'new_value': 2393}]
2025-06-29 09:00:50,951 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-29 09:00:51,514 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-29 09:00:51,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38991.0, 'new_value': 41233.0}, {'field': 'total_amount', 'old_value': 39719.0, 'new_value': 41961.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 157}]
2025-06-29 09:00:51,514 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-29 09:00:51,998 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-29 09:00:51,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203520.0, 'new_value': 209915.9}, {'field': 'total_amount', 'old_value': 203520.0, 'new_value': 209915.9}, {'field': 'order_count', 'old_value': 37, 'new_value': 41}]
2025-06-29 09:00:51,998 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-29 09:00:52,467 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-29 09:00:52,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40627.3, 'new_value': 44511.9}, {'field': 'total_amount', 'old_value': 42034.3, 'new_value': 45918.9}, {'field': 'order_count', 'old_value': 136, 'new_value': 147}]
2025-06-29 09:00:52,467 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-29 09:00:52,873 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-29 09:00:52,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 700787.0, 'new_value': 755538.0}, {'field': 'total_amount', 'old_value': 700787.0, 'new_value': 755538.0}, {'field': 'order_count', 'old_value': 3475, 'new_value': 3634}]
2025-06-29 09:00:52,873 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-29 09:00:53,311 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-29 09:00:53,311 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121486.56, 'new_value': 128521.36}, {'field': 'offline_amount', 'old_value': 230617.3, 'new_value': 244779.97}, {'field': 'total_amount', 'old_value': 352103.86, 'new_value': 373301.33}, {'field': 'order_count', 'old_value': 5077, 'new_value': 5322}]
2025-06-29 09:00:53,311 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-29 09:00:53,764 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-29 09:00:53,764 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 258361.93, 'new_value': 278225.15}, {'field': 'total_amount', 'old_value': 344483.29, 'new_value': 364346.51}, {'field': 'order_count', 'old_value': 688, 'new_value': 725}]
2025-06-29 09:00:53,764 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-29 09:00:54,155 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-29 09:00:54,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6103.31, 'new_value': 6691.51}, {'field': 'offline_amount', 'old_value': 21540.0, 'new_value': 25340.0}, {'field': 'total_amount', 'old_value': 27643.31, 'new_value': 32031.51}, {'field': 'order_count', 'old_value': 93, 'new_value': 102}]
2025-06-29 09:00:54,155 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-29 09:00:54,639 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-29 09:00:54,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38981.8, 'new_value': 40077.49}, {'field': 'total_amount', 'old_value': 41193.04, 'new_value': 42288.73}, {'field': 'order_count', 'old_value': 172, 'new_value': 176}]
2025-06-29 09:00:54,639 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHK
2025-06-29 09:00:55,092 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHK
2025-06-29 09:00:55,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34192.0, 'new_value': 42190.0}, {'field': 'total_amount', 'old_value': 34192.0, 'new_value': 42190.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-29 09:00:55,092 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-29 09:00:55,530 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-29 09:00:55,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 556774.0, 'new_value': 601332.0}, {'field': 'total_amount', 'old_value': 556774.0, 'new_value': 601332.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 139}]
2025-06-29 09:00:55,530 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-29 09:00:55,998 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-29 09:00:55,998 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103024.7, 'new_value': 110622.6}, {'field': 'offline_amount', 'old_value': 59148.2, 'new_value': 64200.6}, {'field': 'total_amount', 'old_value': 162172.9, 'new_value': 174823.2}, {'field': 'order_count', 'old_value': 1071, 'new_value': 1153}]
2025-06-29 09:00:55,998 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-29 09:00:56,576 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-29 09:00:56,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129380.0, 'new_value': 136417.0}, {'field': 'total_amount', 'old_value': 129380.0, 'new_value': 136417.0}, {'field': 'order_count', 'old_value': 477, 'new_value': 502}]
2025-06-29 09:00:56,576 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-29 09:00:57,139 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-29 09:00:57,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25079.23, 'new_value': 26557.33}, {'field': 'total_amount', 'old_value': 25143.83, 'new_value': 26621.93}, {'field': 'order_count', 'old_value': 169, 'new_value': 181}]
2025-06-29 09:00:57,139 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2L
2025-06-29 09:00:57,592 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2L
2025-06-29 09:00:57,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214415.0, 'new_value': 256768.0}, {'field': 'total_amount', 'old_value': 214415.0, 'new_value': 256768.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-29 09:00:57,592 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-29 09:00:58,014 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6L
2025-06-29 09:00:58,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196521.0, 'new_value': 227137.0}, {'field': 'total_amount', 'old_value': 196521.0, 'new_value': 227137.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 48}]
2025-06-29 09:00:58,014 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-29 09:00:58,436 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-29 09:00:58,436 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 272629.13, 'new_value': 284185.92}, {'field': 'offline_amount', 'old_value': 715502.35, 'new_value': 756362.23}, {'field': 'total_amount', 'old_value': 988131.48, 'new_value': 1040548.15}, {'field': 'order_count', 'old_value': 6256, 'new_value': 6553}]
2025-06-29 09:00:58,436 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-29 09:00:58,998 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-29 09:00:58,998 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147363.57, 'new_value': 154489.65}, {'field': 'offline_amount', 'old_value': 320362.42, 'new_value': 341399.29}, {'field': 'total_amount', 'old_value': 467725.99, 'new_value': 495888.94}, {'field': 'order_count', 'old_value': 3947, 'new_value': 4212}]
2025-06-29 09:00:58,998 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-29 09:00:59,483 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-29 09:00:59,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54451.0, 'new_value': 54727.0}, {'field': 'total_amount', 'old_value': 54451.0, 'new_value': 54727.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-29 09:00:59,483 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-29 09:00:59,858 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-29 09:00:59,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59170.0, 'new_value': 80795.0}, {'field': 'total_amount', 'old_value': 59170.0, 'new_value': 80795.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-29 09:00:59,858 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-29 09:01:00,342 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-29 09:01:00,342 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41217.69, 'new_value': 43031.64}, {'field': 'offline_amount', 'old_value': 44508.11, 'new_value': 47574.33}, {'field': 'total_amount', 'old_value': 85725.8, 'new_value': 90605.97}, {'field': 'order_count', 'old_value': 7336, 'new_value': 7714}]
2025-06-29 09:01:00,342 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-29 09:01:00,842 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-29 09:01:00,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55180.0, 'new_value': 59380.0}, {'field': 'total_amount', 'old_value': 57792.0, 'new_value': 61992.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 145}]
2025-06-29 09:01:00,842 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-29 09:01:01,373 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-29 09:01:01,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59412.6, 'new_value': 62595.1}, {'field': 'total_amount', 'old_value': 60308.5, 'new_value': 63491.0}, {'field': 'order_count', 'old_value': 436, 'new_value': 467}]
2025-06-29 09:01:01,373 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-29 09:01:01,873 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-29 09:01:01,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 245943.52, 'new_value': 257198.52}, {'field': 'offline_amount', 'old_value': 228128.79, 'new_value': 250236.79}, {'field': 'total_amount', 'old_value': 474072.31, 'new_value': 507435.31}, {'field': 'order_count', 'old_value': 3158, 'new_value': 3338}]
2025-06-29 09:01:01,873 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-29 09:01:02,311 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-29 09:01:02,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1850000.0, 'new_value': 1950000.0}, {'field': 'total_amount', 'old_value': 1950000.0, 'new_value': 2050000.0}, {'field': 'order_count', 'old_value': 379, 'new_value': 380}]
2025-06-29 09:01:02,311 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-29 09:01:02,764 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-29 09:01:02,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144472.9, 'new_value': 157603.9}, {'field': 'total_amount', 'old_value': 144472.9, 'new_value': 157603.9}, {'field': 'order_count', 'old_value': 4587, 'new_value': 5013}]
2025-06-29 09:01:02,779 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-29 09:01:03,233 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-29 09:01:03,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258308.37, 'new_value': 274129.17}, {'field': 'total_amount', 'old_value': 258308.37, 'new_value': 274129.17}, {'field': 'order_count', 'old_value': 1641, 'new_value': 1757}]
2025-06-29 09:01:03,233 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-29 09:01:03,717 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-29 09:01:03,717 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 292928.95, 'new_value': 312112.76}, {'field': 'offline_amount', 'old_value': 1456068.96, 'new_value': 1533096.69}, {'field': 'total_amount', 'old_value': 1748997.91, 'new_value': 1845209.45}, {'field': 'order_count', 'old_value': 8433, 'new_value': 8857}]
2025-06-29 09:01:03,717 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-29 09:01:04,201 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-29 09:01:04,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53155.25, 'new_value': 55485.49}, {'field': 'offline_amount', 'old_value': 489717.66, 'new_value': 515530.07}, {'field': 'total_amount', 'old_value': 542872.91, 'new_value': 571015.56}, {'field': 'order_count', 'old_value': 2404, 'new_value': 2524}]
2025-06-29 09:01:04,201 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-29 09:01:04,686 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-29 09:01:04,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 585423.65, 'new_value': 642446.65}, {'field': 'total_amount', 'old_value': 585423.65, 'new_value': 642446.65}, {'field': 'order_count', 'old_value': 2259, 'new_value': 2383}]
2025-06-29 09:01:04,686 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-29 09:01:05,076 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-29 09:01:05,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329309.1, 'new_value': 366386.7}, {'field': 'total_amount', 'old_value': 329309.1, 'new_value': 366386.7}, {'field': 'order_count', 'old_value': 384, 'new_value': 420}]
2025-06-29 09:01:05,076 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-29 09:01:05,576 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-29 09:01:05,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131617.2, 'new_value': 153913.6}, {'field': 'offline_amount', 'old_value': 378777.2, 'new_value': 388777.2}, {'field': 'total_amount', 'old_value': 510394.4, 'new_value': 542690.8}, {'field': 'order_count', 'old_value': 3579, 'new_value': 4247}]
2025-06-29 09:01:05,576 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-29 09:01:06,029 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-29 09:01:06,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36412.25, 'new_value': 39934.85}, {'field': 'offline_amount', 'old_value': 30500.7, 'new_value': 32118.5}, {'field': 'total_amount', 'old_value': 66912.95, 'new_value': 72053.35}, {'field': 'order_count', 'old_value': 354, 'new_value': 373}]
2025-06-29 09:01:06,029 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-29 09:01:06,451 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-29 09:01:06,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1998530.0, 'new_value': 1670998.0}, {'field': 'total_amount', 'old_value': 1998530.0, 'new_value': 1670998.0}, {'field': 'order_count', 'old_value': 7274, 'new_value': 7723}]
2025-06-29 09:01:06,451 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-29 09:01:06,920 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-29 09:01:06,920 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340275.33, 'new_value': 361113.07}, {'field': 'total_amount', 'old_value': 340275.33, 'new_value': 361113.07}, {'field': 'order_count', 'old_value': 3290, 'new_value': 3475}]
2025-06-29 09:01:06,920 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-29 09:01:07,529 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-29 09:01:07,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284547.72, 'new_value': 297143.72}, {'field': 'total_amount', 'old_value': 298543.72, 'new_value': 311139.72}, {'field': 'order_count', 'old_value': 75, 'new_value': 78}]
2025-06-29 09:01:07,529 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-29 09:01:07,983 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-29 09:01:07,983 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138190.8, 'new_value': 144419.2}, {'field': 'offline_amount', 'old_value': 102416.7, 'new_value': 106581.4}, {'field': 'total_amount', 'old_value': 240607.5, 'new_value': 251000.6}, {'field': 'order_count', 'old_value': 5839, 'new_value': 6107}]
2025-06-29 09:01:07,983 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-29 09:01:08,498 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-29 09:01:08,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238480.0, 'new_value': 254800.0}, {'field': 'total_amount', 'old_value': 238480.0, 'new_value': 254800.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-06-29 09:01:08,498 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM511
2025-06-29 09:01:08,936 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM511
2025-06-29 09:01:08,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152652.0, 'new_value': 159452.0}, {'field': 'total_amount', 'old_value': 152652.0, 'new_value': 159452.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-29 09:01:08,936 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-29 09:01:09,264 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-29 09:01:09,279 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 209451.92, 'new_value': 224976.92}, {'field': 'offline_amount', 'old_value': 16536.3, 'new_value': 16684.3}, {'field': 'total_amount', 'old_value': 225988.22, 'new_value': 241661.22}, {'field': 'order_count', 'old_value': 15438, 'new_value': 16318}]
2025-06-29 09:01:09,279 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-29 09:01:09,733 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-29 09:01:09,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 394836.7, 'new_value': 405056.7}, {'field': 'total_amount', 'old_value': 394836.7, 'new_value': 405056.7}, {'field': 'order_count', 'old_value': 448, 'new_value': 478}]
2025-06-29 09:01:09,733 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-29 09:01:10,201 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-29 09:01:10,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77945.0, 'new_value': 81613.0}, {'field': 'total_amount', 'old_value': 77945.0, 'new_value': 81613.0}, {'field': 'order_count', 'old_value': 3437, 'new_value': 3606}]
2025-06-29 09:01:10,201 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-29 09:01:10,623 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-29 09:01:10,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68815.0, 'new_value': 70515.0}, {'field': 'offline_amount', 'old_value': 330633.0, 'new_value': 349172.0}, {'field': 'total_amount', 'old_value': 399448.0, 'new_value': 419687.0}, {'field': 'order_count', 'old_value': 1558, 'new_value': 1633}]
2025-06-29 09:01:10,639 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-29 09:01:11,045 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-29 09:01:11,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248502.26, 'new_value': 265117.1}, {'field': 'total_amount', 'old_value': 248502.26, 'new_value': 265117.1}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1203}]
2025-06-29 09:01:11,045 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-29 09:01:11,529 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-29 09:01:11,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 309449.0, 'new_value': 323038.0}, {'field': 'total_amount', 'old_value': 309449.0, 'new_value': 323038.0}, {'field': 'order_count', 'old_value': 393, 'new_value': 413}]
2025-06-29 09:01:11,529 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-29 09:01:11,983 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-29 09:01:11,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 864320.0, 'new_value': 1196961.0}, {'field': 'total_amount', 'old_value': 864320.0, 'new_value': 1196961.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 115}]
2025-06-29 09:01:11,983 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-29 09:01:12,358 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-29 09:01:12,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327393.0, 'new_value': 339243.0}, {'field': 'total_amount', 'old_value': 327393.0, 'new_value': 339243.0}, {'field': 'order_count', 'old_value': 7575, 'new_value': 7878}]
2025-06-29 09:01:12,358 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-29 09:01:12,779 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-29 09:01:12,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96384.0, 'new_value': 115030.0}, {'field': 'total_amount', 'old_value': 96384.0, 'new_value': 115030.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-06-29 09:01:12,779 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-29 09:01:13,201 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-29 09:01:13,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55121.8, 'new_value': 57498.7}, {'field': 'total_amount', 'old_value': 55121.8, 'new_value': 57498.7}, {'field': 'order_count', 'old_value': 277, 'new_value': 287}]
2025-06-29 09:01:13,217 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-29 09:01:13,654 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-29 09:01:13,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93000.0, 'new_value': 133000.0}, {'field': 'total_amount', 'old_value': 99000.0, 'new_value': 139000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-06-29 09:01:13,654 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-29 09:01:14,061 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-29 09:01:14,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128006.0, 'new_value': 131370.0}, {'field': 'total_amount', 'old_value': 128006.0, 'new_value': 131370.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-29 09:01:14,061 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-29 09:01:14,514 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-29 09:01:14,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 976821.96, 'new_value': 996050.07}, {'field': 'total_amount', 'old_value': 976821.96, 'new_value': 996050.07}, {'field': 'order_count', 'old_value': 4607, 'new_value': 4797}]
2025-06-29 09:01:14,529 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-29 09:01:15,014 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-29 09:01:15,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7077.75, 'new_value': 7599.95}, {'field': 'offline_amount', 'old_value': 42387.7, 'new_value': 46529.5}, {'field': 'total_amount', 'old_value': 49465.45, 'new_value': 54129.45}, {'field': 'order_count', 'old_value': 378, 'new_value': 403}]
2025-06-29 09:01:15,029 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-29 09:01:15,514 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-29 09:01:15,514 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113521.98, 'new_value': 137906.2}, {'field': 'total_amount', 'old_value': 316821.81, 'new_value': 341206.03}, {'field': 'order_count', 'old_value': 2559, 'new_value': 2713}]
2025-06-29 09:01:15,514 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-29 09:01:15,936 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-29 09:01:15,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65751.0, 'new_value': 68614.1}, {'field': 'total_amount', 'old_value': 65751.0, 'new_value': 68614.1}, {'field': 'order_count', 'old_value': 748, 'new_value': 833}]
2025-06-29 09:01:15,936 - INFO - 开始更新记录 - 表单实例ID: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECML9
2025-06-29 09:01:16,404 - INFO - 更新表单数据成功: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECML9
2025-06-29 09:01:16,404 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 69.0}, {'field': 'offline_amount', 'old_value': 2000.0, 'new_value': 2100.0}, {'field': 'total_amount', 'old_value': 2000.0, 'new_value': 2169.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-06-29 09:01:16,404 - INFO - 日期 2025-06 处理完成 - 更新: 88 条，插入: 0 条，错误: 0 条
2025-06-29 09:01:16,404 - INFO - 数据同步完成！更新: 88 条，插入: 0 条，错误: 0 条
2025-06-29 09:01:16,404 - INFO - =================同步完成====================
2025-06-29 12:00:03,254 - INFO - =================使用默认全量同步=============
2025-06-29 12:00:05,082 - INFO - MySQL查询成功，共获取 3964 条记录
2025-06-29 12:00:05,082 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 12:00:05,113 - INFO - 开始处理日期: 2025-01
2025-06-29 12:00:05,129 - INFO - Request Parameters - Page 1:
2025-06-29 12:00:05,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:05,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:06,363 - INFO - Response - Page 1:
2025-06-29 12:00:06,567 - INFO - 第 1 页获取到 100 条记录
2025-06-29 12:00:06,567 - INFO - Request Parameters - Page 2:
2025-06-29 12:00:06,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:06,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:07,363 - INFO - Response - Page 2:
2025-06-29 12:00:07,567 - INFO - 第 2 页获取到 100 条记录
2025-06-29 12:00:07,567 - INFO - Request Parameters - Page 3:
2025-06-29 12:00:07,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:07,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:08,145 - INFO - Response - Page 3:
2025-06-29 12:00:08,348 - INFO - 第 3 页获取到 100 条记录
2025-06-29 12:00:08,348 - INFO - Request Parameters - Page 4:
2025-06-29 12:00:08,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:08,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:08,879 - INFO - Response - Page 4:
2025-06-29 12:00:09,082 - INFO - 第 4 页获取到 100 条记录
2025-06-29 12:00:09,082 - INFO - Request Parameters - Page 5:
2025-06-29 12:00:09,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:09,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:09,738 - INFO - Response - Page 5:
2025-06-29 12:00:09,942 - INFO - 第 5 页获取到 100 条记录
2025-06-29 12:00:09,942 - INFO - Request Parameters - Page 6:
2025-06-29 12:00:09,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:09,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:10,488 - INFO - Response - Page 6:
2025-06-29 12:00:10,692 - INFO - 第 6 页获取到 100 条记录
2025-06-29 12:00:10,692 - INFO - Request Parameters - Page 7:
2025-06-29 12:00:10,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:10,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:11,238 - INFO - Response - Page 7:
2025-06-29 12:00:11,442 - INFO - 第 7 页获取到 82 条记录
2025-06-29 12:00:11,442 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 12:00:11,442 - INFO - 获取到 682 条表单数据
2025-06-29 12:00:11,442 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 12:00:11,457 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 12:00:11,457 - INFO - 开始处理日期: 2025-02
2025-06-29 12:00:11,457 - INFO - Request Parameters - Page 1:
2025-06-29 12:00:11,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:11,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:12,035 - INFO - Response - Page 1:
2025-06-29 12:00:12,238 - INFO - 第 1 页获取到 100 条记录
2025-06-29 12:00:12,238 - INFO - Request Parameters - Page 2:
2025-06-29 12:00:12,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:12,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:12,785 - INFO - Response - Page 2:
2025-06-29 12:00:12,988 - INFO - 第 2 页获取到 100 条记录
2025-06-29 12:00:12,988 - INFO - Request Parameters - Page 3:
2025-06-29 12:00:12,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:12,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:13,473 - INFO - Response - Page 3:
2025-06-29 12:00:13,676 - INFO - 第 3 页获取到 100 条记录
2025-06-29 12:00:13,676 - INFO - Request Parameters - Page 4:
2025-06-29 12:00:13,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:13,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:14,285 - INFO - Response - Page 4:
2025-06-29 12:00:14,488 - INFO - 第 4 页获取到 100 条记录
2025-06-29 12:00:14,488 - INFO - Request Parameters - Page 5:
2025-06-29 12:00:14,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:14,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:14,973 - INFO - Response - Page 5:
2025-06-29 12:00:15,176 - INFO - 第 5 页获取到 100 条记录
2025-06-29 12:00:15,176 - INFO - Request Parameters - Page 6:
2025-06-29 12:00:15,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:15,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:15,676 - INFO - Response - Page 6:
2025-06-29 12:00:15,879 - INFO - 第 6 页获取到 100 条记录
2025-06-29 12:00:15,879 - INFO - Request Parameters - Page 7:
2025-06-29 12:00:15,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:15,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:16,363 - INFO - Response - Page 7:
2025-06-29 12:00:16,566 - INFO - 第 7 页获取到 70 条记录
2025-06-29 12:00:16,566 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 12:00:16,566 - INFO - 获取到 670 条表单数据
2025-06-29 12:00:16,566 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 12:00:16,582 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 12:00:16,582 - INFO - 开始处理日期: 2025-03
2025-06-29 12:00:16,582 - INFO - Request Parameters - Page 1:
2025-06-29 12:00:16,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:16,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:17,207 - INFO - Response - Page 1:
2025-06-29 12:00:17,410 - INFO - 第 1 页获取到 100 条记录
2025-06-29 12:00:17,410 - INFO - Request Parameters - Page 2:
2025-06-29 12:00:17,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:17,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:17,879 - INFO - Response - Page 2:
2025-06-29 12:00:18,082 - INFO - 第 2 页获取到 100 条记录
2025-06-29 12:00:18,082 - INFO - Request Parameters - Page 3:
2025-06-29 12:00:18,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:18,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:18,582 - INFO - Response - Page 3:
2025-06-29 12:00:18,785 - INFO - 第 3 页获取到 100 条记录
2025-06-29 12:00:18,785 - INFO - Request Parameters - Page 4:
2025-06-29 12:00:18,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:18,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:19,301 - INFO - Response - Page 4:
2025-06-29 12:00:19,504 - INFO - 第 4 页获取到 100 条记录
2025-06-29 12:00:19,504 - INFO - Request Parameters - Page 5:
2025-06-29 12:00:19,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:19,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:20,004 - INFO - Response - Page 5:
2025-06-29 12:00:20,207 - INFO - 第 5 页获取到 100 条记录
2025-06-29 12:00:20,207 - INFO - Request Parameters - Page 6:
2025-06-29 12:00:20,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:20,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:20,691 - INFO - Response - Page 6:
2025-06-29 12:00:20,895 - INFO - 第 6 页获取到 100 条记录
2025-06-29 12:00:20,895 - INFO - Request Parameters - Page 7:
2025-06-29 12:00:20,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:20,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:21,348 - INFO - Response - Page 7:
2025-06-29 12:00:21,551 - INFO - 第 7 页获取到 61 条记录
2025-06-29 12:00:21,551 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 12:00:21,551 - INFO - 获取到 661 条表单数据
2025-06-29 12:00:21,551 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 12:00:21,566 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 12:00:21,566 - INFO - 开始处理日期: 2025-04
2025-06-29 12:00:21,566 - INFO - Request Parameters - Page 1:
2025-06-29 12:00:21,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:21,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:22,129 - INFO - Response - Page 1:
2025-06-29 12:00:22,332 - INFO - 第 1 页获取到 100 条记录
2025-06-29 12:00:22,332 - INFO - Request Parameters - Page 2:
2025-06-29 12:00:22,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:22,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:22,895 - INFO - Response - Page 2:
2025-06-29 12:00:23,098 - INFO - 第 2 页获取到 100 条记录
2025-06-29 12:00:23,098 - INFO - Request Parameters - Page 3:
2025-06-29 12:00:23,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:23,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:23,598 - INFO - Response - Page 3:
2025-06-29 12:00:23,801 - INFO - 第 3 页获取到 100 条记录
2025-06-29 12:00:23,801 - INFO - Request Parameters - Page 4:
2025-06-29 12:00:23,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:23,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:24,316 - INFO - Response - Page 4:
2025-06-29 12:00:24,520 - INFO - 第 4 页获取到 100 条记录
2025-06-29 12:00:24,520 - INFO - Request Parameters - Page 5:
2025-06-29 12:00:24,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:24,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:25,004 - INFO - Response - Page 5:
2025-06-29 12:00:25,207 - INFO - 第 5 页获取到 100 条记录
2025-06-29 12:00:25,207 - INFO - Request Parameters - Page 6:
2025-06-29 12:00:25,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:25,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:25,770 - INFO - Response - Page 6:
2025-06-29 12:00:25,973 - INFO - 第 6 页获取到 100 条记录
2025-06-29 12:00:25,973 - INFO - Request Parameters - Page 7:
2025-06-29 12:00:25,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:25,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:26,488 - INFO - Response - Page 7:
2025-06-29 12:00:26,691 - INFO - 第 7 页获取到 56 条记录
2025-06-29 12:00:26,691 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 12:00:26,691 - INFO - 获取到 656 条表单数据
2025-06-29 12:00:26,691 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 12:00:26,707 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 12:00:26,707 - INFO - 开始处理日期: 2025-05
2025-06-29 12:00:26,707 - INFO - Request Parameters - Page 1:
2025-06-29 12:00:26,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:26,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:27,270 - INFO - Response - Page 1:
2025-06-29 12:00:27,473 - INFO - 第 1 页获取到 100 条记录
2025-06-29 12:00:27,473 - INFO - Request Parameters - Page 2:
2025-06-29 12:00:27,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:27,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:28,051 - INFO - Response - Page 2:
2025-06-29 12:00:28,254 - INFO - 第 2 页获取到 100 条记录
2025-06-29 12:00:28,254 - INFO - Request Parameters - Page 3:
2025-06-29 12:00:28,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:28,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:28,816 - INFO - Response - Page 3:
2025-06-29 12:00:29,020 - INFO - 第 3 页获取到 100 条记录
2025-06-29 12:00:29,020 - INFO - Request Parameters - Page 4:
2025-06-29 12:00:29,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:29,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:29,566 - INFO - Response - Page 4:
2025-06-29 12:00:29,770 - INFO - 第 4 页获取到 100 条记录
2025-06-29 12:00:29,770 - INFO - Request Parameters - Page 5:
2025-06-29 12:00:29,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:29,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:30,254 - INFO - Response - Page 5:
2025-06-29 12:00:30,457 - INFO - 第 5 页获取到 100 条记录
2025-06-29 12:00:30,457 - INFO - Request Parameters - Page 6:
2025-06-29 12:00:30,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:30,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:30,941 - INFO - Response - Page 6:
2025-06-29 12:00:31,145 - INFO - 第 6 页获取到 100 条记录
2025-06-29 12:00:31,145 - INFO - Request Parameters - Page 7:
2025-06-29 12:00:31,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:31,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:31,613 - INFO - Response - Page 7:
2025-06-29 12:00:31,816 - INFO - 第 7 页获取到 65 条记录
2025-06-29 12:00:31,816 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 12:00:31,816 - INFO - 获取到 665 条表单数据
2025-06-29 12:00:31,816 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 12:00:31,832 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 12:00:31,832 - INFO - 开始处理日期: 2025-06
2025-06-29 12:00:31,832 - INFO - Request Parameters - Page 1:
2025-06-29 12:00:31,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:31,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:32,395 - INFO - Response - Page 1:
2025-06-29 12:00:32,598 - INFO - 第 1 页获取到 100 条记录
2025-06-29 12:00:32,598 - INFO - Request Parameters - Page 2:
2025-06-29 12:00:32,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:32,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:33,082 - INFO - Response - Page 2:
2025-06-29 12:00:33,285 - INFO - 第 2 页获取到 100 条记录
2025-06-29 12:00:33,285 - INFO - Request Parameters - Page 3:
2025-06-29 12:00:33,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:33,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:33,863 - INFO - Response - Page 3:
2025-06-29 12:00:34,066 - INFO - 第 3 页获取到 100 条记录
2025-06-29 12:00:34,066 - INFO - Request Parameters - Page 4:
2025-06-29 12:00:34,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:34,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:34,535 - INFO - Response - Page 4:
2025-06-29 12:00:34,738 - INFO - 第 4 页获取到 100 条记录
2025-06-29 12:00:34,738 - INFO - Request Parameters - Page 5:
2025-06-29 12:00:34,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:34,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:35,223 - INFO - Response - Page 5:
2025-06-29 12:00:35,426 - INFO - 第 5 页获取到 100 条记录
2025-06-29 12:00:35,426 - INFO - Request Parameters - Page 6:
2025-06-29 12:00:35,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:35,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:35,926 - INFO - Response - Page 6:
2025-06-29 12:00:36,129 - INFO - 第 6 页获取到 100 条记录
2025-06-29 12:00:36,129 - INFO - Request Parameters - Page 7:
2025-06-29 12:00:36,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 12:00:36,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 12:00:36,691 - INFO - Response - Page 7:
2025-06-29 12:00:36,894 - INFO - 第 7 页获取到 29 条记录
2025-06-29 12:00:36,894 - INFO - 查询完成，共获取到 629 条记录
2025-06-29 12:00:36,894 - INFO - 获取到 629 条表单数据
2025-06-29 12:00:36,894 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-06-29 12:00:36,894 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-29 12:00:37,457 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-29 12:00:37,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 771692.0, 'new_value': 809478.0}, {'field': 'total_amount', 'old_value': 771692.0, 'new_value': 809478.0}, {'field': 'order_count', 'old_value': 5469, 'new_value': 5668}]
2025-06-29 12:00:37,457 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-29 12:00:37,973 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-29 12:00:37,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186960.0, 'new_value': 161960.0}, {'field': 'total_amount', 'old_value': 186960.0, 'new_value': 161960.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 21}]
2025-06-29 12:00:37,973 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-29 12:00:38,410 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-29 12:00:38,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92270.54, 'new_value': 96818.56}, {'field': 'offline_amount', 'old_value': 1000639.91, 'new_value': 1035051.88}, {'field': 'total_amount', 'old_value': 1088917.96, 'new_value': 1127877.95}, {'field': 'order_count', 'old_value': 5309, 'new_value': 5520}]
2025-06-29 12:00:38,410 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-29 12:00:38,910 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-29 12:00:38,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241400.0, 'new_value': 272600.0}, {'field': 'total_amount', 'old_value': 241400.0, 'new_value': 272600.0}]
2025-06-29 12:00:38,926 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-29 12:00:39,426 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-29 12:00:39,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122229.2, 'new_value': 131691.0}, {'field': 'total_amount', 'old_value': 122229.2, 'new_value': 131691.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-06-29 12:00:39,426 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-29 12:00:39,879 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-29 12:00:39,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29092.86, 'new_value': 29697.16}, {'field': 'offline_amount', 'old_value': 16234.62, 'new_value': 16665.32}, {'field': 'total_amount', 'old_value': 45327.48, 'new_value': 46362.48}, {'field': 'order_count', 'old_value': 1887, 'new_value': 1930}]
2025-06-29 12:00:39,879 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-29 12:00:40,301 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-29 12:00:40,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 401588.0, 'new_value': 439834.0}, {'field': 'offline_amount', 'old_value': 161083.0, 'new_value': 169886.0}, {'field': 'total_amount', 'old_value': 562671.0, 'new_value': 609720.0}, {'field': 'order_count', 'old_value': 633, 'new_value': 679}]
2025-06-29 12:00:40,301 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMVX
2025-06-29 12:00:40,738 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMVX
2025-06-29 12:00:40,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61880.5, 'new_value': 70910.5}, {'field': 'total_amount', 'old_value': 103880.5, 'new_value': 112910.5}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-06-29 12:00:40,738 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-29 12:00:41,144 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-29 12:00:41,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204240.0, 'new_value': 215500.0}, {'field': 'total_amount', 'old_value': 204240.0, 'new_value': 215500.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-29 12:00:41,144 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-29 12:00:41,551 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-29 12:00:41,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120397.0, 'new_value': 140697.0}, {'field': 'total_amount', 'old_value': 120397.0, 'new_value': 140697.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 20}]
2025-06-29 12:00:41,551 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-29 12:00:41,988 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-29 12:00:41,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8014.0, 'new_value': 8549.0}, {'field': 'offline_amount', 'old_value': 104736.0, 'new_value': 113168.0}, {'field': 'total_amount', 'old_value': 112750.0, 'new_value': 121717.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 136}]
2025-06-29 12:00:41,988 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-29 12:00:42,441 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-29 12:00:42,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35206.5, 'new_value': 36253.5}, {'field': 'total_amount', 'old_value': 35206.5, 'new_value': 36253.5}, {'field': 'order_count', 'old_value': 7344, 'new_value': 7561}]
2025-06-29 12:00:42,441 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-29 12:00:42,910 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-29 12:00:42,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188720.0, 'new_value': 189400.0}, {'field': 'total_amount', 'old_value': 188720.0, 'new_value': 189400.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-06-29 12:00:42,910 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBY
2025-06-29 12:00:43,426 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBY
2025-06-29 12:00:43,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1724300.0, 'new_value': 1934200.0}, {'field': 'total_amount', 'old_value': 1724300.0, 'new_value': 1934200.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-29 12:00:43,426 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-29 12:00:43,879 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-29 12:00:43,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98439.29, 'new_value': 114750.84}, {'field': 'total_amount', 'old_value': 119240.95, 'new_value': 135552.5}, {'field': 'order_count', 'old_value': 3037, 'new_value': 3438}]
2025-06-29 12:00:43,879 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-29 12:00:44,332 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-29 12:00:44,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280543.3, 'new_value': 304293.9}, {'field': 'total_amount', 'old_value': 280543.3, 'new_value': 304293.9}, {'field': 'order_count', 'old_value': 4148, 'new_value': 4431}]
2025-06-29 12:00:44,332 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-29 12:00:44,801 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-29 12:00:44,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24803.0, 'new_value': 32083.0}, {'field': 'total_amount', 'old_value': 35403.0, 'new_value': 42683.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-29 12:00:44,801 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-29 12:00:45,254 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-29 12:00:45,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137181.59, 'new_value': 144290.59}, {'field': 'total_amount', 'old_value': 137181.59, 'new_value': 144290.59}, {'field': 'order_count', 'old_value': 656, 'new_value': 685}]
2025-06-29 12:00:45,254 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-29 12:00:45,738 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-29 12:00:45,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6766.3, 'new_value': 7332.3}, {'field': 'offline_amount', 'old_value': 53946.47, 'new_value': 56280.67}, {'field': 'total_amount', 'old_value': 60712.77, 'new_value': 63612.97}, {'field': 'order_count', 'old_value': 536, 'new_value': 566}]
2025-06-29 12:00:45,738 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-29 12:00:46,207 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-29 12:00:46,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59380.0, 'new_value': 64199.0}, {'field': 'offline_amount', 'old_value': 193963.0, 'new_value': 202833.0}, {'field': 'total_amount', 'old_value': 253343.0, 'new_value': 267032.0}, {'field': 'order_count', 'old_value': 1820, 'new_value': 1920}]
2025-06-29 12:00:46,207 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-29 12:00:46,676 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-29 12:00:46,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55988.9, 'new_value': 58141.3}, {'field': 'offline_amount', 'old_value': 128099.77, 'new_value': 133098.17}, {'field': 'total_amount', 'old_value': 184088.67, 'new_value': 191239.47}, {'field': 'order_count', 'old_value': 2055, 'new_value': 2139}]
2025-06-29 12:00:46,676 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-29 12:00:47,144 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-29 12:00:47,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37152.0, 'new_value': 39317.0}, {'field': 'offline_amount', 'old_value': 60326.83, 'new_value': 62055.83}, {'field': 'total_amount', 'old_value': 97478.83, 'new_value': 101372.83}, {'field': 'order_count', 'old_value': 133, 'new_value': 138}]
2025-06-29 12:00:47,144 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-29 12:00:47,629 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-29 12:00:47,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67896.0, 'new_value': 70649.0}, {'field': 'total_amount', 'old_value': 67896.0, 'new_value': 70649.0}, {'field': 'order_count', 'old_value': 1291, 'new_value': 1335}]
2025-06-29 12:00:47,629 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-29 12:00:48,066 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-29 12:00:48,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38175.67, 'new_value': 39174.03}, {'field': 'offline_amount', 'old_value': 113591.54, 'new_value': 116855.04}, {'field': 'total_amount', 'old_value': 151767.21, 'new_value': 156029.07}, {'field': 'order_count', 'old_value': 2177, 'new_value': 2239}]
2025-06-29 12:00:48,066 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-29 12:00:48,551 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-29 12:00:48,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155838.0, 'new_value': 169338.0}, {'field': 'offline_amount', 'old_value': 53747.15, 'new_value': 57160.45}, {'field': 'total_amount', 'old_value': 209585.15, 'new_value': 226498.45}, {'field': 'order_count', 'old_value': 1460, 'new_value': 1561}]
2025-06-29 12:00:48,551 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-29 12:00:49,066 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-29 12:00:49,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4192.0, 'new_value': 4478.0}, {'field': 'offline_amount', 'old_value': 6705.0, 'new_value': 8019.0}, {'field': 'total_amount', 'old_value': 10897.0, 'new_value': 12497.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 212}]
2025-06-29 12:00:49,066 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-29 12:00:49,488 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-29 12:00:49,488 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241395.0, 'new_value': 251868.0}, {'field': 'total_amount', 'old_value': 241395.0, 'new_value': 251868.0}, {'field': 'order_count', 'old_value': 1309, 'new_value': 1374}]
2025-06-29 12:00:49,488 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-29 12:00:49,973 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-29 12:00:49,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153458.0, 'new_value': 162958.0}, {'field': 'total_amount', 'old_value': 168229.08, 'new_value': 177729.08}, {'field': 'order_count', 'old_value': 2713, 'new_value': 2843}]
2025-06-29 12:00:49,973 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-29 12:00:50,426 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-29 12:00:50,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127897.95, 'new_value': 130315.61}, {'field': 'total_amount', 'old_value': 127897.95, 'new_value': 130315.61}, {'field': 'order_count', 'old_value': 261, 'new_value': 268}]
2025-06-29 12:00:50,426 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-29 12:00:50,910 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-29 12:00:50,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169848.08, 'new_value': 184578.16}, {'field': 'total_amount', 'old_value': 169848.08, 'new_value': 184578.16}, {'field': 'order_count', 'old_value': 1111, 'new_value': 1186}]
2025-06-29 12:00:50,910 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-29 12:00:51,394 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-29 12:00:51,394 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96662.0, 'new_value': 171862.0}, {'field': 'offline_amount', 'old_value': 450412.6, 'new_value': 515178.6}, {'field': 'total_amount', 'old_value': 547074.6, 'new_value': 687040.6}, {'field': 'order_count', 'old_value': 91, 'new_value': 110}]
2025-06-29 12:00:51,394 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-29 12:00:51,832 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-29 12:00:51,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26536.95, 'new_value': 28404.19}, {'field': 'offline_amount', 'old_value': 30054.61, 'new_value': 32783.41}, {'field': 'total_amount', 'old_value': 56591.56, 'new_value': 61187.6}, {'field': 'order_count', 'old_value': 2888, 'new_value': 3133}]
2025-06-29 12:00:51,832 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Z
2025-06-29 12:00:52,285 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Z
2025-06-29 12:00:52,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8399.0, 'new_value': 13487.0}, {'field': 'total_amount', 'old_value': 8399.0, 'new_value': 13487.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-29 12:00:52,285 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-29 12:00:52,769 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-29 12:00:52,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 584464.36, 'new_value': 621113.33}, {'field': 'total_amount', 'old_value': 584464.36, 'new_value': 621113.33}, {'field': 'order_count', 'old_value': 4342, 'new_value': 4615}]
2025-06-29 12:00:52,769 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-29 12:00:53,254 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-29 12:00:53,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2349.9, 'new_value': 2417.9}, {'field': 'offline_amount', 'old_value': 11454.5, 'new_value': 11956.5}, {'field': 'total_amount', 'old_value': 13804.4, 'new_value': 14374.4}, {'field': 'order_count', 'old_value': 143, 'new_value': 149}]
2025-06-29 12:00:53,254 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-29 12:00:53,769 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-29 12:00:53,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 966376.6, 'new_value': 1100358.0}, {'field': 'total_amount', 'old_value': 1050732.9, 'new_value': 1184714.3}, {'field': 'order_count', 'old_value': 95, 'new_value': 105}]
2025-06-29 12:00:53,785 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-29 12:00:54,285 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-29 12:00:54,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128141.5, 'new_value': 204077.5}, {'field': 'total_amount', 'old_value': 128141.5, 'new_value': 204077.5}, {'field': 'order_count', 'old_value': 59, 'new_value': 63}]
2025-06-29 12:00:54,285 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-29 12:00:54,754 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-29 12:00:54,754 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11679.0, 'new_value': 11930.0}, {'field': 'total_amount', 'old_value': 33898.0, 'new_value': 34149.0}, {'field': 'order_count', 'old_value': 5244, 'new_value': 5246}]
2025-06-29 12:00:54,754 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-29 12:00:55,363 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-29 12:00:55,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189380.0, 'new_value': 198414.0}, {'field': 'total_amount', 'old_value': 189380.0, 'new_value': 198414.0}, {'field': 'order_count', 'old_value': 337, 'new_value': 355}]
2025-06-29 12:00:55,363 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-29 12:00:55,847 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-29 12:00:55,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10923.72, 'new_value': 11101.12}, {'field': 'offline_amount', 'old_value': 167996.58, 'new_value': 177498.31}, {'field': 'total_amount', 'old_value': 178920.3, 'new_value': 188599.43}, {'field': 'order_count', 'old_value': 2029, 'new_value': 2192}]
2025-06-29 12:00:55,847 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-29 12:00:56,379 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-29 12:00:56,379 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6766.4, 'new_value': 16611.4}, {'field': 'total_amount', 'old_value': 87549.4, 'new_value': 97394.4}, {'field': 'order_count', 'old_value': 80, 'new_value': 85}]
2025-06-29 12:00:56,379 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-29 12:00:56,863 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-29 12:00:56,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11848.1, 'new_value': 15483.2}, {'field': 'total_amount', 'old_value': 11848.1, 'new_value': 15483.2}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-06-29 12:00:56,863 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-29 12:00:57,332 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-29 12:00:57,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154799.92, 'new_value': 167348.63}, {'field': 'total_amount', 'old_value': 154799.92, 'new_value': 167348.63}, {'field': 'order_count', 'old_value': 785, 'new_value': 856}]
2025-06-29 12:00:57,332 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-29 12:00:57,832 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-29 12:00:57,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30932.0, 'new_value': 35027.0}, {'field': 'total_amount', 'old_value': 30932.0, 'new_value': 35027.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 154}]
2025-06-29 12:00:57,832 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-29 12:00:58,347 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-29 12:00:58,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1504.98, 'new_value': 1572.26}, {'field': 'offline_amount', 'old_value': 112590.28, 'new_value': 118925.9}, {'field': 'total_amount', 'old_value': 114095.26, 'new_value': 120498.16}, {'field': 'order_count', 'old_value': 2645, 'new_value': 2807}]
2025-06-29 12:00:58,347 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-29 12:00:58,847 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-29 12:00:58,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117245.03, 'new_value': 119276.23}, {'field': 'offline_amount', 'old_value': 1228248.07, 'new_value': 1278041.53}, {'field': 'total_amount', 'old_value': 1345493.1, 'new_value': 1397317.76}, {'field': 'order_count', 'old_value': 11426, 'new_value': 11854}]
2025-06-29 12:00:58,847 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-29 12:00:59,347 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-29 12:00:59,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201817.0, 'new_value': 209792.0}, {'field': 'total_amount', 'old_value': 201817.0, 'new_value': 209792.0}, {'field': 'order_count', 'old_value': 7786, 'new_value': 8067}]
2025-06-29 12:00:59,347 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-29 12:00:59,847 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-29 12:00:59,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163327.14, 'new_value': 172880.94}, {'field': 'total_amount', 'old_value': 163327.14, 'new_value': 172880.94}, {'field': 'order_count', 'old_value': 652, 'new_value': 688}]
2025-06-29 12:00:59,847 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-29 12:01:00,301 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-29 12:01:00,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136873.5, 'new_value': 145550.5}, {'field': 'offline_amount', 'old_value': 53996.06, 'new_value': 56965.86}, {'field': 'total_amount', 'old_value': 190869.56, 'new_value': 202516.36}, {'field': 'order_count', 'old_value': 1372, 'new_value': 1455}]
2025-06-29 12:01:00,301 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-29 12:01:00,785 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-29 12:01:00,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254711.0, 'new_value': 367742.0}, {'field': 'total_amount', 'old_value': 267399.0, 'new_value': 380430.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 79}]
2025-06-29 12:01:00,785 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-29 12:01:01,238 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-29 12:01:01,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 516.9, 'new_value': 2480.9}, {'field': 'offline_amount', 'old_value': 24818.0, 'new_value': 26618.0}, {'field': 'total_amount', 'old_value': 25334.9, 'new_value': 29098.9}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-06-29 12:01:01,238 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-29 12:01:01,676 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-29 12:01:01,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62290.0, 'new_value': 64512.0}, {'field': 'offline_amount', 'old_value': 196959.0, 'new_value': 201196.0}, {'field': 'total_amount', 'old_value': 259249.0, 'new_value': 265708.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 206}]
2025-06-29 12:01:01,676 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-29 12:01:02,082 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-29 12:01:02,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18821.55, 'new_value': 19553.65}, {'field': 'offline_amount', 'old_value': 419447.13, 'new_value': 452383.48}, {'field': 'total_amount', 'old_value': 438268.68, 'new_value': 471937.13}, {'field': 'order_count', 'old_value': 2082, 'new_value': 2219}]
2025-06-29 12:01:02,082 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-29 12:01:02,582 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-29 12:01:02,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1555769.57, 'new_value': 1613371.51}, {'field': 'total_amount', 'old_value': 1555769.57, 'new_value': 1613371.51}, {'field': 'order_count', 'old_value': 17282, 'new_value': 17878}]
2025-06-29 12:01:02,582 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-29 12:01:02,972 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-29 12:01:02,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88877.73, 'new_value': 91483.97}, {'field': 'total_amount', 'old_value': 88877.73, 'new_value': 91483.97}, {'field': 'order_count', 'old_value': 4663, 'new_value': 4798}]
2025-06-29 12:01:02,972 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-29 12:01:03,441 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-29 12:01:03,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168121.63, 'new_value': 176206.52}, {'field': 'total_amount', 'old_value': 168121.63, 'new_value': 176206.52}, {'field': 'order_count', 'old_value': 4411, 'new_value': 4620}]
2025-06-29 12:01:03,441 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-29 12:01:03,910 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-29 12:01:03,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 565140.01, 'new_value': 574940.01}, {'field': 'total_amount', 'old_value': 658140.01, 'new_value': 667940.01}, {'field': 'order_count', 'old_value': 71, 'new_value': 75}]
2025-06-29 12:01:03,910 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-29 12:01:04,410 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-29 12:01:04,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137350.99, 'new_value': 143654.94}, {'field': 'total_amount', 'old_value': 137350.99, 'new_value': 143654.94}, {'field': 'order_count', 'old_value': 1724, 'new_value': 1798}]
2025-06-29 12:01:04,410 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-29 12:01:04,926 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-29 12:01:04,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83699.0, 'new_value': 88087.0}, {'field': 'total_amount', 'old_value': 83699.0, 'new_value': 88087.0}, {'field': 'order_count', 'old_value': 4950, 'new_value': 5223}]
2025-06-29 12:01:04,926 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-29 12:01:05,394 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-29 12:01:05,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70282.51, 'new_value': 73863.33}, {'field': 'total_amount', 'old_value': 70282.51, 'new_value': 73863.33}, {'field': 'order_count', 'old_value': 306, 'new_value': 321}]
2025-06-29 12:01:05,394 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-29 12:01:05,879 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-29 12:01:05,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13981.29, 'new_value': 14562.29}, {'field': 'offline_amount', 'old_value': 26267.83, 'new_value': 27917.83}, {'field': 'total_amount', 'old_value': 40249.12, 'new_value': 42480.12}, {'field': 'order_count', 'old_value': 1375, 'new_value': 1470}]
2025-06-29 12:01:05,879 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-29 12:01:06,457 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-29 12:01:06,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210620.87, 'new_value': 221091.36}, {'field': 'total_amount', 'old_value': 221867.86, 'new_value': 232338.35}, {'field': 'order_count', 'old_value': 1266, 'new_value': 1316}]
2025-06-29 12:01:06,457 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-29 12:01:06,926 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-29 12:01:06,926 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 214120.75, 'new_value': 222573.91}, {'field': 'offline_amount', 'old_value': 398487.28, 'new_value': 423175.0}, {'field': 'total_amount', 'old_value': 612608.03, 'new_value': 645748.91}, {'field': 'order_count', 'old_value': 4739, 'new_value': 4854}]
2025-06-29 12:01:06,926 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-29 12:01:07,363 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-29 12:01:07,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45352.0, 'new_value': 45551.0}, {'field': 'total_amount', 'old_value': 45352.0, 'new_value': 45551.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-06-29 12:01:07,363 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-29 12:01:07,832 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-29 12:01:07,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 418337.8, 'new_value': 440080.6}, {'field': 'offline_amount', 'old_value': 87001.0, 'new_value': 90073.0}, {'field': 'total_amount', 'old_value': 505338.8, 'new_value': 530153.6}, {'field': 'order_count', 'old_value': 636, 'new_value': 663}]
2025-06-29 12:01:07,832 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-29 12:01:08,332 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-29 12:01:08,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32508.16, 'new_value': 40453.16}, {'field': 'total_amount', 'old_value': 32508.16, 'new_value': 40453.16}, {'field': 'order_count', 'old_value': 1181, 'new_value': 1245}]
2025-06-29 12:01:08,332 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-29 12:01:08,816 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-29 12:01:08,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 541508.24, 'new_value': 578594.4}, {'field': 'total_amount', 'old_value': 541508.24, 'new_value': 578594.4}, {'field': 'order_count', 'old_value': 4152, 'new_value': 4385}]
2025-06-29 12:01:08,816 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-29 12:01:09,301 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-29 12:01:09,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110436.14, 'new_value': 116552.14}, {'field': 'offline_amount', 'old_value': 539479.3, 'new_value': 568014.7}, {'field': 'total_amount', 'old_value': 649915.44, 'new_value': 684566.84}, {'field': 'order_count', 'old_value': 1812, 'new_value': 1893}]
2025-06-29 12:01:09,301 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-29 12:01:09,722 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-29 12:01:09,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114296.3, 'new_value': 118120.04}, {'field': 'total_amount', 'old_value': 114296.3, 'new_value': 118120.04}, {'field': 'order_count', 'old_value': 3224, 'new_value': 3337}]
2025-06-29 12:01:09,722 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-29 12:01:10,129 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-29 12:01:10,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48150.4, 'new_value': 51784.52}, {'field': 'offline_amount', 'old_value': 736582.15, 'new_value': 770442.37}, {'field': 'total_amount', 'old_value': 784732.55, 'new_value': 822226.89}, {'field': 'order_count', 'old_value': 3137, 'new_value': 3309}]
2025-06-29 12:01:10,129 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-29 12:01:10,551 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-29 12:01:10,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206202.71, 'new_value': 390501.71}, {'field': 'total_amount', 'old_value': 206601.71, 'new_value': 390900.71}, {'field': 'order_count', 'old_value': 77, 'new_value': 82}]
2025-06-29 12:01:10,551 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-29 12:01:10,957 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-29 12:01:10,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15614.2, 'new_value': 15994.1}, {'field': 'offline_amount', 'old_value': 36014.7, 'new_value': 41701.7}, {'field': 'total_amount', 'old_value': 51628.9, 'new_value': 57695.8}, {'field': 'order_count', 'old_value': 183, 'new_value': 195}]
2025-06-29 12:01:10,957 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-29 12:01:11,441 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-29 12:01:11,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61848.92, 'new_value': 71939.57}, {'field': 'total_amount', 'old_value': 68630.17, 'new_value': 78720.82}, {'field': 'order_count', 'old_value': 242, 'new_value': 258}]
2025-06-29 12:01:11,441 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-29 12:01:11,988 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-29 12:01:11,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141919.0, 'new_value': 169860.0}, {'field': 'offline_amount', 'old_value': 269801.0, 'new_value': 277898.0}, {'field': 'total_amount', 'old_value': 411720.0, 'new_value': 447758.0}, {'field': 'order_count', 'old_value': 3076, 'new_value': 3333}]
2025-06-29 12:01:11,988 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-29 12:01:12,425 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-29 12:01:12,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85100.0, 'new_value': 90680.0}, {'field': 'total_amount', 'old_value': 104180.0, 'new_value': 109760.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-29 12:01:12,425 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOC
2025-06-29 12:01:13,050 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOC
2025-06-29 12:01:13,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62233.0, 'new_value': 66433.0}, {'field': 'total_amount', 'old_value': 62233.0, 'new_value': 66433.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-29 12:01:13,050 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-29 12:01:13,535 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-29 12:01:13,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 394205.39, 'new_value': 411410.77}, {'field': 'total_amount', 'old_value': 394205.39, 'new_value': 411410.77}, {'field': 'order_count', 'old_value': 1260, 'new_value': 1316}]
2025-06-29 12:01:13,535 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-29 12:01:13,925 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-29 12:01:13,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47540.0, 'new_value': 51338.0}, {'field': 'total_amount', 'old_value': 47909.0, 'new_value': 51707.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 80}]
2025-06-29 12:01:13,925 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-29 12:01:14,379 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-29 12:01:14,379 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79887.0, 'new_value': 84887.0}, {'field': 'offline_amount', 'old_value': 24444.9, 'new_value': 24920.0}, {'field': 'total_amount', 'old_value': 104331.9, 'new_value': 109807.0}, {'field': 'order_count', 'old_value': 354, 'new_value': 368}]
2025-06-29 12:01:14,379 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-29 12:01:14,847 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-29 12:01:14,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16656.0, 'new_value': 18035.0}, {'field': 'offline_amount', 'old_value': 71382.05, 'new_value': 76455.58}, {'field': 'total_amount', 'old_value': 88038.05, 'new_value': 94490.58}, {'field': 'order_count', 'old_value': 907, 'new_value': 967}]
2025-06-29 12:01:14,847 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-29 12:01:15,379 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-29 12:01:15,379 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6969.0, 'new_value': 7610.0}, {'field': 'offline_amount', 'old_value': 32872.7, 'new_value': 35378.2}, {'field': 'total_amount', 'old_value': 39841.7, 'new_value': 42988.2}, {'field': 'order_count', 'old_value': 1395, 'new_value': 1498}]
2025-06-29 12:01:15,379 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-29 12:01:15,816 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-29 12:01:15,816 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27604.64, 'new_value': 28697.24}, {'field': 'offline_amount', 'old_value': 51672.67, 'new_value': 53888.67}, {'field': 'total_amount', 'old_value': 79277.31, 'new_value': 82585.91}, {'field': 'order_count', 'old_value': 2944, 'new_value': 3073}]
2025-06-29 12:01:15,816 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-29 12:01:16,254 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-29 12:01:16,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25550.0, 'new_value': 27010.0}, {'field': 'total_amount', 'old_value': 25610.0, 'new_value': 27070.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 114}]
2025-06-29 12:01:16,254 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-29 12:01:16,738 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-29 12:01:16,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20449.0, 'new_value': 20798.0}, {'field': 'total_amount', 'old_value': 20449.0, 'new_value': 20798.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 118}]
2025-06-29 12:01:16,738 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-29 12:01:17,363 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-29 12:01:17,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21781.7, 'new_value': 21949.7}, {'field': 'total_amount', 'old_value': 21781.7, 'new_value': 21949.7}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-06-29 12:01:17,363 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-29 12:01:17,847 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-29 12:01:17,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101165.96, 'new_value': 113766.86}, {'field': 'offline_amount', 'old_value': 144159.81, 'new_value': 148681.81}, {'field': 'total_amount', 'old_value': 245325.77, 'new_value': 262448.67}, {'field': 'order_count', 'old_value': 1323, 'new_value': 1372}]
2025-06-29 12:01:17,847 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-29 12:01:18,347 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-29 12:01:18,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81455.9, 'new_value': 84765.94}, {'field': 'total_amount', 'old_value': 81455.9, 'new_value': 84765.94}, {'field': 'order_count', 'old_value': 2191, 'new_value': 2288}]
2025-06-29 12:01:18,347 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-29 12:01:18,832 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-29 12:01:18,832 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45930.18, 'new_value': 46023.46}, {'field': 'offline_amount', 'old_value': 56665.15, 'new_value': 63967.71}, {'field': 'total_amount', 'old_value': 102595.33, 'new_value': 109991.17}, {'field': 'order_count', 'old_value': 593, 'new_value': 626}]
2025-06-29 12:01:18,832 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFD
2025-06-29 12:01:19,300 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFD
2025-06-29 12:01:19,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58446.0, 'new_value': 102082.0}, {'field': 'total_amount', 'old_value': 58446.0, 'new_value': 102082.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 35}]
2025-06-29 12:01:19,300 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-29 12:01:19,707 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-29 12:01:19,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18894.0, 'new_value': 19554.0}, {'field': 'total_amount', 'old_value': 18894.0, 'new_value': 19554.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-06-29 12:01:19,707 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-29 12:01:20,191 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-29 12:01:20,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15080.34, 'new_value': 15693.04}, {'field': 'offline_amount', 'old_value': 227803.22, 'new_value': 248489.72}, {'field': 'total_amount', 'old_value': 242883.56, 'new_value': 264182.76}, {'field': 'order_count', 'old_value': 1635, 'new_value': 1762}]
2025-06-29 12:01:20,191 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-29 12:01:20,722 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-29 12:01:20,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46345.3, 'new_value': 48283.0}, {'field': 'total_amount', 'old_value': 46345.3, 'new_value': 48283.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-06-29 12:01:20,722 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-29 12:01:21,175 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-29 12:01:21,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339115.16, 'new_value': 351807.4}, {'field': 'total_amount', 'old_value': 339115.16, 'new_value': 351807.4}, {'field': 'order_count', 'old_value': 10042, 'new_value': 10471}]
2025-06-29 12:01:21,175 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-29 12:01:21,566 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-29 12:01:21,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 193316.25, 'new_value': 205747.74}, {'field': 'offline_amount', 'old_value': 162489.79, 'new_value': 174474.29}, {'field': 'total_amount', 'old_value': 355806.04, 'new_value': 380222.03}, {'field': 'order_count', 'old_value': 3377, 'new_value': 3597}]
2025-06-29 12:01:21,566 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-29 12:01:22,050 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-29 12:01:22,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11825.0, 'new_value': 12200.0}, {'field': 'total_amount', 'old_value': 11825.0, 'new_value': 12200.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 64}]
2025-06-29 12:01:22,066 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-29 12:01:22,550 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-29 12:01:22,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155535.0, 'new_value': 158055.0}, {'field': 'total_amount', 'old_value': 286761.0, 'new_value': 289281.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 149}]
2025-06-29 12:01:22,550 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-29 12:01:23,035 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-29 12:01:23,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33833.0, 'new_value': 33834.0}, {'field': 'total_amount', 'old_value': 33833.0, 'new_value': 33834.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 1322}]
2025-06-29 12:01:23,035 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-29 12:01:23,519 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-29 12:01:23,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53399.0, 'new_value': 55173.0}, {'field': 'total_amount', 'old_value': 53399.0, 'new_value': 55173.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-06-29 12:01:23,519 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-29 12:01:24,050 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-29 12:01:24,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28998.09, 'new_value': 29911.37}, {'field': 'offline_amount', 'old_value': 290487.25, 'new_value': 301531.35}, {'field': 'total_amount', 'old_value': 319485.34, 'new_value': 331442.72}, {'field': 'order_count', 'old_value': 17810, 'new_value': 18548}]
2025-06-29 12:01:24,050 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-29 12:01:24,566 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-29 12:01:24,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62784.0, 'new_value': 64327.0}, {'field': 'total_amount', 'old_value': 62784.0, 'new_value': 64327.0}, {'field': 'order_count', 'old_value': 194, 'new_value': 200}]
2025-06-29 12:01:24,582 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-29 12:01:25,066 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-29 12:01:25,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137319.22, 'new_value': 147985.32}, {'field': 'total_amount', 'old_value': 137319.22, 'new_value': 147985.32}, {'field': 'order_count', 'old_value': 3971, 'new_value': 4302}]
2025-06-29 12:01:25,066 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-29 12:01:25,504 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-29 12:01:25,504 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16868.0, 'new_value': 17332.0}, {'field': 'total_amount', 'old_value': 16868.0, 'new_value': 17332.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 296}]
2025-06-29 12:01:25,504 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-29 12:01:25,988 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-29 12:01:25,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38850.66, 'new_value': 39859.99}, {'field': 'offline_amount', 'old_value': 30873.0, 'new_value': 31445.0}, {'field': 'total_amount', 'old_value': 69723.66, 'new_value': 71304.99}, {'field': 'order_count', 'old_value': 934, 'new_value': 956}]
2025-06-29 12:01:25,988 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-29 12:01:26,519 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-29 12:01:26,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85859.06, 'new_value': 99585.3}, {'field': 'total_amount', 'old_value': 85859.06, 'new_value': 99585.3}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-06-29 12:01:26,519 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-29 12:01:26,988 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-29 12:01:26,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237255.76, 'new_value': 245196.76}, {'field': 'total_amount', 'old_value': 237255.76, 'new_value': 245196.76}, {'field': 'order_count', 'old_value': 1319, 'new_value': 1359}]
2025-06-29 12:01:26,988 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-29 12:01:27,488 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-29 12:01:27,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27802.7, 'new_value': 29860.7}, {'field': 'total_amount', 'old_value': 27802.7, 'new_value': 29860.7}, {'field': 'order_count', 'old_value': 264, 'new_value': 285}]
2025-06-29 12:01:27,488 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-29 12:01:27,988 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-29 12:01:27,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50116.99, 'new_value': 52490.49}, {'field': 'total_amount', 'old_value': 50125.99, 'new_value': 52499.49}, {'field': 'order_count', 'old_value': 2081, 'new_value': 2177}]
2025-06-29 12:01:27,988 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-29 12:01:28,441 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-29 12:01:28,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367575.96, 'new_value': 388728.82}, {'field': 'total_amount', 'old_value': 403559.96, 'new_value': 424712.82}, {'field': 'order_count', 'old_value': 2131, 'new_value': 2229}]
2025-06-29 12:01:28,441 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-29 12:01:28,832 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-29 12:01:28,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79293.0, 'new_value': 84041.0}, {'field': 'total_amount', 'old_value': 82108.0, 'new_value': 86856.0}, {'field': 'order_count', 'old_value': 323, 'new_value': 339}]
2025-06-29 12:01:28,832 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-29 12:01:29,269 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-29 12:01:29,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203767.78, 'new_value': 218682.37}, {'field': 'offline_amount', 'old_value': 44954.7, 'new_value': 48527.37}, {'field': 'total_amount', 'old_value': 248722.48, 'new_value': 267209.74}, {'field': 'order_count', 'old_value': 997, 'new_value': 1064}]
2025-06-29 12:01:29,269 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-29 12:01:29,691 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-29 12:01:29,691 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36536.23, 'new_value': 37615.19}, {'field': 'offline_amount', 'old_value': 22191.65, 'new_value': 22961.94}, {'field': 'total_amount', 'old_value': 58727.88, 'new_value': 60577.13}, {'field': 'order_count', 'old_value': 3655, 'new_value': 3765}]
2025-06-29 12:01:29,691 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-29 12:01:30,082 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-29 12:01:30,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87265.29, 'new_value': 94506.96}, {'field': 'total_amount', 'old_value': 87265.29, 'new_value': 94506.96}, {'field': 'order_count', 'old_value': 434, 'new_value': 464}]
2025-06-29 12:01:30,082 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-29 12:01:30,519 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-29 12:01:30,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71605.0, 'new_value': 76712.0}, {'field': 'total_amount', 'old_value': 71605.0, 'new_value': 76712.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 58}]
2025-06-29 12:01:30,535 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-29 12:01:30,972 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-29 12:01:30,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 626909.02, 'new_value': 670926.48}, {'field': 'total_amount', 'old_value': 626909.02, 'new_value': 670926.48}, {'field': 'order_count', 'old_value': 2020, 'new_value': 2191}]
2025-06-29 12:01:30,972 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-29 12:01:31,410 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-29 12:01:31,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178435.4, 'new_value': 187503.0}, {'field': 'total_amount', 'old_value': 178524.4, 'new_value': 187592.0}, {'field': 'order_count', 'old_value': 2240, 'new_value': 2344}]
2025-06-29 12:01:31,410 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-29 12:01:31,863 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-29 12:01:31,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46252.1, 'new_value': 55719.1}, {'field': 'total_amount', 'old_value': 46252.1, 'new_value': 55719.1}, {'field': 'order_count', 'old_value': 111, 'new_value': 122}]
2025-06-29 12:01:31,863 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-29 12:01:32,316 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-29 12:01:32,316 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107059.0, 'new_value': 111836.0}, {'field': 'offline_amount', 'old_value': 460225.0, 'new_value': 471445.0}, {'field': 'total_amount', 'old_value': 567284.0, 'new_value': 583281.0}, {'field': 'order_count', 'old_value': 751, 'new_value': 782}]
2025-06-29 12:01:32,316 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-29 12:01:32,785 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-29 12:01:32,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62842.47, 'new_value': 65332.67}, {'field': 'offline_amount', 'old_value': 371291.91, 'new_value': 389075.57}, {'field': 'total_amount', 'old_value': 434134.38, 'new_value': 454408.24}, {'field': 'order_count', 'old_value': 2693, 'new_value': 2834}]
2025-06-29 12:01:32,785 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-29 12:01:33,347 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-29 12:01:33,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4689.37, 'new_value': 4750.37}, {'field': 'offline_amount', 'old_value': 152042.35, 'new_value': 157519.63}, {'field': 'total_amount', 'old_value': 156731.72, 'new_value': 162270.0}, {'field': 'order_count', 'old_value': 2421, 'new_value': 2519}]
2025-06-29 12:01:33,347 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-29 12:01:33,863 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-29 12:01:33,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236841.6, 'new_value': 273562.8}, {'field': 'total_amount', 'old_value': 236841.6, 'new_value': 273562.8}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-06-29 12:01:33,863 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-29 12:01:34,332 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-29 12:01:34,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1750532.41, 'new_value': 1894015.21}, {'field': 'total_amount', 'old_value': 1809011.11, 'new_value': 1952493.91}, {'field': 'order_count', 'old_value': 3495, 'new_value': 3736}]
2025-06-29 12:01:34,332 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-29 12:01:34,816 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-29 12:01:34,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31317.0, 'new_value': 32885.0}, {'field': 'total_amount', 'old_value': 31317.0, 'new_value': 32885.0}, {'field': 'order_count', 'old_value': 191, 'new_value': 201}]
2025-06-29 12:01:34,816 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4T
2025-06-29 12:01:35,222 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4T
2025-06-29 12:01:35,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12001.0, 'new_value': 13396.0}, {'field': 'total_amount', 'old_value': 12001.0, 'new_value': 13396.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 71}]
2025-06-29 12:01:35,222 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-29 12:01:35,660 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-29 12:01:35,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159905.69, 'new_value': 170908.58}, {'field': 'offline_amount', 'old_value': 109883.0, 'new_value': 117052.0}, {'field': 'total_amount', 'old_value': 269788.69, 'new_value': 287960.58}, {'field': 'order_count', 'old_value': 2763, 'new_value': 2938}]
2025-06-29 12:01:35,660 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-29 12:01:36,207 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-29 12:01:36,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 616048.83, 'new_value': 639596.72}, {'field': 'total_amount', 'old_value': 616364.19, 'new_value': 639912.08}, {'field': 'order_count', 'old_value': 1655, 'new_value': 1713}]
2025-06-29 12:01:36,207 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-29 12:01:36,691 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-29 12:01:36,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 666918.0, 'new_value': 692915.0}, {'field': 'total_amount', 'old_value': 666918.0, 'new_value': 692915.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-06-29 12:01:36,691 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-29 12:01:37,097 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-29 12:01:37,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300412.4, 'new_value': 327111.0}, {'field': 'total_amount', 'old_value': 300412.4, 'new_value': 327111.0}, {'field': 'order_count', 'old_value': 9098, 'new_value': 9501}]
2025-06-29 12:01:37,097 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-29 12:01:37,503 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-29 12:01:37,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48025.4, 'new_value': 53265.4}, {'field': 'total_amount', 'old_value': 48025.4, 'new_value': 53265.4}, {'field': 'order_count', 'old_value': 634, 'new_value': 702}]
2025-06-29 12:01:37,503 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-29 12:01:37,972 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-29 12:01:37,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145054.0, 'new_value': 146153.0}, {'field': 'total_amount', 'old_value': 145054.0, 'new_value': 146153.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-29 12:01:37,972 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-29 12:01:38,410 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-29 12:01:38,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52796.2, 'new_value': 54617.7}, {'field': 'total_amount', 'old_value': 52796.2, 'new_value': 54617.7}, {'field': 'order_count', 'old_value': 315, 'new_value': 330}]
2025-06-29 12:01:38,410 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-29 12:01:38,894 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-29 12:01:38,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 535735.0, 'new_value': 558936.0}, {'field': 'total_amount', 'old_value': 535735.0, 'new_value': 558936.0}, {'field': 'order_count', 'old_value': 558, 'new_value': 580}]
2025-06-29 12:01:38,894 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-29 12:01:39,332 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-29 12:01:39,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15910.0, 'new_value': 16325.0}, {'field': 'total_amount', 'old_value': 15910.0, 'new_value': 16325.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 186}]
2025-06-29 12:01:39,332 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-29 12:01:39,800 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-29 12:01:39,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132442.0, 'new_value': 142448.0}, {'field': 'total_amount', 'old_value': 210944.0, 'new_value': 220950.0}, {'field': 'order_count', 'old_value': 4827, 'new_value': 5046}]
2025-06-29 12:01:39,800 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-29 12:01:40,285 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-29 12:01:40,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31443.4, 'new_value': 32602.93}, {'field': 'offline_amount', 'old_value': 28282.25, 'new_value': 29499.19}, {'field': 'total_amount', 'old_value': 59725.65, 'new_value': 62102.12}, {'field': 'order_count', 'old_value': 2762, 'new_value': 2875}]
2025-06-29 12:01:40,285 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-29 12:01:40,753 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-29 12:01:40,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145464.28, 'new_value': 154875.06}, {'field': 'total_amount', 'old_value': 168316.47, 'new_value': 177727.25}, {'field': 'order_count', 'old_value': 8182, 'new_value': 8682}]
2025-06-29 12:01:40,753 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-29 12:01:41,316 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-29 12:01:41,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230412.0, 'new_value': 271516.0}, {'field': 'total_amount', 'old_value': 230412.0, 'new_value': 271516.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 49}]
2025-06-29 12:01:41,316 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-29 12:01:41,769 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-29 12:01:41,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62184.07, 'new_value': 65170.91}, {'field': 'offline_amount', 'old_value': 40407.3, 'new_value': 45160.48}, {'field': 'total_amount', 'old_value': 102591.37, 'new_value': 110331.39}, {'field': 'order_count', 'old_value': 6079, 'new_value': 6532}]
2025-06-29 12:01:41,769 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-29 12:01:42,300 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-29 12:01:42,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25042.0, 'new_value': 26898.0}, {'field': 'total_amount', 'old_value': 25042.0, 'new_value': 26898.0}, {'field': 'order_count', 'old_value': 235, 'new_value': 250}]
2025-06-29 12:01:42,300 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-29 12:01:42,738 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-29 12:01:42,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97848.82, 'new_value': 101614.54}, {'field': 'offline_amount', 'old_value': 100799.73, 'new_value': 107224.48}, {'field': 'total_amount', 'old_value': 198648.55, 'new_value': 208839.02}, {'field': 'order_count', 'old_value': 8134, 'new_value': 8764}]
2025-06-29 12:01:42,738 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-29 12:01:43,207 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-29 12:01:43,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116875.0, 'new_value': 127449.0}, {'field': 'total_amount', 'old_value': 116875.0, 'new_value': 127449.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 82}]
2025-06-29 12:01:43,207 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-29 12:01:43,675 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-29 12:01:43,675 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 306063.0, 'new_value': 322389.1}, {'field': 'offline_amount', 'old_value': 115433.1, 'new_value': 123069.0}, {'field': 'total_amount', 'old_value': 421496.1, 'new_value': 445458.1}, {'field': 'order_count', 'old_value': 1310, 'new_value': 1400}]
2025-06-29 12:01:43,675 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-29 12:01:44,175 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-29 12:01:44,175 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 270264.0, 'new_value': 282060.0}, {'field': 'total_amount', 'old_value': 270264.0, 'new_value': 282060.0}, {'field': 'order_count', 'old_value': 22522, 'new_value': 23505}]
2025-06-29 12:01:44,175 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-29 12:01:44,660 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-29 12:01:44,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139489.08, 'new_value': 145110.39}, {'field': 'offline_amount', 'old_value': 379998.59, 'new_value': 398034.92}, {'field': 'total_amount', 'old_value': 519487.67, 'new_value': 543145.31}, {'field': 'order_count', 'old_value': 5028, 'new_value': 5232}]
2025-06-29 12:01:44,660 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-29 12:01:45,113 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-29 12:01:45,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184584.0, 'new_value': 193952.0}, {'field': 'total_amount', 'old_value': 184584.0, 'new_value': 193952.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 139}]
2025-06-29 12:01:45,113 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-29 12:01:45,597 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-29 12:01:45,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 834481.7, 'new_value': 866075.94}, {'field': 'total_amount', 'old_value': 834481.7, 'new_value': 866075.94}, {'field': 'order_count', 'old_value': 5648, 'new_value': 5878}]
2025-06-29 12:01:45,597 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-29 12:01:46,113 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-29 12:01:46,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7038.9, 'new_value': 7518.4}, {'field': 'offline_amount', 'old_value': 39750.9, 'new_value': 41452.4}, {'field': 'total_amount', 'old_value': 46789.8, 'new_value': 48970.8}, {'field': 'order_count', 'old_value': 70, 'new_value': 73}]
2025-06-29 12:01:46,113 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-29 12:01:46,613 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-29 12:01:46,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234125.72, 'new_value': 253788.72}, {'field': 'total_amount', 'old_value': 234125.72, 'new_value': 253788.72}, {'field': 'order_count', 'old_value': 848, 'new_value': 900}]
2025-06-29 12:01:46,613 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-29 12:01:47,066 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-29 12:01:47,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37301.32, 'new_value': 38303.92}, {'field': 'offline_amount', 'old_value': 1139745.45, 'new_value': 1192973.52}, {'field': 'total_amount', 'old_value': 1177046.77, 'new_value': 1231277.44}, {'field': 'order_count', 'old_value': 5621, 'new_value': 5885}]
2025-06-29 12:01:47,066 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-29 12:01:47,613 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-29 12:01:47,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3955.8, 'new_value': 3999.8}, {'field': 'offline_amount', 'old_value': 90333.6, 'new_value': 94033.6}, {'field': 'total_amount', 'old_value': 94289.4, 'new_value': 98033.4}, {'field': 'order_count', 'old_value': 607, 'new_value': 630}]
2025-06-29 12:01:47,613 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-29 12:01:48,097 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-29 12:01:48,097 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92203.0, 'new_value': 99283.0}, {'field': 'total_amount', 'old_value': 92203.0, 'new_value': 99283.0}, {'field': 'order_count', 'old_value': 2772, 'new_value': 2981}]
2025-06-29 12:01:48,097 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-29 12:01:48,566 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-29 12:01:48,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354502.17, 'new_value': 370272.87}, {'field': 'total_amount', 'old_value': 354502.17, 'new_value': 370272.87}, {'field': 'order_count', 'old_value': 1232, 'new_value': 1284}]
2025-06-29 12:01:48,566 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-29 12:01:49,097 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-29 12:01:49,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40767.1, 'new_value': 43179.1}, {'field': 'total_amount', 'old_value': 40767.1, 'new_value': 43179.1}, {'field': 'order_count', 'old_value': 280, 'new_value': 301}]
2025-06-29 12:01:49,097 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-29 12:01:49,566 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-29 12:01:49,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180442.0, 'new_value': 185741.0}, {'field': 'total_amount', 'old_value': 189061.0, 'new_value': 194360.0}, {'field': 'order_count', 'old_value': 13930, 'new_value': 14310}]
2025-06-29 12:01:49,566 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-29 12:01:49,988 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-29 12:01:49,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88250.05, 'new_value': 92678.67}, {'field': 'offline_amount', 'old_value': 944338.16, 'new_value': 987802.32}, {'field': 'total_amount', 'old_value': 1032588.21, 'new_value': 1080480.99}, {'field': 'order_count', 'old_value': 4309, 'new_value': 4515}]
2025-06-29 12:01:49,988 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-29 12:01:50,441 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-29 12:01:50,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91218.0, 'new_value': 95786.0}, {'field': 'total_amount', 'old_value': 91218.0, 'new_value': 95786.0}, {'field': 'order_count', 'old_value': 736, 'new_value': 773}]
2025-06-29 12:01:50,441 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-29 12:01:50,910 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-29 12:01:50,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34097.92, 'new_value': 34535.92}, {'field': 'total_amount', 'old_value': 34655.92, 'new_value': 35093.92}, {'field': 'order_count', 'old_value': 77, 'new_value': 78}]
2025-06-29 12:01:50,910 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-29 12:01:51,394 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-29 12:01:51,394 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12107.0, 'new_value': 12567.0}, {'field': 'offline_amount', 'old_value': 2408.0, 'new_value': 2493.0}, {'field': 'total_amount', 'old_value': 14515.0, 'new_value': 15060.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 136}]
2025-06-29 12:01:51,394 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-29 12:01:51,831 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-29 12:01:51,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 165058.0, 'new_value': 170827.0}, {'field': 'offline_amount', 'old_value': 109794.0, 'new_value': 119228.0}, {'field': 'total_amount', 'old_value': 274852.0, 'new_value': 290055.0}, {'field': 'order_count', 'old_value': 3953, 'new_value': 4155}]
2025-06-29 12:01:51,831 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-29 12:01:52,222 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-29 12:01:52,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1649969.18, 'new_value': 1728671.39}, {'field': 'offline_amount', 'old_value': 362060.8, 'new_value': 377283.8}, {'field': 'total_amount', 'old_value': 2012029.98, 'new_value': 2105955.19}, {'field': 'order_count', 'old_value': 7467, 'new_value': 7807}]
2025-06-29 12:01:52,222 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-29 12:01:52,722 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-29 12:01:52,722 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27200.94, 'new_value': 29714.94}, {'field': 'offline_amount', 'old_value': 32208.46, 'new_value': 32908.46}, {'field': 'total_amount', 'old_value': 59409.4, 'new_value': 62623.4}, {'field': 'order_count', 'old_value': 6707, 'new_value': 6720}]
2025-06-29 12:01:52,722 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-29 12:01:53,206 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-29 12:01:53,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209472.4, 'new_value': 226214.0}, {'field': 'total_amount', 'old_value': 343136.9, 'new_value': 359878.5}, {'field': 'order_count', 'old_value': 9341, 'new_value': 9767}]
2025-06-29 12:01:53,206 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-29 12:01:53,753 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-29 12:01:53,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30119.0, 'new_value': 37624.0}, {'field': 'total_amount', 'old_value': 30119.0, 'new_value': 37624.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 53}]
2025-06-29 12:01:53,753 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-29 12:01:54,222 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-29 12:01:54,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5599.58, 'new_value': 6493.21}, {'field': 'offline_amount', 'old_value': 122728.46, 'new_value': 139645.56}, {'field': 'total_amount', 'old_value': 128328.04, 'new_value': 146138.77}, {'field': 'order_count', 'old_value': 5150, 'new_value': 5862}]
2025-06-29 12:01:54,222 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-29 12:01:54,613 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-29 12:01:54,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6723.0, 'new_value': 7517.0}, {'field': 'total_amount', 'old_value': 6723.0, 'new_value': 7517.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 222}]
2025-06-29 12:01:54,613 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-29 12:01:55,066 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-29 12:01:55,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116691.0, 'new_value': 123146.0}, {'field': 'total_amount', 'old_value': 116691.0, 'new_value': 123146.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 310}]
2025-06-29 12:01:55,081 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-29 12:01:55,597 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-29 12:01:55,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49488.0, 'new_value': 99728.0}, {'field': 'total_amount', 'old_value': 49488.0, 'new_value': 99728.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 25}]
2025-06-29 12:01:55,597 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-29 12:01:56,035 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-29 12:01:56,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 865178.0, 'new_value': 906144.0}, {'field': 'total_amount', 'old_value': 865178.0, 'new_value': 906144.0}, {'field': 'order_count', 'old_value': 1093, 'new_value': 1141}]
2025-06-29 12:01:56,035 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-29 12:01:56,488 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-29 12:01:56,488 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104109.7, 'new_value': 109767.5}, {'field': 'total_amount', 'old_value': 104109.7, 'new_value': 109767.5}, {'field': 'order_count', 'old_value': 478, 'new_value': 505}]
2025-06-29 12:01:56,488 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-29 12:01:56,972 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-29 12:01:56,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43697.85, 'new_value': 44851.66}, {'field': 'offline_amount', 'old_value': 49459.11, 'new_value': 50973.67}, {'field': 'total_amount', 'old_value': 93156.96, 'new_value': 95825.33}, {'field': 'order_count', 'old_value': 4850, 'new_value': 4989}]
2025-06-29 12:01:56,972 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-29 12:01:57,410 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-29 12:01:57,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146241.5, 'new_value': 155930.2}, {'field': 'total_amount', 'old_value': 146241.5, 'new_value': 155930.2}, {'field': 'order_count', 'old_value': 697, 'new_value': 751}]
2025-06-29 12:01:57,410 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-29 12:01:57,910 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-29 12:01:57,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50853.5, 'new_value': 53436.5}, {'field': 'total_amount', 'old_value': 50853.5, 'new_value': 53436.5}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-06-29 12:01:57,910 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-29 12:01:58,363 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-29 12:01:58,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16981.2, 'new_value': 17114.1}, {'field': 'offline_amount', 'old_value': 309870.7, 'new_value': 325038.6}, {'field': 'total_amount', 'old_value': 326851.9, 'new_value': 342152.7}, {'field': 'order_count', 'old_value': 1728, 'new_value': 1801}]
2025-06-29 12:01:58,363 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-29 12:01:58,863 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-29 12:01:58,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35610.0, 'new_value': 37037.0}, {'field': 'total_amount', 'old_value': 35610.0, 'new_value': 37037.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 113}]
2025-06-29 12:01:58,863 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-29 12:01:59,331 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-29 12:01:59,331 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18219.95, 'new_value': 18282.95}, {'field': 'offline_amount', 'old_value': 152807.0, 'new_value': 159700.0}, {'field': 'total_amount', 'old_value': 171026.95, 'new_value': 177982.95}, {'field': 'order_count', 'old_value': 88, 'new_value': 90}]
2025-06-29 12:01:59,331 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-29 12:01:59,769 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-29 12:01:59,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49853.0, 'new_value': 61934.0}, {'field': 'total_amount', 'old_value': 49853.0, 'new_value': 61934.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 55}]
2025-06-29 12:01:59,769 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-29 12:02:00,206 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-29 12:02:00,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69938.56, 'new_value': 78792.36}, {'field': 'total_amount', 'old_value': 69938.56, 'new_value': 78792.36}, {'field': 'order_count', 'old_value': 1058, 'new_value': 1084}]
2025-06-29 12:02:00,206 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-29 12:02:00,660 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-29 12:02:00,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178327.9, 'new_value': 206803.3}, {'field': 'total_amount', 'old_value': 178327.9, 'new_value': 206803.3}, {'field': 'order_count', 'old_value': 5764, 'new_value': 6608}]
2025-06-29 12:02:00,660 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-29 12:02:01,160 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-29 12:02:01,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248057.97, 'new_value': 261559.97}, {'field': 'total_amount', 'old_value': 258684.97, 'new_value': 272186.97}, {'field': 'order_count', 'old_value': 1095, 'new_value': 1154}]
2025-06-29 12:02:01,160 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-29 12:02:01,581 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-29 12:02:01,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6950.13, 'new_value': 7346.28}, {'field': 'offline_amount', 'old_value': 17630.93, 'new_value': 18374.54}, {'field': 'total_amount', 'old_value': 24581.06, 'new_value': 25720.82}, {'field': 'order_count', 'old_value': 262, 'new_value': 277}]
2025-06-29 12:02:01,581 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-29 12:02:02,066 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-29 12:02:02,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38698.79, 'new_value': 40145.79}, {'field': 'total_amount', 'old_value': 38698.79, 'new_value': 40145.79}, {'field': 'order_count', 'old_value': 849, 'new_value': 880}]
2025-06-29 12:02:02,066 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-29 12:02:02,550 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-29 12:02:02,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 313866.16, 'new_value': 334575.81}, {'field': 'total_amount', 'old_value': 383502.97, 'new_value': 404212.62}, {'field': 'order_count', 'old_value': 5265, 'new_value': 5450}]
2025-06-29 12:02:02,550 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-29 12:02:03,097 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-29 12:02:03,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365544.0, 'new_value': 379740.0}, {'field': 'total_amount', 'old_value': 379102.0, 'new_value': 393298.0}, {'field': 'order_count', 'old_value': 322, 'new_value': 334}]
2025-06-29 12:02:03,097 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-29 12:02:03,566 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-29 12:02:03,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135049.0, 'new_value': 142773.0}, {'field': 'total_amount', 'old_value': 135217.0, 'new_value': 142941.0}, {'field': 'order_count', 'old_value': 398, 'new_value': 414}]
2025-06-29 12:02:03,566 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-29 12:02:04,066 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-29 12:02:04,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4676.5, 'new_value': 7230.5}, {'field': 'offline_amount', 'old_value': 65019.01, 'new_value': 65973.01}, {'field': 'total_amount', 'old_value': 69695.51, 'new_value': 73203.51}, {'field': 'order_count', 'old_value': 431, 'new_value': 463}]
2025-06-29 12:02:04,066 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-29 12:02:04,550 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-29 12:02:04,550 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19269.0, 'new_value': 21582.0}, {'field': 'offline_amount', 'old_value': 27962.0, 'new_value': 29576.0}, {'field': 'total_amount', 'old_value': 47231.0, 'new_value': 51158.0}, {'field': 'order_count', 'old_value': 378, 'new_value': 408}]
2025-06-29 12:02:04,550 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-29 12:02:05,003 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-29 12:02:05,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148498.26, 'new_value': 150975.7}, {'field': 'total_amount', 'old_value': 148498.26, 'new_value': 150975.7}, {'field': 'order_count', 'old_value': 718, 'new_value': 735}]
2025-06-29 12:02:05,003 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-29 12:02:05,425 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-29 12:02:05,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 443465.99, 'new_value': 489843.89}, {'field': 'total_amount', 'old_value': 443465.99, 'new_value': 489843.89}, {'field': 'order_count', 'old_value': 10355, 'new_value': 11397}]
2025-06-29 12:02:05,425 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-29 12:02:05,831 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-29 12:02:05,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110998.7, 'new_value': 114731.46}, {'field': 'offline_amount', 'old_value': 144879.6, 'new_value': 151727.98}, {'field': 'total_amount', 'old_value': 255878.3, 'new_value': 266459.44}, {'field': 'order_count', 'old_value': 9766, 'new_value': 10029}]
2025-06-29 12:02:05,831 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-29 12:02:06,300 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-29 12:02:06,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195438.31, 'new_value': 204503.76}, {'field': 'total_amount', 'old_value': 195438.31, 'new_value': 204503.76}, {'field': 'order_count', 'old_value': 829, 'new_value': 863}]
2025-06-29 12:02:06,300 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM321
2025-06-29 12:02:06,753 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM321
2025-06-29 12:02:06,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8188.8, 'new_value': 9200.8}, {'field': 'total_amount', 'old_value': 58188.8, 'new_value': 59200.8}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-29 12:02:06,753 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-29 12:02:07,191 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-29 12:02:07,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40881.0, 'new_value': 43879.0}, {'field': 'total_amount', 'old_value': 40881.0, 'new_value': 43879.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-06-29 12:02:07,191 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-29 12:02:07,753 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-29 12:02:07,753 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 228585.43, 'new_value': 246143.43}, {'field': 'total_amount', 'old_value': 310300.63, 'new_value': 327858.63}, {'field': 'order_count', 'old_value': 489, 'new_value': 511}]
2025-06-29 12:02:07,753 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-29 12:02:08,222 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-29 12:02:08,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93140.68, 'new_value': 97767.08}, {'field': 'total_amount', 'old_value': 93140.68, 'new_value': 97767.08}, {'field': 'order_count', 'old_value': 6109, 'new_value': 6450}]
2025-06-29 12:02:08,222 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-29 12:02:08,675 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-29 12:02:08,675 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3175.0, 'new_value': 3275.0}, {'field': 'offline_amount', 'old_value': 31585.0, 'new_value': 32185.0}, {'field': 'total_amount', 'old_value': 34760.0, 'new_value': 35460.0}, {'field': 'order_count', 'old_value': 443, 'new_value': 451}]
2025-06-29 12:02:08,675 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-29 12:02:09,113 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-29 12:02:09,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146711.0, 'new_value': 154035.0}, {'field': 'offline_amount', 'old_value': 207349.0, 'new_value': 231240.0}, {'field': 'total_amount', 'old_value': 354060.0, 'new_value': 385275.0}, {'field': 'order_count', 'old_value': 198466, 'new_value': 198551}]
2025-06-29 12:02:09,113 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-29 12:02:09,566 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-29 12:02:09,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78935.48, 'new_value': 82543.47}, {'field': 'offline_amount', 'old_value': 77660.21, 'new_value': 79735.35}, {'field': 'total_amount', 'old_value': 156595.69, 'new_value': 162278.82}, {'field': 'order_count', 'old_value': 8263, 'new_value': 8592}]
2025-06-29 12:02:09,566 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-29 12:02:10,019 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-29 12:02:10,019 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10816.29, 'new_value': 11764.41}, {'field': 'offline_amount', 'old_value': 29081.68, 'new_value': 31968.68}, {'field': 'total_amount', 'old_value': 39897.97, 'new_value': 43733.09}, {'field': 'order_count', 'old_value': 118, 'new_value': 123}]
2025-06-29 12:02:10,019 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-29 12:02:10,456 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-29 12:02:10,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98369.0, 'new_value': 101314.0}, {'field': 'total_amount', 'old_value': 98369.0, 'new_value': 101314.0}, {'field': 'order_count', 'old_value': 14319, 'new_value': 14780}]
2025-06-29 12:02:10,456 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-29 12:02:10,941 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-29 12:02:10,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63271.0, 'new_value': 65234.0}, {'field': 'total_amount', 'old_value': 65576.0, 'new_value': 67539.0}, {'field': 'order_count', 'old_value': 14319, 'new_value': 14780}]
2025-06-29 12:02:10,941 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-29 12:02:11,409 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-29 12:02:11,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21734.05, 'new_value': 22719.05}, {'field': 'offline_amount', 'old_value': 12789.84, 'new_value': 13874.84}, {'field': 'total_amount', 'old_value': 34523.89, 'new_value': 36593.89}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1350}]
2025-06-29 12:02:11,409 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-29 12:02:11,894 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-29 12:02:11,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254114.0, 'new_value': 258529.0}, {'field': 'total_amount', 'old_value': 254114.0, 'new_value': 258529.0}, {'field': 'order_count', 'old_value': 26871, 'new_value': 27275}]
2025-06-29 12:02:11,894 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-29 12:02:12,331 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-29 12:02:12,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16823.0, 'new_value': 17322.0}, {'field': 'total_amount', 'old_value': 16823.0, 'new_value': 17322.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-06-29 12:02:12,331 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-29 12:02:12,878 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-29 12:02:12,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14634.18, 'new_value': 15067.71}, {'field': 'offline_amount', 'old_value': 66236.58, 'new_value': 68731.11}, {'field': 'total_amount', 'old_value': 80870.76, 'new_value': 83798.82}, {'field': 'order_count', 'old_value': 1788, 'new_value': 1853}]
2025-06-29 12:02:12,878 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-29 12:02:13,300 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-29 12:02:13,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102573.0, 'new_value': 109675.0}, {'field': 'total_amount', 'old_value': 102573.0, 'new_value': 109675.0}, {'field': 'order_count', 'old_value': 9345, 'new_value': 9817}]
2025-06-29 12:02:13,300 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-29 12:02:13,738 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-29 12:02:13,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 172507.0, 'new_value': 177913.0}, {'field': 'offline_amount', 'old_value': 1244156.0, 'new_value': 1303150.0}, {'field': 'total_amount', 'old_value': 1416663.0, 'new_value': 1481063.0}, {'field': 'order_count', 'old_value': 48006, 'new_value': 49800}]
2025-06-29 12:02:13,738 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-29 12:02:14,206 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-29 12:02:14,206 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6892.5, 'new_value': 7864.5}, {'field': 'total_amount', 'old_value': 15354.0, 'new_value': 16326.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 141}]
2025-06-29 12:02:14,206 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-29 12:02:14,706 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-29 12:02:14,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20186.0, 'new_value': 23404.0}, {'field': 'total_amount', 'old_value': 20186.0, 'new_value': 23404.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-06-29 12:02:14,706 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-29 12:02:15,159 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-29 12:02:15,175 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47976.25, 'new_value': 49740.15}, {'field': 'total_amount', 'old_value': 47976.25, 'new_value': 49740.15}, {'field': 'order_count', 'old_value': 1273, 'new_value': 1319}]
2025-06-29 12:02:15,175 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-29 12:02:15,706 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-29 12:02:15,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 729387.96, 'new_value': 774215.14}, {'field': 'total_amount', 'old_value': 729387.96, 'new_value': 774215.14}, {'field': 'order_count', 'old_value': 6323, 'new_value': 6683}]
2025-06-29 12:02:15,706 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-29 12:02:16,128 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-29 12:02:16,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 277422.9, 'new_value': 303157.9}, {'field': 'offline_amount', 'old_value': 24485.5, 'new_value': 26350.5}, {'field': 'total_amount', 'old_value': 301908.4, 'new_value': 329508.4}, {'field': 'order_count', 'old_value': 3803, 'new_value': 4169}]
2025-06-29 12:02:16,128 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-29 12:02:16,691 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-29 12:02:16,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 846593.0, 'new_value': 949645.0}, {'field': 'total_amount', 'old_value': 846593.0, 'new_value': 949645.0}, {'field': 'order_count', 'old_value': 3090, 'new_value': 3551}]
2025-06-29 12:02:16,691 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-29 12:02:17,113 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-29 12:02:17,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32793.4, 'new_value': 33626.1}, {'field': 'offline_amount', 'old_value': 59873.7, 'new_value': 63817.6}, {'field': 'total_amount', 'old_value': 92667.1, 'new_value': 97443.7}, {'field': 'order_count', 'old_value': 3641, 'new_value': 3800}]
2025-06-29 12:02:17,113 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-29 12:02:17,597 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-29 12:02:17,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 747023.14, 'new_value': 769855.14}, {'field': 'total_amount', 'old_value': 747023.14, 'new_value': 769855.14}, {'field': 'order_count', 'old_value': 4628, 'new_value': 4705}]
2025-06-29 12:02:17,597 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-29 12:02:18,019 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-29 12:02:18,019 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35896.66, 'new_value': 36759.81}, {'field': 'total_amount', 'old_value': 36662.66, 'new_value': 37525.81}, {'field': 'order_count', 'old_value': 352, 'new_value': 364}]
2025-06-29 12:02:18,019 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-29 12:02:18,519 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-29 12:02:18,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36973.92, 'new_value': 38259.59}, {'field': 'total_amount', 'old_value': 36973.92, 'new_value': 38259.59}, {'field': 'order_count', 'old_value': 116, 'new_value': 121}]
2025-06-29 12:02:18,519 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-29 12:02:19,113 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-29 12:02:19,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 426271.71, 'new_value': 524160.51}, {'field': 'total_amount', 'old_value': 464356.64, 'new_value': 562245.44}, {'field': 'order_count', 'old_value': 572, 'new_value': 624}]
2025-06-29 12:02:19,113 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-29 12:02:19,644 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-29 12:02:19,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178386.0, 'new_value': 188053.0}, {'field': 'total_amount', 'old_value': 269496.0, 'new_value': 279163.0}, {'field': 'order_count', 'old_value': 5387, 'new_value': 5575}]
2025-06-29 12:02:19,644 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-29 12:02:20,113 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-29 12:02:20,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5527692.49, 'new_value': 5761598.76}, {'field': 'total_amount', 'old_value': 5527692.49, 'new_value': 5761598.76}, {'field': 'order_count', 'old_value': 111847, 'new_value': 117167}]
2025-06-29 12:02:20,113 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-29 12:02:20,519 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-29 12:02:20,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1150302.56, 'new_value': 1211666.76}, {'field': 'total_amount', 'old_value': 1150302.56, 'new_value': 1211666.76}, {'field': 'order_count', 'old_value': 4492, 'new_value': 4675}]
2025-06-29 12:02:20,519 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-29 12:02:21,034 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-29 12:02:21,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 735007.35, 'new_value': 783009.25}, {'field': 'total_amount', 'old_value': 735007.35, 'new_value': 783009.25}, {'field': 'order_count', 'old_value': 1896, 'new_value': 2004}]
2025-06-29 12:02:21,034 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-29 12:02:21,519 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-29 12:02:21,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91152.0, 'new_value': 94702.0}, {'field': 'total_amount', 'old_value': 91152.0, 'new_value': 94702.0}, {'field': 'order_count', 'old_value': 468, 'new_value': 481}]
2025-06-29 12:02:21,519 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-29 12:02:22,034 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-29 12:02:22,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11739.5, 'new_value': 12062.5}, {'field': 'total_amount', 'old_value': 11739.5, 'new_value': 12062.5}, {'field': 'order_count', 'old_value': 995, 'new_value': 1017}]
2025-06-29 12:02:22,034 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-29 12:02:22,456 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-29 12:02:22,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31559.13, 'new_value': 35841.16}, {'field': 'total_amount', 'old_value': 107509.95, 'new_value': 111791.98}, {'field': 'order_count', 'old_value': 7133, 'new_value': 7434}]
2025-06-29 12:02:22,456 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-29 12:02:22,909 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-29 12:02:22,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59196.0, 'new_value': 65923.25}, {'field': 'total_amount', 'old_value': 193028.71, 'new_value': 199755.96}, {'field': 'order_count', 'old_value': 12871, 'new_value': 13318}]
2025-06-29 12:02:22,909 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-29 12:02:23,316 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-29 12:02:23,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42164.0, 'new_value': 42292.0}, {'field': 'total_amount', 'old_value': 49619.0, 'new_value': 49747.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-29 12:02:23,316 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-29 12:02:23,722 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-29 12:02:23,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85018.0, 'new_value': 96478.0}, {'field': 'total_amount', 'old_value': 103858.0, 'new_value': 115318.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-06-29 12:02:23,722 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-29 12:02:24,159 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-29 12:02:24,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2179588.0, 'new_value': 2279588.0}, {'field': 'total_amount', 'old_value': 2179588.0, 'new_value': 2279588.0}, {'field': 'order_count', 'old_value': 3088, 'new_value': 3089}]
2025-06-29 12:02:24,159 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-29 12:02:24,644 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-29 12:02:24,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195000.0, 'new_value': 205000.0}, {'field': 'total_amount', 'old_value': 195000.0, 'new_value': 205000.0}, {'field': 'order_count', 'old_value': 801, 'new_value': 802}]
2025-06-29 12:02:24,644 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-29 12:02:25,253 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-29 12:02:25,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195000.0, 'new_value': 205000.0}, {'field': 'total_amount', 'old_value': 205000.0, 'new_value': 215000.0}, {'field': 'order_count', 'old_value': 930, 'new_value': 931}]
2025-06-29 12:02:25,253 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-29 12:02:25,706 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-29 12:02:25,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73030.0, 'new_value': 96830.0}, {'field': 'total_amount', 'old_value': 73030.0, 'new_value': 96830.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 20}]
2025-06-29 12:02:25,706 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-29 12:02:26,253 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-29 12:02:26,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 492336.86, 'new_value': 545417.26}, {'field': 'total_amount', 'old_value': 492336.86, 'new_value': 545417.26}, {'field': 'order_count', 'old_value': 3361, 'new_value': 3691}]
2025-06-29 12:02:26,253 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-29 12:02:26,722 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-29 12:02:26,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271705.06, 'new_value': 274085.51}, {'field': 'total_amount', 'old_value': 271705.06, 'new_value': 274085.51}, {'field': 'order_count', 'old_value': 7750, 'new_value': 7953}]
2025-06-29 12:02:26,722 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-29 12:02:27,237 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-29 12:02:27,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153948.24, 'new_value': 163562.87}, {'field': 'total_amount', 'old_value': 153948.24, 'new_value': 163562.87}, {'field': 'order_count', 'old_value': 10899, 'new_value': 11585}]
2025-06-29 12:02:27,237 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-29 12:02:27,706 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-29 12:02:27,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 299500.0, 'new_value': 310900.0}, {'field': 'total_amount', 'old_value': 299500.0, 'new_value': 310900.0}, {'field': 'order_count', 'old_value': 706, 'new_value': 733}]
2025-06-29 12:02:27,706 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-29 12:02:28,128 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-29 12:02:28,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33485.68, 'new_value': 35964.18}, {'field': 'offline_amount', 'old_value': 139287.0, 'new_value': 147391.0}, {'field': 'total_amount', 'old_value': 172772.68, 'new_value': 183355.18}, {'field': 'order_count', 'old_value': 1971, 'new_value': 2065}]
2025-06-29 12:02:28,128 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-29 12:02:28,581 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-29 12:02:28,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195000.0, 'new_value': 205000.0}, {'field': 'total_amount', 'old_value': 195000.0, 'new_value': 205000.0}, {'field': 'order_count', 'old_value': 596, 'new_value': 597}]
2025-06-29 12:02:28,581 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-29 12:02:29,066 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-29 12:02:29,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238128.36, 'new_value': 238290.16}, {'field': 'total_amount', 'old_value': 238128.36, 'new_value': 238290.16}, {'field': 'order_count', 'old_value': 3633, 'new_value': 3701}]
2025-06-29 12:02:29,066 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP1
2025-06-29 12:02:29,519 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP1
2025-06-29 12:02:29,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304076.0, 'new_value': 314076.0}, {'field': 'total_amount', 'old_value': 304076.0, 'new_value': 314076.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-29 12:02:29,519 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-29 12:02:30,050 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-29 12:02:30,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600665.78, 'new_value': 626522.78}, {'field': 'total_amount', 'old_value': 600665.78, 'new_value': 626522.78}, {'field': 'order_count', 'old_value': 560, 'new_value': 573}]
2025-06-29 12:02:30,050 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-29 12:02:30,487 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-29 12:02:30,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450833.0, 'new_value': 501211.0}, {'field': 'total_amount', 'old_value': 488489.0, 'new_value': 538867.0}, {'field': 'order_count', 'old_value': 10171, 'new_value': 11345}]
2025-06-29 12:02:30,487 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-29 12:02:30,987 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-29 12:02:30,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87488.07, 'new_value': 95527.59}, {'field': 'offline_amount', 'old_value': 143906.3, 'new_value': 156565.2}, {'field': 'total_amount', 'old_value': 231394.37, 'new_value': 252092.79}, {'field': 'order_count', 'old_value': 11414, 'new_value': 12471}]
2025-06-29 12:02:30,987 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-29 12:02:31,456 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-29 12:02:31,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86389.2, 'new_value': 92371.2}, {'field': 'total_amount', 'old_value': 86389.2, 'new_value': 92371.2}, {'field': 'order_count', 'old_value': 777, 'new_value': 834}]
2025-06-29 12:02:31,456 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-29 12:02:31,941 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-29 12:02:31,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84354.0, 'new_value': 388024.0}, {'field': 'total_amount', 'old_value': 84354.0, 'new_value': 388024.0}, {'field': 'order_count', 'old_value': 3813, 'new_value': 4007}]
2025-06-29 12:02:31,941 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-29 12:02:32,425 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-29 12:02:32,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132381.0, 'new_value': 136071.0}, {'field': 'total_amount', 'old_value': 132381.0, 'new_value': 136071.0}, {'field': 'order_count', 'old_value': 567, 'new_value': 587}]
2025-06-29 12:02:32,425 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-29 12:02:32,925 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-29 12:02:32,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118158.4, 'new_value': 131336.4}, {'field': 'total_amount', 'old_value': 118158.4, 'new_value': 131336.4}, {'field': 'order_count', 'old_value': 49, 'new_value': 55}]
2025-06-29 12:02:32,925 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-29 12:02:33,362 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-29 12:02:33,362 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45811.8, 'new_value': 48109.8}, {'field': 'offline_amount', 'old_value': 364324.2, 'new_value': 364863.2}, {'field': 'total_amount', 'old_value': 410136.0, 'new_value': 412973.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 141}]
2025-06-29 12:02:33,362 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-29 12:02:33,769 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-29 12:02:33,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79500.0, 'new_value': 81620.0}, {'field': 'total_amount', 'old_value': 82945.0, 'new_value': 85065.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 324}]
2025-06-29 12:02:33,769 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-29 12:02:34,222 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-29 12:02:34,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4186189.0, 'new_value': 4468189.0}, {'field': 'total_amount', 'old_value': 5095089.0, 'new_value': 5377089.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-29 12:02:34,222 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-29 12:02:34,831 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-29 12:02:34,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347165.77, 'new_value': 369703.51}, {'field': 'total_amount', 'old_value': 347165.77, 'new_value': 369703.51}, {'field': 'order_count', 'old_value': 959, 'new_value': 1023}]
2025-06-29 12:02:34,831 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-29 12:02:35,409 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-29 12:02:35,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352095.0, 'new_value': 364656.0}, {'field': 'total_amount', 'old_value': 352095.0, 'new_value': 364656.0}, {'field': 'order_count', 'old_value': 7876, 'new_value': 8127}]
2025-06-29 12:02:35,409 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-29 12:02:35,909 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-29 12:02:35,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123157.0, 'new_value': 127667.0}, {'field': 'total_amount', 'old_value': 123157.0, 'new_value': 127667.0}, {'field': 'order_count', 'old_value': 7034, 'new_value': 7287}]
2025-06-29 12:02:35,909 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-29 12:02:36,362 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-29 12:02:36,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354100.0, 'new_value': 356480.0}, {'field': 'total_amount', 'old_value': 354100.0, 'new_value': 356480.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 86}]
2025-06-29 12:02:36,362 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-29 12:02:36,909 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-29 12:02:36,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3179715.43, 'new_value': 3366759.99}, {'field': 'total_amount', 'old_value': 3179715.43, 'new_value': 3366759.99}, {'field': 'order_count', 'old_value': 6024, 'new_value': 6404}]
2025-06-29 12:02:36,909 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-29 12:02:37,331 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-29 12:02:37,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 862070.0, 'new_value': 920411.0}, {'field': 'total_amount', 'old_value': 862070.0, 'new_value': 920411.0}, {'field': 'order_count', 'old_value': 4671, 'new_value': 4925}]
2025-06-29 12:02:37,331 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-29 12:02:37,878 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-29 12:02:37,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9866360.73, 'new_value': 10502338.73}, {'field': 'total_amount', 'old_value': 9866360.73, 'new_value': 10502338.73}, {'field': 'order_count', 'old_value': 37024, 'new_value': 39659}]
2025-06-29 12:02:37,878 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-29 12:02:38,300 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-29 12:02:38,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185010.57, 'new_value': 194751.57}, {'field': 'total_amount', 'old_value': 185010.57, 'new_value': 194751.57}, {'field': 'order_count', 'old_value': 20407, 'new_value': 21437}]
2025-06-29 12:02:38,300 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-29 12:02:38,784 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-29 12:02:38,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 201862.61, 'new_value': 211827.44}, {'field': 'offline_amount', 'old_value': 171743.33, 'new_value': 183708.16}, {'field': 'total_amount', 'old_value': 373605.94, 'new_value': 395535.6}, {'field': 'order_count', 'old_value': 16093, 'new_value': 17047}]
2025-06-29 12:02:38,784 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-29 12:02:39,175 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-29 12:02:39,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133192.0, 'new_value': 145690.0}, {'field': 'total_amount', 'old_value': 133192.0, 'new_value': 145690.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-29 12:02:39,175 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-29 12:02:39,800 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-29 12:02:39,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241727.9, 'new_value': 247715.9}, {'field': 'total_amount', 'old_value': 241727.9, 'new_value': 247715.9}, {'field': 'order_count', 'old_value': 8284, 'new_value': 8497}]
2025-06-29 12:02:39,800 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP2
2025-06-29 12:02:40,316 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP2
2025-06-29 12:02:40,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2890.0, 'new_value': 4070.0}, {'field': 'total_amount', 'old_value': 2890.0, 'new_value': 4070.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-29 12:02:40,316 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-29 12:02:40,753 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-29 12:02:40,753 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 268431.85, 'new_value': 294407.85}, {'field': 'total_amount', 'old_value': 302633.95, 'new_value': 328609.95}, {'field': 'order_count', 'old_value': 921, 'new_value': 957}]
2025-06-29 12:02:40,753 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-29 12:02:41,222 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-29 12:02:41,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15721681.25, 'new_value': 16979415.75}, {'field': 'total_amount', 'old_value': 15721681.25, 'new_value': 16979415.75}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-06-29 12:02:41,222 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-29 12:02:41,659 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-29 12:02:41,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195896.0, 'new_value': 201119.0}, {'field': 'total_amount', 'old_value': 195896.0, 'new_value': 201119.0}, {'field': 'order_count', 'old_value': 842, 'new_value': 868}]
2025-06-29 12:02:41,659 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-29 12:02:42,081 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-29 12:02:42,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35643.0, 'new_value': 38735.0}, {'field': 'offline_amount', 'old_value': 356012.0, 'new_value': 404192.0}, {'field': 'total_amount', 'old_value': 391655.0, 'new_value': 442927.0}, {'field': 'order_count', 'old_value': 350, 'new_value': 397}]
2025-06-29 12:02:42,081 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM83
2025-06-29 12:02:42,581 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM83
2025-06-29 12:02:42,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8551.2, 'new_value': 9773.8}, {'field': 'total_amount', 'old_value': 8774.7, 'new_value': 9997.3}, {'field': 'order_count', 'old_value': 800, 'new_value': 907}]
2025-06-29 12:02:42,581 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-29 12:02:43,065 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-29 12:02:43,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67522.68, 'new_value': 79809.34}, {'field': 'total_amount', 'old_value': 70942.43, 'new_value': 83229.09}, {'field': 'order_count', 'old_value': 543, 'new_value': 644}]
2025-06-29 12:02:43,065 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-29 12:02:43,487 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-29 12:02:43,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204794.27, 'new_value': 214141.11}, {'field': 'total_amount', 'old_value': 204794.27, 'new_value': 214141.11}, {'field': 'order_count', 'old_value': 15485, 'new_value': 16175}]
2025-06-29 12:02:43,487 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-29 12:02:44,019 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-29 12:02:44,019 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19081.3, 'new_value': 20508.9}, {'field': 'total_amount', 'old_value': 41132.0, 'new_value': 42559.6}, {'field': 'order_count', 'old_value': 4304, 'new_value': 4459}]
2025-06-29 12:02:44,019 - INFO - 开始更新记录 - 表单实例ID: FINST-RTA66X610KDWQ1WBCR65QD898OM630PY6J0CMY1
2025-06-29 12:02:44,550 - INFO - 更新表单数据成功: FINST-RTA66X610KDWQ1WBCR65QD898OM630PY6J0CMY1
2025-06-29 12:02:44,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4700.0, 'new_value': 7000.0}, {'field': 'total_amount', 'old_value': 4700.0, 'new_value': 7000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-29 12:02:44,550 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACME1
2025-06-29 12:02:45,097 - INFO - 更新表单数据成功: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACME1
2025-06-29 12:02:45,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18758.1, 'new_value': 25011.1}, {'field': 'total_amount', 'old_value': 18758.1, 'new_value': 25011.1}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-29 12:02:45,097 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACMF1
2025-06-29 12:02:45,550 - INFO - 更新表单数据成功: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACMF1
2025-06-29 12:02:45,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104025.0, 'new_value': 108186.0}, {'field': 'total_amount', 'old_value': 104025.0, 'new_value': 108186.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-29 12:02:45,550 - INFO - 开始批量插入 1 条新记录
2025-06-29 12:02:45,706 - INFO - 批量插入响应状态码: 200
2025-06-29 12:02:45,706 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 29 Jun 2025 04:02:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E0B69ED-B547-77B1-8AF1-C5B0A3C62040', 'x-acs-trace-id': '1af55321f3d091092d16cc107b0d6c8e', 'etag': '6wyH7OaBCAlvQEUv3eTH7Pw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-29 12:02:45,706 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC1P8OWGZV96GX1F8D3I1Y824WI95HCM3M']}
2025-06-29 12:02:45,706 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-29 12:02:45,706 - INFO - 成功插入的数据ID: ['FINST-5XA66LC1P8OWGZV96GX1F8D3I1Y824WI95HCM3M']
2025-06-29 12:02:48,722 - INFO - 批量插入完成，共 1 条记录
2025-06-29 12:02:48,722 - INFO - 日期 2025-06 处理完成 - 更新: 271 条，插入: 1 条，错误: 0 条
2025-06-29 12:02:48,722 - INFO - 数据同步完成！更新: 271 条，插入: 1 条，错误: 0 条
2025-06-29 12:02:48,722 - INFO - =================同步完成====================
2025-06-29 15:00:03,276 - INFO - =================使用默认全量同步=============
2025-06-29 15:00:05,088 - INFO - MySQL查询成功，共获取 3964 条记录
2025-06-29 15:00:05,088 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 15:00:05,120 - INFO - 开始处理日期: 2025-01
2025-06-29 15:00:05,120 - INFO - Request Parameters - Page 1:
2025-06-29 15:00:05,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:05,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:06,448 - INFO - Response - Page 1:
2025-06-29 15:00:06,651 - INFO - 第 1 页获取到 100 条记录
2025-06-29 15:00:06,651 - INFO - Request Parameters - Page 2:
2025-06-29 15:00:06,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:06,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:07,166 - INFO - Response - Page 2:
2025-06-29 15:00:07,370 - INFO - 第 2 页获取到 100 条记录
2025-06-29 15:00:07,370 - INFO - Request Parameters - Page 3:
2025-06-29 15:00:07,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:07,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:07,932 - INFO - Response - Page 3:
2025-06-29 15:00:08,135 - INFO - 第 3 页获取到 100 条记录
2025-06-29 15:00:08,135 - INFO - Request Parameters - Page 4:
2025-06-29 15:00:08,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:08,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:08,635 - INFO - Response - Page 4:
2025-06-29 15:00:08,838 - INFO - 第 4 页获取到 100 条记录
2025-06-29 15:00:08,838 - INFO - Request Parameters - Page 5:
2025-06-29 15:00:08,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:08,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:09,354 - INFO - Response - Page 5:
2025-06-29 15:00:09,557 - INFO - 第 5 页获取到 100 条记录
2025-06-29 15:00:09,557 - INFO - Request Parameters - Page 6:
2025-06-29 15:00:09,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:09,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:10,041 - INFO - Response - Page 6:
2025-06-29 15:00:10,244 - INFO - 第 6 页获取到 100 条记录
2025-06-29 15:00:10,244 - INFO - Request Parameters - Page 7:
2025-06-29 15:00:10,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:10,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:10,713 - INFO - Response - Page 7:
2025-06-29 15:00:10,916 - INFO - 第 7 页获取到 82 条记录
2025-06-29 15:00:10,916 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 15:00:10,916 - INFO - 获取到 682 条表单数据
2025-06-29 15:00:10,916 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 15:00:10,932 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:10,932 - INFO - 开始处理日期: 2025-02
2025-06-29 15:00:10,932 - INFO - Request Parameters - Page 1:
2025-06-29 15:00:10,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:10,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:11,432 - INFO - Response - Page 1:
2025-06-29 15:00:11,635 - INFO - 第 1 页获取到 100 条记录
2025-06-29 15:00:11,635 - INFO - Request Parameters - Page 2:
2025-06-29 15:00:11,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:11,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:12,151 - INFO - Response - Page 2:
2025-06-29 15:00:12,354 - INFO - 第 2 页获取到 100 条记录
2025-06-29 15:00:12,354 - INFO - Request Parameters - Page 3:
2025-06-29 15:00:12,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:12,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:12,901 - INFO - Response - Page 3:
2025-06-29 15:00:13,104 - INFO - 第 3 页获取到 100 条记录
2025-06-29 15:00:13,104 - INFO - Request Parameters - Page 4:
2025-06-29 15:00:13,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:13,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:13,588 - INFO - Response - Page 4:
2025-06-29 15:00:13,791 - INFO - 第 4 页获取到 100 条记录
2025-06-29 15:00:13,791 - INFO - Request Parameters - Page 5:
2025-06-29 15:00:13,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:13,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:14,338 - INFO - Response - Page 5:
2025-06-29 15:00:14,541 - INFO - 第 5 页获取到 100 条记录
2025-06-29 15:00:14,541 - INFO - Request Parameters - Page 6:
2025-06-29 15:00:14,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:14,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:15,041 - INFO - Response - Page 6:
2025-06-29 15:00:15,244 - INFO - 第 6 页获取到 100 条记录
2025-06-29 15:00:15,244 - INFO - Request Parameters - Page 7:
2025-06-29 15:00:15,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:15,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:15,713 - INFO - Response - Page 7:
2025-06-29 15:00:15,916 - INFO - 第 7 页获取到 70 条记录
2025-06-29 15:00:15,916 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 15:00:15,916 - INFO - 获取到 670 条表单数据
2025-06-29 15:00:15,916 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 15:00:15,932 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:15,932 - INFO - 开始处理日期: 2025-03
2025-06-29 15:00:15,932 - INFO - Request Parameters - Page 1:
2025-06-29 15:00:15,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:15,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:16,494 - INFO - Response - Page 1:
2025-06-29 15:00:16,698 - INFO - 第 1 页获取到 100 条记录
2025-06-29 15:00:16,698 - INFO - Request Parameters - Page 2:
2025-06-29 15:00:16,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:16,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:17,229 - INFO - Response - Page 2:
2025-06-29 15:00:17,432 - INFO - 第 2 页获取到 100 条记录
2025-06-29 15:00:17,432 - INFO - Request Parameters - Page 3:
2025-06-29 15:00:17,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:17,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:17,948 - INFO - Response - Page 3:
2025-06-29 15:00:18,151 - INFO - 第 3 页获取到 100 条记录
2025-06-29 15:00:18,151 - INFO - Request Parameters - Page 4:
2025-06-29 15:00:18,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:18,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:18,713 - INFO - Response - Page 4:
2025-06-29 15:00:18,916 - INFO - 第 4 页获取到 100 条记录
2025-06-29 15:00:18,916 - INFO - Request Parameters - Page 5:
2025-06-29 15:00:18,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:18,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:19,401 - INFO - Response - Page 5:
2025-06-29 15:00:19,604 - INFO - 第 5 页获取到 100 条记录
2025-06-29 15:00:19,604 - INFO - Request Parameters - Page 6:
2025-06-29 15:00:19,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:19,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:20,119 - INFO - Response - Page 6:
2025-06-29 15:00:20,323 - INFO - 第 6 页获取到 100 条记录
2025-06-29 15:00:20,323 - INFO - Request Parameters - Page 7:
2025-06-29 15:00:20,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:20,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:20,776 - INFO - Response - Page 7:
2025-06-29 15:00:20,979 - INFO - 第 7 页获取到 61 条记录
2025-06-29 15:00:20,979 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 15:00:20,979 - INFO - 获取到 661 条表单数据
2025-06-29 15:00:20,979 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 15:00:20,994 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:20,994 - INFO - 开始处理日期: 2025-04
2025-06-29 15:00:20,994 - INFO - Request Parameters - Page 1:
2025-06-29 15:00:20,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:20,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:21,573 - INFO - Response - Page 1:
2025-06-29 15:00:21,776 - INFO - 第 1 页获取到 100 条记录
2025-06-29 15:00:21,776 - INFO - Request Parameters - Page 2:
2025-06-29 15:00:21,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:21,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:22,260 - INFO - Response - Page 2:
2025-06-29 15:00:22,463 - INFO - 第 2 页获取到 100 条记录
2025-06-29 15:00:22,463 - INFO - Request Parameters - Page 3:
2025-06-29 15:00:22,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:22,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:22,979 - INFO - Response - Page 3:
2025-06-29 15:00:23,182 - INFO - 第 3 页获取到 100 条记录
2025-06-29 15:00:23,182 - INFO - Request Parameters - Page 4:
2025-06-29 15:00:23,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:23,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:23,635 - INFO - Response - Page 4:
2025-06-29 15:00:23,838 - INFO - 第 4 页获取到 100 条记录
2025-06-29 15:00:23,838 - INFO - Request Parameters - Page 5:
2025-06-29 15:00:23,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:23,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:24,338 - INFO - Response - Page 5:
2025-06-29 15:00:24,541 - INFO - 第 5 页获取到 100 条记录
2025-06-29 15:00:24,541 - INFO - Request Parameters - Page 6:
2025-06-29 15:00:24,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:24,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:24,994 - INFO - Response - Page 6:
2025-06-29 15:00:25,198 - INFO - 第 6 页获取到 100 条记录
2025-06-29 15:00:25,198 - INFO - Request Parameters - Page 7:
2025-06-29 15:00:25,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:25,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:25,635 - INFO - Response - Page 7:
2025-06-29 15:00:25,838 - INFO - 第 7 页获取到 56 条记录
2025-06-29 15:00:25,838 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 15:00:25,838 - INFO - 获取到 656 条表单数据
2025-06-29 15:00:25,838 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 15:00:25,854 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:25,854 - INFO - 开始处理日期: 2025-05
2025-06-29 15:00:25,854 - INFO - Request Parameters - Page 1:
2025-06-29 15:00:25,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:25,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:26,338 - INFO - Response - Page 1:
2025-06-29 15:00:26,541 - INFO - 第 1 页获取到 100 条记录
2025-06-29 15:00:26,541 - INFO - Request Parameters - Page 2:
2025-06-29 15:00:26,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:26,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:27,041 - INFO - Response - Page 2:
2025-06-29 15:00:27,244 - INFO - 第 2 页获取到 100 条记录
2025-06-29 15:00:27,244 - INFO - Request Parameters - Page 3:
2025-06-29 15:00:27,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:27,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:27,807 - INFO - Response - Page 3:
2025-06-29 15:00:28,010 - INFO - 第 3 页获取到 100 条记录
2025-06-29 15:00:28,010 - INFO - Request Parameters - Page 4:
2025-06-29 15:00:28,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:28,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:28,494 - INFO - Response - Page 4:
2025-06-29 15:00:28,697 - INFO - 第 4 页获取到 100 条记录
2025-06-29 15:00:28,697 - INFO - Request Parameters - Page 5:
2025-06-29 15:00:28,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:28,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:29,229 - INFO - Response - Page 5:
2025-06-29 15:00:29,432 - INFO - 第 5 页获取到 100 条记录
2025-06-29 15:00:29,432 - INFO - Request Parameters - Page 6:
2025-06-29 15:00:29,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:29,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:29,963 - INFO - Response - Page 6:
2025-06-29 15:00:30,166 - INFO - 第 6 页获取到 100 条记录
2025-06-29 15:00:30,166 - INFO - Request Parameters - Page 7:
2025-06-29 15:00:30,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:30,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:30,635 - INFO - Response - Page 7:
2025-06-29 15:00:30,838 - INFO - 第 7 页获取到 65 条记录
2025-06-29 15:00:30,838 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 15:00:30,838 - INFO - 获取到 665 条表单数据
2025-06-29 15:00:30,838 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 15:00:30,854 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:30,854 - INFO - 开始处理日期: 2025-06
2025-06-29 15:00:30,854 - INFO - Request Parameters - Page 1:
2025-06-29 15:00:30,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:30,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:31,354 - INFO - Response - Page 1:
2025-06-29 15:00:31,557 - INFO - 第 1 页获取到 100 条记录
2025-06-29 15:00:31,557 - INFO - Request Parameters - Page 2:
2025-06-29 15:00:31,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:31,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:32,197 - INFO - Response - Page 2:
2025-06-29 15:00:32,401 - INFO - 第 2 页获取到 100 条记录
2025-06-29 15:00:32,401 - INFO - Request Parameters - Page 3:
2025-06-29 15:00:32,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:32,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:32,854 - INFO - Response - Page 3:
2025-06-29 15:00:33,057 - INFO - 第 3 页获取到 100 条记录
2025-06-29 15:00:33,057 - INFO - Request Parameters - Page 4:
2025-06-29 15:00:33,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:33,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:33,572 - INFO - Response - Page 4:
2025-06-29 15:00:33,776 - INFO - 第 4 页获取到 100 条记录
2025-06-29 15:00:33,776 - INFO - Request Parameters - Page 5:
2025-06-29 15:00:33,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:33,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:34,244 - INFO - Response - Page 5:
2025-06-29 15:00:34,447 - INFO - 第 5 页获取到 100 条记录
2025-06-29 15:00:34,447 - INFO - Request Parameters - Page 6:
2025-06-29 15:00:34,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:34,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:35,010 - INFO - Response - Page 6:
2025-06-29 15:00:35,213 - INFO - 第 6 页获取到 100 条记录
2025-06-29 15:00:35,213 - INFO - Request Parameters - Page 7:
2025-06-29 15:00:35,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 15:00:35,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 15:00:35,572 - INFO - Response - Page 7:
2025-06-29 15:00:35,776 - INFO - 第 7 页获取到 30 条记录
2025-06-29 15:00:35,776 - INFO - 查询完成，共获取到 630 条记录
2025-06-29 15:00:35,776 - INFO - 获取到 630 条表单数据
2025-06-29 15:00:35,776 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-06-29 15:00:35,776 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-29 15:00:36,447 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-29 15:00:36,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81085.0, 'new_value': 89484.0}, {'field': 'total_amount', 'old_value': 81085.0, 'new_value': 89484.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-29 15:00:36,447 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-29 15:00:36,901 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-29 15:00:36,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59600.0, 'new_value': 62600.0}, {'field': 'total_amount', 'old_value': 71400.0, 'new_value': 74400.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-29 15:00:36,901 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-29 15:00:37,369 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-29 15:00:37,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21949.7, 'new_value': 22044.7}, {'field': 'total_amount', 'old_value': 21949.7, 'new_value': 22044.7}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-06-29 15:00:37,369 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-29 15:00:37,838 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-29 15:00:37,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165712.4, 'new_value': 182975.2}, {'field': 'total_amount', 'old_value': 193370.2, 'new_value': 210633.0}, {'field': 'order_count', 'old_value': 1758, 'new_value': 1880}]
2025-06-29 15:00:37,838 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5M
2025-06-29 15:00:38,244 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5M
2025-06-29 15:00:38,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22974.0, 'new_value': 27576.44}, {'field': 'total_amount', 'old_value': 97737.0, 'new_value': 102339.44}, {'field': 'order_count', 'old_value': 5748, 'new_value': 5966}]
2025-06-29 15:00:38,244 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-29 15:00:38,697 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-29 15:00:38,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156565.2, 'new_value': 165706.92}, {'field': 'total_amount', 'old_value': 252092.79, 'new_value': 261234.51}, {'field': 'order_count', 'old_value': 12471, 'new_value': 12926}]
2025-06-29 15:00:38,697 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-29 15:00:39,119 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-29 15:00:39,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20661.0, 'new_value': 21551.0}, {'field': 'total_amount', 'old_value': 26483.0, 'new_value': 27373.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 119}]
2025-06-29 15:00:39,119 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-29 15:00:39,541 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-29 15:00:39,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1297000.0, 'new_value': 1352000.0}, {'field': 'total_amount', 'old_value': 1297000.0, 'new_value': 1352000.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 97}]
2025-06-29 15:00:39,541 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-29 15:00:39,994 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-29 15:00:39,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353700.0, 'new_value': 367200.0}, {'field': 'total_amount', 'old_value': 353700.0, 'new_value': 367200.0}, {'field': 'order_count', 'old_value': 9959, 'new_value': 10309}]
2025-06-29 15:00:39,994 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM03
2025-06-29 15:00:40,447 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM03
2025-06-29 15:00:40,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112374.0, 'new_value': 119868.0}, {'field': 'total_amount', 'old_value': 112374.0, 'new_value': 119868.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-06-29 15:00:40,447 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-29 15:00:40,838 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-29 15:00:40,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140736.0, 'new_value': 158389.0}, {'field': 'total_amount', 'old_value': 140736.0, 'new_value': 158389.0}, {'field': 'order_count', 'old_value': 3835, 'new_value': 4245}]
2025-06-29 15:00:40,838 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-29 15:00:41,338 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-29 15:00:41,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21350.0, 'new_value': 21930.0}, {'field': 'total_amount', 'old_value': 21350.0, 'new_value': 21930.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-29 15:00:41,338 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-29 15:00:41,838 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-29 15:00:41,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128760.98, 'new_value': 132327.98}, {'field': 'total_amount', 'old_value': 128760.98, 'new_value': 132327.98}, {'field': 'order_count', 'old_value': 4568, 'new_value': 4657}]
2025-06-29 15:00:41,838 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-29 15:00:42,291 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-29 15:00:42,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105066.0, 'new_value': 110189.0}, {'field': 'total_amount', 'old_value': 105066.0, 'new_value': 110189.0}, {'field': 'order_count', 'old_value': 751, 'new_value': 786}]
2025-06-29 15:00:42,291 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-29 15:00:42,760 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-29 15:00:42,760 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35663.0, 'new_value': 36988.0}, {'field': 'total_amount', 'old_value': 35663.0, 'new_value': 36988.0}, {'field': 'order_count', 'old_value': 3117, 'new_value': 3243}]
2025-06-29 15:00:42,760 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-29 15:00:43,291 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-29 15:00:43,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6661.0, 'new_value': 8860.0}, {'field': 'total_amount', 'old_value': 6661.0, 'new_value': 8860.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-29 15:00:43,291 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-29 15:00:43,729 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-29 15:00:43,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46622.0, 'new_value': 52714.0}, {'field': 'total_amount', 'old_value': 46622.0, 'new_value': 52714.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 53}]
2025-06-29 15:00:43,729 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-29 15:00:44,197 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-29 15:00:44,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8440.8, 'new_value': 10839.8}, {'field': 'total_amount', 'old_value': 8440.8, 'new_value': 10839.8}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-29 15:00:44,197 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-29 15:00:44,666 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-29 15:00:44,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5346395.0, 'new_value': 5682562.0}, {'field': 'total_amount', 'old_value': 5346395.0, 'new_value': 5682562.0}, {'field': 'order_count', 'old_value': 98486, 'new_value': 103766}]
2025-06-29 15:00:44,666 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-29 15:00:45,135 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-29 15:00:45,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86404.0, 'new_value': 90204.0}, {'field': 'total_amount', 'old_value': 86404.0, 'new_value': 90204.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-06-29 15:00:45,150 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-29 15:00:45,479 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-29 15:00:45,479 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254127.12, 'new_value': 272810.1}, {'field': 'total_amount', 'old_value': 254127.12, 'new_value': 272810.1}, {'field': 'order_count', 'old_value': 442, 'new_value': 496}]
2025-06-29 15:00:45,479 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-29 15:00:45,947 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-29 15:00:45,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9794.5, 'new_value': 10475.5}, {'field': 'total_amount', 'old_value': 9794.5, 'new_value': 10475.5}, {'field': 'order_count', 'old_value': 70, 'new_value': 75}]
2025-06-29 15:00:45,947 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-29 15:00:46,432 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-29 15:00:46,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5245410.0, 'new_value': 5395410.0}, {'field': 'total_amount', 'old_value': 5245410.0, 'new_value': 5395410.0}, {'field': 'order_count', 'old_value': 538, 'new_value': 658}]
2025-06-29 15:00:46,432 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-29 15:00:46,885 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-29 15:00:46,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 436273.0, 'new_value': 453305.0}, {'field': 'total_amount', 'old_value': 436273.0, 'new_value': 453305.0}, {'field': 'order_count', 'old_value': 509, 'new_value': 529}]
2025-06-29 15:00:46,885 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-29 15:00:47,291 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-29 15:00:47,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41733.18, 'new_value': 44234.51}, {'field': 'total_amount', 'old_value': 41733.18, 'new_value': 44234.51}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1740}]
2025-06-29 15:00:47,291 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-29 15:00:47,744 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-29 15:00:47,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 554488.0, 'new_value': 594488.0}, {'field': 'total_amount', 'old_value': 554488.0, 'new_value': 594488.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-29 15:00:47,744 - INFO - 日期 2025-06 处理完成 - 更新: 26 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:47,744 - INFO - 数据同步完成！更新: 26 条，插入: 0 条，错误: 0 条
2025-06-29 15:00:47,744 - INFO - =================同步完成====================
2025-06-29 18:00:02,484 - INFO - =================使用默认全量同步=============
2025-06-29 18:00:04,297 - INFO - MySQL查询成功，共获取 3964 条记录
2025-06-29 18:00:04,297 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 18:00:04,328 - INFO - 开始处理日期: 2025-01
2025-06-29 18:00:04,328 - INFO - Request Parameters - Page 1:
2025-06-29 18:00:04,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:04,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:05,640 - INFO - Response - Page 1:
2025-06-29 18:00:05,844 - INFO - 第 1 页获取到 100 条记录
2025-06-29 18:00:05,844 - INFO - Request Parameters - Page 2:
2025-06-29 18:00:05,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:05,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:06,344 - INFO - Response - Page 2:
2025-06-29 18:00:06,547 - INFO - 第 2 页获取到 100 条记录
2025-06-29 18:00:06,547 - INFO - Request Parameters - Page 3:
2025-06-29 18:00:06,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:06,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:06,984 - INFO - Response - Page 3:
2025-06-29 18:00:07,187 - INFO - 第 3 页获取到 100 条记录
2025-06-29 18:00:07,187 - INFO - Request Parameters - Page 4:
2025-06-29 18:00:07,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:07,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:07,687 - INFO - Response - Page 4:
2025-06-29 18:00:07,890 - INFO - 第 4 页获取到 100 条记录
2025-06-29 18:00:07,890 - INFO - Request Parameters - Page 5:
2025-06-29 18:00:07,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:07,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:08,484 - INFO - Response - Page 5:
2025-06-29 18:00:08,687 - INFO - 第 5 页获取到 100 条记录
2025-06-29 18:00:08,687 - INFO - Request Parameters - Page 6:
2025-06-29 18:00:08,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:08,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:09,234 - INFO - Response - Page 6:
2025-06-29 18:00:09,437 - INFO - 第 6 页获取到 100 条记录
2025-06-29 18:00:09,437 - INFO - Request Parameters - Page 7:
2025-06-29 18:00:09,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:09,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:09,906 - INFO - Response - Page 7:
2025-06-29 18:00:10,109 - INFO - 第 7 页获取到 82 条记录
2025-06-29 18:00:10,109 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 18:00:10,109 - INFO - 获取到 682 条表单数据
2025-06-29 18:00:10,109 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 18:00:10,125 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:10,125 - INFO - 开始处理日期: 2025-02
2025-06-29 18:00:10,125 - INFO - Request Parameters - Page 1:
2025-06-29 18:00:10,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:10,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:10,625 - INFO - Response - Page 1:
2025-06-29 18:00:10,828 - INFO - 第 1 页获取到 100 条记录
2025-06-29 18:00:10,828 - INFO - Request Parameters - Page 2:
2025-06-29 18:00:10,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:10,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:11,343 - INFO - Response - Page 2:
2025-06-29 18:00:11,547 - INFO - 第 2 页获取到 100 条记录
2025-06-29 18:00:11,547 - INFO - Request Parameters - Page 3:
2025-06-29 18:00:11,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:11,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:12,031 - INFO - Response - Page 3:
2025-06-29 18:00:12,234 - INFO - 第 3 页获取到 100 条记录
2025-06-29 18:00:12,234 - INFO - Request Parameters - Page 4:
2025-06-29 18:00:12,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:12,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:12,844 - INFO - Response - Page 4:
2025-06-29 18:00:13,047 - INFO - 第 4 页获取到 100 条记录
2025-06-29 18:00:13,047 - INFO - Request Parameters - Page 5:
2025-06-29 18:00:13,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:13,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:13,515 - INFO - Response - Page 5:
2025-06-29 18:00:13,718 - INFO - 第 5 页获取到 100 条记录
2025-06-29 18:00:13,718 - INFO - Request Parameters - Page 6:
2025-06-29 18:00:13,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:13,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:14,265 - INFO - Response - Page 6:
2025-06-29 18:00:14,468 - INFO - 第 6 页获取到 100 条记录
2025-06-29 18:00:14,468 - INFO - Request Parameters - Page 7:
2025-06-29 18:00:14,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:14,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:14,922 - INFO - Response - Page 7:
2025-06-29 18:00:15,125 - INFO - 第 7 页获取到 70 条记录
2025-06-29 18:00:15,125 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 18:00:15,125 - INFO - 获取到 670 条表单数据
2025-06-29 18:00:15,140 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 18:00:15,140 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:15,140 - INFO - 开始处理日期: 2025-03
2025-06-29 18:00:15,140 - INFO - Request Parameters - Page 1:
2025-06-29 18:00:15,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:15,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:15,640 - INFO - Response - Page 1:
2025-06-29 18:00:15,843 - INFO - 第 1 页获取到 100 条记录
2025-06-29 18:00:15,843 - INFO - Request Parameters - Page 2:
2025-06-29 18:00:15,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:15,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:16,500 - INFO - Response - Page 2:
2025-06-29 18:00:16,703 - INFO - 第 2 页获取到 100 条记录
2025-06-29 18:00:16,703 - INFO - Request Parameters - Page 3:
2025-06-29 18:00:16,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:16,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:17,234 - INFO - Response - Page 3:
2025-06-29 18:00:17,437 - INFO - 第 3 页获取到 100 条记录
2025-06-29 18:00:17,437 - INFO - Request Parameters - Page 4:
2025-06-29 18:00:17,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:17,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:18,015 - INFO - Response - Page 4:
2025-06-29 18:00:18,218 - INFO - 第 4 页获取到 100 条记录
2025-06-29 18:00:18,218 - INFO - Request Parameters - Page 5:
2025-06-29 18:00:18,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:18,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:18,718 - INFO - Response - Page 5:
2025-06-29 18:00:18,922 - INFO - 第 5 页获取到 100 条记录
2025-06-29 18:00:18,922 - INFO - Request Parameters - Page 6:
2025-06-29 18:00:18,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:18,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:19,422 - INFO - Response - Page 6:
2025-06-29 18:00:19,625 - INFO - 第 6 页获取到 100 条记录
2025-06-29 18:00:19,625 - INFO - Request Parameters - Page 7:
2025-06-29 18:00:19,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:19,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:20,062 - INFO - Response - Page 7:
2025-06-29 18:00:20,281 - INFO - 第 7 页获取到 61 条记录
2025-06-29 18:00:20,281 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 18:00:20,281 - INFO - 获取到 661 条表单数据
2025-06-29 18:00:20,281 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 18:00:20,297 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:20,297 - INFO - 开始处理日期: 2025-04
2025-06-29 18:00:20,297 - INFO - Request Parameters - Page 1:
2025-06-29 18:00:20,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:20,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:20,765 - INFO - Response - Page 1:
2025-06-29 18:00:20,968 - INFO - 第 1 页获取到 100 条记录
2025-06-29 18:00:20,968 - INFO - Request Parameters - Page 2:
2025-06-29 18:00:20,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:20,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:21,500 - INFO - Response - Page 2:
2025-06-29 18:00:21,703 - INFO - 第 2 页获取到 100 条记录
2025-06-29 18:00:21,703 - INFO - Request Parameters - Page 3:
2025-06-29 18:00:21,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:21,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:22,312 - INFO - Response - Page 3:
2025-06-29 18:00:22,515 - INFO - 第 3 页获取到 100 条记录
2025-06-29 18:00:22,515 - INFO - Request Parameters - Page 4:
2025-06-29 18:00:22,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:22,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:23,109 - INFO - Response - Page 4:
2025-06-29 18:00:23,312 - INFO - 第 4 页获取到 100 条记录
2025-06-29 18:00:23,312 - INFO - Request Parameters - Page 5:
2025-06-29 18:00:23,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:23,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:23,812 - INFO - Response - Page 5:
2025-06-29 18:00:24,015 - INFO - 第 5 页获取到 100 条记录
2025-06-29 18:00:24,015 - INFO - Request Parameters - Page 6:
2025-06-29 18:00:24,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:24,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:24,515 - INFO - Response - Page 6:
2025-06-29 18:00:24,718 - INFO - 第 6 页获取到 100 条记录
2025-06-29 18:00:24,718 - INFO - Request Parameters - Page 7:
2025-06-29 18:00:24,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:24,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:25,172 - INFO - Response - Page 7:
2025-06-29 18:00:25,375 - INFO - 第 7 页获取到 56 条记录
2025-06-29 18:00:25,375 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 18:00:25,375 - INFO - 获取到 656 条表单数据
2025-06-29 18:00:25,375 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 18:00:25,390 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:25,390 - INFO - 开始处理日期: 2025-05
2025-06-29 18:00:25,390 - INFO - Request Parameters - Page 1:
2025-06-29 18:00:25,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:25,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:25,859 - INFO - Response - Page 1:
2025-06-29 18:00:26,062 - INFO - 第 1 页获取到 100 条记录
2025-06-29 18:00:26,062 - INFO - Request Parameters - Page 2:
2025-06-29 18:00:26,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:26,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:26,562 - INFO - Response - Page 2:
2025-06-29 18:00:26,765 - INFO - 第 2 页获取到 100 条记录
2025-06-29 18:00:26,765 - INFO - Request Parameters - Page 3:
2025-06-29 18:00:26,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:26,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:27,343 - INFO - Response - Page 3:
2025-06-29 18:00:27,547 - INFO - 第 3 页获取到 100 条记录
2025-06-29 18:00:27,547 - INFO - Request Parameters - Page 4:
2025-06-29 18:00:27,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:27,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:28,062 - INFO - Response - Page 4:
2025-06-29 18:00:28,265 - INFO - 第 4 页获取到 100 条记录
2025-06-29 18:00:28,265 - INFO - Request Parameters - Page 5:
2025-06-29 18:00:28,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:28,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:28,750 - INFO - Response - Page 5:
2025-06-29 18:00:28,953 - INFO - 第 5 页获取到 100 条记录
2025-06-29 18:00:28,953 - INFO - Request Parameters - Page 6:
2025-06-29 18:00:28,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:28,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:29,546 - INFO - Response - Page 6:
2025-06-29 18:00:29,750 - INFO - 第 6 页获取到 100 条记录
2025-06-29 18:00:29,750 - INFO - Request Parameters - Page 7:
2025-06-29 18:00:29,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:29,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:30,172 - INFO - Response - Page 7:
2025-06-29 18:00:30,375 - INFO - 第 7 页获取到 65 条记录
2025-06-29 18:00:30,375 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 18:00:30,375 - INFO - 获取到 665 条表单数据
2025-06-29 18:00:30,375 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 18:00:30,390 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:30,390 - INFO - 开始处理日期: 2025-06
2025-06-29 18:00:30,390 - INFO - Request Parameters - Page 1:
2025-06-29 18:00:30,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:30,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:30,906 - INFO - Response - Page 1:
2025-06-29 18:00:31,109 - INFO - 第 1 页获取到 100 条记录
2025-06-29 18:00:31,109 - INFO - Request Parameters - Page 2:
2025-06-29 18:00:31,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:31,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:31,578 - INFO - Response - Page 2:
2025-06-29 18:00:31,781 - INFO - 第 2 页获取到 100 条记录
2025-06-29 18:00:31,781 - INFO - Request Parameters - Page 3:
2025-06-29 18:00:31,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:31,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:32,265 - INFO - Response - Page 3:
2025-06-29 18:00:32,468 - INFO - 第 3 页获取到 100 条记录
2025-06-29 18:00:32,468 - INFO - Request Parameters - Page 4:
2025-06-29 18:00:32,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:32,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:33,000 - INFO - Response - Page 4:
2025-06-29 18:00:33,203 - INFO - 第 4 页获取到 100 条记录
2025-06-29 18:00:33,203 - INFO - Request Parameters - Page 5:
2025-06-29 18:00:33,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:33,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:33,718 - INFO - Response - Page 5:
2025-06-29 18:00:33,921 - INFO - 第 5 页获取到 100 条记录
2025-06-29 18:00:33,921 - INFO - Request Parameters - Page 6:
2025-06-29 18:00:33,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:33,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:34,453 - INFO - Response - Page 6:
2025-06-29 18:00:34,656 - INFO - 第 6 页获取到 100 条记录
2025-06-29 18:00:34,656 - INFO - Request Parameters - Page 7:
2025-06-29 18:00:34,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 18:00:34,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 18:00:35,015 - INFO - Response - Page 7:
2025-06-29 18:00:35,218 - INFO - 第 7 页获取到 30 条记录
2025-06-29 18:00:35,218 - INFO - 查询完成，共获取到 630 条记录
2025-06-29 18:00:35,218 - INFO - 获取到 630 条表单数据
2025-06-29 18:00:35,218 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-06-29 18:00:35,234 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXR
2025-06-29 18:00:35,703 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXR
2025-06-29 18:00:35,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89960.8, 'new_value': 98186.26}, {'field': 'total_amount', 'old_value': 137447.54, 'new_value': 145673.0}, {'field': 'order_count', 'old_value': 7087, 'new_value': 7088}]
2025-06-29 18:00:35,703 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBT
2025-06-29 18:00:36,328 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBT
2025-06-29 18:00:36,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24597.0, 'new_value': 26376.0}, {'field': 'total_amount', 'old_value': 24597.0, 'new_value': 26376.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 153}]
2025-06-29 18:00:36,328 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-29 18:00:36,750 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-29 18:00:36,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 295308.4, 'new_value': 298724.4}, {'field': 'total_amount', 'old_value': 295308.4, 'new_value': 298724.4}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-06-29 18:00:36,750 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-29 18:00:37,203 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-29 18:00:37,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228337.0, 'new_value': 228536.0}, {'field': 'total_amount', 'old_value': 240924.0, 'new_value': 241123.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 111}]
2025-06-29 18:00:37,203 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-29 18:00:37,687 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-29 18:00:37,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163627.0, 'new_value': 170948.0}, {'field': 'total_amount', 'old_value': 163627.0, 'new_value': 170948.0}, {'field': 'order_count', 'old_value': 14689, 'new_value': 15488}]
2025-06-29 18:00:37,687 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-29 18:00:38,140 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-29 18:00:38,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16144.0, 'new_value': 17421.0}, {'field': 'total_amount', 'old_value': 18343.0, 'new_value': 19620.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 44}]
2025-06-29 18:00:38,140 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-29 18:00:38,625 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-29 18:00:38,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55420.1, 'new_value': 59378.1}, {'field': 'total_amount', 'old_value': 55420.1, 'new_value': 59378.1}, {'field': 'order_count', 'old_value': 4382, 'new_value': 4642}]
2025-06-29 18:00:38,625 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-29 18:00:39,078 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-29 18:00:39,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175272.0, 'new_value': 175671.0}, {'field': 'total_amount', 'old_value': 175272.0, 'new_value': 175671.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-29 18:00:39,078 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-29 18:00:39,593 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-29 18:00:39,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57845.0, 'new_value': 63445.0}, {'field': 'total_amount', 'old_value': 57845.0, 'new_value': 63445.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-06-29 18:00:39,593 - INFO - 日期 2025-06 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:39,593 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-06-29 18:00:39,593 - INFO - =================同步完成====================
2025-06-29 21:00:03,674 - INFO - =================使用默认全量同步=============
2025-06-29 21:00:05,472 - INFO - MySQL查询成功，共获取 3964 条记录
2025-06-29 21:00:05,472 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-29 21:00:05,503 - INFO - 开始处理日期: 2025-01
2025-06-29 21:00:05,503 - INFO - Request Parameters - Page 1:
2025-06-29 21:00:05,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:05,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:06,597 - INFO - Response - Page 1:
2025-06-29 21:00:06,801 - INFO - 第 1 页获取到 100 条记录
2025-06-29 21:00:06,801 - INFO - Request Parameters - Page 2:
2025-06-29 21:00:06,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:06,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:07,348 - INFO - Response - Page 2:
2025-06-29 21:00:07,551 - INFO - 第 2 页获取到 100 条记录
2025-06-29 21:00:07,551 - INFO - Request Parameters - Page 3:
2025-06-29 21:00:07,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:07,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:08,285 - INFO - Response - Page 3:
2025-06-29 21:00:08,489 - INFO - 第 3 页获取到 100 条记录
2025-06-29 21:00:08,489 - INFO - Request Parameters - Page 4:
2025-06-29 21:00:08,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:08,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:08,989 - INFO - Response - Page 4:
2025-06-29 21:00:09,192 - INFO - 第 4 页获取到 100 条记录
2025-06-29 21:00:09,192 - INFO - Request Parameters - Page 5:
2025-06-29 21:00:09,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:09,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:09,692 - INFO - Response - Page 5:
2025-06-29 21:00:09,896 - INFO - 第 5 页获取到 100 条记录
2025-06-29 21:00:09,896 - INFO - Request Parameters - Page 6:
2025-06-29 21:00:09,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:09,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:10,411 - INFO - Response - Page 6:
2025-06-29 21:00:10,615 - INFO - 第 6 页获取到 100 条记录
2025-06-29 21:00:10,615 - INFO - Request Parameters - Page 7:
2025-06-29 21:00:10,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:10,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:11,083 - INFO - Response - Page 7:
2025-06-29 21:00:11,287 - INFO - 第 7 页获取到 82 条记录
2025-06-29 21:00:11,287 - INFO - 查询完成，共获取到 682 条记录
2025-06-29 21:00:11,287 - INFO - 获取到 682 条表单数据
2025-06-29 21:00:11,287 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-29 21:00:11,302 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:11,302 - INFO - 开始处理日期: 2025-02
2025-06-29 21:00:11,302 - INFO - Request Parameters - Page 1:
2025-06-29 21:00:11,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:11,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:11,834 - INFO - Response - Page 1:
2025-06-29 21:00:12,037 - INFO - 第 1 页获取到 100 条记录
2025-06-29 21:00:12,037 - INFO - Request Parameters - Page 2:
2025-06-29 21:00:12,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:12,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:12,522 - INFO - Response - Page 2:
2025-06-29 21:00:12,725 - INFO - 第 2 页获取到 100 条记录
2025-06-29 21:00:12,725 - INFO - Request Parameters - Page 3:
2025-06-29 21:00:12,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:12,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:13,178 - INFO - Response - Page 3:
2025-06-29 21:00:13,381 - INFO - 第 3 页获取到 100 条记录
2025-06-29 21:00:13,381 - INFO - Request Parameters - Page 4:
2025-06-29 21:00:13,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:13,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:13,881 - INFO - Response - Page 4:
2025-06-29 21:00:14,085 - INFO - 第 4 页获取到 100 条记录
2025-06-29 21:00:14,085 - INFO - Request Parameters - Page 5:
2025-06-29 21:00:14,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:14,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:14,647 - INFO - Response - Page 5:
2025-06-29 21:00:14,851 - INFO - 第 5 页获取到 100 条记录
2025-06-29 21:00:14,851 - INFO - Request Parameters - Page 6:
2025-06-29 21:00:14,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:14,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:15,335 - INFO - Response - Page 6:
2025-06-29 21:00:15,554 - INFO - 第 6 页获取到 100 条记录
2025-06-29 21:00:15,554 - INFO - Request Parameters - Page 7:
2025-06-29 21:00:15,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:15,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:16,023 - INFO - Response - Page 7:
2025-06-29 21:00:16,226 - INFO - 第 7 页获取到 70 条记录
2025-06-29 21:00:16,226 - INFO - 查询完成，共获取到 670 条记录
2025-06-29 21:00:16,226 - INFO - 获取到 670 条表单数据
2025-06-29 21:00:16,226 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-29 21:00:16,242 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:16,242 - INFO - 开始处理日期: 2025-03
2025-06-29 21:00:16,242 - INFO - Request Parameters - Page 1:
2025-06-29 21:00:16,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:16,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:16,773 - INFO - Response - Page 1:
2025-06-29 21:00:16,976 - INFO - 第 1 页获取到 100 条记录
2025-06-29 21:00:16,976 - INFO - Request Parameters - Page 2:
2025-06-29 21:00:16,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:16,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:17,461 - INFO - Response - Page 2:
2025-06-29 21:00:17,664 - INFO - 第 2 页获取到 100 条记录
2025-06-29 21:00:17,664 - INFO - Request Parameters - Page 3:
2025-06-29 21:00:17,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:17,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:18,227 - INFO - Response - Page 3:
2025-06-29 21:00:18,430 - INFO - 第 3 页获取到 100 条记录
2025-06-29 21:00:18,430 - INFO - Request Parameters - Page 4:
2025-06-29 21:00:18,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:18,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:18,868 - INFO - Response - Page 4:
2025-06-29 21:00:19,071 - INFO - 第 4 页获取到 100 条记录
2025-06-29 21:00:19,071 - INFO - Request Parameters - Page 5:
2025-06-29 21:00:19,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:19,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:19,524 - INFO - Response - Page 5:
2025-06-29 21:00:19,728 - INFO - 第 5 页获取到 100 条记录
2025-06-29 21:00:19,728 - INFO - Request Parameters - Page 6:
2025-06-29 21:00:19,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:19,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:20,259 - INFO - Response - Page 6:
2025-06-29 21:00:20,462 - INFO - 第 6 页获取到 100 条记录
2025-06-29 21:00:20,462 - INFO - Request Parameters - Page 7:
2025-06-29 21:00:20,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:20,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:20,853 - INFO - Response - Page 7:
2025-06-29 21:00:21,056 - INFO - 第 7 页获取到 61 条记录
2025-06-29 21:00:21,056 - INFO - 查询完成，共获取到 661 条记录
2025-06-29 21:00:21,056 - INFO - 获取到 661 条表单数据
2025-06-29 21:00:21,056 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-29 21:00:21,072 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:21,072 - INFO - 开始处理日期: 2025-04
2025-06-29 21:00:21,072 - INFO - Request Parameters - Page 1:
2025-06-29 21:00:21,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:21,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:21,603 - INFO - Response - Page 1:
2025-06-29 21:00:21,806 - INFO - 第 1 页获取到 100 条记录
2025-06-29 21:00:21,806 - INFO - Request Parameters - Page 2:
2025-06-29 21:00:21,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:21,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:22,463 - INFO - Response - Page 2:
2025-06-29 21:00:22,666 - INFO - 第 2 页获取到 100 条记录
2025-06-29 21:00:22,666 - INFO - Request Parameters - Page 3:
2025-06-29 21:00:22,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:22,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:23,166 - INFO - Response - Page 3:
2025-06-29 21:00:23,370 - INFO - 第 3 页获取到 100 条记录
2025-06-29 21:00:23,370 - INFO - Request Parameters - Page 4:
2025-06-29 21:00:23,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:23,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:23,839 - INFO - Response - Page 4:
2025-06-29 21:00:24,042 - INFO - 第 4 页获取到 100 条记录
2025-06-29 21:00:24,042 - INFO - Request Parameters - Page 5:
2025-06-29 21:00:24,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:24,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:24,526 - INFO - Response - Page 5:
2025-06-29 21:00:24,730 - INFO - 第 5 页获取到 100 条记录
2025-06-29 21:00:24,730 - INFO - Request Parameters - Page 6:
2025-06-29 21:00:24,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:24,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:25,214 - INFO - Response - Page 6:
2025-06-29 21:00:25,417 - INFO - 第 6 页获取到 100 条记录
2025-06-29 21:00:25,417 - INFO - Request Parameters - Page 7:
2025-06-29 21:00:25,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:25,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:25,839 - INFO - Response - Page 7:
2025-06-29 21:00:26,043 - INFO - 第 7 页获取到 56 条记录
2025-06-29 21:00:26,043 - INFO - 查询完成，共获取到 656 条记录
2025-06-29 21:00:26,043 - INFO - 获取到 656 条表单数据
2025-06-29 21:00:26,043 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-29 21:00:26,058 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:26,058 - INFO - 开始处理日期: 2025-05
2025-06-29 21:00:26,058 - INFO - Request Parameters - Page 1:
2025-06-29 21:00:26,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:26,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:26,590 - INFO - Response - Page 1:
2025-06-29 21:00:26,793 - INFO - 第 1 页获取到 100 条记录
2025-06-29 21:00:26,793 - INFO - Request Parameters - Page 2:
2025-06-29 21:00:26,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:26,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:27,356 - INFO - Response - Page 2:
2025-06-29 21:00:27,559 - INFO - 第 2 页获取到 100 条记录
2025-06-29 21:00:27,559 - INFO - Request Parameters - Page 3:
2025-06-29 21:00:27,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:27,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:28,075 - INFO - Response - Page 3:
2025-06-29 21:00:28,278 - INFO - 第 3 页获取到 100 条记录
2025-06-29 21:00:28,278 - INFO - Request Parameters - Page 4:
2025-06-29 21:00:28,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:28,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:28,872 - INFO - Response - Page 4:
2025-06-29 21:00:29,075 - INFO - 第 4 页获取到 100 条记录
2025-06-29 21:00:29,075 - INFO - Request Parameters - Page 5:
2025-06-29 21:00:29,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:29,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:29,560 - INFO - Response - Page 5:
2025-06-29 21:00:29,763 - INFO - 第 5 页获取到 100 条记录
2025-06-29 21:00:29,763 - INFO - Request Parameters - Page 6:
2025-06-29 21:00:29,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:29,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:30,216 - INFO - Response - Page 6:
2025-06-29 21:00:30,419 - INFO - 第 6 页获取到 100 条记录
2025-06-29 21:00:30,419 - INFO - Request Parameters - Page 7:
2025-06-29 21:00:30,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:30,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:30,810 - INFO - Response - Page 7:
2025-06-29 21:00:31,013 - INFO - 第 7 页获取到 65 条记录
2025-06-29 21:00:31,013 - INFO - 查询完成，共获取到 665 条记录
2025-06-29 21:00:31,013 - INFO - 获取到 665 条表单数据
2025-06-29 21:00:31,013 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-29 21:00:31,029 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:31,029 - INFO - 开始处理日期: 2025-06
2025-06-29 21:00:31,029 - INFO - Request Parameters - Page 1:
2025-06-29 21:00:31,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:31,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:31,451 - INFO - Response - Page 1:
2025-06-29 21:00:31,654 - INFO - 第 1 页获取到 100 条记录
2025-06-29 21:00:31,654 - INFO - Request Parameters - Page 2:
2025-06-29 21:00:31,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:31,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:32,107 - INFO - Response - Page 2:
2025-06-29 21:00:32,311 - INFO - 第 2 页获取到 100 条记录
2025-06-29 21:00:32,311 - INFO - Request Parameters - Page 3:
2025-06-29 21:00:32,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:32,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:32,764 - INFO - Response - Page 3:
2025-06-29 21:00:32,967 - INFO - 第 3 页获取到 100 条记录
2025-06-29 21:00:32,967 - INFO - Request Parameters - Page 4:
2025-06-29 21:00:32,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:32,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:33,483 - INFO - Response - Page 4:
2025-06-29 21:00:33,686 - INFO - 第 4 页获取到 100 条记录
2025-06-29 21:00:33,686 - INFO - Request Parameters - Page 5:
2025-06-29 21:00:33,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:33,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:34,233 - INFO - Response - Page 5:
2025-06-29 21:00:34,436 - INFO - 第 5 页获取到 100 条记录
2025-06-29 21:00:34,436 - INFO - Request Parameters - Page 6:
2025-06-29 21:00:34,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:34,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:34,921 - INFO - Response - Page 6:
2025-06-29 21:00:35,124 - INFO - 第 6 页获取到 100 条记录
2025-06-29 21:00:35,124 - INFO - Request Parameters - Page 7:
2025-06-29 21:00:35,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-29 21:00:35,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-29 21:00:35,562 - INFO - Response - Page 7:
2025-06-29 21:00:35,765 - INFO - 第 7 页获取到 30 条记录
2025-06-29 21:00:35,765 - INFO - 查询完成，共获取到 630 条记录
2025-06-29 21:00:35,765 - INFO - 获取到 630 条表单数据
2025-06-29 21:00:35,765 - INFO - 当前日期 2025-06 有 630 条MySQL数据需要处理
2025-06-29 21:00:35,765 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-29 21:00:36,218 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-29 21:00:36,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131035.0, 'new_value': 133405.0}, {'field': 'total_amount', 'old_value': 131035.0, 'new_value': 133405.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-29 21:00:36,218 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-29 21:00:36,625 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-29 21:00:36,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89633.0, 'new_value': 92065.0}, {'field': 'offline_amount', 'old_value': 113203.0, 'new_value': 117326.0}, {'field': 'total_amount', 'old_value': 202836.0, 'new_value': 209391.0}, {'field': 'order_count', 'old_value': 4293, 'new_value': 4435}]
2025-06-29 21:00:36,640 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-29 21:00:37,109 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-29 21:00:37,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41042.2, 'new_value': 45450.7}, {'field': 'total_amount', 'old_value': 41525.2, 'new_value': 45933.7}, {'field': 'order_count', 'old_value': 178, 'new_value': 180}]
2025-06-29 21:00:37,109 - INFO - 日期 2025-06 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:37,109 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-06-29 21:00:37,109 - INFO - =================同步完成====================
