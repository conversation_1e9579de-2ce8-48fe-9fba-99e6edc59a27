2025-07-20 01:30:33,532 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 01:30:33,532 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 01:30:33,533 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 01:30:33,624 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 0 条记录
2025-07-20 01:30:33,624 - ERROR - 未获取到MySQL数据
2025-07-20 01:31:33,625 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 01:31:33,625 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 01:31:33,625 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 01:31:33,775 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 100 条记录
2025-07-20 01:31:33,775 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 01:31:33,777 - INFO - 开始处理日期: 2025-07-19
2025-07-20 01:31:33,781 - INFO - Request Parameters - Page 1:
2025-07-20 01:31:33,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 01:31:33,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 01:31:41,901 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 238D8DA6-EA77-768C-9226-191AC8E899B6 Response: {'code': 'ServiceUnavailable', 'requestid': '238D8DA6-EA77-768C-9226-191AC8E899B6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 238D8DA6-EA77-768C-9226-191AC8E899B6)
2025-07-20 01:31:41,901 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-20 01:31:41,901 - INFO - 同步完成
2025-07-20 04:30:34,056 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 04:30:34,056 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 04:30:34,057 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 04:30:34,205 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 7 条记录
2025-07-20 04:30:34,206 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 04:30:34,206 - INFO - 开始处理日期: 2025-07-19
2025-07-20 04:30:34,208 - INFO - Request Parameters - Page 1:
2025-07-20 04:30:34,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 04:30:34,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 04:30:42,340 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9220CCBE-6A1F-7F49-8FDD-F6B2E3A2F8FC Response: {'code': 'ServiceUnavailable', 'requestid': '9220CCBE-6A1F-7F49-8FDD-F6B2E3A2F8FC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9220CCBE-6A1F-7F49-8FDD-F6B2E3A2F8FC)
2025-07-20 04:30:42,340 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-20 04:31:42,341 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 04:31:42,341 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 04:31:42,341 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 04:31:42,493 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 129 条记录
2025-07-20 04:31:42,493 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 04:31:42,495 - INFO - 开始处理日期: 2025-07-19
2025-07-20 04:31:42,495 - INFO - Request Parameters - Page 1:
2025-07-20 04:31:42,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 04:31:42,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 04:31:49,877 - INFO - Response - Page 1:
2025-07-20 04:31:49,877 - INFO - 第 1 页获取到 50 条记录
2025-07-20 04:31:50,378 - INFO - Request Parameters - Page 2:
2025-07-20 04:31:50,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 04:31:50,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 04:31:51,115 - INFO - Response - Page 2:
2025-07-20 04:31:51,115 - INFO - 第 2 页获取到 47 条记录
2025-07-20 04:31:51,616 - INFO - 查询完成，共获取到 97 条记录
2025-07-20 04:31:51,616 - INFO - 获取到 97 条表单数据
2025-07-20 04:31:51,618 - INFO - 当前日期 2025-07-19 有 125 条MySQL数据需要处理
2025-07-20 04:31:51,621 - INFO - 开始批量插入 28 条新记录
2025-07-20 04:31:51,854 - INFO - 批量插入响应状态码: 200
2025-07-20 04:31:51,854 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 19 Jul 2025 20:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1356', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4512E06C-E9F7-7B7E-8EE3-75F877D8B812', 'x-acs-trace-id': 'c077679e2f9cafceba9f31c38a3b96ca', 'etag': '11u6BwSVx1Au9ajKjg6SI7w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 04:31:51,854 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3QUJEPADMRB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMSB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMTB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMUB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMVB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMWB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMXB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMYB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMZB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM0C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM1C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM2C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM3C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM4C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM5C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM6C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM7C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM8C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM9C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMAC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMBC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMCC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMDC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMEC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMFC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMGC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMHC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMIC']}
2025-07-20 04:31:51,855 - INFO - 批量插入表单数据成功，批次 1，共 28 条记录
2025-07-20 04:31:51,855 - INFO - 成功插入的数据ID: ['FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3QUJEPADMRB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMSB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMTB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMUB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMVB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMWB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMXB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMYB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMZB', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM0C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM1C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM2C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM3C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM4C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM5C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM6C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM7C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM8C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADM9C', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMAC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMBC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMCC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMDC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMEC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMFC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMGC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMHC', 'FINST-7PF66N914IAXKPTQEY4WS9XF8KIU3RUJEPADMIC']
2025-07-20 04:31:56,856 - INFO - 批量插入完成，共 28 条记录
2025-07-20 04:31:56,856 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 28 条，错误: 0 条
2025-07-20 04:31:56,856 - INFO - 数据同步完成！更新: 0 条，插入: 28 条，错误: 0 条
2025-07-20 04:31:56,856 - INFO - 同步完成
2025-07-20 07:30:33,562 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 07:30:33,562 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 07:30:33,562 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 07:30:33,718 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 7 条记录
2025-07-20 07:30:33,718 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 07:30:33,718 - INFO - 开始处理日期: 2025-07-19
2025-07-20 07:30:33,721 - INFO - Request Parameters - Page 1:
2025-07-20 07:30:33,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 07:30:33,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 07:30:41,408 - INFO - Response - Page 1:
2025-07-20 07:30:41,408 - INFO - 第 1 页获取到 50 条记录
2025-07-20 07:30:41,910 - INFO - Request Parameters - Page 2:
2025-07-20 07:30:41,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 07:30:41,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 07:30:50,019 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F8ECBC16-1AF8-781A-8BEA-CB65AD0F38F8 Response: {'code': 'ServiceUnavailable', 'requestid': 'F8ECBC16-1AF8-781A-8BEA-CB65AD0F38F8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F8ECBC16-1AF8-781A-8BEA-CB65AD0F38F8)
2025-07-20 07:30:50,019 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-20 07:31:50,020 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 07:31:50,020 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 07:31:50,020 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 07:31:50,168 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 129 条记录
2025-07-20 07:31:50,168 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 07:31:50,169 - INFO - 开始处理日期: 2025-07-19
2025-07-20 07:31:50,170 - INFO - Request Parameters - Page 1:
2025-07-20 07:31:50,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 07:31:50,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 07:31:50,924 - INFO - Response - Page 1:
2025-07-20 07:31:50,924 - INFO - 第 1 页获取到 50 条记录
2025-07-20 07:31:51,426 - INFO - Request Parameters - Page 2:
2025-07-20 07:31:51,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 07:31:51,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 07:31:52,098 - INFO - Response - Page 2:
2025-07-20 07:31:52,098 - INFO - 第 2 页获取到 50 条记录
2025-07-20 07:31:52,599 - INFO - Request Parameters - Page 3:
2025-07-20 07:31:52,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 07:31:52,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 07:31:53,281 - INFO - Response - Page 3:
2025-07-20 07:31:53,281 - INFO - 第 3 页获取到 25 条记录
2025-07-20 07:31:53,781 - INFO - 查询完成，共获取到 125 条记录
2025-07-20 07:31:53,781 - INFO - 获取到 125 条表单数据
2025-07-20 07:31:53,783 - INFO - 当前日期 2025-07-19 有 125 条MySQL数据需要处理
2025-07-20 07:31:53,787 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 07:31:53,787 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 07:31:53,787 - INFO - 同步完成
2025-07-20 10:30:33,970 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 10:30:33,970 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 10:30:33,971 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 10:30:34,123 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 98 条记录
2025-07-20 10:30:34,123 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 10:30:34,124 - INFO - 开始处理日期: 2025-07-19
2025-07-20 10:30:34,128 - INFO - Request Parameters - Page 1:
2025-07-20 10:30:34,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 10:30:34,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 10:30:41,997 - INFO - Response - Page 1:
2025-07-20 10:30:41,998 - INFO - 第 1 页获取到 50 条记录
2025-07-20 10:30:42,499 - INFO - Request Parameters - Page 2:
2025-07-20 10:30:42,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 10:30:42,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 10:30:43,350 - INFO - Response - Page 2:
2025-07-20 10:30:43,351 - INFO - 第 2 页获取到 50 条记录
2025-07-20 10:30:43,852 - INFO - Request Parameters - Page 3:
2025-07-20 10:30:43,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 10:30:43,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 10:30:44,543 - INFO - Response - Page 3:
2025-07-20 10:30:44,543 - INFO - 第 3 页获取到 25 条记录
2025-07-20 10:30:45,044 - INFO - 查询完成，共获取到 125 条记录
2025-07-20 10:30:45,044 - INFO - 获取到 125 条表单数据
2025-07-20 10:30:45,048 - INFO - 当前日期 2025-07-19 有 94 条MySQL数据需要处理
2025-07-20 10:30:45,050 - INFO - 开始批量插入 87 条新记录
2025-07-20 10:30:45,320 - INFO - 批量插入响应状态码: 200
2025-07-20 10:30:45,320 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 02:30:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15D06FED-5AEC-7AA1-AD74-6C03AA5CAC7A', 'x-acs-trace-id': '8ec28508eae9582758304f5d86ba992b', 'etag': '2VH4IBMAhhL/Mbh83/UFJoQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 10:30:45,320 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMTB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMUB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMVB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMWB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMXB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMYB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMZB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM0C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM1C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM2C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM3C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM4C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM5C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM6C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM7C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM8C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM9C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMAC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMBC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMCC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMDC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMEC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMFC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMGC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMHC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMIC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMJC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMKC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMLC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMMC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMNC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMOC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMPC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMQC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMRC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMSC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMTC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMUC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMVC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMWC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMXC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMYC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMZC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM0D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM1D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM2D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM3D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM4D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM5D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM6D']}
2025-07-20 10:30:45,320 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-20 10:30:45,320 - INFO - 成功插入的数据ID: ['FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMTB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMUB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMVB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMWB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMXB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMYB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMZB', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM0C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM1C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM2C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM3C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM4C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM5C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM6C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM7C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM8C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM9C', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMAC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMBC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMCC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMDC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMEC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMFC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMGC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMHC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMIC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMJC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMKC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMLC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMMC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMNC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMOC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMPC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMQC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMRC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMSC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMTC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMUC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMVC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMWC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMXC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMYC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDMZC', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM0D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM1D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM2D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM3D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM4D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM5D', 'FINST-TQB66671ZGAXVLLY5813N9GNLSI43EA382BDM6D']
2025-07-20 10:30:50,572 - INFO - 批量插入响应状态码: 200
2025-07-20 10:30:50,572 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 02:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1788', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '53360635-6D1F-791A-84AF-16BB17E9EBAB', 'x-acs-trace-id': '2bf1d5f05d1e1a034a0a8a4095344927', 'etag': '1fgtvcTfnYgXto4ntis6pGQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 10:30:50,572 - INFO - 批量插入响应体: {'result': ['FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMF7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMG7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMH7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMI7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMJ7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMK7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDML7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMM7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMN7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMO7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMP7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMQ7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMR7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMS7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMT7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMU7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMV7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMW7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMX7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMY7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMZ7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM08', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM18', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM28', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM38', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM48', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM58', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM68', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM78', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM88', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM98', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMA8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMB8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMC8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMD8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDME8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMF8']}
2025-07-20 10:30:50,573 - INFO - 批量插入表单数据成功，批次 2，共 37 条记录
2025-07-20 10:30:50,573 - INFO - 成功插入的数据ID: ['FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMF7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMG7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMH7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMI7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMJ7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMK7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDML7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMM7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMN7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMO7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMP7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMQ7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMR7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMS7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMT7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMU7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMV7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMW7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMX7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMY7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMZ7', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM08', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM18', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM28', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM38', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM48', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM58', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM68', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM78', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM88', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDM98', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMA8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMB8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMC8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMD8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDME8', 'FINST-CJ966Q716IAX31XV5P2R295NXPB63CC782BDMF8']
2025-07-20 10:30:55,574 - INFO - 批量插入完成，共 87 条记录
2025-07-20 10:30:55,574 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 87 条，错误: 0 条
2025-07-20 10:30:55,574 - INFO - 数据同步完成！更新: 0 条，插入: 87 条，错误: 0 条
2025-07-20 10:31:55,574 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 10:31:55,574 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 10:31:55,574 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 10:31:55,733 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 392 条记录
2025-07-20 10:31:55,733 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 10:31:55,737 - INFO - 开始处理日期: 2025-07-19
2025-07-20 10:31:55,738 - INFO - Request Parameters - Page 1:
2025-07-20 10:31:55,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 10:31:55,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 10:32:03,859 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 879282C8-3E19-719F-98CB-3C9E6AF269EF Response: {'code': 'ServiceUnavailable', 'requestid': '879282C8-3E19-719F-98CB-3C9E6AF269EF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 879282C8-3E19-719F-98CB-3C9E6AF269EF)
2025-07-20 10:32:03,859 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-20 10:32:03,859 - INFO - 同步完成
2025-07-20 13:30:33,549 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 13:30:33,550 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 13:30:33,550 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 13:30:33,707 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 137 条记录
2025-07-20 13:30:33,707 - INFO - 获取到 2 个日期需要处理: ['2025-07-18', '2025-07-19']
2025-07-20 13:30:33,708 - INFO - 开始处理日期: 2025-07-18
2025-07-20 13:30:33,712 - INFO - Request Parameters - Page 1:
2025-07-20 13:30:33,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:30:33,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:30:41,847 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 23BF88B3-AB4F-7572-8A41-7B7F13D6C48F Response: {'code': 'ServiceUnavailable', 'requestid': '23BF88B3-AB4F-7572-8A41-7B7F13D6C48F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 23BF88B3-AB4F-7572-8A41-7B7F13D6C48F)
2025-07-20 13:30:41,847 - INFO - 开始处理日期: 2025-07-19
2025-07-20 13:30:41,847 - INFO - Request Parameters - Page 1:
2025-07-20 13:30:41,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:30:41,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:30:49,322 - INFO - Response - Page 1:
2025-07-20 13:30:49,322 - INFO - 第 1 页获取到 50 条记录
2025-07-20 13:30:49,823 - INFO - Request Parameters - Page 2:
2025-07-20 13:30:49,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:30:49,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:30:50,557 - INFO - Response - Page 2:
2025-07-20 13:30:50,557 - INFO - 第 2 页获取到 50 条记录
2025-07-20 13:30:51,058 - INFO - Request Parameters - Page 3:
2025-07-20 13:30:51,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:30:51,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:30:51,789 - INFO - Response - Page 3:
2025-07-20 13:30:51,789 - INFO - 第 3 页获取到 50 条记录
2025-07-20 13:30:52,290 - INFO - Request Parameters - Page 4:
2025-07-20 13:30:52,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:30:52,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:30:53,037 - INFO - Response - Page 4:
2025-07-20 13:30:53,037 - INFO - 第 4 页获取到 50 条记录
2025-07-20 13:30:53,537 - INFO - Request Parameters - Page 5:
2025-07-20 13:30:53,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:30:53,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:30:54,105 - INFO - Response - Page 5:
2025-07-20 13:30:54,105 - INFO - 第 5 页获取到 12 条记录
2025-07-20 13:30:54,606 - INFO - 查询完成，共获取到 212 条记录
2025-07-20 13:30:54,606 - INFO - 获取到 212 条表单数据
2025-07-20 13:30:54,609 - INFO - 当前日期 2025-07-19 有 132 条MySQL数据需要处理
2025-07-20 13:30:54,612 - INFO - 开始批量插入 38 条新记录
2025-07-20 13:30:54,856 - INFO - 批量插入响应状态码: 200
2025-07-20 13:30:54,856 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 05:30:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3EA32D53-E93E-78F4-8ACC-9ABFF568D726', 'x-acs-trace-id': '1ebe4d2528edfa6695f8b7e298056c7d', 'etag': '17s04svf7839ewJ5ANQ43mw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 13:30:54,856 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMFA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMGA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMHA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMIA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMJA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMKA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMLA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMMA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMNA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMOA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMPA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMQA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMRA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMSA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMTA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMUA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMVA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMWA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMXA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMYA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMZA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM0B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM1B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM2B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM3B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM4B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM5B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM6B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM7B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM8B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM9B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMAB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMBB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMCB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMDB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMEB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMFB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMGB']}
2025-07-20 13:30:54,857 - INFO - 批量插入表单数据成功，批次 1，共 38 条记录
2025-07-20 13:30:54,857 - INFO - 成功插入的数据ID: ['FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMFA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMGA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMHA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMIA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMJA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMKA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMLA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMMA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMNA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMOA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMPA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMQA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMRA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMSA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMTA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMUA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMVA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMWA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMXA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMYA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMZA', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM0B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM1B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM2B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM3B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM4B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM5B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM6B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM7B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM8B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDM9B', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMAB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMBB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMCB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMDB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMEB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMFB', 'FINST-WBF66B81DIAXX9U57WI6B7QM4K8Y2C1SN8BDMGB']
2025-07-20 13:30:59,858 - INFO - 批量插入完成，共 38 条记录
2025-07-20 13:30:59,858 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 38 条，错误: 0 条
2025-07-20 13:30:59,858 - INFO - 数据同步完成！更新: 0 条，插入: 38 条，错误: 1 条
2025-07-20 13:31:59,859 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 13:31:59,859 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 13:31:59,859 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 13:32:00,022 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 462 条记录
2025-07-20 13:32:00,022 - INFO - 获取到 1 个日期需要处理: ['2025-07-19']
2025-07-20 13:32:00,027 - INFO - 开始处理日期: 2025-07-19
2025-07-20 13:32:00,027 - INFO - Request Parameters - Page 1:
2025-07-20 13:32:00,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:32:00,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:32:00,767 - INFO - Response - Page 1:
2025-07-20 13:32:00,767 - INFO - 第 1 页获取到 50 条记录
2025-07-20 13:32:01,267 - INFO - Request Parameters - Page 2:
2025-07-20 13:32:01,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:32:01,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:32:01,995 - INFO - Response - Page 2:
2025-07-20 13:32:01,995 - INFO - 第 2 页获取到 50 条记录
2025-07-20 13:32:02,496 - INFO - Request Parameters - Page 3:
2025-07-20 13:32:02,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:32:02,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:32:03,283 - INFO - Response - Page 3:
2025-07-20 13:32:03,283 - INFO - 第 3 页获取到 50 条记录
2025-07-20 13:32:03,784 - INFO - Request Parameters - Page 4:
2025-07-20 13:32:03,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:32:03,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:32:04,504 - INFO - Response - Page 4:
2025-07-20 13:32:04,504 - INFO - 第 4 页获取到 50 条记录
2025-07-20 13:32:05,004 - INFO - Request Parameters - Page 5:
2025-07-20 13:32:05,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:32:05,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:32:05,718 - INFO - Response - Page 5:
2025-07-20 13:32:05,718 - INFO - 第 5 页获取到 50 条记录
2025-07-20 13:32:06,219 - INFO - Request Parameters - Page 6:
2025-07-20 13:32:06,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 13:32:06,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 13:32:06,697 - INFO - Response - Page 6:
2025-07-20 13:32:06,698 - INFO - 查询完成，共获取到 250 条记录
2025-07-20 13:32:06,698 - INFO - 获取到 250 条表单数据
2025-07-20 13:32:06,703 - INFO - 当前日期 2025-07-19 有 452 条MySQL数据需要处理
2025-07-20 13:32:06,711 - INFO - 开始批量插入 202 条新记录
2025-07-20 13:32:06,924 - INFO - 批量插入响应状态码: 200
2025-07-20 13:32:06,925 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 05:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '065AB3FE-6B2A-7048-8E43-7B191CFFF001', 'x-acs-trace-id': 'b8e7fe06f0ea037fb18fd1625b679a4f', 'etag': '2Ye9SypzAcx187HwR3NfYGw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 13:32:06,925 - INFO - 批量插入响应体: {'result': ['FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMMI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMNI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMOI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMPI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMQI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMRI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMSI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMTI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMUI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMVI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMWI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMXI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMYI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMZI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM0J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM1J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM2J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM3J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM4J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM5J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM6J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM7J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM8J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM9J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMAJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMBJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMCJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMDJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMEJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMFJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMGJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMHJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMIJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMJJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMKJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMLJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMMJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMNJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMOJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMPJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMQJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMRJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMSJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMTJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMUJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMVJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMWJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMXJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMYJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMZJ']}
2025-07-20 13:32:06,925 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-20 13:32:06,925 - INFO - 成功插入的数据ID: ['FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMMI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMNI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMOI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMPI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMQI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMRI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMSI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMTI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMUI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMVI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMWI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMXI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMYI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMZI', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM0J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM1J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM2J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM3J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM4J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM5J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM6J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM7J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM8J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDM9J', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMAJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMBJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMCJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMDJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMEJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMFJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMGJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMHJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMIJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMJJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMKJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMLJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMMJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMNJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMOJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMPJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMQJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMRJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMSJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMTJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMUJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMVJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMWJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMXJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMYJ', 'FINST-LLF66O71YGAXXSZRA05Q96KZ2NL83FNBP8BDMZJ']
2025-07-20 13:32:12,173 - INFO - 批量插入响应状态码: 200
2025-07-20 13:32:12,173 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 05:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DBB737DC-8C9B-7211-805C-04E124E1FC9F', 'x-acs-trace-id': 'bf2afdd2bf3a32cf8d65c0fde83df9a4', 'etag': '2m2Xu0Xm8GsjjErbXVn/dMw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 13:32:12,173 - INFO - 批量插入响应体: {'result': ['FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM56', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM66', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM76', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM86', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM96', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMA6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMB6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMC6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMD6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDME6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMF6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMG6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMH6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMI6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMJ6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMK6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDML6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMM6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMN6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMO6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMP6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMQ6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMR6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMS6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMT6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMU6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMV6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMW6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMX6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMY6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMZ6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM07', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM17', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM27', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM37', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM47', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM57', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM67', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM77', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM87', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM97', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMA7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMB7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMC7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMD7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDME7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMF7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMG7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMH7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMI7']}
2025-07-20 13:32:12,173 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-20 13:32:12,173 - INFO - 成功插入的数据ID: ['FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM56', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM66', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM76', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM86', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM96', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMA6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMB6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMC6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMD6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDME6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMF6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMG6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMH6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMI6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMJ6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMK6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDML6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMM6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMN6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMO6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMP6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMQ6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMR6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMS6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMT6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMU6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMV6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMW6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMX6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMY6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMZ6', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM07', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM17', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM27', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM37', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM47', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM57', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM67', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM77', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM87', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDM97', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMA7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMB7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMC7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMD7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDME7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMF7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMG7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMH7', 'FINST-49866E71AIAXXS3FF0G3V8HJZAL420PFP8BDMI7']
2025-07-20 13:32:17,431 - INFO - 批量插入响应状态码: 200
2025-07-20 13:32:17,431 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 05:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1B1C5457-709C-7AC6-96EE-0C8402C592F2', 'x-acs-trace-id': 'f6b39ae9d243639ed23b97d6c4d83d84', 'etag': '2rpwzgV2VUOBCvg1YjJz6/Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 13:32:17,431 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM7C', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM8C', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM9C', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMAC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMBC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMCC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMDC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMEC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMFC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMGC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMHC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMIC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMJC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMKC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMLC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMMC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMNC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMOC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMPC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMQC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMRC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMSC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMTC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMUC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMVC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMWC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMXC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMYC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMZC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM0D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM1D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM2D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM3D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM4D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM5D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM6D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM7D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM8D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM9D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMAD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMBD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMCD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMDD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMED', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMFD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMGD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMHD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMID', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMJD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMKD']}
2025-07-20 13:32:17,431 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-20 13:32:17,431 - INFO - 成功插入的数据ID: ['FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM7C', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM8C', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM9C', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMAC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMBC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMCC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMDC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMEC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMFC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMGC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMHC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMIC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMJC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMKC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMLC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMMC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMNC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMOC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMPC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMQC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMRC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMSC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMTC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMUC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMVC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMWC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMXC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMYC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMZC', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM0D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM1D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM2D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM3D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM4D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM5D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM6D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM7D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM8D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDM9D', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMAD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMBD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMCD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMDD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMED', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMFD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMGD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMHD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMID', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMJD', 'FINST-7PF66N913JAXONL37693M5HIN9KN20RJP8BDMKD']
2025-07-20 13:32:22,661 - INFO - 批量插入响应状态码: 200
2025-07-20 13:32:22,661 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 05:32:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9A874C1D-8DEA-7BCF-B735-343A60291C99', 'x-acs-trace-id': '05e56ed37bf300f024f484f5b20c526a', 'etag': '2bw9xcKHGREr25cZtixdBkQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 13:32:22,661 - INFO - 批量插入响应体: {'result': ['FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMXN', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMYN', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMZN', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM0O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM1O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM2O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM3O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM4O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM5O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM6O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM7O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM8O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM9O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMAO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMBO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMCO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMDO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMEO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMFO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMGO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMHO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMIO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMJO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMKO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMLO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMMO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMNO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMOO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMPO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMQO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMRO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMSO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMTO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMUO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMVO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMWO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMXO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMYO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMZO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM0P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM1P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM2P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM3P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM4P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM5P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM6P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM7P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM8P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM9P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMAP']}
2025-07-20 13:32:22,661 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-20 13:32:22,661 - INFO - 成功插入的数据ID: ['FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMXN', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMYN', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMZN', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM0O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM1O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM2O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM3O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM4O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM5O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM6O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM7O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM8O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM9O', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMAO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMBO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMCO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMDO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMEO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMFO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMGO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMHO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMIO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMJO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMKO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMLO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMMO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMNO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMOO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMPO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMQO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMRO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMSO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMTO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMUO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMVO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMWO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMXO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMYO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMZO', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM0P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM1P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM2P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM3P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM4P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM5P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM6P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM7P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM8P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDM9P', 'FINST-6I766IB144AXL1VHE1RXW9ELQ9TX2DSNP8BDMAP']
2025-07-20 13:32:27,825 - INFO - 批量插入响应状态码: 200
2025-07-20 13:32:27,825 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 05:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1239ED3A-26E9-7003-9A79-381F9C0760C3', 'x-acs-trace-id': 'e0edf8bf81ac4f206b6a078a9949d344', 'etag': '1uGZhanseIcm33WAtAtG86g8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 13:32:27,825 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC1KHAXT8N57A1FPD6LNTRJ2XRRP8BDMYD', 'FINST-5XA66LC1KHAXT8N57A1FPD6LNTRJ2XRRP8BDMZD']}
2025-07-20 13:32:27,826 - INFO - 批量插入表单数据成功，批次 5，共 2 条记录
2025-07-20 13:32:27,826 - INFO - 成功插入的数据ID: ['FINST-5XA66LC1KHAXT8N57A1FPD6LNTRJ2XRRP8BDMYD', 'FINST-5XA66LC1KHAXT8N57A1FPD6LNTRJ2XRRP8BDMZD']
2025-07-20 13:32:32,827 - INFO - 批量插入完成，共 202 条记录
2025-07-20 13:32:32,827 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 202 条，错误: 0 条
2025-07-20 13:32:32,827 - INFO - 数据同步完成！更新: 0 条，插入: 202 条，错误: 0 条
2025-07-20 13:32:32,828 - INFO - 同步完成
2025-07-20 16:30:33,644 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 16:30:33,645 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 16:30:33,645 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 16:30:33,803 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 145 条记录
2025-07-20 16:30:33,804 - INFO - 获取到 3 个日期需要处理: ['2025-07-18', '2025-07-19', '2025-07-20']
2025-07-20 16:30:33,805 - INFO - 开始处理日期: 2025-07-18
2025-07-20 16:30:33,810 - INFO - Request Parameters - Page 1:
2025-07-20 16:30:33,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:30:33,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:30:41,943 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 14CCB881-E827-79CA-BA26-9E344FE5A6B1 Response: {'code': 'ServiceUnavailable', 'requestid': '14CCB881-E827-79CA-BA26-9E344FE5A6B1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 14CCB881-E827-79CA-BA26-9E344FE5A6B1)
2025-07-20 16:30:41,943 - INFO - 开始处理日期: 2025-07-19
2025-07-20 16:30:41,943 - INFO - Request Parameters - Page 1:
2025-07-20 16:30:41,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:30:41,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:30:50,066 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2EF0B712-12BF-7024-BBE3-9C35A663F65A Response: {'code': 'ServiceUnavailable', 'requestid': '2EF0B712-12BF-7024-BBE3-9C35A663F65A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2EF0B712-12BF-7024-BBE3-9C35A663F65A)
2025-07-20 16:30:50,066 - INFO - 开始处理日期: 2025-07-20
2025-07-20 16:30:50,066 - INFO - Request Parameters - Page 1:
2025-07-20 16:30:50,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:30:50,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:30:53,350 - INFO - Response - Page 1:
2025-07-20 16:30:53,350 - INFO - 查询完成，共获取到 0 条记录
2025-07-20 16:30:53,351 - INFO - 获取到 0 条表单数据
2025-07-20 16:30:53,351 - INFO - 当前日期 2025-07-20 有 2 条MySQL数据需要处理
2025-07-20 16:30:53,351 - INFO - 开始批量插入 2 条新记录
2025-07-20 16:30:53,512 - INFO - 批量插入响应状态码: 200
2025-07-20 16:30:53,512 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 08:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EF9740C3-1E62-79A7-AEB5-E6EDC2A8E325', 'x-acs-trace-id': 'f08885164b7bea84d3d77055ffa09e7d', 'etag': '1qpALKE4k6uklkRIU42ViKg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 16:30:53,512 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71LABXR2SZ56HCM7774SCL37E83FBDMG', 'FINST-00D66K71LABXR2SZ56HCM7774SCL37E83FBDMH']}
2025-07-20 16:30:53,513 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-20 16:30:53,513 - INFO - 成功插入的数据ID: ['FINST-00D66K71LABXR2SZ56HCM7774SCL37E83FBDMG', 'FINST-00D66K71LABXR2SZ56HCM7774SCL37E83FBDMH']
2025-07-20 16:30:58,514 - INFO - 批量插入完成，共 2 条记录
2025-07-20 16:30:58,514 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-20 16:30:58,514 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-07-20 16:31:58,515 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 16:31:58,515 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 16:31:58,515 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 16:31:58,677 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 470 条记录
2025-07-20 16:31:58,677 - INFO - 获取到 2 个日期需要处理: ['2025-07-19', '2025-07-20']
2025-07-20 16:31:58,682 - INFO - 开始处理日期: 2025-07-19
2025-07-20 16:31:58,682 - INFO - Request Parameters - Page 1:
2025-07-20 16:31:58,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:31:58,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:31:59,465 - INFO - Response - Page 1:
2025-07-20 16:31:59,465 - INFO - 第 1 页获取到 50 条记录
2025-07-20 16:31:59,966 - INFO - Request Parameters - Page 2:
2025-07-20 16:31:59,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:31:59,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:00,761 - INFO - Response - Page 2:
2025-07-20 16:32:00,761 - INFO - 第 2 页获取到 50 条记录
2025-07-20 16:32:01,261 - INFO - Request Parameters - Page 3:
2025-07-20 16:32:01,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:01,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:01,934 - INFO - Response - Page 3:
2025-07-20 16:32:01,934 - INFO - 第 3 页获取到 50 条记录
2025-07-20 16:32:02,434 - INFO - Request Parameters - Page 4:
2025-07-20 16:32:02,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:02,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:03,136 - INFO - Response - Page 4:
2025-07-20 16:32:03,136 - INFO - 第 4 页获取到 50 条记录
2025-07-20 16:32:03,636 - INFO - Request Parameters - Page 5:
2025-07-20 16:32:03,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:03,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:04,389 - INFO - Response - Page 5:
2025-07-20 16:32:04,389 - INFO - 第 5 页获取到 50 条记录
2025-07-20 16:32:04,890 - INFO - Request Parameters - Page 6:
2025-07-20 16:32:04,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:04,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:05,569 - INFO - Response - Page 6:
2025-07-20 16:32:05,570 - INFO - 第 6 页获取到 50 条记录
2025-07-20 16:32:06,070 - INFO - Request Parameters - Page 7:
2025-07-20 16:32:06,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:06,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:06,803 - INFO - Response - Page 7:
2025-07-20 16:32:06,803 - INFO - 第 7 页获取到 50 条记录
2025-07-20 16:32:07,304 - INFO - Request Parameters - Page 8:
2025-07-20 16:32:07,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:07,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:07,994 - INFO - Response - Page 8:
2025-07-20 16:32:07,994 - INFO - 第 8 页获取到 50 条记录
2025-07-20 16:32:08,494 - INFO - Request Parameters - Page 9:
2025-07-20 16:32:08,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:08,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:09,137 - INFO - Response - Page 9:
2025-07-20 16:32:09,137 - INFO - 第 9 页获取到 50 条记录
2025-07-20 16:32:09,637 - INFO - Request Parameters - Page 10:
2025-07-20 16:32:09,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:09,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:10,118 - INFO - Response - Page 10:
2025-07-20 16:32:10,118 - INFO - 第 10 页获取到 2 条记录
2025-07-20 16:32:10,618 - INFO - 查询完成，共获取到 452 条记录
2025-07-20 16:32:10,618 - INFO - 获取到 452 条表单数据
2025-07-20 16:32:10,627 - INFO - 当前日期 2025-07-19 有 458 条MySQL数据需要处理
2025-07-20 16:32:10,639 - INFO - 开始批量插入 6 条新记录
2025-07-20 16:32:10,797 - INFO - 批量插入响应状态码: 200
2025-07-20 16:32:10,797 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 08:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2A7B85AC-B0F0-73F6-BDB4-14A82FB3FCBE', 'x-acs-trace-id': '95e8178489d5c7954d958d2c7ef04a09', 'etag': '3kKkwTjyM/gIHepXh17g78Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 16:32:10,797 - INFO - 批量插入响应体: {'result': ['FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM5F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM6F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM7F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM8F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM9F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDMAF']}
2025-07-20 16:32:10,797 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-07-20 16:32:10,797 - INFO - 成功插入的数据ID: ['FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM5F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM6F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM7F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM8F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDM9F', 'FINST-K8C66U611HAX5JKX6VUDV6OQIUDT3V0W4FBDMAF']
2025-07-20 16:32:15,798 - INFO - 批量插入完成，共 6 条记录
2025-07-20 16:32:15,798 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-07-20 16:32:15,798 - INFO - 开始处理日期: 2025-07-20
2025-07-20 16:32:15,798 - INFO - Request Parameters - Page 1:
2025-07-20 16:32:15,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 16:32:15,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 16:32:16,262 - INFO - Response - Page 1:
2025-07-20 16:32:16,262 - INFO - 第 1 页获取到 2 条记录
2025-07-20 16:32:16,764 - INFO - 查询完成，共获取到 2 条记录
2025-07-20 16:32:16,764 - INFO - 获取到 2 条表单数据
2025-07-20 16:32:16,765 - INFO - 当前日期 2025-07-20 有 2 条MySQL数据需要处理
2025-07-20 16:32:16,765 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 16:32:16,765 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 0 条
2025-07-20 16:32:16,765 - INFO - 同步完成
2025-07-20 19:30:34,525 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 19:30:34,525 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 19:30:34,525 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 19:30:34,683 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 146 条记录
2025-07-20 19:30:34,683 - INFO - 获取到 3 个日期需要处理: ['2025-07-18', '2025-07-19', '2025-07-20']
2025-07-20 19:30:34,685 - INFO - 开始处理日期: 2025-07-18
2025-07-20 19:30:34,689 - INFO - Request Parameters - Page 1:
2025-07-20 19:30:34,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:34,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:41,821 - INFO - Response - Page 1:
2025-07-20 19:30:41,821 - INFO - 第 1 页获取到 50 条记录
2025-07-20 19:30:42,322 - INFO - Request Parameters - Page 2:
2025-07-20 19:30:42,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:42,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:50,450 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8077D51-6BF0-737E-B845-CB953E65DCA2 Response: {'code': 'ServiceUnavailable', 'requestid': 'B8077D51-6BF0-737E-B845-CB953E65DCA2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8077D51-6BF0-737E-B845-CB953E65DCA2)
2025-07-20 19:30:50,450 - INFO - 开始处理日期: 2025-07-19
2025-07-20 19:30:50,450 - INFO - Request Parameters - Page 1:
2025-07-20 19:30:50,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:50,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:51,185 - INFO - Response - Page 1:
2025-07-20 19:30:51,185 - INFO - 第 1 页获取到 50 条记录
2025-07-20 19:30:51,686 - INFO - Request Parameters - Page 2:
2025-07-20 19:30:51,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:51,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:53,323 - INFO - Response - Page 2:
2025-07-20 19:30:53,323 - INFO - 第 2 页获取到 50 条记录
2025-07-20 19:30:53,824 - INFO - Request Parameters - Page 3:
2025-07-20 19:30:53,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:53,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:54,541 - INFO - Response - Page 3:
2025-07-20 19:30:54,542 - INFO - 第 3 页获取到 50 条记录
2025-07-20 19:30:55,043 - INFO - Request Parameters - Page 4:
2025-07-20 19:30:55,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:55,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:55,715 - INFO - Response - Page 4:
2025-07-20 19:30:55,716 - INFO - 第 4 页获取到 50 条记录
2025-07-20 19:30:56,217 - INFO - Request Parameters - Page 5:
2025-07-20 19:30:56,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:56,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:56,871 - INFO - Response - Page 5:
2025-07-20 19:30:56,871 - INFO - 第 5 页获取到 50 条记录
2025-07-20 19:30:57,372 - INFO - Request Parameters - Page 6:
2025-07-20 19:30:57,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:57,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:58,064 - INFO - Response - Page 6:
2025-07-20 19:30:58,064 - INFO - 第 6 页获取到 50 条记录
2025-07-20 19:30:58,565 - INFO - Request Parameters - Page 7:
2025-07-20 19:30:58,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:58,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:30:59,446 - INFO - Response - Page 7:
2025-07-20 19:30:59,446 - INFO - 第 7 页获取到 50 条记录
2025-07-20 19:30:59,946 - INFO - Request Parameters - Page 8:
2025-07-20 19:30:59,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:30:59,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:31:00,633 - INFO - Response - Page 8:
2025-07-20 19:31:00,633 - INFO - 第 8 页获取到 50 条记录
2025-07-20 19:31:01,133 - INFO - Request Parameters - Page 9:
2025-07-20 19:31:01,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:31:01,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:31:01,894 - INFO - Response - Page 9:
2025-07-20 19:31:01,895 - INFO - 第 9 页获取到 50 条记录
2025-07-20 19:31:02,396 - INFO - Request Parameters - Page 10:
2025-07-20 19:31:02,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:31:02,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:31:03,065 - INFO - Response - Page 10:
2025-07-20 19:31:03,066 - INFO - 第 10 页获取到 8 条记录
2025-07-20 19:31:03,567 - INFO - 查询完成，共获取到 458 条记录
2025-07-20 19:31:03,567 - INFO - 获取到 458 条表单数据
2025-07-20 19:31:03,575 - INFO - 当前日期 2025-07-19 有 139 条MySQL数据需要处理
2025-07-20 19:31:03,579 - INFO - 开始批量插入 1 条新记录
2025-07-20 19:31:03,738 - INFO - 批量插入响应状态码: 200
2025-07-20 19:31:03,738 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 11:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '060EFA15-662E-7723-A4E0-10E7EEA80E27', 'x-acs-trace-id': '201cbaf30815a569899d20a586ec5959', 'etag': '6S4TNufDrALpyoik3LfhlMA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 19:31:03,738 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71FHAXRHE39P8R16NFWBLD2ZEXILBDML2']}
2025-07-20 19:31:03,738 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-20 19:31:03,738 - INFO - 成功插入的数据ID: ['FINST-L5766E71FHAXRHE39P8R16NFWBLD2ZEXILBDML2']
2025-07-20 19:31:08,741 - INFO - 批量插入完成，共 1 条记录
2025-07-20 19:31:08,741 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-20 19:31:08,741 - INFO - 开始处理日期: 2025-07-20
2025-07-20 19:31:08,741 - INFO - Request Parameters - Page 1:
2025-07-20 19:31:08,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:31:08,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:31:09,229 - INFO - Response - Page 1:
2025-07-20 19:31:09,229 - INFO - 第 1 页获取到 2 条记录
2025-07-20 19:31:09,729 - INFO - 查询完成，共获取到 2 条记录
2025-07-20 19:31:09,729 - INFO - 获取到 2 条表单数据
2025-07-20 19:31:09,730 - INFO - 当前日期 2025-07-20 有 2 条MySQL数据需要处理
2025-07-20 19:31:09,730 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 19:31:09,730 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-07-20 19:32:09,755 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 19:32:09,755 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 19:32:09,755 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 19:32:09,917 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 471 条记录
2025-07-20 19:32:09,918 - INFO - 获取到 2 个日期需要处理: ['2025-07-19', '2025-07-20']
2025-07-20 19:32:09,922 - INFO - 开始处理日期: 2025-07-19
2025-07-20 19:32:09,923 - INFO - Request Parameters - Page 1:
2025-07-20 19:32:09,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:09,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:10,649 - INFO - Response - Page 1:
2025-07-20 19:32:10,649 - INFO - 第 1 页获取到 50 条记录
2025-07-20 19:32:11,150 - INFO - Request Parameters - Page 2:
2025-07-20 19:32:11,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:11,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:11,853 - INFO - Response - Page 2:
2025-07-20 19:32:11,853 - INFO - 第 2 页获取到 50 条记录
2025-07-20 19:32:12,354 - INFO - Request Parameters - Page 3:
2025-07-20 19:32:12,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:12,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:13,113 - INFO - Response - Page 3:
2025-07-20 19:32:13,113 - INFO - 第 3 页获取到 50 条记录
2025-07-20 19:32:13,615 - INFO - Request Parameters - Page 4:
2025-07-20 19:32:13,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:13,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:14,348 - INFO - Response - Page 4:
2025-07-20 19:32:14,348 - INFO - 第 4 页获取到 50 条记录
2025-07-20 19:32:14,849 - INFO - Request Parameters - Page 5:
2025-07-20 19:32:14,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:14,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:15,534 - INFO - Response - Page 5:
2025-07-20 19:32:15,534 - INFO - 第 5 页获取到 50 条记录
2025-07-20 19:32:16,035 - INFO - Request Parameters - Page 6:
2025-07-20 19:32:16,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:16,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:16,708 - INFO - Response - Page 6:
2025-07-20 19:32:16,708 - INFO - 第 6 页获取到 50 条记录
2025-07-20 19:32:17,209 - INFO - Request Parameters - Page 7:
2025-07-20 19:32:17,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:17,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:17,985 - INFO - Response - Page 7:
2025-07-20 19:32:17,985 - INFO - 第 7 页获取到 50 条记录
2025-07-20 19:32:18,487 - INFO - Request Parameters - Page 8:
2025-07-20 19:32:18,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:18,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:19,141 - INFO - Response - Page 8:
2025-07-20 19:32:19,141 - INFO - 第 8 页获取到 50 条记录
2025-07-20 19:32:19,641 - INFO - Request Parameters - Page 9:
2025-07-20 19:32:19,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:19,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:20,431 - INFO - Response - Page 9:
2025-07-20 19:32:20,431 - INFO - 第 9 页获取到 50 条记录
2025-07-20 19:32:20,932 - INFO - Request Parameters - Page 10:
2025-07-20 19:32:20,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:20,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:21,500 - INFO - Response - Page 10:
2025-07-20 19:32:21,501 - INFO - 第 10 页获取到 9 条记录
2025-07-20 19:32:22,001 - INFO - 查询完成，共获取到 459 条记录
2025-07-20 19:32:22,001 - INFO - 获取到 459 条表单数据
2025-07-20 19:32:22,009 - INFO - 当前日期 2025-07-19 有 459 条MySQL数据需要处理
2025-07-20 19:32:22,020 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 19:32:22,020 - INFO - 开始处理日期: 2025-07-20
2025-07-20 19:32:22,021 - INFO - Request Parameters - Page 1:
2025-07-20 19:32:22,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 19:32:22,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 19:32:22,506 - INFO - Response - Page 1:
2025-07-20 19:32:22,506 - INFO - 第 1 页获取到 2 条记录
2025-07-20 19:32:23,007 - INFO - 查询完成，共获取到 2 条记录
2025-07-20 19:32:23,007 - INFO - 获取到 2 条表单数据
2025-07-20 19:32:23,008 - INFO - 当前日期 2025-07-20 有 2 条MySQL数据需要处理
2025-07-20 19:32:23,008 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 19:32:23,008 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 19:32:23,008 - INFO - 同步完成
2025-07-20 22:30:34,586 - INFO - 使用默认增量同步（当天更新数据）
2025-07-20 22:30:34,586 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-20 22:30:34,586 - INFO - 查询参数: ('2025-07-20',)
2025-07-20 22:30:34,742 - INFO - MySQL查询成功，增量数据（日期: 2025-07-20），共获取 216 条记录
2025-07-20 22:30:34,742 - INFO - 获取到 4 个日期需要处理: ['2025-07-03', '2025-07-18', '2025-07-19', '2025-07-20']
2025-07-20 22:30:34,742 - INFO - 开始处理日期: 2025-07-03
2025-07-20 22:30:34,742 - INFO - Request Parameters - Page 1:
2025-07-20 22:30:34,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:34,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751472000000, 1751558399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:42,933 - ERROR - 处理日期 2025-07-03 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E7314AF9-96C1-7B49-99EA-52AB87053E5B Response: {'code': 'ServiceUnavailable', 'requestid': 'E7314AF9-96C1-7B49-99EA-52AB87053E5B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E7314AF9-96C1-7B49-99EA-52AB87053E5B)
2025-07-20 22:30:42,933 - INFO - 开始处理日期: 2025-07-18
2025-07-20 22:30:42,933 - INFO - Request Parameters - Page 1:
2025-07-20 22:30:42,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:42,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:46,387 - INFO - Response - Page 1:
2025-07-20 22:30:46,387 - INFO - 第 1 页获取到 50 条记录
2025-07-20 22:30:46,888 - INFO - Request Parameters - Page 2:
2025-07-20 22:30:46,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:46,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:54,344 - INFO - Response - Page 2:
2025-07-20 22:30:54,344 - INFO - 第 2 页获取到 50 条记录
2025-07-20 22:30:54,844 - INFO - Request Parameters - Page 3:
2025-07-20 22:30:54,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:54,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:55,547 - INFO - Response - Page 3:
2025-07-20 22:30:55,547 - INFO - 第 3 页获取到 50 条记录
2025-07-20 22:30:56,047 - INFO - Request Parameters - Page 4:
2025-07-20 22:30:56,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:56,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:56,798 - INFO - Response - Page 4:
2025-07-20 22:30:56,798 - INFO - 第 4 页获取到 50 条记录
2025-07-20 22:30:57,314 - INFO - Request Parameters - Page 5:
2025-07-20 22:30:57,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:57,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:57,970 - INFO - Response - Page 5:
2025-07-20 22:30:57,970 - INFO - 第 5 页获取到 50 条记录
2025-07-20 22:30:58,486 - INFO - Request Parameters - Page 6:
2025-07-20 22:30:58,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:58,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:30:59,189 - INFO - Response - Page 6:
2025-07-20 22:30:59,189 - INFO - 第 6 页获取到 50 条记录
2025-07-20 22:30:59,690 - INFO - Request Parameters - Page 7:
2025-07-20 22:30:59,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:30:59,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:00,377 - INFO - Response - Page 7:
2025-07-20 22:31:00,377 - INFO - 第 7 页获取到 50 条记录
2025-07-20 22:31:00,878 - INFO - Request Parameters - Page 8:
2025-07-20 22:31:00,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:00,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:01,612 - INFO - Response - Page 8:
2025-07-20 22:31:01,612 - INFO - 第 8 页获取到 50 条记录
2025-07-20 22:31:02,128 - INFO - Request Parameters - Page 9:
2025-07-20 22:31:02,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:02,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:02,816 - INFO - Response - Page 9:
2025-07-20 22:31:02,816 - INFO - 第 9 页获取到 50 条记录
2025-07-20 22:31:03,316 - INFO - Request Parameters - Page 10:
2025-07-20 22:31:03,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:03,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:03,894 - INFO - Response - Page 10:
2025-07-20 22:31:03,894 - INFO - 第 10 页获取到 16 条记录
2025-07-20 22:31:04,395 - INFO - 查询完成，共获取到 466 条记录
2025-07-20 22:31:04,395 - INFO - 获取到 466 条表单数据
2025-07-20 22:31:04,395 - INFO - 当前日期 2025-07-18 有 1 条MySQL数据需要处理
2025-07-20 22:31:04,395 - INFO - 开始批量插入 1 条新记录
2025-07-20 22:31:04,535 - INFO - 批量插入响应状态码: 200
2025-07-20 22:31:04,535 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 14:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B0BD9412-3E60-730B-87DB-4E9E3A7604DE', 'x-acs-trace-id': '68bfa3f894544d35a23f67289dff7e96', 'etag': '65AztmG0P1gJ9LQE+sMBxLA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 22:31:04,535 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB1QHAXMX3B6RLRLCYI59LU3H2CYRBDMGE']}
2025-07-20 22:31:04,535 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-20 22:31:04,535 - INFO - 成功插入的数据ID: ['FINST-EZD66RB1QHAXMX3B6RLRLCYI59LU3H2CYRBDMGE']
2025-07-20 22:31:09,553 - INFO - 批量插入完成，共 1 条记录
2025-07-20 22:31:09,553 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-20 22:31:09,553 - INFO - 开始处理日期: 2025-07-19
2025-07-20 22:31:09,553 - INFO - Request Parameters - Page 1:
2025-07-20 22:31:09,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:09,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:10,272 - INFO - Response - Page 1:
2025-07-20 22:31:10,272 - INFO - 第 1 页获取到 50 条记录
2025-07-20 22:31:10,788 - INFO - Request Parameters - Page 2:
2025-07-20 22:31:10,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:10,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:11,491 - INFO - Response - Page 2:
2025-07-20 22:31:11,491 - INFO - 第 2 页获取到 50 条记录
2025-07-20 22:31:12,007 - INFO - Request Parameters - Page 3:
2025-07-20 22:31:12,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:12,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:12,648 - INFO - Response - Page 3:
2025-07-20 22:31:12,648 - INFO - 第 3 页获取到 50 条记录
2025-07-20 22:31:13,164 - INFO - Request Parameters - Page 4:
2025-07-20 22:31:13,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:13,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:13,867 - INFO - Response - Page 4:
2025-07-20 22:31:13,867 - INFO - 第 4 页获取到 50 条记录
2025-07-20 22:31:14,383 - INFO - Request Parameters - Page 5:
2025-07-20 22:31:14,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:14,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:15,086 - INFO - Response - Page 5:
2025-07-20 22:31:15,086 - INFO - 第 5 页获取到 50 条记录
2025-07-20 22:31:15,602 - INFO - Request Parameters - Page 6:
2025-07-20 22:31:15,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:15,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:16,274 - INFO - Response - Page 6:
2025-07-20 22:31:16,274 - INFO - 第 6 页获取到 50 条记录
2025-07-20 22:31:16,790 - INFO - Request Parameters - Page 7:
2025-07-20 22:31:16,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:16,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:17,493 - INFO - Response - Page 7:
2025-07-20 22:31:17,493 - INFO - 第 7 页获取到 50 条记录
2025-07-20 22:31:17,994 - INFO - Request Parameters - Page 8:
2025-07-20 22:31:17,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:17,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:18,681 - INFO - Response - Page 8:
2025-07-20 22:31:18,681 - INFO - 第 8 页获取到 50 条记录
2025-07-20 22:31:19,182 - INFO - Request Parameters - Page 9:
2025-07-20 22:31:19,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:19,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:19,979 - INFO - Response - Page 9:
2025-07-20 22:31:19,979 - INFO - 第 9 页获取到 50 条记录
2025-07-20 22:31:20,479 - INFO - Request Parameters - Page 10:
2025-07-20 22:31:20,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:20,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:21,011 - INFO - Response - Page 10:
2025-07-20 22:31:21,011 - INFO - 第 10 页获取到 9 条记录
2025-07-20 22:31:21,526 - INFO - 查询完成，共获取到 459 条记录
2025-07-20 22:31:21,526 - INFO - 获取到 459 条表单数据
2025-07-20 22:31:21,526 - INFO - 当前日期 2025-07-19 有 144 条MySQL数据需要处理
2025-07-20 22:31:21,526 - INFO - 开始批量插入 5 条新记录
2025-07-20 22:31:21,683 - INFO - 批量插入响应状态码: 200
2025-07-20 22:31:21,683 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 14:31:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2BC2C34B-CAAF-75B2-953E-01523F1ABA6F', 'x-acs-trace-id': '6f550b355f624662979cc00ccdf151e2', 'etag': '2/lQqPgzW/AfOYnvTSKY+Gw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 22:31:21,683 - INFO - 批量插入响应体: {'result': ['FINST-SE766YC1D4AXECNH8V31EB4O0KIC3GAPYRBDM5F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3GAPYRBDM6F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3GAPYRBDM7F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3HAPYRBDM8F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3HAPYRBDM9F']}
2025-07-20 22:31:21,683 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-07-20 22:31:21,683 - INFO - 成功插入的数据ID: ['FINST-SE766YC1D4AXECNH8V31EB4O0KIC3GAPYRBDM5F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3GAPYRBDM6F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3GAPYRBDM7F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3HAPYRBDM8F', 'FINST-SE766YC1D4AXECNH8V31EB4O0KIC3HAPYRBDM9F']
2025-07-20 22:31:26,700 - INFO - 批量插入完成，共 5 条记录
2025-07-20 22:31:26,700 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-07-20 22:31:26,700 - INFO - 开始处理日期: 2025-07-20
2025-07-20 22:31:26,700 - INFO - Request Parameters - Page 1:
2025-07-20 22:31:26,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:31:26,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:31:27,169 - INFO - Response - Page 1:
2025-07-20 22:31:27,169 - INFO - 第 1 页获取到 2 条记录
2025-07-20 22:31:27,685 - INFO - 查询完成，共获取到 2 条记录
2025-07-20 22:31:27,685 - INFO - 获取到 2 条表单数据
2025-07-20 22:31:27,685 - INFO - 当前日期 2025-07-20 有 64 条MySQL数据需要处理
2025-07-20 22:31:27,685 - INFO - 开始批量插入 62 条新记录
2025-07-20 22:31:27,966 - INFO - 批量插入响应状态码: 200
2025-07-20 22:31:27,966 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 14:31:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DF69511B-7FA7-7382-B673-FCC0AB6D3AF6', 'x-acs-trace-id': 'ba8118e666aa051b918b88c1ce0dca7e', 'etag': '2/78cZTxAoyn7ztAOVldqMQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 22:31:27,966 - INFO - 批量插入响应体: {'result': ['FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDM9C', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMAC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMBC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMCC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMDC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMEC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMFC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMGC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMHC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMIC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMJC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMKC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMLC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMMC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMNC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMOC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMPC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMQC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMRC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMSC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMTC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMUC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMVC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMWC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMXC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMYC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMZC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM0D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM1D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM2D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM3D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM4D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM5D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM6D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM7D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM8D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM9D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMAD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMBD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMCD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMDD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMED', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMFD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMGD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMHD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMID', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMJD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMKD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMLD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMMD']}
2025-07-20 22:31:27,966 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-20 22:31:27,966 - INFO - 成功插入的数据ID: ['FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDM9C', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMAC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMBC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMCC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMDC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMEC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMFC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMGC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMHC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMIC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMJC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMKC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMLC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMMC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMNC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMOC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMPC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMQC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMRC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMSC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMTC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMUC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMVC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMWC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMXC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMYC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMZC', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM0D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM1D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM2D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM3D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM4D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM5D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM6D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM7D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM8D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDM9D', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMAD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMBD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMCD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMDD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMED', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMFD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMGD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMHD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMID', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMJD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMKD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMLD', 'FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3R4UYRBDMMD']
2025-07-20 22:31:33,140 - INFO - 批量插入响应状态码: 200
2025-07-20 22:31:33,140 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 14:31:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '588', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3C33FBD7-A85F-7E41-B82B-4CBD49989296', 'x-acs-trace-id': '197332d54f45de330b15563d81ee3050', 'etag': '5bORCDqLSqFAVUJqT5+UqCA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 22:31:33,140 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3N4YYRBDM8G', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDM9G', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMAG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMBG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMCG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMDG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMEG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMFG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMGG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMHG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMIG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMJG']}
2025-07-20 22:31:33,140 - INFO - 批量插入表单数据成功，批次 2，共 12 条记录
2025-07-20 22:31:33,140 - INFO - 成功插入的数据ID: ['FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3N4YYRBDM8G', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDM9G', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMAG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMBG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMCG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMDG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMEG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMFG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMGG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMHG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMIG', 'FINST-3PF66271IHAXC6UX6W6Z94UB0B6K3O4YYRBDMJG']
2025-07-20 22:31:38,158 - INFO - 批量插入完成，共 62 条记录
2025-07-20 22:31:38,158 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 62 条，错误: 0 条
2025-07-20 22:31:38,158 - INFO - 数据同步完成！更新: 0 条，插入: 68 条，错误: 1 条
2025-07-20 22:32:38,197 - INFO - 开始同步昨天与今天的销售数据: 2025-07-19 至 2025-07-20
2025-07-20 22:32:38,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-20 22:32:38,197 - INFO - 查询参数: ('2025-07-19', '2025-07-20')
2025-07-20 22:32:38,354 - INFO - MySQL查询成功，时间段: 2025-07-19 至 2025-07-20，共获取 561 条记录
2025-07-20 22:32:38,354 - INFO - 获取到 2 个日期需要处理: ['2025-07-19', '2025-07-20']
2025-07-20 22:32:38,369 - INFO - 开始处理日期: 2025-07-19
2025-07-20 22:32:38,369 - INFO - Request Parameters - Page 1:
2025-07-20 22:32:38,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:38,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:39,120 - INFO - Response - Page 1:
2025-07-20 22:32:39,120 - INFO - 第 1 页获取到 50 条记录
2025-07-20 22:32:39,620 - INFO - Request Parameters - Page 2:
2025-07-20 22:32:39,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:39,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:40,308 - INFO - Response - Page 2:
2025-07-20 22:32:40,308 - INFO - 第 2 页获取到 50 条记录
2025-07-20 22:32:40,823 - INFO - Request Parameters - Page 3:
2025-07-20 22:32:40,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:40,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:41,527 - INFO - Response - Page 3:
2025-07-20 22:32:41,527 - INFO - 第 3 页获取到 50 条记录
2025-07-20 22:32:42,043 - INFO - Request Parameters - Page 4:
2025-07-20 22:32:42,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:42,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:42,715 - INFO - Response - Page 4:
2025-07-20 22:32:42,715 - INFO - 第 4 页获取到 50 条记录
2025-07-20 22:32:43,231 - INFO - Request Parameters - Page 5:
2025-07-20 22:32:43,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:43,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:43,950 - INFO - Response - Page 5:
2025-07-20 22:32:43,950 - INFO - 第 5 页获取到 50 条记录
2025-07-20 22:32:44,465 - INFO - Request Parameters - Page 6:
2025-07-20 22:32:44,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:44,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:45,169 - INFO - Response - Page 6:
2025-07-20 22:32:45,169 - INFO - 第 6 页获取到 50 条记录
2025-07-20 22:32:45,669 - INFO - Request Parameters - Page 7:
2025-07-20 22:32:45,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:45,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:46,326 - INFO - Response - Page 7:
2025-07-20 22:32:46,326 - INFO - 第 7 页获取到 50 条记录
2025-07-20 22:32:46,826 - INFO - Request Parameters - Page 8:
2025-07-20 22:32:46,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:46,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:47,514 - INFO - Response - Page 8:
2025-07-20 22:32:47,514 - INFO - 第 8 页获取到 50 条记录
2025-07-20 22:32:48,014 - INFO - Request Parameters - Page 9:
2025-07-20 22:32:48,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:48,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:48,717 - INFO - Response - Page 9:
2025-07-20 22:32:48,717 - INFO - 第 9 页获取到 50 条记录
2025-07-20 22:32:49,217 - INFO - Request Parameters - Page 10:
2025-07-20 22:32:49,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:49,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:49,796 - INFO - Response - Page 10:
2025-07-20 22:32:49,796 - INFO - 第 10 页获取到 14 条记录
2025-07-20 22:32:50,312 - INFO - 查询完成，共获取到 464 条记录
2025-07-20 22:32:50,312 - INFO - 获取到 464 条表单数据
2025-07-20 22:32:50,312 - INFO - 当前日期 2025-07-19 有 485 条MySQL数据需要处理
2025-07-20 22:32:50,327 - INFO - 开始批量插入 21 条新记录
2025-07-20 22:32:50,530 - INFO - 批量插入响应状态码: 200
2025-07-20 22:32:50,530 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 14:32:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8E8A74C3-F506-7025-8BAF-AB0D2536A82E', 'x-acs-trace-id': '185208862efba0f22993afa6126b0045', 'etag': '1t00Lw6G3QpukpR2kmYITMA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-20 22:32:50,530 - INFO - 批量插入响应体: {'result': ['FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMPG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMQG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMRG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMSG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMTG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMUG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMVG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMWG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMXG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMYG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMZG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM0H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM1H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM2H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM3H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM4H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM5H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM6H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM7H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM8H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2KTL0SBDM9H']}
2025-07-20 22:32:50,530 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-07-20 22:32:50,530 - INFO - 成功插入的数据ID: ['FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMPG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMQG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMRG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMSG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMTG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMUG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMVG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMWG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMXG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMYG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDMZG', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM0H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM1H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM2H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM3H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM4H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM5H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM6H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM7H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2JTL0SBDM8H', 'FINST-LOG66Q61YE9XPF4ODUG4Y843OFHG2KTL0SBDM9H']
2025-07-20 22:32:55,548 - INFO - 批量插入完成，共 21 条记录
2025-07-20 22:32:55,548 - INFO - 日期 2025-07-19 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-07-20 22:32:55,548 - INFO - 开始处理日期: 2025-07-20
2025-07-20 22:32:55,548 - INFO - Request Parameters - Page 1:
2025-07-20 22:32:55,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:55,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:56,251 - INFO - Response - Page 1:
2025-07-20 22:32:56,251 - INFO - 第 1 页获取到 50 条记录
2025-07-20 22:32:56,767 - INFO - Request Parameters - Page 2:
2025-07-20 22:32:56,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-20 22:32:56,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-20 22:32:57,314 - INFO - Response - Page 2:
2025-07-20 22:32:57,314 - INFO - 第 2 页获取到 14 条记录
2025-07-20 22:32:57,815 - INFO - 查询完成，共获取到 64 条记录
2025-07-20 22:32:57,815 - INFO - 获取到 64 条表单数据
2025-07-20 22:32:57,815 - INFO - 当前日期 2025-07-20 有 64 条MySQL数据需要处理
2025-07-20 22:32:57,815 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-20 22:32:57,815 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 0 条
2025-07-20 22:32:57,815 - INFO - 同步完成
