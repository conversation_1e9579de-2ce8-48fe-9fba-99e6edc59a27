2025-06-01 08:00:03,566 - INFO - ==================================================
2025-06-01 08:00:03,566 - INFO - 程序启动 - 版本 v1.0.0
2025-06-01 08:00:03,566 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250601.log
2025-06-01 08:00:03,566 - INFO - ==================================================
2025-06-01 08:00:03,566 - INFO - 程序入口点: __main__
2025-06-01 08:00:03,566 - INFO - ==================================================
2025-06-01 08:00:03,566 - INFO - 程序启动 - 版本 v1.0.1
2025-06-01 08:00:03,566 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250601.log
2025-06-01 08:00:03,566 - INFO - ==================================================
2025-06-01 08:00:03,847 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-01 08:00:03,847 - INFO - sales_data表已存在，无需创建
2025-06-01 08:00:03,847 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-01 08:00:03,847 - INFO - DataSyncManager初始化完成
2025-06-01 08:00:03,847 - INFO - 未提供日期参数，使用默认值
2025-06-01 08:00:03,847 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-01 08:00:03,847 - INFO - 开始综合数据同步流程...
2025-06-01 08:00:03,847 - INFO - 正在获取数衍平台日销售数据...
2025-06-01 08:00:03,847 - INFO - 查询数衍平台数据，时间段为: 2025-04-01, 2025-05-31
2025-06-01 08:00:03,847 - INFO - 正在获取********至********的数据
2025-06-01 08:00:03,847 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:03,847 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CC7B531C43AF1EC27E8D899484325031'}
2025-06-01 08:00:08,675 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:08,691 - INFO - 过滤后保留 1508 条记录
2025-06-01 08:00:10,706 - INFO - 正在获取********至********的数据
2025-06-01 08:00:10,706 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:10,706 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CEABA5AA63E70709ADDB5D75AD6D3683'}
2025-06-01 08:00:13,519 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:13,534 - INFO - 过滤后保留 1503 条记录
2025-06-01 08:00:15,550 - INFO - 正在获取********至********的数据
2025-06-01 08:00:15,550 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:15,550 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EFF915776167B58D98304B74DFACDF6A'}
2025-06-01 08:00:19,097 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:19,112 - INFO - 过滤后保留 1504 条记录
2025-06-01 08:00:21,128 - INFO - 正在获取********至********的数据
2025-06-01 08:00:21,128 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:21,128 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FC3B61EB958E499107D05090F647D17F'}
2025-06-01 08:00:24,097 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:24,112 - INFO - 过滤后保留 1481 条记录
2025-06-01 08:00:26,128 - INFO - 正在获取********至********的数据
2025-06-01 08:00:26,128 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:26,128 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DBA4F876BCFD15A75A41F06BB4937D73'}
2025-06-01 08:00:29,034 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:29,034 - INFO - 过滤后保留 1488 条记录
2025-06-01 08:00:31,050 - INFO - 正在获取********至********的数据
2025-06-01 08:00:31,050 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:31,050 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F978E462ADDCCBD1F2C77FA444157C8A'}
2025-06-01 08:00:33,440 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:33,456 - INFO - 过滤后保留 1477 条记录
2025-06-01 08:00:35,472 - INFO - 正在获取********至********的数据
2025-06-01 08:00:35,472 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:35,472 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4F426394989126237DFA964C4EEF6178'}
2025-06-01 08:00:37,550 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:37,565 - INFO - 过滤后保留 1478 条记录
2025-06-01 08:00:39,581 - INFO - 正在获取********至********的数据
2025-06-01 08:00:39,581 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:39,581 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D7466E3A0FE299E30AF3CA18B8E03912'}
2025-06-01 08:00:43,987 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:44,003 - INFO - 过滤后保留 1466 条记录
2025-06-01 08:00:46,019 - INFO - 正在获取********至********的数据
2025-06-01 08:00:46,019 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-01 08:00:46,019 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5D9AE7C0BEDD058491EEAB1E106D5DD1'}
2025-06-01 08:00:48,487 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-01 08:00:48,487 - INFO - 过滤后保留 1035 条记录
2025-06-01 08:00:50,487 - INFO - 开始保存数据到SQLite数据库，共 12940 条记录待处理
2025-06-01 08:00:50,815 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-04-19
2025-06-01 08:00:50,815 - INFO - 变更字段: recommend_amount: 37444.36 -> 27614.24, daily_bill_amount: 37444.36 -> 27614.24
2025-06-01 08:00:51,112 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-30
2025-06-01 08:00:51,112 - INFO - 变更字段: recommend_amount: 0.0 -> 8034.4, daily_bill_amount: 0.0 -> 8034.4
2025-06-01 08:00:51,128 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-30
2025-06-01 08:00:51,128 - INFO - 变更字段: recommend_amount: 0.0 -> 5790.7, daily_bill_amount: 0.0 -> 5790.7
2025-06-01 08:00:51,378 - INFO - SQLite数据保存完成，统计信息：
2025-06-01 08:00:51,378 - INFO - - 总记录数: 12940
2025-06-01 08:00:51,378 - INFO - - 成功插入: 208
2025-06-01 08:00:51,378 - INFO - - 成功更新: 3
2025-06-01 08:00:51,378 - INFO - - 无需更新: 12729
2025-06-01 08:00:51,378 - INFO - - 处理失败: 0
2025-06-01 08:00:56,784 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250601.xlsx
2025-06-01 08:00:56,800 - INFO - 成功获取数衍平台数据，共 12940 条记录
2025-06-01 08:00:56,800 - INFO - 正在更新SQLite月度汇总数据...
2025-06-01 08:00:56,800 - INFO - 月度数据sqllite清空完成
2025-06-01 08:00:57,081 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-06-01 08:00:57,081 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-06-01 08:00:57,081 - INFO - 正在获取宜搭日销售表单数据...
2025-06-01 08:00:57,081 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-01 00:00:00 至 2025-05-31 23:59:59
2025-06-01 08:00:57,081 - INFO - 查询分段 1: 2025-04-01 至 2025-04-07
2025-06-01 08:00:57,081 - INFO - 查询日期范围: 2025-04-01 至 2025-04-07，使用分页查询，每页 100 条记录
2025-06-01 08:00:57,081 - INFO - Request Parameters - Page 1:
2025-06-01 08:00:57,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:00:57,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:05,065 - INFO - API请求耗时: 7984ms
2025-06-01 08:01:05,065 - INFO - Response - Page 1
2025-06-01 08:01:05,065 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:01:05,565 - INFO - Request Parameters - Page 2:
2025-06-01 08:01:05,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:05,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:06,393 - INFO - API请求耗时: 828ms
2025-06-01 08:01:06,393 - INFO - Response - Page 2
2025-06-01 08:01:06,393 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:01:06,909 - INFO - Request Parameters - Page 3:
2025-06-01 08:01:06,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:06,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:12,050 - INFO - API请求耗时: 5141ms
2025-06-01 08:01:12,050 - INFO - Response - Page 3
2025-06-01 08:01:12,050 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:01:12,550 - INFO - Request Parameters - Page 4:
2025-06-01 08:01:12,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:12,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:13,268 - INFO - API请求耗时: 719ms
2025-06-01 08:01:13,268 - INFO - Response - Page 4
2025-06-01 08:01:13,268 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:01:13,784 - INFO - Request Parameters - Page 5:
2025-06-01 08:01:13,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:13,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:14,643 - INFO - API请求耗时: 859ms
2025-06-01 08:01:14,643 - INFO - Response - Page 5
2025-06-01 08:01:14,643 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:01:15,143 - INFO - Request Parameters - Page 6:
2025-06-01 08:01:15,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:15,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:15,909 - INFO - API请求耗时: 766ms
2025-06-01 08:01:15,909 - INFO - Response - Page 6
2025-06-01 08:01:15,909 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:01:16,425 - INFO - Request Parameters - Page 7:
2025-06-01 08:01:16,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:16,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:17,143 - INFO - API请求耗时: 719ms
2025-06-01 08:01:17,143 - INFO - Response - Page 7
2025-06-01 08:01:17,143 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:01:17,643 - INFO - Request Parameters - Page 8:
2025-06-01 08:01:17,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:17,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:18,331 - INFO - API请求耗时: 688ms
2025-06-01 08:01:18,331 - INFO - Response - Page 8
2025-06-01 08:01:18,331 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:01:18,846 - INFO - Request Parameters - Page 9:
2025-06-01 08:01:18,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:18,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:19,643 - INFO - API请求耗时: 797ms
2025-06-01 08:01:19,643 - INFO - Response - Page 9
2025-06-01 08:01:19,643 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:01:20,159 - INFO - Request Parameters - Page 10:
2025-06-01 08:01:20,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:20,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:20,940 - INFO - API请求耗时: 781ms
2025-06-01 08:01:20,940 - INFO - Response - Page 10
2025-06-01 08:01:20,940 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:01:21,440 - INFO - Request Parameters - Page 11:
2025-06-01 08:01:21,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:21,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:22,175 - INFO - API请求耗时: 734ms
2025-06-01 08:01:22,175 - INFO - Response - Page 11
2025-06-01 08:01:22,175 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:01:22,690 - INFO - Request Parameters - Page 12:
2025-06-01 08:01:22,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:22,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:23,409 - INFO - API请求耗时: 719ms
2025-06-01 08:01:23,409 - INFO - Response - Page 12
2025-06-01 08:01:23,409 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:01:23,925 - INFO - Request Parameters - Page 13:
2025-06-01 08:01:23,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:23,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:24,675 - INFO - API请求耗时: 750ms
2025-06-01 08:01:24,675 - INFO - Response - Page 13
2025-06-01 08:01:24,675 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:01:25,190 - INFO - Request Parameters - Page 14:
2025-06-01 08:01:25,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:25,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:25,893 - INFO - API请求耗时: 703ms
2025-06-01 08:01:25,893 - INFO - Response - Page 14
2025-06-01 08:01:25,893 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:01:26,409 - INFO - Request Parameters - Page 15:
2025-06-01 08:01:26,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:26,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:27,143 - INFO - API请求耗时: 734ms
2025-06-01 08:01:27,143 - INFO - Response - Page 15
2025-06-01 08:01:27,143 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:01:27,659 - INFO - Request Parameters - Page 16:
2025-06-01 08:01:27,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:27,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:28,346 - INFO - API请求耗时: 687ms
2025-06-01 08:01:28,346 - INFO - Response - Page 16
2025-06-01 08:01:28,346 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:01:28,862 - INFO - Request Parameters - Page 17:
2025-06-01 08:01:28,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:28,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:29,596 - INFO - API请求耗时: 734ms
2025-06-01 08:01:29,596 - INFO - Response - Page 17
2025-06-01 08:01:29,596 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:01:30,096 - INFO - Request Parameters - Page 18:
2025-06-01 08:01:30,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:30,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:30,831 - INFO - API请求耗时: 734ms
2025-06-01 08:01:30,831 - INFO - Response - Page 18
2025-06-01 08:01:30,831 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:01:31,331 - INFO - Request Parameters - Page 19:
2025-06-01 08:01:31,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:31,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:32,143 - INFO - API请求耗时: 812ms
2025-06-01 08:01:32,143 - INFO - Response - Page 19
2025-06-01 08:01:32,143 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:01:32,659 - INFO - Request Parameters - Page 20:
2025-06-01 08:01:32,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:32,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:33,440 - INFO - API请求耗时: 781ms
2025-06-01 08:01:33,440 - INFO - Response - Page 20
2025-06-01 08:01:33,440 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:01:33,956 - INFO - Request Parameters - Page 21:
2025-06-01 08:01:33,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:33,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:34,674 - INFO - API请求耗时: 719ms
2025-06-01 08:01:34,674 - INFO - Response - Page 21
2025-06-01 08:01:34,674 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:01:35,190 - INFO - Request Parameters - Page 22:
2025-06-01 08:01:35,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:35,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:35,956 - INFO - API请求耗时: 766ms
2025-06-01 08:01:35,956 - INFO - Response - Page 22
2025-06-01 08:01:35,956 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:01:36,471 - INFO - Request Parameters - Page 23:
2025-06-01 08:01:36,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:36,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:37,174 - INFO - API请求耗时: 703ms
2025-06-01 08:01:37,174 - INFO - Response - Page 23
2025-06-01 08:01:37,174 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:01:37,690 - INFO - Request Parameters - Page 24:
2025-06-01 08:01:37,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:37,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:38,503 - INFO - API请求耗时: 812ms
2025-06-01 08:01:38,503 - INFO - Response - Page 24
2025-06-01 08:01:38,503 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:01:39,018 - INFO - Request Parameters - Page 25:
2025-06-01 08:01:39,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:39,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:39,721 - INFO - API请求耗时: 703ms
2025-06-01 08:01:39,721 - INFO - Response - Page 25
2025-06-01 08:01:39,721 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:01:40,237 - INFO - Request Parameters - Page 26:
2025-06-01 08:01:40,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:40,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:41,081 - INFO - API请求耗时: 844ms
2025-06-01 08:01:41,081 - INFO - Response - Page 26
2025-06-01 08:01:41,081 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:01:41,581 - INFO - Request Parameters - Page 27:
2025-06-01 08:01:41,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:41,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:42,393 - INFO - API请求耗时: 812ms
2025-06-01 08:01:42,393 - INFO - Response - Page 27
2025-06-01 08:01:42,393 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:01:42,909 - INFO - Request Parameters - Page 28:
2025-06-01 08:01:42,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:42,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:43,659 - INFO - API请求耗时: 750ms
2025-06-01 08:01:43,659 - INFO - Response - Page 28
2025-06-01 08:01:43,659 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:01:44,174 - INFO - Request Parameters - Page 29:
2025-06-01 08:01:44,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:44,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:44,909 - INFO - API请求耗时: 734ms
2025-06-01 08:01:44,909 - INFO - Response - Page 29
2025-06-01 08:01:44,909 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:01:45,424 - INFO - Request Parameters - Page 30:
2025-06-01 08:01:45,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:45,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:46,127 - INFO - API请求耗时: 703ms
2025-06-01 08:01:46,127 - INFO - Response - Page 30
2025-06-01 08:01:46,127 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:01:46,643 - INFO - Request Parameters - Page 31:
2025-06-01 08:01:46,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:46,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:47,424 - INFO - API请求耗时: 781ms
2025-06-01 08:01:47,424 - INFO - Response - Page 31
2025-06-01 08:01:47,424 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:01:47,940 - INFO - Request Parameters - Page 32:
2025-06-01 08:01:47,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:47,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:48,659 - INFO - API请求耗时: 719ms
2025-06-01 08:01:48,659 - INFO - Response - Page 32
2025-06-01 08:01:48,659 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:01:49,174 - INFO - Request Parameters - Page 33:
2025-06-01 08:01:49,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:49,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:49,909 - INFO - API请求耗时: 734ms
2025-06-01 08:01:49,909 - INFO - Response - Page 33
2025-06-01 08:01:49,909 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:01:50,424 - INFO - Request Parameters - Page 34:
2025-06-01 08:01:50,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:50,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:51,237 - INFO - API请求耗时: 812ms
2025-06-01 08:01:51,237 - INFO - Response - Page 34
2025-06-01 08:01:51,237 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:01:51,737 - INFO - Request Parameters - Page 35:
2025-06-01 08:01:51,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:51,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:52,534 - INFO - API请求耗时: 797ms
2025-06-01 08:01:52,534 - INFO - Response - Page 35
2025-06-01 08:01:52,534 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:01:53,049 - INFO - Request Parameters - Page 36:
2025-06-01 08:01:53,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:53,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:53,846 - INFO - API请求耗时: 797ms
2025-06-01 08:01:53,846 - INFO - Response - Page 36
2025-06-01 08:01:53,846 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:01:54,362 - INFO - Request Parameters - Page 37:
2025-06-01 08:01:54,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:54,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:55,159 - INFO - API请求耗时: 797ms
2025-06-01 08:01:55,159 - INFO - Response - Page 37
2025-06-01 08:01:55,159 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:01:55,659 - INFO - Request Parameters - Page 38:
2025-06-01 08:01:55,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:55,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:56,393 - INFO - API请求耗时: 734ms
2025-06-01 08:01:56,393 - INFO - Response - Page 38
2025-06-01 08:01:56,393 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:01:56,909 - INFO - Request Parameters - Page 39:
2025-06-01 08:01:56,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:56,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:57,612 - INFO - API请求耗时: 703ms
2025-06-01 08:01:57,612 - INFO - Response - Page 39
2025-06-01 08:01:57,612 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:01:58,127 - INFO - Request Parameters - Page 40:
2025-06-01 08:01:58,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:58,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:01:58,893 - INFO - API请求耗时: 766ms
2025-06-01 08:01:58,893 - INFO - Response - Page 40
2025-06-01 08:01:58,893 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:01:59,409 - INFO - Request Parameters - Page 41:
2025-06-01 08:01:59,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:01:59,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:00,237 - INFO - API请求耗时: 828ms
2025-06-01 08:02:00,237 - INFO - Response - Page 41
2025-06-01 08:02:00,237 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:02:00,737 - INFO - Request Parameters - Page 42:
2025-06-01 08:02:00,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:00,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:01,627 - INFO - API请求耗时: 891ms
2025-06-01 08:02:01,627 - INFO - Response - Page 42
2025-06-01 08:02:01,627 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:02:02,127 - INFO - Request Parameters - Page 43:
2025-06-01 08:02:02,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:02,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:02,893 - INFO - API请求耗时: 766ms
2025-06-01 08:02:02,893 - INFO - Response - Page 43
2025-06-01 08:02:02,893 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:02:03,409 - INFO - Request Parameters - Page 44:
2025-06-01 08:02:03,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:03,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:04,159 - INFO - API请求耗时: 750ms
2025-06-01 08:02:04,159 - INFO - Response - Page 44
2025-06-01 08:02:04,159 - INFO - 第 44 页获取到 100 条记录
2025-06-01 08:02:04,659 - INFO - Request Parameters - Page 45:
2025-06-01 08:02:04,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:04,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:05,362 - INFO - API请求耗时: 703ms
2025-06-01 08:02:05,362 - INFO - Response - Page 45
2025-06-01 08:02:05,362 - INFO - 第 45 页获取到 100 条记录
2025-06-01 08:02:05,862 - INFO - Request Parameters - Page 46:
2025-06-01 08:02:05,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:05,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:06,659 - INFO - API请求耗时: 797ms
2025-06-01 08:02:06,659 - INFO - Response - Page 46
2025-06-01 08:02:06,659 - INFO - 第 46 页获取到 100 条记录
2025-06-01 08:02:07,174 - INFO - Request Parameters - Page 47:
2025-06-01 08:02:07,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:07,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:07,924 - INFO - API请求耗时: 750ms
2025-06-01 08:02:07,924 - INFO - Response - Page 47
2025-06-01 08:02:07,924 - INFO - 第 47 页获取到 100 条记录
2025-06-01 08:02:08,424 - INFO - Request Parameters - Page 48:
2025-06-01 08:02:08,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:08,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:09,237 - INFO - API请求耗时: 812ms
2025-06-01 08:02:09,237 - INFO - Response - Page 48
2025-06-01 08:02:09,237 - INFO - 第 48 页获取到 100 条记录
2025-06-01 08:02:09,752 - INFO - Request Parameters - Page 49:
2025-06-01 08:02:09,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:09,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:10,627 - INFO - API请求耗时: 875ms
2025-06-01 08:02:10,627 - INFO - Response - Page 49
2025-06-01 08:02:10,627 - INFO - 第 49 页获取到 100 条记录
2025-06-01 08:02:11,127 - INFO - Request Parameters - Page 50:
2025-06-01 08:02:11,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:11,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 50, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800081, 1743955200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:11,752 - INFO - API请求耗时: 625ms
2025-06-01 08:02:11,752 - INFO - Response - Page 50
2025-06-01 08:02:11,752 - INFO - 第 50 页获取到 35 条记录
2025-06-01 08:02:11,752 - INFO - 查询完成，共获取到 4935 条记录
2025-06-01 08:02:11,752 - INFO - 分段 1 查询成功，获取到 4935 条记录
2025-06-01 08:02:12,768 - INFO - 查询分段 2: 2025-04-08 至 2025-04-14
2025-06-01 08:02:12,768 - INFO - 查询日期范围: 2025-04-08 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-01 08:02:12,768 - INFO - Request Parameters - Page 1:
2025-06-01 08:02:12,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:12,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:13,502 - INFO - API请求耗时: 734ms
2025-06-01 08:02:13,502 - INFO - Response - Page 1
2025-06-01 08:02:13,502 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:02:14,018 - INFO - Request Parameters - Page 2:
2025-06-01 08:02:14,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:14,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:14,705 - INFO - API请求耗时: 687ms
2025-06-01 08:02:14,705 - INFO - Response - Page 2
2025-06-01 08:02:14,705 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:02:15,205 - INFO - Request Parameters - Page 3:
2025-06-01 08:02:15,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:15,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:15,955 - INFO - API请求耗时: 750ms
2025-06-01 08:02:15,955 - INFO - Response - Page 3
2025-06-01 08:02:15,955 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:02:16,471 - INFO - Request Parameters - Page 4:
2025-06-01 08:02:16,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:16,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:17,237 - INFO - API请求耗时: 766ms
2025-06-01 08:02:17,237 - INFO - Response - Page 4
2025-06-01 08:02:17,237 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:02:17,737 - INFO - Request Parameters - Page 5:
2025-06-01 08:02:17,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:17,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:18,471 - INFO - API请求耗时: 734ms
2025-06-01 08:02:18,471 - INFO - Response - Page 5
2025-06-01 08:02:18,471 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:02:18,987 - INFO - Request Parameters - Page 6:
2025-06-01 08:02:18,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:18,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:19,768 - INFO - API请求耗时: 781ms
2025-06-01 08:02:19,768 - INFO - Response - Page 6
2025-06-01 08:02:19,768 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:02:20,268 - INFO - Request Parameters - Page 7:
2025-06-01 08:02:20,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:20,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:21,049 - INFO - API请求耗时: 781ms
2025-06-01 08:02:21,049 - INFO - Response - Page 7
2025-06-01 08:02:21,049 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:02:21,565 - INFO - Request Parameters - Page 8:
2025-06-01 08:02:21,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:21,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:22,237 - INFO - API请求耗时: 672ms
2025-06-01 08:02:22,237 - INFO - Response - Page 8
2025-06-01 08:02:22,237 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:02:22,752 - INFO - Request Parameters - Page 9:
2025-06-01 08:02:22,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:22,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:23,487 - INFO - API请求耗时: 734ms
2025-06-01 08:02:23,487 - INFO - Response - Page 9
2025-06-01 08:02:23,487 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:02:23,987 - INFO - Request Parameters - Page 10:
2025-06-01 08:02:23,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:23,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:24,846 - INFO - API请求耗时: 859ms
2025-06-01 08:02:24,846 - INFO - Response - Page 10
2025-06-01 08:02:24,846 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:02:25,362 - INFO - Request Parameters - Page 11:
2025-06-01 08:02:25,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:25,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:26,096 - INFO - API请求耗时: 734ms
2025-06-01 08:02:26,096 - INFO - Response - Page 11
2025-06-01 08:02:26,096 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:02:26,612 - INFO - Request Parameters - Page 12:
2025-06-01 08:02:26,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:26,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:27,393 - INFO - API请求耗时: 781ms
2025-06-01 08:02:27,393 - INFO - Response - Page 12
2025-06-01 08:02:27,393 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:02:27,908 - INFO - Request Parameters - Page 13:
2025-06-01 08:02:27,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:27,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:28,658 - INFO - API请求耗时: 750ms
2025-06-01 08:02:28,658 - INFO - Response - Page 13
2025-06-01 08:02:28,658 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:02:29,174 - INFO - Request Parameters - Page 14:
2025-06-01 08:02:29,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:29,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:29,924 - INFO - API请求耗时: 750ms
2025-06-01 08:02:29,924 - INFO - Response - Page 14
2025-06-01 08:02:29,924 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:02:30,424 - INFO - Request Parameters - Page 15:
2025-06-01 08:02:30,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:30,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:31,190 - INFO - API请求耗时: 766ms
2025-06-01 08:02:31,190 - INFO - Response - Page 15
2025-06-01 08:02:31,190 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:02:31,690 - INFO - Request Parameters - Page 16:
2025-06-01 08:02:31,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:31,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:32,455 - INFO - API请求耗时: 766ms
2025-06-01 08:02:32,455 - INFO - Response - Page 16
2025-06-01 08:02:32,455 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:02:32,955 - INFO - Request Parameters - Page 17:
2025-06-01 08:02:32,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:32,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:33,627 - INFO - API请求耗时: 672ms
2025-06-01 08:02:33,627 - INFO - Response - Page 17
2025-06-01 08:02:33,627 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:02:34,143 - INFO - Request Parameters - Page 18:
2025-06-01 08:02:34,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:34,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:35,127 - INFO - API请求耗时: 984ms
2025-06-01 08:02:35,127 - INFO - Response - Page 18
2025-06-01 08:02:35,127 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:02:35,643 - INFO - Request Parameters - Page 19:
2025-06-01 08:02:35,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:35,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:36,471 - INFO - API请求耗时: 828ms
2025-06-01 08:02:36,471 - INFO - Response - Page 19
2025-06-01 08:02:36,471 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:02:36,971 - INFO - Request Parameters - Page 20:
2025-06-01 08:02:36,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:36,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:37,737 - INFO - API请求耗时: 766ms
2025-06-01 08:02:37,737 - INFO - Response - Page 20
2025-06-01 08:02:37,737 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:02:38,252 - INFO - Request Parameters - Page 21:
2025-06-01 08:02:38,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:38,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:39,065 - INFO - API请求耗时: 812ms
2025-06-01 08:02:39,065 - INFO - Response - Page 21
2025-06-01 08:02:39,065 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:02:39,565 - INFO - Request Parameters - Page 22:
2025-06-01 08:02:39,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:39,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:40,299 - INFO - API请求耗时: 734ms
2025-06-01 08:02:40,299 - INFO - Response - Page 22
2025-06-01 08:02:40,299 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:02:40,815 - INFO - Request Parameters - Page 23:
2025-06-01 08:02:40,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:40,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:41,533 - INFO - API请求耗时: 719ms
2025-06-01 08:02:41,533 - INFO - Response - Page 23
2025-06-01 08:02:41,549 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:02:42,065 - INFO - Request Parameters - Page 24:
2025-06-01 08:02:42,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:42,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:42,846 - INFO - API请求耗时: 781ms
2025-06-01 08:02:42,846 - INFO - Response - Page 24
2025-06-01 08:02:42,846 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:02:43,361 - INFO - Request Parameters - Page 25:
2025-06-01 08:02:43,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:43,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:44,143 - INFO - API请求耗时: 781ms
2025-06-01 08:02:44,143 - INFO - Response - Page 25
2025-06-01 08:02:44,143 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:02:44,643 - INFO - Request Parameters - Page 26:
2025-06-01 08:02:44,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:44,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:45,424 - INFO - API请求耗时: 781ms
2025-06-01 08:02:45,424 - INFO - Response - Page 26
2025-06-01 08:02:45,424 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:02:45,940 - INFO - Request Parameters - Page 27:
2025-06-01 08:02:45,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:45,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:46,690 - INFO - API请求耗时: 750ms
2025-06-01 08:02:46,690 - INFO - Response - Page 27
2025-06-01 08:02:46,690 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:02:47,205 - INFO - Request Parameters - Page 28:
2025-06-01 08:02:47,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:47,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:47,971 - INFO - API请求耗时: 766ms
2025-06-01 08:02:47,971 - INFO - Response - Page 28
2025-06-01 08:02:47,971 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:02:48,471 - INFO - Request Parameters - Page 29:
2025-06-01 08:02:48,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:48,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:49,221 - INFO - API请求耗时: 750ms
2025-06-01 08:02:49,236 - INFO - Response - Page 29
2025-06-01 08:02:49,236 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:02:49,752 - INFO - Request Parameters - Page 30:
2025-06-01 08:02:49,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:49,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:50,627 - INFO - API请求耗时: 875ms
2025-06-01 08:02:50,627 - INFO - Response - Page 30
2025-06-01 08:02:50,627 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:02:51,143 - INFO - Request Parameters - Page 31:
2025-06-01 08:02:51,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:51,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:52,033 - INFO - API请求耗时: 891ms
2025-06-01 08:02:52,033 - INFO - Response - Page 31
2025-06-01 08:02:52,033 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:02:52,533 - INFO - Request Parameters - Page 32:
2025-06-01 08:02:52,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:52,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:53,268 - INFO - API请求耗时: 734ms
2025-06-01 08:02:53,268 - INFO - Response - Page 32
2025-06-01 08:02:53,268 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:02:53,783 - INFO - Request Parameters - Page 33:
2025-06-01 08:02:53,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:53,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:54,580 - INFO - API请求耗时: 797ms
2025-06-01 08:02:54,580 - INFO - Response - Page 33
2025-06-01 08:02:54,580 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:02:55,080 - INFO - Request Parameters - Page 34:
2025-06-01 08:02:55,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:55,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:55,846 - INFO - API请求耗时: 766ms
2025-06-01 08:02:55,846 - INFO - Response - Page 34
2025-06-01 08:02:55,846 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:02:56,361 - INFO - Request Parameters - Page 35:
2025-06-01 08:02:56,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:56,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:57,127 - INFO - API请求耗时: 766ms
2025-06-01 08:02:57,127 - INFO - Response - Page 35
2025-06-01 08:02:57,127 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:02:57,627 - INFO - Request Parameters - Page 36:
2025-06-01 08:02:57,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:57,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:58,393 - INFO - API请求耗时: 766ms
2025-06-01 08:02:58,393 - INFO - Response - Page 36
2025-06-01 08:02:58,393 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:02:58,908 - INFO - Request Parameters - Page 37:
2025-06-01 08:02:58,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:02:58,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:02:59,674 - INFO - API请求耗时: 766ms
2025-06-01 08:02:59,674 - INFO - Response - Page 37
2025-06-01 08:02:59,674 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:03:00,189 - INFO - Request Parameters - Page 38:
2025-06-01 08:03:00,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:00,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:00,971 - INFO - API请求耗时: 781ms
2025-06-01 08:03:00,971 - INFO - Response - Page 38
2025-06-01 08:03:00,971 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:03:01,471 - INFO - Request Parameters - Page 39:
2025-06-01 08:03:01,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:01,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:02,221 - INFO - API请求耗时: 750ms
2025-06-01 08:03:02,221 - INFO - Response - Page 39
2025-06-01 08:03:02,221 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:03:02,721 - INFO - Request Parameters - Page 40:
2025-06-01 08:03:02,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:02,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:03,518 - INFO - API请求耗时: 797ms
2025-06-01 08:03:03,518 - INFO - Response - Page 40
2025-06-01 08:03:03,518 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:03:04,033 - INFO - Request Parameters - Page 41:
2025-06-01 08:03:04,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:04,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:04,799 - INFO - API请求耗时: 766ms
2025-06-01 08:03:04,799 - INFO - Response - Page 41
2025-06-01 08:03:04,814 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:03:05,314 - INFO - Request Parameters - Page 42:
2025-06-01 08:03:05,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:05,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:06,002 - INFO - API请求耗时: 687ms
2025-06-01 08:03:06,002 - INFO - Response - Page 42
2025-06-01 08:03:06,002 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:03:06,518 - INFO - Request Parameters - Page 43:
2025-06-01 08:03:06,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:06,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:07,314 - INFO - API请求耗时: 797ms
2025-06-01 08:03:07,314 - INFO - Response - Page 43
2025-06-01 08:03:07,314 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:03:07,814 - INFO - Request Parameters - Page 44:
2025-06-01 08:03:07,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:07,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:08,518 - INFO - API请求耗时: 703ms
2025-06-01 08:03:08,518 - INFO - Response - Page 44
2025-06-01 08:03:08,518 - INFO - 第 44 页获取到 100 条记录
2025-06-01 08:03:09,033 - INFO - Request Parameters - Page 45:
2025-06-01 08:03:09,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:09,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:09,721 - INFO - API请求耗时: 687ms
2025-06-01 08:03:09,721 - INFO - Response - Page 45
2025-06-01 08:03:09,721 - INFO - 第 45 页获取到 100 条记录
2025-06-01 08:03:10,236 - INFO - Request Parameters - Page 46:
2025-06-01 08:03:10,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:10,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:11,080 - INFO - API请求耗时: 844ms
2025-06-01 08:03:11,080 - INFO - Response - Page 46
2025-06-01 08:03:11,080 - INFO - 第 46 页获取到 100 条记录
2025-06-01 08:03:11,580 - INFO - Request Parameters - Page 47:
2025-06-01 08:03:11,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:11,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:12,377 - INFO - API请求耗时: 797ms
2025-06-01 08:03:12,377 - INFO - Response - Page 47
2025-06-01 08:03:12,377 - INFO - 第 47 页获取到 100 条记录
2025-06-01 08:03:12,893 - INFO - Request Parameters - Page 48:
2025-06-01 08:03:12,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:12,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:13,689 - INFO - API请求耗时: 797ms
2025-06-01 08:03:13,689 - INFO - Response - Page 48
2025-06-01 08:03:13,689 - INFO - 第 48 页获取到 100 条记录
2025-06-01 08:03:14,205 - INFO - Request Parameters - Page 49:
2025-06-01 08:03:14,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:14,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:14,955 - INFO - API请求耗时: 750ms
2025-06-01 08:03:14,955 - INFO - Response - Page 49
2025-06-01 08:03:14,955 - INFO - 第 49 页获取到 100 条记录
2025-06-01 08:03:15,455 - INFO - Request Parameters - Page 50:
2025-06-01 08:03:15,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:15,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 50, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744041600081, 1744560000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:16,111 - INFO - API请求耗时: 656ms
2025-06-01 08:03:16,111 - INFO - Response - Page 50
2025-06-01 08:03:16,111 - INFO - 第 50 页获取到 53 条记录
2025-06-01 08:03:16,111 - INFO - 查询完成，共获取到 4953 条记录
2025-06-01 08:03:16,111 - INFO - 分段 2 查询成功，获取到 4953 条记录
2025-06-01 08:03:17,127 - INFO - 查询分段 3: 2025-04-15 至 2025-04-21
2025-06-01 08:03:17,127 - INFO - 查询日期范围: 2025-04-15 至 2025-04-21，使用分页查询，每页 100 条记录
2025-06-01 08:03:17,127 - INFO - Request Parameters - Page 1:
2025-06-01 08:03:17,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:17,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:17,893 - INFO - API请求耗时: 766ms
2025-06-01 08:03:17,893 - INFO - Response - Page 1
2025-06-01 08:03:17,893 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:03:18,393 - INFO - Request Parameters - Page 2:
2025-06-01 08:03:18,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:18,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:19,111 - INFO - API请求耗时: 719ms
2025-06-01 08:03:19,111 - INFO - Response - Page 2
2025-06-01 08:03:19,111 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:03:19,611 - INFO - Request Parameters - Page 3:
2025-06-01 08:03:19,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:19,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:20,299 - INFO - API请求耗时: 687ms
2025-06-01 08:03:20,299 - INFO - Response - Page 3
2025-06-01 08:03:20,299 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:03:20,814 - INFO - Request Parameters - Page 4:
2025-06-01 08:03:20,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:20,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:21,549 - INFO - API请求耗时: 734ms
2025-06-01 08:03:21,549 - INFO - Response - Page 4
2025-06-01 08:03:21,549 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:03:22,064 - INFO - Request Parameters - Page 5:
2025-06-01 08:03:22,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:22,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:22,814 - INFO - API请求耗时: 750ms
2025-06-01 08:03:22,814 - INFO - Response - Page 5
2025-06-01 08:03:22,814 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:03:23,314 - INFO - Request Parameters - Page 6:
2025-06-01 08:03:23,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:23,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:24,174 - INFO - API请求耗时: 859ms
2025-06-01 08:03:24,174 - INFO - Response - Page 6
2025-06-01 08:03:24,174 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:03:24,689 - INFO - Request Parameters - Page 7:
2025-06-01 08:03:24,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:24,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:25,439 - INFO - API请求耗时: 750ms
2025-06-01 08:03:25,439 - INFO - Response - Page 7
2025-06-01 08:03:25,439 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:03:25,939 - INFO - Request Parameters - Page 8:
2025-06-01 08:03:25,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:25,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:26,721 - INFO - API请求耗时: 781ms
2025-06-01 08:03:26,721 - INFO - Response - Page 8
2025-06-01 08:03:26,721 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:03:27,236 - INFO - Request Parameters - Page 9:
2025-06-01 08:03:27,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:27,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:28,033 - INFO - API请求耗时: 797ms
2025-06-01 08:03:28,033 - INFO - Response - Page 9
2025-06-01 08:03:28,033 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:03:28,533 - INFO - Request Parameters - Page 10:
2025-06-01 08:03:28,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:28,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:29,377 - INFO - API请求耗时: 844ms
2025-06-01 08:03:29,377 - INFO - Response - Page 10
2025-06-01 08:03:29,377 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:03:29,892 - INFO - Request Parameters - Page 11:
2025-06-01 08:03:29,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:29,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:30,580 - INFO - API请求耗时: 687ms
2025-06-01 08:03:30,580 - INFO - Response - Page 11
2025-06-01 08:03:30,580 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:03:31,080 - INFO - Request Parameters - Page 12:
2025-06-01 08:03:31,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:31,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:31,877 - INFO - API请求耗时: 797ms
2025-06-01 08:03:31,877 - INFO - Response - Page 12
2025-06-01 08:03:31,877 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:03:32,392 - INFO - Request Parameters - Page 13:
2025-06-01 08:03:32,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:32,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:33,189 - INFO - API请求耗时: 797ms
2025-06-01 08:03:33,189 - INFO - Response - Page 13
2025-06-01 08:03:33,189 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:03:33,705 - INFO - Request Parameters - Page 14:
2025-06-01 08:03:33,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:33,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:34,627 - INFO - API请求耗时: 922ms
2025-06-01 08:03:34,627 - INFO - Response - Page 14
2025-06-01 08:03:34,627 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:03:35,142 - INFO - Request Parameters - Page 15:
2025-06-01 08:03:35,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:35,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:35,877 - INFO - API请求耗时: 734ms
2025-06-01 08:03:35,877 - INFO - Response - Page 15
2025-06-01 08:03:35,877 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:03:36,408 - INFO - Request Parameters - Page 16:
2025-06-01 08:03:36,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:36,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:37,221 - INFO - API请求耗时: 813ms
2025-06-01 08:03:37,221 - INFO - Response - Page 16
2025-06-01 08:03:37,221 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:03:37,721 - INFO - Request Parameters - Page 17:
2025-06-01 08:03:37,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:37,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:38,439 - INFO - API请求耗时: 719ms
2025-06-01 08:03:38,439 - INFO - Response - Page 17
2025-06-01 08:03:38,439 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:03:38,939 - INFO - Request Parameters - Page 18:
2025-06-01 08:03:38,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:38,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:39,720 - INFO - API请求耗时: 781ms
2025-06-01 08:03:39,720 - INFO - Response - Page 18
2025-06-01 08:03:39,720 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:03:40,236 - INFO - Request Parameters - Page 19:
2025-06-01 08:03:40,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:40,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:41,033 - INFO - API请求耗时: 797ms
2025-06-01 08:03:41,033 - INFO - Response - Page 19
2025-06-01 08:03:41,049 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:03:41,549 - INFO - Request Parameters - Page 20:
2025-06-01 08:03:41,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:41,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:42,299 - INFO - API请求耗时: 750ms
2025-06-01 08:03:42,299 - INFO - Response - Page 20
2025-06-01 08:03:42,299 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:03:42,814 - INFO - Request Parameters - Page 21:
2025-06-01 08:03:42,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:42,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:43,580 - INFO - API请求耗时: 766ms
2025-06-01 08:03:43,580 - INFO - Response - Page 21
2025-06-01 08:03:43,580 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:03:44,095 - INFO - Request Parameters - Page 22:
2025-06-01 08:03:44,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:44,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:44,877 - INFO - API请求耗时: 781ms
2025-06-01 08:03:44,877 - INFO - Response - Page 22
2025-06-01 08:03:44,877 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:03:45,377 - INFO - Request Parameters - Page 23:
2025-06-01 08:03:45,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:45,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:46,142 - INFO - API请求耗时: 766ms
2025-06-01 08:03:46,142 - INFO - Response - Page 23
2025-06-01 08:03:46,142 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:03:46,642 - INFO - Request Parameters - Page 24:
2025-06-01 08:03:46,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:46,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:47,439 - INFO - API请求耗时: 797ms
2025-06-01 08:03:47,439 - INFO - Response - Page 24
2025-06-01 08:03:47,439 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:03:47,955 - INFO - Request Parameters - Page 25:
2025-06-01 08:03:47,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:47,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:48,689 - INFO - API请求耗时: 734ms
2025-06-01 08:03:48,689 - INFO - Response - Page 25
2025-06-01 08:03:48,689 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:03:49,205 - INFO - Request Parameters - Page 26:
2025-06-01 08:03:49,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:49,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:49,955 - INFO - API请求耗时: 750ms
2025-06-01 08:03:49,955 - INFO - Response - Page 26
2025-06-01 08:03:49,955 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:03:50,470 - INFO - Request Parameters - Page 27:
2025-06-01 08:03:50,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:50,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:51,189 - INFO - API请求耗时: 719ms
2025-06-01 08:03:51,189 - INFO - Response - Page 27
2025-06-01 08:03:51,189 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:03:51,689 - INFO - Request Parameters - Page 28:
2025-06-01 08:03:51,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:51,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:52,455 - INFO - API请求耗时: 766ms
2025-06-01 08:03:52,455 - INFO - Response - Page 28
2025-06-01 08:03:52,455 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:03:52,970 - INFO - Request Parameters - Page 29:
2025-06-01 08:03:52,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:52,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:53,658 - INFO - API请求耗时: 687ms
2025-06-01 08:03:53,658 - INFO - Response - Page 29
2025-06-01 08:03:53,658 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:03:54,174 - INFO - Request Parameters - Page 30:
2025-06-01 08:03:54,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:54,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:54,939 - INFO - API请求耗时: 766ms
2025-06-01 08:03:54,939 - INFO - Response - Page 30
2025-06-01 08:03:54,939 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:03:55,439 - INFO - Request Parameters - Page 31:
2025-06-01 08:03:55,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:55,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:56,205 - INFO - API请求耗时: 766ms
2025-06-01 08:03:56,205 - INFO - Response - Page 31
2025-06-01 08:03:56,220 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:03:56,736 - INFO - Request Parameters - Page 32:
2025-06-01 08:03:56,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:56,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:57,611 - INFO - API请求耗时: 875ms
2025-06-01 08:03:57,611 - INFO - Response - Page 32
2025-06-01 08:03:57,611 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:03:58,127 - INFO - Request Parameters - Page 33:
2025-06-01 08:03:58,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:58,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:03:58,892 - INFO - API请求耗时: 766ms
2025-06-01 08:03:58,892 - INFO - Response - Page 33
2025-06-01 08:03:58,908 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:03:59,408 - INFO - Request Parameters - Page 34:
2025-06-01 08:03:59,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:03:59,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:00,127 - INFO - API请求耗时: 719ms
2025-06-01 08:04:00,127 - INFO - Response - Page 34
2025-06-01 08:04:00,127 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:04:00,627 - INFO - Request Parameters - Page 35:
2025-06-01 08:04:00,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:00,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:01,408 - INFO - API请求耗时: 781ms
2025-06-01 08:04:01,408 - INFO - Response - Page 35
2025-06-01 08:04:01,408 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:04:01,923 - INFO - Request Parameters - Page 36:
2025-06-01 08:04:01,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:01,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:02,658 - INFO - API请求耗时: 734ms
2025-06-01 08:04:02,658 - INFO - Response - Page 36
2025-06-01 08:04:02,658 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:04:03,173 - INFO - Request Parameters - Page 37:
2025-06-01 08:04:03,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:03,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:03,986 - INFO - API请求耗时: 812ms
2025-06-01 08:04:03,986 - INFO - Response - Page 37
2025-06-01 08:04:03,986 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:04:04,502 - INFO - Request Parameters - Page 38:
2025-06-01 08:04:04,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:04,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:05,298 - INFO - API请求耗时: 797ms
2025-06-01 08:04:05,298 - INFO - Response - Page 38
2025-06-01 08:04:05,298 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:04:05,798 - INFO - Request Parameters - Page 39:
2025-06-01 08:04:05,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:05,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:06,564 - INFO - API请求耗时: 766ms
2025-06-01 08:04:06,564 - INFO - Response - Page 39
2025-06-01 08:04:06,564 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:04:07,080 - INFO - Request Parameters - Page 40:
2025-06-01 08:04:07,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:07,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:07,798 - INFO - API请求耗时: 719ms
2025-06-01 08:04:07,798 - INFO - Response - Page 40
2025-06-01 08:04:07,798 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:04:08,314 - INFO - Request Parameters - Page 41:
2025-06-01 08:04:08,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:08,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:09,017 - INFO - API请求耗时: 703ms
2025-06-01 08:04:09,017 - INFO - Response - Page 41
2025-06-01 08:04:09,033 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:04:09,533 - INFO - Request Parameters - Page 42:
2025-06-01 08:04:09,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:09,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:10,267 - INFO - API请求耗时: 734ms
2025-06-01 08:04:10,267 - INFO - Response - Page 42
2025-06-01 08:04:10,267 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:04:10,783 - INFO - Request Parameters - Page 43:
2025-06-01 08:04:10,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:10,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:11,564 - INFO - API请求耗时: 781ms
2025-06-01 08:04:11,564 - INFO - Response - Page 43
2025-06-01 08:04:11,564 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:04:12,080 - INFO - Request Parameters - Page 44:
2025-06-01 08:04:12,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:12,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:12,861 - INFO - API请求耗时: 781ms
2025-06-01 08:04:12,861 - INFO - Response - Page 44
2025-06-01 08:04:12,861 - INFO - 第 44 页获取到 100 条记录
2025-06-01 08:04:13,377 - INFO - Request Parameters - Page 45:
2025-06-01 08:04:13,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:13,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:14,127 - INFO - API请求耗时: 750ms
2025-06-01 08:04:14,127 - INFO - Response - Page 45
2025-06-01 08:04:14,127 - INFO - 第 45 页获取到 100 条记录
2025-06-01 08:04:14,627 - INFO - Request Parameters - Page 46:
2025-06-01 08:04:14,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:14,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:15,330 - INFO - API请求耗时: 703ms
2025-06-01 08:04:15,330 - INFO - Response - Page 46
2025-06-01 08:04:15,330 - INFO - 第 46 页获取到 100 条记录
2025-06-01 08:04:15,845 - INFO - Request Parameters - Page 47:
2025-06-01 08:04:15,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:15,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:16,611 - INFO - API请求耗时: 766ms
2025-06-01 08:04:16,611 - INFO - Response - Page 47
2025-06-01 08:04:16,611 - INFO - 第 47 页获取到 100 条记录
2025-06-01 08:04:17,127 - INFO - Request Parameters - Page 48:
2025-06-01 08:04:17,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:17,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400081, 1745164800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:17,673 - INFO - API请求耗时: 547ms
2025-06-01 08:04:17,673 - INFO - Response - Page 48
2025-06-01 08:04:17,673 - INFO - 第 48 页获取到 22 条记录
2025-06-01 08:04:17,673 - INFO - 查询完成，共获取到 4722 条记录
2025-06-01 08:04:17,673 - INFO - 分段 3 查询成功，获取到 4722 条记录
2025-06-01 08:04:18,689 - INFO - 查询分段 4: 2025-04-22 至 2025-04-28
2025-06-01 08:04:18,689 - INFO - 查询日期范围: 2025-04-22 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-01 08:04:18,689 - INFO - Request Parameters - Page 1:
2025-06-01 08:04:18,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:18,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:19,408 - INFO - API请求耗时: 719ms
2025-06-01 08:04:19,408 - INFO - Response - Page 1
2025-06-01 08:04:19,408 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:04:19,908 - INFO - Request Parameters - Page 2:
2025-06-01 08:04:19,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:19,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:20,783 - INFO - API请求耗时: 875ms
2025-06-01 08:04:20,783 - INFO - Response - Page 2
2025-06-01 08:04:20,783 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:04:21,298 - INFO - Request Parameters - Page 3:
2025-06-01 08:04:21,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:21,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:22,126 - INFO - API请求耗时: 828ms
2025-06-01 08:04:22,126 - INFO - Response - Page 3
2025-06-01 08:04:22,126 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:04:22,642 - INFO - Request Parameters - Page 4:
2025-06-01 08:04:22,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:22,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:23,376 - INFO - API请求耗时: 734ms
2025-06-01 08:04:23,376 - INFO - Response - Page 4
2025-06-01 08:04:23,392 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:04:23,892 - INFO - Request Parameters - Page 5:
2025-06-01 08:04:23,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:23,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:24,626 - INFO - API请求耗时: 734ms
2025-06-01 08:04:24,626 - INFO - Response - Page 5
2025-06-01 08:04:24,626 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:04:25,126 - INFO - Request Parameters - Page 6:
2025-06-01 08:04:25,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:25,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:25,861 - INFO - API请求耗时: 734ms
2025-06-01 08:04:25,861 - INFO - Response - Page 6
2025-06-01 08:04:25,861 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:04:26,361 - INFO - Request Parameters - Page 7:
2025-06-01 08:04:26,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:26,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:27,095 - INFO - API请求耗时: 734ms
2025-06-01 08:04:27,095 - INFO - Response - Page 7
2025-06-01 08:04:27,111 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:04:27,611 - INFO - Request Parameters - Page 8:
2025-06-01 08:04:27,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:27,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:28,423 - INFO - API请求耗时: 812ms
2025-06-01 08:04:28,423 - INFO - Response - Page 8
2025-06-01 08:04:28,423 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:04:28,939 - INFO - Request Parameters - Page 9:
2025-06-01 08:04:28,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:28,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:29,720 - INFO - API请求耗时: 781ms
2025-06-01 08:04:29,720 - INFO - Response - Page 9
2025-06-01 08:04:29,720 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:04:30,251 - INFO - Request Parameters - Page 10:
2025-06-01 08:04:30,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:30,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:31,033 - INFO - API请求耗时: 781ms
2025-06-01 08:04:31,033 - INFO - Response - Page 10
2025-06-01 08:04:31,033 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:04:31,533 - INFO - Request Parameters - Page 11:
2025-06-01 08:04:31,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:31,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:32,314 - INFO - API请求耗时: 781ms
2025-06-01 08:04:32,314 - INFO - Response - Page 11
2025-06-01 08:04:32,314 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:04:32,830 - INFO - Request Parameters - Page 12:
2025-06-01 08:04:32,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:32,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:33,595 - INFO - API请求耗时: 766ms
2025-06-01 08:04:33,595 - INFO - Response - Page 12
2025-06-01 08:04:33,595 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:04:34,111 - INFO - Request Parameters - Page 13:
2025-06-01 08:04:34,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:34,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:34,861 - INFO - API请求耗时: 750ms
2025-06-01 08:04:34,861 - INFO - Response - Page 13
2025-06-01 08:04:34,861 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:04:35,361 - INFO - Request Parameters - Page 14:
2025-06-01 08:04:35,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:35,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:36,126 - INFO - API请求耗时: 766ms
2025-06-01 08:04:36,142 - INFO - Response - Page 14
2025-06-01 08:04:36,142 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:04:36,658 - INFO - Request Parameters - Page 15:
2025-06-01 08:04:36,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:36,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:37,486 - INFO - API请求耗时: 828ms
2025-06-01 08:04:37,486 - INFO - Response - Page 15
2025-06-01 08:04:37,486 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:04:38,001 - INFO - Request Parameters - Page 16:
2025-06-01 08:04:38,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:38,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:38,783 - INFO - API请求耗时: 781ms
2025-06-01 08:04:38,783 - INFO - Response - Page 16
2025-06-01 08:04:38,798 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:04:39,314 - INFO - Request Parameters - Page 17:
2025-06-01 08:04:39,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:39,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:40,048 - INFO - API请求耗时: 734ms
2025-06-01 08:04:40,048 - INFO - Response - Page 17
2025-06-01 08:04:40,048 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:04:40,548 - INFO - Request Parameters - Page 18:
2025-06-01 08:04:40,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:40,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:41,329 - INFO - API请求耗时: 781ms
2025-06-01 08:04:41,329 - INFO - Response - Page 18
2025-06-01 08:04:41,329 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:04:41,845 - INFO - Request Parameters - Page 19:
2025-06-01 08:04:41,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:41,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:42,704 - INFO - API请求耗时: 859ms
2025-06-01 08:04:42,704 - INFO - Response - Page 19
2025-06-01 08:04:42,704 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:04:43,220 - INFO - Request Parameters - Page 20:
2025-06-01 08:04:43,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:43,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:43,923 - INFO - API请求耗时: 703ms
2025-06-01 08:04:43,923 - INFO - Response - Page 20
2025-06-01 08:04:43,923 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:04:44,423 - INFO - Request Parameters - Page 21:
2025-06-01 08:04:44,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:44,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:45,204 - INFO - API请求耗时: 781ms
2025-06-01 08:04:45,204 - INFO - Response - Page 21
2025-06-01 08:04:45,204 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:04:45,720 - INFO - Request Parameters - Page 22:
2025-06-01 08:04:45,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:45,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:46,454 - INFO - API请求耗时: 734ms
2025-06-01 08:04:46,454 - INFO - Response - Page 22
2025-06-01 08:04:46,454 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:04:46,954 - INFO - Request Parameters - Page 23:
2025-06-01 08:04:46,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:46,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:47,751 - INFO - API请求耗时: 797ms
2025-06-01 08:04:47,751 - INFO - Response - Page 23
2025-06-01 08:04:47,751 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:04:48,267 - INFO - Request Parameters - Page 24:
2025-06-01 08:04:48,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:48,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:49,095 - INFO - API请求耗时: 828ms
2025-06-01 08:04:49,095 - INFO - Response - Page 24
2025-06-01 08:04:49,095 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:04:49,595 - INFO - Request Parameters - Page 25:
2025-06-01 08:04:49,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:49,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:50,314 - INFO - API请求耗时: 719ms
2025-06-01 08:04:50,314 - INFO - Response - Page 25
2025-06-01 08:04:50,314 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:04:50,829 - INFO - Request Parameters - Page 26:
2025-06-01 08:04:50,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:50,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:51,595 - INFO - API请求耗时: 766ms
2025-06-01 08:04:51,595 - INFO - Response - Page 26
2025-06-01 08:04:51,595 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:04:52,095 - INFO - Request Parameters - Page 27:
2025-06-01 08:04:52,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:52,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:52,876 - INFO - API请求耗时: 781ms
2025-06-01 08:04:52,876 - INFO - Response - Page 27
2025-06-01 08:04:52,876 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:04:53,392 - INFO - Request Parameters - Page 28:
2025-06-01 08:04:53,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:53,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:54,204 - INFO - API请求耗时: 812ms
2025-06-01 08:04:54,204 - INFO - Response - Page 28
2025-06-01 08:04:54,204 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:04:54,704 - INFO - Request Parameters - Page 29:
2025-06-01 08:04:54,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:54,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:55,470 - INFO - API请求耗时: 766ms
2025-06-01 08:04:55,470 - INFO - Response - Page 29
2025-06-01 08:04:55,470 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:04:55,986 - INFO - Request Parameters - Page 30:
2025-06-01 08:04:55,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:55,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:56,798 - INFO - API请求耗时: 812ms
2025-06-01 08:04:56,798 - INFO - Response - Page 30
2025-06-01 08:04:56,798 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:04:57,298 - INFO - Request Parameters - Page 31:
2025-06-01 08:04:57,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:57,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:58,048 - INFO - API请求耗时: 750ms
2025-06-01 08:04:58,048 - INFO - Response - Page 31
2025-06-01 08:04:58,064 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:04:58,564 - INFO - Request Parameters - Page 32:
2025-06-01 08:04:58,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:58,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:04:59,423 - INFO - API请求耗时: 859ms
2025-06-01 08:04:59,423 - INFO - Response - Page 32
2025-06-01 08:04:59,423 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:04:59,923 - INFO - Request Parameters - Page 33:
2025-06-01 08:04:59,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:04:59,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:00,689 - INFO - API请求耗时: 766ms
2025-06-01 08:05:00,689 - INFO - Response - Page 33
2025-06-01 08:05:00,689 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:05:01,204 - INFO - Request Parameters - Page 34:
2025-06-01 08:05:01,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:01,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:02,001 - INFO - API请求耗时: 797ms
2025-06-01 08:05:02,001 - INFO - Response - Page 34
2025-06-01 08:05:02,001 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:05:02,517 - INFO - Request Parameters - Page 35:
2025-06-01 08:05:02,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:02,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:03,251 - INFO - API请求耗时: 734ms
2025-06-01 08:05:03,251 - INFO - Response - Page 35
2025-06-01 08:05:03,251 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:05:03,767 - INFO - Request Parameters - Page 36:
2025-06-01 08:05:03,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:03,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:04,454 - INFO - API请求耗时: 687ms
2025-06-01 08:05:04,454 - INFO - Response - Page 36
2025-06-01 08:05:04,454 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:05:04,970 - INFO - Request Parameters - Page 37:
2025-06-01 08:05:04,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:04,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:05,720 - INFO - API请求耗时: 750ms
2025-06-01 08:05:05,720 - INFO - Response - Page 37
2025-06-01 08:05:05,720 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:05:06,236 - INFO - Request Parameters - Page 38:
2025-06-01 08:05:06,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:06,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:07,017 - INFO - API请求耗时: 781ms
2025-06-01 08:05:07,017 - INFO - Response - Page 38
2025-06-01 08:05:07,017 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:05:07,532 - INFO - Request Parameters - Page 39:
2025-06-01 08:05:07,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:07,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:08,251 - INFO - API请求耗时: 719ms
2025-06-01 08:05:08,251 - INFO - Response - Page 39
2025-06-01 08:05:08,251 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:05:08,751 - INFO - Request Parameters - Page 40:
2025-06-01 08:05:08,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:08,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:09,548 - INFO - API请求耗时: 797ms
2025-06-01 08:05:09,548 - INFO - Response - Page 40
2025-06-01 08:05:09,548 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:05:10,064 - INFO - Request Parameters - Page 41:
2025-06-01 08:05:10,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:10,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:10,907 - INFO - API请求耗时: 844ms
2025-06-01 08:05:10,907 - INFO - Response - Page 41
2025-06-01 08:05:10,907 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:05:11,423 - INFO - Request Parameters - Page 42:
2025-06-01 08:05:11,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:11,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:12,173 - INFO - API请求耗时: 750ms
2025-06-01 08:05:12,173 - INFO - Response - Page 42
2025-06-01 08:05:12,173 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:05:12,689 - INFO - Request Parameters - Page 43:
2025-06-01 08:05:12,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:12,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:13,423 - INFO - API请求耗时: 734ms
2025-06-01 08:05:13,423 - INFO - Response - Page 43
2025-06-01 08:05:13,423 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:05:13,923 - INFO - Request Parameters - Page 44:
2025-06-01 08:05:13,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:13,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:14,704 - INFO - API请求耗时: 781ms
2025-06-01 08:05:14,704 - INFO - Response - Page 44
2025-06-01 08:05:14,704 - INFO - 第 44 页获取到 100 条记录
2025-06-01 08:05:15,220 - INFO - Request Parameters - Page 45:
2025-06-01 08:05:15,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:15,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:15,985 - INFO - API请求耗时: 766ms
2025-06-01 08:05:15,985 - INFO - Response - Page 45
2025-06-01 08:05:15,985 - INFO - 第 45 页获取到 100 条记录
2025-06-01 08:05:16,501 - INFO - Request Parameters - Page 46:
2025-06-01 08:05:16,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:16,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:17,267 - INFO - API请求耗时: 766ms
2025-06-01 08:05:17,267 - INFO - Response - Page 46
2025-06-01 08:05:17,267 - INFO - 第 46 页获取到 100 条记录
2025-06-01 08:05:17,782 - INFO - Request Parameters - Page 47:
2025-06-01 08:05:17,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:17,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:18,595 - INFO - API请求耗时: 812ms
2025-06-01 08:05:18,595 - INFO - Response - Page 47
2025-06-01 08:05:18,610 - INFO - 第 47 页获取到 100 条记录
2025-06-01 08:05:19,110 - INFO - Request Parameters - Page 48:
2025-06-01 08:05:19,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:19,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:19,860 - INFO - API请求耗时: 750ms
2025-06-01 08:05:19,860 - INFO - Response - Page 48
2025-06-01 08:05:19,860 - INFO - 第 48 页获取到 100 条记录
2025-06-01 08:05:20,376 - INFO - Request Parameters - Page 49:
2025-06-01 08:05:20,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:20,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745251200081, 1745769600081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:21,048 - INFO - API请求耗时: 672ms
2025-06-01 08:05:21,048 - INFO - Response - Page 49
2025-06-01 08:05:21,048 - INFO - 第 49 页获取到 71 条记录
2025-06-01 08:05:21,048 - INFO - 查询完成，共获取到 4871 条记录
2025-06-01 08:05:21,048 - INFO - 分段 4 查询成功，获取到 4871 条记录
2025-06-01 08:05:22,064 - INFO - 查询分段 5: 2025-04-29 至 2025-05-05
2025-06-01 08:05:22,064 - INFO - 查询日期范围: 2025-04-29 至 2025-05-05，使用分页查询，每页 100 条记录
2025-06-01 08:05:22,064 - INFO - Request Parameters - Page 1:
2025-06-01 08:05:22,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:22,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:22,814 - INFO - API请求耗时: 750ms
2025-06-01 08:05:22,814 - INFO - Response - Page 1
2025-06-01 08:05:22,814 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:05:23,314 - INFO - Request Parameters - Page 2:
2025-06-01 08:05:23,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:23,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:24,032 - INFO - API请求耗时: 719ms
2025-06-01 08:05:24,032 - INFO - Response - Page 2
2025-06-01 08:05:24,032 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:05:24,548 - INFO - Request Parameters - Page 3:
2025-06-01 08:05:24,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:24,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:25,282 - INFO - API请求耗时: 734ms
2025-06-01 08:05:25,282 - INFO - Response - Page 3
2025-06-01 08:05:25,282 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:05:25,798 - INFO - Request Parameters - Page 4:
2025-06-01 08:05:25,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:25,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:26,595 - INFO - API请求耗时: 797ms
2025-06-01 08:05:26,595 - INFO - Response - Page 4
2025-06-01 08:05:26,595 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:05:27,110 - INFO - Request Parameters - Page 5:
2025-06-01 08:05:27,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:27,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:27,892 - INFO - API请求耗时: 781ms
2025-06-01 08:05:27,892 - INFO - Response - Page 5
2025-06-01 08:05:27,892 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:05:28,407 - INFO - Request Parameters - Page 6:
2025-06-01 08:05:28,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:28,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:29,079 - INFO - API请求耗时: 672ms
2025-06-01 08:05:29,079 - INFO - Response - Page 6
2025-06-01 08:05:29,079 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:05:29,579 - INFO - Request Parameters - Page 7:
2025-06-01 08:05:29,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:29,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:30,360 - INFO - API请求耗时: 781ms
2025-06-01 08:05:30,360 - INFO - Response - Page 7
2025-06-01 08:05:30,360 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:05:30,876 - INFO - Request Parameters - Page 8:
2025-06-01 08:05:30,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:30,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:31,610 - INFO - API请求耗时: 734ms
2025-06-01 08:05:31,610 - INFO - Response - Page 8
2025-06-01 08:05:31,610 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:05:32,126 - INFO - Request Parameters - Page 9:
2025-06-01 08:05:32,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:32,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:32,892 - INFO - API请求耗时: 766ms
2025-06-01 08:05:32,892 - INFO - Response - Page 9
2025-06-01 08:05:32,892 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:05:33,392 - INFO - Request Parameters - Page 10:
2025-06-01 08:05:33,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:33,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:34,126 - INFO - API请求耗时: 734ms
2025-06-01 08:05:34,126 - INFO - Response - Page 10
2025-06-01 08:05:34,126 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:05:34,642 - INFO - Request Parameters - Page 11:
2025-06-01 08:05:34,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:34,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:35,376 - INFO - API请求耗时: 734ms
2025-06-01 08:05:35,376 - INFO - Response - Page 11
2025-06-01 08:05:35,376 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:05:35,892 - INFO - Request Parameters - Page 12:
2025-06-01 08:05:35,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:35,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:36,642 - INFO - API请求耗时: 750ms
2025-06-01 08:05:36,642 - INFO - Response - Page 12
2025-06-01 08:05:36,642 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:05:37,157 - INFO - Request Parameters - Page 13:
2025-06-01 08:05:37,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:37,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:37,970 - INFO - API请求耗时: 812ms
2025-06-01 08:05:37,970 - INFO - Response - Page 13
2025-06-01 08:05:37,970 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:05:38,485 - INFO - Request Parameters - Page 14:
2025-06-01 08:05:38,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:38,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:39,267 - INFO - API请求耗时: 781ms
2025-06-01 08:05:39,267 - INFO - Response - Page 14
2025-06-01 08:05:39,267 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:05:39,782 - INFO - Request Parameters - Page 15:
2025-06-01 08:05:39,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:39,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:40,610 - INFO - API请求耗时: 828ms
2025-06-01 08:05:40,610 - INFO - Response - Page 15
2025-06-01 08:05:40,610 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:05:41,126 - INFO - Request Parameters - Page 16:
2025-06-01 08:05:41,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:41,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:41,876 - INFO - API请求耗时: 750ms
2025-06-01 08:05:41,892 - INFO - Response - Page 16
2025-06-01 08:05:41,892 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:05:42,407 - INFO - Request Parameters - Page 17:
2025-06-01 08:05:42,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:42,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:43,142 - INFO - API请求耗时: 734ms
2025-06-01 08:05:43,142 - INFO - Response - Page 17
2025-06-01 08:05:43,142 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:05:43,657 - INFO - Request Parameters - Page 18:
2025-06-01 08:05:43,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:43,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:44,454 - INFO - API请求耗时: 797ms
2025-06-01 08:05:44,454 - INFO - Response - Page 18
2025-06-01 08:05:44,454 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:05:44,970 - INFO - Request Parameters - Page 19:
2025-06-01 08:05:44,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:44,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:45,751 - INFO - API请求耗时: 781ms
2025-06-01 08:05:45,751 - INFO - Response - Page 19
2025-06-01 08:05:45,751 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:05:46,251 - INFO - Request Parameters - Page 20:
2025-06-01 08:05:46,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:46,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:46,923 - INFO - API请求耗时: 672ms
2025-06-01 08:05:46,923 - INFO - Response - Page 20
2025-06-01 08:05:46,923 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:05:47,423 - INFO - Request Parameters - Page 21:
2025-06-01 08:05:47,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:47,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:48,173 - INFO - API请求耗时: 750ms
2025-06-01 08:05:48,173 - INFO - Response - Page 21
2025-06-01 08:05:48,173 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:05:48,673 - INFO - Request Parameters - Page 22:
2025-06-01 08:05:48,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:48,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:49,423 - INFO - API请求耗时: 750ms
2025-06-01 08:05:49,423 - INFO - Response - Page 22
2025-06-01 08:05:49,423 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:05:49,938 - INFO - Request Parameters - Page 23:
2025-06-01 08:05:49,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:49,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:50,642 - INFO - API请求耗时: 703ms
2025-06-01 08:05:50,642 - INFO - Response - Page 23
2025-06-01 08:05:50,642 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:05:51,157 - INFO - Request Parameters - Page 24:
2025-06-01 08:05:51,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:51,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:51,907 - INFO - API请求耗时: 750ms
2025-06-01 08:05:51,907 - INFO - Response - Page 24
2025-06-01 08:05:51,907 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:05:52,407 - INFO - Request Parameters - Page 25:
2025-06-01 08:05:52,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:52,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:53,126 - INFO - API请求耗时: 719ms
2025-06-01 08:05:53,126 - INFO - Response - Page 25
2025-06-01 08:05:53,126 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:05:53,626 - INFO - Request Parameters - Page 26:
2025-06-01 08:05:53,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:53,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:54,345 - INFO - API请求耗时: 719ms
2025-06-01 08:05:54,345 - INFO - Response - Page 26
2025-06-01 08:05:54,345 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:05:54,860 - INFO - Request Parameters - Page 27:
2025-06-01 08:05:54,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:54,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:55,641 - INFO - API请求耗时: 781ms
2025-06-01 08:05:55,641 - INFO - Response - Page 27
2025-06-01 08:05:55,641 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:05:56,141 - INFO - Request Parameters - Page 28:
2025-06-01 08:05:56,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:56,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:56,860 - INFO - API请求耗时: 719ms
2025-06-01 08:05:56,860 - INFO - Response - Page 28
2025-06-01 08:05:56,860 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:05:57,360 - INFO - Request Parameters - Page 29:
2025-06-01 08:05:57,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:57,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:58,079 - INFO - API请求耗时: 719ms
2025-06-01 08:05:58,079 - INFO - Response - Page 29
2025-06-01 08:05:58,079 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:05:58,595 - INFO - Request Parameters - Page 30:
2025-06-01 08:05:58,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:58,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:05:59,454 - INFO - API请求耗时: 859ms
2025-06-01 08:05:59,454 - INFO - Response - Page 30
2025-06-01 08:05:59,454 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:05:59,954 - INFO - Request Parameters - Page 31:
2025-06-01 08:05:59,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:05:59,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:00,704 - INFO - API请求耗时: 750ms
2025-06-01 08:06:00,704 - INFO - Response - Page 31
2025-06-01 08:06:00,704 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:06:01,220 - INFO - Request Parameters - Page 32:
2025-06-01 08:06:01,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:01,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:01,954 - INFO - API请求耗时: 734ms
2025-06-01 08:06:01,954 - INFO - Response - Page 32
2025-06-01 08:06:01,954 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:06:02,470 - INFO - Request Parameters - Page 33:
2025-06-01 08:06:02,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:02,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:03,235 - INFO - API请求耗时: 766ms
2025-06-01 08:06:03,235 - INFO - Response - Page 33
2025-06-01 08:06:03,235 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:06:03,751 - INFO - Request Parameters - Page 34:
2025-06-01 08:06:03,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:03,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:04,548 - INFO - API请求耗时: 797ms
2025-06-01 08:06:04,548 - INFO - Response - Page 34
2025-06-01 08:06:04,548 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:06:05,048 - INFO - Request Parameters - Page 35:
2025-06-01 08:06:05,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:05,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:05,829 - INFO - API请求耗时: 781ms
2025-06-01 08:06:05,829 - INFO - Response - Page 35
2025-06-01 08:06:05,829 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:06:06,329 - INFO - Request Parameters - Page 36:
2025-06-01 08:06:06,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:06,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:07,063 - INFO - API请求耗时: 734ms
2025-06-01 08:06:07,063 - INFO - Response - Page 36
2025-06-01 08:06:07,063 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:06:07,563 - INFO - Request Parameters - Page 37:
2025-06-01 08:06:07,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:07,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:08,329 - INFO - API请求耗时: 766ms
2025-06-01 08:06:08,329 - INFO - Response - Page 37
2025-06-01 08:06:08,329 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:06:08,845 - INFO - Request Parameters - Page 38:
2025-06-01 08:06:08,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:08,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:09,688 - INFO - API请求耗时: 844ms
2025-06-01 08:06:09,688 - INFO - Response - Page 38
2025-06-01 08:06:09,688 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:06:10,204 - INFO - Request Parameters - Page 39:
2025-06-01 08:06:10,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:10,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:11,048 - INFO - API请求耗时: 844ms
2025-06-01 08:06:11,048 - INFO - Response - Page 39
2025-06-01 08:06:11,048 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:06:11,548 - INFO - Request Parameters - Page 40:
2025-06-01 08:06:11,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:11,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:12,298 - INFO - API请求耗时: 750ms
2025-06-01 08:06:12,298 - INFO - Response - Page 40
2025-06-01 08:06:12,298 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:06:12,813 - INFO - Request Parameters - Page 41:
2025-06-01 08:06:12,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:12,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:13,579 - INFO - API请求耗时: 766ms
2025-06-01 08:06:13,579 - INFO - Response - Page 41
2025-06-01 08:06:13,579 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:06:14,094 - INFO - Request Parameters - Page 42:
2025-06-01 08:06:14,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:14,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:14,782 - INFO - API请求耗时: 688ms
2025-06-01 08:06:14,782 - INFO - Response - Page 42
2025-06-01 08:06:14,782 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:06:15,298 - INFO - Request Parameters - Page 43:
2025-06-01 08:06:15,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:15,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:16,079 - INFO - API请求耗时: 781ms
2025-06-01 08:06:16,079 - INFO - Response - Page 43
2025-06-01 08:06:16,079 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:06:16,579 - INFO - Request Parameters - Page 44:
2025-06-01 08:06:16,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:16,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:17,298 - INFO - API请求耗时: 719ms
2025-06-01 08:06:17,298 - INFO - Response - Page 44
2025-06-01 08:06:17,298 - INFO - 第 44 页获取到 100 条记录
2025-06-01 08:06:17,813 - INFO - Request Parameters - Page 45:
2025-06-01 08:06:17,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:17,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:18,594 - INFO - API请求耗时: 781ms
2025-06-01 08:06:18,594 - INFO - Response - Page 45
2025-06-01 08:06:18,594 - INFO - 第 45 页获取到 100 条记录
2025-06-01 08:06:19,110 - INFO - Request Parameters - Page 46:
2025-06-01 08:06:19,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:19,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:19,860 - INFO - API请求耗时: 750ms
2025-06-01 08:06:19,860 - INFO - Response - Page 46
2025-06-01 08:06:19,860 - INFO - 第 46 页获取到 100 条记录
2025-06-01 08:06:20,360 - INFO - Request Parameters - Page 47:
2025-06-01 08:06:20,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:20,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:21,110 - INFO - API请求耗时: 750ms
2025-06-01 08:06:21,126 - INFO - Response - Page 47
2025-06-01 08:06:21,126 - INFO - 第 47 页获取到 100 条记录
2025-06-01 08:06:21,641 - INFO - Request Parameters - Page 48:
2025-06-01 08:06:21,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:21,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:22,360 - INFO - API请求耗时: 719ms
2025-06-01 08:06:22,360 - INFO - Response - Page 48
2025-06-01 08:06:22,376 - INFO - 第 48 页获取到 100 条记录
2025-06-01 08:06:22,891 - INFO - Request Parameters - Page 49:
2025-06-01 08:06:22,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:22,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:23,673 - INFO - API请求耗时: 781ms
2025-06-01 08:06:23,673 - INFO - Response - Page 49
2025-06-01 08:06:23,673 - INFO - 第 49 页获取到 100 条记录
2025-06-01 08:06:24,188 - INFO - Request Parameters - Page 50:
2025-06-01 08:06:24,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:24,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 50, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:24,938 - INFO - API请求耗时: 750ms
2025-06-01 08:06:24,938 - INFO - Response - Page 50
2025-06-01 08:06:24,938 - INFO - 第 50 页获取到 100 条记录
2025-06-01 08:06:25,454 - INFO - Request Parameters - Page 51:
2025-06-01 08:06:25,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:25,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 51, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:26,157 - INFO - API请求耗时: 703ms
2025-06-01 08:06:26,157 - INFO - Response - Page 51
2025-06-01 08:06:26,157 - INFO - 第 51 页获取到 100 条记录
2025-06-01 08:06:26,673 - INFO - Request Parameters - Page 52:
2025-06-01 08:06:26,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:26,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 52, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:27,454 - INFO - API请求耗时: 781ms
2025-06-01 08:06:27,454 - INFO - Response - Page 52
2025-06-01 08:06:27,454 - INFO - 第 52 页获取到 100 条记录
2025-06-01 08:06:27,969 - INFO - Request Parameters - Page 53:
2025-06-01 08:06:27,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:27,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 53, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:28,782 - INFO - API请求耗时: 813ms
2025-06-01 08:06:28,782 - INFO - Response - Page 53
2025-06-01 08:06:28,782 - INFO - 第 53 页获取到 100 条记录
2025-06-01 08:06:29,297 - INFO - Request Parameters - Page 54:
2025-06-01 08:06:29,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:29,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 54, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:30,094 - INFO - API请求耗时: 797ms
2025-06-01 08:06:30,094 - INFO - Response - Page 54
2025-06-01 08:06:30,094 - INFO - 第 54 页获取到 100 条记录
2025-06-01 08:06:30,610 - INFO - Request Parameters - Page 55:
2025-06-01 08:06:30,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:30,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 55, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000081, 1746374400081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:31,219 - INFO - API请求耗时: 609ms
2025-06-01 08:06:31,219 - INFO - Response - Page 55
2025-06-01 08:06:31,219 - INFO - 第 55 页获取到 39 条记录
2025-06-01 08:06:31,219 - INFO - 查询完成，共获取到 5439 条记录
2025-06-01 08:06:31,219 - INFO - 分段 5 查询成功，获取到 5439 条记录
2025-06-01 08:06:32,235 - INFO - 查询分段 6: 2025-05-06 至 2025-05-12
2025-06-01 08:06:32,235 - INFO - 查询日期范围: 2025-05-06 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-01 08:06:32,235 - INFO - Request Parameters - Page 1:
2025-06-01 08:06:32,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:32,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:32,985 - INFO - API请求耗时: 750ms
2025-06-01 08:06:32,985 - INFO - Response - Page 1
2025-06-01 08:06:32,985 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:06:33,501 - INFO - Request Parameters - Page 2:
2025-06-01 08:06:33,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:33,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:34,188 - INFO - API请求耗时: 687ms
2025-06-01 08:06:34,188 - INFO - Response - Page 2
2025-06-01 08:06:34,188 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:06:34,704 - INFO - Request Parameters - Page 3:
2025-06-01 08:06:34,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:34,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:35,438 - INFO - API请求耗时: 734ms
2025-06-01 08:06:35,438 - INFO - Response - Page 3
2025-06-01 08:06:35,438 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:06:35,954 - INFO - Request Parameters - Page 4:
2025-06-01 08:06:35,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:35,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:36,704 - INFO - API请求耗时: 750ms
2025-06-01 08:06:36,704 - INFO - Response - Page 4
2025-06-01 08:06:36,704 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:06:37,219 - INFO - Request Parameters - Page 5:
2025-06-01 08:06:37,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:37,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:37,922 - INFO - API请求耗时: 703ms
2025-06-01 08:06:37,922 - INFO - Response - Page 5
2025-06-01 08:06:37,922 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:06:38,422 - INFO - Request Parameters - Page 6:
2025-06-01 08:06:38,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:38,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:39,063 - INFO - API请求耗时: 641ms
2025-06-01 08:06:39,079 - INFO - Response - Page 6
2025-06-01 08:06:39,079 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:06:39,594 - INFO - Request Parameters - Page 7:
2025-06-01 08:06:39,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:39,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:40,344 - INFO - API请求耗时: 750ms
2025-06-01 08:06:40,344 - INFO - Response - Page 7
2025-06-01 08:06:40,344 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:06:40,860 - INFO - Request Parameters - Page 8:
2025-06-01 08:06:40,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:40,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:41,657 - INFO - API请求耗时: 797ms
2025-06-01 08:06:41,657 - INFO - Response - Page 8
2025-06-01 08:06:41,657 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:06:42,172 - INFO - Request Parameters - Page 9:
2025-06-01 08:06:42,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:42,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:42,938 - INFO - API请求耗时: 766ms
2025-06-01 08:06:42,938 - INFO - Response - Page 9
2025-06-01 08:06:42,938 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:06:43,454 - INFO - Request Parameters - Page 10:
2025-06-01 08:06:43,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:43,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:44,188 - INFO - API请求耗时: 734ms
2025-06-01 08:06:44,188 - INFO - Response - Page 10
2025-06-01 08:06:44,188 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:06:44,688 - INFO - Request Parameters - Page 11:
2025-06-01 08:06:44,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:44,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:45,485 - INFO - API请求耗时: 797ms
2025-06-01 08:06:45,485 - INFO - Response - Page 11
2025-06-01 08:06:45,485 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:06:46,001 - INFO - Request Parameters - Page 12:
2025-06-01 08:06:46,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:46,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:46,735 - INFO - API请求耗时: 734ms
2025-06-01 08:06:46,735 - INFO - Response - Page 12
2025-06-01 08:06:46,735 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:06:47,250 - INFO - Request Parameters - Page 13:
2025-06-01 08:06:47,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:47,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:48,016 - INFO - API请求耗时: 766ms
2025-06-01 08:06:48,016 - INFO - Response - Page 13
2025-06-01 08:06:48,016 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:06:48,516 - INFO - Request Parameters - Page 14:
2025-06-01 08:06:48,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:48,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:49,204 - INFO - API请求耗时: 687ms
2025-06-01 08:06:49,204 - INFO - Response - Page 14
2025-06-01 08:06:49,204 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:06:49,719 - INFO - Request Parameters - Page 15:
2025-06-01 08:06:49,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:49,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:50,469 - INFO - API请求耗时: 750ms
2025-06-01 08:06:50,485 - INFO - Response - Page 15
2025-06-01 08:06:50,485 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:06:51,000 - INFO - Request Parameters - Page 16:
2025-06-01 08:06:51,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:51,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:51,719 - INFO - API请求耗时: 719ms
2025-06-01 08:06:51,719 - INFO - Response - Page 16
2025-06-01 08:06:51,719 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:06:52,219 - INFO - Request Parameters - Page 17:
2025-06-01 08:06:52,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:52,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:52,969 - INFO - API请求耗时: 750ms
2025-06-01 08:06:52,969 - INFO - Response - Page 17
2025-06-01 08:06:52,969 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:06:53,469 - INFO - Request Parameters - Page 18:
2025-06-01 08:06:53,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:53,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:54,266 - INFO - API请求耗时: 797ms
2025-06-01 08:06:54,266 - INFO - Response - Page 18
2025-06-01 08:06:54,266 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:06:54,782 - INFO - Request Parameters - Page 19:
2025-06-01 08:06:54,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:54,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:55,532 - INFO - API请求耗时: 750ms
2025-06-01 08:06:55,532 - INFO - Response - Page 19
2025-06-01 08:06:55,532 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:06:56,032 - INFO - Request Parameters - Page 20:
2025-06-01 08:06:56,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:56,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:56,766 - INFO - API请求耗时: 734ms
2025-06-01 08:06:56,766 - INFO - Response - Page 20
2025-06-01 08:06:56,766 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:06:57,266 - INFO - Request Parameters - Page 21:
2025-06-01 08:06:57,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:57,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:57,938 - INFO - API请求耗时: 672ms
2025-06-01 08:06:57,938 - INFO - Response - Page 21
2025-06-01 08:06:57,938 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:06:58,454 - INFO - Request Parameters - Page 22:
2025-06-01 08:06:58,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:58,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:06:59,219 - INFO - API请求耗时: 766ms
2025-06-01 08:06:59,219 - INFO - Response - Page 22
2025-06-01 08:06:59,219 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:06:59,719 - INFO - Request Parameters - Page 23:
2025-06-01 08:06:59,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:06:59,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:00,454 - INFO - API请求耗时: 734ms
2025-06-01 08:07:00,454 - INFO - Response - Page 23
2025-06-01 08:07:00,454 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:07:00,969 - INFO - Request Parameters - Page 24:
2025-06-01 08:07:00,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:00,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:01,766 - INFO - API请求耗时: 797ms
2025-06-01 08:07:01,766 - INFO - Response - Page 24
2025-06-01 08:07:01,766 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:07:02,266 - INFO - Request Parameters - Page 25:
2025-06-01 08:07:02,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:02,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:03,047 - INFO - API请求耗时: 781ms
2025-06-01 08:07:03,047 - INFO - Response - Page 25
2025-06-01 08:07:03,047 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:07:03,563 - INFO - Request Parameters - Page 26:
2025-06-01 08:07:03,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:03,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:04,329 - INFO - API请求耗时: 766ms
2025-06-01 08:07:04,329 - INFO - Response - Page 26
2025-06-01 08:07:04,329 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:07:04,844 - INFO - Request Parameters - Page 27:
2025-06-01 08:07:04,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:04,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:05,641 - INFO - API请求耗时: 797ms
2025-06-01 08:07:05,641 - INFO - Response - Page 27
2025-06-01 08:07:05,641 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:07:06,141 - INFO - Request Parameters - Page 28:
2025-06-01 08:07:06,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:06,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:06,813 - INFO - API请求耗时: 672ms
2025-06-01 08:07:06,813 - INFO - Response - Page 28
2025-06-01 08:07:06,813 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:07:07,328 - INFO - Request Parameters - Page 29:
2025-06-01 08:07:07,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:07,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:08,172 - INFO - API请求耗时: 844ms
2025-06-01 08:07:08,172 - INFO - Response - Page 29
2025-06-01 08:07:08,172 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:07:08,688 - INFO - Request Parameters - Page 30:
2025-06-01 08:07:08,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:08,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:09,422 - INFO - API请求耗时: 734ms
2025-06-01 08:07:09,422 - INFO - Response - Page 30
2025-06-01 08:07:09,422 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:07:09,922 - INFO - Request Parameters - Page 31:
2025-06-01 08:07:09,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:09,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:10,688 - INFO - API请求耗时: 766ms
2025-06-01 08:07:10,688 - INFO - Response - Page 31
2025-06-01 08:07:10,688 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:07:11,203 - INFO - Request Parameters - Page 32:
2025-06-01 08:07:11,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:11,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:11,985 - INFO - API请求耗时: 781ms
2025-06-01 08:07:11,985 - INFO - Response - Page 32
2025-06-01 08:07:11,985 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:07:12,485 - INFO - Request Parameters - Page 33:
2025-06-01 08:07:12,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:12,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:13,219 - INFO - API请求耗时: 734ms
2025-06-01 08:07:13,219 - INFO - Response - Page 33
2025-06-01 08:07:13,219 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:07:13,735 - INFO - Request Parameters - Page 34:
2025-06-01 08:07:13,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:13,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:14,485 - INFO - API请求耗时: 750ms
2025-06-01 08:07:14,485 - INFO - Response - Page 34
2025-06-01 08:07:14,485 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:07:14,985 - INFO - Request Parameters - Page 35:
2025-06-01 08:07:14,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:14,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:15,688 - INFO - API请求耗时: 703ms
2025-06-01 08:07:15,688 - INFO - Response - Page 35
2025-06-01 08:07:15,688 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:07:16,203 - INFO - Request Parameters - Page 36:
2025-06-01 08:07:16,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:16,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:16,907 - INFO - API请求耗时: 703ms
2025-06-01 08:07:16,907 - INFO - Response - Page 36
2025-06-01 08:07:16,907 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:07:17,422 - INFO - Request Parameters - Page 37:
2025-06-01 08:07:17,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:17,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:18,141 - INFO - API请求耗时: 719ms
2025-06-01 08:07:18,141 - INFO - Response - Page 37
2025-06-01 08:07:18,141 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:07:18,641 - INFO - Request Parameters - Page 38:
2025-06-01 08:07:18,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:18,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:19,453 - INFO - API请求耗时: 812ms
2025-06-01 08:07:19,453 - INFO - Response - Page 38
2025-06-01 08:07:19,453 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:07:19,969 - INFO - Request Parameters - Page 39:
2025-06-01 08:07:19,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:19,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:20,750 - INFO - API请求耗时: 781ms
2025-06-01 08:07:20,750 - INFO - Response - Page 39
2025-06-01 08:07:20,750 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:07:21,266 - INFO - Request Parameters - Page 40:
2025-06-01 08:07:21,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:21,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:22,016 - INFO - API请求耗时: 750ms
2025-06-01 08:07:22,016 - INFO - Response - Page 40
2025-06-01 08:07:22,016 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:07:22,516 - INFO - Request Parameters - Page 41:
2025-06-01 08:07:22,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:22,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:23,250 - INFO - API请求耗时: 734ms
2025-06-01 08:07:23,250 - INFO - Response - Page 41
2025-06-01 08:07:23,250 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:07:23,766 - INFO - Request Parameters - Page 42:
2025-06-01 08:07:23,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:23,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:24,516 - INFO - API请求耗时: 750ms
2025-06-01 08:07:24,516 - INFO - Response - Page 42
2025-06-01 08:07:24,516 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:07:25,031 - INFO - Request Parameters - Page 43:
2025-06-01 08:07:25,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:25,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:25,828 - INFO - API请求耗时: 797ms
2025-06-01 08:07:25,828 - INFO - Response - Page 43
2025-06-01 08:07:25,828 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:07:26,328 - INFO - Request Parameters - Page 44:
2025-06-01 08:07:26,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:26,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:27,031 - INFO - API请求耗时: 703ms
2025-06-01 08:07:27,031 - INFO - Response - Page 44
2025-06-01 08:07:27,031 - INFO - 第 44 页获取到 100 条记录
2025-06-01 08:07:27,531 - INFO - Request Parameters - Page 45:
2025-06-01 08:07:27,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:27,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:28,281 - INFO - API请求耗时: 750ms
2025-06-01 08:07:28,281 - INFO - Response - Page 45
2025-06-01 08:07:28,281 - INFO - 第 45 页获取到 100 条记录
2025-06-01 08:07:28,797 - INFO - Request Parameters - Page 46:
2025-06-01 08:07:28,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:28,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 46, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:29,469 - INFO - API请求耗时: 672ms
2025-06-01 08:07:29,469 - INFO - Response - Page 46
2025-06-01 08:07:29,469 - INFO - 第 46 页获取到 100 条记录
2025-06-01 08:07:29,985 - INFO - Request Parameters - Page 47:
2025-06-01 08:07:29,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:29,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 47, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:30,766 - INFO - API请求耗时: 781ms
2025-06-01 08:07:30,766 - INFO - Response - Page 47
2025-06-01 08:07:30,766 - INFO - 第 47 页获取到 100 条记录
2025-06-01 08:07:31,266 - INFO - Request Parameters - Page 48:
2025-06-01 08:07:31,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:31,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 48, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:32,047 - INFO - API请求耗时: 781ms
2025-06-01 08:07:32,047 - INFO - Response - Page 48
2025-06-01 08:07:32,047 - INFO - 第 48 页获取到 100 条记录
2025-06-01 08:07:32,578 - INFO - Request Parameters - Page 49:
2025-06-01 08:07:32,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:32,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 49, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746460800081, 1746979200081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:33,297 - INFO - API请求耗时: 719ms
2025-06-01 08:07:33,297 - INFO - Response - Page 49
2025-06-01 08:07:33,297 - INFO - 第 49 页获取到 78 条记录
2025-06-01 08:07:33,297 - INFO - 查询完成，共获取到 4878 条记录
2025-06-01 08:07:33,297 - INFO - 分段 6 查询成功，获取到 4878 条记录
2025-06-01 08:07:34,313 - INFO - 查询分段 7: 2025-05-13 至 2025-05-19
2025-06-01 08:07:34,313 - INFO - 查询日期范围: 2025-05-13 至 2025-05-19，使用分页查询，每页 100 条记录
2025-06-01 08:07:34,313 - INFO - Request Parameters - Page 1:
2025-06-01 08:07:34,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:34,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:35,094 - INFO - API请求耗时: 781ms
2025-06-01 08:07:35,094 - INFO - Response - Page 1
2025-06-01 08:07:35,094 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:07:35,610 - INFO - Request Parameters - Page 2:
2025-06-01 08:07:35,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:35,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:36,297 - INFO - API请求耗时: 688ms
2025-06-01 08:07:36,297 - INFO - Response - Page 2
2025-06-01 08:07:36,297 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:07:36,797 - INFO - Request Parameters - Page 3:
2025-06-01 08:07:36,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:36,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:37,516 - INFO - API请求耗时: 719ms
2025-06-01 08:07:37,516 - INFO - Response - Page 3
2025-06-01 08:07:37,516 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:07:38,031 - INFO - Request Parameters - Page 4:
2025-06-01 08:07:38,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:38,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:38,906 - INFO - API请求耗时: 875ms
2025-06-01 08:07:38,906 - INFO - Response - Page 4
2025-06-01 08:07:38,906 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:07:39,422 - INFO - Request Parameters - Page 5:
2025-06-01 08:07:39,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:39,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:40,125 - INFO - API请求耗时: 703ms
2025-06-01 08:07:40,125 - INFO - Response - Page 5
2025-06-01 08:07:40,125 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:07:40,625 - INFO - Request Parameters - Page 6:
2025-06-01 08:07:40,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:40,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:41,297 - INFO - API请求耗时: 672ms
2025-06-01 08:07:41,297 - INFO - Response - Page 6
2025-06-01 08:07:41,297 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:07:41,813 - INFO - Request Parameters - Page 7:
2025-06-01 08:07:41,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:41,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:42,531 - INFO - API请求耗时: 719ms
2025-06-01 08:07:42,531 - INFO - Response - Page 7
2025-06-01 08:07:42,531 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:07:43,047 - INFO - Request Parameters - Page 8:
2025-06-01 08:07:43,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:43,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:43,750 - INFO - API请求耗时: 703ms
2025-06-01 08:07:43,750 - INFO - Response - Page 8
2025-06-01 08:07:43,750 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:07:44,266 - INFO - Request Parameters - Page 9:
2025-06-01 08:07:44,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:44,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:45,000 - INFO - API请求耗时: 734ms
2025-06-01 08:07:45,000 - INFO - Response - Page 9
2025-06-01 08:07:45,000 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:07:45,500 - INFO - Request Parameters - Page 10:
2025-06-01 08:07:45,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:45,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:46,438 - INFO - API请求耗时: 937ms
2025-06-01 08:07:46,438 - INFO - Response - Page 10
2025-06-01 08:07:46,438 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:07:46,938 - INFO - Request Parameters - Page 11:
2025-06-01 08:07:46,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:46,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:47,781 - INFO - API请求耗时: 844ms
2025-06-01 08:07:47,781 - INFO - Response - Page 11
2025-06-01 08:07:47,781 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:07:48,297 - INFO - Request Parameters - Page 12:
2025-06-01 08:07:48,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:48,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:49,031 - INFO - API请求耗时: 734ms
2025-06-01 08:07:49,031 - INFO - Response - Page 12
2025-06-01 08:07:49,031 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:07:49,531 - INFO - Request Parameters - Page 13:
2025-06-01 08:07:49,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:49,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:50,297 - INFO - API请求耗时: 766ms
2025-06-01 08:07:50,297 - INFO - Response - Page 13
2025-06-01 08:07:50,297 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:07:50,813 - INFO - Request Parameters - Page 14:
2025-06-01 08:07:50,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:50,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:51,563 - INFO - API请求耗时: 750ms
2025-06-01 08:07:51,563 - INFO - Response - Page 14
2025-06-01 08:07:51,578 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:07:52,078 - INFO - Request Parameters - Page 15:
2025-06-01 08:07:52,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:52,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:52,766 - INFO - API请求耗时: 687ms
2025-06-01 08:07:52,766 - INFO - Response - Page 15
2025-06-01 08:07:52,766 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:07:53,266 - INFO - Request Parameters - Page 16:
2025-06-01 08:07:53,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:53,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:54,031 - INFO - API请求耗时: 766ms
2025-06-01 08:07:54,031 - INFO - Response - Page 16
2025-06-01 08:07:54,031 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:07:54,547 - INFO - Request Parameters - Page 17:
2025-06-01 08:07:54,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:54,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:55,313 - INFO - API请求耗时: 766ms
2025-06-01 08:07:55,313 - INFO - Response - Page 17
2025-06-01 08:07:55,313 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:07:55,828 - INFO - Request Parameters - Page 18:
2025-06-01 08:07:55,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:55,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:56,609 - INFO - API请求耗时: 781ms
2025-06-01 08:07:56,609 - INFO - Response - Page 18
2025-06-01 08:07:56,609 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:07:57,125 - INFO - Request Parameters - Page 19:
2025-06-01 08:07:57,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:57,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:57,844 - INFO - API请求耗时: 719ms
2025-06-01 08:07:57,844 - INFO - Response - Page 19
2025-06-01 08:07:57,844 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:07:58,359 - INFO - Request Parameters - Page 20:
2025-06-01 08:07:58,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:58,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:07:59,125 - INFO - API请求耗时: 766ms
2025-06-01 08:07:59,125 - INFO - Response - Page 20
2025-06-01 08:07:59,125 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:07:59,625 - INFO - Request Parameters - Page 21:
2025-06-01 08:07:59,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:07:59,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:00,359 - INFO - API请求耗时: 734ms
2025-06-01 08:08:00,359 - INFO - Response - Page 21
2025-06-01 08:08:00,359 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:08:00,875 - INFO - Request Parameters - Page 22:
2025-06-01 08:08:00,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:00,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:01,609 - INFO - API请求耗时: 734ms
2025-06-01 08:08:01,609 - INFO - Response - Page 22
2025-06-01 08:08:01,609 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:08:02,109 - INFO - Request Parameters - Page 23:
2025-06-01 08:08:02,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:02,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:02,922 - INFO - API请求耗时: 812ms
2025-06-01 08:08:02,922 - INFO - Response - Page 23
2025-06-01 08:08:02,922 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:08:03,422 - INFO - Request Parameters - Page 24:
2025-06-01 08:08:03,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:03,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:04,172 - INFO - API请求耗时: 750ms
2025-06-01 08:08:04,172 - INFO - Response - Page 24
2025-06-01 08:08:04,172 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:08:04,672 - INFO - Request Parameters - Page 25:
2025-06-01 08:08:04,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:04,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:05,406 - INFO - API请求耗时: 734ms
2025-06-01 08:08:05,406 - INFO - Response - Page 25
2025-06-01 08:08:05,406 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:08:05,922 - INFO - Request Parameters - Page 26:
2025-06-01 08:08:05,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:05,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:06,719 - INFO - API请求耗时: 797ms
2025-06-01 08:08:06,719 - INFO - Response - Page 26
2025-06-01 08:08:06,719 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:08:07,219 - INFO - Request Parameters - Page 27:
2025-06-01 08:08:07,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:07,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:08,000 - INFO - API请求耗时: 781ms
2025-06-01 08:08:08,000 - INFO - Response - Page 27
2025-06-01 08:08:08,000 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:08:08,500 - INFO - Request Parameters - Page 28:
2025-06-01 08:08:08,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:08,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:09,281 - INFO - API请求耗时: 781ms
2025-06-01 08:08:09,281 - INFO - Response - Page 28
2025-06-01 08:08:09,281 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:08:09,797 - INFO - Request Parameters - Page 29:
2025-06-01 08:08:09,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:09,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:10,547 - INFO - API请求耗时: 750ms
2025-06-01 08:08:10,547 - INFO - Response - Page 29
2025-06-01 08:08:10,547 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:08:11,062 - INFO - Request Parameters - Page 30:
2025-06-01 08:08:11,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:11,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:11,875 - INFO - API请求耗时: 812ms
2025-06-01 08:08:11,875 - INFO - Response - Page 30
2025-06-01 08:08:11,875 - INFO - 第 30 页获取到 100 条记录
2025-06-01 08:08:12,391 - INFO - Request Parameters - Page 31:
2025-06-01 08:08:12,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:12,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:13,187 - INFO - API请求耗时: 797ms
2025-06-01 08:08:13,187 - INFO - Response - Page 31
2025-06-01 08:08:13,187 - INFO - 第 31 页获取到 100 条记录
2025-06-01 08:08:13,703 - INFO - Request Parameters - Page 32:
2025-06-01 08:08:13,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:13,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:14,453 - INFO - API请求耗时: 750ms
2025-06-01 08:08:14,453 - INFO - Response - Page 32
2025-06-01 08:08:14,453 - INFO - 第 32 页获取到 100 条记录
2025-06-01 08:08:14,969 - INFO - Request Parameters - Page 33:
2025-06-01 08:08:14,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:14,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:15,750 - INFO - API请求耗时: 781ms
2025-06-01 08:08:15,750 - INFO - Response - Page 33
2025-06-01 08:08:15,750 - INFO - 第 33 页获取到 100 条记录
2025-06-01 08:08:16,250 - INFO - Request Parameters - Page 34:
2025-06-01 08:08:16,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:16,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:17,000 - INFO - API请求耗时: 750ms
2025-06-01 08:08:17,000 - INFO - Response - Page 34
2025-06-01 08:08:17,000 - INFO - 第 34 页获取到 100 条记录
2025-06-01 08:08:17,516 - INFO - Request Parameters - Page 35:
2025-06-01 08:08:17,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:17,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:18,250 - INFO - API请求耗时: 734ms
2025-06-01 08:08:18,250 - INFO - Response - Page 35
2025-06-01 08:08:18,250 - INFO - 第 35 页获取到 100 条记录
2025-06-01 08:08:18,750 - INFO - Request Parameters - Page 36:
2025-06-01 08:08:18,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:18,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:19,500 - INFO - API请求耗时: 750ms
2025-06-01 08:08:19,500 - INFO - Response - Page 36
2025-06-01 08:08:19,500 - INFO - 第 36 页获取到 100 条记录
2025-06-01 08:08:20,016 - INFO - Request Parameters - Page 37:
2025-06-01 08:08:20,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:20,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:20,828 - INFO - API请求耗时: 812ms
2025-06-01 08:08:20,828 - INFO - Response - Page 37
2025-06-01 08:08:20,828 - INFO - 第 37 页获取到 100 条记录
2025-06-01 08:08:21,344 - INFO - Request Parameters - Page 38:
2025-06-01 08:08:21,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:21,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:22,062 - INFO - API请求耗时: 719ms
2025-06-01 08:08:22,062 - INFO - Response - Page 38
2025-06-01 08:08:22,062 - INFO - 第 38 页获取到 100 条记录
2025-06-01 08:08:22,562 - INFO - Request Parameters - Page 39:
2025-06-01 08:08:22,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:22,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:23,281 - INFO - API请求耗时: 719ms
2025-06-01 08:08:23,281 - INFO - Response - Page 39
2025-06-01 08:08:23,281 - INFO - 第 39 页获取到 100 条记录
2025-06-01 08:08:23,797 - INFO - Request Parameters - Page 40:
2025-06-01 08:08:23,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:23,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:24,562 - INFO - API请求耗时: 766ms
2025-06-01 08:08:24,562 - INFO - Response - Page 40
2025-06-01 08:08:24,562 - INFO - 第 40 页获取到 100 条记录
2025-06-01 08:08:25,078 - INFO - Request Parameters - Page 41:
2025-06-01 08:08:25,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:25,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:25,922 - INFO - API请求耗时: 844ms
2025-06-01 08:08:25,922 - INFO - Response - Page 41
2025-06-01 08:08:25,922 - INFO - 第 41 页获取到 100 条记录
2025-06-01 08:08:26,422 - INFO - Request Parameters - Page 42:
2025-06-01 08:08:26,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:26,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:27,203 - INFO - API请求耗时: 781ms
2025-06-01 08:08:27,203 - INFO - Response - Page 42
2025-06-01 08:08:27,203 - INFO - 第 42 页获取到 100 条记录
2025-06-01 08:08:27,703 - INFO - Request Parameters - Page 43:
2025-06-01 08:08:27,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:27,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:28,328 - INFO - API请求耗时: 625ms
2025-06-01 08:08:28,328 - INFO - Response - Page 43
2025-06-01 08:08:28,328 - INFO - 第 43 页获取到 100 条记录
2025-06-01 08:08:28,844 - INFO - Request Parameters - Page 44:
2025-06-01 08:08:28,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:28,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600081, 1747584000081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:29,625 - INFO - API请求耗时: 781ms
2025-06-01 08:08:29,625 - INFO - Response - Page 44
2025-06-01 08:08:29,625 - INFO - 第 44 页获取到 83 条记录
2025-06-01 08:08:29,625 - INFO - 查询完成，共获取到 4383 条记录
2025-06-01 08:08:29,625 - INFO - 分段 7 查询成功，获取到 4383 条记录
2025-06-01 08:08:30,625 - INFO - 查询分段 8: 2025-05-20 至 2025-05-26
2025-06-01 08:08:30,625 - INFO - 查询日期范围: 2025-05-20 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-01 08:08:30,625 - INFO - Request Parameters - Page 1:
2025-06-01 08:08:30,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:30,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:31,375 - INFO - API请求耗时: 750ms
2025-06-01 08:08:31,375 - INFO - Response - Page 1
2025-06-01 08:08:31,375 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:08:31,875 - INFO - Request Parameters - Page 2:
2025-06-01 08:08:31,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:31,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:32,609 - INFO - API请求耗时: 734ms
2025-06-01 08:08:32,609 - INFO - Response - Page 2
2025-06-01 08:08:32,609 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:08:33,109 - INFO - Request Parameters - Page 3:
2025-06-01 08:08:33,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:33,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:33,859 - INFO - API请求耗时: 750ms
2025-06-01 08:08:33,859 - INFO - Response - Page 3
2025-06-01 08:08:33,859 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:08:34,375 - INFO - Request Parameters - Page 4:
2025-06-01 08:08:34,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:34,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:35,140 - INFO - API请求耗时: 766ms
2025-06-01 08:08:35,140 - INFO - Response - Page 4
2025-06-01 08:08:35,140 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:08:35,656 - INFO - Request Parameters - Page 5:
2025-06-01 08:08:35,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:35,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:36,422 - INFO - API请求耗时: 766ms
2025-06-01 08:08:36,422 - INFO - Response - Page 5
2025-06-01 08:08:36,422 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:08:36,937 - INFO - Request Parameters - Page 6:
2025-06-01 08:08:36,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:36,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:37,750 - INFO - API请求耗时: 812ms
2025-06-01 08:08:37,750 - INFO - Response - Page 6
2025-06-01 08:08:37,750 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:08:38,265 - INFO - Request Parameters - Page 7:
2025-06-01 08:08:38,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:38,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:39,047 - INFO - API请求耗时: 781ms
2025-06-01 08:08:39,047 - INFO - Response - Page 7
2025-06-01 08:08:39,047 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:08:39,547 - INFO - Request Parameters - Page 8:
2025-06-01 08:08:39,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:39,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:40,297 - INFO - API请求耗时: 750ms
2025-06-01 08:08:40,297 - INFO - Response - Page 8
2025-06-01 08:08:40,297 - INFO - 第 8 页获取到 100 条记录
2025-06-01 08:08:40,812 - INFO - Request Parameters - Page 9:
2025-06-01 08:08:40,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:40,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:41,593 - INFO - API请求耗时: 781ms
2025-06-01 08:08:41,593 - INFO - Response - Page 9
2025-06-01 08:08:41,593 - INFO - 第 9 页获取到 100 条记录
2025-06-01 08:08:42,109 - INFO - Request Parameters - Page 10:
2025-06-01 08:08:42,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:42,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:42,828 - INFO - API请求耗时: 719ms
2025-06-01 08:08:42,828 - INFO - Response - Page 10
2025-06-01 08:08:42,828 - INFO - 第 10 页获取到 100 条记录
2025-06-01 08:08:43,328 - INFO - Request Parameters - Page 11:
2025-06-01 08:08:43,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:43,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:44,047 - INFO - API请求耗时: 719ms
2025-06-01 08:08:44,047 - INFO - Response - Page 11
2025-06-01 08:08:44,047 - INFO - 第 11 页获取到 100 条记录
2025-06-01 08:08:44,562 - INFO - Request Parameters - Page 12:
2025-06-01 08:08:44,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:44,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:45,281 - INFO - API请求耗时: 719ms
2025-06-01 08:08:45,281 - INFO - Response - Page 12
2025-06-01 08:08:45,281 - INFO - 第 12 页获取到 100 条记录
2025-06-01 08:08:45,797 - INFO - Request Parameters - Page 13:
2025-06-01 08:08:45,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:45,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:46,515 - INFO - API请求耗时: 719ms
2025-06-01 08:08:46,515 - INFO - Response - Page 13
2025-06-01 08:08:46,515 - INFO - 第 13 页获取到 100 条记录
2025-06-01 08:08:47,031 - INFO - Request Parameters - Page 14:
2025-06-01 08:08:47,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:47,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:47,828 - INFO - API请求耗时: 797ms
2025-06-01 08:08:47,828 - INFO - Response - Page 14
2025-06-01 08:08:47,828 - INFO - 第 14 页获取到 100 条记录
2025-06-01 08:08:48,328 - INFO - Request Parameters - Page 15:
2025-06-01 08:08:48,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:48,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:49,078 - INFO - API请求耗时: 750ms
2025-06-01 08:08:49,078 - INFO - Response - Page 15
2025-06-01 08:08:49,078 - INFO - 第 15 页获取到 100 条记录
2025-06-01 08:08:49,578 - INFO - Request Parameters - Page 16:
2025-06-01 08:08:49,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:49,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:50,359 - INFO - API请求耗时: 781ms
2025-06-01 08:08:50,359 - INFO - Response - Page 16
2025-06-01 08:08:50,359 - INFO - 第 16 页获取到 100 条记录
2025-06-01 08:08:50,875 - INFO - Request Parameters - Page 17:
2025-06-01 08:08:50,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:50,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:51,640 - INFO - API请求耗时: 766ms
2025-06-01 08:08:51,640 - INFO - Response - Page 17
2025-06-01 08:08:51,640 - INFO - 第 17 页获取到 100 条记录
2025-06-01 08:08:52,156 - INFO - Request Parameters - Page 18:
2025-06-01 08:08:52,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:52,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:52,968 - INFO - API请求耗时: 813ms
2025-06-01 08:08:52,968 - INFO - Response - Page 18
2025-06-01 08:08:52,968 - INFO - 第 18 页获取到 100 条记录
2025-06-01 08:08:53,484 - INFO - Request Parameters - Page 19:
2025-06-01 08:08:53,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:53,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:54,203 - INFO - API请求耗时: 719ms
2025-06-01 08:08:54,203 - INFO - Response - Page 19
2025-06-01 08:08:54,203 - INFO - 第 19 页获取到 100 条记录
2025-06-01 08:08:54,703 - INFO - Request Parameters - Page 20:
2025-06-01 08:08:54,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:54,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:55,437 - INFO - API请求耗时: 734ms
2025-06-01 08:08:55,437 - INFO - Response - Page 20
2025-06-01 08:08:55,437 - INFO - 第 20 页获取到 100 条记录
2025-06-01 08:08:55,937 - INFO - Request Parameters - Page 21:
2025-06-01 08:08:55,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:55,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:56,703 - INFO - API请求耗时: 766ms
2025-06-01 08:08:56,703 - INFO - Response - Page 21
2025-06-01 08:08:56,718 - INFO - 第 21 页获取到 100 条记录
2025-06-01 08:08:57,218 - INFO - Request Parameters - Page 22:
2025-06-01 08:08:57,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:57,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:57,984 - INFO - API请求耗时: 766ms
2025-06-01 08:08:57,984 - INFO - Response - Page 22
2025-06-01 08:08:57,984 - INFO - 第 22 页获取到 100 条记录
2025-06-01 08:08:58,500 - INFO - Request Parameters - Page 23:
2025-06-01 08:08:58,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:58,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:08:59,265 - INFO - API请求耗时: 766ms
2025-06-01 08:08:59,265 - INFO - Response - Page 23
2025-06-01 08:08:59,265 - INFO - 第 23 页获取到 100 条记录
2025-06-01 08:08:59,781 - INFO - Request Parameters - Page 24:
2025-06-01 08:08:59,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:08:59,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:00,484 - INFO - API请求耗时: 703ms
2025-06-01 08:09:00,484 - INFO - Response - Page 24
2025-06-01 08:09:00,484 - INFO - 第 24 页获取到 100 条记录
2025-06-01 08:09:01,000 - INFO - Request Parameters - Page 25:
2025-06-01 08:09:01,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:01,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:01,812 - INFO - API请求耗时: 812ms
2025-06-01 08:09:01,812 - INFO - Response - Page 25
2025-06-01 08:09:01,812 - INFO - 第 25 页获取到 100 条记录
2025-06-01 08:09:02,328 - INFO - Request Parameters - Page 26:
2025-06-01 08:09:02,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:02,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:03,000 - INFO - API请求耗时: 672ms
2025-06-01 08:09:03,000 - INFO - Response - Page 26
2025-06-01 08:09:03,000 - INFO - 第 26 页获取到 100 条记录
2025-06-01 08:09:03,515 - INFO - Request Parameters - Page 27:
2025-06-01 08:09:03,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:03,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:04,250 - INFO - API请求耗时: 734ms
2025-06-01 08:09:04,250 - INFO - Response - Page 27
2025-06-01 08:09:04,250 - INFO - 第 27 页获取到 100 条记录
2025-06-01 08:09:04,765 - INFO - Request Parameters - Page 28:
2025-06-01 08:09:04,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:04,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:05,578 - INFO - API请求耗时: 812ms
2025-06-01 08:09:05,578 - INFO - Response - Page 28
2025-06-01 08:09:05,578 - INFO - 第 28 页获取到 100 条记录
2025-06-01 08:09:06,078 - INFO - Request Parameters - Page 29:
2025-06-01 08:09:06,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:06,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:06,796 - INFO - API请求耗时: 719ms
2025-06-01 08:09:06,796 - INFO - Response - Page 29
2025-06-01 08:09:06,796 - INFO - 第 29 页获取到 100 条记录
2025-06-01 08:09:07,296 - INFO - Request Parameters - Page 30:
2025-06-01 08:09:07,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:07,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747670400081, 1748188800081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:07,765 - INFO - API请求耗时: 469ms
2025-06-01 08:09:07,765 - INFO - Response - Page 30
2025-06-01 08:09:07,765 - INFO - 第 30 页获取到 8 条记录
2025-06-01 08:09:07,765 - INFO - 查询完成，共获取到 2908 条记录
2025-06-01 08:09:07,765 - INFO - 分段 8 查询成功，获取到 2908 条记录
2025-06-01 08:09:08,765 - INFO - 查询分段 9: 2025-05-27 至 2025-05-31
2025-06-01 08:09:08,765 - INFO - 查询日期范围: 2025-05-27 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-01 08:09:08,765 - INFO - Request Parameters - Page 1:
2025-06-01 08:09:08,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:08,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:09,531 - INFO - API请求耗时: 766ms
2025-06-01 08:09:09,531 - INFO - Response - Page 1
2025-06-01 08:09:09,531 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:09:10,046 - INFO - Request Parameters - Page 2:
2025-06-01 08:09:10,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:10,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:10,843 - INFO - API请求耗时: 797ms
2025-06-01 08:09:10,843 - INFO - Response - Page 2
2025-06-01 08:09:10,843 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:09:11,359 - INFO - Request Parameters - Page 3:
2025-06-01 08:09:11,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:11,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:12,125 - INFO - API请求耗时: 766ms
2025-06-01 08:09:12,140 - INFO - Response - Page 3
2025-06-01 08:09:12,140 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:09:12,656 - INFO - Request Parameters - Page 4:
2025-06-01 08:09:12,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:12,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:13,437 - INFO - API请求耗时: 781ms
2025-06-01 08:09:13,437 - INFO - Response - Page 4
2025-06-01 08:09:13,437 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:09:13,953 - INFO - Request Parameters - Page 5:
2025-06-01 08:09:13,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:13,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:14,671 - INFO - API请求耗时: 719ms
2025-06-01 08:09:14,671 - INFO - Response - Page 5
2025-06-01 08:09:14,671 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:09:15,187 - INFO - Request Parameters - Page 6:
2025-06-01 08:09:15,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:15,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:15,953 - INFO - API请求耗时: 766ms
2025-06-01 08:09:15,953 - INFO - Response - Page 6
2025-06-01 08:09:15,953 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:09:16,453 - INFO - Request Parameters - Page 7:
2025-06-01 08:09:16,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:09:16,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200081, 1748707199081], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:09:16,984 - INFO - API请求耗时: 531ms
2025-06-01 08:09:16,984 - INFO - Response - Page 7
2025-06-01 08:09:16,984 - INFO - 第 7 页获取到 22 条记录
2025-06-01 08:09:16,984 - INFO - 查询完成，共获取到 622 条记录
2025-06-01 08:09:16,999 - INFO - 分段 9 查询成功，获取到 622 条记录
2025-06-01 08:09:18,015 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 37711 条记录，失败 0 次
2025-06-01 08:09:18,015 - INFO - 成功获取宜搭日销售表单数据，共 37711 条记录
2025-06-01 08:09:18,015 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-01 08:09:18,015 - INFO - 开始对比和同步日销售数据...
2025-06-01 08:09:19,062 - INFO - 成功创建宜搭日销售数据索引，共 10832 条记录
2025-06-01 08:09:19,062 - INFO - 开始处理数衍数据，共 12940 条记录
2025-06-01 08:09:19,718 - INFO - 更新表单数据成功: FINST-GX9663D1W9GVID6SFHIH9B77OGVW3UBN69PAMN1
2025-06-01 08:09:19,718 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_20250419, 变更字段: [{'field': 'recommendAmount', 'old_value': 37444.36, 'new_value': 27614.24}, {'field': 'dailyBillAmount', 'old_value': 37444.36, 'new_value': 27614.24}]
2025-06-01 08:09:20,359 - INFO - 更新表单数据成功: FINST-1PF66KA17FSVXEY0B18X8CIV43PP2TSX8M8BMT3
2025-06-01 08:09:20,359 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'amount', 'old_value': 1259.9, 'new_value': 1387.9}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1259.9, 'new_value': 1387.9}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-06-01 08:09:20,859 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O21FGDR5BM1B
2025-06-01 08:09:20,859 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2115.66, 'new_value': 2121.66}, {'field': 'dailyBillAmount', 'old_value': 2115.66, 'new_value': 2121.66}]
2025-06-01 08:09:21,328 - INFO - 更新表单数据成功: FINST-1PF66KA1YESVHVKUAZTZU6PXVKI33HZC6HBBMUR
2025-06-01 08:09:21,328 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250530, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8034.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8034.4}]
2025-06-01 08:09:21,781 - INFO - 更新表单数据成功: FINST-R1A66H91EAUV1TQIB6MBS6M2N1EE2GOF6HBBMDD
2025-06-01 08:09:21,781 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250530, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5790.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5790.7}]
2025-06-01 08:09:21,843 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-06-01 08:09:22,312 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-01 08:09:25,328 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-06-01 08:09:25,718 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-01 08:09:28,734 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-06-01 08:09:29,078 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-01 08:09:32,093 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-06-01 08:09:32,499 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-01 08:09:35,515 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-06-01 08:09:35,921 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-01 08:09:38,937 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-06-01 08:09:39,343 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-01 08:09:42,359 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-06-01 08:09:42,734 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-01 08:09:45,749 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-06-01 08:09:46,140 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-01 08:09:49,156 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-06-01 08:09:49,593 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-01 08:09:52,609 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-06-01 08:09:53,015 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-01 08:09:56,030 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-06-01 08:09:56,359 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-01 08:09:59,359 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-06-01 08:09:59,780 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-01 08:10:02,796 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-06-01 08:10:03,265 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-01 08:10:06,280 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-06-01 08:10:06,640 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-01 08:10:09,655 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-06-01 08:10:10,155 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-01 08:10:13,171 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-06-01 08:10:13,624 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-01 08:10:16,640 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-06-01 08:10:17,030 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-01 08:10:20,046 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-06-01 08:10:20,468 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-01 08:10:23,483 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-06-01 08:10:23,905 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-01 08:10:26,921 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-06-01 08:10:27,390 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-01 08:10:30,405 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-06-01 08:10:30,858 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-01 08:10:33,874 - INFO - 正在批量插入每日数据，批次 22/22，共 8 条记录
2025-06-01 08:10:34,030 - INFO - 批量插入每日数据成功，批次 22，8 条记录
2025-06-01 08:10:37,046 - INFO - 批量插入每日数据完成: 总计 2108 条，成功 2108 条，失败 0 条
2025-06-01 08:10:37,046 - INFO - 批量插入日销售数据完成，共 2108 条记录
2025-06-01 08:10:37,046 - INFO - 日销售数据同步完成！更新: 5 条，插入: 2108 条，错误: 0 条，跳过: 10827 条
2025-06-01 08:10:37,046 - INFO - 正在获取宜搭月销售表单数据...
2025-06-01 08:10:37,046 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-06-01 08:10:37,046 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-06-01 08:10:37,046 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:37,046 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:37,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:37,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:37,733 - INFO - API请求耗时: 688ms
2025-06-01 08:10:37,733 - INFO - Response - Page 1
2025-06-01 08:10:37,733 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:37,733 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:37,733 - WARNING - 月度分段 1 查询返回空数据
2025-06-01 08:10:37,733 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-01 08:10:37,733 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:37,733 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:37,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:37,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:37,968 - INFO - API请求耗时: 234ms
2025-06-01 08:10:37,968 - INFO - Response - Page 1
2025-06-01 08:10:37,968 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:37,968 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:37,968 - WARNING - 单月查询返回空数据: 2024-05
2025-06-01 08:10:38,468 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-01 08:10:38,468 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:38,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:38,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:38,671 - INFO - API请求耗时: 203ms
2025-06-01 08:10:38,671 - INFO - Response - Page 1
2025-06-01 08:10:38,671 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:38,671 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:38,671 - WARNING - 单月查询返回空数据: 2024-06
2025-06-01 08:10:39,171 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:39,171 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:39,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:39,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:39,405 - INFO - API请求耗时: 234ms
2025-06-01 08:10:39,405 - INFO - Response - Page 1
2025-06-01 08:10:39,405 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:39,405 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:39,405 - WARNING - 单月查询返回空数据: 2024-07
2025-06-01 08:10:40,936 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-06-01 08:10:40,936 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:40,936 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:40,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:40,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:41,171 - INFO - API请求耗时: 234ms
2025-06-01 08:10:41,171 - INFO - Response - Page 1
2025-06-01 08:10:41,171 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:41,171 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:41,171 - WARNING - 月度分段 2 查询返回空数据
2025-06-01 08:10:41,171 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-01 08:10:41,171 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:41,171 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:41,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:41,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:41,405 - INFO - API请求耗时: 234ms
2025-06-01 08:10:41,405 - INFO - Response - Page 1
2025-06-01 08:10:41,405 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:41,405 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:41,405 - WARNING - 单月查询返回空数据: 2024-08
2025-06-01 08:10:41,921 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-01 08:10:41,921 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:41,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:41,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:42,124 - INFO - API请求耗时: 203ms
2025-06-01 08:10:42,124 - INFO - Response - Page 1
2025-06-01 08:10:42,124 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:42,140 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:42,140 - WARNING - 单月查询返回空数据: 2024-09
2025-06-01 08:10:42,640 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:42,640 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:42,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:42,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:42,843 - INFO - API请求耗时: 203ms
2025-06-01 08:10:42,843 - INFO - Response - Page 1
2025-06-01 08:10:42,843 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-01 08:10:42,843 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 08:10:42,843 - WARNING - 单月查询返回空数据: 2024-10
2025-06-01 08:10:44,358 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-06-01 08:10:44,358 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:44,358 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:44,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:44,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:44,999 - INFO - API请求耗时: 641ms
2025-06-01 08:10:44,999 - INFO - Response - Page 1
2025-06-01 08:10:44,999 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:10:45,515 - INFO - Request Parameters - Page 2:
2025-06-01 08:10:45,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:45,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:46,061 - INFO - API请求耗时: 547ms
2025-06-01 08:10:46,061 - INFO - Response - Page 2
2025-06-01 08:10:46,061 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:10:46,577 - INFO - Request Parameters - Page 3:
2025-06-01 08:10:46,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:46,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:47,061 - INFO - API请求耗时: 484ms
2025-06-01 08:10:47,061 - INFO - Response - Page 3
2025-06-01 08:10:47,061 - INFO - 第 3 页获取到 48 条记录
2025-06-01 08:10:47,061 - INFO - 查询完成，共获取到 248 条记录
2025-06-01 08:10:47,061 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-06-01 08:10:48,061 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-06-01 08:10:48,061 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-01 08:10:48,061 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:48,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:48,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:48,811 - INFO - API请求耗时: 750ms
2025-06-01 08:10:48,811 - INFO - Response - Page 1
2025-06-01 08:10:48,811 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:10:49,311 - INFO - Request Parameters - Page 2:
2025-06-01 08:10:49,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:49,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:49,921 - INFO - API请求耗时: 609ms
2025-06-01 08:10:49,921 - INFO - Response - Page 2
2025-06-01 08:10:49,921 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:10:50,436 - INFO - Request Parameters - Page 3:
2025-06-01 08:10:50,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:50,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:50,936 - INFO - API请求耗时: 500ms
2025-06-01 08:10:50,952 - INFO - Response - Page 3
2025-06-01 08:10:50,952 - INFO - 第 3 页获取到 100 条记录
2025-06-01 08:10:51,452 - INFO - Request Parameters - Page 4:
2025-06-01 08:10:51,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:51,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:51,999 - INFO - API请求耗时: 547ms
2025-06-01 08:10:51,999 - INFO - Response - Page 4
2025-06-01 08:10:51,999 - INFO - 第 4 页获取到 100 条记录
2025-06-01 08:10:52,499 - INFO - Request Parameters - Page 5:
2025-06-01 08:10:52,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:52,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:53,061 - INFO - API请求耗时: 562ms
2025-06-01 08:10:53,061 - INFO - Response - Page 5
2025-06-01 08:10:53,061 - INFO - 第 5 页获取到 100 条记录
2025-06-01 08:10:53,577 - INFO - Request Parameters - Page 6:
2025-06-01 08:10:53,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:53,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:54,093 - INFO - API请求耗时: 516ms
2025-06-01 08:10:54,093 - INFO - Response - Page 6
2025-06-01 08:10:54,093 - INFO - 第 6 页获取到 100 条记录
2025-06-01 08:10:54,593 - INFO - Request Parameters - Page 7:
2025-06-01 08:10:54,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:54,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:55,171 - INFO - API请求耗时: 578ms
2025-06-01 08:10:55,171 - INFO - Response - Page 7
2025-06-01 08:10:55,171 - INFO - 第 7 页获取到 100 条记录
2025-06-01 08:10:55,686 - INFO - Request Parameters - Page 8:
2025-06-01 08:10:55,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:55,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:55,999 - INFO - API请求耗时: 312ms
2025-06-01 08:10:55,999 - INFO - Response - Page 8
2025-06-01 08:10:55,999 - INFO - 第 8 页获取到 16 条记录
2025-06-01 08:10:55,999 - INFO - 查询完成，共获取到 716 条记录
2025-06-01 08:10:55,999 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-06-01 08:10:56,999 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-06-01 08:10:56,999 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-01 08:10:56,999 - INFO - Request Parameters - Page 1:
2025-06-01 08:10:56,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:56,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:57,561 - INFO - API请求耗时: 562ms
2025-06-01 08:10:57,561 - INFO - Response - Page 1
2025-06-01 08:10:57,561 - INFO - 第 1 页获取到 100 条记录
2025-06-01 08:10:58,077 - INFO - Request Parameters - Page 2:
2025-06-01 08:10:58,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:58,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:58,608 - INFO - API请求耗时: 531ms
2025-06-01 08:10:58,608 - INFO - Response - Page 2
2025-06-01 08:10:58,624 - INFO - 第 2 页获取到 100 条记录
2025-06-01 08:10:59,139 - INFO - Request Parameters - Page 3:
2025-06-01 08:10:59,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 08:10:59,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 08:10:59,546 - INFO - API请求耗时: 406ms
2025-06-01 08:10:59,546 - INFO - Response - Page 3
2025-06-01 08:10:59,546 - INFO - 第 3 页获取到 28 条记录
2025-06-01 08:10:59,546 - INFO - 查询完成，共获取到 228 条记录
2025-06-01 08:10:59,546 - INFO - 月度分段 5 查询成功，获取到 228 条记录
2025-06-01 08:11:00,546 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1192 条记录，失败 0 次
2025-06-01 08:11:00,546 - INFO - 成功获取宜搭月销售表单数据，共 1192 条记录
2025-06-01 08:11:00,546 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-01 08:11:00,546 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-01 08:11:00,546 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-06-01 08:11:00,608 - INFO - 成功创建宜搭月销售数据索引，共 1192 条记录
2025-06-01 08:11:00,624 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:01,155 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-06-01 08:11:01,155 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 209666.63, 'new_value': 218977.21}, {'field': 'dailyBillAmount', 'old_value': 209666.63, 'new_value': 218977.21}, {'field': 'amount', 'old_value': 6470.9, 'new_value': 6575.1}, {'field': 'count', 'old_value': 90, 'new_value': 92}, {'field': 'onlineAmount', 'old_value': 6547.47, 'new_value': 6651.67}, {'field': 'onlineCount', 'old_value': 90, 'new_value': 92}]
2025-06-01 08:11:01,155 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:01,593 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-06-01 08:11:01,593 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 533538.21, 'new_value': 563222.57}, {'field': 'dailyBillAmount', 'old_value': 533538.21, 'new_value': 563222.57}, {'field': 'amount', 'old_value': 289956.6, 'new_value': 303553.9}, {'field': 'count', 'old_value': 2739, 'new_value': 2844}, {'field': 'instoreAmount', 'old_value': 120388.0, 'new_value': 127175.3}, {'field': 'instoreCount', 'old_value': 949, 'new_value': 992}, {'field': 'onlineAmount', 'old_value': 169921.5, 'new_value': 176731.5}, {'field': 'onlineCount', 'old_value': 1790, 'new_value': 1852}]
2025-06-01 08:11:01,593 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:02,046 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-06-01 08:11:02,046 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 353677.53, 'new_value': 371862.14}, {'field': 'dailyBillAmount', 'old_value': 353677.53, 'new_value': 371862.14}, {'field': 'amount', 'old_value': 356928.43, 'new_value': 375220.07}, {'field': 'count', 'old_value': 2377, 'new_value': 2487}, {'field': 'instoreAmount', 'old_value': 338040.27999999997, 'new_value': 355541.32}, {'field': 'instoreCount', 'old_value': 2092, 'new_value': 2189}, {'field': 'onlineAmount', 'old_value': 19215.25, 'new_value': 20005.85}, {'field': 'onlineCount', 'old_value': 285, 'new_value': 298}]
2025-06-01 08:11:02,046 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:02,530 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-06-01 08:11:02,530 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 775526.46, 'new_value': 814018.53}, {'field': 'dailyBillAmount', 'old_value': 775526.46, 'new_value': 814018.53}, {'field': 'amount', 'old_value': 568179.63, 'new_value': 599763.38}, {'field': 'count', 'old_value': 2748, 'new_value': 2886}, {'field': 'instoreAmount', 'old_value': 568179.63, 'new_value': 599763.38}, {'field': 'instoreCount', 'old_value': 2748, 'new_value': 2886}]
2025-06-01 08:11:02,530 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:02,921 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-06-01 08:11:02,921 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 595550.05, 'new_value': 624271.7}, {'field': 'dailyBillAmount', 'old_value': 595550.05, 'new_value': 624271.7}, {'field': 'amount', 'old_value': 991184.2, 'new_value': 1024660.2}, {'field': 'count', 'old_value': 3503, 'new_value': 3617}, {'field': 'instoreAmount', 'old_value': 992200.0, 'new_value': 1025676.0}, {'field': 'instoreCount', 'old_value': 3495, 'new_value': 3609}]
2025-06-01 08:11:02,921 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:03,452 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-06-01 08:11:03,452 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66709.4, 'new_value': 67951.4}, {'field': 'dailyBillAmount', 'old_value': 66709.4, 'new_value': 67951.4}, {'field': 'amount', 'old_value': 90669.31, 'new_value': 92132.41}, {'field': 'count', 'old_value': 384, 'new_value': 400}, {'field': 'instoreAmount', 'old_value': 47083.3, 'new_value': 47681.3}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 52}, {'field': 'onlineAmount', 'old_value': 51788.020000000004, 'new_value': 52653.12}, {'field': 'onlineCount', 'old_value': 333, 'new_value': 348}]
2025-06-01 08:11:03,452 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:03,905 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-06-01 08:11:03,905 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231775.9, 'new_value': 240231.9}, {'field': 'amount', 'old_value': 231775.9, 'new_value': 240231.9}, {'field': 'count', 'old_value': 145, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 231775.9, 'new_value': 240231.9}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 154}]
2025-06-01 08:11:03,905 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:04,343 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-06-01 08:11:04,343 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 870080.41, 'new_value': 909873.64}, {'field': 'dailyBillAmount', 'old_value': 870080.41, 'new_value': 909873.64}, {'field': 'amount', 'old_value': 745477.8, 'new_value': 769480.3}, {'field': 'count', 'old_value': 5326, 'new_value': 5444}, {'field': 'instoreAmount', 'old_value': 620743.13, 'new_value': 643348.13}, {'field': 'instoreCount', 'old_value': 2699, 'new_value': 2784}, {'field': 'onlineAmount', 'old_value': 130187.77, 'new_value': 131602.07}, {'field': 'onlineCount', 'old_value': 2627, 'new_value': 2660}]
2025-06-01 08:11:04,343 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:04,874 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-06-01 08:11:04,874 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 846382.5599999999, 'new_value': 879522.67}, {'field': 'dailyBillAmount', 'old_value': 846382.5599999999, 'new_value': 879522.67}, {'field': 'amount', 'old_value': 226777.98, 'new_value': 236883.98}, {'field': 'count', 'old_value': 1282, 'new_value': 1321}, {'field': 'instoreAmount', 'old_value': 226777.98, 'new_value': 236883.98}, {'field': 'instoreCount', 'old_value': 1282, 'new_value': 1321}]
2025-06-01 08:11:04,889 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:05,374 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-06-01 08:11:05,374 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 134772.1, 'new_value': 142662.1}, {'field': 'count', 'old_value': 337, 'new_value': 348}, {'field': 'instoreAmount', 'old_value': 134773.8, 'new_value': 142663.8}, {'field': 'instoreCount', 'old_value': 337, 'new_value': 348}]
2025-06-01 08:11:05,374 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:05,983 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXJ
2025-06-01 08:11:05,983 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSCOR6MVCC7Q2OV392411100148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34920.7, 'new_value': 38020.7}, {'field': 'amount', 'old_value': 34920.7, 'new_value': 38020.7}, {'field': 'count', 'old_value': 33, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 36316.7, 'new_value': 39416.7}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 36}]
2025-06-01 08:11:05,983 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:06,483 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-06-01 08:11:06,483 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1161848.31, 'new_value': 1230862.51}, {'field': 'dailyBillAmount', 'old_value': 1161848.31, 'new_value': 1230862.51}, {'field': 'amount', 'old_value': -415489.96, 'new_value': -415293.63}, {'field': 'count', 'old_value': 1282, 'new_value': 1374}, {'field': 'instoreAmount', 'old_value': 740782.99, 'new_value': 807153.97}, {'field': 'instoreCount', 'old_value': 1282, 'new_value': 1374}]
2025-06-01 08:11:06,483 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:06,999 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-06-01 08:11:06,999 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 499209.0, 'new_value': 521555.0}, {'field': 'amount', 'old_value': 499209.0, 'new_value': 521555.0}, {'field': 'count', 'old_value': 1677, 'new_value': 1760}, {'field': 'instoreAmount', 'old_value': 499209.0, 'new_value': 521555.0}, {'field': 'instoreCount', 'old_value': 1677, 'new_value': 1760}]
2025-06-01 08:11:06,999 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:07,468 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-06-01 08:11:07,483 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 468683.2, 'new_value': 485660.38}, {'field': 'dailyBillAmount', 'old_value': 371707.9, 'new_value': 387873.48}, {'field': 'amount', 'old_value': 468683.2, 'new_value': 485660.38}, {'field': 'count', 'old_value': 1569, 'new_value': 1615}, {'field': 'instoreAmount', 'old_value': 468683.2, 'new_value': 485660.38}, {'field': 'instoreCount', 'old_value': 1569, 'new_value': 1615}]
2025-06-01 08:11:07,483 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:07,936 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-06-01 08:11:07,936 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 244034.61000000002, 'new_value': 249825.31}, {'field': 'dailyBillAmount', 'old_value': 244034.61000000002, 'new_value': 249825.31}, {'field': 'amount', 'old_value': 15735.4, 'new_value': 16079.4}, {'field': 'count', 'old_value': 108, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 18400.0, 'new_value': 18744.0}, {'field': 'instoreCount', 'old_value': 108, 'new_value': 109}]
2025-06-01 08:11:07,936 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:08,483 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-06-01 08:11:08,483 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 119862.06, 'new_value': 123101.17}, {'field': 'dailyBillAmount', 'old_value': 119862.06, 'new_value': 123101.17}, {'field': 'amount', 'old_value': 72990.06, 'new_value': 73932.78}, {'field': 'count', 'old_value': 1105, 'new_value': 1128}, {'field': 'instoreAmount', 'old_value': 75063.96, 'new_value': 76006.68000000001}, {'field': 'instoreCount', 'old_value': 1105, 'new_value': 1128}]
2025-06-01 08:11:08,483 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:08,921 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-06-01 08:11:08,921 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 182757.82, 'new_value': 186089.08}, {'field': 'amount', 'old_value': 182756.96, 'new_value': 186088.22}, {'field': 'count', 'old_value': 6244, 'new_value': 6354}, {'field': 'instoreAmount', 'old_value': 159463.52, 'new_value': 162261.94}, {'field': 'instoreCount', 'old_value': 5647, 'new_value': 5742}, {'field': 'onlineAmount', 'old_value': 23294.3, 'new_value': 23827.14}, {'field': 'onlineCount', 'old_value': 597, 'new_value': 612}]
2025-06-01 08:11:08,921 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:09,358 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-06-01 08:11:09,358 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 359867.22, 'new_value': 369685.22}, {'field': 'dailyBillAmount', 'old_value': 354477.0, 'new_value': 364295.0}, {'field': 'amount', 'old_value': 296274.01, 'new_value': 299462.01}, {'field': 'count', 'old_value': 280, 'new_value': 290}, {'field': 'instoreAmount', 'old_value': 296041.0, 'new_value': 299229.0}, {'field': 'instoreCount', 'old_value': 277, 'new_value': 287}]
2025-06-01 08:11:09,358 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:09,842 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-06-01 08:11:09,842 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 695316.92, 'new_value': 718468.23}, {'field': 'dailyBillAmount', 'old_value': 694812.37, 'new_value': 717963.68}, {'field': 'amount', 'old_value': 695316.92, 'new_value': 718468.23}, {'field': 'count', 'old_value': 614, 'new_value': 633}, {'field': 'instoreAmount', 'old_value': 695317.92, 'new_value': 718469.23}, {'field': 'instoreCount', 'old_value': 614, 'new_value': 633}]
2025-06-01 08:11:09,842 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:10,358 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-06-01 08:11:10,358 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 191441.0, 'new_value': 195095.0}, {'field': 'dailyBillAmount', 'old_value': 191441.0, 'new_value': 195095.0}]
2025-06-01 08:11:10,358 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:10,905 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-06-01 08:11:10,905 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124936.0, 'new_value': 129042.4}, {'field': 'dailyBillAmount', 'old_value': 124936.0, 'new_value': 129042.4}, {'field': 'amount', 'old_value': 135973.3, 'new_value': 140079.7}, {'field': 'count', 'old_value': 365, 'new_value': 375}, {'field': 'instoreAmount', 'old_value': 135979.2, 'new_value': 140085.6}, {'field': 'instoreCount', 'old_value': 365, 'new_value': 375}]
2025-06-01 08:11:10,905 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:11,421 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-06-01 08:11:11,421 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 195728.7, 'new_value': 198476.7}, {'field': 'amount', 'old_value': 195728.7, 'new_value': 198476.7}, {'field': 'count', 'old_value': 231, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 195855.7, 'new_value': 198603.7}, {'field': 'instoreCount', 'old_value': 231, 'new_value': 234}]
2025-06-01 08:11:11,421 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:11,936 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-06-01 08:11:11,936 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 263574.18, 'new_value': 273339.33}, {'field': 'dailyBillAmount', 'old_value': 263574.18, 'new_value': 273339.33}, {'field': 'amount', 'old_value': 281974.75, 'new_value': 295168.75}, {'field': 'count', 'old_value': 1848, 'new_value': 1905}, {'field': 'instoreAmount', 'old_value': 283183.75, 'new_value': 296377.75}, {'field': 'instoreCount', 'old_value': 1848, 'new_value': 1905}]
2025-06-01 08:11:11,936 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:12,421 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-06-01 08:11:12,421 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 186274.33, 'new_value': 189903.32}, {'field': 'dailyBillAmount', 'old_value': 186274.33, 'new_value': 189903.32}, {'field': 'amount', 'old_value': 18224.489999999998, 'new_value': 18721.73}, {'field': 'count', 'old_value': 1684, 'new_value': 1708}, {'field': 'instoreAmount', 'old_value': 24159.55, 'new_value': 24773.68}, {'field': 'instoreCount', 'old_value': 1684, 'new_value': 1708}]
2025-06-01 08:11:12,421 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:12,874 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-06-01 08:11:12,874 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 381079.45, 'new_value': 395906.05}, {'field': 'amount', 'old_value': 381074.33, 'new_value': 395900.93}, {'field': 'count', 'old_value': 7971, 'new_value': 8172}, {'field': 'instoreAmount', 'old_value': 375011.26, 'new_value': 389002.04}, {'field': 'instoreCount', 'old_value': 7682, 'new_value': 7864}, {'field': 'onlineAmount', 'old_value': 14949.77, 'new_value': 16341.39}, {'field': 'onlineCount', 'old_value': 289, 'new_value': 308}]
2025-06-01 08:11:12,874 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:13,342 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-06-01 08:11:13,342 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 278970.4, 'new_value': 294942.9}, {'field': 'dailyBillAmount', 'old_value': 278970.4, 'new_value': 294942.9}, {'field': 'amount', 'old_value': 278970.4, 'new_value': 296887.9}, {'field': 'count', 'old_value': 831, 'new_value': 869}, {'field': 'instoreAmount', 'old_value': 278970.4, 'new_value': 296887.9}, {'field': 'instoreCount', 'old_value': 831, 'new_value': 869}]
2025-06-01 08:11:13,342 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:13,796 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-06-01 08:11:13,796 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 244148.19, 'new_value': 258051.97}, {'field': 'dailyBillAmount', 'old_value': 244148.19, 'new_value': 258051.97}, {'field': 'amount', 'old_value': 79395.2, 'new_value': 85508.6}, {'field': 'count', 'old_value': 186, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 79395.2, 'new_value': 85508.6}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 201}]
2025-06-01 08:11:13,796 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:14,264 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-06-01 08:11:14,264 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 479046.68, 'new_value': 498678.76}, {'field': 'dailyBillAmount', 'old_value': 479046.68, 'new_value': 498678.76}, {'field': 'amount', 'old_value': 198737.18, 'new_value': 205903.08}, {'field': 'count', 'old_value': 744, 'new_value': 772}, {'field': 'instoreAmount', 'old_value': 198737.44, 'new_value': 205903.34}, {'field': 'instoreCount', 'old_value': 744, 'new_value': 772}]
2025-06-01 08:11:14,264 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:14,749 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-06-01 08:11:14,749 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109453.98, 'new_value': 112840.98}, {'field': 'dailyBillAmount', 'old_value': 109453.98, 'new_value': 112840.98}, {'field': 'amount', 'old_value': 32148.3, 'new_value': 33117.43}, {'field': 'count', 'old_value': 1177, 'new_value': 1213}, {'field': 'instoreAmount', 'old_value': 7414.22, 'new_value': 7510.62}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 204}, {'field': 'onlineAmount', 'old_value': 25072.99, 'new_value': 25971.62}, {'field': 'onlineCount', 'old_value': 977, 'new_value': 1009}]
2025-06-01 08:11:14,749 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:15,202 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-06-01 08:11:15,217 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174935.96, 'new_value': 180745.99}, {'field': 'dailyBillAmount', 'old_value': 174935.96, 'new_value': 180745.99}, {'field': 'amount', 'old_value': 29053.37, 'new_value': 30024.01}, {'field': 'count', 'old_value': 716, 'new_value': 744}, {'field': 'instoreAmount', 'old_value': 25375.97, 'new_value': 26139.99}, {'field': 'instoreCount', 'old_value': 638, 'new_value': 660}, {'field': 'onlineAmount', 'old_value': 3678.09, 'new_value': 3884.71}, {'field': 'onlineCount', 'old_value': 78, 'new_value': 84}]
2025-06-01 08:11:15,217 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:15,702 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-06-01 08:11:15,702 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23066.93, 'new_value': 24603.93}, {'field': 'dailyBillAmount', 'old_value': 23066.93, 'new_value': 24603.93}, {'field': 'amount', 'old_value': 18005.08, 'new_value': 19342.08}, {'field': 'count', 'old_value': 671, 'new_value': 708}, {'field': 'instoreAmount', 'old_value': 18372.68, 'new_value': 19709.68}, {'field': 'instoreCount', 'old_value': 671, 'new_value': 708}]
2025-06-01 08:11:15,702 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:16,171 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-06-01 08:11:16,171 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51918.72, 'new_value': 54235.68}, {'field': 'dailyBillAmount', 'old_value': 51918.72, 'new_value': 54235.68}, {'field': 'amount', 'old_value': 35547.96, 'new_value': 36861.14}, {'field': 'count', 'old_value': 1831, 'new_value': 1903}, {'field': 'instoreAmount', 'old_value': 18381.42, 'new_value': 18841.3}, {'field': 'instoreCount', 'old_value': 712, 'new_value': 728}, {'field': 'onlineAmount', 'old_value': 18168.41, 'new_value': 19137.11}, {'field': 'onlineCount', 'old_value': 1119, 'new_value': 1175}]
2025-06-01 08:11:16,171 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:16,624 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-06-01 08:11:16,624 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 387197.54, 'new_value': 396467.4}, {'field': 'dailyBillAmount', 'old_value': 387197.54, 'new_value': 396467.4}, {'field': 'amount', 'old_value': 182674.45, 'new_value': 187875.24}, {'field': 'count', 'old_value': 767, 'new_value': 789}, {'field': 'instoreAmount', 'old_value': 188170.9, 'new_value': 193750.9}, {'field': 'instoreCount', 'old_value': 767, 'new_value': 789}]
2025-06-01 08:11:16,624 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:17,092 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-06-01 08:11:17,092 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 239878.8, 'new_value': 247661.8}, {'field': 'dailyBillAmount', 'old_value': 239878.8, 'new_value': 247661.8}, {'field': 'amount', 'old_value': 118984.45, 'new_value': 123302.69}, {'field': 'count', 'old_value': 5045, 'new_value': 5221}, {'field': 'instoreAmount', 'old_value': 121388.59, 'new_value': 125792.73}, {'field': 'instoreCount', 'old_value': 5045, 'new_value': 5221}]
2025-06-01 08:11:17,092 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:17,608 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-06-01 08:11:17,608 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 563567.0, 'new_value': 586386.5}, {'field': 'dailyBillAmount', 'old_value': 563567.0, 'new_value': 586386.5}, {'field': 'amount', 'old_value': 563567.0, 'new_value': 586386.5}, {'field': 'count', 'old_value': 714, 'new_value': 744}, {'field': 'instoreAmount', 'old_value': 563567.0, 'new_value': 586386.5}, {'field': 'instoreCount', 'old_value': 714, 'new_value': 744}]
2025-06-01 08:11:17,608 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:18,108 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-06-01 08:11:18,108 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 239923.13, 'new_value': 254512.23}, {'field': 'dailyBillAmount', 'old_value': 239923.13, 'new_value': 254512.23}, {'field': 'amount', 'old_value': 139047.91, 'new_value': 150230.81}, {'field': 'count', 'old_value': 367, 'new_value': 395}, {'field': 'instoreAmount', 'old_value': 140465.25, 'new_value': 151648.15}, {'field': 'instoreCount', 'old_value': 367, 'new_value': 395}]
2025-06-01 08:11:18,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:18,592 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-06-01 08:11:18,592 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65262.0, 'new_value': 66740.0}, {'field': 'dailyBillAmount', 'old_value': 65262.0, 'new_value': 66740.0}, {'field': 'amount', 'old_value': 65262.0, 'new_value': 66740.0}, {'field': 'count', 'old_value': 1272, 'new_value': 1302}, {'field': 'instoreAmount', 'old_value': 65301.0, 'new_value': 66779.0}, {'field': 'instoreCount', 'old_value': 1272, 'new_value': 1302}]
2025-06-01 08:11:18,592 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:19,046 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-06-01 08:11:19,046 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 108723.24, 'new_value': 113047.95}, {'field': 'dailyBillAmount', 'old_value': 108723.24, 'new_value': 113047.95}, {'field': 'amount', 'old_value': 112049.1, 'new_value': 116406.54}, {'field': 'count', 'old_value': 5931, 'new_value': 6139}, {'field': 'instoreAmount', 'old_value': 55254.88, 'new_value': 57206.24}, {'field': 'instoreCount', 'old_value': 2766, 'new_value': 2861}, {'field': 'onlineAmount', 'old_value': 58210.64, 'new_value': 60719.22}, {'field': 'onlineCount', 'old_value': 3165, 'new_value': 3278}]
2025-06-01 08:11:19,046 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:19,514 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-06-01 08:11:19,514 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38591.85, 'new_value': 39684.85}, {'field': 'dailyBillAmount', 'old_value': 38591.85, 'new_value': 39684.85}, {'field': 'amount', 'old_value': 52081.2, 'new_value': 53427.4}, {'field': 'count', 'old_value': 1520, 'new_value': 1554}, {'field': 'instoreAmount', 'old_value': 48321.55, 'new_value': 49667.75}, {'field': 'instoreCount', 'old_value': 1369, 'new_value': 1403}]
2025-06-01 08:11:19,514 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:20,030 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-06-01 08:11:20,030 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 79867.81, 'new_value': 82459.6}, {'field': 'dailyBillAmount', 'old_value': 79867.81, 'new_value': 82459.6}, {'field': 'amount', 'old_value': 79812.03, 'new_value': 82368.69}, {'field': 'count', 'old_value': 3103, 'new_value': 3187}, {'field': 'instoreAmount', 'old_value': 52657.32, 'new_value': 54297.32}, {'field': 'instoreCount', 'old_value': 1877, 'new_value': 1919}, {'field': 'onlineAmount', 'old_value': 27477.54, 'new_value': 28394.2}, {'field': 'onlineCount', 'old_value': 1226, 'new_value': 1268}]
2025-06-01 08:11:20,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:20,530 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-06-01 08:11:20,530 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 75631.29, 'new_value': 76519.29}, {'field': 'count', 'old_value': 952, 'new_value': 963}, {'field': 'instoreAmount', 'old_value': 76179.99, 'new_value': 77067.99}, {'field': 'instoreCount', 'old_value': 952, 'new_value': 963}]
2025-06-01 08:11:20,530 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:20,983 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-06-01 08:11:20,983 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80777.6, 'new_value': 84990.0}, {'field': 'amount', 'old_value': 80777.1, 'new_value': 84989.5}, {'field': 'count', 'old_value': 2101, 'new_value': 2186}, {'field': 'instoreAmount', 'old_value': 81930.6, 'new_value': 86199.0}, {'field': 'instoreCount', 'old_value': 2101, 'new_value': 2186}]
2025-06-01 08:11:20,983 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:21,452 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-06-01 08:11:21,452 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 375809.42, 'new_value': 402567.42}, {'field': 'dailyBillAmount', 'old_value': 375809.42, 'new_value': 402567.42}, {'field': 'amount', 'old_value': 122662.82, 'new_value': 130285.82}, {'field': 'count', 'old_value': 421, 'new_value': 444}, {'field': 'instoreAmount', 'old_value': 122762.82, 'new_value': 130385.82}, {'field': 'instoreCount', 'old_value': 421, 'new_value': 444}]
2025-06-01 08:11:21,452 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:22,092 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-06-01 08:11:22,092 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 97018.56, 'new_value': 99290.46}, {'field': 'dailyBillAmount', 'old_value': 97018.56, 'new_value': 99290.46}, {'field': 'amount', 'old_value': 98084.36, 'new_value': 100356.26}, {'field': 'count', 'old_value': 366, 'new_value': 380}, {'field': 'instoreAmount', 'old_value': 100218.99, 'new_value': 102490.89}, {'field': 'instoreCount', 'old_value': 366, 'new_value': 380}]
2025-06-01 08:11:22,092 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:22,608 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-06-01 08:11:22,608 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62005.0, 'new_value': 64853.0}, {'field': 'dailyBillAmount', 'old_value': 62005.0, 'new_value': 64853.0}, {'field': 'amount', 'old_value': 75334.0, 'new_value': 77980.0}, {'field': 'count', 'old_value': 142, 'new_value': 147}, {'field': 'instoreAmount', 'old_value': 80686.0, 'new_value': 83332.0}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 147}]
2025-06-01 08:11:22,608 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:23,014 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-06-01 08:11:23,014 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115061.75, 'new_value': 117482.75}, {'field': 'dailyBillAmount', 'old_value': 107675.25, 'new_value': 110096.25}, {'field': 'amount', 'old_value': 115059.15, 'new_value': 117480.15}, {'field': 'count', 'old_value': 354, 'new_value': 361}, {'field': 'instoreAmount', 'old_value': 128983.05, 'new_value': 131404.05}, {'field': 'instoreCount', 'old_value': 354, 'new_value': 361}]
2025-06-01 08:11:23,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:23,577 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-06-01 08:11:23,577 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 139239.86, 'new_value': 146050.26}, {'field': 'dailyBillAmount', 'old_value': 139084.26, 'new_value': 145894.66}, {'field': 'amount', 'old_value': 79113.9, 'new_value': 84333.83}, {'field': 'count', 'old_value': 2135, 'new_value': 2266}, {'field': 'instoreAmount', 'old_value': 68568.65, 'new_value': 72158.3}, {'field': 'instoreCount', 'old_value': 1799, 'new_value': 1887}, {'field': 'onlineAmount', 'old_value': 11682.46, 'new_value': 13385.24}, {'field': 'onlineCount', 'old_value': 336, 'new_value': 379}]
2025-06-01 08:11:23,577 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:24,124 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-06-01 08:11:24,124 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 205652.21, 'new_value': 209461.17}, {'field': 'dailyBillAmount', 'old_value': 200126.59, 'new_value': 203863.24}, {'field': 'amount', 'old_value': 205652.01, 'new_value': 209460.97}, {'field': 'count', 'old_value': 2550, 'new_value': 2595}, {'field': 'instoreAmount', 'old_value': 196538.35, 'new_value': 200008.55}, {'field': 'instoreCount', 'old_value': 2441, 'new_value': 2483}, {'field': 'onlineAmount', 'old_value': 9173.1, 'new_value': 9511.86}, {'field': 'onlineCount', 'old_value': 109, 'new_value': 112}]
2025-06-01 08:11:24,124 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:24,499 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-06-01 08:11:24,499 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86681.53, 'new_value': 91228.27}, {'field': 'dailyBillAmount', 'old_value': 86681.53, 'new_value': 91228.27}, {'field': 'amount', 'old_value': 115290.6, 'new_value': 119933.46}, {'field': 'count', 'old_value': 522, 'new_value': 545}, {'field': 'instoreAmount', 'old_value': 111288.84, 'new_value': 115610.81999999999}, {'field': 'instoreCount', 'old_value': 467, 'new_value': 487}, {'field': 'onlineAmount', 'old_value': 4159.76, 'new_value': 4480.64}, {'field': 'onlineCount', 'old_value': 55, 'new_value': 58}]
2025-06-01 08:11:24,499 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:24,999 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-06-01 08:11:24,999 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 229541.6, 'new_value': 235336.5}, {'field': 'dailyBillAmount', 'old_value': 229541.6, 'new_value': 235336.5}, {'field': 'amount', 'old_value': 234396.2, 'new_value': 242202.1}, {'field': 'count', 'old_value': 856, 'new_value': 885}, {'field': 'instoreAmount', 'old_value': 239327.1, 'new_value': 247879.0}, {'field': 'instoreCount', 'old_value': 856, 'new_value': 885}]
2025-06-01 08:11:24,999 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:25,467 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-06-01 08:11:25,467 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52400.0, 'new_value': 53811.0}, {'field': 'dailyBillAmount', 'old_value': 52400.0, 'new_value': 53811.0}, {'field': 'amount', 'old_value': 49598.0, 'new_value': 51009.0}, {'field': 'count', 'old_value': 121, 'new_value': 127}, {'field': 'instoreAmount', 'old_value': 50440.0, 'new_value': 51851.0}, {'field': 'instoreCount', 'old_value': 121, 'new_value': 127}]
2025-06-01 08:11:25,467 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:25,936 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-06-01 08:11:25,936 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 23262.4, 'new_value': 23661.9}, {'field': 'count', 'old_value': 41, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 23543.8, 'new_value': 23943.3}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 42}]
2025-06-01 08:11:25,936 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:26,342 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-06-01 08:11:26,342 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 47167.64, 'new_value': 48001.74}, {'field': 'count', 'old_value': 264, 'new_value': 269}, {'field': 'instoreAmount', 'old_value': 44969.200000000004, 'new_value': 45779.200000000004}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 210}, {'field': 'onlineAmount', 'old_value': 3079.65, 'new_value': 3103.75}, {'field': 'onlineCount', 'old_value': 58, 'new_value': 59}]
2025-06-01 08:11:26,342 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:26,796 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-06-01 08:11:26,796 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59851.25, 'new_value': 61091.01}, {'field': 'amount', 'old_value': 59849.61, 'new_value': 61089.37}, {'field': 'count', 'old_value': 2988, 'new_value': 3074}, {'field': 'instoreAmount', 'old_value': 68106.64, 'new_value': 69841.34}, {'field': 'instoreCount', 'old_value': 2988, 'new_value': 3074}]
2025-06-01 08:11:26,796 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:27,296 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-06-01 08:11:27,296 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 157294.82, 'new_value': 160650.29}, {'field': 'dailyBillAmount', 'old_value': 157294.82, 'new_value': 160650.29}, {'field': 'amount', 'old_value': 124292.2, 'new_value': 126521.0}, {'field': 'count', 'old_value': 510, 'new_value': 523}, {'field': 'instoreAmount', 'old_value': 124190.9, 'new_value': 126419.7}, {'field': 'instoreCount', 'old_value': 505, 'new_value': 518}]
2025-06-01 08:11:27,296 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:27,733 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-06-01 08:11:27,749 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 441684.27, 'new_value': 459080.83}, {'field': 'dailyBillAmount', 'old_value': 441684.27, 'new_value': 459080.83}, {'field': 'amount', 'old_value': 249575.79, 'new_value': 256658.0}, {'field': 'count', 'old_value': 2841, 'new_value': 2911}, {'field': 'instoreAmount', 'old_value': 114292.78, 'new_value': 118000.89}, {'field': 'instoreCount', 'old_value': 1243, 'new_value': 1272}, {'field': 'onlineAmount', 'old_value': 135286.48, 'new_value': 138660.58}, {'field': 'onlineCount', 'old_value': 1598, 'new_value': 1639}]
2025-06-01 08:11:27,749 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:28,217 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-06-01 08:11:28,217 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247560.05, 'new_value': 256681.25}, {'field': 'dailyBillAmount', 'old_value': 247560.05, 'new_value': 256681.25}, {'field': 'amount', 'old_value': 266556.0, 'new_value': 276264.0}, {'field': 'count', 'old_value': 1625, 'new_value': 1675}, {'field': 'instoreAmount', 'old_value': 267335.9, 'new_value': 277043.9}, {'field': 'instoreCount', 'old_value': 1625, 'new_value': 1675}]
2025-06-01 08:11:28,217 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:28,639 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-06-01 08:11:28,639 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82165.89, 'new_value': 83743.89}, {'field': 'amount', 'old_value': 82165.89, 'new_value': 83743.89}, {'field': 'count', 'old_value': 39, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 82165.89, 'new_value': 83743.89}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 41}]
2025-06-01 08:11:28,639 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:29,202 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-06-01 08:11:29,202 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 186682.27, 'new_value': 192493.51}, {'field': 'dailyBillAmount', 'old_value': 186682.27, 'new_value': 192493.51}, {'field': 'amount', 'old_value': 116316.23, 'new_value': 118831.81}, {'field': 'count', 'old_value': 1294, 'new_value': 1320}, {'field': 'instoreAmount', 'old_value': 106758.56, 'new_value': 109396.94}, {'field': 'instoreCount', 'old_value': 951, 'new_value': 975}, {'field': 'onlineAmount', 'old_value': 11375.91, 'new_value': 11424.11}, {'field': 'onlineCount', 'old_value': 343, 'new_value': 345}]
2025-06-01 08:11:29,202 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:29,717 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-06-01 08:11:29,717 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2939.7, 'new_value': 2969.6}, {'field': 'count', 'old_value': 37, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 2939.7, 'new_value': 2969.6}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 38}]
2025-06-01 08:11:29,717 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:30,186 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-06-01 08:11:30,186 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 235863.34, 'new_value': 239733.84}, {'field': 'amount', 'old_value': 235863.34, 'new_value': 239733.84}, {'field': 'count', 'old_value': 1002, 'new_value': 1014}, {'field': 'instoreAmount', 'old_value': 235863.34, 'new_value': 239733.84}, {'field': 'instoreCount', 'old_value': 1002, 'new_value': 1014}]
2025-06-01 08:11:30,186 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:30,624 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-06-01 08:11:30,624 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29774.45, 'new_value': 30987.25}, {'field': 'dailyBillAmount', 'old_value': 29774.45, 'new_value': 30987.25}, {'field': 'amount', 'old_value': 35450.05, 'new_value': 36717.950000000004}, {'field': 'count', 'old_value': 1052, 'new_value': 1094}, {'field': 'instoreAmount', 'old_value': 35469.85, 'new_value': 36737.75}, {'field': 'instoreCount', 'old_value': 1052, 'new_value': 1094}]
2025-06-01 08:11:30,624 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:31,045 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-06-01 08:11:31,045 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 362437.8, 'new_value': 373021.8}, {'field': 'amount', 'old_value': 362437.8, 'new_value': 373021.8}, {'field': 'count', 'old_value': 570, 'new_value': 586}, {'field': 'instoreAmount', 'old_value': 362437.8, 'new_value': 373021.8}, {'field': 'instoreCount', 'old_value': 570, 'new_value': 586}]
2025-06-01 08:11:31,045 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:31,639 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-06-01 08:11:31,639 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53745.78, 'new_value': 54490.48}, {'field': 'amount', 'old_value': 53745.78, 'new_value': 54490.48}, {'field': 'count', 'old_value': 437, 'new_value': 441}, {'field': 'instoreAmount', 'old_value': 53745.78, 'new_value': 54490.48}, {'field': 'instoreCount', 'old_value': 437, 'new_value': 441}]
2025-06-01 08:11:31,639 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:32,045 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-06-01 08:11:32,045 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 381472.0, 'new_value': 399220.0}, {'field': 'amount', 'old_value': 381472.0, 'new_value': 399220.0}, {'field': 'count', 'old_value': 86, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 381472.0, 'new_value': 399220.0}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 88}]
2025-06-01 08:11:32,045 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:32,514 - INFO - 更新表单数据成功: FINST-OPC666D1DEUV15H0FNQXAAZ3JUDL2OCUYB9BM41
2025-06-01 08:11:32,514 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92158.8, 'new_value': 95867.8}, {'field': 'dailyBillAmount', 'old_value': 92158.8, 'new_value': 95867.8}, {'field': 'amount', 'old_value': 92158.8, 'new_value': 95867.8}, {'field': 'count', 'old_value': 547, 'new_value': 568}, {'field': 'instoreAmount', 'old_value': 92158.8, 'new_value': 95867.8}, {'field': 'instoreCount', 'old_value': 547, 'new_value': 568}]
2025-06-01 08:11:32,514 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:33,030 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMG01
2025-06-01 08:11:33,030 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_8711865ACD3B4C2AADD3843CA2A204D9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 62716.0, 'new_value': 66004.0}, {'field': 'amount', 'old_value': 62716.0, 'new_value': 66004.0}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 62716.0, 'new_value': 66004.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-01 08:11:33,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:33,483 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-06-01 08:11:33,483 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 58916.49, 'new_value': 60158.19}, {'field': 'count', 'old_value': 604, 'new_value': 616}, {'field': 'instoreAmount', 'old_value': 58916.49, 'new_value': 60158.19}, {'field': 'instoreCount', 'old_value': 604, 'new_value': 616}]
2025-06-01 08:11:33,483 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:33,905 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-06-01 08:11:33,905 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49622.2, 'new_value': 50587.5}, {'field': 'dailyBillAmount', 'old_value': 49622.2, 'new_value': 50587.5}, {'field': 'amount', 'old_value': 51085.5, 'new_value': 52050.8}, {'field': 'count', 'old_value': 65, 'new_value': 68}, {'field': 'instoreAmount', 'old_value': 51983.5, 'new_value': 53457.07}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 68}]
2025-06-01 08:11:33,905 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:34,358 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-06-01 08:11:34,358 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 527132.48, 'new_value': 542065.48}, {'field': 'dailyBillAmount', 'old_value': 527132.48, 'new_value': 542065.48}, {'field': 'amount', 'old_value': 534303.48, 'new_value': 549236.48}, {'field': 'count', 'old_value': 1720, 'new_value': 1767}, {'field': 'instoreAmount', 'old_value': 534303.48, 'new_value': 549236.48}, {'field': 'instoreCount', 'old_value': 1720, 'new_value': 1767}]
2025-06-01 08:11:34,358 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:34,858 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-06-01 08:11:34,858 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 368202.0, 'new_value': 443724.0}, {'field': 'dailyBillAmount', 'old_value': 368202.0, 'new_value': 443724.0}, {'field': 'amount', 'old_value': 1231570.27, 'new_value': 1301017.21}, {'field': 'count', 'old_value': 1541, 'new_value': 1612}, {'field': 'instoreAmount', 'old_value': 1231570.44, 'new_value': 1301017.3800000001}, {'field': 'instoreCount', 'old_value': 1541, 'new_value': 1612}]
2025-06-01 08:11:34,858 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:35,264 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-06-01 08:11:35,264 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 168434.6, 'new_value': 176469.0}, {'field': 'dailyBillAmount', 'old_value': 168434.6, 'new_value': 176469.0}, {'field': 'amount', 'old_value': 35948.6, 'new_value': 39748.4}, {'field': 'count', 'old_value': 142, 'new_value': 157}, {'field': 'instoreAmount', 'old_value': 35951.0, 'new_value': 39750.8}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 157}]
2025-06-01 08:11:35,264 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:35,749 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-06-01 08:11:35,749 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 217327.99, 'new_value': 229132.52}, {'field': 'amount', 'old_value': 217323.75, 'new_value': 229128.28}, {'field': 'count', 'old_value': 2295, 'new_value': 2409}, {'field': 'instoreAmount', 'old_value': 136515.3, 'new_value': 144456.63}, {'field': 'instoreCount', 'old_value': 1257, 'new_value': 1331}, {'field': 'onlineAmount', 'old_value': 86448.15, 'new_value': 90341.95}, {'field': 'onlineCount', 'old_value': 1038, 'new_value': 1078}]
2025-06-01 08:11:35,749 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:36,249 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-06-01 08:11:36,249 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 388192.92, 'new_value': 403617.47}, {'field': 'dailyBillAmount', 'old_value': 388192.92, 'new_value': 403617.47}, {'field': 'amount', 'old_value': 34061.34, 'new_value': 35288.21}, {'field': 'count', 'old_value': 1031, 'new_value': 1061}, {'field': 'instoreAmount', 'old_value': 39827.840000000004, 'new_value': 41181.01}, {'field': 'instoreCount', 'old_value': 1031, 'new_value': 1061}]
2025-06-01 08:11:36,264 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:36,764 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-06-01 08:11:36,764 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 346486.94, 'new_value': 375811.28}, {'field': 'dailyBillAmount', 'old_value': 346486.94, 'new_value': 375811.28}, {'field': 'amount', 'old_value': 177678.67, 'new_value': 187504.68}, {'field': 'count', 'old_value': 3942, 'new_value': 4180}, {'field': 'instoreAmount', 'old_value': 148027.61000000002, 'new_value': 157901.72}, {'field': 'instoreCount', 'old_value': 3277, 'new_value': 3512}, {'field': 'onlineAmount', 'old_value': 32184.37, 'new_value': 32274.670000000002}, {'field': 'onlineCount', 'old_value': 665, 'new_value': 668}]
2025-06-01 08:11:36,764 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:37,217 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-06-01 08:11:37,217 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 311681.9, 'new_value': 334434.2}, {'field': 'amount', 'old_value': 311679.4, 'new_value': 334431.7}, {'field': 'count', 'old_value': 1230, 'new_value': 1302}, {'field': 'instoreAmount', 'old_value': 317330.5, 'new_value': 340401.8}, {'field': 'instoreCount', 'old_value': 1230, 'new_value': 1302}]
2025-06-01 08:11:37,217 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:37,733 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-06-01 08:11:37,733 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 486505.22, 'new_value': 551384.46}, {'field': 'dailyBillAmount', 'old_value': 486505.22, 'new_value': 551384.46}, {'field': 'amount', 'old_value': 480281.55, 'new_value': 527328.9}, {'field': 'count', 'old_value': 9043, 'new_value': 9877}, {'field': 'instoreAmount', 'old_value': 447746.23, 'new_value': 493325.15}, {'field': 'instoreCount', 'old_value': 8411, 'new_value': 9211}, {'field': 'onlineAmount', 'old_value': 34457.49, 'new_value': 35939.72}, {'field': 'onlineCount', 'old_value': 632, 'new_value': 666}]
2025-06-01 08:11:37,733 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:38,186 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-06-01 08:11:38,186 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 477725.57, 'new_value': 499831.12}, {'field': 'amount', 'old_value': 445440.2, 'new_value': 467545.75}, {'field': 'count', 'old_value': 10459, 'new_value': 10932}, {'field': 'instoreAmount', 'old_value': 326415.7, 'new_value': 340054.1}, {'field': 'instoreCount', 'old_value': 7031, 'new_value': 7298}, {'field': 'onlineAmount', 'old_value': 119184.4, 'new_value': 127651.55}, {'field': 'onlineCount', 'old_value': 3428, 'new_value': 3634}]
2025-06-01 08:11:38,186 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:38,717 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-06-01 08:11:38,717 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30440.85, 'new_value': 29890.12}, {'field': 'count', 'old_value': 850, 'new_value': 853}, {'field': 'instoreAmount', 'old_value': 2857.0, 'new_value': 3033.0}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 45175.729999999996, 'new_value': 45214.729999999996}, {'field': 'onlineCount', 'old_value': 803, 'new_value': 804}]
2025-06-01 08:11:38,717 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:39,108 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-06-01 08:11:39,108 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 195119.74, 'new_value': 202690.56}, {'field': 'count', 'old_value': 13267, 'new_value': 13757}, {'field': 'instoreAmount', 'old_value': 154504.63, 'new_value': 159928.05}, {'field': 'instoreCount', 'old_value': 10217, 'new_value': 10548}, {'field': 'onlineAmount', 'old_value': 45015.98, 'new_value': 47301.08}, {'field': 'onlineCount', 'old_value': 3050, 'new_value': 3209}]
2025-06-01 08:11:39,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:39,577 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-06-01 08:11:39,577 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 300637.25, 'new_value': 317884.05}, {'field': 'dailyBillAmount', 'old_value': 300637.25, 'new_value': 317884.05}, {'field': 'amount', 'old_value': 291881.87, 'new_value': 309476.99}, {'field': 'count', 'old_value': 8570, 'new_value': 9055}, {'field': 'instoreAmount', 'old_value': 293747.38, 'new_value': 311460.1}, {'field': 'instoreCount', 'old_value': 8570, 'new_value': 9055}]
2025-06-01 08:11:39,577 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:40,108 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-06-01 08:11:40,108 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92122.87, 'new_value': 98784.33}, {'field': 'amount', 'old_value': 92119.46, 'new_value': 98780.92}, {'field': 'count', 'old_value': 5048, 'new_value': 5433}, {'field': 'instoreAmount', 'old_value': 49414.22, 'new_value': 53593.76}, {'field': 'instoreCount', 'old_value': 2978, 'new_value': 3230}, {'field': 'onlineAmount', 'old_value': 42708.65, 'new_value': 45190.57}, {'field': 'onlineCount', 'old_value': 2070, 'new_value': 2203}]
2025-06-01 08:11:40,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:40,592 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-06-01 08:11:40,592 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 160524.56, 'new_value': 168529.24}, {'field': 'dailyBillAmount', 'old_value': 160524.56, 'new_value': 168529.24}, {'field': 'amount', 'old_value': 33300.04, 'new_value': 34820.54}, {'field': 'count', 'old_value': 1195, 'new_value': 1250}, {'field': 'instoreAmount', 'old_value': 34459.68, 'new_value': 35980.18}, {'field': 'instoreCount', 'old_value': 1195, 'new_value': 1250}]
2025-06-01 08:11:40,592 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:41,108 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-06-01 08:11:41,108 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 120954.77, 'new_value': 129030.76}, {'field': 'dailyBillAmount', 'old_value': 120954.77, 'new_value': 129030.76}, {'field': 'amount', 'old_value': 108338.0, 'new_value': 112208.17}, {'field': 'count', 'old_value': 5326, 'new_value': 5537}, {'field': 'instoreAmount', 'old_value': 22818.690000000002, 'new_value': 23751.66}, {'field': 'instoreCount', 'old_value': 1590, 'new_value': 1654}, {'field': 'onlineAmount', 'old_value': 87330.04000000001, 'new_value': 90323.24}, {'field': 'onlineCount', 'old_value': 3736, 'new_value': 3883}]
2025-06-01 08:11:41,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:41,561 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-06-01 08:11:41,561 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126958.07, 'new_value': 135750.91}, {'field': 'amount', 'old_value': 126956.66, 'new_value': 135749.5}, {'field': 'count', 'old_value': 3187, 'new_value': 3424}, {'field': 'instoreAmount', 'old_value': 120057.77, 'new_value': 128156.41}, {'field': 'instoreCount', 'old_value': 3086, 'new_value': 3316}, {'field': 'onlineAmount', 'old_value': 7975.72, 'new_value': 8718.92}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 108}]
2025-06-01 08:11:41,561 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:41,999 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-06-01 08:11:41,999 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 204305.68, 'new_value': 213466.35}, {'field': 'count', 'old_value': 8444, 'new_value': 8924}, {'field': 'instoreAmount', 'old_value': 208322.44, 'new_value': 218030.29}, {'field': 'instoreCount', 'old_value': 8363, 'new_value': 8837}, {'field': 'onlineAmount', 'old_value': 2647.61, 'new_value': 2803.0099999999998}, {'field': 'onlineCount', 'old_value': 81, 'new_value': 87}]
2025-06-01 08:11:41,999 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:42,467 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-06-01 08:11:42,467 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 209286.16, 'new_value': 220011.86}, {'field': 'dailyBillAmount', 'old_value': 209286.16, 'new_value': 220011.86}, {'field': 'amount', 'old_value': 141528.84, 'new_value': 146825.91}, {'field': 'count', 'old_value': 11074, 'new_value': 11478}, {'field': 'instoreAmount', 'old_value': 11734.82, 'new_value': 12036.82}, {'field': 'instoreCount', 'old_value': 619, 'new_value': 650}, {'field': 'onlineAmount', 'old_value': 136369.93, 'new_value': 141824.82}, {'field': 'onlineCount', 'old_value': 10455, 'new_value': 10828}]
2025-06-01 08:11:42,467 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:42,920 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-06-01 08:11:42,920 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 181423.38999999998, 'new_value': 193366.81}, {'field': 'dailyBillAmount', 'old_value': 181423.38999999998, 'new_value': 193366.81}, {'field': 'amount', 'old_value': 156305.48, 'new_value': 166755.39}, {'field': 'count', 'old_value': 5083, 'new_value': 5397}, {'field': 'instoreAmount', 'old_value': 84992.12, 'new_value': 92642.83}, {'field': 'instoreCount', 'old_value': 3631, 'new_value': 3879}, {'field': 'onlineAmount', 'old_value': 81298.53, 'new_value': 84623.73}, {'field': 'onlineCount', 'old_value': 1452, 'new_value': 1518}]
2025-06-01 08:11:42,920 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:43,389 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-06-01 08:11:43,389 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 139587.45, 'new_value': 140267.45}, {'field': 'amount', 'old_value': 139586.92, 'new_value': 140266.92}, {'field': 'count', 'old_value': 99, 'new_value': 100}, {'field': 'instoreAmount', 'old_value': 139587.45, 'new_value': 140267.45}, {'field': 'instoreCount', 'old_value': 99, 'new_value': 100}]
2025-06-01 08:11:43,389 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:43,874 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-06-01 08:11:43,874 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67976.22, 'new_value': 70985.77}, {'field': 'dailyBillAmount', 'old_value': 67976.22, 'new_value': 70985.77}, {'field': 'amount', 'old_value': 88871.44, 'new_value': 92207.51}, {'field': 'count', 'old_value': 3467, 'new_value': 3596}, {'field': 'instoreAmount', 'old_value': 27616.1, 'new_value': 29267.88}, {'field': 'instoreCount', 'old_value': 1188, 'new_value': 1250}, {'field': 'onlineAmount', 'old_value': 62435.29, 'new_value': 64123.48}, {'field': 'onlineCount', 'old_value': 2279, 'new_value': 2346}]
2025-06-01 08:11:43,874 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:44,389 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-06-01 08:11:44,389 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 107984.51, 'new_value': 114826.34}, {'field': 'dailyBillAmount', 'old_value': 107984.51, 'new_value': 114826.34}, {'field': 'amount', 'old_value': 111524.25, 'new_value': 118568.58}, {'field': 'count', 'old_value': 3962, 'new_value': 4198}, {'field': 'instoreAmount', 'old_value': 111396.84999999999, 'new_value': 118441.18}, {'field': 'instoreCount', 'old_value': 3956, 'new_value': 4192}]
2025-06-01 08:11:44,389 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:44,827 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-06-01 08:11:44,827 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 380858.0, 'new_value': 401959.0}, {'field': 'count', 'old_value': 305, 'new_value': 316}, {'field': 'instoreAmount', 'old_value': 414929.0, 'new_value': 436030.0}, {'field': 'instoreCount', 'old_value': 305, 'new_value': 316}]
2025-06-01 08:11:44,827 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:45,405 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-06-01 08:11:45,405 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 252926.85, 'new_value': 267933.45}, {'field': 'dailyBillAmount', 'old_value': 252926.85, 'new_value': 267933.45}, {'field': 'amount', 'old_value': 257379.21, 'new_value': 272690.86}, {'field': 'count', 'old_value': 514, 'new_value': 546}, {'field': 'instoreAmount', 'old_value': 260848.71, 'new_value': 276160.36}, {'field': 'instoreCount', 'old_value': 514, 'new_value': 546}]
2025-06-01 08:11:45,405 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:45,889 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8M
2025-06-01 08:11:45,889 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55729.0, 'new_value': 67983.0}, {'field': 'amount', 'old_value': 55729.0, 'new_value': 67983.0}, {'field': 'count', 'old_value': 130, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 55729.0, 'new_value': 67983.0}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 146}]
2025-06-01 08:11:45,889 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:46,389 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-06-01 08:11:46,389 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 44591.0, 'new_value': 47070.0}, {'field': 'count', 'old_value': 120, 'new_value': 127}, {'field': 'instoreAmount', 'old_value': 46053.0, 'new_value': 48532.0}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 127}]
2025-06-01 08:11:46,389 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:46,827 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-06-01 08:11:46,827 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84575.0, 'new_value': 87786.0}, {'field': 'amount', 'old_value': 67229.0, 'new_value': 70440.0}, {'field': 'count', 'old_value': 93, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 67229.0, 'new_value': 70440.0}, {'field': 'instoreCount', 'old_value': 93, 'new_value': 97}]
2025-06-01 08:11:46,827 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:47,342 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-06-01 08:11:47,342 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77447.7, 'new_value': 81469.7}, {'field': 'amount', 'old_value': 77445.5, 'new_value': 81467.5}, {'field': 'count', 'old_value': 206, 'new_value': 213}, {'field': 'instoreAmount', 'old_value': 77936.4, 'new_value': 81958.4}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 213}]
2025-06-01 08:11:47,342 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:47,780 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-06-01 08:11:47,780 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 113277.0, 'new_value': 121001.0}, {'field': 'amount', 'old_value': 113277.0, 'new_value': 121001.0}, {'field': 'count', 'old_value': 32, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 113277.0, 'new_value': 121001.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 34}]
2025-06-01 08:11:47,780 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:48,233 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-06-01 08:11:48,233 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30254.0, 'new_value': 34327.0}, {'field': 'amount', 'old_value': 30254.0, 'new_value': 34327.0}, {'field': 'count', 'old_value': 45, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 30254.0, 'new_value': 34327.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 53}]
2025-06-01 08:11:48,233 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:48,686 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-06-01 08:11:48,686 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78619.0, 'new_value': 81107.0}, {'field': 'amount', 'old_value': 78619.0, 'new_value': 81107.0}, {'field': 'count', 'old_value': 89, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 78619.0, 'new_value': 81107.0}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 93}]
2025-06-01 08:11:48,686 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:49,155 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-06-01 08:11:49,155 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287468.0, 'new_value': 309292.4}, {'field': 'dailyBillAmount', 'old_value': 287468.0, 'new_value': 309292.4}, {'field': 'amount', 'old_value': 375537.1, 'new_value': 393885.1}, {'field': 'count', 'old_value': 470, 'new_value': 495}, {'field': 'instoreAmount', 'old_value': 389689.96, 'new_value': 408282.26}, {'field': 'instoreCount', 'old_value': 470, 'new_value': 495}]
2025-06-01 08:11:49,155 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:49,655 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-06-01 08:11:49,655 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121356.64, 'new_value': 126407.62}, {'field': 'dailyBillAmount', 'old_value': 121356.64, 'new_value': 126407.62}, {'field': 'amount', 'old_value': 60501.5, 'new_value': 65982.9}, {'field': 'count', 'old_value': 599, 'new_value': 666}, {'field': 'instoreAmount', 'old_value': 58816.18, 'new_value': 64387.98}, {'field': 'instoreCount', 'old_value': 531, 'new_value': 594}, {'field': 'onlineAmount', 'old_value': 4247.42, 'new_value': 4456.02}, {'field': 'onlineCount', 'old_value': 68, 'new_value': 72}]
2025-06-01 08:11:49,655 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:50,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-06-01 08:11:50,139 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16964.0, 'new_value': 17864.0}, {'field': 'amount', 'old_value': 16964.0, 'new_value': 17864.0}, {'field': 'count', 'old_value': 45, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 16964.0, 'new_value': 17864.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 46}]
2025-06-01 08:11:50,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:50,639 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-06-01 08:11:50,639 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40768.0, 'new_value': 42190.0}, {'field': 'dailyBillAmount', 'old_value': 40768.0, 'new_value': 42190.0}, {'field': 'amount', 'old_value': 46851.0, 'new_value': 48270.0}, {'field': 'count', 'old_value': 147, 'new_value': 154}, {'field': 'instoreAmount', 'old_value': 46851.0, 'new_value': 48270.0}, {'field': 'instoreCount', 'old_value': 147, 'new_value': 154}]
2025-06-01 08:11:50,639 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:51,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-06-01 08:11:51,139 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37323.7, 'new_value': 39018.7}, {'field': 'amount', 'old_value': 37323.7, 'new_value': 39018.7}, {'field': 'count', 'old_value': 229, 'new_value': 241}, {'field': 'instoreAmount', 'old_value': 37661.7, 'new_value': 39356.7}, {'field': 'instoreCount', 'old_value': 229, 'new_value': 241}]
2025-06-01 08:11:51,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:51,608 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-06-01 08:11:51,608 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9906.0, 'new_value': 10290.0}, {'field': 'dailyBillAmount', 'old_value': 9906.0, 'new_value': 10290.0}, {'field': 'amount', 'old_value': 46474.0, 'new_value': 49160.0}, {'field': 'count', 'old_value': 143, 'new_value': 150}, {'field': 'instoreAmount', 'old_value': 47249.0, 'new_value': 49935.0}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 150}]
2025-06-01 08:11:51,608 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:52,045 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-06-01 08:11:52,045 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 912681.69, 'new_value': 1014480.99}, {'field': 'dailyBillAmount', 'old_value': 912681.69, 'new_value': 1014480.99}, {'field': 'amount', 'old_value': 58385.95, 'new_value': 61463.67}, {'field': 'count', 'old_value': 599, 'new_value': 632}, {'field': 'instoreAmount', 'old_value': 45765.99, 'new_value': 48536.63}, {'field': 'instoreCount', 'old_value': 413, 'new_value': 439}, {'field': 'onlineAmount', 'old_value': 13674.61, 'new_value': 14136.19}, {'field': 'onlineCount', 'old_value': 186, 'new_value': 193}]
2025-06-01 08:11:52,045 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:52,530 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-06-01 08:11:52,530 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87489.5, 'new_value': 88587.5}, {'field': 'amount', 'old_value': 87289.5, 'new_value': 88387.5}, {'field': 'count', 'old_value': 115, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 89836.0, 'new_value': 90934.0}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 117}]
2025-06-01 08:11:52,530 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:53,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-06-01 08:11:53,139 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17522.0, 'new_value': 17871.0}, {'field': 'amount', 'old_value': 17522.0, 'new_value': 17871.0}, {'field': 'count', 'old_value': 33, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 17522.0, 'new_value': 17871.0}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 34}]
2025-06-01 08:11:53,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:53,639 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-06-01 08:11:53,639 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28136.37, 'new_value': 29025.61}, {'field': 'amount', 'old_value': 28134.94, 'new_value': 29024.18}, {'field': 'count', 'old_value': 113, 'new_value': 117}, {'field': 'instoreAmount', 'old_value': 28136.37, 'new_value': 29025.61}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 117}]
2025-06-01 08:11:53,639 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:54,030 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-06-01 08:11:54,030 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51861.0, 'new_value': 54691.0}, {'field': 'dailyBillAmount', 'old_value': 51861.0, 'new_value': 54691.0}, {'field': 'amount', 'old_value': 52077.0, 'new_value': 54947.0}, {'field': 'count', 'old_value': 123, 'new_value': 128}, {'field': 'instoreAmount', 'old_value': 53994.0, 'new_value': 57101.0}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 128}]
2025-06-01 08:11:54,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:54,561 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-06-01 08:11:54,561 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 374193.93, 'new_value': 392845.89}, {'field': 'dailyBillAmount', 'old_value': 345010.62, 'new_value': 346028.12}, {'field': 'amount', 'old_value': 371612.64, 'new_value': 390264.6}, {'field': 'count', 'old_value': 1025, 'new_value': 1126}, {'field': 'instoreAmount', 'old_value': 375659.88, 'new_value': 394311.83999999997}, {'field': 'instoreCount', 'old_value': 1025, 'new_value': 1126}]
2025-06-01 08:11:54,561 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:54,983 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-06-01 08:11:54,983 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77875.0, 'new_value': 82114.0}, {'field': 'amount', 'old_value': 77875.0, 'new_value': 82114.0}, {'field': 'count', 'old_value': 330, 'new_value': 347}, {'field': 'instoreAmount', 'old_value': 78977.0, 'new_value': 83274.0}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 347}]
2025-06-01 08:11:54,983 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:55,483 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-06-01 08:11:55,483 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127321.79, 'new_value': 146577.79}, {'field': 'dailyBillAmount', 'old_value': 127321.79, 'new_value': 146577.79}, {'field': 'amount', 'old_value': 129716.31, 'new_value': 148972.31}, {'field': 'count', 'old_value': 795, 'new_value': 899}, {'field': 'instoreAmount', 'old_value': 129716.31, 'new_value': 148972.31}, {'field': 'instoreCount', 'old_value': 795, 'new_value': 899}]
2025-06-01 08:11:55,483 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:55,936 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-06-01 08:11:55,936 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115113.16, 'new_value': 120467.76}, {'field': 'dailyBillAmount', 'old_value': 115113.16, 'new_value': 120467.76}, {'field': 'amount', 'old_value': 40027.62, 'new_value': 43310.4}, {'field': 'count', 'old_value': 3984, 'new_value': 4258}, {'field': 'instoreAmount', 'old_value': 42542.17, 'new_value': 45870.25}, {'field': 'instoreCount', 'old_value': 3984, 'new_value': 4258}]
2025-06-01 08:11:55,936 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:56,373 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-06-01 08:11:56,373 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 697873.12, 'new_value': 737427.99}, {'field': 'dailyBillAmount', 'old_value': 697873.12, 'new_value': 737427.99}, {'field': 'amount', 'old_value': 710920.08, 'new_value': 752630.23}, {'field': 'count', 'old_value': 7369, 'new_value': 7631}, {'field': 'instoreAmount', 'old_value': 537189.35, 'new_value': 571949.43}, {'field': 'instoreCount', 'old_value': 2750, 'new_value': 2910}, {'field': 'onlineAmount', 'old_value': 179816.58, 'new_value': 186814.18}, {'field': 'onlineCount', 'old_value': 4619, 'new_value': 4721}]
2025-06-01 08:11:56,389 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:56,717 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-06-01 08:11:56,733 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 210639.98, 'new_value': 227434.98}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16795.0}, {'field': 'amount', 'old_value': 210639.98, 'new_value': 227434.98}, {'field': 'count', 'old_value': 1422, 'new_value': 1525}, {'field': 'instoreAmount', 'old_value': 211074.98, 'new_value': 227869.98}, {'field': 'instoreCount', 'old_value': 1422, 'new_value': 1525}]
2025-06-01 08:11:56,733 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:57,217 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMP71
2025-06-01 08:11:57,217 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-05, 变更字段: [{'field': 'amount', 'old_value': 201492.73, 'new_value': 206457.57}, {'field': 'count', 'old_value': 8475, 'new_value': 8645}, {'field': 'instoreAmount', 'old_value': 75845.36, 'new_value': 78246.19}, {'field': 'instoreCount', 'old_value': 3362, 'new_value': 3435}, {'field': 'onlineAmount', 'old_value': 128972.6, 'new_value': 131610.41}, {'field': 'onlineCount', 'old_value': 5113, 'new_value': 5210}]
2025-06-01 08:11:57,217 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:57,717 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-06-01 08:11:57,717 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 104655.4, 'new_value': 107912.06}, {'field': 'dailyBillAmount', 'old_value': 104655.4, 'new_value': 107912.06}, {'field': 'amount', 'old_value': 131417.87, 'new_value': 136410.1}, {'field': 'count', 'old_value': 6170, 'new_value': 6440}, {'field': 'instoreAmount', 'old_value': 64941.48, 'new_value': 67546.8}, {'field': 'instoreCount', 'old_value': 3447, 'new_value': 3605}, {'field': 'onlineAmount', 'old_value': 69698.24, 'new_value': 72131.15}, {'field': 'onlineCount', 'old_value': 2723, 'new_value': 2835}]
2025-06-01 08:11:57,717 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:58,248 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR71
2025-06-01 08:11:58,248 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-05, 变更字段: [{'field': 'count', 'old_value': 123, 'new_value': 130}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 130}]
2025-06-01 08:11:58,248 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:58,686 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-06-01 08:11:58,686 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87391.0, 'new_value': 88269.0}, {'field': 'amount', 'old_value': 87391.0, 'new_value': 88269.0}, {'field': 'count', 'old_value': 44, 'new_value': 47}, {'field': 'instoreAmount', 'old_value': 87391.0, 'new_value': 88269.0}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 47}]
2025-06-01 08:11:58,686 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:59,155 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-06-01 08:11:59,155 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 132637.6, 'new_value': 139387.96}, {'field': 'dailyBillAmount', 'old_value': 132637.6, 'new_value': 139387.96}, {'field': 'amount', 'old_value': 64618.28, 'new_value': 67109.4}, {'field': 'count', 'old_value': 4600, 'new_value': 4760}, {'field': 'instoreAmount', 'old_value': 8955.74, 'new_value': 9613.039999999999}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 412}, {'field': 'onlineAmount', 'old_value': 55662.54, 'new_value': 57496.36}, {'field': 'onlineCount', 'old_value': 4214, 'new_value': 4348}]
2025-06-01 08:11:59,155 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:11:59,623 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-06-01 08:11:59,623 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 399756.01, 'new_value': 426324.63}, {'field': 'dailyBillAmount', 'old_value': 399756.01, 'new_value': 426324.63}, {'field': 'amount', 'old_value': 376246.76, 'new_value': 399338.62}, {'field': 'count', 'old_value': 3407, 'new_value': 3624}, {'field': 'instoreAmount', 'old_value': 273898.18, 'new_value': 292786.49}, {'field': 'instoreCount', 'old_value': 1480, 'new_value': 1601}, {'field': 'onlineAmount', 'old_value': 102352.08, 'new_value': 106555.63}, {'field': 'onlineCount', 'old_value': 1927, 'new_value': 2023}]
2025-06-01 08:11:59,623 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:00,092 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-06-01 08:12:00,092 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 447211.05, 'new_value': 477956.56}, {'field': 'dailyBillAmount', 'old_value': 447211.05, 'new_value': 477956.56}, {'field': 'amount', 'old_value': 449607.97, 'new_value': 477822.97}, {'field': 'count', 'old_value': 2738, 'new_value': 2879}, {'field': 'instoreAmount', 'old_value': 412567.77, 'new_value': 440997.77}, {'field': 'instoreCount', 'old_value': 2343, 'new_value': 2484}]
2025-06-01 08:12:00,092 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:00,498 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-06-01 08:12:00,498 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1135374.25, 'new_value': 1197288.11}, {'field': 'dailyBillAmount', 'old_value': 1135374.25, 'new_value': 1197288.11}, {'field': 'amount', 'old_value': 1246962.25, 'new_value': 1314834.34}, {'field': 'count', 'old_value': 7152, 'new_value': 7482}, {'field': 'instoreAmount', 'old_value': 931094.08, 'new_value': 985305.25}, {'field': 'instoreCount', 'old_value': 3787, 'new_value': 3996}, {'field': 'onlineAmount', 'old_value': 325556.9, 'new_value': 339362.62}, {'field': 'onlineCount', 'old_value': 3365, 'new_value': 3486}]
2025-06-01 08:12:00,498 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:00,967 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-06-01 08:12:00,967 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 357565.02, 'new_value': 375575.22000000003}, {'field': 'dailyBillAmount', 'old_value': 357565.02, 'new_value': 375575.22000000003}, {'field': 'amount', 'old_value': 509383.03, 'new_value': 532183.95}, {'field': 'count', 'old_value': 2447, 'new_value': 2540}, {'field': 'instoreAmount', 'old_value': 476612.98, 'new_value': 498790.1}, {'field': 'instoreCount', 'old_value': 1932, 'new_value': 2015}, {'field': 'onlineAmount', 'old_value': 33530.25, 'new_value': 34154.05}, {'field': 'onlineCount', 'old_value': 515, 'new_value': 525}]
2025-06-01 08:12:00,967 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:01,436 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-06-01 08:12:01,436 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 410713.44, 'new_value': 423056.79000000004}, {'field': 'dailyBillAmount', 'old_value': 410713.44, 'new_value': 423056.79000000004}, {'field': 'amount', 'old_value': 395689.9, 'new_value': 421960.9}, {'field': 'count', 'old_value': 1808, 'new_value': 1913}, {'field': 'instoreAmount', 'old_value': 402828.4, 'new_value': 429099.4}, {'field': 'instoreCount', 'old_value': 1808, 'new_value': 1913}]
2025-06-01 08:12:01,436 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:01,936 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-06-01 08:12:01,936 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 895569.12, 'new_value': 944932.69}, {'field': 'amount', 'old_value': 895568.02, 'new_value': 944931.59}, {'field': 'count', 'old_value': 7321, 'new_value': 7622}, {'field': 'instoreAmount', 'old_value': 895569.12, 'new_value': 944932.69}, {'field': 'instoreCount', 'old_value': 7321, 'new_value': 7622}]
2025-06-01 08:12:01,936 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:02,405 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-06-01 08:12:02,405 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 807041.92, 'new_value': 853131.39}, {'field': 'dailyBillAmount', 'old_value': 807041.92, 'new_value': 853131.39}, {'field': 'amount', 'old_value': 941974.08, 'new_value': 989731.2}, {'field': 'count', 'old_value': 6623, 'new_value': 6948}, {'field': 'instoreAmount', 'old_value': 510275.7, 'new_value': 539053.3}, {'field': 'instoreCount', 'old_value': 2703, 'new_value': 2855}, {'field': 'onlineAmount', 'old_value': 444509.0, 'new_value': 464651.1}, {'field': 'onlineCount', 'old_value': 3920, 'new_value': 4093}]
2025-06-01 08:12:02,405 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:02,827 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-06-01 08:12:02,827 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 470680.9, 'new_value': 502198.33}, {'field': 'dailyBillAmount', 'old_value': 470680.9, 'new_value': 502198.33}, {'field': 'amount', 'old_value': 537303.96, 'new_value': 568994.2}, {'field': 'count', 'old_value': 6053, 'new_value': 6307}, {'field': 'instoreAmount', 'old_value': 361168.32, 'new_value': 387514.82}, {'field': 'instoreCount', 'old_value': 2513, 'new_value': 2675}, {'field': 'onlineAmount', 'old_value': 178432.95, 'new_value': 183776.69}, {'field': 'onlineCount', 'old_value': 3540, 'new_value': 3632}]
2025-06-01 08:12:02,827 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:03,311 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-06-01 08:12:03,311 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 595353.23, 'new_value': 631657.62}, {'field': 'dailyBillAmount', 'old_value': 595353.23, 'new_value': 631657.62}, {'field': 'amount', 'old_value': 603496.93, 'new_value': 640400.28}, {'field': 'count', 'old_value': 5841, 'new_value': 6070}, {'field': 'instoreAmount', 'old_value': 523415.95, 'new_value': 557478.04}, {'field': 'instoreCount', 'old_value': 3061, 'new_value': 3224}, {'field': 'onlineAmount', 'old_value': 81256.24, 'new_value': 84116.9}, {'field': 'onlineCount', 'old_value': 2780, 'new_value': 2846}]
2025-06-01 08:12:03,311 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:03,780 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-06-01 08:12:03,780 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137793.8, 'new_value': 143411.8}, {'field': 'amount', 'old_value': 137793.3, 'new_value': 143411.3}, {'field': 'count', 'old_value': 648, 'new_value': 680}, {'field': 'instoreAmount', 'old_value': 137793.8, 'new_value': 143411.8}, {'field': 'instoreCount', 'old_value': 648, 'new_value': 680}]
2025-06-01 08:12:03,780 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:04,280 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-06-01 08:12:04,280 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 394222.45, 'new_value': 401511.05}, {'field': 'dailyBillAmount', 'old_value': 394222.45, 'new_value': 401511.05}, {'field': 'amount', 'old_value': -309040.98, 'new_value': -332934.38}, {'field': 'count', 'old_value': 1066, 'new_value': 1105}, {'field': 'instoreAmount', 'old_value': 7695.0, 'new_value': 8198.6}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 379}, {'field': 'onlineAmount', 'old_value': 21889.87, 'new_value': 22286.87}, {'field': 'onlineCount', 'old_value': 716, 'new_value': 726}]
2025-06-01 08:12:04,280 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:04,748 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-06-01 08:12:04,748 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'amount', 'old_value': 503174.58, 'new_value': 545386.07}, {'field': 'count', 'old_value': 2135, 'new_value': 2277}, {'field': 'instoreAmount', 'old_value': 503174.58, 'new_value': 545386.07}, {'field': 'instoreCount', 'old_value': 2135, 'new_value': 2277}]
2025-06-01 08:12:04,748 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:05,202 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-06-01 08:12:05,202 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 469207.39999999997, 'new_value': 517367.13}, {'field': 'dailyBillAmount', 'old_value': 469207.39999999997, 'new_value': 517367.13}, {'field': 'amount', 'old_value': 187336.4, 'new_value': 211565.0}, {'field': 'count', 'old_value': 760, 'new_value': 835}, {'field': 'instoreAmount', 'old_value': 192792.9, 'new_value': 217500.5}, {'field': 'instoreCount', 'old_value': 733, 'new_value': 804}, {'field': 'onlineAmount', 'old_value': 2158.7, 'new_value': 2406.7}, {'field': 'onlineCount', 'old_value': 27, 'new_value': 31}]
2025-06-01 08:12:05,202 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:05,670 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-06-01 08:12:05,670 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 345275.73, 'new_value': 364380.75}, {'field': 'dailyBillAmount', 'old_value': 345275.73, 'new_value': 364380.75}, {'field': 'amount', 'old_value': 334664.32, 'new_value': 353344.05}, {'field': 'count', 'old_value': 2331, 'new_value': 2450}, {'field': 'instoreAmount', 'old_value': 312327.63, 'new_value': 329739.88}, {'field': 'instoreCount', 'old_value': 1697, 'new_value': 1782}, {'field': 'onlineAmount', 'old_value': 22503.41, 'new_value': 23770.89}, {'field': 'onlineCount', 'old_value': 634, 'new_value': 668}]
2025-06-01 08:12:05,670 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:06,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-06-01 08:12:06,139 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 377741.23, 'new_value': 402276.11}, {'field': 'dailyBillAmount', 'old_value': 377741.23, 'new_value': 402276.11}, {'field': 'amount', 'old_value': 162469.4, 'new_value': 171089.59}, {'field': 'count', 'old_value': 2820, 'new_value': 2927}, {'field': 'instoreAmount', 'old_value': 92468.98, 'new_value': 99200.02}, {'field': 'instoreCount', 'old_value': 749, 'new_value': 791}, {'field': 'onlineAmount', 'old_value': 70003.73, 'new_value': 71892.88}, {'field': 'onlineCount', 'old_value': 2071, 'new_value': 2136}]
2025-06-01 08:12:06,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:06,702 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-06-01 08:12:06,702 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 60391.0, 'new_value': 62377.0}, {'field': 'count', 'old_value': 35, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 60391.0, 'new_value': 62377.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 37}]
2025-06-01 08:12:06,702 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:07,155 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-06-01 08:12:07,155 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165656.95, 'new_value': 172401.08}, {'field': 'amount', 'old_value': 165641.97, 'new_value': 172386.1}, {'field': 'count', 'old_value': 7705, 'new_value': 8021}, {'field': 'instoreAmount', 'old_value': 57286.73, 'new_value': 59656.34}, {'field': 'instoreCount', 'old_value': 2270, 'new_value': 2368}, {'field': 'onlineAmount', 'old_value': 116298.0, 'new_value': 120836.1}, {'field': 'onlineCount', 'old_value': 5435, 'new_value': 5653}]
2025-06-01 08:12:07,155 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:07,670 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-06-01 08:12:07,670 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48474.9, 'new_value': 50948.9}, {'field': 'amount', 'old_value': 48474.9, 'new_value': 50948.9}, {'field': 'count', 'old_value': 216, 'new_value': 227}, {'field': 'instoreAmount', 'old_value': 48474.9, 'new_value': 50948.9}, {'field': 'instoreCount', 'old_value': 216, 'new_value': 227}]
2025-06-01 08:12:07,670 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:08,108 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-06-01 08:12:08,108 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 457753.14, 'new_value': 486756.36}, {'field': 'dailyBillAmount', 'old_value': 457753.14, 'new_value': 486756.36}, {'field': 'amount', 'old_value': 184106.4, 'new_value': 197638.83000000002}, {'field': 'count', 'old_value': 3465, 'new_value': 3689}, {'field': 'instoreAmount', 'old_value': 185479.1, 'new_value': 199214.24}, {'field': 'instoreCount', 'old_value': 3465, 'new_value': 3689}]
2025-06-01 08:12:08,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:08,592 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-06-01 08:12:08,592 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 180255.1, 'new_value': 192168.64}, {'field': 'amount', 'old_value': 180253.9, 'new_value': 192167.44}, {'field': 'count', 'old_value': 4316, 'new_value': 4566}, {'field': 'instoreAmount', 'old_value': 180512.58000000002, 'new_value': 192426.12}, {'field': 'instoreCount', 'old_value': 4316, 'new_value': 4566}]
2025-06-01 08:12:08,592 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:09,030 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-06-01 08:12:09,030 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36309.99, 'new_value': 38205.15}, {'field': 'amount', 'old_value': 36303.27, 'new_value': 38198.43}, {'field': 'count', 'old_value': 2296, 'new_value': 2405}, {'field': 'instoreAmount', 'old_value': 17557.47, 'new_value': 18764.77}, {'field': 'instoreCount', 'old_value': 889, 'new_value': 948}, {'field': 'onlineAmount', 'old_value': 19355.95, 'new_value': 20077.85}, {'field': 'onlineCount', 'old_value': 1407, 'new_value': 1457}]
2025-06-01 08:12:09,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:09,467 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-06-01 08:12:09,467 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66166.3, 'new_value': 69541.7}, {'field': 'amount', 'old_value': 66164.0, 'new_value': 69539.4}, {'field': 'count', 'old_value': 162, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 66166.3, 'new_value': 69541.7}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 175}]
2025-06-01 08:12:09,467 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:09,951 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-06-01 08:12:09,951 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221383.24, 'new_value': 231138.65}, {'field': 'dailyBillAmount', 'old_value': 181613.8, 'new_value': 190498.7}, {'field': 'amount', 'old_value': 221382.56, 'new_value': 231137.97}, {'field': 'count', 'old_value': 3195, 'new_value': 3330}, {'field': 'instoreAmount', 'old_value': 210705.2, 'new_value': 220034.2}, {'field': 'instoreCount', 'old_value': 2760, 'new_value': 2879}, {'field': 'onlineAmount', 'old_value': 10956.44, 'new_value': 11382.85}, {'field': 'onlineCount', 'old_value': 435, 'new_value': 451}]
2025-06-01 08:12:09,951 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:10,436 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-06-01 08:12:10,436 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33866.6, 'new_value': 36425.86}, {'field': 'amount', 'old_value': 33865.8, 'new_value': 36425.06}, {'field': 'count', 'old_value': 1461, 'new_value': 1572}, {'field': 'instoreAmount', 'old_value': 28161.9, 'new_value': 30394.86}, {'field': 'instoreCount', 'old_value': 1295, 'new_value': 1396}, {'field': 'onlineAmount', 'old_value': 5790.4, 'new_value': 6116.7}, {'field': 'onlineCount', 'old_value': 166, 'new_value': 176}]
2025-06-01 08:12:10,436 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:10,905 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-06-01 08:12:10,905 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 448398.43, 'new_value': 469614.33}, {'field': 'dailyBillAmount', 'old_value': 448398.43, 'new_value': 469614.33}, {'field': 'amount', 'old_value': 587559.8, 'new_value': 614301.5}, {'field': 'count', 'old_value': 6126, 'new_value': 6298}, {'field': 'instoreAmount', 'old_value': 551897.05, 'new_value': 577509.1}, {'field': 'instoreCount', 'old_value': 4221, 'new_value': 4344}, {'field': 'onlineAmount', 'old_value': 46275.22, 'new_value': 47404.87}, {'field': 'onlineCount', 'old_value': 1905, 'new_value': 1954}]
2025-06-01 08:12:10,905 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:11,389 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-06-01 08:12:11,389 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'amount', 'old_value': 44233.88, 'new_value': 45884.6}, {'field': 'count', 'old_value': 771, 'new_value': 796}, {'field': 'instoreAmount', 'old_value': 29452.69, 'new_value': 30750.89}, {'field': 'instoreCount', 'old_value': 423, 'new_value': 436}, {'field': 'onlineAmount', 'old_value': 15689.51, 'new_value': 16072.03}, {'field': 'onlineCount', 'old_value': 348, 'new_value': 360}]
2025-06-01 08:12:11,389 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:11,780 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-06-01 08:12:11,780 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 180921.73, 'new_value': 184862.54}, {'field': 'dailyBillAmount', 'old_value': 165998.99, 'new_value': 170044.5}, {'field': 'amount', 'old_value': 180918.94, 'new_value': 184859.75}, {'field': 'count', 'old_value': 10264, 'new_value': 10489}, {'field': 'instoreAmount', 'old_value': 109382.88, 'new_value': 111310.65}, {'field': 'instoreCount', 'old_value': 6045, 'new_value': 6139}, {'field': 'onlineAmount', 'old_value': 73926.81999999999, 'new_value': 76012.18}, {'field': 'onlineCount', 'old_value': 4219, 'new_value': 4350}]
2025-06-01 08:12:11,780 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:12,248 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-06-01 08:12:12,248 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92670.71, 'new_value': 96722.18}, {'field': 'amount', 'old_value': 92657.98, 'new_value': 96709.45}, {'field': 'count', 'old_value': 5913, 'new_value': 6080}, {'field': 'instoreAmount', 'old_value': 41355.24, 'new_value': 43984.04}, {'field': 'instoreCount', 'old_value': 2391, 'new_value': 2475}, {'field': 'onlineAmount', 'old_value': 54189.12, 'new_value': 55633.770000000004}, {'field': 'onlineCount', 'old_value': 3522, 'new_value': 3605}]
2025-06-01 08:12:12,248 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:12,670 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-06-01 08:12:12,670 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16552.0, 'new_value': 22742.0}, {'field': 'dailyBillAmount', 'old_value': 16552.0, 'new_value': 22742.0}, {'field': 'amount', 'old_value': 183191.45, 'new_value': 195863.07}, {'field': 'count', 'old_value': 1902, 'new_value': 1998}, {'field': 'instoreAmount', 'old_value': 183551.73, 'new_value': 196223.35}, {'field': 'instoreCount', 'old_value': 1902, 'new_value': 1998}]
2025-06-01 08:12:12,670 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:13,123 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-06-01 08:12:13,123 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 148702.04, 'new_value': 154235.1}, {'field': 'dailyBillAmount', 'old_value': 154156.66, 'new_value': 160041.13999999998}, {'field': 'amount', 'old_value': 148696.23, 'new_value': 154229.29}, {'field': 'count', 'old_value': 3225, 'new_value': 3357}, {'field': 'instoreAmount', 'old_value': 141072.1, 'new_value': 146331.39}, {'field': 'instoreCount', 'old_value': 2623, 'new_value': 2730}, {'field': 'onlineAmount', 'old_value': 7751.76, 'new_value': 8025.53}, {'field': 'onlineCount', 'old_value': 602, 'new_value': 627}]
2025-06-01 08:12:13,123 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-01 08:12:13,576 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M0B
2025-06-01 08:12:13,576 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 925540.49, 'new_value': 915710.37}, {'field': 'dailyBillAmount', 'old_value': 925540.49, 'new_value': 915710.37}]
2025-06-01 08:12:13,576 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:14,092 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-06-01 08:12:14,092 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 259219.1, 'new_value': 264132.56}, {'field': 'dailyBillAmount', 'old_value': 259219.1, 'new_value': 264132.56}, {'field': 'amount', 'old_value': 33604.66, 'new_value': 34591.85}, {'field': 'count', 'old_value': 1270, 'new_value': 1297}, {'field': 'instoreAmount', 'old_value': 37912.35, 'new_value': 38899.54}, {'field': 'instoreCount', 'old_value': 1270, 'new_value': 1297}]
2025-06-01 08:12:14,108 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:14,608 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-06-01 08:12:14,608 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 638271.84, 'new_value': 669585.64}, {'field': 'dailyBillAmount', 'old_value': 638271.84, 'new_value': 669585.64}, {'field': 'amount', 'old_value': 66928.96, 'new_value': 69976.96}, {'field': 'count', 'old_value': 326, 'new_value': 344}, {'field': 'instoreAmount', 'old_value': 67424.66, 'new_value': 70472.66}, {'field': 'instoreCount', 'old_value': 326, 'new_value': 344}]
2025-06-01 08:12:14,608 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:14,998 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-06-01 08:12:14,998 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 20720.93, 'new_value': 21051.87}, {'field': 'count', 'old_value': 1070, 'new_value': 1084}, {'field': 'onlineAmount', 'old_value': 20959.59, 'new_value': 21290.53}, {'field': 'onlineCount', 'old_value': 1070, 'new_value': 1084}]
2025-06-01 08:12:15,014 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:15,451 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-06-01 08:12:15,467 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 385835.97, 'new_value': 397282.35}, {'field': 'amount', 'old_value': 385682.19, 'new_value': 397128.57}, {'field': 'count', 'old_value': 4020, 'new_value': 4109}, {'field': 'instoreAmount', 'old_value': 366702.2, 'new_value': 378604.2}, {'field': 'instoreCount', 'old_value': 3380, 'new_value': 3459}, {'field': 'onlineAmount', 'old_value': 25430.86, 'new_value': 25674.010000000002}, {'field': 'onlineCount', 'old_value': 640, 'new_value': 650}]
2025-06-01 08:12:15,467 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:15,936 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-06-01 08:12:15,936 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 217129.68, 'new_value': 225700.85}, {'field': 'dailyBillAmount', 'old_value': 213329.21, 'new_value': 221900.38}, {'field': 'amount', 'old_value': 160890.55, 'new_value': 167110.49}, {'field': 'count', 'old_value': 5740, 'new_value': 5979}, {'field': 'instoreAmount', 'old_value': 62386.700000000004, 'new_value': 64296.29}, {'field': 'instoreCount', 'old_value': 2076, 'new_value': 2128}, {'field': 'onlineAmount', 'old_value': 100517.45, 'new_value': 104990.6}, {'field': 'onlineCount', 'old_value': 3664, 'new_value': 3851}]
2025-06-01 08:12:15,936 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:16,420 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-06-01 08:12:16,420 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 7277.15, 'new_value': 7326.42}, {'field': 'count', 'old_value': 330, 'new_value': 332}, {'field': 'instoreAmount', 'old_value': 7277.89, 'new_value': 7327.16}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 332}]
2025-06-01 08:12:16,420 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:16,858 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-06-01 08:12:16,858 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 7540.2699999999995, 'new_value': 7789.9}, {'field': 'count', 'old_value': 325, 'new_value': 332}, {'field': 'onlineAmount', 'old_value': 7540.2699999999995, 'new_value': 7789.9}, {'field': 'onlineCount', 'old_value': 325, 'new_value': 332}]
2025-06-01 08:12:16,858 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:17,295 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-06-01 08:12:17,295 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 120675.83, 'new_value': 126510.14}, {'field': 'dailyBillAmount', 'old_value': 57877.340000000004, 'new_value': 61749.340000000004}, {'field': 'amount', 'old_value': 120675.24, 'new_value': 126509.55}, {'field': 'count', 'old_value': 3035, 'new_value': 3177}, {'field': 'instoreAmount', 'old_value': 62881.04, 'new_value': 67173.97}, {'field': 'instoreCount', 'old_value': 1571, 'new_value': 1676}, {'field': 'onlineAmount', 'old_value': 60894.72, 'new_value': 62573.1}, {'field': 'onlineCount', 'old_value': 1464, 'new_value': 1501}]
2025-06-01 08:12:17,295 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:17,748 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-06-01 08:12:17,748 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77536.15, 'new_value': 82508.99}, {'field': 'amount', 'old_value': 77536.15, 'new_value': 82508.99}, {'field': 'count', 'old_value': 3224, 'new_value': 3441}, {'field': 'instoreAmount', 'old_value': 78771.24, 'new_value': 83854.5}, {'field': 'instoreCount', 'old_value': 3224, 'new_value': 3441}]
2025-06-01 08:12:17,748 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:18,264 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-06-01 08:12:18,264 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64053.8, 'new_value': 66906.76}, {'field': 'dailyBillAmount', 'old_value': 64053.8, 'new_value': 66906.76}, {'field': 'amount', 'old_value': 48064.32, 'new_value': 49811.64}, {'field': 'count', 'old_value': 2329, 'new_value': 2413}, {'field': 'instoreAmount', 'old_value': 22882.63, 'new_value': 23530.13}, {'field': 'instoreCount', 'old_value': 788, 'new_value': 810}, {'field': 'onlineAmount', 'old_value': 25305.82, 'new_value': 26405.64}, {'field': 'onlineCount', 'old_value': 1541, 'new_value': 1603}]
2025-06-01 08:12:18,264 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:18,733 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-06-01 08:12:18,733 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95984.6, 'new_value': 98779.28}, {'field': 'amount', 'old_value': 95984.6, 'new_value': 98779.28}, {'field': 'count', 'old_value': 2902, 'new_value': 2980}, {'field': 'instoreAmount', 'old_value': 39651.75, 'new_value': 41222.65}, {'field': 'instoreCount', 'old_value': 1498, 'new_value': 1547}, {'field': 'onlineAmount', 'old_value': 56434.34, 'new_value': 57658.12}, {'field': 'onlineCount', 'old_value': 1404, 'new_value': 1433}]
2025-06-01 08:12:18,733 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:19,233 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-06-01 08:12:19,233 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57957.46, 'new_value': 59479.090000000004}, {'field': 'amount', 'old_value': 57956.56, 'new_value': 59478.19}, {'field': 'count', 'old_value': 1413, 'new_value': 1454}, {'field': 'instoreAmount', 'old_value': 44660.02, 'new_value': 45884.42}, {'field': 'instoreCount', 'old_value': 1127, 'new_value': 1159}, {'field': 'onlineAmount', 'old_value': 13815.45, 'new_value': 14147.68}, {'field': 'onlineCount', 'old_value': 286, 'new_value': 295}]
2025-06-01 08:12:19,233 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:19,670 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-06-01 08:12:19,670 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 278962.08, 'new_value': 287287.33}, {'field': 'dailyBillAmount', 'old_value': 278962.08, 'new_value': 287287.33}, {'field': 'amount', 'old_value': 187833.28, 'new_value': 193723.03}, {'field': 'count', 'old_value': 4739, 'new_value': 4876}, {'field': 'instoreAmount', 'old_value': 119043.56, 'new_value': 122791.06}, {'field': 'instoreCount', 'old_value': 2356, 'new_value': 2424}, {'field': 'onlineAmount', 'old_value': 85345.87, 'new_value': 88004.12}, {'field': 'onlineCount', 'old_value': 2383, 'new_value': 2452}]
2025-06-01 08:12:19,670 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:20,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-06-01 08:12:20,139 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 804171.31, 'new_value': 860165.94}, {'field': 'dailyBillAmount', 'old_value': 804171.31, 'new_value': 860165.94}, {'field': 'amount', 'old_value': 768030.5, 'new_value': 811634.8}, {'field': 'count', 'old_value': 4640, 'new_value': 4926}, {'field': 'instoreAmount', 'old_value': 537276.5, 'new_value': 572608.1}, {'field': 'instoreCount', 'old_value': 3559, 'new_value': 3804}, {'field': 'onlineAmount', 'old_value': 230756.9, 'new_value': 239029.6}, {'field': 'onlineCount', 'old_value': 1081, 'new_value': 1122}]
2025-06-01 08:12:20,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:20,545 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-06-01 08:12:20,545 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1251471.61, 'new_value': 1327953.46}, {'field': 'amount', 'old_value': 1251471.11, 'new_value': 1327952.96}, {'field': 'count', 'old_value': 4363, 'new_value': 4610}, {'field': 'instoreAmount', 'old_value': 1251471.61, 'new_value': 1327953.46}, {'field': 'instoreCount', 'old_value': 4363, 'new_value': 4610}]
2025-06-01 08:12:20,545 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:21,030 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-06-01 08:12:21,030 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 716886.53, 'new_value': 756119.09}, {'field': 'dailyBillAmount', 'old_value': 633894.42, 'new_value': 671421.26}, {'field': 'amount', 'old_value': 716886.53, 'new_value': 756119.09}, {'field': 'count', 'old_value': 4523, 'new_value': 4699}, {'field': 'instoreAmount', 'old_value': 652471.53, 'new_value': 690753.51}, {'field': 'instoreCount', 'old_value': 2777, 'new_value': 2930}, {'field': 'onlineAmount', 'old_value': 64838.65, 'new_value': 65789.23}, {'field': 'onlineCount', 'old_value': 1746, 'new_value': 1769}]
2025-06-01 08:12:21,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:21,514 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-06-01 08:12:21,514 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 667882.66, 'new_value': 689128.61}, {'field': 'dailyBillAmount', 'old_value': 650336.83, 'new_value': 671402.78}, {'field': 'amount', 'old_value': 667876.07, 'new_value': 689122.02}, {'field': 'count', 'old_value': 1691, 'new_value': 1751}, {'field': 'instoreAmount', 'old_value': 618854.4, 'new_value': 638426.4}, {'field': 'instoreCount', 'old_value': 1309, 'new_value': 1356}, {'field': 'onlineAmount', 'old_value': 49204.46, 'new_value': 50878.41}, {'field': 'onlineCount', 'old_value': 382, 'new_value': 395}]
2025-06-01 08:12:21,514 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:22,030 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-06-01 08:12:22,030 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 865478.35, 'new_value': 924152.64}, {'field': 'amount', 'old_value': 865477.03, 'new_value': 924151.32}, {'field': 'count', 'old_value': 4781, 'new_value': 5011}, {'field': 'instoreAmount', 'old_value': 806461.94, 'new_value': 862075.36}, {'field': 'instoreCount', 'old_value': 2978, 'new_value': 3166}, {'field': 'onlineAmount', 'old_value': 59161.21, 'new_value': 62247.79}, {'field': 'onlineCount', 'old_value': 1803, 'new_value': 1845}]
2025-06-01 08:12:22,030 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:22,498 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-06-01 08:12:22,498 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 946491.08, 'new_value': 980259.83}, {'field': 'dailyBillAmount', 'old_value': 946491.08, 'new_value': 980259.83}, {'field': 'amount', 'old_value': 889446.14, 'new_value': 934889.41}, {'field': 'count', 'old_value': 4408, 'new_value': 4617}, {'field': 'instoreAmount', 'old_value': 816846.27, 'new_value': 857704.45}, {'field': 'instoreCount', 'old_value': 3636, 'new_value': 3814}, {'field': 'onlineAmount', 'old_value': 73706.31, 'new_value': 78621.77}, {'field': 'onlineCount', 'old_value': 772, 'new_value': 803}]
2025-06-01 08:12:22,498 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:22,951 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-06-01 08:12:22,951 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 241537.08, 'new_value': 247733.08}, {'field': 'dailyBillAmount', 'old_value': 240130.53, 'new_value': 246326.53}, {'field': 'amount', 'old_value': 236494.9, 'new_value': 242690.9}, {'field': 'count', 'old_value': 345, 'new_value': 355}, {'field': 'instoreAmount', 'old_value': 236494.9, 'new_value': 242690.9}, {'field': 'instoreCount', 'old_value': 345, 'new_value': 355}]
2025-06-01 08:12:22,951 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:23,405 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-06-01 08:12:23,405 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 191215.73, 'new_value': 196604.93}, {'field': 'dailyBillAmount', 'old_value': 191215.73, 'new_value': 196604.93}, {'field': 'amount', 'old_value': 170699.95, 'new_value': 176357.75}, {'field': 'count', 'old_value': 277, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 167757.9, 'new_value': 173415.7}, {'field': 'instoreCount', 'old_value': 257, 'new_value': 265}]
2025-06-01 08:12:23,405 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:24,014 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-06-01 08:12:24,014 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25064.56, 'new_value': 27103.61}, {'field': 'amount', 'old_value': 25064.56, 'new_value': 27103.61}, {'field': 'count', 'old_value': 525, 'new_value': 568}, {'field': 'instoreAmount', 'old_value': 25064.56, 'new_value': 27103.61}, {'field': 'instoreCount', 'old_value': 525, 'new_value': 568}]
2025-06-01 08:12:24,014 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:24,467 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-06-01 08:12:24,467 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 110216.54, 'new_value': 113388.26}, {'field': 'amount', 'old_value': 110216.54, 'new_value': 113388.26}, {'field': 'count', 'old_value': 943, 'new_value': 967}, {'field': 'instoreAmount', 'old_value': 110767.38, 'new_value': 113939.1}, {'field': 'instoreCount', 'old_value': 943, 'new_value': 967}]
2025-06-01 08:12:24,467 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:24,951 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-06-01 08:12:24,951 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 381809.84, 'new_value': 399307.48}, {'field': 'dailyBillAmount', 'old_value': 381809.84, 'new_value': 399307.48}, {'field': 'amount', 'old_value': 402045.7, 'new_value': 422561.68}, {'field': 'count', 'old_value': 11122, 'new_value': 11600}, {'field': 'instoreAmount', 'old_value': 377511.93, 'new_value': 397564.2}, {'field': 'instoreCount', 'old_value': 9888, 'new_value': 10336}, {'field': 'onlineAmount', 'old_value': 30376.01, 'new_value': 30923.42}, {'field': 'onlineCount', 'old_value': 1234, 'new_value': 1264}]
2025-06-01 08:12:24,951 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:25,342 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-06-01 08:12:25,342 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 938253.28, 'new_value': 968466.64}, {'field': 'dailyBillAmount', 'old_value': 938253.28, 'new_value': 968466.64}, {'field': 'amount', 'old_value': 845183.94, 'new_value': 872814.49}, {'field': 'count', 'old_value': 2306, 'new_value': 2371}, {'field': 'instoreAmount', 'old_value': 883629.48, 'new_value': 911068.0599999999}, {'field': 'instoreCount', 'old_value': 1892, 'new_value': 1952}, {'field': 'onlineAmount', 'old_value': 9867.73, 'new_value': 10059.7}, {'field': 'onlineCount', 'old_value': 414, 'new_value': 419}]
2025-06-01 08:12:25,342 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:25,779 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-06-01 08:12:25,779 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1470846.5, 'new_value': 1538764.13}, {'field': 'amount', 'old_value': 1470846.1, 'new_value': 1538763.73}, {'field': 'count', 'old_value': 4729, 'new_value': 4947}, {'field': 'instoreAmount', 'old_value': 1472057.5, 'new_value': 1539975.13}, {'field': 'instoreCount', 'old_value': 4729, 'new_value': 4947}]
2025-06-01 08:12:25,779 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:26,186 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-06-01 08:12:26,201 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1202933.02, 'new_value': 1244693.29}, {'field': 'dailyBillAmount', 'old_value': 1202933.02, 'new_value': 1244693.29}, {'field': 'amount', 'old_value': 956891.2, 'new_value': 989382.59}, {'field': 'count', 'old_value': 3377, 'new_value': 3470}, {'field': 'instoreAmount', 'old_value': 933114.05, 'new_value': 965310.54}, {'field': 'instoreCount', 'old_value': 2095, 'new_value': 2166}, {'field': 'onlineAmount', 'old_value': 38990.42, 'new_value': 39600.22}, {'field': 'onlineCount', 'old_value': 1282, 'new_value': 1304}]
2025-06-01 08:12:26,201 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:26,686 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-06-01 08:12:26,686 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 2172737.95, 'new_value': 2260181.94}, {'field': 'dailyBillAmount', 'old_value': 2172737.95, 'new_value': 2260181.94}, {'field': 'amount', 'old_value': 2180866.0, 'new_value': 2257165.0}, {'field': 'count', 'old_value': 5610, 'new_value': 5775}, {'field': 'instoreAmount', 'old_value': 2198821.0, 'new_value': 2276451.0}, {'field': 'instoreCount', 'old_value': 5610, 'new_value': 5775}]
2025-06-01 08:12:26,686 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:27,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-06-01 08:12:27,139 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 346240.7, 'new_value': 358122.9}, {'field': 'dailyBillAmount', 'old_value': 346240.7, 'new_value': 358122.9}, {'field': 'amount', 'old_value': 350196.47000000003, 'new_value': 360297.67}, {'field': 'count', 'old_value': 1907, 'new_value': 1962}, {'field': 'instoreAmount', 'old_value': 339415.3, 'new_value': 350847.3}, {'field': 'instoreCount', 'old_value': 1611, 'new_value': 1658}, {'field': 'onlineAmount', 'old_value': 18122.87, 'new_value': 18594.07}, {'field': 'onlineCount', 'old_value': 296, 'new_value': 304}]
2025-06-01 08:12:27,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:27,608 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-06-01 08:12:27,608 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1139655.42, 'new_value': 1200859.15}, {'field': 'dailyBillAmount', 'old_value': 1139655.42, 'new_value': 1200859.15}, {'field': 'amount', 'old_value': 1227689.8800000001, 'new_value': 1288893.61}, {'field': 'count', 'old_value': 5170, 'new_value': 5359}, {'field': 'instoreAmount', 'old_value': 1227690.53, 'new_value': 1288894.26}, {'field': 'instoreCount', 'old_value': 5170, 'new_value': 5359}]
2025-06-01 08:12:27,608 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:28,014 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-06-01 08:12:28,014 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 508368.83, 'new_value': 526330.17}, {'field': 'dailyBillAmount', 'old_value': 508368.83, 'new_value': 526330.17}, {'field': 'amount', 'old_value': 830907.4, 'new_value': 857792.9}, {'field': 'count', 'old_value': 1435, 'new_value': 1475}, {'field': 'instoreAmount', 'old_value': 825446.4, 'new_value': 852225.4}, {'field': 'instoreCount', 'old_value': 1395, 'new_value': 1434}, {'field': 'onlineAmount', 'old_value': 5794.7, 'new_value': 5901.2}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 41}]
2025-06-01 08:12:28,014 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:28,404 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-06-01 08:12:28,404 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 293833.44, 'new_value': 304902.79}, {'field': 'dailyBillAmount', 'old_value': 293833.44, 'new_value': 304902.79}, {'field': 'amount', 'old_value': 328898.3, 'new_value': 341171.2}, {'field': 'count', 'old_value': 2351, 'new_value': 2448}, {'field': 'instoreAmount', 'old_value': 333096.3, 'new_value': 345369.2}, {'field': 'instoreCount', 'old_value': 2351, 'new_value': 2448}]
2025-06-01 08:12:28,404 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:28,826 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-06-01 08:12:28,826 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 215734.84, 'new_value': 229320.55}, {'field': 'dailyBillAmount', 'old_value': 215734.84, 'new_value': 229320.55}, {'field': 'amount', 'old_value': 180285.66999999998, 'new_value': 192484.16999999998}, {'field': 'count', 'old_value': 1211, 'new_value': 1279}, {'field': 'instoreAmount', 'old_value': 181227.0, 'new_value': 194067.0}, {'field': 'instoreCount', 'old_value': 1146, 'new_value': 1208}, {'field': 'onlineAmount', 'old_value': 3065.67, 'new_value': 3296.17}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 71}]
2025-06-01 08:12:28,842 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:29,311 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-06-01 08:12:29,311 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63743.6, 'new_value': 64797.8}, {'field': 'dailyBillAmount', 'old_value': 63743.6, 'new_value': 64797.8}]
2025-06-01 08:12:29,311 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:29,764 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-06-01 08:12:29,764 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 173478.14, 'new_value': 177498.35}, {'field': 'count', 'old_value': 8388, 'new_value': 8592}, {'field': 'instoreAmount', 'old_value': 87394.81, 'new_value': 88024.39}, {'field': 'instoreCount', 'old_value': 4442, 'new_value': 4491}, {'field': 'onlineAmount', 'old_value': 90793.26, 'new_value': 94355.96}, {'field': 'onlineCount', 'old_value': 3946, 'new_value': 4101}]
2025-06-01 08:12:29,764 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:30,279 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-06-01 08:12:30,279 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 271917.5, 'new_value': 282287.15}, {'field': 'amount', 'old_value': 271905.48, 'new_value': 282275.13}, {'field': 'count', 'old_value': 5060, 'new_value': 5196}, {'field': 'instoreAmount', 'old_value': 243631.63, 'new_value': 252483.98}, {'field': 'instoreCount', 'old_value': 4502, 'new_value': 4607}, {'field': 'onlineAmount', 'old_value': 28285.87, 'new_value': 29803.17}, {'field': 'onlineCount', 'old_value': 558, 'new_value': 589}]
2025-06-01 08:12:30,279 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:30,748 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-06-01 08:12:30,748 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41801.3, 'new_value': 43711.1}, {'field': 'dailyBillAmount', 'old_value': 3325.6, 'new_value': 3361.6}, {'field': 'amount', 'old_value': 41801.3, 'new_value': 43711.1}, {'field': 'count', 'old_value': 274, 'new_value': 289}, {'field': 'instoreAmount', 'old_value': 41801.3, 'new_value': 43711.1}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 289}]
2025-06-01 08:12:30,748 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:31,248 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-06-01 08:12:31,248 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 55495.9, 'new_value': 55808.700000000004}, {'field': 'count', 'old_value': 499, 'new_value': 504}, {'field': 'instoreAmount', 'old_value': 55843.3, 'new_value': 56156.1}, {'field': 'instoreCount', 'old_value': 499, 'new_value': 504}]
2025-06-01 08:12:31,248 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:31,748 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-06-01 08:12:31,748 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66348.0, 'new_value': 67348.0}, {'field': 'dailyBillAmount', 'old_value': 66348.0, 'new_value': 67348.0}]
2025-06-01 08:12:31,748 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:32,217 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-06-01 08:12:32,217 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 372104.5, 'new_value': 396670.9}, {'field': 'dailyBillAmount', 'old_value': 372104.5, 'new_value': 396670.9}, {'field': 'amount', 'old_value': 270465.76, 'new_value': 289874.36}, {'field': 'count', 'old_value': 6302, 'new_value': 6589}, {'field': 'instoreAmount', 'old_value': 265190.25, 'new_value': 283985.55}, {'field': 'instoreCount', 'old_value': 6076, 'new_value': 6339}, {'field': 'onlineAmount', 'old_value': 10425.58, 'new_value': 11489.88}, {'field': 'onlineCount', 'old_value': 226, 'new_value': 250}]
2025-06-01 08:12:32,217 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:32,670 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-06-01 08:12:32,670 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59064.5, 'new_value': 60614.3}, {'field': 'dailyBillAmount', 'old_value': 59064.5, 'new_value': 60614.3}, {'field': 'amount', 'old_value': 59039.0, 'new_value': 60588.8}, {'field': 'count', 'old_value': 350, 'new_value': 361}, {'field': 'instoreAmount', 'old_value': 61908.8, 'new_value': 63458.6}, {'field': 'instoreCount', 'old_value': 346, 'new_value': 357}]
2025-06-01 08:12:32,670 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:33,154 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-06-01 08:12:33,154 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60189.22, 'new_value': 61830.01}, {'field': 'amount', 'old_value': 60188.32, 'new_value': 61829.11}, {'field': 'count', 'old_value': 3538, 'new_value': 3656}, {'field': 'instoreAmount', 'old_value': 61169.85, 'new_value': 62855.14}, {'field': 'instoreCount', 'old_value': 3538, 'new_value': 3656}]
2025-06-01 08:12:33,154 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:33,608 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-06-01 08:12:33,608 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 92245.67, 'new_value': 94454.62}, {'field': 'dailyBillAmount', 'old_value': 92245.67, 'new_value': 94454.62}, {'field': 'amount', 'old_value': 95063.08, 'new_value': 97304.43}, {'field': 'count', 'old_value': 4733, 'new_value': 4834}, {'field': 'instoreAmount', 'old_value': 88348.1, 'new_value': 90304.4}, {'field': 'instoreCount', 'old_value': 4428, 'new_value': 4514}, {'field': 'onlineAmount', 'old_value': 6867.24, 'new_value': 7152.29}, {'field': 'onlineCount', 'old_value': 305, 'new_value': 320}]
2025-06-01 08:12:33,608 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:34,139 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-06-01 08:12:34,139 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 63721.159999999996, 'new_value': 66145.25}, {'field': 'amount', 'old_value': 63721.159999999996, 'new_value': 66145.25}, {'field': 'count', 'old_value': 3109, 'new_value': 3214}, {'field': 'instoreAmount', 'old_value': 39109.24, 'new_value': 40058.1}, {'field': 'instoreCount', 'old_value': 2033, 'new_value': 2076}, {'field': 'onlineAmount', 'old_value': 24752.93, 'new_value': 26228.16}, {'field': 'onlineCount', 'old_value': 1076, 'new_value': 1138}]
2025-06-01 08:12:34,139 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:34,889 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-06-01 08:12:34,889 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46529.42, 'new_value': 47613.45}, {'field': 'dailyBillAmount', 'old_value': 46529.42, 'new_value': 47613.45}, {'field': 'amount', 'old_value': 32428.25, 'new_value': 33105.01}, {'field': 'count', 'old_value': 1307, 'new_value': 1329}, {'field': 'instoreAmount', 'old_value': 32714.25, 'new_value': 33391.01}, {'field': 'instoreCount', 'old_value': 1307, 'new_value': 1329}]
2025-06-01 08:12:34,889 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:35,342 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-06-01 08:12:35,358 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82354.59, 'new_value': 86225.18}, {'field': 'amount', 'old_value': 82344.14, 'new_value': 86214.73}, {'field': 'count', 'old_value': 4848, 'new_value': 5055}, {'field': 'instoreAmount', 'old_value': 20737.31, 'new_value': 21645.49}, {'field': 'instoreCount', 'old_value': 1168, 'new_value': 1211}, {'field': 'onlineAmount', 'old_value': 63928.71, 'new_value': 67036.02}, {'field': 'onlineCount', 'old_value': 3680, 'new_value': 3844}]
2025-06-01 08:12:35,358 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:35,826 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-06-01 08:12:35,826 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162265.9, 'new_value': 165886.4}, {'field': 'dailyBillAmount', 'old_value': 162265.9, 'new_value': 165886.4}, {'field': 'amount', 'old_value': 134158.78, 'new_value': 136724.28}, {'field': 'count', 'old_value': 1298, 'new_value': 1322}, {'field': 'instoreAmount', 'old_value': 134282.78, 'new_value': 136848.28}, {'field': 'instoreCount', 'old_value': 1298, 'new_value': 1322}]
2025-06-01 08:12:35,826 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:36,295 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-06-01 08:12:36,295 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 123015.25, 'new_value': 130806.08}, {'field': 'dailyBillAmount', 'old_value': 123015.25, 'new_value': 130806.08}, {'field': 'amount', 'old_value': 142877.8, 'new_value': 151730.8}, {'field': 'count', 'old_value': 602, 'new_value': 637}, {'field': 'instoreAmount', 'old_value': 142877.8, 'new_value': 151730.8}, {'field': 'instoreCount', 'old_value': 602, 'new_value': 637}]
2025-06-01 08:12:36,295 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:36,779 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-06-01 08:12:36,779 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 75517.6, 'new_value': 80926.1}, {'field': 'dailyBillAmount', 'old_value': 75517.6, 'new_value': 80926.1}, {'field': 'amount', 'old_value': 64831.49, 'new_value': 68304.95}, {'field': 'count', 'old_value': 350, 'new_value': 365}, {'field': 'instoreAmount', 'old_value': 66268.49, 'new_value': 69741.95}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 365}]
2025-06-01 08:12:36,779 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:37,311 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-06-01 08:12:37,311 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156100.0, 'new_value': 162277.0}, {'field': 'amount', 'old_value': 156100.0, 'new_value': 162277.0}, {'field': 'count', 'old_value': 1598, 'new_value': 1643}, {'field': 'instoreAmount', 'old_value': 156100.0, 'new_value': 162277.0}, {'field': 'instoreCount', 'old_value': 1598, 'new_value': 1643}]
2025-06-01 08:12:37,311 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:37,873 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-06-01 08:12:37,873 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40659.94, 'new_value': 40946.24}, {'field': 'dailyBillAmount', 'old_value': 40659.94, 'new_value': 40946.24}, {'field': 'amount', 'old_value': 6122.76, 'new_value': 6113.76}]
2025-06-01 08:12:37,873 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:38,358 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-06-01 08:12:38,358 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'amount', 'old_value': 78148.5, 'new_value': 82156.5}, {'field': 'count', 'old_value': 317, 'new_value': 333}, {'field': 'instoreAmount', 'old_value': 78337.5, 'new_value': 82621.5}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 332}]
2025-06-01 08:12:38,358 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:38,795 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-06-01 08:12:38,795 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52472.0, 'new_value': 54913.0}, {'field': 'dailyBillAmount', 'old_value': 52472.0, 'new_value': 54913.0}, {'field': 'amount', 'old_value': 55474.0, 'new_value': 57865.0}, {'field': 'count', 'old_value': 304, 'new_value': 317}, {'field': 'instoreAmount', 'old_value': 55488.0, 'new_value': 57879.0}, {'field': 'instoreCount', 'old_value': 304, 'new_value': 317}]
2025-06-01 08:12:38,795 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:39,326 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-06-01 08:12:39,326 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88270.7, 'new_value': 90341.57}, {'field': 'dailyBillAmount', 'old_value': 88270.7, 'new_value': 90341.57}, {'field': 'amount', 'old_value': 79213.22, 'new_value': 80947.55}, {'field': 'count', 'old_value': 2690, 'new_value': 2743}, {'field': 'instoreAmount', 'old_value': 72268.95, 'new_value': 73653.88}, {'field': 'instoreCount', 'old_value': 2352, 'new_value': 2391}, {'field': 'onlineAmount', 'old_value': 6980.71, 'new_value': 7330.11}, {'field': 'onlineCount', 'old_value': 338, 'new_value': 352}]
2025-06-01 08:12:39,326 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:39,733 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-06-01 08:12:39,733 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64236.17, 'new_value': 67143.54}, {'field': 'dailyBillAmount', 'old_value': 64236.17, 'new_value': 67143.54}, {'field': 'amount', 'old_value': 69098.52, 'new_value': 71788.32}, {'field': 'count', 'old_value': 422, 'new_value': 438}, {'field': 'instoreAmount', 'old_value': 69043.31, 'new_value': 71699.31}, {'field': 'instoreCount', 'old_value': 343, 'new_value': 358}, {'field': 'onlineAmount', 'old_value': 1768.11, 'new_value': 1801.91}, {'field': 'onlineCount', 'old_value': 79, 'new_value': 80}]
2025-06-01 08:12:39,733 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-01 08:12:40,170 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-06-01 08:12:40,170 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 250535.71, 'new_value': 261079.42}, {'field': 'dailyBillAmount', 'old_value': 250535.71, 'new_value': 261079.42}, {'field': 'amount', 'old_value': 258813.5, 'new_value': 278687.1}, {'field': 'count', 'old_value': 1700, 'new_value': 1763}, {'field': 'instoreAmount', 'old_value': 250817.7, 'new_value': 270079.7}, {'field': 'instoreCount', 'old_value': 1524, 'new_value': 1578}, {'field': 'onlineAmount', 'old_value': 12430.6, 'new_value': 13042.2}, {'field': 'onlineCount', 'old_value': 176, 'new_value': 185}]
2025-06-01 08:12:40,170 - INFO - 月销售数据同步完成！更新: 209 条，插入: 0 条，错误: 0 条，跳过: 983 条
2025-06-01 08:12:40,170 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-7 至 2025-6
2025-06-01 08:12:40,654 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250601.xlsx
2025-06-01 08:12:40,654 - INFO - 综合数据同步流程完成！
2025-06-01 08:12:40,748 - INFO - 综合数据同步完成
2025-06-01 08:12:40,748 - INFO - ==================================================
2025-06-01 08:12:40,748 - INFO - 程序退出
2025-06-01 08:12:40,748 - INFO - ==================================================
