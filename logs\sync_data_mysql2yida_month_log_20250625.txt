2025-06-25 00:00:03,347 - INFO - =================使用默认全量同步=============
2025-06-25 00:00:05,186 - INFO - MySQL查询成功，共获取 3959 条记录
2025-06-25 00:00:05,187 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 00:00:05,220 - INFO - 开始处理日期: 2025-01
2025-06-25 00:00:05,223 - INFO - Request Parameters - Page 1:
2025-06-25 00:00:05,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:05,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:06,633 - INFO - Response - Page 1:
2025-06-25 00:00:06,833 - INFO - 第 1 页获取到 100 条记录
2025-06-25 00:00:06,833 - INFO - Request Parameters - Page 2:
2025-06-25 00:00:06,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:06,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:07,308 - INFO - Response - Page 2:
2025-06-25 00:00:07,508 - INFO - 第 2 页获取到 100 条记录
2025-06-25 00:00:07,508 - INFO - Request Parameters - Page 3:
2025-06-25 00:00:07,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:07,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:08,026 - INFO - Response - Page 3:
2025-06-25 00:00:08,226 - INFO - 第 3 页获取到 100 条记录
2025-06-25 00:00:08,226 - INFO - Request Parameters - Page 4:
2025-06-25 00:00:08,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:08,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:08,735 - INFO - Response - Page 4:
2025-06-25 00:00:08,935 - INFO - 第 4 页获取到 100 条记录
2025-06-25 00:00:08,935 - INFO - Request Parameters - Page 5:
2025-06-25 00:00:08,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:08,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:09,429 - INFO - Response - Page 5:
2025-06-25 00:00:09,638 - INFO - 第 5 页获取到 100 条记录
2025-06-25 00:00:09,638 - INFO - Request Parameters - Page 6:
2025-06-25 00:00:09,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:09,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:10,202 - INFO - Response - Page 6:
2025-06-25 00:00:10,402 - INFO - 第 6 页获取到 100 条记录
2025-06-25 00:00:10,402 - INFO - Request Parameters - Page 7:
2025-06-25 00:00:10,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:10,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:10,851 - INFO - Response - Page 7:
2025-06-25 00:00:11,055 - INFO - 第 7 页获取到 82 条记录
2025-06-25 00:00:11,055 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 00:00:11,055 - INFO - 获取到 682 条表单数据
2025-06-25 00:00:11,055 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 00:00:11,070 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:11,070 - INFO - 开始处理日期: 2025-02
2025-06-25 00:00:11,070 - INFO - Request Parameters - Page 1:
2025-06-25 00:00:11,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:11,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:11,539 - INFO - Response - Page 1:
2025-06-25 00:00:11,742 - INFO - 第 1 页获取到 100 条记录
2025-06-25 00:00:11,742 - INFO - Request Parameters - Page 2:
2025-06-25 00:00:11,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:11,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:12,197 - INFO - Response - Page 2:
2025-06-25 00:00:12,398 - INFO - 第 2 页获取到 100 条记录
2025-06-25 00:00:12,398 - INFO - Request Parameters - Page 3:
2025-06-25 00:00:12,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:12,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:13,087 - INFO - Response - Page 3:
2025-06-25 00:00:13,290 - INFO - 第 3 页获取到 100 条记录
2025-06-25 00:00:13,290 - INFO - Request Parameters - Page 4:
2025-06-25 00:00:13,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:13,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:13,756 - INFO - Response - Page 4:
2025-06-25 00:00:13,956 - INFO - 第 4 页获取到 100 条记录
2025-06-25 00:00:13,956 - INFO - Request Parameters - Page 5:
2025-06-25 00:00:13,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:13,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:14,504 - INFO - Response - Page 5:
2025-06-25 00:00:14,708 - INFO - 第 5 页获取到 100 条记录
2025-06-25 00:00:14,708 - INFO - Request Parameters - Page 6:
2025-06-25 00:00:14,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:14,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:15,176 - INFO - Response - Page 6:
2025-06-25 00:00:15,380 - INFO - 第 6 页获取到 100 条记录
2025-06-25 00:00:15,380 - INFO - Request Parameters - Page 7:
2025-06-25 00:00:15,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:15,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:15,799 - INFO - Response - Page 7:
2025-06-25 00:00:16,003 - INFO - 第 7 页获取到 70 条记录
2025-06-25 00:00:16,003 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 00:00:16,004 - INFO - 获取到 670 条表单数据
2025-06-25 00:00:16,015 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 00:00:16,027 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:16,027 - INFO - 开始处理日期: 2025-03
2025-06-25 00:00:16,027 - INFO - Request Parameters - Page 1:
2025-06-25 00:00:16,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:16,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:16,561 - INFO - Response - Page 1:
2025-06-25 00:00:16,765 - INFO - 第 1 页获取到 100 条记录
2025-06-25 00:00:16,765 - INFO - Request Parameters - Page 2:
2025-06-25 00:00:16,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:16,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:17,344 - INFO - Response - Page 2:
2025-06-25 00:00:17,544 - INFO - 第 2 页获取到 100 条记录
2025-06-25 00:00:17,544 - INFO - Request Parameters - Page 3:
2025-06-25 00:00:17,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:17,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:18,061 - INFO - Response - Page 3:
2025-06-25 00:00:18,263 - INFO - 第 3 页获取到 100 条记录
2025-06-25 00:00:18,263 - INFO - Request Parameters - Page 4:
2025-06-25 00:00:18,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:18,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:18,697 - INFO - Response - Page 4:
2025-06-25 00:00:18,898 - INFO - 第 4 页获取到 100 条记录
2025-06-25 00:00:18,898 - INFO - Request Parameters - Page 5:
2025-06-25 00:00:18,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:18,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:19,435 - INFO - Response - Page 5:
2025-06-25 00:00:19,650 - INFO - 第 5 页获取到 100 条记录
2025-06-25 00:00:19,650 - INFO - Request Parameters - Page 6:
2025-06-25 00:00:19,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:19,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:20,197 - INFO - Response - Page 6:
2025-06-25 00:00:20,397 - INFO - 第 6 页获取到 100 条记录
2025-06-25 00:00:20,397 - INFO - Request Parameters - Page 7:
2025-06-25 00:00:20,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:20,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:20,851 - INFO - Response - Page 7:
2025-06-25 00:00:21,055 - INFO - 第 7 页获取到 61 条记录
2025-06-25 00:00:21,055 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 00:00:21,055 - INFO - 获取到 661 条表单数据
2025-06-25 00:00:21,055 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 00:00:21,071 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:21,071 - INFO - 开始处理日期: 2025-04
2025-06-25 00:00:21,071 - INFO - Request Parameters - Page 1:
2025-06-25 00:00:21,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:21,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:21,585 - INFO - Response - Page 1:
2025-06-25 00:00:21,788 - INFO - 第 1 页获取到 100 条记录
2025-06-25 00:00:21,788 - INFO - Request Parameters - Page 2:
2025-06-25 00:00:21,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:21,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:22,271 - INFO - Response - Page 2:
2025-06-25 00:00:22,471 - INFO - 第 2 页获取到 100 条记录
2025-06-25 00:00:22,471 - INFO - Request Parameters - Page 3:
2025-06-25 00:00:22,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:22,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:23,004 - INFO - Response - Page 3:
2025-06-25 00:00:23,207 - INFO - 第 3 页获取到 100 条记录
2025-06-25 00:00:23,207 - INFO - Request Parameters - Page 4:
2025-06-25 00:00:23,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:23,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:23,755 - INFO - Response - Page 4:
2025-06-25 00:00:23,955 - INFO - 第 4 页获取到 100 条记录
2025-06-25 00:00:23,955 - INFO - Request Parameters - Page 5:
2025-06-25 00:00:23,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:23,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:24,435 - INFO - Response - Page 5:
2025-06-25 00:00:24,644 - INFO - 第 5 页获取到 100 条记录
2025-06-25 00:00:24,644 - INFO - Request Parameters - Page 6:
2025-06-25 00:00:24,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:24,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:25,078 - INFO - Response - Page 6:
2025-06-25 00:00:25,281 - INFO - 第 6 页获取到 100 条记录
2025-06-25 00:00:25,281 - INFO - Request Parameters - Page 7:
2025-06-25 00:00:25,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:25,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:25,688 - INFO - Response - Page 7:
2025-06-25 00:00:25,904 - INFO - 第 7 页获取到 56 条记录
2025-06-25 00:00:25,904 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 00:00:25,904 - INFO - 获取到 656 条表单数据
2025-06-25 00:00:25,916 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 00:00:25,927 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:25,927 - INFO - 开始处理日期: 2025-05
2025-06-25 00:00:25,928 - INFO - Request Parameters - Page 1:
2025-06-25 00:00:25,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:25,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:26,390 - INFO - Response - Page 1:
2025-06-25 00:00:26,591 - INFO - 第 1 页获取到 100 条记录
2025-06-25 00:00:26,591 - INFO - Request Parameters - Page 2:
2025-06-25 00:00:26,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:26,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:27,059 - INFO - Response - Page 2:
2025-06-25 00:00:27,261 - INFO - 第 2 页获取到 100 条记录
2025-06-25 00:00:27,261 - INFO - Request Parameters - Page 3:
2025-06-25 00:00:27,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:27,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:27,762 - INFO - Response - Page 3:
2025-06-25 00:00:27,963 - INFO - 第 3 页获取到 100 条记录
2025-06-25 00:00:27,963 - INFO - Request Parameters - Page 4:
2025-06-25 00:00:27,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:27,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:28,446 - INFO - Response - Page 4:
2025-06-25 00:00:28,649 - INFO - 第 4 页获取到 100 条记录
2025-06-25 00:00:28,649 - INFO - Request Parameters - Page 5:
2025-06-25 00:00:28,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:28,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:29,181 - INFO - Response - Page 5:
2025-06-25 00:00:29,385 - INFO - 第 5 页获取到 100 条记录
2025-06-25 00:00:29,385 - INFO - Request Parameters - Page 6:
2025-06-25 00:00:29,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:29,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:29,867 - INFO - Response - Page 6:
2025-06-25 00:00:30,070 - INFO - 第 6 页获取到 100 条记录
2025-06-25 00:00:30,070 - INFO - Request Parameters - Page 7:
2025-06-25 00:00:30,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:30,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:30,537 - INFO - Response - Page 7:
2025-06-25 00:00:30,740 - INFO - 第 7 页获取到 65 条记录
2025-06-25 00:00:30,740 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 00:00:30,740 - INFO - 获取到 665 条表单数据
2025-06-25 00:00:30,740 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 00:00:30,756 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:30,756 - INFO - 开始处理日期: 2025-06
2025-06-25 00:00:30,756 - INFO - Request Parameters - Page 1:
2025-06-25 00:00:30,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:30,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:31,288 - INFO - Response - Page 1:
2025-06-25 00:00:31,503 - INFO - 第 1 页获取到 100 条记录
2025-06-25 00:00:31,503 - INFO - Request Parameters - Page 2:
2025-06-25 00:00:31,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:31,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:32,005 - INFO - Response - Page 2:
2025-06-25 00:00:32,208 - INFO - 第 2 页获取到 100 条记录
2025-06-25 00:00:32,208 - INFO - Request Parameters - Page 3:
2025-06-25 00:00:32,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:32,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:32,648 - INFO - Response - Page 3:
2025-06-25 00:00:32,851 - INFO - 第 3 页获取到 100 条记录
2025-06-25 00:00:32,851 - INFO - Request Parameters - Page 4:
2025-06-25 00:00:32,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:32,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:33,333 - INFO - Response - Page 4:
2025-06-25 00:00:33,534 - INFO - 第 4 页获取到 100 条记录
2025-06-25 00:00:33,534 - INFO - Request Parameters - Page 5:
2025-06-25 00:00:33,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:33,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:34,005 - INFO - Response - Page 5:
2025-06-25 00:00:34,209 - INFO - 第 5 页获取到 100 条记录
2025-06-25 00:00:34,209 - INFO - Request Parameters - Page 6:
2025-06-25 00:00:34,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:34,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:34,647 - INFO - Response - Page 6:
2025-06-25 00:00:34,850 - INFO - 第 6 页获取到 100 条记录
2025-06-25 00:00:34,850 - INFO - Request Parameters - Page 7:
2025-06-25 00:00:34,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 00:00:34,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 00:00:35,255 - INFO - Response - Page 7:
2025-06-25 00:00:35,455 - INFO - 第 7 页获取到 25 条记录
2025-06-25 00:00:35,455 - INFO - 查询完成，共获取到 625 条记录
2025-06-25 00:00:35,455 - INFO - 获取到 625 条表单数据
2025-06-25 00:00:35,467 - INFO - 当前日期 2025-06 有 625 条MySQL数据需要处理
2025-06-25 00:00:35,467 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Y
2025-06-25 00:00:35,972 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Y
2025-06-25 00:00:35,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5070.0, 'new_value': 6420.0}, {'field': 'total_amount', 'old_value': 5070.0, 'new_value': 6420.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-25 00:00:35,972 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-25 00:00:36,438 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-25 00:00:36,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 765952.0, 'new_value': 810946.0}, {'field': 'total_amount', 'old_value': 765952.0, 'new_value': 810946.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 166}]
2025-06-25 00:00:36,439 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-25 00:00:36,908 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-25 00:00:36,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1319523.15, 'new_value': 1375327.11}, {'field': 'total_amount', 'old_value': 1319523.15, 'new_value': 1375327.11}, {'field': 'order_count', 'old_value': 14450, 'new_value': 15190}]
2025-06-25 00:00:36,909 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-25 00:00:37,333 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-25 00:00:37,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56796.24, 'new_value': 58638.58}, {'field': 'offline_amount', 'old_value': 85826.3, 'new_value': 89196.56}, {'field': 'total_amount', 'old_value': 142622.54, 'new_value': 147835.14}, {'field': 'order_count', 'old_value': 4799, 'new_value': 4991}]
2025-06-25 00:00:37,333 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-25 00:00:37,773 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-25 00:00:37,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25006.0, 'new_value': 25007.0}, {'field': 'offline_amount', 'old_value': 521424.0, 'new_value': 577962.0}, {'field': 'total_amount', 'old_value': 546430.0, 'new_value': 602969.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 185}]
2025-06-25 00:00:37,773 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-25 00:00:38,210 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-25 00:00:38,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 431670.0, 'new_value': 459629.0}, {'field': 'total_amount', 'old_value': 435586.0, 'new_value': 463545.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 71}]
2025-06-25 00:00:38,210 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-25 00:00:38,662 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-25 00:00:38,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178554.5, 'new_value': 192110.5}, {'field': 'total_amount', 'old_value': 178554.5, 'new_value': 192110.5}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-06-25 00:00:38,662 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-25 00:00:39,099 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-25 00:00:39,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29237.0, 'new_value': 31370.0}, {'field': 'total_amount', 'old_value': 29714.0, 'new_value': 31847.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-06-25 00:00:39,099 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-25 00:00:39,534 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-25 00:00:39,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329688.28, 'new_value': 339381.33}, {'field': 'total_amount', 'old_value': 329688.28, 'new_value': 339381.33}, {'field': 'order_count', 'old_value': 1646, 'new_value': 1707}]
2025-06-25 00:00:39,534 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-25 00:00:40,034 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-25 00:00:40,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8782.0, 'new_value': 9328.0}, {'field': 'total_amount', 'old_value': 60861.0, 'new_value': 61407.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-06-25 00:00:40,034 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-25 00:00:40,454 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-25 00:00:40,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56436.0, 'new_value': 57575.0}, {'field': 'total_amount', 'old_value': 56436.0, 'new_value': 57575.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 152}]
2025-06-25 00:00:40,469 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-25 00:00:40,860 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-25 00:00:40,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50333.94, 'new_value': 53730.7}, {'field': 'offline_amount', 'old_value': 424736.7, 'new_value': 436422.29}, {'field': 'total_amount', 'old_value': 475070.64, 'new_value': 490152.99}, {'field': 'order_count', 'old_value': 4292, 'new_value': 4536}]
2025-06-25 00:00:40,860 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-25 00:00:41,345 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-25 00:00:41,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234444.53, 'new_value': 240549.53}, {'field': 'total_amount', 'old_value': 234444.53, 'new_value': 240549.53}, {'field': 'order_count', 'old_value': 144, 'new_value': 145}]
2025-06-25 00:00:41,345 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-25 00:00:41,875 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-25 00:00:41,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16961.6, 'new_value': 17527.12}, {'field': 'total_amount', 'old_value': 18427.97, 'new_value': 18993.49}, {'field': 'order_count', 'old_value': 363, 'new_value': 377}]
2025-06-25 00:00:41,875 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-25 00:00:42,327 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-25 00:00:42,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52930.0, 'new_value': 54589.0}, {'field': 'total_amount', 'old_value': 52930.0, 'new_value': 54589.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 87}]
2025-06-25 00:00:42,327 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-25 00:00:42,764 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-25 00:00:42,764 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31423.0, 'new_value': 31813.0}, {'field': 'total_amount', 'old_value': 31423.0, 'new_value': 31813.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-25 00:00:42,764 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-25 00:00:43,216 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-25 00:00:43,216 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47449.18, 'new_value': 49812.26}, {'field': 'offline_amount', 'old_value': 294676.6, 'new_value': 303695.9}, {'field': 'total_amount', 'old_value': 342125.78, 'new_value': 353508.16}, {'field': 'order_count', 'old_value': 2600, 'new_value': 2724}]
2025-06-25 00:00:43,216 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-25 00:00:43,670 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-25 00:00:43,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270082.7, 'new_value': 275108.5}, {'field': 'total_amount', 'old_value': 270082.7, 'new_value': 275108.5}, {'field': 'order_count', 'old_value': 2742, 'new_value': 2795}]
2025-06-25 00:00:43,670 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-25 00:00:44,102 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-25 00:00:44,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58951.5, 'new_value': 60105.5}, {'field': 'total_amount', 'old_value': 65576.9, 'new_value': 66730.9}, {'field': 'order_count', 'old_value': 160, 'new_value': 162}]
2025-06-25 00:00:44,103 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-25 00:00:44,544 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-25 00:00:44,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 780295.0, 'new_value': 790115.0}, {'field': 'total_amount', 'old_value': 852887.0, 'new_value': 862707.0}, {'field': 'order_count', 'old_value': 904, 'new_value': 919}]
2025-06-25 00:00:44,544 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-25 00:00:45,073 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-25 00:00:45,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58582.0, 'new_value': 60811.0}, {'field': 'total_amount', 'old_value': 58582.0, 'new_value': 60811.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-25 00:00:45,073 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-25 00:00:45,527 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-25 00:00:45,527 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95100.26, 'new_value': 98103.11}, {'field': 'total_amount', 'old_value': 95100.26, 'new_value': 98103.11}, {'field': 'order_count', 'old_value': 8206, 'new_value': 8508}]
2025-06-25 00:00:45,527 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-25 00:00:45,856 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-25 00:00:45,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354337.0, 'new_value': 368697.44}, {'field': 'total_amount', 'old_value': 433546.09, 'new_value': 447906.53}, {'field': 'order_count', 'old_value': 1398, 'new_value': 1440}]
2025-06-25 00:00:45,871 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-25 00:00:46,385 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-25 00:00:46,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65475.44, 'new_value': 69309.87}, {'field': 'offline_amount', 'old_value': 78297.58, 'new_value': 81975.36}, {'field': 'total_amount', 'old_value': 143773.02, 'new_value': 151285.23}, {'field': 'order_count', 'old_value': 7470, 'new_value': 7904}]
2025-06-25 00:00:46,386 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-25 00:00:46,839 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-25 00:00:46,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344485.0, 'new_value': 347976.0}, {'field': 'total_amount', 'old_value': 354285.0, 'new_value': 357776.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 88}]
2025-06-25 00:00:46,839 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-25 00:00:47,258 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-25 00:00:47,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23951.66, 'new_value': 24671.34}, {'field': 'offline_amount', 'old_value': 233705.76, 'new_value': 239184.04}, {'field': 'total_amount', 'old_value': 257657.42, 'new_value': 263855.38}, {'field': 'order_count', 'old_value': 1236, 'new_value': 1270}]
2025-06-25 00:00:47,258 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-25 00:00:47,696 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-25 00:00:47,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140715.91, 'new_value': 142806.81}, {'field': 'total_amount', 'old_value': 140715.91, 'new_value': 142806.81}, {'field': 'order_count', 'old_value': 310, 'new_value': 317}]
2025-06-25 00:00:47,696 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-25 00:00:48,119 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-25 00:00:48,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 252028.52, 'new_value': 260320.0}, {'field': 'offline_amount', 'old_value': 70378.32, 'new_value': 71921.22}, {'field': 'total_amount', 'old_value': 322406.84, 'new_value': 332241.22}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2095}]
2025-06-25 00:00:48,119 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-25 00:00:48,540 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-25 00:00:48,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117653.0, 'new_value': 120949.0}, {'field': 'total_amount', 'old_value': 122220.0, 'new_value': 125516.0}, {'field': 'order_count', 'old_value': 4332, 'new_value': 4342}]
2025-06-25 00:00:48,555 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-25 00:00:48,991 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-25 00:00:48,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39351.0, 'new_value': 40659.0}, {'field': 'offline_amount', 'old_value': 125897.07, 'new_value': 129233.07}, {'field': 'total_amount', 'old_value': 165248.07, 'new_value': 169892.07}, {'field': 'order_count', 'old_value': 238, 'new_value': 248}]
2025-06-25 00:00:48,991 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-25 00:00:49,415 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-25 00:00:49,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5996.51, 'new_value': 6103.31}, {'field': 'total_amount', 'old_value': 27536.51, 'new_value': 27643.31}, {'field': 'order_count', 'old_value': 91, 'new_value': 93}]
2025-06-25 00:00:49,415 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-25 00:00:49,849 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-25 00:00:49,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207435.0, 'new_value': 214904.0}, {'field': 'total_amount', 'old_value': 207435.0, 'new_value': 214904.0}, {'field': 'order_count', 'old_value': 215, 'new_value': 223}]
2025-06-25 00:00:49,849 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-25 00:00:50,269 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-25 00:00:50,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 453683.0, 'new_value': 478513.0}, {'field': 'total_amount', 'old_value': 453683.0, 'new_value': 478513.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 112}]
2025-06-25 00:00:50,269 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-25 00:00:50,645 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-25 00:00:50,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94381.7, 'new_value': 96315.7}, {'field': 'offline_amount', 'old_value': 55681.7, 'new_value': 55971.6}, {'field': 'total_amount', 'old_value': 150063.4, 'new_value': 152287.3}, {'field': 'order_count', 'old_value': 983, 'new_value': 1001}]
2025-06-25 00:00:50,645 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-25 00:00:51,083 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-25 00:00:51,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23121.33, 'new_value': 23229.63}, {'field': 'total_amount', 'old_value': 23185.93, 'new_value': 23294.23}, {'field': 'order_count', 'old_value': 134, 'new_value': 136}]
2025-06-25 00:00:51,083 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-25 00:00:51,567 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-25 00:00:51,567 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 160459.68, 'new_value': 170691.28}, {'field': 'offline_amount', 'old_value': 198808.71, 'new_value': 208808.71}, {'field': 'total_amount', 'old_value': 359268.39, 'new_value': 379499.99}, {'field': 'order_count', 'old_value': 1142, 'new_value': 1202}]
2025-06-25 00:00:51,567 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-25 00:00:51,972 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-25 00:00:51,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35101.17, 'new_value': 36801.51}, {'field': 'offline_amount', 'old_value': 39394.47, 'new_value': 40431.45}, {'field': 'total_amount', 'old_value': 74495.64, 'new_value': 77232.96}, {'field': 'order_count', 'old_value': 6377, 'new_value': 6621}]
2025-06-25 00:00:51,972 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-25 00:00:52,346 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-25 00:00:52,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93663.6, 'new_value': 99064.0}, {'field': 'total_amount', 'old_value': 93663.6, 'new_value': 99064.0}, {'field': 'order_count', 'old_value': 199, 'new_value': 208}]
2025-06-25 00:00:52,346 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-25 00:00:52,798 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-25 00:00:52,798 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 261911.92, 'new_value': 269142.02}, {'field': 'offline_amount', 'old_value': 1274035.89, 'new_value': 1314601.64}, {'field': 'total_amount', 'old_value': 1535947.81, 'new_value': 1583743.66}, {'field': 'order_count', 'old_value': 7325, 'new_value': 7584}]
2025-06-25 00:00:52,798 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-25 00:00:53,219 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-25 00:00:53,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 622159.0, 'new_value': 631088.0}, {'field': 'total_amount', 'old_value': 622159.0, 'new_value': 631088.0}, {'field': 'order_count', 'old_value': 421, 'new_value': 428}]
2025-06-25 00:00:53,219 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-25 00:00:53,664 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-25 00:00:53,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45000.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 45000.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-25 00:00:53,664 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-25 00:00:54,043 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-25 00:00:54,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 313195.5, 'new_value': 316857.6}, {'field': 'total_amount', 'old_value': 313195.5, 'new_value': 316857.6}, {'field': 'order_count', 'old_value': 360, 'new_value': 365}]
2025-06-25 00:00:54,043 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-25 00:00:54,471 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-25 00:00:54,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1401856.0, 'new_value': 1437322.0}, {'field': 'total_amount', 'old_value': 1401856.0, 'new_value': 1437322.0}, {'field': 'order_count', 'old_value': 6497, 'new_value': 6672}]
2025-06-25 00:00:54,471 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-25 00:00:54,923 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-25 00:00:54,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251571.17, 'new_value': 260868.17}, {'field': 'total_amount', 'old_value': 265566.17, 'new_value': 274863.17}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-06-25 00:00:54,924 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-25 00:00:55,351 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-25 00:00:55,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119778.9, 'new_value': 124269.0}, {'field': 'offline_amount', 'old_value': 91464.3, 'new_value': 94332.0}, {'field': 'total_amount', 'old_value': 211243.2, 'new_value': 218601.0}, {'field': 'order_count', 'old_value': 5097, 'new_value': 5293}]
2025-06-25 00:00:55,351 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-25 00:00:55,816 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-25 00:00:55,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75028.0, 'new_value': 79433.0}, {'field': 'offline_amount', 'old_value': 151395.0, 'new_value': 157673.0}, {'field': 'total_amount', 'old_value': 226423.0, 'new_value': 237106.0}, {'field': 'order_count', 'old_value': 4500, 'new_value': 4714}]
2025-06-25 00:00:55,818 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-25 00:00:56,216 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-25 00:00:56,216 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38897.0, 'new_value': 44793.8}, {'field': 'offline_amount', 'old_value': 334394.2, 'new_value': 338890.2}, {'field': 'total_amount', 'old_value': 373291.2, 'new_value': 383684.0}, {'field': 'order_count', 'old_value': 112, 'new_value': 121}]
2025-06-25 00:00:56,216 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-25 00:00:56,667 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-25 00:00:56,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47631.2, 'new_value': 53576.4}, {'field': 'total_amount', 'old_value': 47631.2, 'new_value': 53576.4}, {'field': 'order_count', 'old_value': 601, 'new_value': 623}]
2025-06-25 00:00:56,667 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-25 00:00:57,039 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-25 00:00:57,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43823.32, 'new_value': 48879.32}, {'field': 'total_amount', 'old_value': 43823.32, 'new_value': 48879.32}, {'field': 'order_count', 'old_value': 262, 'new_value': 291}]
2025-06-25 00:00:57,039 - INFO - 日期 2025-06 处理完成 - 更新: 49 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:57,039 - INFO - 数据同步完成！更新: 49 条，插入: 0 条，错误: 0 条
2025-06-25 00:00:57,039 - INFO - =================同步完成====================
2025-06-25 03:00:03,335 - INFO - =================使用默认全量同步=============
2025-06-25 03:00:05,101 - INFO - MySQL查询成功，共获取 3959 条记录
2025-06-25 03:00:05,101 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 03:00:05,132 - INFO - 开始处理日期: 2025-01
2025-06-25 03:00:05,148 - INFO - Request Parameters - Page 1:
2025-06-25 03:00:05,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:05,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:06,679 - INFO - Response - Page 1:
2025-06-25 03:00:06,882 - INFO - 第 1 页获取到 100 条记录
2025-06-25 03:00:06,882 - INFO - Request Parameters - Page 2:
2025-06-25 03:00:06,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:06,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:07,414 - INFO - Response - Page 2:
2025-06-25 03:00:07,617 - INFO - 第 2 页获取到 100 条记录
2025-06-25 03:00:07,617 - INFO - Request Parameters - Page 3:
2025-06-25 03:00:07,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:07,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:08,117 - INFO - Response - Page 3:
2025-06-25 03:00:08,320 - INFO - 第 3 页获取到 100 条记录
2025-06-25 03:00:08,320 - INFO - Request Parameters - Page 4:
2025-06-25 03:00:08,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:08,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:08,804 - INFO - Response - Page 4:
2025-06-25 03:00:09,008 - INFO - 第 4 页获取到 100 条记录
2025-06-25 03:00:09,008 - INFO - Request Parameters - Page 5:
2025-06-25 03:00:09,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:09,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:09,586 - INFO - Response - Page 5:
2025-06-25 03:00:09,789 - INFO - 第 5 页获取到 100 条记录
2025-06-25 03:00:09,789 - INFO - Request Parameters - Page 6:
2025-06-25 03:00:09,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:09,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:10,273 - INFO - Response - Page 6:
2025-06-25 03:00:10,477 - INFO - 第 6 页获取到 100 条记录
2025-06-25 03:00:10,477 - INFO - Request Parameters - Page 7:
2025-06-25 03:00:10,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:10,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:10,945 - INFO - Response - Page 7:
2025-06-25 03:00:11,149 - INFO - 第 7 页获取到 82 条记录
2025-06-25 03:00:11,149 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 03:00:11,149 - INFO - 获取到 682 条表单数据
2025-06-25 03:00:11,149 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 03:00:11,164 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:11,164 - INFO - 开始处理日期: 2025-02
2025-06-25 03:00:11,164 - INFO - Request Parameters - Page 1:
2025-06-25 03:00:11,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:11,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:11,649 - INFO - Response - Page 1:
2025-06-25 03:00:11,852 - INFO - 第 1 页获取到 100 条记录
2025-06-25 03:00:11,852 - INFO - Request Parameters - Page 2:
2025-06-25 03:00:11,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:11,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:12,321 - INFO - Response - Page 2:
2025-06-25 03:00:12,524 - INFO - 第 2 页获取到 100 条记录
2025-06-25 03:00:12,524 - INFO - Request Parameters - Page 3:
2025-06-25 03:00:12,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:12,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:13,086 - INFO - Response - Page 3:
2025-06-25 03:00:13,289 - INFO - 第 3 页获取到 100 条记录
2025-06-25 03:00:13,289 - INFO - Request Parameters - Page 4:
2025-06-25 03:00:13,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:13,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:13,774 - INFO - Response - Page 4:
2025-06-25 03:00:13,977 - INFO - 第 4 页获取到 100 条记录
2025-06-25 03:00:13,977 - INFO - Request Parameters - Page 5:
2025-06-25 03:00:13,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:13,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:14,555 - INFO - Response - Page 5:
2025-06-25 03:00:14,758 - INFO - 第 5 页获取到 100 条记录
2025-06-25 03:00:14,758 - INFO - Request Parameters - Page 6:
2025-06-25 03:00:14,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:14,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:15,258 - INFO - Response - Page 6:
2025-06-25 03:00:15,462 - INFO - 第 6 页获取到 100 条记录
2025-06-25 03:00:15,462 - INFO - Request Parameters - Page 7:
2025-06-25 03:00:15,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:15,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:15,946 - INFO - Response - Page 7:
2025-06-25 03:00:16,149 - INFO - 第 7 页获取到 70 条记录
2025-06-25 03:00:16,149 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 03:00:16,149 - INFO - 获取到 670 条表单数据
2025-06-25 03:00:16,149 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 03:00:16,165 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:16,165 - INFO - 开始处理日期: 2025-03
2025-06-25 03:00:16,165 - INFO - Request Parameters - Page 1:
2025-06-25 03:00:16,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:16,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:16,727 - INFO - Response - Page 1:
2025-06-25 03:00:16,931 - INFO - 第 1 页获取到 100 条记录
2025-06-25 03:00:16,931 - INFO - Request Parameters - Page 2:
2025-06-25 03:00:16,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:16,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:17,493 - INFO - Response - Page 2:
2025-06-25 03:00:17,696 - INFO - 第 2 页获取到 100 条记录
2025-06-25 03:00:17,696 - INFO - Request Parameters - Page 3:
2025-06-25 03:00:17,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:17,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:18,275 - INFO - Response - Page 3:
2025-06-25 03:00:18,478 - INFO - 第 3 页获取到 100 条记录
2025-06-25 03:00:18,478 - INFO - Request Parameters - Page 4:
2025-06-25 03:00:18,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:18,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:18,993 - INFO - Response - Page 4:
2025-06-25 03:00:19,197 - INFO - 第 4 页获取到 100 条记录
2025-06-25 03:00:19,197 - INFO - Request Parameters - Page 5:
2025-06-25 03:00:19,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:19,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:19,884 - INFO - Response - Page 5:
2025-06-25 03:00:20,087 - INFO - 第 5 页获取到 100 条记录
2025-06-25 03:00:20,087 - INFO - Request Parameters - Page 6:
2025-06-25 03:00:20,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:20,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:20,587 - INFO - Response - Page 6:
2025-06-25 03:00:20,791 - INFO - 第 6 页获取到 100 条记录
2025-06-25 03:00:20,791 - INFO - Request Parameters - Page 7:
2025-06-25 03:00:20,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:20,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:21,291 - INFO - Response - Page 7:
2025-06-25 03:00:21,494 - INFO - 第 7 页获取到 61 条记录
2025-06-25 03:00:21,494 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 03:00:21,494 - INFO - 获取到 661 条表单数据
2025-06-25 03:00:21,494 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 03:00:21,509 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:21,509 - INFO - 开始处理日期: 2025-04
2025-06-25 03:00:21,509 - INFO - Request Parameters - Page 1:
2025-06-25 03:00:21,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:21,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:21,994 - INFO - Response - Page 1:
2025-06-25 03:00:22,197 - INFO - 第 1 页获取到 100 条记录
2025-06-25 03:00:22,197 - INFO - Request Parameters - Page 2:
2025-06-25 03:00:22,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:22,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:22,650 - INFO - Response - Page 2:
2025-06-25 03:00:22,853 - INFO - 第 2 页获取到 100 条记录
2025-06-25 03:00:22,853 - INFO - Request Parameters - Page 3:
2025-06-25 03:00:22,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:22,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:23,291 - INFO - Response - Page 3:
2025-06-25 03:00:23,494 - INFO - 第 3 页获取到 100 条记录
2025-06-25 03:00:23,494 - INFO - Request Parameters - Page 4:
2025-06-25 03:00:23,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:23,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:24,025 - INFO - Response - Page 4:
2025-06-25 03:00:24,229 - INFO - 第 4 页获取到 100 条记录
2025-06-25 03:00:24,229 - INFO - Request Parameters - Page 5:
2025-06-25 03:00:24,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:24,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:24,744 - INFO - Response - Page 5:
2025-06-25 03:00:24,947 - INFO - 第 5 页获取到 100 条记录
2025-06-25 03:00:24,947 - INFO - Request Parameters - Page 6:
2025-06-25 03:00:24,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:24,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:25,447 - INFO - Response - Page 6:
2025-06-25 03:00:25,651 - INFO - 第 6 页获取到 100 条记录
2025-06-25 03:00:25,651 - INFO - Request Parameters - Page 7:
2025-06-25 03:00:25,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:25,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:26,119 - INFO - Response - Page 7:
2025-06-25 03:00:26,323 - INFO - 第 7 页获取到 56 条记录
2025-06-25 03:00:26,323 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 03:00:26,323 - INFO - 获取到 656 条表单数据
2025-06-25 03:00:26,323 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 03:00:26,338 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:26,338 - INFO - 开始处理日期: 2025-05
2025-06-25 03:00:26,338 - INFO - Request Parameters - Page 1:
2025-06-25 03:00:26,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:26,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:26,854 - INFO - Response - Page 1:
2025-06-25 03:00:27,057 - INFO - 第 1 页获取到 100 条记录
2025-06-25 03:00:27,057 - INFO - Request Parameters - Page 2:
2025-06-25 03:00:27,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:27,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:27,573 - INFO - Response - Page 2:
2025-06-25 03:00:27,776 - INFO - 第 2 页获取到 100 条记录
2025-06-25 03:00:27,776 - INFO - Request Parameters - Page 3:
2025-06-25 03:00:27,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:27,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:28,245 - INFO - Response - Page 3:
2025-06-25 03:00:28,448 - INFO - 第 3 页获取到 100 条记录
2025-06-25 03:00:28,448 - INFO - Request Parameters - Page 4:
2025-06-25 03:00:28,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:28,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:28,979 - INFO - Response - Page 4:
2025-06-25 03:00:29,182 - INFO - 第 4 页获取到 100 条记录
2025-06-25 03:00:29,182 - INFO - Request Parameters - Page 5:
2025-06-25 03:00:29,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:29,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:29,761 - INFO - Response - Page 5:
2025-06-25 03:00:29,964 - INFO - 第 5 页获取到 100 条记录
2025-06-25 03:00:29,964 - INFO - Request Parameters - Page 6:
2025-06-25 03:00:29,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:29,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:30,464 - INFO - Response - Page 6:
2025-06-25 03:00:30,667 - INFO - 第 6 页获取到 100 条记录
2025-06-25 03:00:30,667 - INFO - Request Parameters - Page 7:
2025-06-25 03:00:30,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:30,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:31,105 - INFO - Response - Page 7:
2025-06-25 03:00:31,308 - INFO - 第 7 页获取到 65 条记录
2025-06-25 03:00:31,308 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 03:00:31,308 - INFO - 获取到 665 条表单数据
2025-06-25 03:00:31,308 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 03:00:31,323 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:31,323 - INFO - 开始处理日期: 2025-06
2025-06-25 03:00:31,323 - INFO - Request Parameters - Page 1:
2025-06-25 03:00:31,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:31,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:31,917 - INFO - Response - Page 1:
2025-06-25 03:00:32,120 - INFO - 第 1 页获取到 100 条记录
2025-06-25 03:00:32,120 - INFO - Request Parameters - Page 2:
2025-06-25 03:00:32,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:32,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:32,574 - INFO - Response - Page 2:
2025-06-25 03:00:32,777 - INFO - 第 2 页获取到 100 条记录
2025-06-25 03:00:32,777 - INFO - Request Parameters - Page 3:
2025-06-25 03:00:32,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:32,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:33,261 - INFO - Response - Page 3:
2025-06-25 03:00:33,464 - INFO - 第 3 页获取到 100 条记录
2025-06-25 03:00:33,464 - INFO - Request Parameters - Page 4:
2025-06-25 03:00:33,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:33,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:33,980 - INFO - Response - Page 4:
2025-06-25 03:00:34,183 - INFO - 第 4 页获取到 100 条记录
2025-06-25 03:00:34,183 - INFO - Request Parameters - Page 5:
2025-06-25 03:00:34,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:34,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:34,636 - INFO - Response - Page 5:
2025-06-25 03:00:34,839 - INFO - 第 5 页获取到 100 条记录
2025-06-25 03:00:34,839 - INFO - Request Parameters - Page 6:
2025-06-25 03:00:34,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:34,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:35,371 - INFO - Response - Page 6:
2025-06-25 03:00:35,574 - INFO - 第 6 页获取到 100 条记录
2025-06-25 03:00:35,574 - INFO - Request Parameters - Page 7:
2025-06-25 03:00:35,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 03:00:35,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 03:00:35,949 - INFO - Response - Page 7:
2025-06-25 03:00:36,152 - INFO - 第 7 页获取到 25 条记录
2025-06-25 03:00:36,152 - INFO - 查询完成，共获取到 625 条记录
2025-06-25 03:00:36,152 - INFO - 获取到 625 条表单数据
2025-06-25 03:00:36,152 - INFO - 当前日期 2025-06 有 625 条MySQL数据需要处理
2025-06-25 03:00:36,168 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:36,168 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 03:00:36,168 - INFO - =================同步完成====================
2025-06-25 06:00:03,455 - INFO - =================使用默认全量同步=============
2025-06-25 06:00:05,222 - INFO - MySQL查询成功，共获取 3959 条记录
2025-06-25 06:00:05,222 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 06:00:05,253 - INFO - 开始处理日期: 2025-01
2025-06-25 06:00:05,253 - INFO - Request Parameters - Page 1:
2025-06-25 06:00:05,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:05,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:06,816 - INFO - Response - Page 1:
2025-06-25 06:00:07,019 - INFO - 第 1 页获取到 100 条记录
2025-06-25 06:00:07,019 - INFO - Request Parameters - Page 2:
2025-06-25 06:00:07,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:07,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:07,551 - INFO - Response - Page 2:
2025-06-25 06:00:07,754 - INFO - 第 2 页获取到 100 条记录
2025-06-25 06:00:07,754 - INFO - Request Parameters - Page 3:
2025-06-25 06:00:07,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:07,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:08,458 - INFO - Response - Page 3:
2025-06-25 06:00:08,661 - INFO - 第 3 页获取到 100 条记录
2025-06-25 06:00:08,661 - INFO - Request Parameters - Page 4:
2025-06-25 06:00:08,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:08,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:09,208 - INFO - Response - Page 4:
2025-06-25 06:00:09,411 - INFO - 第 4 页获取到 100 条记录
2025-06-25 06:00:09,411 - INFO - Request Parameters - Page 5:
2025-06-25 06:00:09,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:09,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:09,958 - INFO - Response - Page 5:
2025-06-25 06:00:10,162 - INFO - 第 5 页获取到 100 条记录
2025-06-25 06:00:10,162 - INFO - Request Parameters - Page 6:
2025-06-25 06:00:10,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:10,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:10,709 - INFO - Response - Page 6:
2025-06-25 06:00:10,912 - INFO - 第 6 页获取到 100 条记录
2025-06-25 06:00:10,912 - INFO - Request Parameters - Page 7:
2025-06-25 06:00:10,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:10,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:11,412 - INFO - Response - Page 7:
2025-06-25 06:00:11,615 - INFO - 第 7 页获取到 82 条记录
2025-06-25 06:00:11,615 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 06:00:11,615 - INFO - 获取到 682 条表单数据
2025-06-25 06:00:11,615 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 06:00:11,631 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:11,631 - INFO - 开始处理日期: 2025-02
2025-06-25 06:00:11,631 - INFO - Request Parameters - Page 1:
2025-06-25 06:00:11,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:11,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:12,147 - INFO - Response - Page 1:
2025-06-25 06:00:12,350 - INFO - 第 1 页获取到 100 条记录
2025-06-25 06:00:12,350 - INFO - Request Parameters - Page 2:
2025-06-25 06:00:12,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:12,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:12,882 - INFO - Response - Page 2:
2025-06-25 06:00:13,085 - INFO - 第 2 页获取到 100 条记录
2025-06-25 06:00:13,085 - INFO - Request Parameters - Page 3:
2025-06-25 06:00:13,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:13,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:13,601 - INFO - Response - Page 3:
2025-06-25 06:00:13,804 - INFO - 第 3 页获取到 100 条记录
2025-06-25 06:00:13,804 - INFO - Request Parameters - Page 4:
2025-06-25 06:00:13,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:13,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:14,492 - INFO - Response - Page 4:
2025-06-25 06:00:14,695 - INFO - 第 4 页获取到 100 条记录
2025-06-25 06:00:14,695 - INFO - Request Parameters - Page 5:
2025-06-25 06:00:14,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:14,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:15,164 - INFO - Response - Page 5:
2025-06-25 06:00:15,367 - INFO - 第 5 页获取到 100 条记录
2025-06-25 06:00:15,367 - INFO - Request Parameters - Page 6:
2025-06-25 06:00:15,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:15,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:15,899 - INFO - Response - Page 6:
2025-06-25 06:00:16,102 - INFO - 第 6 页获取到 100 条记录
2025-06-25 06:00:16,102 - INFO - Request Parameters - Page 7:
2025-06-25 06:00:16,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:16,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:16,587 - INFO - Response - Page 7:
2025-06-25 06:00:16,790 - INFO - 第 7 页获取到 70 条记录
2025-06-25 06:00:16,790 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 06:00:16,790 - INFO - 获取到 670 条表单数据
2025-06-25 06:00:16,790 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 06:00:16,805 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:16,805 - INFO - 开始处理日期: 2025-03
2025-06-25 06:00:16,805 - INFO - Request Parameters - Page 1:
2025-06-25 06:00:16,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:16,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:17,290 - INFO - Response - Page 1:
2025-06-25 06:00:17,493 - INFO - 第 1 页获取到 100 条记录
2025-06-25 06:00:17,493 - INFO - Request Parameters - Page 2:
2025-06-25 06:00:17,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:17,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:17,947 - INFO - Response - Page 2:
2025-06-25 06:00:18,150 - INFO - 第 2 页获取到 100 条记录
2025-06-25 06:00:18,150 - INFO - Request Parameters - Page 3:
2025-06-25 06:00:18,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:18,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:18,713 - INFO - Response - Page 3:
2025-06-25 06:00:18,916 - INFO - 第 3 页获取到 100 条记录
2025-06-25 06:00:18,916 - INFO - Request Parameters - Page 4:
2025-06-25 06:00:18,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:18,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:19,400 - INFO - Response - Page 4:
2025-06-25 06:00:19,604 - INFO - 第 4 页获取到 100 条记录
2025-06-25 06:00:19,604 - INFO - Request Parameters - Page 5:
2025-06-25 06:00:19,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:19,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:20,073 - INFO - Response - Page 5:
2025-06-25 06:00:20,276 - INFO - 第 5 页获取到 100 条记录
2025-06-25 06:00:20,276 - INFO - Request Parameters - Page 6:
2025-06-25 06:00:20,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:20,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:20,776 - INFO - Response - Page 6:
2025-06-25 06:00:20,979 - INFO - 第 6 页获取到 100 条记录
2025-06-25 06:00:20,979 - INFO - Request Parameters - Page 7:
2025-06-25 06:00:20,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:20,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:21,417 - INFO - Response - Page 7:
2025-06-25 06:00:21,620 - INFO - 第 7 页获取到 61 条记录
2025-06-25 06:00:21,620 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 06:00:21,620 - INFO - 获取到 661 条表单数据
2025-06-25 06:00:21,620 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 06:00:21,636 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:21,636 - INFO - 开始处理日期: 2025-04
2025-06-25 06:00:21,636 - INFO - Request Parameters - Page 1:
2025-06-25 06:00:21,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:21,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:22,199 - INFO - Response - Page 1:
2025-06-25 06:00:22,402 - INFO - 第 1 页获取到 100 条记录
2025-06-25 06:00:22,402 - INFO - Request Parameters - Page 2:
2025-06-25 06:00:22,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:22,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:22,902 - INFO - Response - Page 2:
2025-06-25 06:00:23,105 - INFO - 第 2 页获取到 100 条记录
2025-06-25 06:00:23,105 - INFO - Request Parameters - Page 3:
2025-06-25 06:00:23,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:23,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:23,590 - INFO - Response - Page 3:
2025-06-25 06:00:23,793 - INFO - 第 3 页获取到 100 条记录
2025-06-25 06:00:23,793 - INFO - Request Parameters - Page 4:
2025-06-25 06:00:23,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:23,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:24,293 - INFO - Response - Page 4:
2025-06-25 06:00:24,497 - INFO - 第 4 页获取到 100 条记录
2025-06-25 06:00:24,497 - INFO - Request Parameters - Page 5:
2025-06-25 06:00:24,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:24,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:24,997 - INFO - Response - Page 5:
2025-06-25 06:00:25,200 - INFO - 第 5 页获取到 100 条记录
2025-06-25 06:00:25,200 - INFO - Request Parameters - Page 6:
2025-06-25 06:00:25,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:25,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:25,700 - INFO - Response - Page 6:
2025-06-25 06:00:25,903 - INFO - 第 6 页获取到 100 条记录
2025-06-25 06:00:25,903 - INFO - Request Parameters - Page 7:
2025-06-25 06:00:25,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:25,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:26,357 - INFO - Response - Page 7:
2025-06-25 06:00:26,560 - INFO - 第 7 页获取到 56 条记录
2025-06-25 06:00:26,560 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 06:00:26,560 - INFO - 获取到 656 条表单数据
2025-06-25 06:00:26,560 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 06:00:26,576 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:26,576 - INFO - 开始处理日期: 2025-05
2025-06-25 06:00:26,576 - INFO - Request Parameters - Page 1:
2025-06-25 06:00:26,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:26,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:27,107 - INFO - Response - Page 1:
2025-06-25 06:00:27,310 - INFO - 第 1 页获取到 100 条记录
2025-06-25 06:00:27,310 - INFO - Request Parameters - Page 2:
2025-06-25 06:00:27,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:27,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:27,842 - INFO - Response - Page 2:
2025-06-25 06:00:28,045 - INFO - 第 2 页获取到 100 条记录
2025-06-25 06:00:28,045 - INFO - Request Parameters - Page 3:
2025-06-25 06:00:28,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:28,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:28,592 - INFO - Response - Page 3:
2025-06-25 06:00:28,795 - INFO - 第 3 页获取到 100 条记录
2025-06-25 06:00:28,795 - INFO - Request Parameters - Page 4:
2025-06-25 06:00:28,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:28,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:29,280 - INFO - Response - Page 4:
2025-06-25 06:00:29,483 - INFO - 第 4 页获取到 100 条记录
2025-06-25 06:00:29,483 - INFO - Request Parameters - Page 5:
2025-06-25 06:00:29,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:29,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:29,921 - INFO - Response - Page 5:
2025-06-25 06:00:30,124 - INFO - 第 5 页获取到 100 条记录
2025-06-25 06:00:30,124 - INFO - Request Parameters - Page 6:
2025-06-25 06:00:30,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:30,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:30,656 - INFO - Response - Page 6:
2025-06-25 06:00:30,859 - INFO - 第 6 页获取到 100 条记录
2025-06-25 06:00:30,859 - INFO - Request Parameters - Page 7:
2025-06-25 06:00:30,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:30,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:31,359 - INFO - Response - Page 7:
2025-06-25 06:00:31,562 - INFO - 第 7 页获取到 65 条记录
2025-06-25 06:00:31,562 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 06:00:31,562 - INFO - 获取到 665 条表单数据
2025-06-25 06:00:31,562 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 06:00:31,578 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:31,578 - INFO - 开始处理日期: 2025-06
2025-06-25 06:00:31,578 - INFO - Request Parameters - Page 1:
2025-06-25 06:00:31,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:31,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:32,141 - INFO - Response - Page 1:
2025-06-25 06:00:32,344 - INFO - 第 1 页获取到 100 条记录
2025-06-25 06:00:32,344 - INFO - Request Parameters - Page 2:
2025-06-25 06:00:32,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:32,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:33,000 - INFO - Response - Page 2:
2025-06-25 06:00:33,204 - INFO - 第 2 页获取到 100 条记录
2025-06-25 06:00:33,204 - INFO - Request Parameters - Page 3:
2025-06-25 06:00:33,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:33,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:33,751 - INFO - Response - Page 3:
2025-06-25 06:00:33,954 - INFO - 第 3 页获取到 100 条记录
2025-06-25 06:00:33,954 - INFO - Request Parameters - Page 4:
2025-06-25 06:00:33,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:33,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:34,486 - INFO - Response - Page 4:
2025-06-25 06:00:34,689 - INFO - 第 4 页获取到 100 条记录
2025-06-25 06:00:34,689 - INFO - Request Parameters - Page 5:
2025-06-25 06:00:34,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:34,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:35,142 - INFO - Response - Page 5:
2025-06-25 06:00:35,345 - INFO - 第 5 页获取到 100 条记录
2025-06-25 06:00:35,345 - INFO - Request Parameters - Page 6:
2025-06-25 06:00:35,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:35,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:35,861 - INFO - Response - Page 6:
2025-06-25 06:00:36,064 - INFO - 第 6 页获取到 100 条记录
2025-06-25 06:00:36,064 - INFO - Request Parameters - Page 7:
2025-06-25 06:00:36,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 06:00:36,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 06:00:36,393 - INFO - Response - Page 7:
2025-06-25 06:00:36,596 - INFO - 第 7 页获取到 25 条记录
2025-06-25 06:00:36,596 - INFO - 查询完成，共获取到 625 条记录
2025-06-25 06:00:36,596 - INFO - 获取到 625 条表单数据
2025-06-25 06:00:36,596 - INFO - 当前日期 2025-06 有 625 条MySQL数据需要处理
2025-06-25 06:00:36,611 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-25 06:00:37,112 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-25 06:00:37,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78900.0, 'new_value': 83015.0}, {'field': 'total_amount', 'old_value': 78900.0, 'new_value': 83015.0}, {'field': 'order_count', 'old_value': 403, 'new_value': 423}]
2025-06-25 06:00:37,112 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:37,112 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-25 06:00:37,112 - INFO - =================同步完成====================
2025-06-25 09:00:03,017 - INFO - =================使用默认全量同步=============
2025-06-25 09:00:04,798 - INFO - MySQL查询成功，共获取 3959 条记录
2025-06-25 09:00:04,798 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 09:00:04,829 - INFO - 开始处理日期: 2025-01
2025-06-25 09:00:04,829 - INFO - Request Parameters - Page 1:
2025-06-25 09:00:04,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:04,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:06,235 - INFO - Response - Page 1:
2025-06-25 09:00:06,438 - INFO - 第 1 页获取到 100 条记录
2025-06-25 09:00:06,438 - INFO - Request Parameters - Page 2:
2025-06-25 09:00:06,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:06,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:06,970 - INFO - Response - Page 2:
2025-06-25 09:00:07,173 - INFO - 第 2 页获取到 100 条记录
2025-06-25 09:00:07,173 - INFO - Request Parameters - Page 3:
2025-06-25 09:00:07,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:07,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:07,720 - INFO - Response - Page 3:
2025-06-25 09:00:07,923 - INFO - 第 3 页获取到 100 条记录
2025-06-25 09:00:07,923 - INFO - Request Parameters - Page 4:
2025-06-25 09:00:07,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:07,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:08,438 - INFO - Response - Page 4:
2025-06-25 09:00:08,642 - INFO - 第 4 页获取到 100 条记录
2025-06-25 09:00:08,642 - INFO - Request Parameters - Page 5:
2025-06-25 09:00:08,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:08,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:09,157 - INFO - Response - Page 5:
2025-06-25 09:00:09,360 - INFO - 第 5 页获取到 100 条记录
2025-06-25 09:00:09,360 - INFO - Request Parameters - Page 6:
2025-06-25 09:00:09,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:09,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:09,829 - INFO - Response - Page 6:
2025-06-25 09:00:10,032 - INFO - 第 6 页获取到 100 条记录
2025-06-25 09:00:10,032 - INFO - Request Parameters - Page 7:
2025-06-25 09:00:10,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:10,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:10,517 - INFO - Response - Page 7:
2025-06-25 09:00:10,720 - INFO - 第 7 页获取到 82 条记录
2025-06-25 09:00:10,720 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 09:00:10,720 - INFO - 获取到 682 条表单数据
2025-06-25 09:00:10,720 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 09:00:10,735 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 09:00:10,735 - INFO - 开始处理日期: 2025-02
2025-06-25 09:00:10,735 - INFO - Request Parameters - Page 1:
2025-06-25 09:00:10,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:10,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:11,329 - INFO - Response - Page 1:
2025-06-25 09:00:11,532 - INFO - 第 1 页获取到 100 条记录
2025-06-25 09:00:11,532 - INFO - Request Parameters - Page 2:
2025-06-25 09:00:11,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:11,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:12,188 - INFO - Response - Page 2:
2025-06-25 09:00:12,392 - INFO - 第 2 页获取到 100 条记录
2025-06-25 09:00:12,392 - INFO - Request Parameters - Page 3:
2025-06-25 09:00:12,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:12,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:12,954 - INFO - Response - Page 3:
2025-06-25 09:00:13,157 - INFO - 第 3 页获取到 100 条记录
2025-06-25 09:00:13,157 - INFO - Request Parameters - Page 4:
2025-06-25 09:00:13,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:13,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:13,673 - INFO - Response - Page 4:
2025-06-25 09:00:13,876 - INFO - 第 4 页获取到 100 条记录
2025-06-25 09:00:13,876 - INFO - Request Parameters - Page 5:
2025-06-25 09:00:13,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:13,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:14,376 - INFO - Response - Page 5:
2025-06-25 09:00:14,579 - INFO - 第 5 页获取到 100 条记录
2025-06-25 09:00:14,579 - INFO - Request Parameters - Page 6:
2025-06-25 09:00:14,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:14,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:15,110 - INFO - Response - Page 6:
2025-06-25 09:00:15,313 - INFO - 第 6 页获取到 100 条记录
2025-06-25 09:00:15,313 - INFO - Request Parameters - Page 7:
2025-06-25 09:00:15,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:15,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:15,892 - INFO - Response - Page 7:
2025-06-25 09:00:16,095 - INFO - 第 7 页获取到 70 条记录
2025-06-25 09:00:16,095 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 09:00:16,095 - INFO - 获取到 670 条表单数据
2025-06-25 09:00:16,095 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 09:00:16,110 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 09:00:16,110 - INFO - 开始处理日期: 2025-03
2025-06-25 09:00:16,110 - INFO - Request Parameters - Page 1:
2025-06-25 09:00:16,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:16,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:16,657 - INFO - Response - Page 1:
2025-06-25 09:00:16,860 - INFO - 第 1 页获取到 100 条记录
2025-06-25 09:00:16,860 - INFO - Request Parameters - Page 2:
2025-06-25 09:00:16,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:16,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:17,626 - INFO - Response - Page 2:
2025-06-25 09:00:17,829 - INFO - 第 2 页获取到 100 条记录
2025-06-25 09:00:17,829 - INFO - Request Parameters - Page 3:
2025-06-25 09:00:17,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:17,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:18,360 - INFO - Response - Page 3:
2025-06-25 09:00:18,563 - INFO - 第 3 页获取到 100 条记录
2025-06-25 09:00:18,563 - INFO - Request Parameters - Page 4:
2025-06-25 09:00:18,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:18,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:19,032 - INFO - Response - Page 4:
2025-06-25 09:00:19,235 - INFO - 第 4 页获取到 100 条记录
2025-06-25 09:00:19,235 - INFO - Request Parameters - Page 5:
2025-06-25 09:00:19,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:19,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:19,766 - INFO - Response - Page 5:
2025-06-25 09:00:19,970 - INFO - 第 5 页获取到 100 条记录
2025-06-25 09:00:19,970 - INFO - Request Parameters - Page 6:
2025-06-25 09:00:19,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:19,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:20,438 - INFO - Response - Page 6:
2025-06-25 09:00:20,641 - INFO - 第 6 页获取到 100 条记录
2025-06-25 09:00:20,641 - INFO - Request Parameters - Page 7:
2025-06-25 09:00:20,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:20,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:21,080 - INFO - Response - Page 7:
2025-06-25 09:00:21,282 - INFO - 第 7 页获取到 61 条记录
2025-06-25 09:00:21,282 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 09:00:21,282 - INFO - 获取到 661 条表单数据
2025-06-25 09:00:21,282 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 09:00:21,298 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 09:00:21,298 - INFO - 开始处理日期: 2025-04
2025-06-25 09:00:21,298 - INFO - Request Parameters - Page 1:
2025-06-25 09:00:21,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:21,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:21,782 - INFO - Response - Page 1:
2025-06-25 09:00:21,985 - INFO - 第 1 页获取到 100 条记录
2025-06-25 09:00:21,985 - INFO - Request Parameters - Page 2:
2025-06-25 09:00:21,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:21,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:22,813 - INFO - Response - Page 2:
2025-06-25 09:00:23,016 - INFO - 第 2 页获取到 100 条记录
2025-06-25 09:00:23,016 - INFO - Request Parameters - Page 3:
2025-06-25 09:00:23,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:23,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:23,516 - INFO - Response - Page 3:
2025-06-25 09:00:23,720 - INFO - 第 3 页获取到 100 条记录
2025-06-25 09:00:23,720 - INFO - Request Parameters - Page 4:
2025-06-25 09:00:23,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:23,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:24,188 - INFO - Response - Page 4:
2025-06-25 09:00:24,391 - INFO - 第 4 页获取到 100 条记录
2025-06-25 09:00:24,391 - INFO - Request Parameters - Page 5:
2025-06-25 09:00:24,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:24,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:24,891 - INFO - Response - Page 5:
2025-06-25 09:00:25,095 - INFO - 第 5 页获取到 100 条记录
2025-06-25 09:00:25,095 - INFO - Request Parameters - Page 6:
2025-06-25 09:00:25,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:25,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:25,579 - INFO - Response - Page 6:
2025-06-25 09:00:25,782 - INFO - 第 6 页获取到 100 条记录
2025-06-25 09:00:25,782 - INFO - Request Parameters - Page 7:
2025-06-25 09:00:25,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:25,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:26,188 - INFO - Response - Page 7:
2025-06-25 09:00:26,391 - INFO - 第 7 页获取到 56 条记录
2025-06-25 09:00:26,391 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 09:00:26,391 - INFO - 获取到 656 条表单数据
2025-06-25 09:00:26,391 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 09:00:26,407 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 09:00:26,407 - INFO - 开始处理日期: 2025-05
2025-06-25 09:00:26,407 - INFO - Request Parameters - Page 1:
2025-06-25 09:00:26,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:26,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:26,907 - INFO - Response - Page 1:
2025-06-25 09:00:27,110 - INFO - 第 1 页获取到 100 条记录
2025-06-25 09:00:27,110 - INFO - Request Parameters - Page 2:
2025-06-25 09:00:27,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:27,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:27,657 - INFO - Response - Page 2:
2025-06-25 09:00:27,860 - INFO - 第 2 页获取到 100 条记录
2025-06-25 09:00:27,860 - INFO - Request Parameters - Page 3:
2025-06-25 09:00:27,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:27,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:28,313 - INFO - Response - Page 3:
2025-06-25 09:00:28,516 - INFO - 第 3 页获取到 100 条记录
2025-06-25 09:00:28,516 - INFO - Request Parameters - Page 4:
2025-06-25 09:00:28,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:28,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:29,048 - INFO - Response - Page 4:
2025-06-25 09:00:29,251 - INFO - 第 4 页获取到 100 条记录
2025-06-25 09:00:29,251 - INFO - Request Parameters - Page 5:
2025-06-25 09:00:29,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:29,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:29,798 - INFO - Response - Page 5:
2025-06-25 09:00:30,001 - INFO - 第 5 页获取到 100 条记录
2025-06-25 09:00:30,001 - INFO - Request Parameters - Page 6:
2025-06-25 09:00:30,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:30,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:30,470 - INFO - Response - Page 6:
2025-06-25 09:00:30,673 - INFO - 第 6 页获取到 100 条记录
2025-06-25 09:00:30,673 - INFO - Request Parameters - Page 7:
2025-06-25 09:00:30,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:30,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:31,157 - INFO - Response - Page 7:
2025-06-25 09:00:31,360 - INFO - 第 7 页获取到 65 条记录
2025-06-25 09:00:31,360 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 09:00:31,360 - INFO - 获取到 665 条表单数据
2025-06-25 09:00:31,360 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 09:00:31,376 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 09:00:31,376 - INFO - 开始处理日期: 2025-06
2025-06-25 09:00:31,376 - INFO - Request Parameters - Page 1:
2025-06-25 09:00:31,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:31,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:31,876 - INFO - Response - Page 1:
2025-06-25 09:00:32,079 - INFO - 第 1 页获取到 100 条记录
2025-06-25 09:00:32,079 - INFO - Request Parameters - Page 2:
2025-06-25 09:00:32,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:32,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:32,563 - INFO - Response - Page 2:
2025-06-25 09:00:32,766 - INFO - 第 2 页获取到 100 条记录
2025-06-25 09:00:32,766 - INFO - Request Parameters - Page 3:
2025-06-25 09:00:32,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:32,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:33,266 - INFO - Response - Page 3:
2025-06-25 09:00:33,470 - INFO - 第 3 页获取到 100 条记录
2025-06-25 09:00:33,470 - INFO - Request Parameters - Page 4:
2025-06-25 09:00:33,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:33,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:33,954 - INFO - Response - Page 4:
2025-06-25 09:00:34,157 - INFO - 第 4 页获取到 100 条记录
2025-06-25 09:00:34,157 - INFO - Request Parameters - Page 5:
2025-06-25 09:00:34,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:34,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:34,657 - INFO - Response - Page 5:
2025-06-25 09:00:34,860 - INFO - 第 5 页获取到 100 条记录
2025-06-25 09:00:34,860 - INFO - Request Parameters - Page 6:
2025-06-25 09:00:34,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:34,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:35,344 - INFO - Response - Page 6:
2025-06-25 09:00:35,548 - INFO - 第 6 页获取到 100 条记录
2025-06-25 09:00:35,548 - INFO - Request Parameters - Page 7:
2025-06-25 09:00:35,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 09:00:35,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 09:00:35,923 - INFO - Response - Page 7:
2025-06-25 09:00:36,126 - INFO - 第 7 页获取到 25 条记录
2025-06-25 09:00:36,126 - INFO - 查询完成，共获取到 625 条记录
2025-06-25 09:00:36,126 - INFO - 获取到 625 条表单数据
2025-06-25 09:00:36,126 - INFO - 当前日期 2025-06 有 625 条MySQL数据需要处理
2025-06-25 09:00:36,126 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-25 09:00:36,688 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-25 09:00:36,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 673159.0, 'new_value': 696231.0}, {'field': 'total_amount', 'old_value': 673159.0, 'new_value': 696231.0}, {'field': 'order_count', 'old_value': 4759, 'new_value': 4937}]
2025-06-25 09:00:36,688 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-25 09:00:37,282 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-25 09:00:37,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103560.0, 'new_value': 111960.0}, {'field': 'total_amount', 'old_value': 103560.0, 'new_value': 111960.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-25 09:00:37,282 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-25 09:00:37,719 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-25 09:00:37,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300490.0, 'new_value': 310670.0}, {'field': 'total_amount', 'old_value': 322670.0, 'new_value': 332850.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 231}]
2025-06-25 09:00:37,719 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-25 09:00:38,173 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-25 09:00:38,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175200.0, 'new_value': 184060.0}, {'field': 'total_amount', 'old_value': 175200.0, 'new_value': 184060.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-25 09:00:38,173 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-25 09:00:38,594 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-25 09:00:38,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84562.0, 'new_value': 97028.0}, {'field': 'total_amount', 'old_value': 91528.0, 'new_value': 103994.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 118}]
2025-06-25 09:00:38,594 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-25 09:00:39,016 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-25 09:00:39,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180361.0, 'new_value': 184341.0}, {'field': 'total_amount', 'old_value': 180361.0, 'new_value': 184341.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-06-25 09:00:39,016 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-25 09:00:39,469 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-25 09:00:39,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255120.5, 'new_value': 260952.2}, {'field': 'total_amount', 'old_value': 255120.5, 'new_value': 260952.2}, {'field': 'order_count', 'old_value': 3696, 'new_value': 3826}]
2025-06-25 09:00:39,469 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-25 09:00:39,907 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-25 09:00:39,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4862.0, 'new_value': 5437.7}, {'field': 'offline_amount', 'old_value': 48238.67, 'new_value': 50042.77}, {'field': 'total_amount', 'old_value': 53100.67, 'new_value': 55480.47}, {'field': 'order_count', 'old_value': 459, 'new_value': 486}]
2025-06-25 09:00:39,907 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-25 09:00:40,469 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-25 09:00:40,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46574.04, 'new_value': 49833.98}, {'field': 'offline_amount', 'old_value': 104690.62, 'new_value': 112934.02}, {'field': 'total_amount', 'old_value': 151264.66, 'new_value': 162768.0}, {'field': 'order_count', 'old_value': 1718, 'new_value': 1827}]
2025-06-25 09:00:40,469 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-25 09:00:40,923 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-25 09:00:40,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30628.0, 'new_value': 34101.0}, {'field': 'offline_amount', 'old_value': 49715.08, 'new_value': 53858.08}, {'field': 'total_amount', 'old_value': 80343.08, 'new_value': 87959.08}, {'field': 'order_count', 'old_value': 111, 'new_value': 118}]
2025-06-25 09:00:40,923 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-25 09:00:41,438 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-25 09:00:41,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56717.0, 'new_value': 59514.0}, {'field': 'total_amount', 'old_value': 56717.0, 'new_value': 59514.0}, {'field': 'order_count', 'old_value': 1067, 'new_value': 1123}]
2025-06-25 09:00:41,438 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-25 09:00:41,891 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-25 09:00:41,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136875.0, 'new_value': 141228.0}, {'field': 'offline_amount', 'old_value': 45804.6, 'new_value': 46965.87}, {'field': 'total_amount', 'old_value': 182679.6, 'new_value': 188193.87}, {'field': 'order_count', 'old_value': 1227, 'new_value': 1269}]
2025-06-25 09:00:41,891 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-25 09:00:42,235 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-25 09:00:42,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 568.2, 'new_value': 701.2}, {'field': 'total_amount', 'old_value': 40569.2, 'new_value': 40702.2}, {'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-06-25 09:00:42,235 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-25 09:00:42,641 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-25 09:00:42,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 439460.6, 'new_value': 440540.6}, {'field': 'total_amount', 'old_value': 494953.6, 'new_value': 496033.6}, {'field': 'order_count', 'old_value': 82, 'new_value': 83}]
2025-06-25 09:00:42,641 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-25 09:00:43,063 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-25 09:00:43,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22546.56, 'new_value': 23354.21}, {'field': 'offline_amount', 'old_value': 26253.87, 'new_value': 27261.92}, {'field': 'total_amount', 'old_value': 48800.43, 'new_value': 50616.13}, {'field': 'order_count', 'old_value': 2434, 'new_value': 2535}]
2025-06-25 09:00:43,063 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-25 09:00:43,438 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-25 09:00:43,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122067.5, 'new_value': 125198.5}, {'field': 'total_amount', 'old_value': 122067.5, 'new_value': 125198.5}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-06-25 09:00:43,438 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-25 09:00:43,923 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-25 09:00:43,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11418.0, 'new_value': 11679.0}, {'field': 'total_amount', 'old_value': 32947.0, 'new_value': 33208.0}, {'field': 'order_count', 'old_value': 5237, 'new_value': 5239}]
2025-06-25 09:00:43,923 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-25 09:00:44,329 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-25 09:00:44,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162409.0, 'new_value': 165604.0}, {'field': 'total_amount', 'old_value': 162409.0, 'new_value': 165604.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 305}]
2025-06-25 09:00:44,329 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-25 09:00:44,719 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-25 09:00:44,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9499.1, 'new_value': 9945.2}, {'field': 'offline_amount', 'old_value': 142969.87, 'new_value': 148256.61}, {'field': 'total_amount', 'old_value': 152468.97, 'new_value': 158201.81}, {'field': 'order_count', 'old_value': 1721, 'new_value': 1783}]
2025-06-25 09:00:44,719 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-25 09:00:45,157 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-25 09:00:45,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70695.0, 'new_value': 74773.0}, {'field': 'total_amount', 'old_value': 77155.4, 'new_value': 81233.4}, {'field': 'order_count', 'old_value': 70, 'new_value': 73}]
2025-06-25 09:00:45,157 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-25 09:00:45,594 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-25 09:00:45,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9264.16, 'new_value': 10162.16}, {'field': 'total_amount', 'old_value': 9264.16, 'new_value': 10162.16}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-25 09:00:45,594 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-25 09:00:46,094 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-25 09:00:46,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130107.99, 'new_value': 134704.26}, {'field': 'total_amount', 'old_value': 130107.99, 'new_value': 134704.26}, {'field': 'order_count', 'old_value': 655, 'new_value': 683}]
2025-06-25 09:00:46,094 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-25 09:00:46,516 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-25 09:00:46,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25960.0, 'new_value': 27241.0}, {'field': 'total_amount', 'old_value': 25960.0, 'new_value': 27241.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 127}]
2025-06-25 09:00:46,516 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-25 09:00:46,954 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-25 09:00:46,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170317.0, 'new_value': 178569.0}, {'field': 'total_amount', 'old_value': 170317.0, 'new_value': 178569.0}, {'field': 'order_count', 'old_value': 6560, 'new_value': 6882}]
2025-06-25 09:00:46,954 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-25 09:00:47,548 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-25 09:00:47,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141037.34, 'new_value': 144820.14}, {'field': 'total_amount', 'old_value': 141037.34, 'new_value': 144820.14}, {'field': 'order_count', 'old_value': 559, 'new_value': 578}]
2025-06-25 09:00:47,548 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-25 09:00:47,985 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-25 09:00:47,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121240.0, 'new_value': 124117.5}, {'field': 'offline_amount', 'old_value': 47896.06, 'new_value': 49359.06}, {'field': 'total_amount', 'old_value': 169136.06, 'new_value': 173476.56}, {'field': 'order_count', 'old_value': 1200, 'new_value': 1237}]
2025-06-25 09:00:47,985 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-25 09:00:48,469 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-25 09:00:48,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123908.23, 'new_value': 129354.4}, {'field': 'total_amount', 'old_value': 123908.23, 'new_value': 129354.4}, {'field': 'order_count', 'old_value': 4354, 'new_value': 4578}]
2025-06-25 09:00:48,469 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-25 09:00:48,923 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-25 09:00:48,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4056.0, 'new_value': 8064.0}, {'field': 'total_amount', 'old_value': 4550.0, 'new_value': 8558.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-06-25 09:00:48,923 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-25 09:00:49,391 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-25 09:00:49,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56752.0, 'new_value': 58254.0}, {'field': 'offline_amount', 'old_value': 170487.0, 'new_value': 172933.0}, {'field': 'total_amount', 'old_value': 227239.0, 'new_value': 231187.0}, {'field': 'order_count', 'old_value': 170, 'new_value': 180}]
2025-06-25 09:00:49,391 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-25 09:00:49,923 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-25 09:00:49,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16365.56, 'new_value': 16879.37}, {'field': 'offline_amount', 'old_value': 346208.43, 'new_value': 367815.19}, {'field': 'total_amount', 'old_value': 362573.99, 'new_value': 384694.56}, {'field': 'order_count', 'old_value': 1787, 'new_value': 1864}]
2025-06-25 09:00:49,923 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-25 09:00:50,376 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-25 09:00:50,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142624.0, 'new_value': 149076.0}, {'field': 'total_amount', 'old_value': 142624.0, 'new_value': 149076.0}, {'field': 'order_count', 'old_value': 3734, 'new_value': 3908}]
2025-06-25 09:00:50,376 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-25 09:00:50,782 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-25 09:00:50,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117584.05, 'new_value': 121949.54}, {'field': 'total_amount', 'old_value': 117584.05, 'new_value': 121949.54}, {'field': 'order_count', 'old_value': 1493, 'new_value': 1548}]
2025-06-25 09:00:50,782 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-25 09:00:51,251 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-25 09:00:51,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63803.06, 'new_value': 64322.46}, {'field': 'total_amount', 'old_value': 63803.06, 'new_value': 64322.46}, {'field': 'order_count', 'old_value': 268, 'new_value': 274}]
2025-06-25 09:00:51,266 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-25 09:00:51,829 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-25 09:00:51,829 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11774.29, 'new_value': 12651.29}, {'field': 'offline_amount', 'old_value': 22889.83, 'new_value': 23728.83}, {'field': 'total_amount', 'old_value': 34664.12, 'new_value': 36380.12}, {'field': 'order_count', 'old_value': 1204, 'new_value': 1254}]
2025-06-25 09:00:51,829 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-25 09:00:52,251 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-25 09:00:52,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37676.0, 'new_value': 40676.0}, {'field': 'total_amount', 'old_value': 37676.0, 'new_value': 40676.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-25 09:00:52,251 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-25 09:00:52,751 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-25 09:00:52,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 366780.3, 'new_value': 381506.1}, {'field': 'offline_amount', 'old_value': 80493.5, 'new_value': 80642.5}, {'field': 'total_amount', 'old_value': 447273.8, 'new_value': 462148.6}, {'field': 'order_count', 'old_value': 565, 'new_value': 581}]
2025-06-25 09:00:52,751 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-25 09:00:53,219 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-25 09:00:53,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24956.96, 'new_value': 26054.96}, {'field': 'total_amount', 'old_value': 24956.96, 'new_value': 26054.96}, {'field': 'order_count', 'old_value': 990, 'new_value': 1038}]
2025-06-25 09:00:53,219 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-25 09:00:53,672 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-25 09:00:53,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94230.94, 'new_value': 99235.7}, {'field': 'total_amount', 'old_value': 94230.94, 'new_value': 99235.7}, {'field': 'order_count', 'old_value': 2667, 'new_value': 2808}]
2025-06-25 09:00:53,672 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-25 09:00:54,126 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-25 09:00:54,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339629.36, 'new_value': 352683.19}, {'field': 'total_amount', 'old_value': 339629.36, 'new_value': 352683.19}, {'field': 'order_count', 'old_value': 1081, 'new_value': 1124}]
2025-06-25 09:00:54,126 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-25 09:00:54,563 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-25 09:00:54,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 369.0}, {'field': 'offline_amount', 'old_value': 41855.0, 'new_value': 42190.0}, {'field': 'total_amount', 'old_value': 41855.0, 'new_value': 42559.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-06-25 09:00:54,563 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-25 09:00:54,969 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-25 09:00:54,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66401.0, 'new_value': 70401.0}, {'field': 'offline_amount', 'old_value': 21650.2, 'new_value': 22240.8}, {'field': 'total_amount', 'old_value': 88051.2, 'new_value': 92641.8}, {'field': 'order_count', 'old_value': 310, 'new_value': 317}]
2025-06-25 09:00:54,969 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-25 09:00:55,391 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-25 09:00:55,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14453.0, 'new_value': 15716.0}, {'field': 'offline_amount', 'old_value': 64697.85, 'new_value': 66581.15}, {'field': 'total_amount', 'old_value': 79150.85, 'new_value': 82297.15}, {'field': 'order_count', 'old_value': 788, 'new_value': 817}]
2025-06-25 09:00:55,391 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-25 09:00:55,860 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-25 09:00:55,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5189.0, 'new_value': 5706.0}, {'field': 'offline_amount', 'old_value': 26956.7, 'new_value': 28279.1}, {'field': 'total_amount', 'old_value': 32145.7, 'new_value': 33985.1}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1206}]
2025-06-25 09:00:55,860 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-25 09:00:56,329 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-25 09:00:56,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22768.57, 'new_value': 23891.55}, {'field': 'offline_amount', 'old_value': 43787.12, 'new_value': 46120.72}, {'field': 'total_amount', 'old_value': 66555.69, 'new_value': 70012.27}, {'field': 'order_count', 'old_value': 2474, 'new_value': 2595}]
2025-06-25 09:00:56,329 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-25 09:00:56,751 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-25 09:00:56,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17211.0, 'new_value': 18363.0}, {'field': 'total_amount', 'old_value': 17211.0, 'new_value': 18363.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 103}]
2025-06-25 09:00:56,751 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-25 09:00:57,204 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-25 09:00:57,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86151.96, 'new_value': 90510.96}, {'field': 'offline_amount', 'old_value': 132079.25, 'new_value': 135539.19}, {'field': 'total_amount', 'old_value': 218231.21, 'new_value': 226050.15}, {'field': 'order_count', 'old_value': 1247, 'new_value': 1268}]
2025-06-25 09:00:57,204 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-25 09:00:57,626 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-25 09:00:57,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69191.62, 'new_value': 72558.72}, {'field': 'total_amount', 'old_value': 69191.62, 'new_value': 72558.72}, {'field': 'order_count', 'old_value': 1860, 'new_value': 1958}]
2025-06-25 09:00:57,626 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-25 09:00:58,063 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-25 09:00:58,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39265.81, 'new_value': 39397.17}, {'field': 'offline_amount', 'old_value': 52269.26, 'new_value': 54532.38}, {'field': 'total_amount', 'old_value': 91535.07, 'new_value': 93929.55}, {'field': 'order_count', 'old_value': 549, 'new_value': 561}]
2025-06-25 09:00:58,063 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-25 09:00:58,532 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-25 09:00:58,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15017.0, 'new_value': 16629.0}, {'field': 'total_amount', 'old_value': 15017.0, 'new_value': 16629.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 48}]
2025-06-25 09:00:58,532 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-25 09:00:59,001 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-25 09:00:59,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155549.83, 'new_value': 166075.47}, {'field': 'offline_amount', 'old_value': 137190.9, 'new_value': 141903.26}, {'field': 'total_amount', 'old_value': 292740.73, 'new_value': 307978.73}, {'field': 'order_count', 'old_value': 2741, 'new_value': 2894}]
2025-06-25 09:00:59,001 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-25 09:00:59,563 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-25 09:00:59,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38978.0, 'new_value': 40668.0}, {'field': 'total_amount', 'old_value': 74184.0, 'new_value': 75874.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 90}]
2025-06-25 09:00:59,563 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-25 09:01:00,110 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-25 09:01:00,126 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7556.2, 'new_value': 8198.7}, {'field': 'offline_amount', 'old_value': 64172.0, 'new_value': 66372.0}, {'field': 'total_amount', 'old_value': 71728.2, 'new_value': 74570.7}, {'field': 'order_count', 'old_value': 60, 'new_value': 64}]
2025-06-25 09:01:00,126 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-25 09:01:00,594 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-25 09:01:00,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14780.0, 'new_value': 15070.0}, {'field': 'total_amount', 'old_value': 14780.0, 'new_value': 15070.0}, {'field': 'order_count', 'old_value': 252, 'new_value': 257}]
2025-06-25 09:01:00,594 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-25 09:01:01,079 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-25 09:01:01,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67984.84, 'new_value': 68862.12}, {'field': 'total_amount', 'old_value': 67984.84, 'new_value': 68862.12}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-06-25 09:01:01,079 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIR
2025-06-25 09:01:01,547 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIR
2025-06-25 09:01:01,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12424.0, 'new_value': 15044.0}, {'field': 'total_amount', 'old_value': 12424.0, 'new_value': 15044.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-25 09:01:01,547 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-25 09:01:01,985 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-25 09:01:01,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24747.7, 'new_value': 25208.7}, {'field': 'total_amount', 'old_value': 24747.7, 'new_value': 25208.7}, {'field': 'order_count', 'old_value': 234, 'new_value': 239}]
2025-06-25 09:01:01,985 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-25 09:01:02,391 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-25 09:01:02,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42947.89, 'new_value': 44510.79}, {'field': 'total_amount', 'old_value': 42956.89, 'new_value': 44519.79}, {'field': 'order_count', 'old_value': 1788, 'new_value': 1852}]
2025-06-25 09:01:02,391 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-25 09:01:04,204 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-25 09:01:04,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326295.92, 'new_value': 336297.92}, {'field': 'total_amount', 'old_value': 362279.92, 'new_value': 372281.92}, {'field': 'order_count', 'old_value': 1894, 'new_value': 1946}]
2025-06-25 09:01:04,204 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-25 09:01:04,672 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-25 09:01:04,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69027.0, 'new_value': 70363.0}, {'field': 'total_amount', 'old_value': 71842.0, 'new_value': 73178.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 286}]
2025-06-25 09:01:04,672 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-25 09:01:05,079 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-25 09:01:05,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176256.14, 'new_value': 179404.97}, {'field': 'offline_amount', 'old_value': 35438.2, 'new_value': 41080.68}, {'field': 'total_amount', 'old_value': 211694.34, 'new_value': 220485.65}, {'field': 'order_count', 'old_value': 869, 'new_value': 888}]
2025-06-25 09:01:05,079 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-25 09:01:05,563 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-25 09:01:05,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55354.0, 'new_value': 55819.0}, {'field': 'total_amount', 'old_value': 55354.0, 'new_value': 55819.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-25 09:01:05,563 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-25 09:01:06,001 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-25 09:01:06,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38182.1, 'new_value': 41415.1}, {'field': 'total_amount', 'old_value': 38182.1, 'new_value': 41415.1}, {'field': 'order_count', 'old_value': 93, 'new_value': 100}]
2025-06-25 09:01:06,001 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-25 09:01:06,532 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-25 09:01:06,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91357.0, 'new_value': 94475.0}, {'field': 'offline_amount', 'old_value': 390396.0, 'new_value': 408003.0}, {'field': 'total_amount', 'old_value': 481753.0, 'new_value': 502478.0}, {'field': 'order_count', 'old_value': 625, 'new_value': 654}]
2025-06-25 09:01:06,532 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-25 09:01:07,063 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-25 09:01:07,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208621.4, 'new_value': 230403.6}, {'field': 'total_amount', 'old_value': 208621.4, 'new_value': 230403.6}, {'field': 'order_count', 'old_value': 47, 'new_value': 53}]
2025-06-25 09:01:07,063 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-25 09:01:07,579 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-25 09:01:07,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1532813.11, 'new_value': 1575525.31}, {'field': 'total_amount', 'old_value': 1591291.81, 'new_value': 1634004.01}, {'field': 'order_count', 'old_value': 3000, 'new_value': 3092}]
2025-06-25 09:01:07,579 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-25 09:01:08,016 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-25 09:01:08,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28315.43, 'new_value': 29529.73}, {'field': 'total_amount', 'old_value': 28315.43, 'new_value': 29529.73}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-06-25 09:01:08,016 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-25 09:01:08,438 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-25 09:01:08,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252532.6, 'new_value': 263755.3}, {'field': 'total_amount', 'old_value': 252532.6, 'new_value': 263755.3}, {'field': 'order_count', 'old_value': 7597, 'new_value': 7974}]
2025-06-25 09:01:08,438 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-25 09:01:08,860 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-25 09:01:08,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108822.0, 'new_value': 115821.0}, {'field': 'total_amount', 'old_value': 108822.0, 'new_value': 115821.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-06-25 09:01:08,860 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-25 09:01:09,313 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-25 09:01:09,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 641154.0, 'new_value': 654455.0}, {'field': 'total_amount', 'old_value': 641154.0, 'new_value': 654455.0}, {'field': 'order_count', 'old_value': 3022, 'new_value': 3121}]
2025-06-25 09:01:09,313 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-25 09:01:09,704 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-25 09:01:09,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103492.22, 'new_value': 107846.02}, {'field': 'offline_amount', 'old_value': 209702.97, 'new_value': 215531.37}, {'field': 'total_amount', 'old_value': 313195.19, 'new_value': 323377.39}, {'field': 'order_count', 'old_value': 4147, 'new_value': 4571}]
2025-06-25 09:01:09,704 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-25 09:01:10,079 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-25 09:01:10,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 448665.0, 'new_value': 469061.0}, {'field': 'total_amount', 'old_value': 448665.0, 'new_value': 469061.0}, {'field': 'order_count', 'old_value': 454, 'new_value': 483}]
2025-06-25 09:01:10,079 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-25 09:01:10,532 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-25 09:01:10,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10680.07, 'new_value': 15792.41}, {'field': 'offline_amount', 'old_value': 131358.56, 'new_value': 131744.75}, {'field': 'total_amount', 'old_value': 142038.63, 'new_value': 147537.16}, {'field': 'order_count', 'old_value': 6833, 'new_value': 7199}]
2025-06-25 09:01:10,532 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-25 09:01:11,032 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-25 09:01:11,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52169.64, 'new_value': 55075.98}, {'field': 'offline_amount', 'old_value': 34659.14, 'new_value': 35951.88}, {'field': 'total_amount', 'old_value': 86828.78, 'new_value': 91027.86}, {'field': 'order_count', 'old_value': 5145, 'new_value': 5390}]
2025-06-25 09:01:11,032 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-25 09:01:11,454 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-25 09:01:11,454 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81505.5, 'new_value': 85111.01}, {'field': 'offline_amount', 'old_value': 84738.44, 'new_value': 87919.67}, {'field': 'total_amount', 'old_value': 166243.94, 'new_value': 173030.68}, {'field': 'order_count', 'old_value': 6874, 'new_value': 7148}]
2025-06-25 09:01:11,454 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-25 09:01:11,938 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-25 09:01:11,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 227004.0, 'new_value': 237948.0}, {'field': 'total_amount', 'old_value': 227004.0, 'new_value': 237948.0}, {'field': 'order_count', 'old_value': 18917, 'new_value': 19829}]
2025-06-25 09:01:11,938 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-25 09:01:12,360 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-25 09:01:12,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 240619.19, 'new_value': 247003.24}, {'field': 'offline_amount', 'old_value': 631048.96, 'new_value': 652938.21}, {'field': 'total_amount', 'old_value': 871668.15, 'new_value': 899941.45}, {'field': 'order_count', 'old_value': 5465, 'new_value': 5662}]
2025-06-25 09:01:12,360 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-25 09:01:12,844 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-25 09:01:12,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201914.64, 'new_value': 207634.69}, {'field': 'total_amount', 'old_value': 201914.64, 'new_value': 207634.69}, {'field': 'order_count', 'old_value': 722, 'new_value': 744}]
2025-06-25 09:01:12,844 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-25 09:01:13,297 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-25 09:01:13,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49220.0, 'new_value': 50300.0}, {'field': 'total_amount', 'old_value': 51832.0, 'new_value': 52912.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 125}]
2025-06-25 09:01:13,297 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-25 09:01:13,735 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-25 09:01:13,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 318925.99, 'new_value': 328141.98}, {'field': 'total_amount', 'old_value': 318925.99, 'new_value': 328141.98}, {'field': 'order_count', 'old_value': 1094, 'new_value': 1125}]
2025-06-25 09:01:13,735 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-25 09:01:14,188 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-25 09:01:14,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54745.5, 'new_value': 56092.3}, {'field': 'total_amount', 'old_value': 55482.4, 'new_value': 56829.2}, {'field': 'order_count', 'old_value': 399, 'new_value': 409}]
2025-06-25 09:01:14,188 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-25 09:01:14,610 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-25 09:01:14,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146401.0, 'new_value': 154197.0}, {'field': 'total_amount', 'old_value': 155020.0, 'new_value': 162816.0}, {'field': 'order_count', 'old_value': 11236, 'new_value': 11915}]
2025-06-25 09:01:14,610 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-25 09:01:15,079 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-25 09:01:15,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238399.52, 'new_value': 240079.52}, {'field': 'offline_amount', 'old_value': 221253.79, 'new_value': 221298.79}, {'field': 'total_amount', 'old_value': 459653.31, 'new_value': 461378.31}, {'field': 'order_count', 'old_value': 3043, 'new_value': 3063}]
2025-06-25 09:01:15,079 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-25 09:01:15,485 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-25 09:01:15,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8039.0, 'new_value': 8409.0}, {'field': 'total_amount', 'old_value': 10422.0, 'new_value': 10792.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 104}]
2025-06-25 09:01:15,485 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-25 09:01:15,907 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-25 09:01:15,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138650.0, 'new_value': 144070.0}, {'field': 'offline_amount', 'old_value': 91548.0, 'new_value': 99030.0}, {'field': 'total_amount', 'old_value': 230198.0, 'new_value': 243100.0}, {'field': 'order_count', 'old_value': 3345, 'new_value': 3481}]
2025-06-25 09:01:15,907 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-25 09:01:16,360 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-25 09:01:16,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23772.94, 'new_value': 25758.94}, {'field': 'offline_amount', 'old_value': 29563.22, 'new_value': 31083.46}, {'field': 'total_amount', 'old_value': 53336.16, 'new_value': 56842.4}, {'field': 'order_count', 'old_value': 6681, 'new_value': 6690}]
2025-06-25 09:01:16,360 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-25 09:01:16,813 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-25 09:01:16,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125987.9, 'new_value': 133664.5}, {'field': 'total_amount', 'old_value': 290560.5, 'new_value': 298237.1}, {'field': 'order_count', 'old_value': 7813, 'new_value': 8051}]
2025-06-25 09:01:16,813 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-25 09:01:17,266 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-25 09:01:17,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 503458.5, 'new_value': 521829.55}, {'field': 'total_amount', 'old_value': 503458.5, 'new_value': 521829.55}, {'field': 'order_count', 'old_value': 1956, 'new_value': 2001}]
2025-06-25 09:01:17,266 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-25 09:01:17,766 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-25 09:01:17,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100940.0, 'new_value': 103762.0}, {'field': 'total_amount', 'old_value': 100940.0, 'new_value': 103762.0}, {'field': 'order_count', 'old_value': 251, 'new_value': 261}]
2025-06-25 09:01:17,766 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-25 09:01:18,594 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-25 09:01:18,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 727291.0, 'new_value': 763109.0}, {'field': 'total_amount', 'old_value': 727291.0, 'new_value': 763109.0}, {'field': 'order_count', 'old_value': 923, 'new_value': 970}]
2025-06-25 09:01:18,594 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-25 09:01:19,094 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-25 09:01:19,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88474.4, 'new_value': 91217.9}, {'field': 'total_amount', 'old_value': 88474.4, 'new_value': 91217.9}, {'field': 'order_count', 'old_value': 398, 'new_value': 413}]
2025-06-25 09:01:19,094 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-25 09:01:19,579 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-25 09:01:19,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36462.97, 'new_value': 38208.95}, {'field': 'offline_amount', 'old_value': 42005.33, 'new_value': 43845.8}, {'field': 'total_amount', 'old_value': 78468.3, 'new_value': 82054.75}, {'field': 'order_count', 'old_value': 4067, 'new_value': 4260}]
2025-06-25 09:01:19,579 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-25 09:01:20,094 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-25 09:01:20,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129139.9, 'new_value': 132774.5}, {'field': 'total_amount', 'old_value': 129139.9, 'new_value': 132774.5}, {'field': 'order_count', 'old_value': 621, 'new_value': 640}]
2025-06-25 09:01:20,094 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-25 09:01:20,594 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-25 09:01:20,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42981.5, 'new_value': 45831.5}, {'field': 'total_amount', 'old_value': 42981.5, 'new_value': 45831.5}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-06-25 09:01:20,594 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-25 09:01:21,032 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-25 09:01:21,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15987.57, 'new_value': 16641.57}, {'field': 'offline_amount', 'old_value': 139887.0, 'new_value': 140247.0}, {'field': 'total_amount', 'old_value': 155874.57, 'new_value': 156888.57}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-06-25 09:01:21,032 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-25 09:01:21,469 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-25 09:01:21,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49473.0, 'new_value': 49826.0}, {'field': 'total_amount', 'old_value': 49473.0, 'new_value': 49826.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-06-25 09:01:21,469 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-25 09:01:21,938 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-25 09:01:21,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152790.1, 'new_value': 155814.6}, {'field': 'total_amount', 'old_value': 152790.1, 'new_value': 155814.6}, {'field': 'order_count', 'old_value': 4978, 'new_value': 5068}]
2025-06-25 09:01:21,938 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-25 09:01:22,469 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-25 09:01:22,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202764.07, 'new_value': 212834.07}, {'field': 'total_amount', 'old_value': 213391.07, 'new_value': 223461.07}, {'field': 'order_count', 'old_value': 899, 'new_value': 938}]
2025-06-25 09:01:22,469 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-25 09:01:22,907 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-25 09:01:22,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6145.63, 'new_value': 6314.13}, {'field': 'offline_amount', 'old_value': 15123.85, 'new_value': 16282.12}, {'field': 'total_amount', 'old_value': 21269.48, 'new_value': 22596.25}, {'field': 'order_count', 'old_value': 219, 'new_value': 237}]
2025-06-25 09:01:22,907 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-25 09:01:23,313 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-25 09:01:23,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197137.13, 'new_value': 199610.13}, {'field': 'offline_amount', 'old_value': 16232.3, 'new_value': 16238.3}, {'field': 'total_amount', 'old_value': 213369.43, 'new_value': 215848.43}, {'field': 'order_count', 'old_value': 14600, 'new_value': 14834}]
2025-06-25 09:01:23,313 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-25 09:01:23,719 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-25 09:01:23,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269837.56, 'new_value': 283283.35}, {'field': 'total_amount', 'old_value': 333943.49, 'new_value': 347389.28}, {'field': 'order_count', 'old_value': 4695, 'new_value': 4857}]
2025-06-25 09:01:23,719 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-25 09:01:24,172 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-25 09:01:24,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315788.0, 'new_value': 325878.0}, {'field': 'total_amount', 'old_value': 329346.0, 'new_value': 339436.0}, {'field': 'order_count', 'old_value': 275, 'new_value': 286}]
2025-06-25 09:01:24,172 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-25 09:01:24,594 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-25 09:01:24,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1748.0, 'new_value': 4358.5}, {'field': 'total_amount', 'old_value': 57720.01, 'new_value': 60330.51}, {'field': 'order_count', 'old_value': 361, 'new_value': 379}]
2025-06-25 09:01:24,594 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-25 09:01:24,985 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-25 09:01:24,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65472.0, 'new_value': 68435.0}, {'field': 'total_amount', 'old_value': 65472.0, 'new_value': 68435.0}, {'field': 'order_count', 'old_value': 2923, 'new_value': 3047}]
2025-06-25 09:01:24,985 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-25 09:01:25,532 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-25 09:01:25,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63947.0, 'new_value': 64935.0}, {'field': 'offline_amount', 'old_value': 287765.0, 'new_value': 294840.0}, {'field': 'total_amount', 'old_value': 351712.0, 'new_value': 359775.0}, {'field': 'order_count', 'old_value': 1334, 'new_value': 1393}]
2025-06-25 09:01:25,532 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-25 09:01:26,079 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-25 09:01:26,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238859.02, 'new_value': 240616.56}, {'field': 'total_amount', 'old_value': 238859.02, 'new_value': 240616.56}, {'field': 'order_count', 'old_value': 1057, 'new_value': 1069}]
2025-06-25 09:01:26,079 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-25 09:01:26,500 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-25 09:01:26,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93105.95, 'new_value': 96793.26}, {'field': 'offline_amount', 'old_value': 120290.59, 'new_value': 127103.64}, {'field': 'total_amount', 'old_value': 213396.54, 'new_value': 223896.9}, {'field': 'order_count', 'old_value': 8619, 'new_value': 8890}]
2025-06-25 09:01:26,500 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-25 09:01:26,860 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-25 09:01:26,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169430.31, 'new_value': 176177.83}, {'field': 'total_amount', 'old_value': 169430.31, 'new_value': 176177.83}, {'field': 'order_count', 'old_value': 715, 'new_value': 743}]
2025-06-25 09:01:26,860 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-25 09:01:27,297 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-25 09:01:27,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183900.43, 'new_value': 193876.43}, {'field': 'total_amount', 'old_value': 265615.63, 'new_value': 275591.63}, {'field': 'order_count', 'old_value': 418, 'new_value': 438}]
2025-06-25 09:01:27,297 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-25 09:01:27,672 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-25 09:01:27,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2575.0, 'new_value': 2675.0}, {'field': 'offline_amount', 'old_value': 29185.0, 'new_value': 29685.0}, {'field': 'total_amount', 'old_value': 31760.0, 'new_value': 32360.0}, {'field': 'order_count', 'old_value': 400, 'new_value': 408}]
2025-06-25 09:01:27,672 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-25 09:01:28,157 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-25 09:01:28,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130830.0, 'new_value': 133181.0}, {'field': 'offline_amount', 'old_value': 157211.0, 'new_value': 161247.0}, {'field': 'total_amount', 'old_value': 288041.0, 'new_value': 294428.0}, {'field': 'order_count', 'old_value': 179481, 'new_value': 179512}]
2025-06-25 09:01:28,157 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-25 09:01:28,625 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-25 09:01:28,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62058.5, 'new_value': 66424.24}, {'field': 'offline_amount', 'old_value': 65337.58, 'new_value': 69149.01}, {'field': 'total_amount', 'old_value': 127396.08, 'new_value': 135573.25}, {'field': 'order_count', 'old_value': 6676, 'new_value': 7094}]
2025-06-25 09:01:28,625 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-25 09:01:29,110 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-25 09:01:29,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 570615.0, 'new_value': 579314.0}, {'field': 'total_amount', 'old_value': 570615.0, 'new_value': 579314.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-06-25 09:01:29,110 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-25 09:01:29,532 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-25 09:01:29,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156910.0, 'new_value': 159921.0}, {'field': 'offline_amount', 'old_value': 1065373.0, 'new_value': 1111462.0}, {'field': 'total_amount', 'old_value': 1222283.0, 'new_value': 1271383.0}, {'field': 'order_count', 'old_value': 31525, 'new_value': 32883}]
2025-06-25 09:01:29,532 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-25 09:01:30,000 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-25 09:01:30,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 276067.0, 'new_value': 290113.0}, {'field': 'total_amount', 'old_value': 276067.0, 'new_value': 290113.0}, {'field': 'order_count', 'old_value': 6379, 'new_value': 6693}]
2025-06-25 09:01:30,000 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-25 09:01:30,485 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-25 09:01:30,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7571.5, 'new_value': 8276.5}, {'field': 'total_amount', 'old_value': 13994.5, 'new_value': 14699.5}, {'field': 'order_count', 'old_value': 122, 'new_value': 126}]
2025-06-25 09:01:30,485 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-25 09:01:30,891 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-25 09:01:30,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17519.0, 'new_value': 17618.0}, {'field': 'total_amount', 'old_value': 17519.0, 'new_value': 17618.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-06-25 09:01:30,891 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-25 09:01:31,344 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-25 09:01:31,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42208.55, 'new_value': 43133.75}, {'field': 'total_amount', 'old_value': 42208.55, 'new_value': 43133.75}, {'field': 'order_count', 'old_value': 1094, 'new_value': 1137}]
2025-06-25 09:01:31,344 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-25 09:01:31,844 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-25 09:01:31,844 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 227539.9, 'new_value': 237797.9}, {'field': 'offline_amount', 'old_value': 18767.5, 'new_value': 20221.0}, {'field': 'total_amount', 'old_value': 246307.4, 'new_value': 258018.9}, {'field': 'order_count', 'old_value': 2766, 'new_value': 3010}]
2025-06-25 09:01:31,844 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-25 09:01:32,297 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-25 09:01:32,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29478.7, 'new_value': 30545.8}, {'field': 'offline_amount', 'old_value': 50416.6, 'new_value': 52945.9}, {'field': 'total_amount', 'old_value': 79895.3, 'new_value': 83491.7}, {'field': 'order_count', 'old_value': 3223, 'new_value': 3341}]
2025-06-25 09:01:32,297 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-25 09:01:32,844 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-25 09:01:32,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31352.31, 'new_value': 32113.92}, {'field': 'total_amount', 'old_value': 32118.31, 'new_value': 32879.92}, {'field': 'order_count', 'old_value': 294, 'new_value': 306}]
2025-06-25 09:01:32,844 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-25 09:01:33,360 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-25 09:01:33,360 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 101, 'new_value': 104}]
2025-06-25 09:01:33,360 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-25 09:01:33,813 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-25 09:01:33,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374119.71, 'new_value': 376947.71}, {'field': 'total_amount', 'old_value': 406523.64, 'new_value': 409351.64}, {'field': 'order_count', 'old_value': 499, 'new_value': 517}]
2025-06-25 09:01:33,813 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-25 09:01:34,266 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-25 09:01:34,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102232.0, 'new_value': 104360.0}, {'field': 'total_amount', 'old_value': 102232.0, 'new_value': 104360.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-25 09:01:34,266 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-25 09:01:34,750 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-25 09:01:34,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430052.86, 'new_value': 446004.86}, {'field': 'total_amount', 'old_value': 430052.86, 'new_value': 446004.86}, {'field': 'order_count', 'old_value': 2926, 'new_value': 3051}]
2025-06-25 09:01:34,750 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-25 09:01:35,157 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-25 09:01:35,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40292.9, 'new_value': 40776.9}, {'field': 'total_amount', 'old_value': 47024.55, 'new_value': 47508.55}, {'field': 'order_count', 'old_value': 355, 'new_value': 361}]
2025-06-25 09:01:35,157 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-25 09:01:35,625 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-25 09:01:35,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146325.32, 'new_value': 152739.09}, {'field': 'offline_amount', 'old_value': 42556.64, 'new_value': 48228.14}, {'field': 'total_amount', 'old_value': 188881.96, 'new_value': 200967.23}, {'field': 'order_count', 'old_value': 811, 'new_value': 864}]
2025-06-25 09:01:35,625 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-25 09:01:36,063 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-25 09:01:36,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111429.0, 'new_value': 114646.0}, {'field': 'total_amount', 'old_value': 140487.1, 'new_value': 143704.1}, {'field': 'order_count', 'old_value': 1713, 'new_value': 1763}]
2025-06-25 09:01:36,063 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-25 09:01:36,563 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-25 09:01:36,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52895.0, 'new_value': 53282.0}, {'field': 'total_amount', 'old_value': 52895.0, 'new_value': 53282.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-25 09:01:36,563 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-25 09:01:37,063 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-25 09:01:37,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119671.0, 'new_value': 123067.0}, {'field': 'total_amount', 'old_value': 119671.0, 'new_value': 123067.0}, {'field': 'order_count', 'old_value': 504, 'new_value': 521}]
2025-06-25 09:01:37,063 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-25 09:01:37,469 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-25 09:01:37,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63600.0, 'new_value': 68105.0}, {'field': 'total_amount', 'old_value': 67045.0, 'new_value': 71550.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 270}]
2025-06-25 09:01:37,469 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-25 09:01:37,922 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-25 09:01:37,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43471.81, 'new_value': 45420.07}, {'field': 'total_amount', 'old_value': 43471.81, 'new_value': 45420.07}, {'field': 'order_count', 'old_value': 5505, 'new_value': 5752}]
2025-06-25 09:01:37,922 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-25 09:01:38,391 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-25 09:01:38,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 292131.52, 'new_value': 303180.56}, {'field': 'total_amount', 'old_value': 292131.52, 'new_value': 303180.56}, {'field': 'order_count', 'old_value': 815, 'new_value': 844}]
2025-06-25 09:01:38,391 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-25 09:01:38,875 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-25 09:01:38,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104912.0, 'new_value': 109536.0}, {'field': 'total_amount', 'old_value': 104912.0, 'new_value': 109536.0}, {'field': 'order_count', 'old_value': 5952, 'new_value': 6217}]
2025-06-25 09:01:38,875 - INFO - 日期 2025-06 处理完成 - 更新: 133 条，插入: 0 条，错误: 0 条
2025-06-25 09:01:38,875 - INFO - 数据同步完成！更新: 133 条，插入: 0 条，错误: 0 条
2025-06-25 09:01:38,875 - INFO - =================同步完成====================
2025-06-25 12:00:02,862 - INFO - =================使用默认全量同步=============
2025-06-25 12:00:04,643 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-25 12:00:04,643 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 12:00:04,675 - INFO - 开始处理日期: 2025-01
2025-06-25 12:00:04,690 - INFO - Request Parameters - Page 1:
2025-06-25 12:00:04,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:04,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:05,800 - INFO - Response - Page 1:
2025-06-25 12:00:06,003 - INFO - 第 1 页获取到 100 条记录
2025-06-25 12:00:06,003 - INFO - Request Parameters - Page 2:
2025-06-25 12:00:06,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:06,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:06,878 - INFO - Response - Page 2:
2025-06-25 12:00:07,081 - INFO - 第 2 页获取到 100 条记录
2025-06-25 12:00:07,081 - INFO - Request Parameters - Page 3:
2025-06-25 12:00:07,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:07,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:07,612 - INFO - Response - Page 3:
2025-06-25 12:00:07,815 - INFO - 第 3 页获取到 100 条记录
2025-06-25 12:00:07,815 - INFO - Request Parameters - Page 4:
2025-06-25 12:00:07,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:07,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:08,284 - INFO - Response - Page 4:
2025-06-25 12:00:08,487 - INFO - 第 4 页获取到 100 条记录
2025-06-25 12:00:08,487 - INFO - Request Parameters - Page 5:
2025-06-25 12:00:08,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:08,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:08,971 - INFO - Response - Page 5:
2025-06-25 12:00:09,174 - INFO - 第 5 页获取到 100 条记录
2025-06-25 12:00:09,174 - INFO - Request Parameters - Page 6:
2025-06-25 12:00:09,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:09,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:09,674 - INFO - Response - Page 6:
2025-06-25 12:00:09,878 - INFO - 第 6 页获取到 100 条记录
2025-06-25 12:00:09,878 - INFO - Request Parameters - Page 7:
2025-06-25 12:00:09,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:09,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:10,346 - INFO - Response - Page 7:
2025-06-25 12:00:10,549 - INFO - 第 7 页获取到 82 条记录
2025-06-25 12:00:10,549 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 12:00:10,549 - INFO - 获取到 682 条表单数据
2025-06-25 12:00:10,549 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 12:00:10,565 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 12:00:10,565 - INFO - 开始处理日期: 2025-02
2025-06-25 12:00:10,565 - INFO - Request Parameters - Page 1:
2025-06-25 12:00:10,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:10,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:11,112 - INFO - Response - Page 1:
2025-06-25 12:00:11,315 - INFO - 第 1 页获取到 100 条记录
2025-06-25 12:00:11,315 - INFO - Request Parameters - Page 2:
2025-06-25 12:00:11,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:11,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:11,940 - INFO - Response - Page 2:
2025-06-25 12:00:12,143 - INFO - 第 2 页获取到 100 条记录
2025-06-25 12:00:12,143 - INFO - Request Parameters - Page 3:
2025-06-25 12:00:12,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:12,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:12,643 - INFO - Response - Page 3:
2025-06-25 12:00:12,846 - INFO - 第 3 页获取到 100 条记录
2025-06-25 12:00:12,846 - INFO - Request Parameters - Page 4:
2025-06-25 12:00:12,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:12,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:13,346 - INFO - Response - Page 4:
2025-06-25 12:00:13,549 - INFO - 第 4 页获取到 100 条记录
2025-06-25 12:00:13,549 - INFO - Request Parameters - Page 5:
2025-06-25 12:00:13,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:13,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:14,034 - INFO - Response - Page 5:
2025-06-25 12:00:14,237 - INFO - 第 5 页获取到 100 条记录
2025-06-25 12:00:14,237 - INFO - Request Parameters - Page 6:
2025-06-25 12:00:14,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:14,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:14,815 - INFO - Response - Page 6:
2025-06-25 12:00:15,018 - INFO - 第 6 页获取到 100 条记录
2025-06-25 12:00:15,018 - INFO - Request Parameters - Page 7:
2025-06-25 12:00:15,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:15,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:15,565 - INFO - Response - Page 7:
2025-06-25 12:00:15,768 - INFO - 第 7 页获取到 70 条记录
2025-06-25 12:00:15,768 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 12:00:15,768 - INFO - 获取到 670 条表单数据
2025-06-25 12:00:15,768 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 12:00:15,784 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 12:00:15,784 - INFO - 开始处理日期: 2025-03
2025-06-25 12:00:15,784 - INFO - Request Parameters - Page 1:
2025-06-25 12:00:15,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:15,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:16,378 - INFO - Response - Page 1:
2025-06-25 12:00:16,581 - INFO - 第 1 页获取到 100 条记录
2025-06-25 12:00:16,581 - INFO - Request Parameters - Page 2:
2025-06-25 12:00:16,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:16,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:17,096 - INFO - Response - Page 2:
2025-06-25 12:00:17,299 - INFO - 第 2 页获取到 100 条记录
2025-06-25 12:00:17,299 - INFO - Request Parameters - Page 3:
2025-06-25 12:00:17,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:17,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:17,893 - INFO - Response - Page 3:
2025-06-25 12:00:18,096 - INFO - 第 3 页获取到 100 条记录
2025-06-25 12:00:18,096 - INFO - Request Parameters - Page 4:
2025-06-25 12:00:18,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:18,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:18,612 - INFO - Response - Page 4:
2025-06-25 12:00:18,815 - INFO - 第 4 页获取到 100 条记录
2025-06-25 12:00:18,815 - INFO - Request Parameters - Page 5:
2025-06-25 12:00:18,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:18,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:19,362 - INFO - Response - Page 5:
2025-06-25 12:00:19,565 - INFO - 第 5 页获取到 100 条记录
2025-06-25 12:00:19,565 - INFO - Request Parameters - Page 6:
2025-06-25 12:00:19,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:19,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:20,065 - INFO - Response - Page 6:
2025-06-25 12:00:20,268 - INFO - 第 6 页获取到 100 条记录
2025-06-25 12:00:20,268 - INFO - Request Parameters - Page 7:
2025-06-25 12:00:20,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:20,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:20,846 - INFO - Response - Page 7:
2025-06-25 12:00:21,049 - INFO - 第 7 页获取到 61 条记录
2025-06-25 12:00:21,049 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 12:00:21,049 - INFO - 获取到 661 条表单数据
2025-06-25 12:00:21,049 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 12:00:21,065 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 12:00:21,065 - INFO - 开始处理日期: 2025-04
2025-06-25 12:00:21,065 - INFO - Request Parameters - Page 1:
2025-06-25 12:00:21,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:21,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:21,690 - INFO - Response - Page 1:
2025-06-25 12:00:21,893 - INFO - 第 1 页获取到 100 条记录
2025-06-25 12:00:21,893 - INFO - Request Parameters - Page 2:
2025-06-25 12:00:21,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:21,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:22,378 - INFO - Response - Page 2:
2025-06-25 12:00:22,581 - INFO - 第 2 页获取到 100 条记录
2025-06-25 12:00:22,581 - INFO - Request Parameters - Page 3:
2025-06-25 12:00:22,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:22,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:23,174 - INFO - Response - Page 3:
2025-06-25 12:00:23,378 - INFO - 第 3 页获取到 100 条记录
2025-06-25 12:00:23,378 - INFO - Request Parameters - Page 4:
2025-06-25 12:00:23,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:23,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:23,878 - INFO - Response - Page 4:
2025-06-25 12:00:24,081 - INFO - 第 4 页获取到 100 条记录
2025-06-25 12:00:24,081 - INFO - Request Parameters - Page 5:
2025-06-25 12:00:24,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:24,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:24,581 - INFO - Response - Page 5:
2025-06-25 12:00:24,784 - INFO - 第 5 页获取到 100 条记录
2025-06-25 12:00:24,784 - INFO - Request Parameters - Page 6:
2025-06-25 12:00:24,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:24,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:25,268 - INFO - Response - Page 6:
2025-06-25 12:00:25,471 - INFO - 第 6 页获取到 100 条记录
2025-06-25 12:00:25,471 - INFO - Request Parameters - Page 7:
2025-06-25 12:00:25,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:25,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:25,956 - INFO - Response - Page 7:
2025-06-25 12:00:26,159 - INFO - 第 7 页获取到 56 条记录
2025-06-25 12:00:26,159 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 12:00:26,159 - INFO - 获取到 656 条表单数据
2025-06-25 12:00:26,159 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 12:00:26,174 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 12:00:26,174 - INFO - 开始处理日期: 2025-05
2025-06-25 12:00:26,174 - INFO - Request Parameters - Page 1:
2025-06-25 12:00:26,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:26,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:26,768 - INFO - Response - Page 1:
2025-06-25 12:00:26,971 - INFO - 第 1 页获取到 100 条记录
2025-06-25 12:00:26,971 - INFO - Request Parameters - Page 2:
2025-06-25 12:00:26,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:26,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:27,549 - INFO - Response - Page 2:
2025-06-25 12:00:27,752 - INFO - 第 2 页获取到 100 条记录
2025-06-25 12:00:27,752 - INFO - Request Parameters - Page 3:
2025-06-25 12:00:27,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:27,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:28,237 - INFO - Response - Page 3:
2025-06-25 12:00:28,440 - INFO - 第 3 页获取到 100 条记录
2025-06-25 12:00:28,440 - INFO - Request Parameters - Page 4:
2025-06-25 12:00:28,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:28,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:28,924 - INFO - Response - Page 4:
2025-06-25 12:00:29,127 - INFO - 第 4 页获取到 100 条记录
2025-06-25 12:00:29,127 - INFO - Request Parameters - Page 5:
2025-06-25 12:00:29,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:29,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:29,627 - INFO - Response - Page 5:
2025-06-25 12:00:29,831 - INFO - 第 5 页获取到 100 条记录
2025-06-25 12:00:29,831 - INFO - Request Parameters - Page 6:
2025-06-25 12:00:29,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:29,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:30,299 - INFO - Response - Page 6:
2025-06-25 12:00:30,502 - INFO - 第 6 页获取到 100 条记录
2025-06-25 12:00:30,502 - INFO - Request Parameters - Page 7:
2025-06-25 12:00:30,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:30,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:30,971 - INFO - Response - Page 7:
2025-06-25 12:00:31,190 - INFO - 第 7 页获取到 65 条记录
2025-06-25 12:00:31,190 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 12:00:31,190 - INFO - 获取到 665 条表单数据
2025-06-25 12:00:31,206 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 12:00:31,206 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 12:00:31,206 - INFO - 开始处理日期: 2025-06
2025-06-25 12:00:31,206 - INFO - Request Parameters - Page 1:
2025-06-25 12:00:31,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:31,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:31,752 - INFO - Response - Page 1:
2025-06-25 12:00:31,956 - INFO - 第 1 页获取到 100 条记录
2025-06-25 12:00:31,956 - INFO - Request Parameters - Page 2:
2025-06-25 12:00:31,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:31,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:32,502 - INFO - Response - Page 2:
2025-06-25 12:00:32,706 - INFO - 第 2 页获取到 100 条记录
2025-06-25 12:00:32,706 - INFO - Request Parameters - Page 3:
2025-06-25 12:00:32,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:32,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:33,252 - INFO - Response - Page 3:
2025-06-25 12:00:33,456 - INFO - 第 3 页获取到 100 条记录
2025-06-25 12:00:33,456 - INFO - Request Parameters - Page 4:
2025-06-25 12:00:33,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:33,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:33,971 - INFO - Response - Page 4:
2025-06-25 12:00:34,174 - INFO - 第 4 页获取到 100 条记录
2025-06-25 12:00:34,174 - INFO - Request Parameters - Page 5:
2025-06-25 12:00:34,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:34,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:34,643 - INFO - Response - Page 5:
2025-06-25 12:00:34,846 - INFO - 第 5 页获取到 100 条记录
2025-06-25 12:00:34,846 - INFO - Request Parameters - Page 6:
2025-06-25 12:00:34,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:34,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:35,409 - INFO - Response - Page 6:
2025-06-25 12:00:35,612 - INFO - 第 6 页获取到 100 条记录
2025-06-25 12:00:35,612 - INFO - Request Parameters - Page 7:
2025-06-25 12:00:35,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 12:00:35,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 12:00:35,987 - INFO - Response - Page 7:
2025-06-25 12:00:36,190 - INFO - 第 7 页获取到 25 条记录
2025-06-25 12:00:36,190 - INFO - 查询完成，共获取到 625 条记录
2025-06-25 12:00:36,190 - INFO - 获取到 625 条表单数据
2025-06-25 12:00:36,190 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-25 12:00:36,190 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-25 12:00:36,815 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-25 12:00:36,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158134.0, 'new_value': 160172.0}, {'field': 'total_amount', 'old_value': 158134.0, 'new_value': 160172.0}, {'field': 'order_count', 'old_value': 2600, 'new_value': 2645}]
2025-06-25 12:00:36,815 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-25 12:00:37,315 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-25 12:00:37,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129512.18, 'new_value': 132723.28}, {'field': 'total_amount', 'old_value': 129512.18, 'new_value': 132723.28}, {'field': 'order_count', 'old_value': 3375, 'new_value': 3464}]
2025-06-25 12:00:37,315 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-25 12:00:37,799 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-25 12:00:37,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75821.94, 'new_value': 79450.73}, {'field': 'offline_amount', 'old_value': 842585.75, 'new_value': 883842.07}, {'field': 'total_amount', 'old_value': 914415.2, 'new_value': 959300.31}, {'field': 'order_count', 'old_value': 4447, 'new_value': 4648}]
2025-06-25 12:00:37,799 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-25 12:00:38,315 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-25 12:00:38,315 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24847.51, 'new_value': 25645.4}, {'field': 'offline_amount', 'old_value': 14167.22, 'new_value': 14606.22}, {'field': 'total_amount', 'old_value': 39014.73, 'new_value': 40251.62}, {'field': 'order_count', 'old_value': 1620, 'new_value': 1675}]
2025-06-25 12:00:38,315 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-25 12:00:38,815 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-25 12:00:38,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25359.82, 'new_value': 25959.82}, {'field': 'total_amount', 'old_value': 25359.82, 'new_value': 25959.82}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-06-25 12:00:38,815 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-25 12:00:39,237 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-25 12:00:39,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46720.0, 'new_value': 48760.0}, {'field': 'total_amount', 'old_value': 55920.0, 'new_value': 57960.0}, {'field': 'order_count', 'old_value': 612, 'new_value': 639}]
2025-06-25 12:00:39,237 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-25 12:00:39,674 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-25 12:00:39,674 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46541.0, 'new_value': 46620.8}, {'field': 'total_amount', 'old_value': 46541.0, 'new_value': 46620.8}, {'field': 'order_count', 'old_value': 440, 'new_value': 442}]
2025-06-25 12:00:39,674 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-25 12:00:40,127 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-25 12:00:40,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211541.0, 'new_value': 221476.0}, {'field': 'total_amount', 'old_value': 211541.0, 'new_value': 221476.0}, {'field': 'order_count', 'old_value': 1101, 'new_value': 1148}]
2025-06-25 12:00:40,127 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-25 12:00:40,596 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-25 12:00:40,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90952.27, 'new_value': 119173.59}, {'field': 'total_amount', 'old_value': 90952.27, 'new_value': 119173.59}, {'field': 'order_count', 'old_value': 195, 'new_value': 221}]
2025-06-25 12:00:40,596 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-25 12:00:41,049 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-25 12:00:41,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1217.9, 'new_value': 1350.9}, {'field': 'offline_amount', 'old_value': 10796.5, 'new_value': 10909.5}, {'field': 'total_amount', 'old_value': 12014.4, 'new_value': 12260.4}, {'field': 'order_count', 'old_value': 131, 'new_value': 134}]
2025-06-25 12:00:41,049 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-25 12:00:41,549 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-25 12:00:41,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26174.71, 'new_value': 26774.71}, {'field': 'offline_amount', 'old_value': 141357.71, 'new_value': 141423.71}, {'field': 'total_amount', 'old_value': 167532.42, 'new_value': 168198.42}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-25 12:00:41,549 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-25 12:00:42,018 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-25 12:00:42,018 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95429.51, 'new_value': 97997.21}, {'field': 'total_amount', 'old_value': 96850.18, 'new_value': 99417.88}, {'field': 'order_count', 'old_value': 2282, 'new_value': 2344}]
2025-06-25 12:00:42,018 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-25 12:00:43,409 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-25 12:00:43,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109174.23, 'new_value': 111209.33}, {'field': 'offline_amount', 'old_value': 1040194.66, 'new_value': 1089144.08}, {'field': 'total_amount', 'old_value': 1149368.89, 'new_value': 1200353.41}, {'field': 'order_count', 'old_value': 9679, 'new_value': 10104}]
2025-06-25 12:00:43,409 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-25 12:00:43,971 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-25 12:00:43,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235984.0, 'new_value': 241735.0}, {'field': 'total_amount', 'old_value': 244846.0, 'new_value': 250597.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 63}]
2025-06-25 12:00:43,971 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-25 12:00:44,471 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-25 12:00:44,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6198.0, 'new_value': 6434.0}, {'field': 'offline_amount', 'old_value': 53826.0, 'new_value': 54616.0}, {'field': 'total_amount', 'old_value': 60024.0, 'new_value': 61050.0}, {'field': 'order_count', 'old_value': 489, 'new_value': 503}]
2025-06-25 12:00:44,471 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-25 12:00:44,909 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-25 12:00:44,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5292.0, 'new_value': 5688.0}, {'field': 'total_amount', 'old_value': 5292.0, 'new_value': 5688.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-25 12:00:44,909 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-25 12:00:45,346 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-25 12:00:45,346 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8905.82, 'new_value': 9313.7}, {'field': 'offline_amount', 'old_value': 175428.52, 'new_value': 188871.52}, {'field': 'total_amount', 'old_value': 184334.34, 'new_value': 198185.22}, {'field': 'order_count', 'old_value': 1081, 'new_value': 1131}]
2025-06-25 12:00:45,346 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-25 12:00:45,799 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-25 12:00:45,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 179498.61, 'new_value': 187564.88}, {'field': 'offline_amount', 'old_value': 358476.06, 'new_value': 366335.65}, {'field': 'total_amount', 'old_value': 537974.67, 'new_value': 553900.53}, {'field': 'order_count', 'old_value': 4049, 'new_value': 4197}]
2025-06-25 12:00:45,799 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-25 12:00:46,221 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-25 12:00:46,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3485.0, 'new_value': 3545.0}, {'field': 'total_amount', 'old_value': 26831.01, 'new_value': 26891.01}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-06-25 12:00:46,221 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-25 12:00:46,752 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-25 12:00:46,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20251.3, 'new_value': 20921.3}, {'field': 'total_amount', 'old_value': 20251.3, 'new_value': 20921.3}, {'field': 'order_count', 'old_value': 107, 'new_value': 112}]
2025-06-25 12:00:46,752 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-25 12:00:47,221 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-25 12:00:47,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89443.0, 'new_value': 89675.0}, {'field': 'total_amount', 'old_value': 89443.0, 'new_value': 89675.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 320}]
2025-06-25 12:00:47,221 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-25 12:00:47,752 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-25 12:00:47,752 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-25 12:00:47,752 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-25 12:00:48,221 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-25 12:00:48,221 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95257.88, 'new_value': 99777.38}, {'field': 'offline_amount', 'old_value': 463791.69, 'new_value': 481718.2}, {'field': 'total_amount', 'old_value': 559049.57, 'new_value': 581495.58}, {'field': 'order_count', 'old_value': 1560, 'new_value': 1626}]
2025-06-25 12:00:48,221 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-25 12:00:48,737 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-25 12:00:48,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42796.0, 'new_value': 43180.0}, {'field': 'total_amount', 'old_value': 42796.0, 'new_value': 43180.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-06-25 12:00:48,737 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-25 12:00:49,315 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-25 12:00:49,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 619953.32, 'new_value': 648753.32}, {'field': 'total_amount', 'old_value': 662215.04, 'new_value': 691015.04}, {'field': 'order_count', 'old_value': 2791, 'new_value': 2792}]
2025-06-25 12:00:49,330 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-25 12:00:49,862 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-25 12:00:49,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52583.02, 'new_value': 54388.92}, {'field': 'total_amount', 'old_value': 59364.27, 'new_value': 61170.17}, {'field': 'order_count', 'old_value': 213, 'new_value': 224}]
2025-06-25 12:00:49,862 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-25 12:00:50,299 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-25 12:00:50,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23337.0, 'new_value': 23548.0}, {'field': 'total_amount', 'old_value': 23337.0, 'new_value': 23548.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 93}]
2025-06-25 12:00:50,299 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-25 12:00:50,784 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-25 12:00:50,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11176.27, 'new_value': 11837.77}, {'field': 'offline_amount', 'old_value': 8780.9, 'new_value': 8909.9}, {'field': 'total_amount', 'old_value': 19957.17, 'new_value': 20747.67}, {'field': 'order_count', 'old_value': 91, 'new_value': 96}]
2025-06-25 12:00:50,784 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-25 12:00:51,205 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-25 12:00:51,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59621.0, 'new_value': 59801.0}, {'field': 'total_amount', 'old_value': 67759.95, 'new_value': 67939.95}, {'field': 'order_count', 'old_value': 1552, 'new_value': 1565}]
2025-06-25 12:00:51,205 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-25 12:00:51,627 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-25 12:00:51,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21941.0, 'new_value': 23088.0}, {'field': 'total_amount', 'old_value': 21941.0, 'new_value': 23088.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 116}]
2025-06-25 12:00:51,627 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-25 12:00:52,065 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-25 12:00:52,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96740.7, 'new_value': 99176.6}, {'field': 'total_amount', 'old_value': 121035.9, 'new_value': 123471.8}, {'field': 'order_count', 'old_value': 148, 'new_value': 152}]
2025-06-25 12:00:52,065 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-25 12:00:52,549 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-25 12:00:52,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22695.0, 'new_value': 23164.0}, {'field': 'total_amount', 'old_value': 22755.0, 'new_value': 23224.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-06-25 12:00:52,549 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-25 12:00:53,018 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-25 12:00:53,018 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20688.9, 'new_value': 21606.9}, {'field': 'total_amount', 'old_value': 20688.9, 'new_value': 21606.9}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-06-25 12:00:53,018 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-25 12:00:53,440 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-25 12:00:53,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286313.42, 'new_value': 298028.85}, {'field': 'total_amount', 'old_value': 286313.42, 'new_value': 298028.85}, {'field': 'order_count', 'old_value': 8353, 'new_value': 8722}]
2025-06-25 12:00:53,440 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-25 12:00:53,940 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-25 12:00:53,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81673.5, 'new_value': 84302.5}, {'field': 'total_amount', 'old_value': 81673.5, 'new_value': 84302.5}, {'field': 'order_count', 'old_value': 270, 'new_value': 277}]
2025-06-25 12:00:53,940 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-25 12:00:54,440 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-25 12:00:54,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9685.0, 'new_value': 10055.0}, {'field': 'total_amount', 'old_value': 9685.0, 'new_value': 10055.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-06-25 12:00:54,440 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUD
2025-06-25 12:00:54,877 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUD
2025-06-25 12:00:54,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87523.0, 'new_value': 98912.0}, {'field': 'total_amount', 'old_value': 87523.0, 'new_value': 98912.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-25 12:00:54,877 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-25 12:00:55,346 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-25 12:00:55,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129877.0, 'new_value': 134061.0}, {'field': 'total_amount', 'old_value': 261211.0, 'new_value': 265395.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 142}]
2025-06-25 12:00:55,346 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-25 12:00:55,752 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-25 12:00:55,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81397.0, 'new_value': 83702.0}, {'field': 'total_amount', 'old_value': 81397.0, 'new_value': 83702.0}, {'field': 'order_count', 'old_value': 881, 'new_value': 882}]
2025-06-25 12:00:55,752 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-25 12:00:56,252 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-25 12:00:56,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71576.24, 'new_value': 73506.15}, {'field': 'total_amount', 'old_value': 71576.24, 'new_value': 73506.15}, {'field': 'order_count', 'old_value': 2300, 'new_value': 2370}]
2025-06-25 12:00:56,252 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-25 12:00:56,830 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-25 12:00:56,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26677.52, 'new_value': 26943.99}, {'field': 'offline_amount', 'old_value': 244498.45, 'new_value': 255268.25}, {'field': 'total_amount', 'old_value': 271175.97, 'new_value': 282212.24}, {'field': 'order_count', 'old_value': 14959, 'new_value': 15578}]
2025-06-25 12:00:56,830 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-25 12:00:57,299 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-25 12:00:57,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44009.0, 'new_value': 45068.0}, {'field': 'total_amount', 'old_value': 44009.0, 'new_value': 45068.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 110}]
2025-06-25 12:00:57,299 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-25 12:00:57,768 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-25 12:00:57,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118149.93, 'new_value': 122546.0}, {'field': 'total_amount', 'old_value': 118149.93, 'new_value': 122546.0}, {'field': 'order_count', 'old_value': 3387, 'new_value': 3508}]
2025-06-25 12:00:57,768 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-25 12:00:58,174 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-25 12:00:58,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72069.15, 'new_value': 73240.15}, {'field': 'total_amount', 'old_value': 122875.05, 'new_value': 124046.05}, {'field': 'order_count', 'old_value': 99, 'new_value': 101}]
2025-06-25 12:00:58,174 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-25 12:00:58,627 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-25 12:00:58,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30826.69, 'new_value': 31460.69}, {'field': 'total_amount', 'old_value': 30826.69, 'new_value': 31460.69}, {'field': 'order_count', 'old_value': 182, 'new_value': 187}]
2025-06-25 12:00:58,627 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-25 12:00:59,096 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-25 12:00:59,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143398.0, 'new_value': 146092.0}, {'field': 'total_amount', 'old_value': 143398.0, 'new_value': 146092.0}, {'field': 'order_count', 'old_value': 548, 'new_value': 558}]
2025-06-25 12:00:59,096 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-25 12:00:59,534 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-25 12:00:59,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33798.86, 'new_value': 34803.24}, {'field': 'offline_amount', 'old_value': 25510.0, 'new_value': 26741.0}, {'field': 'total_amount', 'old_value': 59308.86, 'new_value': 61544.24}, {'field': 'order_count', 'old_value': 799, 'new_value': 827}]
2025-06-25 12:00:59,534 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-25 12:01:00,002 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-25 12:01:00,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204285.32, 'new_value': 211438.76}, {'field': 'total_amount', 'old_value': 204285.32, 'new_value': 211438.76}, {'field': 'order_count', 'old_value': 1136, 'new_value': 1179}]
2025-06-25 12:01:00,002 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-25 12:01:00,471 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-25 12:01:00,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 296796.0, 'new_value': 339584.0}, {'field': 'total_amount', 'old_value': 296796.0, 'new_value': 339584.0}]
2025-06-25 12:01:00,471 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-25 12:01:00,940 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-25 12:01:00,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91303.0, 'new_value': 94310.29}, {'field': 'offline_amount', 'old_value': 47297.51, 'new_value': 48663.9}, {'field': 'total_amount', 'old_value': 138600.51, 'new_value': 142974.19}, {'field': 'order_count', 'old_value': 7990, 'new_value': 8250}]
2025-06-25 12:01:00,940 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-25 12:01:01,377 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-25 12:01:01,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101831.3, 'new_value': 103834.7}, {'field': 'total_amount', 'old_value': 101831.3, 'new_value': 103834.7}, {'field': 'order_count', 'old_value': 253, 'new_value': 259}]
2025-06-25 12:01:01,377 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-25 12:01:01,830 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-25 12:01:01,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62668.45, 'new_value': 62954.25}, {'field': 'total_amount', 'old_value': 62668.45, 'new_value': 62954.25}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-06-25 12:01:01,830 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-25 12:01:02,252 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-25 12:01:02,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49902.0, 'new_value': 51921.9}, {'field': 'total_amount', 'old_value': 49902.0, 'new_value': 51921.9}, {'field': 'order_count', 'old_value': 313, 'new_value': 317}]
2025-06-25 12:01:02,252 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-25 12:01:02,705 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-25 12:01:02,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53560.69, 'new_value': 55961.31}, {'field': 'offline_amount', 'old_value': 72880.86, 'new_value': 75169.53}, {'field': 'total_amount', 'old_value': 126441.55, 'new_value': 131130.84}, {'field': 'order_count', 'old_value': 4946, 'new_value': 5147}]
2025-06-25 12:01:02,705 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-25 12:01:03,143 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-25 12:01:03,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83348.47, 'new_value': 85146.91}, {'field': 'total_amount', 'old_value': 83348.47, 'new_value': 85146.91}, {'field': 'order_count', 'old_value': 3099, 'new_value': 3171}]
2025-06-25 12:01:03,143 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-25 12:01:03,612 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-25 12:01:03,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 364350.48, 'new_value': 372219.58}, {'field': 'total_amount', 'old_value': 364350.48, 'new_value': 372219.58}, {'field': 'order_count', 'old_value': 1915, 'new_value': 1955}]
2025-06-25 12:01:03,612 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-25 12:01:04,080 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-25 12:01:04,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22000.19, 'new_value': 23656.19}, {'field': 'total_amount', 'old_value': 22500.19, 'new_value': 24156.19}, {'field': 'order_count', 'old_value': 104, 'new_value': 106}]
2025-06-25 12:01:04,080 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-25 12:01:04,580 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-25 12:01:04,580 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37238.82, 'new_value': 39537.65}, {'field': 'offline_amount', 'old_value': 18050.6, 'new_value': 18378.2}, {'field': 'total_amount', 'old_value': 55289.42, 'new_value': 57915.85}, {'field': 'order_count', 'old_value': 2479, 'new_value': 2594}]
2025-06-25 12:01:04,580 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-25 12:01:05,033 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-25 12:01:05,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15640.24, 'new_value': 16442.71}, {'field': 'offline_amount', 'old_value': 10294.69, 'new_value': 10576.89}, {'field': 'total_amount', 'old_value': 25934.93, 'new_value': 27019.6}, {'field': 'order_count', 'old_value': 2108, 'new_value': 2201}]
2025-06-25 12:01:05,049 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-25 12:01:05,502 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-25 12:01:05,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 588778.51, 'new_value': 603123.62}, {'field': 'total_amount', 'old_value': 588778.51, 'new_value': 603123.62}, {'field': 'order_count', 'old_value': 10142, 'new_value': 10406}]
2025-06-25 12:01:05,502 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-25 12:01:06,080 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-25 12:01:06,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37076.7, 'new_value': 37964.1}, {'field': 'offline_amount', 'old_value': 252870.43, 'new_value': 258741.76}, {'field': 'total_amount', 'old_value': 289947.13, 'new_value': 296705.86}, {'field': 'order_count', 'old_value': 35787, 'new_value': 35936}]
2025-06-25 12:01:06,080 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-25 12:01:06,565 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-25 12:01:06,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75159.29, 'new_value': 79276.29}, {'field': 'total_amount', 'old_value': 75159.29, 'new_value': 79276.29}, {'field': 'order_count', 'old_value': 372, 'new_value': 388}]
2025-06-25 12:01:06,565 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-25 12:01:07,080 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-25 12:01:07,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151621.3, 'new_value': 159687.8}, {'field': 'total_amount', 'old_value': 151710.3, 'new_value': 159776.8}, {'field': 'order_count', 'old_value': 1880, 'new_value': 1993}]
2025-06-25 12:01:07,080 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-25 12:01:07,565 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-25 12:01:07,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36958.0, 'new_value': 37345.0}, {'field': 'total_amount', 'old_value': 37686.0, 'new_value': 38073.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 140}]
2025-06-25 12:01:07,565 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-25 12:01:08,127 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-25 12:01:08,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71668.63, 'new_value': 76764.01}, {'field': 'offline_amount', 'old_value': 305268.81, 'new_value': 319215.79}, {'field': 'total_amount', 'old_value': 376937.44, 'new_value': 395979.8}, {'field': 'order_count', 'old_value': 4349, 'new_value': 4559}]
2025-06-25 12:01:08,127 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-25 12:01:08,721 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-25 12:01:08,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9323.12, 'new_value': 9591.12}, {'field': 'offline_amount', 'old_value': 29786.29, 'new_value': 30470.19}, {'field': 'total_amount', 'old_value': 39109.41, 'new_value': 40061.31}, {'field': 'order_count', 'old_value': 1377, 'new_value': 1411}]
2025-06-25 12:01:08,721 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-25 12:01:09,143 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-25 12:01:09,143 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51236.81, 'new_value': 53414.62}, {'field': 'offline_amount', 'old_value': 319664.52, 'new_value': 330300.11}, {'field': 'total_amount', 'old_value': 370901.33, 'new_value': 383714.73}, {'field': 'order_count', 'old_value': 2314, 'new_value': 2385}]
2025-06-25 12:01:09,143 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-25 12:01:09,737 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-25 12:01:09,737 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34909.48, 'new_value': 36242.7}, {'field': 'offline_amount', 'old_value': 31295.94, 'new_value': 32295.94}, {'field': 'total_amount', 'old_value': 66205.42, 'new_value': 68538.64}, {'field': 'order_count', 'old_value': 3121, 'new_value': 3253}]
2025-06-25 12:01:09,737 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-25 12:01:10,158 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-25 12:01:10,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72793.26, 'new_value': 75359.18}, {'field': 'offline_amount', 'old_value': 29828.87, 'new_value': 30386.37}, {'field': 'total_amount', 'old_value': 102622.13, 'new_value': 105745.55}, {'field': 'order_count', 'old_value': 5948, 'new_value': 6131}]
2025-06-25 12:01:10,158 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-25 12:01:10,627 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-25 12:01:10,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 441056.28, 'new_value': 451812.72}, {'field': 'total_amount', 'old_value': 441056.28, 'new_value': 451812.72}, {'field': 'order_count', 'old_value': 6376, 'new_value': 6547}]
2025-06-25 12:01:10,627 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-25 12:01:11,065 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-25 12:01:11,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26564.0, 'new_value': 28052.0}, {'field': 'total_amount', 'old_value': 26564.0, 'new_value': 28052.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 169}]
2025-06-25 12:01:11,065 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-25 12:01:11,549 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-25 12:01:11,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37972.3, 'new_value': 38142.3}, {'field': 'total_amount', 'old_value': 39379.3, 'new_value': 39549.3}, {'field': 'order_count', 'old_value': 128, 'new_value': 129}]
2025-06-25 12:01:11,549 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-25 12:01:11,987 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-25 12:01:12,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141136.6, 'new_value': 145408.03}, {'field': 'offline_amount', 'old_value': 97282.0, 'new_value': 100907.0}, {'field': 'total_amount', 'old_value': 238418.6, 'new_value': 246315.03}, {'field': 'order_count', 'old_value': 2435, 'new_value': 2505}]
2025-06-25 12:01:12,002 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-25 12:01:12,487 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-25 12:01:12,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 516122.63, 'new_value': 539087.43}, {'field': 'total_amount', 'old_value': 516437.99, 'new_value': 539402.79}, {'field': 'order_count', 'old_value': 1380, 'new_value': 1451}]
2025-06-25 12:01:12,487 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-25 12:01:12,940 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-25 12:01:12,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 577437.0, 'new_value': 600035.0}, {'field': 'total_amount', 'old_value': 577437.0, 'new_value': 600035.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-06-25 12:01:12,940 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-25 12:01:13,424 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-25 12:01:13,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177422.78, 'new_value': 186784.92}, {'field': 'total_amount', 'old_value': 237103.69, 'new_value': 246465.83}, {'field': 'order_count', 'old_value': 10543, 'new_value': 10965}]
2025-06-25 12:01:13,424 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-25 12:01:13,924 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-25 12:01:13,924 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72226.49, 'new_value': 75332.7}, {'field': 'offline_amount', 'old_value': 60594.49, 'new_value': 61632.93}, {'field': 'total_amount', 'old_value': 132820.98, 'new_value': 136965.63}, {'field': 'order_count', 'old_value': 6226, 'new_value': 6408}]
2025-06-25 12:01:13,924 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-25 12:01:14,377 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-25 12:01:14,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43815.7, 'new_value': 44589.4}, {'field': 'total_amount', 'old_value': 43815.7, 'new_value': 44589.4}, {'field': 'order_count', 'old_value': 587, 'new_value': 598}]
2025-06-25 12:01:14,377 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-25 12:01:14,830 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-25 12:01:14,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 242876.37, 'new_value': 245674.19}, {'field': 'offline_amount', 'old_value': 81753.62, 'new_value': 82091.62}, {'field': 'total_amount', 'old_value': 324629.99, 'new_value': 327765.81}, {'field': 'order_count', 'old_value': 644, 'new_value': 652}]
2025-06-25 12:01:14,830 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-25 12:01:15,252 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-25 12:01:15,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10930.88, 'new_value': 11559.98}, {'field': 'offline_amount', 'old_value': 28134.0, 'new_value': 28732.84}, {'field': 'total_amount', 'old_value': 39064.88, 'new_value': 40292.82}, {'field': 'order_count', 'old_value': 1948, 'new_value': 2027}]
2025-06-25 12:01:15,252 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-25 12:01:15,783 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-25 12:01:15,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2211.24}, {'field': 'total_amount', 'old_value': 34742.87, 'new_value': 36954.11}, {'field': 'order_count', 'old_value': 148, 'new_value': 157}]
2025-06-25 12:01:15,783 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-25 12:01:16,205 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-25 12:01:16,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15003.0, 'new_value': 15239.0}, {'field': 'total_amount', 'old_value': 15003.0, 'new_value': 15239.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 170}]
2025-06-25 12:01:16,205 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-25 12:01:16,643 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-25 12:01:16,643 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102335.1, 'new_value': 105772.9}, {'field': 'offline_amount', 'old_value': 128326.3, 'new_value': 129105.8}, {'field': 'total_amount', 'old_value': 230661.4, 'new_value': 234878.7}, {'field': 'order_count', 'old_value': 4166, 'new_value': 4250}]
2025-06-25 12:01:16,643 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-25 12:01:17,096 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-25 12:01:17,096 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53513.96, 'new_value': 55153.79}, {'field': 'offline_amount', 'old_value': 204028.09, 'new_value': 208881.01}, {'field': 'total_amount', 'old_value': 257542.05, 'new_value': 264034.8}, {'field': 'order_count', 'old_value': 5740, 'new_value': 5933}]
2025-06-25 12:01:17,096 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-25 12:01:17,533 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-25 12:01:17,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215654.0, 'new_value': 221834.0}, {'field': 'total_amount', 'old_value': 215654.0, 'new_value': 221834.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-06-25 12:01:17,533 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-25 12:01:18,127 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-25 12:01:18,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5803.44, 'new_value': 6001.07}, {'field': 'offline_amount', 'old_value': 324334.97, 'new_value': 335540.57}, {'field': 'total_amount', 'old_value': 330138.41, 'new_value': 341541.64}, {'field': 'order_count', 'old_value': 16347, 'new_value': 16911}]
2025-06-25 12:01:18,127 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-25 12:01:18,612 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-25 12:01:18,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21597.0, 'new_value': 21930.0}, {'field': 'total_amount', 'old_value': 21597.0, 'new_value': 21930.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 95}]
2025-06-25 12:01:18,612 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-25 12:01:19,143 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-25 12:01:19,143 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26931.41, 'new_value': 28549.46}, {'field': 'offline_amount', 'old_value': 193459.9, 'new_value': 198138.0}, {'field': 'total_amount', 'old_value': 220391.31, 'new_value': 226687.46}, {'field': 'order_count', 'old_value': 7106, 'new_value': 7327}]
2025-06-25 12:01:19,143 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-25 12:01:19,611 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-25 12:01:19,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103865.0, 'new_value': 108007.0}, {'field': 'total_amount', 'old_value': 103865.0, 'new_value': 108007.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 69}]
2025-06-25 12:01:19,611 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-25 12:01:20,033 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-25 12:01:20,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112632.0, 'new_value': 115687.0}, {'field': 'total_amount', 'old_value': 112632.0, 'new_value': 115687.0}, {'field': 'order_count', 'old_value': 411, 'new_value': 426}]
2025-06-25 12:01:20,033 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-25 12:01:20,502 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-25 12:01:20,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 274749.34, 'new_value': 284863.0}, {'field': 'offline_amount', 'old_value': 103280.07, 'new_value': 107203.6}, {'field': 'total_amount', 'old_value': 378029.41, 'new_value': 392066.6}, {'field': 'order_count', 'old_value': 1169, 'new_value': 1215}]
2025-06-25 12:01:20,502 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-25 12:01:21,018 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-25 12:01:21,018 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104146.54, 'new_value': 108367.15}, {'field': 'offline_amount', 'old_value': 218917.14, 'new_value': 224871.74}, {'field': 'total_amount', 'old_value': 323063.68, 'new_value': 333238.89}, {'field': 'order_count', 'old_value': 11546, 'new_value': 11952}]
2025-06-25 12:01:21,018 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-25 12:01:21,486 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-25 12:01:21,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159586.0, 'new_value': 163728.0}, {'field': 'total_amount', 'old_value': 159586.0, 'new_value': 163728.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 113}]
2025-06-25 12:01:21,486 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-25 12:01:21,955 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-25 12:01:21,955 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140743.99, 'new_value': 140752.99}, {'field': 'offline_amount', 'old_value': 280150.23, 'new_value': 289876.23}, {'field': 'total_amount', 'old_value': 420894.22, 'new_value': 430629.22}, {'field': 'order_count', 'old_value': 3655, 'new_value': 3687}]
2025-06-25 12:01:21,955 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-25 12:01:22,455 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-25 12:01:22,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 702852.0, 'new_value': 725898.34}, {'field': 'total_amount', 'old_value': 702852.0, 'new_value': 725898.34}, {'field': 'order_count', 'old_value': 4710, 'new_value': 4876}]
2025-06-25 12:01:22,455 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-25 12:01:22,893 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-25 12:01:22,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54870.0, 'new_value': 59170.0}, {'field': 'total_amount', 'old_value': 54870.0, 'new_value': 59170.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-25 12:01:22,893 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-25 12:01:23,393 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-25 12:01:23,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33524.22, 'new_value': 34291.92}, {'field': 'offline_amount', 'old_value': 976999.95, 'new_value': 1007453.71}, {'field': 'total_amount', 'old_value': 1010524.17, 'new_value': 1041745.63}, {'field': 'order_count', 'old_value': 4763, 'new_value': 4953}]
2025-06-25 12:01:23,393 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-25 12:01:23,861 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-25 12:01:23,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10767.0, 'new_value': 11035.0}, {'field': 'total_amount', 'old_value': 10767.0, 'new_value': 11035.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-06-25 12:01:23,861 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-25 12:01:24,408 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-25 12:01:24,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141815.86, 'new_value': 145879.71}, {'field': 'total_amount', 'old_value': 141815.86, 'new_value': 145879.71}, {'field': 'order_count', 'old_value': 6335, 'new_value': 6500}]
2025-06-25 12:01:24,408 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-25 12:01:24,861 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-25 12:01:24,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81251.0, 'new_value': 83090.0}, {'field': 'total_amount', 'old_value': 81251.0, 'new_value': 83090.0}, {'field': 'order_count', 'old_value': 2432, 'new_value': 2488}]
2025-06-25 12:01:24,861 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-25 12:01:25,346 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-25 12:01:25,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199386.12, 'new_value': 206333.42}, {'field': 'total_amount', 'old_value': 199386.12, 'new_value': 206333.42}, {'field': 'order_count', 'old_value': 1594, 'new_value': 1655}]
2025-06-25 12:01:25,346 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-25 12:01:25,783 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-25 12:01:25,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80391.0, 'new_value': 82340.0}, {'field': 'total_amount', 'old_value': 80391.0, 'new_value': 82340.0}, {'field': 'order_count', 'old_value': 643, 'new_value': 661}]
2025-06-25 12:01:25,783 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-25 12:01:26,221 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-25 12:01:26,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242879.35, 'new_value': 247621.26}, {'field': 'total_amount', 'old_value': 242879.35, 'new_value': 247621.26}, {'field': 'order_count', 'old_value': 5125, 'new_value': 5226}]
2025-06-25 12:01:26,221 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-25 12:01:26,690 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-25 12:01:26,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26598.92, 'new_value': 27197.92}, {'field': 'total_amount', 'old_value': 27156.92, 'new_value': 27755.92}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-06-25 12:01:26,690 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-25 12:01:27,236 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-25 12:01:27,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 373528.53, 'new_value': 382941.53}, {'field': 'total_amount', 'old_value': 373528.53, 'new_value': 382941.53}, {'field': 'order_count', 'old_value': 15180, 'new_value': 15613}]
2025-06-25 12:01:27,236 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-25 12:01:27,861 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-25 12:01:27,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138127.9, 'new_value': 139344.9}, {'field': 'total_amount', 'old_value': 138127.9, 'new_value': 139344.9}, {'field': 'order_count', 'old_value': 4395, 'new_value': 4432}]
2025-06-25 12:01:27,861 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-25 12:01:28,330 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-25 12:01:28,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235153.04, 'new_value': 240015.55}, {'field': 'total_amount', 'old_value': 235153.04, 'new_value': 240015.55}, {'field': 'order_count', 'old_value': 1466, 'new_value': 1497}]
2025-06-25 12:01:28,330 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-25 12:01:28,783 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-25 12:01:28,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1383974.36, 'new_value': 1446697.23}, {'field': 'offline_amount', 'old_value': 313535.0, 'new_value': 323531.0}, {'field': 'total_amount', 'old_value': 1697509.36, 'new_value': 1770228.23}, {'field': 'order_count', 'old_value': 6341, 'new_value': 6599}]
2025-06-25 12:01:28,783 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-25 12:01:29,268 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-25 12:01:29,268 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46092.29, 'new_value': 47835.39}, {'field': 'offline_amount', 'old_value': 430799.8, 'new_value': 441717.91}, {'field': 'total_amount', 'old_value': 476892.09, 'new_value': 489553.3}, {'field': 'order_count', 'old_value': 2111, 'new_value': 2171}]
2025-06-25 12:01:29,268 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-25 12:01:29,721 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-25 12:01:29,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26731.0, 'new_value': 28046.0}, {'field': 'total_amount', 'old_value': 26731.0, 'new_value': 28046.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 44}]
2025-06-25 12:01:29,721 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-25 12:01:30,174 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-25 12:01:30,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353334.2, 'new_value': 359814.1}, {'field': 'total_amount', 'old_value': 484951.4, 'new_value': 491431.3}, {'field': 'order_count', 'old_value': 3560, 'new_value': 3562}]
2025-06-25 12:01:30,174 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-25 12:01:30,580 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-25 12:01:30,580 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29023.75, 'new_value': 30917.65}, {'field': 'offline_amount', 'old_value': 27214.0, 'new_value': 28453.3}, {'field': 'total_amount', 'old_value': 56237.75, 'new_value': 59370.95}, {'field': 'order_count', 'old_value': 309, 'new_value': 322}]
2025-06-25 12:01:30,580 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-25 12:01:31,080 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-25 12:01:31,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303819.29, 'new_value': 310669.46}, {'field': 'total_amount', 'old_value': 303819.29, 'new_value': 310669.46}, {'field': 'order_count', 'old_value': 2847, 'new_value': 2933}]
2025-06-25 12:01:31,080 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-25 12:01:31,565 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-25 12:01:31,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49560.0, 'new_value': 51444.92}, {'field': 'offline_amount', 'old_value': 335291.19, 'new_value': 342273.39}, {'field': 'total_amount', 'old_value': 384851.19, 'new_value': 393718.31}, {'field': 'order_count', 'old_value': 3172, 'new_value': 3248}]
2025-06-25 12:01:31,565 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-25 12:01:32,002 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-25 12:01:32,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 274824.55, 'new_value': 283999.07}, {'field': 'offline_amount', 'old_value': 20939.29, 'new_value': 21111.19}, {'field': 'total_amount', 'old_value': 295763.84, 'new_value': 305110.26}, {'field': 'order_count', 'old_value': 11245, 'new_value': 11560}]
2025-06-25 12:01:32,002 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-25 12:01:32,455 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-25 12:01:32,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10000000.0, 'new_value': 10400000.0}, {'field': 'total_amount', 'old_value': 10000000.0, 'new_value': 10400000.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-06-25 12:01:32,455 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-25 12:01:32,924 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-25 12:01:32,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119634.0, 'new_value': 122199.0}, {'field': 'total_amount', 'old_value': 119634.0, 'new_value': 122199.0}, {'field': 'order_count', 'old_value': 214, 'new_value': 219}]
2025-06-25 12:01:32,924 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-25 12:01:33,377 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-25 12:01:33,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3640.0, 'new_value': 3748.0}, {'field': 'offline_amount', 'old_value': 24161.4, 'new_value': 24593.4}, {'field': 'total_amount', 'old_value': 27801.4, 'new_value': 28341.4}, {'field': 'order_count', 'old_value': 948, 'new_value': 962}]
2025-06-25 12:01:33,377 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-25 12:01:33,768 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-25 12:01:33,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14651.32, 'new_value': 15133.48}, {'field': 'offline_amount', 'old_value': 264839.2, 'new_value': 275381.1}, {'field': 'total_amount', 'old_value': 279490.52, 'new_value': 290514.58}, {'field': 'order_count', 'old_value': 1466, 'new_value': 1524}]
2025-06-25 12:01:33,768 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-25 12:01:34,174 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-25 12:01:34,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134045.84, 'new_value': 140719.84}, {'field': 'offline_amount', 'old_value': 190764.06, 'new_value': 197051.44}, {'field': 'total_amount', 'old_value': 324809.9, 'new_value': 337771.28}, {'field': 'order_count', 'old_value': 10886, 'new_value': 11344}]
2025-06-25 12:01:34,174 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-25 12:01:34,565 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-25 12:01:34,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 230624.83, 'new_value': 240117.17}, {'field': 'offline_amount', 'old_value': 290880.36, 'new_value': 298450.85}, {'field': 'total_amount', 'old_value': 521505.19, 'new_value': 538568.02}, {'field': 'order_count', 'old_value': 16514, 'new_value': 17142}]
2025-06-25 12:01:34,565 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-25 12:01:35,018 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-25 12:01:35,018 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59619.54, 'new_value': 62093.15}, {'field': 'total_amount', 'old_value': 59619.54, 'new_value': 62093.15}, {'field': 'order_count', 'old_value': 972, 'new_value': 993}]
2025-06-25 12:01:35,018 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-25 12:01:35,408 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-25 12:01:35,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153165.02, 'new_value': 159455.97}, {'field': 'total_amount', 'old_value': 182360.03, 'new_value': 188650.98}, {'field': 'order_count', 'old_value': 10292, 'new_value': 10625}]
2025-06-25 12:01:35,408 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-25 12:01:35,893 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-25 12:01:35,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32053.32, 'new_value': 33780.32}, {'field': 'total_amount', 'old_value': 32053.32, 'new_value': 33780.32}, {'field': 'order_count', 'old_value': 699, 'new_value': 738}]
2025-06-25 12:01:35,893 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-25 12:01:36,330 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-25 12:01:36,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16275.0, 'new_value': 17030.0}, {'field': 'offline_amount', 'old_value': 24445.0, 'new_value': 25095.0}, {'field': 'total_amount', 'old_value': 40720.0, 'new_value': 42125.0}, {'field': 'order_count', 'old_value': 333, 'new_value': 343}]
2025-06-25 12:01:36,330 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-25 12:01:36,799 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-25 12:01:36,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129160.28, 'new_value': 134965.29}, {'field': 'offline_amount', 'old_value': 41453.19, 'new_value': 42789.43}, {'field': 'total_amount', 'old_value': 170613.47, 'new_value': 177754.72}, {'field': 'order_count', 'old_value': 10165, 'new_value': 10591}]
2025-06-25 12:01:36,799 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-25 12:01:37,252 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-25 12:01:37,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65141.19, 'new_value': 67041.65}, {'field': 'offline_amount', 'old_value': 89074.89, 'new_value': 91023.2}, {'field': 'total_amount', 'old_value': 154216.08, 'new_value': 158064.85}, {'field': 'order_count', 'old_value': 1764, 'new_value': 1809}]
2025-06-25 12:01:37,252 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-25 12:01:37,705 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-25 12:01:37,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128806.77, 'new_value': 131797.18}, {'field': 'total_amount', 'old_value': 128806.77, 'new_value': 131797.18}, {'field': 'order_count', 'old_value': 607, 'new_value': 632}]
2025-06-25 12:01:37,705 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-25 12:01:38,205 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-25 12:01:38,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387814.09, 'new_value': 402644.09}, {'field': 'total_amount', 'old_value': 387814.09, 'new_value': 402644.09}, {'field': 'order_count', 'old_value': 9312, 'new_value': 9604}]
2025-06-25 12:01:38,205 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-25 12:01:38,627 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-25 12:01:38,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81524.73, 'new_value': 84440.07}, {'field': 'total_amount', 'old_value': 81524.73, 'new_value': 84440.07}, {'field': 'order_count', 'old_value': 5270, 'new_value': 5480}]
2025-06-25 12:01:38,627 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-25 12:01:39,033 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-25 12:01:39,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7773.89, 'new_value': 8209.8}, {'field': 'offline_amount', 'old_value': 23586.68, 'new_value': 25584.68}, {'field': 'total_amount', 'old_value': 31360.57, 'new_value': 33794.48}, {'field': 'order_count', 'old_value': 101, 'new_value': 107}]
2025-06-25 12:01:39,033 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-25 12:01:39,502 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-25 12:01:39,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204603.0, 'new_value': 216888.0}, {'field': 'total_amount', 'old_value': 204603.0, 'new_value': 216888.0}, {'field': 'order_count', 'old_value': 21552, 'new_value': 22892}]
2025-06-25 12:01:39,502 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-25 12:01:39,924 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-25 12:01:39,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89252.0, 'new_value': 91623.0}, {'field': 'total_amount', 'old_value': 89252.0, 'new_value': 91623.0}, {'field': 'order_count', 'old_value': 8406, 'new_value': 8613}]
2025-06-25 12:01:39,924 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-25 12:01:40,330 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-25 12:01:40,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44487.1, 'new_value': 46863.1}, {'field': 'total_amount', 'old_value': 44487.1, 'new_value': 46863.1}, {'field': 'order_count', 'old_value': 237, 'new_value': 247}]
2025-06-25 12:01:40,330 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-25 12:01:40,814 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-25 12:01:40,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 642430.22, 'new_value': 667546.79}, {'field': 'total_amount', 'old_value': 642430.22, 'new_value': 667546.79}, {'field': 'order_count', 'old_value': 5447, 'new_value': 5717}]
2025-06-25 12:01:40,814 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-25 12:01:41,252 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-25 12:01:41,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 801318.0, 'new_value': 828436.0}, {'field': 'total_amount', 'old_value': 801318.0, 'new_value': 828436.0}, {'field': 'order_count', 'old_value': 2898, 'new_value': 3002}]
2025-06-25 12:01:41,252 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-25 12:01:41,658 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-25 12:01:41,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 671370.34, 'new_value': 696569.4}, {'field': 'total_amount', 'old_value': 671370.34, 'new_value': 696569.4}, {'field': 'order_count', 'old_value': 4293, 'new_value': 4449}]
2025-06-25 12:01:41,658 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-25 12:01:42,111 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-25 12:01:42,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 986950.4, 'new_value': 1024313.4}, {'field': 'total_amount', 'old_value': 986950.4, 'new_value': 1024313.4}, {'field': 'order_count', 'old_value': 4024, 'new_value': 4135}]
2025-06-25 12:01:42,111 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-25 12:01:42,533 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-25 12:01:42,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 635354.25, 'new_value': 663621.25}, {'field': 'total_amount', 'old_value': 635354.25, 'new_value': 663621.25}, {'field': 'order_count', 'old_value': 1633, 'new_value': 1689}]
2025-06-25 12:01:42,533 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-25 12:01:42,955 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-25 12:01:42,955 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110870.89, 'new_value': 119014.95}, {'field': 'total_amount', 'old_value': 163346.72, 'new_value': 171490.78}, {'field': 'order_count', 'old_value': 10836, 'new_value': 11392}]
2025-06-25 12:01:42,955 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-25 12:01:43,377 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-25 12:01:43,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10186.94, 'new_value': 10812.04}, {'field': 'offline_amount', 'old_value': 74850.23, 'new_value': 77428.28}, {'field': 'total_amount', 'old_value': 85037.17, 'new_value': 88240.32}, {'field': 'order_count', 'old_value': 2653, 'new_value': 2738}]
2025-06-25 12:01:43,377 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-25 12:01:43,877 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-25 12:01:43,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245900.0, 'new_value': 257100.0}, {'field': 'total_amount', 'old_value': 245900.0, 'new_value': 257100.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 606}]
2025-06-25 12:01:43,893 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-25 12:01:44,346 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-25 12:01:44,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194534.11, 'new_value': 205301.31}, {'field': 'total_amount', 'old_value': 194534.11, 'new_value': 205301.31}, {'field': 'order_count', 'old_value': 2948, 'new_value': 3131}]
2025-06-25 12:01:44,346 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-25 12:01:44,830 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-25 12:01:44,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 509325.05, 'new_value': 536944.95}, {'field': 'total_amount', 'old_value': 509325.05, 'new_value': 536944.95}, {'field': 'order_count', 'old_value': 482, 'new_value': 505}]
2025-06-25 12:01:44,830 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-25 12:01:45,299 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-25 12:01:45,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39739.6, 'new_value': 40445.5}, {'field': 'total_amount', 'old_value': 39739.6, 'new_value': 40445.5}, {'field': 'order_count', 'old_value': 333, 'new_value': 340}]
2025-06-25 12:01:45,299 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-25 12:01:45,736 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-25 12:01:45,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81619.37, 'new_value': 87488.07}, {'field': 'total_amount', 'old_value': 217352.67, 'new_value': 223221.37}, {'field': 'order_count', 'old_value': 10646, 'new_value': 10961}]
2025-06-25 12:01:45,736 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-25 12:01:46,189 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-25 12:01:46,189 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98796.31, 'new_value': 100332.79}, {'field': 'offline_amount', 'old_value': 180483.05, 'new_value': 185802.55}, {'field': 'total_amount', 'old_value': 279279.36, 'new_value': 286135.34}, {'field': 'order_count', 'old_value': 2198, 'new_value': 2276}]
2025-06-25 12:01:46,189 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-25 12:01:46,736 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-25 12:01:46,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79037.3, 'new_value': 81413.2}, {'field': 'total_amount', 'old_value': 79037.3, 'new_value': 81413.2}, {'field': 'order_count', 'old_value': 716, 'new_value': 738}]
2025-06-25 12:01:46,736 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-25 12:01:47,252 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-25 12:01:47,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77134.0, 'new_value': 79409.0}, {'field': 'total_amount', 'old_value': 77134.0, 'new_value': 79409.0}, {'field': 'order_count', 'old_value': 1171, 'new_value': 1223}]
2025-06-25 12:01:47,252 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-25 12:01:47,689 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-25 12:01:47,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55557.7, 'new_value': 58557.7}, {'field': 'offline_amount', 'old_value': 102353.77, 'new_value': 103174.63}, {'field': 'total_amount', 'old_value': 157911.47, 'new_value': 161732.33}, {'field': 'order_count', 'old_value': 3800, 'new_value': 3922}]
2025-06-25 12:01:47,705 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-25 12:01:48,080 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-25 12:01:48,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79502.4, 'new_value': 108902.4}, {'field': 'total_amount', 'old_value': 79502.4, 'new_value': 108902.4}, {'field': 'order_count', 'old_value': 38, 'new_value': 44}]
2025-06-25 12:01:48,080 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-25 12:01:48,502 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-25 12:01:48,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3286189.0, 'new_value': 3595189.0}, {'field': 'total_amount', 'old_value': 4195089.0, 'new_value': 4504089.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-25 12:01:48,502 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-25 12:01:48,924 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-25 12:01:48,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279492.0, 'new_value': 283447.0}, {'field': 'total_amount', 'old_value': 279492.0, 'new_value': 283447.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-06-25 12:01:48,924 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-25 12:01:49,471 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-25 12:01:49,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202367.9, 'new_value': 212769.9}, {'field': 'total_amount', 'old_value': 202367.9, 'new_value': 212769.9}, {'field': 'order_count', 'old_value': 7034, 'new_value': 7356}]
2025-06-25 12:01:49,486 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-25 12:01:49,924 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-25 12:01:49,924 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 210018.85, 'new_value': 222162.85}, {'field': 'total_amount', 'old_value': 244220.95, 'new_value': 256364.95}, {'field': 'order_count', 'old_value': 751, 'new_value': 788}]
2025-06-25 12:01:49,924 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-25 12:01:50,502 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-25 12:01:50,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167190.0, 'new_value': 176482.0}, {'field': 'total_amount', 'old_value': 167190.0, 'new_value': 176482.0}, {'field': 'order_count', 'old_value': 722, 'new_value': 758}]
2025-06-25 12:01:50,502 - INFO - 开始批量插入 1 条新记录
2025-06-25 12:01:50,658 - INFO - 批量插入响应状态码: 200
2025-06-25 12:01:50,658 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 04:01:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'close', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A02DF672-E025-75D8-BC0E-0393812D2428', 'x-acs-trace-id': '19c1cad2e005a22f58e8b23e58a1774a', 'etag': '6Jsyfqj17NpI4lL0wt+aAlQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-25 12:01:50,658 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G71POLWR3A59C81N8WVTUQ53HBQGFBCMG3']}
2025-06-25 12:01:50,658 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-25 12:01:50,658 - INFO - 成功插入的数据ID: ['FINST-IOC66G71POLWR3A59C81N8WVTUQ53HBQGFBCMG3']
2025-06-25 12:01:53,674 - INFO - 批量插入完成，共 1 条记录
2025-06-25 12:01:53,674 - INFO - 日期 2025-06 处理完成 - 更新: 156 条，插入: 1 条，错误: 0 条
2025-06-25 12:01:53,674 - INFO - 数据同步完成！更新: 156 条，插入: 1 条，错误: 0 条
2025-06-25 12:01:53,674 - INFO - =================同步完成====================
2025-06-25 15:00:02,908 - INFO - =================使用默认全量同步=============
2025-06-25 15:00:04,643 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-25 15:00:04,643 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 15:00:04,674 - INFO - 开始处理日期: 2025-01
2025-06-25 15:00:04,674 - INFO - Request Parameters - Page 1:
2025-06-25 15:00:04,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:04,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:05,736 - INFO - Response - Page 1:
2025-06-25 15:00:05,939 - INFO - 第 1 页获取到 100 条记录
2025-06-25 15:00:05,939 - INFO - Request Parameters - Page 2:
2025-06-25 15:00:05,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:05,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:06,486 - INFO - Response - Page 2:
2025-06-25 15:00:06,689 - INFO - 第 2 页获取到 100 条记录
2025-06-25 15:00:06,689 - INFO - Request Parameters - Page 3:
2025-06-25 15:00:06,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:06,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:07,752 - INFO - Response - Page 3:
2025-06-25 15:00:07,955 - INFO - 第 3 页获取到 100 条记录
2025-06-25 15:00:07,955 - INFO - Request Parameters - Page 4:
2025-06-25 15:00:07,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:07,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:08,424 - INFO - Response - Page 4:
2025-06-25 15:00:08,627 - INFO - 第 4 页获取到 100 条记录
2025-06-25 15:00:08,627 - INFO - Request Parameters - Page 5:
2025-06-25 15:00:08,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:08,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:09,127 - INFO - Response - Page 5:
2025-06-25 15:00:09,330 - INFO - 第 5 页获取到 100 条记录
2025-06-25 15:00:09,330 - INFO - Request Parameters - Page 6:
2025-06-25 15:00:09,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:09,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:09,861 - INFO - Response - Page 6:
2025-06-25 15:00:10,064 - INFO - 第 6 页获取到 100 条记录
2025-06-25 15:00:10,064 - INFO - Request Parameters - Page 7:
2025-06-25 15:00:10,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:10,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:10,564 - INFO - Response - Page 7:
2025-06-25 15:00:10,767 - INFO - 第 7 页获取到 82 条记录
2025-06-25 15:00:10,767 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 15:00:10,767 - INFO - 获取到 682 条表单数据
2025-06-25 15:00:10,767 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 15:00:10,783 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:10,783 - INFO - 开始处理日期: 2025-02
2025-06-25 15:00:10,783 - INFO - Request Parameters - Page 1:
2025-06-25 15:00:10,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:10,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:11,314 - INFO - Response - Page 1:
2025-06-25 15:00:11,517 - INFO - 第 1 页获取到 100 条记录
2025-06-25 15:00:11,517 - INFO - Request Parameters - Page 2:
2025-06-25 15:00:11,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:11,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:12,049 - INFO - Response - Page 2:
2025-06-25 15:00:12,252 - INFO - 第 2 页获取到 100 条记录
2025-06-25 15:00:12,252 - INFO - Request Parameters - Page 3:
2025-06-25 15:00:12,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:12,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:12,752 - INFO - Response - Page 3:
2025-06-25 15:00:12,955 - INFO - 第 3 页获取到 100 条记录
2025-06-25 15:00:12,955 - INFO - Request Parameters - Page 4:
2025-06-25 15:00:12,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:12,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:13,439 - INFO - Response - Page 4:
2025-06-25 15:00:13,642 - INFO - 第 4 页获取到 100 条记录
2025-06-25 15:00:13,642 - INFO - Request Parameters - Page 5:
2025-06-25 15:00:13,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:13,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:14,158 - INFO - Response - Page 5:
2025-06-25 15:00:14,361 - INFO - 第 5 页获取到 100 条记录
2025-06-25 15:00:14,361 - INFO - Request Parameters - Page 6:
2025-06-25 15:00:14,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:14,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:14,830 - INFO - Response - Page 6:
2025-06-25 15:00:15,033 - INFO - 第 6 页获取到 100 条记录
2025-06-25 15:00:15,033 - INFO - Request Parameters - Page 7:
2025-06-25 15:00:15,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:15,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:15,611 - INFO - Response - Page 7:
2025-06-25 15:00:15,814 - INFO - 第 7 页获取到 70 条记录
2025-06-25 15:00:15,814 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 15:00:15,814 - INFO - 获取到 670 条表单数据
2025-06-25 15:00:15,814 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 15:00:15,830 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:15,830 - INFO - 开始处理日期: 2025-03
2025-06-25 15:00:15,830 - INFO - Request Parameters - Page 1:
2025-06-25 15:00:15,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:15,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:16,377 - INFO - Response - Page 1:
2025-06-25 15:00:16,580 - INFO - 第 1 页获取到 100 条记录
2025-06-25 15:00:16,580 - INFO - Request Parameters - Page 2:
2025-06-25 15:00:16,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:16,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:17,142 - INFO - Response - Page 2:
2025-06-25 15:00:17,346 - INFO - 第 2 页获取到 100 条记录
2025-06-25 15:00:17,346 - INFO - Request Parameters - Page 3:
2025-06-25 15:00:17,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:17,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:17,908 - INFO - Response - Page 3:
2025-06-25 15:00:18,111 - INFO - 第 3 页获取到 100 条记录
2025-06-25 15:00:18,111 - INFO - Request Parameters - Page 4:
2025-06-25 15:00:18,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:18,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:18,642 - INFO - Response - Page 4:
2025-06-25 15:00:18,846 - INFO - 第 4 页获取到 100 条记录
2025-06-25 15:00:18,846 - INFO - Request Parameters - Page 5:
2025-06-25 15:00:18,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:18,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:19,424 - INFO - Response - Page 5:
2025-06-25 15:00:19,627 - INFO - 第 5 页获取到 100 条记录
2025-06-25 15:00:19,627 - INFO - Request Parameters - Page 6:
2025-06-25 15:00:19,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:19,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:20,174 - INFO - Response - Page 6:
2025-06-25 15:00:20,377 - INFO - 第 6 页获取到 100 条记录
2025-06-25 15:00:20,377 - INFO - Request Parameters - Page 7:
2025-06-25 15:00:20,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:20,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:20,814 - INFO - Response - Page 7:
2025-06-25 15:00:21,017 - INFO - 第 7 页获取到 61 条记录
2025-06-25 15:00:21,017 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 15:00:21,017 - INFO - 获取到 661 条表单数据
2025-06-25 15:00:21,017 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 15:00:21,033 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:21,033 - INFO - 开始处理日期: 2025-04
2025-06-25 15:00:21,033 - INFO - Request Parameters - Page 1:
2025-06-25 15:00:21,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:21,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:21,533 - INFO - Response - Page 1:
2025-06-25 15:00:21,736 - INFO - 第 1 页获取到 100 条记录
2025-06-25 15:00:21,736 - INFO - Request Parameters - Page 2:
2025-06-25 15:00:21,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:21,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:22,283 - INFO - Response - Page 2:
2025-06-25 15:00:22,486 - INFO - 第 2 页获取到 100 条记录
2025-06-25 15:00:22,486 - INFO - Request Parameters - Page 3:
2025-06-25 15:00:22,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:22,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:22,971 - INFO - Response - Page 3:
2025-06-25 15:00:23,174 - INFO - 第 3 页获取到 100 条记录
2025-06-25 15:00:23,174 - INFO - Request Parameters - Page 4:
2025-06-25 15:00:23,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:23,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:23,642 - INFO - Response - Page 4:
2025-06-25 15:00:23,845 - INFO - 第 4 页获取到 100 条记录
2025-06-25 15:00:23,845 - INFO - Request Parameters - Page 5:
2025-06-25 15:00:23,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:23,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:24,424 - INFO - Response - Page 5:
2025-06-25 15:00:24,627 - INFO - 第 5 页获取到 100 条记录
2025-06-25 15:00:24,627 - INFO - Request Parameters - Page 6:
2025-06-25 15:00:24,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:24,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:25,064 - INFO - Response - Page 6:
2025-06-25 15:00:25,267 - INFO - 第 6 页获取到 100 条记录
2025-06-25 15:00:25,267 - INFO - Request Parameters - Page 7:
2025-06-25 15:00:25,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:25,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:25,752 - INFO - Response - Page 7:
2025-06-25 15:00:25,955 - INFO - 第 7 页获取到 56 条记录
2025-06-25 15:00:25,955 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 15:00:25,955 - INFO - 获取到 656 条表单数据
2025-06-25 15:00:25,955 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 15:00:25,970 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:25,970 - INFO - 开始处理日期: 2025-05
2025-06-25 15:00:25,970 - INFO - Request Parameters - Page 1:
2025-06-25 15:00:25,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:25,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:26,549 - INFO - Response - Page 1:
2025-06-25 15:00:26,752 - INFO - 第 1 页获取到 100 条记录
2025-06-25 15:00:26,752 - INFO - Request Parameters - Page 2:
2025-06-25 15:00:26,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:26,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:27,220 - INFO - Response - Page 2:
2025-06-25 15:00:27,424 - INFO - 第 2 页获取到 100 条记录
2025-06-25 15:00:27,424 - INFO - Request Parameters - Page 3:
2025-06-25 15:00:27,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:27,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:27,970 - INFO - Response - Page 3:
2025-06-25 15:00:28,174 - INFO - 第 3 页获取到 100 条记录
2025-06-25 15:00:28,174 - INFO - Request Parameters - Page 4:
2025-06-25 15:00:28,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:28,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:28,752 - INFO - Response - Page 4:
2025-06-25 15:00:28,955 - INFO - 第 4 页获取到 100 条记录
2025-06-25 15:00:28,955 - INFO - Request Parameters - Page 5:
2025-06-25 15:00:28,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:28,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:30,002 - INFO - Response - Page 5:
2025-06-25 15:00:30,205 - INFO - 第 5 页获取到 100 条记录
2025-06-25 15:00:30,205 - INFO - Request Parameters - Page 6:
2025-06-25 15:00:30,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:30,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:30,736 - INFO - Response - Page 6:
2025-06-25 15:00:30,939 - INFO - 第 6 页获取到 100 条记录
2025-06-25 15:00:30,939 - INFO - Request Parameters - Page 7:
2025-06-25 15:00:30,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:30,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:31,377 - INFO - Response - Page 7:
2025-06-25 15:00:31,580 - INFO - 第 7 页获取到 65 条记录
2025-06-25 15:00:31,580 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 15:00:31,580 - INFO - 获取到 665 条表单数据
2025-06-25 15:00:31,580 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 15:00:31,595 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:31,595 - INFO - 开始处理日期: 2025-06
2025-06-25 15:00:31,595 - INFO - Request Parameters - Page 1:
2025-06-25 15:00:31,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:31,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:32,189 - INFO - Response - Page 1:
2025-06-25 15:00:32,392 - INFO - 第 1 页获取到 100 条记录
2025-06-25 15:00:32,392 - INFO - Request Parameters - Page 2:
2025-06-25 15:00:32,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:32,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:32,877 - INFO - Response - Page 2:
2025-06-25 15:00:33,080 - INFO - 第 2 页获取到 100 条记录
2025-06-25 15:00:33,080 - INFO - Request Parameters - Page 3:
2025-06-25 15:00:33,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:33,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:33,642 - INFO - Response - Page 3:
2025-06-25 15:00:33,845 - INFO - 第 3 页获取到 100 条记录
2025-06-25 15:00:33,845 - INFO - Request Parameters - Page 4:
2025-06-25 15:00:33,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:33,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:34,377 - INFO - Response - Page 4:
2025-06-25 15:00:34,580 - INFO - 第 4 页获取到 100 条记录
2025-06-25 15:00:34,580 - INFO - Request Parameters - Page 5:
2025-06-25 15:00:34,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:34,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:35,064 - INFO - Response - Page 5:
2025-06-25 15:00:35,267 - INFO - 第 5 页获取到 100 条记录
2025-06-25 15:00:35,267 - INFO - Request Parameters - Page 6:
2025-06-25 15:00:35,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:35,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:35,767 - INFO - Response - Page 6:
2025-06-25 15:00:35,970 - INFO - 第 6 页获取到 100 条记录
2025-06-25 15:00:35,970 - INFO - Request Parameters - Page 7:
2025-06-25 15:00:35,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 15:00:35,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 15:00:36,345 - INFO - Response - Page 7:
2025-06-25 15:00:36,549 - INFO - 第 7 页获取到 26 条记录
2025-06-25 15:00:36,549 - INFO - 查询完成，共获取到 626 条记录
2025-06-25 15:00:36,549 - INFO - 获取到 626 条表单数据
2025-06-25 15:00:36,549 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-25 15:00:36,549 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-25 15:00:37,002 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-25 15:00:37,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39997.48, 'new_value': 44922.8}, {'field': 'total_amount', 'old_value': 82294.78, 'new_value': 87220.1}, {'field': 'order_count', 'old_value': 2922, 'new_value': 3098}]
2025-06-25 15:00:37,002 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-25 15:00:37,502 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-25 15:00:37,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145849.53, 'new_value': 153237.01}, {'field': 'offline_amount', 'old_value': 16777.95, 'new_value': 17648.95}, {'field': 'total_amount', 'old_value': 162627.48, 'new_value': 170885.96}, {'field': 'order_count', 'old_value': 4871, 'new_value': 5086}]
2025-06-25 15:00:37,502 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-25 15:00:38,033 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-25 15:00:38,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128566.05, 'new_value': 134706.21}, {'field': 'offline_amount', 'old_value': 94376.1, 'new_value': 99903.18}, {'field': 'total_amount', 'old_value': 222942.15, 'new_value': 234609.39}, {'field': 'order_count', 'old_value': 9202, 'new_value': 9693}]
2025-06-25 15:00:38,033 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-25 15:00:38,455 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-25 15:00:38,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 485038.51, 'new_value': 499904.51}, {'field': 'total_amount', 'old_value': 485038.51, 'new_value': 499904.51}, {'field': 'order_count', 'old_value': 5557, 'new_value': 5753}]
2025-06-25 15:00:38,455 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-25 15:00:39,002 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-25 15:00:39,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28588.87, 'new_value': 29769.55}, {'field': 'offline_amount', 'old_value': 59106.38, 'new_value': 61625.96}, {'field': 'total_amount', 'old_value': 87695.25, 'new_value': 91395.51}, {'field': 'order_count', 'old_value': 779, 'new_value': 809}]
2025-06-25 15:00:39,002 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-25 15:00:39,439 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-25 15:00:39,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195823.63, 'new_value': 206662.41}, {'field': 'total_amount', 'old_value': 245611.94, 'new_value': 256450.72}, {'field': 'order_count', 'old_value': 6126, 'new_value': 6393}]
2025-06-25 15:00:39,439 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-25 15:00:39,877 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-25 15:00:39,877 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37766.34, 'new_value': 38732.07}, {'field': 'offline_amount', 'old_value': 291756.82, 'new_value': 303045.6}, {'field': 'total_amount', 'old_value': 329523.16, 'new_value': 341777.67}, {'field': 'order_count', 'old_value': 22251, 'new_value': 22678}]
2025-06-25 15:00:39,877 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-25 15:00:40,330 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-25 15:00:40,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90450.05, 'new_value': 95114.82}, {'field': 'total_amount', 'old_value': 90450.05, 'new_value': 95114.82}, {'field': 'order_count', 'old_value': 2709, 'new_value': 2865}]
2025-06-25 15:00:40,330 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-25 15:00:40,767 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-25 15:00:40,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191187.07, 'new_value': 201090.71}, {'field': 'total_amount', 'old_value': 191187.07, 'new_value': 201090.71}, {'field': 'order_count', 'old_value': 8569, 'new_value': 9036}]
2025-06-25 15:00:40,767 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-25 15:00:41,174 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-25 15:00:41,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289463.17, 'new_value': 304118.21}, {'field': 'total_amount', 'old_value': 289463.17, 'new_value': 304118.21}, {'field': 'order_count', 'old_value': 363, 'new_value': 416}]
2025-06-25 15:00:41,174 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-25 15:00:41,642 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-25 15:00:41,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 325079.8, 'new_value': 339113.8}, {'field': 'total_amount', 'old_value': 325079.8, 'new_value': 339113.8}, {'field': 'order_count', 'old_value': 385, 'new_value': 399}]
2025-06-25 15:00:41,642 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-25 15:00:42,174 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-25 15:00:42,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112870.0, 'new_value': 119019.0}, {'field': 'total_amount', 'old_value': 112870.0, 'new_value': 119019.0}, {'field': 'order_count', 'old_value': 3451, 'new_value': 3650}]
2025-06-25 15:00:42,174 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-25 15:00:42,658 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-25 15:00:42,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142744.0, 'new_value': 150992.0}, {'field': 'total_amount', 'old_value': 142844.0, 'new_value': 151092.0}, {'field': 'order_count', 'old_value': 15007, 'new_value': 15936}]
2025-06-25 15:00:42,658 - INFO - 日期 2025-06 处理完成 - 更新: 13 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:42,658 - INFO - 数据同步完成！更新: 13 条，插入: 0 条，错误: 0 条
2025-06-25 15:00:42,658 - INFO - =================同步完成====================
2025-06-25 18:00:02,397 - INFO - =================使用默认全量同步=============
2025-06-25 18:00:04,194 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-25 18:00:04,194 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 18:00:04,225 - INFO - 开始处理日期: 2025-01
2025-06-25 18:00:04,225 - INFO - Request Parameters - Page 1:
2025-06-25 18:00:04,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:04,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:05,663 - INFO - Response - Page 1:
2025-06-25 18:00:05,866 - INFO - 第 1 页获取到 100 条记录
2025-06-25 18:00:05,866 - INFO - Request Parameters - Page 2:
2025-06-25 18:00:05,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:05,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:06,428 - INFO - Response - Page 2:
2025-06-25 18:00:06,632 - INFO - 第 2 页获取到 100 条记录
2025-06-25 18:00:06,632 - INFO - Request Parameters - Page 3:
2025-06-25 18:00:06,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:06,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:07,100 - INFO - Response - Page 3:
2025-06-25 18:00:07,303 - INFO - 第 3 页获取到 100 条记录
2025-06-25 18:00:07,303 - INFO - Request Parameters - Page 4:
2025-06-25 18:00:07,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:07,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:07,788 - INFO - Response - Page 4:
2025-06-25 18:00:07,991 - INFO - 第 4 页获取到 100 条记录
2025-06-25 18:00:07,991 - INFO - Request Parameters - Page 5:
2025-06-25 18:00:07,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:07,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:08,819 - INFO - Response - Page 5:
2025-06-25 18:00:09,022 - INFO - 第 5 页获取到 100 条记录
2025-06-25 18:00:09,022 - INFO - Request Parameters - Page 6:
2025-06-25 18:00:09,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:09,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:09,538 - INFO - Response - Page 6:
2025-06-25 18:00:09,741 - INFO - 第 6 页获取到 100 条记录
2025-06-25 18:00:09,741 - INFO - Request Parameters - Page 7:
2025-06-25 18:00:09,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:09,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:10,241 - INFO - Response - Page 7:
2025-06-25 18:00:10,444 - INFO - 第 7 页获取到 82 条记录
2025-06-25 18:00:10,444 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 18:00:10,444 - INFO - 获取到 682 条表单数据
2025-06-25 18:00:10,444 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 18:00:10,460 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:10,460 - INFO - 开始处理日期: 2025-02
2025-06-25 18:00:10,460 - INFO - Request Parameters - Page 1:
2025-06-25 18:00:10,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:10,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:11,022 - INFO - Response - Page 1:
2025-06-25 18:00:11,225 - INFO - 第 1 页获取到 100 条记录
2025-06-25 18:00:11,225 - INFO - Request Parameters - Page 2:
2025-06-25 18:00:11,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:11,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:12,225 - INFO - Response - Page 2:
2025-06-25 18:00:12,428 - INFO - 第 2 页获取到 100 条记录
2025-06-25 18:00:12,428 - INFO - Request Parameters - Page 3:
2025-06-25 18:00:12,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:12,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:12,975 - INFO - Response - Page 3:
2025-06-25 18:00:13,178 - INFO - 第 3 页获取到 100 条记录
2025-06-25 18:00:13,178 - INFO - Request Parameters - Page 4:
2025-06-25 18:00:13,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:13,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:13,678 - INFO - Response - Page 4:
2025-06-25 18:00:13,882 - INFO - 第 4 页获取到 100 条记录
2025-06-25 18:00:13,882 - INFO - Request Parameters - Page 5:
2025-06-25 18:00:13,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:13,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:14,366 - INFO - Response - Page 5:
2025-06-25 18:00:14,569 - INFO - 第 5 页获取到 100 条记录
2025-06-25 18:00:14,569 - INFO - Request Parameters - Page 6:
2025-06-25 18:00:14,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:14,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:15,022 - INFO - Response - Page 6:
2025-06-25 18:00:15,225 - INFO - 第 6 页获取到 100 条记录
2025-06-25 18:00:15,225 - INFO - Request Parameters - Page 7:
2025-06-25 18:00:15,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:15,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:15,725 - INFO - Response - Page 7:
2025-06-25 18:00:15,928 - INFO - 第 7 页获取到 70 条记录
2025-06-25 18:00:15,928 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 18:00:15,928 - INFO - 获取到 670 条表单数据
2025-06-25 18:00:15,928 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 18:00:15,944 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:15,944 - INFO - 开始处理日期: 2025-03
2025-06-25 18:00:15,944 - INFO - Request Parameters - Page 1:
2025-06-25 18:00:15,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:15,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:16,428 - INFO - Response - Page 1:
2025-06-25 18:00:16,632 - INFO - 第 1 页获取到 100 条记录
2025-06-25 18:00:16,632 - INFO - Request Parameters - Page 2:
2025-06-25 18:00:16,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:16,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:17,100 - INFO - Response - Page 2:
2025-06-25 18:00:17,303 - INFO - 第 2 页获取到 100 条记录
2025-06-25 18:00:17,303 - INFO - Request Parameters - Page 3:
2025-06-25 18:00:17,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:17,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:17,913 - INFO - Response - Page 3:
2025-06-25 18:00:18,116 - INFO - 第 3 页获取到 100 条记录
2025-06-25 18:00:18,116 - INFO - Request Parameters - Page 4:
2025-06-25 18:00:18,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:18,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:18,647 - INFO - Response - Page 4:
2025-06-25 18:00:18,850 - INFO - 第 4 页获取到 100 条记录
2025-06-25 18:00:18,850 - INFO - Request Parameters - Page 5:
2025-06-25 18:00:18,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:18,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:19,335 - INFO - Response - Page 5:
2025-06-25 18:00:19,538 - INFO - 第 5 页获取到 100 条记录
2025-06-25 18:00:19,538 - INFO - Request Parameters - Page 6:
2025-06-25 18:00:19,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:19,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:20,116 - INFO - Response - Page 6:
2025-06-25 18:00:20,319 - INFO - 第 6 页获取到 100 条记录
2025-06-25 18:00:20,319 - INFO - Request Parameters - Page 7:
2025-06-25 18:00:20,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:20,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:20,741 - INFO - Response - Page 7:
2025-06-25 18:00:20,944 - INFO - 第 7 页获取到 61 条记录
2025-06-25 18:00:20,944 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 18:00:20,944 - INFO - 获取到 661 条表单数据
2025-06-25 18:00:20,944 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 18:00:20,960 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:20,960 - INFO - 开始处理日期: 2025-04
2025-06-25 18:00:20,960 - INFO - Request Parameters - Page 1:
2025-06-25 18:00:20,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:20,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:21,507 - INFO - Response - Page 1:
2025-06-25 18:00:21,710 - INFO - 第 1 页获取到 100 条记录
2025-06-25 18:00:21,710 - INFO - Request Parameters - Page 2:
2025-06-25 18:00:21,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:21,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:22,428 - INFO - Response - Page 2:
2025-06-25 18:00:22,632 - INFO - 第 2 页获取到 100 条记录
2025-06-25 18:00:22,632 - INFO - Request Parameters - Page 3:
2025-06-25 18:00:22,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:22,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:23,116 - INFO - Response - Page 3:
2025-06-25 18:00:23,319 - INFO - 第 3 页获取到 100 条记录
2025-06-25 18:00:23,319 - INFO - Request Parameters - Page 4:
2025-06-25 18:00:23,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:23,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:24,007 - INFO - Response - Page 4:
2025-06-25 18:00:24,210 - INFO - 第 4 页获取到 100 条记录
2025-06-25 18:00:24,210 - INFO - Request Parameters - Page 5:
2025-06-25 18:00:24,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:24,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:24,757 - INFO - Response - Page 5:
2025-06-25 18:00:24,960 - INFO - 第 5 页获取到 100 条记录
2025-06-25 18:00:24,960 - INFO - Request Parameters - Page 6:
2025-06-25 18:00:24,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:24,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:25,444 - INFO - Response - Page 6:
2025-06-25 18:00:25,647 - INFO - 第 6 页获取到 100 条记录
2025-06-25 18:00:25,647 - INFO - Request Parameters - Page 7:
2025-06-25 18:00:25,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:25,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:26,131 - INFO - Response - Page 7:
2025-06-25 18:00:26,335 - INFO - 第 7 页获取到 56 条记录
2025-06-25 18:00:26,335 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 18:00:26,335 - INFO - 获取到 656 条表单数据
2025-06-25 18:00:26,335 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 18:00:26,350 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:26,350 - INFO - 开始处理日期: 2025-05
2025-06-25 18:00:26,350 - INFO - Request Parameters - Page 1:
2025-06-25 18:00:26,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:26,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:26,803 - INFO - Response - Page 1:
2025-06-25 18:00:27,006 - INFO - 第 1 页获取到 100 条记录
2025-06-25 18:00:27,006 - INFO - Request Parameters - Page 2:
2025-06-25 18:00:27,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:27,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:27,522 - INFO - Response - Page 2:
2025-06-25 18:00:27,725 - INFO - 第 2 页获取到 100 条记录
2025-06-25 18:00:27,725 - INFO - Request Parameters - Page 3:
2025-06-25 18:00:27,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:27,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:28,303 - INFO - Response - Page 3:
2025-06-25 18:00:28,506 - INFO - 第 3 页获取到 100 条记录
2025-06-25 18:00:28,506 - INFO - Request Parameters - Page 4:
2025-06-25 18:00:28,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:28,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:29,069 - INFO - Response - Page 4:
2025-06-25 18:00:29,272 - INFO - 第 4 页获取到 100 条记录
2025-06-25 18:00:29,272 - INFO - Request Parameters - Page 5:
2025-06-25 18:00:29,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:29,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:29,835 - INFO - Response - Page 5:
2025-06-25 18:00:30,038 - INFO - 第 5 页获取到 100 条记录
2025-06-25 18:00:30,038 - INFO - Request Parameters - Page 6:
2025-06-25 18:00:30,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:30,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:30,585 - INFO - Response - Page 6:
2025-06-25 18:00:30,788 - INFO - 第 6 页获取到 100 条记录
2025-06-25 18:00:30,788 - INFO - Request Parameters - Page 7:
2025-06-25 18:00:30,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:30,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:31,210 - INFO - Response - Page 7:
2025-06-25 18:00:31,413 - INFO - 第 7 页获取到 65 条记录
2025-06-25 18:00:31,413 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 18:00:31,413 - INFO - 获取到 665 条表单数据
2025-06-25 18:00:31,413 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 18:00:31,428 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:31,428 - INFO - 开始处理日期: 2025-06
2025-06-25 18:00:31,428 - INFO - Request Parameters - Page 1:
2025-06-25 18:00:31,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:31,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:31,897 - INFO - Response - Page 1:
2025-06-25 18:00:32,100 - INFO - 第 1 页获取到 100 条记录
2025-06-25 18:00:32,100 - INFO - Request Parameters - Page 2:
2025-06-25 18:00:32,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:32,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:32,616 - INFO - Response - Page 2:
2025-06-25 18:00:32,819 - INFO - 第 2 页获取到 100 条记录
2025-06-25 18:00:32,819 - INFO - Request Parameters - Page 3:
2025-06-25 18:00:32,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:32,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:33,522 - INFO - Response - Page 3:
2025-06-25 18:00:33,725 - INFO - 第 3 页获取到 100 条记录
2025-06-25 18:00:33,725 - INFO - Request Parameters - Page 4:
2025-06-25 18:00:33,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:33,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:34,475 - INFO - Response - Page 4:
2025-06-25 18:00:34,678 - INFO - 第 4 页获取到 100 条记录
2025-06-25 18:00:34,678 - INFO - Request Parameters - Page 5:
2025-06-25 18:00:34,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:34,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:35,225 - INFO - Response - Page 5:
2025-06-25 18:00:35,428 - INFO - 第 5 页获取到 100 条记录
2025-06-25 18:00:35,428 - INFO - Request Parameters - Page 6:
2025-06-25 18:00:35,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:35,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:35,928 - INFO - Response - Page 6:
2025-06-25 18:00:36,131 - INFO - 第 6 页获取到 100 条记录
2025-06-25 18:00:36,131 - INFO - Request Parameters - Page 7:
2025-06-25 18:00:36,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 18:00:36,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 18:00:36,569 - INFO - Response - Page 7:
2025-06-25 18:00:36,772 - INFO - 第 7 页获取到 26 条记录
2025-06-25 18:00:36,772 - INFO - 查询完成，共获取到 626 条记录
2025-06-25 18:00:36,772 - INFO - 获取到 626 条表单数据
2025-06-25 18:00:36,772 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-25 18:00:36,772 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-25 18:00:37,241 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-25 18:00:37,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20801.66, 'new_value': 24173.96}, {'field': 'offline_amount', 'old_value': 89743.99, 'new_value': 92294.47}, {'field': 'total_amount', 'old_value': 110545.65, 'new_value': 116468.43}, {'field': 'order_count', 'old_value': 2770, 'new_value': 2919}]
2025-06-25 18:00:37,241 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKY
2025-06-25 18:00:37,741 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKY
2025-06-25 18:00:37,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3313.73, 'new_value': 3439.05}, {'field': 'offline_amount', 'old_value': 62412.02, 'new_value': 64908.81}, {'field': 'total_amount', 'old_value': 65725.75, 'new_value': 68347.86}, {'field': 'order_count', 'old_value': 2647, 'new_value': 2766}]
2025-06-25 18:00:37,741 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-25 18:00:38,163 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-25 18:00:38,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10899.59, 'new_value': 11306.24}, {'field': 'offline_amount', 'old_value': 26639.64, 'new_value': 28115.49}, {'field': 'total_amount', 'old_value': 37539.23, 'new_value': 39421.73}, {'field': 'order_count', 'old_value': 825, 'new_value': 866}]
2025-06-25 18:00:38,163 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-25 18:00:38,647 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-25 18:00:38,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27735.63, 'new_value': 31374.25}, {'field': 'offline_amount', 'old_value': 32964.83, 'new_value': 34211.49}, {'field': 'total_amount', 'old_value': 60700.46, 'new_value': 65585.74}, {'field': 'order_count', 'old_value': 2993, 'new_value': 3174}]
2025-06-25 18:00:38,647 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-25 18:00:39,053 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-25 18:00:39,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 304806.47, 'new_value': 324713.55}, {'field': 'total_amount', 'old_value': 305965.47, 'new_value': 325872.55}, {'field': 'order_count', 'old_value': 4643, 'new_value': 4837}]
2025-06-25 18:00:39,053 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-25 18:00:39,491 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-25 18:00:39,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53505.81, 'new_value': 56550.57}, {'field': 'offline_amount', 'old_value': 422958.84, 'new_value': 440552.6}, {'field': 'total_amount', 'old_value': 476464.65, 'new_value': 497103.17}, {'field': 'order_count', 'old_value': 1486, 'new_value': 1556}]
2025-06-25 18:00:39,491 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-25 18:00:39,991 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-25 18:00:39,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118543.3, 'new_value': 124284.3}, {'field': 'total_amount', 'old_value': 119547.3, 'new_value': 125288.3}, {'field': 'order_count', 'old_value': 194, 'new_value': 206}]
2025-06-25 18:00:40,006 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZD
2025-06-25 18:00:40,585 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZD
2025-06-25 18:00:40,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52895.0, 'new_value': 54815.0}, {'field': 'total_amount', 'old_value': 52895.0, 'new_value': 54815.0}, {'field': 'order_count', 'old_value': 356, 'new_value': 366}]
2025-06-25 18:00:40,585 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXR
2025-06-25 18:00:41,085 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXR
2025-06-25 18:00:41,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84155.81, 'new_value': 89960.8}, {'field': 'total_amount', 'old_value': 131642.55, 'new_value': 137447.54}, {'field': 'order_count', 'old_value': 6781, 'new_value': 7087}]
2025-06-25 18:00:41,085 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-25 18:00:41,522 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-25 18:00:41,522 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26371.14, 'new_value': 27742.93}, {'field': 'offline_amount', 'old_value': 24063.57, 'new_value': 25030.57}, {'field': 'total_amount', 'old_value': 50434.71, 'new_value': 52773.5}, {'field': 'order_count', 'old_value': 2347, 'new_value': 2460}]
2025-06-25 18:00:41,522 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7L
2025-06-25 18:00:41,944 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7L
2025-06-25 18:00:41,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174807.8, 'new_value': 178397.4}, {'field': 'total_amount', 'old_value': 174807.8, 'new_value': 178397.4}, {'field': 'order_count', 'old_value': 1838, 'new_value': 1865}]
2025-06-25 18:00:41,944 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-25 18:00:42,397 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-25 18:00:42,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34923.5, 'new_value': 37083.5}, {'field': 'total_amount', 'old_value': 36127.02, 'new_value': 38287.02}, {'field': 'order_count', 'old_value': 248, 'new_value': 258}]
2025-06-25 18:00:42,397 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-25 18:00:42,850 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-25 18:00:42,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1650000.0, 'new_value': 1700000.0}, {'field': 'total_amount', 'old_value': 1750000.0, 'new_value': 1800000.0}, {'field': 'order_count', 'old_value': 375, 'new_value': 376}]
2025-06-25 18:00:42,850 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-25 18:00:43,303 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-25 18:00:43,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5599.58, 'new_value': 6011.12}, {'field': 'offline_amount', 'old_value': 116471.6, 'new_value': 120738.1}, {'field': 'total_amount', 'old_value': 122071.18, 'new_value': 126749.22}, {'field': 'order_count', 'old_value': 4728, 'new_value': 4909}]
2025-06-25 18:00:43,303 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-25 18:00:43,741 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-25 18:00:43,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42031.36, 'new_value': 43852.11}, {'field': 'offline_amount', 'old_value': 53151.27, 'new_value': 54510.39}, {'field': 'total_amount', 'old_value': 95182.63, 'new_value': 98362.5}, {'field': 'order_count', 'old_value': 2382, 'new_value': 2474}]
2025-06-25 18:00:43,741 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-25 18:00:44,178 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-25 18:00:44,178 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111581.0, 'new_value': 118725.0}, {'field': 'offline_amount', 'old_value': 96797.0, 'new_value': 101696.0}, {'field': 'total_amount', 'old_value': 208378.0, 'new_value': 220421.0}, {'field': 'order_count', 'old_value': 8629, 'new_value': 9138}]
2025-06-25 18:00:44,178 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-25 18:00:44,600 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-25 18:00:44,600 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 941368.51, 'new_value': 977136.61}, {'field': 'total_amount', 'old_value': 941368.51, 'new_value': 977136.61}, {'field': 'order_count', 'old_value': 3213, 'new_value': 3349}]
2025-06-25 18:00:44,600 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-25 18:00:45,022 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-25 18:00:45,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4908086.49, 'new_value': 5071296.49}, {'field': 'total_amount', 'old_value': 4908086.49, 'new_value': 5071296.49}, {'field': 'order_count', 'old_value': 99858, 'new_value': 102539}]
2025-06-25 18:00:45,022 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-25 18:00:45,538 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-25 18:00:45,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1979588.0, 'new_value': 2029588.0}, {'field': 'total_amount', 'old_value': 1979588.0, 'new_value': 2029588.0}, {'field': 'order_count', 'old_value': 3084, 'new_value': 3085}]
2025-06-25 18:00:45,538 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-25 18:00:45,959 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-25 18:00:45,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'order_count', 'old_value': 797, 'new_value': 798}]
2025-06-25 18:00:45,959 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-25 18:00:46,428 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-25 18:00:46,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'order_count', 'old_value': 926, 'new_value': 927}]
2025-06-25 18:00:46,428 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-25 18:00:47,022 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-25 18:00:47,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 929324.66, 'new_value': 937057.36}, {'field': 'total_amount', 'old_value': 929324.66, 'new_value': 937057.36}, {'field': 'order_count', 'old_value': 4228, 'new_value': 4337}]
2025-06-25 18:00:47,022 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-25 18:00:47,428 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-25 18:00:47,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133866.99, 'new_value': 138572.07}, {'field': 'total_amount', 'old_value': 133866.99, 'new_value': 138572.07}, {'field': 'order_count', 'old_value': 9511, 'new_value': 9840}]
2025-06-25 18:00:47,428 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-25 18:00:47,959 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-25 18:00:47,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'total_amount', 'old_value': 175000.0, 'new_value': 180000.0}, {'field': 'order_count', 'old_value': 592, 'new_value': 593}]
2025-06-25 18:00:47,959 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-25 18:00:48,413 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-25 18:00:48,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 410308.0, 'new_value': 417834.0}, {'field': 'total_amount', 'old_value': 447964.0, 'new_value': 455490.0}, {'field': 'order_count', 'old_value': 9316, 'new_value': 9477}]
2025-06-25 18:00:48,413 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-25 18:00:48,834 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-25 18:00:48,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141078.0, 'new_value': 146176.0}, {'field': 'total_amount', 'old_value': 141078.0, 'new_value': 146176.0}, {'field': 'order_count', 'old_value': 12642, 'new_value': 13092}]
2025-06-25 18:00:48,834 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM42
2025-06-25 18:00:49,366 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM42
2025-06-25 18:00:49,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11260.0, 'new_value': 11640.0}, {'field': 'total_amount', 'old_value': 11260.0, 'new_value': 11640.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-25 18:00:49,366 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-25 18:00:49,772 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-25 18:00:49,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2730083.01, 'new_value': 2821548.31}, {'field': 'total_amount', 'old_value': 2730083.01, 'new_value': 2821548.31}, {'field': 'order_count', 'old_value': 5130, 'new_value': 5326}]
2025-06-25 18:00:49,772 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-25 18:00:50,225 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-25 18:00:50,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 755535.0, 'new_value': 784277.0}, {'field': 'total_amount', 'old_value': 755535.0, 'new_value': 784277.0}, {'field': 'order_count', 'old_value': 4113, 'new_value': 4259}]
2025-06-25 18:00:50,225 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-25 18:00:50,663 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-25 18:00:50,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155274.91, 'new_value': 162450.81}, {'field': 'total_amount', 'old_value': 155274.91, 'new_value': 162450.81}, {'field': 'order_count', 'old_value': 16953, 'new_value': 17816}]
2025-06-25 18:00:50,663 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-25 18:00:51,116 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-25 18:00:51,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60917.69, 'new_value': 65412.87}, {'field': 'total_amount', 'old_value': 64337.44, 'new_value': 68832.62}, {'field': 'order_count', 'old_value': 485, 'new_value': 523}]
2025-06-25 18:00:51,116 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMPP
2025-06-25 18:00:51,553 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMPP
2025-06-25 18:00:51,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5720.5, 'new_value': 6680.5}, {'field': 'total_amount', 'old_value': 9368.5, 'new_value': 10328.5}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-06-25 18:00:51,569 - INFO - 开始更新记录 - 表单实例ID: FINST-IOC66G71POLWR3A59C81N8WVTUQ53HBQGFBCMG3
2025-06-25 18:00:52,084 - INFO - 更新表单数据成功: FINST-IOC66G71POLWR3A59C81N8WVTUQ53HBQGFBCMG3
2025-06-25 18:00:52,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1.0, 'new_value': 2.0}, {'field': 'offline_amount', 'old_value': 1.0, 'new_value': 2.0}, {'field': 'total_amount', 'old_value': 2.0, 'new_value': 4.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-25 18:00:52,084 - INFO - 日期 2025-06 处理完成 - 更新: 33 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:52,084 - INFO - 数据同步完成！更新: 33 条，插入: 0 条，错误: 0 条
2025-06-25 18:00:52,100 - INFO - =================同步完成====================
2025-06-25 21:00:02,927 - INFO - =================使用默认全量同步=============
2025-06-25 21:00:04,662 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-25 21:00:04,662 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-25 21:00:04,693 - INFO - 开始处理日期: 2025-01
2025-06-25 21:00:04,709 - INFO - Request Parameters - Page 1:
2025-06-25 21:00:04,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:04,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:06,225 - INFO - Response - Page 1:
2025-06-25 21:00:06,428 - INFO - 第 1 页获取到 100 条记录
2025-06-25 21:00:06,428 - INFO - Request Parameters - Page 2:
2025-06-25 21:00:06,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:06,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:07,053 - INFO - Response - Page 2:
2025-06-25 21:00:07,257 - INFO - 第 2 页获取到 100 条记录
2025-06-25 21:00:07,257 - INFO - Request Parameters - Page 3:
2025-06-25 21:00:07,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:07,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:07,788 - INFO - Response - Page 3:
2025-06-25 21:00:07,991 - INFO - 第 3 页获取到 100 条记录
2025-06-25 21:00:07,991 - INFO - Request Parameters - Page 4:
2025-06-25 21:00:07,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:07,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:08,538 - INFO - Response - Page 4:
2025-06-25 21:00:08,741 - INFO - 第 4 页获取到 100 条记录
2025-06-25 21:00:08,741 - INFO - Request Parameters - Page 5:
2025-06-25 21:00:08,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:08,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:09,288 - INFO - Response - Page 5:
2025-06-25 21:00:09,492 - INFO - 第 5 页获取到 100 条记录
2025-06-25 21:00:09,492 - INFO - Request Parameters - Page 6:
2025-06-25 21:00:09,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:09,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:10,070 - INFO - Response - Page 6:
2025-06-25 21:00:10,273 - INFO - 第 6 页获取到 100 条记录
2025-06-25 21:00:10,273 - INFO - Request Parameters - Page 7:
2025-06-25 21:00:10,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:10,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:10,742 - INFO - Response - Page 7:
2025-06-25 21:00:10,945 - INFO - 第 7 页获取到 82 条记录
2025-06-25 21:00:10,945 - INFO - 查询完成，共获取到 682 条记录
2025-06-25 21:00:10,945 - INFO - 获取到 682 条表单数据
2025-06-25 21:00:10,945 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-25 21:00:10,961 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 21:00:10,961 - INFO - 开始处理日期: 2025-02
2025-06-25 21:00:10,961 - INFO - Request Parameters - Page 1:
2025-06-25 21:00:10,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:10,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:11,539 - INFO - Response - Page 1:
2025-06-25 21:00:11,742 - INFO - 第 1 页获取到 100 条记录
2025-06-25 21:00:11,742 - INFO - Request Parameters - Page 2:
2025-06-25 21:00:11,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:11,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:12,368 - INFO - Response - Page 2:
2025-06-25 21:00:12,571 - INFO - 第 2 页获取到 100 条记录
2025-06-25 21:00:12,571 - INFO - Request Parameters - Page 3:
2025-06-25 21:00:12,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:12,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:13,102 - INFO - Response - Page 3:
2025-06-25 21:00:13,305 - INFO - 第 3 页获取到 100 条记录
2025-06-25 21:00:13,305 - INFO - Request Parameters - Page 4:
2025-06-25 21:00:13,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:13,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:13,853 - INFO - Response - Page 4:
2025-06-25 21:00:14,056 - INFO - 第 4 页获取到 100 条记录
2025-06-25 21:00:14,056 - INFO - Request Parameters - Page 5:
2025-06-25 21:00:14,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:14,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:14,603 - INFO - Response - Page 5:
2025-06-25 21:00:14,806 - INFO - 第 5 页获取到 100 条记录
2025-06-25 21:00:14,806 - INFO - Request Parameters - Page 6:
2025-06-25 21:00:14,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:14,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:15,416 - INFO - Response - Page 6:
2025-06-25 21:00:15,619 - INFO - 第 6 页获取到 100 条记录
2025-06-25 21:00:15,619 - INFO - Request Parameters - Page 7:
2025-06-25 21:00:15,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:15,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:16,056 - INFO - Response - Page 7:
2025-06-25 21:00:16,260 - INFO - 第 7 页获取到 70 条记录
2025-06-25 21:00:16,260 - INFO - 查询完成，共获取到 670 条记录
2025-06-25 21:00:16,260 - INFO - 获取到 670 条表单数据
2025-06-25 21:00:16,260 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-25 21:00:16,275 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 21:00:16,275 - INFO - 开始处理日期: 2025-03
2025-06-25 21:00:16,275 - INFO - Request Parameters - Page 1:
2025-06-25 21:00:16,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:16,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:16,775 - INFO - Response - Page 1:
2025-06-25 21:00:16,979 - INFO - 第 1 页获取到 100 条记录
2025-06-25 21:00:16,979 - INFO - Request Parameters - Page 2:
2025-06-25 21:00:16,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:16,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:17,447 - INFO - Response - Page 2:
2025-06-25 21:00:17,651 - INFO - 第 2 页获取到 100 条记录
2025-06-25 21:00:17,651 - INFO - Request Parameters - Page 3:
2025-06-25 21:00:17,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:17,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:18,213 - INFO - Response - Page 3:
2025-06-25 21:00:18,417 - INFO - 第 3 页获取到 100 条记录
2025-06-25 21:00:18,417 - INFO - Request Parameters - Page 4:
2025-06-25 21:00:18,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:18,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:18,948 - INFO - Response - Page 4:
2025-06-25 21:00:19,151 - INFO - 第 4 页获取到 100 条记录
2025-06-25 21:00:19,151 - INFO - Request Parameters - Page 5:
2025-06-25 21:00:19,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:19,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:19,651 - INFO - Response - Page 5:
2025-06-25 21:00:19,855 - INFO - 第 5 页获取到 100 条记录
2025-06-25 21:00:19,855 - INFO - Request Parameters - Page 6:
2025-06-25 21:00:19,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:19,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:20,386 - INFO - Response - Page 6:
2025-06-25 21:00:20,589 - INFO - 第 6 页获取到 100 条记录
2025-06-25 21:00:20,589 - INFO - Request Parameters - Page 7:
2025-06-25 21:00:20,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:20,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:21,074 - INFO - Response - Page 7:
2025-06-25 21:00:21,277 - INFO - 第 7 页获取到 61 条记录
2025-06-25 21:00:21,277 - INFO - 查询完成，共获取到 661 条记录
2025-06-25 21:00:21,277 - INFO - 获取到 661 条表单数据
2025-06-25 21:00:21,277 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-25 21:00:21,293 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 21:00:21,293 - INFO - 开始处理日期: 2025-04
2025-06-25 21:00:21,293 - INFO - Request Parameters - Page 1:
2025-06-25 21:00:21,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:21,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:21,793 - INFO - Response - Page 1:
2025-06-25 21:00:21,996 - INFO - 第 1 页获取到 100 条记录
2025-06-25 21:00:21,996 - INFO - Request Parameters - Page 2:
2025-06-25 21:00:21,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:21,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:22,684 - INFO - Response - Page 2:
2025-06-25 21:00:22,887 - INFO - 第 2 页获取到 100 条记录
2025-06-25 21:00:22,887 - INFO - Request Parameters - Page 3:
2025-06-25 21:00:22,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:22,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:23,465 - INFO - Response - Page 3:
2025-06-25 21:00:23,668 - INFO - 第 3 页获取到 100 条记录
2025-06-25 21:00:23,668 - INFO - Request Parameters - Page 4:
2025-06-25 21:00:23,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:23,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:24,215 - INFO - Response - Page 4:
2025-06-25 21:00:24,419 - INFO - 第 4 页获取到 100 条记录
2025-06-25 21:00:24,419 - INFO - Request Parameters - Page 5:
2025-06-25 21:00:24,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:24,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:24,903 - INFO - Response - Page 5:
2025-06-25 21:00:25,106 - INFO - 第 5 页获取到 100 条记录
2025-06-25 21:00:25,106 - INFO - Request Parameters - Page 6:
2025-06-25 21:00:25,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:25,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:25,560 - INFO - Response - Page 6:
2025-06-25 21:00:25,763 - INFO - 第 6 页获取到 100 条记录
2025-06-25 21:00:25,763 - INFO - Request Parameters - Page 7:
2025-06-25 21:00:25,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:25,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:26,138 - INFO - Response - Page 7:
2025-06-25 21:00:26,341 - INFO - 第 7 页获取到 56 条记录
2025-06-25 21:00:26,341 - INFO - 查询完成，共获取到 656 条记录
2025-06-25 21:00:26,341 - INFO - 获取到 656 条表单数据
2025-06-25 21:00:26,341 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-25 21:00:26,357 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 21:00:26,357 - INFO - 开始处理日期: 2025-05
2025-06-25 21:00:26,357 - INFO - Request Parameters - Page 1:
2025-06-25 21:00:26,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:26,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:26,873 - INFO - Response - Page 1:
2025-06-25 21:00:27,076 - INFO - 第 1 页获取到 100 条记录
2025-06-25 21:00:27,076 - INFO - Request Parameters - Page 2:
2025-06-25 21:00:27,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:27,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:27,607 - INFO - Response - Page 2:
2025-06-25 21:00:27,810 - INFO - 第 2 页获取到 100 条记录
2025-06-25 21:00:27,810 - INFO - Request Parameters - Page 3:
2025-06-25 21:00:27,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:27,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:28,373 - INFO - Response - Page 3:
2025-06-25 21:00:28,576 - INFO - 第 3 页获取到 100 条记录
2025-06-25 21:00:28,576 - INFO - Request Parameters - Page 4:
2025-06-25 21:00:28,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:28,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:29,170 - INFO - Response - Page 4:
2025-06-25 21:00:29,373 - INFO - 第 4 页获取到 100 条记录
2025-06-25 21:00:29,373 - INFO - Request Parameters - Page 5:
2025-06-25 21:00:29,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:29,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:29,889 - INFO - Response - Page 5:
2025-06-25 21:00:30,092 - INFO - 第 5 页获取到 100 条记录
2025-06-25 21:00:30,092 - INFO - Request Parameters - Page 6:
2025-06-25 21:00:30,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:30,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:30,593 - INFO - Response - Page 6:
2025-06-25 21:00:30,796 - INFO - 第 6 页获取到 100 条记录
2025-06-25 21:00:30,796 - INFO - Request Parameters - Page 7:
2025-06-25 21:00:30,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:30,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:31,312 - INFO - Response - Page 7:
2025-06-25 21:00:31,515 - INFO - 第 7 页获取到 65 条记录
2025-06-25 21:00:31,515 - INFO - 查询完成，共获取到 665 条记录
2025-06-25 21:00:31,515 - INFO - 获取到 665 条表单数据
2025-06-25 21:00:31,530 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-25 21:00:31,530 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-25 21:00:31,530 - INFO - 开始处理日期: 2025-06
2025-06-25 21:00:31,530 - INFO - Request Parameters - Page 1:
2025-06-25 21:00:31,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:31,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:32,077 - INFO - Response - Page 1:
2025-06-25 21:00:32,281 - INFO - 第 1 页获取到 100 条记录
2025-06-25 21:00:32,281 - INFO - Request Parameters - Page 2:
2025-06-25 21:00:32,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:32,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:32,796 - INFO - Response - Page 2:
2025-06-25 21:00:33,000 - INFO - 第 2 页获取到 100 条记录
2025-06-25 21:00:33,000 - INFO - Request Parameters - Page 3:
2025-06-25 21:00:33,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:33,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:33,547 - INFO - Response - Page 3:
2025-06-25 21:00:33,750 - INFO - 第 3 页获取到 100 条记录
2025-06-25 21:00:33,750 - INFO - Request Parameters - Page 4:
2025-06-25 21:00:33,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:33,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:34,250 - INFO - Response - Page 4:
2025-06-25 21:00:34,453 - INFO - 第 4 页获取到 100 条记录
2025-06-25 21:00:34,453 - INFO - Request Parameters - Page 5:
2025-06-25 21:00:34,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:34,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:34,953 - INFO - Response - Page 5:
2025-06-25 21:00:35,157 - INFO - 第 5 页获取到 100 条记录
2025-06-25 21:00:35,157 - INFO - Request Parameters - Page 6:
2025-06-25 21:00:35,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:35,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:35,735 - INFO - Response - Page 6:
2025-06-25 21:00:35,938 - INFO - 第 6 页获取到 100 条记录
2025-06-25 21:00:35,938 - INFO - Request Parameters - Page 7:
2025-06-25 21:00:35,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-25 21:00:35,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-25 21:00:36,313 - INFO - Response - Page 7:
2025-06-25 21:00:36,516 - INFO - 第 7 页获取到 26 条记录
2025-06-25 21:00:36,516 - INFO - 查询完成，共获取到 626 条记录
2025-06-25 21:00:36,516 - INFO - 获取到 626 条表单数据
2025-06-25 21:00:36,516 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-25 21:00:36,516 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-25 21:00:37,063 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-25 21:00:37,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25645.4, 'new_value': 26811.27}, {'field': 'offline_amount', 'old_value': 14606.22, 'new_value': 15255.57}, {'field': 'total_amount', 'old_value': 40251.62, 'new_value': 42066.84}, {'field': 'order_count', 'old_value': 1675, 'new_value': 1752}]
2025-06-25 21:00:37,063 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-25 21:00:37,579 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-25 21:00:37,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 366774.0, 'new_value': 379823.0}, {'field': 'offline_amount', 'old_value': 143658.0, 'new_value': 149809.0}, {'field': 'total_amount', 'old_value': 510432.0, 'new_value': 529632.0}, {'field': 'order_count', 'old_value': 553, 'new_value': 582}]
2025-06-25 21:00:37,579 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Y
2025-06-25 21:00:38,001 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Y
2025-06-25 21:00:38,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280353.0, 'new_value': 310153.0}, {'field': 'total_amount', 'old_value': 310353.0, 'new_value': 340153.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-06-25 21:00:38,001 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Y
2025-06-25 21:00:38,501 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Y
2025-06-25 21:00:38,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237365.0, 'new_value': 258365.0}, {'field': 'total_amount', 'old_value': 287365.0, 'new_value': 308365.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-06-25 21:00:38,501 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Y
2025-06-25 21:00:38,986 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM3Y
2025-06-25 21:00:38,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254983.0, 'new_value': 289983.0}, {'field': 'total_amount', 'old_value': 254983.0, 'new_value': 289983.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-06-25 21:00:38,986 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-25 21:00:39,377 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-25 21:00:39,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28002.5, 'new_value': 29612.5}, {'field': 'total_amount', 'old_value': 28002.5, 'new_value': 29612.5}, {'field': 'order_count', 'old_value': 5798, 'new_value': 6143}]
2025-06-25 21:00:39,377 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Y
2025-06-25 21:00:39,846 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Y
2025-06-25 21:00:39,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6420.0, 'new_value': 11620.0}, {'field': 'total_amount', 'old_value': 6420.0, 'new_value': 11620.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-06-25 21:00:39,846 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-25 21:00:40,315 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-25 21:00:40,315 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24173.96, 'new_value': 20801.66}, {'field': 'offline_amount', 'old_value': 92294.47, 'new_value': 93265.29}, {'field': 'total_amount', 'old_value': 116468.43, 'new_value': 114066.95}]
2025-06-25 21:00:40,315 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-25 21:00:40,737 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-25 21:00:40,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117618.76, 'new_value': 121148.59}, {'field': 'total_amount', 'old_value': 117618.76, 'new_value': 121148.59}, {'field': 'order_count', 'old_value': 565, 'new_value': 581}]
2025-06-25 21:00:40,737 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-25 21:00:41,190 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-25 21:00:41,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50594.0, 'new_value': 53187.0}, {'field': 'offline_amount', 'old_value': 165173.0, 'new_value': 173424.0}, {'field': 'total_amount', 'old_value': 215767.0, 'new_value': 226611.0}, {'field': 'order_count', 'old_value': 1538, 'new_value': 1612}]
2025-06-25 21:00:41,190 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKY
2025-06-25 21:00:41,612 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKY
2025-06-25 21:00:41,612 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3439.05, 'new_value': 3313.73}, {'field': 'offline_amount', 'old_value': 64908.81, 'new_value': 62656.34}, {'field': 'total_amount', 'old_value': 68347.86, 'new_value': 65970.07}]
2025-06-25 21:00:41,612 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-25 21:00:42,018 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-25 21:00:42,018 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1059871.0, 'new_value': 1108791.0}, {'field': 'total_amount', 'old_value': 1348097.0, 'new_value': 1397017.0}, {'field': 'order_count', 'old_value': 1454, 'new_value': 1519}]
2025-06-25 21:00:42,018 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-25 21:00:42,534 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-25 21:00:42,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71606.0, 'new_value': 79127.0}, {'field': 'total_amount', 'old_value': 166718.0, 'new_value': 174239.0}, {'field': 'order_count', 'old_value': 3552, 'new_value': 3708}]
2025-06-25 21:00:42,534 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-25 21:00:42,987 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-25 21:00:42,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32914.25, 'new_value': 34357.64}, {'field': 'offline_amount', 'old_value': 93103.03, 'new_value': 96871.33}, {'field': 'total_amount', 'old_value': 126017.28, 'new_value': 131228.97}, {'field': 'order_count', 'old_value': 1829, 'new_value': 1906}]
2025-06-25 21:00:42,987 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUY
2025-06-25 21:00:43,394 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUY
2025-06-25 21:00:43,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51969.48, 'new_value': 55026.56}, {'field': 'total_amount', 'old_value': 51969.48, 'new_value': 55026.56}, {'field': 'order_count', 'old_value': 2332, 'new_value': 2450}]
2025-06-25 21:00:43,394 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-25 21:00:43,816 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-25 21:00:43,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119173.59, 'new_value': 124071.08}, {'field': 'total_amount', 'old_value': 119173.59, 'new_value': 124071.08}, {'field': 'order_count', 'old_value': 221, 'new_value': 236}]
2025-06-25 21:00:43,816 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-25 21:00:44,253 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-25 21:00:44,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142428.98, 'new_value': 148886.63}, {'field': 'total_amount', 'old_value': 142428.98, 'new_value': 148886.63}, {'field': 'order_count', 'old_value': 922, 'new_value': 968}]
2025-06-25 21:00:44,253 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-25 21:00:44,675 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-25 21:00:44,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 482006.56, 'new_value': 505153.44}, {'field': 'total_amount', 'old_value': 482006.56, 'new_value': 505153.44}, {'field': 'order_count', 'old_value': 3396, 'new_value': 3609}]
2025-06-25 21:00:44,675 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-25 21:00:45,113 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-25 21:00:45,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 841880.0, 'new_value': 877931.6}, {'field': 'total_amount', 'old_value': 926236.3, 'new_value': 962287.9}, {'field': 'order_count', 'old_value': 77, 'new_value': 82}]
2025-06-25 21:00:45,113 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFZ
2025-06-25 21:00:45,519 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFZ
2025-06-25 21:00:45,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60461.65, 'new_value': 64646.23}, {'field': 'total_amount', 'old_value': 68259.19, 'new_value': 72443.77}, {'field': 'order_count', 'old_value': 1917, 'new_value': 1997}]
2025-06-25 21:00:45,535 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-25 21:00:46,004 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-25 21:00:46,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73900.16, 'new_value': 77518.3}, {'field': 'total_amount', 'old_value': 73900.16, 'new_value': 77518.3}, {'field': 'order_count', 'old_value': 3872, 'new_value': 4065}]
2025-06-25 21:00:46,004 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLB
2025-06-25 21:00:46,504 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLB
2025-06-25 21:00:46,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42830.2, 'new_value': 44813.2}, {'field': 'total_amount', 'old_value': 42830.2, 'new_value': 44813.2}, {'field': 'order_count', 'old_value': 2235, 'new_value': 2327}]
2025-06-25 21:00:46,504 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-25 21:00:46,926 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-25 21:00:46,926 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450034.2, 'new_value': 473143.98}, {'field': 'total_amount', 'old_value': 450034.2, 'new_value': 473143.98}, {'field': 'order_count', 'old_value': 3423, 'new_value': 3602}]
2025-06-25 21:00:46,926 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-06-25 21:00:47,411 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3C
2025-06-25 21:00:47,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3386.56, 'new_value': 3745.01}, {'field': 'offline_amount', 'old_value': 110950.3, 'new_value': 113965.46}, {'field': 'total_amount', 'old_value': 114336.86, 'new_value': 117710.47}, {'field': 'order_count', 'old_value': 552, 'new_value': 571}]
2025-06-25 21:00:47,411 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-25 21:00:47,911 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-25 21:00:47,911 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14461.9, 'new_value': 14739.3}, {'field': 'offline_amount', 'old_value': 33281.8, 'new_value': 35032.8}, {'field': 'total_amount', 'old_value': 47743.7, 'new_value': 49772.1}, {'field': 'order_count', 'old_value': 152, 'new_value': 159}]
2025-06-25 21:00:47,911 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-25 21:00:48,380 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-25 21:00:48,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 173134.18, 'new_value': 182570.18}, {'field': 'offline_amount', 'old_value': 78192.15, 'new_value': 81022.15}, {'field': 'total_amount', 'old_value': 251326.33, 'new_value': 263592.33}, {'field': 'order_count', 'old_value': 1082, 'new_value': 1129}]
2025-06-25 21:00:48,380 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-25 21:00:48,833 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-25 21:00:48,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96154.0, 'new_value': 104523.0}, {'field': 'offline_amount', 'old_value': 256344.0, 'new_value': 259655.0}, {'field': 'total_amount', 'old_value': 352498.0, 'new_value': 364178.0}, {'field': 'order_count', 'old_value': 2606, 'new_value': 2690}]
2025-06-25 21:00:48,833 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-25 21:00:49,302 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-25 21:00:49,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124952.0, 'new_value': 127296.0}, {'field': 'total_amount', 'old_value': 124952.0, 'new_value': 127296.0}, {'field': 'order_count', 'old_value': 173, 'new_value': 184}]
2025-06-25 21:00:49,302 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-25 21:00:49,755 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-25 21:00:49,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12483.41, 'new_value': 13109.83}, {'field': 'offline_amount', 'old_value': 193550.22, 'new_value': 200815.22}, {'field': 'total_amount', 'old_value': 206033.63, 'new_value': 213925.05}, {'field': 'order_count', 'old_value': 1366, 'new_value': 1423}]
2025-06-25 21:00:49,771 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPD
2025-06-25 21:00:50,224 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPD
2025-06-25 21:00:50,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81974.57, 'new_value': 85602.66}, {'field': 'offline_amount', 'old_value': 40628.22, 'new_value': 41655.08}, {'field': 'total_amount', 'old_value': 122602.79, 'new_value': 127257.74}, {'field': 'order_count', 'old_value': 4527, 'new_value': 4708}]
2025-06-25 21:00:50,224 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXQ
2025-06-25 21:00:50,740 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXQ
2025-06-25 21:00:50,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77509.91, 'new_value': 79854.22}, {'field': 'offline_amount', 'old_value': 56914.47, 'new_value': 59510.31}, {'field': 'total_amount', 'old_value': 134424.38, 'new_value': 139364.53}, {'field': 'order_count', 'old_value': 6017, 'new_value': 6215}]
2025-06-25 21:00:50,740 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-25 21:00:51,178 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-25 21:00:51,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52372.0, 'new_value': 54077.0}, {'field': 'total_amount', 'old_value': 52372.0, 'new_value': 54077.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 167}]
2025-06-25 21:00:51,178 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9R
2025-06-25 21:00:51,678 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9R
2025-06-25 21:00:51,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168775.65, 'new_value': 173701.09}, {'field': 'total_amount', 'old_value': 204963.78, 'new_value': 209889.22}, {'field': 'order_count', 'old_value': 3941, 'new_value': 4052}]
2025-06-25 21:00:51,678 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCR
2025-06-25 21:00:52,194 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCR
2025-06-25 21:00:52,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26068.7, 'new_value': 27994.7}, {'field': 'total_amount', 'old_value': 55249.76, 'new_value': 57175.76}, {'field': 'order_count', 'old_value': 4223, 'new_value': 4322}]
2025-06-25 21:00:52,194 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-25 21:00:52,631 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-25 21:00:52,631 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30575.24, 'new_value': 31954.53}, {'field': 'offline_amount', 'old_value': 19192.3, 'new_value': 19936.6}, {'field': 'total_amount', 'old_value': 49767.54, 'new_value': 51891.13}, {'field': 'order_count', 'old_value': 3071, 'new_value': 3212}]
2025-06-25 21:00:52,631 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-25 21:00:53,085 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-25 21:00:53,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 516259.99, 'new_value': 545503.62}, {'field': 'total_amount', 'old_value': 516259.99, 'new_value': 545503.62}, {'field': 'order_count', 'old_value': 1668, 'new_value': 1769}]
2025-06-25 21:00:53,085 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-25 21:00:53,569 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-25 21:00:53,569 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3972.58, 'new_value': 4066.98}, {'field': 'offline_amount', 'old_value': 129946.05, 'new_value': 136198.75}, {'field': 'total_amount', 'old_value': 133918.63, 'new_value': 140265.73}, {'field': 'order_count', 'old_value': 2049, 'new_value': 2147}]
2025-06-25 21:00:53,569 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-25 21:00:53,991 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-25 21:00:53,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255837.4, 'new_value': 260378.4}, {'field': 'total_amount', 'old_value': 255837.4, 'new_value': 260378.4}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-06-25 21:00:53,991 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-25 21:00:54,351 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-25 21:00:54,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44466.9, 'new_value': 47250.1}, {'field': 'total_amount', 'old_value': 44466.9, 'new_value': 47250.1}, {'field': 'order_count', 'old_value': 262, 'new_value': 274}]
2025-06-25 21:00:54,351 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-25 21:00:54,835 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-25 21:00:54,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60709.0, 'new_value': 64368.0}, {'field': 'offline_amount', 'old_value': 115735.0, 'new_value': 119756.0}, {'field': 'total_amount', 'old_value': 176444.0, 'new_value': 184124.0}, {'field': 'order_count', 'old_value': 4022, 'new_value': 4206}]
2025-06-25 21:00:54,835 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-06-25 21:00:55,304 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLK
2025-06-25 21:00:55,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25410.4, 'new_value': 26288.1}, {'field': 'offline_amount', 'old_value': 180481.8, 'new_value': 187252.0}, {'field': 'total_amount', 'old_value': 205892.2, 'new_value': 213540.1}, {'field': 'order_count', 'old_value': 6287, 'new_value': 6482}]
2025-06-25 21:00:55,304 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-25 21:00:55,773 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-25 21:00:55,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 472099.29, 'new_value': 486290.25}, {'field': 'total_amount', 'old_value': 472099.29, 'new_value': 486290.25}, {'field': 'order_count', 'old_value': 5090, 'new_value': 5303}]
2025-06-25 21:00:55,773 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-25 21:00:56,179 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-25 21:00:56,179 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121426.06, 'new_value': 125513.92}, {'field': 'offline_amount', 'old_value': 309693.5, 'new_value': 326988.68}, {'field': 'total_amount', 'old_value': 431119.56, 'new_value': 452502.6}, {'field': 'order_count', 'old_value': 4276, 'new_value': 4454}]
2025-06-25 21:00:56,179 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-25 21:00:56,570 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-25 21:00:56,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6557.9, 'new_value': 6686.9}, {'field': 'offline_amount', 'old_value': 34969.9, 'new_value': 35157.9}, {'field': 'total_amount', 'old_value': 41527.8, 'new_value': 41844.8}, {'field': 'order_count', 'old_value': 58, 'new_value': 61}]
2025-06-25 21:00:56,570 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-25 21:00:57,086 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-25 21:00:57,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3529.4, 'new_value': 3828.6}, {'field': 'offline_amount', 'old_value': 76056.6, 'new_value': 79353.6}, {'field': 'total_amount', 'old_value': 79586.0, 'new_value': 83182.2}, {'field': 'order_count', 'old_value': 489, 'new_value': 522}]
2025-06-25 21:00:57,086 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-25 21:00:57,570 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-25 21:00:57,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31943.1, 'new_value': 33385.1}, {'field': 'total_amount', 'old_value': 31943.1, 'new_value': 33385.1}, {'field': 'order_count', 'old_value': 215, 'new_value': 224}]
2025-06-25 21:00:57,570 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-25 21:00:57,946 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-25 21:00:57,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156028.2, 'new_value': 161666.95}, {'field': 'offline_amount', 'old_value': 237903.5, 'new_value': 244770.7}, {'field': 'total_amount', 'old_value': 393931.7, 'new_value': 406437.65}, {'field': 'order_count', 'old_value': 3530, 'new_value': 3661}]
2025-06-25 21:00:57,946 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-25 21:00:58,446 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-25 21:00:58,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6011.12, 'new_value': 5599.58}, {'field': 'offline_amount', 'old_value': 120738.1, 'new_value': 117064.14}, {'field': 'total_amount', 'old_value': 126749.22, 'new_value': 122663.72}]
2025-06-25 21:00:58,446 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-25 21:00:58,899 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-25 21:00:58,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5055.0, 'new_value': 5555.0}, {'field': 'total_amount', 'old_value': 5055.0, 'new_value': 5555.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 182}]
2025-06-25 21:00:58,899 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-25 21:00:59,368 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-25 21:00:59,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30488.0, 'new_value': 31566.0}, {'field': 'total_amount', 'old_value': 30488.0, 'new_value': 31566.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 96}]
2025-06-25 21:00:59,368 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-25 21:00:59,837 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-25 21:00:59,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115607.0, 'new_value': 120436.0}, {'field': 'total_amount', 'old_value': 115775.0, 'new_value': 120604.0}, {'field': 'order_count', 'old_value': 334, 'new_value': 351}]
2025-06-25 21:00:59,837 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-25 21:01:00,259 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-25 21:01:00,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83096.0, 'new_value': 86554.0}, {'field': 'total_amount', 'old_value': 83096.0, 'new_value': 86554.0}, {'field': 'order_count', 'old_value': 11985, 'new_value': 12522}]
2025-06-25 21:01:00,259 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-25 21:01:00,775 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-25 21:01:00,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2305.0}, {'field': 'total_amount', 'old_value': 55394.0, 'new_value': 57699.0}, {'field': 'order_count', 'old_value': 11985, 'new_value': 12522}]
2025-06-25 21:01:00,775 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-25 21:01:01,181 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-25 21:01:01,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18022.41, 'new_value': 18990.41}, {'field': 'offline_amount', 'old_value': 10761.84, 'new_value': 11073.84}, {'field': 'total_amount', 'old_value': 28784.25, 'new_value': 30064.25}, {'field': 'order_count', 'old_value': 1071, 'new_value': 1110}]
2025-06-25 21:01:01,181 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-25 21:01:01,634 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-25 21:01:01,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12732.87, 'new_value': 13172.28}, {'field': 'offline_amount', 'old_value': 55887.65, 'new_value': 58674.84}, {'field': 'total_amount', 'old_value': 68620.52, 'new_value': 71847.12}, {'field': 'order_count', 'old_value': 1506, 'new_value': 1582}]
2025-06-25 21:01:01,634 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-25 21:01:02,150 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-25 21:01:02,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10040.1, 'new_value': 10411.1}, {'field': 'total_amount', 'old_value': 10040.1, 'new_value': 10411.1}, {'field': 'order_count', 'old_value': 860, 'new_value': 893}]
2025-06-25 21:01:02,150 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-25 21:01:02,635 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-25 21:01:02,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64508.35, 'new_value': 68231.24}, {'field': 'total_amount', 'old_value': 92905.47, 'new_value': 96628.36}, {'field': 'order_count', 'old_value': 6114, 'new_value': 6374}]
2025-06-25 21:01:02,635 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-25 21:01:03,135 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-25 21:01:03,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 483.0}, {'field': 'total_amount', 'old_value': 31834.2, 'new_value': 32317.2}, {'field': 'order_count', 'old_value': 153, 'new_value': 155}]
2025-06-25 21:01:03,135 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-25 21:01:03,651 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-25 21:01:03,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225027.55, 'new_value': 238007.93}, {'field': 'total_amount', 'old_value': 225027.55, 'new_value': 238007.93}, {'field': 'order_count', 'old_value': 6624, 'new_value': 6925}]
2025-06-25 21:01:03,651 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-25 21:01:04,166 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-25 21:01:04,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65164.05, 'new_value': 67717.29}, {'field': 'total_amount', 'old_value': 65164.05, 'new_value': 67717.29}, {'field': 'order_count', 'old_value': 2539, 'new_value': 2649}]
2025-06-25 21:01:04,166 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-25 21:01:04,635 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-25 21:01:04,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13288.0, 'new_value': 14047.0}, {'field': 'total_amount', 'old_value': 15487.0, 'new_value': 16246.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-06-25 21:01:04,635 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-25 21:01:05,057 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-25 21:01:05,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290276.0, 'new_value': 306101.0}, {'field': 'total_amount', 'old_value': 290276.0, 'new_value': 306101.0}, {'field': 'order_count', 'old_value': 6571, 'new_value': 6887}]
2025-06-25 21:01:05,057 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-25 21:01:05,589 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-25 21:01:05,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5502.0, 'new_value': 5742.0}, {'field': 'offline_amount', 'old_value': 19116.0, 'new_value': 19973.0}, {'field': 'total_amount', 'old_value': 24618.0, 'new_value': 25715.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 111}]
2025-06-25 21:01:05,589 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-25 21:01:06,011 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-25 21:01:06,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8731462.73, 'new_value': 9020028.73}, {'field': 'total_amount', 'old_value': 8731462.73, 'new_value': 9020028.73}, {'field': 'order_count', 'old_value': 32676, 'new_value': 33766}]
2025-06-25 21:01:06,011 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-25 21:01:06,511 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-25 21:01:06,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167327.4, 'new_value': 175157.43}, {'field': 'offline_amount', 'old_value': 143242.88, 'new_value': 149428.77}, {'field': 'total_amount', 'old_value': 310570.28, 'new_value': 324586.2}, {'field': 'order_count', 'old_value': 13420, 'new_value': 14024}]
2025-06-25 21:01:06,511 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-25 21:01:06,933 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-25 21:01:06,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321768.0, 'new_value': 339044.0}, {'field': 'total_amount', 'old_value': 357411.0, 'new_value': 374687.0}, {'field': 'order_count', 'old_value': 318, 'new_value': 334}]
2025-06-25 21:01:06,949 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-25 21:01:07,402 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-25 21:01:07,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40007.1, 'new_value': 43172.1}, {'field': 'total_amount', 'old_value': 40007.1, 'new_value': 43172.1}, {'field': 'order_count', 'old_value': 3411, 'new_value': 3596}]
2025-06-25 21:01:07,402 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-25 21:01:07,902 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-25 21:01:07,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1103000.0, 'new_value': 1151000.0}, {'field': 'total_amount', 'old_value': 1103000.0, 'new_value': 1151000.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 58}]
2025-06-25 21:01:07,918 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-25 21:01:08,371 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-25 21:01:08,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315700.0, 'new_value': 328200.0}, {'field': 'total_amount', 'old_value': 315700.0, 'new_value': 328200.0}, {'field': 'order_count', 'old_value': 9021, 'new_value': 9381}]
2025-06-25 21:01:08,371 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ2
2025-06-25 21:01:08,871 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ2
2025-06-25 21:01:08,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16080.0, 'new_value': 17880.0}, {'field': 'total_amount', 'old_value': 16080.0, 'new_value': 17880.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-25 21:01:08,871 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-25 21:01:09,324 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-25 21:01:09,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124859.0, 'new_value': 126474.0}, {'field': 'total_amount', 'old_value': 124859.0, 'new_value': 126474.0}, {'field': 'order_count', 'old_value': 3453, 'new_value': 3495}]
2025-06-25 21:01:09,324 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM53
2025-06-25 21:01:09,762 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM53
2025-06-25 21:01:09,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6180.0, 'new_value': 7980.0}, {'field': 'total_amount', 'old_value': 6180.0, 'new_value': 7980.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-25 21:01:09,762 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-25 21:01:10,215 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-25 21:01:10,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242775.84, 'new_value': 261037.04}, {'field': 'total_amount', 'old_value': 242775.84, 'new_value': 261037.04}, {'field': 'order_count', 'old_value': 91, 'new_value': 101}]
2025-06-25 21:01:10,215 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-25 21:01:10,700 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-25 21:01:10,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147000.0, 'new_value': 154000.0}, {'field': 'total_amount', 'old_value': 147000.0, 'new_value': 154000.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-25 21:01:10,700 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-25 21:01:11,184 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-25 21:01:11,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123062.0, 'new_value': 128388.0}, {'field': 'total_amount', 'old_value': 123062.0, 'new_value': 128388.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-06-25 21:01:11,184 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-25 21:01:11,685 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-25 21:01:11,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112896.98, 'new_value': 116476.98}, {'field': 'total_amount', 'old_value': 112896.98, 'new_value': 116476.98}, {'field': 'order_count', 'old_value': 4256, 'new_value': 4329}]
2025-06-25 21:01:11,700 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-25 21:01:12,122 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-25 21:01:12,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90080.0, 'new_value': 95102.0}, {'field': 'total_amount', 'old_value': 90080.0, 'new_value': 95102.0}, {'field': 'order_count', 'old_value': 660, 'new_value': 682}]
2025-06-25 21:01:12,122 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-25 21:01:12,576 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-25 21:01:12,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32084.0, 'new_value': 33069.0}, {'field': 'total_amount', 'old_value': 32084.0, 'new_value': 33069.0}, {'field': 'order_count', 'old_value': 2790, 'new_value': 2825}]
2025-06-25 21:01:12,576 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-25 21:01:13,029 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-25 21:01:13,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11073.0, 'new_value': 11598.0}, {'field': 'total_amount', 'old_value': 11073.0, 'new_value': 11598.0}, {'field': 'order_count', 'old_value': 1074, 'new_value': 1091}]
2025-06-25 21:01:13,029 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-25 21:01:13,591 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-25 21:01:13,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250119.43, 'new_value': 259423.35}, {'field': 'total_amount', 'old_value': 250119.43, 'new_value': 259423.35}, {'field': 'order_count', 'old_value': 1821, 'new_value': 1923}]
2025-06-25 21:01:13,591 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-25 21:01:13,982 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-25 21:01:13,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4726021.0, 'new_value': 4922074.0}, {'field': 'total_amount', 'old_value': 4726021.0, 'new_value': 4922074.0}, {'field': 'order_count', 'old_value': 86386, 'new_value': 90354}]
2025-06-25 21:01:13,982 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-25 21:01:14,389 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-25 21:01:14,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80066.0, 'new_value': 82104.0}, {'field': 'total_amount', 'old_value': 80066.0, 'new_value': 82104.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-06-25 21:01:14,389 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-25 21:01:14,889 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-25 21:01:14,889 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191051.22, 'new_value': 192378.22}, {'field': 'total_amount', 'old_value': 191051.22, 'new_value': 192378.22}, {'field': 'order_count', 'old_value': 373, 'new_value': 374}]
2025-06-25 21:01:14,889 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-25 21:01:15,358 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-25 21:01:15,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8368.5, 'new_value': 8713.5}, {'field': 'total_amount', 'old_value': 8368.5, 'new_value': 8713.5}, {'field': 'order_count', 'old_value': 58, 'new_value': 61}]
2025-06-25 21:01:15,358 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-25 21:01:15,811 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-25 21:01:15,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4945610.0, 'new_value': 5043610.0}, {'field': 'total_amount', 'old_value': 4945610.0, 'new_value': 5043610.0}, {'field': 'order_count', 'old_value': 194, 'new_value': 283}]
2025-06-25 21:01:15,811 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-25 21:01:16,311 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-25 21:01:16,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 404910.0, 'new_value': 412334.0}, {'field': 'total_amount', 'old_value': 404910.0, 'new_value': 412334.0}, {'field': 'order_count', 'old_value': 472, 'new_value': 481}]
2025-06-25 21:01:16,311 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-25 21:01:16,718 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-25 21:01:16,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35760.36, 'new_value': 37624.07}, {'field': 'total_amount', 'old_value': 35760.36, 'new_value': 37624.07}, {'field': 'order_count', 'old_value': 1424, 'new_value': 1493}]
2025-06-25 21:01:16,718 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-25 21:01:17,155 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-25 21:01:17,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166649.72, 'new_value': 176057.15}, {'field': 'total_amount', 'old_value': 166649.72, 'new_value': 176057.15}, {'field': 'order_count', 'old_value': 12580, 'new_value': 13297}]
2025-06-25 21:01:17,155 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-25 21:01:17,593 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-25 21:01:17,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185266.24, 'new_value': 198085.24}, {'field': 'total_amount', 'old_value': 185266.24, 'new_value': 198085.24}, {'field': 'order_count', 'old_value': 13079, 'new_value': 13335}]
2025-06-25 21:01:17,593 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-25 21:01:18,124 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-25 21:01:18,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 439000.0, 'new_value': 457000.0}, {'field': 'total_amount', 'old_value': 439000.0, 'new_value': 457000.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-25 21:01:18,124 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-25 21:01:18,515 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-25 21:01:18,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9018.75, 'new_value': 11288.0}, {'field': 'total_amount', 'old_value': 31069.45, 'new_value': 33338.7}, {'field': 'order_count', 'old_value': 3200, 'new_value': 3449}]
2025-06-25 21:01:18,515 - INFO - 日期 2025-06 处理完成 - 更新: 91 条，插入: 0 条，错误: 0 条
2025-06-25 21:01:18,515 - INFO - 数据同步完成！更新: 91 条，插入: 0 条，错误: 0 条
2025-06-25 21:01:18,531 - INFO - =================同步完成====================
