2025-07-06 01:30:33,955 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 01:30:33,955 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 01:30:33,955 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 01:30:34,096 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 1 条记录
2025-07-06 01:30:34,096 - INFO - 获取到 1 个日期需要处理: ['2025-07-05']
2025-07-06 01:30:34,096 - INFO - 开始处理日期: 2025-07-05
2025-07-06 01:30:34,096 - INFO - Request Parameters - Page 1:
2025-07-06 01:30:34,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 01:30:34,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 01:30:40,783 - INFO - Response - Page 1:
2025-07-06 01:30:40,783 - INFO - 第 1 页获取到 50 条记录
2025-07-06 01:30:41,299 - INFO - Request Parameters - Page 2:
2025-07-06 01:30:41,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 01:30:41,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 01:30:49,408 - ERROR - 处理日期 2025-07-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 816E51F0-DBF8-788D-AE93-AB622B801E1C Response: {'code': 'ServiceUnavailable', 'requestid': '816E51F0-DBF8-788D-AE93-AB622B801E1C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 816E51F0-DBF8-788D-AE93-AB622B801E1C)
2025-07-06 01:30:49,408 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-06 01:31:49,424 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 01:31:49,424 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 01:31:49,424 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 01:31:49,549 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 84 条记录
2025-07-06 01:31:49,549 - INFO - 获取到 1 个日期需要处理: ['2025-07-05']
2025-07-06 01:31:49,564 - INFO - 开始处理日期: 2025-07-05
2025-07-06 01:31:49,564 - INFO - Request Parameters - Page 1:
2025-07-06 01:31:49,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 01:31:49,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 01:31:50,283 - INFO - Response - Page 1:
2025-07-06 01:31:50,283 - INFO - 第 1 页获取到 50 条记录
2025-07-06 01:31:50,783 - INFO - Request Parameters - Page 2:
2025-07-06 01:31:50,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 01:31:50,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 01:31:51,392 - INFO - Response - Page 2:
2025-07-06 01:31:51,392 - INFO - 第 2 页获取到 28 条记录
2025-07-06 01:31:51,892 - INFO - 查询完成，共获取到 78 条记录
2025-07-06 01:31:51,892 - INFO - 获取到 78 条表单数据
2025-07-06 01:31:51,892 - INFO - 当前日期 2025-07-05 有 79 条MySQL数据需要处理
2025-07-06 01:31:51,892 - INFO - 开始批量插入 1 条新记录
2025-07-06 01:31:52,049 - INFO - 批量插入响应状态码: 200
2025-07-06 01:31:52,049 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 05 Jul 2025 17:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4B13DAB7-53A3-7231-B193-D23D89811C59', 'x-acs-trace-id': '76332e3a363e46749c6b44149f3fc3cd', 'etag': '6FtobDbUrhx5DqTsM/Hy+Gw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 01:31:52,049 - INFO - 批量插入响应体: {'result': ['FINST-HXD667B1T0UWEDC8D6ZJHAS12N352IA5TIQCMLL']}
2025-07-06 01:31:52,049 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-06 01:31:52,049 - INFO - 成功插入的数据ID: ['FINST-HXD667B1T0UWEDC8D6ZJHAS12N352IA5TIQCMLL']
2025-07-06 01:31:57,064 - INFO - 批量插入完成，共 1 条记录
2025-07-06 01:31:57,064 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-06 01:31:57,064 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-07-06 01:31:57,064 - INFO - 同步完成
2025-07-06 04:30:34,228 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 04:30:34,228 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 04:30:34,228 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 04:30:34,353 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 9 条记录
2025-07-06 04:30:34,353 - INFO - 获取到 1 个日期需要处理: ['2025-07-05']
2025-07-06 04:30:34,353 - INFO - 开始处理日期: 2025-07-05
2025-07-06 04:30:34,368 - INFO - Request Parameters - Page 1:
2025-07-06 04:30:34,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 04:30:34,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 04:30:40,978 - INFO - Response - Page 1:
2025-07-06 04:30:40,978 - INFO - 第 1 页获取到 50 条记录
2025-07-06 04:30:41,493 - INFO - Request Parameters - Page 2:
2025-07-06 04:30:41,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 04:30:41,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 04:30:49,603 - ERROR - 处理日期 2025-07-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5FE71A21-84E4-7BF2-BC31-8FD8E007F069 Response: {'code': 'ServiceUnavailable', 'requestid': '5FE71A21-84E4-7BF2-BC31-8FD8E007F069', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5FE71A21-84E4-7BF2-BC31-8FD8E007F069)
2025-07-06 04:30:49,603 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-06 04:31:49,618 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 04:31:49,618 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 04:31:49,618 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 04:31:49,743 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 122 条记录
2025-07-06 04:31:49,743 - INFO - 获取到 1 个日期需要处理: ['2025-07-05']
2025-07-06 04:31:49,743 - INFO - 开始处理日期: 2025-07-05
2025-07-06 04:31:49,743 - INFO - Request Parameters - Page 1:
2025-07-06 04:31:49,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 04:31:49,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 04:31:50,446 - INFO - Response - Page 1:
2025-07-06 04:31:50,446 - INFO - 第 1 页获取到 50 条记录
2025-07-06 04:31:50,962 - INFO - Request Parameters - Page 2:
2025-07-06 04:31:50,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 04:31:50,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 04:31:51,571 - INFO - Response - Page 2:
2025-07-06 04:31:51,571 - INFO - 第 2 页获取到 29 条记录
2025-07-06 04:31:52,087 - INFO - 查询完成，共获取到 79 条记录
2025-07-06 04:31:52,087 - INFO - 获取到 79 条表单数据
2025-07-06 04:31:52,087 - INFO - 当前日期 2025-07-05 有 116 条MySQL数据需要处理
2025-07-06 04:31:52,087 - INFO - 开始批量插入 37 条新记录
2025-07-06 04:31:52,337 - INFO - 批量插入响应状态码: 200
2025-07-06 04:31:52,337 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 05 Jul 2025 20:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1788', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9DEF2C4E-78C6-7D78-8831-37D562F93344', 'x-acs-trace-id': '8b100d26ab2a778d63b9da21a82e7196', 'etag': '1xeHLJTN0lHQmomT0fr9g9g8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 04:31:52,337 - INFO - 批量插入响应体: {'result': ['FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM1H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM2H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM3H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM4H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM5H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM6H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM7H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM8H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM9H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMAH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMBH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMCH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMDH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMEH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMFH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMGH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMHH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMIH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMJH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMKH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMLH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMMH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMNH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMOH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMPH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMQH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMRH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMSH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMTH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMUH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMVH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMWH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMXH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMYH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMZH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCM0I', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCM1I']}
2025-07-06 04:31:52,337 - INFO - 批量插入表单数据成功，批次 1，共 37 条记录
2025-07-06 04:31:52,337 - INFO - 成功插入的数据ID: ['FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM1H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM2H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM3H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM4H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM5H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM6H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM7H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM8H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCM9H', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMAH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMBH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMCH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMDH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMEH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMFH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMGH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMHH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMIH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMJH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMKH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMLH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMMH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23HWM8PQCMNH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMOH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMPH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMQH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMRH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMSH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMTH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMUH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMVH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMWH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMXH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMYH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCMZH', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCM0I', 'FINST-N79668C1N7VWI6RG9731T5H8JUG23IWM8PQCM1I']
2025-07-06 04:31:57,352 - INFO - 批量插入完成，共 37 条记录
2025-07-06 04:31:57,352 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 37 条，错误: 0 条
2025-07-06 04:31:57,352 - INFO - 数据同步完成！更新: 0 条，插入: 37 条，错误: 0 条
2025-07-06 04:31:57,352 - INFO - 同步完成
2025-07-06 07:30:34,265 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 07:30:34,265 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 07:30:34,265 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 07:30:34,421 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 9 条记录
2025-07-06 07:30:34,421 - INFO - 获取到 1 个日期需要处理: ['2025-07-05']
2025-07-06 07:30:34,421 - INFO - 开始处理日期: 2025-07-05
2025-07-06 07:30:34,421 - INFO - Request Parameters - Page 1:
2025-07-06 07:30:34,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 07:30:34,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 07:30:42,531 - ERROR - 处理日期 2025-07-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99DD3740-F3A5-7658-AB9B-77C92C923905 Response: {'code': 'ServiceUnavailable', 'requestid': '99DD3740-F3A5-7658-AB9B-77C92C923905', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99DD3740-F3A5-7658-AB9B-77C92C923905)
2025-07-06 07:30:42,531 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-06 07:31:42,546 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 07:31:42,546 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 07:31:42,546 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 07:31:42,687 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 122 条记录
2025-07-06 07:31:42,687 - INFO - 获取到 1 个日期需要处理: ['2025-07-05']
2025-07-06 07:31:42,687 - INFO - 开始处理日期: 2025-07-05
2025-07-06 07:31:42,687 - INFO - Request Parameters - Page 1:
2025-07-06 07:31:42,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 07:31:42,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 07:31:48,999 - INFO - Response - Page 1:
2025-07-06 07:31:48,999 - INFO - 第 1 页获取到 50 条记录
2025-07-06 07:31:49,515 - INFO - Request Parameters - Page 2:
2025-07-06 07:31:49,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 07:31:49,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 07:31:50,124 - INFO - Response - Page 2:
2025-07-06 07:31:50,124 - INFO - 第 2 页获取到 50 条记录
2025-07-06 07:31:50,640 - INFO - Request Parameters - Page 3:
2025-07-06 07:31:50,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 07:31:50,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 07:31:51,186 - INFO - Response - Page 3:
2025-07-06 07:31:51,186 - INFO - 第 3 页获取到 16 条记录
2025-07-06 07:31:51,702 - INFO - 查询完成，共获取到 116 条记录
2025-07-06 07:31:51,702 - INFO - 获取到 116 条表单数据
2025-07-06 07:31:51,702 - INFO - 当前日期 2025-07-05 有 116 条MySQL数据需要处理
2025-07-06 07:31:51,702 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 07:31:51,702 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 07:31:51,702 - INFO - 同步完成
2025-07-06 10:30:34,068 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 10:30:34,068 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 10:30:34,068 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 10:30:34,224 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 113 条记录
2025-07-06 10:30:34,224 - INFO - 获取到 4 个日期需要处理: ['2025-06-17', '2025-07-04', '2025-07-05', '2025-07-06']
2025-07-06 10:30:34,224 - INFO - 开始处理日期: 2025-06-17
2025-07-06 10:30:34,224 - INFO - Request Parameters - Page 1:
2025-07-06 10:30:34,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:34,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:42,349 - ERROR - 处理日期 2025-06-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3F6A57F5-BE2B-72CB-AA9F-F4293BCC5C04 Response: {'code': 'ServiceUnavailable', 'requestid': '3F6A57F5-BE2B-72CB-AA9F-F4293BCC5C04', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3F6A57F5-BE2B-72CB-AA9F-F4293BCC5C04)
2025-07-06 10:30:42,349 - INFO - 开始处理日期: 2025-07-04
2025-07-06 10:30:42,349 - INFO - Request Parameters - Page 1:
2025-07-06 10:30:42,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:42,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:45,380 - INFO - Response - Page 1:
2025-07-06 10:30:45,380 - INFO - 第 1 页获取到 50 条记录
2025-07-06 10:30:45,896 - INFO - Request Parameters - Page 2:
2025-07-06 10:30:45,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:45,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:52,349 - INFO - Response - Page 2:
2025-07-06 10:30:52,349 - INFO - 第 2 页获取到 50 条记录
2025-07-06 10:30:52,865 - INFO - Request Parameters - Page 3:
2025-07-06 10:30:52,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:52,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:53,537 - INFO - Response - Page 3:
2025-07-06 10:30:53,537 - INFO - 第 3 页获取到 50 条记录
2025-07-06 10:30:54,052 - INFO - Request Parameters - Page 4:
2025-07-06 10:30:54,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:54,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:54,740 - INFO - Response - Page 4:
2025-07-06 10:30:54,740 - INFO - 第 4 页获取到 50 条记录
2025-07-06 10:30:55,255 - INFO - Request Parameters - Page 5:
2025-07-06 10:30:55,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:55,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:55,943 - INFO - Response - Page 5:
2025-07-06 10:30:55,943 - INFO - 第 5 页获取到 50 条记录
2025-07-06 10:30:56,458 - INFO - Request Parameters - Page 6:
2025-07-06 10:30:56,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:56,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:57,146 - INFO - Response - Page 6:
2025-07-06 10:30:57,146 - INFO - 第 6 页获取到 50 条记录
2025-07-06 10:30:57,646 - INFO - Request Parameters - Page 7:
2025-07-06 10:30:57,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:57,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:58,302 - INFO - Response - Page 7:
2025-07-06 10:30:58,302 - INFO - 第 7 页获取到 50 条记录
2025-07-06 10:30:58,818 - INFO - Request Parameters - Page 8:
2025-07-06 10:30:58,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:30:58,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:30:59,490 - INFO - Response - Page 8:
2025-07-06 10:30:59,490 - INFO - 第 8 页获取到 50 条记录
2025-07-06 10:31:00,005 - INFO - Request Parameters - Page 9:
2025-07-06 10:31:00,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:31:00,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:31:00,615 - INFO - Response - Page 9:
2025-07-06 10:31:00,615 - INFO - 第 9 页获取到 39 条记录
2025-07-06 10:31:01,130 - INFO - 查询完成，共获取到 439 条记录
2025-07-06 10:31:01,130 - INFO - 获取到 439 条表单数据
2025-07-06 10:31:01,130 - INFO - 当前日期 2025-07-04 有 1 条MySQL数据需要处理
2025-07-06 10:31:01,130 - INFO - 开始批量插入 1 条新记录
2025-07-06 10:31:01,302 - INFO - 批量插入响应状态码: 200
2025-07-06 10:31:01,302 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A074852A-D96F-7247-863E-44B50B90EF62', 'x-acs-trace-id': '35fb54a80e588efe8511bf8a999f774b', 'etag': '6FmpXjKS+1k2MH0oxRtiOOA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:31:01,302 - INFO - 批量插入响应体: {'result': ['FINST-ACB66071BXUWZ7E2DAZDQC013DPB3RAI22RCM2G']}
2025-07-06 10:31:01,302 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-06 10:31:01,302 - INFO - 成功插入的数据ID: ['FINST-ACB66071BXUWZ7E2DAZDQC013DPB3RAI22RCM2G']
2025-07-06 10:31:06,318 - INFO - 批量插入完成，共 1 条记录
2025-07-06 10:31:06,318 - INFO - 日期 2025-07-04 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-06 10:31:06,318 - INFO - 开始处理日期: 2025-07-05
2025-07-06 10:31:06,318 - INFO - Request Parameters - Page 1:
2025-07-06 10:31:06,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:31:06,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:31:06,974 - INFO - Response - Page 1:
2025-07-06 10:31:06,974 - INFO - 第 1 页获取到 50 条记录
2025-07-06 10:31:07,490 - INFO - Request Parameters - Page 2:
2025-07-06 10:31:07,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:31:07,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:31:08,193 - INFO - Response - Page 2:
2025-07-06 10:31:08,193 - INFO - 第 2 页获取到 50 条记录
2025-07-06 10:31:08,708 - INFO - Request Parameters - Page 3:
2025-07-06 10:31:08,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:31:08,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:31:09,255 - INFO - Response - Page 3:
2025-07-06 10:31:09,255 - INFO - 第 3 页获取到 16 条记录
2025-07-06 10:31:09,755 - INFO - 查询完成，共获取到 116 条记录
2025-07-06 10:31:09,755 - INFO - 获取到 116 条表单数据
2025-07-06 10:31:09,755 - INFO - 当前日期 2025-07-05 有 109 条MySQL数据需要处理
2025-07-06 10:31:09,755 - INFO - 开始批量插入 100 条新记录
2025-07-06 10:31:10,005 - INFO - 批量插入响应状态码: 200
2025-07-06 10:31:10,005 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '48523500-FDCD-7F1C-AB1C-D2FCC40FE136', 'x-acs-trace-id': 'd1f14b4e4449fc18abd5103b5d1aac4a', 'etag': '2aSnUi3g9d6Wn5wML5TYCdQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:31:10,005 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMOE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMPE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMQE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMRE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMSE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMTE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMUE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMVE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMWE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMXE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMYE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMZE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM0F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM1F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM2F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM3F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM4F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM5F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM6F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM7F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM8F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM9F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMAF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMBF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMCF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMDF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMEF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMFF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMGF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMHF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMIF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMJF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMKF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMLF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMMF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMNF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMOF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMPF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMQF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMRF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMSF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMTF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMUF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMVF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMWF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMXF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMYF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMZF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM0G', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM1G']}
2025-07-06 10:31:10,005 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-06 10:31:10,005 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMOE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMPE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMQE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMRE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMSE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMTE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMUE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMVE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMWE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMXE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMYE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMZE', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM0F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM1F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM2F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM3F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM4F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM5F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM6F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM7F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM8F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM9F', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMAF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMBF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMCF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMDF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMEF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMFF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMGF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMHF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMIF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMJF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMKF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMLF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMMF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMNF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMOF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMPF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMQF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMRF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMSF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMTF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMUF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMVF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMWF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMXF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMYF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCMZF', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM0G', 'FINST-W4G66DA1LVVWV0G0EFWCL67WCCKB2O0P22RCM1G']
2025-07-06 10:31:15,271 - INFO - 批量插入响应状态码: 200
2025-07-06 10:31:15,271 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A7E3E5B-6AD5-7CC1-AEB8-8DAC898011A7', 'x-acs-trace-id': '8537d43c05a3ea7fca52f34f3a927372', 'etag': '2fEi1CtLsztOAmaZiMQ1vgQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:31:15,271 - INFO - 批量插入响应体: {'result': ['FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM1N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM2N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM3N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM4N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM5N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM6N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM7N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM8N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM9N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMAN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMBN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMCN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMDN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMEN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMFN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMGN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMHN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMIN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMJN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMKN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMLN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMMN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMNN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMON', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMPN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMQN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMRN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMSN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMTN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMUN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMVN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMWN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMXN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMYN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMZN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM0O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM1O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM2O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM3O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM4O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM5O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM6O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM7O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCM8O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCM9O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMAO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMBO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMCO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMDO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMEO']}
2025-07-06 10:31:15,271 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-06 10:31:15,271 - INFO - 成功插入的数据ID: ['FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM1N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM2N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM3N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM4N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM5N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM6N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM7N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM8N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM9N', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMAN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMBN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMCN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMDN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMEN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMFN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMGN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMHN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMIN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMJN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMKN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMLN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMMN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMNN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMON', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMPN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMQN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMRN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMSN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMTN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMUN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMVN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMWN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMXN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMYN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCMZN', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM0O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM1O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM2O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM3O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM4O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM5O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM6O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3P2T22RCM7O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCM8O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCM9O', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMAO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMBO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMCO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMDO', 'FINST-IQE66ZC11VUWM1YZEIUCN5FQFLAG3Q2T22RCMEO']
2025-07-06 10:31:20,286 - INFO - 批量插入完成，共 100 条记录
2025-07-06 10:31:20,286 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 100 条，错误: 0 条
2025-07-06 10:31:20,286 - INFO - 开始处理日期: 2025-07-06
2025-07-06 10:31:20,286 - INFO - Request Parameters - Page 1:
2025-07-06 10:31:20,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:31:20,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:31:20,755 - INFO - Response - Page 1:
2025-07-06 10:31:20,755 - INFO - 查询完成，共获取到 0 条记录
2025-07-06 10:31:20,755 - INFO - 获取到 0 条表单数据
2025-07-06 10:31:20,755 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 10:31:20,755 - INFO - 开始批量插入 1 条新记录
2025-07-06 10:31:20,911 - INFO - 批量插入响应状态码: 200
2025-07-06 10:31:20,911 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:31:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-FAB0-7F29-A2B4-5A0F5EA8581B', 'x-acs-trace-id': 'cfd82490ddd61aa8a9a8a6ab75a84080', 'etag': '6bXK1MN0CDF0llnLYz4NesQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:31:20,911 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B120UWP8B0FXA0T5X5P2X22RFX22RCMXZ']}
2025-07-06 10:31:20,911 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-06 10:31:20,911 - INFO - 成功插入的数据ID: ['FINST-LR5668B120UWP8B0FXA0T5X5P2X22RFX22RCMXZ']
2025-07-06 10:31:25,927 - INFO - 批量插入完成，共 1 条记录
2025-07-06 10:31:25,927 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-06 10:31:25,927 - INFO - 数据同步完成！更新: 0 条，插入: 102 条，错误: 1 条
2025-07-06 10:32:25,942 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 10:32:25,942 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 10:32:25,942 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 10:32:26,083 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 391 条记录
2025-07-06 10:32:26,083 - INFO - 获取到 2 个日期需要处理: ['2025-07-05', '2025-07-06']
2025-07-06 10:32:26,098 - INFO - 开始处理日期: 2025-07-05
2025-07-06 10:32:26,098 - INFO - Request Parameters - Page 1:
2025-07-06 10:32:26,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:32:26,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:32:26,817 - INFO - Response - Page 1:
2025-07-06 10:32:26,817 - INFO - 第 1 页获取到 50 条记录
2025-07-06 10:32:27,317 - INFO - Request Parameters - Page 2:
2025-07-06 10:32:27,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:32:27,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:32:28,067 - INFO - Response - Page 2:
2025-07-06 10:32:28,067 - INFO - 第 2 页获取到 50 条记录
2025-07-06 10:32:28,583 - INFO - Request Parameters - Page 3:
2025-07-06 10:32:28,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:32:28,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:32:29,239 - INFO - Response - Page 3:
2025-07-06 10:32:29,239 - INFO - 第 3 页获取到 50 条记录
2025-07-06 10:32:29,755 - INFO - Request Parameters - Page 4:
2025-07-06 10:32:29,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:32:29,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:32:30,505 - INFO - Response - Page 4:
2025-07-06 10:32:30,505 - INFO - 第 4 页获取到 50 条记录
2025-07-06 10:32:31,020 - INFO - Request Parameters - Page 5:
2025-07-06 10:32:31,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:32:31,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:32:31,614 - INFO - Response - Page 5:
2025-07-06 10:32:31,614 - INFO - 第 5 页获取到 16 条记录
2025-07-06 10:32:32,114 - INFO - 查询完成，共获取到 216 条记录
2025-07-06 10:32:32,114 - INFO - 获取到 216 条表单数据
2025-07-06 10:32:32,114 - INFO - 当前日期 2025-07-05 有 380 条MySQL数据需要处理
2025-07-06 10:32:32,114 - INFO - 开始批量插入 164 条新记录
2025-07-06 10:32:32,380 - INFO - 批量插入响应状态码: 200
2025-07-06 10:32:32,380 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:32:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8A9171D2-19C5-7065-9CA8-FE7B50881D01', 'x-acs-trace-id': 'e49bd72bf09eafa60144f5b097194415', 'etag': '2gDXHj0CXcMXNjNAC0s5OWw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:32:32,380 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMFJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMGJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMHJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMIJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMJJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMKJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMLJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMMJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMNJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMOJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMPJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMQJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMRJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMSJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMTJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMUJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMVJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMWJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMXJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMYJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMZJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM0K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM1K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM2K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM3K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM4K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM5K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM6K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM7K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM8K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM9K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMAK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMBK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMCK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMDK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMEK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMFK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMGK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMHK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMIK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMJK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMKK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMLK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMMK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMNK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMOK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMPK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMQK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMRK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMSK']}
2025-07-06 10:32:32,380 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-06 10:32:32,380 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMFJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMGJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMHJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMIJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMJJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMKJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMLJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMMJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMNJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMOJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMPJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMQJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMRJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMSJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMTJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMUJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMVJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMWJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMXJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMYJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMZJ', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM0K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM1K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM2K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM3K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM4K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM5K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM6K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM7K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM8K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCM9K', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMAK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMBK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMCK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMDK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMEK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMFK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMGK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMHK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMIK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMJK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMKK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMLK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMMK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMNK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMOK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMPK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMQK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMRK', 'FINST-KLF66WC1QXUWJB5DED7W0CX3AQST3DKG42RCMSK']
2025-07-06 10:32:37,630 - INFO - 批量插入响应状态码: 200
2025-07-06 10:32:37,630 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:32:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F99820A4-F7BF-7F9F-BD68-6482F283BD1A', 'x-acs-trace-id': '54f97ee4e6d01f3cdd21fc19e9e96f6a', 'etag': '2nxFgDkLKg0rDTXdQqb0JBg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:32:37,630 - INFO - 批量插入响应体: {'result': ['FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMZO', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM0P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM1P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM2P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM3P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM4P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM5P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM6P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM7P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM8P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM9P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMAP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMBP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMCP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMDP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMEP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMFP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMGP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMHP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMIP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMJP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMKP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMLP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMMP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMNP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMOP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMPP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMQP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMRP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMSP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMTP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMUP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMVP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMWP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMXP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMYP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMZP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM0Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM1Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM2Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM3Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM4Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM5Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM6Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM7Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM8Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM9Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMAQ', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMBQ', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMCQ']}
2025-07-06 10:32:37,630 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-06 10:32:37,630 - INFO - 成功插入的数据ID: ['FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMZO', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM0P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM1P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM2P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM3P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM4P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM5P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM6P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM7P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM8P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM9P', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMAP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMBP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMCP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMDP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMEP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMFP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMGP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMHP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMIP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMJP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMKP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMLP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMMP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMNP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMOP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMPP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMQP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMRP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMSP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMTP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMUP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMVP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMWP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMXP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMYP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMZP', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM0Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM1Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM2Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM3Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM4Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM5Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM6Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM7Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM8Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCM9Q', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMAQ', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMBQ', 'FINST-7PF66MD1HTVWP8AMFGDC69GNV9DB2HMK42RCMCQ']
2025-07-06 10:32:42,895 - INFO - 批量插入响应状态码: 200
2025-07-06 10:32:42,895 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:32:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '27968913-2E23-7515-9B79-CCB509945A6E', 'x-acs-trace-id': '64955e2d18179e96971bea44fdd055f1', 'etag': '2LfK0rX2fwQ3S10Wy0Eh3tw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:32:42,895 - INFO - 批量插入响应体: {'result': ['FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMUM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMVM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMWM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMXM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMYM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMZM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM0N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM1N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM2N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM3N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM4N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM5N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM6N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM7N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM8N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM9N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMAN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMBN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMCN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMDN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMEN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMFN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMGN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMHN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMIN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMJN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMKN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMLN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMMN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMNN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMON1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMPN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMQN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMRN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMSN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMTN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMUN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMVN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMWN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMXN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMYN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMZN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM0O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM1O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM2O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM3O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM4O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM5O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM6O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM7O1']}
2025-07-06 10:32:42,895 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-06 10:32:42,895 - INFO - 成功插入的数据ID: ['FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMUM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMVM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMWM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMXM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMYM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMZM1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM0N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM1N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM2N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM3N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM4N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM5N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM6N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM7N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM8N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM9N1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMAN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMBN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMCN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMDN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMEN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMFN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMGN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMHN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMIN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMJN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMKN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMLN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMMN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMNN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMON1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMPN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMQN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMRN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMSN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMTN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMUN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMVN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMWN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMXN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMYN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCMZN1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM0O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM1O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM2O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM3O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM4O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM5O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM6O1', 'FINST-UW966371O1UWO9SF9XJVH8FN1P0K2POO42RCM7O1']
2025-07-06 10:32:48,067 - INFO - 批量插入响应状态码: 200
2025-07-06 10:32:48,067 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 02:32:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '698', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '934DB990-10B7-715D-B86F-6A430F946E83', 'x-acs-trace-id': '9be844270c04f14cc147908f32be8d76', 'etag': '63r1r8vjNs88K5FBLvNaAVg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 10:32:48,067 - INFO - 批量插入响应体: {'result': ['FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCME01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMF01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMG01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMH01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMI01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMJ01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMK01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCML01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMM01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMN01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMO01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMP01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMQ01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMR01']}
2025-07-06 10:32:48,067 - INFO - 批量插入表单数据成功，批次 4，共 14 条记录
2025-07-06 10:32:48,067 - INFO - 成功插入的数据ID: ['FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCME01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMF01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMG01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMH01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMI01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMJ01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMK01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCML01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMM01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMN01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMO01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMP01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMQ01', 'FINST-IQG66AD157RWLQG89TOKP7RJY4YX2GOS42RCMR01']
2025-07-06 10:32:53,083 - INFO - 批量插入完成，共 164 条记录
2025-07-06 10:32:53,083 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 164 条，错误: 0 条
2025-07-06 10:32:53,083 - INFO - 开始处理日期: 2025-07-06
2025-07-06 10:32:53,083 - INFO - Request Parameters - Page 1:
2025-07-06 10:32:53,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 10:32:53,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 10:32:53,567 - INFO - Response - Page 1:
2025-07-06 10:32:53,567 - INFO - 第 1 页获取到 1 条记录
2025-07-06 10:32:54,067 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 10:32:54,067 - INFO - 获取到 1 条表单数据
2025-07-06 10:32:54,067 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 10:32:54,067 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 10:32:54,067 - INFO - 数据同步完成！更新: 0 条，插入: 164 条，错误: 0 条
2025-07-06 10:32:54,067 - INFO - 同步完成
2025-07-06 13:30:33,901 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 13:30:33,901 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 13:30:33,901 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 13:30:34,042 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 140 条记录
2025-07-06 13:30:34,042 - INFO - 获取到 4 个日期需要处理: ['2025-06-17', '2025-07-04', '2025-07-05', '2025-07-06']
2025-07-06 13:30:34,058 - INFO - 开始处理日期: 2025-06-17
2025-07-06 13:30:34,058 - INFO - Request Parameters - Page 1:
2025-07-06 13:30:34,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:34,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:42,167 - ERROR - 处理日期 2025-06-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7C494015-E031-79AE-8764-6AC5D6157180 Response: {'code': 'ServiceUnavailable', 'requestid': '7C494015-E031-79AE-8764-6AC5D6157180', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7C494015-E031-79AE-8764-6AC5D6157180)
2025-07-06 13:30:42,167 - INFO - 开始处理日期: 2025-07-04
2025-07-06 13:30:42,167 - INFO - Request Parameters - Page 1:
2025-07-06 13:30:42,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:42,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:49,761 - INFO - Response - Page 1:
2025-07-06 13:30:49,761 - INFO - 第 1 页获取到 50 条记录
2025-07-06 13:30:50,276 - INFO - Request Parameters - Page 2:
2025-07-06 13:30:50,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:50,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:50,901 - INFO - Response - Page 2:
2025-07-06 13:30:50,901 - INFO - 第 2 页获取到 50 条记录
2025-07-06 13:30:51,417 - INFO - Request Parameters - Page 3:
2025-07-06 13:30:51,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:51,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:52,073 - INFO - Response - Page 3:
2025-07-06 13:30:52,073 - INFO - 第 3 页获取到 50 条记录
2025-07-06 13:30:52,573 - INFO - Request Parameters - Page 4:
2025-07-06 13:30:52,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:52,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:53,323 - INFO - Response - Page 4:
2025-07-06 13:30:53,323 - INFO - 第 4 页获取到 50 条记录
2025-07-06 13:30:53,839 - INFO - Request Parameters - Page 5:
2025-07-06 13:30:53,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:53,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:54,542 - INFO - Response - Page 5:
2025-07-06 13:30:54,542 - INFO - 第 5 页获取到 50 条记录
2025-07-06 13:30:55,042 - INFO - Request Parameters - Page 6:
2025-07-06 13:30:55,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:55,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:55,839 - INFO - Response - Page 6:
2025-07-06 13:30:55,839 - INFO - 第 6 页获取到 50 条记录
2025-07-06 13:30:56,339 - INFO - Request Parameters - Page 7:
2025-07-06 13:30:56,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:56,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:57,042 - INFO - Response - Page 7:
2025-07-06 13:30:57,042 - INFO - 第 7 页获取到 50 条记录
2025-07-06 13:30:57,542 - INFO - Request Parameters - Page 8:
2025-07-06 13:30:57,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:57,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:58,198 - INFO - Response - Page 8:
2025-07-06 13:30:58,198 - INFO - 第 8 页获取到 50 条记录
2025-07-06 13:30:58,714 - INFO - Request Parameters - Page 9:
2025-07-06 13:30:58,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:30:58,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:30:59,495 - INFO - Response - Page 9:
2025-07-06 13:30:59,495 - INFO - 第 9 页获取到 40 条记录
2025-07-06 13:31:00,011 - INFO - 查询完成，共获取到 440 条记录
2025-07-06 13:31:00,011 - INFO - 获取到 440 条表单数据
2025-07-06 13:31:00,011 - INFO - 当前日期 2025-07-04 有 3 条MySQL数据需要处理
2025-07-06 13:31:00,011 - INFO - 开始批量插入 2 条新记录
2025-07-06 13:31:00,151 - INFO - 批量插入响应状态码: 200
2025-07-06 13:31:00,151 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 05:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CE346115-A48E-7831-B90D-E70AD80556C7', 'x-acs-trace-id': 'f732b8096b6e63b879e8bcc0cf21bef3', 'etag': '13fg6nZ40qfkjaMQLoU1taQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 13:31:00,151 - INFO - 批量插入响应体: {'result': ['FINST-I3D66ED1Q6VWXBN8CCW2JCGUV9NB3XSYH8RCMOZ', 'FINST-I3D66ED1Q6VWXBN8CCW2JCGUV9NB3XSYH8RCMPZ']}
2025-07-06 13:31:00,151 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-06 13:31:00,151 - INFO - 成功插入的数据ID: ['FINST-I3D66ED1Q6VWXBN8CCW2JCGUV9NB3XSYH8RCMOZ', 'FINST-I3D66ED1Q6VWXBN8CCW2JCGUV9NB3XSYH8RCMPZ']
2025-07-06 13:31:05,167 - INFO - 批量插入完成，共 2 条记录
2025-07-06 13:31:05,167 - INFO - 日期 2025-07-04 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-06 13:31:05,167 - INFO - 开始处理日期: 2025-07-05
2025-07-06 13:31:05,167 - INFO - Request Parameters - Page 1:
2025-07-06 13:31:05,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:05,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:05,823 - INFO - Response - Page 1:
2025-07-06 13:31:05,823 - INFO - 第 1 页获取到 50 条记录
2025-07-06 13:31:06,339 - INFO - Request Parameters - Page 2:
2025-07-06 13:31:06,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:06,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:07,104 - INFO - Response - Page 2:
2025-07-06 13:31:07,104 - INFO - 第 2 页获取到 50 条记录
2025-07-06 13:31:07,604 - INFO - Request Parameters - Page 3:
2025-07-06 13:31:07,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:07,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:08,323 - INFO - Response - Page 3:
2025-07-06 13:31:08,323 - INFO - 第 3 页获取到 50 条记录
2025-07-06 13:31:08,839 - INFO - Request Parameters - Page 4:
2025-07-06 13:31:08,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:08,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:09,542 - INFO - Response - Page 4:
2025-07-06 13:31:09,542 - INFO - 第 4 页获取到 50 条记录
2025-07-06 13:31:10,057 - INFO - Request Parameters - Page 5:
2025-07-06 13:31:10,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:10,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:10,761 - INFO - Response - Page 5:
2025-07-06 13:31:10,761 - INFO - 第 5 页获取到 50 条记录
2025-07-06 13:31:11,276 - INFO - Request Parameters - Page 6:
2025-07-06 13:31:11,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:11,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:11,979 - INFO - Response - Page 6:
2025-07-06 13:31:11,979 - INFO - 第 6 页获取到 50 条记录
2025-07-06 13:31:12,495 - INFO - Request Parameters - Page 7:
2025-07-06 13:31:12,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:12,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:13,167 - INFO - Response - Page 7:
2025-07-06 13:31:13,167 - INFO - 第 7 页获取到 50 条记录
2025-07-06 13:31:13,682 - INFO - Request Parameters - Page 8:
2025-07-06 13:31:13,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:13,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:14,292 - INFO - Response - Page 8:
2025-07-06 13:31:14,292 - INFO - 第 8 页获取到 30 条记录
2025-07-06 13:31:14,807 - INFO - 查询完成，共获取到 380 条记录
2025-07-06 13:31:14,807 - INFO - 获取到 380 条表单数据
2025-07-06 13:31:14,807 - INFO - 当前日期 2025-07-05 有 134 条MySQL数据需要处理
2025-07-06 13:31:14,807 - INFO - 开始批量插入 25 条新记录
2025-07-06 13:31:15,042 - INFO - 批量插入响应状态码: 200
2025-07-06 13:31:15,042 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 05:31:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1212', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F8DB8660-914B-788E-9A3D-C27B32478F29', 'x-acs-trace-id': '4cdecf41f34eccfe09727502f9f8a2ca', 'etag': '1tHP4XWqvvQFN/zm3QARY0g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 13:31:15,042 - INFO - 批量插入响应体: {'result': ['FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMLG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMMG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMNG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMOG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMPG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMQG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMRG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMSG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMTG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMUG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMVG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMWG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMXG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMYG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMZG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM0H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM1H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM2H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM3H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM4H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM5H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM6H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM7H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM8H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM9H']}
2025-07-06 13:31:15,042 - INFO - 批量插入表单数据成功，批次 1，共 25 条记录
2025-07-06 13:31:15,042 - INFO - 成功插入的数据ID: ['FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMLG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMMG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMNG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMOG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMPG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMQG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMRG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMSG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMTG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMUG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMVG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMWG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMXG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMYG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCMZG', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM0H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM1H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM2H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM3H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM4H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM5H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM6H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM7H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM8H', 'FINST-VFF66XA1C7WWCZGO65KI77OHKHR926AAI8RCM9H']
2025-07-06 13:31:20,057 - INFO - 批量插入完成，共 25 条记录
2025-07-06 13:31:20,057 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 25 条，错误: 0 条
2025-07-06 13:31:20,057 - INFO - 开始处理日期: 2025-07-06
2025-07-06 13:31:20,057 - INFO - Request Parameters - Page 1:
2025-07-06 13:31:20,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:31:20,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:31:20,542 - INFO - Response - Page 1:
2025-07-06 13:31:20,542 - INFO - 第 1 页获取到 1 条记录
2025-07-06 13:31:21,057 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 13:31:21,057 - INFO - 获取到 1 条表单数据
2025-07-06 13:31:21,057 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 13:31:21,057 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 13:31:21,057 - INFO - 数据同步完成！更新: 0 条，插入: 27 条，错误: 1 条
2025-07-06 13:32:21,073 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 13:32:21,073 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 13:32:21,073 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 13:32:21,213 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 475 条记录
2025-07-06 13:32:21,213 - INFO - 获取到 2 个日期需要处理: ['2025-07-05', '2025-07-06']
2025-07-06 13:32:21,229 - INFO - 开始处理日期: 2025-07-05
2025-07-06 13:32:21,229 - INFO - Request Parameters - Page 1:
2025-07-06 13:32:21,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:21,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:21,916 - INFO - Response - Page 1:
2025-07-06 13:32:21,916 - INFO - 第 1 页获取到 50 条记录
2025-07-06 13:32:22,416 - INFO - Request Parameters - Page 2:
2025-07-06 13:32:22,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:22,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:23,104 - INFO - Response - Page 2:
2025-07-06 13:32:23,104 - INFO - 第 2 页获取到 50 条记录
2025-07-06 13:32:23,604 - INFO - Request Parameters - Page 3:
2025-07-06 13:32:23,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:23,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:24,276 - INFO - Response - Page 3:
2025-07-06 13:32:24,276 - INFO - 第 3 页获取到 50 条记录
2025-07-06 13:32:24,776 - INFO - Request Parameters - Page 4:
2025-07-06 13:32:24,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:24,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:25,448 - INFO - Response - Page 4:
2025-07-06 13:32:25,448 - INFO - 第 4 页获取到 50 条记录
2025-07-06 13:32:25,948 - INFO - Request Parameters - Page 5:
2025-07-06 13:32:25,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:25,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:26,619 - INFO - Response - Page 5:
2025-07-06 13:32:26,619 - INFO - 第 5 页获取到 50 条记录
2025-07-06 13:32:27,135 - INFO - Request Parameters - Page 6:
2025-07-06 13:32:27,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:27,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:27,807 - INFO - Response - Page 6:
2025-07-06 13:32:27,807 - INFO - 第 6 页获取到 50 条记录
2025-07-06 13:32:28,307 - INFO - Request Parameters - Page 7:
2025-07-06 13:32:28,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:28,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:29,026 - INFO - Response - Page 7:
2025-07-06 13:32:29,026 - INFO - 第 7 页获取到 50 条记录
2025-07-06 13:32:29,541 - INFO - Request Parameters - Page 8:
2025-07-06 13:32:29,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:29,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:30,229 - INFO - Response - Page 8:
2025-07-06 13:32:30,229 - INFO - 第 8 页获取到 50 条记录
2025-07-06 13:32:30,744 - INFO - Request Parameters - Page 9:
2025-07-06 13:32:30,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:30,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:31,244 - INFO - Response - Page 9:
2025-07-06 13:32:31,244 - INFO - 第 9 页获取到 5 条记录
2025-07-06 13:32:31,760 - INFO - 查询完成，共获取到 405 条记录
2025-07-06 13:32:31,760 - INFO - 获取到 405 条表单数据
2025-07-06 13:32:31,760 - INFO - 当前日期 2025-07-05 有 464 条MySQL数据需要处理
2025-07-06 13:32:31,776 - INFO - 开始批量插入 59 条新记录
2025-07-06 13:32:32,010 - INFO - 批量插入响应状态码: 200
2025-07-06 13:32:32,010 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 05:32:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '98EC1EEB-6944-78F5-B18C-5833319DCD49', 'x-acs-trace-id': '8cbe1f2a9e0103fafbe558d6539734af', 'etag': '2F39XwlDZHcqygEZxphg8Vw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 13:32:32,010 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM4A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM5A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM6A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM7A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM8A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM9A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMAA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMBA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMCA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMDA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMEA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMFA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMGA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMHA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMIA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMJA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMKA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMLA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMMA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMNA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMOA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMPA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMQA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMRA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMSA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMTA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMUA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMVA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMWA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMXA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMYA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMZA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM0B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM1B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM2B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM3B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM4B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM5B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM6B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM7B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM8B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM9B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMAB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMBB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMCB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMDB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMEB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMFB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMGB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMHB']}
2025-07-06 13:32:32,010 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-06 13:32:32,010 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM4A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM5A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM6A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM7A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM8A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM9A', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMAA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMBA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMCA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMDA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMEA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMFA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMGA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMHA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMIA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMJA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMKA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMLA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMMA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMNA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMOA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMPA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMQA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMRA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMSA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMTA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMUA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMVA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMWA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMXA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMYA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMZA', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM0B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM1B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM2B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM3B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM4B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM5B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM6B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM7B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM8B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCM9B', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMAB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMBB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMCB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMDB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMEB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMFB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMGB', 'FINST-YPE66RB1R6VWA5QTAL3YT5TSUKA53JOXJ8RCMHB']
2025-07-06 13:32:37,182 - INFO - 批量插入响应状态码: 200
2025-07-06 13:32:37,182 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 05:32:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '327D2A98-17E1-72B7-AAFB-9E795C99AA88', 'x-acs-trace-id': '8b1e083cc1d8c5b34196c3d6e3eee853', 'etag': '4saD/K7Oqns6dxCBjzLcwdg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 13:32:37,182 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCME5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMF5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMG5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMH5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMI5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMJ5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMK5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCML5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMM5']}
2025-07-06 13:32:37,182 - INFO - 批量插入表单数据成功，批次 2，共 9 条记录
2025-07-06 13:32:37,182 - INFO - 成功插入的数据ID: ['FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCME5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMF5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMG5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMH5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMI5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMJ5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMK5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCML5', 'FINST-8PF66V71EYSW62R9AUY6275MNMOL3GO1K8RCMM5']
2025-07-06 13:32:42,197 - INFO - 批量插入完成，共 59 条记录
2025-07-06 13:32:42,197 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 59 条，错误: 0 条
2025-07-06 13:32:42,197 - INFO - 开始处理日期: 2025-07-06
2025-07-06 13:32:42,197 - INFO - Request Parameters - Page 1:
2025-07-06 13:32:42,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 13:32:42,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 13:32:42,838 - INFO - Response - Page 1:
2025-07-06 13:32:42,838 - INFO - 第 1 页获取到 1 条记录
2025-07-06 13:32:43,338 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 13:32:43,338 - INFO - 获取到 1 条表单数据
2025-07-06 13:32:43,338 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 13:32:43,338 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 13:32:43,338 - INFO - 数据同步完成！更新: 0 条，插入: 59 条，错误: 0 条
2025-07-06 13:32:43,338 - INFO - 同步完成
2025-07-06 16:30:33,689 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 16:30:33,689 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 16:30:33,689 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 16:30:33,845 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 157 条记录
2025-07-06 16:30:33,845 - INFO - 获取到 15 个日期需要处理: ['2025-06-01', '2025-06-02', '2025-06-03', '2025-06-04', '2025-06-06', '2025-06-07', '2025-06-14', '2025-06-15', '2025-06-17', '2025-06-23', '2025-06-24', '2025-06-27', '2025-07-04', '2025-07-05', '2025-07-06']
2025-07-06 16:30:33,845 - INFO - 开始处理日期: 2025-06-01
2025-07-06 16:30:33,845 - INFO - Request Parameters - Page 1:
2025-07-06 16:30:33,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:33,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:41,954 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7FCAE70E-408A-750F-967C-C4AA6CD45FCB Response: {'code': 'ServiceUnavailable', 'requestid': '7FCAE70E-408A-750F-967C-C4AA6CD45FCB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7FCAE70E-408A-750F-967C-C4AA6CD45FCB)
2025-07-06 16:30:41,954 - INFO - 开始处理日期: 2025-06-02
2025-07-06 16:30:41,954 - INFO - Request Parameters - Page 1:
2025-07-06 16:30:41,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:41,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:49,361 - INFO - Response - Page 1:
2025-07-06 16:30:49,361 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:30:49,861 - INFO - Request Parameters - Page 2:
2025-07-06 16:30:49,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:49,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:50,579 - INFO - Response - Page 2:
2025-07-06 16:30:50,595 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:30:51,095 - INFO - Request Parameters - Page 3:
2025-07-06 16:30:51,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:51,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:51,845 - INFO - Response - Page 3:
2025-07-06 16:30:51,845 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:30:52,361 - INFO - Request Parameters - Page 4:
2025-07-06 16:30:52,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:52,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:53,111 - INFO - Response - Page 4:
2025-07-06 16:30:53,111 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:30:53,626 - INFO - Request Parameters - Page 5:
2025-07-06 16:30:53,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:53,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:54,282 - INFO - Response - Page 5:
2025-07-06 16:30:54,282 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:30:54,782 - INFO - Request Parameters - Page 6:
2025-07-06 16:30:54,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:54,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:55,454 - INFO - Response - Page 6:
2025-07-06 16:30:55,454 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:30:55,954 - INFO - Request Parameters - Page 7:
2025-07-06 16:30:55,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:55,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:56,689 - INFO - Response - Page 7:
2025-07-06 16:30:56,689 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:30:57,204 - INFO - Request Parameters - Page 8:
2025-07-06 16:30:57,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:57,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:57,907 - INFO - Response - Page 8:
2025-07-06 16:30:57,907 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:30:58,423 - INFO - Request Parameters - Page 9:
2025-07-06 16:30:58,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:58,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:30:59,079 - INFO - Response - Page 9:
2025-07-06 16:30:59,079 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:30:59,579 - INFO - Request Parameters - Page 10:
2025-07-06 16:30:59,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:30:59,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:00,267 - INFO - Response - Page 10:
2025-07-06 16:31:00,267 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:31:00,782 - INFO - Request Parameters - Page 11:
2025-07-06 16:31:00,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:00,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:01,486 - INFO - Response - Page 11:
2025-07-06 16:31:01,501 - INFO - 第 11 页获取到 50 条记录
2025-07-06 16:31:02,001 - INFO - Request Parameters - Page 12:
2025-07-06 16:31:02,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:02,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:02,548 - INFO - Response - Page 12:
2025-07-06 16:31:02,548 - INFO - 第 12 页获取到 14 条记录
2025-07-06 16:31:03,064 - INFO - 查询完成，共获取到 564 条记录
2025-07-06 16:31:03,064 - INFO - 获取到 564 条表单数据
2025-07-06 16:31:03,064 - INFO - 当前日期 2025-06-02 有 2 条MySQL数据需要处理
2025-07-06 16:31:03,064 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB1UO5WNR6S7B4AXDLWKVDF3D368XOBMK1
2025-07-06 16:31:03,657 - INFO - 更新表单数据成功: FINST-3RE66ZB1UO5WNR6S7B4AXDLWKVDF3D368XOBMK1
2025-07-06 16:31:03,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5951.0, 'new_value': 7632.5}, {'field': 'total_amount', 'old_value': 5951.0, 'new_value': 7632.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:31:03,657 - INFO - 开始更新记录 - 表单实例ID: FINST-I6E66WA1ZQ5WAD4OBODEG6WAD54S24M17XOBME
2025-07-06 16:31:04,376 - INFO - 更新表单数据成功: FINST-I6E66WA1ZQ5WAD4OBODEG6WAD54S24M17XOBME
2025-07-06 16:31:04,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1298.0, 'new_value': 1502.0}, {'field': 'total_amount', 'old_value': 1298.0, 'new_value': 1502.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:31:04,376 - INFO - 日期 2025-06-02 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-07-06 16:31:04,376 - INFO - 开始处理日期: 2025-06-03
2025-07-06 16:31:04,376 - INFO - Request Parameters - Page 1:
2025-07-06 16:31:04,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:04,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:05,017 - INFO - Response - Page 1:
2025-07-06 16:31:05,017 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:31:05,532 - INFO - Request Parameters - Page 2:
2025-07-06 16:31:05,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:05,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:06,298 - INFO - Response - Page 2:
2025-07-06 16:31:06,298 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:31:06,814 - INFO - Request Parameters - Page 3:
2025-07-06 16:31:06,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:06,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:07,454 - INFO - Response - Page 3:
2025-07-06 16:31:07,454 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:31:07,970 - INFO - Request Parameters - Page 4:
2025-07-06 16:31:07,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:07,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:08,626 - INFO - Response - Page 4:
2025-07-06 16:31:08,626 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:31:09,126 - INFO - Request Parameters - Page 5:
2025-07-06 16:31:09,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:09,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:09,829 - INFO - Response - Page 5:
2025-07-06 16:31:09,829 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:31:10,345 - INFO - Request Parameters - Page 6:
2025-07-06 16:31:10,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:10,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:10,954 - INFO - Response - Page 6:
2025-07-06 16:31:10,954 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:31:11,454 - INFO - Request Parameters - Page 7:
2025-07-06 16:31:11,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:11,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:12,173 - INFO - Response - Page 7:
2025-07-06 16:31:12,173 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:31:12,673 - INFO - Request Parameters - Page 8:
2025-07-06 16:31:12,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:12,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:13,392 - INFO - Response - Page 8:
2025-07-06 16:31:13,392 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:31:13,907 - INFO - Request Parameters - Page 9:
2025-07-06 16:31:13,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:13,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:14,564 - INFO - Response - Page 9:
2025-07-06 16:31:14,564 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:31:15,079 - INFO - Request Parameters - Page 10:
2025-07-06 16:31:15,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:15,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:15,782 - INFO - Response - Page 10:
2025-07-06 16:31:15,782 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:31:16,282 - INFO - Request Parameters - Page 11:
2025-07-06 16:31:16,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:16,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:16,954 - INFO - Response - Page 11:
2025-07-06 16:31:16,954 - INFO - 第 11 页获取到 50 条记录
2025-07-06 16:31:17,454 - INFO - Request Parameters - Page 12:
2025-07-06 16:31:17,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:17,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:18,017 - INFO - Response - Page 12:
2025-07-06 16:31:18,017 - INFO - 第 12 页获取到 12 条记录
2025-07-06 16:31:18,532 - INFO - 查询完成，共获取到 562 条记录
2025-07-06 16:31:18,532 - INFO - 获取到 562 条表单数据
2025-07-06 16:31:18,532 - INFO - 当前日期 2025-06-03 有 1 条MySQL数据需要处理
2025-07-06 16:31:18,532 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071ZR2W7GLJAVEYDCECKVIE3O3B9XOBM5N
2025-07-06 16:31:19,142 - INFO - 更新表单数据成功: FINST-XBF66071ZR2W7GLJAVEYDCECKVIE3O3B9XOBM5N
2025-07-06 16:31:19,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6860.0, 'new_value': 7983.2}, {'field': 'total_amount', 'old_value': 6860.0, 'new_value': 7983.2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:31:19,142 - INFO - 日期 2025-06-03 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:31:19,142 - INFO - 开始处理日期: 2025-06-04
2025-07-06 16:31:19,142 - INFO - Request Parameters - Page 1:
2025-07-06 16:31:19,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:19,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:19,829 - INFO - Response - Page 1:
2025-07-06 16:31:19,829 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:31:20,329 - INFO - Request Parameters - Page 2:
2025-07-06 16:31:20,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:20,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:21,001 - INFO - Response - Page 2:
2025-07-06 16:31:21,001 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:31:21,501 - INFO - Request Parameters - Page 3:
2025-07-06 16:31:21,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:21,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:22,189 - INFO - Response - Page 3:
2025-07-06 16:31:22,189 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:31:22,704 - INFO - Request Parameters - Page 4:
2025-07-06 16:31:22,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:22,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:23,345 - INFO - Response - Page 4:
2025-07-06 16:31:23,345 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:31:23,860 - INFO - Request Parameters - Page 5:
2025-07-06 16:31:23,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:23,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:24,579 - INFO - Response - Page 5:
2025-07-06 16:31:24,579 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:31:25,079 - INFO - Request Parameters - Page 6:
2025-07-06 16:31:25,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:25,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:25,704 - INFO - Response - Page 6:
2025-07-06 16:31:25,704 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:31:26,204 - INFO - Request Parameters - Page 7:
2025-07-06 16:31:26,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:26,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:26,939 - INFO - Response - Page 7:
2025-07-06 16:31:26,939 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:31:27,439 - INFO - Request Parameters - Page 8:
2025-07-06 16:31:27,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:27,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:28,110 - INFO - Response - Page 8:
2025-07-06 16:31:28,110 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:31:28,610 - INFO - Request Parameters - Page 9:
2025-07-06 16:31:28,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:28,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:29,251 - INFO - Response - Page 9:
2025-07-06 16:31:29,251 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:31:29,767 - INFO - Request Parameters - Page 10:
2025-07-06 16:31:29,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:29,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:30,438 - INFO - Response - Page 10:
2025-07-06 16:31:30,438 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:31:30,939 - INFO - Request Parameters - Page 11:
2025-07-06 16:31:30,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:30,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:31,657 - INFO - Response - Page 11:
2025-07-06 16:31:31,657 - INFO - 第 11 页获取到 50 条记录
2025-07-06 16:31:32,173 - INFO - Request Parameters - Page 12:
2025-07-06 16:31:32,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:32,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:32,767 - INFO - Response - Page 12:
2025-07-06 16:31:32,767 - INFO - 第 12 页获取到 13 条记录
2025-07-06 16:31:33,267 - INFO - 查询完成，共获取到 563 条记录
2025-07-06 16:31:33,267 - INFO - 获取到 563 条表单数据
2025-07-06 16:31:33,267 - INFO - 当前日期 2025-06-04 有 2 条MySQL数据需要处理
2025-07-06 16:31:33,267 - INFO - 开始更新记录 - 表单实例ID: FINST-OLC66Z61R82W421HEUSSH6EVA9VG3ABWAXOBMAA
2025-07-06 16:31:33,860 - INFO - 更新表单数据成功: FINST-OLC66Z61R82W421HEUSSH6EVA9VG3ABWAXOBMAA
2025-07-06 16:31:33,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4612.0, 'new_value': 5802.3}, {'field': 'total_amount', 'old_value': 4612.0, 'new_value': 5802.3}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:31:33,860 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1VO5W4MKC7AG9RBZH213B2KRR9XOBMK3
2025-07-06 16:31:34,517 - INFO - 更新表单数据成功: FINST-BD766BC1VO5W4MKC7AG9RBZH213B2KRR9XOBMK3
2025-07-06 16:31:34,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3822.0, 'new_value': 4182.0}, {'field': 'total_amount', 'old_value': 3822.0, 'new_value': 4182.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:31:34,517 - INFO - 日期 2025-06-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-07-06 16:31:34,517 - INFO - 开始处理日期: 2025-06-06
2025-07-06 16:31:34,517 - INFO - Request Parameters - Page 1:
2025-07-06 16:31:34,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:34,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:35,282 - INFO - Response - Page 1:
2025-07-06 16:31:35,282 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:31:35,782 - INFO - Request Parameters - Page 2:
2025-07-06 16:31:35,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:35,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:36,485 - INFO - Response - Page 2:
2025-07-06 16:31:36,485 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:31:36,985 - INFO - Request Parameters - Page 3:
2025-07-06 16:31:36,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:36,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:37,673 - INFO - Response - Page 3:
2025-07-06 16:31:37,673 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:31:38,173 - INFO - Request Parameters - Page 4:
2025-07-06 16:31:38,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:38,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:38,876 - INFO - Response - Page 4:
2025-07-06 16:31:38,876 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:31:39,392 - INFO - Request Parameters - Page 5:
2025-07-06 16:31:39,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:39,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:40,017 - INFO - Response - Page 5:
2025-07-06 16:31:40,017 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:31:40,532 - INFO - Request Parameters - Page 6:
2025-07-06 16:31:40,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:40,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:41,235 - INFO - Response - Page 6:
2025-07-06 16:31:41,235 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:31:41,735 - INFO - Request Parameters - Page 7:
2025-07-06 16:31:41,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:41,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:42,423 - INFO - Response - Page 7:
2025-07-06 16:31:42,423 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:31:42,938 - INFO - Request Parameters - Page 8:
2025-07-06 16:31:42,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:42,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:43,657 - INFO - Response - Page 8:
2025-07-06 16:31:43,657 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:31:44,157 - INFO - Request Parameters - Page 9:
2025-07-06 16:31:44,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:44,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:44,860 - INFO - Response - Page 9:
2025-07-06 16:31:44,860 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:31:45,360 - INFO - Request Parameters - Page 10:
2025-07-06 16:31:45,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:45,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:46,095 - INFO - Response - Page 10:
2025-07-06 16:31:46,095 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:31:46,595 - INFO - Request Parameters - Page 11:
2025-07-06 16:31:46,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:46,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:47,298 - INFO - Response - Page 11:
2025-07-06 16:31:47,298 - INFO - 第 11 页获取到 50 条记录
2025-07-06 16:31:47,813 - INFO - Request Parameters - Page 12:
2025-07-06 16:31:47,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:47,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:48,423 - INFO - Response - Page 12:
2025-07-06 16:31:48,423 - INFO - 第 12 页获取到 16 条记录
2025-07-06 16:31:48,923 - INFO - 查询完成，共获取到 566 条记录
2025-07-06 16:31:48,923 - INFO - 获取到 566 条表单数据
2025-07-06 16:31:48,923 - INFO - 当前日期 2025-06-06 有 1 条MySQL数据需要处理
2025-07-06 16:31:48,923 - INFO - 开始更新记录 - 表单实例ID: FINST-1T666B91KM5WGDIWDDW3GCYMXWPE2SYPDXOBMI4
2025-07-06 16:31:49,516 - INFO - 更新表单数据成功: FINST-1T666B91KM5WGDIWDDW3GCYMXWPE2SYPDXOBMI4
2025-07-06 16:31:49,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2719.0, 'new_value': 4635.5}, {'field': 'total_amount', 'old_value': 2719.0, 'new_value': 4635.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:31:49,516 - INFO - 日期 2025-06-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:31:49,516 - INFO - 开始处理日期: 2025-06-07
2025-07-06 16:31:49,516 - INFO - Request Parameters - Page 1:
2025-07-06 16:31:49,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:49,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:50,235 - INFO - Response - Page 1:
2025-07-06 16:31:50,235 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:31:50,751 - INFO - Request Parameters - Page 2:
2025-07-06 16:31:50,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:50,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:51,438 - INFO - Response - Page 2:
2025-07-06 16:31:51,438 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:31:51,954 - INFO - Request Parameters - Page 3:
2025-07-06 16:31:51,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:51,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:52,923 - INFO - Response - Page 3:
2025-07-06 16:31:52,923 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:31:53,423 - INFO - Request Parameters - Page 4:
2025-07-06 16:31:53,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:53,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:54,141 - INFO - Response - Page 4:
2025-07-06 16:31:54,141 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:31:54,641 - INFO - Request Parameters - Page 5:
2025-07-06 16:31:54,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:54,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:55,376 - INFO - Response - Page 5:
2025-07-06 16:31:55,376 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:31:55,876 - INFO - Request Parameters - Page 6:
2025-07-06 16:31:55,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:55,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:56,532 - INFO - Response - Page 6:
2025-07-06 16:31:56,532 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:31:57,048 - INFO - Request Parameters - Page 7:
2025-07-06 16:31:57,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:57,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:57,720 - INFO - Response - Page 7:
2025-07-06 16:31:57,720 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:31:58,220 - INFO - Request Parameters - Page 8:
2025-07-06 16:31:58,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:58,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:31:58,938 - INFO - Response - Page 8:
2025-07-06 16:31:58,938 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:31:59,454 - INFO - Request Parameters - Page 9:
2025-07-06 16:31:59,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:31:59,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:00,157 - INFO - Response - Page 9:
2025-07-06 16:32:00,157 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:32:00,673 - INFO - Request Parameters - Page 10:
2025-07-06 16:32:00,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:00,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:01,329 - INFO - Response - Page 10:
2025-07-06 16:32:01,329 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:32:01,829 - INFO - Request Parameters - Page 11:
2025-07-06 16:32:01,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:01,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:02,579 - INFO - Response - Page 11:
2025-07-06 16:32:02,579 - INFO - 第 11 页获取到 50 条记录
2025-07-06 16:32:03,079 - INFO - Request Parameters - Page 12:
2025-07-06 16:32:03,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:03,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:03,766 - INFO - Response - Page 12:
2025-07-06 16:32:03,766 - INFO - 第 12 页获取到 50 条记录
2025-07-06 16:32:04,266 - INFO - Request Parameters - Page 13:
2025-07-06 16:32:04,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:04,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:04,813 - INFO - Response - Page 13:
2025-07-06 16:32:04,813 - INFO - 第 13 页获取到 10 条记录
2025-07-06 16:32:05,329 - INFO - 查询完成，共获取到 610 条记录
2025-07-06 16:32:05,329 - INFO - 获取到 610 条表单数据
2025-07-06 16:32:05,329 - INFO - 当前日期 2025-06-07 有 1 条MySQL数据需要处理
2025-07-06 16:32:05,329 - INFO - 开始更新记录 - 表单实例ID: FINST-PGC66MB1B82WDHSJBNFXJCKG9FKO3B03FXOBM0H
2025-07-06 16:32:05,923 - INFO - 更新表单数据成功: FINST-PGC66MB1B82WDHSJBNFXJCKG9FKO3B03FXOBM0H
2025-07-06 16:32:05,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2664.7, 'new_value': 2661.7}, {'field': 'total_amount', 'old_value': 10598.7, 'new_value': 10595.7}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:32:05,923 - INFO - 日期 2025-06-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:32:05,923 - INFO - 开始处理日期: 2025-06-14
2025-07-06 16:32:05,923 - INFO - Request Parameters - Page 1:
2025-07-06 16:32:05,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:05,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:06,641 - INFO - Response - Page 1:
2025-07-06 16:32:06,641 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:32:07,157 - INFO - Request Parameters - Page 2:
2025-07-06 16:32:07,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:07,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:07,844 - INFO - Response - Page 2:
2025-07-06 16:32:07,844 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:32:08,344 - INFO - Request Parameters - Page 3:
2025-07-06 16:32:08,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:08,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:08,970 - INFO - Response - Page 3:
2025-07-06 16:32:08,970 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:32:09,485 - INFO - Request Parameters - Page 4:
2025-07-06 16:32:09,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:09,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:10,173 - INFO - Response - Page 4:
2025-07-06 16:32:10,173 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:32:10,688 - INFO - Request Parameters - Page 5:
2025-07-06 16:32:10,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:10,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:11,360 - INFO - Response - Page 5:
2025-07-06 16:32:11,360 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:32:11,876 - INFO - Request Parameters - Page 6:
2025-07-06 16:32:11,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:11,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:12,579 - INFO - Response - Page 6:
2025-07-06 16:32:12,579 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:32:13,094 - INFO - Request Parameters - Page 7:
2025-07-06 16:32:13,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:13,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:13,782 - INFO - Response - Page 7:
2025-07-06 16:32:13,782 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:32:14,298 - INFO - Request Parameters - Page 8:
2025-07-06 16:32:14,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:14,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:14,923 - INFO - Response - Page 8:
2025-07-06 16:32:14,923 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:32:15,423 - INFO - Request Parameters - Page 9:
2025-07-06 16:32:15,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:15,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:16,126 - INFO - Response - Page 9:
2025-07-06 16:32:16,126 - INFO - 第 9 页获取到 41 条记录
2025-07-06 16:32:16,626 - INFO - 查询完成，共获取到 441 条记录
2025-07-06 16:32:16,626 - INFO - 获取到 441 条表单数据
2025-07-06 16:32:16,626 - INFO - 当前日期 2025-06-14 有 1 条MySQL数据需要处理
2025-07-06 16:32:16,626 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMRK
2025-07-06 16:32:17,204 - INFO - 更新表单数据成功: FINST-XL866HB1BJ9WKUVMES65ECNLZSP73OV9V1XBMRK
2025-07-06 16:32:17,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8004.0, 'new_value': 11251.6}, {'field': 'total_amount', 'old_value': 8004.0, 'new_value': 11251.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:32:17,204 - INFO - 日期 2025-06-14 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:32:17,204 - INFO - 开始处理日期: 2025-06-15
2025-07-06 16:32:17,204 - INFO - Request Parameters - Page 1:
2025-07-06 16:32:17,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:17,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:17,876 - INFO - Response - Page 1:
2025-07-06 16:32:17,876 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:32:18,376 - INFO - Request Parameters - Page 2:
2025-07-06 16:32:18,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:18,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:19,094 - INFO - Response - Page 2:
2025-07-06 16:32:19,094 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:32:19,594 - INFO - Request Parameters - Page 3:
2025-07-06 16:32:19,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:19,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:20,313 - INFO - Response - Page 3:
2025-07-06 16:32:20,313 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:32:20,829 - INFO - Request Parameters - Page 4:
2025-07-06 16:32:20,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:20,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:21,516 - INFO - Response - Page 4:
2025-07-06 16:32:21,516 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:32:22,016 - INFO - Request Parameters - Page 5:
2025-07-06 16:32:22,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:22,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:22,673 - INFO - Response - Page 5:
2025-07-06 16:32:22,673 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:32:23,188 - INFO - Request Parameters - Page 6:
2025-07-06 16:32:23,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:23,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:23,891 - INFO - Response - Page 6:
2025-07-06 16:32:23,891 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:32:24,391 - INFO - Request Parameters - Page 7:
2025-07-06 16:32:24,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:24,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:25,063 - INFO - Response - Page 7:
2025-07-06 16:32:25,063 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:32:25,579 - INFO - Request Parameters - Page 8:
2025-07-06 16:32:25,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:25,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:26,219 - INFO - Response - Page 8:
2025-07-06 16:32:26,219 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:32:26,735 - INFO - Request Parameters - Page 9:
2025-07-06 16:32:26,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:26,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:27,391 - INFO - Response - Page 9:
2025-07-06 16:32:27,391 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:32:27,907 - INFO - Request Parameters - Page 10:
2025-07-06 16:32:27,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:27,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:28,641 - INFO - Response - Page 10:
2025-07-06 16:32:28,641 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:32:29,141 - INFO - Request Parameters - Page 11:
2025-07-06 16:32:29,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:29,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:29,876 - INFO - Response - Page 11:
2025-07-06 16:32:29,876 - INFO - 第 11 页获取到 50 条记录
2025-07-06 16:32:30,391 - INFO - Request Parameters - Page 12:
2025-07-06 16:32:30,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:30,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:30,876 - INFO - Response - Page 12:
2025-07-06 16:32:30,876 - INFO - 第 12 页获取到 2 条记录
2025-07-06 16:32:31,376 - INFO - 查询完成，共获取到 552 条记录
2025-07-06 16:32:31,376 - INFO - 获取到 552 条表单数据
2025-07-06 16:32:31,376 - INFO - 当前日期 2025-06-15 有 1 条MySQL数据需要处理
2025-07-06 16:32:31,376 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMB3
2025-07-06 16:32:31,954 - INFO - 更新表单数据成功: FINST-3PF66X612T9WOAL5FSZ3NCBZPASF37LM9HYBMB3
2025-07-06 16:32:31,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3808.0, 'new_value': 5830.8}, {'field': 'total_amount', 'old_value': 3808.0, 'new_value': 5830.8}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:32:31,954 - INFO - 日期 2025-06-15 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:32:31,954 - INFO - 开始处理日期: 2025-06-17
2025-07-06 16:32:31,954 - INFO - Request Parameters - Page 1:
2025-07-06 16:32:31,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:31,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:32,610 - INFO - Response - Page 1:
2025-07-06 16:32:32,610 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:32:33,126 - INFO - Request Parameters - Page 2:
2025-07-06 16:32:33,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:33,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:33,782 - INFO - Response - Page 2:
2025-07-06 16:32:33,782 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:32:34,297 - INFO - Request Parameters - Page 3:
2025-07-06 16:32:34,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:34,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:35,047 - INFO - Response - Page 3:
2025-07-06 16:32:35,047 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:32:35,563 - INFO - Request Parameters - Page 4:
2025-07-06 16:32:35,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:35,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:36,266 - INFO - Response - Page 4:
2025-07-06 16:32:36,266 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:32:36,782 - INFO - Request Parameters - Page 5:
2025-07-06 16:32:36,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:36,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:37,516 - INFO - Response - Page 5:
2025-07-06 16:32:37,516 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:32:38,032 - INFO - Request Parameters - Page 6:
2025-07-06 16:32:38,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:38,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:38,688 - INFO - Response - Page 6:
2025-07-06 16:32:38,688 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:32:39,188 - INFO - Request Parameters - Page 7:
2025-07-06 16:32:39,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:39,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:39,860 - INFO - Response - Page 7:
2025-07-06 16:32:39,860 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:32:40,360 - INFO - Request Parameters - Page 8:
2025-07-06 16:32:40,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:40,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:41,047 - INFO - Response - Page 8:
2025-07-06 16:32:41,047 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:32:41,563 - INFO - Request Parameters - Page 9:
2025-07-06 16:32:41,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:41,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:42,282 - INFO - Response - Page 9:
2025-07-06 16:32:42,282 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:32:42,782 - INFO - Request Parameters - Page 10:
2025-07-06 16:32:42,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:42,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:43,485 - INFO - Response - Page 10:
2025-07-06 16:32:43,485 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:32:43,985 - INFO - Request Parameters - Page 11:
2025-07-06 16:32:43,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:43,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:44,626 - INFO - Response - Page 11:
2025-07-06 16:32:44,626 - INFO - 第 11 页获取到 38 条记录
2025-07-06 16:32:45,126 - INFO - 查询完成，共获取到 538 条记录
2025-07-06 16:32:45,126 - INFO - 获取到 538 条表单数据
2025-07-06 16:32:45,126 - INFO - 当前日期 2025-06-17 有 2 条MySQL数据需要处理
2025-07-06 16:32:45,126 - INFO - 开始更新记录 - 表单实例ID: FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMJ7
2025-07-06 16:32:45,704 - INFO - 更新表单数据成功: FINST-SWC66P91K0EWW0BMC1CY8515TNAL26J17C1CMJ7
2025-07-06 16:32:45,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600.0, 'new_value': 605.0}, {'field': 'total_amount', 'old_value': 600.0, 'new_value': 605.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:32:45,704 - INFO - 开始更新记录 - 表单实例ID: FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMR1
2025-07-06 16:32:46,375 - INFO - 更新表单数据成功: FINST-NS766991UJEWCX52EG8ZU4TINOD43O835C1CMR1
2025-07-06 16:32:46,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1078.3, 'new_value': 1060.3}, {'field': 'total_amount', 'old_value': 4509.3, 'new_value': 4491.3}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:32:46,375 - INFO - 日期 2025-06-17 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-07-06 16:32:46,375 - INFO - 开始处理日期: 2025-06-23
2025-07-06 16:32:46,375 - INFO - Request Parameters - Page 1:
2025-07-06 16:32:46,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:46,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:47,063 - INFO - Response - Page 1:
2025-07-06 16:32:47,063 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:32:47,563 - INFO - Request Parameters - Page 2:
2025-07-06 16:32:47,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:47,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:48,313 - INFO - Response - Page 2:
2025-07-06 16:32:48,313 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:32:48,813 - INFO - Request Parameters - Page 3:
2025-07-06 16:32:48,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:48,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:49,547 - INFO - Response - Page 3:
2025-07-06 16:32:49,547 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:32:50,047 - INFO - Request Parameters - Page 4:
2025-07-06 16:32:50,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:50,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:50,704 - INFO - Response - Page 4:
2025-07-06 16:32:50,704 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:32:51,204 - INFO - Request Parameters - Page 5:
2025-07-06 16:32:51,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:51,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:51,860 - INFO - Response - Page 5:
2025-07-06 16:32:51,860 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:32:52,375 - INFO - Request Parameters - Page 6:
2025-07-06 16:32:52,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:52,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:53,016 - INFO - Response - Page 6:
2025-07-06 16:32:53,016 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:32:53,516 - INFO - Request Parameters - Page 7:
2025-07-06 16:32:53,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:53,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:54,125 - INFO - Response - Page 7:
2025-07-06 16:32:54,125 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:32:54,625 - INFO - Request Parameters - Page 8:
2025-07-06 16:32:54,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:54,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:55,329 - INFO - Response - Page 8:
2025-07-06 16:32:55,329 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:32:55,829 - INFO - Request Parameters - Page 9:
2025-07-06 16:32:55,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:55,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:56,579 - INFO - Response - Page 9:
2025-07-06 16:32:56,579 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:32:57,094 - INFO - Request Parameters - Page 10:
2025-07-06 16:32:57,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:57,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:57,860 - INFO - Response - Page 10:
2025-07-06 16:32:57,860 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:32:58,360 - INFO - Request Parameters - Page 11:
2025-07-06 16:32:58,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:32:58,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:32:59,094 - INFO - Response - Page 11:
2025-07-06 16:32:59,094 - INFO - 第 11 页获取到 45 条记录
2025-07-06 16:32:59,610 - INFO - 查询完成，共获取到 545 条记录
2025-07-06 16:32:59,610 - INFO - 获取到 545 条表单数据
2025-07-06 16:32:59,610 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-07-06 16:32:59,610 - INFO - 开始更新记录 - 表单实例ID: FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM81
2025-07-06 16:33:00,188 - INFO - 更新表单数据成功: FINST-L5766E715XJWTM1L6EUA6B7831O43HGXTW9CM81
2025-07-06 16:33:00,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3966.0, 'new_value': 6078.2}, {'field': 'total_amount', 'old_value': 3966.0, 'new_value': 6078.2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:33:00,188 - INFO - 日期 2025-06-23 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:33:00,188 - INFO - 开始处理日期: 2025-06-24
2025-07-06 16:33:00,188 - INFO - Request Parameters - Page 1:
2025-07-06 16:33:00,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:00,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:00,922 - INFO - Response - Page 1:
2025-07-06 16:33:00,922 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:33:01,422 - INFO - Request Parameters - Page 2:
2025-07-06 16:33:01,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:01,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:02,110 - INFO - Response - Page 2:
2025-07-06 16:33:02,110 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:33:02,625 - INFO - Request Parameters - Page 3:
2025-07-06 16:33:02,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:02,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:03,282 - INFO - Response - Page 3:
2025-07-06 16:33:03,282 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:33:03,782 - INFO - Request Parameters - Page 4:
2025-07-06 16:33:03,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:03,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:04,422 - INFO - Response - Page 4:
2025-07-06 16:33:04,422 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:33:04,938 - INFO - Request Parameters - Page 5:
2025-07-06 16:33:04,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:04,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:05,657 - INFO - Response - Page 5:
2025-07-06 16:33:05,657 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:33:06,172 - INFO - Request Parameters - Page 6:
2025-07-06 16:33:06,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:06,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:06,907 - INFO - Response - Page 6:
2025-07-06 16:33:06,907 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:33:07,422 - INFO - Request Parameters - Page 7:
2025-07-06 16:33:07,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:07,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:08,125 - INFO - Response - Page 7:
2025-07-06 16:33:08,125 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:33:08,641 - INFO - Request Parameters - Page 8:
2025-07-06 16:33:08,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:08,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:09,313 - INFO - Response - Page 8:
2025-07-06 16:33:09,313 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:33:09,828 - INFO - Request Parameters - Page 9:
2025-07-06 16:33:09,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:09,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:10,516 - INFO - Response - Page 9:
2025-07-06 16:33:10,516 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:33:11,032 - INFO - Request Parameters - Page 10:
2025-07-06 16:33:11,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:11,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:11,735 - INFO - Response - Page 10:
2025-07-06 16:33:11,735 - INFO - 第 10 页获取到 50 条记录
2025-07-06 16:33:12,250 - INFO - Request Parameters - Page 11:
2025-07-06 16:33:12,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:12,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:12,688 - INFO - Response - Page 11:
2025-07-06 16:33:12,688 - INFO - 查询完成，共获取到 500 条记录
2025-07-06 16:33:12,688 - INFO - 获取到 500 条表单数据
2025-07-06 16:33:12,703 - INFO - 当前日期 2025-06-24 有 1 条MySQL数据需要处理
2025-07-06 16:33:12,703 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM6I
2025-07-06 16:33:13,297 - INFO - 更新表单数据成功: FINST-90D66XA1VMKWBWVO9DSI8DBRDEH42NVO7CBCM6I
2025-07-06 16:33:13,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3217.0, 'new_value': 4391.4}, {'field': 'total_amount', 'old_value': 3217.0, 'new_value': 4391.4}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:33:13,297 - INFO - 日期 2025-06-24 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:33:13,297 - INFO - 开始处理日期: 2025-06-27
2025-07-06 16:33:13,297 - INFO - Request Parameters - Page 1:
2025-07-06 16:33:13,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:13,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:14,032 - INFO - Response - Page 1:
2025-07-06 16:33:14,032 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:33:14,547 - INFO - Request Parameters - Page 2:
2025-07-06 16:33:14,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:14,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:15,219 - INFO - Response - Page 2:
2025-07-06 16:33:15,219 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:33:15,719 - INFO - Request Parameters - Page 3:
2025-07-06 16:33:15,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:15,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:16,438 - INFO - Response - Page 3:
2025-07-06 16:33:16,438 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:33:16,953 - INFO - Request Parameters - Page 4:
2025-07-06 16:33:16,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:16,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:17,594 - INFO - Response - Page 4:
2025-07-06 16:33:17,594 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:33:18,110 - INFO - Request Parameters - Page 5:
2025-07-06 16:33:18,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:18,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:18,750 - INFO - Response - Page 5:
2025-07-06 16:33:18,750 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:33:19,266 - INFO - Request Parameters - Page 6:
2025-07-06 16:33:19,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:19,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:19,938 - INFO - Response - Page 6:
2025-07-06 16:33:19,938 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:33:20,453 - INFO - Request Parameters - Page 7:
2025-07-06 16:33:20,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:20,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:21,125 - INFO - Response - Page 7:
2025-07-06 16:33:21,125 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:33:21,625 - INFO - Request Parameters - Page 8:
2025-07-06 16:33:21,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:21,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:22,328 - INFO - Response - Page 8:
2025-07-06 16:33:22,328 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:33:22,828 - INFO - Request Parameters - Page 9:
2025-07-06 16:33:22,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:22,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:23,672 - INFO - Response - Page 9:
2025-07-06 16:33:23,672 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:33:24,188 - INFO - Request Parameters - Page 10:
2025-07-06 16:33:24,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:24,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:24,797 - INFO - Response - Page 10:
2025-07-06 16:33:24,797 - INFO - 第 10 页获取到 28 条记录
2025-07-06 16:33:25,297 - INFO - 查询完成，共获取到 478 条记录
2025-07-06 16:33:25,297 - INFO - 获取到 478 条表单数据
2025-07-06 16:33:25,297 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-07-06 16:33:25,297 - INFO - 开始更新记录 - 表单实例ID: FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCML7
2025-07-06 16:33:25,860 - INFO - 更新表单数据成功: FINST-CJ966Q71C9OW247K8HM6KCMF8V9B3W7ALMFCML7
2025-07-06 16:33:25,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1377.6, 'new_value': 1377.5}, {'field': 'total_amount', 'old_value': 17629.6, 'new_value': 17629.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-06 16:33:25,860 - INFO - 日期 2025-06-27 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 16:33:25,860 - INFO - 开始处理日期: 2025-07-04
2025-07-06 16:33:25,860 - INFO - Request Parameters - Page 1:
2025-07-06 16:33:25,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:25,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:26,563 - INFO - Response - Page 1:
2025-07-06 16:33:26,563 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:33:27,078 - INFO - Request Parameters - Page 2:
2025-07-06 16:33:27,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:27,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:27,703 - INFO - Response - Page 2:
2025-07-06 16:33:27,703 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:33:28,203 - INFO - Request Parameters - Page 3:
2025-07-06 16:33:28,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:28,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:28,922 - INFO - Response - Page 3:
2025-07-06 16:33:28,922 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:33:29,438 - INFO - Request Parameters - Page 4:
2025-07-06 16:33:29,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:29,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:30,094 - INFO - Response - Page 4:
2025-07-06 16:33:30,094 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:33:30,610 - INFO - Request Parameters - Page 5:
2025-07-06 16:33:30,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:30,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:31,313 - INFO - Response - Page 5:
2025-07-06 16:33:31,313 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:33:31,813 - INFO - Request Parameters - Page 6:
2025-07-06 16:33:31,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:31,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:32,469 - INFO - Response - Page 6:
2025-07-06 16:33:32,469 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:33:32,969 - INFO - Request Parameters - Page 7:
2025-07-06 16:33:32,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:32,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:33,625 - INFO - Response - Page 7:
2025-07-06 16:33:33,625 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:33:34,141 - INFO - Request Parameters - Page 8:
2025-07-06 16:33:34,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:34,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:34,844 - INFO - Response - Page 8:
2025-07-06 16:33:34,844 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:33:35,360 - INFO - Request Parameters - Page 9:
2025-07-06 16:33:35,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:35,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:36,016 - INFO - Response - Page 9:
2025-07-06 16:33:36,016 - INFO - 第 9 页获取到 42 条记录
2025-07-06 16:33:36,516 - INFO - 查询完成，共获取到 442 条记录
2025-07-06 16:33:36,516 - INFO - 获取到 442 条表单数据
2025-07-06 16:33:36,516 - INFO - 当前日期 2025-07-04 有 3 条MySQL数据需要处理
2025-07-06 16:33:36,516 - INFO - 日期 2025-07-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 16:33:36,516 - INFO - 开始处理日期: 2025-07-05
2025-07-06 16:33:36,516 - INFO - Request Parameters - Page 1:
2025-07-06 16:33:36,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:36,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:37,250 - INFO - Response - Page 1:
2025-07-06 16:33:37,250 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:33:37,750 - INFO - Request Parameters - Page 2:
2025-07-06 16:33:37,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:37,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:38,422 - INFO - Response - Page 2:
2025-07-06 16:33:38,422 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:33:38,922 - INFO - Request Parameters - Page 3:
2025-07-06 16:33:38,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:38,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:39,563 - INFO - Response - Page 3:
2025-07-06 16:33:39,563 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:33:40,078 - INFO - Request Parameters - Page 4:
2025-07-06 16:33:40,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:40,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:40,735 - INFO - Response - Page 4:
2025-07-06 16:33:40,735 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:33:41,235 - INFO - Request Parameters - Page 5:
2025-07-06 16:33:41,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:41,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:41,906 - INFO - Response - Page 5:
2025-07-06 16:33:41,906 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:33:42,406 - INFO - Request Parameters - Page 6:
2025-07-06 16:33:42,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:42,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:43,141 - INFO - Response - Page 6:
2025-07-06 16:33:43,141 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:33:43,641 - INFO - Request Parameters - Page 7:
2025-07-06 16:33:43,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:43,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:44,344 - INFO - Response - Page 7:
2025-07-06 16:33:44,344 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:33:44,844 - INFO - Request Parameters - Page 8:
2025-07-06 16:33:44,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:44,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:45,500 - INFO - Response - Page 8:
2025-07-06 16:33:45,500 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:33:46,000 - INFO - Request Parameters - Page 9:
2025-07-06 16:33:46,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:46,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:46,672 - INFO - Response - Page 9:
2025-07-06 16:33:46,672 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:33:47,172 - INFO - Request Parameters - Page 10:
2025-07-06 16:33:47,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:47,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:47,750 - INFO - Response - Page 10:
2025-07-06 16:33:47,750 - INFO - 第 10 页获取到 14 条记录
2025-07-06 16:33:48,250 - INFO - 查询完成，共获取到 464 条记录
2025-07-06 16:33:48,250 - INFO - 获取到 464 条表单数据
2025-07-06 16:33:48,250 - INFO - 当前日期 2025-07-05 有 136 条MySQL数据需要处理
2025-07-06 16:33:48,250 - INFO - 开始批量插入 2 条新记录
2025-07-06 16:33:48,406 - INFO - 批量插入响应状态码: 200
2025-07-06 16:33:48,406 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 08:33:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '98608303-9A13-7ED6-99EA-E78CC940641B', 'x-acs-trace-id': '070cb00b69a802b769a713184cabdd3a', 'etag': '1TEtSLLLLxwwtp+ziBuVODg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 16:33:48,406 - INFO - 批量插入响应体: {'result': ['FINST-VOC66Y91X2WWOM1FD5YGFCF1QOWW3I021FRCMX4', 'FINST-VOC66Y91X2WWOM1FD5YGFCF1QOWW3I021FRCMY4']}
2025-07-06 16:33:48,406 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-06 16:33:48,406 - INFO - 成功插入的数据ID: ['FINST-VOC66Y91X2WWOM1FD5YGFCF1QOWW3I021FRCMX4', 'FINST-VOC66Y91X2WWOM1FD5YGFCF1QOWW3I021FRCMY4']
2025-07-06 16:33:53,422 - INFO - 批量插入完成，共 2 条记录
2025-07-06 16:33:53,422 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-06 16:33:53,422 - INFO - 开始处理日期: 2025-07-06
2025-07-06 16:33:53,422 - INFO - Request Parameters - Page 1:
2025-07-06 16:33:53,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:33:53,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:33:53,906 - INFO - Response - Page 1:
2025-07-06 16:33:53,906 - INFO - 第 1 页获取到 1 条记录
2025-07-06 16:33:54,406 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 16:33:54,406 - INFO - 获取到 1 条表单数据
2025-07-06 16:33:54,406 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 16:33:54,406 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 16:33:54,406 - INFO - 数据同步完成！更新: 14 条，插入: 2 条，错误: 1 条
2025-07-06 16:34:54,422 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 16:34:54,422 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 16:34:54,422 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 16:34:54,562 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 477 条记录
2025-07-06 16:34:54,562 - INFO - 获取到 2 个日期需要处理: ['2025-07-05', '2025-07-06']
2025-07-06 16:34:54,578 - INFO - 开始处理日期: 2025-07-05
2025-07-06 16:34:54,578 - INFO - Request Parameters - Page 1:
2025-07-06 16:34:54,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:34:54,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:34:55,281 - INFO - Response - Page 1:
2025-07-06 16:34:55,281 - INFO - 第 1 页获取到 50 条记录
2025-07-06 16:34:55,781 - INFO - Request Parameters - Page 2:
2025-07-06 16:34:55,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:34:55,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:34:56,484 - INFO - Response - Page 2:
2025-07-06 16:34:56,484 - INFO - 第 2 页获取到 50 条记录
2025-07-06 16:34:57,000 - INFO - Request Parameters - Page 3:
2025-07-06 16:34:57,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:34:57,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:34:57,718 - INFO - Response - Page 3:
2025-07-06 16:34:57,718 - INFO - 第 3 页获取到 50 条记录
2025-07-06 16:34:58,234 - INFO - Request Parameters - Page 4:
2025-07-06 16:34:58,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:34:58,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:34:58,937 - INFO - Response - Page 4:
2025-07-06 16:34:58,937 - INFO - 第 4 页获取到 50 条记录
2025-07-06 16:34:59,453 - INFO - Request Parameters - Page 5:
2025-07-06 16:34:59,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:34:59,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:00,156 - INFO - Response - Page 5:
2025-07-06 16:35:00,156 - INFO - 第 5 页获取到 50 条记录
2025-07-06 16:35:00,671 - INFO - Request Parameters - Page 6:
2025-07-06 16:35:00,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:35:00,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:01,328 - INFO - Response - Page 6:
2025-07-06 16:35:01,328 - INFO - 第 6 页获取到 50 条记录
2025-07-06 16:35:01,843 - INFO - Request Parameters - Page 7:
2025-07-06 16:35:01,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:35:01,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:02,531 - INFO - Response - Page 7:
2025-07-06 16:35:02,531 - INFO - 第 7 页获取到 50 条记录
2025-07-06 16:35:03,046 - INFO - Request Parameters - Page 8:
2025-07-06 16:35:03,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:35:03,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:03,781 - INFO - Response - Page 8:
2025-07-06 16:35:03,781 - INFO - 第 8 页获取到 50 条记录
2025-07-06 16:35:04,281 - INFO - Request Parameters - Page 9:
2025-07-06 16:35:04,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:35:04,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:05,000 - INFO - Response - Page 9:
2025-07-06 16:35:05,000 - INFO - 第 9 页获取到 50 条记录
2025-07-06 16:35:05,515 - INFO - Request Parameters - Page 10:
2025-07-06 16:35:05,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:35:05,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:06,171 - INFO - Response - Page 10:
2025-07-06 16:35:06,171 - INFO - 第 10 页获取到 16 条记录
2025-07-06 16:35:06,687 - INFO - 查询完成，共获取到 466 条记录
2025-07-06 16:35:06,687 - INFO - 获取到 466 条表单数据
2025-07-06 16:35:06,687 - INFO - 当前日期 2025-07-05 有 466 条MySQL数据需要处理
2025-07-06 16:35:06,703 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 16:35:06,703 - INFO - 开始处理日期: 2025-07-06
2025-07-06 16:35:06,703 - INFO - Request Parameters - Page 1:
2025-07-06 16:35:06,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 16:35:06,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 16:35:07,140 - INFO - Response - Page 1:
2025-07-06 16:35:07,140 - INFO - 第 1 页获取到 1 条记录
2025-07-06 16:35:07,640 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 16:35:07,640 - INFO - 获取到 1 条表单数据
2025-07-06 16:35:07,640 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 16:35:07,640 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 16:35:07,640 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 16:35:07,640 - INFO - 同步完成
2025-07-06 19:30:34,547 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 19:30:34,547 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 19:30:34,547 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 19:30:34,696 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 158 条记录
2025-07-06 19:30:34,696 - INFO - 获取到 15 个日期需要处理: ['2025-06-01', '2025-06-02', '2025-06-03', '2025-06-04', '2025-06-06', '2025-06-07', '2025-06-14', '2025-06-15', '2025-06-17', '2025-06-23', '2025-06-24', '2025-06-27', '2025-07-04', '2025-07-05', '2025-07-06']
2025-07-06 19:30:34,698 - INFO - 开始处理日期: 2025-06-01
2025-07-06 19:30:34,702 - INFO - Request Parameters - Page 1:
2025-07-06 19:30:34,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:34,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:42,802 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8488F0D3-F7AF-734C-92ED-F47AABF83801 Response: {'code': 'ServiceUnavailable', 'requestid': '8488F0D3-F7AF-734C-92ED-F47AABF83801', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8488F0D3-F7AF-734C-92ED-F47AABF83801)
2025-07-06 19:30:42,802 - INFO - 开始处理日期: 2025-06-02
2025-07-06 19:30:42,802 - INFO - Request Parameters - Page 1:
2025-07-06 19:30:42,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:42,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:50,366 - INFO - Response - Page 1:
2025-07-06 19:30:50,366 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:30:50,867 - INFO - Request Parameters - Page 2:
2025-07-06 19:30:50,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:50,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:51,586 - INFO - Response - Page 2:
2025-07-06 19:30:51,586 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:30:52,101 - INFO - Request Parameters - Page 3:
2025-07-06 19:30:52,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:52,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:52,789 - INFO - Response - Page 3:
2025-07-06 19:30:52,789 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:30:53,305 - INFO - Request Parameters - Page 4:
2025-07-06 19:30:53,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:53,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:54,024 - INFO - Response - Page 4:
2025-07-06 19:30:54,024 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:30:54,540 - INFO - Request Parameters - Page 5:
2025-07-06 19:30:54,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:54,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:55,213 - INFO - Response - Page 5:
2025-07-06 19:30:55,213 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:30:55,728 - INFO - Request Parameters - Page 6:
2025-07-06 19:30:55,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:55,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:56,447 - INFO - Response - Page 6:
2025-07-06 19:30:56,447 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:30:56,947 - INFO - Request Parameters - Page 7:
2025-07-06 19:30:56,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:56,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:57,607 - INFO - Response - Page 7:
2025-07-06 19:30:57,607 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:30:58,119 - INFO - Request Parameters - Page 8:
2025-07-06 19:30:58,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:58,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:30:58,963 - INFO - Response - Page 8:
2025-07-06 19:30:58,963 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:30:59,479 - INFO - Request Parameters - Page 9:
2025-07-06 19:30:59,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:30:59,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:00,151 - INFO - Response - Page 9:
2025-07-06 19:31:00,151 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:31:00,667 - INFO - Request Parameters - Page 10:
2025-07-06 19:31:00,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:00,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:01,370 - INFO - Response - Page 10:
2025-07-06 19:31:01,370 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:31:01,872 - INFO - Request Parameters - Page 11:
2025-07-06 19:31:01,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:01,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:02,512 - INFO - Response - Page 11:
2025-07-06 19:31:02,512 - INFO - 第 11 页获取到 50 条记录
2025-07-06 19:31:03,013 - INFO - Request Parameters - Page 12:
2025-07-06 19:31:03,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:03,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:03,607 - INFO - Response - Page 12:
2025-07-06 19:31:03,607 - INFO - 第 12 页获取到 14 条记录
2025-07-06 19:31:04,122 - INFO - 查询完成，共获取到 564 条记录
2025-07-06 19:31:04,122 - INFO - 获取到 564 条表单数据
2025-07-06 19:31:04,122 - INFO - 当前日期 2025-06-02 有 2 条MySQL数据需要处理
2025-07-06 19:31:04,122 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:31:04,122 - INFO - 开始处理日期: 2025-06-03
2025-07-06 19:31:04,122 - INFO - Request Parameters - Page 1:
2025-07-06 19:31:04,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:04,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:04,950 - INFO - Response - Page 1:
2025-07-06 19:31:04,950 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:31:05,450 - INFO - Request Parameters - Page 2:
2025-07-06 19:31:05,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:05,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:06,154 - INFO - Response - Page 2:
2025-07-06 19:31:06,169 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:31:06,670 - INFO - Request Parameters - Page 3:
2025-07-06 19:31:06,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:06,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:07,373 - INFO - Response - Page 3:
2025-07-06 19:31:07,373 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:31:07,874 - INFO - Request Parameters - Page 4:
2025-07-06 19:31:07,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:07,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:08,515 - INFO - Response - Page 4:
2025-07-06 19:31:08,515 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:31:09,031 - INFO - Request Parameters - Page 5:
2025-07-06 19:31:09,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:09,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:09,749 - INFO - Response - Page 5:
2025-07-06 19:31:09,749 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:31:10,249 - INFO - Request Parameters - Page 6:
2025-07-06 19:31:10,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:10,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:10,921 - INFO - Response - Page 6:
2025-07-06 19:31:10,921 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:31:11,422 - INFO - Request Parameters - Page 7:
2025-07-06 19:31:11,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:11,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:12,047 - INFO - Response - Page 7:
2025-07-06 19:31:12,047 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:31:12,547 - INFO - Request Parameters - Page 8:
2025-07-06 19:31:12,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:12,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:13,235 - INFO - Response - Page 8:
2025-07-06 19:31:13,235 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:31:13,750 - INFO - Request Parameters - Page 9:
2025-07-06 19:31:13,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:13,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:14,486 - INFO - Response - Page 9:
2025-07-06 19:31:14,486 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:31:15,001 - INFO - Request Parameters - Page 10:
2025-07-06 19:31:15,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:15,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:15,751 - INFO - Response - Page 10:
2025-07-06 19:31:15,751 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:31:16,252 - INFO - Request Parameters - Page 11:
2025-07-06 19:31:16,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:16,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:16,939 - INFO - Response - Page 11:
2025-07-06 19:31:16,939 - INFO - 第 11 页获取到 50 条记录
2025-07-06 19:31:17,455 - INFO - Request Parameters - Page 12:
2025-07-06 19:31:17,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:17,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:18,019 - INFO - Response - Page 12:
2025-07-06 19:31:18,019 - INFO - 第 12 页获取到 12 条记录
2025-07-06 19:31:18,533 - INFO - 查询完成，共获取到 562 条记录
2025-07-06 19:31:18,533 - INFO - 获取到 562 条表单数据
2025-07-06 19:31:18,533 - INFO - 当前日期 2025-06-03 有 1 条MySQL数据需要处理
2025-07-06 19:31:18,533 - INFO - 日期 2025-06-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:31:18,533 - INFO - 开始处理日期: 2025-06-04
2025-07-06 19:31:18,533 - INFO - Request Parameters - Page 1:
2025-07-06 19:31:18,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:18,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:19,144 - INFO - Response - Page 1:
2025-07-06 19:31:19,160 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:31:19,676 - INFO - Request Parameters - Page 2:
2025-07-06 19:31:19,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:19,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:20,402 - INFO - Response - Page 2:
2025-07-06 19:31:20,403 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:31:20,909 - INFO - Request Parameters - Page 3:
2025-07-06 19:31:20,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:20,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:21,566 - INFO - Response - Page 3:
2025-07-06 19:31:21,566 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:31:22,066 - INFO - Request Parameters - Page 4:
2025-07-06 19:31:22,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:22,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:22,739 - INFO - Response - Page 4:
2025-07-06 19:31:22,739 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:31:23,254 - INFO - Request Parameters - Page 5:
2025-07-06 19:31:23,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:23,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:23,943 - INFO - Response - Page 5:
2025-07-06 19:31:23,943 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:31:24,458 - INFO - Request Parameters - Page 6:
2025-07-06 19:31:24,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:24,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:25,181 - INFO - Response - Page 6:
2025-07-06 19:31:25,181 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:31:25,682 - INFO - Request Parameters - Page 7:
2025-07-06 19:31:25,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:25,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:26,474 - INFO - Response - Page 7:
2025-07-06 19:31:26,474 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:31:26,990 - INFO - Request Parameters - Page 8:
2025-07-06 19:31:26,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:26,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:27,726 - INFO - Response - Page 8:
2025-07-06 19:31:27,726 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:31:28,240 - INFO - Request Parameters - Page 9:
2025-07-06 19:31:28,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:28,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:28,902 - INFO - Response - Page 9:
2025-07-06 19:31:28,903 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:31:29,404 - INFO - Request Parameters - Page 10:
2025-07-06 19:31:29,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:29,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:30,069 - INFO - Response - Page 10:
2025-07-06 19:31:30,069 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:31:30,570 - INFO - Request Parameters - Page 11:
2025-07-06 19:31:30,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:30,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:31,382 - INFO - Response - Page 11:
2025-07-06 19:31:31,382 - INFO - 第 11 页获取到 50 条记录
2025-07-06 19:31:31,884 - INFO - Request Parameters - Page 12:
2025-07-06 19:31:31,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:31,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:32,430 - INFO - Response - Page 12:
2025-07-06 19:31:32,430 - INFO - 第 12 页获取到 13 条记录
2025-07-06 19:31:32,930 - INFO - 查询完成，共获取到 563 条记录
2025-07-06 19:31:32,930 - INFO - 获取到 563 条表单数据
2025-07-06 19:31:32,930 - INFO - 当前日期 2025-06-04 有 2 条MySQL数据需要处理
2025-07-06 19:31:32,930 - INFO - 日期 2025-06-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:31:32,930 - INFO - 开始处理日期: 2025-06-06
2025-07-06 19:31:32,930 - INFO - Request Parameters - Page 1:
2025-07-06 19:31:32,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:32,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:33,571 - INFO - Response - Page 1:
2025-07-06 19:31:33,571 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:31:34,087 - INFO - Request Parameters - Page 2:
2025-07-06 19:31:34,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:34,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:34,742 - INFO - Response - Page 2:
2025-07-06 19:31:34,743 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:31:35,244 - INFO - Request Parameters - Page 3:
2025-07-06 19:31:35,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:35,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:35,916 - INFO - Response - Page 3:
2025-07-06 19:31:35,916 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:31:36,431 - INFO - Request Parameters - Page 4:
2025-07-06 19:31:36,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:36,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:37,103 - INFO - Response - Page 4:
2025-07-06 19:31:37,103 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:31:37,604 - INFO - Request Parameters - Page 5:
2025-07-06 19:31:37,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:37,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:38,276 - INFO - Response - Page 5:
2025-07-06 19:31:38,276 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:31:38,776 - INFO - Request Parameters - Page 6:
2025-07-06 19:31:38,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:38,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:39,417 - INFO - Response - Page 6:
2025-07-06 19:31:39,417 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:31:39,933 - INFO - Request Parameters - Page 7:
2025-07-06 19:31:39,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:39,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:40,636 - INFO - Response - Page 7:
2025-07-06 19:31:40,636 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:31:41,152 - INFO - Request Parameters - Page 8:
2025-07-06 19:31:41,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:41,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:41,777 - INFO - Response - Page 8:
2025-07-06 19:31:41,793 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:31:42,309 - INFO - Request Parameters - Page 9:
2025-07-06 19:31:42,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:42,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:42,981 - INFO - Response - Page 9:
2025-07-06 19:31:42,981 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:31:43,481 - INFO - Request Parameters - Page 10:
2025-07-06 19:31:43,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:43,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:44,138 - INFO - Response - Page 10:
2025-07-06 19:31:44,138 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:31:44,654 - INFO - Request Parameters - Page 11:
2025-07-06 19:31:44,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:44,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:45,372 - INFO - Response - Page 11:
2025-07-06 19:31:45,372 - INFO - 第 11 页获取到 50 条记录
2025-07-06 19:31:45,873 - INFO - Request Parameters - Page 12:
2025-07-06 19:31:45,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:45,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:46,467 - INFO - Response - Page 12:
2025-07-06 19:31:46,467 - INFO - 第 12 页获取到 16 条记录
2025-07-06 19:31:46,982 - INFO - 查询完成，共获取到 566 条记录
2025-07-06 19:31:46,982 - INFO - 获取到 566 条表单数据
2025-07-06 19:31:46,982 - INFO - 当前日期 2025-06-06 有 1 条MySQL数据需要处理
2025-07-06 19:31:46,982 - INFO - 日期 2025-06-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:31:46,982 - INFO - 开始处理日期: 2025-06-07
2025-07-06 19:31:46,982 - INFO - Request Parameters - Page 1:
2025-07-06 19:31:46,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:46,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:47,718 - INFO - Response - Page 1:
2025-07-06 19:31:47,718 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:31:48,218 - INFO - Request Parameters - Page 2:
2025-07-06 19:31:48,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:48,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:48,921 - INFO - Response - Page 2:
2025-07-06 19:31:48,921 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:31:49,421 - INFO - Request Parameters - Page 3:
2025-07-06 19:31:49,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:49,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:50,110 - INFO - Response - Page 3:
2025-07-06 19:31:50,110 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:31:50,610 - INFO - Request Parameters - Page 4:
2025-07-06 19:31:50,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:50,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:51,252 - INFO - Response - Page 4:
2025-07-06 19:31:51,252 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:31:51,754 - INFO - Request Parameters - Page 5:
2025-07-06 19:31:51,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:51,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:52,392 - INFO - Response - Page 5:
2025-07-06 19:31:52,392 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:31:52,893 - INFO - Request Parameters - Page 6:
2025-07-06 19:31:52,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:52,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:53,563 - INFO - Response - Page 6:
2025-07-06 19:31:53,563 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:31:54,079 - INFO - Request Parameters - Page 7:
2025-07-06 19:31:54,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:54,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:54,908 - INFO - Response - Page 7:
2025-07-06 19:31:54,908 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:31:55,409 - INFO - Request Parameters - Page 8:
2025-07-06 19:31:55,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:55,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:56,082 - INFO - Response - Page 8:
2025-07-06 19:31:56,082 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:31:56,596 - INFO - Request Parameters - Page 9:
2025-07-06 19:31:56,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:56,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:57,284 - INFO - Response - Page 9:
2025-07-06 19:31:57,300 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:31:57,816 - INFO - Request Parameters - Page 10:
2025-07-06 19:31:57,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:57,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:58,503 - INFO - Response - Page 10:
2025-07-06 19:31:58,503 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:31:59,005 - INFO - Request Parameters - Page 11:
2025-07-06 19:31:59,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:31:59,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:31:59,676 - INFO - Response - Page 11:
2025-07-06 19:31:59,676 - INFO - 第 11 页获取到 50 条记录
2025-07-06 19:32:00,176 - INFO - Request Parameters - Page 12:
2025-07-06 19:32:00,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:00,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:00,895 - INFO - Response - Page 12:
2025-07-06 19:32:00,895 - INFO - 第 12 页获取到 50 条记录
2025-07-06 19:32:01,396 - INFO - Request Parameters - Page 13:
2025-07-06 19:32:01,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:01,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:01,958 - INFO - Response - Page 13:
2025-07-06 19:32:01,958 - INFO - 第 13 页获取到 10 条记录
2025-07-06 19:32:02,474 - INFO - 查询完成，共获取到 610 条记录
2025-07-06 19:32:02,474 - INFO - 获取到 610 条表单数据
2025-07-06 19:32:02,474 - INFO - 当前日期 2025-06-07 有 1 条MySQL数据需要处理
2025-07-06 19:32:02,474 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:32:02,474 - INFO - 开始处理日期: 2025-06-14
2025-07-06 19:32:02,474 - INFO - Request Parameters - Page 1:
2025-07-06 19:32:02,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:02,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:03,199 - INFO - Response - Page 1:
2025-07-06 19:32:03,200 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:32:03,700 - INFO - Request Parameters - Page 2:
2025-07-06 19:32:03,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:03,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:04,402 - INFO - Response - Page 2:
2025-07-06 19:32:04,402 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:32:04,912 - INFO - Request Parameters - Page 3:
2025-07-06 19:32:04,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:04,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:05,741 - INFO - Response - Page 3:
2025-07-06 19:32:05,741 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:32:06,257 - INFO - Request Parameters - Page 4:
2025-07-06 19:32:06,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:06,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:06,944 - INFO - Response - Page 4:
2025-07-06 19:32:06,944 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:32:07,460 - INFO - Request Parameters - Page 5:
2025-07-06 19:32:07,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:07,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:08,232 - INFO - Response - Page 5:
2025-07-06 19:32:08,232 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:32:08,733 - INFO - Request Parameters - Page 6:
2025-07-06 19:32:08,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:08,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:09,399 - INFO - Response - Page 6:
2025-07-06 19:32:09,399 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:32:09,914 - INFO - Request Parameters - Page 7:
2025-07-06 19:32:09,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:09,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:10,618 - INFO - Response - Page 7:
2025-07-06 19:32:10,618 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:32:11,133 - INFO - Request Parameters - Page 8:
2025-07-06 19:32:11,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:11,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:11,790 - INFO - Response - Page 8:
2025-07-06 19:32:11,790 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:32:12,290 - INFO - Request Parameters - Page 9:
2025-07-06 19:32:12,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:12,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:12,947 - INFO - Response - Page 9:
2025-07-06 19:32:12,947 - INFO - 第 9 页获取到 41 条记录
2025-07-06 19:32:13,448 - INFO - 查询完成，共获取到 441 条记录
2025-07-06 19:32:13,448 - INFO - 获取到 441 条表单数据
2025-07-06 19:32:13,458 - INFO - 当前日期 2025-06-14 有 1 条MySQL数据需要处理
2025-07-06 19:32:13,458 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:32:13,458 - INFO - 开始处理日期: 2025-06-15
2025-07-06 19:32:13,458 - INFO - Request Parameters - Page 1:
2025-07-06 19:32:13,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:13,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:14,166 - INFO - Response - Page 1:
2025-07-06 19:32:14,166 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:32:14,667 - INFO - Request Parameters - Page 2:
2025-07-06 19:32:14,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:14,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:15,370 - INFO - Response - Page 2:
2025-07-06 19:32:15,370 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:32:15,885 - INFO - Request Parameters - Page 3:
2025-07-06 19:32:15,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:15,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:16,512 - INFO - Response - Page 3:
2025-07-06 19:32:16,512 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:32:17,026 - INFO - Request Parameters - Page 4:
2025-07-06 19:32:17,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:17,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:17,710 - INFO - Response - Page 4:
2025-07-06 19:32:17,710 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:32:18,210 - INFO - Request Parameters - Page 5:
2025-07-06 19:32:18,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:18,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:18,825 - INFO - Response - Page 5:
2025-07-06 19:32:18,825 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:32:19,340 - INFO - Request Parameters - Page 6:
2025-07-06 19:32:19,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:19,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:20,075 - INFO - Response - Page 6:
2025-07-06 19:32:20,075 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:32:20,575 - INFO - Request Parameters - Page 7:
2025-07-06 19:32:20,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:20,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:21,262 - INFO - Response - Page 7:
2025-07-06 19:32:21,278 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:32:21,794 - INFO - Request Parameters - Page 8:
2025-07-06 19:32:21,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:21,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:22,544 - INFO - Response - Page 8:
2025-07-06 19:32:22,544 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:32:23,060 - INFO - Request Parameters - Page 9:
2025-07-06 19:32:23,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:23,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:23,796 - INFO - Response - Page 9:
2025-07-06 19:32:23,796 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:32:24,311 - INFO - Request Parameters - Page 10:
2025-07-06 19:32:24,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:24,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:24,951 - INFO - Response - Page 10:
2025-07-06 19:32:24,951 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:32:25,452 - INFO - Request Parameters - Page 11:
2025-07-06 19:32:25,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:25,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:26,212 - INFO - Response - Page 11:
2025-07-06 19:32:26,212 - INFO - 第 11 页获取到 50 条记录
2025-07-06 19:32:26,713 - INFO - Request Parameters - Page 12:
2025-07-06 19:32:26,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:26,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:27,204 - INFO - Response - Page 12:
2025-07-06 19:32:27,204 - INFO - 第 12 页获取到 2 条记录
2025-07-06 19:32:27,705 - INFO - 查询完成，共获取到 552 条记录
2025-07-06 19:32:27,705 - INFO - 获取到 552 条表单数据
2025-07-06 19:32:27,717 - INFO - 当前日期 2025-06-15 有 1 条MySQL数据需要处理
2025-07-06 19:32:27,717 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:32:27,717 - INFO - 开始处理日期: 2025-06-17
2025-07-06 19:32:27,717 - INFO - Request Parameters - Page 1:
2025-07-06 19:32:27,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:27,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:28,453 - INFO - Response - Page 1:
2025-07-06 19:32:28,453 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:32:28,969 - INFO - Request Parameters - Page 2:
2025-07-06 19:32:28,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:28,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:29,626 - INFO - Response - Page 2:
2025-07-06 19:32:29,626 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:32:30,142 - INFO - Request Parameters - Page 3:
2025-07-06 19:32:30,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:30,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:30,798 - INFO - Response - Page 3:
2025-07-06 19:32:30,798 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:32:31,299 - INFO - Request Parameters - Page 4:
2025-07-06 19:32:31,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:31,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:31,983 - INFO - Response - Page 4:
2025-07-06 19:32:31,983 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:32:32,486 - INFO - Request Parameters - Page 5:
2025-07-06 19:32:32,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:32,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:33,212 - INFO - Response - Page 5:
2025-07-06 19:32:33,213 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:32:33,714 - INFO - Request Parameters - Page 6:
2025-07-06 19:32:33,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:33,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:34,330 - INFO - Response - Page 6:
2025-07-06 19:32:34,330 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:32:34,830 - INFO - Request Parameters - Page 7:
2025-07-06 19:32:34,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:34,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:35,659 - INFO - Response - Page 7:
2025-07-06 19:32:35,659 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:32:36,159 - INFO - Request Parameters - Page 8:
2025-07-06 19:32:36,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:36,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:36,862 - INFO - Response - Page 8:
2025-07-06 19:32:36,862 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:32:37,364 - INFO - Request Parameters - Page 9:
2025-07-06 19:32:37,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:37,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:38,113 - INFO - Response - Page 9:
2025-07-06 19:32:38,113 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:32:38,613 - INFO - Request Parameters - Page 10:
2025-07-06 19:32:38,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:38,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:39,270 - INFO - Response - Page 10:
2025-07-06 19:32:39,270 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:32:39,785 - INFO - Request Parameters - Page 11:
2025-07-06 19:32:39,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:39,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:40,412 - INFO - Response - Page 11:
2025-07-06 19:32:40,412 - INFO - 第 11 页获取到 38 条记录
2025-07-06 19:32:40,927 - INFO - 查询完成，共获取到 538 条记录
2025-07-06 19:32:40,927 - INFO - 获取到 538 条表单数据
2025-07-06 19:32:40,927 - INFO - 当前日期 2025-06-17 有 2 条MySQL数据需要处理
2025-07-06 19:32:40,927 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:32:40,927 - INFO - 开始处理日期: 2025-06-23
2025-07-06 19:32:40,927 - INFO - Request Parameters - Page 1:
2025-07-06 19:32:40,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:40,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:41,646 - INFO - Response - Page 1:
2025-07-06 19:32:41,646 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:32:42,161 - INFO - Request Parameters - Page 2:
2025-07-06 19:32:42,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:42,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:42,865 - INFO - Response - Page 2:
2025-07-06 19:32:42,865 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:32:43,381 - INFO - Request Parameters - Page 3:
2025-07-06 19:32:43,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:43,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:44,053 - INFO - Response - Page 3:
2025-07-06 19:32:44,053 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:32:44,569 - INFO - Request Parameters - Page 4:
2025-07-06 19:32:44,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:44,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:45,223 - INFO - Response - Page 4:
2025-07-06 19:32:45,223 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:32:45,723 - INFO - Request Parameters - Page 5:
2025-07-06 19:32:45,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:45,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:46,397 - INFO - Response - Page 5:
2025-07-06 19:32:46,397 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:32:46,913 - INFO - Request Parameters - Page 6:
2025-07-06 19:32:46,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:46,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:47,648 - INFO - Response - Page 6:
2025-07-06 19:32:47,648 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:32:48,164 - INFO - Request Parameters - Page 7:
2025-07-06 19:32:48,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:48,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:48,805 - INFO - Response - Page 7:
2025-07-06 19:32:48,805 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:32:49,305 - INFO - Request Parameters - Page 8:
2025-07-06 19:32:49,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:49,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:50,031 - INFO - Response - Page 8:
2025-07-06 19:32:50,031 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:32:50,532 - INFO - Request Parameters - Page 9:
2025-07-06 19:32:50,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:50,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:51,238 - INFO - Response - Page 9:
2025-07-06 19:32:51,238 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:32:51,739 - INFO - Request Parameters - Page 10:
2025-07-06 19:32:51,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:51,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:52,384 - INFO - Response - Page 10:
2025-07-06 19:32:52,384 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:32:52,885 - INFO - Request Parameters - Page 11:
2025-07-06 19:32:52,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:52,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:53,589 - INFO - Response - Page 11:
2025-07-06 19:32:53,589 - INFO - 第 11 页获取到 45 条记录
2025-07-06 19:32:54,089 - INFO - 查询完成，共获取到 545 条记录
2025-07-06 19:32:54,089 - INFO - 获取到 545 条表单数据
2025-07-06 19:32:54,089 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-07-06 19:32:54,089 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:32:54,089 - INFO - 开始处理日期: 2025-06-24
2025-07-06 19:32:54,089 - INFO - Request Parameters - Page 1:
2025-07-06 19:32:54,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:54,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:54,792 - INFO - Response - Page 1:
2025-07-06 19:32:54,792 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:32:55,293 - INFO - Request Parameters - Page 2:
2025-07-06 19:32:55,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:55,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:56,026 - INFO - Response - Page 2:
2025-07-06 19:32:56,026 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:32:56,542 - INFO - Request Parameters - Page 3:
2025-07-06 19:32:56,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:56,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:57,243 - INFO - Response - Page 3:
2025-07-06 19:32:57,243 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:32:57,745 - INFO - Request Parameters - Page 4:
2025-07-06 19:32:57,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:57,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:58,387 - INFO - Response - Page 4:
2025-07-06 19:32:58,387 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:32:58,902 - INFO - Request Parameters - Page 5:
2025-07-06 19:32:58,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:32:58,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:32:59,573 - INFO - Response - Page 5:
2025-07-06 19:32:59,573 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:33:00,074 - INFO - Request Parameters - Page 6:
2025-07-06 19:33:00,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:00,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:00,778 - INFO - Response - Page 6:
2025-07-06 19:33:00,778 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:33:01,278 - INFO - Request Parameters - Page 7:
2025-07-06 19:33:01,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:01,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:01,951 - INFO - Response - Page 7:
2025-07-06 19:33:01,951 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:33:02,466 - INFO - Request Parameters - Page 8:
2025-07-06 19:33:02,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:02,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:03,373 - INFO - Response - Page 8:
2025-07-06 19:33:03,373 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:33:03,889 - INFO - Request Parameters - Page 9:
2025-07-06 19:33:03,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:03,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:04,562 - INFO - Response - Page 9:
2025-07-06 19:33:04,562 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:33:05,077 - INFO - Request Parameters - Page 10:
2025-07-06 19:33:05,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:05,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:05,827 - INFO - Response - Page 10:
2025-07-06 19:33:05,827 - INFO - 第 10 页获取到 50 条记录
2025-07-06 19:33:06,327 - INFO - Request Parameters - Page 11:
2025-07-06 19:33:06,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:06,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:06,781 - INFO - Response - Page 11:
2025-07-06 19:33:06,781 - INFO - 查询完成，共获取到 500 条记录
2025-07-06 19:33:06,781 - INFO - 获取到 500 条表单数据
2025-07-06 19:33:06,796 - INFO - 当前日期 2025-06-24 有 1 条MySQL数据需要处理
2025-07-06 19:33:06,796 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:33:06,796 - INFO - 开始处理日期: 2025-06-27
2025-07-06 19:33:06,796 - INFO - Request Parameters - Page 1:
2025-07-06 19:33:06,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:06,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:07,533 - INFO - Response - Page 1:
2025-07-06 19:33:07,534 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:33:08,035 - INFO - Request Parameters - Page 2:
2025-07-06 19:33:08,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:08,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:08,797 - INFO - Response - Page 2:
2025-07-06 19:33:08,797 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:33:09,297 - INFO - Request Parameters - Page 3:
2025-07-06 19:33:09,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:09,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:09,985 - INFO - Response - Page 3:
2025-07-06 19:33:09,985 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:33:10,485 - INFO - Request Parameters - Page 4:
2025-07-06 19:33:10,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:10,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:11,111 - INFO - Response - Page 4:
2025-07-06 19:33:11,111 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:33:11,626 - INFO - Request Parameters - Page 5:
2025-07-06 19:33:11,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:11,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:12,299 - INFO - Response - Page 5:
2025-07-06 19:33:12,299 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:33:12,799 - INFO - Request Parameters - Page 6:
2025-07-06 19:33:12,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:12,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:13,424 - INFO - Response - Page 6:
2025-07-06 19:33:13,424 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:33:13,940 - INFO - Request Parameters - Page 7:
2025-07-06 19:33:13,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:13,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:14,597 - INFO - Response - Page 7:
2025-07-06 19:33:14,597 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:33:15,112 - INFO - Request Parameters - Page 8:
2025-07-06 19:33:15,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:15,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:15,847 - INFO - Response - Page 8:
2025-07-06 19:33:15,847 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:33:16,362 - INFO - Request Parameters - Page 9:
2025-07-06 19:33:16,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:16,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:17,048 - INFO - Response - Page 9:
2025-07-06 19:33:17,048 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:33:17,551 - INFO - Request Parameters - Page 10:
2025-07-06 19:33:17,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:17,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:18,133 - INFO - Response - Page 10:
2025-07-06 19:33:18,133 - INFO - 第 10 页获取到 28 条记录
2025-07-06 19:33:18,634 - INFO - 查询完成，共获取到 478 条记录
2025-07-06 19:33:18,634 - INFO - 获取到 478 条表单数据
2025-07-06 19:33:18,643 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-07-06 19:33:18,643 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:33:18,644 - INFO - 开始处理日期: 2025-07-04
2025-07-06 19:33:18,644 - INFO - Request Parameters - Page 1:
2025-07-06 19:33:18,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:18,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:19,317 - INFO - Response - Page 1:
2025-07-06 19:33:19,317 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:33:19,833 - INFO - Request Parameters - Page 2:
2025-07-06 19:33:19,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:19,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:20,506 - INFO - Response - Page 2:
2025-07-06 19:33:20,506 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:33:21,021 - INFO - Request Parameters - Page 3:
2025-07-06 19:33:21,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:21,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:21,661 - INFO - Response - Page 3:
2025-07-06 19:33:21,661 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:33:22,177 - INFO - Request Parameters - Page 4:
2025-07-06 19:33:22,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:22,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:22,889 - INFO - Response - Page 4:
2025-07-06 19:33:22,890 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:33:23,396 - INFO - Request Parameters - Page 5:
2025-07-06 19:33:23,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:23,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:24,022 - INFO - Response - Page 5:
2025-07-06 19:33:24,022 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:33:24,522 - INFO - Request Parameters - Page 6:
2025-07-06 19:33:24,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:24,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:25,178 - INFO - Response - Page 6:
2025-07-06 19:33:25,178 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:33:25,680 - INFO - Request Parameters - Page 7:
2025-07-06 19:33:25,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:25,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:26,288 - INFO - Response - Page 7:
2025-07-06 19:33:26,288 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:33:26,804 - INFO - Request Parameters - Page 8:
2025-07-06 19:33:26,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:26,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:27,461 - INFO - Response - Page 8:
2025-07-06 19:33:27,461 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:33:27,976 - INFO - Request Parameters - Page 9:
2025-07-06 19:33:27,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:27,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:28,633 - INFO - Response - Page 9:
2025-07-06 19:33:28,633 - INFO - 第 9 页获取到 42 条记录
2025-07-06 19:33:29,134 - INFO - 查询完成，共获取到 442 条记录
2025-07-06 19:33:29,134 - INFO - 获取到 442 条表单数据
2025-07-06 19:33:29,134 - INFO - 当前日期 2025-07-04 有 4 条MySQL数据需要处理
2025-07-06 19:33:29,134 - INFO - 开始批量插入 1 条新记录
2025-07-06 19:33:29,289 - INFO - 批量插入响应状态码: 200
2025-07-06 19:33:29,289 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 11:33:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '563A6037-2FB9-760B-9B6F-F1E94DC7A495', 'x-acs-trace-id': '7445a2574548143038f1dff0ed738688', 'etag': '6h90pWtuXsgS+zVhXSF6Dig0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 19:33:29,289 - INFO - 批量插入响应体: {'result': ['FINST-2K666OB1D2WWWCWG9LP2W5D2VBLN3RC4GLRCMYD']}
2025-07-06 19:33:29,289 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-06 19:33:29,289 - INFO - 成功插入的数据ID: ['FINST-2K666OB1D2WWWCWG9LP2W5D2VBLN3RC4GLRCMYD']
2025-07-06 19:33:34,307 - INFO - 批量插入完成，共 1 条记录
2025-07-06 19:33:34,307 - INFO - 日期 2025-07-04 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-06 19:33:34,307 - INFO - 开始处理日期: 2025-07-05
2025-07-06 19:33:34,307 - INFO - Request Parameters - Page 1:
2025-07-06 19:33:34,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:34,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:34,995 - INFO - Response - Page 1:
2025-07-06 19:33:34,995 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:33:35,495 - INFO - Request Parameters - Page 2:
2025-07-06 19:33:35,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:35,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:36,183 - INFO - Response - Page 2:
2025-07-06 19:33:36,183 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:33:36,684 - INFO - Request Parameters - Page 3:
2025-07-06 19:33:36,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:36,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:37,402 - INFO - Response - Page 3:
2025-07-06 19:33:37,402 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:33:37,903 - INFO - Request Parameters - Page 4:
2025-07-06 19:33:37,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:37,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:38,607 - INFO - Response - Page 4:
2025-07-06 19:33:38,607 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:33:39,121 - INFO - Request Parameters - Page 5:
2025-07-06 19:33:39,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:39,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:40,122 - INFO - Response - Page 5:
2025-07-06 19:33:40,122 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:33:40,638 - INFO - Request Parameters - Page 6:
2025-07-06 19:33:40,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:40,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:41,294 - INFO - Response - Page 6:
2025-07-06 19:33:41,294 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:33:41,810 - INFO - Request Parameters - Page 7:
2025-07-06 19:33:41,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:41,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:42,521 - INFO - Response - Page 7:
2025-07-06 19:33:42,521 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:33:43,022 - INFO - Request Parameters - Page 8:
2025-07-06 19:33:43,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:43,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:43,718 - INFO - Response - Page 8:
2025-07-06 19:33:43,718 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:33:44,233 - INFO - Request Parameters - Page 9:
2025-07-06 19:33:44,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:44,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:44,889 - INFO - Response - Page 9:
2025-07-06 19:33:44,889 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:33:45,390 - INFO - Request Parameters - Page 10:
2025-07-06 19:33:45,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:45,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:45,921 - INFO - Response - Page 10:
2025-07-06 19:33:45,921 - INFO - 第 10 页获取到 16 条记录
2025-07-06 19:33:46,437 - INFO - 查询完成，共获取到 466 条记录
2025-07-06 19:33:46,437 - INFO - 获取到 466 条表单数据
2025-07-06 19:33:46,437 - INFO - 当前日期 2025-07-05 有 136 条MySQL数据需要处理
2025-07-06 19:33:46,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91X2WWOM1FD5YGFCF1QOWW3I021FRCMX4
2025-07-06 19:33:47,016 - INFO - 更新表单数据成功: FINST-VOC66Y91X2WWOM1FD5YGFCF1QOWW3I021FRCMX4
2025-07-06 19:33:47,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11238.18, 'new_value': 11750.46}, {'field': 'total_amount', 'old_value': 11238.18, 'new_value': 11750.46}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9526e9830d5b4a4598080d10bc043e73.jpg?Expires=2066720553&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=bP5DzwMoMkjT8E6ZIJK7qXNOSZ0%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/3719236880f74ff3b0752feb21a7f247.jpg?Expires=2066720548&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=9rrwfpWUF%2FC9vsPnrUduvQGZtd0%3D'}]
2025-07-06 19:33:47,016 - INFO - 日期 2025-07-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-06 19:33:47,016 - INFO - 开始处理日期: 2025-07-06
2025-07-06 19:33:47,016 - INFO - Request Parameters - Page 1:
2025-07-06 19:33:47,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:33:47,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:33:47,453 - INFO - Response - Page 1:
2025-07-06 19:33:47,453 - INFO - 第 1 页获取到 1 条记录
2025-07-06 19:33:47,953 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 19:33:47,953 - INFO - 获取到 1 条表单数据
2025-07-06 19:33:47,953 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 19:33:47,953 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:33:47,953 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 1 条
2025-07-06 19:34:47,978 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 19:34:47,978 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 19:34:47,978 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 19:34:48,119 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 477 条记录
2025-07-06 19:34:48,119 - INFO - 获取到 2 个日期需要处理: ['2025-07-05', '2025-07-06']
2025-07-06 19:34:48,134 - INFO - 开始处理日期: 2025-07-05
2025-07-06 19:34:48,134 - INFO - Request Parameters - Page 1:
2025-07-06 19:34:48,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:48,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:48,853 - INFO - Response - Page 1:
2025-07-06 19:34:48,853 - INFO - 第 1 页获取到 50 条记录
2025-07-06 19:34:49,354 - INFO - Request Parameters - Page 2:
2025-07-06 19:34:49,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:49,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:50,026 - INFO - Response - Page 2:
2025-07-06 19:34:50,026 - INFO - 第 2 页获取到 50 条记录
2025-07-06 19:34:50,527 - INFO - Request Parameters - Page 3:
2025-07-06 19:34:50,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:50,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:51,229 - INFO - Response - Page 3:
2025-07-06 19:34:51,229 - INFO - 第 3 页获取到 50 条记录
2025-07-06 19:34:51,729 - INFO - Request Parameters - Page 4:
2025-07-06 19:34:51,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:51,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:52,401 - INFO - Response - Page 4:
2025-07-06 19:34:52,401 - INFO - 第 4 页获取到 50 条记录
2025-07-06 19:34:52,902 - INFO - Request Parameters - Page 5:
2025-07-06 19:34:52,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:52,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:53,871 - INFO - Response - Page 5:
2025-07-06 19:34:53,871 - INFO - 第 5 页获取到 50 条记录
2025-07-06 19:34:54,387 - INFO - Request Parameters - Page 6:
2025-07-06 19:34:54,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:54,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:55,090 - INFO - Response - Page 6:
2025-07-06 19:34:55,090 - INFO - 第 6 页获取到 50 条记录
2025-07-06 19:34:55,590 - INFO - Request Parameters - Page 7:
2025-07-06 19:34:55,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:55,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:56,230 - INFO - Response - Page 7:
2025-07-06 19:34:56,230 - INFO - 第 7 页获取到 50 条记录
2025-07-06 19:34:56,731 - INFO - Request Parameters - Page 8:
2025-07-06 19:34:56,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:56,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:57,389 - INFO - Response - Page 8:
2025-07-06 19:34:57,389 - INFO - 第 8 页获取到 50 条记录
2025-07-06 19:34:57,904 - INFO - Request Parameters - Page 9:
2025-07-06 19:34:57,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:57,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:58,591 - INFO - Response - Page 9:
2025-07-06 19:34:58,591 - INFO - 第 9 页获取到 50 条记录
2025-07-06 19:34:59,093 - INFO - Request Parameters - Page 10:
2025-07-06 19:34:59,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:34:59,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:34:59,655 - INFO - Response - Page 10:
2025-07-06 19:34:59,655 - INFO - 第 10 页获取到 16 条记录
2025-07-06 19:35:00,170 - INFO - 查询完成，共获取到 466 条记录
2025-07-06 19:35:00,170 - INFO - 获取到 466 条表单数据
2025-07-06 19:35:00,170 - INFO - 当前日期 2025-07-05 有 466 条MySQL数据需要处理
2025-07-06 19:35:00,186 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:35:00,186 - INFO - 开始处理日期: 2025-07-06
2025-07-06 19:35:00,186 - INFO - Request Parameters - Page 1:
2025-07-06 19:35:00,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 19:35:00,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 19:35:00,733 - INFO - Response - Page 1:
2025-07-06 19:35:00,733 - INFO - 第 1 页获取到 1 条记录
2025-07-06 19:35:01,233 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 19:35:01,233 - INFO - 获取到 1 条表单数据
2025-07-06 19:35:01,233 - INFO - 当前日期 2025-07-06 有 1 条MySQL数据需要处理
2025-07-06 19:35:01,233 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:35:01,233 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 19:35:01,233 - INFO - 同步完成
2025-07-06 22:30:34,538 - INFO - 使用默认增量同步（当天更新数据）
2025-07-06 22:30:34,538 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-06 22:30:34,538 - INFO - 查询参数: ('2025-07-06',)
2025-07-06 22:30:34,678 - INFO - MySQL查询成功，增量数据（日期: 2025-07-06），共获取 206 条记录
2025-07-06 22:30:34,678 - INFO - 获取到 15 个日期需要处理: ['2025-06-01', '2025-06-02', '2025-06-03', '2025-06-04', '2025-06-06', '2025-06-07', '2025-06-14', '2025-06-15', '2025-06-17', '2025-06-23', '2025-06-24', '2025-06-27', '2025-07-04', '2025-07-05', '2025-07-06']
2025-07-06 22:30:34,678 - INFO - 开始处理日期: 2025-06-01
2025-07-06 22:30:34,694 - INFO - Request Parameters - Page 1:
2025-07-06 22:30:34,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:34,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:42,807 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-4415-7FDF-BC8B-5FDC00D87B68 Response: {'code': 'ServiceUnavailable', 'requestid': '********-4415-7FDF-BC8B-5FDC00D87B68', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-4415-7FDF-BC8B-5FDC00D87B68)
2025-07-06 22:30:42,807 - INFO - 开始处理日期: 2025-06-02
2025-07-06 22:30:42,807 - INFO - Request Parameters - Page 1:
2025-07-06 22:30:42,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:42,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:43,541 - INFO - Response - Page 1:
2025-07-06 22:30:43,541 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:30:44,041 - INFO - Request Parameters - Page 2:
2025-07-06 22:30:44,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:44,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:50,794 - INFO - Response - Page 2:
2025-07-06 22:30:50,794 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:30:51,310 - INFO - Request Parameters - Page 3:
2025-07-06 22:30:51,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:51,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:52,045 - INFO - Response - Page 3:
2025-07-06 22:30:52,045 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:30:52,545 - INFO - Request Parameters - Page 4:
2025-07-06 22:30:52,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:52,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:53,248 - INFO - Response - Page 4:
2025-07-06 22:30:53,248 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:30:53,764 - INFO - Request Parameters - Page 5:
2025-07-06 22:30:53,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:53,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:54,514 - INFO - Response - Page 5:
2025-07-06 22:30:54,514 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:30:55,015 - INFO - Request Parameters - Page 6:
2025-07-06 22:30:55,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:55,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:55,655 - INFO - Response - Page 6:
2025-07-06 22:30:55,655 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:30:56,156 - INFO - Request Parameters - Page 7:
2025-07-06 22:30:56,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:56,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:56,828 - INFO - Response - Page 7:
2025-07-06 22:30:56,828 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:30:57,328 - INFO - Request Parameters - Page 8:
2025-07-06 22:30:57,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:57,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:57,984 - INFO - Response - Page 8:
2025-07-06 22:30:57,984 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:30:58,500 - INFO - Request Parameters - Page 9:
2025-07-06 22:30:58,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:58,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:30:59,172 - INFO - Response - Page 9:
2025-07-06 22:30:59,172 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:30:59,673 - INFO - Request Parameters - Page 10:
2025-07-06 22:30:59,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:30:59,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:00,360 - INFO - Response - Page 10:
2025-07-06 22:31:00,360 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:31:00,876 - INFO - Request Parameters - Page 11:
2025-07-06 22:31:00,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:00,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:01,517 - INFO - Response - Page 11:
2025-07-06 22:31:01,517 - INFO - 第 11 页获取到 50 条记录
2025-07-06 22:31:02,033 - INFO - Request Parameters - Page 12:
2025-07-06 22:31:02,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:02,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:02,611 - INFO - Response - Page 12:
2025-07-06 22:31:02,611 - INFO - 第 12 页获取到 14 条记录
2025-07-06 22:31:03,127 - INFO - 查询完成，共获取到 564 条记录
2025-07-06 22:31:03,127 - INFO - 获取到 564 条表单数据
2025-07-06 22:31:03,127 - INFO - 当前日期 2025-06-02 有 2 条MySQL数据需要处理
2025-07-06 22:31:03,127 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:31:03,127 - INFO - 开始处理日期: 2025-06-03
2025-07-06 22:31:03,127 - INFO - Request Parameters - Page 1:
2025-07-06 22:31:03,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:03,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:03,831 - INFO - Response - Page 1:
2025-07-06 22:31:03,831 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:31:04,346 - INFO - Request Parameters - Page 2:
2025-07-06 22:31:04,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:04,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:05,050 - INFO - Response - Page 2:
2025-07-06 22:31:05,050 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:31:05,550 - INFO - Request Parameters - Page 3:
2025-07-06 22:31:05,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:05,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:06,238 - INFO - Response - Page 3:
2025-07-06 22:31:06,238 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:31:06,754 - INFO - Request Parameters - Page 4:
2025-07-06 22:31:06,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:06,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:07,426 - INFO - Response - Page 4:
2025-07-06 22:31:07,426 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:31:07,942 - INFO - Request Parameters - Page 5:
2025-07-06 22:31:07,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:07,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:08,645 - INFO - Response - Page 5:
2025-07-06 22:31:08,645 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:31:09,145 - INFO - Request Parameters - Page 6:
2025-07-06 22:31:09,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:09,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:09,833 - INFO - Response - Page 6:
2025-07-06 22:31:09,833 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:31:10,333 - INFO - Request Parameters - Page 7:
2025-07-06 22:31:10,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:10,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:10,943 - INFO - Response - Page 7:
2025-07-06 22:31:10,943 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:31:11,443 - INFO - Request Parameters - Page 8:
2025-07-06 22:31:11,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:11,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:12,131 - INFO - Response - Page 8:
2025-07-06 22:31:12,131 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:31:12,647 - INFO - Request Parameters - Page 9:
2025-07-06 22:31:12,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:12,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:13,366 - INFO - Response - Page 9:
2025-07-06 22:31:13,366 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:31:13,881 - INFO - Request Parameters - Page 10:
2025-07-06 22:31:13,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:13,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:14,616 - INFO - Response - Page 10:
2025-07-06 22:31:14,616 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:31:15,132 - INFO - Request Parameters - Page 11:
2025-07-06 22:31:15,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:15,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:15,835 - INFO - Response - Page 11:
2025-07-06 22:31:15,835 - INFO - 第 11 页获取到 50 条记录
2025-07-06 22:31:16,335 - INFO - Request Parameters - Page 12:
2025-07-06 22:31:16,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:16,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748880000000, 1748966399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:16,914 - INFO - Response - Page 12:
2025-07-06 22:31:16,914 - INFO - 第 12 页获取到 12 条记录
2025-07-06 22:31:17,430 - INFO - 查询完成，共获取到 562 条记录
2025-07-06 22:31:17,430 - INFO - 获取到 562 条表单数据
2025-07-06 22:31:17,430 - INFO - 当前日期 2025-06-03 有 1 条MySQL数据需要处理
2025-07-06 22:31:17,430 - INFO - 日期 2025-06-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:31:17,430 - INFO - 开始处理日期: 2025-06-04
2025-07-06 22:31:17,430 - INFO - Request Parameters - Page 1:
2025-07-06 22:31:17,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:17,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:18,117 - INFO - Response - Page 1:
2025-07-06 22:31:18,117 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:31:18,633 - INFO - Request Parameters - Page 2:
2025-07-06 22:31:18,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:18,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:19,430 - INFO - Response - Page 2:
2025-07-06 22:31:19,430 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:31:19,946 - INFO - Request Parameters - Page 3:
2025-07-06 22:31:19,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:19,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:20,618 - INFO - Response - Page 3:
2025-07-06 22:31:20,618 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:31:21,119 - INFO - Request Parameters - Page 4:
2025-07-06 22:31:21,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:21,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:21,775 - INFO - Response - Page 4:
2025-07-06 22:31:21,775 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:31:22,275 - INFO - Request Parameters - Page 5:
2025-07-06 22:31:22,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:22,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:23,010 - INFO - Response - Page 5:
2025-07-06 22:31:23,010 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:31:23,510 - INFO - Request Parameters - Page 6:
2025-07-06 22:31:23,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:23,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:24,182 - INFO - Response - Page 6:
2025-07-06 22:31:24,182 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:31:24,698 - INFO - Request Parameters - Page 7:
2025-07-06 22:31:24,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:24,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:25,323 - INFO - Response - Page 7:
2025-07-06 22:31:25,323 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:31:25,839 - INFO - Request Parameters - Page 8:
2025-07-06 22:31:25,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:25,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:26,527 - INFO - Response - Page 8:
2025-07-06 22:31:26,527 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:31:27,027 - INFO - Request Parameters - Page 9:
2025-07-06 22:31:27,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:27,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:27,715 - INFO - Response - Page 9:
2025-07-06 22:31:27,715 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:31:28,231 - INFO - Request Parameters - Page 10:
2025-07-06 22:31:28,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:28,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:28,919 - INFO - Response - Page 10:
2025-07-06 22:31:28,919 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:31:29,434 - INFO - Request Parameters - Page 11:
2025-07-06 22:31:29,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:29,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:30,122 - INFO - Response - Page 11:
2025-07-06 22:31:30,122 - INFO - 第 11 页获取到 50 条记录
2025-07-06 22:31:30,638 - INFO - Request Parameters - Page 12:
2025-07-06 22:31:30,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:30,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748966400000, 1749052799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:31,169 - INFO - Response - Page 12:
2025-07-06 22:31:31,169 - INFO - 第 12 页获取到 13 条记录
2025-07-06 22:31:31,685 - INFO - 查询完成，共获取到 563 条记录
2025-07-06 22:31:31,685 - INFO - 获取到 563 条表单数据
2025-07-06 22:31:31,685 - INFO - 当前日期 2025-06-04 有 2 条MySQL数据需要处理
2025-07-06 22:31:31,685 - INFO - 日期 2025-06-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:31:31,685 - INFO - 开始处理日期: 2025-06-06
2025-07-06 22:31:31,685 - INFO - Request Parameters - Page 1:
2025-07-06 22:31:31,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:31,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:32,404 - INFO - Response - Page 1:
2025-07-06 22:31:32,404 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:31:32,920 - INFO - Request Parameters - Page 2:
2025-07-06 22:31:32,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:32,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:33,577 - INFO - Response - Page 2:
2025-07-06 22:31:33,577 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:31:34,093 - INFO - Request Parameters - Page 3:
2025-07-06 22:31:34,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:34,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:34,718 - INFO - Response - Page 3:
2025-07-06 22:31:34,718 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:31:35,218 - INFO - Request Parameters - Page 4:
2025-07-06 22:31:35,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:35,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:35,859 - INFO - Response - Page 4:
2025-07-06 22:31:35,859 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:31:36,359 - INFO - Request Parameters - Page 5:
2025-07-06 22:31:36,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:36,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:37,016 - INFO - Response - Page 5:
2025-07-06 22:31:37,016 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:31:37,531 - INFO - Request Parameters - Page 6:
2025-07-06 22:31:37,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:37,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:38,125 - INFO - Response - Page 6:
2025-07-06 22:31:38,125 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:31:38,626 - INFO - Request Parameters - Page 7:
2025-07-06 22:31:38,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:38,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:39,313 - INFO - Response - Page 7:
2025-07-06 22:31:39,313 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:31:39,814 - INFO - Request Parameters - Page 8:
2025-07-06 22:31:39,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:39,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:40,470 - INFO - Response - Page 8:
2025-07-06 22:31:40,470 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:31:40,986 - INFO - Request Parameters - Page 9:
2025-07-06 22:31:40,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:40,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:41,627 - INFO - Response - Page 9:
2025-07-06 22:31:41,627 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:31:42,143 - INFO - Request Parameters - Page 10:
2025-07-06 22:31:42,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:42,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:42,768 - INFO - Response - Page 10:
2025-07-06 22:31:42,768 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:31:43,268 - INFO - Request Parameters - Page 11:
2025-07-06 22:31:43,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:43,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:43,956 - INFO - Response - Page 11:
2025-07-06 22:31:43,956 - INFO - 第 11 页获取到 50 条记录
2025-07-06 22:31:44,472 - INFO - Request Parameters - Page 12:
2025-07-06 22:31:44,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:44,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749139200000, 1749225599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:45,034 - INFO - Response - Page 12:
2025-07-06 22:31:45,034 - INFO - 第 12 页获取到 16 条记录
2025-07-06 22:31:45,550 - INFO - 查询完成，共获取到 566 条记录
2025-07-06 22:31:45,550 - INFO - 获取到 566 条表单数据
2025-07-06 22:31:45,550 - INFO - 当前日期 2025-06-06 有 1 条MySQL数据需要处理
2025-07-06 22:31:45,550 - INFO - 日期 2025-06-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:31:45,550 - INFO - 开始处理日期: 2025-06-07
2025-07-06 22:31:45,550 - INFO - Request Parameters - Page 1:
2025-07-06 22:31:45,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:45,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:46,238 - INFO - Response - Page 1:
2025-07-06 22:31:46,238 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:31:46,754 - INFO - Request Parameters - Page 2:
2025-07-06 22:31:46,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:46,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:47,395 - INFO - Response - Page 2:
2025-07-06 22:31:47,395 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:31:47,911 - INFO - Request Parameters - Page 3:
2025-07-06 22:31:47,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:47,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:48,645 - INFO - Response - Page 3:
2025-07-06 22:31:48,645 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:31:49,145 - INFO - Request Parameters - Page 4:
2025-07-06 22:31:49,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:49,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:49,818 - INFO - Response - Page 4:
2025-07-06 22:31:49,818 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:31:50,333 - INFO - Request Parameters - Page 5:
2025-07-06 22:31:50,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:50,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:50,974 - INFO - Response - Page 5:
2025-07-06 22:31:50,974 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:31:51,474 - INFO - Request Parameters - Page 6:
2025-07-06 22:31:51,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:51,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:52,147 - INFO - Response - Page 6:
2025-07-06 22:31:52,162 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:31:52,678 - INFO - Request Parameters - Page 7:
2025-07-06 22:31:52,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:52,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:53,319 - INFO - Response - Page 7:
2025-07-06 22:31:53,319 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:31:53,819 - INFO - Request Parameters - Page 8:
2025-07-06 22:31:53,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:53,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:54,522 - INFO - Response - Page 8:
2025-07-06 22:31:54,522 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:31:55,023 - INFO - Request Parameters - Page 9:
2025-07-06 22:31:55,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:55,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:55,726 - INFO - Response - Page 9:
2025-07-06 22:31:55,726 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:31:56,258 - INFO - Request Parameters - Page 10:
2025-07-06 22:31:56,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:56,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:56,883 - INFO - Response - Page 10:
2025-07-06 22:31:56,883 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:31:57,399 - INFO - Request Parameters - Page 11:
2025-07-06 22:31:57,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:57,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:58,071 - INFO - Response - Page 11:
2025-07-06 22:31:58,071 - INFO - 第 11 页获取到 50 条记录
2025-07-06 22:31:58,571 - INFO - Request Parameters - Page 12:
2025-07-06 22:31:58,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:58,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:31:59,243 - INFO - Response - Page 12:
2025-07-06 22:31:59,243 - INFO - 第 12 页获取到 50 条记录
2025-07-06 22:31:59,743 - INFO - Request Parameters - Page 13:
2025-07-06 22:31:59,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:31:59,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:00,306 - INFO - Response - Page 13:
2025-07-06 22:32:00,306 - INFO - 第 13 页获取到 10 条记录
2025-07-06 22:32:00,806 - INFO - 查询完成，共获取到 610 条记录
2025-07-06 22:32:00,806 - INFO - 获取到 610 条表单数据
2025-07-06 22:32:00,806 - INFO - 当前日期 2025-06-07 有 1 条MySQL数据需要处理
2025-07-06 22:32:00,806 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:32:00,806 - INFO - 开始处理日期: 2025-06-14
2025-07-06 22:32:00,806 - INFO - Request Parameters - Page 1:
2025-07-06 22:32:00,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:00,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:01,541 - INFO - Response - Page 1:
2025-07-06 22:32:01,541 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:32:02,041 - INFO - Request Parameters - Page 2:
2025-07-06 22:32:02,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:02,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:02,776 - INFO - Response - Page 2:
2025-07-06 22:32:02,776 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:32:03,276 - INFO - Request Parameters - Page 3:
2025-07-06 22:32:03,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:03,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:03,948 - INFO - Response - Page 3:
2025-07-06 22:32:03,948 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:32:04,464 - INFO - Request Parameters - Page 4:
2025-07-06 22:32:04,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:04,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:05,199 - INFO - Response - Page 4:
2025-07-06 22:32:05,199 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:32:05,699 - INFO - Request Parameters - Page 5:
2025-07-06 22:32:05,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:05,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:06,387 - INFO - Response - Page 5:
2025-07-06 22:32:06,387 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:32:06,902 - INFO - Request Parameters - Page 6:
2025-07-06 22:32:06,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:06,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:07,575 - INFO - Response - Page 6:
2025-07-06 22:32:07,575 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:32:08,075 - INFO - Request Parameters - Page 7:
2025-07-06 22:32:08,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:08,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:08,856 - INFO - Response - Page 7:
2025-07-06 22:32:08,856 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:32:09,372 - INFO - Request Parameters - Page 8:
2025-07-06 22:32:09,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:09,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:10,076 - INFO - Response - Page 8:
2025-07-06 22:32:10,076 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:32:10,576 - INFO - Request Parameters - Page 9:
2025-07-06 22:32:10,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:10,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:11,248 - INFO - Response - Page 9:
2025-07-06 22:32:11,248 - INFO - 第 9 页获取到 41 条记录
2025-07-06 22:32:11,764 - INFO - 查询完成，共获取到 441 条记录
2025-07-06 22:32:11,764 - INFO - 获取到 441 条表单数据
2025-07-06 22:32:11,764 - INFO - 当前日期 2025-06-14 有 1 条MySQL数据需要处理
2025-07-06 22:32:11,764 - INFO - 日期 2025-06-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:32:11,764 - INFO - 开始处理日期: 2025-06-15
2025-07-06 22:32:11,764 - INFO - Request Parameters - Page 1:
2025-07-06 22:32:11,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:11,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:12,420 - INFO - Response - Page 1:
2025-07-06 22:32:12,420 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:32:12,936 - INFO - Request Parameters - Page 2:
2025-07-06 22:32:12,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:12,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:13,593 - INFO - Response - Page 2:
2025-07-06 22:32:13,593 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:32:14,108 - INFO - Request Parameters - Page 3:
2025-07-06 22:32:14,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:14,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:14,765 - INFO - Response - Page 3:
2025-07-06 22:32:14,765 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:32:15,265 - INFO - Request Parameters - Page 4:
2025-07-06 22:32:15,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:15,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:15,922 - INFO - Response - Page 4:
2025-07-06 22:32:15,922 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:32:16,437 - INFO - Request Parameters - Page 5:
2025-07-06 22:32:16,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:16,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:17,156 - INFO - Response - Page 5:
2025-07-06 22:32:17,156 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:32:17,672 - INFO - Request Parameters - Page 6:
2025-07-06 22:32:17,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:17,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:18,329 - INFO - Response - Page 6:
2025-07-06 22:32:18,329 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:32:18,845 - INFO - Request Parameters - Page 7:
2025-07-06 22:32:18,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:18,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:19,548 - INFO - Response - Page 7:
2025-07-06 22:32:19,548 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:32:20,064 - INFO - Request Parameters - Page 8:
2025-07-06 22:32:20,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:20,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:20,845 - INFO - Response - Page 8:
2025-07-06 22:32:20,845 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:32:21,361 - INFO - Request Parameters - Page 9:
2025-07-06 22:32:21,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:21,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:22,049 - INFO - Response - Page 9:
2025-07-06 22:32:22,049 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:32:22,565 - INFO - Request Parameters - Page 10:
2025-07-06 22:32:22,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:22,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:23,190 - INFO - Response - Page 10:
2025-07-06 22:32:23,190 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:32:23,706 - INFO - Request Parameters - Page 11:
2025-07-06 22:32:23,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:23,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:24,362 - INFO - Response - Page 11:
2025-07-06 22:32:24,362 - INFO - 第 11 页获取到 50 条记录
2025-07-06 22:32:24,863 - INFO - Request Parameters - Page 12:
2025-07-06 22:32:24,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:24,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749916800000, 1750003199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:25,316 - INFO - Response - Page 12:
2025-07-06 22:32:25,316 - INFO - 第 12 页获取到 2 条记录
2025-07-06 22:32:25,832 - INFO - 查询完成，共获取到 552 条记录
2025-07-06 22:32:25,832 - INFO - 获取到 552 条表单数据
2025-07-06 22:32:25,832 - INFO - 当前日期 2025-06-15 有 1 条MySQL数据需要处理
2025-07-06 22:32:25,832 - INFO - 日期 2025-06-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:32:25,832 - INFO - 开始处理日期: 2025-06-17
2025-07-06 22:32:25,832 - INFO - Request Parameters - Page 1:
2025-07-06 22:32:25,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:25,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:26,582 - INFO - Response - Page 1:
2025-07-06 22:32:26,582 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:32:27,098 - INFO - Request Parameters - Page 2:
2025-07-06 22:32:27,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:27,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:27,801 - INFO - Response - Page 2:
2025-07-06 22:32:27,801 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:32:28,317 - INFO - Request Parameters - Page 3:
2025-07-06 22:32:28,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:28,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:29,021 - INFO - Response - Page 3:
2025-07-06 22:32:29,021 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:32:29,536 - INFO - Request Parameters - Page 4:
2025-07-06 22:32:29,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:29,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:30,271 - INFO - Response - Page 4:
2025-07-06 22:32:30,271 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:32:30,787 - INFO - Request Parameters - Page 5:
2025-07-06 22:32:30,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:30,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:31,459 - INFO - Response - Page 5:
2025-07-06 22:32:31,459 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:32:31,959 - INFO - Request Parameters - Page 6:
2025-07-06 22:32:31,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:31,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:32,647 - INFO - Response - Page 6:
2025-07-06 22:32:32,663 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:32:33,163 - INFO - Request Parameters - Page 7:
2025-07-06 22:32:33,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:33,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:33,804 - INFO - Response - Page 7:
2025-07-06 22:32:33,804 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:32:34,304 - INFO - Request Parameters - Page 8:
2025-07-06 22:32:34,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:34,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:35,039 - INFO - Response - Page 8:
2025-07-06 22:32:35,039 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:32:35,539 - INFO - Request Parameters - Page 9:
2025-07-06 22:32:35,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:35,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:36,227 - INFO - Response - Page 9:
2025-07-06 22:32:36,227 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:32:36,727 - INFO - Request Parameters - Page 10:
2025-07-06 22:32:36,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:36,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:37,352 - INFO - Response - Page 10:
2025-07-06 22:32:37,352 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:32:37,852 - INFO - Request Parameters - Page 11:
2025-07-06 22:32:37,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:37,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:38,540 - INFO - Response - Page 11:
2025-07-06 22:32:38,540 - INFO - 第 11 页获取到 38 条记录
2025-07-06 22:32:39,056 - INFO - 查询完成，共获取到 538 条记录
2025-07-06 22:32:39,056 - INFO - 获取到 538 条表单数据
2025-07-06 22:32:39,056 - INFO - 当前日期 2025-06-17 有 2 条MySQL数据需要处理
2025-07-06 22:32:39,056 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:32:39,056 - INFO - 开始处理日期: 2025-06-23
2025-07-06 22:32:39,056 - INFO - Request Parameters - Page 1:
2025-07-06 22:32:39,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:39,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:39,712 - INFO - Response - Page 1:
2025-07-06 22:32:39,712 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:32:40,228 - INFO - Request Parameters - Page 2:
2025-07-06 22:32:40,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:40,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:40,932 - INFO - Response - Page 2:
2025-07-06 22:32:40,932 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:32:41,447 - INFO - Request Parameters - Page 3:
2025-07-06 22:32:41,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:41,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:42,120 - INFO - Response - Page 3:
2025-07-06 22:32:42,120 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:32:42,635 - INFO - Request Parameters - Page 4:
2025-07-06 22:32:42,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:42,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:43,339 - INFO - Response - Page 4:
2025-07-06 22:32:43,339 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:32:43,839 - INFO - Request Parameters - Page 5:
2025-07-06 22:32:43,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:43,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:44,495 - INFO - Response - Page 5:
2025-07-06 22:32:44,495 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:32:44,996 - INFO - Request Parameters - Page 6:
2025-07-06 22:32:44,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:44,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:45,668 - INFO - Response - Page 6:
2025-07-06 22:32:45,668 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:32:46,168 - INFO - Request Parameters - Page 7:
2025-07-06 22:32:46,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:46,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:46,809 - INFO - Response - Page 7:
2025-07-06 22:32:46,809 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:32:47,309 - INFO - Request Parameters - Page 8:
2025-07-06 22:32:47,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:47,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:47,966 - INFO - Response - Page 8:
2025-07-06 22:32:47,966 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:32:48,481 - INFO - Request Parameters - Page 9:
2025-07-06 22:32:48,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:48,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:49,154 - INFO - Response - Page 9:
2025-07-06 22:32:49,154 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:32:49,669 - INFO - Request Parameters - Page 10:
2025-07-06 22:32:49,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:49,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:50,341 - INFO - Response - Page 10:
2025-07-06 22:32:50,341 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:32:50,842 - INFO - Request Parameters - Page 11:
2025-07-06 22:32:50,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:50,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:51,483 - INFO - Response - Page 11:
2025-07-06 22:32:51,483 - INFO - 第 11 页获取到 45 条记录
2025-07-06 22:32:51,998 - INFO - 查询完成，共获取到 545 条记录
2025-07-06 22:32:51,998 - INFO - 获取到 545 条表单数据
2025-07-06 22:32:51,998 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-07-06 22:32:51,998 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:32:51,998 - INFO - 开始处理日期: 2025-06-24
2025-07-06 22:32:51,998 - INFO - Request Parameters - Page 1:
2025-07-06 22:32:51,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:51,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:52,639 - INFO - Response - Page 1:
2025-07-06 22:32:52,639 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:32:53,140 - INFO - Request Parameters - Page 2:
2025-07-06 22:32:53,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:53,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:53,859 - INFO - Response - Page 2:
2025-07-06 22:32:53,859 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:32:54,374 - INFO - Request Parameters - Page 3:
2025-07-06 22:32:54,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:54,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:55,062 - INFO - Response - Page 3:
2025-07-06 22:32:55,062 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:32:55,578 - INFO - Request Parameters - Page 4:
2025-07-06 22:32:55,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:55,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:56,266 - INFO - Response - Page 4:
2025-07-06 22:32:56,266 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:32:56,782 - INFO - Request Parameters - Page 5:
2025-07-06 22:32:56,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:56,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:57,501 - INFO - Response - Page 5:
2025-07-06 22:32:57,501 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:32:58,016 - INFO - Request Parameters - Page 6:
2025-07-06 22:32:58,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:58,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:58,720 - INFO - Response - Page 6:
2025-07-06 22:32:58,720 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:32:59,220 - INFO - Request Parameters - Page 7:
2025-07-06 22:32:59,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:32:59,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:32:59,861 - INFO - Response - Page 7:
2025-07-06 22:32:59,861 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:33:00,377 - INFO - Request Parameters - Page 8:
2025-07-06 22:33:00,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:00,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:01,002 - INFO - Response - Page 8:
2025-07-06 22:33:01,002 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:33:01,518 - INFO - Request Parameters - Page 9:
2025-07-06 22:33:01,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:01,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:02,190 - INFO - Response - Page 9:
2025-07-06 22:33:02,190 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:33:02,706 - INFO - Request Parameters - Page 10:
2025-07-06 22:33:02,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:02,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:03,362 - INFO - Response - Page 10:
2025-07-06 22:33:03,362 - INFO - 第 10 页获取到 50 条记录
2025-07-06 22:33:03,878 - INFO - Request Parameters - Page 11:
2025-07-06 22:33:03,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:03,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:04,300 - INFO - Response - Page 11:
2025-07-06 22:33:04,300 - INFO - 查询完成，共获取到 500 条记录
2025-07-06 22:33:04,300 - INFO - 获取到 500 条表单数据
2025-07-06 22:33:04,316 - INFO - 当前日期 2025-06-24 有 1 条MySQL数据需要处理
2025-07-06 22:33:04,316 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:33:04,316 - INFO - 开始处理日期: 2025-06-27
2025-07-06 22:33:04,316 - INFO - Request Parameters - Page 1:
2025-07-06 22:33:04,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:04,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:04,957 - INFO - Response - Page 1:
2025-07-06 22:33:04,957 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:33:05,457 - INFO - Request Parameters - Page 2:
2025-07-06 22:33:05,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:05,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:06,098 - INFO - Response - Page 2:
2025-07-06 22:33:06,098 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:33:06,614 - INFO - Request Parameters - Page 3:
2025-07-06 22:33:06,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:06,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:07,270 - INFO - Response - Page 3:
2025-07-06 22:33:07,270 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:33:07,786 - INFO - Request Parameters - Page 4:
2025-07-06 22:33:07,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:07,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:08,567 - INFO - Response - Page 4:
2025-07-06 22:33:08,567 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:33:09,068 - INFO - Request Parameters - Page 5:
2025-07-06 22:33:09,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:09,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:09,771 - INFO - Response - Page 5:
2025-07-06 22:33:09,771 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:33:10,287 - INFO - Request Parameters - Page 6:
2025-07-06 22:33:10,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:10,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:10,912 - INFO - Response - Page 6:
2025-07-06 22:33:10,912 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:33:11,428 - INFO - Request Parameters - Page 7:
2025-07-06 22:33:11,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:11,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:12,100 - INFO - Response - Page 7:
2025-07-06 22:33:12,100 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:33:12,600 - INFO - Request Parameters - Page 8:
2025-07-06 22:33:12,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:12,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:13,335 - INFO - Response - Page 8:
2025-07-06 22:33:13,335 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:33:13,835 - INFO - Request Parameters - Page 9:
2025-07-06 22:33:13,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:13,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:14,492 - INFO - Response - Page 9:
2025-07-06 22:33:14,492 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:33:14,992 - INFO - Request Parameters - Page 10:
2025-07-06 22:33:14,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:14,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:15,586 - INFO - Response - Page 10:
2025-07-06 22:33:15,586 - INFO - 第 10 页获取到 28 条记录
2025-07-06 22:33:16,102 - INFO - 查询完成，共获取到 478 条记录
2025-07-06 22:33:16,102 - INFO - 获取到 478 条表单数据
2025-07-06 22:33:16,102 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-07-06 22:33:16,102 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:33:16,102 - INFO - 开始处理日期: 2025-07-04
2025-07-06 22:33:16,102 - INFO - Request Parameters - Page 1:
2025-07-06 22:33:16,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:16,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:16,774 - INFO - Response - Page 1:
2025-07-06 22:33:16,774 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:33:17,290 - INFO - Request Parameters - Page 2:
2025-07-06 22:33:17,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:17,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:17,946 - INFO - Response - Page 2:
2025-07-06 22:33:17,946 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:33:18,462 - INFO - Request Parameters - Page 3:
2025-07-06 22:33:18,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:18,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:19,228 - INFO - Response - Page 3:
2025-07-06 22:33:19,228 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:33:19,744 - INFO - Request Parameters - Page 4:
2025-07-06 22:33:19,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:19,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:20,432 - INFO - Response - Page 4:
2025-07-06 22:33:20,432 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:33:20,947 - INFO - Request Parameters - Page 5:
2025-07-06 22:33:20,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:20,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:21,588 - INFO - Response - Page 5:
2025-07-06 22:33:21,588 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:33:22,104 - INFO - Request Parameters - Page 6:
2025-07-06 22:33:22,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:22,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:22,808 - INFO - Response - Page 6:
2025-07-06 22:33:22,808 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:33:23,323 - INFO - Request Parameters - Page 7:
2025-07-06 22:33:23,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:23,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:23,933 - INFO - Response - Page 7:
2025-07-06 22:33:23,933 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:33:24,433 - INFO - Request Parameters - Page 8:
2025-07-06 22:33:24,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:24,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:25,137 - INFO - Response - Page 8:
2025-07-06 22:33:25,137 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:33:25,652 - INFO - Request Parameters - Page 9:
2025-07-06 22:33:25,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:25,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751558400000, 1751644799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:26,309 - INFO - Response - Page 9:
2025-07-06 22:33:26,309 - INFO - 第 9 页获取到 43 条记录
2025-07-06 22:33:26,809 - INFO - 查询完成，共获取到 443 条记录
2025-07-06 22:33:26,809 - INFO - 获取到 443 条表单数据
2025-07-06 22:33:26,809 - INFO - 当前日期 2025-07-04 有 4 条MySQL数据需要处理
2025-07-06 22:33:26,809 - INFO - 日期 2025-07-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:33:26,809 - INFO - 开始处理日期: 2025-07-05
2025-07-06 22:33:26,809 - INFO - Request Parameters - Page 1:
2025-07-06 22:33:26,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:26,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:27,497 - INFO - Response - Page 1:
2025-07-06 22:33:27,497 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:33:28,013 - INFO - Request Parameters - Page 2:
2025-07-06 22:33:28,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:28,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:28,700 - INFO - Response - Page 2:
2025-07-06 22:33:28,700 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:33:29,201 - INFO - Request Parameters - Page 3:
2025-07-06 22:33:29,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:29,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:29,857 - INFO - Response - Page 3:
2025-07-06 22:33:29,857 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:33:30,373 - INFO - Request Parameters - Page 4:
2025-07-06 22:33:30,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:30,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:30,998 - INFO - Response - Page 4:
2025-07-06 22:33:30,998 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:33:31,514 - INFO - Request Parameters - Page 5:
2025-07-06 22:33:31,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:31,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:32,186 - INFO - Response - Page 5:
2025-07-06 22:33:32,186 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:33:32,686 - INFO - Request Parameters - Page 6:
2025-07-06 22:33:32,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:32,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:33,405 - INFO - Response - Page 6:
2025-07-06 22:33:33,405 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:33:33,921 - INFO - Request Parameters - Page 7:
2025-07-06 22:33:33,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:33,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:34,593 - INFO - Response - Page 7:
2025-07-06 22:33:34,593 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:33:35,125 - INFO - Request Parameters - Page 8:
2025-07-06 22:33:35,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:35,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:35,781 - INFO - Response - Page 8:
2025-07-06 22:33:35,781 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:33:36,297 - INFO - Request Parameters - Page 9:
2025-07-06 22:33:36,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:36,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:36,985 - INFO - Response - Page 9:
2025-07-06 22:33:36,985 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:33:37,501 - INFO - Request Parameters - Page 10:
2025-07-06 22:33:37,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:37,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:38,064 - INFO - Response - Page 10:
2025-07-06 22:33:38,064 - INFO - 第 10 页获取到 16 条记录
2025-07-06 22:33:38,564 - INFO - 查询完成，共获取到 466 条记录
2025-07-06 22:33:38,564 - INFO - 获取到 466 条表单数据
2025-07-06 22:33:38,564 - INFO - 当前日期 2025-07-05 有 137 条MySQL数据需要处理
2025-07-06 22:33:38,564 - INFO - 开始批量插入 1 条新记录
2025-07-06 22:33:38,720 - INFO - 批量插入响应状态码: 200
2025-07-06 22:33:38,720 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 14:33:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '839FE5D4-6161-70B2-894A-8AEE1C730607', 'x-acs-trace-id': '5796935234bb4399a35fb4229f91688a', 'etag': '645bjqwDAVlpYhjOZft077Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 22:33:38,720 - INFO - 批量插入响应体: {'result': ['FINST-MQA6627186VWCUKEARUSJ9RJRP0Z2KNPVRRCMYG']}
2025-07-06 22:33:38,720 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-06 22:33:38,720 - INFO - 成功插入的数据ID: ['FINST-MQA6627186VWCUKEARUSJ9RJRP0Z2KNPVRRCMYG']
2025-07-06 22:33:43,738 - INFO - 批量插入完成，共 1 条记录
2025-07-06 22:33:43,738 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-06 22:33:43,738 - INFO - 开始处理日期: 2025-07-06
2025-07-06 22:33:43,738 - INFO - Request Parameters - Page 1:
2025-07-06 22:33:43,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:33:43,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:33:44,175 - INFO - Response - Page 1:
2025-07-06 22:33:44,175 - INFO - 第 1 页获取到 1 条记录
2025-07-06 22:33:44,676 - INFO - 查询完成，共获取到 1 条记录
2025-07-06 22:33:44,676 - INFO - 获取到 1 条表单数据
2025-07-06 22:33:44,676 - INFO - 当前日期 2025-07-06 有 46 条MySQL数据需要处理
2025-07-06 22:33:44,676 - INFO - 开始批量插入 45 条新记录
2025-07-06 22:33:44,894 - INFO - 批量插入响应状态码: 200
2025-07-06 22:33:44,894 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 06 Jul 2025 14:33:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2172', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F0E64399-0BFB-7F43-B51F-29154B9DB62F', 'x-acs-trace-id': 'e8a6b5859d10151d2aae27e523a9bf19', 'etag': '2PE701v+ymgwd/HvhmKkbLQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-06 22:33:44,894 - INFO - 批量插入响应体: {'result': ['FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMZG', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM0H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM1H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM2H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM3H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM4H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM5H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM6H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM7H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM8H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM9H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMAH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMBH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMCH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMDH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMEH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMFH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMGH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMHH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMIH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMJH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMKH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMLH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMMH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMNH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMOH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMPH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMQH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMRH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMSH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMTH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMUH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMVH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMWH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMXH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMYH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMZH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM0I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM1I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM2I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM3I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM4I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM5I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM6I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM7I']}
2025-07-06 22:33:44,894 - INFO - 批量插入表单数据成功，批次 1，共 45 条记录
2025-07-06 22:33:44,894 - INFO - 成功插入的数据ID: ['FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMZG', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM0H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM1H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM2H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM3H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM4H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM5H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM6H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM7H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM8H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM9H', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMAH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMBH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMCH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMDH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMEH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMFH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMGH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMHH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMIH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMJH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMKH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMLH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMMH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMNH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMOH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMPH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMQH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMRH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMSH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMTH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMUH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMVH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMWH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMXH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMYH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMZH', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM0I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCM1I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM2I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM3I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM4I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM5I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM6I', 'FINST-MQA6627186VWCUKEARUSJ9RJRP0Z22FUVRRCM7I']
2025-07-06 22:33:49,912 - INFO - 批量插入完成，共 45 条记录
2025-07-06 22:33:49,912 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 45 条，错误: 0 条
2025-07-06 22:33:49,912 - INFO - 数据同步完成！更新: 0 条，插入: 46 条，错误: 1 条
2025-07-06 22:34:49,951 - INFO - 开始同步昨天与今天的销售数据: 2025-07-05 至 2025-07-06
2025-07-06 22:34:49,951 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-06 22:34:49,951 - INFO - 查询参数: ('2025-07-05', '2025-07-06')
2025-07-06 22:34:50,108 - INFO - MySQL查询成功，时间段: 2025-07-05 至 2025-07-06，共获取 525 条记录
2025-07-06 22:34:50,108 - INFO - 获取到 2 个日期需要处理: ['2025-07-05', '2025-07-06']
2025-07-06 22:34:50,108 - INFO - 开始处理日期: 2025-07-05
2025-07-06 22:34:50,108 - INFO - Request Parameters - Page 1:
2025-07-06 22:34:50,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:50,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:50,811 - INFO - Response - Page 1:
2025-07-06 22:34:50,811 - INFO - 第 1 页获取到 50 条记录
2025-07-06 22:34:51,327 - INFO - Request Parameters - Page 2:
2025-07-06 22:34:51,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:51,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:52,062 - INFO - Response - Page 2:
2025-07-06 22:34:52,062 - INFO - 第 2 页获取到 50 条记录
2025-07-06 22:34:52,577 - INFO - Request Parameters - Page 3:
2025-07-06 22:34:52,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:52,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:53,234 - INFO - Response - Page 3:
2025-07-06 22:34:53,234 - INFO - 第 3 页获取到 50 条记录
2025-07-06 22:34:53,750 - INFO - Request Parameters - Page 4:
2025-07-06 22:34:53,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:53,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:54,453 - INFO - Response - Page 4:
2025-07-06 22:34:54,453 - INFO - 第 4 页获取到 50 条记录
2025-07-06 22:34:54,969 - INFO - Request Parameters - Page 5:
2025-07-06 22:34:54,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:54,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:55,610 - INFO - Response - Page 5:
2025-07-06 22:34:55,610 - INFO - 第 5 页获取到 50 条记录
2025-07-06 22:34:56,126 - INFO - Request Parameters - Page 6:
2025-07-06 22:34:56,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:56,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:56,782 - INFO - Response - Page 6:
2025-07-06 22:34:56,782 - INFO - 第 6 页获取到 50 条记录
2025-07-06 22:34:57,298 - INFO - Request Parameters - Page 7:
2025-07-06 22:34:57,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:57,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:58,001 - INFO - Response - Page 7:
2025-07-06 22:34:58,001 - INFO - 第 7 页获取到 50 条记录
2025-07-06 22:34:58,517 - INFO - Request Parameters - Page 8:
2025-07-06 22:34:58,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:58,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:34:59,158 - INFO - Response - Page 8:
2025-07-06 22:34:59,158 - INFO - 第 8 页获取到 50 条记录
2025-07-06 22:34:59,674 - INFO - Request Parameters - Page 9:
2025-07-06 22:34:59,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:34:59,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:35:00,315 - INFO - Response - Page 9:
2025-07-06 22:35:00,315 - INFO - 第 9 页获取到 50 条记录
2025-07-06 22:35:00,831 - INFO - Request Parameters - Page 10:
2025-07-06 22:35:00,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:35:00,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751644800000, 1751731199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:35:01,378 - INFO - Response - Page 10:
2025-07-06 22:35:01,378 - INFO - 第 10 页获取到 17 条记录
2025-07-06 22:35:01,878 - INFO - 查询完成，共获取到 467 条记录
2025-07-06 22:35:01,878 - INFO - 获取到 467 条表单数据
2025-07-06 22:35:01,878 - INFO - 当前日期 2025-07-05 有 467 条MySQL数据需要处理
2025-07-06 22:35:01,894 - INFO - 日期 2025-07-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:35:01,894 - INFO - 开始处理日期: 2025-07-06
2025-07-06 22:35:01,894 - INFO - Request Parameters - Page 1:
2025-07-06 22:35:01,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-06 22:35:01,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751731200000, 1751817599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-06 22:35:02,550 - INFO - Response - Page 1:
2025-07-06 22:35:02,550 - INFO - 第 1 页获取到 46 条记录
2025-07-06 22:35:03,066 - INFO - 查询完成，共获取到 46 条记录
2025-07-06 22:35:03,066 - INFO - 获取到 46 条表单数据
2025-07-06 22:35:03,066 - INFO - 当前日期 2025-07-06 有 46 条MySQL数据需要处理
2025-07-06 22:35:03,066 - INFO - 日期 2025-07-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:35:03,066 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-06 22:35:03,066 - INFO - 同步完成
