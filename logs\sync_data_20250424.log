2025-04-24 18:23:50,901 - INFO - ==================================================
2025-04-24 18:23:50,901 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 18:23:50,902 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 18:23:50,902 - INFO - ==================================================
2025-04-24 18:23:50,902 - INFO - 程序入口点: __main__
2025-04-24 18:23:50,902 - INFO - ==================================================
2025-04-24 18:23:50,903 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 18:23:50,903 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 18:23:50,903 - INFO - ==================================================
2025-04-24 18:23:50,903 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 18:23:50,905 - INFO - sales_data表已存在，无需创建
2025-04-24 18:23:50,906 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 18:23:50,906 - INFO - DataSyncManager初始化完成
2025-04-24 18:23:50,907 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 18:23:50,907 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 18:23:50,907 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 18:23:50,907 - INFO - 开始综合数据同步流程...
2025-04-24 18:23:50,907 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 18:23:50,908 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 18:23:50,908 - INFO - 正在获取********至********的数据
2025-04-24 18:23:50,908 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 18:23:50,908 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '68A4ABFA30D28160750B8714EE8BE19B'}
2025-04-24 18:23:53,786 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 18:23:53,789 - INFO - 过滤后保留 238 条记录
2025-04-24 18:23:55,792 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 18:23:55,816 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 18:23:55,816 - INFO - - 总记录数: 238
2025-04-24 18:23:55,816 - INFO - - 成功插入: 0
2025-04-24 18:23:55,816 - INFO - - 成功更新: 0
2025-04-24 18:23:55,817 - INFO - - 无需更新: 238
2025-04-24 18:23:55,817 - INFO - - 处理失败: 0
2025-04-24 18:23:55,991 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 18:23:55,992 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 18:23:55,992 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 18:23:55,993 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 18:23:56,018 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 18:23:56,019 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 18:23:56,019 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 18:23:56,019 - INFO - 开始获取宜搭日销售表单数据...
2025-04-24 18:23:56,019 - INFO - 查询宜搭日销售表单数据，时间段为: ******** 至 ********
2025-04-24 18:23:56,020 - INFO - 初始化宜搭表单数据客户端，配置: {'accessKeyId': 'dingzzwjtejp6nyxyssh', 'accessKeySecret': '****************************************************************', 'endpoint': 'api.dingtalk.com', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'formCode': 'FORM-DAILY-SALES', 'monthlyFormUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'monthlyFormCode': 'FORM-MONTHLY-SALES', 'userId': 'hexuepeng', 'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD'}
2025-04-24 18:23:56,020 - INFO - 宜搭API客户端创建成功
2025-04-24 18:23:56,020 - INFO - 开始获取宜搭表单数据，页大小：100，搜索条件：[{'key': 'dateField_m9dkdkoz', 'value': [1735660800000, 1735747199000], 'type': 'DOUBLE', 'operator': 'between', 'componentName': 'DateField'}]
2025-04-24 18:23:56,420 - INFO - 宜搭API请求参数 - 第1页:
2025-04-24 18:23:59,066 - INFO - 宜搭API返回数据 - 第1页，记录数: 100
2025-04-24 18:23:59,070 - INFO - 准备获取第2页数据
2025-04-24 18:23:59,192 - INFO - 宜搭API请求参数 - 第2页:
2025-04-24 18:23:59,866 - INFO - 宜搭API返回数据 - 第2页，记录数: 100
2025-04-24 18:23:59,869 - INFO - 准备获取第3页数据
2025-04-24 18:23:59,975 - INFO - 宜搭API请求参数 - 第3页:
2025-04-24 18:24:00,700 - INFO - 宜搭API返回数据 - 第3页，记录数: 100
2025-04-24 18:24:00,703 - INFO - 准备获取第4页数据
2025-04-24 18:24:00,813 - INFO - 宜搭API请求参数 - 第4页:
2025-04-24 18:24:01,446 - INFO - 宜搭API返回数据 - 第4页，记录数: 100
2025-04-24 18:24:01,449 - INFO - 准备获取第5页数据
2025-04-24 18:24:01,571 - INFO - 宜搭API请求参数 - 第5页:
2025-04-24 18:24:02,246 - INFO - 宜搭API返回数据 - 第5页，记录数: 100
2025-04-24 18:24:02,249 - INFO - 准备获取第6页数据
2025-04-24 18:24:02,374 - INFO - 宜搭API请求参数 - 第6页:
2025-04-24 18:24:02,965 - INFO - 宜搭API返回数据 - 第6页，记录数: 100
2025-04-24 18:24:02,967 - INFO - 准备获取第7页数据
2025-04-24 18:24:03,099 - INFO - 宜搭API请求参数 - 第7页:
2025-04-24 18:24:03,722 - INFO - 宜搭API返回数据 - 第7页，记录数: 100
2025-04-24 18:24:03,725 - INFO - 准备获取第8页数据
2025-04-24 18:24:03,856 - INFO - 宜搭API请求参数 - 第8页:
2025-04-24 18:24:04,510 - INFO - 宜搭API返回数据 - 第8页，记录数: 100
2025-04-24 18:24:04,514 - INFO - 准备获取第9页数据
2025-04-24 18:24:04,631 - INFO - 宜搭API请求参数 - 第9页:
2025-04-24 18:24:05,297 - INFO - 宜搭API返回数据 - 第9页，记录数: 100
2025-04-24 18:24:05,300 - INFO - 准备获取第10页数据
2025-04-24 18:24:05,416 - INFO - 宜搭API请求参数 - 第10页:
2025-04-24 18:24:05,969 - INFO - 宜搭API返回数据 - 第10页，记录数: 52
2025-04-24 18:24:05,971 - INFO - 已到达最后一页
2025-04-24 18:24:05,971 - INFO - 成功获取宜搭表单数据，总记录数：952
2025-04-24 18:24:05,971 - INFO - 数据示例（前3条）:
2025-04-24 18:24:05,971 - INFO - 示例 #1: formInstanceId=FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M64, 字段数量=21
2025-04-24 18:24:05,972 - INFO - 示例 #2: formInstanceId=FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M44, 字段数量=21
2025-04-24 18:24:05,972 - INFO - 示例 #3: formInstanceId=FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9MU3, 字段数量=21
2025-04-24 18:24:05,972 - INFO - 成功获取宜搭日销售表单数据，共 952 条记录
2025-04-24 18:24:05,972 - INFO - ===== 宜搭日销售表单数据示例 =====
2025-04-24 18:24:05,973 - INFO - 示例 #1 formInstanceId: FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M64
2025-04-24 18:24:05,973 - INFO - 示例 #2 formInstanceId: FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M44
2025-04-24 18:24:05,973 - INFO - 示例 #3 formInstanceId: FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9MU3
2025-04-24 18:24:05,977 - INFO - ===== 宜搭日销售表单数据示例结束 =====
2025-04-24 18:24:05,977 - INFO - 成功获取宜搭日销售表单数据，共 952 条记录
2025-04-24 18:24:05,977 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'form_data']
2025-04-24 18:24:05,977 - INFO - form_data字段结构示例: ['numberField_m9dkdkpb_value', 'numberField_m9dkdkp9', 'numberField_m9dkdkp8', 'numberField_m9dkdkp8_value', 'numberField_m9dkdkpc', 'numberField_m9dkdkpb', 'numberField_m9dkdkpa', 'textField_m9dkdkpi', 'textField_m9dkdkph', 'numberField_m9dkdkpa_value', 'textField_m9dkdkpg', 'numberField_m9dkdkp9_value', 'numberField_m9dkdkpf', 'numberField_m9dkdkpe', 'numberField_m9dkdkpe_value', 'numberField_m9dkdkpd', 'numberField_m9dkdkpf_value', 'numberField_m9dkdkpd_value', 'textField_m9dkdkox', 'numberField_m9dkdkpc_value', 'dateField_m9dkdkoz']
2025-04-24 18:24:05,978 - INFO - 开始对比和同步日销售数据...
2025-04-24 18:24:05,980 - INFO - 成功创建宜搭日销售数据索引，共 1 条记录
2025-04-24 18:24:05,980 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 18:24:05,980 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 18:24:05,980 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 18:24:05,981 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 18:24:05,981 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 18:24:05,981 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 18:24:05,987 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 18:24:05,993 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 18:24:05,995 - INFO - 开始批量插入日销售数据，共 238 条记录，批量大小: 100
2025-04-24 18:24:05,995 - INFO - 开始创建宜搭API客户端...
2025-04-24 18:24:05,996 - INFO - 宜搭API客户端创建成功
2025-04-24 18:24:05,996 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 18:24:05,997 - INFO - 宜搭API批量插入请求参数: systemToken=BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2, appType=APP, formUuid=FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P
2025-04-24 18:24:05,997 - ERROR - 批量插入请求失败: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1178, in batch_insert_daily_form_data
    response = client.batch_save_form_data(**params)
TypeError: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
2025-04-24 18:24:05,999 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 18:24:06,000 - INFO - 宜搭API批量插入请求参数: systemToken=BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2, appType=APP, formUuid=FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P
2025-04-24 18:24:06,000 - ERROR - 批量插入请求失败: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1178, in batch_insert_daily_form_data
    response = client.batch_save_form_data(**params)
TypeError: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
2025-04-24 18:24:06,001 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 18:24:06,001 - INFO - 宜搭API批量插入请求参数: systemToken=BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2, appType=APP, formUuid=FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P
2025-04-24 18:24:06,003 - ERROR - 批量插入请求失败: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1178, in batch_insert_daily_form_data
    response = client.batch_save_form_data(**params)
TypeError: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
2025-04-24 18:24:06,003 - INFO - 日销售数据批量插入完成！成功: 0 条，失败: 238 条
2025-04-24 18:24:06,003 - INFO - 批量插入日销售数据完成，共 238 条记录
2025-04-24 18:24:06,006 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条，跳过: 0 条
2025-04-24 18:24:06,007 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 18:24:06,008 - INFO - 开始获取宜搭月销售表单数据...
2025-04-24 18:24:06,008 - INFO - 查询宜搭月销售表单数据，时间段为: ******** 至 ********
2025-04-24 18:24:06,008 - INFO - 初始化宜搭表单数据客户端，配置: {'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'formCode': 'FORM-MONTHLY-SALES', 'accessKeyId': 'dingzzwjtejp6nyxyssh', 'accessKeySecret': '****************************************************************', 'endpoint': 'api.dingtalk.com', 'userId': 'hexuepeng', 'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD'}
2025-04-24 18:24:06,008 - INFO - 宜搭API客户端创建成功
2025-04-24 18:24:06,009 - INFO - 开始获取宜搭表单数据，页大小：100，搜索条件：None
2025-04-24 18:24:06,129 - INFO - 宜搭API请求参数 - 第1页:
2025-04-24 18:24:06,723 - INFO - 宜搭API返回数据 - 第1页，记录数: 100
2025-04-24 18:24:06,726 - INFO - 准备获取第2页数据
2025-04-24 18:24:06,854 - INFO - 宜搭API请求参数 - 第2页:
2025-04-24 18:24:07,406 - INFO - 宜搭API返回数据 - 第2页，记录数: 100
2025-04-24 18:24:07,410 - INFO - 准备获取第3页数据
2025-04-24 18:24:07,520 - INFO - 宜搭API请求参数 - 第3页:
2025-04-24 18:24:08,003 - INFO - 宜搭API返回数据 - 第3页，记录数: 100
2025-04-24 18:24:08,006 - INFO - 准备获取第4页数据
2025-04-24 18:24:08,132 - INFO - 宜搭API请求参数 - 第4页:
2025-04-24 18:24:08,669 - INFO - 宜搭API返回数据 - 第4页，记录数: 100
2025-04-24 18:24:08,672 - INFO - 准备获取第5页数据
2025-04-24 18:24:08,796 - INFO - 宜搭API请求参数 - 第5页:
2025-04-24 18:24:09,287 - INFO - 宜搭API返回数据 - 第5页，记录数: 86
2025-04-24 18:24:09,289 - INFO - 已到达最后一页
2025-04-24 18:24:09,290 - INFO - 成功获取宜搭表单数据，总记录数：486
2025-04-24 18:24:09,290 - INFO - 数据示例（前3条）:
2025-04-24 18:24:09,290 - INFO - 示例 #1: formInstanceId=FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MB7, 字段数量=21
2025-04-24 18:24:09,290 - INFO - 示例 #2: formInstanceId=FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MD7, 字段数量=21
2025-04-24 18:24:09,290 - INFO - 示例 #3: formInstanceId=FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MF7, 字段数量=21
2025-04-24 18:24:09,291 - INFO - 成功获取宜搭月销售表单数据，共 486 条记录
2025-04-24 18:24:09,291 - INFO - ===== 宜搭月销售表单数据示例 =====
2025-04-24 18:24:09,292 - INFO - 示例 #1 formInstanceId: FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MB7
2025-04-24 18:24:09,292 - INFO - 示例 #2 formInstanceId: FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MD7
2025-04-24 18:24:09,292 - INFO - 示例 #3 formInstanceId: FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MF7
2025-04-24 18:24:09,292 - INFO - ===== 宜搭月销售表单数据示例结束 =====
2025-04-24 18:24:09,293 - INFO - 成功获取宜搭月销售表单数据，共 486 条记录
2025-04-24 18:24:09,293 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'form_data']
2025-04-24 18:24:09,293 - INFO - 月度form_data字段结构示例: ['numberField_m9dkdkpb_value', 'numberField_m9dkdkp9', 'numberField_m9dkdkp8', 'numberField_m9dkdkp8_value', 'numberField_m9dkdkpc', 'numberField_m9dkdkpb', 'numberField_m9dkdkpa', 'textField_m9dkdkpi', 'textField_m9dkdkph', 'numberField_m9dkdkpa_value', 'textField_m9dkdkpg', 'numberField_m9dkdkp9_value', 'numberField_m9dkdkpf', 'numberField_m9dkdkpe', 'numberField_m9dkdkpe_value', 'numberField_m9dkdkpd', 'numberField_m9dkdkpf_value', 'numberField_m9dkdkpd_value', 'textField_m9dkdkox', 'numberField_m9dkdkpc_value', 'dateField_m9dkdkoz']
2025-04-24 18:24:09,293 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 18:24:09,294 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 18:24:09,311 - INFO - 开始批量插入月销售数据，共 238 条记录，批量大小: 100
2025-04-24 18:24:09,311 - INFO - 开始创建宜搭API客户端...
2025-04-24 18:24:09,311 - INFO - 宜搭API客户端创建成功
2025-04-24 18:24:09,312 - INFO - 处理月度数据批次 1/3，包含 100 条记录
2025-04-24 18:24:09,313 - INFO - 宜搭API批量插入月度数据请求参数: systemToken=BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2, appType=APP, formUuid=FORM-B485DB81D8E94A5487DF601F30226AECLXCE
2025-04-24 18:24:09,313 - ERROR - 批量插入月度数据请求失败: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1269, in batch_insert_monthly_form_data
    response = client.batch_save_form_data(**params)
TypeError: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
2025-04-24 18:24:09,314 - INFO - 处理月度数据批次 2/3，包含 100 条记录
2025-04-24 18:24:09,316 - INFO - 宜搭API批量插入月度数据请求参数: systemToken=BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2, appType=APP, formUuid=FORM-B485DB81D8E94A5487DF601F30226AECLXCE
2025-04-24 18:24:09,316 - ERROR - 批量插入月度数据请求失败: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1269, in batch_insert_monthly_form_data
    response = client.batch_save_form_data(**params)
TypeError: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
2025-04-24 18:24:09,316 - INFO - 处理月度数据批次 3/3，包含 38 条记录
2025-04-24 18:24:09,317 - INFO - 宜搭API批量插入月度数据请求参数: systemToken=BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2, appType=APP, formUuid=FORM-B485DB81D8E94A5487DF601F30226AECLXCE
2025-04-24 18:24:09,317 - ERROR - 批量插入月度数据请求失败: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1269, in batch_insert_monthly_form_data
    response = client.batch_save_form_data(**params)
TypeError: Client.batch_save_form_data() got an unexpected keyword argument 'systemToken'
2025-04-24 18:24:09,317 - INFO - 月销售数据批量插入完成！成功: 0 条，失败: 238 条
2025-04-24 18:24:09,318 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 18:24:09,318 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 18:24:09,318 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 18:24:09,434 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 18:24:09,435 - INFO - 综合数据同步流程完成！
2025-04-24 18:24:09,438 - INFO - 综合数据同步完成
2025-04-24 18:24:09,438 - INFO - ==================================================
2025-04-24 18:24:09,438 - INFO - 程序退出
2025-04-24 18:24:09,438 - INFO - ==================================================
2025-04-24 18:38:42,218 - INFO - ==================================================
2025-04-24 18:38:42,218 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 18:38:42,218 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 18:38:42,219 - INFO - ==================================================
2025-04-24 18:38:42,219 - INFO - 程序入口点: __main__
2025-04-24 18:38:42,219 - INFO - ==================================================
2025-04-24 18:38:42,219 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 18:38:42,219 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 18:38:42,220 - INFO - ==================================================
2025-04-24 18:38:42,220 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 18:38:42,221 - INFO - sales_data表已存在，无需创建
2025-04-24 18:38:42,222 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 18:38:42,222 - INFO - DataSyncManager初始化完成
2025-04-24 18:38:42,222 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 18:38:42,222 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 18:38:42,223 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 18:38:42,223 - INFO - 开始综合数据同步流程...
2025-04-24 18:38:42,223 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 18:38:42,224 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 18:38:42,224 - INFO - 正在获取********至********的数据
2025-04-24 18:38:42,225 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 18:38:42,225 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FDCAE56783E866730540494D5FA960B7'}
2025-04-24 18:38:44,326 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 18:38:44,329 - INFO - 过滤后保留 238 条记录
2025-04-24 18:38:46,332 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 18:38:46,355 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 18:38:46,356 - INFO - - 总记录数: 238
2025-04-24 18:38:46,356 - INFO - - 成功插入: 0
2025-04-24 18:38:46,356 - INFO - - 成功更新: 0
2025-04-24 18:38:46,356 - INFO - - 无需更新: 238
2025-04-24 18:38:46,357 - INFO - - 处理失败: 0
2025-04-24 18:38:46,529 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 18:38:46,530 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 18:38:46,530 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 18:38:46,530 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 18:38:46,556 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 18:38:46,557 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 18:38:46,557 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 18:38:46,557 - INFO - 开始获取宜搭日销售表单数据...
2025-04-24 18:38:46,557 - INFO - 查询宜搭日销售表单数据，时间段为: ******** 至 ********
2025-04-24 18:38:46,557 - INFO - 初始化宜搭表单数据客户端，配置: {'accessKeyId': 'dingzzwjtejp6nyxyssh', 'accessKeySecret': '****************************************************************', 'endpoint': 'api.dingtalk.com', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'formCode': 'FORM-DAILY-SALES', 'monthlyFormUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'monthlyFormCode': 'FORM-MONTHLY-SALES', 'userId': 'hexuepeng', 'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD'}
2025-04-24 18:38:46,558 - INFO - 宜搭API客户端创建成功
2025-04-24 18:38:46,558 - INFO - 开始获取宜搭表单数据，页大小：100，搜索条件：[{'key': 'dateField_m9dkdkoz', 'value': [1735660800000, 1735747199000], 'type': 'DOUBLE', 'operator': 'between', 'componentName': 'DateField'}]
2025-04-24 18:38:46,913 - INFO - 宜搭API请求参数 - 第1页:
2025-04-24 18:38:52,503 - INFO - 宜搭API返回数据 - 第1页，记录数: 100
2025-04-24 18:38:52,507 - INFO - 准备获取第2页数据
2025-04-24 18:38:52,621 - INFO - 宜搭API请求参数 - 第2页:
2025-04-24 18:38:53,241 - INFO - 宜搭API返回数据 - 第2页，记录数: 100
2025-04-24 18:38:53,244 - INFO - 准备获取第3页数据
2025-04-24 18:38:53,362 - INFO - 宜搭API请求参数 - 第3页:
2025-04-24 18:38:53,984 - INFO - 宜搭API返回数据 - 第3页，记录数: 100
2025-04-24 18:38:53,987 - INFO - 准备获取第4页数据
2025-04-24 18:38:54,120 - INFO - 宜搭API请求参数 - 第4页:
2025-04-24 18:38:54,727 - INFO - 宜搭API返回数据 - 第4页，记录数: 100
2025-04-24 18:38:54,730 - INFO - 准备获取第5页数据
2025-04-24 18:38:54,848 - INFO - 宜搭API请求参数 - 第5页:
2025-04-24 18:38:55,922 - INFO - 宜搭API返回数据 - 第5页，记录数: 100
2025-04-24 18:38:55,926 - INFO - 准备获取第6页数据
2025-04-24 18:38:56,036 - INFO - 宜搭API请求参数 - 第6页:
2025-04-24 18:38:56,718 - INFO - 宜搭API返回数据 - 第6页，记录数: 100
2025-04-24 18:38:56,721 - INFO - 准备获取第7页数据
2025-04-24 18:38:56,840 - INFO - 宜搭API请求参数 - 第7页:
2025-04-24 18:38:57,429 - INFO - 宜搭API返回数据 - 第7页，记录数: 100
2025-04-24 18:38:57,432 - INFO - 准备获取第8页数据
2025-04-24 18:38:57,556 - INFO - 宜搭API请求参数 - 第8页:
2025-04-24 18:38:58,198 - INFO - 宜搭API返回数据 - 第8页，记录数: 100
2025-04-24 18:38:58,201 - INFO - 准备获取第9页数据
2025-04-24 18:38:58,303 - INFO - 宜搭API请求参数 - 第9页:
2025-04-24 18:38:58,969 - INFO - 宜搭API返回数据 - 第9页，记录数: 100
2025-04-24 18:38:58,972 - INFO - 准备获取第10页数据
2025-04-24 18:38:59,087 - INFO - 宜搭API请求参数 - 第10页:
2025-04-24 18:38:59,609 - INFO - 宜搭API返回数据 - 第10页，记录数: 52
2025-04-24 18:38:59,611 - INFO - 已到达最后一页
2025-04-24 18:38:59,611 - INFO - 成功获取宜搭表单数据，总记录数：952
2025-04-24 18:38:59,611 - INFO - 数据示例（前3条）:
2025-04-24 18:38:59,611 - INFO - 示例 #1: formInstanceId=FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M64, 字段数量=21
2025-04-24 18:38:59,612 - INFO - 示例 #2: formInstanceId=FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M44, 字段数量=21
2025-04-24 18:38:59,612 - INFO - 示例 #3: formInstanceId=FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9MU3, 字段数量=21
2025-04-24 18:38:59,612 - INFO - 成功获取宜搭日销售表单数据，共 952 条记录
2025-04-24 18:38:59,612 - INFO - ===== 宜搭日销售表单数据示例 =====
2025-04-24 18:38:59,612 - INFO - 示例 #1 formInstanceId: FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M64
2025-04-24 18:38:59,613 - INFO - 示例 #2 formInstanceId: FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9M44
2025-04-24 18:38:59,613 - INFO - 示例 #3 formInstanceId: FINST-X8D66N81JMUUSMLP9GMFAC1Q8EFK2CH036V9MU3
2025-04-24 18:38:59,613 - INFO - ===== 宜搭日销售表单数据示例结束 =====
2025-04-24 18:38:59,613 - INFO - 成功获取宜搭日销售表单数据，共 952 条记录
2025-04-24 18:38:59,613 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'form_data']
2025-04-24 18:38:59,613 - INFO - form_data字段结构示例: ['numberField_m9dkdkpb_value', 'numberField_m9dkdkp9', 'numberField_m9dkdkp8', 'numberField_m9dkdkp8_value', 'numberField_m9dkdkpc', 'numberField_m9dkdkpb', 'numberField_m9dkdkpa', 'textField_m9dkdkpi', 'textField_m9dkdkph', 'numberField_m9dkdkpa_value', 'textField_m9dkdkpg', 'numberField_m9dkdkp9_value', 'numberField_m9dkdkpf', 'numberField_m9dkdkpe', 'numberField_m9dkdkpe_value', 'numberField_m9dkdkpd', 'numberField_m9dkdkpf_value', 'numberField_m9dkdkpd_value', 'textField_m9dkdkox', 'numberField_m9dkdkpc_value', 'dateField_m9dkdkoz']
2025-04-24 18:38:59,614 - INFO - 开始对比和同步日销售数据...
2025-04-24 18:38:59,616 - INFO - 成功创建宜搭日销售数据索引，共 1 条记录
2025-04-24 18:38:59,616 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 18:38:59,616 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 18:38:59,617 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 18:38:59,617 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 18:38:59,617 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 18:38:59,618 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 18:38:59,623 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 18:38:59,628 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 18:38:59,631 - INFO - 开始批量插入日销售数据，共 238 条记录，批量大小: 100
2025-04-24 18:38:59,631 - INFO - 开始创建宜搭API客户端...
2025-04-24 18:38:59,631 - INFO - 宜搭API客户端创建成功
2025-04-24 18:38:59,631 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 18:38:59,748 - INFO - 宜搭API批量插入请求参数: formUuid=FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P, appType=APP_D7E6ZB94ZUL5Q1GUAOLD, 记录数=100
2025-04-24 18:39:00,130 - INFO - 宜搭API批量插入响应: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:37:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2E08BF33-77D2-7253-8EF7-75C3CECCE6BF', 'x-acs-trace-id': '757a1d41fdfd7ff17ff52a0ea9b655cb', 'etag': '4s1Kz52pGbSOYCqmFc81/CA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9ML5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MM5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MN5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MO5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MP5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MQ5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MR5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MS5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MT5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MU5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MV5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MW5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MX5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MY5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MZ5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M06', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M16', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M26', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M36', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M46', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M56', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M66', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M76', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M86', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M96', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MA6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MB6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MC6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MD6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ME6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MF6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MG6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MH6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MI6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MJ6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MK6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ML6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MM6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MN6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MO6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MP6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MQ6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MR6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MS6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MT6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MU6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MV6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MW6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MX6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MY6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MZ6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M07', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M17', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M27', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M37', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M47', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M57', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M67', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M77', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M87', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M97', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MA7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MB7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MC7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MD7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ME7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MF7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MG7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MH7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MI7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MJ7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MK7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ML7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MM7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MN7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MO7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MP7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MQ7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MR7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MS7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MT7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MU7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MV7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MW7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MX7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MY7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MZ7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M08', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M18', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M28', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M38', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M48', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M58', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M68', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M78', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M88', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M98', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MA8', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MB8', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MC8']}}
2025-04-24 18:39:00,131 - WARNING - 宜搭API返回批量插入格式异常: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:37:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2E08BF33-77D2-7253-8EF7-75C3CECCE6BF', 'x-acs-trace-id': '757a1d41fdfd7ff17ff52a0ea9b655cb', 'etag': '4s1Kz52pGbSOYCqmFc81/CA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9ML5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MM5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MN5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MO5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MP5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MQ5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MR5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MS5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MT5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MU5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MV5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MW5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MX5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MY5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MZ5', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M06', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M16', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M26', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M36', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M46', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M56', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M66', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M76', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M86', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9M96', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MA6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MB6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22FIIB8V9MC6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MD6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ME6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MF6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MG6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MH6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MI6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MJ6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MK6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ML6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MM6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MN6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MO6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MP6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MQ6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MR6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MS6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MT6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MU6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MV6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MW6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MX6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MY6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MZ6', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M07', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M17', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M27', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M37', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M47', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M57', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M67', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M77', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M87', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M97', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MA7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MB7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MC7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MD7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ME7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MF7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MG7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MH7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MI7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MJ7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MK7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9ML7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MM7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MN7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MO7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MP7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MQ7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MR7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MS7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MT7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MU7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MV7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MW7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MX7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MY7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MZ7', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M08', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M18', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M28', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M38', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M48', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M58', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M68', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M78', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M88', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9M98', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MA8', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MB8', 'FINST-RNA66D71JYUUZFBJBIB3R7UZTVA22GIIB8V9MC8']}}
2025-04-24 18:39:00,132 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 18:39:00,254 - INFO - 宜搭API批量插入请求参数: formUuid=FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P, appType=APP_D7E6ZB94ZUL5Q1GUAOLD, 记录数=100
2025-04-24 18:39:00,569 - INFO - 宜搭API批量插入响应: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:37:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4778', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '77F0382A-EB3A-7F63-8F20-E8418AEBE313', 'x-acs-trace-id': 'bb54c15622baa30bd190a5e372f77aad', 'etag': '4NbkQ/Om4WK/+cthPEOsJIw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M3', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M4', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M5', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M6', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M7', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M8', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M9', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MA', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MB', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MC', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MD', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9ME', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MF', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MG', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MH', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MI', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MJ', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MK', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9ML', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MM', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MN', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MO', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MP', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MQ', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MR', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MS', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MT', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MU', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MV', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MW', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MX', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MY', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MZ', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M01', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M11', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M21', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M31', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M41', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M51', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M61', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M71', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M81', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M91', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MA1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MB1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MC1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MD1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ME1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MF1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MG1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MH1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MI1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MJ1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MK1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ML1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MM1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MN1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MO1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MP1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MQ1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MR1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MS1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MT1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MU1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MV1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MW1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MX1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MY1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MZ1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M02', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M12', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M22', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M32', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M42', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M52', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M62', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M72', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M82', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M92', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MA2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MB2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MC2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MD2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ME2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MF2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MG2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MH2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MI2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MJ2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MK2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ML2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MM2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MN2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MO2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MP2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MQ2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MR2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MS2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MT2']}}
2025-04-24 18:39:00,570 - WARNING - 宜搭API返回批量插入格式异常: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:37:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4778', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '77F0382A-EB3A-7F63-8F20-E8418AEBE313', 'x-acs-trace-id': 'bb54c15622baa30bd190a5e372f77aad', 'etag': '4NbkQ/Om4WK/+cthPEOsJIw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M3', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M4', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M5', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M6', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M7', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M8', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9M9', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MA', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MB', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MC', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MD', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9ME', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MF', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MG', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MH', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MI', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MJ', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MK', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9ML', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MM', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MN', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MO', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3TUIB8V9MP', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MQ', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MR', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MS', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MT', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MU', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MV', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MW', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MX', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MY', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MZ', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M01', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M11', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M21', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M31', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M41', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M51', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M61', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M71', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M81', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M91', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MA1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MB1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MC1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MD1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ME1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MF1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MG1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MH1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MI1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MJ1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MK1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ML1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MM1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MN1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MO1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MP1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MQ1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MR1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MS1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MT1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MU1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MV1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MW1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MX1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MY1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MZ1', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M02', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M12', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M22', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M32', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M42', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M52', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M62', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M72', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M82', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9M92', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MA2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MB2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MC2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MD2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ME2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MF2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MG2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MH2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MI2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MJ2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MK2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9ML2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MM2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MN2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3UUIB8V9MO2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MP2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MQ2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MR2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MS2', 'FINST-V7966QC13XUU9O1Z6DJU4A3GVUZT3VUIB8V9MT2']}}
2025-04-24 18:39:00,571 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 18:39:00,692 - INFO - 宜搭API批量插入请求参数: formUuid=FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P, appType=APP_D7E6ZB94ZUL5Q1GUAOLD, 记录数=38
2025-04-24 18:39:00,912 - INFO - 宜搭API批量插入响应: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:37:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '240FA714-4480-7849-9DD3-063AD3FD7DCD', 'x-acs-trace-id': '16b04f981279bda48766902b56f78583', 'etag': '1x/jNVccuDOTKJP/RGVIx9w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MD8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9ME8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MF8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MG8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MH8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MI8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MJ8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MK8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9ML8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MM8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MN8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MO8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MP8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MQ8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MR8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MS8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MT8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MU8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MV8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MW8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MX8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MY8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MZ8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M09', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M19', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M29', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M39', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M49', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M59', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M69', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M79', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M89', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M99', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MA9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MB9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MC9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MD9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9ME9']}}
2025-04-24 18:39:00,913 - WARNING - 宜搭API返回批量插入格式异常: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:37:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '240FA714-4480-7849-9DD3-063AD3FD7DCD', 'x-acs-trace-id': '16b04f981279bda48766902b56f78583', 'etag': '1x/jNVccuDOTKJP/RGVIx9w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MD8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9ME8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MF8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MG8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MH8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MI8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MJ8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MK8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9ML8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MM8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MN8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MO8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MP8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MQ8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MR8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MS8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MT8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MU8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MV8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MW8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MX8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MY8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MZ8', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M09', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M19', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M29', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M39', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M49', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M59', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M69', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M79', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M89', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9M99', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MA9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MB9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MC9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9MD9', 'FINST-1OC66A9187UULW6K9XH9HBNANVIX384JB8V9ME9']}}
2025-04-24 18:39:00,914 - INFO - 日销售数据批量插入完成！成功: 0 条，失败: 238 条
2025-04-24 18:39:00,914 - INFO - 批量插入日销售数据完成，共 238 条记录
2025-04-24 18:39:00,914 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条，跳过: 0 条
2025-04-24 18:39:00,915 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 18:39:00,915 - INFO - 开始获取宜搭月销售表单数据...
2025-04-24 18:39:00,915 - INFO - 查询宜搭月销售表单数据，时间段为: ******** 至 ********
2025-04-24 18:39:00,915 - INFO - 初始化宜搭表单数据客户端，配置: {'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'formCode': 'FORM-MONTHLY-SALES', 'accessKeyId': 'dingzzwjtejp6nyxyssh', 'accessKeySecret': '****************************************************************', 'endpoint': 'api.dingtalk.com', 'userId': 'hexuepeng', 'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD'}
2025-04-24 18:39:00,915 - INFO - 宜搭API客户端创建成功
2025-04-24 18:39:00,915 - INFO - 开始获取宜搭表单数据，页大小：100，搜索条件：None
2025-04-24 18:39:01,029 - INFO - 宜搭API请求参数 - 第1页:
2025-04-24 18:39:01,666 - INFO - 宜搭API返回数据 - 第1页，记录数: 100
2025-04-24 18:39:01,669 - INFO - 准备获取第2页数据
2025-04-24 18:39:01,792 - INFO - 宜搭API请求参数 - 第2页:
2025-04-24 18:39:02,304 - INFO - 宜搭API返回数据 - 第2页，记录数: 100
2025-04-24 18:39:02,307 - INFO - 准备获取第3页数据
2025-04-24 18:39:02,415 - INFO - 宜搭API请求参数 - 第3页:
2025-04-24 18:39:02,983 - INFO - 宜搭API返回数据 - 第3页，记录数: 100
2025-04-24 18:39:02,987 - INFO - 准备获取第4页数据
2025-04-24 18:39:03,102 - INFO - 宜搭API请求参数 - 第4页:
2025-04-24 18:39:03,700 - INFO - 宜搭API返回数据 - 第4页，记录数: 100
2025-04-24 18:39:03,703 - INFO - 准备获取第5页数据
2025-04-24 18:39:03,819 - INFO - 宜搭API请求参数 - 第5页:
2025-04-24 18:39:04,448 - INFO - 宜搭API返回数据 - 第5页，记录数: 86
2025-04-24 18:39:04,452 - INFO - 已到达最后一页
2025-04-24 18:39:04,452 - INFO - 成功获取宜搭表单数据，总记录数：486
2025-04-24 18:39:04,453 - INFO - 数据示例（前3条）:
2025-04-24 18:39:04,453 - INFO - 示例 #1: formInstanceId=FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MB7, 字段数量=21
2025-04-24 18:39:04,453 - INFO - 示例 #2: formInstanceId=FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MD7, 字段数量=21
2025-04-24 18:39:04,453 - INFO - 示例 #3: formInstanceId=FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MF7, 字段数量=21
2025-04-24 18:39:04,454 - INFO - 成功获取宜搭月销售表单数据，共 486 条记录
2025-04-24 18:39:04,454 - INFO - ===== 宜搭月销售表单数据示例 =====
2025-04-24 18:39:04,455 - INFO - 示例 #1 formInstanceId: FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MB7
2025-04-24 18:39:04,455 - INFO - 示例 #2 formInstanceId: FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MD7
2025-04-24 18:39:04,455 - INFO - 示例 #3 formInstanceId: FINST-W3B66L716EUULKPOFE4UVBRVW8WP2QHE36V9MF7
2025-04-24 18:39:04,455 - INFO - ===== 宜搭月销售表单数据示例结束 =====
2025-04-24 18:39:04,456 - INFO - 成功获取宜搭月销售表单数据，共 486 条记录
2025-04-24 18:39:04,456 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'form_data']
2025-04-24 18:39:04,456 - INFO - 月度form_data字段结构示例: ['numberField_m9dkdkpb_value', 'numberField_m9dkdkp9', 'numberField_m9dkdkp8', 'numberField_m9dkdkp8_value', 'numberField_m9dkdkpc', 'numberField_m9dkdkpb', 'numberField_m9dkdkpa', 'textField_m9dkdkpi', 'textField_m9dkdkph', 'numberField_m9dkdkpa_value', 'textField_m9dkdkpg', 'numberField_m9dkdkp9_value', 'numberField_m9dkdkpf', 'numberField_m9dkdkpe', 'numberField_m9dkdkpe_value', 'numberField_m9dkdkpd', 'numberField_m9dkdkpf_value', 'numberField_m9dkdkpd_value', 'textField_m9dkdkox', 'numberField_m9dkdkpc_value', 'dateField_m9dkdkoz']
2025-04-24 18:39:04,456 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 18:39:04,460 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 18:39:04,479 - INFO - 开始批量插入月销售数据，共 238 条记录，批量大小: 100
2025-04-24 18:39:04,479 - INFO - 开始创建宜搭API客户端...
2025-04-24 18:39:04,479 - INFO - 宜搭API客户端创建成功
2025-04-24 18:39:04,480 - INFO - 处理月度数据批次 1/3，包含 100 条记录
2025-04-24 18:39:04,592 - INFO - 宜搭API批量插入月度数据请求参数: formUuid=FORM-B485DB81D8E94A5487DF601F30226AECLXCE, appType=APP_D7E6ZB94ZUL5Q1GUAOLD, 记录数=100
2025-04-24 18:39:04,927 - INFO - 宜搭API批量插入月度数据响应: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:38:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4778', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F930A764-5F84-715E-B77E-F34EE0B03AD9', 'x-acs-trace-id': '2f74b0dfd4b80e379c3cfa2a44625836', 'etag': '48G43W2PpvoQcVKxL/nieBw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M3', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M4', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M5', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M6', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M7', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M8', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M9', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MA', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MB', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MC', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MD', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9ME', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MF', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MG', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MH', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MI', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MJ', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MK', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9ML', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MM', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MN', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MO', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MP', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MQ', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MR', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MS', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MT', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MU', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MV', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MW', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MX', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MY', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MZ', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M01', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M11', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M21', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M31', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M41', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M51', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M61', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M71', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M81', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M91', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MA1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MB1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MC1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MD1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ME1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MF1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MG1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MH1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MI1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MJ1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MK1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ML1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MM1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MN1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MO1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MP1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MQ1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MR1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MS1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MT1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MU1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MV1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MW1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MX1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MY1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MZ1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M02', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M12', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M22', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M32', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M42', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M52', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M62', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M72', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M82', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M92', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MA2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MB2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MC2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MD2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ME2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MF2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MG2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MH2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MI2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MJ2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MK2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ML2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MM2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MN2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MO2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MP2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MQ2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MR2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MS2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MT2']}}
2025-04-24 18:39:04,928 - WARNING - 宜搭API返回批量插入月度数据格式异常: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:38:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4778', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F930A764-5F84-715E-B77E-F34EE0B03AD9', 'x-acs-trace-id': '2f74b0dfd4b80e379c3cfa2a44625836', 'etag': '48G43W2PpvoQcVKxL/nieBw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M3', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M4', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M5', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M6', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M7', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M8', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M9', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MA', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MB', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MC', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MD', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9ME', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MF', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MG', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MH', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MI', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MJ', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MK', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9ML', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MM', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MN', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MO', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MP', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MQ', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MR', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MS', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MT', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MU', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MV', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MW', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MX', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MY', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9MZ', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M01', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M11', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M21', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M31', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M41', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M51', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2R7MB8V9M61', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M71', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M81', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M91', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MA1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MB1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MC1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MD1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ME1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MF1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MG1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MH1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MI1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MJ1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MK1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ML1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MM1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MN1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MO1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MP1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MQ1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MR1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MS1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MT1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MU1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MV1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MW1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MX1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MY1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MZ1', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M02', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M12', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M22', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M32', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M42', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M52', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M62', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M72', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M82', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9M92', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MA2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MB2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MC2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MD2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ME2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MF2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MG2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MH2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MI2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MJ2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MK2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9ML2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MM2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MN2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MO2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MP2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MQ2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MR2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MS2', 'FINST-6IF66PC12YUU2HSFACELECOV2S4D2S7MB8V9MT2']}}
2025-04-24 18:39:04,929 - INFO - 处理月度数据批次 2/3，包含 100 条记录
2025-04-24 18:39:05,060 - INFO - 宜搭API批量插入月度数据请求参数: formUuid=FORM-B485DB81D8E94A5487DF601F30226AECLXCE, appType=APP_D7E6ZB94ZUL5Q1GUAOLD, 记录数=100
2025-04-24 18:39:05,421 - INFO - 宜搭API批量插入月度数据响应: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:38:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '63A6D46E-2BAF-760F-80A8-64F3A8395BF7', 'x-acs-trace-id': 'b3c33e1781103f9f079716b8714a9450', 'etag': '4nTiv2Z+coYaAhXdXK78mKA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M61', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M71', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M81', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M91', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MA1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MB1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MC1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MD1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9ME1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MF1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MG1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MH1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MI1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MJ1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MK1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9ML1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MM1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MN1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MO1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MP1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MQ1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MR1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MS1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MT1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MU1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MV1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MW1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MX1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MY1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MZ1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M02', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M12', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M22', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M32', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M42', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M52', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M62', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M72', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M82', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M92', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MA2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MB2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MC2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MD2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ME2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MF2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MG2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MH2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MI2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MJ2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MK2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ML2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MM2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MN2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MO2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MP2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MQ2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MR2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MS2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MT2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MU2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MV2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MW2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MX2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MY2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MZ2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M03', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M13', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M23', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M33', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M43', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M53', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M63', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M73', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M83', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M93', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MA3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MB3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MC3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MD3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ME3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MF3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MG3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MH3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MI3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MJ3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MK3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ML3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MM3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MN3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MO3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MP3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MQ3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MR3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MS3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MT3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MU3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MV3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MW3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MX3']}}
2025-04-24 18:39:05,422 - WARNING - 宜搭API返回批量插入月度数据格式异常: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:38:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '63A6D46E-2BAF-760F-80A8-64F3A8395BF7', 'x-acs-trace-id': 'b3c33e1781103f9f079716b8714a9450', 'etag': '4nTiv2Z+coYaAhXdXK78mKA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M61', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M71', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M81', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M91', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MA1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MB1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MC1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MD1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9ME1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MF1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MG1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MH1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MI1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MJ1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MK1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9ML1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MM1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MN1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MO1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MP1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MQ1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MR1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MS1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MT1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MU1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MV1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MW1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MX1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MY1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MZ1', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M02', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M12', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M22', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M32', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M42', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M52', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M62', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M72', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M82', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9M92', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MA2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MB2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MC2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33FLMB8V9MD2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ME2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MF2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MG2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MH2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MI2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MJ2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MK2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ML2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MM2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MN2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MO2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MP2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MQ2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MR2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MS2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MT2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MU2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MV2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MW2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MX2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MY2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MZ2', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M03', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M13', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M23', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M33', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M43', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M53', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M63', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M73', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M83', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9M93', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MA3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MB3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MC3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MD3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ME3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MF3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MG3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MH3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MI3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MJ3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MK3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9ML3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MM3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MN3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MO3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MP3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MQ3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MR3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MS3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MT3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MU3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MV3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MW3', 'FINST-COC668A1F7UUF9SKFU0L5CNSDXL33GLMB8V9MX3']}}
2025-04-24 18:39:05,423 - INFO - 处理月度数据批次 3/3，包含 38 条记录
2025-04-24 18:39:05,531 - INFO - 宜搭API批量插入月度数据请求参数: formUuid=FORM-B485DB81D8E94A5487DF601F30226AECLXCE, appType=APP_D7E6ZB94ZUL5Q1GUAOLD, 记录数=38
2025-04-24 18:39:05,743 - INFO - 宜搭API批量插入月度数据响应: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:38:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1821', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2CD9BB4F-4A4E-74B4-8EB5-DEAAC74D8C2B', 'x-acs-trace-id': 'a4e13934166e639e4933979d537f2cee', 'etag': '1QbDPm0sBtKxwChGQuFyOHg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9ML', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MM', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MN', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MO', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MP', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MQ', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MR', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MS', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MT', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MU', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MV', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MW', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MX', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MY', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MZ', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M01', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M11', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M21', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M31', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M41', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M51', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M61', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M71', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M81', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M91', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MA1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MB1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MC1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MD1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9ME1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MF1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MG1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MH1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MI1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MJ1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MK1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9ML1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MM1']}}
2025-04-24 18:39:05,744 - WARNING - 宜搭API返回批量插入月度数据格式异常: {'headers': {'server': 'DingTalk/1.0.0', 'date': 'Thu, 24 Apr 2025 10:38:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1821', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2CD9BB4F-4A4E-74B4-8EB5-DEAAC74D8C2B', 'x-acs-trace-id': 'a4e13934166e639e4933979d537f2cee', 'etag': '1QbDPm0sBtKxwChGQuFyOHg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}, 'statusCode': 200, 'body': {'result': ['FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9ML', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MM', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MN', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MO', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MP', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MQ', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MR', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MS', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MT', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MU', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MV', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MW', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MX', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MY', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MZ', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M01', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M11', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M21', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M31', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M41', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M51', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M61', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M71', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M81', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9M91', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MA1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MB1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MC1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MD1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9ME1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MF1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MG1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MH1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MI1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MJ1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MK1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9ML1', 'FINST-BSE66T81BLUUD5FXA64P74O8L2LB3HUMB8V9MM1']}}
2025-04-24 18:39:05,745 - INFO - 月销售数据批量插入完成！成功: 0 条，失败: 238 条
2025-04-24 18:39:05,745 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 18:39:05,745 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 18:39:05,745 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 18:39:05,867 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 18:39:05,867 - INFO - 综合数据同步流程完成！
2025-04-24 18:39:05,870 - INFO - 综合数据同步完成
2025-04-24 18:39:05,870 - INFO - ==================================================
2025-04-24 18:39:05,871 - INFO - 程序退出
2025-04-24 18:39:05,871 - INFO - ==================================================
2025-04-24 19:34:47,830 - INFO - ==================================================
2025-04-24 19:34:47,830 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 19:34:47,830 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:34:47,830 - INFO - ==================================================
2025-04-24 19:34:47,831 - INFO - 程序入口点: __main__
2025-04-24 19:34:47,831 - INFO - ==================================================
2025-04-24 19:34:47,831 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 19:34:47,831 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:34:47,831 - INFO - ==================================================
2025-04-24 19:34:47,832 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 19:34:47,833 - INFO - sales_data表已存在，无需创建
2025-04-24 19:34:47,834 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 19:34:47,834 - INFO - DataSyncManager初始化完成
2025-04-24 19:34:47,834 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 19:34:47,835 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 19:34:47,835 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 19:34:47,835 - INFO - 开始综合数据同步流程...
2025-04-24 19:34:47,835 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 19:34:47,836 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 19:34:47,836 - INFO - 正在获取********至********的数据
2025-04-24 19:34:47,836 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 19:34:47,838 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2E359C401FEBE067CDDCF2D1F76DAA2E'}
2025-04-24 19:34:50,086 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 19:34:50,089 - INFO - 过滤后保留 238 条记录
2025-04-24 19:34:52,092 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 19:34:52,115 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 19:34:52,115 - INFO - - 总记录数: 238
2025-04-24 19:34:52,115 - INFO - - 成功插入: 0
2025-04-24 19:34:52,115 - INFO - - 成功更新: 0
2025-04-24 19:34:52,115 - INFO - - 无需更新: 238
2025-04-24 19:34:52,115 - INFO - - 处理失败: 0
2025-04-24 19:34:52,286 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 19:34:52,287 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 19:34:52,287 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 19:34:52,287 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 19:34:52,315 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 19:34:52,315 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 19:34:52,315 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 19:34:52,316 - ERROR - 获取宜搭表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 928, in get_daily_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:34:52,318 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-04-24 19:34:52,318 - INFO - 开始对比和同步日销售数据...
2025-04-24 19:34:52,318 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-04-24 19:34:52,318 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 19:34:52,318 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 19:34:52,319 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 19:34:52,319 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 19:34:52,319 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 19:34:52,320 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 19:34:52,325 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 19:34:52,331 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 19:34:52,332 - INFO - 开始批量插入日销售数据，共 238 条记录，批量大小: 100
2025-04-24 19:34:52,333 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:34:52,333 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:34:52,333 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:34:52,333 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:34:52,335 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:34:52,335 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:34:52,335 - INFO - 日销售数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:34:52,336 - INFO - 批量插入日销售数据完成，共 238 条记录
2025-04-24 19:34:52,336 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条，跳过: 0 条
2025-04-24 19:34:52,336 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 19:34:52,336 - ERROR - 获取宜搭月度表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 993, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:34:52,337 - INFO - 成功获取宜搭月销售表单数据，共 0 条记录
2025-04-24 19:34:52,337 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 19:34:52,341 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 19:34:52,357 - INFO - 开始批量插入月度汇总数据，共 238 条记录，批量大小: 100
2025-04-24 19:34:52,358 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:34:52,359 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1264, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:34:52,360 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:34:52,360 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1264, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:34:52,360 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:34:52,361 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1264, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:34:52,361 - INFO - 月度汇总数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:34:52,362 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 19:34:52,362 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 19:34:52,362 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 19:34:52,493 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 19:34:52,493 - INFO - 综合数据同步流程完成！
2025-04-24 19:34:52,494 - INFO - 综合数据同步完成
2025-04-24 19:34:52,495 - INFO - ==================================================
2025-04-24 19:34:52,495 - INFO - 程序退出
2025-04-24 19:34:52,495 - INFO - ==================================================
2025-04-24 19:37:27,525 - INFO - ==================================================
2025-04-24 19:37:27,526 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 19:37:27,526 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:37:27,527 - INFO - ==================================================
2025-04-24 19:37:27,527 - INFO - 程序入口点: __main__
2025-04-24 19:37:27,528 - INFO - ==================================================
2025-04-24 19:37:27,528 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 19:37:27,528 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:37:27,528 - INFO - ==================================================
2025-04-24 19:37:27,528 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 19:37:27,529 - INFO - sales_data表已存在，无需创建
2025-04-24 19:37:27,530 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 19:37:27,531 - INFO - DataSyncManager初始化完成
2025-04-24 19:37:27,531 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 19:37:27,531 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 19:37:27,531 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 19:37:27,531 - INFO - 开始综合数据同步流程...
2025-04-24 19:37:27,531 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 19:37:27,532 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 19:37:27,532 - INFO - 正在获取********至********的数据
2025-04-24 19:37:27,533 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 19:37:27,534 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '51094F9F04DCB0962961F95CE21DA5A9'}
2025-04-24 19:37:29,129 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 19:37:29,132 - INFO - 过滤后保留 238 条记录
2025-04-24 19:37:31,136 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 19:37:31,160 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 19:37:31,160 - INFO - - 总记录数: 238
2025-04-24 19:37:31,160 - INFO - - 成功插入: 0
2025-04-24 19:37:31,161 - INFO - - 成功更新: 0
2025-04-24 19:37:31,161 - INFO - - 无需更新: 238
2025-04-24 19:37:31,161 - INFO - - 处理失败: 0
2025-04-24 19:37:31,330 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 19:37:31,330 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 19:37:31,330 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 19:37:31,331 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 19:37:31,356 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 19:37:31,356 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 19:37:31,356 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 19:37:31,357 - ERROR - 获取宜搭表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 928, in get_daily_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:37:31,359 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-04-24 19:37:31,359 - INFO - 开始对比和同步日销售数据...
2025-04-24 19:37:31,359 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-04-24 19:37:31,359 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 19:37:31,359 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 19:37:31,360 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 19:37:31,360 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 19:37:31,360 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 19:37:31,361 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 19:37:31,366 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 19:37:31,370 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 19:37:31,372 - INFO - 开始批量插入日销售数据，共 238 条记录，批量大小: 100
2025-04-24 19:37:31,372 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:37:31,372 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:37:31,373 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:37:31,373 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:37:31,374 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:37:31,375 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:37:31,375 - INFO - 日销售数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:37:31,376 - INFO - 批量插入日销售数据完成，共 238 条记录
2025-04-24 19:37:31,376 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条，跳过: 0 条
2025-04-24 19:37:31,376 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 19:37:31,376 - ERROR - 获取宜搭月度表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 993, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:37:31,377 - INFO - 成功获取宜搭月销售表单数据，共 0 条记录
2025-04-24 19:37:31,377 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 19:37:31,381 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 19:37:31,397 - INFO - 开始批量插入月度汇总数据，共 238 条记录，批量大小: 100
2025-04-24 19:37:31,397 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:37:31,398 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1261, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:37:31,398 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:37:31,398 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1261, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:37:31,399 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:37:31,399 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1261, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:37:31,399 - INFO - 月度汇总数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:37:31,399 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 19:37:31,400 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 19:37:31,400 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 19:37:31,523 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 19:37:31,523 - INFO - 综合数据同步流程完成！
2025-04-24 19:37:31,525 - INFO - 综合数据同步完成
2025-04-24 19:37:31,525 - INFO - ==================================================
2025-04-24 19:37:31,525 - INFO - 程序退出
2025-04-24 19:37:31,525 - INFO - ==================================================
2025-04-24 19:39:49,394 - INFO - ==================================================
2025-04-24 19:39:49,395 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 19:39:49,395 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:39:49,395 - INFO - ==================================================
2025-04-24 19:39:49,396 - INFO - 程序入口点: __main__
2025-04-24 19:39:49,396 - INFO - ==================================================
2025-04-24 19:39:49,396 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 19:39:49,396 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:39:49,396 - INFO - ==================================================
2025-04-24 19:39:49,397 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 19:39:49,398 - INFO - sales_data表已存在，无需创建
2025-04-24 19:39:49,398 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 19:39:49,399 - INFO - DataSyncManager初始化完成
2025-04-24 19:39:49,399 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 19:39:49,399 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 19:39:49,399 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 19:39:49,399 - INFO - 开始综合数据同步流程...
2025-04-24 19:39:49,399 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 19:39:49,400 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 19:39:49,400 - INFO - 正在获取********至********的数据
2025-04-24 19:39:49,401 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 19:39:49,401 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9D9F20EA28AF6A532CB7688D5410BFD7'}
2025-04-24 19:39:50,855 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 19:39:50,858 - INFO - 过滤后保留 238 条记录
2025-04-24 19:39:52,861 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 19:39:52,884 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 19:39:52,884 - INFO - - 总记录数: 238
2025-04-24 19:39:52,885 - INFO - - 成功插入: 0
2025-04-24 19:39:52,885 - INFO - - 成功更新: 0
2025-04-24 19:39:52,885 - INFO - - 无需更新: 238
2025-04-24 19:39:52,886 - INFO - - 处理失败: 0
2025-04-24 19:39:53,061 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 19:39:53,061 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 19:39:53,062 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 19:39:53,062 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 19:39:53,086 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 19:39:53,087 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 19:39:53,087 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 19:39:53,087 - ERROR - 获取宜搭表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 928, in get_daily_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:39:53,089 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-04-24 19:39:53,089 - INFO - 开始对比和同步日销售数据...
2025-04-24 19:39:53,089 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-04-24 19:39:53,090 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 19:39:53,090 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 19:39:53,090 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 19:39:53,090 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 19:39:53,091 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 19:39:53,091 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 19:39:53,096 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 19:39:53,102 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 19:39:53,104 - INFO - 开始批量插入日销售数据，共 238 条记录，批量大小: 100
2025-04-24 19:39:53,105 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:39:53,105 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:39:53,105 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:39:53,106 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:39:53,107 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:39:53,107 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:39:53,108 - INFO - 日销售数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:39:53,108 - INFO - 批量插入日销售数据完成，共 238 条记录
2025-04-24 19:39:53,108 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条，跳过: 0 条
2025-04-24 19:39:53,108 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 19:39:53,108 - ERROR - 获取宜搭月度表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 993, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:39:53,109 - INFO - 成功获取宜搭月销售表单数据，共 0 条记录
2025-04-24 19:39:53,109 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 19:39:53,112 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 19:39:53,127 - INFO - 开始批量插入月度汇总数据，共 238 条记录，批量大小: 100
2025-04-24 19:39:53,127 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:39:53,128 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1256, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:39:53,128 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:39:53,129 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1256, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:39:53,129 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:39:53,129 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1256, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:39:53,130 - INFO - 月度汇总数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:39:53,130 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 19:39:53,130 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 19:39:53,130 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 19:39:53,250 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 19:39:53,250 - INFO - 综合数据同步流程完成！
2025-04-24 19:39:53,251 - INFO - 综合数据同步完成
2025-04-24 19:39:53,251 - INFO - ==================================================
2025-04-24 19:39:53,252 - INFO - 程序退出
2025-04-24 19:39:53,252 - INFO - ==================================================
2025-04-24 19:44:26,435 - INFO - ==================================================
2025-04-24 19:44:26,435 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 19:44:26,435 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:44:26,436 - INFO - ==================================================
2025-04-24 19:44:26,436 - INFO - 程序入口点: __main__
2025-04-24 19:44:26,436 - INFO - ==================================================
2025-04-24 19:44:26,436 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 19:44:26,437 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:44:26,437 - INFO - ==================================================
2025-04-24 19:44:26,437 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 19:44:26,438 - INFO - sales_data表已存在，无需创建
2025-04-24 19:44:26,439 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 19:44:26,439 - INFO - DataSyncManager初始化完成
2025-04-24 19:44:26,440 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 19:44:26,440 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 19:44:26,440 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 19:44:26,440 - INFO - 开始综合数据同步流程...
2025-04-24 19:44:26,440 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 19:44:26,441 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 19:44:26,441 - INFO - 正在获取********至********的数据
2025-04-24 19:44:26,441 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 19:44:26,442 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AC1B4000F1E6A01D64214A1DE1BEEBB0'}
2025-04-24 19:44:28,326 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 19:44:28,329 - INFO - 过滤后保留 238 条记录
2025-04-24 19:44:30,332 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 19:44:30,357 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 19:44:30,358 - INFO - - 总记录数: 238
2025-04-24 19:44:30,358 - INFO - - 成功插入: 0
2025-04-24 19:44:30,359 - INFO - - 成功更新: 0
2025-04-24 19:44:30,359 - INFO - - 无需更新: 238
2025-04-24 19:44:30,359 - INFO - - 处理失败: 0
2025-04-24 19:44:30,530 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 19:44:30,531 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 19:44:30,532 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 19:44:30,532 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 19:44:30,556 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 19:44:30,557 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 19:44:30,557 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 19:44:30,558 - ERROR - 获取宜搭表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 928, in get_daily_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:44:30,559 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-04-24 19:44:30,559 - INFO - 开始对比和同步日销售数据...
2025-04-24 19:44:30,560 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-04-24 19:44:30,560 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 19:44:30,560 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 19:44:30,560 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 19:44:30,561 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 19:44:30,561 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 19:44:30,561 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 19:44:30,566 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 19:44:30,571 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 19:44:30,575 - INFO - 开始批量插入日销售数据，共 238 条记录，批量大小: 100
2025-04-24 19:44:30,576 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:44:30,576 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1192, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:44:30,577 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:44:30,577 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1192, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:44:30,577 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:44:30,578 - ERROR - 批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1192, in batch_insert_daily_form_data
    headers = dingtalkyida__2__0_models.BatchSaveFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchSaveFormDataHeaders'
2025-04-24 19:44:30,578 - INFO - 日销售数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:44:30,578 - INFO - 批量插入日销售数据完成，共 238 条记录
2025-04-24 19:44:30,578 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条，跳过: 0 条
2025-04-24 19:44:30,578 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 19:44:30,579 - INFO - 开始获取宜搭月销售表单数据...
2025-04-24 19:44:30,579 - INFO - 设置搜索条件: 日期范围 ******** 至 ********
2025-04-24 19:44:30,579 - ERROR - 获取宜搭月销售表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1007, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:44:30,579 - ERROR - 获取宜搭月销售表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1559, in sync_data
    yida_monthly_data = self.get_monthly_form_data(start_date, end_date)
  File "D:\yida_program\sync_data_integrated.py", line 1007, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:44:30,579 - WARNING - 将尝试继续处理其他数据...
2025-04-24 19:44:30,579 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 19:44:30,584 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 19:44:30,601 - INFO - 开始批量插入月度汇总数据，共 238 条记录，批量大小: 100
2025-04-24 19:44:30,602 - INFO - 处理批次 1/3，包含 100 条记录
2025-04-24 19:44:30,602 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1267, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:44:30,602 - INFO - 处理批次 2/3，包含 100 条记录
2025-04-24 19:44:30,603 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1267, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:44:30,603 - INFO - 处理批次 3/3，包含 38 条记录
2025-04-24 19:44:30,603 - ERROR - 月度批量插入请求失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1267, in batch_insert_monthly_form_data
    headers = dingtalkyida__2__0_models.BatchInsertFormDataHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'BatchInsertFormDataHeaders'
2025-04-24 19:44:30,603 - INFO - 月度汇总数据批量插入完成！成功请求: 0 条，失败: 238 条
2025-04-24 19:44:30,605 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 19:44:30,605 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 19:44:30,605 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 19:44:30,726 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 19:44:30,726 - INFO - 综合数据同步流程完成！
2025-04-24 19:44:30,727 - INFO - 综合数据同步完成
2025-04-24 19:44:30,728 - INFO - ==================================================
2025-04-24 19:44:30,728 - INFO - 程序退出
2025-04-24 19:44:30,728 - INFO - ==================================================
2025-04-24 19:46:23,473 - INFO - ==================================================
2025-04-24 19:46:23,473 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 19:46:23,473 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:46:23,473 - INFO - ==================================================
2025-04-24 19:46:23,473 - INFO - 程序入口点: __main__
2025-04-24 19:46:23,474 - INFO - ==================================================
2025-04-24 19:46:23,474 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 19:46:23,474 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:46:23,474 - INFO - ==================================================
2025-04-24 19:46:23,474 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 19:46:23,476 - INFO - sales_data表已存在，无需创建
2025-04-24 19:46:23,476 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 19:46:23,477 - INFO - DataSyncManager初始化完成
2025-04-24 19:46:23,477 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 19:46:23,477 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 19:46:23,477 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 19:46:23,477 - INFO - 开始综合数据同步流程...
2025-04-24 19:46:23,478 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 19:46:23,478 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 19:46:23,479 - INFO - 正在获取********至********的数据
2025-04-24 19:46:23,479 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 19:46:23,479 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B1276EF4AE501931D2AA6846B6B84EA3'}
2025-04-24 19:45:18,407 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 19:45:18,410 - INFO - 过滤后保留 238 条记录
2025-04-24 19:45:20,412 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 19:45:20,433 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 19:45:20,433 - INFO - - 总记录数: 238
2025-04-24 19:45:20,434 - INFO - - 成功插入: 0
2025-04-24 19:45:20,434 - INFO - - 成功更新: 0
2025-04-24 19:45:20,434 - INFO - - 无需更新: 238
2025-04-24 19:45:20,434 - INFO - - 处理失败: 0
2025-04-24 19:45:20,619 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 19:45:20,620 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 19:45:20,620 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 19:45:20,620 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 19:45:20,644 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 19:45:20,645 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 19:45:20,645 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 19:45:20,645 - ERROR - 获取宜搭表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 928, in get_daily_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:45:20,647 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-04-24 19:45:20,647 - INFO - 开始对比和同步日销售数据...
2025-04-24 19:45:20,648 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-04-24 19:45:20,648 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 19:45:20,648 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 19:45:20,648 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 19:45:20,649 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 19:45:20,649 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 19:45:20,649 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 19:45:20,655 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 19:45:20,660 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 19:45:20,662 - ERROR - 批量插入请求失败: 'DataSyncManager' object has no attribute '_get_access_token'
2025-04-24 19:45:20,662 - ERROR - 批量插入日销售数据失败: 'DataSyncManager' object has no attribute '_get_access_token'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1511, in sync_data
    self.batch_insert_daily_form_data(daily_insert_list)
  File "D:\yida_program\sync_data_integrated.py", line 1181, in batch_insert_daily_form_data
    headers.x_acs_dingtalk_access_token = self._get_access_token()
AttributeError: 'DataSyncManager' object has no attribute '_get_access_token'
2025-04-24 19:45:20,663 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 238 条，跳过: 0 条
2025-04-24 19:45:20,663 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 19:45:20,663 - INFO - 开始获取宜搭月销售表单数据...
2025-04-24 19:45:20,663 - INFO - 设置搜索条件: 日期范围 ******** 至 ********
2025-04-24 19:45:20,664 - ERROR - 获取宜搭月销售表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1007, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:45:20,665 - ERROR - 获取宜搭月销售表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1523, in sync_data
    yida_monthly_data = self.get_monthly_form_data(start_date, end_date)
  File "D:\yida_program\sync_data_integrated.py", line 1007, in get_monthly_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:45:20,665 - WARNING - 将尝试继续处理其他数据...
2025-04-24 19:45:20,666 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 19:45:20,670 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 19:45:20,685 - ERROR - 批量插入请求失败: 'DataSyncManager' object has no attribute '_get_access_token'
2025-04-24 19:45:20,686 - ERROR - 批量插入月销售数据失败: 'DataSyncManager' object has no attribute '_get_access_token'
2025-04-24 19:45:20,686 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 238 条
2025-04-24 19:45:20,687 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 19:45:20,806 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 19:45:20,807 - INFO - 综合数据同步流程完成！
2025-04-24 19:45:20,808 - INFO - 综合数据同步完成
2025-04-24 19:45:20,808 - INFO - ==================================================
2025-04-24 19:45:20,808 - INFO - 程序退出
2025-04-24 19:45:20,808 - INFO - ==================================================
2025-04-24 19:48:13,736 - INFO - ==================================================
2025-04-24 19:48:13,737 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 19:48:13,737 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:48:13,737 - INFO - ==================================================
2025-04-24 19:48:13,737 - INFO - 程序入口点: __main__
2025-04-24 19:48:13,738 - INFO - ==================================================
2025-04-24 19:48:13,738 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 19:48:13,738 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 19:48:13,738 - INFO - ==================================================
2025-04-24 19:48:13,738 - INFO - 数据库文件已存在: data\sales_data.db
2025-04-24 19:48:13,739 - INFO - sales_data表已存在，无需创建
2025-04-24 19:48:13,740 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-04-24 19:48:13,741 - INFO - DataSyncManager初始化完成
2025-04-24 19:48:13,741 - INFO - 接收到命令行参数: ['********', '********']
2025-04-24 19:48:13,741 - INFO - 使用指定的日期范围: ******** 至 ********
2025-04-24 19:48:13,741 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-04-24 19:48:13,741 - INFO - 开始综合数据同步流程...
2025-04-24 19:48:13,741 - INFO - 正在获取数衍平台日销售数据...
2025-04-24 19:48:13,742 - INFO - 查询数衍平台数据，时间段为: 2025-01-01 00:00:00, 2025-01-01 00:00:00
2025-04-24 19:48:13,742 - INFO - 正在获取********至********的数据
2025-04-24 19:48:13,743 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-04-24 19:48:13,743 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '875C5829987B20FA2CF1DFD050A9C3CD'}
2025-04-24 19:48:15,540 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-04-24 19:48:15,543 - INFO - 过滤后保留 238 条记录
2025-04-24 19:48:17,544 - INFO - 开始保存数据到SQLite数据库，共 238 条记录待处理
2025-04-24 19:48:17,564 - INFO - SQLite数据保存完成，统计信息：
2025-04-24 19:48:17,565 - INFO - - 总记录数: 238
2025-04-24 19:48:17,565 - INFO - - 成功插入: 0
2025-04-24 19:48:17,566 - INFO - - 成功更新: 0
2025-04-24 19:48:17,566 - INFO - - 无需更新: 238
2025-04-24 19:48:17,566 - INFO - - 处理失败: 0
2025-04-24 19:48:17,732 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250424.xlsx
2025-04-24 19:48:17,733 - INFO - 成功获取数衍平台数据，共 238 条记录
2025-04-24 19:48:17,733 - INFO - 正在更新SQLite月度汇总数据...
2025-04-24 19:48:17,734 - INFO - 开始更新月度汇总数据，时间范围: 2025-01-01 至 2025-01-01
2025-04-24 19:48:17,758 - INFO - 月度汇总数据更新完成，处理了 238 条汇总记录
2025-04-24 19:48:17,759 - INFO - 成功更新月度汇总数据，共 238 条记录
2025-04-24 19:48:17,759 - INFO - 正在获取宜搭日销售表单数据...
2025-04-24 19:48:17,760 - ERROR - 获取宜搭表单数据失败: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 928, in get_daily_form_data
    headers = dingtalkyida__2__0_models.GetFormDataByPageHeaders()
AttributeError: module 'alibabacloud_dingtalk.yida_2_0.models' has no attribute 'GetFormDataByPageHeaders'
2025-04-24 19:48:17,762 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-04-24 19:48:17,763 - INFO - 开始对比和同步日销售数据...
2025-04-24 19:48:17,763 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-04-24 19:48:17,763 - INFO - 开始处理数衍数据，共 238 条记录
2025-04-24 19:48:17,763 - INFO - 正在处理第 1/238 条数衍数据
2025-04-24 19:48:17,764 - INFO - 正在处理第 2/238 条数衍数据
2025-04-24 19:48:17,764 - INFO - 正在处理第 3/238 条数衍数据
2025-04-24 19:48:17,765 - INFO - 正在处理第 4/238 条数衍数据
2025-04-24 19:48:17,765 - INFO - 正在处理第 5/238 条数衍数据
2025-04-24 19:48:17,773 - INFO - 正在处理第 101/238 条数衍数据
2025-04-24 19:48:17,780 - INFO - 正在处理第 201/238 条数衍数据
2025-04-24 19:48:17,782 - ERROR - 批量插入请求失败: 'DataSyncManager' object has no attribute '_get_access_token'
2025-04-24 19:48:17,782 - ERROR - 批量插入日销售数据失败: 'DataSyncManager' object has no attribute '_get_access_token'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1472, in sync_data
    self.batch_insert_daily_form_data(daily_insert_list)
  File "D:\yida_program\sync_data_integrated.py", line 1162, in batch_insert_daily_form_data
    headers.x_acs_dingtalk_access_token = self._get_access_token()
AttributeError: 'DataSyncManager' object has no attribute '_get_access_token'
2025-04-24 19:48:17,783 - INFO - 日销售数据同步完成！更新: 0 条，插入: 238 条，错误: 238 条，跳过: 0 条
2025-04-24 19:48:17,783 - INFO - 正在获取宜搭月销售表单数据...
2025-04-24 19:48:17,783 - INFO - 开始获取月度表单数据: ******** 至 ********
2025-04-24 19:48:17,783 - ERROR - 获取月度表单数据时发生错误: 'DataSyncManager' object has no attribute 'yida_client'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 993, in get_monthly_form_data
    client = self.yida_client._create_client()
AttributeError: 'DataSyncManager' object has no attribute 'yida_client'
2025-04-24 19:48:17,784 - INFO - 成功获取宜搭月销售表单数据，共 0 条记录
2025-04-24 19:48:17,784 - INFO - 正在从SQLite获取月度汇总数据...
2025-04-24 19:48:17,788 - INFO - 成功获取SQLite月度汇总数据，共 238 条记录
2025-04-24 19:48:17,803 - ERROR - 批量插入月度表单数据时发生错误: 'DataSyncManager' object has no attribute 'yida_client'
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1208, in batch_insert_monthly_form_data
    client = self.yida_client._create_client()
AttributeError: 'DataSyncManager' object has no attribute 'yida_client'
2025-04-24 19:48:17,803 - INFO - 批量插入月销售数据完成，共 238 条记录
2025-04-24 19:48:17,803 - INFO - 月销售数据同步完成！更新: 0 条，插入: 238 条，错误: 0 条
2025-04-24 19:48:17,804 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-1 至 2025-1
2025-04-24 19:48:17,926 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250424.xlsx
2025-04-24 19:48:17,927 - INFO - 综合数据同步流程完成！
2025-04-24 19:48:17,928 - INFO - 综合数据同步完成
2025-04-24 19:48:17,929 - INFO - ==================================================
2025-04-24 19:48:17,929 - INFO - 程序退出
2025-04-24 19:48:17,929 - INFO - ==================================================
2025-04-24 23:28:24,302 - INFO - ==================================================
2025-04-24 23:28:24,303 - INFO - 程序启动 - 版本 v1.0.0
2025-04-24 23:28:24,303 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 23:28:24,303 - INFO - ==================================================
2025-04-24 23:28:24,304 - INFO - 程序入口点: __main__
2025-04-24 23:28:24,304 - INFO - ==================================================
2025-04-24 23:28:24,304 - INFO - 程序启动 - 版本 v1.0.1
2025-04-24 23:28:24,304 - INFO - 日志文件: logs\sync_data_20250424.log
2025-04-24 23:28:24,304 - INFO - ==================================================
2025-04-24 23:28:24,706 - ERROR - 创建宜搭API客户端失败: name 'dingtalkyida_2_0Client' is not defined
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 164, in _create_client
    return dingtalkyida_2_0Client(config)
NameError: name 'dingtalkyida_2_0Client' is not defined
2025-04-24 23:28:24,708 - ERROR - 程序执行失败: name 'dingtalkyida_2_0Client' is not defined
Traceback (most recent call last):
  File "D:\yida_program\sync_data_integrated.py", line 1821, in main
    sync_manager = DataSyncManager()
  File "D:\yida_program\sync_data_integrated.py", line 405, in __init__
    self.yida_client = YidaFormDataClient(YIDA_CONFIG, is_monthly=False)
  File "D:\yida_program\sync_data_integrated.py", line 147, in __init__
    self.client = self._create_client()
  File "D:\yida_program\sync_data_integrated.py", line 164, in _create_client
    return dingtalkyida_2_0Client(config)
NameError: name 'dingtalkyida_2_0Client' is not defined
2025-04-24 23:28:24,708 - INFO - ==================================================
2025-04-24 23:28:24,709 - INFO - 程序退出
2025-04-24 23:28:24,709 - INFO - ==================================================
