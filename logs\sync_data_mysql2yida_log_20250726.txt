2025-07-26 01:30:33,852 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 01:30:33,852 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 01:30:33,852 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 01:30:34,009 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 3 条记录
2025-07-26 01:30:34,009 - INFO - 获取到 1 个日期需要处理: ['2025-07-25']
2025-07-26 01:30:34,009 - INFO - 开始处理日期: 2025-07-25
2025-07-26 01:30:34,009 - INFO - Request Parameters - Page 1:
2025-07-26 01:30:34,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 01:30:34,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 01:30:42,118 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 03513457-68D1-70A5-8A7A-278130331A78 Response: {'code': 'ServiceUnavailable', 'requestid': '03513457-68D1-70A5-8A7A-278130331A78', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 03513457-68D1-70A5-8A7A-278130331A78)
2025-07-26 01:30:42,118 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-26 01:31:42,149 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 01:31:42,149 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 01:31:42,149 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 01:31:42,289 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 122 条记录
2025-07-26 01:31:42,289 - INFO - 获取到 1 个日期需要处理: ['2025-07-25']
2025-07-26 01:31:42,289 - INFO - 开始处理日期: 2025-07-25
2025-07-26 01:31:42,289 - INFO - Request Parameters - Page 1:
2025-07-26 01:31:42,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 01:31:42,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 01:31:50,164 - INFO - Response - Page 1:
2025-07-26 01:31:50,164 - INFO - 第 1 页获取到 50 条记录
2025-07-26 01:31:50,664 - INFO - Request Parameters - Page 2:
2025-07-26 01:31:50,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 01:31:50,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 01:31:51,383 - INFO - Response - Page 2:
2025-07-26 01:31:51,383 - INFO - 第 2 页获取到 37 条记录
2025-07-26 01:31:51,899 - INFO - 查询完成，共获取到 87 条记录
2025-07-26 01:31:51,899 - INFO - 获取到 87 条表单数据
2025-07-26 01:31:51,899 - INFO - 当前日期 2025-07-25 有 119 条MySQL数据需要处理
2025-07-26 01:31:51,899 - INFO - 开始批量插入 32 条新记录
2025-07-26 01:31:52,149 - INFO - 批量插入响应状态码: 200
2025-07-26 01:31:52,149 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 25 Jul 2025 17:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1548', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D7CD5261-FFF6-7657-AF67-27C338EEC1A3', 'x-acs-trace-id': '848409afc3bee0b7016882d777786cc4', 'etag': '14gycDhPIqVSv6PfzfYyfIQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 01:31:52,149 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMZD', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM0E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM1E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM2E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM3E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM4E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM5E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM6E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM7E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM8E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM9E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMAE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMBE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMCE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMDE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMEE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMFE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMGE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMHE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMIE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMJE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMKE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMLE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMME', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMNE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMOE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMPE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMQE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMRE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMSE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMTE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMUE']}
2025-07-26 01:31:52,149 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-07-26 01:31:52,149 - INFO - 成功插入的数据ID: ['FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMZD', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM0E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM1E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM2E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM3E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM4E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM5E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM6E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM7E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM8E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDM9E', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMAE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMBE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMCE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMDE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMEE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMFE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMGE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMHE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMIE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMJE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMKE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMLE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMME', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMNE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMOE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMPE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMQE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMRE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMSE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMTE', 'FINST-QUA66S71IHGXDXRN93WQ04HLN9A32CP6M3JDMUE']
2025-07-26 01:31:57,164 - INFO - 批量插入完成，共 32 条记录
2025-07-26 01:31:57,164 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 32 条，错误: 0 条
2025-07-26 01:31:57,164 - INFO - 数据同步完成！更新: 0 条，插入: 32 条，错误: 0 条
2025-07-26 01:31:57,164 - INFO - 同步完成
2025-07-26 04:30:33,826 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 04:30:33,826 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 04:30:33,826 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 04:30:33,982 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 3 条记录
2025-07-26 04:30:33,982 - INFO - 获取到 1 个日期需要处理: ['2025-07-25']
2025-07-26 04:30:33,982 - INFO - 开始处理日期: 2025-07-25
2025-07-26 04:30:33,982 - INFO - Request Parameters - Page 1:
2025-07-26 04:30:33,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 04:30:33,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 04:30:42,091 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 440102C8-4E50-7B76-89F5-CCFE8B00CFAB Response: {'code': 'ServiceUnavailable', 'requestid': '440102C8-4E50-7B76-89F5-CCFE8B00CFAB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 440102C8-4E50-7B76-89F5-CCFE8B00CFAB)
2025-07-26 04:30:42,091 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-26 04:31:42,107 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 04:31:42,107 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 04:31:42,107 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 04:31:42,247 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 122 条记录
2025-07-26 04:31:42,247 - INFO - 获取到 1 个日期需要处理: ['2025-07-25']
2025-07-26 04:31:42,263 - INFO - 开始处理日期: 2025-07-25
2025-07-26 04:31:42,263 - INFO - Request Parameters - Page 1:
2025-07-26 04:31:42,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 04:31:42,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 04:31:43,044 - INFO - Response - Page 1:
2025-07-26 04:31:43,044 - INFO - 第 1 页获取到 50 条记录
2025-07-26 04:31:43,544 - INFO - Request Parameters - Page 2:
2025-07-26 04:31:43,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 04:31:43,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 04:31:51,060 - INFO - Response - Page 2:
2025-07-26 04:31:51,060 - INFO - 第 2 页获取到 50 条记录
2025-07-26 04:31:51,560 - INFO - Request Parameters - Page 3:
2025-07-26 04:31:51,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 04:31:51,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 04:31:52,185 - INFO - Response - Page 3:
2025-07-26 04:31:52,185 - INFO - 第 3 页获取到 19 条记录
2025-07-26 04:31:52,685 - INFO - 查询完成，共获取到 119 条记录
2025-07-26 04:31:52,685 - INFO - 获取到 119 条表单数据
2025-07-26 04:31:52,685 - INFO - 当前日期 2025-07-25 有 119 条MySQL数据需要处理
2025-07-26 04:31:52,685 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 04:31:52,685 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 04:31:52,685 - INFO - 同步完成
2025-07-26 07:30:33,830 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 07:30:33,830 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 07:30:33,830 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 07:30:34,002 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 3 条记录
2025-07-26 07:30:34,002 - INFO - 获取到 1 个日期需要处理: ['2025-07-25']
2025-07-26 07:30:34,002 - INFO - 开始处理日期: 2025-07-25
2025-07-26 07:30:34,002 - INFO - Request Parameters - Page 1:
2025-07-26 07:30:34,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 07:30:34,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 07:30:41,877 - INFO - Response - Page 1:
2025-07-26 07:30:41,877 - INFO - 第 1 页获取到 50 条记录
2025-07-26 07:30:42,393 - INFO - Request Parameters - Page 2:
2025-07-26 07:30:42,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 07:30:42,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 07:30:50,502 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0FF7856A-6490-78BB-BC36-06971EA9E2B1 Response: {'code': 'ServiceUnavailable', 'requestid': '0FF7856A-6490-78BB-BC36-06971EA9E2B1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0FF7856A-6490-78BB-BC36-06971EA9E2B1)
2025-07-26 07:30:50,502 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-26 07:31:50,517 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 07:31:50,517 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 07:31:50,517 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 07:31:50,658 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 122 条记录
2025-07-26 07:31:50,658 - INFO - 获取到 1 个日期需要处理: ['2025-07-25']
2025-07-26 07:31:50,673 - INFO - 开始处理日期: 2025-07-25
2025-07-26 07:31:50,673 - INFO - Request Parameters - Page 1:
2025-07-26 07:31:50,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 07:31:50,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 07:31:51,455 - INFO - Response - Page 1:
2025-07-26 07:31:51,455 - INFO - 第 1 页获取到 50 条记录
2025-07-26 07:31:51,970 - INFO - Request Parameters - Page 2:
2025-07-26 07:31:51,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 07:31:51,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 07:31:52,673 - INFO - Response - Page 2:
2025-07-26 07:31:52,673 - INFO - 第 2 页获取到 50 条记录
2025-07-26 07:31:53,189 - INFO - Request Parameters - Page 3:
2025-07-26 07:31:53,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 07:31:53,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 07:31:53,830 - INFO - Response - Page 3:
2025-07-26 07:31:53,830 - INFO - 第 3 页获取到 19 条记录
2025-07-26 07:31:54,330 - INFO - 查询完成，共获取到 119 条记录
2025-07-26 07:31:54,330 - INFO - 获取到 119 条表单数据
2025-07-26 07:31:54,330 - INFO - 当前日期 2025-07-25 有 119 条MySQL数据需要处理
2025-07-26 07:31:54,330 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 07:31:54,330 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 07:31:54,330 - INFO - 同步完成
2025-07-26 10:30:33,522 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 10:30:33,522 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 10:30:33,522 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 10:30:33,679 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 116 条记录
2025-07-26 10:30:33,679 - INFO - 获取到 3 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26']
2025-07-26 10:30:33,679 - INFO - 开始处理日期: 2025-07-24
2025-07-26 10:30:33,679 - INFO - Request Parameters - Page 1:
2025-07-26 10:30:33,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:30:33,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:30:41,835 - INFO - Response - Page 1:
2025-07-26 10:30:41,835 - INFO - 第 1 页获取到 50 条记录
2025-07-26 10:30:42,335 - INFO - Request Parameters - Page 2:
2025-07-26 10:30:42,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:30:42,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:30:50,428 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EFE0A250-A94D-7E5C-BF65-D71823CFF375 Response: {'code': 'ServiceUnavailable', 'requestid': 'EFE0A250-A94D-7E5C-BF65-D71823CFF375', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EFE0A250-A94D-7E5C-BF65-D71823CFF375)
2025-07-26 10:30:50,428 - INFO - 开始处理日期: 2025-07-25
2025-07-26 10:30:50,428 - INFO - Request Parameters - Page 1:
2025-07-26 10:30:50,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:30:50,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:30:51,132 - INFO - Response - Page 1:
2025-07-26 10:30:51,132 - INFO - 第 1 页获取到 50 条记录
2025-07-26 10:30:51,647 - INFO - Request Parameters - Page 2:
2025-07-26 10:30:51,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:30:51,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:30:54,741 - INFO - Response - Page 2:
2025-07-26 10:30:54,741 - INFO - 第 2 页获取到 50 条记录
2025-07-26 10:30:55,241 - INFO - Request Parameters - Page 3:
2025-07-26 10:30:55,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:30:55,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:30:55,866 - INFO - Response - Page 3:
2025-07-26 10:30:55,866 - INFO - 第 3 页获取到 19 条记录
2025-07-26 10:30:56,382 - INFO - 查询完成，共获取到 119 条记录
2025-07-26 10:30:56,382 - INFO - 获取到 119 条表单数据
2025-07-26 10:30:56,382 - INFO - 当前日期 2025-07-25 有 113 条MySQL数据需要处理
2025-07-26 10:30:56,382 - INFO - 开始批量插入 110 条新记录
2025-07-26 10:30:56,647 - INFO - 批量插入响应状态码: 200
2025-07-26 10:30:56,647 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '81B9AC4C-1A8E-7876-9103-B816ABFFC8F5', 'x-acs-trace-id': 'a161954013cfc7dc77cc4f5b71b277cc', 'etag': '2t5wMB2s+tkMOwQQp7/7Q8A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:30:56,647 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMCN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMDN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMEN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMFN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMGN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMHN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMIN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMJN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMKN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMLN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMMN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMNN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMON', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMPN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMQN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMRN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMSN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMTN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMUN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMVN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMWN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMXN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMYN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMZN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM0O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM1O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM2O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM3O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM4O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM5O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM6O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM7O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM8O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM9O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMAO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMBO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMCO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMDO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMEO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMFO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMGO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMHO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMIO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMJO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMKO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMLO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMMO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMNO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMOO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMPO']}
2025-07-26 10:30:56,647 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-26 10:30:56,647 - INFO - 成功插入的数据ID: ['FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMCN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMDN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMEN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMFN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMGN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMHN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMIN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMJN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMKN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMLN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMMN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMNN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMON', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMPN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMQN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMRN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMSN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMTN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMUN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMVN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMWN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMXN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMYN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMZN', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM0O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM1O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM2O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM3O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM4O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM5O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM6O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM7O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM8O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDM9O', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMAO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMBO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMCO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMDO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMEO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMFO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMGO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMHO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMIO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMJO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMKO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMLO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMMO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMNO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMOO', 'FINST-TQB66671ZCFXCLJVD6GK1DH3XL202P1GVMJDMPO']
2025-07-26 10:31:01,928 - INFO - 批量插入响应状态码: 200
2025-07-26 10:31:01,928 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '60242E1D-A115-70F3-9FD8-67BEE58C7A25', 'x-acs-trace-id': '13cd84175f24514d54e71668bc7e9549', 'etag': '2TV8Hbkjiks8pAYwRTvhf0w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:31:01,928 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM7A', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM8A', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM9A', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMAA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMBA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMCA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMDA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMEA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMFA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMGA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMHA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMIA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMJA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMKA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMLA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMMA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMNA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMOA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMPA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMQA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMRA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMSA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMTA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMUA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMVA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMWA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMXA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMYA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMZA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM0B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM1B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM2B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM3B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM4B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM5B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM6B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM7B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM8B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM9B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMAB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMBB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMCB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMDB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMEB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMFB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMGB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMHB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMIB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMJB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMKB']}
2025-07-26 10:31:01,928 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-26 10:31:01,928 - INFO - 成功插入的数据ID: ['FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM7A', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM8A', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM9A', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMAA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMBA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMCA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMDA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMEA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMFA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMGA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMHA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMIA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMJA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMKA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMLA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMMA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMNA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMOA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMPA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMQA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMRA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMSA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMTA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMUA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMVA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMWA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMXA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMYA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMZA', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM0B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM1B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM2B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM3B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM4B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM5B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM6B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM7B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM8B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDM9B', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMAB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMBB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMCB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMDB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMEB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMFB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMGB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMHB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMIB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMJB', 'FINST-XMC66R913EEXDDN0ASC4V4102A2O234KVMJDMKB']
2025-07-26 10:31:07,116 - INFO - 批量插入响应状态码: 200
2025-07-26 10:31:07,116 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '492', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BB446D60-DAF4-7AB3-BA02-2D85A2C47049', 'x-acs-trace-id': '445d2779d242600e2647f3c885055204', 'etag': '4KYRFlaQGUurCRSb+DUve7A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:31:07,116 - INFO - 批量插入响应体: {'result': ['FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMOM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMPM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMQM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMRM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMSM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMTM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMUM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMVM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMWM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMXM']}
2025-07-26 10:31:07,116 - INFO - 批量插入表单数据成功，批次 3，共 10 条记录
2025-07-26 10:31:07,116 - INFO - 成功插入的数据ID: ['FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMOM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMPM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMQM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMRM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMSM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMTM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMUM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMVM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMWM', 'FINST-G9G669D14LGXIQ29DRXDS7293WIN2B4OVMJDMXM']
2025-07-26 10:31:12,131 - INFO - 批量插入完成，共 110 条记录
2025-07-26 10:31:12,131 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 110 条，错误: 0 条
2025-07-26 10:31:12,131 - INFO - 开始处理日期: 2025-07-26
2025-07-26 10:31:12,131 - INFO - Request Parameters - Page 1:
2025-07-26 10:31:12,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:31:12,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:31:12,694 - INFO - Response - Page 1:
2025-07-26 10:31:12,694 - INFO - 查询完成，共获取到 0 条记录
2025-07-26 10:31:12,694 - INFO - 获取到 0 条表单数据
2025-07-26 10:31:12,694 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 10:31:12,694 - INFO - 开始批量插入 1 条新记录
2025-07-26 10:31:12,866 - INFO - 批量插入响应状态码: 200
2025-07-26 10:31:12,866 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:31:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15E736EB-E16C-79AE-BFC7-76BDBFAB5A9D', 'x-acs-trace-id': 'e02095e4d1b62b433563252e4f30f422', 'etag': '6A7TVHyuKg55a+1oVFAFgdg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:31:12,866 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC13HFXJS2963KPCBG8A9YV33KSVMJDMOJ']}
2025-07-26 10:31:12,866 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-26 10:31:12,866 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC13HFXJS2963KPCBG8A9YV33KSVMJDMOJ']
2025-07-26 10:31:17,881 - INFO - 批量插入完成，共 1 条记录
2025-07-26 10:31:17,881 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-26 10:31:17,881 - INFO - 数据同步完成！更新: 0 条，插入: 111 条，错误: 1 条
2025-07-26 10:32:17,897 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 10:32:17,897 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 10:32:17,897 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 10:32:18,053 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 404 条记录
2025-07-26 10:32:18,053 - INFO - 获取到 2 个日期需要处理: ['2025-07-25', '2025-07-26']
2025-07-26 10:32:18,053 - INFO - 开始处理日期: 2025-07-25
2025-07-26 10:32:18,053 - INFO - Request Parameters - Page 1:
2025-07-26 10:32:18,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:32:18,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:32:18,818 - INFO - Response - Page 1:
2025-07-26 10:32:18,818 - INFO - 第 1 页获取到 50 条记录
2025-07-26 10:32:19,318 - INFO - Request Parameters - Page 2:
2025-07-26 10:32:19,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:32:19,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:32:20,037 - INFO - Response - Page 2:
2025-07-26 10:32:20,037 - INFO - 第 2 页获取到 50 条记录
2025-07-26 10:32:20,537 - INFO - Request Parameters - Page 3:
2025-07-26 10:32:20,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:32:20,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:32:21,287 - INFO - Response - Page 3:
2025-07-26 10:32:21,287 - INFO - 第 3 页获取到 50 条记录
2025-07-26 10:32:21,803 - INFO - Request Parameters - Page 4:
2025-07-26 10:32:21,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:32:21,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:32:22,553 - INFO - Response - Page 4:
2025-07-26 10:32:22,553 - INFO - 第 4 页获取到 50 条记录
2025-07-26 10:32:23,068 - INFO - Request Parameters - Page 5:
2025-07-26 10:32:23,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:32:23,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:32:23,834 - INFO - Response - Page 5:
2025-07-26 10:32:23,834 - INFO - 第 5 页获取到 29 条记录
2025-07-26 10:32:24,350 - INFO - 查询完成，共获取到 229 条记录
2025-07-26 10:32:24,350 - INFO - 获取到 229 条表单数据
2025-07-26 10:32:24,350 - INFO - 当前日期 2025-07-25 有 395 条MySQL数据需要处理
2025-07-26 10:32:24,350 - INFO - 开始批量插入 166 条新记录
2025-07-26 10:32:24,584 - INFO - 批量插入响应状态码: 200
2025-07-26 10:32:24,584 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5A2A110E-92D8-72AC-8AAE-23B18BDF1DF3', 'x-acs-trace-id': '9cd47f4ed8abe321fd3e2bf28bd10edf', 'etag': '22FwOJOrxPoxd5lOrtWlxZA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:32:24,584 - INFO - 批量插入响应体: {'result': ['FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMG91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMH91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMI91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMJ91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMK91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDML91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMM91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMN91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMO91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMP91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMQ91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMR91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMS91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMT91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMU91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMV91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMW91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMX91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMY91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMZ91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM0A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM1A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM2A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM3A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM4A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM5A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM6A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM7A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM8A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM9A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMAA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMBA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMCA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMDA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMEA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMFA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMGA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMHA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMIA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMJA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMKA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMLA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMMA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMNA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMOA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMPA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMQA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMRA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMSA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMTA1']}
2025-07-26 10:32:24,584 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-26 10:32:24,584 - INFO - 成功插入的数据ID: ['FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMG91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMH91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMI91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMJ91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMK91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDML91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMM91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMN91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMO91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMP91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMQ91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMR91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMS91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMT91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMU91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMV91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMW91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMX91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMY91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMZ91', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM0A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM1A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM2A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM3A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM4A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM5A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM6A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM7A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM8A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDM9A1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMAA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMBA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMCA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMDA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMEA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMFA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMGA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMHA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMIA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMJA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMKA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMLA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMMA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMNA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMOA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMPA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMQA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMRA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMSA1', 'FINST-BTF66DB1APFXX104DEZ72A0GJXLH2OWBXMJDMTA1']
2025-07-26 10:32:29,834 - INFO - 批量插入响应状态码: 200
2025-07-26 10:32:29,834 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B7DC6270-71C8-7D19-A140-FD379A3A071A', 'x-acs-trace-id': '2399fac2de026f216b2a356ad9484082', 'etag': '2FGixK5GqzX+WIQtOMpoYeQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:32:29,834 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMKI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMLI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMMI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMNI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMOI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMPI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMQI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMRI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMSI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMTI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMUI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMVI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMWI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMXI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMYI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMZI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM0J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM1J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM2J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM3J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM4J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM5J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM6J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM7J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM8J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM9J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMAJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMBJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMCJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMDJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMEJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMFJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMGJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMHJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMIJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMJJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMKJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMLJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMMJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMNJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMOJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMPJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMQJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMRJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMSJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMTJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMUJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMVJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMWJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMXJ']}
2025-07-26 10:32:29,834 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-26 10:32:29,834 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMKI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMLI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMMI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMNI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMOI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMPI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMQI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMRI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMSI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMTI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMUI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMVI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMWI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMXI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMYI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMZI', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM0J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM1J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM2J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM3J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM4J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM5J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM6J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM7J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM8J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDM9J', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMAJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMBJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMCJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMDJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMEJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMFJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMGJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMHJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMIJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMJJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMKJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMLJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMMJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMNJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMOJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMPJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMQJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMRJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMSJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMTJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMUJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMVJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMWJ', 'FINST-7PF66CC1GEGXULEVC58L2DWIXKFY2GYFXMJDMXJ']
2025-07-26 10:32:35,100 - INFO - 批量插入响应状态码: 200
2025-07-26 10:32:35,100 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7E3D54F0-125F-7F03-8FD6-82198435337D', 'x-acs-trace-id': '4defcc4c30ff6398abb948f6d390071f', 'etag': '2CQOX7jr7WOck2Dc4AxuI6g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:32:35,100 - INFO - 批量插入响应体: {'result': ['FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM961', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMA61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMB61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMC61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMD61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDME61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMF61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMG61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMH61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMI61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMJ61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMK61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDML61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMM61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMN61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMO61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMP61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMQ61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMR61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMS61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMT61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMU61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMV61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMW61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMX61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMY61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMZ61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM071', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM171', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM271', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM371', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM471', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM571', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM671', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM771', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM871', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM971', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMA71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMB71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMC71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMD71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDME71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMF71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMG71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMH71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMI71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMJ71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMK71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDML71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMM71']}
2025-07-26 10:32:35,100 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-26 10:32:35,100 - INFO - 成功插入的数据ID: ['FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM961', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMA61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMB61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMC61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMD61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDME61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMF61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMG61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMH61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMI61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMJ61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMK61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDML61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMM61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMN61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMO61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMP61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMQ61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMR61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMS61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMT61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMU61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMV61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMW61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMX61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMY61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMZ61', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM071', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM171', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM271', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM371', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM471', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM571', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM671', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM771', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM871', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDM971', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMA71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMB71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMC71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMD71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDME71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMF71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMG71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMH71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMI71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMJ71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMK71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDML71', 'FINST-V7966QC17IEXEQN3AZGAV8RFIJ1R2I0KXMJDMM71']
2025-07-26 10:32:40,287 - INFO - 批量插入响应状态码: 200
2025-07-26 10:32:40,287 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 02:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '780', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A7884C52-25B7-7E35-8DB6-98D94C82BA5E', 'x-acs-trace-id': 'f4c93c4edc59693f861e208fb0068a94', 'etag': '7FXkOzLKR4FYoERCZSUQkMA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 10:32:40,287 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMJG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMKG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMLG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMMG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMNG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMOG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMPG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMQG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMRG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMSG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMTG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMUG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMVG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMWG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMXG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMYG']}
2025-07-26 10:32:40,287 - INFO - 批量插入表单数据成功，批次 4，共 16 条记录
2025-07-26 10:32:40,287 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMJG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMKG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMLG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMMG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMNG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMOG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMPG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMQG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMRG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMSG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMTG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMUG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMVG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMWG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMXG', 'FINST-AAG66KB1YFGXGGQRFQY2SC79ZJ3C3N0OXMJDMYG']
2025-07-26 10:32:45,303 - INFO - 批量插入完成，共 166 条记录
2025-07-26 10:32:45,303 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 166 条，错误: 0 条
2025-07-26 10:32:45,303 - INFO - 开始处理日期: 2025-07-26
2025-07-26 10:32:45,303 - INFO - Request Parameters - Page 1:
2025-07-26 10:32:45,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 10:32:45,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 10:32:45,787 - INFO - Response - Page 1:
2025-07-26 10:32:45,787 - INFO - 第 1 页获取到 1 条记录
2025-07-26 10:32:46,303 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 10:32:46,303 - INFO - 获取到 1 条表单数据
2025-07-26 10:32:46,303 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 10:32:46,303 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 10:32:46,303 - INFO - 数据同步完成！更新: 0 条，插入: 166 条，错误: 0 条
2025-07-26 10:32:46,303 - INFO - 同步完成
2025-07-26 13:30:33,839 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 13:30:33,839 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 13:30:33,839 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 13:30:34,011 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 121 条记录
2025-07-26 13:30:34,011 - INFO - 获取到 3 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26']
2025-07-26 13:30:34,011 - INFO - 开始处理日期: 2025-07-24
2025-07-26 13:30:34,011 - INFO - Request Parameters - Page 1:
2025-07-26 13:30:34,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:30:34,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:30:42,136 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E06AB485-42D8-7E9A-A51A-1032BAE9B7B6 Response: {'code': 'ServiceUnavailable', 'requestid': 'E06AB485-42D8-7E9A-A51A-1032BAE9B7B6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E06AB485-42D8-7E9A-A51A-1032BAE9B7B6)
2025-07-26 13:30:42,136 - INFO - 开始处理日期: 2025-07-25
2025-07-26 13:30:42,136 - INFO - Request Parameters - Page 1:
2025-07-26 13:30:42,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:30:42,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:30:42,917 - INFO - Response - Page 1:
2025-07-26 13:30:42,917 - INFO - 第 1 页获取到 50 条记录
2025-07-26 13:30:43,433 - INFO - Request Parameters - Page 2:
2025-07-26 13:30:43,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:30:43,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:30:44,152 - INFO - Response - Page 2:
2025-07-26 13:30:44,152 - INFO - 第 2 页获取到 50 条记录
2025-07-26 13:30:44,667 - INFO - Request Parameters - Page 3:
2025-07-26 13:30:44,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:30:44,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:30:52,776 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6C8E58BF-165E-7FD6-81A0-B3A81AA07BAF Response: {'code': 'ServiceUnavailable', 'requestid': '6C8E58BF-165E-7FD6-81A0-B3A81AA07BAF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6C8E58BF-165E-7FD6-81A0-B3A81AA07BAF)
2025-07-26 13:30:52,776 - INFO - 开始处理日期: 2025-07-26
2025-07-26 13:30:52,776 - INFO - Request Parameters - Page 1:
2025-07-26 13:30:52,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:30:52,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:30:53,261 - INFO - Response - Page 1:
2025-07-26 13:30:53,261 - INFO - 第 1 页获取到 1 条记录
2025-07-26 13:30:53,776 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 13:30:53,776 - INFO - 获取到 1 条表单数据
2025-07-26 13:30:53,776 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 13:30:53,776 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 13:30:53,776 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-26 13:31:53,792 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 13:31:53,792 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 13:31:53,792 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 13:31:53,948 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 409 条记录
2025-07-26 13:31:53,948 - INFO - 获取到 2 个日期需要处理: ['2025-07-25', '2025-07-26']
2025-07-26 13:31:53,964 - INFO - 开始处理日期: 2025-07-25
2025-07-26 13:31:53,964 - INFO - Request Parameters - Page 1:
2025-07-26 13:31:53,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:31:53,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:31:54,745 - INFO - Response - Page 1:
2025-07-26 13:31:54,745 - INFO - 第 1 页获取到 50 条记录
2025-07-26 13:31:55,260 - INFO - Request Parameters - Page 2:
2025-07-26 13:31:55,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:31:55,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:31:56,010 - INFO - Response - Page 2:
2025-07-26 13:31:56,010 - INFO - 第 2 页获取到 50 条记录
2025-07-26 13:31:56,526 - INFO - Request Parameters - Page 3:
2025-07-26 13:31:56,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:31:56,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:31:57,214 - INFO - Response - Page 3:
2025-07-26 13:31:57,214 - INFO - 第 3 页获取到 50 条记录
2025-07-26 13:31:57,729 - INFO - Request Parameters - Page 4:
2025-07-26 13:31:57,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:31:57,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:31:58,448 - INFO - Response - Page 4:
2025-07-26 13:31:58,448 - INFO - 第 4 页获取到 50 条记录
2025-07-26 13:31:58,948 - INFO - Request Parameters - Page 5:
2025-07-26 13:31:58,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:31:58,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:31:59,682 - INFO - Response - Page 5:
2025-07-26 13:31:59,682 - INFO - 第 5 页获取到 50 条记录
2025-07-26 13:32:00,198 - INFO - Request Parameters - Page 6:
2025-07-26 13:32:00,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:32:00,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:32:00,901 - INFO - Response - Page 6:
2025-07-26 13:32:00,901 - INFO - 第 6 页获取到 50 条记录
2025-07-26 13:32:01,401 - INFO - Request Parameters - Page 7:
2025-07-26 13:32:01,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:32:01,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:32:02,214 - INFO - Response - Page 7:
2025-07-26 13:32:02,214 - INFO - 第 7 页获取到 50 条记录
2025-07-26 13:32:02,729 - INFO - Request Parameters - Page 8:
2025-07-26 13:32:02,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:32:02,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:32:03,557 - INFO - Response - Page 8:
2025-07-26 13:32:03,557 - INFO - 第 8 页获取到 45 条记录
2025-07-26 13:32:04,057 - INFO - 查询完成，共获取到 395 条记录
2025-07-26 13:32:04,057 - INFO - 获取到 395 条表单数据
2025-07-26 13:32:04,057 - INFO - 当前日期 2025-07-25 有 400 条MySQL数据需要处理
2025-07-26 13:32:04,073 - INFO - 开始批量插入 5 条新记录
2025-07-26 13:32:04,229 - INFO - 批量插入响应状态码: 200
2025-07-26 13:32:04,229 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 05:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DD6C367E-2738-708A-A79F-40C30722B61D', 'x-acs-trace-id': '9223f070fa9eda9439c0f33155e9ee39', 'etag': '2DjI6nlKbsWXXEvGqvoLNsA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 13:32:04,229 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMKB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMLB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMMB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMNB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMOB']}
2025-07-26 13:32:04,229 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-07-26 13:32:04,229 - INFO - 成功插入的数据ID: ['FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMKB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMLB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMMB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMNB', 'FINST-L5766E71HDGXQLKPDKQED7V0OUSH25LDCTJDMOB']
2025-07-26 13:32:09,245 - INFO - 批量插入完成，共 5 条记录
2025-07-26 13:32:09,245 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-07-26 13:32:09,245 - INFO - 开始处理日期: 2025-07-26
2025-07-26 13:32:09,245 - INFO - Request Parameters - Page 1:
2025-07-26 13:32:09,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 13:32:09,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 13:32:09,713 - INFO - Response - Page 1:
2025-07-26 13:32:09,713 - INFO - 第 1 页获取到 1 条记录
2025-07-26 13:32:10,213 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 13:32:10,213 - INFO - 获取到 1 条表单数据
2025-07-26 13:32:10,213 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 13:32:10,213 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 13:32:10,213 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 0 条
2025-07-26 13:32:10,213 - INFO - 同步完成
2025-07-26 16:30:33,875 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 16:30:33,875 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 16:30:33,875 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 16:30:34,031 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 137 条记录
2025-07-26 16:30:34,031 - INFO - 获取到 4 个日期需要处理: ['2025-07-23', '2025-07-24', '2025-07-25', '2025-07-26']
2025-07-26 16:30:34,031 - INFO - 开始处理日期: 2025-07-23
2025-07-26 16:30:34,031 - INFO - Request Parameters - Page 1:
2025-07-26 16:30:34,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:34,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:42,171 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F07433A1-9BCB-70FB-932C-5B6211624908 Response: {'code': 'ServiceUnavailable', 'requestid': 'F07433A1-9BCB-70FB-932C-5B6211624908', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F07433A1-9BCB-70FB-932C-5B6211624908)
2025-07-26 16:30:42,171 - INFO - 开始处理日期: 2025-07-24
2025-07-26 16:30:42,171 - INFO - Request Parameters - Page 1:
2025-07-26 16:30:42,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:42,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:50,093 - INFO - Response - Page 1:
2025-07-26 16:30:50,093 - INFO - 第 1 页获取到 50 条记录
2025-07-26 16:30:50,609 - INFO - Request Parameters - Page 2:
2025-07-26 16:30:50,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:50,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:51,343 - INFO - Response - Page 2:
2025-07-26 16:30:51,343 - INFO - 第 2 页获取到 50 条记录
2025-07-26 16:30:51,859 - INFO - Request Parameters - Page 3:
2025-07-26 16:30:51,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:51,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:52,578 - INFO - Response - Page 3:
2025-07-26 16:30:52,578 - INFO - 第 3 页获取到 50 条记录
2025-07-26 16:30:53,078 - INFO - Request Parameters - Page 4:
2025-07-26 16:30:53,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:53,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:53,781 - INFO - Response - Page 4:
2025-07-26 16:30:53,781 - INFO - 第 4 页获取到 50 条记录
2025-07-26 16:30:54,281 - INFO - Request Parameters - Page 5:
2025-07-26 16:30:54,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:54,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:55,046 - INFO - Response - Page 5:
2025-07-26 16:30:55,046 - INFO - 第 5 页获取到 50 条记录
2025-07-26 16:30:55,562 - INFO - Request Parameters - Page 6:
2025-07-26 16:30:55,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:55,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:56,265 - INFO - Response - Page 6:
2025-07-26 16:30:56,265 - INFO - 第 6 页获取到 50 条记录
2025-07-26 16:30:56,781 - INFO - Request Parameters - Page 7:
2025-07-26 16:30:56,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:56,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:57,437 - INFO - Response - Page 7:
2025-07-26 16:30:57,437 - INFO - 第 7 页获取到 50 条记录
2025-07-26 16:30:57,953 - INFO - Request Parameters - Page 8:
2025-07-26 16:30:57,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:57,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:30:58,687 - INFO - Response - Page 8:
2025-07-26 16:30:58,687 - INFO - 第 8 页获取到 50 条记录
2025-07-26 16:30:59,203 - INFO - Request Parameters - Page 9:
2025-07-26 16:30:59,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:30:59,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:00,265 - INFO - Response - Page 9:
2025-07-26 16:31:00,265 - INFO - 第 9 页获取到 50 条记录
2025-07-26 16:31:00,781 - INFO - Request Parameters - Page 10:
2025-07-26 16:31:00,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:00,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:01,437 - INFO - Response - Page 10:
2025-07-26 16:31:01,437 - INFO - 第 10 页获取到 50 条记录
2025-07-26 16:31:01,937 - INFO - Request Parameters - Page 11:
2025-07-26 16:31:01,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:01,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:02,718 - INFO - Response - Page 11:
2025-07-26 16:31:02,718 - INFO - 第 11 页获取到 50 条记录
2025-07-26 16:31:03,218 - INFO - Request Parameters - Page 12:
2025-07-26 16:31:03,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:03,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:03,718 - INFO - Response - Page 12:
2025-07-26 16:31:03,718 - INFO - 第 12 页获取到 1 条记录
2025-07-26 16:31:04,234 - INFO - 查询完成，共获取到 551 条记录
2025-07-26 16:31:04,234 - INFO - 获取到 551 条表单数据
2025-07-26 16:31:04,234 - INFO - 当前日期 2025-07-24 有 1 条MySQL数据需要处理
2025-07-26 16:31:04,234 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMXF
2025-07-26 16:31:04,843 - INFO - 更新表单数据成功: FINST-F7D66UA12DFXKIKDFZD46CFIBTIM22TVH7IDMXF
2025-07-26 16:31:04,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3720.0, 'new_value': 3477.75}, {'field': 'total_amount', 'old_value': 3720.0, 'new_value': 3477.75}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1f1dee28a63b46b09258c79dabeb26f7.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=HEZc2bXo7z6UsCo5e8L6TpetdMY%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/4cd4e8508fef4c6d8272cd8aea1b4bdd.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=l4iqTp%2B3IUb8gVUz2dUBOuCVUto%3D'}]
2025-07-26 16:31:04,843 - INFO - 日期 2025-07-24 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-26 16:31:04,843 - INFO - 开始处理日期: 2025-07-25
2025-07-26 16:31:04,843 - INFO - Request Parameters - Page 1:
2025-07-26 16:31:04,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:04,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:05,562 - INFO - Response - Page 1:
2025-07-26 16:31:05,562 - INFO - 第 1 页获取到 50 条记录
2025-07-26 16:31:06,077 - INFO - Request Parameters - Page 2:
2025-07-26 16:31:06,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:06,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:06,749 - INFO - Response - Page 2:
2025-07-26 16:31:06,749 - INFO - 第 2 页获取到 50 条记录
2025-07-26 16:31:07,249 - INFO - Request Parameters - Page 3:
2025-07-26 16:31:07,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:07,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:08,031 - INFO - Response - Page 3:
2025-07-26 16:31:08,031 - INFO - 第 3 页获取到 50 条记录
2025-07-26 16:31:08,546 - INFO - Request Parameters - Page 4:
2025-07-26 16:31:08,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:08,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:09,374 - INFO - Response - Page 4:
2025-07-26 16:31:09,374 - INFO - 第 4 页获取到 50 条记录
2025-07-26 16:31:09,890 - INFO - Request Parameters - Page 5:
2025-07-26 16:31:09,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:09,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:10,624 - INFO - Response - Page 5:
2025-07-26 16:31:10,624 - INFO - 第 5 页获取到 50 条记录
2025-07-26 16:31:11,124 - INFO - Request Parameters - Page 6:
2025-07-26 16:31:11,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:11,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:11,843 - INFO - Response - Page 6:
2025-07-26 16:31:11,843 - INFO - 第 6 页获取到 50 条记录
2025-07-26 16:31:12,343 - INFO - Request Parameters - Page 7:
2025-07-26 16:31:12,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:12,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:13,046 - INFO - Response - Page 7:
2025-07-26 16:31:13,046 - INFO - 第 7 页获取到 50 条记录
2025-07-26 16:31:13,562 - INFO - Request Parameters - Page 8:
2025-07-26 16:31:13,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:13,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:14,281 - INFO - Response - Page 8:
2025-07-26 16:31:14,281 - INFO - 第 8 页获取到 50 条记录
2025-07-26 16:31:14,781 - INFO - Request Parameters - Page 9:
2025-07-26 16:31:14,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:14,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:15,249 - INFO - Response - Page 9:
2025-07-26 16:31:15,249 - INFO - 查询完成，共获取到 400 条记录
2025-07-26 16:31:15,249 - INFO - 获取到 400 条表单数据
2025-07-26 16:31:15,265 - INFO - 当前日期 2025-07-25 有 133 条MySQL数据需要处理
2025-07-26 16:31:15,265 - INFO - 开始批量插入 15 条新记录
2025-07-26 16:31:15,437 - INFO - 批量插入响应状态码: 200
2025-07-26 16:31:15,437 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 08:31:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2572F807-6D9B-7604-B431-759F4A4FB203', 'x-acs-trace-id': 'e7adaa6d25a619ab566cef14b9162550', 'etag': '7RIrYk9jI2w2GT5+90JFypA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 16:31:15,437 - INFO - 批量插入响应体: {'result': ['FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM0T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM1T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM2T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM3T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM4T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM5T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM6T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM7T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM8T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM9T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMAT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMBT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMCT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMDT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMET']}
2025-07-26 16:31:15,437 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-07-26 16:31:15,437 - INFO - 成功插入的数据ID: ['FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM0T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM1T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM2T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM3T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM4T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM5T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM6T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM7T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM8T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDM9T', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMAT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMBT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMCT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMDT', 'FINST-SED66Q615IEXD5BRBYJXG9MI6CNX2LBTQZJDMET']
2025-07-26 16:31:20,452 - INFO - 批量插入完成，共 15 条记录
2025-07-26 16:31:20,452 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 15 条，错误: 0 条
2025-07-26 16:31:20,452 - INFO - 开始处理日期: 2025-07-26
2025-07-26 16:31:20,452 - INFO - Request Parameters - Page 1:
2025-07-26 16:31:20,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:31:20,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:31:20,968 - INFO - Response - Page 1:
2025-07-26 16:31:20,968 - INFO - 第 1 页获取到 1 条记录
2025-07-26 16:31:21,468 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 16:31:21,468 - INFO - 获取到 1 条表单数据
2025-07-26 16:31:21,468 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 16:31:21,468 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 16:31:21,468 - INFO - 数据同步完成！更新: 1 条，插入: 15 条，错误: 1 条
2025-07-26 16:32:21,483 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 16:32:21,483 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 16:32:21,483 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 16:32:21,639 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 450 条记录
2025-07-26 16:32:21,639 - INFO - 获取到 2 个日期需要处理: ['2025-07-25', '2025-07-26']
2025-07-26 16:32:21,639 - INFO - 开始处理日期: 2025-07-25
2025-07-26 16:32:21,639 - INFO - Request Parameters - Page 1:
2025-07-26 16:32:21,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:21,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:22,452 - INFO - Response - Page 1:
2025-07-26 16:32:22,452 - INFO - 第 1 页获取到 50 条记录
2025-07-26 16:32:22,952 - INFO - Request Parameters - Page 2:
2025-07-26 16:32:22,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:22,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:23,671 - INFO - Response - Page 2:
2025-07-26 16:32:23,671 - INFO - 第 2 页获取到 50 条记录
2025-07-26 16:32:24,171 - INFO - Request Parameters - Page 3:
2025-07-26 16:32:24,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:24,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:24,843 - INFO - Response - Page 3:
2025-07-26 16:32:24,843 - INFO - 第 3 页获取到 50 条记录
2025-07-26 16:32:25,358 - INFO - Request Parameters - Page 4:
2025-07-26 16:32:25,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:25,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:26,046 - INFO - Response - Page 4:
2025-07-26 16:32:26,046 - INFO - 第 4 页获取到 50 条记录
2025-07-26 16:32:26,546 - INFO - Request Parameters - Page 5:
2025-07-26 16:32:26,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:26,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:27,280 - INFO - Response - Page 5:
2025-07-26 16:32:27,280 - INFO - 第 5 页获取到 50 条记录
2025-07-26 16:32:27,796 - INFO - Request Parameters - Page 6:
2025-07-26 16:32:27,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:27,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:28,468 - INFO - Response - Page 6:
2025-07-26 16:32:28,468 - INFO - 第 6 页获取到 50 条记录
2025-07-26 16:32:28,983 - INFO - Request Parameters - Page 7:
2025-07-26 16:32:28,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:28,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:29,733 - INFO - Response - Page 7:
2025-07-26 16:32:29,733 - INFO - 第 7 页获取到 50 条记录
2025-07-26 16:32:30,233 - INFO - Request Parameters - Page 8:
2025-07-26 16:32:30,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:30,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:30,999 - INFO - Response - Page 8:
2025-07-26 16:32:30,999 - INFO - 第 8 页获取到 50 条记录
2025-07-26 16:32:31,514 - INFO - Request Parameters - Page 9:
2025-07-26 16:32:31,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:31,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:32,108 - INFO - Response - Page 9:
2025-07-26 16:32:32,108 - INFO - 第 9 页获取到 15 条记录
2025-07-26 16:32:32,624 - INFO - 查询完成，共获取到 415 条记录
2025-07-26 16:32:32,624 - INFO - 获取到 415 条表单数据
2025-07-26 16:32:32,624 - INFO - 当前日期 2025-07-25 有 440 条MySQL数据需要处理
2025-07-26 16:32:32,639 - INFO - 开始批量插入 25 条新记录
2025-07-26 16:32:32,921 - INFO - 批量插入响应状态码: 200
2025-07-26 16:32:32,921 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 08:32:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1187', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B7E78934-F64E-797D-8934-B57A2E4F0594', 'x-acs-trace-id': '0d65361d87b34f99492bccd32ad3a1a2', 'etag': '13OyoX05GSKIJnWZ0lQ8EJA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 16:32:32,921 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM1', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM2', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM3', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM4', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM5', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM6', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM7', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM8', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM9', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMA', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMB', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMC', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMD', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDME', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMF', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMG', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMH', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMI', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMJ', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMK', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDML', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMM', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3B4HSZJDMN', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3B4HSZJDMO', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3B4HSZJDMP']}
2025-07-26 16:32:32,921 - INFO - 批量插入表单数据成功，批次 1，共 25 条记录
2025-07-26 16:32:32,921 - INFO - 成功插入的数据ID: ['FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM1', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM2', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM3', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM4', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM5', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM6', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM7', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM8', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDM9', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMA', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMB', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMC', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMD', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDME', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMF', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMG', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMH', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMI', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMJ', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMK', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDML', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3A4HSZJDMM', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3B4HSZJDMN', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3B4HSZJDMO', 'FINST-LLF66FD1XHHXPM7EFZED37BGG2OI3B4HSZJDMP']
2025-07-26 16:32:37,936 - INFO - 批量插入完成，共 25 条记录
2025-07-26 16:32:37,936 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 25 条，错误: 0 条
2025-07-26 16:32:37,936 - INFO - 开始处理日期: 2025-07-26
2025-07-26 16:32:37,936 - INFO - Request Parameters - Page 1:
2025-07-26 16:32:37,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 16:32:37,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 16:32:38,421 - INFO - Response - Page 1:
2025-07-26 16:32:38,421 - INFO - 第 1 页获取到 1 条记录
2025-07-26 16:32:38,936 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 16:32:38,936 - INFO - 获取到 1 条表单数据
2025-07-26 16:32:38,936 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 16:32:38,936 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 16:32:38,936 - INFO - 数据同步完成！更新: 0 条，插入: 25 条，错误: 0 条
2025-07-26 16:32:38,936 - INFO - 同步完成
2025-07-26 19:30:34,310 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 19:30:34,310 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 19:30:34,310 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 19:30:34,466 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 137 条记录
2025-07-26 19:30:34,466 - INFO - 获取到 4 个日期需要处理: ['2025-07-23', '2025-07-24', '2025-07-25', '2025-07-26']
2025-07-26 19:30:34,482 - INFO - 开始处理日期: 2025-07-23
2025-07-26 19:30:34,482 - INFO - Request Parameters - Page 1:
2025-07-26 19:30:34,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:34,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:42,610 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 440E090C-4E14-7BC7-8719-9B0CEFFF19E6 Response: {'code': 'ServiceUnavailable', 'requestid': '440E090C-4E14-7BC7-8719-9B0CEFFF19E6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 440E090C-4E14-7BC7-8719-9B0CEFFF19E6)
2025-07-26 19:30:42,610 - INFO - 开始处理日期: 2025-07-24
2025-07-26 19:30:42,610 - INFO - Request Parameters - Page 1:
2025-07-26 19:30:42,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:42,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:46,205 - INFO - Response - Page 1:
2025-07-26 19:30:46,205 - INFO - 第 1 页获取到 50 条记录
2025-07-26 19:30:46,706 - INFO - Request Parameters - Page 2:
2025-07-26 19:30:46,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:46,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:54,255 - INFO - Response - Page 2:
2025-07-26 19:30:54,255 - INFO - 第 2 页获取到 50 条记录
2025-07-26 19:30:54,756 - INFO - Request Parameters - Page 3:
2025-07-26 19:30:54,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:54,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:55,443 - INFO - Response - Page 3:
2025-07-26 19:30:55,443 - INFO - 第 3 页获取到 50 条记录
2025-07-26 19:30:55,959 - INFO - Request Parameters - Page 4:
2025-07-26 19:30:55,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:55,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:56,647 - INFO - Response - Page 4:
2025-07-26 19:30:56,647 - INFO - 第 4 页获取到 50 条记录
2025-07-26 19:30:57,163 - INFO - Request Parameters - Page 5:
2025-07-26 19:30:57,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:57,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:57,866 - INFO - Response - Page 5:
2025-07-26 19:30:57,866 - INFO - 第 5 页获取到 50 条记录
2025-07-26 19:30:58,366 - INFO - Request Parameters - Page 6:
2025-07-26 19:30:58,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:58,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:30:59,054 - INFO - Response - Page 6:
2025-07-26 19:30:59,054 - INFO - 第 6 页获取到 50 条记录
2025-07-26 19:30:59,570 - INFO - Request Parameters - Page 7:
2025-07-26 19:30:59,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:30:59,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:00,273 - INFO - Response - Page 7:
2025-07-26 19:31:00,273 - INFO - 第 7 页获取到 50 条记录
2025-07-26 19:31:00,789 - INFO - Request Parameters - Page 8:
2025-07-26 19:31:00,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:00,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:01,493 - INFO - Response - Page 8:
2025-07-26 19:31:01,493 - INFO - 第 8 页获取到 50 条记录
2025-07-26 19:31:01,993 - INFO - Request Parameters - Page 9:
2025-07-26 19:31:01,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:01,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:02,774 - INFO - Response - Page 9:
2025-07-26 19:31:02,774 - INFO - 第 9 页获取到 50 条记录
2025-07-26 19:31:03,290 - INFO - Request Parameters - Page 10:
2025-07-26 19:31:03,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:03,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:03,994 - INFO - Response - Page 10:
2025-07-26 19:31:03,994 - INFO - 第 10 页获取到 50 条记录
2025-07-26 19:31:04,510 - INFO - Request Parameters - Page 11:
2025-07-26 19:31:04,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:04,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:05,197 - INFO - Response - Page 11:
2025-07-26 19:31:05,197 - INFO - 第 11 页获取到 50 条记录
2025-07-26 19:31:05,698 - INFO - Request Parameters - Page 12:
2025-07-26 19:31:05,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:05,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:06,166 - INFO - Response - Page 12:
2025-07-26 19:31:06,166 - INFO - 第 12 页获取到 1 条记录
2025-07-26 19:31:06,682 - INFO - 查询完成，共获取到 551 条记录
2025-07-26 19:31:06,682 - INFO - 获取到 551 条表单数据
2025-07-26 19:31:06,682 - INFO - 当前日期 2025-07-24 有 1 条MySQL数据需要处理
2025-07-26 19:31:06,682 - INFO - 日期 2025-07-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 19:31:06,682 - INFO - 开始处理日期: 2025-07-25
2025-07-26 19:31:06,682 - INFO - Request Parameters - Page 1:
2025-07-26 19:31:06,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:06,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:07,370 - INFO - Response - Page 1:
2025-07-26 19:31:07,370 - INFO - 第 1 页获取到 50 条记录
2025-07-26 19:31:07,886 - INFO - Request Parameters - Page 2:
2025-07-26 19:31:07,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:07,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:08,714 - INFO - Response - Page 2:
2025-07-26 19:31:08,714 - INFO - 第 2 页获取到 50 条记录
2025-07-26 19:31:09,230 - INFO - Request Parameters - Page 3:
2025-07-26 19:31:09,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:09,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:09,918 - INFO - Response - Page 3:
2025-07-26 19:31:09,918 - INFO - 第 3 页获取到 50 条记录
2025-07-26 19:31:10,418 - INFO - Request Parameters - Page 4:
2025-07-26 19:31:10,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:10,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:11,168 - INFO - Response - Page 4:
2025-07-26 19:31:11,168 - INFO - 第 4 页获取到 50 条记录
2025-07-26 19:31:11,684 - INFO - Request Parameters - Page 5:
2025-07-26 19:31:11,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:11,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:12,356 - INFO - Response - Page 5:
2025-07-26 19:31:12,356 - INFO - 第 5 页获取到 50 条记录
2025-07-26 19:31:12,857 - INFO - Request Parameters - Page 6:
2025-07-26 19:31:12,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:12,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:13,576 - INFO - Response - Page 6:
2025-07-26 19:31:13,576 - INFO - 第 6 页获取到 50 条记录
2025-07-26 19:31:14,091 - INFO - Request Parameters - Page 7:
2025-07-26 19:31:14,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:14,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:14,795 - INFO - Response - Page 7:
2025-07-26 19:31:14,795 - INFO - 第 7 页获取到 50 条记录
2025-07-26 19:31:15,295 - INFO - Request Parameters - Page 8:
2025-07-26 19:31:15,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:15,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:15,967 - INFO - Response - Page 8:
2025-07-26 19:31:15,967 - INFO - 第 8 页获取到 50 条记录
2025-07-26 19:31:16,483 - INFO - Request Parameters - Page 9:
2025-07-26 19:31:16,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:16,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:17,186 - INFO - Response - Page 9:
2025-07-26 19:31:17,186 - INFO - 第 9 页获取到 40 条记录
2025-07-26 19:31:17,702 - INFO - 查询完成，共获取到 440 条记录
2025-07-26 19:31:17,702 - INFO - 获取到 440 条表单数据
2025-07-26 19:31:17,702 - INFO - 当前日期 2025-07-25 有 133 条MySQL数据需要处理
2025-07-26 19:31:17,702 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 19:31:17,702 - INFO - 开始处理日期: 2025-07-26
2025-07-26 19:31:17,702 - INFO - Request Parameters - Page 1:
2025-07-26 19:31:17,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:31:17,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:31:18,171 - INFO - Response - Page 1:
2025-07-26 19:31:18,171 - INFO - 第 1 页获取到 1 条记录
2025-07-26 19:31:18,687 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 19:31:18,687 - INFO - 获取到 1 条表单数据
2025-07-26 19:31:18,687 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 19:31:18,687 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 19:31:18,687 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-26 19:32:18,726 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 19:32:18,726 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 19:32:18,726 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 19:32:18,883 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 450 条记录
2025-07-26 19:32:18,883 - INFO - 获取到 2 个日期需要处理: ['2025-07-25', '2025-07-26']
2025-07-26 19:32:18,898 - INFO - 开始处理日期: 2025-07-25
2025-07-26 19:32:18,898 - INFO - Request Parameters - Page 1:
2025-07-26 19:32:18,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:18,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:19,633 - INFO - Response - Page 1:
2025-07-26 19:32:19,633 - INFO - 第 1 页获取到 50 条记录
2025-07-26 19:32:20,149 - INFO - Request Parameters - Page 2:
2025-07-26 19:32:20,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:20,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:20,852 - INFO - Response - Page 2:
2025-07-26 19:32:20,852 - INFO - 第 2 页获取到 50 条记录
2025-07-26 19:32:21,353 - INFO - Request Parameters - Page 3:
2025-07-26 19:32:21,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:21,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:22,025 - INFO - Response - Page 3:
2025-07-26 19:32:22,025 - INFO - 第 3 页获取到 50 条记录
2025-07-26 19:32:22,540 - INFO - Request Parameters - Page 4:
2025-07-26 19:32:22,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:22,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:23,213 - INFO - Response - Page 4:
2025-07-26 19:32:23,213 - INFO - 第 4 页获取到 50 条记录
2025-07-26 19:32:23,728 - INFO - Request Parameters - Page 5:
2025-07-26 19:32:23,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:23,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:24,416 - INFO - Response - Page 5:
2025-07-26 19:32:24,416 - INFO - 第 5 页获取到 50 条记录
2025-07-26 19:32:24,916 - INFO - Request Parameters - Page 6:
2025-07-26 19:32:24,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:24,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:25,682 - INFO - Response - Page 6:
2025-07-26 19:32:25,682 - INFO - 第 6 页获取到 50 条记录
2025-07-26 19:32:26,183 - INFO - Request Parameters - Page 7:
2025-07-26 19:32:26,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:26,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:26,823 - INFO - Response - Page 7:
2025-07-26 19:32:26,823 - INFO - 第 7 页获取到 50 条记录
2025-07-26 19:32:27,324 - INFO - Request Parameters - Page 8:
2025-07-26 19:32:27,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:27,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:28,011 - INFO - Response - Page 8:
2025-07-26 19:32:28,011 - INFO - 第 8 页获取到 50 条记录
2025-07-26 19:32:28,512 - INFO - Request Parameters - Page 9:
2025-07-26 19:32:28,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:28,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:29,231 - INFO - Response - Page 9:
2025-07-26 19:32:29,231 - INFO - 第 9 页获取到 40 条记录
2025-07-26 19:32:29,746 - INFO - 查询完成，共获取到 440 条记录
2025-07-26 19:32:29,746 - INFO - 获取到 440 条表单数据
2025-07-26 19:32:29,746 - INFO - 当前日期 2025-07-25 有 440 条MySQL数据需要处理
2025-07-26 19:32:29,762 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 19:32:29,762 - INFO - 开始处理日期: 2025-07-26
2025-07-26 19:32:29,762 - INFO - Request Parameters - Page 1:
2025-07-26 19:32:29,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 19:32:29,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 19:32:30,215 - INFO - Response - Page 1:
2025-07-26 19:32:30,215 - INFO - 第 1 页获取到 1 条记录
2025-07-26 19:32:30,731 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 19:32:30,731 - INFO - 获取到 1 条表单数据
2025-07-26 19:32:30,731 - INFO - 当前日期 2025-07-26 有 1 条MySQL数据需要处理
2025-07-26 19:32:30,731 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 19:32:30,731 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 19:32:30,731 - INFO - 同步完成
2025-07-26 22:30:35,590 - INFO - 使用默认增量同步（当天更新数据）
2025-07-26 22:30:35,590 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-26 22:30:35,590 - INFO - 查询参数: ('2025-07-26',)
2025-07-26 22:30:35,762 - INFO - MySQL查询成功，增量数据（日期: 2025-07-26），共获取 171 条记录
2025-07-26 22:30:35,762 - INFO - 获取到 4 个日期需要处理: ['2025-07-23', '2025-07-24', '2025-07-25', '2025-07-26']
2025-07-26 22:30:35,762 - INFO - 开始处理日期: 2025-07-23
2025-07-26 22:30:35,762 - INFO - Request Parameters - Page 1:
2025-07-26 22:30:35,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:35,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753200000000, 1753286399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:30:43,875 - ERROR - 处理日期 2025-07-23 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9068B620-A4BF-7B23-93CF-B2E50A3989F1 Response: {'code': 'ServiceUnavailable', 'requestid': '9068B620-A4BF-7B23-93CF-B2E50A3989F1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9068B620-A4BF-7B23-93CF-B2E50A3989F1)
2025-07-26 22:30:43,875 - INFO - 开始处理日期: 2025-07-24
2025-07-26 22:30:43,875 - INFO - Request Parameters - Page 1:
2025-07-26 22:30:43,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:43,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:30:51,987 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1D1A37F5-A70C-7EBE-8D8B-0058D6C475D3 Response: {'code': 'ServiceUnavailable', 'requestid': '1D1A37F5-A70C-7EBE-8D8B-0058D6C475D3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1D1A37F5-A70C-7EBE-8D8B-0058D6C475D3)
2025-07-26 22:30:51,987 - INFO - 开始处理日期: 2025-07-25
2025-07-26 22:30:51,987 - INFO - Request Parameters - Page 1:
2025-07-26 22:30:51,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:51,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:30:55,411 - INFO - Response - Page 1:
2025-07-26 22:30:55,411 - INFO - 第 1 页获取到 50 条记录
2025-07-26 22:30:55,911 - INFO - Request Parameters - Page 2:
2025-07-26 22:30:55,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:55,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:30:56,614 - INFO - Response - Page 2:
2025-07-26 22:30:56,614 - INFO - 第 2 页获取到 50 条记录
2025-07-26 22:30:57,114 - INFO - Request Parameters - Page 3:
2025-07-26 22:30:57,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:57,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:30:57,833 - INFO - Response - Page 3:
2025-07-26 22:30:57,833 - INFO - 第 3 页获取到 50 条记录
2025-07-26 22:30:58,349 - INFO - Request Parameters - Page 4:
2025-07-26 22:30:58,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:58,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:30:59,147 - INFO - Response - Page 4:
2025-07-26 22:30:59,147 - INFO - 第 4 页获取到 50 条记录
2025-07-26 22:30:59,662 - INFO - Request Parameters - Page 5:
2025-07-26 22:30:59,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:30:59,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:31:00,366 - INFO - Response - Page 5:
2025-07-26 22:31:00,366 - INFO - 第 5 页获取到 50 条记录
2025-07-26 22:31:00,882 - INFO - Request Parameters - Page 6:
2025-07-26 22:31:00,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:31:00,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:31:01,569 - INFO - Response - Page 6:
2025-07-26 22:31:01,569 - INFO - 第 6 页获取到 50 条记录
2025-07-26 22:31:02,070 - INFO - Request Parameters - Page 7:
2025-07-26 22:31:02,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:31:02,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:31:02,789 - INFO - Response - Page 7:
2025-07-26 22:31:02,789 - INFO - 第 7 页获取到 50 条记录
2025-07-26 22:31:03,304 - INFO - Request Parameters - Page 8:
2025-07-26 22:31:03,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:31:03,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:31:04,055 - INFO - Response - Page 8:
2025-07-26 22:31:04,055 - INFO - 第 8 页获取到 50 条记录
2025-07-26 22:31:04,571 - INFO - Request Parameters - Page 9:
2025-07-26 22:31:04,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:31:04,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:31:05,258 - INFO - Response - Page 9:
2025-07-26 22:31:05,258 - INFO - 第 9 页获取到 40 条记录
2025-07-26 22:31:05,759 - INFO - 查询完成，共获取到 440 条记录
2025-07-26 22:31:05,759 - INFO - 获取到 440 条表单数据
2025-07-26 22:31:05,759 - INFO - 当前日期 2025-07-25 有 133 条MySQL数据需要处理
2025-07-26 22:31:05,759 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 22:31:05,759 - INFO - 开始处理日期: 2025-07-26
2025-07-26 22:31:05,759 - INFO - Request Parameters - Page 1:
2025-07-26 22:31:05,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:31:05,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:31:06,243 - INFO - Response - Page 1:
2025-07-26 22:31:06,243 - INFO - 第 1 页获取到 1 条记录
2025-07-26 22:31:06,759 - INFO - 查询完成，共获取到 1 条记录
2025-07-26 22:31:06,759 - INFO - 获取到 1 条表单数据
2025-07-26 22:31:06,759 - INFO - 当前日期 2025-07-26 有 33 条MySQL数据需要处理
2025-07-26 22:31:06,759 - INFO - 开始批量插入 32 条新记录
2025-07-26 22:31:06,978 - INFO - 批量插入响应状态码: 200
2025-07-26 22:31:06,978 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 14:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1548', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FA8CA1EA-5912-7F6D-83F4-B1C41F2D4D7E', 'x-acs-trace-id': 'de2e834d4fd564c11508ce9e53087d4d', 'etag': '1qxNbeL7pNmItuc3vukIv9w8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 22:31:06,978 - INFO - 批量插入响应体: {'result': ['FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMJ5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMK5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDML5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMM5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMN5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMO5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMP5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMQ5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMR5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMS5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMT5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMU5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMV5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMW5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMX5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMY5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMZ5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM06', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM16', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM26', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM36', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM46', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM56', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM66', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM76', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM86', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM96', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMA6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMB6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMC6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMD6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDME6']}
2025-07-26 22:31:06,978 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-07-26 22:31:06,978 - INFO - 成功插入的数据ID: ['FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMJ5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMK5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDML5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMM5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMN5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMO5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMP5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMQ5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMR5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMS5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMT5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMU5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMV5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMW5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMX5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMY5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMZ5', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM06', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM16', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM26', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM36', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM46', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM56', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM66', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM76', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM86', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDM96', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMA6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMB6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMC6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDMD6', 'FINST-6AG66W818JHXIS1FFZA5K9UJF2SW37YHLCKDME6']
2025-07-26 22:31:11,995 - INFO - 批量插入完成，共 32 条记录
2025-07-26 22:31:11,995 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 32 条，错误: 0 条
2025-07-26 22:31:11,995 - INFO - 数据同步完成！更新: 0 条，插入: 32 条，错误: 2 条
2025-07-26 22:32:12,035 - INFO - 开始同步昨天与今天的销售数据: 2025-07-25 至 2025-07-26
2025-07-26 22:32:12,035 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-26 22:32:12,035 - INFO - 查询参数: ('2025-07-25', '2025-07-26')
2025-07-26 22:32:12,191 - INFO - MySQL查询成功，时间段: 2025-07-25 至 2025-07-26，共获取 487 条记录
2025-07-26 22:32:12,191 - INFO - 获取到 2 个日期需要处理: ['2025-07-25', '2025-07-26']
2025-07-26 22:32:12,207 - INFO - 开始处理日期: 2025-07-25
2025-07-26 22:32:12,207 - INFO - Request Parameters - Page 1:
2025-07-26 22:32:12,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:12,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:12,879 - INFO - Response - Page 1:
2025-07-26 22:32:12,879 - INFO - 第 1 页获取到 50 条记录
2025-07-26 22:32:13,395 - INFO - Request Parameters - Page 2:
2025-07-26 22:32:13,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:13,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:14,114 - INFO - Response - Page 2:
2025-07-26 22:32:14,114 - INFO - 第 2 页获取到 50 条记录
2025-07-26 22:32:14,630 - INFO - Request Parameters - Page 3:
2025-07-26 22:32:14,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:14,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:15,302 - INFO - Response - Page 3:
2025-07-26 22:32:15,302 - INFO - 第 3 页获取到 50 条记录
2025-07-26 22:32:15,818 - INFO - Request Parameters - Page 4:
2025-07-26 22:32:15,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:15,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:16,505 - INFO - Response - Page 4:
2025-07-26 22:32:16,505 - INFO - 第 4 页获取到 50 条记录
2025-07-26 22:32:17,021 - INFO - Request Parameters - Page 5:
2025-07-26 22:32:17,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:17,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:17,756 - INFO - Response - Page 5:
2025-07-26 22:32:17,756 - INFO - 第 5 页获取到 50 条记录
2025-07-26 22:32:18,272 - INFO - Request Parameters - Page 6:
2025-07-26 22:32:18,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:18,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:19,069 - INFO - Response - Page 6:
2025-07-26 22:32:19,069 - INFO - 第 6 页获取到 50 条记录
2025-07-26 22:32:19,569 - INFO - Request Parameters - Page 7:
2025-07-26 22:32:19,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:19,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:20,335 - INFO - Response - Page 7:
2025-07-26 22:32:20,335 - INFO - 第 7 页获取到 50 条记录
2025-07-26 22:32:20,835 - INFO - Request Parameters - Page 8:
2025-07-26 22:32:20,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:20,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:21,554 - INFO - Response - Page 8:
2025-07-26 22:32:21,554 - INFO - 第 8 页获取到 50 条记录
2025-07-26 22:32:22,070 - INFO - Request Parameters - Page 9:
2025-07-26 22:32:22,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:22,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:22,727 - INFO - Response - Page 9:
2025-07-26 22:32:22,727 - INFO - 第 9 页获取到 40 条记录
2025-07-26 22:32:23,227 - INFO - 查询完成，共获取到 440 条记录
2025-07-26 22:32:23,227 - INFO - 获取到 440 条表单数据
2025-07-26 22:32:23,227 - INFO - 当前日期 2025-07-25 有 440 条MySQL数据需要处理
2025-07-26 22:32:23,242 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-26 22:32:23,242 - INFO - 开始处理日期: 2025-07-26
2025-07-26 22:32:23,242 - INFO - Request Parameters - Page 1:
2025-07-26 22:32:23,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-26 22:32:23,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-26 22:32:23,836 - INFO - Response - Page 1:
2025-07-26 22:32:23,836 - INFO - 第 1 页获取到 33 条记录
2025-07-26 22:32:24,352 - INFO - 查询完成，共获取到 33 条记录
2025-07-26 22:32:24,352 - INFO - 获取到 33 条表单数据
2025-07-26 22:32:24,352 - INFO - 当前日期 2025-07-26 有 36 条MySQL数据需要处理
2025-07-26 22:32:24,352 - INFO - 开始批量插入 3 条新记录
2025-07-26 22:32:24,493 - INFO - 批量插入响应状态码: 200
2025-07-26 22:32:24,493 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 14:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CB424011-7000-7FFF-8940-DB57B5EA26A6', 'x-acs-trace-id': 'b4ab4856b2d486e29ab920c6ed5feeca', 'etag': '1lP91mRNmOnZ+90+6W3mDlg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-26 22:32:24,493 - INFO - 批量插入响应体: {'result': ['FINST-1OC66A91AJHXLFJ0BH7FT5HK6ZQZ2LQ5NCKDM53', 'FINST-1OC66A91AJHXLFJ0BH7FT5HK6ZQZ2LQ5NCKDM63', 'FINST-1OC66A91AJHXLFJ0BH7FT5HK6ZQZ2LQ5NCKDM73']}
2025-07-26 22:32:24,493 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-26 22:32:24,493 - INFO - 成功插入的数据ID: ['FINST-1OC66A91AJHXLFJ0BH7FT5HK6ZQZ2LQ5NCKDM53', 'FINST-1OC66A91AJHXLFJ0BH7FT5HK6ZQZ2LQ5NCKDM63', 'FINST-1OC66A91AJHXLFJ0BH7FT5HK6ZQZ2LQ5NCKDM73']
2025-07-26 22:32:29,510 - INFO - 批量插入完成，共 3 条记录
2025-07-26 22:32:29,510 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-26 22:32:29,510 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-07-26 22:32:29,510 - INFO - 同步完成
