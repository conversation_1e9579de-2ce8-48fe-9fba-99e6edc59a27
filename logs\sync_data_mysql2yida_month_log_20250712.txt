2025-07-12 00:00:03,233 - INFO - =================使用默认全量同步=============
2025-07-12 00:00:05,233 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 00:00:05,233 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 00:00:05,264 - INFO - 开始处理日期: 2025-01
2025-07-12 00:00:05,280 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:05,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:05,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:06,717 - INFO - Response - Page 1:
2025-07-12 00:00:06,920 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:06,920 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:06,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:06,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:07,858 - INFO - Response - Page 2:
2025-07-12 00:00:08,061 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:08,061 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:08,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:08,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:08,639 - INFO - Response - Page 3:
2025-07-12 00:00:08,842 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:08,842 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:08,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:08,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:09,436 - INFO - Response - Page 4:
2025-07-12 00:00:09,639 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:09,639 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:09,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:09,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:10,170 - INFO - Response - Page 5:
2025-07-12 00:00:10,373 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:10,373 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:10,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:10,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:10,889 - INFO - Response - Page 6:
2025-07-12 00:00:11,092 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:11,092 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:11,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:11,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:11,608 - INFO - Response - Page 7:
2025-07-12 00:00:11,811 - INFO - 第 7 页获取到 82 条记录
2025-07-12 00:00:11,811 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 00:00:11,811 - INFO - 获取到 682 条表单数据
2025-07-12 00:00:11,811 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 00:00:11,826 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 00:00:11,826 - INFO - 开始处理日期: 2025-02
2025-07-12 00:00:11,826 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:11,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:11,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:12,436 - INFO - Response - Page 1:
2025-07-12 00:00:12,639 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:12,639 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:12,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:12,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:13,155 - INFO - Response - Page 2:
2025-07-12 00:00:13,358 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:13,358 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:13,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:13,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:13,889 - INFO - Response - Page 3:
2025-07-12 00:00:14,092 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:14,092 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:14,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:14,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:14,655 - INFO - Response - Page 4:
2025-07-12 00:00:14,858 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:14,858 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:14,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:14,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:15,389 - INFO - Response - Page 5:
2025-07-12 00:00:15,592 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:15,592 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:15,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:15,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:16,061 - INFO - Response - Page 6:
2025-07-12 00:00:16,264 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:16,264 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:16,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:16,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:16,717 - INFO - Response - Page 7:
2025-07-12 00:00:16,920 - INFO - 第 7 页获取到 70 条记录
2025-07-12 00:00:16,920 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 00:00:16,920 - INFO - 获取到 670 条表单数据
2025-07-12 00:00:16,920 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 00:00:16,936 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 00:00:16,936 - INFO - 开始处理日期: 2025-03
2025-07-12 00:00:16,936 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:16,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:16,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:17,498 - INFO - Response - Page 1:
2025-07-12 00:00:17,701 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:17,701 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:17,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:17,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:18,201 - INFO - Response - Page 2:
2025-07-12 00:00:18,404 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:18,404 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:18,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:18,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:18,889 - INFO - Response - Page 3:
2025-07-12 00:00:19,092 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:19,092 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:19,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:19,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:19,639 - INFO - Response - Page 4:
2025-07-12 00:00:19,842 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:19,842 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:19,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:19,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:20,358 - INFO - Response - Page 5:
2025-07-12 00:00:20,561 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:20,561 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:20,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:20,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:21,061 - INFO - Response - Page 6:
2025-07-12 00:00:21,264 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:21,264 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:21,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:21,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:21,701 - INFO - Response - Page 7:
2025-07-12 00:00:21,904 - INFO - 第 7 页获取到 61 条记录
2025-07-12 00:00:21,904 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 00:00:21,904 - INFO - 获取到 661 条表单数据
2025-07-12 00:00:21,904 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 00:00:21,920 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 00:00:21,920 - INFO - 开始处理日期: 2025-04
2025-07-12 00:00:21,920 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:21,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:21,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:22,404 - INFO - Response - Page 1:
2025-07-12 00:00:22,608 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:22,608 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:22,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:22,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:23,123 - INFO - Response - Page 2:
2025-07-12 00:00:23,326 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:23,326 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:23,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:23,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:23,826 - INFO - Response - Page 3:
2025-07-12 00:00:24,029 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:24,029 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:24,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:24,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:24,545 - INFO - Response - Page 4:
2025-07-12 00:00:24,748 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:24,748 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:24,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:24,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:25,248 - INFO - Response - Page 5:
2025-07-12 00:00:25,451 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:25,451 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:25,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:25,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:25,998 - INFO - Response - Page 6:
2025-07-12 00:00:26,201 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:26,201 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:26,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:26,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:26,670 - INFO - Response - Page 7:
2025-07-12 00:00:26,873 - INFO - 第 7 页获取到 56 条记录
2025-07-12 00:00:26,873 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 00:00:26,873 - INFO - 获取到 656 条表单数据
2025-07-12 00:00:26,873 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 00:00:26,889 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 00:00:26,889 - INFO - 开始处理日期: 2025-05
2025-07-12 00:00:26,889 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:26,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:26,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:27,420 - INFO - Response - Page 1:
2025-07-12 00:00:27,623 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:27,623 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:27,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:27,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:28,139 - INFO - Response - Page 2:
2025-07-12 00:00:28,342 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:28,342 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:28,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:28,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:28,858 - INFO - Response - Page 3:
2025-07-12 00:00:29,061 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:29,061 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:29,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:29,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:29,623 - INFO - Response - Page 4:
2025-07-12 00:00:29,826 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:29,826 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:29,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:29,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:30,295 - INFO - Response - Page 5:
2025-07-12 00:00:30,498 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:30,498 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:30,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:30,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:31,061 - INFO - Response - Page 6:
2025-07-12 00:00:31,264 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:31,264 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:31,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:31,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:31,748 - INFO - Response - Page 7:
2025-07-12 00:00:31,951 - INFO - 第 7 页获取到 65 条记录
2025-07-12 00:00:31,951 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 00:00:31,951 - INFO - 获取到 665 条表单数据
2025-07-12 00:00:31,951 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 00:00:31,967 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 00:00:31,967 - INFO - 开始处理日期: 2025-06
2025-07-12 00:00:31,967 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:31,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:31,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:32,451 - INFO - Response - Page 1:
2025-07-12 00:00:32,654 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:32,654 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:32,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:32,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:33,233 - INFO - Response - Page 2:
2025-07-12 00:00:33,436 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:33,436 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:33,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:33,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:33,982 - INFO - Response - Page 3:
2025-07-12 00:00:34,186 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:34,186 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:34,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:34,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:34,826 - INFO - Response - Page 4:
2025-07-12 00:00:35,029 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:35,029 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:35,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:35,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:35,514 - INFO - Response - Page 5:
2025-07-12 00:00:35,717 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:35,717 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:35,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:35,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:36,201 - INFO - Response - Page 6:
2025-07-12 00:00:36,404 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:36,404 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:36,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:36,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:36,795 - INFO - Response - Page 7:
2025-07-12 00:00:36,998 - INFO - 第 7 页获取到 33 条记录
2025-07-12 00:00:36,998 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 00:00:36,998 - INFO - 获取到 633 条表单数据
2025-07-12 00:00:36,998 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 00:00:37,014 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 00:00:37,014 - INFO - 开始处理日期: 2025-07
2025-07-12 00:00:37,014 - INFO - Request Parameters - Page 1:
2025-07-12 00:00:37,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:37,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:37,561 - INFO - Response - Page 1:
2025-07-12 00:00:37,764 - INFO - 第 1 页获取到 100 条记录
2025-07-12 00:00:37,764 - INFO - Request Parameters - Page 2:
2025-07-12 00:00:37,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:37,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:38,248 - INFO - Response - Page 2:
2025-07-12 00:00:38,451 - INFO - 第 2 页获取到 100 条记录
2025-07-12 00:00:38,451 - INFO - Request Parameters - Page 3:
2025-07-12 00:00:38,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:38,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:38,951 - INFO - Response - Page 3:
2025-07-12 00:00:39,154 - INFO - 第 3 页获取到 100 条记录
2025-07-12 00:00:39,154 - INFO - Request Parameters - Page 4:
2025-07-12 00:00:39,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:39,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:39,670 - INFO - Response - Page 4:
2025-07-12 00:00:39,873 - INFO - 第 4 页获取到 100 条记录
2025-07-12 00:00:39,873 - INFO - Request Parameters - Page 5:
2025-07-12 00:00:39,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:39,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:40,389 - INFO - Response - Page 5:
2025-07-12 00:00:40,592 - INFO - 第 5 页获取到 100 条记录
2025-07-12 00:00:40,592 - INFO - Request Parameters - Page 6:
2025-07-12 00:00:40,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:40,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:41,092 - INFO - Response - Page 6:
2025-07-12 00:00:41,295 - INFO - 第 6 页获取到 100 条记录
2025-07-12 00:00:41,295 - INFO - Request Parameters - Page 7:
2025-07-12 00:00:41,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 00:00:41,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 00:00:41,545 - INFO - Response - Page 7:
2025-07-12 00:00:41,748 - INFO - 第 7 页获取到 9 条记录
2025-07-12 00:00:41,748 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 00:00:41,748 - INFO - 获取到 609 条表单数据
2025-07-12 00:00:41,748 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 00:00:41,748 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV7
2025-07-12 00:00:42,186 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV7
2025-07-12 00:00:42,186 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23429.15, 'new_value': 28437.77}, {'field': 'offline_amount', 'old_value': 34951.01, 'new_value': 35011.01}, {'field': 'total_amount', 'old_value': 58380.16, 'new_value': 63448.78}, {'field': 'order_count', 'old_value': 2076, 'new_value': 2270}]
2025-07-12 00:00:42,186 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU5
2025-07-12 00:00:42,607 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMU5
2025-07-12 00:00:42,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4615.0, 'new_value': 4773.0}, {'field': 'total_amount', 'old_value': 4615.0, 'new_value': 4773.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-07-12 00:00:42,607 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX5
2025-07-12 00:00:43,076 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX5
2025-07-12 00:00:43,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17096.6, 'new_value': 17216.3}, {'field': 'total_amount', 'old_value': 17096.6, 'new_value': 17216.3}, {'field': 'order_count', 'old_value': 51, 'new_value': 54}]
2025-07-12 00:00:43,076 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW7
2025-07-12 00:00:43,623 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW7
2025-07-12 00:00:43,623 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34311.73, 'new_value': 37923.9}, {'field': 'offline_amount', 'old_value': 177311.21, 'new_value': 195365.71}, {'field': 'total_amount', 'old_value': 211622.94, 'new_value': 233289.61}, {'field': 'order_count', 'old_value': 2867, 'new_value': 3197}]
2025-07-12 00:00:43,623 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMQ8
2025-07-12 00:00:44,201 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMQ8
2025-07-12 00:00:44,201 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22246.47, 'new_value': 24176.38}, {'field': 'offline_amount', 'old_value': 266133.65, 'new_value': 301058.85}, {'field': 'total_amount', 'old_value': 288380.12, 'new_value': 325235.23}, {'field': 'order_count', 'old_value': 1063, 'new_value': 1208}]
2025-07-12 00:00:44,201 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ7
2025-07-12 00:00:44,717 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ7
2025-07-12 00:00:44,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30175.0, 'new_value': 32074.0}, {'field': 'total_amount', 'old_value': 32059.2, 'new_value': 33958.2}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-07-12 00:00:44,717 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM18
2025-07-12 00:00:45,201 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM18
2025-07-12 00:00:45,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179778.19, 'new_value': 204809.62}, {'field': 'total_amount', 'old_value': 247317.66, 'new_value': 272349.09}, {'field': 'order_count', 'old_value': 904, 'new_value': 994}]
2025-07-12 00:00:45,201 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8
2025-07-12 00:00:45,592 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMR8
2025-07-12 00:00:45,592 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41122.0, 'new_value': 47432.0}, {'field': 'offline_amount', 'old_value': 107565.0, 'new_value': 112246.0}, {'field': 'total_amount', 'old_value': 148687.0, 'new_value': 159678.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 169}]
2025-07-12 00:00:45,592 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG6
2025-07-12 00:00:46,045 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG6
2025-07-12 00:00:46,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51491.16, 'new_value': 57093.6}, {'field': 'total_amount', 'old_value': 51491.16, 'new_value': 57093.6}, {'field': 'order_count', 'old_value': 695, 'new_value': 816}]
2025-07-12 00:00:46,045 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMS8
2025-07-12 00:00:46,514 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMS8
2025-07-12 00:00:46,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 590489.17, 'new_value': 654498.38}, {'field': 'total_amount', 'old_value': 590489.17, 'new_value': 654498.38}, {'field': 'order_count', 'old_value': 6873, 'new_value': 7558}]
2025-07-12 00:00:46,514 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM38
2025-07-12 00:00:46,967 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM38
2025-07-12 00:00:46,967 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44646.6, 'new_value': 48307.6}, {'field': 'offline_amount', 'old_value': 29482.9, 'new_value': 32252.0}, {'field': 'total_amount', 'old_value': 74129.5, 'new_value': 80559.6}, {'field': 'order_count', 'old_value': 503, 'new_value': 551}]
2025-07-12 00:00:46,967 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR6
2025-07-12 00:00:47,436 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR6
2025-07-12 00:00:47,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41134.77, 'new_value': 44421.82}, {'field': 'total_amount', 'old_value': 41134.77, 'new_value': 44421.82}, {'field': 'order_count', 'old_value': 1557, 'new_value': 1690}]
2025-07-12 00:00:47,436 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMU8
2025-07-12 00:00:47,904 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMU8
2025-07-12 00:00:47,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43323.47, 'new_value': 48686.4}, {'field': 'offline_amount', 'old_value': 40525.56, 'new_value': 44517.41}, {'field': 'total_amount', 'old_value': 83849.03, 'new_value': 93203.81}, {'field': 'order_count', 'old_value': 4574, 'new_value': 5137}]
2025-07-12 00:00:47,904 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX6
2025-07-12 00:00:48,326 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMX6
2025-07-12 00:00:48,326 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50828.25, 'new_value': 55597.18}, {'field': 'offline_amount', 'old_value': 112330.48, 'new_value': 122576.64}, {'field': 'total_amount', 'old_value': 163158.73, 'new_value': 178173.82}, {'field': 'order_count', 'old_value': 5770, 'new_value': 6325}]
2025-07-12 00:00:48,326 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM07
2025-07-12 00:00:48,748 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM07
2025-07-12 00:00:48,748 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7878.0, 'new_value': 8644.0}, {'field': 'total_amount', 'old_value': 7878.0, 'new_value': 8644.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 57}]
2025-07-12 00:00:48,748 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM17
2025-07-12 00:00:49,170 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM17
2025-07-12 00:00:49,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202026.28, 'new_value': 224931.94}, {'field': 'total_amount', 'old_value': 202026.28, 'new_value': 224931.94}, {'field': 'order_count', 'old_value': 3134, 'new_value': 3434}]
2025-07-12 00:00:49,170 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM67
2025-07-12 00:00:49,623 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM67
2025-07-12 00:00:49,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3695.62, 'new_value': 4063.12}, {'field': 'total_amount', 'old_value': 8180.2, 'new_value': 8547.7}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-07-12 00:00:49,623 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM97
2025-07-12 00:00:50,076 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM97
2025-07-12 00:00:50,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48531.0, 'new_value': 56437.0}, {'field': 'total_amount', 'old_value': 53531.0, 'new_value': 61437.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-07-12 00:00:50,076 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM88
2025-07-12 00:00:50,482 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM88
2025-07-12 00:00:50,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44184.8, 'new_value': 50410.6}, {'field': 'total_amount', 'old_value': 44184.8, 'new_value': 50410.6}, {'field': 'order_count', 'old_value': 75, 'new_value': 84}]
2025-07-12 00:00:50,482 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMA8
2025-07-12 00:00:50,951 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMA8
2025-07-12 00:00:50,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51374.0, 'new_value': 53800.0}, {'field': 'total_amount', 'old_value': 55444.0, 'new_value': 57870.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-07-12 00:00:50,951 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMB8
2025-07-12 00:00:51,389 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMB8
2025-07-12 00:00:51,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 3800.0}, {'field': 'total_amount', 'old_value': 3000.0, 'new_value': 3800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-07-12 00:00:51,389 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMC8
2025-07-12 00:00:51,873 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMC8
2025-07-12 00:00:51,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96029.01, 'new_value': 104851.72}, {'field': 'offline_amount', 'old_value': 273875.61, 'new_value': 295403.93}, {'field': 'total_amount', 'old_value': 369904.62, 'new_value': 400255.65}, {'field': 'order_count', 'old_value': 2310, 'new_value': 2506}]
2025-07-12 00:00:51,873 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCME8
2025-07-12 00:00:52,310 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCME8
2025-07-12 00:00:52,310 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191435.0, 'new_value': 197276.0}, {'field': 'total_amount', 'old_value': 191435.0, 'new_value': 197276.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-07-12 00:00:52,310 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB
2025-07-12 00:00:52,748 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB
2025-07-12 00:00:52,748 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10658.0, 'new_value': 10953.0}, {'field': 'total_amount', 'old_value': 10658.0, 'new_value': 10953.0}]
2025-07-12 00:00:52,748 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMF8
2025-07-12 00:00:53,185 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMF8
2025-07-12 00:00:53,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16257.0, 'new_value': 16565.0}, {'field': 'total_amount', 'old_value': 16257.0, 'new_value': 16565.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-07-12 00:00:53,185 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMH8
2025-07-12 00:00:53,623 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMH8
2025-07-12 00:00:53,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24338.0, 'new_value': 26565.0}, {'field': 'total_amount', 'old_value': 24338.0, 'new_value': 26565.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 75}]
2025-07-12 00:00:53,623 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO
2025-07-12 00:00:54,092 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO
2025-07-12 00:00:54,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29629.9, 'new_value': 32275.35}, {'field': 'offline_amount', 'old_value': 31812.11, 'new_value': 35113.78}, {'field': 'total_amount', 'old_value': 61442.01, 'new_value': 67389.13}, {'field': 'order_count', 'old_value': 2510, 'new_value': 2760}]
2025-07-12 00:00:54,092 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMK8
2025-07-12 00:00:54,498 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMK8
2025-07-12 00:00:54,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148594.0, 'new_value': 170863.0}, {'field': 'total_amount', 'old_value': 182314.0, 'new_value': 204583.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 73}]
2025-07-12 00:00:54,498 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCML8
2025-07-12 00:00:54,935 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCML8
2025-07-12 00:00:54,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120104.0, 'new_value': 121427.0}, {'field': 'total_amount', 'old_value': 120104.0, 'new_value': 121427.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 108}]
2025-07-12 00:00:54,935 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX
2025-07-12 00:00:55,373 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX
2025-07-12 00:00:55,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8567.0, 'new_value': 8926.0}, {'field': 'total_amount', 'old_value': 8567.0, 'new_value': 8926.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-07-12 00:00:55,373 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY
2025-07-12 00:00:55,842 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY
2025-07-12 00:00:55,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81483.0, 'new_value': 96709.0}, {'field': 'total_amount', 'old_value': 81483.0, 'new_value': 96709.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-07-12 00:00:55,842 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM11
2025-07-12 00:00:56,279 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM11
2025-07-12 00:00:56,279 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178288.0, 'new_value': 193558.0}, {'field': 'total_amount', 'old_value': 178290.0, 'new_value': 193560.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-07-12 00:00:56,279 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM61
2025-07-12 00:00:56,701 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM61
2025-07-12 00:00:56,701 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1.0}, {'field': 'offline_amount', 'old_value': 56159.93, 'new_value': 59086.03}, {'field': 'total_amount', 'old_value': 56159.93, 'new_value': 59087.03}, {'field': 'order_count', 'old_value': 714, 'new_value': 775}]
2025-07-12 00:00:56,701 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM81
2025-07-12 00:00:57,107 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM81
2025-07-12 00:00:57,107 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23226.08, 'new_value': 25044.97}, {'field': 'offline_amount', 'old_value': 7198.57, 'new_value': 7522.37}, {'field': 'total_amount', 'old_value': 30424.65, 'new_value': 32567.34}, {'field': 'order_count', 'old_value': 1576, 'new_value': 1717}]
2025-07-12 00:00:57,107 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA1
2025-07-12 00:00:57,560 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA1
2025-07-12 00:00:57,560 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41654.62, 'new_value': 45769.47}, {'field': 'offline_amount', 'old_value': 11186.67, 'new_value': 11800.83}, {'field': 'total_amount', 'old_value': 52841.29, 'new_value': 57570.3}, {'field': 'order_count', 'old_value': 3261, 'new_value': 3526}]
2025-07-12 00:00:57,560 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH1
2025-07-12 00:00:58,014 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH1
2025-07-12 00:00:58,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53677.23, 'new_value': 60025.03}, {'field': 'offline_amount', 'old_value': 93897.13, 'new_value': 101648.21}, {'field': 'total_amount', 'old_value': 147574.36, 'new_value': 161673.24}, {'field': 'order_count', 'old_value': 2684, 'new_value': 2828}]
2025-07-12 00:00:58,014 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ1
2025-07-12 00:00:58,467 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ1
2025-07-12 00:00:58,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117508.0, 'new_value': 129027.5}, {'field': 'total_amount', 'old_value': 117508.0, 'new_value': 129027.5}, {'field': 'order_count', 'old_value': 1280, 'new_value': 1411}]
2025-07-12 00:00:58,467 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM1
2025-07-12 00:00:58,951 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMM1
2025-07-12 00:00:58,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25760.0, 'new_value': 28228.0}, {'field': 'total_amount', 'old_value': 25760.0, 'new_value': 28228.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 32}]
2025-07-12 00:00:58,951 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN1
2025-07-12 00:00:59,435 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN1
2025-07-12 00:00:59,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33818.4, 'new_value': 37505.7}, {'field': 'total_amount', 'old_value': 33818.4, 'new_value': 37505.7}, {'field': 'order_count', 'old_value': 117, 'new_value': 129}]
2025-07-12 00:00:59,435 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO1
2025-07-12 00:00:59,873 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMO1
2025-07-12 00:00:59,873 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13930.26, 'new_value': 15819.8}, {'field': 'offline_amount', 'old_value': 126561.51, 'new_value': 139020.38}, {'field': 'total_amount', 'old_value': 140491.77, 'new_value': 154840.18}, {'field': 'order_count', 'old_value': 15070, 'new_value': 15400}]
2025-07-12 00:00:59,873 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ1
2025-07-12 00:01:00,248 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ1
2025-07-12 00:01:00,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8103.3, 'new_value': 9715.4}, {'field': 'total_amount', 'old_value': 8103.3, 'new_value': 9715.4}, {'field': 'order_count', 'old_value': 59, 'new_value': 69}]
2025-07-12 00:01:00,248 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU1
2025-07-12 00:01:00,717 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU1
2025-07-12 00:01:00,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348010.0, 'new_value': 358126.0}, {'field': 'total_amount', 'old_value': 348010.0, 'new_value': 358126.0}, {'field': 'order_count', 'old_value': 425, 'new_value': 445}]
2025-07-12 00:01:00,717 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV1
2025-07-12 00:01:01,185 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV1
2025-07-12 00:01:01,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109860.68, 'new_value': 119138.0}, {'field': 'offline_amount', 'old_value': 25206.29, 'new_value': 28356.39}, {'field': 'total_amount', 'old_value': 135066.97, 'new_value': 147494.39}, {'field': 'order_count', 'old_value': 711, 'new_value': 756}]
2025-07-12 00:01:01,185 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM02
2025-07-12 00:01:01,639 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM02
2025-07-12 00:01:01,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16957.0, 'new_value': 17962.0}, {'field': 'total_amount', 'old_value': 16957.0, 'new_value': 17962.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 61}]
2025-07-12 00:01:01,639 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP8
2025-07-12 00:01:02,092 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP8
2025-07-12 00:01:02,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80537.72, 'new_value': 88870.12}, {'field': 'total_amount', 'old_value': 80537.72, 'new_value': 88870.12}, {'field': 'order_count', 'old_value': 166, 'new_value': 176}]
2025-07-12 00:01:02,092 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ8
2025-07-12 00:01:02,560 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ8
2025-07-12 00:01:02,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138292.02, 'new_value': 152722.77}, {'field': 'total_amount', 'old_value': 147975.66, 'new_value': 162406.41}, {'field': 'order_count', 'old_value': 782, 'new_value': 857}]
2025-07-12 00:01:02,560 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM62
2025-07-12 00:01:02,982 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM62
2025-07-12 00:01:02,982 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9433.4, 'new_value': 10715.4}, {'field': 'offline_amount', 'old_value': 4055.8, 'new_value': 4428.5}, {'field': 'total_amount', 'old_value': 13489.2, 'new_value': 15143.9}, {'field': 'order_count', 'old_value': 814, 'new_value': 870}]
2025-07-12 00:01:02,982 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR8
2025-07-12 00:01:03,373 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR8
2025-07-12 00:01:03,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56567.0, 'new_value': 61705.0}, {'field': 'total_amount', 'old_value': 56567.0, 'new_value': 61705.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 101}]
2025-07-12 00:01:03,373 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM92
2025-07-12 00:01:03,795 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM92
2025-07-12 00:01:03,795 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7188.35, 'new_value': 8314.14}, {'field': 'offline_amount', 'old_value': 27056.75, 'new_value': 29153.43}, {'field': 'total_amount', 'old_value': 34245.1, 'new_value': 37467.57}, {'field': 'order_count', 'old_value': 1510, 'new_value': 1667}]
2025-07-12 00:01:03,795 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT8
2025-07-12 00:01:04,310 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT8
2025-07-12 00:01:04,310 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55757.54, 'new_value': 60460.88}, {'field': 'total_amount', 'old_value': 55757.54, 'new_value': 60460.88}, {'field': 'order_count', 'old_value': 6113, 'new_value': 6552}]
2025-07-12 00:01:04,310 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD2
2025-07-12 00:01:04,779 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD2
2025-07-12 00:01:04,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33883.96, 'new_value': 36857.24}, {'field': 'offline_amount', 'old_value': 29802.78, 'new_value': 33727.22}, {'field': 'total_amount', 'old_value': 63686.74, 'new_value': 70584.46}, {'field': 'order_count', 'old_value': 2961, 'new_value': 3262}]
2025-07-12 00:01:04,779 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME2
2025-07-12 00:01:05,217 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME2
2025-07-12 00:01:05,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23517.02, 'new_value': 25794.19}, {'field': 'total_amount', 'old_value': 23517.02, 'new_value': 25794.19}, {'field': 'order_count', 'old_value': 888, 'new_value': 966}]
2025-07-12 00:01:05,217 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG2
2025-07-12 00:01:05,685 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMG2
2025-07-12 00:01:05,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40094.9, 'new_value': 41811.9}, {'field': 'total_amount', 'old_value': 55331.6, 'new_value': 57048.6}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-07-12 00:01:05,685 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV8
2025-07-12 00:01:06,123 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMV8
2025-07-12 00:01:06,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113673.0, 'new_value': 116950.0}, {'field': 'total_amount', 'old_value': 113673.0, 'new_value': 116950.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 79}]
2025-07-12 00:01:06,123 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW8
2025-07-12 00:01:06,545 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMW8
2025-07-12 00:01:06,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195587.0, 'new_value': 222188.0}, {'field': 'total_amount', 'old_value': 195587.0, 'new_value': 222188.0}, {'field': 'order_count', 'old_value': 39067, 'new_value': 39073}]
2025-07-12 00:01:06,545 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMV8
2025-07-12 00:01:06,951 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMV8
2025-07-12 00:01:06,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49036.0, 'new_value': 54585.5}, {'field': 'total_amount', 'old_value': 49036.0, 'new_value': 54585.5}, {'field': 'order_count', 'old_value': 298, 'new_value': 334}]
2025-07-12 00:01:06,951 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML2
2025-07-12 00:01:07,467 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML2
2025-07-12 00:01:07,467 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4181.3, 'new_value': 4940.21}, {'field': 'offline_amount', 'old_value': 15298.16, 'new_value': 17348.44}, {'field': 'total_amount', 'old_value': 19479.46, 'new_value': 22288.65}, {'field': 'order_count', 'old_value': 697, 'new_value': 783}]
2025-07-12 00:01:07,467 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS2
2025-07-12 00:01:07,904 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS2
2025-07-12 00:01:07,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194417.9, 'new_value': 212703.8}, {'field': 'total_amount', 'old_value': 194417.9, 'new_value': 212703.8}, {'field': 'order_count', 'old_value': 895, 'new_value': 972}]
2025-07-12 00:01:07,904 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY8
2025-07-12 00:01:08,342 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY8
2025-07-12 00:01:08,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171605.0, 'new_value': 183738.0}, {'field': 'total_amount', 'old_value': 171605.0, 'new_value': 183738.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-07-12 00:01:08,342 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMN
2025-07-12 00:01:08,857 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMN
2025-07-12 00:01:08,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8494.0, 'new_value': 9144.0}, {'field': 'total_amount', 'old_value': 8494.0, 'new_value': 9144.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 42}]
2025-07-12 00:01:08,857 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM29
2025-07-12 00:01:09,295 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM29
2025-07-12 00:01:09,295 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29995.0, 'new_value': 31454.0}, {'field': 'total_amount', 'old_value': 29995.0, 'new_value': 31454.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-07-12 00:01:09,295 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMU
2025-07-12 00:01:09,701 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMU
2025-07-12 00:01:09,701 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129604.61, 'new_value': 141397.57}, {'field': 'offline_amount', 'old_value': 7527.9, 'new_value': 7966.7}, {'field': 'total_amount', 'old_value': 137132.51, 'new_value': 149364.27}, {'field': 'order_count', 'old_value': 5341, 'new_value': 5797}]
2025-07-12 00:01:09,701 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ
2025-07-12 00:01:10,092 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ
2025-07-12 00:01:10,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28454.13, 'new_value': 30517.0}, {'field': 'offline_amount', 'old_value': 41522.52, 'new_value': 45471.28}, {'field': 'total_amount', 'old_value': 69976.65, 'new_value': 75988.28}, {'field': 'order_count', 'old_value': 856, 'new_value': 933}]
2025-07-12 00:01:10,092 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM41
2025-07-12 00:01:10,560 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM41
2025-07-12 00:01:10,560 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29577.2, 'new_value': 44243.6}, {'field': 'offline_amount', 'old_value': 61114.7, 'new_value': 65164.6}, {'field': 'total_amount', 'old_value': 90691.9, 'new_value': 109408.2}, {'field': 'order_count', 'old_value': 1811, 'new_value': 2186}]
2025-07-12 00:01:10,560 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA1
2025-07-12 00:01:11,029 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMA1
2025-07-12 00:01:11,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53261.2, 'new_value': 59102.61}, {'field': 'offline_amount', 'old_value': 20324.25, 'new_value': 22646.73}, {'field': 'total_amount', 'old_value': 73585.45, 'new_value': 81749.34}, {'field': 'order_count', 'old_value': 4514, 'new_value': 5005}]
2025-07-12 00:01:11,029 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMZ8
2025-07-12 00:01:11,435 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMZ8
2025-07-12 00:01:11,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 310961.87, 'new_value': 340526.09}, {'field': 'total_amount', 'old_value': 310961.87, 'new_value': 340526.09}, {'field': 'order_count', 'old_value': 2424, 'new_value': 2638}]
2025-07-12 00:01:11,451 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM52
2025-07-12 00:01:11,935 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM52
2025-07-12 00:01:11,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41256.89, 'new_value': 45825.61}, {'field': 'offline_amount', 'old_value': 133753.33, 'new_value': 147519.72}, {'field': 'total_amount', 'old_value': 175010.22, 'new_value': 193345.33}, {'field': 'order_count', 'old_value': 1734, 'new_value': 1886}]
2025-07-12 00:01:11,935 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC2
2025-07-12 00:01:12,342 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC2
2025-07-12 00:01:12,342 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20645.43, 'new_value': 23041.07}, {'field': 'offline_amount', 'old_value': 14000.0, 'new_value': 15000.0}, {'field': 'total_amount', 'old_value': 34645.43, 'new_value': 38041.07}, {'field': 'order_count', 'old_value': 1692, 'new_value': 1868}]
2025-07-12 00:01:12,342 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN2
2025-07-12 00:01:12,795 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN2
2025-07-12 00:01:12,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60532.0, 'new_value': 64364.0}, {'field': 'total_amount', 'old_value': 60532.0, 'new_value': 64364.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-07-12 00:01:12,795 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM71
2025-07-12 00:01:13,263 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM71
2025-07-12 00:01:13,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1280.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1280.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 00:01:13,263 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM62
2025-07-12 00:01:13,717 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM62
2025-07-12 00:01:13,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273760.0, 'new_value': 305751.0}, {'field': 'total_amount', 'old_value': 273760.0, 'new_value': 305751.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 60}]
2025-07-12 00:01:13,717 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMR4
2025-07-12 00:01:14,154 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMR4
2025-07-12 00:01:14,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1451.36, 'new_value': 1501.36}, {'field': 'offline_amount', 'old_value': 8673.93, 'new_value': 9649.28}, {'field': 'total_amount', 'old_value': 10125.29, 'new_value': 11150.64}, {'field': 'order_count', 'old_value': 195, 'new_value': 215}]
2025-07-12 00:01:14,154 - INFO - 开始更新记录 - 表单实例ID: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-07-12 00:01:14,638 - INFO - 更新表单数据成功: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-07-12 00:01:14,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228071.0, 'new_value': 281363.4}, {'field': 'total_amount', 'old_value': 228071.0, 'new_value': 281363.4}, {'field': 'order_count', 'old_value': 82, 'new_value': 104}]
2025-07-12 00:01:14,638 - INFO - 日期 2025-07 处理完成 - 更新: 73 条，插入: 0 条，错误: 0 条
2025-07-12 00:01:14,638 - INFO - 数据同步完成！更新: 73 条，插入: 0 条，错误: 0 条
2025-07-12 00:01:14,638 - INFO - =================同步完成====================
2025-07-12 03:00:03,364 - INFO - =================使用默认全量同步=============
2025-07-12 03:00:05,332 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 03:00:05,332 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 03:00:05,364 - INFO - 开始处理日期: 2025-01
2025-07-12 03:00:05,379 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:05,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:05,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:06,832 - INFO - Response - Page 1:
2025-07-12 03:00:07,035 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:07,035 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:07,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:07,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:07,598 - INFO - Response - Page 2:
2025-07-12 03:00:07,801 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:07,801 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:07,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:07,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:08,723 - INFO - Response - Page 3:
2025-07-12 03:00:08,926 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:08,926 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:08,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:08,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:09,395 - INFO - Response - Page 4:
2025-07-12 03:00:09,598 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:09,598 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:09,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:09,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:10,082 - INFO - Response - Page 5:
2025-07-12 03:00:10,285 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:10,285 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:10,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:10,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:10,801 - INFO - Response - Page 6:
2025-07-12 03:00:11,004 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:11,004 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:11,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:11,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:11,582 - INFO - Response - Page 7:
2025-07-12 03:00:11,785 - INFO - 第 7 页获取到 82 条记录
2025-07-12 03:00:11,785 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 03:00:11,785 - INFO - 获取到 682 条表单数据
2025-07-12 03:00:11,785 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 03:00:11,801 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:11,801 - INFO - 开始处理日期: 2025-02
2025-07-12 03:00:11,801 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:11,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:11,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:12,301 - INFO - Response - Page 1:
2025-07-12 03:00:12,520 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:12,520 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:12,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:12,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:13,020 - INFO - Response - Page 2:
2025-07-12 03:00:13,238 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:13,238 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:13,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:13,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:13,738 - INFO - Response - Page 3:
2025-07-12 03:00:13,942 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:13,942 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:13,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:13,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:14,442 - INFO - Response - Page 4:
2025-07-12 03:00:14,645 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:14,645 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:14,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:14,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:15,098 - INFO - Response - Page 5:
2025-07-12 03:00:15,301 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:15,301 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:15,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:15,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:15,832 - INFO - Response - Page 6:
2025-07-12 03:00:16,035 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:16,035 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:16,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:16,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:16,488 - INFO - Response - Page 7:
2025-07-12 03:00:16,692 - INFO - 第 7 页获取到 70 条记录
2025-07-12 03:00:16,692 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 03:00:16,692 - INFO - 获取到 670 条表单数据
2025-07-12 03:00:16,692 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 03:00:16,707 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:16,707 - INFO - 开始处理日期: 2025-03
2025-07-12 03:00:16,707 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:16,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:16,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:17,254 - INFO - Response - Page 1:
2025-07-12 03:00:17,457 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:17,457 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:17,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:17,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:17,942 - INFO - Response - Page 2:
2025-07-12 03:00:18,145 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:18,145 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:18,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:18,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:18,629 - INFO - Response - Page 3:
2025-07-12 03:00:18,832 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:18,832 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:18,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:18,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:19,332 - INFO - Response - Page 4:
2025-07-12 03:00:19,535 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:19,535 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:19,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:19,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:19,957 - INFO - Response - Page 5:
2025-07-12 03:00:20,160 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:20,160 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:20,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:20,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:20,629 - INFO - Response - Page 6:
2025-07-12 03:00:20,832 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:20,832 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:20,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:20,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:21,238 - INFO - Response - Page 7:
2025-07-12 03:00:21,442 - INFO - 第 7 页获取到 61 条记录
2025-07-12 03:00:21,442 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 03:00:21,442 - INFO - 获取到 661 条表单数据
2025-07-12 03:00:21,442 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 03:00:21,457 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:21,457 - INFO - 开始处理日期: 2025-04
2025-07-12 03:00:21,457 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:21,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:21,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:21,942 - INFO - Response - Page 1:
2025-07-12 03:00:22,145 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:22,145 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:22,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:22,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:22,676 - INFO - Response - Page 2:
2025-07-12 03:00:22,879 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:22,879 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:22,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:22,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:23,301 - INFO - Response - Page 3:
2025-07-12 03:00:23,504 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:23,504 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:23,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:23,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:24,067 - INFO - Response - Page 4:
2025-07-12 03:00:24,270 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:24,270 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:24,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:24,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:24,801 - INFO - Response - Page 5:
2025-07-12 03:00:25,004 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:25,004 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:25,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:25,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:25,442 - INFO - Response - Page 6:
2025-07-12 03:00:25,645 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:25,645 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:25,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:25,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:26,129 - INFO - Response - Page 7:
2025-07-12 03:00:26,332 - INFO - 第 7 页获取到 56 条记录
2025-07-12 03:00:26,332 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 03:00:26,332 - INFO - 获取到 656 条表单数据
2025-07-12 03:00:26,332 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 03:00:26,348 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:26,348 - INFO - 开始处理日期: 2025-05
2025-07-12 03:00:26,348 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:26,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:26,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:26,863 - INFO - Response - Page 1:
2025-07-12 03:00:27,067 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:27,067 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:27,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:27,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:27,551 - INFO - Response - Page 2:
2025-07-12 03:00:27,754 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:27,754 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:27,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:27,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:28,317 - INFO - Response - Page 3:
2025-07-12 03:00:28,520 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:28,520 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:28,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:28,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:28,973 - INFO - Response - Page 4:
2025-07-12 03:00:29,176 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:29,176 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:29,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:29,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:29,676 - INFO - Response - Page 5:
2025-07-12 03:00:29,879 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:29,879 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:29,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:29,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:30,379 - INFO - Response - Page 6:
2025-07-12 03:00:30,582 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:30,582 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:30,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:30,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:31,035 - INFO - Response - Page 7:
2025-07-12 03:00:31,238 - INFO - 第 7 页获取到 65 条记录
2025-07-12 03:00:31,238 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 03:00:31,238 - INFO - 获取到 665 条表单数据
2025-07-12 03:00:31,238 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 03:00:31,254 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:31,254 - INFO - 开始处理日期: 2025-06
2025-07-12 03:00:31,254 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:31,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:31,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:31,895 - INFO - Response - Page 1:
2025-07-12 03:00:32,098 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:32,098 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:32,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:32,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:32,629 - INFO - Response - Page 2:
2025-07-12 03:00:32,832 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:32,832 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:32,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:32,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:33,395 - INFO - Response - Page 3:
2025-07-12 03:00:33,598 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:33,598 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:33,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:33,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:34,082 - INFO - Response - Page 4:
2025-07-12 03:00:34,285 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:34,285 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:34,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:34,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:34,785 - INFO - Response - Page 5:
2025-07-12 03:00:34,988 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:34,988 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:34,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:34,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:35,410 - INFO - Response - Page 6:
2025-07-12 03:00:35,613 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:35,613 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:35,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:35,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:35,988 - INFO - Response - Page 7:
2025-07-12 03:00:36,191 - INFO - 第 7 页获取到 33 条记录
2025-07-12 03:00:36,191 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 03:00:36,191 - INFO - 获取到 633 条表单数据
2025-07-12 03:00:36,191 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 03:00:36,207 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:36,207 - INFO - 开始处理日期: 2025-07
2025-07-12 03:00:36,207 - INFO - Request Parameters - Page 1:
2025-07-12 03:00:36,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:36,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:36,723 - INFO - Response - Page 1:
2025-07-12 03:00:36,926 - INFO - 第 1 页获取到 100 条记录
2025-07-12 03:00:36,926 - INFO - Request Parameters - Page 2:
2025-07-12 03:00:36,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:36,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:37,441 - INFO - Response - Page 2:
2025-07-12 03:00:37,645 - INFO - 第 2 页获取到 100 条记录
2025-07-12 03:00:37,645 - INFO - Request Parameters - Page 3:
2025-07-12 03:00:37,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:37,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:38,129 - INFO - Response - Page 3:
2025-07-12 03:00:38,332 - INFO - 第 3 页获取到 100 条记录
2025-07-12 03:00:38,332 - INFO - Request Parameters - Page 4:
2025-07-12 03:00:38,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:38,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:38,848 - INFO - Response - Page 4:
2025-07-12 03:00:39,051 - INFO - 第 4 页获取到 100 条记录
2025-07-12 03:00:39,051 - INFO - Request Parameters - Page 5:
2025-07-12 03:00:39,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:39,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:39,566 - INFO - Response - Page 5:
2025-07-12 03:00:39,770 - INFO - 第 5 页获取到 100 条记录
2025-07-12 03:00:39,770 - INFO - Request Parameters - Page 6:
2025-07-12 03:00:39,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:39,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:40,332 - INFO - Response - Page 6:
2025-07-12 03:00:40,535 - INFO - 第 6 页获取到 100 条记录
2025-07-12 03:00:40,535 - INFO - Request Parameters - Page 7:
2025-07-12 03:00:40,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 03:00:40,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 03:00:40,863 - INFO - Response - Page 7:
2025-07-12 03:00:41,066 - INFO - 第 7 页获取到 9 条记录
2025-07-12 03:00:41,066 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 03:00:41,066 - INFO - 获取到 609 条表单数据
2025-07-12 03:00:41,066 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 03:00:41,082 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP1
2025-07-12 03:00:41,613 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP1
2025-07-12 03:00:41,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2020.31, 'new_value': 2157.89}, {'field': 'offline_amount', 'old_value': 37341.66, 'new_value': 40915.64}, {'field': 'total_amount', 'old_value': 39361.97, 'new_value': 43073.53}, {'field': 'order_count', 'old_value': 1337, 'new_value': 1445}]
2025-07-12 03:00:41,613 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY1
2025-07-12 03:00:42,020 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMY1
2025-07-12 03:00:42,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49644.0, 'new_value': 53758.0}, {'field': 'total_amount', 'old_value': 49644.0, 'new_value': 53758.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 106}]
2025-07-12 03:00:42,020 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ1
2025-07-12 03:00:42,504 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ1
2025-07-12 03:00:42,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68674.0, 'new_value': 80773.0}, {'field': 'total_amount', 'old_value': 84174.0, 'new_value': 96273.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 377}]
2025-07-12 03:00:42,504 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM22
2025-07-12 03:00:43,113 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM22
2025-07-12 03:00:43,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7773.0, 'new_value': 8122.0}, {'field': 'total_amount', 'old_value': 10773.0, 'new_value': 11122.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-07-12 03:00:43,113 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM42
2025-07-12 03:00:43,566 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM42
2025-07-12 03:00:43,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177617.36, 'new_value': 195312.17}, {'field': 'total_amount', 'old_value': 177617.36, 'new_value': 195312.17}, {'field': 'order_count', 'old_value': 7094, 'new_value': 7827}]
2025-07-12 03:00:43,566 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR2
2025-07-12 03:00:43,973 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR2
2025-07-12 03:00:43,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23428.74, 'new_value': 25934.13}, {'field': 'offline_amount', 'old_value': 90145.66, 'new_value': 97620.45}, {'field': 'total_amount', 'old_value': 113574.4, 'new_value': 123554.58}, {'field': 'order_count', 'old_value': 2572, 'new_value': 2815}]
2025-07-12 03:00:43,973 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV2
2025-07-12 03:00:44,426 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV2
2025-07-12 03:00:44,426 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93669.63, 'new_value': 102064.42}, {'field': 'offline_amount', 'old_value': 145185.32, 'new_value': 160262.19}, {'field': 'total_amount', 'old_value': 238854.95, 'new_value': 262326.61}, {'field': 'order_count', 'old_value': 7292, 'new_value': 7944}]
2025-07-12 03:00:44,426 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMO
2025-07-12 03:00:44,848 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMO
2025-07-12 03:00:44,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12846.91, 'new_value': 14832.52}, {'field': 'offline_amount', 'old_value': 86186.59, 'new_value': 93701.69}, {'field': 'total_amount', 'old_value': 99033.5, 'new_value': 108534.21}, {'field': 'order_count', 'old_value': 3230, 'new_value': 3541}]
2025-07-12 03:00:44,848 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMS
2025-07-12 03:00:45,285 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMS
2025-07-12 03:00:45,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69163.2, 'new_value': 76232.22}, {'field': 'offline_amount', 'old_value': 84634.45, 'new_value': 92289.71}, {'field': 'total_amount', 'old_value': 153797.65, 'new_value': 168521.93}, {'field': 'order_count', 'old_value': 4914, 'new_value': 5405}]
2025-07-12 03:00:45,285 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMV
2025-07-12 03:00:45,676 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMV
2025-07-12 03:00:45,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86644.59, 'new_value': 96163.52}, {'field': 'total_amount', 'old_value': 104323.83, 'new_value': 113842.76}, {'field': 'order_count', 'old_value': 4709, 'new_value': 5123}]
2025-07-12 03:00:45,676 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM31
2025-07-12 03:00:46,129 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM31
2025-07-12 03:00:46,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3086.43, 'new_value': 3409.33}, {'field': 'offline_amount', 'old_value': 167793.1, 'new_value': 185024.7}, {'field': 'total_amount', 'old_value': 170879.53, 'new_value': 188434.03}, {'field': 'order_count', 'old_value': 9095, 'new_value': 9952}]
2025-07-12 03:00:46,129 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM91
2025-07-12 03:00:46,598 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM91
2025-07-12 03:00:46,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101767.97, 'new_value': 110794.05}, {'field': 'total_amount', 'old_value': 101767.97, 'new_value': 110794.05}, {'field': 'order_count', 'old_value': 800, 'new_value': 876}]
2025-07-12 03:00:46,598 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME1
2025-07-12 03:00:46,988 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME1
2025-07-12 03:00:46,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281712.49, 'new_value': 312546.8}, {'field': 'total_amount', 'old_value': 281712.49, 'new_value': 312546.8}, {'field': 'order_count', 'old_value': 6036, 'new_value': 6586}]
2025-07-12 03:00:46,988 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG1
2025-07-12 03:00:47,379 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMG1
2025-07-12 03:00:47,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58253.29, 'new_value': 64246.68}, {'field': 'total_amount', 'old_value': 58253.29, 'new_value': 64246.68}, {'field': 'order_count', 'old_value': 2613, 'new_value': 2883}]
2025-07-12 03:00:47,379 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML1
2025-07-12 03:00:47,785 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML1
2025-07-12 03:00:47,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52948.26, 'new_value': 62122.54}, {'field': 'total_amount', 'old_value': 104052.28, 'new_value': 113226.56}, {'field': 'order_count', 'old_value': 7037, 'new_value': 7537}]
2025-07-12 03:00:47,785 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK2
2025-07-12 03:00:48,238 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK2
2025-07-12 03:00:48,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1002.0, 'new_value': 1109.0}, {'field': 'offline_amount', 'old_value': 7845.8, 'new_value': 8476.8}, {'field': 'total_amount', 'old_value': 8847.8, 'new_value': 9585.8}, {'field': 'order_count', 'old_value': 322, 'new_value': 343}]
2025-07-12 03:00:48,238 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU2
2025-07-12 03:00:48,785 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU2
2025-07-12 03:00:48,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43857.43, 'new_value': 47127.35}, {'field': 'total_amount', 'old_value': 43857.43, 'new_value': 47127.35}, {'field': 'order_count', 'old_value': 2904, 'new_value': 3092}]
2025-07-12 03:00:48,785 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB3
2025-07-12 03:00:49,238 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB3
2025-07-12 03:00:49,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57294.09, 'new_value': 63364.26}, {'field': 'offline_amount', 'old_value': 18551.97, 'new_value': 20048.46}, {'field': 'total_amount', 'old_value': 75846.06, 'new_value': 83412.72}, {'field': 'order_count', 'old_value': 4607, 'new_value': 5018}]
2025-07-12 03:00:49,238 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM41
2025-07-12 03:00:49,613 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM41
2025-07-12 03:00:49,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5081.18, 'new_value': 5460.46}, {'field': 'offline_amount', 'old_value': 11389.96, 'new_value': 12090.48}, {'field': 'total_amount', 'old_value': 16471.14, 'new_value': 17550.94}, {'field': 'order_count', 'old_value': 927, 'new_value': 1009}]
2025-07-12 03:00:49,629 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO1
2025-07-12 03:00:50,113 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO1
2025-07-12 03:00:50,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122075.0, 'new_value': 131406.0}, {'field': 'total_amount', 'old_value': 122075.0, 'new_value': 131406.0}, {'field': 'order_count', 'old_value': 1564, 'new_value': 1701}]
2025-07-12 03:00:50,113 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ1
2025-07-12 03:00:50,566 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ1
2025-07-12 03:00:50,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25008.84, 'new_value': 31008.84}, {'field': 'offline_amount', 'old_value': 46335.22, 'new_value': 46998.22}, {'field': 'total_amount', 'old_value': 71344.06, 'new_value': 78007.06}, {'field': 'order_count', 'old_value': 1943, 'new_value': 2106}]
2025-07-12 03:00:50,566 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMEH
2025-07-12 03:00:51,066 - INFO - 更新表单数据成功: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMEH
2025-07-12 03:00:51,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21502.5, 'new_value': 23003.34}, {'field': 'offline_amount', 'old_value': 159339.1, 'new_value': 173144.06}, {'field': 'total_amount', 'old_value': 180841.6, 'new_value': 196147.4}, {'field': 'order_count', 'old_value': 1591, 'new_value': 1721}]
2025-07-12 03:00:51,066 - INFO - 日期 2025-07 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:51,066 - INFO - 数据同步完成！更新: 22 条，插入: 0 条，错误: 0 条
2025-07-12 03:00:51,066 - INFO - =================同步完成====================
2025-07-12 06:00:03,245 - INFO - =================使用默认全量同步=============
2025-07-12 06:00:05,245 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 06:00:05,245 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 06:00:05,276 - INFO - 开始处理日期: 2025-01
2025-07-12 06:00:05,276 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:05,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:05,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:06,464 - INFO - Response - Page 1:
2025-07-12 06:00:06,667 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:06,667 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:06,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:06,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:07,917 - INFO - Response - Page 2:
2025-07-12 06:00:08,120 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:08,120 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:08,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:08,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:08,589 - INFO - Response - Page 3:
2025-07-12 06:00:08,792 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:08,792 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:08,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:08,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:09,323 - INFO - Response - Page 4:
2025-07-12 06:00:09,526 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:09,526 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:09,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:09,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:10,136 - INFO - Response - Page 5:
2025-07-12 06:00:10,339 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:10,339 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:10,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:10,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:10,823 - INFO - Response - Page 6:
2025-07-12 06:00:11,026 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:11,026 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:11,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:11,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:11,557 - INFO - Response - Page 7:
2025-07-12 06:00:11,761 - INFO - 第 7 页获取到 82 条记录
2025-07-12 06:00:11,761 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 06:00:11,761 - INFO - 获取到 682 条表单数据
2025-07-12 06:00:11,761 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 06:00:11,776 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:11,776 - INFO - 开始处理日期: 2025-02
2025-07-12 06:00:11,776 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:11,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:11,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:12,307 - INFO - Response - Page 1:
2025-07-12 06:00:12,511 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:12,511 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:12,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:12,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:12,995 - INFO - Response - Page 2:
2025-07-12 06:00:13,198 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:13,198 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:13,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:13,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:13,807 - INFO - Response - Page 3:
2025-07-12 06:00:14,011 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:14,011 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:14,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:14,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:14,573 - INFO - Response - Page 4:
2025-07-12 06:00:14,776 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:14,776 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:14,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:14,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:15,307 - INFO - Response - Page 5:
2025-07-12 06:00:15,510 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:15,510 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:15,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:15,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:16,057 - INFO - Response - Page 6:
2025-07-12 06:00:16,260 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:16,260 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:16,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:16,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:16,776 - INFO - Response - Page 7:
2025-07-12 06:00:16,979 - INFO - 第 7 页获取到 70 条记录
2025-07-12 06:00:16,979 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 06:00:16,979 - INFO - 获取到 670 条表单数据
2025-07-12 06:00:16,979 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 06:00:16,995 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:16,995 - INFO - 开始处理日期: 2025-03
2025-07-12 06:00:16,995 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:16,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:16,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:17,495 - INFO - Response - Page 1:
2025-07-12 06:00:17,698 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:17,698 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:17,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:17,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:18,260 - INFO - Response - Page 2:
2025-07-12 06:00:18,464 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:18,464 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:18,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:18,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:18,995 - INFO - Response - Page 3:
2025-07-12 06:00:19,198 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:19,198 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:19,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:19,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:19,698 - INFO - Response - Page 4:
2025-07-12 06:00:19,901 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:19,901 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:19,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:19,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:20,417 - INFO - Response - Page 5:
2025-07-12 06:00:20,620 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:20,620 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:20,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:20,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:21,104 - INFO - Response - Page 6:
2025-07-12 06:00:21,307 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:21,307 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:21,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:21,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:21,760 - INFO - Response - Page 7:
2025-07-12 06:00:21,964 - INFO - 第 7 页获取到 61 条记录
2025-07-12 06:00:21,964 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 06:00:21,964 - INFO - 获取到 661 条表单数据
2025-07-12 06:00:21,964 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 06:00:21,979 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:21,979 - INFO - 开始处理日期: 2025-04
2025-07-12 06:00:21,979 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:21,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:21,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:22,510 - INFO - Response - Page 1:
2025-07-12 06:00:22,714 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:22,714 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:22,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:22,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:23,151 - INFO - Response - Page 2:
2025-07-12 06:00:23,370 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:23,370 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:23,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:23,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:23,901 - INFO - Response - Page 3:
2025-07-12 06:00:24,104 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:24,104 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:24,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:24,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:24,604 - INFO - Response - Page 4:
2025-07-12 06:00:24,807 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:24,807 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:24,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:24,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:25,276 - INFO - Response - Page 5:
2025-07-12 06:00:25,479 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:25,479 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:25,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:25,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:26,010 - INFO - Response - Page 6:
2025-07-12 06:00:26,214 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:26,214 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:26,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:26,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:26,807 - INFO - Response - Page 7:
2025-07-12 06:00:27,010 - INFO - 第 7 页获取到 56 条记录
2025-07-12 06:00:27,010 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 06:00:27,010 - INFO - 获取到 656 条表单数据
2025-07-12 06:00:27,010 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 06:00:27,026 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:27,026 - INFO - 开始处理日期: 2025-05
2025-07-12 06:00:27,026 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:27,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:27,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:27,495 - INFO - Response - Page 1:
2025-07-12 06:00:27,698 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:27,698 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:27,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:27,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:28,198 - INFO - Response - Page 2:
2025-07-12 06:00:28,401 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:28,401 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:28,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:28,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:28,885 - INFO - Response - Page 3:
2025-07-12 06:00:29,089 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:29,089 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:29,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:29,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:29,604 - INFO - Response - Page 4:
2025-07-12 06:00:29,807 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:29,807 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:29,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:29,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:30,323 - INFO - Response - Page 5:
2025-07-12 06:00:30,526 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:30,526 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:30,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:30,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:31,042 - INFO - Response - Page 6:
2025-07-12 06:00:31,245 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:31,245 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:31,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:31,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:31,776 - INFO - Response - Page 7:
2025-07-12 06:00:31,979 - INFO - 第 7 页获取到 65 条记录
2025-07-12 06:00:31,979 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 06:00:31,979 - INFO - 获取到 665 条表单数据
2025-07-12 06:00:31,979 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 06:00:31,995 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:31,995 - INFO - 开始处理日期: 2025-06
2025-07-12 06:00:31,995 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:31,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:31,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:32,495 - INFO - Response - Page 1:
2025-07-12 06:00:32,698 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:32,698 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:32,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:32,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:33,245 - INFO - Response - Page 2:
2025-07-12 06:00:33,448 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:33,448 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:33,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:33,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:33,917 - INFO - Response - Page 3:
2025-07-12 06:00:34,120 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:34,120 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:34,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:34,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:34,620 - INFO - Response - Page 4:
2025-07-12 06:00:34,823 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:34,823 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:34,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:34,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:35,432 - INFO - Response - Page 5:
2025-07-12 06:00:35,635 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:35,635 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:35,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:35,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:36,167 - INFO - Response - Page 6:
2025-07-12 06:00:36,370 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:36,370 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:36,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:36,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:36,760 - INFO - Response - Page 7:
2025-07-12 06:00:36,963 - INFO - 第 7 页获取到 33 条记录
2025-07-12 06:00:36,963 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 06:00:36,963 - INFO - 获取到 633 条表单数据
2025-07-12 06:00:36,963 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 06:00:36,979 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:36,979 - INFO - 开始处理日期: 2025-07
2025-07-12 06:00:36,979 - INFO - Request Parameters - Page 1:
2025-07-12 06:00:36,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:36,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:37,542 - INFO - Response - Page 1:
2025-07-12 06:00:37,745 - INFO - 第 1 页获取到 100 条记录
2025-07-12 06:00:37,745 - INFO - Request Parameters - Page 2:
2025-07-12 06:00:37,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:37,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:38,307 - INFO - Response - Page 2:
2025-07-12 06:00:38,510 - INFO - 第 2 页获取到 100 条记录
2025-07-12 06:00:38,510 - INFO - Request Parameters - Page 3:
2025-07-12 06:00:38,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:38,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:39,026 - INFO - Response - Page 3:
2025-07-12 06:00:39,229 - INFO - 第 3 页获取到 100 条记录
2025-07-12 06:00:39,229 - INFO - Request Parameters - Page 4:
2025-07-12 06:00:39,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:39,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:39,745 - INFO - Response - Page 4:
2025-07-12 06:00:39,948 - INFO - 第 4 页获取到 100 条记录
2025-07-12 06:00:39,948 - INFO - Request Parameters - Page 5:
2025-07-12 06:00:39,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:39,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:40,573 - INFO - Response - Page 5:
2025-07-12 06:00:40,776 - INFO - 第 5 页获取到 100 条记录
2025-07-12 06:00:40,776 - INFO - Request Parameters - Page 6:
2025-07-12 06:00:40,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:40,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:41,307 - INFO - Response - Page 6:
2025-07-12 06:00:41,510 - INFO - 第 6 页获取到 100 条记录
2025-07-12 06:00:41,510 - INFO - Request Parameters - Page 7:
2025-07-12 06:00:41,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 06:00:41,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 06:00:41,807 - INFO - Response - Page 7:
2025-07-12 06:00:42,010 - INFO - 第 7 页获取到 9 条记录
2025-07-12 06:00:42,010 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 06:00:42,010 - INFO - 获取到 609 条表单数据
2025-07-12 06:00:42,010 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 06:00:42,026 - INFO - 开始更新记录 - 表单实例ID: FINST-K7G66FA1N2SWHIR9APGKP6AW4Q143TOV7WKCMP3
2025-07-12 06:00:42,557 - INFO - 更新表单数据成功: FINST-K7G66FA1N2SWHIR9APGKP6AW4Q143TOV7WKCMP3
2025-07-12 06:00:42,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37056.0, 'new_value': 42211.0}, {'field': 'total_amount', 'old_value': 37056.0, 'new_value': 42211.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 206}]
2025-07-12 06:00:42,557 - INFO - 日期 2025-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:42,557 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-07-12 06:00:42,557 - INFO - =================同步完成====================
2025-07-12 09:00:03,033 - INFO - =================使用默认全量同步=============
2025-07-12 09:00:04,986 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 09:00:04,986 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 09:00:05,033 - INFO - 开始处理日期: 2025-01
2025-07-12 09:00:05,033 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:05,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:05,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:06,939 - INFO - Response - Page 1:
2025-07-12 09:00:07,142 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:07,142 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:07,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:07,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:07,861 - INFO - Response - Page 2:
2025-07-12 09:00:08,064 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:08,064 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:08,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:08,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:08,657 - INFO - Response - Page 3:
2025-07-12 09:00:08,861 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:08,861 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:08,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:08,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:09,532 - INFO - Response - Page 4:
2025-07-12 09:00:09,736 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:09,736 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:09,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:09,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:10,267 - INFO - Response - Page 5:
2025-07-12 09:00:10,470 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:10,470 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:10,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:10,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:11,017 - INFO - Response - Page 6:
2025-07-12 09:00:11,220 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:11,220 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:11,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:11,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:11,767 - INFO - Response - Page 7:
2025-07-12 09:00:11,970 - INFO - 第 7 页获取到 82 条记录
2025-07-12 09:00:11,970 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 09:00:11,970 - INFO - 获取到 682 条表单数据
2025-07-12 09:00:11,970 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 09:00:11,986 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 09:00:11,986 - INFO - 开始处理日期: 2025-02
2025-07-12 09:00:11,986 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:11,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:11,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:12,501 - INFO - Response - Page 1:
2025-07-12 09:00:12,704 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:12,704 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:12,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:12,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:13,298 - INFO - Response - Page 2:
2025-07-12 09:00:13,501 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:13,501 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:13,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:13,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:13,986 - INFO - Response - Page 3:
2025-07-12 09:00:14,189 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:14,189 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:14,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:14,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:14,689 - INFO - Response - Page 4:
2025-07-12 09:00:14,892 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:14,892 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:14,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:14,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:15,345 - INFO - Response - Page 5:
2025-07-12 09:00:15,548 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:15,548 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:15,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:15,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:16,126 - INFO - Response - Page 6:
2025-07-12 09:00:16,329 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:16,329 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:16,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:16,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:16,829 - INFO - Response - Page 7:
2025-07-12 09:00:17,032 - INFO - 第 7 页获取到 70 条记录
2025-07-12 09:00:17,032 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 09:00:17,032 - INFO - 获取到 670 条表单数据
2025-07-12 09:00:17,032 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 09:00:17,048 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 09:00:17,048 - INFO - 开始处理日期: 2025-03
2025-07-12 09:00:17,048 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:17,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:17,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:17,548 - INFO - Response - Page 1:
2025-07-12 09:00:17,751 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:17,751 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:17,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:17,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:18,267 - INFO - Response - Page 2:
2025-07-12 09:00:18,470 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:18,470 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:18,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:18,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:19,157 - INFO - Response - Page 3:
2025-07-12 09:00:19,361 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:19,361 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:19,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:19,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:19,845 - INFO - Response - Page 4:
2025-07-12 09:00:20,048 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:20,048 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:20,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:20,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:20,579 - INFO - Response - Page 5:
2025-07-12 09:00:20,782 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:20,782 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:20,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:20,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:21,345 - INFO - Response - Page 6:
2025-07-12 09:00:21,548 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:21,548 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:21,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:21,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:22,142 - INFO - Response - Page 7:
2025-07-12 09:00:22,345 - INFO - 第 7 页获取到 61 条记录
2025-07-12 09:00:22,345 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 09:00:22,345 - INFO - 获取到 661 条表单数据
2025-07-12 09:00:22,345 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 09:00:22,361 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 09:00:22,361 - INFO - 开始处理日期: 2025-04
2025-07-12 09:00:22,361 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:22,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:22,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:22,876 - INFO - Response - Page 1:
2025-07-12 09:00:23,079 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:23,079 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:23,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:23,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:23,626 - INFO - Response - Page 2:
2025-07-12 09:00:23,829 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:23,829 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:23,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:23,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:24,298 - INFO - Response - Page 3:
2025-07-12 09:00:24,501 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:24,501 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:24,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:24,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:25,017 - INFO - Response - Page 4:
2025-07-12 09:00:25,220 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:25,220 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:25,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:25,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:25,751 - INFO - Response - Page 5:
2025-07-12 09:00:25,954 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:25,954 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:25,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:25,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:26,517 - INFO - Response - Page 6:
2025-07-12 09:00:26,720 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:26,720 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:26,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:26,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:27,173 - INFO - Response - Page 7:
2025-07-12 09:00:27,376 - INFO - 第 7 页获取到 56 条记录
2025-07-12 09:00:27,376 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 09:00:27,376 - INFO - 获取到 656 条表单数据
2025-07-12 09:00:27,376 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 09:00:27,392 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 09:00:27,392 - INFO - 开始处理日期: 2025-05
2025-07-12 09:00:27,392 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:27,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:27,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:27,860 - INFO - Response - Page 1:
2025-07-12 09:00:28,064 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:28,064 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:28,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:28,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:28,579 - INFO - Response - Page 2:
2025-07-12 09:00:28,782 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:28,782 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:28,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:28,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:29,314 - INFO - Response - Page 3:
2025-07-12 09:00:29,517 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:29,517 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:29,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:29,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:30,048 - INFO - Response - Page 4:
2025-07-12 09:00:30,251 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:30,251 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:30,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:30,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:30,829 - INFO - Response - Page 5:
2025-07-12 09:00:31,032 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:31,032 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:31,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:31,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:31,517 - INFO - Response - Page 6:
2025-07-12 09:00:31,720 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:31,720 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:31,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:31,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:32,282 - INFO - Response - Page 7:
2025-07-12 09:00:32,485 - INFO - 第 7 页获取到 65 条记录
2025-07-12 09:00:32,485 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 09:00:32,485 - INFO - 获取到 665 条表单数据
2025-07-12 09:00:32,485 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 09:00:32,501 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 09:00:32,501 - INFO - 开始处理日期: 2025-06
2025-07-12 09:00:32,501 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:32,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:32,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:33,017 - INFO - Response - Page 1:
2025-07-12 09:00:33,220 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:33,220 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:33,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:33,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:33,704 - INFO - Response - Page 2:
2025-07-12 09:00:33,907 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:33,907 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:33,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:33,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:34,423 - INFO - Response - Page 3:
2025-07-12 09:00:34,626 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:34,626 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:34,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:34,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:35,126 - INFO - Response - Page 4:
2025-07-12 09:00:35,329 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:35,329 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:35,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:35,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:35,845 - INFO - Response - Page 5:
2025-07-12 09:00:36,048 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:36,048 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:36,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:36,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:36,579 - INFO - Response - Page 6:
2025-07-12 09:00:36,782 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:36,782 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:36,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:36,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:37,173 - INFO - Response - Page 7:
2025-07-12 09:00:37,376 - INFO - 第 7 页获取到 33 条记录
2025-07-12 09:00:37,376 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 09:00:37,376 - INFO - 获取到 633 条表单数据
2025-07-12 09:00:37,376 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 09:00:37,392 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 09:00:37,392 - INFO - 开始处理日期: 2025-07
2025-07-12 09:00:37,392 - INFO - Request Parameters - Page 1:
2025-07-12 09:00:37,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:37,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:37,892 - INFO - Response - Page 1:
2025-07-12 09:00:38,095 - INFO - 第 1 页获取到 100 条记录
2025-07-12 09:00:38,095 - INFO - Request Parameters - Page 2:
2025-07-12 09:00:38,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:38,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:38,642 - INFO - Response - Page 2:
2025-07-12 09:00:38,845 - INFO - 第 2 页获取到 100 条记录
2025-07-12 09:00:38,845 - INFO - Request Parameters - Page 3:
2025-07-12 09:00:38,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:38,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:39,360 - INFO - Response - Page 3:
2025-07-12 09:00:39,564 - INFO - 第 3 页获取到 100 条记录
2025-07-12 09:00:39,564 - INFO - Request Parameters - Page 4:
2025-07-12 09:00:39,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:39,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:40,032 - INFO - Response - Page 4:
2025-07-12 09:00:40,235 - INFO - 第 4 页获取到 100 条记录
2025-07-12 09:00:40,235 - INFO - Request Parameters - Page 5:
2025-07-12 09:00:40,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:40,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:40,720 - INFO - Response - Page 5:
2025-07-12 09:00:40,923 - INFO - 第 5 页获取到 100 条记录
2025-07-12 09:00:40,923 - INFO - Request Parameters - Page 6:
2025-07-12 09:00:40,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:40,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:41,439 - INFO - Response - Page 6:
2025-07-12 09:00:41,642 - INFO - 第 6 页获取到 100 条记录
2025-07-12 09:00:41,642 - INFO - Request Parameters - Page 7:
2025-07-12 09:00:41,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 09:00:41,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 09:00:42,048 - INFO - Response - Page 7:
2025-07-12 09:00:42,251 - INFO - 第 7 页获取到 9 条记录
2025-07-12 09:00:42,251 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 09:00:42,251 - INFO - 获取到 609 条表单数据
2025-07-12 09:00:42,251 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 09:00:42,251 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMT4
2025-07-12 09:00:42,720 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMT4
2025-07-12 09:00:42,720 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39485.6, 'new_value': 44795.11}, {'field': 'total_amount', 'old_value': 42571.4, 'new_value': 47880.91}, {'field': 'order_count', 'old_value': 207, 'new_value': 229}]
2025-07-12 09:00:42,720 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMU4
2025-07-12 09:00:43,157 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMU4
2025-07-12 09:00:43,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275454.0, 'new_value': 298671.0}, {'field': 'total_amount', 'old_value': 275454.0, 'new_value': 298671.0}, {'field': 'order_count', 'old_value': 376, 'new_value': 408}]
2025-07-12 09:00:43,157 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMV4
2025-07-12 09:00:43,610 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMV4
2025-07-12 09:00:43,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45338.22, 'new_value': 49723.69}, {'field': 'offline_amount', 'old_value': 64976.85, 'new_value': 71109.9}, {'field': 'total_amount', 'old_value': 110315.07, 'new_value': 120833.59}, {'field': 'order_count', 'old_value': 3019, 'new_value': 3293}]
2025-07-12 09:00:43,610 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMW4
2025-07-12 09:00:44,095 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMW4
2025-07-12 09:00:44,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11135.0, 'new_value': 11270.0}, {'field': 'total_amount', 'old_value': 11135.0, 'new_value': 11270.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-07-12 09:00:44,095 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMX4
2025-07-12 09:00:44,673 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMX4
2025-07-12 09:00:44,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20743.53, 'new_value': 23131.67}, {'field': 'total_amount', 'old_value': 20743.53, 'new_value': 23131.67}, {'field': 'order_count', 'old_value': 2787, 'new_value': 3090}]
2025-07-12 09:00:44,673 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM15
2025-07-12 09:00:45,126 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM15
2025-07-12 09:00:45,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4822.0, 'new_value': 8802.0}, {'field': 'total_amount', 'old_value': 4822.0, 'new_value': 8802.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-07-12 09:00:45,126 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP7
2025-07-12 09:00:45,657 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMP7
2025-07-12 09:00:45,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2153.0, 'new_value': 3701.0}, {'field': 'total_amount', 'old_value': 8477.0, 'new_value': 10025.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-07-12 09:00:45,657 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM55
2025-07-12 09:00:46,079 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM55
2025-07-12 09:00:46,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2092.0, 'new_value': 2224.0}, {'field': 'offline_amount', 'old_value': 43541.0, 'new_value': 45923.0}, {'field': 'total_amount', 'old_value': 45633.0, 'new_value': 48147.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 53}]
2025-07-12 09:00:46,079 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM65
2025-07-12 09:00:46,517 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM65
2025-07-12 09:00:46,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7500.0, 'new_value': 8100.0}, {'field': 'total_amount', 'old_value': 7500.0, 'new_value': 8100.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-07-12 09:00:46,517 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM75
2025-07-12 09:00:46,923 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM75
2025-07-12 09:00:46,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122857.8, 'new_value': 130011.1}, {'field': 'total_amount', 'old_value': 122857.8, 'new_value': 130011.1}, {'field': 'order_count', 'old_value': 1603, 'new_value': 1732}]
2025-07-12 09:00:46,923 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM95
2025-07-12 09:00:47,392 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM95
2025-07-12 09:00:47,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7200.0, 'new_value': 15330.0}, {'field': 'total_amount', 'old_value': 7200.0, 'new_value': 15330.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-07-12 09:00:47,392 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMA5
2025-07-12 09:00:48,079 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMA5
2025-07-12 09:00:48,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26030.11, 'new_value': 29242.45}, {'field': 'offline_amount', 'old_value': 54358.0, 'new_value': 58153.0}, {'field': 'total_amount', 'old_value': 80388.11, 'new_value': 87395.45}, {'field': 'order_count', 'old_value': 908, 'new_value': 981}]
2025-07-12 09:00:48,079 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMB5
2025-07-12 09:00:48,501 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMB5
2025-07-12 09:00:48,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58030.0, 'new_value': 62846.0}, {'field': 'total_amount', 'old_value': 58030.0, 'new_value': 62846.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 130}]
2025-07-12 09:00:48,501 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMC5
2025-07-12 09:00:48,970 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMC5
2025-07-12 09:00:48,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43669.0, 'new_value': 47983.0}, {'field': 'total_amount', 'old_value': 43669.0, 'new_value': 47983.0}, {'field': 'order_count', 'old_value': 2630, 'new_value': 2910}]
2025-07-12 09:00:48,970 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ7
2025-07-12 09:00:49,376 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMQ7
2025-07-12 09:00:49,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31544.0, 'new_value': 36313.0}, {'field': 'total_amount', 'old_value': 31544.0, 'new_value': 36313.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 300}]
2025-07-12 09:00:49,376 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH5
2025-07-12 09:00:49,845 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH5
2025-07-12 09:00:49,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31154.0, 'new_value': 34297.0}, {'field': 'total_amount', 'old_value': 31154.0, 'new_value': 34297.0}, {'field': 'order_count', 'old_value': 556, 'new_value': 614}]
2025-07-12 09:00:49,845 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT7
2025-07-12 09:00:50,267 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMT7
2025-07-12 09:00:50,267 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45425.81, 'new_value': 50728.42}, {'field': 'offline_amount', 'old_value': 487755.83, 'new_value': 532253.22}, {'field': 'total_amount', 'old_value': 533181.64, 'new_value': 582981.64}, {'field': 'order_count', 'old_value': 4395, 'new_value': 4813}]
2025-07-12 09:00:50,267 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN5
2025-07-12 09:00:50,688 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN5
2025-07-12 09:00:50,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14894.0, 'new_value': 18629.0}, {'field': 'offline_amount', 'old_value': 77084.0, 'new_value': 81996.0}, {'field': 'total_amount', 'old_value': 91978.0, 'new_value': 100625.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 80}]
2025-07-12 09:00:50,688 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO5
2025-07-12 09:00:51,188 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO5
2025-07-12 09:00:51,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34926.0, 'new_value': 38406.0}, {'field': 'total_amount', 'old_value': 34926.0, 'new_value': 38406.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-07-12 09:00:51,188 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU7
2025-07-12 09:00:51,626 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU7
2025-07-12 09:00:51,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9105.0, 'new_value': 10144.0}, {'field': 'total_amount', 'old_value': 9105.0, 'new_value': 10144.0}, {'field': 'order_count', 'old_value': 1450, 'new_value': 1458}]
2025-07-12 09:00:51,626 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR5
2025-07-12 09:00:52,142 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMR5
2025-07-12 09:00:52,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55566.0, 'new_value': 75366.0}, {'field': 'total_amount', 'old_value': 55566.0, 'new_value': 75366.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-07-12 09:00:52,142 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS5
2025-07-12 09:00:52,563 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS5
2025-07-12 09:00:52,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5800.0, 'new_value': 6612.0}, {'field': 'total_amount', 'old_value': 5800.0, 'new_value': 6612.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 114}]
2025-07-12 09:00:52,563 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV5
2025-07-12 09:00:52,970 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMV5
2025-07-12 09:00:52,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6582.9, 'new_value': 7682.9}, {'field': 'total_amount', 'old_value': 7303.8, 'new_value': 8403.8}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-07-12 09:00:52,970 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM06
2025-07-12 09:00:53,392 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM06
2025-07-12 09:00:53,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3303.0, 'new_value': 3902.0}, {'field': 'total_amount', 'old_value': 3765.0, 'new_value': 4364.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 92}]
2025-07-12 09:00:53,392 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX7
2025-07-12 09:00:53,876 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX7
2025-07-12 09:00:53,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10963.32, 'new_value': 11632.22}, {'field': 'offline_amount', 'old_value': 86901.72, 'new_value': 94636.04}, {'field': 'total_amount', 'old_value': 97865.04, 'new_value': 106268.26}, {'field': 'order_count', 'old_value': 531, 'new_value': 576}]
2025-07-12 09:00:53,876 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY7
2025-07-12 09:00:54,345 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMY7
2025-07-12 09:00:54,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1791.21, 'new_value': 1883.13}, {'field': 'offline_amount', 'old_value': 49052.45, 'new_value': 54110.52}, {'field': 'total_amount', 'old_value': 50843.66, 'new_value': 55993.65}, {'field': 'order_count', 'old_value': 1288, 'new_value': 1446}]
2025-07-12 09:00:54,345 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM16
2025-07-12 09:00:54,876 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM16
2025-07-12 09:00:54,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71588.8, 'new_value': 78790.8}, {'field': 'total_amount', 'old_value': 71588.8, 'new_value': 78790.8}, {'field': 'order_count', 'old_value': 421, 'new_value': 466}]
2025-07-12 09:00:54,876 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM08
2025-07-12 09:00:55,360 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM08
2025-07-12 09:00:55,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149745.6, 'new_value': 165235.6}, {'field': 'total_amount', 'old_value': 167856.7, 'new_value': 183346.7}, {'field': 'order_count', 'old_value': 198, 'new_value': 217}]
2025-07-12 09:00:55,360 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM28
2025-07-12 09:00:55,813 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM28
2025-07-12 09:00:55,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4889.63, 'new_value': 5185.82}, {'field': 'offline_amount', 'old_value': 98570.36, 'new_value': 108376.35}, {'field': 'total_amount', 'old_value': 103459.99, 'new_value': 113562.17}, {'field': 'order_count', 'old_value': 467, 'new_value': 519}]
2025-07-12 09:00:55,813 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA6
2025-07-12 09:00:56,313 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA6
2025-07-12 09:00:56,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57723.0, 'new_value': 65628.0}, {'field': 'offline_amount', 'old_value': 19296.79, 'new_value': 20988.69}, {'field': 'total_amount', 'old_value': 77019.79, 'new_value': 86616.69}, {'field': 'order_count', 'old_value': 532, 'new_value': 594}]
2025-07-12 09:00:56,313 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC6
2025-07-12 09:00:56,782 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC6
2025-07-12 09:00:56,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52692.0, 'new_value': 58832.0}, {'field': 'total_amount', 'old_value': 52692.0, 'new_value': 58832.0}, {'field': 'order_count', 'old_value': 194, 'new_value': 215}]
2025-07-12 09:00:56,782 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD6
2025-07-12 09:00:57,267 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMD6
2025-07-12 09:00:57,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50111.53, 'new_value': 54297.65}, {'field': 'total_amount', 'old_value': 50111.53, 'new_value': 54297.65}, {'field': 'order_count', 'old_value': 1388, 'new_value': 1492}]
2025-07-12 09:00:57,267 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK6
2025-07-12 09:00:57,751 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMK6
2025-07-12 09:00:57,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54587.0, 'new_value': 61503.0}, {'field': 'offline_amount', 'old_value': 16427.88, 'new_value': 17714.78}, {'field': 'total_amount', 'old_value': 71014.88, 'new_value': 79217.78}, {'field': 'order_count', 'old_value': 523, 'new_value': 584}]
2025-07-12 09:00:57,751 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM48
2025-07-12 09:00:58,173 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM48
2025-07-12 09:00:58,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18376.94, 'new_value': 20834.44}, {'field': 'offline_amount', 'old_value': 130952.2, 'new_value': 143393.1}, {'field': 'total_amount', 'old_value': 149329.14, 'new_value': 164227.54}, {'field': 'order_count', 'old_value': 756, 'new_value': 825}]
2025-07-12 09:00:58,173 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO6
2025-07-12 09:00:58,610 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMO6
2025-07-12 09:00:58,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62652.29, 'new_value': 70023.79}, {'field': 'total_amount', 'old_value': 62652.29, 'new_value': 70023.79}, {'field': 'order_count', 'old_value': 2128, 'new_value': 2358}]
2025-07-12 09:00:58,610 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ6
2025-07-12 09:00:59,048 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ6
2025-07-12 09:00:59,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47475.67, 'new_value': 53867.67}, {'field': 'total_amount', 'old_value': 47475.67, 'new_value': 53867.67}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-07-12 09:00:59,048 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT6
2025-07-12 09:00:59,454 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT6
2025-07-12 09:00:59,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20384.2, 'new_value': 23038.8}, {'field': 'total_amount', 'old_value': 20384.2, 'new_value': 23038.8}, {'field': 'order_count', 'old_value': 107, 'new_value': 122}]
2025-07-12 09:00:59,454 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM78
2025-07-12 09:00:59,892 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM78
2025-07-12 09:00:59,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10571.0, 'new_value': 11643.0}, {'field': 'total_amount', 'old_value': 10571.0, 'new_value': 11643.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 53}]
2025-07-12 09:00:59,892 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM57
2025-07-12 09:01:00,392 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM57
2025-07-12 09:01:00,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87851.0, 'new_value': 97514.0}, {'field': 'total_amount', 'old_value': 87851.0, 'new_value': 97514.0}, {'field': 'order_count', 'old_value': 3325, 'new_value': 3701}]
2025-07-12 09:01:00,392 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM77
2025-07-12 09:01:00,813 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM77
2025-07-12 09:01:00,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7901.0, 'new_value': 8434.0}, {'field': 'total_amount', 'old_value': 7901.0, 'new_value': 8434.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-07-12 09:01:00,813 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB7
2025-07-12 09:01:01,235 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB7
2025-07-12 09:01:01,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36488.0, 'new_value': 39311.0}, {'field': 'offline_amount', 'old_value': 139426.0, 'new_value': 151456.0}, {'field': 'total_amount', 'old_value': 175914.0, 'new_value': 190767.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 263}]
2025-07-12 09:01:01,235 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC7
2025-07-12 09:01:01,860 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMC7
2025-07-12 09:01:01,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28081.84, 'new_value': 31231.58}, {'field': 'total_amount', 'old_value': 28081.84, 'new_value': 31231.58}, {'field': 'order_count', 'old_value': 848, 'new_value': 944}]
2025-07-12 09:01:01,860 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH7
2025-07-12 09:01:02,345 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH7
2025-07-12 09:01:02,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17567.0, 'new_value': 18685.0}, {'field': 'total_amount', 'old_value': 17567.0, 'new_value': 18685.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 84}]
2025-07-12 09:01:02,345 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM98
2025-07-12 09:01:02,829 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM98
2025-07-12 09:01:02,829 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29632.55, 'new_value': 32196.96}, {'field': 'offline_amount', 'old_value': 158416.45, 'new_value': 172656.36}, {'field': 'total_amount', 'old_value': 188049.0, 'new_value': 204853.32}, {'field': 'order_count', 'old_value': 1255, 'new_value': 1373}]
2025-07-12 09:01:02,829 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI7
2025-07-12 09:01:03,267 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI7
2025-07-12 09:01:03,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76115.17, 'new_value': 83500.17}, {'field': 'total_amount', 'old_value': 76115.17, 'new_value': 83500.17}, {'field': 'order_count', 'old_value': 1934, 'new_value': 2141}]
2025-07-12 09:01:03,267 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM4
2025-07-12 09:01:03,767 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM4
2025-07-12 09:01:03,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13935.1, 'new_value': 15300.1}, {'field': 'total_amount', 'old_value': 13935.1, 'new_value': 15300.1}, {'field': 'order_count', 'old_value': 133, 'new_value': 146}]
2025-07-12 09:01:03,767 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM5
2025-07-12 09:01:04,204 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM5
2025-07-12 09:01:04,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244632.0, 'new_value': 275349.0}, {'field': 'total_amount', 'old_value': 253644.0, 'new_value': 284361.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 34}]
2025-07-12 09:01:04,204 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM6
2025-07-12 09:01:04,704 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM6
2025-07-12 09:01:04,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8189.0, 'new_value': 8633.0}, {'field': 'offline_amount', 'old_value': 21357.84, 'new_value': 22153.44}, {'field': 'total_amount', 'old_value': 29546.84, 'new_value': 30786.44}, {'field': 'order_count', 'old_value': 336, 'new_value': 354}]
2025-07-12 09:01:04,704 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM8
2025-07-12 09:01:05,220 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM8
2025-07-12 09:01:05,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124590.83, 'new_value': 137235.71}, {'field': 'total_amount', 'old_value': 124590.83, 'new_value': 137235.71}, {'field': 'order_count', 'old_value': 419, 'new_value': 457}]
2025-07-12 09:01:05,220 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM9
2025-07-12 09:01:05,657 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM9
2025-07-12 09:01:05,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11409.81, 'new_value': 12710.19}, {'field': 'offline_amount', 'old_value': 9677.54, 'new_value': 11165.5}, {'field': 'total_amount', 'old_value': 21087.35, 'new_value': 23875.69}, {'field': 'order_count', 'old_value': 1015, 'new_value': 1142}]
2025-07-12 09:01:05,657 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA
2025-07-12 09:01:06,095 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA
2025-07-12 09:01:06,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15405.19, 'new_value': 16481.54}, {'field': 'total_amount', 'old_value': 16465.99, 'new_value': 17542.34}, {'field': 'order_count', 'old_value': 133, 'new_value': 145}]
2025-07-12 09:01:06,095 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMD8
2025-07-12 09:01:06,532 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMD8
2025-07-12 09:01:06,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90784.96, 'new_value': 98079.29}, {'field': 'offline_amount', 'old_value': 68488.16, 'new_value': 72381.09}, {'field': 'total_amount', 'old_value': 159273.12, 'new_value': 170460.38}, {'field': 'order_count', 'old_value': 1557, 'new_value': 1694}]
2025-07-12 09:01:06,532 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME
2025-07-12 09:01:06,923 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME
2025-07-12 09:01:06,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12497.9, 'new_value': 16451.1}, {'field': 'total_amount', 'old_value': 12497.9, 'new_value': 16451.1}, {'field': 'order_count', 'old_value': 13, 'new_value': 17}]
2025-07-12 09:01:06,923 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF
2025-07-12 09:01:07,298 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF
2025-07-12 09:01:07,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5735.0, 'new_value': 6240.0}, {'field': 'offline_amount', 'old_value': 17500.5, 'new_value': 18757.49}, {'field': 'total_amount', 'old_value': 23235.5, 'new_value': 24997.49}, {'field': 'order_count', 'old_value': 737, 'new_value': 790}]
2025-07-12 09:01:07,298 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH
2025-07-12 09:01:07,751 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH
2025-07-12 09:01:07,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122559.62, 'new_value': 137018.24}, {'field': 'total_amount', 'old_value': 122559.62, 'new_value': 137018.24}, {'field': 'order_count', 'old_value': 703, 'new_value': 772}]
2025-07-12 09:01:07,751 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI
2025-07-12 09:01:08,141 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI
2025-07-12 09:01:08,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19301.3, 'new_value': 19680.7}, {'field': 'offline_amount', 'old_value': 7179.7, 'new_value': 7617.6}, {'field': 'total_amount', 'old_value': 26481.0, 'new_value': 27298.3}, {'field': 'order_count', 'old_value': 89, 'new_value': 95}]
2025-07-12 09:01:08,141 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ
2025-07-12 09:01:08,595 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMJ
2025-07-12 09:01:08,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11350.9, 'new_value': 12397.9}, {'field': 'total_amount', 'old_value': 11350.9, 'new_value': 12397.9}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-07-12 09:01:08,595 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK
2025-07-12 09:01:09,095 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK
2025-07-12 09:01:09,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54996.95, 'new_value': 59080.35}, {'field': 'total_amount', 'old_value': 54996.95, 'new_value': 59080.35}, {'field': 'order_count', 'old_value': 703, 'new_value': 754}]
2025-07-12 09:01:09,095 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMG8
2025-07-12 09:01:09,532 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMG8
2025-07-12 09:01:09,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119503.44, 'new_value': 134839.04}, {'field': 'total_amount', 'old_value': 155376.74, 'new_value': 170712.34}, {'field': 'order_count', 'old_value': 1812, 'new_value': 2005}]
2025-07-12 09:01:09,532 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML
2025-07-12 09:01:10,095 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCML
2025-07-12 09:01:10,095 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12477.58, 'new_value': 14032.71}, {'field': 'offline_amount', 'old_value': 20088.48, 'new_value': 21895.1}, {'field': 'total_amount', 'old_value': 32566.06, 'new_value': 35927.81}, {'field': 'order_count', 'old_value': 1271, 'new_value': 1402}]
2025-07-12 09:01:10,095 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ
2025-07-12 09:01:10,485 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ
2025-07-12 09:01:10,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17024.0, 'new_value': 17515.0}, {'field': 'total_amount', 'old_value': 17024.0, 'new_value': 17515.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-07-12 09:01:10,485 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR
2025-07-12 09:01:10,938 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMR
2025-07-12 09:01:10,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15078.0, 'new_value': 17005.0}, {'field': 'total_amount', 'old_value': 15078.0, 'new_value': 17005.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 33}]
2025-07-12 09:01:10,938 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT
2025-07-12 09:01:11,423 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT
2025-07-12 09:01:11,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34684.0, 'new_value': 37626.0}, {'field': 'total_amount', 'old_value': 34684.0, 'new_value': 37626.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 24}]
2025-07-12 09:01:11,423 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV
2025-07-12 09:01:11,845 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMV
2025-07-12 09:01:11,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8748.0, 'new_value': 9346.0}, {'field': 'total_amount', 'old_value': 8748.0, 'new_value': 9346.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-07-12 09:01:11,845 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ
2025-07-12 09:01:12,298 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMZ
2025-07-12 09:01:12,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24014.9, 'new_value': 26158.8}, {'field': 'total_amount', 'old_value': 24014.9, 'new_value': 26158.8}, {'field': 'order_count', 'old_value': 55, 'new_value': 60}]
2025-07-12 09:01:12,298 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM71
2025-07-12 09:01:12,782 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM71
2025-07-12 09:01:12,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3577.3, 'new_value': 4009.2}, {'field': 'offline_amount', 'old_value': 18548.03, 'new_value': 21267.83}, {'field': 'total_amount', 'old_value': 22125.33, 'new_value': 25277.03}, {'field': 'order_count', 'old_value': 253, 'new_value': 278}]
2025-07-12 09:01:12,782 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM91
2025-07-12 09:01:13,391 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM91
2025-07-12 09:01:13,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17698.6, 'new_value': 19425.1}, {'field': 'total_amount', 'old_value': 17698.6, 'new_value': 19425.1}, {'field': 'order_count', 'old_value': 792, 'new_value': 875}]
2025-07-12 09:01:13,391 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB1
2025-07-12 09:01:13,860 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB1
2025-07-12 09:01:13,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57998.65, 'new_value': 65306.58}, {'field': 'offline_amount', 'old_value': 31555.2, 'new_value': 34260.32}, {'field': 'total_amount', 'old_value': 89553.85, 'new_value': 99566.9}, {'field': 'order_count', 'old_value': 339, 'new_value': 377}]
2025-07-12 09:01:13,860 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC1
2025-07-12 09:01:14,345 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC1
2025-07-12 09:01:14,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5708.0, 'new_value': 6227.0}, {'field': 'total_amount', 'old_value': 5708.0, 'new_value': 6227.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-07-12 09:01:14,345 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD1
2025-07-12 09:01:14,970 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMD1
2025-07-12 09:01:14,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140604.9, 'new_value': 141004.9}, {'field': 'total_amount', 'old_value': 140604.9, 'new_value': 141004.9}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-07-12 09:01:14,970 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME1
2025-07-12 09:01:15,423 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCME1
2025-07-12 09:01:15,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32021.31, 'new_value': 36667.61}, {'field': 'total_amount', 'old_value': 32021.31, 'new_value': 36667.61}, {'field': 'order_count', 'old_value': 157, 'new_value': 181}]
2025-07-12 09:01:15,423 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF1
2025-07-12 09:01:15,891 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF1
2025-07-12 09:01:15,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59572.97, 'new_value': 64742.32}, {'field': 'total_amount', 'old_value': 59572.97, 'new_value': 64742.32}, {'field': 'order_count', 'old_value': 1808, 'new_value': 1969}]
2025-07-12 09:01:15,891 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMM8
2025-07-12 09:01:16,407 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMM8
2025-07-12 09:01:16,407 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80007.3, 'new_value': 89430.39}, {'field': 'offline_amount', 'old_value': 14706.58, 'new_value': 16182.35}, {'field': 'total_amount', 'old_value': 94713.88, 'new_value': 105612.74}, {'field': 'order_count', 'old_value': 360, 'new_value': 399}]
2025-07-12 09:01:16,407 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMN8
2025-07-12 09:01:16,845 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMN8
2025-07-12 09:01:16,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17854.94, 'new_value': 18008.53}, {'field': 'offline_amount', 'old_value': 19624.35, 'new_value': 21915.55}, {'field': 'total_amount', 'old_value': 37479.29, 'new_value': 39924.08}, {'field': 'order_count', 'old_value': 172, 'new_value': 184}]
2025-07-12 09:01:16,845 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT1
2025-07-12 09:01:17,282 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMT1
2025-07-12 09:01:17,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42997.28, 'new_value': 46956.19}, {'field': 'offline_amount', 'old_value': 45661.4, 'new_value': 49681.18}, {'field': 'total_amount', 'old_value': 88658.68, 'new_value': 96637.37}, {'field': 'order_count', 'old_value': 3647, 'new_value': 3998}]
2025-07-12 09:01:17,298 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX1
2025-07-12 09:01:17,704 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMX1
2025-07-12 09:01:17,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86959.0, 'new_value': 89997.2}, {'field': 'total_amount', 'old_value': 86959.0, 'new_value': 89997.2}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-07-12 09:01:17,704 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA2
2025-07-12 09:01:18,110 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMA2
2025-07-12 09:01:18,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 656913.53, 'new_value': 706959.53}, {'field': 'total_amount', 'old_value': 656913.53, 'new_value': 706959.53}, {'field': 'order_count', 'old_value': 1241, 'new_value': 1357}]
2025-07-12 09:01:18,110 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC2
2025-07-12 09:01:18,579 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMC2
2025-07-12 09:01:18,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98656.8, 'new_value': 110264.9}, {'field': 'total_amount', 'old_value': 98656.8, 'new_value': 110264.9}, {'field': 'order_count', 'old_value': 3124, 'new_value': 3489}]
2025-07-12 09:01:18,579 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU8
2025-07-12 09:01:19,032 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMU8
2025-07-12 09:01:19,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2080.0, 'new_value': 2180.0}, {'field': 'offline_amount', 'old_value': 11290.0, 'new_value': 14090.0}, {'field': 'total_amount', 'old_value': 13370.0, 'new_value': 16270.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 178}]
2025-07-12 09:01:19,032 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH2
2025-07-12 09:01:19,485 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMH2
2025-07-12 09:01:19,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10612.0, 'new_value': 12535.0}, {'field': 'offline_amount', 'old_value': 15630.0, 'new_value': 18024.0}, {'field': 'total_amount', 'old_value': 26242.0, 'new_value': 30559.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 66}]
2025-07-12 09:01:19,485 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI2
2025-07-12 09:01:19,954 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI2
2025-07-12 09:01:19,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251090.0, 'new_value': 271170.0}, {'field': 'total_amount', 'old_value': 251090.0, 'new_value': 271170.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 191}]
2025-07-12 09:01:19,954 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX8
2025-07-12 09:01:20,391 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMX8
2025-07-12 09:01:20,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60028.94, 'new_value': 68946.19}, {'field': 'total_amount', 'old_value': 60028.94, 'new_value': 68946.19}, {'field': 'order_count', 'old_value': 308, 'new_value': 348}]
2025-07-12 09:01:20,391 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK2
2025-07-12 09:01:20,813 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMK2
2025-07-12 09:01:20,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38231.0, 'new_value': 44616.0}, {'field': 'total_amount', 'old_value': 38231.0, 'new_value': 44616.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 112}]
2025-07-12 09:01:20,813 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ8
2025-07-12 09:01:21,266 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMZ8
2025-07-12 09:01:21,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7349.48, 'new_value': 7460.72}, {'field': 'total_amount', 'old_value': 7349.48, 'new_value': 7460.72}, {'field': 'order_count', 'old_value': 72, 'new_value': 74}]
2025-07-12 09:01:21,266 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM09
2025-07-12 09:01:21,735 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM09
2025-07-12 09:01:21,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151863.0, 'new_value': 160831.0}, {'field': 'offline_amount', 'old_value': 20903.5, 'new_value': 22125.5}, {'field': 'total_amount', 'old_value': 172766.5, 'new_value': 182956.5}, {'field': 'order_count', 'old_value': 2575, 'new_value': 2770}]
2025-07-12 09:01:21,735 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMP
2025-07-12 09:01:22,220 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMP
2025-07-12 09:01:22,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30522.0, 'new_value': 32152.0}, {'field': 'offline_amount', 'old_value': 42414.0, 'new_value': 44254.0}, {'field': 'total_amount', 'old_value': 72936.0, 'new_value': 76406.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-07-12 09:01:22,220 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM19
2025-07-12 09:01:22,673 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM19
2025-07-12 09:01:22,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260148.0, 'new_value': 284884.0}, {'field': 'total_amount', 'old_value': 260148.0, 'new_value': 284884.0}, {'field': 'order_count', 'old_value': 1236, 'new_value': 1369}]
2025-07-12 09:01:22,673 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMT
2025-07-12 09:01:23,110 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMT
2025-07-12 09:01:23,110 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5559.03, 'new_value': 6244.07}, {'field': 'offline_amount', 'old_value': 78066.48, 'new_value': 86375.22}, {'field': 'total_amount', 'old_value': 83625.51, 'new_value': 92619.29}, {'field': 'order_count', 'old_value': 1000, 'new_value': 1113}]
2025-07-12 09:01:23,110 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM39
2025-07-12 09:01:23,688 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCM39
2025-07-12 09:01:23,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114384.05, 'new_value': 125161.13}, {'field': 'total_amount', 'old_value': 114384.05, 'new_value': 125161.13}, {'field': 'order_count', 'old_value': 418, 'new_value': 457}]
2025-07-12 09:01:23,688 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM49
2025-07-12 09:01:24,094 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM49
2025-07-12 09:01:24,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112694.3, 'new_value': 120180.0}, {'field': 'total_amount', 'old_value': 112694.3, 'new_value': 120180.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 160}]
2025-07-12 09:01:24,094 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM01
2025-07-12 09:01:24,594 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM01
2025-07-12 09:01:24,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122088.0, 'new_value': 134280.0}, {'field': 'total_amount', 'old_value': 122088.0, 'new_value': 134280.0}, {'field': 'order_count', 'old_value': 10164, 'new_value': 11180}]
2025-07-12 09:01:24,594 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM11
2025-07-12 09:01:25,032 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM11
2025-07-12 09:01:25,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34277.72, 'new_value': 38671.06}, {'field': 'offline_amount', 'old_value': 18531.11, 'new_value': 20438.09}, {'field': 'total_amount', 'old_value': 52808.83, 'new_value': 59109.15}, {'field': 'order_count', 'old_value': 3311, 'new_value': 3694}]
2025-07-12 09:01:25,032 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM21
2025-07-12 09:01:25,532 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM21
2025-07-12 09:01:25,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47720.5, 'new_value': 55375.7}, {'field': 'offline_amount', 'old_value': 32027.4, 'new_value': 38817.53}, {'field': 'total_amount', 'old_value': 79747.9, 'new_value': 94193.23}, {'field': 'order_count', 'old_value': 221, 'new_value': 247}]
2025-07-12 09:01:25,532 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM69
2025-07-12 09:01:25,891 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM69
2025-07-12 09:01:25,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23808.65, 'new_value': 26632.06}, {'field': 'offline_amount', 'old_value': 23272.16, 'new_value': 24839.94}, {'field': 'total_amount', 'old_value': 47080.81, 'new_value': 51472.0}, {'field': 'order_count', 'old_value': 3988, 'new_value': 4355}]
2025-07-12 09:01:25,891 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM79
2025-07-12 09:01:26,329 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM79
2025-07-12 09:01:26,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5629.99, 'new_value': 6071.4}, {'field': 'offline_amount', 'old_value': 128799.07, 'new_value': 141761.3}, {'field': 'total_amount', 'old_value': 134429.06, 'new_value': 147832.7}, {'field': 'order_count', 'old_value': 9133, 'new_value': 10061}]
2025-07-12 09:01:26,329 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB1
2025-07-12 09:01:26,766 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMB1
2025-07-12 09:01:26,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81583.0, 'new_value': 85830.0}, {'field': 'total_amount', 'old_value': 81583.0, 'new_value': 85830.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 21}]
2025-07-12 09:01:26,766 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM89
2025-07-12 09:01:27,219 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM89
2025-07-12 09:01:27,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54430.0, 'new_value': 57883.0}, {'field': 'total_amount', 'old_value': 54430.0, 'new_value': 57883.0}, {'field': 'order_count', 'old_value': 1699, 'new_value': 1817}]
2025-07-12 09:01:27,219 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM99
2025-07-12 09:01:27,782 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM99
2025-07-12 09:01:27,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50616.4, 'new_value': 56124.0}, {'field': 'offline_amount', 'old_value': 35044.8, 'new_value': 37705.5}, {'field': 'total_amount', 'old_value': 85661.2, 'new_value': 93829.5}, {'field': 'order_count', 'old_value': 2168, 'new_value': 2365}]
2025-07-12 09:01:27,782 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMA9
2025-07-12 09:01:28,282 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMA9
2025-07-12 09:01:28,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1095.7, 'new_value': 1306.4}, {'field': 'offline_amount', 'old_value': 3980.0, 'new_value': 9980.0}, {'field': 'total_amount', 'old_value': 5075.7, 'new_value': 11286.4}, {'field': 'order_count', 'old_value': 14, 'new_value': 18}]
2025-07-12 09:01:28,282 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMB9
2025-07-12 09:01:28,719 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMB9
2025-07-12 09:01:28,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 576498.0, 'new_value': 636183.0}, {'field': 'total_amount', 'old_value': 576498.0, 'new_value': 636183.0}, {'field': 'order_count', 'old_value': 2723, 'new_value': 2985}]
2025-07-12 09:01:28,719 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM1
2025-07-12 09:01:29,173 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM1
2025-07-12 09:01:29,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200312.0, 'new_value': 214431.0}, {'field': 'total_amount', 'old_value': 200312.0, 'new_value': 214431.0}, {'field': 'order_count', 'old_value': 251, 'new_value': 272}]
2025-07-12 09:01:29,173 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ1
2025-07-12 09:01:29,673 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ1
2025-07-12 09:01:29,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10775.83, 'new_value': 11413.83}, {'field': 'offline_amount', 'old_value': 12002.24, 'new_value': 12137.24}, {'field': 'total_amount', 'old_value': 22778.07, 'new_value': 23551.07}, {'field': 'order_count', 'old_value': 87, 'new_value': 95}]
2025-07-12 09:01:29,673 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMC9
2025-07-12 09:01:30,298 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMC9
2025-07-12 09:01:30,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 235.0, 'new_value': 344.0}, {'field': 'offline_amount', 'old_value': 29738.9, 'new_value': 44836.9}, {'field': 'total_amount', 'old_value': 29973.9, 'new_value': 45180.9}, {'field': 'order_count', 'old_value': 24, 'new_value': 31}]
2025-07-12 09:01:30,298 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU1
2025-07-12 09:01:30,704 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMU1
2025-07-12 09:01:30,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93050.0, 'new_value': 98908.0}, {'field': 'offline_amount', 'old_value': 437071.0, 'new_value': 481513.0}, {'field': 'total_amount', 'old_value': 530121.0, 'new_value': 580421.0}, {'field': 'order_count', 'old_value': 15211, 'new_value': 16739}]
2025-07-12 09:01:30,704 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ1
2025-07-12 09:01:31,188 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMZ1
2025-07-12 09:01:31,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7532.0, 'new_value': 7631.0}, {'field': 'total_amount', 'old_value': 7532.0, 'new_value': 7631.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-07-12 09:01:31,188 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM12
2025-07-12 09:01:31,688 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM12
2025-07-12 09:01:31,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21017.4, 'new_value': 23152.8}, {'field': 'offline_amount', 'old_value': 21919.4, 'new_value': 24711.1}, {'field': 'total_amount', 'old_value': 42936.8, 'new_value': 47863.9}, {'field': 'order_count', 'old_value': 1738, 'new_value': 1950}]
2025-07-12 09:01:31,688 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMD9
2025-07-12 09:01:32,141 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMD9
2025-07-12 09:01:32,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41265.0, 'new_value': 45074.0}, {'field': 'total_amount', 'old_value': 41265.0, 'new_value': 45074.0}, {'field': 'order_count', 'old_value': 2020, 'new_value': 2191}]
2025-07-12 09:01:32,141 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCME9
2025-07-12 09:01:32,735 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCME9
2025-07-12 09:01:32,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108095.65, 'new_value': 118133.47}, {'field': 'offline_amount', 'old_value': 629495.92, 'new_value': 692567.09}, {'field': 'total_amount', 'old_value': 737591.57, 'new_value': 810700.56}, {'field': 'order_count', 'old_value': 3577, 'new_value': 3930}]
2025-07-12 09:01:32,735 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMF9
2025-07-12 09:01:33,188 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMF9
2025-07-12 09:01:33,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198533.65, 'new_value': 226653.65}, {'field': 'total_amount', 'old_value': 198533.65, 'new_value': 226653.65}, {'field': 'order_count', 'old_value': 810, 'new_value': 906}]
2025-07-12 09:01:33,188 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM22
2025-07-12 09:01:33,626 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM22
2025-07-12 09:01:33,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81747.98, 'new_value': 90720.55}, {'field': 'total_amount', 'old_value': 81747.98, 'new_value': 90720.55}, {'field': 'order_count', 'old_value': 348, 'new_value': 384}]
2025-07-12 09:01:33,626 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM42
2025-07-12 09:01:34,173 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM42
2025-07-12 09:01:34,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1413.0, 'new_value': 1486.0}, {'field': 'total_amount', 'old_value': 21288.13, 'new_value': 21361.13}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-07-12 09:01:34,173 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM62
2025-07-12 09:01:34,626 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM62
2025-07-12 09:01:34,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2718.0, 'new_value': 3106.0}, {'field': 'total_amount', 'old_value': 5745.0, 'new_value': 6133.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-07-12 09:01:34,626 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM72
2025-07-12 09:01:35,110 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM72
2025-07-12 09:01:35,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60496.1, 'new_value': 65365.5}, {'field': 'total_amount', 'old_value': 60496.1, 'new_value': 65365.5}, {'field': 'order_count', 'old_value': 247, 'new_value': 265}]
2025-07-12 09:01:35,110 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM92
2025-07-12 09:01:35,516 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM92
2025-07-12 09:01:35,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61989.0, 'new_value': 69227.0}, {'field': 'total_amount', 'old_value': 61989.0, 'new_value': 69227.0}, {'field': 'order_count', 'old_value': 5269, 'new_value': 5907}]
2025-07-12 09:01:35,516 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMH9
2025-07-12 09:01:35,907 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMH9
2025-07-12 09:01:35,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71553.0, 'new_value': 91873.0}, {'field': 'total_amount', 'old_value': 83013.0, 'new_value': 103333.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 24}]
2025-07-12 09:01:35,907 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD2
2025-07-12 09:01:36,423 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD2
2025-07-12 09:01:36,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28428.0, 'new_value': 32927.0}, {'field': 'total_amount', 'old_value': 28428.0, 'new_value': 32927.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-07-12 09:01:36,423 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF2
2025-07-12 09:01:36,907 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF2
2025-07-12 09:01:36,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60763.61, 'new_value': 69644.68}, {'field': 'total_amount', 'old_value': 72206.73, 'new_value': 81087.8}, {'field': 'order_count', 'old_value': 3458, 'new_value': 3982}]
2025-07-12 09:01:36,907 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMI9
2025-07-12 09:01:37,376 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMI9
2025-07-12 09:01:37,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78797.6, 'new_value': 85115.2}, {'field': 'total_amount', 'old_value': 78797.6, 'new_value': 85115.2}, {'field': 'order_count', 'old_value': 2366, 'new_value': 2559}]
2025-07-12 09:01:37,376 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMJ9
2025-07-12 09:01:37,876 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMJ9
2025-07-12 09:01:37,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75114.24, 'new_value': 75285.24}, {'field': 'offline_amount', 'old_value': 9628.0, 'new_value': 16988.0}, {'field': 'total_amount', 'old_value': 84742.24, 'new_value': 92273.24}, {'field': 'order_count', 'old_value': 4030, 'new_value': 4350}]
2025-07-12 09:01:37,876 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMK9
2025-07-12 09:01:38,313 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMK9
2025-07-12 09:01:38,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44126.0, 'new_value': 50628.8}, {'field': 'total_amount', 'old_value': 44126.0, 'new_value': 50628.8}, {'field': 'order_count', 'old_value': 198, 'new_value': 229}]
2025-07-12 09:01:38,313 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI2
2025-07-12 09:01:38,782 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMI2
2025-07-12 09:01:38,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24805.0, 'new_value': 28798.0}, {'field': 'total_amount', 'old_value': 24805.0, 'new_value': 28798.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-07-12 09:01:38,782 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML2
2025-07-12 09:01:39,235 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCML2
2025-07-12 09:01:39,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85367.0, 'new_value': 100173.0}, {'field': 'total_amount', 'old_value': 85367.0, 'new_value': 100173.0}, {'field': 'order_count', 'old_value': 380, 'new_value': 440}]
2025-07-12 09:01:39,235 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM2
2025-07-12 09:01:39,704 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMM2
2025-07-12 09:01:39,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7697.84, 'new_value': 7957.44}, {'field': 'offline_amount', 'old_value': 81582.0, 'new_value': 94412.0}, {'field': 'total_amount', 'old_value': 89279.84, 'new_value': 102369.44}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-07-12 09:01:39,704 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMM9
2025-07-12 09:01:40,157 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMM9
2025-07-12 09:01:40,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9402.0, 'new_value': 11238.0}, {'field': 'offline_amount', 'old_value': 8171.0, 'new_value': 8998.0}, {'field': 'total_amount', 'old_value': 17573.0, 'new_value': 20236.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 162}]
2025-07-12 09:01:40,157 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS2
2025-07-12 09:01:40,657 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMS2
2025-07-12 09:01:40,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148909.0, 'new_value': 166369.0}, {'field': 'total_amount', 'old_value': 148909.0, 'new_value': 166369.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 154}]
2025-07-12 09:01:40,657 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMO9
2025-07-12 09:01:41,079 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMO9
2025-07-12 09:01:41,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27739.53, 'new_value': 31125.22}, {'field': 'total_amount', 'old_value': 27739.53, 'new_value': 31125.22}, {'field': 'order_count', 'old_value': 228, 'new_value': 247}]
2025-07-12 09:01:41,079 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMV2
2025-07-12 09:01:41,626 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMV2
2025-07-12 09:01:41,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12818.0, 'new_value': 14244.0}, {'field': 'total_amount', 'old_value': 12818.0, 'new_value': 14244.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 24}]
2025-07-12 09:01:41,626 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY2
2025-07-12 09:01:42,141 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY2
2025-07-12 09:01:42,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16396.0, 'new_value': 20395.0}, {'field': 'total_amount', 'old_value': 16396.0, 'new_value': 20395.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-07-12 09:01:42,141 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMP9
2025-07-12 09:01:42,626 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMP9
2025-07-12 09:01:42,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18582.9, 'new_value': 19085.4}, {'field': 'total_amount', 'old_value': 18582.9, 'new_value': 19085.4}, {'field': 'order_count', 'old_value': 337, 'new_value': 369}]
2025-07-12 09:01:42,626 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM03
2025-07-12 09:01:43,079 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM03
2025-07-12 09:01:43,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60329.0, 'new_value': 79683.52}, {'field': 'total_amount', 'old_value': 136074.92, 'new_value': 155429.44}, {'field': 'order_count', 'old_value': 216, 'new_value': 252}]
2025-07-12 09:01:43,079 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM23
2025-07-12 09:01:43,485 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM23
2025-07-12 09:01:43,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37928.0, 'new_value': 43732.0}, {'field': 'offline_amount', 'old_value': 53515.0, 'new_value': 64257.0}, {'field': 'total_amount', 'old_value': 91443.0, 'new_value': 107989.0}, {'field': 'order_count', 'old_value': 6110, 'new_value': 6165}]
2025-07-12 09:01:43,485 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM33
2025-07-12 09:01:43,969 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM33
2025-07-12 09:01:43,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4946.0, 'new_value': 5105.0}, {'field': 'offline_amount', 'old_value': 24447.0, 'new_value': 31142.0}, {'field': 'total_amount', 'old_value': 29393.0, 'new_value': 36247.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 294}]
2025-07-12 09:01:43,969 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC3
2025-07-12 09:01:44,391 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC3
2025-07-12 09:01:44,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113256.0, 'new_value': 125636.0}, {'field': 'total_amount', 'old_value': 119931.0, 'new_value': 132311.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 92}]
2025-07-12 09:01:44,391 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD3
2025-07-12 09:01:44,860 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD3
2025-07-12 09:01:44,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5854.0, 'new_value': 7636.0}, {'field': 'total_amount', 'old_value': 5854.0, 'new_value': 7636.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-07-12 09:01:44,860 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMR9
2025-07-12 09:01:45,391 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMR9
2025-07-12 09:01:45,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45659.0, 'new_value': 49708.0}, {'field': 'total_amount', 'old_value': 45659.0, 'new_value': 49708.0}, {'field': 'order_count', 'old_value': 3357, 'new_value': 3626}]
2025-07-12 09:01:45,391 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO
2025-07-12 09:01:45,922 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO
2025-07-12 09:01:45,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125150.0, 'new_value': 138710.0}, {'field': 'total_amount', 'old_value': 125150.0, 'new_value': 138710.0}, {'field': 'order_count', 'old_value': 2975, 'new_value': 3291}]
2025-07-12 09:01:45,922 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR
2025-07-12 09:01:46,422 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR
2025-07-12 09:01:46,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46464.69, 'new_value': 50149.39}, {'field': 'total_amount', 'old_value': 46464.69, 'new_value': 50149.39}, {'field': 'order_count', 'old_value': 297, 'new_value': 320}]
2025-07-12 09:01:46,422 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS
2025-07-12 09:01:46,860 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS
2025-07-12 09:01:46,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26902.0, 'new_value': 29360.0}, {'field': 'total_amount', 'old_value': 26902.0, 'new_value': 29360.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 110}]
2025-07-12 09:01:46,860 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT
2025-07-12 09:01:47,266 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT
2025-07-12 09:01:47,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17336.84, 'new_value': 18988.46}, {'field': 'offline_amount', 'old_value': 17773.97, 'new_value': 19526.75}, {'field': 'total_amount', 'old_value': 35110.81, 'new_value': 38515.21}, {'field': 'order_count', 'old_value': 1923, 'new_value': 2105}]
2025-07-12 09:01:47,266 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV
2025-07-12 09:01:47,751 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV
2025-07-12 09:01:47,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15037.44, 'new_value': 16293.95}, {'field': 'offline_amount', 'old_value': 10405.0, 'new_value': 10944.0}, {'field': 'total_amount', 'old_value': 25442.44, 'new_value': 27237.95}, {'field': 'order_count', 'old_value': 333, 'new_value': 357}]
2025-07-12 09:01:47,751 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY
2025-07-12 09:01:48,204 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY
2025-07-12 09:01:48,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13744.55, 'new_value': 14153.55}, {'field': 'total_amount', 'old_value': 13744.55, 'new_value': 14153.55}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-07-12 09:01:48,219 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG1
2025-07-12 09:01:48,641 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG1
2025-07-12 09:01:48,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7731.5, 'new_value': 8318.5}, {'field': 'offline_amount', 'old_value': 211612.27, 'new_value': 233704.07}, {'field': 'total_amount', 'old_value': 219343.77, 'new_value': 242022.57}, {'field': 'order_count', 'old_value': 731, 'new_value': 798}]
2025-07-12 09:01:48,641 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM1
2025-07-12 09:01:49,157 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM1
2025-07-12 09:01:49,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32860.0, 'new_value': 35510.0}, {'field': 'total_amount', 'old_value': 32860.0, 'new_value': 35510.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 134}]
2025-07-12 09:01:49,157 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ1
2025-07-12 09:01:49,672 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ1
2025-07-12 09:01:49,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14841.0, 'new_value': 16047.6}, {'field': 'offline_amount', 'old_value': 401925.38, 'new_value': 446621.07}, {'field': 'total_amount', 'old_value': 416766.38, 'new_value': 462668.67}, {'field': 'order_count', 'old_value': 2011, 'new_value': 2227}]
2025-07-12 09:01:49,672 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY1
2025-07-12 09:01:50,094 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY1
2025-07-12 09:01:50,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102157.46, 'new_value': 112321.96}, {'field': 'offline_amount', 'old_value': 39373.69, 'new_value': 44167.05}, {'field': 'total_amount', 'old_value': 141531.15, 'new_value': 156489.01}, {'field': 'order_count', 'old_value': 459, 'new_value': 511}]
2025-07-12 09:01:50,094 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM52
2025-07-12 09:01:50,563 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM52
2025-07-12 09:01:50,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97194.0, 'new_value': 108178.0}, {'field': 'total_amount', 'old_value': 97202.0, 'new_value': 108186.0}, {'field': 'order_count', 'old_value': 3275, 'new_value': 3597}]
2025-07-12 09:01:50,563 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM72
2025-07-12 09:01:51,032 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM72
2025-07-12 09:01:51,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20780.0, 'new_value': 28935.0}, {'field': 'total_amount', 'old_value': 21217.0, 'new_value': 29372.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-07-12 09:01:51,032 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMS9
2025-07-12 09:01:51,516 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMS9
2025-07-12 09:01:51,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98254.0, 'new_value': 108401.0}, {'field': 'total_amount', 'old_value': 98254.0, 'new_value': 108401.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 153}]
2025-07-12 09:01:51,516 - INFO - 日期 2025-07 处理完成 - 更新: 148 条，插入: 0 条，错误: 0 条
2025-07-12 09:01:51,516 - INFO - 数据同步完成！更新: 148 条，插入: 0 条，错误: 0 条
2025-07-12 09:01:51,516 - INFO - =================同步完成====================
2025-07-12 12:00:03,195 - INFO - =================使用默认全量同步=============
2025-07-12 12:00:05,148 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 12:00:05,148 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 12:00:05,195 - INFO - 开始处理日期: 2025-01
2025-07-12 12:00:05,195 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:05,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:05,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:07,164 - INFO - Response - Page 1:
2025-07-12 12:00:07,367 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:07,367 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:07,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:07,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:07,945 - INFO - Response - Page 2:
2025-07-12 12:00:08,148 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:08,148 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:08,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:08,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:08,742 - INFO - Response - Page 3:
2025-07-12 12:00:08,945 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:08,945 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:08,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:08,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:09,445 - INFO - Response - Page 4:
2025-07-12 12:00:09,648 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:09,648 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:09,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:09,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:10,132 - INFO - Response - Page 5:
2025-07-12 12:00:10,336 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:10,336 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:10,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:10,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:10,882 - INFO - Response - Page 6:
2025-07-12 12:00:11,086 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:11,086 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:11,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:11,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:11,632 - INFO - Response - Page 7:
2025-07-12 12:00:11,836 - INFO - 第 7 页获取到 82 条记录
2025-07-12 12:00:11,836 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 12:00:11,836 - INFO - 获取到 682 条表单数据
2025-07-12 12:00:11,836 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 12:00:11,851 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 12:00:11,851 - INFO - 开始处理日期: 2025-02
2025-07-12 12:00:11,851 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:11,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:11,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:12,351 - INFO - Response - Page 1:
2025-07-12 12:00:12,554 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:12,554 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:12,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:12,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:13,101 - INFO - Response - Page 2:
2025-07-12 12:00:13,304 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:13,304 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:13,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:13,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:13,882 - INFO - Response - Page 3:
2025-07-12 12:00:14,086 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:14,086 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:14,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:14,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:14,664 - INFO - Response - Page 4:
2025-07-12 12:00:14,867 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:14,867 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:14,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:14,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:15,367 - INFO - Response - Page 5:
2025-07-12 12:00:15,570 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:15,570 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:15,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:15,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:16,070 - INFO - Response - Page 6:
2025-07-12 12:00:16,273 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:16,273 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:16,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:16,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:16,789 - INFO - Response - Page 7:
2025-07-12 12:00:16,992 - INFO - 第 7 页获取到 70 条记录
2025-07-12 12:00:16,992 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 12:00:16,992 - INFO - 获取到 670 条表单数据
2025-07-12 12:00:16,992 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 12:00:17,007 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 12:00:17,007 - INFO - 开始处理日期: 2025-03
2025-07-12 12:00:17,007 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:17,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:17,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:17,648 - INFO - Response - Page 1:
2025-07-12 12:00:17,851 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:17,851 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:17,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:17,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:18,460 - INFO - Response - Page 2:
2025-07-12 12:00:18,664 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:18,664 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:18,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:18,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:19,210 - INFO - Response - Page 3:
2025-07-12 12:00:19,414 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:19,414 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:19,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:19,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:20,429 - INFO - Response - Page 4:
2025-07-12 12:00:20,632 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:20,632 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:20,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:20,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:21,164 - INFO - Response - Page 5:
2025-07-12 12:00:21,367 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:21,367 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:21,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:21,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:21,882 - INFO - Response - Page 6:
2025-07-12 12:00:22,085 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:22,085 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:22,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:22,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:22,492 - INFO - Response - Page 7:
2025-07-12 12:00:22,695 - INFO - 第 7 页获取到 61 条记录
2025-07-12 12:00:22,695 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 12:00:22,695 - INFO - 获取到 661 条表单数据
2025-07-12 12:00:22,710 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 12:00:22,710 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 12:00:22,710 - INFO - 开始处理日期: 2025-04
2025-07-12 12:00:22,710 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:22,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:22,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:23,257 - INFO - Response - Page 1:
2025-07-12 12:00:23,460 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:23,460 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:23,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:23,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:24,039 - INFO - Response - Page 2:
2025-07-12 12:00:24,242 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:24,242 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:24,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:24,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:24,929 - INFO - Response - Page 3:
2025-07-12 12:00:25,132 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:25,132 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:25,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:25,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:25,570 - INFO - Response - Page 4:
2025-07-12 12:00:25,773 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:25,773 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:25,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:25,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:26,304 - INFO - Response - Page 5:
2025-07-12 12:00:26,507 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:26,507 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:26,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:26,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:27,007 - INFO - Response - Page 6:
2025-07-12 12:00:27,210 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:27,210 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:27,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:27,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:27,632 - INFO - Response - Page 7:
2025-07-12 12:00:27,835 - INFO - 第 7 页获取到 56 条记录
2025-07-12 12:00:27,835 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 12:00:27,835 - INFO - 获取到 656 条表单数据
2025-07-12 12:00:27,835 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 12:00:27,851 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 12:00:27,851 - INFO - 开始处理日期: 2025-05
2025-07-12 12:00:27,851 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:27,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:27,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:28,460 - INFO - Response - Page 1:
2025-07-12 12:00:28,664 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:28,664 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:28,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:28,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:29,179 - INFO - Response - Page 2:
2025-07-12 12:00:29,382 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:29,382 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:29,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:29,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:29,898 - INFO - Response - Page 3:
2025-07-12 12:00:30,101 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:30,101 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:30,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:30,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:30,601 - INFO - Response - Page 4:
2025-07-12 12:00:30,804 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:30,804 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:30,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:30,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:31,320 - INFO - Response - Page 5:
2025-07-12 12:00:31,523 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:31,523 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:31,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:31,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:32,023 - INFO - Response - Page 6:
2025-07-12 12:00:32,226 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:32,226 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:32,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:32,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:32,851 - INFO - Response - Page 7:
2025-07-12 12:00:33,054 - INFO - 第 7 页获取到 65 条记录
2025-07-12 12:00:33,054 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 12:00:33,054 - INFO - 获取到 665 条表单数据
2025-07-12 12:00:33,054 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 12:00:33,070 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 12:00:33,070 - INFO - 开始处理日期: 2025-06
2025-07-12 12:00:33,070 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:33,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:33,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:33,570 - INFO - Response - Page 1:
2025-07-12 12:00:33,773 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:33,773 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:33,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:33,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:34,242 - INFO - Response - Page 2:
2025-07-12 12:00:34,460 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:34,460 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:34,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:34,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:34,913 - INFO - Response - Page 3:
2025-07-12 12:00:35,117 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:35,117 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:35,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:35,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:35,663 - INFO - Response - Page 4:
2025-07-12 12:00:35,867 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:35,867 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:35,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:35,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:36,398 - INFO - Response - Page 5:
2025-07-12 12:00:36,601 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:36,601 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:36,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:36,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:37,132 - INFO - Response - Page 6:
2025-07-12 12:00:37,335 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:37,335 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:37,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:37,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:37,773 - INFO - Response - Page 7:
2025-07-12 12:00:37,992 - INFO - 第 7 页获取到 33 条记录
2025-07-12 12:00:37,992 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 12:00:37,992 - INFO - 获取到 633 条表单数据
2025-07-12 12:00:37,992 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 12:00:38,007 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 12:00:38,007 - INFO - 开始处理日期: 2025-07
2025-07-12 12:00:38,007 - INFO - Request Parameters - Page 1:
2025-07-12 12:00:38,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:38,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:38,570 - INFO - Response - Page 1:
2025-07-12 12:00:38,773 - INFO - 第 1 页获取到 100 条记录
2025-07-12 12:00:38,773 - INFO - Request Parameters - Page 2:
2025-07-12 12:00:38,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:38,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:39,273 - INFO - Response - Page 2:
2025-07-12 12:00:39,476 - INFO - 第 2 页获取到 100 条记录
2025-07-12 12:00:39,476 - INFO - Request Parameters - Page 3:
2025-07-12 12:00:39,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:39,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:39,992 - INFO - Response - Page 3:
2025-07-12 12:00:40,195 - INFO - 第 3 页获取到 100 条记录
2025-07-12 12:00:40,195 - INFO - Request Parameters - Page 4:
2025-07-12 12:00:40,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:40,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:40,788 - INFO - Response - Page 4:
2025-07-12 12:00:40,992 - INFO - 第 4 页获取到 100 条记录
2025-07-12 12:00:40,992 - INFO - Request Parameters - Page 5:
2025-07-12 12:00:40,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:40,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:41,538 - INFO - Response - Page 5:
2025-07-12 12:00:41,742 - INFO - 第 5 页获取到 100 条记录
2025-07-12 12:00:41,742 - INFO - Request Parameters - Page 6:
2025-07-12 12:00:41,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:41,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:42,226 - INFO - Response - Page 6:
2025-07-12 12:00:42,429 - INFO - 第 6 页获取到 100 条记录
2025-07-12 12:00:42,429 - INFO - Request Parameters - Page 7:
2025-07-12 12:00:42,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 12:00:42,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 12:00:42,695 - INFO - Response - Page 7:
2025-07-12 12:00:42,898 - INFO - 第 7 页获取到 9 条记录
2025-07-12 12:00:42,898 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 12:00:42,898 - INFO - 获取到 609 条表单数据
2025-07-12 12:00:42,898 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 12:00:42,898 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMS4
2025-07-12 12:00:43,382 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMS4
2025-07-12 12:00:43,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129350.0, 'new_value': 157700.0}, {'field': 'total_amount', 'old_value': 129350.0, 'new_value': 157700.0}]
2025-07-12 12:00:43,398 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMY4
2025-07-12 12:00:43,867 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMY4
2025-07-12 12:00:43,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5937.0, 'new_value': 6706.0}, {'field': 'total_amount', 'old_value': 5937.0, 'new_value': 6706.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 68}]
2025-07-12 12:00:43,867 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMZ4
2025-07-12 12:00:44,320 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCMZ4
2025-07-12 12:00:44,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77384.0, 'new_value': 84228.0}, {'field': 'offline_amount', 'old_value': 37576.0, 'new_value': 39788.0}, {'field': 'total_amount', 'old_value': 114960.0, 'new_value': 124016.0}, {'field': 'order_count', 'old_value': 1729, 'new_value': 1881}]
2025-07-12 12:00:44,320 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05
2025-07-12 12:00:44,757 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05
2025-07-12 12:00:44,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8840.05, 'new_value': 10233.05}, {'field': 'offline_amount', 'old_value': 5598.75, 'new_value': 6118.75}, {'field': 'total_amount', 'old_value': 14438.8, 'new_value': 16351.8}, {'field': 'order_count', 'old_value': 586, 'new_value': 660}]
2025-07-12 12:00:44,757 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM64
2025-07-12 12:00:45,210 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM64
2025-07-12 12:00:45,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 732.0, 'new_value': 920.0}, {'field': 'total_amount', 'old_value': 732.0, 'new_value': 920.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-07-12 12:00:45,210 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM35
2025-07-12 12:00:45,679 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM35
2025-07-12 12:00:45,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12437.15, 'new_value': 12614.15}, {'field': 'total_amount', 'old_value': 12437.15, 'new_value': 12614.15}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-07-12 12:00:45,679 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM45
2025-07-12 12:00:46,132 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM45
2025-07-12 12:00:46,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22238.07, 'new_value': 55964.14}, {'field': 'total_amount', 'old_value': 22238.07, 'new_value': 55964.14}, {'field': 'order_count', 'old_value': 84, 'new_value': 99}]
2025-07-12 12:00:46,132 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM74
2025-07-12 12:00:46,695 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM74
2025-07-12 12:00:46,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 397.0, 'new_value': 801.0}, {'field': 'offline_amount', 'old_value': 43175.0, 'new_value': 43316.0}, {'field': 'total_amount', 'old_value': 43572.0, 'new_value': 44117.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 17}]
2025-07-12 12:00:46,695 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG5
2025-07-12 12:00:47,132 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMG5
2025-07-12 12:00:47,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42170.0, 'new_value': 46887.0}, {'field': 'total_amount', 'old_value': 42170.0, 'new_value': 46887.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-07-12 12:00:47,132 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI5
2025-07-12 12:00:47,695 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMI5
2025-07-12 12:00:47,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65291.8, 'new_value': 73967.39}, {'field': 'total_amount', 'old_value': 65291.8, 'new_value': 73967.39}, {'field': 'order_count', 'old_value': 384, 'new_value': 428}]
2025-07-12 12:00:47,695 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR7
2025-07-12 12:00:48,179 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMR7
2025-07-12 12:00:48,179 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20886.57, 'new_value': 22766.31}, {'field': 'offline_amount', 'old_value': 13469.75, 'new_value': 14782.21}, {'field': 'total_amount', 'old_value': 34356.32, 'new_value': 37548.52}, {'field': 'order_count', 'old_value': 1740, 'new_value': 1897}]
2025-07-12 12:00:48,179 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ5
2025-07-12 12:00:48,695 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ5
2025-07-12 12:00:48,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166478.46, 'new_value': 203610.9}, {'field': 'total_amount', 'old_value': 166478.46, 'new_value': 203610.9}, {'field': 'order_count', 'old_value': 140, 'new_value': 159}]
2025-07-12 12:00:48,695 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML5
2025-07-12 12:00:49,320 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCML5
2025-07-12 12:00:49,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8388.2, 'new_value': 11127.2}, {'field': 'total_amount', 'old_value': 8388.2, 'new_value': 11127.2}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-07-12 12:00:49,320 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM5
2025-07-12 12:00:49,851 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM5
2025-07-12 12:00:49,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 197206.38, 'new_value': 229711.69}, {'field': 'total_amount', 'old_value': 197206.38, 'new_value': 229711.69}, {'field': 'order_count', 'old_value': 1517, 'new_value': 1776}]
2025-07-12 12:00:49,851 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM84
2025-07-12 12:00:50,335 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM84
2025-07-12 12:00:50,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19636.0, 'new_value': 20935.0}, {'field': 'total_amount', 'old_value': 30136.0, 'new_value': 31435.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-07-12 12:00:50,335 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ5
2025-07-12 12:00:50,820 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMQ5
2025-07-12 12:00:50,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71580.0, 'new_value': 83260.0}, {'field': 'total_amount', 'old_value': 80440.0, 'new_value': 92120.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-07-12 12:00:50,820 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT5
2025-07-12 12:00:51,257 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMT5
2025-07-12 12:00:51,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8522.0, 'new_value': 9028.0}, {'field': 'total_amount', 'old_value': 10546.0, 'new_value': 11052.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 54}]
2025-07-12 12:00:51,257 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM94
2025-07-12 12:00:51,663 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCM94
2025-07-12 12:00:51,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138547.0, 'new_value': 204147.0}, {'field': 'total_amount', 'old_value': 158547.0, 'new_value': 224147.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 16}]
2025-07-12 12:00:51,663 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMA4
2025-07-12 12:00:52,148 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMA4
2025-07-12 12:00:52,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148280.0, 'new_value': 190080.0}, {'field': 'total_amount', 'old_value': 208280.0, 'new_value': 250080.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 35}]
2025-07-12 12:00:52,148 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMB4
2025-07-12 12:00:52,570 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMB4
2025-07-12 12:00:52,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108320.0, 'new_value': 160420.0}, {'field': 'total_amount', 'old_value': 148320.0, 'new_value': 200420.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 24}]
2025-07-12 12:00:52,570 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMC4
2025-07-12 12:00:52,976 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMC4
2025-07-12 12:00:52,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10682.0, 'new_value': 18645.0}, {'field': 'total_amount', 'old_value': 10682.0, 'new_value': 18645.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 67}]
2025-07-12 12:00:52,976 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM26
2025-07-12 12:00:53,382 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM26
2025-07-12 12:00:53,382 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2248.99, 'new_value': 2675.84}, {'field': 'offline_amount', 'old_value': 63578.5, 'new_value': 73491.4}, {'field': 'total_amount', 'old_value': 65827.49, 'new_value': 76167.24}, {'field': 'order_count', 'old_value': 449, 'new_value': 529}]
2025-07-12 12:00:53,382 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM36
2025-07-12 12:00:53,898 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM36
2025-07-12 12:00:53,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43295.16, 'new_value': 49423.46}, {'field': 'offline_amount', 'old_value': 88380.23, 'new_value': 105495.21}, {'field': 'total_amount', 'old_value': 131675.39, 'new_value': 154918.67}, {'field': 'order_count', 'old_value': 1131, 'new_value': 1302}]
2025-07-12 12:00:53,898 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM56
2025-07-12 12:00:54,304 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCM56
2025-07-12 12:00:54,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44052.94, 'new_value': 47582.44}, {'field': 'offline_amount', 'old_value': 209757.85, 'new_value': 227506.31}, {'field': 'total_amount', 'old_value': 253810.79, 'new_value': 275088.75}, {'field': 'order_count', 'old_value': 758, 'new_value': 837}]
2025-07-12 12:00:54,320 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB6
2025-07-12 12:00:54,741 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMB6
2025-07-12 12:00:54,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23335.0, 'new_value': 27541.0}, {'field': 'total_amount', 'old_value': 62093.0, 'new_value': 66299.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-07-12 12:00:54,741 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMD4
2025-07-12 12:00:55,226 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMD4
2025-07-12 12:00:55,226 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4567.2, 'new_value': 5545.0}, {'field': 'offline_amount', 'old_value': 12435.5, 'new_value': 14072.1}, {'field': 'total_amount', 'old_value': 17002.7, 'new_value': 19617.1}, {'field': 'order_count', 'old_value': 100, 'new_value': 117}]
2025-07-12 12:00:55,226 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCME4
2025-07-12 12:00:55,726 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCME4
2025-07-12 12:00:55,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13932.0, 'new_value': 16820.0}, {'field': 'total_amount', 'old_value': 13932.0, 'new_value': 16820.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 85}]
2025-07-12 12:00:55,726 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH6
2025-07-12 12:00:56,163 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMH6
2025-07-12 12:00:56,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28506.98, 'new_value': 31598.52}, {'field': 'offline_amount', 'old_value': 334197.34, 'new_value': 374136.59}, {'field': 'total_amount', 'old_value': 362704.32, 'new_value': 405735.11}, {'field': 'order_count', 'old_value': 1519, 'new_value': 1696}]
2025-07-12 12:00:56,163 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ6
2025-07-12 12:00:56,648 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMJ6
2025-07-12 12:00:56,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78111.9, 'new_value': 85624.69}, {'field': 'offline_amount', 'old_value': 48209.3, 'new_value': 51180.3}, {'field': 'total_amount', 'old_value': 126321.2, 'new_value': 136804.99}, {'field': 'order_count', 'old_value': 1173, 'new_value': 1287}]
2025-07-12 12:00:56,648 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM6
2025-07-12 12:00:57,085 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMM6
2025-07-12 12:00:57,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19694.0, 'new_value': 23774.0}, {'field': 'total_amount', 'old_value': 19694.0, 'new_value': 23774.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-07-12 12:00:57,085 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN6
2025-07-12 12:00:57,632 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMN6
2025-07-12 12:00:57,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107770.9, 'new_value': 184418.5}, {'field': 'total_amount', 'old_value': 107770.9, 'new_value': 184418.5}, {'field': 'order_count', 'old_value': 12, 'new_value': 17}]
2025-07-12 12:00:57,632 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMT8
2025-07-12 12:00:58,085 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMT8
2025-07-12 12:00:58,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36186.0, 'new_value': 46164.0}, {'field': 'offline_amount', 'old_value': 49422.0, 'new_value': 62300.0}, {'field': 'total_amount', 'old_value': 85608.0, 'new_value': 108464.0}, {'field': 'order_count', 'old_value': 1996, 'new_value': 2497}]
2025-07-12 12:00:58,085 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP6
2025-07-12 12:00:58,538 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMP6
2025-07-12 12:00:58,538 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92070.0, 'new_value': 107539.0}, {'field': 'total_amount', 'old_value': 92070.0, 'new_value': 107539.0}, {'field': 'order_count', 'old_value': 425, 'new_value': 487}]
2025-07-12 12:00:58,538 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMF4
2025-07-12 12:00:58,976 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMF4
2025-07-12 12:00:58,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18037.0, 'new_value': 21735.0}, {'field': 'total_amount', 'old_value': 18037.0, 'new_value': 21735.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 24}]
2025-07-12 12:00:58,976 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS6
2025-07-12 12:00:59,476 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMS6
2025-07-12 12:00:59,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 710409.35, 'new_value': 780954.87}, {'field': 'offline_amount', 'old_value': 128544.0, 'new_value': 136172.0}, {'field': 'total_amount', 'old_value': 838953.35, 'new_value': 917126.87}, {'field': 'order_count', 'old_value': 3086, 'new_value': 3394}]
2025-07-12 12:00:59,476 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW6
2025-07-12 12:00:59,976 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMW6
2025-07-12 12:00:59,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3832.0, 'new_value': 4228.0}, {'field': 'offline_amount', 'old_value': 9754.0, 'new_value': 11042.0}, {'field': 'total_amount', 'old_value': 13586.0, 'new_value': 15270.0}, {'field': 'order_count', 'old_value': 473, 'new_value': 536}]
2025-07-12 12:00:59,976 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMG4
2025-07-12 12:01:00,429 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMG4
2025-07-12 12:01:00,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1097.3, 'new_value': 1304.85}, {'field': 'offline_amount', 'old_value': 51768.68, 'new_value': 63019.81}, {'field': 'total_amount', 'old_value': 52865.98, 'new_value': 64324.66}, {'field': 'order_count', 'old_value': 254, 'new_value': 310}]
2025-07-12 12:01:00,429 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA7
2025-07-12 12:01:00,898 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMA7
2025-07-12 12:01:00,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88948.0, 'new_value': 92524.0}, {'field': 'total_amount', 'old_value': 88948.0, 'new_value': 92524.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 13}]
2025-07-12 12:01:00,898 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME7
2025-07-12 12:01:01,351 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCME7
2025-07-12 12:01:01,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163404.66, 'new_value': 190374.99}, {'field': 'total_amount', 'old_value': 163404.66, 'new_value': 190374.99}, {'field': 'order_count', 'old_value': 1227, 'new_value': 1419}]
2025-07-12 12:01:01,351 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF7
2025-07-12 12:01:01,913 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2NVHIFLCMF7
2025-07-12 12:01:01,913 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113781.0, 'new_value': 131771.0}, {'field': 'offline_amount', 'old_value': 37478.0, 'new_value': 41393.0}, {'field': 'total_amount', 'old_value': 151259.0, 'new_value': 173164.0}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1271}]
2025-07-12 12:01:01,913 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMH4
2025-07-12 12:01:02,398 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMH4
2025-07-12 12:01:02,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9000.0, 'new_value': 12600.0}, {'field': 'total_amount', 'old_value': 9000.0, 'new_value': 12600.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-07-12 12:01:02,398 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMI4
2025-07-12 12:01:02,820 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMI4
2025-07-12 12:01:02,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39830.0, 'new_value': 41827.0}, {'field': 'total_amount', 'old_value': 45829.0, 'new_value': 47826.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-07-12 12:01:02,820 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMJ4
2025-07-12 12:01:03,257 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMJ4
2025-07-12 12:01:03,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16251.72, 'new_value': 18443.92}, {'field': 'total_amount', 'old_value': 16251.72, 'new_value': 18443.92}, {'field': 'order_count', 'old_value': 39, 'new_value': 45}]
2025-07-12 12:01:03,257 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN
2025-07-12 12:01:03,788 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN
2025-07-12 12:01:03,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6415.11, 'new_value': 7080.38}, {'field': 'offline_amount', 'old_value': 129615.6, 'new_value': 143559.6}, {'field': 'total_amount', 'old_value': 136030.71, 'new_value': 150639.98}, {'field': 'order_count', 'old_value': 711, 'new_value': 781}]
2025-07-12 12:01:03,788 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU
2025-07-12 12:01:04,288 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU
2025-07-12 12:01:04,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35291.31, 'new_value': 42703.95}, {'field': 'offline_amount', 'old_value': 155349.36, 'new_value': 163600.17}, {'field': 'total_amount', 'old_value': 190640.67, 'new_value': 206304.12}, {'field': 'order_count', 'old_value': 1415, 'new_value': 1598}]
2025-07-12 12:01:04,288 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM01
2025-07-12 12:01:04,835 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCM01
2025-07-12 12:01:04,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144973.41, 'new_value': 159319.14}, {'field': 'total_amount', 'old_value': 144973.41, 'new_value': 159319.14}, {'field': 'order_count', 'old_value': 5049, 'new_value': 5554}]
2025-07-12 12:01:04,835 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMK4
2025-07-12 12:01:05,335 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMK4
2025-07-12 12:01:05,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119795.0, 'new_value': 144439.0}, {'field': 'total_amount', 'old_value': 120990.0, 'new_value': 145634.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 123}]
2025-07-12 12:01:05,335 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI1
2025-07-12 12:01:05,757 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMI1
2025-07-12 12:01:05,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128.0, 'new_value': 456.0}, {'field': 'offline_amount', 'old_value': 51876.0, 'new_value': 74292.0}, {'field': 'total_amount', 'old_value': 52004.0, 'new_value': 74748.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-07-12 12:01:05,757 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS1
2025-07-12 12:01:06,210 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMS1
2025-07-12 12:01:06,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81553.5, 'new_value': 93971.5}, {'field': 'total_amount', 'old_value': 81553.5, 'new_value': 93971.5}, {'field': 'order_count', 'old_value': 503, 'new_value': 567}]
2025-07-12 12:01:06,210 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO8
2025-07-12 12:01:06,663 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY27TT29LCMO8
2025-07-12 12:01:06,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6970.0}, {'field': 'total_amount', 'old_value': 73224.9, 'new_value': 80194.9}, {'field': 'order_count', 'old_value': 979, 'new_value': 1129}]
2025-07-12 12:01:06,663 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCML4
2025-07-12 12:01:07,241 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCML4
2025-07-12 12:01:07,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34331.12, 'new_value': 40573.78}, {'field': 'offline_amount', 'old_value': 25298.75, 'new_value': 31035.38}, {'field': 'total_amount', 'old_value': 59629.87, 'new_value': 71609.16}, {'field': 'order_count', 'old_value': 2650, 'new_value': 3207}]
2025-07-12 12:01:07,241 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMM4
2025-07-12 12:01:07,663 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMM4
2025-07-12 12:01:07,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62174.24, 'new_value': 63443.24}, {'field': 'total_amount', 'old_value': 62174.24, 'new_value': 63443.24}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-07-12 12:01:07,663 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB2
2025-07-12 12:01:08,085 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMB2
2025-07-12 12:01:08,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2550.0, 'new_value': 2930.0}, {'field': 'offline_amount', 'old_value': 6138.0, 'new_value': 6338.0}, {'field': 'total_amount', 'old_value': 8688.0, 'new_value': 9268.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-07-12 12:01:08,085 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF2
2025-07-12 12:01:08,648 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMF2
2025-07-12 12:01:08,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18333.0, 'new_value': 23368.0}, {'field': 'offline_amount', 'old_value': 47296.0, 'new_value': 51853.0}, {'field': 'total_amount', 'old_value': 65629.0, 'new_value': 75221.0}, {'field': 'order_count', 'old_value': 1569, 'new_value': 1826}]
2025-07-12 12:01:08,648 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMN4
2025-07-12 12:01:09,116 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMN4
2025-07-12 12:01:09,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26192.25, 'new_value': 31363.05}, {'field': 'offline_amount', 'old_value': 10014.7, 'new_value': 12498.17}, {'field': 'total_amount', 'old_value': 36206.95, 'new_value': 43861.22}, {'field': 'order_count', 'old_value': 1280, 'new_value': 1534}]
2025-07-12 12:01:09,116 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMO4
2025-07-12 12:01:09,538 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMO4
2025-07-12 12:01:09,538 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9251.7, 'new_value': 11376.3}, {'field': 'offline_amount', 'old_value': 85250.9, 'new_value': 104961.0}, {'field': 'total_amount', 'old_value': 94502.6, 'new_value': 116337.3}, {'field': 'order_count', 'old_value': 2531, 'new_value': 3242}]
2025-07-12 12:01:09,538 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN2
2025-07-12 12:01:10,007 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMN2
2025-07-12 12:01:10,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88899.8, 'new_value': 96679.8}, {'field': 'total_amount', 'old_value': 88899.8, 'new_value': 96679.8}, {'field': 'order_count', 'old_value': 475, 'new_value': 516}]
2025-07-12 12:01:10,007 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP2
2025-07-12 12:01:10,710 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMP2
2025-07-12 12:01:10,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2820.0, 'new_value': 3640.0}, {'field': 'total_amount', 'old_value': 2820.0, 'new_value': 3640.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 24}]
2025-07-12 12:01:10,710 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ2
2025-07-12 12:01:11,148 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMQ2
2025-07-12 12:01:11,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228843.75, 'new_value': 252417.86}, {'field': 'total_amount', 'old_value': 228843.75, 'new_value': 252417.86}, {'field': 'order_count', 'old_value': 626, 'new_value': 697}]
2025-07-12 12:01:11,148 - INFO - 开始更新记录 - 表单实例ID: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU2
2025-07-12 12:01:11,569 - INFO - 更新表单数据成功: FINST-1MD668B19VSWS0MM9TQF06KPTJ8221FKIFLCMU2
2025-07-12 12:01:11,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16921.6, 'new_value': 17311.6}, {'field': 'total_amount', 'old_value': 146757.4, 'new_value': 147147.4}, {'field': 'order_count', 'old_value': 3835, 'new_value': 19310}]
2025-07-12 12:01:11,569 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMP4
2025-07-12 12:01:12,007 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMP4
2025-07-12 12:01:12,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 433758.0, 'new_value': 546446.0}, {'field': 'offline_amount', 'old_value': 148083.0, 'new_value': 170082.0}, {'field': 'total_amount', 'old_value': 581841.0, 'new_value': 716528.0}, {'field': 'order_count', 'old_value': 530, 'new_value': 681}]
2025-07-12 12:01:12,007 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMW8
2025-07-12 12:01:12,429 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMW8
2025-07-12 12:01:12,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18963.2, 'new_value': 20750.7}, {'field': 'total_amount', 'old_value': 18963.2, 'new_value': 20750.7}, {'field': 'order_count', 'old_value': 17, 'new_value': 21}]
2025-07-12 12:01:12,429 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMW
2025-07-12 12:01:12,898 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMW
2025-07-12 12:01:12,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34037.44, 'new_value': 40340.06}, {'field': 'offline_amount', 'old_value': 135690.51, 'new_value': 152363.08}, {'field': 'total_amount', 'old_value': 169727.95, 'new_value': 192703.14}, {'field': 'order_count', 'old_value': 1336, 'new_value': 1554}]
2025-07-12 12:01:12,898 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMY
2025-07-12 12:01:13,351 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3TYMIFLCMY
2025-07-12 12:01:13,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27997.0, 'new_value': 28677.0}, {'field': 'total_amount', 'old_value': 27997.0, 'new_value': 28677.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-07-12 12:01:13,351 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM59
2025-07-12 12:01:13,835 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM59
2025-07-12 12:01:13,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251527.0, 'new_value': 264140.0}, {'field': 'total_amount', 'old_value': 251527.0, 'new_value': 264140.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 141}]
2025-07-12 12:01:13,835 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM51
2025-07-12 12:01:14,273 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM51
2025-07-12 12:01:14,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50295.11, 'new_value': 58668.58}, {'field': 'offline_amount', 'old_value': 52775.23, 'new_value': 59364.8}, {'field': 'total_amount', 'old_value': 103070.34, 'new_value': 118033.38}, {'field': 'order_count', 'old_value': 4198, 'new_value': 4863}]
2025-07-12 12:01:14,273 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMY8
2025-07-12 12:01:14,741 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMY8
2025-07-12 12:01:14,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9168.99, 'new_value': 11214.99}, {'field': 'offline_amount', 'old_value': 25515.0, 'new_value': 25814.0}, {'field': 'total_amount', 'old_value': 34683.99, 'new_value': 37028.99}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-07-12 12:01:14,741 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM61
2025-07-12 12:01:15,226 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM61
2025-07-12 12:01:15,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52495.0, 'new_value': 59888.0}, {'field': 'total_amount', 'old_value': 52495.0, 'new_value': 59888.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-07-12 12:01:15,226 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM71
2025-07-12 12:01:15,741 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM71
2025-07-12 12:01:15,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61369.15, 'new_value': 66908.24}, {'field': 'total_amount', 'old_value': 61369.15, 'new_value': 66908.24}, {'field': 'order_count', 'old_value': 1619, 'new_value': 1772}]
2025-07-12 12:01:15,741 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC1
2025-07-12 12:01:16,241 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMC1
2025-07-12 12:01:16,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3926.7, 'new_value': 4104.6}, {'field': 'offline_amount', 'old_value': 12873.0, 'new_value': 13551.0}, {'field': 'total_amount', 'old_value': 16799.7, 'new_value': 17655.6}, {'field': 'order_count', 'old_value': 46, 'new_value': 52}]
2025-07-12 12:01:16,241 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD1
2025-07-12 12:01:16,741 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMD1
2025-07-12 12:01:16,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209875.56, 'new_value': 247981.75}, {'field': 'total_amount', 'old_value': 209875.56, 'new_value': 247981.75}, {'field': 'order_count', 'old_value': 777, 'new_value': 904}]
2025-07-12 12:01:16,741 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF1
2025-07-12 12:01:17,304 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMF1
2025-07-12 12:01:17,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124932.36, 'new_value': 136018.72}, {'field': 'total_amount', 'old_value': 124932.36, 'new_value': 136018.72}, {'field': 'order_count', 'old_value': 2634, 'new_value': 2862}]
2025-07-12 12:01:17,304 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK1
2025-07-12 12:01:17,741 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMK1
2025-07-12 12:01:17,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49468.0, 'new_value': 54206.0}, {'field': 'total_amount', 'old_value': 49468.0, 'new_value': 54206.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-07-12 12:01:17,741 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN1
2025-07-12 12:01:18,210 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMN1
2025-07-12 12:01:18,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54340.0, 'new_value': 55060.0}, {'field': 'total_amount', 'old_value': 54340.0, 'new_value': 55060.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-07-12 12:01:18,210 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO1
2025-07-12 12:01:18,648 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO1
2025-07-12 12:01:18,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6971.0, 'new_value': 9308.0}, {'field': 'total_amount', 'old_value': 6971.0, 'new_value': 9308.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 29}]
2025-07-12 12:01:18,648 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY1
2025-07-12 12:01:19,101 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMY1
2025-07-12 12:01:19,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4019.61, 'new_value': 4352.49}, {'field': 'offline_amount', 'old_value': 16737.0, 'new_value': 17934.0}, {'field': 'total_amount', 'old_value': 20756.61, 'new_value': 22286.49}, {'field': 'order_count', 'old_value': 76, 'new_value': 83}]
2025-07-12 12:01:19,101 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM32
2025-07-12 12:01:19,585 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM32
2025-07-12 12:01:19,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36724.0, 'new_value': 40256.0}, {'field': 'total_amount', 'old_value': 36724.0, 'new_value': 40256.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 139}]
2025-07-12 12:01:19,601 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM82
2025-07-12 12:01:20,038 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM82
2025-07-12 12:01:20,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126031.8, 'new_value': 139910.52}, {'field': 'total_amount', 'old_value': 126031.8, 'new_value': 139910.52}, {'field': 'order_count', 'old_value': 328, 'new_value': 359}]
2025-07-12 12:01:20,038 - INFO - 开始更新记录 - 表单实例ID: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH
2025-07-12 12:01:20,585 - INFO - 更新表单数据成功: FINST-LR5668B1EERW8CG78I3XRAT8JIVU28EWXLLCMDH
2025-07-12 12:01:20,585 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61058.45, 'new_value': 67344.22}, {'field': 'offline_amount', 'old_value': 86689.0, 'new_value': 94689.0}, {'field': 'total_amount', 'old_value': 147747.45, 'new_value': 162033.22}, {'field': 'order_count', 'old_value': 486, 'new_value': 534}]
2025-07-12 12:01:20,585 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCML9
2025-07-12 12:01:21,101 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCML9
2025-07-12 12:01:21,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90022.94, 'new_value': 99083.82}, {'field': 'total_amount', 'old_value': 90022.94, 'new_value': 99083.82}, {'field': 'order_count', 'old_value': 328, 'new_value': 362}]
2025-07-12 12:01:21,101 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO2
2025-07-12 12:01:21,554 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMO2
2025-07-12 12:01:21,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14958.3, 'new_value': 16360.5}, {'field': 'offline_amount', 'old_value': 54523.0, 'new_value': 59751.0}, {'field': 'total_amount', 'old_value': 69481.3, 'new_value': 76111.5}, {'field': 'order_count', 'old_value': 626, 'new_value': 688}]
2025-07-12 12:01:21,554 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ2
2025-07-12 12:01:22,023 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMQ2
2025-07-12 12:01:22,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7724.0, 'new_value': 9168.0}, {'field': 'total_amount', 'old_value': 7724.0, 'new_value': 9168.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 86}]
2025-07-12 12:01:22,023 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR2
2025-07-12 12:01:22,507 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMR2
2025-07-12 12:01:22,507 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11904.0, 'new_value': 12223.0}, {'field': 'offline_amount', 'old_value': 241804.74, 'new_value': 282804.74}, {'field': 'total_amount', 'old_value': 253708.74, 'new_value': 295027.74}, {'field': 'order_count', 'old_value': 263, 'new_value': 287}]
2025-07-12 12:01:22,507 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMN9
2025-07-12 12:01:22,944 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMN9
2025-07-12 12:01:22,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4000000.0, 'new_value': 4400000.0}, {'field': 'total_amount', 'old_value': 4000000.0, 'new_value': 4400000.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-07-12 12:01:22,944 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT2
2025-07-12 12:01:23,366 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT2
2025-07-12 12:01:23,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20454.03, 'new_value': 22236.03}, {'field': 'total_amount', 'old_value': 20454.03, 'new_value': 22236.03}, {'field': 'order_count', 'old_value': 377, 'new_value': 410}]
2025-07-12 12:01:23,366 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW2
2025-07-12 12:01:23,804 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMW2
2025-07-12 12:01:23,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 297966.7, 'new_value': 327458.3}, {'field': 'total_amount', 'old_value': 297966.7, 'new_value': 327458.3}, {'field': 'order_count', 'old_value': 6328, 'new_value': 6945}]
2025-07-12 12:01:23,804 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM63
2025-07-12 12:01:24,257 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM63
2025-07-12 12:01:24,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25368.0, 'new_value': 28262.0}, {'field': 'offline_amount', 'old_value': 27662.27, 'new_value': 30579.5}, {'field': 'total_amount', 'old_value': 53030.27, 'new_value': 58841.5}, {'field': 'order_count', 'old_value': 2672, 'new_value': 2983}]
2025-07-12 12:01:24,257 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM73
2025-07-12 12:01:24,851 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM73
2025-07-12 12:01:24,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105069.0, 'new_value': 116301.0}, {'field': 'total_amount', 'old_value': 105069.0, 'new_value': 116301.0}, {'field': 'order_count', 'old_value': 11656, 'new_value': 12911}]
2025-07-12 12:01:24,851 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM83
2025-07-12 12:01:25,304 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCM83
2025-07-12 12:01:25,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68166.0, 'new_value': 73408.0}, {'field': 'total_amount', 'old_value': 68166.0, 'new_value': 73408.0}, {'field': 'order_count', 'old_value': 1286, 'new_value': 1393}]
2025-07-12 12:01:25,304 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMS9
2025-07-12 12:01:25,804 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMS9
2025-07-12 12:01:25,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'total_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 145}]
2025-07-12 12:01:25,804 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMT9
2025-07-12 12:01:26,273 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMT9
2025-07-12 12:01:26,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'total_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-07-12 12:01:26,273 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMU9
2025-07-12 12:01:26,788 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMU9
2025-07-12 12:01:26,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'total_amount', 'old_value': 60000.0, 'new_value': 65000.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-07-12 12:01:26,788 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMV9
2025-07-12 12:01:27,273 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMV9
2025-07-12 12:01:27,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600000.0, 'new_value': 650000.0}, {'field': 'total_amount', 'old_value': 600000.0, 'new_value': 650000.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 120}]
2025-07-12 12:01:27,273 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMW9
2025-07-12 12:01:27,757 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMW9
2025-07-12 12:01:27,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600000.0, 'new_value': 650000.0}, {'field': 'total_amount', 'old_value': 600000.0, 'new_value': 650000.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-07-12 12:01:27,757 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMX9
2025-07-12 12:01:28,226 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMX9
2025-07-12 12:01:28,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65235.04, 'new_value': 70604.42}, {'field': 'total_amount', 'old_value': 65235.04, 'new_value': 70604.42}, {'field': 'order_count', 'old_value': 4511, 'new_value': 4899}]
2025-07-12 12:01:28,226 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMY9
2025-07-12 12:01:28,726 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMY9
2025-07-12 12:01:28,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139608.18, 'new_value': 182470.66}, {'field': 'total_amount', 'old_value': 139608.18, 'new_value': 182470.66}, {'field': 'order_count', 'old_value': 1332, 'new_value': 1491}]
2025-07-12 12:01:28,726 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMZ9
2025-07-12 12:01:29,241 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCMZ9
2025-07-12 12:01:29,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 361241.0, 'new_value': 389955.0}, {'field': 'total_amount', 'old_value': 361241.0, 'new_value': 389955.0}, {'field': 'order_count', 'old_value': 8138, 'new_value': 8800}]
2025-07-12 12:01:29,241 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME3
2025-07-12 12:01:29,663 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCME3
2025-07-12 12:01:29,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59360.14, 'new_value': 66015.02}, {'field': 'total_amount', 'old_value': 59360.14, 'new_value': 66015.02}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-07-12 12:01:29,663 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU
2025-07-12 12:01:30,147 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU
2025-07-12 12:01:30,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23698.0, 'new_value': 25697.0}, {'field': 'total_amount', 'old_value': 23698.0, 'new_value': 25697.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-07-12 12:01:30,147 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM0A
2025-07-12 12:01:30,569 - INFO - 更新表单数据成功: FINST-7PF66CC125SW2OHWCVD49DMVT2VY28TT29LCM0A
2025-07-12 12:01:30,569 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42289.38, 'new_value': 51162.63}, {'field': 'total_amount', 'old_value': 89037.54, 'new_value': 97910.79}, {'field': 'order_count', 'old_value': 5796, 'new_value': 6382}]
2025-07-12 12:01:30,569 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW
2025-07-12 12:01:31,022 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW
2025-07-12 12:01:31,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20838.86, 'new_value': 25089.29}, {'field': 'total_amount', 'old_value': 41695.5, 'new_value': 45945.93}, {'field': 'order_count', 'old_value': 2770, 'new_value': 3048}]
2025-07-12 12:01:31,022 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX
2025-07-12 12:01:31,491 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX
2025-07-12 12:01:31,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420522.65, 'new_value': 460963.03}, {'field': 'total_amount', 'old_value': 420522.65, 'new_value': 460963.03}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1386}]
2025-07-12 12:01:31,491 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM11
2025-07-12 12:01:31,944 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM11
2025-07-12 12:01:31,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3600.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3600.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 12:01:31,944 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM21
2025-07-12 12:01:32,413 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM21
2025-07-12 12:01:32,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37924.1, 'new_value': 43389.1}, {'field': 'total_amount', 'old_value': 37924.1, 'new_value': 43389.1}, {'field': 'order_count', 'old_value': 276, 'new_value': 303}]
2025-07-12 12:01:32,413 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM31
2025-07-12 12:01:32,835 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM31
2025-07-12 12:01:32,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138891.54, 'new_value': 151122.56}, {'field': 'offline_amount', 'old_value': 121478.37, 'new_value': 127422.37}, {'field': 'total_amount', 'old_value': 260369.91, 'new_value': 278544.93}, {'field': 'order_count', 'old_value': 1781, 'new_value': 1936}]
2025-07-12 12:01:32,835 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM51
2025-07-12 12:01:33,304 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM51
2025-07-12 12:01:33,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116600.0, 'new_value': 126400.0}, {'field': 'total_amount', 'old_value': 116600.0, 'new_value': 126400.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 296}]
2025-07-12 12:01:33,304 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM91
2025-07-12 12:01:33,866 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM91
2025-07-12 12:01:33,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6490.59, 'new_value': 7726.71}, {'field': 'offline_amount', 'old_value': 4783.1, 'new_value': 5369.1}, {'field': 'total_amount', 'old_value': 11273.69, 'new_value': 13095.81}, {'field': 'order_count', 'old_value': 384, 'new_value': 423}]
2025-07-12 12:01:33,866 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA1
2025-07-12 12:01:34,319 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMA1
2025-07-12 12:01:34,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166779.15, 'new_value': 185056.49}, {'field': 'total_amount', 'old_value': 166779.15, 'new_value': 185056.49}, {'field': 'order_count', 'old_value': 659, 'new_value': 729}]
2025-07-12 12:01:34,319 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB1
2025-07-12 12:01:34,804 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMB1
2025-07-12 12:01:34,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81998.64, 'new_value': 90491.24}, {'field': 'total_amount', 'old_value': 81998.64, 'new_value': 90491.24}, {'field': 'order_count', 'old_value': 12856, 'new_value': 13536}]
2025-07-12 12:01:34,804 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC1
2025-07-12 12:01:35,272 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMC1
2025-07-12 12:01:35,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348627.0, 'new_value': 383986.0}, {'field': 'total_amount', 'old_value': 348627.0, 'new_value': 383986.0}, {'field': 'order_count', 'old_value': 1797, 'new_value': 1962}]
2025-07-12 12:01:35,272 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD1
2025-07-12 12:01:35,726 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMD1
2025-07-12 12:01:35,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237054.1, 'new_value': 264619.4}, {'field': 'total_amount', 'old_value': 237054.1, 'new_value': 264619.4}, {'field': 'order_count', 'old_value': 631, 'new_value': 710}]
2025-07-12 12:01:35,726 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCME1
2025-07-12 12:01:36,241 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCME1
2025-07-12 12:01:36,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139801.0, 'new_value': 153139.0}, {'field': 'offline_amount', 'old_value': 66122.0, 'new_value': 75547.0}, {'field': 'total_amount', 'old_value': 205923.0, 'new_value': 228686.0}, {'field': 'order_count', 'old_value': 278, 'new_value': 306}]
2025-07-12 12:01:36,241 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF1
2025-07-12 12:01:36,835 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF1
2025-07-12 12:01:36,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1381713.21, 'new_value': 1505715.46}, {'field': 'total_amount', 'old_value': 1381713.21, 'new_value': 1505715.46}, {'field': 'order_count', 'old_value': 2723, 'new_value': 2977}]
2025-07-12 12:01:36,835 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH1
2025-07-12 12:01:37,335 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMH1
2025-07-12 12:01:37,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94387.82, 'new_value': 109618.45}, {'field': 'total_amount', 'old_value': 94387.82, 'new_value': 109618.45}, {'field': 'order_count', 'old_value': 10410, 'new_value': 11689}]
2025-07-12 12:01:37,335 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI1
2025-07-12 12:01:37,913 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMI1
2025-07-12 12:01:37,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4192301.0, 'new_value': 4612725.0}, {'field': 'total_amount', 'old_value': 4192301.0, 'new_value': 4612725.0}, {'field': 'order_count', 'old_value': 18120, 'new_value': 20016}]
2025-07-12 12:01:37,913 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ1
2025-07-12 12:01:38,444 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMJ1
2025-07-12 12:01:38,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46731.08, 'new_value': 49509.32}, {'field': 'total_amount', 'old_value': 46731.08, 'new_value': 49509.32}, {'field': 'order_count', 'old_value': 266, 'new_value': 290}]
2025-07-12 12:01:38,444 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK1
2025-07-12 12:01:38,882 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMK1
2025-07-12 12:01:38,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36696.0, 'new_value': 39988.0}, {'field': 'total_amount', 'old_value': 36696.0, 'new_value': 39988.0}, {'field': 'order_count', 'old_value': 549, 'new_value': 602}]
2025-07-12 12:01:38,882 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML1
2025-07-12 12:01:39,366 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML1
2025-07-12 12:01:39,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115306.88, 'new_value': 127905.88}, {'field': 'total_amount', 'old_value': 115306.88, 'new_value': 127905.88}, {'field': 'order_count', 'old_value': 2064, 'new_value': 2316}]
2025-07-12 12:01:39,366 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN1
2025-07-12 12:01:39,882 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMN1
2025-07-12 12:01:39,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1815781.33, 'new_value': 2001631.33}, {'field': 'total_amount', 'old_value': 1815781.33, 'new_value': 2001631.33}, {'field': 'order_count', 'old_value': 40894, 'new_value': 44605}]
2025-07-12 12:01:39,897 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMQ4
2025-07-12 12:01:40,304 - INFO - 更新表单数据成功: FINST-2PF66TC19QRWCKU1A5FRQ9OBKYC03G6O85MCMQ4
2025-07-12 12:01:40,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6900.0, 'new_value': 7500.0}, {'field': 'total_amount', 'old_value': 6900.0, 'new_value': 7500.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-07-12 12:01:40,304 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR1
2025-07-12 12:01:40,741 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR1
2025-07-12 12:01:40,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332329.85, 'new_value': 364348.19}, {'field': 'total_amount', 'old_value': 332329.85, 'new_value': 364348.19}, {'field': 'order_count', 'old_value': 2867, 'new_value': 3151}]
2025-07-12 12:01:40,741 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT1
2025-07-12 12:01:41,194 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMT1
2025-07-12 12:01:41,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5030938.0, 'new_value': 5659805.25}, {'field': 'total_amount', 'old_value': 5030938.0, 'new_value': 5659805.25}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-07-12 12:01:41,194 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU1
2025-07-12 12:01:41,632 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU1
2025-07-12 12:01:41,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71934.0, 'new_value': 77934.0}, {'field': 'total_amount', 'old_value': 71934.0, 'new_value': 77934.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 230}]
2025-07-12 12:01:41,647 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV1
2025-07-12 12:01:42,210 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV1
2025-07-12 12:01:42,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62525.4, 'new_value': 68777.4}, {'field': 'total_amount', 'old_value': 62525.4, 'new_value': 68777.4}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-07-12 12:01:42,210 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW1
2025-07-12 12:01:42,647 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW1
2025-07-12 12:01:42,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14104.0, 'new_value': 16404.0}, {'field': 'total_amount', 'old_value': 14104.0, 'new_value': 16404.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-07-12 12:01:42,647 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM12
2025-07-12 12:01:43,179 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM12
2025-07-12 12:01:43,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54032.89, 'new_value': 59385.39}, {'field': 'total_amount', 'old_value': 54032.89, 'new_value': 59385.39}, {'field': 'order_count', 'old_value': 1431, 'new_value': 1556}]
2025-07-12 12:01:43,179 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM42
2025-07-12 12:01:43,663 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCM42
2025-07-12 12:01:43,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43371.29, 'new_value': 53320.53}, {'field': 'total_amount', 'old_value': 85761.86, 'new_value': 95711.1}, {'field': 'order_count', 'old_value': 1398, 'new_value': 1550}]
2025-07-12 12:01:43,663 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF2
2025-07-12 12:01:44,241 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMF2
2025-07-12 12:01:44,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41610.0, 'new_value': 45771.0}, {'field': 'total_amount', 'old_value': 41610.0, 'new_value': 45771.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-07-12 12:01:44,241 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG2
2025-07-12 12:01:44,694 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMG2
2025-07-12 12:01:44,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24190.8, 'new_value': 26830.3}, {'field': 'total_amount', 'old_value': 24190.8, 'new_value': 26830.3}, {'field': 'order_count', 'old_value': 100, 'new_value': 110}]
2025-07-12 12:01:44,694 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML2
2025-07-12 12:01:45,210 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML2
2025-07-12 12:01:45,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126425.0, 'new_value': 144270.0}, {'field': 'total_amount', 'old_value': 126425.0, 'new_value': 144270.0}, {'field': 'order_count', 'old_value': 3503, 'new_value': 3657}]
2025-07-12 12:01:45,210 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2
2025-07-12 12:01:45,647 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMM2
2025-07-12 12:01:45,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22931.64, 'new_value': 25812.34}, {'field': 'total_amount', 'old_value': 22931.64, 'new_value': 25812.34}, {'field': 'order_count', 'old_value': 2380, 'new_value': 2713}]
2025-07-12 12:01:45,647 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP2
2025-07-12 12:01:46,132 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMP2
2025-07-12 12:01:46,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 676100.0, 'new_value': 728100.0}, {'field': 'total_amount', 'old_value': 676100.0, 'new_value': 728100.0}, {'field': 'order_count', 'old_value': 961, 'new_value': 1028}]
2025-07-12 12:01:46,132 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR2
2025-07-12 12:01:46,632 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMR2
2025-07-12 12:01:46,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24928.0, 'new_value': 30928.0}, {'field': 'total_amount', 'old_value': 24928.0, 'new_value': 30928.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-07-12 12:01:46,632 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS2
2025-07-12 12:01:47,085 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMS2
2025-07-12 12:01:47,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3395.0, 'new_value': 4094.0}, {'field': 'total_amount', 'old_value': 3395.0, 'new_value': 4094.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-07-12 12:01:47,085 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM03
2025-07-12 12:01:47,538 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM03
2025-07-12 12:01:47,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131583.0, 'new_value': 143377.0}, {'field': 'total_amount', 'old_value': 131583.0, 'new_value': 143377.0}, {'field': 'order_count', 'old_value': 3077, 'new_value': 3345}]
2025-07-12 12:01:47,538 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM13
2025-07-12 12:01:48,022 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM13
2025-07-12 12:01:48,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395001.0, 'new_value': 445001.0}, {'field': 'total_amount', 'old_value': 395001.0, 'new_value': 445001.0}, {'field': 'order_count', 'old_value': 35073, 'new_value': 35078}]
2025-07-12 12:01:48,022 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM23
2025-07-12 12:01:48,507 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM23
2025-07-12 12:01:48,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2399.0, 'new_value': 4398.0}, {'field': 'total_amount', 'old_value': 2399.0, 'new_value': 4398.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-07-12 12:01:48,507 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9
2025-07-12 12:01:48,960 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMV9
2025-07-12 12:01:48,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74535.0, 'new_value': 98130.0}, {'field': 'total_amount', 'old_value': 139184.63, 'new_value': 162779.63}, {'field': 'order_count', 'old_value': 384, 'new_value': 425}]
2025-07-12 12:01:48,960 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMW9
2025-07-12 12:01:49,397 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMW9
2025-07-12 12:01:49,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35016.0, 'new_value': 37950.0}, {'field': 'total_amount', 'old_value': 35016.0, 'new_value': 37950.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-07-12 12:01:49,397 - INFO - 日期 2025-07 处理完成 - 更新: 139 条，插入: 0 条，错误: 0 条
2025-07-12 12:01:49,397 - INFO - 数据同步完成！更新: 139 条，插入: 0 条，错误: 0 条
2025-07-12 12:01:49,413 - INFO - =================同步完成====================
2025-07-12 15:00:03,170 - INFO - =================使用默认全量同步=============
2025-07-12 15:00:05,138 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 15:00:05,138 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 15:00:05,170 - INFO - 开始处理日期: 2025-01
2025-07-12 15:00:05,185 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:05,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:05,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:06,420 - INFO - Response - Page 1:
2025-07-12 15:00:06,623 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:06,623 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:06,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:06,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:07,919 - INFO - Response - Page 2:
2025-07-12 15:00:08,123 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:08,123 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:08,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:08,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:08,701 - INFO - Response - Page 3:
2025-07-12 15:00:08,919 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:08,919 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:08,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:08,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:09,482 - INFO - Response - Page 4:
2025-07-12 15:00:09,701 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:09,701 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:09,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:09,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:10,388 - INFO - Response - Page 5:
2025-07-12 15:00:10,591 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:10,591 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:10,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:10,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:11,154 - INFO - Response - Page 6:
2025-07-12 15:00:11,357 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:11,357 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:11,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:11,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:11,919 - INFO - Response - Page 7:
2025-07-12 15:00:12,123 - INFO - 第 7 页获取到 82 条记录
2025-07-12 15:00:12,123 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 15:00:12,123 - INFO - 获取到 682 条表单数据
2025-07-12 15:00:12,123 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 15:00:12,138 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:12,138 - INFO - 开始处理日期: 2025-02
2025-07-12 15:00:12,138 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:12,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:12,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:12,701 - INFO - Response - Page 1:
2025-07-12 15:00:12,904 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:12,904 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:12,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:12,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:13,451 - INFO - Response - Page 2:
2025-07-12 15:00:13,654 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:13,654 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:13,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:13,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:14,294 - INFO - Response - Page 3:
2025-07-12 15:00:14,498 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:14,498 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:14,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:14,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:15,107 - INFO - Response - Page 4:
2025-07-12 15:00:15,310 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:15,310 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:15,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:15,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:15,841 - INFO - Response - Page 5:
2025-07-12 15:00:16,044 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:16,044 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:16,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:16,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:16,576 - INFO - Response - Page 6:
2025-07-12 15:00:16,779 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:16,779 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:16,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:16,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:17,279 - INFO - Response - Page 7:
2025-07-12 15:00:17,482 - INFO - 第 7 页获取到 70 条记录
2025-07-12 15:00:17,482 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 15:00:17,482 - INFO - 获取到 670 条表单数据
2025-07-12 15:00:17,482 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 15:00:17,498 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:17,498 - INFO - 开始处理日期: 2025-03
2025-07-12 15:00:17,498 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:17,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:17,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:17,966 - INFO - Response - Page 1:
2025-07-12 15:00:18,169 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:18,169 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:18,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:18,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:18,732 - INFO - Response - Page 2:
2025-07-12 15:00:18,935 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:18,935 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:18,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:18,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:19,466 - INFO - Response - Page 3:
2025-07-12 15:00:19,669 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:19,669 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:19,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:19,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:20,154 - INFO - Response - Page 4:
2025-07-12 15:00:20,357 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:20,357 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:20,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:20,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:20,888 - INFO - Response - Page 5:
2025-07-12 15:00:21,091 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:21,091 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:21,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:21,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:21,638 - INFO - Response - Page 6:
2025-07-12 15:00:21,841 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:21,841 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:21,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:21,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:22,341 - INFO - Response - Page 7:
2025-07-12 15:00:22,544 - INFO - 第 7 页获取到 61 条记录
2025-07-12 15:00:22,544 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 15:00:22,544 - INFO - 获取到 661 条表单数据
2025-07-12 15:00:22,544 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 15:00:22,560 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:22,560 - INFO - 开始处理日期: 2025-04
2025-07-12 15:00:22,560 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:22,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:22,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:23,060 - INFO - Response - Page 1:
2025-07-12 15:00:23,263 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:23,263 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:23,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:23,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:23,826 - INFO - Response - Page 2:
2025-07-12 15:00:24,029 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:24,029 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:24,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:24,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:24,576 - INFO - Response - Page 3:
2025-07-12 15:00:24,779 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:24,779 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:24,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:24,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:25,263 - INFO - Response - Page 4:
2025-07-12 15:00:25,466 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:25,466 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:25,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:25,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:25,966 - INFO - Response - Page 5:
2025-07-12 15:00:26,169 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:26,169 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:26,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:26,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:26,685 - INFO - Response - Page 6:
2025-07-12 15:00:26,888 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:26,888 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:26,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:26,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:27,326 - INFO - Response - Page 7:
2025-07-12 15:00:27,529 - INFO - 第 7 页获取到 56 条记录
2025-07-12 15:00:27,529 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 15:00:27,529 - INFO - 获取到 656 条表单数据
2025-07-12 15:00:27,529 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 15:00:27,544 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:27,544 - INFO - 开始处理日期: 2025-05
2025-07-12 15:00:27,544 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:27,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:27,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:28,044 - INFO - Response - Page 1:
2025-07-12 15:00:28,247 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:28,247 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:28,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:28,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:28,747 - INFO - Response - Page 2:
2025-07-12 15:00:28,951 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:28,951 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:28,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:28,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:29,404 - INFO - Response - Page 3:
2025-07-12 15:00:29,607 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:29,607 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:29,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:29,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:30,185 - INFO - Response - Page 4:
2025-07-12 15:00:30,388 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:30,388 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:30,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:30,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:30,951 - INFO - Response - Page 5:
2025-07-12 15:00:31,154 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:31,154 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:31,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:31,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:31,654 - INFO - Response - Page 6:
2025-07-12 15:00:31,857 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:31,857 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:31,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:31,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:32,310 - INFO - Response - Page 7:
2025-07-12 15:00:32,513 - INFO - 第 7 页获取到 65 条记录
2025-07-12 15:00:32,513 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 15:00:32,513 - INFO - 获取到 665 条表单数据
2025-07-12 15:00:32,513 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 15:00:32,529 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:32,529 - INFO - 开始处理日期: 2025-06
2025-07-12 15:00:32,529 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:32,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:32,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:33,044 - INFO - Response - Page 1:
2025-07-12 15:00:33,247 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:33,247 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:33,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:33,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:33,779 - INFO - Response - Page 2:
2025-07-12 15:00:33,982 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:33,982 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:33,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:33,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:34,513 - INFO - Response - Page 3:
2025-07-12 15:00:34,716 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:34,716 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:34,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:34,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:35,201 - INFO - Response - Page 4:
2025-07-12 15:00:35,404 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:35,404 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:35,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:35,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:35,872 - INFO - Response - Page 5:
2025-07-12 15:00:36,076 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:36,076 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:36,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:36,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:36,607 - INFO - Response - Page 6:
2025-07-12 15:00:36,810 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:36,810 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:36,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:36,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:37,263 - INFO - Response - Page 7:
2025-07-12 15:00:37,466 - INFO - 第 7 页获取到 33 条记录
2025-07-12 15:00:37,466 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 15:00:37,466 - INFO - 获取到 633 条表单数据
2025-07-12 15:00:37,466 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 15:00:37,482 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:37,482 - INFO - 开始处理日期: 2025-07
2025-07-12 15:00:37,482 - INFO - Request Parameters - Page 1:
2025-07-12 15:00:37,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:37,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:37,951 - INFO - Response - Page 1:
2025-07-12 15:00:38,154 - INFO - 第 1 页获取到 100 条记录
2025-07-12 15:00:38,154 - INFO - Request Parameters - Page 2:
2025-07-12 15:00:38,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:38,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:38,685 - INFO - Response - Page 2:
2025-07-12 15:00:38,888 - INFO - 第 2 页获取到 100 条记录
2025-07-12 15:00:38,888 - INFO - Request Parameters - Page 3:
2025-07-12 15:00:38,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:38,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:39,419 - INFO - Response - Page 3:
2025-07-12 15:00:39,622 - INFO - 第 3 页获取到 100 条记录
2025-07-12 15:00:39,622 - INFO - Request Parameters - Page 4:
2025-07-12 15:00:39,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:39,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:40,247 - INFO - Response - Page 4:
2025-07-12 15:00:40,451 - INFO - 第 4 页获取到 100 条记录
2025-07-12 15:00:40,451 - INFO - Request Parameters - Page 5:
2025-07-12 15:00:40,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:40,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:40,951 - INFO - Response - Page 5:
2025-07-12 15:00:41,154 - INFO - 第 5 页获取到 100 条记录
2025-07-12 15:00:41,154 - INFO - Request Parameters - Page 6:
2025-07-12 15:00:41,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:41,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:41,826 - INFO - Response - Page 6:
2025-07-12 15:00:42,029 - INFO - 第 6 页获取到 100 条记录
2025-07-12 15:00:42,029 - INFO - Request Parameters - Page 7:
2025-07-12 15:00:42,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 15:00:42,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 15:00:42,326 - INFO - Response - Page 7:
2025-07-12 15:00:42,529 - INFO - 第 7 页获取到 9 条记录
2025-07-12 15:00:42,529 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 15:00:42,529 - INFO - 获取到 609 条表单数据
2025-07-12 15:00:42,529 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 15:00:42,544 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ
2025-07-12 15:00:42,982 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ
2025-07-12 15:00:42,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143535.0, 'new_value': 158260.0}, {'field': 'total_amount', 'old_value': 143535.0, 'new_value': 158260.0}, {'field': 'order_count', 'old_value': 3110, 'new_value': 3460}]
2025-07-12 15:00:42,982 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO2
2025-07-12 15:00:43,419 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMO2
2025-07-12 15:00:43,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3149.0, 'new_value': 4149.0}, {'field': 'total_amount', 'old_value': 3149.0, 'new_value': 4149.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-07-12 15:00:43,419 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX2
2025-07-12 15:00:43,872 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMX2
2025-07-12 15:00:43,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2990.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2990.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 15:00:43,872 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM33
2025-07-12 15:00:44,357 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM33
2025-07-12 15:00:44,357 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21480.0, 'new_value': 23870.0}, {'field': 'total_amount', 'old_value': 21480.0, 'new_value': 23870.0}, {'field': 'order_count', 'old_value': 1989, 'new_value': 2200}]
2025-07-12 15:00:44,357 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM63
2025-07-12 15:00:44,966 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM63
2025-07-12 15:00:44,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34780.2, 'new_value': 38081.72}, {'field': 'total_amount', 'old_value': 34780.2, 'new_value': 38081.72}, {'field': 'order_count', 'old_value': 1499, 'new_value': 1645}]
2025-07-12 15:00:44,966 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM83
2025-07-12 15:00:45,419 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM83
2025-07-12 15:00:45,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11498.18, 'new_value': 13691.26}, {'field': 'total_amount', 'old_value': 11498.18, 'new_value': 13691.26}, {'field': 'order_count', 'old_value': 457, 'new_value': 549}]
2025-07-12 15:00:45,419 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMD3
2025-07-12 15:00:45,982 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMD3
2025-07-12 15:00:45,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35714.0, 'new_value': 36014.0}, {'field': 'total_amount', 'old_value': 35714.0, 'new_value': 36014.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-07-12 15:00:45,982 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMH9
2025-07-12 15:00:46,435 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMH9
2025-07-12 15:00:46,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134060.0, 'new_value': 157060.0}, {'field': 'total_amount', 'old_value': 134060.0, 'new_value': 157060.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-07-12 15:00:46,435 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMQ9
2025-07-12 15:00:46,951 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMQ9
2025-07-12 15:00:46,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5590.0, 'new_value': 6137.0}, {'field': 'total_amount', 'old_value': 5590.0, 'new_value': 6137.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 56}]
2025-07-12 15:00:46,951 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9
2025-07-12 15:00:47,372 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMU9
2025-07-12 15:00:47,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110761.55, 'new_value': 123513.75}, {'field': 'total_amount', 'old_value': 110761.55, 'new_value': 123513.75}, {'field': 'order_count', 'old_value': 2765, 'new_value': 3071}]
2025-07-12 15:00:47,388 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81FQRWK4E3C4Q5S40RSKU92PJP1VMCM65
2025-07-12 15:00:47,857 - INFO - 更新表单数据成功: FINST-NU966I81FQRWK4E3C4Q5S40RSKU92PJP1VMCM65
2025-07-12 15:00:47,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71986.0, 'new_value': 78782.0}, {'field': 'total_amount', 'old_value': 71986.0, 'new_value': 78782.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-07-12 15:00:47,857 - INFO - 日期 2025-07 处理完成 - 更新: 11 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:47,857 - INFO - 数据同步完成！更新: 11 条，插入: 0 条，错误: 0 条
2025-07-12 15:00:47,872 - INFO - =================同步完成====================
2025-07-12 18:00:02,567 - INFO - =================使用默认全量同步=============
2025-07-12 18:00:04,473 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 18:00:04,473 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 18:00:04,520 - INFO - 开始处理日期: 2025-01
2025-07-12 18:00:04,520 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:04,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:04,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:06,223 - INFO - Response - Page 1:
2025-07-12 18:00:06,426 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:06,426 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:06,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:06,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:06,942 - INFO - Response - Page 2:
2025-07-12 18:00:07,145 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:07,145 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:07,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:07,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:07,660 - INFO - Response - Page 3:
2025-07-12 18:00:07,863 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:07,863 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:07,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:07,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:08,348 - INFO - Response - Page 4:
2025-07-12 18:00:08,551 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:08,551 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:08,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:08,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:09,098 - INFO - Response - Page 5:
2025-07-12 18:00:09,301 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:09,301 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:09,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:09,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:09,801 - INFO - Response - Page 6:
2025-07-12 18:00:10,004 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:10,004 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:10,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:10,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:10,504 - INFO - Response - Page 7:
2025-07-12 18:00:10,707 - INFO - 第 7 页获取到 82 条记录
2025-07-12 18:00:10,707 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 18:00:10,707 - INFO - 获取到 682 条表单数据
2025-07-12 18:00:10,707 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 18:00:10,723 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:10,723 - INFO - 开始处理日期: 2025-02
2025-07-12 18:00:10,723 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:10,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:10,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:11,254 - INFO - Response - Page 1:
2025-07-12 18:00:11,457 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:11,457 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:11,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:11,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:11,973 - INFO - Response - Page 2:
2025-07-12 18:00:12,176 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:12,176 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:12,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:12,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:12,613 - INFO - Response - Page 3:
2025-07-12 18:00:12,817 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:12,817 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:12,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:12,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:13,363 - INFO - Response - Page 4:
2025-07-12 18:00:13,567 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:13,567 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:13,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:13,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:14,129 - INFO - Response - Page 5:
2025-07-12 18:00:14,332 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:14,332 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:14,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:14,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:14,942 - INFO - Response - Page 6:
2025-07-12 18:00:15,145 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:15,145 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:15,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:15,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:15,629 - INFO - Response - Page 7:
2025-07-12 18:00:15,832 - INFO - 第 7 页获取到 70 条记录
2025-07-12 18:00:15,832 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 18:00:15,832 - INFO - 获取到 670 条表单数据
2025-07-12 18:00:15,832 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 18:00:15,848 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:15,848 - INFO - 开始处理日期: 2025-03
2025-07-12 18:00:15,848 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:15,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:15,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:16,551 - INFO - Response - Page 1:
2025-07-12 18:00:16,754 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:16,754 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:16,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:16,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:17,191 - INFO - Response - Page 2:
2025-07-12 18:00:17,395 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:17,395 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:17,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:17,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:17,957 - INFO - Response - Page 3:
2025-07-12 18:00:18,160 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:18,160 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:18,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:18,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:18,660 - INFO - Response - Page 4:
2025-07-12 18:00:18,863 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:18,863 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:18,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:18,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:19,395 - INFO - Response - Page 5:
2025-07-12 18:00:19,598 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:19,598 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:19,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:19,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:20,145 - INFO - Response - Page 6:
2025-07-12 18:00:20,348 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:20,348 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:20,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:20,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:20,816 - INFO - Response - Page 7:
2025-07-12 18:00:21,020 - INFO - 第 7 页获取到 61 条记录
2025-07-12 18:00:21,020 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 18:00:21,020 - INFO - 获取到 661 条表单数据
2025-07-12 18:00:21,020 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 18:00:21,035 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:21,035 - INFO - 开始处理日期: 2025-04
2025-07-12 18:00:21,035 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:21,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:21,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:21,504 - INFO - Response - Page 1:
2025-07-12 18:00:21,707 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:21,707 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:21,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:21,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:22,223 - INFO - Response - Page 2:
2025-07-12 18:00:22,426 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:22,426 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:22,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:22,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:22,895 - INFO - Response - Page 3:
2025-07-12 18:00:23,098 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:23,098 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:23,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:23,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:23,598 - INFO - Response - Page 4:
2025-07-12 18:00:23,801 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:23,801 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:23,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:23,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:24,332 - INFO - Response - Page 5:
2025-07-12 18:00:24,535 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:24,535 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:24,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:24,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:25,113 - INFO - Response - Page 6:
2025-07-12 18:00:25,316 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:25,316 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:25,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:25,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:25,754 - INFO - Response - Page 7:
2025-07-12 18:00:25,957 - INFO - 第 7 页获取到 56 条记录
2025-07-12 18:00:25,957 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 18:00:25,957 - INFO - 获取到 656 条表单数据
2025-07-12 18:00:25,957 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 18:00:25,973 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:25,973 - INFO - 开始处理日期: 2025-05
2025-07-12 18:00:25,973 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:25,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:25,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:26,535 - INFO - Response - Page 1:
2025-07-12 18:00:26,738 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:26,738 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:26,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:26,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:27,238 - INFO - Response - Page 2:
2025-07-12 18:00:27,441 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:27,441 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:27,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:27,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:27,910 - INFO - Response - Page 3:
2025-07-12 18:00:28,113 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:28,113 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:28,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:28,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:28,582 - INFO - Response - Page 4:
2025-07-12 18:00:28,785 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:28,785 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:28,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:28,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:29,363 - INFO - Response - Page 5:
2025-07-12 18:00:29,566 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:29,566 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:29,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:29,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:30,035 - INFO - Response - Page 6:
2025-07-12 18:00:30,238 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:30,238 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:30,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:30,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:30,691 - INFO - Response - Page 7:
2025-07-12 18:00:30,895 - INFO - 第 7 页获取到 65 条记录
2025-07-12 18:00:30,895 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 18:00:30,895 - INFO - 获取到 665 条表单数据
2025-07-12 18:00:30,895 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 18:00:30,910 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:30,910 - INFO - 开始处理日期: 2025-06
2025-07-12 18:00:30,910 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:30,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:30,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:31,457 - INFO - Response - Page 1:
2025-07-12 18:00:31,660 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:31,660 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:31,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:31,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:32,191 - INFO - Response - Page 2:
2025-07-12 18:00:32,395 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:32,395 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:32,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:32,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:32,879 - INFO - Response - Page 3:
2025-07-12 18:00:33,082 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:33,082 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:33,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:33,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:33,629 - INFO - Response - Page 4:
2025-07-12 18:00:33,832 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:33,832 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:33,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:33,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:34,316 - INFO - Response - Page 5:
2025-07-12 18:00:34,520 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:34,520 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:34,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:34,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:35,082 - INFO - Response - Page 6:
2025-07-12 18:00:35,301 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:35,301 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:35,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:35,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:35,723 - INFO - Response - Page 7:
2025-07-12 18:00:35,926 - INFO - 第 7 页获取到 33 条记录
2025-07-12 18:00:35,926 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 18:00:35,926 - INFO - 获取到 633 条表单数据
2025-07-12 18:00:35,926 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 18:00:35,941 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:35,941 - INFO - 开始处理日期: 2025-07
2025-07-12 18:00:35,941 - INFO - Request Parameters - Page 1:
2025-07-12 18:00:35,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:35,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:36,520 - INFO - Response - Page 1:
2025-07-12 18:00:36,723 - INFO - 第 1 页获取到 100 条记录
2025-07-12 18:00:36,723 - INFO - Request Parameters - Page 2:
2025-07-12 18:00:36,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:36,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:37,191 - INFO - Response - Page 2:
2025-07-12 18:00:37,395 - INFO - 第 2 页获取到 100 条记录
2025-07-12 18:00:37,395 - INFO - Request Parameters - Page 3:
2025-07-12 18:00:37,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:37,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:37,941 - INFO - Response - Page 3:
2025-07-12 18:00:38,144 - INFO - 第 3 页获取到 100 条记录
2025-07-12 18:00:38,144 - INFO - Request Parameters - Page 4:
2025-07-12 18:00:38,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:38,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:38,660 - INFO - Response - Page 4:
2025-07-12 18:00:38,863 - INFO - 第 4 页获取到 100 条记录
2025-07-12 18:00:38,863 - INFO - Request Parameters - Page 5:
2025-07-12 18:00:38,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:38,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:39,394 - INFO - Response - Page 5:
2025-07-12 18:00:39,598 - INFO - 第 5 页获取到 100 条记录
2025-07-12 18:00:39,598 - INFO - Request Parameters - Page 6:
2025-07-12 18:00:39,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:39,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:40,066 - INFO - Response - Page 6:
2025-07-12 18:00:40,269 - INFO - 第 6 页获取到 100 条记录
2025-07-12 18:00:40,269 - INFO - Request Parameters - Page 7:
2025-07-12 18:00:40,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 18:00:40,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 18:00:40,551 - INFO - Response - Page 7:
2025-07-12 18:00:40,754 - INFO - 第 7 页获取到 9 条记录
2025-07-12 18:00:40,754 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 18:00:40,754 - INFO - 获取到 609 条表单数据
2025-07-12 18:00:40,754 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 18:00:40,754 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1RUZWJFZX9BHEO5TEIR7T2TRZOFVCME4
2025-07-12 18:00:41,191 - INFO - 更新表单数据成功: FINST-90E66JD1RUZWJFZX9BHEO5TEIR7T2TRZOFVCME4
2025-07-12 18:00:41,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1676.9, 'new_value': 1864.9}, {'field': 'total_amount', 'old_value': 1677.9, 'new_value': 1865.9}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-07-12 18:00:41,191 - INFO - 开始更新记录 - 表单实例ID: FINST-90E66JD1RUZWJFZX9BHEO5TEIR7T2TRZOFVCMI4
2025-07-12 18:00:41,676 - INFO - 更新表单数据成功: FINST-90E66JD1RUZWJFZX9BHEO5TEIR7T2TRZOFVCMI4
2025-07-12 18:00:41,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31455.05, 'new_value': 35766.6}, {'field': 'total_amount', 'old_value': 39649.05, 'new_value': 43960.6}, {'field': 'order_count', 'old_value': 929, 'new_value': 1049}]
2025-07-12 18:00:41,676 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML2
2025-07-12 18:00:42,238 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCML2
2025-07-12 18:00:42,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144270.0, 'new_value': 138725.0}, {'field': 'total_amount', 'old_value': 144270.0, 'new_value': 138725.0}, {'field': 'order_count', 'old_value': 3657, 'new_value': 3913}]
2025-07-12 18:00:42,238 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ2
2025-07-12 18:00:42,801 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMQ2
2025-07-12 18:00:42,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89127.08, 'new_value': 109005.08}, {'field': 'total_amount', 'old_value': 89127.08, 'new_value': 109005.08}, {'field': 'order_count', 'old_value': 710, 'new_value': 766}]
2025-07-12 18:00:42,801 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU2
2025-07-12 18:00:43,269 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMU2
2025-07-12 18:00:43,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43613.0, 'new_value': 47433.0}, {'field': 'total_amount', 'old_value': 43613.0, 'new_value': 47433.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 285}]
2025-07-12 18:00:43,269 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV2
2025-07-12 18:00:43,707 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMV2
2025-07-12 18:00:43,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32357.0, 'new_value': 37857.0}, {'field': 'total_amount', 'old_value': 32357.0, 'new_value': 37857.0}, {'field': 'order_count', 'old_value': 3989, 'new_value': 4049}]
2025-07-12 18:00:43,707 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW2
2025-07-12 18:00:44,160 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMW2
2025-07-12 18:00:44,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5078.0, 'new_value': 5777.0}, {'field': 'total_amount', 'old_value': 5078.0, 'new_value': 5777.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-07-12 18:00:44,160 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY2
2025-07-12 18:00:44,582 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMY2
2025-07-12 18:00:44,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19320.0, 'new_value': 22200.0}, {'field': 'total_amount', 'old_value': 19320.0, 'new_value': 22200.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-07-12 18:00:44,582 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ2
2025-07-12 18:00:45,098 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3OHPIFLCMZ2
2025-07-12 18:00:45,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91288.0, 'new_value': 96288.0}, {'field': 'total_amount', 'old_value': 91288.0, 'new_value': 96288.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-07-12 18:00:45,098 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM53
2025-07-12 18:00:45,535 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCM53
2025-07-12 18:00:45,535 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1498.0, 'new_value': 2696.0}, {'field': 'total_amount', 'old_value': 1498.0, 'new_value': 2696.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-07-12 18:00:45,535 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMA3
2025-07-12 18:00:45,957 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCMA3
2025-07-12 18:00:45,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4375.0, 'new_value': 4830.0}, {'field': 'total_amount', 'old_value': 4375.0, 'new_value': 4830.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 145}]
2025-07-12 18:00:45,957 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCME3
2025-07-12 18:00:46,410 - INFO - 更新表单数据成功: FINST-7PF66CC14URWNHR48M3KY8JFI6NS3PHPIFLCME3
2025-07-12 18:00:46,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30150.0, 'new_value': 42170.0}, {'field': 'total_amount', 'old_value': 30150.0, 'new_value': 42170.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 14}]
2025-07-12 18:00:46,410 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMF9
2025-07-12 18:00:46,848 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMF9
2025-07-12 18:00:46,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49000.0, 'new_value': 50200.0}, {'field': 'total_amount', 'old_value': 49000.0, 'new_value': 50200.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-07-12 18:00:46,848 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMM9
2025-07-12 18:00:47,348 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMM9
2025-07-12 18:00:47,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75623.0, 'new_value': 89467.0}, {'field': 'total_amount', 'old_value': 75623.0, 'new_value': 89467.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 35}]
2025-07-12 18:00:47,348 - INFO - 日期 2025-07 处理完成 - 更新: 14 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:47,348 - INFO - 数据同步完成！更新: 14 条，插入: 0 条，错误: 0 条
2025-07-12 18:00:47,348 - INFO - =================同步完成====================
2025-07-12 21:00:03,695 - INFO - =================使用默认全量同步=============
2025-07-12 21:00:05,648 - INFO - MySQL查询成功，共获取 4576 条记录
2025-07-12 21:00:05,648 - INFO - 获取到 7 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07']
2025-07-12 21:00:05,680 - INFO - 开始处理日期: 2025-01
2025-07-12 21:00:05,680 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:05,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:05,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:06,914 - INFO - Response - Page 1:
2025-07-12 21:00:07,118 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:07,118 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:07,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:07,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:07,649 - INFO - Response - Page 2:
2025-07-12 21:00:07,852 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:07,852 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:07,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:07,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:08,384 - INFO - Response - Page 3:
2025-07-12 21:00:08,587 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:08,587 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:08,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:08,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:09,150 - INFO - Response - Page 4:
2025-07-12 21:00:09,353 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:09,353 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:09,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:09,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:10,557 - INFO - Response - Page 5:
2025-07-12 21:00:10,760 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:10,760 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:10,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:10,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:11,276 - INFO - Response - Page 6:
2025-07-12 21:00:11,479 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:11,479 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:11,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:11,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:12,010 - INFO - Response - Page 7:
2025-07-12 21:00:12,213 - INFO - 第 7 页获取到 82 条记录
2025-07-12 21:00:12,213 - INFO - 查询完成，共获取到 682 条记录
2025-07-12 21:00:12,213 - INFO - 获取到 682 条表单数据
2025-07-12 21:00:12,213 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-07-12 21:00:12,229 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:12,229 - INFO - 开始处理日期: 2025-02
2025-07-12 21:00:12,229 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:12,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:12,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:12,729 - INFO - Response - Page 1:
2025-07-12 21:00:12,933 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:12,933 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:12,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:12,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:13,527 - INFO - Response - Page 2:
2025-07-12 21:00:13,730 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:13,730 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:13,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:13,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:14,386 - INFO - Response - Page 3:
2025-07-12 21:00:14,589 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:14,589 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:14,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:14,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:15,199 - INFO - Response - Page 4:
2025-07-12 21:00:15,402 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:15,402 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:15,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:15,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:15,949 - INFO - Response - Page 5:
2025-07-12 21:00:16,153 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:16,153 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:16,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:16,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:16,731 - INFO - Response - Page 6:
2025-07-12 21:00:16,934 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:16,934 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:16,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:16,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:17,466 - INFO - Response - Page 7:
2025-07-12 21:00:17,669 - INFO - 第 7 页获取到 70 条记录
2025-07-12 21:00:17,669 - INFO - 查询完成，共获取到 670 条记录
2025-07-12 21:00:17,669 - INFO - 获取到 670 条表单数据
2025-07-12 21:00:17,669 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-07-12 21:00:17,684 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:17,684 - INFO - 开始处理日期: 2025-03
2025-07-12 21:00:17,684 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:17,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:17,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:18,231 - INFO - Response - Page 1:
2025-07-12 21:00:18,435 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:18,435 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:18,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:18,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:18,997 - INFO - Response - Page 2:
2025-07-12 21:00:19,201 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:19,201 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:19,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:19,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:19,810 - INFO - Response - Page 3:
2025-07-12 21:00:20,013 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:20,013 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:20,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:20,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:20,686 - INFO - Response - Page 4:
2025-07-12 21:00:20,889 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:20,889 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:20,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:20,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:21,389 - INFO - Response - Page 5:
2025-07-12 21:00:21,592 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:21,592 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:21,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:21,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:22,233 - INFO - Response - Page 6:
2025-07-12 21:00:22,436 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:22,436 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:22,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:22,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:22,858 - INFO - Response - Page 7:
2025-07-12 21:00:23,062 - INFO - 第 7 页获取到 61 条记录
2025-07-12 21:00:23,062 - INFO - 查询完成，共获取到 661 条记录
2025-07-12 21:00:23,062 - INFO - 获取到 661 条表单数据
2025-07-12 21:00:23,062 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-07-12 21:00:23,077 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:23,077 - INFO - 开始处理日期: 2025-04
2025-07-12 21:00:23,077 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:23,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:23,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:23,593 - INFO - Response - Page 1:
2025-07-12 21:00:23,796 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:23,796 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:23,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:23,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:24,390 - INFO - Response - Page 2:
2025-07-12 21:00:24,593 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:24,593 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:24,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:24,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:25,172 - INFO - Response - Page 3:
2025-07-12 21:00:25,375 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:25,375 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:25,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:25,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:25,906 - INFO - Response - Page 4:
2025-07-12 21:00:26,125 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:26,125 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:26,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:26,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:26,672 - INFO - Response - Page 5:
2025-07-12 21:00:26,876 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:26,876 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:26,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:26,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:27,344 - INFO - Response - Page 6:
2025-07-12 21:00:27,548 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:27,548 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:27,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:27,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:28,048 - INFO - Response - Page 7:
2025-07-12 21:00:28,251 - INFO - 第 7 页获取到 56 条记录
2025-07-12 21:00:28,251 - INFO - 查询完成，共获取到 656 条记录
2025-07-12 21:00:28,251 - INFO - 获取到 656 条表单数据
2025-07-12 21:00:28,251 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-07-12 21:00:28,267 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:28,267 - INFO - 开始处理日期: 2025-05
2025-07-12 21:00:28,267 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:28,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:28,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:28,783 - INFO - Response - Page 1:
2025-07-12 21:00:28,986 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:28,986 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:28,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:28,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:29,580 - INFO - Response - Page 2:
2025-07-12 21:00:29,783 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:29,783 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:29,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:29,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:30,314 - INFO - Response - Page 3:
2025-07-12 21:00:30,518 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:30,518 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:30,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:30,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:31,112 - INFO - Response - Page 4:
2025-07-12 21:00:31,315 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:31,315 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:31,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:31,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:31,956 - INFO - Response - Page 5:
2025-07-12 21:00:32,159 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:32,159 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:32,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:32,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:32,628 - INFO - Response - Page 6:
2025-07-12 21:00:32,831 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:32,831 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:32,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:32,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:33,316 - INFO - Response - Page 7:
2025-07-12 21:00:33,519 - INFO - 第 7 页获取到 65 条记录
2025-07-12 21:00:33,519 - INFO - 查询完成，共获取到 665 条记录
2025-07-12 21:00:33,519 - INFO - 获取到 665 条表单数据
2025-07-12 21:00:33,519 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-07-12 21:00:33,534 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:33,534 - INFO - 开始处理日期: 2025-06
2025-07-12 21:00:33,534 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:33,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:33,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:34,113 - INFO - Response - Page 1:
2025-07-12 21:00:34,316 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:34,316 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:34,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:34,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:34,832 - INFO - Response - Page 2:
2025-07-12 21:00:35,035 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:35,035 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:35,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:35,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:35,660 - INFO - Response - Page 3:
2025-07-12 21:00:35,863 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:35,863 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:35,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:35,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:36,457 - INFO - Response - Page 4:
2025-07-12 21:00:36,661 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:36,661 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:36,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:36,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:37,176 - INFO - Response - Page 5:
2025-07-12 21:00:37,380 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:37,380 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:37,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:37,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:37,942 - INFO - Response - Page 6:
2025-07-12 21:00:38,146 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:38,146 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:38,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:38,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:38,552 - INFO - Response - Page 7:
2025-07-12 21:00:38,755 - INFO - 第 7 页获取到 33 条记录
2025-07-12 21:00:38,755 - INFO - 查询完成，共获取到 633 条记录
2025-07-12 21:00:38,755 - INFO - 获取到 633 条表单数据
2025-07-12 21:00:38,755 - INFO - 当前日期 2025-06 有 633 条MySQL数据需要处理
2025-07-12 21:00:38,771 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:38,771 - INFO - 开始处理日期: 2025-07
2025-07-12 21:00:38,771 - INFO - Request Parameters - Page 1:
2025-07-12 21:00:38,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:38,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:39,318 - INFO - Response - Page 1:
2025-07-12 21:00:39,521 - INFO - 第 1 页获取到 100 条记录
2025-07-12 21:00:39,521 - INFO - Request Parameters - Page 2:
2025-07-12 21:00:39,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:39,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:40,053 - INFO - Response - Page 2:
2025-07-12 21:00:40,256 - INFO - 第 2 页获取到 100 条记录
2025-07-12 21:00:40,256 - INFO - Request Parameters - Page 3:
2025-07-12 21:00:40,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:40,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:40,819 - INFO - Response - Page 3:
2025-07-12 21:00:41,022 - INFO - 第 3 页获取到 100 条记录
2025-07-12 21:00:41,022 - INFO - Request Parameters - Page 4:
2025-07-12 21:00:41,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:41,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:41,553 - INFO - Response - Page 4:
2025-07-12 21:00:41,756 - INFO - 第 4 页获取到 100 条记录
2025-07-12 21:00:41,756 - INFO - Request Parameters - Page 5:
2025-07-12 21:00:41,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:41,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:42,350 - INFO - Response - Page 5:
2025-07-12 21:00:42,554 - INFO - 第 5 页获取到 100 条记录
2025-07-12 21:00:42,554 - INFO - Request Parameters - Page 6:
2025-07-12 21:00:42,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:42,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:43,179 - INFO - Response - Page 6:
2025-07-12 21:00:43,382 - INFO - 第 6 页获取到 100 条记录
2025-07-12 21:00:43,382 - INFO - Request Parameters - Page 7:
2025-07-12 21:00:43,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 21:00:43,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 21:00:43,679 - INFO - Response - Page 7:
2025-07-12 21:00:43,882 - INFO - 第 7 页获取到 9 条记录
2025-07-12 21:00:43,882 - INFO - 查询完成，共获取到 609 条记录
2025-07-12 21:00:43,882 - INFO - 获取到 609 条表单数据
2025-07-12 21:00:43,882 - INFO - 当前日期 2025-07 有 609 条MySQL数据需要处理
2025-07-12 21:00:43,882 - INFO - 开始更新记录 - 表单实例ID: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05
2025-07-12 21:00:44,336 - INFO - 更新表单数据成功: FINST-UW966371QSRW9R55CW8FND1L2RBR2MVHIFLCM05
2025-07-12 21:00:44,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10233.05, 'new_value': 10818.05}, {'field': 'offline_amount', 'old_value': 6118.75, 'new_value': 6640.75}, {'field': 'total_amount', 'old_value': 16351.8, 'new_value': 17458.8}, {'field': 'order_count', 'old_value': 660, 'new_value': 701}]
2025-07-12 21:00:44,351 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMX8
2025-07-12 21:00:44,805 - INFO - 更新表单数据成功: FINST-HJ966H81GBRWN9IGBR88Q8LZSZ0U1AIESPKCMX8
2025-07-12 21:00:44,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34202.0, 'new_value': 37324.0}, {'field': 'offline_amount', 'old_value': 47885.0, 'new_value': 52108.0}, {'field': 'total_amount', 'old_value': 82087.0, 'new_value': 89432.0}, {'field': 'order_count', 'old_value': 1502, 'new_value': 1634}]
2025-07-12 21:00:44,805 - INFO - 开始更新记录 - 表单实例ID: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9
2025-07-12 21:00:45,336 - INFO - 更新表单数据成功: FINST-90D66XA1U2SWOWYS96V3V4D9ZHQW2FYRIFLCMT9
2025-07-12 21:00:45,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34394.06, 'new_value': 38137.85}, {'field': 'offline_amount', 'old_value': 412678.97, 'new_value': 456154.11}, {'field': 'total_amount', 'old_value': 447073.03, 'new_value': 494291.96}, {'field': 'order_count', 'old_value': 2095, 'new_value': 2301}]
2025-07-12 21:00:45,336 - INFO - 日期 2025-07 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:45,336 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-07-12 21:00:45,336 - INFO - =================同步完成====================
