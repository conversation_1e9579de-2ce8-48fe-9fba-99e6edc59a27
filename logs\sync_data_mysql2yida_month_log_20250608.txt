2025-06-08 00:00:02,416 - INFO - =================使用默认全量同步=============
2025-06-08 00:00:04,056 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 00:00:04,056 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 00:00:04,087 - INFO - 开始处理日期: 2025-01
2025-06-08 00:00:04,087 - INFO - Request Parameters - Page 1:
2025-06-08 00:00:04,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:04,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:05,196 - INFO - Response - Page 1:
2025-06-08 00:00:05,399 - INFO - 第 1 页获取到 100 条记录
2025-06-08 00:00:05,399 - INFO - Request Parameters - Page 2:
2025-06-08 00:00:05,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:05,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:06,306 - INFO - Response - Page 2:
2025-06-08 00:00:06,509 - INFO - 第 2 页获取到 100 条记录
2025-06-08 00:00:06,509 - INFO - Request Parameters - Page 3:
2025-06-08 00:00:06,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:06,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:07,024 - INFO - Response - Page 3:
2025-06-08 00:00:07,227 - INFO - 第 3 页获取到 100 条记录
2025-06-08 00:00:07,227 - INFO - Request Parameters - Page 4:
2025-06-08 00:00:07,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:07,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:07,758 - INFO - Response - Page 4:
2025-06-08 00:00:07,961 - INFO - 第 4 页获取到 100 条记录
2025-06-08 00:00:07,961 - INFO - Request Parameters - Page 5:
2025-06-08 00:00:07,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:07,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:08,414 - INFO - Response - Page 5:
2025-06-08 00:00:08,618 - INFO - 第 5 页获取到 100 条记录
2025-06-08 00:00:08,618 - INFO - Request Parameters - Page 6:
2025-06-08 00:00:08,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:08,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:09,211 - INFO - Response - Page 6:
2025-06-08 00:00:09,414 - INFO - 第 6 页获取到 100 条记录
2025-06-08 00:00:09,414 - INFO - Request Parameters - Page 7:
2025-06-08 00:00:09,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:09,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:09,883 - INFO - Response - Page 7:
2025-06-08 00:00:10,086 - INFO - 第 7 页获取到 82 条记录
2025-06-08 00:00:10,086 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 00:00:10,086 - INFO - 获取到 682 条表单数据
2025-06-08 00:00:10,086 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 00:00:10,102 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:10,102 - INFO - 开始处理日期: 2025-02
2025-06-08 00:00:10,102 - INFO - Request Parameters - Page 1:
2025-06-08 00:00:10,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:10,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:10,570 - INFO - Response - Page 1:
2025-06-08 00:00:10,773 - INFO - 第 1 页获取到 100 条记录
2025-06-08 00:00:10,773 - INFO - Request Parameters - Page 2:
2025-06-08 00:00:10,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:10,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:11,351 - INFO - Response - Page 2:
2025-06-08 00:00:11,554 - INFO - 第 2 页获取到 100 条记录
2025-06-08 00:00:11,554 - INFO - Request Parameters - Page 3:
2025-06-08 00:00:11,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:11,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:12,086 - INFO - Response - Page 3:
2025-06-08 00:00:12,289 - INFO - 第 3 页获取到 100 条记录
2025-06-08 00:00:12,289 - INFO - Request Parameters - Page 4:
2025-06-08 00:00:12,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:12,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:12,773 - INFO - Response - Page 4:
2025-06-08 00:00:12,976 - INFO - 第 4 页获取到 100 条记录
2025-06-08 00:00:12,976 - INFO - Request Parameters - Page 5:
2025-06-08 00:00:12,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:12,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:13,476 - INFO - Response - Page 5:
2025-06-08 00:00:13,679 - INFO - 第 5 页获取到 100 条记录
2025-06-08 00:00:13,679 - INFO - Request Parameters - Page 6:
2025-06-08 00:00:13,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:13,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:14,148 - INFO - Response - Page 6:
2025-06-08 00:00:14,351 - INFO - 第 6 页获取到 100 条记录
2025-06-08 00:00:14,351 - INFO - Request Parameters - Page 7:
2025-06-08 00:00:14,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:14,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:14,819 - INFO - Response - Page 7:
2025-06-08 00:00:15,023 - INFO - 第 7 页获取到 70 条记录
2025-06-08 00:00:15,023 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 00:00:15,023 - INFO - 获取到 670 条表单数据
2025-06-08 00:00:15,023 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 00:00:15,038 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:15,038 - INFO - 开始处理日期: 2025-03
2025-06-08 00:00:15,038 - INFO - Request Parameters - Page 1:
2025-06-08 00:00:15,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:15,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:15,522 - INFO - Response - Page 1:
2025-06-08 00:00:15,725 - INFO - 第 1 页获取到 100 条记录
2025-06-08 00:00:15,725 - INFO - Request Parameters - Page 2:
2025-06-08 00:00:15,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:15,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:16,210 - INFO - Response - Page 2:
2025-06-08 00:00:16,413 - INFO - 第 2 页获取到 100 条记录
2025-06-08 00:00:16,413 - INFO - Request Parameters - Page 3:
2025-06-08 00:00:16,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:16,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:16,944 - INFO - Response - Page 3:
2025-06-08 00:00:17,147 - INFO - 第 3 页获取到 100 条记录
2025-06-08 00:00:17,147 - INFO - Request Parameters - Page 4:
2025-06-08 00:00:17,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:17,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:17,678 - INFO - Response - Page 4:
2025-06-08 00:00:17,881 - INFO - 第 4 页获取到 100 条记录
2025-06-08 00:00:17,881 - INFO - Request Parameters - Page 5:
2025-06-08 00:00:17,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:17,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:18,350 - INFO - Response - Page 5:
2025-06-08 00:00:18,553 - INFO - 第 5 页获取到 100 条记录
2025-06-08 00:00:18,553 - INFO - Request Parameters - Page 6:
2025-06-08 00:00:18,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:18,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:19,069 - INFO - Response - Page 6:
2025-06-08 00:00:19,272 - INFO - 第 6 页获取到 100 条记录
2025-06-08 00:00:19,272 - INFO - Request Parameters - Page 7:
2025-06-08 00:00:19,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:19,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:19,693 - INFO - Response - Page 7:
2025-06-08 00:00:19,897 - INFO - 第 7 页获取到 61 条记录
2025-06-08 00:00:19,897 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 00:00:19,897 - INFO - 获取到 661 条表单数据
2025-06-08 00:00:19,897 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 00:00:19,912 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:19,912 - INFO - 开始处理日期: 2025-04
2025-06-08 00:00:19,912 - INFO - Request Parameters - Page 1:
2025-06-08 00:00:19,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:19,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:20,459 - INFO - Response - Page 1:
2025-06-08 00:00:20,662 - INFO - 第 1 页获取到 100 条记录
2025-06-08 00:00:20,662 - INFO - Request Parameters - Page 2:
2025-06-08 00:00:20,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:20,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:21,084 - INFO - Response - Page 2:
2025-06-08 00:00:21,287 - INFO - 第 2 页获取到 100 条记录
2025-06-08 00:00:21,287 - INFO - Request Parameters - Page 3:
2025-06-08 00:00:21,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:21,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:21,802 - INFO - Response - Page 3:
2025-06-08 00:00:22,005 - INFO - 第 3 页获取到 100 条记录
2025-06-08 00:00:22,005 - INFO - Request Parameters - Page 4:
2025-06-08 00:00:22,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:22,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:22,552 - INFO - Response - Page 4:
2025-06-08 00:00:22,755 - INFO - 第 4 页获取到 100 条记录
2025-06-08 00:00:22,755 - INFO - Request Parameters - Page 5:
2025-06-08 00:00:22,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:22,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:23,224 - INFO - Response - Page 5:
2025-06-08 00:00:23,427 - INFO - 第 5 页获取到 100 条记录
2025-06-08 00:00:23,427 - INFO - Request Parameters - Page 6:
2025-06-08 00:00:23,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:23,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:24,021 - INFO - Response - Page 6:
2025-06-08 00:00:24,224 - INFO - 第 6 页获取到 100 条记录
2025-06-08 00:00:24,224 - INFO - Request Parameters - Page 7:
2025-06-08 00:00:24,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:24,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:24,661 - INFO - Response - Page 7:
2025-06-08 00:00:24,864 - INFO - 第 7 页获取到 56 条记录
2025-06-08 00:00:24,864 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 00:00:24,864 - INFO - 获取到 656 条表单数据
2025-06-08 00:00:24,864 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 00:00:24,880 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:24,880 - INFO - 开始处理日期: 2025-05
2025-06-08 00:00:24,880 - INFO - Request Parameters - Page 1:
2025-06-08 00:00:24,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:24,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:25,395 - INFO - Response - Page 1:
2025-06-08 00:00:25,598 - INFO - 第 1 页获取到 100 条记录
2025-06-08 00:00:25,598 - INFO - Request Parameters - Page 2:
2025-06-08 00:00:25,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:25,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:26,051 - INFO - Response - Page 2:
2025-06-08 00:00:26,255 - INFO - 第 2 页获取到 100 条记录
2025-06-08 00:00:26,255 - INFO - Request Parameters - Page 3:
2025-06-08 00:00:26,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:26,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:26,676 - INFO - Response - Page 3:
2025-06-08 00:00:26,879 - INFO - 第 3 页获取到 100 条记录
2025-06-08 00:00:26,879 - INFO - Request Parameters - Page 4:
2025-06-08 00:00:26,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:26,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:27,301 - INFO - Response - Page 4:
2025-06-08 00:00:27,504 - INFO - 第 4 页获取到 100 条记录
2025-06-08 00:00:27,504 - INFO - Request Parameters - Page 5:
2025-06-08 00:00:27,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:27,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:27,957 - INFO - Response - Page 5:
2025-06-08 00:00:28,160 - INFO - 第 5 页获取到 100 条记录
2025-06-08 00:00:28,160 - INFO - Request Parameters - Page 6:
2025-06-08 00:00:28,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:28,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:28,645 - INFO - Response - Page 6:
2025-06-08 00:00:28,848 - INFO - 第 6 页获取到 100 条记录
2025-06-08 00:00:28,848 - INFO - Request Parameters - Page 7:
2025-06-08 00:00:28,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:28,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:29,207 - INFO - Response - Page 7:
2025-06-08 00:00:29,410 - INFO - 第 7 页获取到 38 条记录
2025-06-08 00:00:29,410 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 00:00:29,410 - INFO - 获取到 638 条表单数据
2025-06-08 00:00:29,410 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 00:00:29,426 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:29,426 - INFO - 开始处理日期: 2025-06
2025-06-08 00:00:29,426 - INFO - Request Parameters - Page 1:
2025-06-08 00:00:29,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:29,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:29,894 - INFO - Response - Page 1:
2025-06-08 00:00:30,098 - INFO - 第 1 页获取到 100 条记录
2025-06-08 00:00:30,098 - INFO - Request Parameters - Page 2:
2025-06-08 00:00:30,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:30,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:30,629 - INFO - Response - Page 2:
2025-06-08 00:00:30,832 - INFO - 第 2 页获取到 100 条记录
2025-06-08 00:00:30,832 - INFO - Request Parameters - Page 3:
2025-06-08 00:00:30,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:30,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:31,347 - INFO - Response - Page 3:
2025-06-08 00:00:31,550 - INFO - 第 3 页获取到 100 条记录
2025-06-08 00:00:31,550 - INFO - Request Parameters - Page 4:
2025-06-08 00:00:31,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:31,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:32,019 - INFO - Response - Page 4:
2025-06-08 00:00:32,222 - INFO - 第 4 页获取到 100 条记录
2025-06-08 00:00:32,222 - INFO - Request Parameters - Page 5:
2025-06-08 00:00:32,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:32,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:32,863 - INFO - Response - Page 5:
2025-06-08 00:00:33,066 - INFO - 第 5 页获取到 100 条记录
2025-06-08 00:00:33,066 - INFO - Request Parameters - Page 6:
2025-06-08 00:00:33,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:33,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:33,534 - INFO - Response - Page 6:
2025-06-08 00:00:33,737 - INFO - 第 6 页获取到 100 条记录
2025-06-08 00:00:33,737 - INFO - Request Parameters - Page 7:
2025-06-08 00:00:33,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 00:00:33,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 00:00:34,034 - INFO - Response - Page 7:
2025-06-08 00:00:34,237 - INFO - 第 7 页获取到 19 条记录
2025-06-08 00:00:34,237 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 00:00:34,237 - INFO - 获取到 619 条表单数据
2025-06-08 00:00:34,237 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 00:00:34,237 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM45
2025-06-08 00:00:34,643 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM45
2025-06-08 00:00:34,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334846.33, 'new_value': 407735.64}, {'field': 'total_amount', 'old_value': 334846.33, 'new_value': 407735.64}, {'field': 'order_count', 'old_value': 4214, 'new_value': 4830}]
2025-06-08 00:00:34,643 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM95
2025-06-08 00:00:35,112 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM95
2025-06-08 00:00:35,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11778.62, 'new_value': 13456.72}, {'field': 'offline_amount', 'old_value': 145139.62, 'new_value': 175679.22}, {'field': 'total_amount', 'old_value': 156918.24, 'new_value': 189135.94}, {'field': 'order_count', 'old_value': 650, 'new_value': 769}]
2025-06-08 00:00:35,112 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMD6
2025-06-08 00:00:35,503 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMD6
2025-06-08 00:00:35,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9856.0, 'new_value': 10592.0}, {'field': 'offline_amount', 'old_value': 30388.92, 'new_value': 33344.52}, {'field': 'total_amount', 'old_value': 40244.92, 'new_value': 43936.52}, {'field': 'order_count', 'old_value': 59, 'new_value': 65}]
2025-06-08 00:00:35,503 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLO
2025-06-08 00:00:35,893 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLO
2025-06-08 00:00:35,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21759.0, 'new_value': 24983.0}, {'field': 'offline_amount', 'old_value': 40057.0, 'new_value': 45774.0}, {'field': 'total_amount', 'old_value': 61816.0, 'new_value': 70757.0}, {'field': 'order_count', 'old_value': 1241, 'new_value': 1423}]
2025-06-08 00:00:35,893 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTP
2025-06-08 00:00:36,315 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTP
2025-06-08 00:00:36,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97862.0, 'new_value': 100887.0}, {'field': 'total_amount', 'old_value': 101535.0, 'new_value': 104560.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 35}]
2025-06-08 00:00:36,315 - INFO - 日期 2025-06 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:36,315 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-06-08 00:00:36,315 - INFO - =================同步完成====================
2025-06-08 03:00:02,358 - INFO - =================使用默认全量同步=============
2025-06-08 03:00:03,967 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 03:00:03,967 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 03:00:03,998 - INFO - 开始处理日期: 2025-01
2025-06-08 03:00:04,014 - INFO - Request Parameters - Page 1:
2025-06-08 03:00:04,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:04,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:05,139 - INFO - Response - Page 1:
2025-06-08 03:00:05,342 - INFO - 第 1 页获取到 100 条记录
2025-06-08 03:00:05,342 - INFO - Request Parameters - Page 2:
2025-06-08 03:00:05,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:05,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:06,107 - INFO - Response - Page 2:
2025-06-08 03:00:06,310 - INFO - 第 2 页获取到 100 条记录
2025-06-08 03:00:06,310 - INFO - Request Parameters - Page 3:
2025-06-08 03:00:06,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:06,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:06,810 - INFO - Response - Page 3:
2025-06-08 03:00:07,013 - INFO - 第 3 页获取到 100 条记录
2025-06-08 03:00:07,013 - INFO - Request Parameters - Page 4:
2025-06-08 03:00:07,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:07,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:07,482 - INFO - Response - Page 4:
2025-06-08 03:00:07,685 - INFO - 第 4 页获取到 100 条记录
2025-06-08 03:00:07,685 - INFO - Request Parameters - Page 5:
2025-06-08 03:00:07,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:07,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:08,216 - INFO - Response - Page 5:
2025-06-08 03:00:08,419 - INFO - 第 5 页获取到 100 条记录
2025-06-08 03:00:08,419 - INFO - Request Parameters - Page 6:
2025-06-08 03:00:08,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:08,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:08,903 - INFO - Response - Page 6:
2025-06-08 03:00:09,107 - INFO - 第 6 页获取到 100 条记录
2025-06-08 03:00:09,107 - INFO - Request Parameters - Page 7:
2025-06-08 03:00:09,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:09,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:09,606 - INFO - Response - Page 7:
2025-06-08 03:00:09,810 - INFO - 第 7 页获取到 82 条记录
2025-06-08 03:00:09,810 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 03:00:09,810 - INFO - 获取到 682 条表单数据
2025-06-08 03:00:09,810 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 03:00:09,825 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:09,825 - INFO - 开始处理日期: 2025-02
2025-06-08 03:00:09,825 - INFO - Request Parameters - Page 1:
2025-06-08 03:00:09,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:09,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:10,309 - INFO - Response - Page 1:
2025-06-08 03:00:10,513 - INFO - 第 1 页获取到 100 条记录
2025-06-08 03:00:10,513 - INFO - Request Parameters - Page 2:
2025-06-08 03:00:10,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:10,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:10,997 - INFO - Response - Page 2:
2025-06-08 03:00:11,200 - INFO - 第 2 页获取到 100 条记录
2025-06-08 03:00:11,200 - INFO - Request Parameters - Page 3:
2025-06-08 03:00:11,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:11,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:11,653 - INFO - Response - Page 3:
2025-06-08 03:00:11,856 - INFO - 第 3 页获取到 100 条记录
2025-06-08 03:00:11,856 - INFO - Request Parameters - Page 4:
2025-06-08 03:00:11,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:11,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:12,372 - INFO - Response - Page 4:
2025-06-08 03:00:12,575 - INFO - 第 4 页获取到 100 条记录
2025-06-08 03:00:12,575 - INFO - Request Parameters - Page 5:
2025-06-08 03:00:12,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:12,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:13,121 - INFO - Response - Page 5:
2025-06-08 03:00:13,324 - INFO - 第 5 页获取到 100 条记录
2025-06-08 03:00:13,324 - INFO - Request Parameters - Page 6:
2025-06-08 03:00:13,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:13,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:13,793 - INFO - Response - Page 6:
2025-06-08 03:00:13,996 - INFO - 第 6 页获取到 100 条记录
2025-06-08 03:00:13,996 - INFO - Request Parameters - Page 7:
2025-06-08 03:00:13,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:13,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:14,480 - INFO - Response - Page 7:
2025-06-08 03:00:14,684 - INFO - 第 7 页获取到 70 条记录
2025-06-08 03:00:14,684 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 03:00:14,684 - INFO - 获取到 670 条表单数据
2025-06-08 03:00:14,684 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 03:00:14,699 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:14,699 - INFO - 开始处理日期: 2025-03
2025-06-08 03:00:14,699 - INFO - Request Parameters - Page 1:
2025-06-08 03:00:14,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:14,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:15,230 - INFO - Response - Page 1:
2025-06-08 03:00:15,433 - INFO - 第 1 页获取到 100 条记录
2025-06-08 03:00:15,433 - INFO - Request Parameters - Page 2:
2025-06-08 03:00:15,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:15,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:15,965 - INFO - Response - Page 2:
2025-06-08 03:00:16,168 - INFO - 第 2 页获取到 100 条记录
2025-06-08 03:00:16,168 - INFO - Request Parameters - Page 3:
2025-06-08 03:00:16,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:16,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:16,683 - INFO - Response - Page 3:
2025-06-08 03:00:16,886 - INFO - 第 3 页获取到 100 条记录
2025-06-08 03:00:16,886 - INFO - Request Parameters - Page 4:
2025-06-08 03:00:16,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:16,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:17,386 - INFO - Response - Page 4:
2025-06-08 03:00:17,589 - INFO - 第 4 页获取到 100 条记录
2025-06-08 03:00:17,589 - INFO - Request Parameters - Page 5:
2025-06-08 03:00:17,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:17,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:18,105 - INFO - Response - Page 5:
2025-06-08 03:00:18,308 - INFO - 第 5 页获取到 100 条记录
2025-06-08 03:00:18,308 - INFO - Request Parameters - Page 6:
2025-06-08 03:00:18,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:18,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:18,776 - INFO - Response - Page 6:
2025-06-08 03:00:18,980 - INFO - 第 6 页获取到 100 条记录
2025-06-08 03:00:18,980 - INFO - Request Parameters - Page 7:
2025-06-08 03:00:18,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:18,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:19,433 - INFO - Response - Page 7:
2025-06-08 03:00:19,636 - INFO - 第 7 页获取到 61 条记录
2025-06-08 03:00:19,636 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 03:00:19,636 - INFO - 获取到 661 条表单数据
2025-06-08 03:00:19,636 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 03:00:19,651 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:19,651 - INFO - 开始处理日期: 2025-04
2025-06-08 03:00:19,651 - INFO - Request Parameters - Page 1:
2025-06-08 03:00:19,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:19,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:20,307 - INFO - Response - Page 1:
2025-06-08 03:00:20,510 - INFO - 第 1 页获取到 100 条记录
2025-06-08 03:00:20,510 - INFO - Request Parameters - Page 2:
2025-06-08 03:00:20,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:20,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:20,964 - INFO - Response - Page 2:
2025-06-08 03:00:21,167 - INFO - 第 2 页获取到 100 条记录
2025-06-08 03:00:21,167 - INFO - Request Parameters - Page 3:
2025-06-08 03:00:21,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:21,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:21,651 - INFO - Response - Page 3:
2025-06-08 03:00:21,854 - INFO - 第 3 页获取到 100 条记录
2025-06-08 03:00:21,854 - INFO - Request Parameters - Page 4:
2025-06-08 03:00:21,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:21,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:22,323 - INFO - Response - Page 4:
2025-06-08 03:00:22,526 - INFO - 第 4 页获取到 100 条记录
2025-06-08 03:00:22,526 - INFO - Request Parameters - Page 5:
2025-06-08 03:00:22,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:22,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:22,963 - INFO - Response - Page 5:
2025-06-08 03:00:23,166 - INFO - 第 5 页获取到 100 条记录
2025-06-08 03:00:23,166 - INFO - Request Parameters - Page 6:
2025-06-08 03:00:23,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:23,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:23,666 - INFO - Response - Page 6:
2025-06-08 03:00:23,869 - INFO - 第 6 页获取到 100 条记录
2025-06-08 03:00:23,869 - INFO - Request Parameters - Page 7:
2025-06-08 03:00:23,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:23,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:24,291 - INFO - Response - Page 7:
2025-06-08 03:00:24,494 - INFO - 第 7 页获取到 56 条记录
2025-06-08 03:00:24,494 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 03:00:24,494 - INFO - 获取到 656 条表单数据
2025-06-08 03:00:24,494 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 03:00:24,510 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:24,510 - INFO - 开始处理日期: 2025-05
2025-06-08 03:00:24,510 - INFO - Request Parameters - Page 1:
2025-06-08 03:00:24,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:24,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:25,010 - INFO - Response - Page 1:
2025-06-08 03:00:25,213 - INFO - 第 1 页获取到 100 条记录
2025-06-08 03:00:25,213 - INFO - Request Parameters - Page 2:
2025-06-08 03:00:25,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:25,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:25,666 - INFO - Response - Page 2:
2025-06-08 03:00:25,869 - INFO - 第 2 页获取到 100 条记录
2025-06-08 03:00:25,869 - INFO - Request Parameters - Page 3:
2025-06-08 03:00:25,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:25,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:26,353 - INFO - Response - Page 3:
2025-06-08 03:00:26,556 - INFO - 第 3 页获取到 100 条记录
2025-06-08 03:00:26,556 - INFO - Request Parameters - Page 4:
2025-06-08 03:00:26,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:26,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:27,072 - INFO - Response - Page 4:
2025-06-08 03:00:27,275 - INFO - 第 4 页获取到 100 条记录
2025-06-08 03:00:27,275 - INFO - Request Parameters - Page 5:
2025-06-08 03:00:27,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:27,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:27,728 - INFO - Response - Page 5:
2025-06-08 03:00:27,931 - INFO - 第 5 页获取到 100 条记录
2025-06-08 03:00:27,931 - INFO - Request Parameters - Page 6:
2025-06-08 03:00:27,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:27,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:28,446 - INFO - Response - Page 6:
2025-06-08 03:00:28,649 - INFO - 第 6 页获取到 100 条记录
2025-06-08 03:00:28,649 - INFO - Request Parameters - Page 7:
2025-06-08 03:00:28,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:28,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:29,009 - INFO - Response - Page 7:
2025-06-08 03:00:29,212 - INFO - 第 7 页获取到 38 条记录
2025-06-08 03:00:29,212 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 03:00:29,212 - INFO - 获取到 638 条表单数据
2025-06-08 03:00:29,212 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 03:00:29,227 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:29,227 - INFO - 开始处理日期: 2025-06
2025-06-08 03:00:29,227 - INFO - Request Parameters - Page 1:
2025-06-08 03:00:29,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:29,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:29,774 - INFO - Response - Page 1:
2025-06-08 03:00:29,977 - INFO - 第 1 页获取到 100 条记录
2025-06-08 03:00:29,977 - INFO - Request Parameters - Page 2:
2025-06-08 03:00:29,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:29,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:30,602 - INFO - Response - Page 2:
2025-06-08 03:00:30,805 - INFO - 第 2 页获取到 100 条记录
2025-06-08 03:00:30,805 - INFO - Request Parameters - Page 3:
2025-06-08 03:00:30,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:30,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:31,336 - INFO - Response - Page 3:
2025-06-08 03:00:31,539 - INFO - 第 3 页获取到 100 条记录
2025-06-08 03:00:31,539 - INFO - Request Parameters - Page 4:
2025-06-08 03:00:31,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:31,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:31,992 - INFO - Response - Page 4:
2025-06-08 03:00:32,196 - INFO - 第 4 页获取到 100 条记录
2025-06-08 03:00:32,196 - INFO - Request Parameters - Page 5:
2025-06-08 03:00:32,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:32,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:32,633 - INFO - Response - Page 5:
2025-06-08 03:00:32,836 - INFO - 第 5 页获取到 100 条记录
2025-06-08 03:00:32,836 - INFO - Request Parameters - Page 6:
2025-06-08 03:00:32,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:32,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:33,273 - INFO - Response - Page 6:
2025-06-08 03:00:33,477 - INFO - 第 6 页获取到 100 条记录
2025-06-08 03:00:33,477 - INFO - Request Parameters - Page 7:
2025-06-08 03:00:33,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 03:00:33,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 03:00:33,789 - INFO - Response - Page 7:
2025-06-08 03:00:33,992 - INFO - 第 7 页获取到 19 条记录
2025-06-08 03:00:33,992 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 03:00:33,992 - INFO - 获取到 619 条表单数据
2025-06-08 03:00:33,992 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 03:00:34,008 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:34,008 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 03:00:34,008 - INFO - =================同步完成====================
2025-06-08 06:00:02,332 - INFO - =================使用默认全量同步=============
2025-06-08 06:00:03,941 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 06:00:03,941 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 06:00:03,972 - INFO - 开始处理日期: 2025-01
2025-06-08 06:00:03,972 - INFO - Request Parameters - Page 1:
2025-06-08 06:00:03,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:03,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:05,347 - INFO - Response - Page 1:
2025-06-08 06:00:05,550 - INFO - 第 1 页获取到 100 条记录
2025-06-08 06:00:05,550 - INFO - Request Parameters - Page 2:
2025-06-08 06:00:05,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:05,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:06,034 - INFO - Response - Page 2:
2025-06-08 06:00:06,237 - INFO - 第 2 页获取到 100 条记录
2025-06-08 06:00:06,237 - INFO - Request Parameters - Page 3:
2025-06-08 06:00:06,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:06,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:06,737 - INFO - Response - Page 3:
2025-06-08 06:00:06,940 - INFO - 第 3 页获取到 100 条记录
2025-06-08 06:00:06,940 - INFO - Request Parameters - Page 4:
2025-06-08 06:00:06,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:06,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:07,424 - INFO - Response - Page 4:
2025-06-08 06:00:07,627 - INFO - 第 4 页获取到 100 条记录
2025-06-08 06:00:07,627 - INFO - Request Parameters - Page 5:
2025-06-08 06:00:07,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:07,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:08,112 - INFO - Response - Page 5:
2025-06-08 06:00:08,315 - INFO - 第 5 页获取到 100 条记录
2025-06-08 06:00:08,315 - INFO - Request Parameters - Page 6:
2025-06-08 06:00:08,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:08,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:08,799 - INFO - Response - Page 6:
2025-06-08 06:00:09,002 - INFO - 第 6 页获取到 100 条记录
2025-06-08 06:00:09,002 - INFO - Request Parameters - Page 7:
2025-06-08 06:00:09,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:09,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:09,518 - INFO - Response - Page 7:
2025-06-08 06:00:09,721 - INFO - 第 7 页获取到 82 条记录
2025-06-08 06:00:09,721 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 06:00:09,721 - INFO - 获取到 682 条表单数据
2025-06-08 06:00:09,721 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 06:00:09,736 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:09,736 - INFO - 开始处理日期: 2025-02
2025-06-08 06:00:09,736 - INFO - Request Parameters - Page 1:
2025-06-08 06:00:09,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:09,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:10,205 - INFO - Response - Page 1:
2025-06-08 06:00:10,408 - INFO - 第 1 页获取到 100 条记录
2025-06-08 06:00:10,408 - INFO - Request Parameters - Page 2:
2025-06-08 06:00:10,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:10,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:10,892 - INFO - Response - Page 2:
2025-06-08 06:00:11,095 - INFO - 第 2 页获取到 100 条记录
2025-06-08 06:00:11,095 - INFO - Request Parameters - Page 3:
2025-06-08 06:00:11,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:11,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:11,564 - INFO - Response - Page 3:
2025-06-08 06:00:11,767 - INFO - 第 3 页获取到 100 条记录
2025-06-08 06:00:11,767 - INFO - Request Parameters - Page 4:
2025-06-08 06:00:11,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:11,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:12,236 - INFO - Response - Page 4:
2025-06-08 06:00:12,439 - INFO - 第 4 页获取到 100 条记录
2025-06-08 06:00:12,439 - INFO - Request Parameters - Page 5:
2025-06-08 06:00:12,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:12,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:13,048 - INFO - Response - Page 5:
2025-06-08 06:00:13,251 - INFO - 第 5 页获取到 100 条记录
2025-06-08 06:00:13,251 - INFO - Request Parameters - Page 6:
2025-06-08 06:00:13,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:13,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:13,814 - INFO - Response - Page 6:
2025-06-08 06:00:14,017 - INFO - 第 6 页获取到 100 条记录
2025-06-08 06:00:14,017 - INFO - Request Parameters - Page 7:
2025-06-08 06:00:14,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:14,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:14,501 - INFO - Response - Page 7:
2025-06-08 06:00:14,704 - INFO - 第 7 页获取到 70 条记录
2025-06-08 06:00:14,704 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 06:00:14,704 - INFO - 获取到 670 条表单数据
2025-06-08 06:00:14,704 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 06:00:14,720 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:14,720 - INFO - 开始处理日期: 2025-03
2025-06-08 06:00:14,720 - INFO - Request Parameters - Page 1:
2025-06-08 06:00:14,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:14,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:15,188 - INFO - Response - Page 1:
2025-06-08 06:00:15,391 - INFO - 第 1 页获取到 100 条记录
2025-06-08 06:00:15,391 - INFO - Request Parameters - Page 2:
2025-06-08 06:00:15,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:15,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:15,844 - INFO - Response - Page 2:
2025-06-08 06:00:16,047 - INFO - 第 2 页获取到 100 条记录
2025-06-08 06:00:16,047 - INFO - Request Parameters - Page 3:
2025-06-08 06:00:16,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:16,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:16,563 - INFO - Response - Page 3:
2025-06-08 06:00:16,766 - INFO - 第 3 页获取到 100 条记录
2025-06-08 06:00:16,766 - INFO - Request Parameters - Page 4:
2025-06-08 06:00:16,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:16,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:17,235 - INFO - Response - Page 4:
2025-06-08 06:00:17,438 - INFO - 第 4 页获取到 100 条记录
2025-06-08 06:00:17,438 - INFO - Request Parameters - Page 5:
2025-06-08 06:00:17,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:17,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:17,938 - INFO - Response - Page 5:
2025-06-08 06:00:18,141 - INFO - 第 5 页获取到 100 条记录
2025-06-08 06:00:18,141 - INFO - Request Parameters - Page 6:
2025-06-08 06:00:18,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:18,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:18,672 - INFO - Response - Page 6:
2025-06-08 06:00:18,875 - INFO - 第 6 页获取到 100 条记录
2025-06-08 06:00:18,875 - INFO - Request Parameters - Page 7:
2025-06-08 06:00:18,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:18,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:19,297 - INFO - Response - Page 7:
2025-06-08 06:00:19,500 - INFO - 第 7 页获取到 61 条记录
2025-06-08 06:00:19,500 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 06:00:19,500 - INFO - 获取到 661 条表单数据
2025-06-08 06:00:19,500 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 06:00:19,515 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:19,515 - INFO - 开始处理日期: 2025-04
2025-06-08 06:00:19,515 - INFO - Request Parameters - Page 1:
2025-06-08 06:00:19,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:19,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:20,062 - INFO - Response - Page 1:
2025-06-08 06:00:20,265 - INFO - 第 1 页获取到 100 条记录
2025-06-08 06:00:20,265 - INFO - Request Parameters - Page 2:
2025-06-08 06:00:20,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:20,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:20,734 - INFO - Response - Page 2:
2025-06-08 06:00:20,937 - INFO - 第 2 页获取到 100 条记录
2025-06-08 06:00:20,937 - INFO - Request Parameters - Page 3:
2025-06-08 06:00:20,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:20,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:21,421 - INFO - Response - Page 3:
2025-06-08 06:00:21,624 - INFO - 第 3 页获取到 100 条记录
2025-06-08 06:00:21,624 - INFO - Request Parameters - Page 4:
2025-06-08 06:00:21,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:21,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:22,124 - INFO - Response - Page 4:
2025-06-08 06:00:22,327 - INFO - 第 4 页获取到 100 条记录
2025-06-08 06:00:22,327 - INFO - Request Parameters - Page 5:
2025-06-08 06:00:22,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:22,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:22,952 - INFO - Response - Page 5:
2025-06-08 06:00:23,155 - INFO - 第 5 页获取到 100 条记录
2025-06-08 06:00:23,155 - INFO - Request Parameters - Page 6:
2025-06-08 06:00:23,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:23,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:23,686 - INFO - Response - Page 6:
2025-06-08 06:00:23,890 - INFO - 第 6 页获取到 100 条记录
2025-06-08 06:00:23,890 - INFO - Request Parameters - Page 7:
2025-06-08 06:00:23,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:23,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:24,389 - INFO - Response - Page 7:
2025-06-08 06:00:24,593 - INFO - 第 7 页获取到 56 条记录
2025-06-08 06:00:24,593 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 06:00:24,593 - INFO - 获取到 656 条表单数据
2025-06-08 06:00:24,593 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 06:00:24,608 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:24,608 - INFO - 开始处理日期: 2025-05
2025-06-08 06:00:24,608 - INFO - Request Parameters - Page 1:
2025-06-08 06:00:24,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:24,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:25,202 - INFO - Response - Page 1:
2025-06-08 06:00:25,405 - INFO - 第 1 页获取到 100 条记录
2025-06-08 06:00:25,405 - INFO - Request Parameters - Page 2:
2025-06-08 06:00:25,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:25,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:25,952 - INFO - Response - Page 2:
2025-06-08 06:00:26,155 - INFO - 第 2 页获取到 100 条记录
2025-06-08 06:00:26,155 - INFO - Request Parameters - Page 3:
2025-06-08 06:00:26,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:26,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:26,623 - INFO - Response - Page 3:
2025-06-08 06:00:26,826 - INFO - 第 3 页获取到 100 条记录
2025-06-08 06:00:26,826 - INFO - Request Parameters - Page 4:
2025-06-08 06:00:26,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:26,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:27,311 - INFO - Response - Page 4:
2025-06-08 06:00:27,514 - INFO - 第 4 页获取到 100 条记录
2025-06-08 06:00:27,514 - INFO - Request Parameters - Page 5:
2025-06-08 06:00:27,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:27,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:27,998 - INFO - Response - Page 5:
2025-06-08 06:00:28,201 - INFO - 第 5 页获取到 100 条记录
2025-06-08 06:00:28,201 - INFO - Request Parameters - Page 6:
2025-06-08 06:00:28,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:28,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:28,732 - INFO - Response - Page 6:
2025-06-08 06:00:28,935 - INFO - 第 6 页获取到 100 条记录
2025-06-08 06:00:28,935 - INFO - Request Parameters - Page 7:
2025-06-08 06:00:28,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:28,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:29,373 - INFO - Response - Page 7:
2025-06-08 06:00:29,576 - INFO - 第 7 页获取到 38 条记录
2025-06-08 06:00:29,576 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 06:00:29,576 - INFO - 获取到 638 条表单数据
2025-06-08 06:00:29,576 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 06:00:29,592 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:29,592 - INFO - 开始处理日期: 2025-06
2025-06-08 06:00:29,592 - INFO - Request Parameters - Page 1:
2025-06-08 06:00:29,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:29,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:30,107 - INFO - Response - Page 1:
2025-06-08 06:00:30,310 - INFO - 第 1 页获取到 100 条记录
2025-06-08 06:00:30,310 - INFO - Request Parameters - Page 2:
2025-06-08 06:00:30,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:30,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:30,794 - INFO - Response - Page 2:
2025-06-08 06:00:30,998 - INFO - 第 2 页获取到 100 条记录
2025-06-08 06:00:30,998 - INFO - Request Parameters - Page 3:
2025-06-08 06:00:30,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:30,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:31,513 - INFO - Response - Page 3:
2025-06-08 06:00:31,716 - INFO - 第 3 页获取到 100 条记录
2025-06-08 06:00:31,716 - INFO - Request Parameters - Page 4:
2025-06-08 06:00:31,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:31,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:32,138 - INFO - Response - Page 4:
2025-06-08 06:00:32,341 - INFO - 第 4 页获取到 100 条记录
2025-06-08 06:00:32,341 - INFO - Request Parameters - Page 5:
2025-06-08 06:00:32,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:32,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:32,888 - INFO - Response - Page 5:
2025-06-08 06:00:33,091 - INFO - 第 5 页获取到 100 条记录
2025-06-08 06:00:33,091 - INFO - Request Parameters - Page 6:
2025-06-08 06:00:33,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:33,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:33,622 - INFO - Response - Page 6:
2025-06-08 06:00:33,825 - INFO - 第 6 页获取到 100 条记录
2025-06-08 06:00:33,825 - INFO - Request Parameters - Page 7:
2025-06-08 06:00:33,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 06:00:33,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 06:00:34,153 - INFO - Response - Page 7:
2025-06-08 06:00:34,356 - INFO - 第 7 页获取到 19 条记录
2025-06-08 06:00:34,356 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 06:00:34,356 - INFO - 获取到 619 条表单数据
2025-06-08 06:00:34,356 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 06:00:34,356 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7B
2025-06-08 06:00:34,856 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7B
2025-06-08 06:00:34,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72694.0, 'new_value': 85160.0}, {'field': 'total_amount', 'old_value': 72694.0, 'new_value': 85160.0}, {'field': 'order_count', 'old_value': 1030, 'new_value': 1236}]
2025-06-08 06:00:34,856 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9B
2025-06-08 06:00:35,325 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9B
2025-06-08 06:00:35,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19927.6, 'new_value': 22657.6}, {'field': 'total_amount', 'old_value': 19927.6, 'new_value': 22657.6}, {'field': 'order_count', 'old_value': 154, 'new_value': 186}]
2025-06-08 06:00:35,325 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAB
2025-06-08 06:00:35,762 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAB
2025-06-08 06:00:35,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15357.85, 'new_value': 24274.85}, {'field': 'total_amount', 'old_value': 21025.56, 'new_value': 29942.56}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-08 06:00:35,762 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMBB
2025-06-08 06:00:36,262 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMBB
2025-06-08 06:00:36,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1435.0, 'new_value': 1733.0}, {'field': 'total_amount', 'old_value': 1435.0, 'new_value': 1733.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-08 06:00:36,262 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMDB
2025-06-08 06:00:36,684 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMDB
2025-06-08 06:00:36,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6566.4, 'new_value': 7619.4}, {'field': 'total_amount', 'old_value': 6566.4, 'new_value': 7619.4}, {'field': 'order_count', 'old_value': 33, 'new_value': 37}]
2025-06-08 06:00:36,684 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMEB
2025-06-08 06:00:37,106 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMEB
2025-06-08 06:00:37,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4516.0, 'new_value': 5205.0}, {'field': 'total_amount', 'old_value': 4516.0, 'new_value': 5205.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 23}]
2025-06-08 06:00:37,106 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMFB
2025-06-08 06:00:37,559 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMFB
2025-06-08 06:00:37,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2567.6, 'new_value': 3514.6}, {'field': 'total_amount', 'old_value': 6807.74, 'new_value': 7754.74}, {'field': 'order_count', 'old_value': 28, 'new_value': 34}]
2025-06-08 06:00:37,559 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMGB
2025-06-08 06:00:37,996 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMGB
2025-06-08 06:00:37,996 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3587.6, 'new_value': 7216.4}, {'field': 'offline_amount', 'old_value': 20731.9, 'new_value': 25702.9}, {'field': 'total_amount', 'old_value': 24319.5, 'new_value': 32919.3}, {'field': 'order_count', 'old_value': 30, 'new_value': 40}]
2025-06-08 06:00:37,996 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMHB
2025-06-08 06:00:38,371 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMHB
2025-06-08 06:00:38,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10000.0, 'new_value': 11540.0}, {'field': 'total_amount', 'old_value': 10000.0, 'new_value': 11540.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-08 06:00:38,371 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMIB
2025-06-08 06:00:38,824 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMIB
2025-06-08 06:00:38,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20516.8, 'new_value': 23133.7}, {'field': 'total_amount', 'old_value': 20516.8, 'new_value': 23133.7}, {'field': 'order_count', 'old_value': 72, 'new_value': 84}]
2025-06-08 06:00:38,824 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMJB
2025-06-08 06:00:39,230 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMJB
2025-06-08 06:00:39,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19575.05, 'new_value': 24112.0}, {'field': 'total_amount', 'old_value': 19575.05, 'new_value': 24112.0}, {'field': 'order_count', 'old_value': 628, 'new_value': 773}]
2025-06-08 06:00:39,230 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMKB
2025-06-08 06:00:39,683 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMKB
2025-06-08 06:00:39,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1000.0}, {'field': 'total_amount', 'old_value': 46888.0, 'new_value': 47888.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 06:00:39,683 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMLB
2025-06-08 06:00:40,105 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMLB
2025-06-08 06:00:40,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13533.0, 'new_value': 14417.0}, {'field': 'total_amount', 'old_value': 13533.0, 'new_value': 14417.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 38}]
2025-06-08 06:00:40,105 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMMB
2025-06-08 06:00:40,652 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMMB
2025-06-08 06:00:40,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8429.7, 'new_value': 9408.7}, {'field': 'total_amount', 'old_value': 8429.7, 'new_value': 9408.7}, {'field': 'order_count', 'old_value': 47, 'new_value': 55}]
2025-06-08 06:00:40,652 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMNB
2025-06-08 06:00:41,074 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMNB
2025-06-08 06:00:41,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39644.0, 'new_value': 47948.0}, {'field': 'total_amount', 'old_value': 39644.0, 'new_value': 47948.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 178}]
2025-06-08 06:00:41,074 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY5
2025-06-08 06:00:41,495 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY5
2025-06-08 06:00:41,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26909.14, 'new_value': 32200.09}, {'field': 'offline_amount', 'old_value': 13926.1, 'new_value': 17561.1}, {'field': 'total_amount', 'old_value': 40835.24, 'new_value': 49761.19}, {'field': 'order_count', 'old_value': 2430, 'new_value': 2960}]
2025-06-08 06:00:41,495 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMOB
2025-06-08 06:00:41,917 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMOB
2025-06-08 06:00:41,917 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12693.28, 'new_value': 15517.05}, {'field': 'offline_amount', 'old_value': 21191.43, 'new_value': 26506.57}, {'field': 'total_amount', 'old_value': 33884.71, 'new_value': 42023.62}, {'field': 'order_count', 'old_value': 1226, 'new_value': 1527}]
2025-06-08 06:00:41,917 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMPB
2025-06-08 06:00:42,292 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMPB
2025-06-08 06:00:42,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25513.44, 'new_value': 32607.25}, {'field': 'total_amount', 'old_value': 25513.44, 'new_value': 32607.25}, {'field': 'order_count', 'old_value': 910, 'new_value': 1165}]
2025-06-08 06:00:42,292 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM16
2025-06-08 06:00:42,729 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM16
2025-06-08 06:00:42,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89839.69, 'new_value': 114492.89}, {'field': 'total_amount', 'old_value': 89839.69, 'new_value': 114492.89}, {'field': 'order_count', 'old_value': 717, 'new_value': 836}]
2025-06-08 06:00:42,729 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMQB
2025-06-08 06:00:43,183 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMQB
2025-06-08 06:00:43,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3542.6, 'new_value': 4019.6}, {'field': 'total_amount', 'old_value': 3542.6, 'new_value': 4019.6}, {'field': 'order_count', 'old_value': 26, 'new_value': 32}]
2025-06-08 06:00:43,183 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM26
2025-06-08 06:00:43,651 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM26
2025-06-08 06:00:43,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9749.24, 'new_value': 11634.3}, {'field': 'offline_amount', 'old_value': 6187.26, 'new_value': 7895.0}, {'field': 'total_amount', 'old_value': 15936.5, 'new_value': 19529.3}, {'field': 'order_count', 'old_value': 629, 'new_value': 770}]
2025-06-08 06:00:43,651 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM36
2025-06-08 06:00:44,104 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM36
2025-06-08 06:00:44,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4515.16, 'new_value': 5571.09}, {'field': 'offline_amount', 'old_value': 3367.9, 'new_value': 4019.45}, {'field': 'total_amount', 'old_value': 7883.06, 'new_value': 9590.54}, {'field': 'order_count', 'old_value': 542, 'new_value': 707}]
2025-06-08 06:00:44,104 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMRB
2025-06-08 06:00:44,542 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMRB
2025-06-08 06:00:44,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77372.0, 'new_value': 89256.0}, {'field': 'total_amount', 'old_value': 112372.0, 'new_value': 124256.0}, {'field': 'order_count', 'old_value': 1041, 'new_value': 1192}]
2025-06-08 06:00:44,542 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM46
2025-06-08 06:00:44,948 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM46
2025-06-08 06:00:44,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192052.92, 'new_value': 237939.38}, {'field': 'total_amount', 'old_value': 192052.92, 'new_value': 237939.38}, {'field': 'order_count', 'old_value': 3078, 'new_value': 3856}]
2025-06-08 06:00:44,948 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM56
2025-06-08 06:00:45,401 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM56
2025-06-08 06:00:45,401 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11973.72, 'new_value': 14279.2}, {'field': 'offline_amount', 'old_value': 84000.51, 'new_value': 101015.58}, {'field': 'total_amount', 'old_value': 95974.23, 'new_value': 115294.78}, {'field': 'order_count', 'old_value': 2109, 'new_value': 2428}]
2025-06-08 06:00:45,401 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC6
2025-06-08 06:00:45,901 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC6
2025-06-08 06:00:45,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23923.28, 'new_value': 26765.48}, {'field': 'offline_amount', 'old_value': 75749.75, 'new_value': 97542.09}, {'field': 'total_amount', 'old_value': 99673.03, 'new_value': 124307.57}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1383}]
2025-06-08 06:00:45,901 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMSB
2025-06-08 06:00:46,338 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMSB
2025-06-08 06:00:46,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2593.9, 'new_value': 2965.7}, {'field': 'offline_amount', 'old_value': 8039.93, 'new_value': 9810.79}, {'field': 'total_amount', 'old_value': 10633.83, 'new_value': 12776.49}, {'field': 'order_count', 'old_value': 381, 'new_value': 458}]
2025-06-08 06:00:46,338 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMUB
2025-06-08 06:00:46,791 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMUB
2025-06-08 06:00:46,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18785.6, 'new_value': 23024.74}, {'field': 'offline_amount', 'old_value': 8492.09, 'new_value': 10357.93}, {'field': 'total_amount', 'old_value': 27277.69, 'new_value': 33382.67}, {'field': 'order_count', 'old_value': 1702, 'new_value': 2095}]
2025-06-08 06:00:46,791 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBME6
2025-06-08 06:00:47,244 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBME6
2025-06-08 06:00:47,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118751.48, 'new_value': 147353.23}, {'field': 'total_amount', 'old_value': 118751.48, 'new_value': 147353.23}, {'field': 'order_count', 'old_value': 1636, 'new_value': 2038}]
2025-06-08 06:00:47,244 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMG6
2025-06-08 06:00:47,728 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMG6
2025-06-08 06:00:47,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59197.66, 'new_value': 73338.3}, {'field': 'total_amount', 'old_value': 67813.19, 'new_value': 81953.83}, {'field': 'order_count', 'old_value': 2966, 'new_value': 3554}]
2025-06-08 06:00:47,728 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMVB
2025-06-08 06:00:48,166 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMVB
2025-06-08 06:00:48,166 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16430.89, 'new_value': 20866.84}, {'field': 'offline_amount', 'old_value': 20384.96, 'new_value': 24821.57}, {'field': 'total_amount', 'old_value': 36815.85, 'new_value': 45688.41}, {'field': 'order_count', 'old_value': 1549, 'new_value': 1968}]
2025-06-08 06:00:48,166 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMJ6
2025-06-08 06:00:48,603 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMJ6
2025-06-08 06:00:48,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2501.25, 'new_value': 3064.96}, {'field': 'offline_amount', 'old_value': 8212.9, 'new_value': 10712.3}, {'field': 'total_amount', 'old_value': 10714.15, 'new_value': 13777.26}, {'field': 'order_count', 'old_value': 446, 'new_value': 574}]
2025-06-08 06:00:48,603 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBML6
2025-06-08 06:00:48,963 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBML6
2025-06-08 06:00:48,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4665.0, 'new_value': 6460.0}, {'field': 'total_amount', 'old_value': 4665.0, 'new_value': 6460.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 78}]
2025-06-08 06:00:48,963 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMM6
2025-06-08 06:00:49,462 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMM6
2025-06-08 06:00:49,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15337.6, 'new_value': 20156.8}, {'field': 'offline_amount', 'old_value': 61813.2, 'new_value': 70189.0}, {'field': 'total_amount', 'old_value': 77150.8, 'new_value': 90345.8}, {'field': 'order_count', 'old_value': 1555, 'new_value': 1819}]
2025-06-08 06:00:49,462 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMN6
2025-06-08 06:00:49,931 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMN6
2025-06-08 06:00:49,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15175.78, 'new_value': 17993.64}, {'field': 'offline_amount', 'old_value': 57169.33, 'new_value': 73890.51}, {'field': 'total_amount', 'old_value': 72345.11, 'new_value': 91884.15}, {'field': 'order_count', 'old_value': 1549, 'new_value': 1898}]
2025-06-08 06:00:49,931 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMO6
2025-06-08 06:00:50,400 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMO6
2025-06-08 06:00:50,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 989.74, 'new_value': 1180.64}, {'field': 'offline_amount', 'old_value': 93366.09, 'new_value': 114954.39}, {'field': 'total_amount', 'old_value': 94355.83, 'new_value': 116135.03}, {'field': 'order_count', 'old_value': 4513, 'new_value': 5663}]
2025-06-08 06:00:50,400 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMWB
2025-06-08 06:00:50,947 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMWB
2025-06-08 06:00:50,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5918.0, 'new_value': 7117.0}, {'field': 'total_amount', 'old_value': 5918.0, 'new_value': 7117.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 25}]
2025-06-08 06:00:50,947 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMXB
2025-06-08 06:00:51,400 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMXB
2025-06-08 06:00:51,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6522.88, 'new_value': 6964.72}, {'field': 'offline_amount', 'old_value': 55073.0, 'new_value': 68274.0}, {'field': 'total_amount', 'old_value': 61595.88, 'new_value': 75238.72}, {'field': 'order_count', 'old_value': 2024, 'new_value': 2483}]
2025-06-08 06:00:51,400 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMT6
2025-06-08 06:00:51,837 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMT6
2025-06-08 06:00:51,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27765.44, 'new_value': 32638.98}, {'field': 'offline_amount', 'old_value': 59889.12, 'new_value': 75029.23}, {'field': 'total_amount', 'old_value': 87654.56, 'new_value': 107668.21}, {'field': 'order_count', 'old_value': 3028, 'new_value': 3704}]
2025-06-08 06:00:51,837 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMYB
2025-06-08 06:00:52,306 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMYB
2025-06-08 06:00:52,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32199.0, 'new_value': 40609.0}, {'field': 'total_amount', 'old_value': 32199.0, 'new_value': 40609.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-08 06:00:52,306 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY6
2025-06-08 06:00:52,774 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY6
2025-06-08 06:00:52,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3755.0, 'new_value': 5608.0}, {'field': 'total_amount', 'old_value': 3755.0, 'new_value': 5608.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-06-08 06:00:52,774 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ6
2025-06-08 06:00:53,227 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ6
2025-06-08 06:00:53,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39054.1, 'new_value': 47394.0}, {'field': 'total_amount', 'old_value': 39054.1, 'new_value': 47394.0}, {'field': 'order_count', 'old_value': 1770, 'new_value': 2146}]
2025-06-08 06:00:53,227 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM17
2025-06-08 06:00:53,649 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM17
2025-06-08 06:00:53,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52837.02, 'new_value': 64948.43}, {'field': 'total_amount', 'old_value': 52837.02, 'new_value': 64948.43}, {'field': 'order_count', 'old_value': 372, 'new_value': 470}]
2025-06-08 06:00:53,649 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMZB
2025-06-08 06:00:54,165 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMZB
2025-06-08 06:00:54,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87113.33, 'new_value': 104088.9}, {'field': 'total_amount', 'old_value': 87113.33, 'new_value': 104088.9}, {'field': 'order_count', 'old_value': 1660, 'new_value': 2058}]
2025-06-08 06:00:54,165 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM27
2025-06-08 06:00:54,602 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM27
2025-06-08 06:00:54,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111815.51, 'new_value': 135232.14}, {'field': 'total_amount', 'old_value': 111815.51, 'new_value': 135232.14}, {'field': 'order_count', 'old_value': 4312, 'new_value': 5311}]
2025-06-08 06:00:54,602 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM0C
2025-06-08 06:00:55,024 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM0C
2025-06-08 06:00:55,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11644.07, 'new_value': 14960.7}, {'field': 'offline_amount', 'old_value': 98618.17, 'new_value': 128247.48}, {'field': 'total_amount', 'old_value': 110262.24, 'new_value': 143208.18}, {'field': 'order_count', 'old_value': 890, 'new_value': 1150}]
2025-06-08 06:00:55,024 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM1C
2025-06-08 06:00:55,446 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM1C
2025-06-08 06:00:55,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81046.78, 'new_value': 98282.07}, {'field': 'offline_amount', 'old_value': 11198.14, 'new_value': 12167.34}, {'field': 'total_amount', 'old_value': 92244.92, 'new_value': 110449.41}, {'field': 'order_count', 'old_value': 3599, 'new_value': 4328}]
2025-06-08 06:00:55,446 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM2C
2025-06-08 06:00:55,852 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM2C
2025-06-08 06:00:55,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37751.0, 'new_value': 43835.0}, {'field': 'total_amount', 'old_value': 37751.0, 'new_value': 43835.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 69}]
2025-06-08 06:00:55,852 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM4C
2025-06-08 06:00:56,320 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM4C
2025-06-08 06:00:56,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 810.0, 'new_value': 1157.0}, {'field': 'offline_amount', 'old_value': 8748.6, 'new_value': 10898.6}, {'field': 'total_amount', 'old_value': 9558.6, 'new_value': 12055.6}, {'field': 'order_count', 'old_value': 328, 'new_value': 412}]
2025-06-08 06:00:56,320 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM5C
2025-06-08 06:00:56,789 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM5C
2025-06-08 06:00:56,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27514.57, 'new_value': 32815.9}, {'field': 'offline_amount', 'old_value': 50970.19, 'new_value': 64341.82}, {'field': 'total_amount', 'old_value': 78484.76, 'new_value': 97157.72}, {'field': 'order_count', 'old_value': 2509, 'new_value': 3028}]
2025-06-08 06:00:56,789 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM6C
2025-06-08 06:00:57,273 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM6C
2025-06-08 06:00:57,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51932.08, 'new_value': 60794.38}, {'field': 'offline_amount', 'old_value': 77151.4, 'new_value': 98961.26}, {'field': 'total_amount', 'old_value': 129083.48, 'new_value': 159755.64}, {'field': 'order_count', 'old_value': 3994, 'new_value': 4799}]
2025-06-08 06:00:57,273 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7C
2025-06-08 06:00:57,680 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7C
2025-06-08 06:00:57,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33770.39, 'new_value': 46614.52}, {'field': 'total_amount', 'old_value': 43260.95, 'new_value': 56105.08}, {'field': 'order_count', 'old_value': 2411, 'new_value': 3084}]
2025-06-08 06:00:57,680 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9C
2025-06-08 06:00:58,101 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9C
2025-06-08 06:00:58,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19666.66, 'new_value': 23951.95}, {'field': 'offline_amount', 'old_value': 33921.2, 'new_value': 41380.5}, {'field': 'total_amount', 'old_value': 53587.86, 'new_value': 65332.45}, {'field': 'order_count', 'old_value': 572, 'new_value': 691}]
2025-06-08 06:00:58,101 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5O
2025-06-08 06:00:58,554 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5O
2025-06-08 06:00:58,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23934.64, 'new_value': 29479.25}, {'field': 'total_amount', 'old_value': 23934.64, 'new_value': 29479.25}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1819}]
2025-06-08 06:00:58,570 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIP
2025-06-08 06:00:58,976 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIP
2025-06-08 06:00:58,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14849.7, 'new_value': 17334.5}, {'field': 'total_amount', 'old_value': 14849.7, 'new_value': 17334.5}, {'field': 'order_count', 'old_value': 114, 'new_value': 133}]
2025-06-08 06:00:58,992 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLP
2025-06-08 06:00:59,476 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLP
2025-06-08 06:00:59,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23619.0, 'new_value': 27356.0}, {'field': 'total_amount', 'old_value': 23619.0, 'new_value': 27356.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 249}]
2025-06-08 06:00:59,476 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQP
2025-06-08 06:00:59,945 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQP
2025-06-08 06:00:59,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10643.4, 'new_value': 13403.4}, {'field': 'total_amount', 'old_value': 10643.4, 'new_value': 13403.4}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-06-08 06:00:59,945 - INFO - 日期 2025-06 处理完成 - 更新: 57 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:59,945 - INFO - 数据同步完成！更新: 57 条，插入: 0 条，错误: 0 条
2025-06-08 06:00:59,945 - INFO - =================同步完成====================
2025-06-08 09:00:02,611 - INFO - =================使用默认全量同步=============
2025-06-08 09:00:04,204 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 09:00:04,204 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 09:00:04,236 - INFO - 开始处理日期: 2025-01
2025-06-08 09:00:04,251 - INFO - Request Parameters - Page 1:
2025-06-08 09:00:04,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:04,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:05,720 - INFO - Response - Page 1:
2025-06-08 09:00:05,923 - INFO - 第 1 页获取到 100 条记录
2025-06-08 09:00:05,923 - INFO - Request Parameters - Page 2:
2025-06-08 09:00:05,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:05,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:06,532 - INFO - Response - Page 2:
2025-06-08 09:00:06,736 - INFO - 第 2 页获取到 100 条记录
2025-06-08 09:00:06,736 - INFO - Request Parameters - Page 3:
2025-06-08 09:00:06,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:06,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:07,236 - INFO - Response - Page 3:
2025-06-08 09:00:07,439 - INFO - 第 3 页获取到 100 条记录
2025-06-08 09:00:07,439 - INFO - Request Parameters - Page 4:
2025-06-08 09:00:07,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:07,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:07,954 - INFO - Response - Page 4:
2025-06-08 09:00:08,157 - INFO - 第 4 页获取到 100 条记录
2025-06-08 09:00:08,157 - INFO - Request Parameters - Page 5:
2025-06-08 09:00:08,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:08,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:08,611 - INFO - Response - Page 5:
2025-06-08 09:00:08,814 - INFO - 第 5 页获取到 100 条记录
2025-06-08 09:00:08,814 - INFO - Request Parameters - Page 6:
2025-06-08 09:00:08,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:08,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:09,314 - INFO - Response - Page 6:
2025-06-08 09:00:09,517 - INFO - 第 6 页获取到 100 条记录
2025-06-08 09:00:09,517 - INFO - Request Parameters - Page 7:
2025-06-08 09:00:09,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:09,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:10,079 - INFO - Response - Page 7:
2025-06-08 09:00:10,282 - INFO - 第 7 页获取到 82 条记录
2025-06-08 09:00:10,282 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 09:00:10,282 - INFO - 获取到 682 条表单数据
2025-06-08 09:00:10,282 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 09:00:10,298 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 09:00:10,298 - INFO - 开始处理日期: 2025-02
2025-06-08 09:00:10,298 - INFO - Request Parameters - Page 1:
2025-06-08 09:00:10,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:10,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:10,892 - INFO - Response - Page 1:
2025-06-08 09:00:11,095 - INFO - 第 1 页获取到 100 条记录
2025-06-08 09:00:11,095 - INFO - Request Parameters - Page 2:
2025-06-08 09:00:11,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:11,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:11,611 - INFO - Response - Page 2:
2025-06-08 09:00:11,814 - INFO - 第 2 页获取到 100 条记录
2025-06-08 09:00:11,814 - INFO - Request Parameters - Page 3:
2025-06-08 09:00:11,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:11,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:12,298 - INFO - Response - Page 3:
2025-06-08 09:00:12,501 - INFO - 第 3 页获取到 100 条记录
2025-06-08 09:00:12,501 - INFO - Request Parameters - Page 4:
2025-06-08 09:00:12,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:12,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:13,017 - INFO - Response - Page 4:
2025-06-08 09:00:13,220 - INFO - 第 4 页获取到 100 条记录
2025-06-08 09:00:13,220 - INFO - Request Parameters - Page 5:
2025-06-08 09:00:13,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:13,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:13,720 - INFO - Response - Page 5:
2025-06-08 09:00:13,923 - INFO - 第 5 页获取到 100 条记录
2025-06-08 09:00:13,923 - INFO - Request Parameters - Page 6:
2025-06-08 09:00:13,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:13,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:14,470 - INFO - Response - Page 6:
2025-06-08 09:00:14,673 - INFO - 第 6 页获取到 100 条记录
2025-06-08 09:00:14,673 - INFO - Request Parameters - Page 7:
2025-06-08 09:00:14,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:14,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:15,189 - INFO - Response - Page 7:
2025-06-08 09:00:15,392 - INFO - 第 7 页获取到 70 条记录
2025-06-08 09:00:15,392 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 09:00:15,392 - INFO - 获取到 670 条表单数据
2025-06-08 09:00:15,392 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 09:00:15,407 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 09:00:15,407 - INFO - 开始处理日期: 2025-03
2025-06-08 09:00:15,407 - INFO - Request Parameters - Page 1:
2025-06-08 09:00:15,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:15,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:15,939 - INFO - Response - Page 1:
2025-06-08 09:00:16,142 - INFO - 第 1 页获取到 100 条记录
2025-06-08 09:00:16,142 - INFO - Request Parameters - Page 2:
2025-06-08 09:00:16,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:16,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:16,673 - INFO - Response - Page 2:
2025-06-08 09:00:16,876 - INFO - 第 2 页获取到 100 条记录
2025-06-08 09:00:16,876 - INFO - Request Parameters - Page 3:
2025-06-08 09:00:16,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:16,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:17,376 - INFO - Response - Page 3:
2025-06-08 09:00:17,579 - INFO - 第 3 页获取到 100 条记录
2025-06-08 09:00:17,579 - INFO - Request Parameters - Page 4:
2025-06-08 09:00:17,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:17,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:18,001 - INFO - Response - Page 4:
2025-06-08 09:00:18,204 - INFO - 第 4 页获取到 100 条记录
2025-06-08 09:00:18,204 - INFO - Request Parameters - Page 5:
2025-06-08 09:00:18,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:18,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:18,689 - INFO - Response - Page 5:
2025-06-08 09:00:18,892 - INFO - 第 5 页获取到 100 条记录
2025-06-08 09:00:18,892 - INFO - Request Parameters - Page 6:
2025-06-08 09:00:18,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:18,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:19,376 - INFO - Response - Page 6:
2025-06-08 09:00:19,579 - INFO - 第 6 页获取到 100 条记录
2025-06-08 09:00:19,579 - INFO - Request Parameters - Page 7:
2025-06-08 09:00:19,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:19,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:20,032 - INFO - Response - Page 7:
2025-06-08 09:00:20,235 - INFO - 第 7 页获取到 61 条记录
2025-06-08 09:00:20,235 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 09:00:20,235 - INFO - 获取到 661 条表单数据
2025-06-08 09:00:20,235 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 09:00:20,251 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 09:00:20,251 - INFO - 开始处理日期: 2025-04
2025-06-08 09:00:20,251 - INFO - Request Parameters - Page 1:
2025-06-08 09:00:20,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:20,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:20,798 - INFO - Response - Page 1:
2025-06-08 09:00:21,001 - INFO - 第 1 页获取到 100 条记录
2025-06-08 09:00:21,001 - INFO - Request Parameters - Page 2:
2025-06-08 09:00:21,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:21,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:21,501 - INFO - Response - Page 2:
2025-06-08 09:00:21,704 - INFO - 第 2 页获取到 100 条记录
2025-06-08 09:00:21,704 - INFO - Request Parameters - Page 3:
2025-06-08 09:00:21,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:21,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:22,220 - INFO - Response - Page 3:
2025-06-08 09:00:22,423 - INFO - 第 3 页获取到 100 条记录
2025-06-08 09:00:22,423 - INFO - Request Parameters - Page 4:
2025-06-08 09:00:22,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:22,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:22,970 - INFO - Response - Page 4:
2025-06-08 09:00:23,173 - INFO - 第 4 页获取到 100 条记录
2025-06-08 09:00:23,173 - INFO - Request Parameters - Page 5:
2025-06-08 09:00:23,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:23,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:23,657 - INFO - Response - Page 5:
2025-06-08 09:00:23,860 - INFO - 第 5 页获取到 100 条记录
2025-06-08 09:00:23,860 - INFO - Request Parameters - Page 6:
2025-06-08 09:00:23,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:23,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:24,298 - INFO - Response - Page 6:
2025-06-08 09:00:24,501 - INFO - 第 6 页获取到 100 条记录
2025-06-08 09:00:24,501 - INFO - Request Parameters - Page 7:
2025-06-08 09:00:24,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:24,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:24,939 - INFO - Response - Page 7:
2025-06-08 09:00:25,157 - INFO - 第 7 页获取到 56 条记录
2025-06-08 09:00:25,157 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 09:00:25,157 - INFO - 获取到 656 条表单数据
2025-06-08 09:00:25,157 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 09:00:25,173 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 09:00:25,173 - INFO - 开始处理日期: 2025-05
2025-06-08 09:00:25,173 - INFO - Request Parameters - Page 1:
2025-06-08 09:00:25,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:25,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:25,735 - INFO - Response - Page 1:
2025-06-08 09:00:25,939 - INFO - 第 1 页获取到 100 条记录
2025-06-08 09:00:25,939 - INFO - Request Parameters - Page 2:
2025-06-08 09:00:25,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:25,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:26,423 - INFO - Response - Page 2:
2025-06-08 09:00:26,626 - INFO - 第 2 页获取到 100 条记录
2025-06-08 09:00:26,626 - INFO - Request Parameters - Page 3:
2025-06-08 09:00:26,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:26,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:27,079 - INFO - Response - Page 3:
2025-06-08 09:00:27,282 - INFO - 第 3 页获取到 100 条记录
2025-06-08 09:00:27,282 - INFO - Request Parameters - Page 4:
2025-06-08 09:00:27,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:27,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:27,735 - INFO - Response - Page 4:
2025-06-08 09:00:27,939 - INFO - 第 4 页获取到 100 条记录
2025-06-08 09:00:27,939 - INFO - Request Parameters - Page 5:
2025-06-08 09:00:27,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:27,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:28,407 - INFO - Response - Page 5:
2025-06-08 09:00:28,610 - INFO - 第 5 页获取到 100 条记录
2025-06-08 09:00:28,610 - INFO - Request Parameters - Page 6:
2025-06-08 09:00:28,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:28,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:29,110 - INFO - Response - Page 6:
2025-06-08 09:00:29,314 - INFO - 第 6 页获取到 100 条记录
2025-06-08 09:00:29,314 - INFO - Request Parameters - Page 7:
2025-06-08 09:00:29,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:29,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:29,720 - INFO - Response - Page 7:
2025-06-08 09:00:29,923 - INFO - 第 7 页获取到 38 条记录
2025-06-08 09:00:29,923 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 09:00:29,923 - INFO - 获取到 638 条表单数据
2025-06-08 09:00:29,923 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 09:00:29,939 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 09:00:29,939 - INFO - 开始处理日期: 2025-06
2025-06-08 09:00:29,939 - INFO - Request Parameters - Page 1:
2025-06-08 09:00:29,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:29,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:30,407 - INFO - Response - Page 1:
2025-06-08 09:00:30,610 - INFO - 第 1 页获取到 100 条记录
2025-06-08 09:00:30,610 - INFO - Request Parameters - Page 2:
2025-06-08 09:00:30,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:30,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:31,064 - INFO - Response - Page 2:
2025-06-08 09:00:31,267 - INFO - 第 2 页获取到 100 条记录
2025-06-08 09:00:31,267 - INFO - Request Parameters - Page 3:
2025-06-08 09:00:31,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:31,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:31,720 - INFO - Response - Page 3:
2025-06-08 09:00:31,923 - INFO - 第 3 页获取到 100 条记录
2025-06-08 09:00:31,923 - INFO - Request Parameters - Page 4:
2025-06-08 09:00:31,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:31,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:32,407 - INFO - Response - Page 4:
2025-06-08 09:00:32,610 - INFO - 第 4 页获取到 100 条记录
2025-06-08 09:00:32,610 - INFO - Request Parameters - Page 5:
2025-06-08 09:00:32,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:32,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:33,157 - INFO - Response - Page 5:
2025-06-08 09:00:33,360 - INFO - 第 5 页获取到 100 条记录
2025-06-08 09:00:33,360 - INFO - Request Parameters - Page 6:
2025-06-08 09:00:33,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:33,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:33,829 - INFO - Response - Page 6:
2025-06-08 09:00:34,032 - INFO - 第 6 页获取到 100 条记录
2025-06-08 09:00:34,032 - INFO - Request Parameters - Page 7:
2025-06-08 09:00:34,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 09:00:34,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 09:00:34,329 - INFO - Response - Page 7:
2025-06-08 09:00:34,532 - INFO - 第 7 页获取到 19 条记录
2025-06-08 09:00:34,532 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 09:00:34,532 - INFO - 获取到 619 条表单数据
2025-06-08 09:00:34,532 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 09:00:34,532 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMZ4
2025-06-08 09:00:34,985 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMZ4
2025-06-08 09:00:34,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3190.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3190.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 09:00:34,985 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMP5
2025-06-08 09:00:35,438 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMP5
2025-06-08 09:00:35,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 754700.0, 'new_value': 1053600.0}, {'field': 'total_amount', 'old_value': 754700.0, 'new_value': 1053600.0}]
2025-06-08 09:00:35,438 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMT5
2025-06-08 09:00:35,923 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMT5
2025-06-08 09:00:35,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6398.0, 'new_value': 7429.0}, {'field': 'offline_amount', 'old_value': 12347.74, 'new_value': 13171.74}, {'field': 'total_amount', 'old_value': 18745.74, 'new_value': 20600.74}, {'field': 'order_count', 'old_value': 34, 'new_value': 39}]
2025-06-08 09:00:35,923 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX5
2025-06-08 09:00:36,329 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX5
2025-06-08 09:00:36,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2300.0, 'new_value': 2700.0}, {'field': 'offline_amount', 'old_value': 1625.0, 'new_value': 1928.0}, {'field': 'total_amount', 'old_value': 3925.0, 'new_value': 4628.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 71}]
2025-06-08 09:00:36,329 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ5
2025-06-08 09:00:36,782 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ5
2025-06-08 09:00:36,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5699.02, 'new_value': 6871.09}, {'field': 'offline_amount', 'old_value': 7054.05, 'new_value': 8602.38}, {'field': 'total_amount', 'old_value': 12753.07, 'new_value': 15473.47}, {'field': 'order_count', 'old_value': 630, 'new_value': 762}]
2025-06-08 09:00:36,782 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM16
2025-06-08 09:00:37,220 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM16
2025-06-08 09:00:37,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27158.0, 'new_value': 43957.0}, {'field': 'total_amount', 'old_value': 27158.0, 'new_value': 43957.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 17}]
2025-06-08 09:00:37,220 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM46
2025-06-08 09:00:37,688 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM46
2025-06-08 09:00:37,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2503.71, 'new_value': 2890.56}, {'field': 'offline_amount', 'old_value': 35712.09, 'new_value': 41645.57}, {'field': 'total_amount', 'old_value': 38215.8, 'new_value': 44536.13}, {'field': 'order_count', 'old_value': 436, 'new_value': 501}]
2025-06-08 09:00:37,688 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM15
2025-06-08 09:00:38,079 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM15
2025-06-08 09:00:38,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191267.0, 'new_value': 233101.0}, {'field': 'total_amount', 'old_value': 191267.0, 'new_value': 233101.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 52}]
2025-06-08 09:00:38,079 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM76
2025-06-08 09:00:38,548 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM76
2025-06-08 09:00:38,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32411.46, 'new_value': 37619.61}, {'field': 'total_amount', 'old_value': 32411.46, 'new_value': 37619.61}, {'field': 'order_count', 'old_value': 148, 'new_value': 170}]
2025-06-08 09:00:38,548 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM86
2025-06-08 09:00:39,110 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM86
2025-06-08 09:00:39,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5459.0, 'new_value': 6262.0}, {'field': 'total_amount', 'old_value': 5459.0, 'new_value': 6262.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 32}]
2025-06-08 09:00:39,110 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM96
2025-06-08 09:00:39,532 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM96
2025-06-08 09:00:39,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42836.0, 'new_value': 49112.0}, {'field': 'total_amount', 'old_value': 42836.0, 'new_value': 49112.0}, {'field': 'order_count', 'old_value': 1644, 'new_value': 1859}]
2025-06-08 09:00:39,532 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB6
2025-06-08 09:00:39,985 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB6
2025-06-08 09:00:39,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35234.0, 'new_value': 43923.0}, {'field': 'offline_amount', 'old_value': 16238.66, 'new_value': 17962.56}, {'field': 'total_amount', 'old_value': 51472.66, 'new_value': 61885.56}, {'field': 'order_count', 'old_value': 359, 'new_value': 431}]
2025-06-08 09:00:39,985 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC6
2025-06-08 09:00:40,595 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC6
2025-06-08 09:00:40,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34862.14, 'new_value': 39283.8}, {'field': 'total_amount', 'old_value': 34862.14, 'new_value': 39283.8}, {'field': 'order_count', 'old_value': 1208, 'new_value': 1362}]
2025-06-08 09:00:40,595 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF6
2025-06-08 09:00:41,032 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF6
2025-06-08 09:00:41,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3191.91, 'new_value': 3796.3}, {'field': 'offline_amount', 'old_value': 94295.25, 'new_value': 113087.84}, {'field': 'total_amount', 'old_value': 97487.16, 'new_value': 116884.14}, {'field': 'order_count', 'old_value': 451, 'new_value': 535}]
2025-06-08 09:00:41,032 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM55
2025-06-08 09:00:41,485 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM55
2025-06-08 09:00:41,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18733.82, 'new_value': 20970.19}, {'field': 'offline_amount', 'old_value': 21266.34, 'new_value': 27763.05}, {'field': 'total_amount', 'old_value': 40000.16, 'new_value': 48733.24}, {'field': 'order_count', 'old_value': 1322, 'new_value': 1592}]
2025-06-08 09:00:41,485 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG6
2025-06-08 09:00:41,954 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG6
2025-06-08 09:00:41,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37549.0, 'new_value': 43784.0}, {'field': 'total_amount', 'old_value': 37549.0, 'new_value': 43784.0}, {'field': 'order_count', 'old_value': 938, 'new_value': 1094}]
2025-06-08 09:00:41,954 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI6
2025-06-08 09:00:42,438 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI6
2025-06-08 09:00:42,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18267.04, 'new_value': 21940.74}, {'field': 'total_amount', 'old_value': 18267.04, 'new_value': 21940.74}, {'field': 'order_count', 'old_value': 84, 'new_value': 94}]
2025-06-08 09:00:42,438 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ6
2025-06-08 09:00:42,876 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ6
2025-06-08 09:00:42,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3349.08, 'new_value': 3898.88}, {'field': 'offline_amount', 'old_value': 6453.03, 'new_value': 7164.01}, {'field': 'total_amount', 'old_value': 9802.11, 'new_value': 11062.89}, {'field': 'order_count', 'old_value': 308, 'new_value': 351}]
2025-06-08 09:00:42,876 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM75
2025-06-08 09:00:43,360 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM75
2025-06-08 09:00:43,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32169.0, 'new_value': 33969.0}, {'field': 'total_amount', 'old_value': 32819.0, 'new_value': 34619.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-08 09:00:43,360 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM85
2025-06-08 09:00:43,813 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM85
2025-06-08 09:00:43,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 579.0, 'new_value': 614.0}, {'field': 'offline_amount', 'old_value': 227796.0, 'new_value': 245972.0}, {'field': 'total_amount', 'old_value': 228375.0, 'new_value': 246586.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 65}]
2025-06-08 09:00:43,813 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML6
2025-06-08 09:00:44,235 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML6
2025-06-08 09:00:44,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102482.7, 'new_value': 127037.1}, {'field': 'offline_amount', 'old_value': 21817.0, 'new_value': 28969.0}, {'field': 'total_amount', 'old_value': 124299.7, 'new_value': 156006.1}, {'field': 'order_count', 'old_value': 157, 'new_value': 190}]
2025-06-08 09:00:44,235 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM6
2025-06-08 09:00:44,704 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM6
2025-06-08 09:00:44,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4755.0, 'new_value': 5013.0}, {'field': 'total_amount', 'old_value': 4755.0, 'new_value': 5013.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 263}]
2025-06-08 09:00:44,704 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO6
2025-06-08 09:00:45,126 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO6
2025-06-08 09:00:45,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22830.0, 'new_value': 29858.0}, {'field': 'total_amount', 'old_value': 22830.0, 'new_value': 29858.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-06-08 09:00:45,126 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMB5
2025-06-08 09:00:45,563 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMB5
2025-06-08 09:00:45,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59435.0, 'new_value': 64029.0}, {'field': 'total_amount', 'old_value': 59435.0, 'new_value': 64029.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-06-08 09:00:45,563 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMD5
2025-06-08 09:00:46,048 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMD5
2025-06-08 09:00:46,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44549.5, 'new_value': 51481.5}, {'field': 'total_amount', 'old_value': 44549.5, 'new_value': 51481.5}, {'field': 'order_count', 'old_value': 12, 'new_value': 16}]
2025-06-08 09:00:46,048 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ6
2025-06-08 09:00:46,454 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ6
2025-06-08 09:00:46,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92338.22, 'new_value': 110444.29}, {'field': 'total_amount', 'old_value': 92338.22, 'new_value': 110444.29}, {'field': 'order_count', 'old_value': 294, 'new_value': 347}]
2025-06-08 09:00:46,454 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR6
2025-06-08 09:00:46,938 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR6
2025-06-08 09:00:46,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15570.0, 'new_value': 16797.0}, {'field': 'total_amount', 'old_value': 15570.0, 'new_value': 16797.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-06-08 09:00:46,938 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBME5
2025-06-08 09:00:47,438 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBME5
2025-06-08 09:00:47,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7642.0, 'new_value': 9440.0}, {'field': 'total_amount', 'old_value': 7642.0, 'new_value': 9440.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-06-08 09:00:47,438 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS6
2025-06-08 09:00:47,876 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS6
2025-06-08 09:00:47,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13019.5, 'new_value': 14886.5}, {'field': 'offline_amount', 'old_value': 8509.7, 'new_value': 9101.1}, {'field': 'total_amount', 'old_value': 21529.2, 'new_value': 23987.6}, {'field': 'order_count', 'old_value': 55, 'new_value': 63}]
2025-06-08 09:00:47,876 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMG5
2025-06-08 09:00:48,313 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMG5
2025-06-08 09:00:48,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13588.0, 'new_value': 24586.0}, {'field': 'total_amount', 'old_value': 13588.0, 'new_value': 24586.0}, {'field': 'order_count', 'old_value': 5591, 'new_value': 5593}]
2025-06-08 09:00:48,313 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT6
2025-06-08 09:00:48,688 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT6
2025-06-08 09:00:48,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4547.0, 'new_value': 5845.0}, {'field': 'offline_amount', 'old_value': 18075.96, 'new_value': 19717.56}, {'field': 'total_amount', 'old_value': 22622.96, 'new_value': 25562.56}, {'field': 'order_count', 'old_value': 257, 'new_value': 285}]
2025-06-08 09:00:48,688 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMH5
2025-06-08 09:00:49,157 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMH5
2025-06-08 09:00:49,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47474.0, 'new_value': 55625.0}, {'field': 'total_amount', 'old_value': 51380.2, 'new_value': 59531.2}, {'field': 'order_count', 'old_value': 22, 'new_value': 28}]
2025-06-08 09:00:49,157 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV6
2025-06-08 09:00:49,610 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV6
2025-06-08 09:00:49,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4581.52, 'new_value': 5682.59}, {'field': 'offline_amount', 'old_value': 11520.6, 'new_value': 13182.2}, {'field': 'total_amount', 'old_value': 16102.12, 'new_value': 18864.79}, {'field': 'order_count', 'old_value': 590, 'new_value': 692}]
2025-06-08 09:00:49,610 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMI5
2025-06-08 09:00:50,095 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMI5
2025-06-08 09:00:50,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12090.0, 'new_value': 21183.0}, {'field': 'total_amount', 'old_value': 14399.0, 'new_value': 23492.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 18}]
2025-06-08 09:00:50,095 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMJ5
2025-06-08 09:00:50,579 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMJ5
2025-06-08 09:00:50,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14119.0, 'new_value': 17032.0}, {'field': 'total_amount', 'old_value': 14119.0, 'new_value': 17032.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 50}]
2025-06-08 09:00:50,579 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW6
2025-06-08 09:00:51,142 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW6
2025-06-08 09:00:51,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4396.0, 'new_value': 5406.0}, {'field': 'total_amount', 'old_value': 4396.0, 'new_value': 5406.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 33}]
2025-06-08 09:00:51,142 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY6
2025-06-08 09:00:51,626 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY6
2025-06-08 09:00:51,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18915.68, 'new_value': 22962.94}, {'field': 'total_amount', 'old_value': 18915.68, 'new_value': 22962.94}, {'field': 'order_count', 'old_value': 513, 'new_value': 620}]
2025-06-08 09:00:51,626 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBML5
2025-06-08 09:00:52,142 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBML5
2025-06-08 09:00:52,142 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9180.98, 'new_value': 11450.75}, {'field': 'offline_amount', 'old_value': 121188.79, 'new_value': 153028.77}, {'field': 'total_amount', 'old_value': 130369.77, 'new_value': 164479.52}, {'field': 'order_count', 'old_value': 648, 'new_value': 795}]
2025-06-08 09:00:52,142 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ6
2025-06-08 09:00:52,735 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ6
2025-06-08 09:00:52,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9597.53, 'new_value': 9789.03}, {'field': 'offline_amount', 'old_value': 13732.45, 'new_value': 16989.57}, {'field': 'total_amount', 'old_value': 23329.98, 'new_value': 26778.6}, {'field': 'order_count', 'old_value': 131, 'new_value': 144}]
2025-06-08 09:00:52,735 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMM5
2025-06-08 09:00:53,298 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMM5
2025-06-08 09:00:53,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39297.0, 'new_value': 49747.0}, {'field': 'total_amount', 'old_value': 39297.0, 'new_value': 49747.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 37}]
2025-06-08 09:00:53,298 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM07
2025-06-08 09:00:53,782 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM07
2025-06-08 09:00:53,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32788.0, 'new_value': 36988.0}, {'field': 'total_amount', 'old_value': 32788.0, 'new_value': 36988.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-08 09:00:53,798 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM17
2025-06-08 09:00:54,391 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM17
2025-06-08 09:00:54,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5719.0, 'new_value': 6107.0}, {'field': 'total_amount', 'old_value': 5719.0, 'new_value': 6107.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-08 09:00:54,391 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM27
2025-06-08 09:00:54,829 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM27
2025-06-08 09:00:54,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8480.0, 'new_value': 26960.0}, {'field': 'total_amount', 'old_value': 8480.0, 'new_value': 26960.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 15}]
2025-06-08 09:00:54,829 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM37
2025-06-08 09:00:55,266 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM37
2025-06-08 09:00:55,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9458.5, 'new_value': 13060.8}, {'field': 'total_amount', 'old_value': 9458.5, 'new_value': 13060.8}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-06-08 09:00:55,266 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMN5
2025-06-08 09:00:55,720 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMN5
2025-06-08 09:00:55,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11574.0, 'new_value': 15083.0}, {'field': 'total_amount', 'old_value': 11574.0, 'new_value': 15083.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 21}]
2025-06-08 09:00:55,720 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM47
2025-06-08 09:00:56,157 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM47
2025-06-08 09:00:56,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42466.42, 'new_value': 50357.7}, {'field': 'offline_amount', 'old_value': 39920.89, 'new_value': 43775.19}, {'field': 'total_amount', 'old_value': 82387.31, 'new_value': 94132.89}, {'field': 'order_count', 'old_value': 697, 'new_value': 804}]
2025-06-08 09:00:56,157 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM57
2025-06-08 09:00:56,610 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM57
2025-06-08 09:00:56,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7100.0, 'new_value': 8080.0}, {'field': 'offline_amount', 'old_value': 24147.0, 'new_value': 24217.0}, {'field': 'total_amount', 'old_value': 31247.0, 'new_value': 32297.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 32}]
2025-06-08 09:00:56,610 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMP5
2025-06-08 09:00:57,110 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMP5
2025-06-08 09:00:57,110 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12506.15, 'new_value': 14600.86}, {'field': 'offline_amount', 'old_value': 80133.78, 'new_value': 100834.37}, {'field': 'total_amount', 'old_value': 92639.93, 'new_value': 115435.23}, {'field': 'order_count', 'old_value': 697, 'new_value': 847}]
2025-06-08 09:00:57,110 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM77
2025-06-08 09:00:57,516 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM77
2025-06-08 09:00:57,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36257.68, 'new_value': 43458.68}, {'field': 'total_amount', 'old_value': 36257.68, 'new_value': 43458.68}, {'field': 'order_count', 'old_value': 938, 'new_value': 1143}]
2025-06-08 09:00:57,516 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMR5
2025-06-08 09:00:58,095 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMR5
2025-06-08 09:00:58,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91024.0, 'new_value': 108197.7}, {'field': 'total_amount', 'old_value': 91024.0, 'new_value': 108197.7}, {'field': 'order_count', 'old_value': 856, 'new_value': 1063}]
2025-06-08 09:00:58,095 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM87
2025-06-08 09:00:58,532 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM87
2025-06-08 09:00:58,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4930.0, 'new_value': 5336.0}, {'field': 'total_amount', 'old_value': 4930.0, 'new_value': 5336.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 92}]
2025-06-08 09:00:58,532 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMS5
2025-06-08 09:00:59,032 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMS5
2025-06-08 09:00:59,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17642.5, 'new_value': 19423.5}, {'field': 'total_amount', 'old_value': 17642.5, 'new_value': 19423.5}, {'field': 'order_count', 'old_value': 46, 'new_value': 51}]
2025-06-08 09:00:59,032 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV5
2025-06-08 09:00:59,470 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV5
2025-06-08 09:00:59,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223508.0, 'new_value': 281672.0}, {'field': 'total_amount', 'old_value': 223508.0, 'new_value': 281672.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 335}]
2025-06-08 09:00:59,470 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB7
2025-06-08 09:00:59,907 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB7
2025-06-08 09:00:59,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3415.0, 'new_value': 3835.0}, {'field': 'total_amount', 'old_value': 3415.0, 'new_value': 3835.0}, {'field': 'order_count', 'old_value': 2377, 'new_value': 2378}]
2025-06-08 09:00:59,907 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW5
2025-06-08 09:01:00,454 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW5
2025-06-08 09:01:00,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19624.0, 'new_value': 27805.0}, {'field': 'total_amount', 'old_value': 19624.0, 'new_value': 27805.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 8}]
2025-06-08 09:01:00,454 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX5
2025-06-08 09:01:00,891 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX5
2025-06-08 09:01:00,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26067.92, 'new_value': 32783.65}, {'field': 'total_amount', 'old_value': 26067.92, 'new_value': 32783.65}, {'field': 'order_count', 'old_value': 2144, 'new_value': 2641}]
2025-06-08 09:01:00,891 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ5
2025-06-08 09:01:01,360 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ5
2025-06-08 09:01:01,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104679.29, 'new_value': 135634.35}, {'field': 'total_amount', 'old_value': 133759.59, 'new_value': 164714.65}, {'field': 'order_count', 'old_value': 492, 'new_value': 598}]
2025-06-08 09:01:01,360 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM06
2025-06-08 09:01:01,829 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM06
2025-06-08 09:01:01,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34489.0, 'new_value': 47887.0}, {'field': 'total_amount', 'old_value': 34490.0, 'new_value': 47888.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-06-08 09:01:01,829 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC7
2025-06-08 09:01:02,313 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC7
2025-06-08 09:01:02,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7820.6, 'new_value': 9643.6}, {'field': 'total_amount', 'old_value': 7820.6, 'new_value': 9643.6}, {'field': 'order_count', 'old_value': 71, 'new_value': 90}]
2025-06-08 09:01:02,313 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD7
2025-06-08 09:01:02,766 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD7
2025-06-08 09:01:02,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11774.2, 'new_value': 14187.6}, {'field': 'total_amount', 'old_value': 11774.2, 'new_value': 14187.6}, {'field': 'order_count', 'old_value': 452, 'new_value': 567}]
2025-06-08 09:01:02,766 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME7
2025-06-08 09:01:03,251 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME7
2025-06-08 09:01:03,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87811.64, 'new_value': 109174.64}, {'field': 'total_amount', 'old_value': 87811.64, 'new_value': 109174.64}, {'field': 'order_count', 'old_value': 446, 'new_value': 556}]
2025-06-08 09:01:03,251 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF7
2025-06-08 09:01:03,751 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF7
2025-06-08 09:01:03,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18187.0, 'new_value': 21146.0}, {'field': 'total_amount', 'old_value': 21002.0, 'new_value': 23961.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 82}]
2025-06-08 09:01:03,751 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG7
2025-06-08 09:01:04,204 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG7
2025-06-08 09:01:04,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55601.8, 'new_value': 64053.77}, {'field': 'offline_amount', 'old_value': 11275.95, 'new_value': 13425.05}, {'field': 'total_amount', 'old_value': 66877.75, 'new_value': 77478.82}, {'field': 'order_count', 'old_value': 247, 'new_value': 293}]
2025-06-08 09:01:04,204 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM66
2025-06-08 09:01:04,688 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM66
2025-06-08 09:01:04,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77580.0, 'new_value': 121918.0}, {'field': 'total_amount', 'old_value': 77580.0, 'new_value': 121918.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 36}]
2025-06-08 09:01:04,688 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM76
2025-06-08 09:01:05,157 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM76
2025-06-08 09:01:05,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5762.38, 'new_value': 6405.04}, {'field': 'offline_amount', 'old_value': 66371.45, 'new_value': 84678.29}, {'field': 'total_amount', 'old_value': 72133.83, 'new_value': 91083.33}, {'field': 'order_count', 'old_value': 344, 'new_value': 439}]
2025-06-08 09:01:05,157 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM86
2025-06-08 09:01:05,563 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM86
2025-06-08 09:01:05,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40861.4, 'new_value': 47183.6}, {'field': 'total_amount', 'old_value': 40861.4, 'new_value': 47183.6}, {'field': 'order_count', 'old_value': 83, 'new_value': 102}]
2025-06-08 09:01:05,563 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI7
2025-06-08 09:01:06,079 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI7
2025-06-08 09:01:06,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16222.0, 'new_value': 24442.0}, {'field': 'total_amount', 'old_value': 16222.0, 'new_value': 24442.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-08 09:01:06,079 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM96
2025-06-08 09:01:06,688 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM96
2025-06-08 09:01:06,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67685.97, 'new_value': 81877.02}, {'field': 'offline_amount', 'old_value': 16025.23, 'new_value': 19258.23}, {'field': 'total_amount', 'old_value': 83711.2, 'new_value': 101135.25}, {'field': 'order_count', 'old_value': 473, 'new_value': 577}]
2025-06-08 09:01:06,688 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA6
2025-06-08 09:01:07,173 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA6
2025-06-08 09:01:07,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4567.0}, {'field': 'total_amount', 'old_value': 35189.0, 'new_value': 39756.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 65}]
2025-06-08 09:01:07,173 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK7
2025-06-08 09:01:07,657 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK7
2025-06-08 09:01:07,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8490.1, 'new_value': 8492.1}, {'field': 'total_amount', 'old_value': 8490.1, 'new_value': 8492.1}, {'field': 'order_count', 'old_value': 23, 'new_value': 856}]
2025-06-08 09:01:07,657 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML7
2025-06-08 09:01:08,173 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML7
2025-06-08 09:01:08,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29506.0, 'new_value': 35629.0}, {'field': 'offline_amount', 'old_value': 120226.0, 'new_value': 136032.0}, {'field': 'total_amount', 'old_value': 149732.0, 'new_value': 171661.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 217}]
2025-06-08 09:01:08,173 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMTB
2025-06-08 09:01:08,626 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMTB
2025-06-08 09:01:08,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7829.08, 'new_value': 10345.7}, {'field': 'offline_amount', 'old_value': 10425.85, 'new_value': 11425.85}, {'field': 'total_amount', 'old_value': 18254.93, 'new_value': 21771.55}, {'field': 'order_count', 'old_value': 843, 'new_value': 1002}]
2025-06-08 09:01:08,626 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN7
2025-06-08 09:01:09,157 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN7
2025-06-08 09:01:09,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98460.5, 'new_value': 98570.5}, {'field': 'total_amount', 'old_value': 98460.5, 'new_value': 98570.5}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-08 09:01:09,157 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO7
2025-06-08 09:01:09,704 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO7
2025-06-08 09:01:09,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372489.8, 'new_value': 460774.4}, {'field': 'total_amount', 'old_value': 430968.5, 'new_value': 519253.1}, {'field': 'order_count', 'old_value': 791, 'new_value': 952}]
2025-06-08 09:01:09,704 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT7
2025-06-08 09:01:10,110 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT7
2025-06-08 09:01:10,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67959.2, 'new_value': 80249.6}, {'field': 'total_amount', 'old_value': 67959.2, 'new_value': 80249.6}, {'field': 'order_count', 'old_value': 2042, 'new_value': 2354}]
2025-06-08 09:01:10,110 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV7
2025-06-08 09:01:10,563 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV7
2025-06-08 09:01:10,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167069.0, 'new_value': 207744.0}, {'field': 'total_amount', 'old_value': 167069.0, 'new_value': 207744.0}, {'field': 'order_count', 'old_value': 808, 'new_value': 961}]
2025-06-08 09:01:10,563 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMI6
2025-06-08 09:01:11,032 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMI6
2025-06-08 09:01:11,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1621.2, 'new_value': 2139.3}, {'field': 'offline_amount', 'old_value': 2900.0, 'new_value': 8900.0}, {'field': 'total_amount', 'old_value': 4521.2, 'new_value': 11039.3}, {'field': 'order_count', 'old_value': 29, 'new_value': 39}]
2025-06-08 09:01:11,032 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW7
2025-06-08 09:01:11,485 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW7
2025-06-08 09:01:11,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108549.0, 'new_value': 122440.0}, {'field': 'total_amount', 'old_value': 108549.0, 'new_value': 122440.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 118}]
2025-06-08 09:01:11,485 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX7
2025-06-08 09:01:12,001 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX7
2025-06-08 09:01:12,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7211.72, 'new_value': 8385.86}, {'field': 'offline_amount', 'old_value': 7934.03, 'new_value': 8733.83}, {'field': 'total_amount', 'old_value': 15145.75, 'new_value': 17119.69}, {'field': 'order_count', 'old_value': 664, 'new_value': 752}]
2025-06-08 09:01:12,001 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY7
2025-06-08 09:01:12,391 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY7
2025-06-08 09:01:12,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32373.98, 'new_value': 37912.52}, {'field': 'total_amount', 'old_value': 32373.98, 'new_value': 37912.52}, {'field': 'order_count', 'old_value': 1694, 'new_value': 1844}]
2025-06-08 09:01:12,391 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMP6
2025-06-08 09:01:12,860 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMP6
2025-06-08 09:01:12,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88850.0, 'new_value': 102445.0}, {'field': 'total_amount', 'old_value': 88850.0, 'new_value': 102445.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-06-08 09:01:12,860 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM08
2025-06-08 09:01:13,313 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM08
2025-06-08 09:01:13,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12894.31, 'new_value': 14796.46}, {'field': 'offline_amount', 'old_value': 8627.15, 'new_value': 9669.25}, {'field': 'total_amount', 'old_value': 21521.46, 'new_value': 24465.71}, {'field': 'order_count', 'old_value': 1196, 'new_value': 1397}]
2025-06-08 09:01:13,313 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMQ6
2025-06-08 09:01:13,813 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMQ6
2025-06-08 09:01:13,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27531.0, 'new_value': 36486.0}, {'field': 'offline_amount', 'old_value': 17739.9, 'new_value': 22543.3}, {'field': 'total_amount', 'old_value': 45270.9, 'new_value': 59029.3}, {'field': 'order_count', 'old_value': 278, 'new_value': 365}]
2025-06-08 09:01:13,813 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM18
2025-06-08 09:01:14,251 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM18
2025-06-08 09:01:14,251 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22316.97, 'new_value': 25713.7}, {'field': 'offline_amount', 'old_value': 22824.57, 'new_value': 26035.79}, {'field': 'total_amount', 'old_value': 45141.54, 'new_value': 51749.49}, {'field': 'order_count', 'old_value': 1867, 'new_value': 2166}]
2025-06-08 09:01:14,251 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMR6
2025-06-08 09:01:14,751 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMR6
2025-06-08 09:01:14,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16484.59, 'new_value': 17051.04}, {'field': 'total_amount', 'old_value': 16484.59, 'new_value': 17051.04}, {'field': 'order_count', 'old_value': 66, 'new_value': 71}]
2025-06-08 09:01:14,751 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM38
2025-06-08 09:01:15,173 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM38
2025-06-08 09:01:15,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77745.77, 'new_value': 94631.82}, {'field': 'offline_amount', 'old_value': 33368.7, 'new_value': 36656.4}, {'field': 'total_amount', 'old_value': 111114.47, 'new_value': 131288.22}, {'field': 'order_count', 'old_value': 348, 'new_value': 419}]
2025-06-08 09:01:15,173 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM58
2025-06-08 09:01:15,594 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM58
2025-06-08 09:01:15,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57096.0, 'new_value': 66192.0}, {'field': 'total_amount', 'old_value': 57096.0, 'new_value': 66192.0}, {'field': 'order_count', 'old_value': 4758, 'new_value': 5516}]
2025-06-08 09:01:15,594 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMS6
2025-06-08 09:01:16,110 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMS6
2025-06-08 09:01:16,110 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51513.26, 'new_value': 65537.02}, {'field': 'offline_amount', 'old_value': 169261.33, 'new_value': 206653.49}, {'field': 'total_amount', 'old_value': 220774.59, 'new_value': 272190.51}, {'field': 'order_count', 'old_value': 1339, 'new_value': 1629}]
2025-06-08 09:01:16,110 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM68
2025-06-08 09:01:16,516 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM68
2025-06-08 09:01:16,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59342.75, 'new_value': 74212.6}, {'field': 'total_amount', 'old_value': 59342.75, 'new_value': 74212.6}, {'field': 'order_count', 'old_value': 210, 'new_value': 255}]
2025-06-08 09:01:16,516 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX6
2025-06-08 09:01:17,001 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX6
2025-06-08 09:01:17,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7252.48, 'new_value': 9555.37}, {'field': 'offline_amount', 'old_value': 11941.82, 'new_value': 14782.51}, {'field': 'total_amount', 'old_value': 19194.3, 'new_value': 24337.88}, {'field': 'order_count', 'old_value': 1641, 'new_value': 2062}]
2025-06-08 09:01:17,001 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM07
2025-06-08 09:01:17,469 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM07
2025-06-08 09:01:17,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26208.9, 'new_value': 33264.8}, {'field': 'total_amount', 'old_value': 26208.9, 'new_value': 33264.8}, {'field': 'order_count', 'old_value': 54, 'new_value': 72}]
2025-06-08 09:01:17,469 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM98
2025-06-08 09:01:17,860 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM98
2025-06-08 09:01:17,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83509.98, 'new_value': 99756.65}, {'field': 'total_amount', 'old_value': 83509.98, 'new_value': 99756.65}, {'field': 'order_count', 'old_value': 297, 'new_value': 357}]
2025-06-08 09:01:17,860 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA8
2025-06-08 09:01:18,313 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA8
2025-06-08 09:01:18,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37595.0, 'new_value': 42343.0}, {'field': 'total_amount', 'old_value': 37595.0, 'new_value': 42343.0}, {'field': 'order_count', 'old_value': 2863, 'new_value': 3186}]
2025-06-08 09:01:18,313 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMEQ1
2025-06-08 09:01:18,782 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMEQ1
2025-06-08 09:01:18,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3255.0, 'new_value': 4132.0}, {'field': 'offline_amount', 'old_value': 495.0, 'new_value': 845.0}, {'field': 'total_amount', 'old_value': 3750.0, 'new_value': 4977.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 51}]
2025-06-08 09:01:18,782 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2E1
2025-06-08 09:01:19,298 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2E1
2025-06-08 09:01:19,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74362.98, 'new_value': 96994.48}, {'field': 'offline_amount', 'old_value': 357765.94, 'new_value': 429142.21}, {'field': 'total_amount', 'old_value': 432128.92, 'new_value': 526136.69}, {'field': 'order_count', 'old_value': 2006, 'new_value': 2463}]
2025-06-08 09:01:19,298 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM47
2025-06-08 09:01:19,766 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM47
2025-06-08 09:01:19,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 444636.0, 'new_value': 455953.0}, {'field': 'total_amount', 'old_value': 444636.0, 'new_value': 455953.0}, {'field': 'order_count', 'old_value': 225, 'new_value': 242}]
2025-06-08 09:01:19,766 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIQ1
2025-06-08 09:01:20,188 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIQ1
2025-06-08 09:01:20,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33422.8, 'new_value': 51248.6}, {'field': 'total_amount', 'old_value': 79251.4, 'new_value': 97077.2}, {'field': 'order_count', 'old_value': 1941, 'new_value': 2328}]
2025-06-08 09:01:20,188 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM67
2025-06-08 09:01:20,719 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM67
2025-06-08 09:01:20,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78416.0, 'new_value': 94676.65}, {'field': 'total_amount', 'old_value': 78416.0, 'new_value': 94676.65}, {'field': 'order_count', 'old_value': 283, 'new_value': 328}]
2025-06-08 09:01:20,719 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM87
2025-06-08 09:01:21,188 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM87
2025-06-08 09:01:21,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7159.0, 'new_value': 8588.0}, {'field': 'total_amount', 'old_value': 7159.0, 'new_value': 8588.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 16}]
2025-06-08 09:01:21,188 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM97
2025-06-08 09:01:21,641 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM97
2025-06-08 09:01:21,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55527.3, 'new_value': 71610.0}, {'field': 'total_amount', 'old_value': 55527.3, 'new_value': 71610.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 94}]
2025-06-08 09:01:21,641 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA7
2025-06-08 09:01:22,141 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA7
2025-06-08 09:01:22,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385663.0, 'new_value': 476805.0}, {'field': 'total_amount', 'old_value': 385663.0, 'new_value': 476805.0}, {'field': 'order_count', 'old_value': 1713, 'new_value': 2173}]
2025-06-08 09:01:22,141 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB7
2025-06-08 09:01:22,626 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB7
2025-06-08 09:01:22,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50805.0, 'new_value': 56825.99}, {'field': 'total_amount', 'old_value': 64800.0, 'new_value': 70820.99}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-08 09:01:22,626 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJQ1
2025-06-08 09:01:23,079 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJQ1
2025-06-08 09:01:23,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27370.0, 'new_value': 33016.0}, {'field': 'total_amount', 'old_value': 27370.0, 'new_value': 33016.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 70}]
2025-06-08 09:01:23,079 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMKQ1
2025-06-08 09:01:23,501 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMKQ1
2025-06-08 09:01:23,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12496.0, 'new_value': 13288.0}, {'field': 'total_amount', 'old_value': 12496.0, 'new_value': 13288.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-08 09:01:23,501 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC7
2025-06-08 09:01:23,938 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC7
2025-06-08 09:01:23,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31871.8, 'new_value': 38064.8}, {'field': 'offline_amount', 'old_value': 25338.1, 'new_value': 31560.1}, {'field': 'total_amount', 'old_value': 57209.9, 'new_value': 69624.9}, {'field': 'order_count', 'old_value': 1337, 'new_value': 1630}]
2025-06-08 09:01:23,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMLQ1
2025-06-08 09:01:24,407 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMLQ1
2025-06-08 09:01:24,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207354.0, 'new_value': 238357.0}, {'field': 'total_amount', 'old_value': 207354.0, 'new_value': 238357.0}, {'field': 'order_count', 'old_value': 255, 'new_value': 293}]
2025-06-08 09:01:24,407 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMMQ1
2025-06-08 09:01:24,860 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMMQ1
2025-06-08 09:01:24,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20349.1, 'new_value': 25047.9}, {'field': 'total_amount', 'old_value': 20349.1, 'new_value': 25047.9}, {'field': 'order_count', 'old_value': 96, 'new_value': 118}]
2025-06-08 09:01:24,860 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMNQ1
2025-06-08 09:01:25,266 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMNQ1
2025-06-08 09:01:25,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7504.21, 'new_value': 8588.26}, {'field': 'offline_amount', 'old_value': 10982.92, 'new_value': 12862.27}, {'field': 'total_amount', 'old_value': 18487.13, 'new_value': 21450.53}, {'field': 'order_count', 'old_value': 955, 'new_value': 1106}]
2025-06-08 09:01:25,266 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMOQ1
2025-06-08 09:01:25,719 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMOQ1
2025-06-08 09:01:25,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35346.7, 'new_value': 42204.4}, {'field': 'total_amount', 'old_value': 35346.7, 'new_value': 42204.4}, {'field': 'order_count', 'old_value': 154, 'new_value': 189}]
2025-06-08 09:01:25,719 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMPQ1
2025-06-08 09:01:26,188 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMPQ1
2025-06-08 09:01:26,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4787.8, 'new_value': 5303.84}, {'field': 'offline_amount', 'old_value': 34399.0, 'new_value': 38641.0}, {'field': 'total_amount', 'old_value': 39186.8, 'new_value': 43944.84}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-06-08 09:01:26,188 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMQQ1
2025-06-08 09:01:26,610 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMQQ1
2025-06-08 09:01:26,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9651.0, 'new_value': 9767.0}, {'field': 'total_amount', 'old_value': 9651.0, 'new_value': 9767.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 13}]
2025-06-08 09:01:26,626 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMSQ1
2025-06-08 09:01:27,063 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMSQ1
2025-06-08 09:01:27,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42223.0, 'new_value': 54350.8}, {'field': 'total_amount', 'old_value': 42223.0, 'new_value': 54350.8}, {'field': 'order_count', 'old_value': 1160, 'new_value': 1516}]
2025-06-08 09:01:27,063 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMUQ1
2025-06-08 09:01:27,516 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMUQ1
2025-06-08 09:01:27,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54078.9, 'new_value': 64481.9}, {'field': 'total_amount', 'old_value': 54078.9, 'new_value': 64481.9}, {'field': 'order_count', 'old_value': 245, 'new_value': 291}]
2025-06-08 09:01:27,516 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8Q
2025-06-08 09:01:27,969 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8Q
2025-06-08 09:01:27,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1088.6, 'new_value': 1319.6}, {'field': 'offline_amount', 'old_value': 3750.65, 'new_value': 4452.23}, {'field': 'total_amount', 'old_value': 4839.25, 'new_value': 5771.83}, {'field': 'order_count', 'old_value': 49, 'new_value': 58}]
2025-06-08 09:01:27,969 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEE1
2025-06-08 09:01:28,391 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEE1
2025-06-08 09:01:28,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88898.24, 'new_value': 106355.24}, {'field': 'offline_amount', 'old_value': 1266.0, 'new_value': 1733.0}, {'field': 'total_amount', 'old_value': 90164.24, 'new_value': 108088.24}, {'field': 'order_count', 'old_value': 7880, 'new_value': 8955}]
2025-06-08 09:01:28,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMXQ1
2025-06-08 09:01:28,860 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMXQ1
2025-06-08 09:01:28,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83988.0, 'new_value': 105632.0}, {'field': 'total_amount', 'old_value': 83988.0, 'new_value': 105632.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 84}]
2025-06-08 09:01:28,860 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0O
2025-06-08 09:01:29,329 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0O
2025-06-08 09:01:29,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24176.0, 'new_value': 25587.0}, {'field': 'total_amount', 'old_value': 24176.0, 'new_value': 25587.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-08 09:01:29,329 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM1R1
2025-06-08 09:01:29,782 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM1R1
2025-06-08 09:01:29,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63587.5, 'new_value': 80251.5}, {'field': 'total_amount', 'old_value': 63587.5, 'new_value': 80251.5}, {'field': 'order_count', 'old_value': 112, 'new_value': 133}]
2025-06-08 09:01:29,782 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1O
2025-06-08 09:01:30,219 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1O
2025-06-08 09:01:30,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18254.0, 'new_value': 21978.0}, {'field': 'total_amount', 'old_value': 18254.0, 'new_value': 21978.0}, {'field': 'order_count', 'old_value': 862, 'new_value': 1060}]
2025-06-08 09:01:30,219 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3O
2025-06-08 09:01:30,626 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3O
2025-06-08 09:01:30,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68041.26, 'new_value': 89688.91}, {'field': 'total_amount', 'old_value': 68041.26, 'new_value': 89688.91}, {'field': 'order_count', 'old_value': 350, 'new_value': 468}]
2025-06-08 09:01:30,626 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4O
2025-06-08 09:01:31,079 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4O
2025-06-08 09:01:31,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79156.0, 'new_value': 99009.0}, {'field': 'total_amount', 'old_value': 79156.0, 'new_value': 99009.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 119}]
2025-06-08 09:01:31,079 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM3R1
2025-06-08 09:01:31,516 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM3R1
2025-06-08 09:01:31,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23939.03, 'new_value': 28371.87}, {'field': 'offline_amount', 'old_value': 26090.03, 'new_value': 31292.4}, {'field': 'total_amount', 'old_value': 50029.06, 'new_value': 59664.27}, {'field': 'order_count', 'old_value': 1284, 'new_value': 1518}]
2025-06-08 09:01:31,516 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM4R1
2025-06-08 09:01:31,969 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM4R1
2025-06-08 09:01:31,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48215.05, 'new_value': 57764.05}, {'field': 'total_amount', 'old_value': 48215.05, 'new_value': 57764.05}, {'field': 'order_count', 'old_value': 196, 'new_value': 236}]
2025-06-08 09:01:31,969 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM6R1
2025-06-08 09:01:32,391 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM6R1
2025-06-08 09:01:32,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59371.25, 'new_value': 74587.25}, {'field': 'total_amount', 'old_value': 66901.95, 'new_value': 82117.95}, {'field': 'order_count', 'old_value': 114, 'new_value': 136}]
2025-06-08 09:01:32,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM7R1
2025-06-08 09:01:32,891 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM7R1
2025-06-08 09:01:32,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 825.0, 'new_value': 935.0}, {'field': 'offline_amount', 'old_value': 11450.0, 'new_value': 12310.0}, {'field': 'total_amount', 'old_value': 12275.0, 'new_value': 13245.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 182}]
2025-06-08 09:01:32,891 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM8R1
2025-06-08 09:01:33,297 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM8R1
2025-06-08 09:01:33,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38175.0, 'new_value': 49724.0}, {'field': 'offline_amount', 'old_value': 31514.0, 'new_value': 43933.0}, {'field': 'total_amount', 'old_value': 69689.0, 'new_value': 93657.0}, {'field': 'order_count', 'old_value': 24468, 'new_value': 48436}]
2025-06-08 09:01:33,297 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAR1
2025-06-08 09:01:33,813 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAR1
2025-06-08 09:01:33,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 918.78, 'new_value': 1116.8}, {'field': 'total_amount', 'old_value': 6816.58, 'new_value': 7014.6}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-06-08 09:01:33,813 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDR1
2025-06-08 09:01:34,282 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDR1
2025-06-08 09:01:34,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1695.0, 'new_value': 4587.0}, {'field': 'total_amount', 'old_value': 1695.0, 'new_value': 4587.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-06-08 09:01:34,282 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAO
2025-06-08 09:01:34,782 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAO
2025-06-08 09:01:34,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22175.0, 'new_value': 29564.0}, {'field': 'total_amount', 'old_value': 22175.0, 'new_value': 29564.0}, {'field': 'order_count', 'old_value': 3826, 'new_value': 4288}]
2025-06-08 09:01:34,782 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFR1
2025-06-08 09:01:35,251 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFR1
2025-06-08 09:01:35,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72990.0, 'new_value': 81934.0}, {'field': 'total_amount', 'old_value': 72990.0, 'new_value': 81934.0}, {'field': 'order_count', 'old_value': 1678, 'new_value': 1890}]
2025-06-08 09:01:35,251 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGR1
2025-06-08 09:01:35,751 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGR1
2025-06-08 09:01:35,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4796.0, 'new_value': 5320.0}, {'field': 'total_amount', 'old_value': 5834.0, 'new_value': 6358.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 61}]
2025-06-08 09:01:35,751 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHR1
2025-06-08 09:01:36,110 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHR1
2025-06-08 09:01:36,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4992.0, 'new_value': 5586.0}, {'field': 'total_amount', 'old_value': 4992.0, 'new_value': 5586.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-08 09:01:36,110 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIR1
2025-06-08 09:01:36,579 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIR1
2025-06-08 09:01:36,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11885.5, 'new_value': 14263.4}, {'field': 'total_amount', 'old_value': 11885.5, 'new_value': 14263.4}, {'field': 'order_count', 'old_value': 365, 'new_value': 407}]
2025-06-08 09:01:36,579 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFO
2025-06-08 09:01:37,079 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFO
2025-06-08 09:01:37,079 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7770.8, 'new_value': 8797.3}, {'field': 'offline_amount', 'old_value': 13937.6, 'new_value': 15442.3}, {'field': 'total_amount', 'old_value': 21708.4, 'new_value': 24239.6}, {'field': 'order_count', 'old_value': 912, 'new_value': 1018}]
2025-06-08 09:01:37,079 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHO
2025-06-08 09:01:37,579 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHO
2025-06-08 09:01:37,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9604.71, 'new_value': 10403.06}, {'field': 'total_amount', 'old_value': 9974.31, 'new_value': 10772.66}, {'field': 'order_count', 'old_value': 90, 'new_value': 101}]
2025-06-08 09:01:37,594 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIO
2025-06-08 09:01:38,032 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIO
2025-06-08 09:01:38,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7087.0, 'new_value': 8046.33}, {'field': 'total_amount', 'old_value': 7087.0, 'new_value': 8046.33}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-06-08 09:01:38,032 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJO
2025-06-08 09:01:38,454 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJO
2025-06-08 09:01:38,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128590.3, 'new_value': 132890.28}, {'field': 'total_amount', 'old_value': 132288.3, 'new_value': 136588.28}, {'field': 'order_count', 'old_value': 164, 'new_value': 177}]
2025-06-08 09:01:38,454 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAP
2025-06-08 09:01:38,954 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAP
2025-06-08 09:01:38,954 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38534.94, 'new_value': 44708.76}, {'field': 'offline_amount', 'old_value': 9103.72, 'new_value': 9643.52}, {'field': 'total_amount', 'old_value': 47638.66, 'new_value': 54352.28}, {'field': 'order_count', 'old_value': 207, 'new_value': 238}]
2025-06-08 09:01:38,954 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMP
2025-06-08 09:01:39,407 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMP
2025-06-08 09:01:39,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22584.0, 'new_value': 26479.0}, {'field': 'total_amount', 'old_value': 22584.0, 'new_value': 26479.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 349}]
2025-06-08 09:01:39,407 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVP
2025-06-08 09:01:39,860 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVP
2025-06-08 09:01:39,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10568.73, 'new_value': 12328.19}, {'field': 'total_amount', 'old_value': 10568.73, 'new_value': 12328.19}, {'field': 'order_count', 'old_value': 1341, 'new_value': 1566}]
2025-06-08 09:01:39,860 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7Q
2025-06-08 09:01:40,407 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7Q
2025-06-08 09:01:40,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37798.0, 'new_value': 46438.0}, {'field': 'total_amount', 'old_value': 37798.0, 'new_value': 46438.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-08 09:01:40,407 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMJK
2025-06-08 09:01:40,876 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMJK
2025-06-08 09:01:40,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49680.0, 'new_value': 56685.0}, {'field': 'total_amount', 'old_value': 49680.0, 'new_value': 56685.0}, {'field': 'order_count', 'old_value': 1701, 'new_value': 1926}]
2025-06-08 09:01:40,876 - INFO - 日期 2025-06 处理完成 - 更新: 142 条，插入: 0 条，错误: 0 条
2025-06-08 09:01:40,876 - INFO - 数据同步完成！更新: 142 条，插入: 0 条，错误: 0 条
2025-06-08 09:01:40,876 - INFO - =================同步完成====================
2025-06-08 12:00:02,648 - INFO - =================使用默认全量同步=============
2025-06-08 12:00:04,242 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 12:00:04,242 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 12:00:04,273 - INFO - 开始处理日期: 2025-01
2025-06-08 12:00:04,289 - INFO - Request Parameters - Page 1:
2025-06-08 12:00:04,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:04,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:05,586 - INFO - Response - Page 1:
2025-06-08 12:00:05,789 - INFO - 第 1 页获取到 100 条记录
2025-06-08 12:00:05,789 - INFO - Request Parameters - Page 2:
2025-06-08 12:00:05,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:05,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:06,586 - INFO - Response - Page 2:
2025-06-08 12:00:06,789 - INFO - 第 2 页获取到 100 条记录
2025-06-08 12:00:06,789 - INFO - Request Parameters - Page 3:
2025-06-08 12:00:06,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:06,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:07,367 - INFO - Response - Page 3:
2025-06-08 12:00:07,570 - INFO - 第 3 页获取到 100 条记录
2025-06-08 12:00:07,570 - INFO - Request Parameters - Page 4:
2025-06-08 12:00:07,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:07,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:08,070 - INFO - Response - Page 4:
2025-06-08 12:00:08,273 - INFO - 第 4 页获取到 100 条记录
2025-06-08 12:00:08,273 - INFO - Request Parameters - Page 5:
2025-06-08 12:00:08,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:08,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:08,836 - INFO - Response - Page 5:
2025-06-08 12:00:09,039 - INFO - 第 5 页获取到 100 条记录
2025-06-08 12:00:09,039 - INFO - Request Parameters - Page 6:
2025-06-08 12:00:09,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:09,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:09,586 - INFO - Response - Page 6:
2025-06-08 12:00:09,789 - INFO - 第 6 页获取到 100 条记录
2025-06-08 12:00:09,789 - INFO - Request Parameters - Page 7:
2025-06-08 12:00:09,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:09,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:10,273 - INFO - Response - Page 7:
2025-06-08 12:00:10,477 - INFO - 第 7 页获取到 82 条记录
2025-06-08 12:00:10,477 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 12:00:10,477 - INFO - 获取到 682 条表单数据
2025-06-08 12:00:10,477 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 12:00:10,492 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 12:00:10,492 - INFO - 开始处理日期: 2025-02
2025-06-08 12:00:10,492 - INFO - Request Parameters - Page 1:
2025-06-08 12:00:10,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:10,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:10,977 - INFO - Response - Page 1:
2025-06-08 12:00:11,180 - INFO - 第 1 页获取到 100 条记录
2025-06-08 12:00:11,180 - INFO - Request Parameters - Page 2:
2025-06-08 12:00:11,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:11,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:11,664 - INFO - Response - Page 2:
2025-06-08 12:00:11,867 - INFO - 第 2 页获取到 100 条记录
2025-06-08 12:00:11,867 - INFO - Request Parameters - Page 3:
2025-06-08 12:00:11,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:11,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:12,414 - INFO - Response - Page 3:
2025-06-08 12:00:12,617 - INFO - 第 3 页获取到 100 条记录
2025-06-08 12:00:12,617 - INFO - Request Parameters - Page 4:
2025-06-08 12:00:12,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:12,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:13,539 - INFO - Response - Page 4:
2025-06-08 12:00:13,742 - INFO - 第 4 页获取到 100 条记录
2025-06-08 12:00:13,742 - INFO - Request Parameters - Page 5:
2025-06-08 12:00:13,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:13,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:14,305 - INFO - Response - Page 5:
2025-06-08 12:00:14,508 - INFO - 第 5 页获取到 100 条记录
2025-06-08 12:00:14,508 - INFO - Request Parameters - Page 6:
2025-06-08 12:00:14,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:14,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:14,992 - INFO - Response - Page 6:
2025-06-08 12:00:15,195 - INFO - 第 6 页获取到 100 条记录
2025-06-08 12:00:15,195 - INFO - Request Parameters - Page 7:
2025-06-08 12:00:15,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:15,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:15,695 - INFO - Response - Page 7:
2025-06-08 12:00:15,898 - INFO - 第 7 页获取到 70 条记录
2025-06-08 12:00:15,898 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 12:00:15,898 - INFO - 获取到 670 条表单数据
2025-06-08 12:00:15,898 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 12:00:15,914 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 12:00:15,914 - INFO - 开始处理日期: 2025-03
2025-06-08 12:00:15,914 - INFO - Request Parameters - Page 1:
2025-06-08 12:00:15,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:15,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:16,492 - INFO - Response - Page 1:
2025-06-08 12:00:16,695 - INFO - 第 1 页获取到 100 条记录
2025-06-08 12:00:16,695 - INFO - Request Parameters - Page 2:
2025-06-08 12:00:16,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:16,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:17,164 - INFO - Response - Page 2:
2025-06-08 12:00:17,367 - INFO - 第 2 页获取到 100 条记录
2025-06-08 12:00:17,367 - INFO - Request Parameters - Page 3:
2025-06-08 12:00:17,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:17,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:17,820 - INFO - Response - Page 3:
2025-06-08 12:00:18,023 - INFO - 第 3 页获取到 100 条记录
2025-06-08 12:00:18,023 - INFO - Request Parameters - Page 4:
2025-06-08 12:00:18,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:18,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:18,555 - INFO - Response - Page 4:
2025-06-08 12:00:18,758 - INFO - 第 4 页获取到 100 条记录
2025-06-08 12:00:18,758 - INFO - Request Parameters - Page 5:
2025-06-08 12:00:18,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:18,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:19,226 - INFO - Response - Page 5:
2025-06-08 12:00:19,430 - INFO - 第 5 页获取到 100 条记录
2025-06-08 12:00:19,430 - INFO - Request Parameters - Page 6:
2025-06-08 12:00:19,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:19,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:19,930 - INFO - Response - Page 6:
2025-06-08 12:00:20,133 - INFO - 第 6 页获取到 100 条记录
2025-06-08 12:00:20,133 - INFO - Request Parameters - Page 7:
2025-06-08 12:00:20,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:20,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:20,648 - INFO - Response - Page 7:
2025-06-08 12:00:20,851 - INFO - 第 7 页获取到 61 条记录
2025-06-08 12:00:20,851 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 12:00:20,851 - INFO - 获取到 661 条表单数据
2025-06-08 12:00:20,851 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 12:00:20,867 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 12:00:20,867 - INFO - 开始处理日期: 2025-04
2025-06-08 12:00:20,867 - INFO - Request Parameters - Page 1:
2025-06-08 12:00:20,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:20,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:21,851 - INFO - Response - Page 1:
2025-06-08 12:00:22,055 - INFO - 第 1 页获取到 100 条记录
2025-06-08 12:00:22,055 - INFO - Request Parameters - Page 2:
2025-06-08 12:00:22,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:22,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:22,539 - INFO - Response - Page 2:
2025-06-08 12:00:22,742 - INFO - 第 2 页获取到 100 条记录
2025-06-08 12:00:22,742 - INFO - Request Parameters - Page 3:
2025-06-08 12:00:22,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:22,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:23,211 - INFO - Response - Page 3:
2025-06-08 12:00:23,414 - INFO - 第 3 页获取到 100 条记录
2025-06-08 12:00:23,414 - INFO - Request Parameters - Page 4:
2025-06-08 12:00:23,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:23,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:23,961 - INFO - Response - Page 4:
2025-06-08 12:00:24,164 - INFO - 第 4 页获取到 100 条记录
2025-06-08 12:00:24,164 - INFO - Request Parameters - Page 5:
2025-06-08 12:00:24,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:24,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:24,680 - INFO - Response - Page 5:
2025-06-08 12:00:24,883 - INFO - 第 5 页获取到 100 条记录
2025-06-08 12:00:24,883 - INFO - Request Parameters - Page 6:
2025-06-08 12:00:24,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:24,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:25,383 - INFO - Response - Page 6:
2025-06-08 12:00:25,586 - INFO - 第 6 页获取到 100 条记录
2025-06-08 12:00:25,586 - INFO - Request Parameters - Page 7:
2025-06-08 12:00:25,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:25,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:26,039 - INFO - Response - Page 7:
2025-06-08 12:00:26,242 - INFO - 第 7 页获取到 56 条记录
2025-06-08 12:00:26,242 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 12:00:26,242 - INFO - 获取到 656 条表单数据
2025-06-08 12:00:26,242 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 12:00:26,258 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 12:00:26,258 - INFO - 开始处理日期: 2025-05
2025-06-08 12:00:26,258 - INFO - Request Parameters - Page 1:
2025-06-08 12:00:26,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:26,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:26,758 - INFO - Response - Page 1:
2025-06-08 12:00:26,961 - INFO - 第 1 页获取到 100 条记录
2025-06-08 12:00:26,961 - INFO - Request Parameters - Page 2:
2025-06-08 12:00:26,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:26,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:27,805 - INFO - Response - Page 2:
2025-06-08 12:00:28,008 - INFO - 第 2 页获取到 100 条记录
2025-06-08 12:00:28,008 - INFO - Request Parameters - Page 3:
2025-06-08 12:00:28,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:28,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:28,508 - INFO - Response - Page 3:
2025-06-08 12:00:28,726 - INFO - 第 3 页获取到 100 条记录
2025-06-08 12:00:28,726 - INFO - Request Parameters - Page 4:
2025-06-08 12:00:28,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:28,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:29,289 - INFO - Response - Page 4:
2025-06-08 12:00:29,492 - INFO - 第 4 页获取到 100 条记录
2025-06-08 12:00:29,492 - INFO - Request Parameters - Page 5:
2025-06-08 12:00:29,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:29,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:29,992 - INFO - Response - Page 5:
2025-06-08 12:00:30,195 - INFO - 第 5 页获取到 100 条记录
2025-06-08 12:00:30,195 - INFO - Request Parameters - Page 6:
2025-06-08 12:00:30,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:30,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:30,648 - INFO - Response - Page 6:
2025-06-08 12:00:30,851 - INFO - 第 6 页获取到 100 条记录
2025-06-08 12:00:30,851 - INFO - Request Parameters - Page 7:
2025-06-08 12:00:30,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:30,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:31,367 - INFO - Response - Page 7:
2025-06-08 12:00:31,570 - INFO - 第 7 页获取到 38 条记录
2025-06-08 12:00:31,570 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 12:00:31,570 - INFO - 获取到 638 条表单数据
2025-06-08 12:00:31,570 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 12:00:31,586 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 12:00:31,586 - INFO - 开始处理日期: 2025-06
2025-06-08 12:00:31,586 - INFO - Request Parameters - Page 1:
2025-06-08 12:00:31,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:31,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:32,101 - INFO - Response - Page 1:
2025-06-08 12:00:32,305 - INFO - 第 1 页获取到 100 条记录
2025-06-08 12:00:32,305 - INFO - Request Parameters - Page 2:
2025-06-08 12:00:32,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:32,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:32,742 - INFO - Response - Page 2:
2025-06-08 12:00:32,945 - INFO - 第 2 页获取到 100 条记录
2025-06-08 12:00:32,945 - INFO - Request Parameters - Page 3:
2025-06-08 12:00:32,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:32,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:33,867 - INFO - Response - Page 3:
2025-06-08 12:00:34,070 - INFO - 第 3 页获取到 100 条记录
2025-06-08 12:00:34,070 - INFO - Request Parameters - Page 4:
2025-06-08 12:00:34,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:34,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:34,539 - INFO - Response - Page 4:
2025-06-08 12:00:34,742 - INFO - 第 4 页获取到 100 条记录
2025-06-08 12:00:34,742 - INFO - Request Parameters - Page 5:
2025-06-08 12:00:34,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:34,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:35,242 - INFO - Response - Page 5:
2025-06-08 12:00:35,445 - INFO - 第 5 页获取到 100 条记录
2025-06-08 12:00:35,445 - INFO - Request Parameters - Page 6:
2025-06-08 12:00:35,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:35,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:36,023 - INFO - Response - Page 6:
2025-06-08 12:00:36,226 - INFO - 第 6 页获取到 100 条记录
2025-06-08 12:00:36,226 - INFO - Request Parameters - Page 7:
2025-06-08 12:00:36,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 12:00:36,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 12:00:36,555 - INFO - Response - Page 7:
2025-06-08 12:00:36,758 - INFO - 第 7 页获取到 19 条记录
2025-06-08 12:00:36,758 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 12:00:36,758 - INFO - 获取到 619 条表单数据
2025-06-08 12:00:36,758 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 12:00:36,758 - INFO - 开始更新记录 - 表单实例ID: FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21
2025-06-08 12:00:37,226 - INFO - 更新表单数据成功: FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21
2025-06-08 12:00:37,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178484.0, 'new_value': 219804.0}, {'field': 'total_amount', 'old_value': 178484.0, 'new_value': 219804.0}, {'field': 'order_count', 'old_value': 1354, 'new_value': 1576}]
2025-06-08 12:00:37,226 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8B
2025-06-08 12:00:37,664 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8B
2025-06-08 12:00:37,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37974.61, 'new_value': 48685.63}, {'field': 'total_amount', 'old_value': 37974.61, 'new_value': 48685.63}, {'field': 'order_count', 'old_value': 961, 'new_value': 1224}]
2025-06-08 12:00:37,664 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM8I
2025-06-08 12:00:38,117 - INFO - 更新表单数据成功: FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM8I
2025-06-08 12:00:38,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20146.17, 'new_value': 23636.63}, {'field': 'offline_amount', 'old_value': 215209.72, 'new_value': 247598.88}, {'field': 'total_amount', 'old_value': 231363.4, 'new_value': 267243.02}, {'field': 'order_count', 'old_value': 1092, 'new_value': 1259}]
2025-06-08 12:00:38,117 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMJ5
2025-06-08 12:00:38,617 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMJ5
2025-06-08 12:00:38,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16200.0, 'new_value': 52200.0}, {'field': 'total_amount', 'old_value': 16200.0, 'new_value': 52200.0}]
2025-06-08 12:00:38,617 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMX4
2025-06-08 12:00:39,070 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMX4
2025-06-08 12:00:39,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8295.0, 'new_value': 10665.0}, {'field': 'total_amount', 'old_value': 8295.0, 'new_value': 10665.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-08 12:00:39,070 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOB1
2025-06-08 12:00:39,586 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOB1
2025-06-08 12:00:39,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6800.0, 'new_value': 7760.0}, {'field': 'total_amount', 'old_value': 6800.0, 'new_value': 7760.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-08 12:00:39,586 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPB1
2025-06-08 12:00:39,992 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPB1
2025-06-08 12:00:39,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6510.38, 'new_value': 7234.38}, {'field': 'offline_amount', 'old_value': 2855.44, 'new_value': 3885.44}, {'field': 'total_amount', 'old_value': 9365.82, 'new_value': 11119.82}, {'field': 'order_count', 'old_value': 393, 'new_value': 458}]
2025-06-08 12:00:39,992 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQB1
2025-06-08 12:00:40,398 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQB1
2025-06-08 12:00:40,398 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116560.0, 'new_value': 140587.0}, {'field': 'offline_amount', 'old_value': 52145.0, 'new_value': 58017.0}, {'field': 'total_amount', 'old_value': 168705.0, 'new_value': 198604.0}, {'field': 'order_count', 'old_value': 203, 'new_value': 240}]
2025-06-08 12:00:40,398 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRB1
2025-06-08 12:00:40,789 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRB1
2025-06-08 12:00:40,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4166.82, 'new_value': 5126.82}, {'field': 'total_amount', 'old_value': 4166.82, 'new_value': 5126.82}, {'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-06-08 12:00:40,789 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMY4
2025-06-08 12:00:41,320 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMY4
2025-06-08 12:00:41,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10898.0, 'new_value': 45398.0}, {'field': 'total_amount', 'old_value': 10898.0, 'new_value': 45398.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-06-08 12:00:41,320 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTB1
2025-06-08 12:00:41,789 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTB1
2025-06-08 12:00:41,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12900.0, 'new_value': 16480.0}, {'field': 'total_amount', 'old_value': 15800.0, 'new_value': 19380.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 185}]
2025-06-08 12:00:41,789 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUB1
2025-06-08 12:00:42,179 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUB1
2025-06-08 12:00:42,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1.0, 'new_value': 6011.0}, {'field': 'total_amount', 'old_value': 10601.0, 'new_value': 16611.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-08 12:00:42,179 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW5
2025-06-08 12:00:42,648 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW5
2025-06-08 12:00:42,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 212.0, 'new_value': 568.2}, {'field': 'total_amount', 'old_value': 213.0, 'new_value': 569.2}, {'field': 'order_count', 'old_value': 4, 'new_value': 8}]
2025-06-08 12:00:42,648 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYB1
2025-06-08 12:00:43,101 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYB1
2025-06-08 12:00:43,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60968.0, 'new_value': 71898.0}, {'field': 'total_amount', 'old_value': 60968.0, 'new_value': 71898.0}, {'field': 'order_count', 'old_value': 295, 'new_value': 352}]
2025-06-08 12:00:43,101 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZB1
2025-06-08 12:00:43,586 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZB1
2025-06-08 12:00:43,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13745.49, 'new_value': 30940.33}, {'field': 'total_amount', 'old_value': 13745.49, 'new_value': 30940.33}, {'field': 'order_count', 'old_value': 41, 'new_value': 58}]
2025-06-08 12:00:43,586 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0C1
2025-06-08 12:00:44,117 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0C1
2025-06-08 12:00:44,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50714.41, 'new_value': 59234.69}, {'field': 'total_amount', 'old_value': 50714.41, 'new_value': 59234.69}, {'field': 'order_count', 'old_value': 292, 'new_value': 340}]
2025-06-08 12:00:44,117 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2C1
2025-06-08 12:00:44,570 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2C1
2025-06-08 12:00:44,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161199.23, 'new_value': 197110.77}, {'field': 'total_amount', 'old_value': 161199.23, 'new_value': 197110.77}, {'field': 'order_count', 'old_value': 1216, 'new_value': 1469}]
2025-06-08 12:00:44,570 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM05
2025-06-08 12:00:45,054 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM05
2025-06-08 12:00:45,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5136.0, 'new_value': 5352.9}, {'field': 'total_amount', 'old_value': 5136.0, 'new_value': 5352.9}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-06-08 12:00:45,054 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3C1
2025-06-08 12:00:45,508 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3C1
2025-06-08 12:00:45,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141697.5, 'new_value': 223162.9}, {'field': 'total_amount', 'old_value': 141697.5, 'new_value': 223162.9}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-06-08 12:00:45,508 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM25
2025-06-08 12:00:46,039 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM25
2025-06-08 12:00:46,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 783.07, 'new_value': 805.33}, {'field': 'offline_amount', 'old_value': 28863.89, 'new_value': 36251.56}, {'field': 'total_amount', 'old_value': 29646.96, 'new_value': 37056.89}, {'field': 'order_count', 'old_value': 606, 'new_value': 786}]
2025-06-08 12:00:46,039 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM35
2025-06-08 12:00:46,523 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM35
2025-06-08 12:00:46,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30967.5, 'new_value': 35323.6}, {'field': 'offline_amount', 'old_value': 260630.94, 'new_value': 305335.52}, {'field': 'total_amount', 'old_value': 291598.44, 'new_value': 340659.12}, {'field': 'order_count', 'old_value': 2573, 'new_value': 3001}]
2025-06-08 12:00:46,523 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4C1
2025-06-08 12:00:47,008 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4C1
2025-06-08 12:00:47,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 677.0, 'new_value': 686.0}, {'field': 'offline_amount', 'old_value': 5199.0, 'new_value': 23578.0}, {'field': 'total_amount', 'old_value': 5876.0, 'new_value': 24264.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-06-08 12:00:47,008 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6C1
2025-06-08 12:00:47,476 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6C1
2025-06-08 12:00:47,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1766.0, 'new_value': 2472.0}, {'field': 'offline_amount', 'old_value': 21174.0, 'new_value': 24910.0}, {'field': 'total_amount', 'old_value': 22940.0, 'new_value': 27382.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 183}]
2025-06-08 12:00:47,476 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7C1
2025-06-08 12:00:47,961 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7C1
2025-06-08 12:00:47,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35400.0, 'new_value': 41400.0}, {'field': 'total_amount', 'old_value': 35400.0, 'new_value': 41400.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-08 12:00:47,961 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9C1
2025-06-08 12:00:48,429 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9C1
2025-06-08 12:00:48,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14730.0, 'new_value': 16213.0}, {'field': 'total_amount', 'old_value': 14730.0, 'new_value': 16213.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-06-08 12:00:48,429 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBC1
2025-06-08 12:00:48,867 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBC1
2025-06-08 12:00:48,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 608.0, 'new_value': 1124.0}, {'field': 'offline_amount', 'old_value': 5770.0, 'new_value': 6286.0}, {'field': 'total_amount', 'old_value': 6378.0, 'new_value': 7410.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 14}]
2025-06-08 12:00:48,867 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM65
2025-06-08 12:00:49,273 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM65
2025-06-08 12:00:49,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1332.51, 'new_value': 1735.85}, {'field': 'offline_amount', 'old_value': 43798.94, 'new_value': 49657.77}, {'field': 'total_amount', 'old_value': 45131.45, 'new_value': 51393.62}, {'field': 'order_count', 'old_value': 308, 'new_value': 352}]
2025-06-08 12:00:49,273 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCC1
2025-06-08 12:00:49,773 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCC1
2025-06-08 12:00:49,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41292.65, 'new_value': 49870.71}, {'field': 'offline_amount', 'old_value': 88197.14, 'new_value': 111432.95}, {'field': 'total_amount', 'old_value': 129489.79, 'new_value': 161303.66}, {'field': 'order_count', 'old_value': 935, 'new_value': 1139}]
2025-06-08 12:00:49,789 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDC1
2025-06-08 12:00:50,289 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDC1
2025-06-08 12:00:50,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8836.45, 'new_value': 9848.45}, {'field': 'offline_amount', 'old_value': 15.0, 'new_value': 21.0}, {'field': 'total_amount', 'old_value': 8851.45, 'new_value': 9869.45}, {'field': 'order_count', 'old_value': 39, 'new_value': 45}]
2025-06-08 12:00:50,289 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMCB
2025-06-08 12:00:50,711 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMCB
2025-06-08 12:00:50,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2880.0, 'new_value': 3527.0}, {'field': 'total_amount', 'old_value': 2880.0, 'new_value': 3527.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-08 12:00:50,711 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEC1
2025-06-08 12:00:51,211 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEC1
2025-06-08 12:00:51,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11997.0, 'new_value': 17696.0}, {'field': 'total_amount', 'old_value': 11997.0, 'new_value': 17696.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-08 12:00:51,211 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFC1
2025-06-08 12:00:51,679 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFC1
2025-06-08 12:00:51,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30692.0, 'new_value': 42328.0}, {'field': 'total_amount', 'old_value': 30692.0, 'new_value': 42328.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 159}]
2025-06-08 12:00:51,679 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGC1
2025-06-08 12:00:52,164 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGC1
2025-06-08 12:00:52,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147511.71, 'new_value': 179120.12}, {'field': 'total_amount', 'old_value': 147511.71, 'new_value': 179120.12}, {'field': 'order_count', 'old_value': 1102, 'new_value': 1309}]
2025-06-08 12:00:52,164 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHC1
2025-06-08 12:00:52,695 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHC1
2025-06-08 12:00:52,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 9200.0}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 9200.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:00:52,695 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMIC1
2025-06-08 12:00:53,133 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMIC1
2025-06-08 12:00:53,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20141.42, 'new_value': 25535.56}, {'field': 'offline_amount', 'old_value': 143380.94, 'new_value': 169151.55}, {'field': 'total_amount', 'old_value': 163522.36, 'new_value': 194687.11}, {'field': 'order_count', 'old_value': 386, 'new_value': 454}]
2025-06-08 12:00:53,133 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJC1
2025-06-08 12:00:53,664 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJC1
2025-06-08 12:00:53,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7946.0, 'new_value': 9280.0}, {'field': 'total_amount', 'old_value': 7946.0, 'new_value': 9280.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-06-08 12:00:53,664 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKC1
2025-06-08 12:00:54,101 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKC1
2025-06-08 12:00:54,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2966.3, 'new_value': 3135.2}, {'field': 'offline_amount', 'old_value': 7302.9, 'new_value': 11078.9}, {'field': 'total_amount', 'old_value': 10269.2, 'new_value': 14214.1}, {'field': 'order_count', 'old_value': 46, 'new_value': 52}]
2025-06-08 12:00:54,101 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMA5
2025-06-08 12:00:54,523 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMA5
2025-06-08 12:00:54,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12502.6, 'new_value': 14939.0}, {'field': 'total_amount', 'old_value': 12503.6, 'new_value': 14940.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 55}]
2025-06-08 12:00:54,523 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLC1
2025-06-08 12:00:54,929 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLC1
2025-06-08 12:00:54,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47669.9, 'new_value': 72655.9}, {'field': 'offline_amount', 'old_value': 27186.0, 'new_value': 32825.0}, {'field': 'total_amount', 'old_value': 74855.9, 'new_value': 105480.9}, {'field': 'order_count', 'old_value': 296, 'new_value': 396}]
2025-06-08 12:00:54,929 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMC1
2025-06-08 12:00:55,429 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMC1
2025-06-08 12:00:55,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2464.0, 'new_value': 3017.0}, {'field': 'offline_amount', 'old_value': 109565.0, 'new_value': 142565.0}, {'field': 'total_amount', 'old_value': 112029.0, 'new_value': 145582.0}, {'field': 'order_count', 'old_value': 980, 'new_value': 1233}]
2025-06-08 12:00:55,429 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMNC1
2025-06-08 12:00:55,883 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMNC1
2025-06-08 12:00:55,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25026.0, 'new_value': 39989.0}, {'field': 'total_amount', 'old_value': 25026.0, 'new_value': 39989.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-08 12:00:55,883 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5
2025-06-08 12:00:56,383 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5
2025-06-08 12:00:56,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17890.0, 'new_value': 28582.0}, {'field': 'total_amount', 'old_value': 17890.0, 'new_value': 28582.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-06-08 12:00:56,383 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOC1
2025-06-08 12:00:56,914 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOC1
2025-06-08 12:00:56,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3315.95, 'new_value': 4237.0}, {'field': 'offline_amount', 'old_value': 26478.0, 'new_value': 29871.0}, {'field': 'total_amount', 'old_value': 29793.95, 'new_value': 34108.0}, {'field': 'order_count', 'old_value': 492, 'new_value': 651}]
2025-06-08 12:00:56,914 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPC1
2025-06-08 12:00:57,367 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPC1
2025-06-08 12:00:57,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9522.0, 'new_value': 10577.0}, {'field': 'total_amount', 'old_value': 9522.0, 'new_value': 10577.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 50}]
2025-06-08 12:00:57,367 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMF5
2025-06-08 12:00:57,882 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMF5
2025-06-08 12:00:57,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87285.78, 'new_value': 110451.24}, {'field': 'total_amount', 'old_value': 87285.78, 'new_value': 110451.24}, {'field': 'order_count', 'old_value': 399, 'new_value': 510}]
2025-06-08 12:00:57,882 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMK5
2025-06-08 12:00:58,320 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMK5
2025-06-08 12:00:58,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6460.0, 'new_value': 7410.0}, {'field': 'total_amount', 'old_value': 6520.0, 'new_value': 7470.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 28}]
2025-06-08 12:00:58,320 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQC1
2025-06-08 12:00:58,961 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQC1
2025-06-08 12:00:58,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4502.76, 'new_value': 5419.89}, {'field': 'offline_amount', 'old_value': 66820.63, 'new_value': 80649.63}, {'field': 'total_amount', 'old_value': 71323.39, 'new_value': 86069.52}, {'field': 'order_count', 'old_value': 468, 'new_value': 560}]
2025-06-08 12:00:58,961 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMT31
2025-06-08 12:00:59,414 - INFO - 更新表单数据成功: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMT31
2025-06-08 12:00:59,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4614.86, 'new_value': 6118.53}, {'field': 'total_amount', 'old_value': 5031.23, 'new_value': 6534.9}, {'field': 'order_count', 'old_value': 96, 'new_value': 130}]
2025-06-08 12:00:59,414 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRC1
2025-06-08 12:00:59,882 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRC1
2025-06-08 12:00:59,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68502.85, 'new_value': 80942.26}, {'field': 'total_amount', 'old_value': 68502.85, 'new_value': 80942.26}, {'field': 'order_count', 'old_value': 2003, 'new_value': 2371}]
2025-06-08 12:00:59,882 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSC1
2025-06-08 12:01:00,367 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSC1
2025-06-08 12:01:00,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3970.0, 'new_value': 4590.0}, {'field': 'total_amount', 'old_value': 3970.0, 'new_value': 4590.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-06-08 12:01:00,367 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTC1
2025-06-08 12:01:00,804 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTC1
2025-06-08 12:01:00,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34000.0, 'new_value': 46000.0}, {'field': 'total_amount', 'old_value': 34000.0, 'new_value': 46000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-06-08 12:01:00,804 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVC1
2025-06-08 12:01:01,273 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVC1
2025-06-08 12:01:01,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37893.0, 'new_value': 61188.0}, {'field': 'total_amount', 'old_value': 37893.0, 'new_value': 61188.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-06-08 12:01:01,273 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWC1
2025-06-08 12:01:01,773 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWC1
2025-06-08 12:01:01,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30617.0, 'new_value': 34118.0}, {'field': 'total_amount', 'old_value': 30617.0, 'new_value': 34118.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-08 12:01:01,773 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXC1
2025-06-08 12:01:02,242 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXC1
2025-06-08 12:01:02,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12938.0, 'new_value': 15327.0}, {'field': 'total_amount', 'old_value': 12938.0, 'new_value': 15327.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-08 12:01:02,242 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMQ5
2025-06-08 12:01:02,804 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMQ5
2025-06-08 12:01:02,804 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2531.02, 'new_value': 3009.64}, {'field': 'offline_amount', 'old_value': 75690.49, 'new_value': 88918.89}, {'field': 'total_amount', 'old_value': 78221.51, 'new_value': 91928.53}, {'field': 'order_count', 'old_value': 3738, 'new_value': 4483}]
2025-06-08 12:01:02,804 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYC1
2025-06-08 12:01:03,320 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYC1
2025-06-08 12:01:03,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1925.1, 'new_value': 2587.8}, {'field': 'offline_amount', 'old_value': 10195.0, 'new_value': 10695.0}, {'field': 'total_amount', 'old_value': 12120.1, 'new_value': 13282.8}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-06-08 12:01:03,320 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0D1
2025-06-08 12:01:03,961 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0D1
2025-06-08 12:01:03,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16708.97, 'new_value': 20105.97}, {'field': 'offline_amount', 'old_value': 27607.0, 'new_value': 32771.15}, {'field': 'total_amount', 'old_value': 44315.97, 'new_value': 52877.12}, {'field': 'order_count', 'old_value': 32, 'new_value': 41}]
2025-06-08 12:01:03,961 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMT5
2025-06-08 12:01:04,476 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMT5
2025-06-08 12:01:04,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1333600.0, 'new_value': 1966500.0}, {'field': 'total_amount', 'old_value': 1333600.0, 'new_value': 1966500.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-06-08 12:01:04,476 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMU5
2025-06-08 12:01:04,945 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMU5
2025-06-08 12:01:04,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11722.35, 'new_value': 13182.32}, {'field': 'offline_amount', 'old_value': 7931.0, 'new_value': 9937.0}, {'field': 'total_amount', 'old_value': 19653.35, 'new_value': 23119.32}, {'field': 'order_count', 'old_value': 265, 'new_value': 304}]
2025-06-08 12:01:04,945 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2D1
2025-06-08 12:01:05,351 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2D1
2025-06-08 12:01:05,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52602.8, 'new_value': 63017.8}, {'field': 'total_amount', 'old_value': 52602.8, 'new_value': 63017.8}, {'field': 'order_count', 'old_value': 289, 'new_value': 348}]
2025-06-08 12:01:05,351 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3D1
2025-06-08 12:01:05,820 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3D1
2025-06-08 12:01:05,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46380.0, 'new_value': 60088.0}, {'field': 'total_amount', 'old_value': 46380.0, 'new_value': 60088.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 35}]
2025-06-08 12:01:05,820 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4D1
2025-06-08 12:01:06,382 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4D1
2025-06-08 12:01:06,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35605.5, 'new_value': 43484.6}, {'field': 'total_amount', 'old_value': 35605.5, 'new_value': 43484.6}, {'field': 'order_count', 'old_value': 89, 'new_value': 106}]
2025-06-08 12:01:06,382 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5D1
2025-06-08 12:01:06,898 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5D1
2025-06-08 12:01:06,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16408.8, 'new_value': 22523.7}, {'field': 'total_amount', 'old_value': 16408.8, 'new_value': 22523.7}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:01:06,898 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6D1
2025-06-08 12:01:07,304 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6D1
2025-06-08 12:01:07,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20293.7, 'new_value': 22823.6}, {'field': 'total_amount', 'old_value': 20293.7, 'new_value': 22823.6}, {'field': 'order_count', 'old_value': 128, 'new_value': 146}]
2025-06-08 12:01:07,304 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMS31
2025-06-08 12:01:07,773 - INFO - 更新表单数据成功: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMS31
2025-06-08 12:01:07,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13210.94, 'new_value': 15693.04}, {'field': 'offline_amount', 'old_value': 21494.17, 'new_value': 24802.64}, {'field': 'total_amount', 'old_value': 34705.11, 'new_value': 40495.68}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1962}]
2025-06-08 12:01:07,773 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH7
2025-06-08 12:01:08,195 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH7
2025-06-08 12:01:08,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16810.0, 'new_value': 21586.0}, {'field': 'total_amount', 'old_value': 16810.0, 'new_value': 21586.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 119}]
2025-06-08 12:01:08,195 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8D1
2025-06-08 12:01:08,601 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8D1
2025-06-08 12:01:08,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164023.0, 'new_value': 198248.0}, {'field': 'total_amount', 'old_value': 164023.0, 'new_value': 198248.0}, {'field': 'order_count', 'old_value': 534, 'new_value': 651}]
2025-06-08 12:01:08,601 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ7
2025-06-08 12:01:09,039 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ7
2025-06-08 12:01:09,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35399.1, 'new_value': 44694.4}, {'field': 'total_amount', 'old_value': 35399.1, 'new_value': 44694.4}, {'field': 'order_count', 'old_value': 464, 'new_value': 573}]
2025-06-08 12:01:09,039 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9D1
2025-06-08 12:01:09,492 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9D1
2025-06-08 12:01:09,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10497.0, 'new_value': 13402.0}, {'field': 'total_amount', 'old_value': 10716.0, 'new_value': 13621.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 49}]
2025-06-08 12:01:09,492 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM7
2025-06-08 12:01:10,023 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM7
2025-06-08 12:01:10,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13498.4, 'new_value': 16979.2}, {'field': 'offline_amount', 'old_value': 86932.92, 'new_value': 108160.51}, {'field': 'total_amount', 'old_value': 100431.32, 'new_value': 125139.71}, {'field': 'order_count', 'old_value': 609, 'new_value': 764}]
2025-06-08 12:01:10,023 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBD1
2025-06-08 12:01:10,523 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBD1
2025-06-08 12:01:10,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21136.0, 'new_value': 42452.0}, {'field': 'total_amount', 'old_value': 21136.0, 'new_value': 42452.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-06-08 12:01:10,523 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP7
2025-06-08 12:01:11,007 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP7
2025-06-08 12:01:11,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4114.7, 'new_value': 6086.33}, {'field': 'total_amount', 'old_value': 4114.7, 'new_value': 6086.33}, {'field': 'order_count', 'old_value': 7, 'new_value': 13}]
2025-06-08 12:01:11,007 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ7
2025-06-08 12:01:11,461 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ7
2025-06-08 12:01:11,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5885.0, 'new_value': 7719.0}, {'field': 'total_amount', 'old_value': 5885.0, 'new_value': 7719.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 49}]
2025-06-08 12:01:11,461 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCD1
2025-06-08 12:01:11,851 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCD1
2025-06-08 12:01:11,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14930.3, 'new_value': 16389.7}, {'field': 'total_amount', 'old_value': 15196.7, 'new_value': 16656.1}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-06-08 12:01:11,851 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR7
2025-06-08 12:01:12,320 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR7
2025-06-08 12:01:12,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36196.13, 'new_value': 46074.75}, {'field': 'offline_amount', 'old_value': 23669.0, 'new_value': 32563.0}, {'field': 'total_amount', 'old_value': 59865.13, 'new_value': 78637.75}, {'field': 'order_count', 'old_value': 573, 'new_value': 741}]
2025-06-08 12:01:12,320 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS7
2025-06-08 12:01:12,757 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS7
2025-06-08 12:01:12,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124450.2, 'new_value': 151960.78}, {'field': 'total_amount', 'old_value': 124704.36, 'new_value': 152214.94}, {'field': 'order_count', 'old_value': 316, 'new_value': 368}]
2025-06-08 12:01:12,757 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMF6
2025-06-08 12:01:13,179 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMF6
2025-06-08 12:01:13,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127302.0, 'new_value': 146698.0}, {'field': 'total_amount', 'old_value': 127302.0, 'new_value': 146698.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-08 12:01:13,179 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMH6
2025-06-08 12:01:13,617 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMH6
2025-06-08 12:01:13,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19555.95, 'new_value': 20663.23}, {'field': 'offline_amount', 'old_value': 71530.45, 'new_value': 82583.85}, {'field': 'total_amount', 'old_value': 91086.4, 'new_value': 103247.08}, {'field': 'order_count', 'old_value': 1300, 'new_value': 1571}]
2025-06-08 12:01:13,617 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMED1
2025-06-08 12:01:14,070 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMED1
2025-06-08 12:01:14,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130260.82, 'new_value': 147606.32}, {'field': 'total_amount', 'old_value': 170790.06, 'new_value': 188135.56}, {'field': 'order_count', 'old_value': 337, 'new_value': 370}]
2025-06-08 12:01:14,070 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGD1
2025-06-08 12:01:14,461 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGD1
2025-06-08 12:01:14,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4299.0, 'new_value': 6399.0}, {'field': 'total_amount', 'old_value': 4299.0, 'new_value': 6399.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-08 12:01:14,461 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMK6
2025-06-08 12:01:14,898 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMK6
2025-06-08 12:01:14,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63661.0, 'new_value': 71987.0}, {'field': 'total_amount', 'old_value': 63661.0, 'new_value': 71987.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 74}]
2025-06-08 12:01:14,898 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHD1
2025-06-08 12:01:15,367 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHD1
2025-06-08 12:01:15,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8636.65, 'new_value': 9264.54}, {'field': 'total_amount', 'old_value': 8636.65, 'new_value': 9264.54}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-06-08 12:01:15,367 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMID1
2025-06-08 12:01:15,867 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMID1
2025-06-08 12:01:15,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20286.0, 'new_value': 23552.0}, {'field': 'offline_amount', 'old_value': 36918.0, 'new_value': 44304.0}, {'field': 'total_amount', 'old_value': 57204.0, 'new_value': 67856.0}, {'field': 'order_count', 'old_value': 1293, 'new_value': 1540}]
2025-06-08 12:01:15,867 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ7
2025-06-08 12:01:16,367 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ7
2025-06-08 12:01:16,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49490.0, 'new_value': 59564.0}, {'field': 'total_amount', 'old_value': 49490.0, 'new_value': 59564.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-08 12:01:16,367 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKD1
2025-06-08 12:01:16,867 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKD1
2025-06-08 12:01:16,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7640.0, 'new_value': 9148.0}, {'field': 'total_amount', 'old_value': 7640.0, 'new_value': 9148.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 82}]
2025-06-08 12:01:16,867 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLD1
2025-06-08 12:01:17,304 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLD1
2025-06-08 12:01:17,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161546.14, 'new_value': 203020.26}, {'field': 'total_amount', 'old_value': 161546.14, 'new_value': 203020.26}, {'field': 'order_count', 'old_value': 1742, 'new_value': 2098}]
2025-06-08 12:01:17,304 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM28
2025-06-08 12:01:17,757 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM28
2025-06-08 12:01:17,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16141.0, 'new_value': 20586.0}, {'field': 'total_amount', 'old_value': 16141.0, 'new_value': 20586.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-06-08 12:01:17,757 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMD1
2025-06-08 12:01:18,289 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMD1
2025-06-08 12:01:18,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29830.0, 'new_value': 36510.0}, {'field': 'total_amount', 'old_value': 29830.0, 'new_value': 36510.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 128}]
2025-06-08 12:01:18,304 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM48
2025-06-08 12:01:18,757 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM48
2025-06-08 12:01:18,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8670.08, 'new_value': 10759.44}, {'field': 'total_amount', 'old_value': 20908.22, 'new_value': 22997.58}, {'field': 'order_count', 'old_value': 443, 'new_value': 475}]
2025-06-08 12:01:18,757 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOD1
2025-06-08 12:01:19,195 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOD1
2025-06-08 12:01:19,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29384.0, 'new_value': 44581.0}, {'field': 'total_amount', 'old_value': 29384.0, 'new_value': 44581.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 11}]
2025-06-08 12:01:19,195 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPD1
2025-06-08 12:01:19,664 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPD1
2025-06-08 12:01:19,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40063.21, 'new_value': 45391.47}, {'field': 'offline_amount', 'old_value': 106301.78, 'new_value': 120738.09}, {'field': 'total_amount', 'old_value': 146364.99, 'new_value': 166129.56}, {'field': 'order_count', 'old_value': 1213, 'new_value': 1413}]
2025-06-08 12:01:19,664 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMU6
2025-06-08 12:01:20,148 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMU6
2025-06-08 12:01:20,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38630.39, 'new_value': 46688.28}, {'field': 'offline_amount', 'old_value': 46000.0, 'new_value': 58000.0}, {'field': 'total_amount', 'old_value': 84630.39, 'new_value': 104688.28}, {'field': 'order_count', 'old_value': 266, 'new_value': 333}]
2025-06-08 12:01:20,148 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV6
2025-06-08 12:01:20,585 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV6
2025-06-08 12:01:20,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58951.4, 'new_value': 74140.4}, {'field': 'total_amount', 'old_value': 58951.4, 'new_value': 74140.4}, {'field': 'order_count', 'old_value': 337, 'new_value': 425}]
2025-06-08 12:01:20,585 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQD1
2025-06-08 12:01:21,070 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQD1
2025-06-08 12:01:21,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28953.27, 'new_value': 38214.05}, {'field': 'offline_amount', 'old_value': 79375.89, 'new_value': 99918.76}, {'field': 'total_amount', 'old_value': 108329.16, 'new_value': 138132.81}, {'field': 'order_count', 'old_value': 965, 'new_value': 1252}]
2025-06-08 12:01:21,070 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSD1
2025-06-08 12:01:21,523 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSD1
2025-06-08 12:01:21,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9826.6, 'new_value': 16214.6}, {'field': 'total_amount', 'old_value': 9826.6, 'new_value': 16214.6}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-06-08 12:01:21,523 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUD1
2025-06-08 12:01:21,992 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUD1
2025-06-08 12:01:21,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34960.0, 'new_value': 41140.0}, {'field': 'total_amount', 'old_value': 34960.0, 'new_value': 41140.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-08 12:01:21,992 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM78
2025-06-08 12:01:22,476 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM78
2025-06-08 12:01:22,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8196.4, 'new_value': 10323.6}, {'field': 'offline_amount', 'old_value': 290554.6, 'new_value': 333824.74}, {'field': 'total_amount', 'old_value': 298751.0, 'new_value': 344148.34}, {'field': 'order_count', 'old_value': 1236, 'new_value': 1470}]
2025-06-08 12:01:22,476 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVD1
2025-06-08 12:01:22,976 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVD1
2025-06-08 12:01:22,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15546.0, 'new_value': 18466.0}, {'field': 'total_amount', 'old_value': 15546.0, 'new_value': 18466.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 46}]
2025-06-08 12:01:22,976 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM88
2025-06-08 12:01:23,523 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM88
2025-06-08 12:01:23,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30193.0, 'new_value': 34170.0}, {'field': 'total_amount', 'old_value': 30193.0, 'new_value': 34170.0}, {'field': 'order_count', 'old_value': 910, 'new_value': 1025}]
2025-06-08 12:01:23,523 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXD1
2025-06-08 12:01:24,023 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXD1
2025-06-08 12:01:24,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16809.6, 'new_value': 22247.5}, {'field': 'total_amount', 'old_value': 17277.5, 'new_value': 22715.4}, {'field': 'order_count', 'old_value': 120, 'new_value': 151}]
2025-06-08 12:01:24,023 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYD1
2025-06-08 12:01:24,507 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYD1
2025-06-08 12:01:24,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5634.0, 'new_value': 7103.0}, {'field': 'total_amount', 'old_value': 5634.0, 'new_value': 7103.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 61}]
2025-06-08 12:01:24,507 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAQ1
2025-06-08 12:01:25,007 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAQ1
2025-06-08 12:01:25,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21746.21, 'new_value': 26045.41}, {'field': 'offline_amount', 'old_value': 209697.11, 'new_value': 256071.31}, {'field': 'total_amount', 'old_value': 231443.32, 'new_value': 282116.72}, {'field': 'order_count', 'old_value': 967, 'new_value': 1172}]
2025-06-08 12:01:25,007 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZD1
2025-06-08 12:01:25,429 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZD1
2025-06-08 12:01:25,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132752.02, 'new_value': 149774.02}, {'field': 'offline_amount', 'old_value': 91944.02, 'new_value': 109121.24}, {'field': 'total_amount', 'old_value': 224696.04, 'new_value': 258895.26}, {'field': 'order_count', 'old_value': 1560, 'new_value': 1773}]
2025-06-08 12:01:25,429 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBQ1
2025-06-08 12:01:25,835 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBQ1
2025-06-08 12:01:25,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18041.0, 'new_value': 22461.0}, {'field': 'total_amount', 'old_value': 18041.0, 'new_value': 22461.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 193}]
2025-06-08 12:01:25,835 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCQ1
2025-06-08 12:01:26,304 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCQ1
2025-06-08 12:01:26,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4108.0, 'new_value': 4196.0}, {'field': 'total_amount', 'old_value': 4108.0, 'new_value': 4196.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-08 12:01:26,304 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDQ1
2025-06-08 12:01:26,773 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDQ1
2025-06-08 12:01:26,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8627.0, 'new_value': 8657.8}, {'field': 'total_amount', 'old_value': 8627.0, 'new_value': 8657.8}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-06-08 12:01:26,773 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM37
2025-06-08 12:01:27,226 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM37
2025-06-08 12:01:27,226 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 100000.0}, {'field': 'total_amount', 'old_value': 600000.0, 'new_value': 700000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 359}]
2025-06-08 12:01:27,226 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0E1
2025-06-08 12:01:27,632 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0E1
2025-06-08 12:01:27,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62414.0, 'new_value': 76568.0}, {'field': 'total_amount', 'old_value': 62414.0, 'new_value': 76568.0}, {'field': 'order_count', 'old_value': 1986, 'new_value': 2449}]
2025-06-08 12:01:27,632 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1E1
2025-06-08 12:01:28,257 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1E1
2025-06-08 12:01:28,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69900.12, 'new_value': 87834.22}, {'field': 'total_amount', 'old_value': 69900.12, 'new_value': 87834.22}, {'field': 'order_count', 'old_value': 389, 'new_value': 490}]
2025-06-08 12:01:28,257 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFQ1
2025-06-08 12:01:28,710 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFQ1
2025-06-08 12:01:28,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35955.0, 'new_value': 45273.0}, {'field': 'offline_amount', 'old_value': 23640.0, 'new_value': 26595.0}, {'field': 'total_amount', 'old_value': 59595.0, 'new_value': 71868.0}, {'field': 'order_count', 'old_value': 813, 'new_value': 1000}]
2025-06-08 12:01:28,710 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGQ1
2025-06-08 12:01:29,101 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGQ1
2025-06-08 12:01:29,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 388752.54, 'new_value': 455584.29}, {'field': 'offline_amount', 'old_value': 28712.0, 'new_value': 38786.0}, {'field': 'total_amount', 'old_value': 417464.54, 'new_value': 494370.29}, {'field': 'order_count', 'old_value': 1569, 'new_value': 1834}]
2025-06-08 12:01:29,101 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHQ1
2025-06-08 12:01:29,601 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHQ1
2025-06-08 12:01:29,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2910.0, 'new_value': 5770.0}, {'field': 'total_amount', 'old_value': 13785.8, 'new_value': 16645.8}, {'field': 'order_count', 'old_value': 71, 'new_value': 3351}]
2025-06-08 12:01:29,601 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3E1
2025-06-08 12:01:30,007 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3E1
2025-06-08 12:01:30,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114011.18, 'new_value': 142958.95}, {'field': 'total_amount', 'old_value': 127001.45, 'new_value': 155949.22}, {'field': 'order_count', 'old_value': 583, 'new_value': 715}]
2025-06-08 12:01:30,007 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM57
2025-06-08 12:01:30,507 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM57
2025-06-08 12:01:30,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10000.0, 'new_value': 20000.0}, {'field': 'total_amount', 'old_value': 10000.0, 'new_value': 20000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-06-08 12:01:30,507 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4E1
2025-06-08 12:01:30,976 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4E1
2025-06-08 12:01:30,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3595.5, 'new_value': 24586.8}, {'field': 'offline_amount', 'old_value': 264168.3, 'new_value': 275168.3}, {'field': 'total_amount', 'old_value': 267763.8, 'new_value': 299755.1}, {'field': 'order_count', 'old_value': 306, 'new_value': 974}]
2025-06-08 12:01:30,976 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5E1
2025-06-08 12:01:31,429 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5E1
2025-06-08 12:01:31,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1587.0, 'new_value': 2506.0}, {'field': 'offline_amount', 'old_value': 11271.6, 'new_value': 12558.0}, {'field': 'total_amount', 'old_value': 12858.6, 'new_value': 15064.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 107}]
2025-06-08 12:01:31,429 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6E1
2025-06-08 12:01:31,898 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6E1
2025-06-08 12:01:31,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79518.38, 'new_value': 98571.48}, {'field': 'total_amount', 'old_value': 79518.38, 'new_value': 98571.48}, {'field': 'order_count', 'old_value': 693, 'new_value': 850}]
2025-06-08 12:01:31,898 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9E1
2025-06-08 12:01:32,320 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9E1
2025-06-08 12:01:32,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3200000.0, 'new_value': 3600000.0}, {'field': 'total_amount', 'old_value': 3200000.0, 'new_value': 3600000.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-08 12:01:32,320 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAE1
2025-06-08 12:01:32,757 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAE1
2025-06-08 12:01:32,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35180.0, 'new_value': 42160.0}, {'field': 'total_amount', 'old_value': 35180.0, 'new_value': 42160.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-08 12:01:32,757 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBE1
2025-06-08 12:01:33,273 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBE1
2025-06-08 12:01:33,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4988.5, 'new_value': 6858.5}, {'field': 'total_amount', 'old_value': 4988.5, 'new_value': 6858.5}, {'field': 'order_count', 'old_value': 8, 'new_value': 18}]
2025-06-08 12:01:33,273 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCE1
2025-06-08 12:01:33,757 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCE1
2025-06-08 12:01:33,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 28732.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 28732.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 4}]
2025-06-08 12:01:33,757 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWP
2025-06-08 12:01:34,179 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWP
2025-06-08 12:01:34,179 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4126.21, 'new_value': 4435.21}, {'field': 'offline_amount', 'old_value': 65154.8, 'new_value': 76995.8}, {'field': 'total_amount', 'old_value': 69281.01, 'new_value': 81431.01}, {'field': 'order_count', 'old_value': 352, 'new_value': 413}]
2025-06-08 12:01:34,179 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDE1
2025-06-08 12:01:34,648 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDE1
2025-06-08 12:01:34,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8786.0, 'new_value': 9836.0}, {'field': 'total_amount', 'old_value': 8786.0, 'new_value': 9836.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 27}]
2025-06-08 12:01:34,648 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMRQ1
2025-06-08 12:01:35,085 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMRQ1
2025-06-08 12:01:35,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15487.92, 'new_value': 19533.92}, {'field': 'total_amount', 'old_value': 15487.92, 'new_value': 19533.92}, {'field': 'order_count', 'old_value': 359, 'new_value': 449}]
2025-06-08 12:01:35,085 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMVQ1
2025-06-08 12:01:35,585 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMVQ1
2025-06-08 12:01:35,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8715.05, 'new_value': 9984.05}, {'field': 'total_amount', 'old_value': 8715.05, 'new_value': 9984.05}, {'field': 'order_count', 'old_value': 177, 'new_value': 207}]
2025-06-08 12:01:35,585 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFE1
2025-06-08 12:01:36,132 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFE1
2025-06-08 12:01:36,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27783.0, 'new_value': 31148.0}, {'field': 'total_amount', 'old_value': 27951.0, 'new_value': 31316.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 107}]
2025-06-08 12:01:36,132 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMYQ1
2025-06-08 12:01:36,570 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMYQ1
2025-06-08 12:01:36,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15329.51, 'new_value': 18691.01}, {'field': 'total_amount', 'old_value': 15329.51, 'new_value': 18691.01}, {'field': 'order_count', 'old_value': 96, 'new_value': 115}]
2025-06-08 12:01:36,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMZQ1
2025-06-08 12:01:37,117 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMZQ1
2025-06-08 12:01:37,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4632.0, 'new_value': 6364.0}, {'field': 'offline_amount', 'old_value': 8094.0, 'new_value': 9146.0}, {'field': 'total_amount', 'old_value': 12726.0, 'new_value': 15510.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 115}]
2025-06-08 12:01:37,117 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8C
2025-06-08 12:01:37,601 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8C
2025-06-08 12:01:37,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34980.43, 'new_value': 42006.4}, {'field': 'offline_amount', 'old_value': 12460.96, 'new_value': 14722.91}, {'field': 'total_amount', 'old_value': 47441.39, 'new_value': 56729.31}, {'field': 'order_count', 'old_value': 2632, 'new_value': 3189}]
2025-06-08 12:01:37,601 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM0R1
2025-06-08 12:01:38,054 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM0R1
2025-06-08 12:01:38,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33556.46, 'new_value': 39755.73}, {'field': 'total_amount', 'old_value': 33556.46, 'new_value': 39755.73}, {'field': 'order_count', 'old_value': 152, 'new_value': 180}]
2025-06-08 12:01:38,054 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2O
2025-06-08 12:01:38,445 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2O
2025-06-08 12:01:38,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20018.0, 'new_value': 25172.0}, {'field': 'offline_amount', 'old_value': 60834.0, 'new_value': 67990.0}, {'field': 'total_amount', 'old_value': 80852.0, 'new_value': 93162.0}, {'field': 'order_count', 'old_value': 348, 'new_value': 407}]
2025-06-08 12:01:38,445 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM2R1
2025-06-08 12:01:38,960 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM2R1
2025-06-08 12:01:38,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130949.2, 'new_value': 169780.3}, {'field': 'total_amount', 'old_value': 130949.2, 'new_value': 169780.3}, {'field': 'order_count', 'old_value': 2801, 'new_value': 3702}]
2025-06-08 12:01:38,960 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM9R1
2025-06-08 12:01:39,476 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM9R1
2025-06-08 12:01:39,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11441.83, 'new_value': 13249.61}, {'field': 'offline_amount', 'old_value': 16815.73, 'new_value': 19065.39}, {'field': 'total_amount', 'old_value': 28257.56, 'new_value': 32315.0}, {'field': 'order_count', 'old_value': 1376, 'new_value': 1576}]
2025-06-08 12:01:39,476 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8O
2025-06-08 12:01:39,945 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8O
2025-06-08 12:01:39,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5703.36, 'new_value': 6512.36}, {'field': 'offline_amount', 'old_value': 3565.0, 'new_value': 4258.0}, {'field': 'total_amount', 'old_value': 9268.36, 'new_value': 10770.36}, {'field': 'order_count', 'old_value': 342, 'new_value': 405}]
2025-06-08 12:01:39,945 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBR1
2025-06-08 12:01:40,382 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBR1
2025-06-08 12:01:40,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48584.0, 'new_value': 53039.0}, {'field': 'total_amount', 'old_value': 48584.0, 'new_value': 53039.0}, {'field': 'order_count', 'old_value': 5175, 'new_value': 5596}]
2025-06-08 12:01:40,382 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMER1
2025-06-08 12:01:40,851 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMER1
2025-06-08 12:01:40,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18957.0, 'new_value': 31400.0}, {'field': 'offline_amount', 'old_value': 302772.0, 'new_value': 361049.0}, {'field': 'total_amount', 'old_value': 321729.0, 'new_value': 392449.0}, {'field': 'order_count', 'old_value': 8129, 'new_value': 9352}]
2025-06-08 12:01:40,851 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCO
2025-06-08 12:01:41,335 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCO
2025-06-08 12:01:41,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10762.1, 'new_value': 13052.1}, {'field': 'total_amount', 'old_value': 10762.1, 'new_value': 13052.1}, {'field': 'order_count', 'old_value': 60, 'new_value': 77}]
2025-06-08 12:01:41,335 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDO
2025-06-08 12:01:41,757 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDO
2025-06-08 12:01:41,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187297.65, 'new_value': 227842.56}, {'field': 'total_amount', 'old_value': 187297.65, 'new_value': 227842.56}, {'field': 'order_count', 'old_value': 1424, 'new_value': 1731}]
2025-06-08 12:01:41,757 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGO
2025-06-08 12:01:42,257 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGO
2025-06-08 12:01:42,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168939.03, 'new_value': 208384.51}, {'field': 'total_amount', 'old_value': 168939.03, 'new_value': 208384.51}, {'field': 'order_count', 'old_value': 1046, 'new_value': 1248}]
2025-06-08 12:01:42,257 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMO
2025-06-08 12:01:42,757 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMO
2025-06-08 12:01:42,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1177495.18, 'new_value': 1482771.18}, {'field': 'total_amount', 'old_value': 1177495.18, 'new_value': 1482771.18}, {'field': 'order_count', 'old_value': 25492, 'new_value': 31118}]
2025-06-08 12:01:42,757 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNO
2025-06-08 12:01:43,132 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNO
2025-06-08 12:01:43,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243823.23, 'new_value': 295980.55}, {'field': 'total_amount', 'old_value': 243823.23, 'new_value': 295980.55}, {'field': 'order_count', 'old_value': 759, 'new_value': 905}]
2025-06-08 12:01:43,132 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOO
2025-06-08 12:01:43,585 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOO
2025-06-08 12:01:43,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180837.63, 'new_value': 218961.55}, {'field': 'total_amount', 'old_value': 180837.63, 'new_value': 218961.55}, {'field': 'order_count', 'old_value': 463, 'new_value': 585}]
2025-06-08 12:01:43,585 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPO
2025-06-08 12:01:44,038 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPO
2025-06-08 12:01:44,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19744.0, 'new_value': 21951.0}, {'field': 'total_amount', 'old_value': 19744.0, 'new_value': 21951.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 124}]
2025-06-08 12:01:44,038 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSO
2025-06-08 12:01:44,507 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSO
2025-06-08 12:01:44,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12824.61, 'new_value': 15952.08}, {'field': 'total_amount', 'old_value': 25261.12, 'new_value': 28388.59}, {'field': 'order_count', 'old_value': 1665, 'new_value': 1861}]
2025-06-08 12:01:44,507 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTO
2025-06-08 12:01:44,929 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTO
2025-06-08 12:01:44,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21831.48, 'new_value': 28572.26}, {'field': 'total_amount', 'old_value': 43764.81, 'new_value': 50505.59}, {'field': 'order_count', 'old_value': 2877, 'new_value': 3338}]
2025-06-08 12:01:44,929 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUO
2025-06-08 12:01:45,382 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUO
2025-06-08 12:01:45,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7160.0, 'new_value': 7210.0}, {'field': 'total_amount', 'old_value': 7160.0, 'new_value': 7210.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-08 12:01:45,382 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVO
2025-06-08 12:01:45,898 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVO
2025-06-08 12:01:45,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 829588.0, 'new_value': 929588.0}, {'field': 'total_amount', 'old_value': 829588.0, 'new_value': 929588.0}, {'field': 'order_count', 'old_value': 1084, 'new_value': 1447}]
2025-06-08 12:01:45,898 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXO
2025-06-08 12:01:46,320 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXO
2025-06-08 12:01:46,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-08 12:01:46,320 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYO
2025-06-08 12:01:46,773 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYO
2025-06-08 12:01:46,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60000.0, 'new_value': 70000.0}, {'field': 'total_amount', 'old_value': 60000.0, 'new_value': 70000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 126}]
2025-06-08 12:01:46,773 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZO
2025-06-08 12:01:47,148 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZO
2025-06-08 12:01:47,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 10000.0}, {'field': 'total_amount', 'old_value': 70000.0, 'new_value': 80000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 216}]
2025-06-08 12:01:47,148 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0P
2025-06-08 12:01:47,679 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0P
2025-06-08 12:01:47,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17960.0, 'new_value': 23694.0}, {'field': 'total_amount', 'old_value': 17960.0, 'new_value': 23694.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:01:47,679 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1P
2025-06-08 12:01:48,117 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1P
2025-06-08 12:01:48,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2422.78, 'new_value': 3339.88}, {'field': 'offline_amount', 'old_value': 21687.54, 'new_value': 27152.7}, {'field': 'total_amount', 'old_value': 24110.32, 'new_value': 30492.58}, {'field': 'order_count', 'old_value': 763, 'new_value': 964}]
2025-06-08 12:01:48,117 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2P
2025-06-08 12:01:48,523 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2P
2025-06-08 12:01:48,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228609.64, 'new_value': 245401.98}, {'field': 'total_amount', 'old_value': 228609.64, 'new_value': 245401.98}, {'field': 'order_count', 'old_value': 1142, 'new_value': 1610}]
2025-06-08 12:01:48,523 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4P
2025-06-08 12:01:49,007 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4P
2025-06-08 12:01:49,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127339.0, 'new_value': 148301.0}, {'field': 'total_amount', 'old_value': 127339.0, 'new_value': 148301.0}, {'field': 'order_count', 'old_value': 775, 'new_value': 900}]
2025-06-08 12:01:49,007 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5P
2025-06-08 12:01:49,538 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5P
2025-06-08 12:01:49,538 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2908.25, 'new_value': 3461.05}, {'field': 'offline_amount', 'old_value': 17128.1, 'new_value': 23080.8}, {'field': 'total_amount', 'old_value': 20036.35, 'new_value': 26541.85}, {'field': 'order_count', 'old_value': 125, 'new_value': 158}]
2025-06-08 12:01:49,538 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6P
2025-06-08 12:01:49,960 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6P
2025-06-08 12:01:49,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52911.77, 'new_value': 61973.72}, {'field': 'total_amount', 'old_value': 52911.77, 'new_value': 61973.72}, {'field': 'order_count', 'old_value': 1750, 'new_value': 2048}]
2025-06-08 12:01:49,960 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8P
2025-06-08 12:01:50,429 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8P
2025-06-08 12:01:50,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37564.16, 'new_value': 45662.93}, {'field': 'total_amount', 'old_value': 37564.16, 'new_value': 45662.93}, {'field': 'order_count', 'old_value': 2712, 'new_value': 3272}]
2025-06-08 12:01:50,429 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P
2025-06-08 12:01:51,023 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P
2025-06-08 12:01:51,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59400.0, 'new_value': 70100.0}, {'field': 'total_amount', 'old_value': 59400.0, 'new_value': 70100.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 166}]
2025-06-08 12:01:51,023 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBP
2025-06-08 12:01:51,445 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBP
2025-06-08 12:01:51,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6747.7, 'new_value': 9412.4}, {'field': 'offline_amount', 'old_value': 31948.0, 'new_value': 39882.0}, {'field': 'total_amount', 'old_value': 38695.7, 'new_value': 49294.4}, {'field': 'order_count', 'old_value': 526, 'new_value': 627}]
2025-06-08 12:01:51,445 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCP
2025-06-08 12:01:51,898 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCP
2025-06-08 12:01:51,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60000.0, 'new_value': 70000.0}, {'field': 'total_amount', 'old_value': 60000.0, 'new_value': 70000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 156}]
2025-06-08 12:01:51,898 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDP
2025-06-08 12:01:52,335 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDP
2025-06-08 12:01:52,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44676.55, 'new_value': 52100.7}, {'field': 'total_amount', 'old_value': 44676.55, 'new_value': 52100.7}, {'field': 'order_count', 'old_value': 800, 'new_value': 917}]
2025-06-08 12:01:52,335 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEP
2025-06-08 12:01:52,773 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEP
2025-06-08 12:01:52,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110948.0, 'new_value': 120948.0}, {'field': 'total_amount', 'old_value': 110948.0, 'new_value': 120948.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-08 12:01:52,773 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFP
2025-06-08 12:01:53,210 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFP
2025-06-08 12:01:53,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144250.0, 'new_value': 154235.0}, {'field': 'total_amount', 'old_value': 144250.0, 'new_value': 154235.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 167}]
2025-06-08 12:01:53,210 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHP
2025-06-08 12:01:53,663 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHP
2025-06-08 12:01:53,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154632.0, 'new_value': 187074.0}, {'field': 'total_amount', 'old_value': 154632.0, 'new_value': 187074.0}, {'field': 'order_count', 'old_value': 3550, 'new_value': 4298}]
2025-06-08 12:01:53,663 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJP
2025-06-08 12:01:54,132 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJP
2025-06-08 12:01:54,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31626.75, 'new_value': 38689.05}, {'field': 'total_amount', 'old_value': 31626.75, 'new_value': 38689.05}, {'field': 'order_count', 'old_value': 812, 'new_value': 974}]
2025-06-08 12:01:54,132 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKP
2025-06-08 12:01:54,570 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKP
2025-06-08 12:01:54,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17473.54, 'new_value': 38965.51}, {'field': 'total_amount', 'old_value': 78709.21, 'new_value': 100201.18}, {'field': 'order_count', 'old_value': 508, 'new_value': 670}]
2025-06-08 12:01:54,570 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYP
2025-06-08 12:01:55,007 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYP
2025-06-08 12:01:55,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77402.34, 'new_value': 90643.32}, {'field': 'total_amount', 'old_value': 77402.34, 'new_value': 90643.32}, {'field': 'order_count', 'old_value': 223, 'new_value': 259}]
2025-06-08 12:01:55,007 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9Q
2025-06-08 12:01:55,460 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9Q
2025-06-08 12:01:55,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110120.0, 'new_value': 135677.0}, {'field': 'total_amount', 'old_value': 110120.0, 'new_value': 135677.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 29}]
2025-06-08 12:01:55,460 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1Q
2025-06-08 12:01:56,023 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1Q
2025-06-08 12:01:56,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 726719.56, 'new_value': 891041.35}, {'field': 'total_amount', 'old_value': 726719.56, 'new_value': 891041.35}, {'field': 'order_count', 'old_value': 1389, 'new_value': 1684}]
2025-06-08 12:01:56,023 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2Q
2025-06-08 12:01:56,491 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2Q
2025-06-08 12:01:56,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1341.0, 'new_value': 1809.0}, {'field': 'offline_amount', 'old_value': 4811.0, 'new_value': 5211.0}, {'field': 'total_amount', 'old_value': 6152.0, 'new_value': 7020.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-06-08 12:01:56,491 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3Q
2025-06-08 12:01:57,085 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3Q
2025-06-08 12:01:57,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215227.0, 'new_value': 260683.0}, {'field': 'total_amount', 'old_value': 215227.0, 'new_value': 260683.0}, {'field': 'order_count', 'old_value': 1016, 'new_value': 1259}]
2025-06-08 12:01:57,085 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4Q
2025-06-08 12:01:57,616 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4Q
2025-06-08 12:01:57,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2344137.73, 'new_value': 2930589.73}, {'field': 'total_amount', 'old_value': 2344137.73, 'new_value': 2930589.73}, {'field': 'order_count', 'old_value': 8899, 'new_value': 11009}]
2025-06-08 12:01:57,616 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6Q
2025-06-08 12:01:58,023 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6Q
2025-06-08 12:01:58,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50871.55, 'new_value': 60837.89}, {'field': 'offline_amount', 'old_value': 45019.43, 'new_value': 55972.36}, {'field': 'total_amount', 'old_value': 95890.98, 'new_value': 116810.25}, {'field': 'order_count', 'old_value': 4077, 'new_value': 4962}]
2025-06-08 12:01:58,023 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMLK
2025-06-08 12:01:58,491 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMLK
2025-06-08 12:01:58,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40047.7, 'new_value': 61223.7}, {'field': 'total_amount', 'old_value': 69559.8, 'new_value': 90735.8}, {'field': 'order_count', 'old_value': 180, 'new_value': 213}]
2025-06-08 12:01:58,491 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMNK
2025-06-08 12:01:58,991 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMNK
2025-06-08 12:01:58,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42575.0, 'new_value': 48133.0}, {'field': 'total_amount', 'old_value': 42575.0, 'new_value': 48133.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 211}]
2025-06-08 12:01:58,991 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM2
2025-06-08 12:01:59,507 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM2
2025-06-08 12:01:59,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8484.0, 'new_value': 10445.0}, {'field': 'total_amount', 'old_value': 8484.0, 'new_value': 10445.0}, {'field': 'order_count', 'old_value': 827, 'new_value': 1037}]
2025-06-08 12:01:59,507 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM3
2025-06-08 12:02:00,007 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM3
2025-06-08 12:02:00,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 308000.0, 'new_value': 358000.0}, {'field': 'total_amount', 'old_value': 308000.0, 'new_value': 358000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:02:00,007 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM4
2025-06-08 12:02:00,398 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM4
2025-06-08 12:02:00,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84370.0, 'new_value': 99819.0}, {'field': 'total_amount', 'old_value': 84370.0, 'new_value': 99819.0}, {'field': 'order_count', 'old_value': 2245, 'new_value': 2613}]
2025-06-08 12:02:00,398 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM6
2025-06-08 12:02:00,898 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM6
2025-06-08 12:02:00,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36822.0, 'new_value': 50914.0}, {'field': 'total_amount', 'old_value': 36822.0, 'new_value': 50914.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 14}]
2025-06-08 12:02:00,898 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMA
2025-06-08 12:02:01,366 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMA
2025-06-08 12:02:01,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47600.0, 'new_value': 57296.0}, {'field': 'total_amount', 'old_value': 47600.0, 'new_value': 57296.0}, {'field': 'order_count', 'old_value': 1031, 'new_value': 1263}]
2025-06-08 12:02:01,366 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMD
2025-06-08 12:02:01,820 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMD
2025-06-08 12:02:01,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6081.0, 'new_value': 7401.0}, {'field': 'total_amount', 'old_value': 6081.0, 'new_value': 7401.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:02:01,820 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBME
2025-06-08 12:02:02,335 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBME
2025-06-08 12:02:02,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343.5, 'new_value': 1540.6}, {'field': 'total_amount', 'old_value': 343.5, 'new_value': 1540.6}, {'field': 'order_count', 'old_value': 11, 'new_value': 122}]
2025-06-08 12:02:02,335 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMG
2025-06-08 12:02:02,851 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMG
2025-06-08 12:02:02,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91000.0, 'new_value': 98000.0}, {'field': 'total_amount', 'old_value': 91000.0, 'new_value': 98000.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-08 12:02:02,851 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMI
2025-06-08 12:02:03,288 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMI
2025-06-08 12:02:03,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94172.0, 'new_value': 97732.0}, {'field': 'total_amount', 'old_value': 94172.0, 'new_value': 97732.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-08 12:02:03,288 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMK
2025-06-08 12:02:03,773 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMK
2025-06-08 12:02:03,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29322.0, 'new_value': 36041.0}, {'field': 'total_amount', 'old_value': 29322.0, 'new_value': 36041.0}, {'field': 'order_count', 'old_value': 1336, 'new_value': 1625}]
2025-06-08 12:02:03,773 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMM
2025-06-08 12:02:04,288 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMM
2025-06-08 12:02:04,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24698.0, 'new_value': 30626.0}, {'field': 'total_amount', 'old_value': 24698.0, 'new_value': 30626.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 218}]
2025-06-08 12:02:04,288 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMQ
2025-06-08 12:02:04,773 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMQ
2025-06-08 12:02:04,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7784.0, 'new_value': 9402.0}, {'field': 'total_amount', 'old_value': 7784.0, 'new_value': 9402.0}, {'field': 'order_count', 'old_value': 729, 'new_value': 878}]
2025-06-08 12:02:04,773 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMX
2025-06-08 12:02:05,304 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMX
2025-06-08 12:02:05,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11801.0, 'new_value': 15847.0}, {'field': 'total_amount', 'old_value': 11801.0, 'new_value': 15847.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 16}]
2025-06-08 12:02:05,304 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMZ
2025-06-08 12:02:05,804 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMZ
2025-06-08 12:02:05,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1302063.0, 'new_value': 1600546.0}, {'field': 'total_amount', 'old_value': 1302063.0, 'new_value': 1600546.0}, {'field': 'order_count', 'old_value': 23990, 'new_value': 28910}]
2025-06-08 12:02:05,804 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM01
2025-06-08 12:02:06,273 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM01
2025-06-08 12:02:06,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13886.0, 'new_value': 15685.0}, {'field': 'total_amount', 'old_value': 13886.0, 'new_value': 15685.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:02:06,273 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM21
2025-06-08 12:02:06,757 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM21
2025-06-08 12:02:06,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69495.55, 'new_value': 80724.41}, {'field': 'total_amount', 'old_value': 69495.55, 'new_value': 80724.41}, {'field': 'order_count', 'old_value': 134, 'new_value': 170}]
2025-06-08 12:02:06,757 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM31
2025-06-08 12:02:07,241 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM31
2025-06-08 12:02:07,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1168.7, 'new_value': 1592.7}, {'field': 'total_amount', 'old_value': 1256.7, 'new_value': 1680.7}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-08 12:02:07,241 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM51
2025-06-08 12:02:07,695 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM51
2025-06-08 12:02:07,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2331700.0, 'new_value': 2490000.0}, {'field': 'total_amount', 'old_value': 2331700.0, 'new_value': 2490000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 12:02:07,695 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM71
2025-06-08 12:02:08,132 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM71
2025-06-08 12:02:08,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8980.44, 'new_value': 11165.12}, {'field': 'total_amount', 'old_value': 8980.44, 'new_value': 11165.12}, {'field': 'order_count', 'old_value': 326, 'new_value': 405}]
2025-06-08 12:02:08,132 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM61
2025-06-08 12:02:08,616 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM61
2025-06-08 12:02:08,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35423.84, 'new_value': 42009.01}, {'field': 'total_amount', 'old_value': 35423.84, 'new_value': 42009.01}, {'field': 'order_count', 'old_value': 2666, 'new_value': 3176}]
2025-06-08 12:02:08,616 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM91
2025-06-08 12:02:09,054 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM91
2025-06-08 12:02:09,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102000.0, 'new_value': 126000.0}, {'field': 'total_amount', 'old_value': 102000.0, 'new_value': 126000.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-08 12:02:09,054 - INFO - 开始更新记录 - 表单实例ID: FINST-5A966081KR2WKZ2OCIITF9X4YONC3OIQ4JLBMU
2025-06-08 12:02:09,601 - INFO - 更新表单数据成功: FINST-5A966081KR2WKZ2OCIITF9X4YONC3OIQ4JLBMU
2025-06-08 12:02:09,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2000.0, 'new_value': 3500.0}, {'field': 'total_amount', 'old_value': 2000.0, 'new_value': 3500.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 272}]
2025-06-08 12:02:09,601 - INFO - 日期 2025-06 处理完成 - 更新: 197 条，插入: 0 条，错误: 0 条
2025-06-08 12:02:09,601 - INFO - 数据同步完成！更新: 197 条，插入: 0 条，错误: 0 条
2025-06-08 12:02:09,601 - INFO - =================同步完成====================
2025-06-08 15:00:02,624 - INFO - =================使用默认全量同步=============
2025-06-08 15:00:04,242 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 15:00:04,242 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 15:00:04,275 - INFO - 开始处理日期: 2025-01
2025-06-08 15:00:04,279 - INFO - Request Parameters - Page 1:
2025-06-08 15:00:04,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:04,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:05,017 - INFO - Response - Page 1:
2025-06-08 15:00:05,217 - INFO - 第 1 页获取到 100 条记录
2025-06-08 15:00:05,217 - INFO - Request Parameters - Page 2:
2025-06-08 15:00:05,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:05,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:05,939 - INFO - Response - Page 2:
2025-06-08 15:00:06,139 - INFO - 第 2 页获取到 100 条记录
2025-06-08 15:00:06,139 - INFO - Request Parameters - Page 3:
2025-06-08 15:00:06,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:06,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:06,765 - INFO - Response - Page 3:
2025-06-08 15:00:06,965 - INFO - 第 3 页获取到 100 条记录
2025-06-08 15:00:06,965 - INFO - Request Parameters - Page 4:
2025-06-08 15:00:06,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:06,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:07,528 - INFO - Response - Page 4:
2025-06-08 15:00:07,728 - INFO - 第 4 页获取到 100 条记录
2025-06-08 15:00:07,728 - INFO - Request Parameters - Page 5:
2025-06-08 15:00:07,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:07,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:08,215 - INFO - Response - Page 5:
2025-06-08 15:00:08,415 - INFO - 第 5 页获取到 100 条记录
2025-06-08 15:00:08,415 - INFO - Request Parameters - Page 6:
2025-06-08 15:00:08,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:08,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:08,903 - INFO - Response - Page 6:
2025-06-08 15:00:09,103 - INFO - 第 6 页获取到 100 条记录
2025-06-08 15:00:09,103 - INFO - Request Parameters - Page 7:
2025-06-08 15:00:09,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:09,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:09,582 - INFO - Response - Page 7:
2025-06-08 15:00:09,783 - INFO - 第 7 页获取到 82 条记录
2025-06-08 15:00:09,783 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 15:00:09,783 - INFO - 获取到 682 条表单数据
2025-06-08 15:00:09,796 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 15:00:09,808 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:09,808 - INFO - 开始处理日期: 2025-02
2025-06-08 15:00:09,808 - INFO - Request Parameters - Page 1:
2025-06-08 15:00:09,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:09,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:10,312 - INFO - Response - Page 1:
2025-06-08 15:00:10,512 - INFO - 第 1 页获取到 100 条记录
2025-06-08 15:00:10,512 - INFO - Request Parameters - Page 2:
2025-06-08 15:00:10,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:10,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:11,067 - INFO - Response - Page 2:
2025-06-08 15:00:11,268 - INFO - 第 2 页获取到 100 条记录
2025-06-08 15:00:11,268 - INFO - Request Parameters - Page 3:
2025-06-08 15:00:11,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:11,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:11,788 - INFO - Response - Page 3:
2025-06-08 15:00:11,990 - INFO - 第 3 页获取到 100 条记录
2025-06-08 15:00:11,990 - INFO - Request Parameters - Page 4:
2025-06-08 15:00:11,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:11,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:12,450 - INFO - Response - Page 4:
2025-06-08 15:00:12,656 - INFO - 第 4 页获取到 100 条记录
2025-06-08 15:00:12,656 - INFO - Request Parameters - Page 5:
2025-06-08 15:00:12,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:12,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:13,179 - INFO - Response - Page 5:
2025-06-08 15:00:13,379 - INFO - 第 5 页获取到 100 条记录
2025-06-08 15:00:13,379 - INFO - Request Parameters - Page 6:
2025-06-08 15:00:13,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:13,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:13,983 - INFO - Response - Page 6:
2025-06-08 15:00:14,183 - INFO - 第 6 页获取到 100 条记录
2025-06-08 15:00:14,183 - INFO - Request Parameters - Page 7:
2025-06-08 15:00:14,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:14,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:14,664 - INFO - Response - Page 7:
2025-06-08 15:00:14,865 - INFO - 第 7 页获取到 70 条记录
2025-06-08 15:00:14,865 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 15:00:14,865 - INFO - 获取到 670 条表单数据
2025-06-08 15:00:14,877 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 15:00:14,889 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:14,889 - INFO - 开始处理日期: 2025-03
2025-06-08 15:00:14,889 - INFO - Request Parameters - Page 1:
2025-06-08 15:00:14,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:14,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:15,405 - INFO - Response - Page 1:
2025-06-08 15:00:15,607 - INFO - 第 1 页获取到 100 条记录
2025-06-08 15:00:15,607 - INFO - Request Parameters - Page 2:
2025-06-08 15:00:15,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:15,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:16,133 - INFO - Response - Page 2:
2025-06-08 15:00:16,333 - INFO - 第 2 页获取到 100 条记录
2025-06-08 15:00:16,333 - INFO - Request Parameters - Page 3:
2025-06-08 15:00:16,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:16,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:16,951 - INFO - Response - Page 3:
2025-06-08 15:00:17,151 - INFO - 第 3 页获取到 100 条记录
2025-06-08 15:00:17,151 - INFO - Request Parameters - Page 4:
2025-06-08 15:00:17,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:17,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:17,684 - INFO - Response - Page 4:
2025-06-08 15:00:17,884 - INFO - 第 4 页获取到 100 条记录
2025-06-08 15:00:17,884 - INFO - Request Parameters - Page 5:
2025-06-08 15:00:17,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:17,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:18,448 - INFO - Response - Page 5:
2025-06-08 15:00:18,649 - INFO - 第 5 页获取到 100 条记录
2025-06-08 15:00:18,649 - INFO - Request Parameters - Page 6:
2025-06-08 15:00:18,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:18,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:19,153 - INFO - Response - Page 6:
2025-06-08 15:00:19,353 - INFO - 第 6 页获取到 100 条记录
2025-06-08 15:00:19,353 - INFO - Request Parameters - Page 7:
2025-06-08 15:00:19,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:19,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:19,762 - INFO - Response - Page 7:
2025-06-08 15:00:19,962 - INFO - 第 7 页获取到 61 条记录
2025-06-08 15:00:19,962 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 15:00:19,963 - INFO - 获取到 661 条表单数据
2025-06-08 15:00:19,977 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 15:00:19,989 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:19,989 - INFO - 开始处理日期: 2025-04
2025-06-08 15:00:19,989 - INFO - Request Parameters - Page 1:
2025-06-08 15:00:19,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:19,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:20,544 - INFO - Response - Page 1:
2025-06-08 15:00:20,744 - INFO - 第 1 页获取到 100 条记录
2025-06-08 15:00:20,744 - INFO - Request Parameters - Page 2:
2025-06-08 15:00:20,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:20,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:21,347 - INFO - Response - Page 2:
2025-06-08 15:00:21,556 - INFO - 第 2 页获取到 100 条记录
2025-06-08 15:00:21,556 - INFO - Request Parameters - Page 3:
2025-06-08 15:00:21,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:21,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:22,090 - INFO - Response - Page 3:
2025-06-08 15:00:22,290 - INFO - 第 3 页获取到 100 条记录
2025-06-08 15:00:22,290 - INFO - Request Parameters - Page 4:
2025-06-08 15:00:22,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:22,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:22,824 - INFO - Response - Page 4:
2025-06-08 15:00:23,024 - INFO - 第 4 页获取到 100 条记录
2025-06-08 15:00:23,024 - INFO - Request Parameters - Page 5:
2025-06-08 15:00:23,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:23,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:23,534 - INFO - Response - Page 5:
2025-06-08 15:00:23,734 - INFO - 第 5 页获取到 100 条记录
2025-06-08 15:00:23,734 - INFO - Request Parameters - Page 6:
2025-06-08 15:00:23,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:23,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:24,323 - INFO - Response - Page 6:
2025-06-08 15:00:24,534 - INFO - 第 6 页获取到 100 条记录
2025-06-08 15:00:24,534 - INFO - Request Parameters - Page 7:
2025-06-08 15:00:24,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:24,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:24,951 - INFO - Response - Page 7:
2025-06-08 15:00:25,152 - INFO - 第 7 页获取到 56 条记录
2025-06-08 15:00:25,152 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 15:00:25,152 - INFO - 获取到 656 条表单数据
2025-06-08 15:00:25,164 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 15:00:25,176 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:25,176 - INFO - 开始处理日期: 2025-05
2025-06-08 15:00:25,176 - INFO - Request Parameters - Page 1:
2025-06-08 15:00:25,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:25,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:25,761 - INFO - Response - Page 1:
2025-06-08 15:00:25,962 - INFO - 第 1 页获取到 100 条记录
2025-06-08 15:00:25,962 - INFO - Request Parameters - Page 2:
2025-06-08 15:00:25,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:25,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:26,487 - INFO - Response - Page 2:
2025-06-08 15:00:26,687 - INFO - 第 2 页获取到 100 条记录
2025-06-08 15:00:26,687 - INFO - Request Parameters - Page 3:
2025-06-08 15:00:26,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:26,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:27,205 - INFO - Response - Page 3:
2025-06-08 15:00:27,406 - INFO - 第 3 页获取到 100 条记录
2025-06-08 15:00:27,406 - INFO - Request Parameters - Page 4:
2025-06-08 15:00:27,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:27,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:27,857 - INFO - Response - Page 4:
2025-06-08 15:00:28,057 - INFO - 第 4 页获取到 100 条记录
2025-06-08 15:00:28,057 - INFO - Request Parameters - Page 5:
2025-06-08 15:00:28,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:28,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:28,547 - INFO - Response - Page 5:
2025-06-08 15:00:28,747 - INFO - 第 5 页获取到 100 条记录
2025-06-08 15:00:28,747 - INFO - Request Parameters - Page 6:
2025-06-08 15:00:28,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:28,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:29,224 - INFO - Response - Page 6:
2025-06-08 15:00:29,425 - INFO - 第 6 页获取到 100 条记录
2025-06-08 15:00:29,425 - INFO - Request Parameters - Page 7:
2025-06-08 15:00:29,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:29,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:29,849 - INFO - Response - Page 7:
2025-06-08 15:00:30,049 - INFO - 第 7 页获取到 38 条记录
2025-06-08 15:00:30,049 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 15:00:30,049 - INFO - 获取到 638 条表单数据
2025-06-08 15:00:30,062 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 15:00:30,074 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:30,074 - INFO - 开始处理日期: 2025-06
2025-06-08 15:00:30,074 - INFO - Request Parameters - Page 1:
2025-06-08 15:00:30,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:30,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:30,533 - INFO - Response - Page 1:
2025-06-08 15:00:30,733 - INFO - 第 1 页获取到 100 条记录
2025-06-08 15:00:30,733 - INFO - Request Parameters - Page 2:
2025-06-08 15:00:30,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:30,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:31,245 - INFO - Response - Page 2:
2025-06-08 15:00:31,446 - INFO - 第 2 页获取到 100 条记录
2025-06-08 15:00:31,446 - INFO - Request Parameters - Page 3:
2025-06-08 15:00:31,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:31,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:31,951 - INFO - Response - Page 3:
2025-06-08 15:00:32,152 - INFO - 第 3 页获取到 100 条记录
2025-06-08 15:00:32,152 - INFO - Request Parameters - Page 4:
2025-06-08 15:00:32,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:32,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:32,893 - INFO - Response - Page 4:
2025-06-08 15:00:33,093 - INFO - 第 4 页获取到 100 条记录
2025-06-08 15:00:33,093 - INFO - Request Parameters - Page 5:
2025-06-08 15:00:33,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:33,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:33,611 - INFO - Response - Page 5:
2025-06-08 15:00:33,811 - INFO - 第 5 页获取到 100 条记录
2025-06-08 15:00:33,811 - INFO - Request Parameters - Page 6:
2025-06-08 15:00:33,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:33,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:34,468 - INFO - Response - Page 6:
2025-06-08 15:00:34,668 - INFO - 第 6 页获取到 100 条记录
2025-06-08 15:00:34,668 - INFO - Request Parameters - Page 7:
2025-06-08 15:00:34,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 15:00:34,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 15:00:34,979 - INFO - Response - Page 7:
2025-06-08 15:00:35,179 - INFO - 第 7 页获取到 19 条记录
2025-06-08 15:00:35,179 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 15:00:35,179 - INFO - 获取到 619 条表单数据
2025-06-08 15:00:35,191 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 15:00:35,194 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU6
2025-06-08 15:00:35,574 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU6
2025-06-08 15:00:35,574 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 935.0, 'new_value': 1190.0}, {'field': 'offline_amount', 'old_value': 6293.0, 'new_value': 7967.7}, {'field': 'total_amount', 'old_value': 7228.0, 'new_value': 9157.7}, {'field': 'order_count', 'old_value': 265, 'new_value': 337}]
2025-06-08 15:00:35,575 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1
2025-06-08 15:00:36,010 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1
2025-06-08 15:00:36,010 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43601.0, 'new_value': 52337.0}, {'field': 'offline_amount', 'old_value': 39883.0, 'new_value': 49051.0}, {'field': 'total_amount', 'old_value': 83484.0, 'new_value': 101388.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 53}]
2025-06-08 15:00:36,014 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW6
2025-06-08 15:00:36,506 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW6
2025-06-08 15:00:36,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187677.26, 'new_value': 219073.17}, {'field': 'total_amount', 'old_value': 187677.26, 'new_value': 219073.17}, {'field': 'order_count', 'old_value': 1089, 'new_value': 1324}]
2025-06-08 15:00:36,506 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7E1
2025-06-08 15:00:36,937 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7E1
2025-06-08 15:00:36,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38945.93, 'new_value': 47746.09}, {'field': 'offline_amount', 'old_value': 73137.06, 'new_value': 90567.12}, {'field': 'total_amount', 'old_value': 112082.99, 'new_value': 138313.21}, {'field': 'order_count', 'old_value': 937, 'new_value': 1143}]
2025-06-08 15:00:36,940 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUP
2025-06-08 15:00:37,406 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUP
2025-06-08 15:00:37,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9275.0, 'new_value': 10865.0}, {'field': 'total_amount', 'old_value': 12720.0, 'new_value': 14310.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 54}]
2025-06-08 15:00:37,406 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZP
2025-06-08 15:00:37,915 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZP
2025-06-08 15:00:37,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72110.0, 'new_value': 83559.0}, {'field': 'total_amount', 'old_value': 72110.0, 'new_value': 83559.0}, {'field': 'order_count', 'old_value': 1750, 'new_value': 1978}]
2025-06-08 15:00:37,915 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5Q
2025-06-08 15:00:38,345 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5Q
2025-06-08 15:00:38,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39585.38, 'new_value': 48384.17}, {'field': 'total_amount', 'old_value': 39585.38, 'new_value': 48384.17}, {'field': 'order_count', 'old_value': 4221, 'new_value': 5235}]
2025-06-08 15:00:38,346 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM81
2025-06-08 15:00:38,909 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM81
2025-06-08 15:00:38,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48525.28, 'new_value': 61176.28}, {'field': 'total_amount', 'old_value': 48525.28, 'new_value': 61176.28}, {'field': 'order_count', 'old_value': 971, 'new_value': 1330}]
2025-06-08 15:00:38,909 - INFO - 日期 2025-06 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:38,909 - INFO - 数据同步完成！更新: 8 条，插入: 0 条，错误: 0 条
2025-06-08 15:00:38,911 - INFO - =================同步完成====================
2025-06-08 18:00:02,258 - INFO - =================使用默认全量同步=============
2025-06-08 18:00:03,875 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 18:00:03,876 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 18:00:03,910 - INFO - 开始处理日期: 2025-01
2025-06-08 18:00:03,913 - INFO - Request Parameters - Page 1:
2025-06-08 18:00:03,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:03,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:05,100 - INFO - Response - Page 1:
2025-06-08 18:00:05,305 - INFO - 第 1 页获取到 100 条记录
2025-06-08 18:00:05,305 - INFO - Request Parameters - Page 2:
2025-06-08 18:00:05,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:05,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:05,761 - INFO - Response - Page 2:
2025-06-08 18:00:05,961 - INFO - 第 2 页获取到 100 条记录
2025-06-08 18:00:05,961 - INFO - Request Parameters - Page 3:
2025-06-08 18:00:05,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:05,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:06,695 - INFO - Response - Page 3:
2025-06-08 18:00:06,895 - INFO - 第 3 页获取到 100 条记录
2025-06-08 18:00:06,895 - INFO - Request Parameters - Page 4:
2025-06-08 18:00:06,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:06,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:07,362 - INFO - Response - Page 4:
2025-06-08 18:00:07,562 - INFO - 第 4 页获取到 100 条记录
2025-06-08 18:00:07,562 - INFO - Request Parameters - Page 5:
2025-06-08 18:00:07,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:07,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:08,216 - INFO - Response - Page 5:
2025-06-08 18:00:08,416 - INFO - 第 5 页获取到 100 条记录
2025-06-08 18:00:08,416 - INFO - Request Parameters - Page 6:
2025-06-08 18:00:08,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:08,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:08,850 - INFO - Response - Page 6:
2025-06-08 18:00:09,052 - INFO - 第 6 页获取到 100 条记录
2025-06-08 18:00:09,052 - INFO - Request Parameters - Page 7:
2025-06-08 18:00:09,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:09,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:09,596 - INFO - Response - Page 7:
2025-06-08 18:00:09,796 - INFO - 第 7 页获取到 82 条记录
2025-06-08 18:00:09,796 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 18:00:09,796 - INFO - 获取到 682 条表单数据
2025-06-08 18:00:09,809 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 18:00:09,821 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:09,821 - INFO - 开始处理日期: 2025-02
2025-06-08 18:00:09,822 - INFO - Request Parameters - Page 1:
2025-06-08 18:00:09,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:09,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:10,355 - INFO - Response - Page 1:
2025-06-08 18:00:10,555 - INFO - 第 1 页获取到 100 条记录
2025-06-08 18:00:10,555 - INFO - Request Parameters - Page 2:
2025-06-08 18:00:10,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:10,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:11,026 - INFO - Response - Page 2:
2025-06-08 18:00:11,229 - INFO - 第 2 页获取到 100 条记录
2025-06-08 18:00:11,229 - INFO - Request Parameters - Page 3:
2025-06-08 18:00:11,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:11,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:11,746 - INFO - Response - Page 3:
2025-06-08 18:00:11,946 - INFO - 第 3 页获取到 100 条记录
2025-06-08 18:00:11,946 - INFO - Request Parameters - Page 4:
2025-06-08 18:00:11,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:11,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:12,441 - INFO - Response - Page 4:
2025-06-08 18:00:12,648 - INFO - 第 4 页获取到 100 条记录
2025-06-08 18:00:12,648 - INFO - Request Parameters - Page 5:
2025-06-08 18:00:12,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:12,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:13,099 - INFO - Response - Page 5:
2025-06-08 18:00:13,299 - INFO - 第 5 页获取到 100 条记录
2025-06-08 18:00:13,299 - INFO - Request Parameters - Page 6:
2025-06-08 18:00:13,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:13,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:13,771 - INFO - Response - Page 6:
2025-06-08 18:00:13,982 - INFO - 第 6 页获取到 100 条记录
2025-06-08 18:00:13,982 - INFO - Request Parameters - Page 7:
2025-06-08 18:00:13,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:13,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:14,686 - INFO - Response - Page 7:
2025-06-08 18:00:14,886 - INFO - 第 7 页获取到 70 条记录
2025-06-08 18:00:14,886 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 18:00:14,886 - INFO - 获取到 670 条表单数据
2025-06-08 18:00:14,898 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 18:00:14,910 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:14,911 - INFO - 开始处理日期: 2025-03
2025-06-08 18:00:14,911 - INFO - Request Parameters - Page 1:
2025-06-08 18:00:14,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:14,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:15,427 - INFO - Response - Page 1:
2025-06-08 18:00:15,627 - INFO - 第 1 页获取到 100 条记录
2025-06-08 18:00:15,627 - INFO - Request Parameters - Page 2:
2025-06-08 18:00:15,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:15,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:16,130 - INFO - Response - Page 2:
2025-06-08 18:00:16,331 - INFO - 第 2 页获取到 100 条记录
2025-06-08 18:00:16,331 - INFO - Request Parameters - Page 3:
2025-06-08 18:00:16,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:16,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:16,882 - INFO - Response - Page 3:
2025-06-08 18:00:17,096 - INFO - 第 3 页获取到 100 条记录
2025-06-08 18:00:17,096 - INFO - Request Parameters - Page 4:
2025-06-08 18:00:17,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:17,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:17,609 - INFO - Response - Page 4:
2025-06-08 18:00:17,822 - INFO - 第 4 页获取到 100 条记录
2025-06-08 18:00:17,822 - INFO - Request Parameters - Page 5:
2025-06-08 18:00:17,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:17,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:18,307 - INFO - Response - Page 5:
2025-06-08 18:00:18,508 - INFO - 第 5 页获取到 100 条记录
2025-06-08 18:00:18,508 - INFO - Request Parameters - Page 6:
2025-06-08 18:00:18,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:18,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:19,016 - INFO - Response - Page 6:
2025-06-08 18:00:19,216 - INFO - 第 6 页获取到 100 条记录
2025-06-08 18:00:19,216 - INFO - Request Parameters - Page 7:
2025-06-08 18:00:19,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:19,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:19,592 - INFO - Response - Page 7:
2025-06-08 18:00:19,795 - INFO - 第 7 页获取到 61 条记录
2025-06-08 18:00:19,795 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 18:00:19,795 - INFO - 获取到 661 条表单数据
2025-06-08 18:00:19,795 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 18:00:19,811 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:19,811 - INFO - 开始处理日期: 2025-04
2025-06-08 18:00:19,811 - INFO - Request Parameters - Page 1:
2025-06-08 18:00:19,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:19,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:20,555 - INFO - Response - Page 1:
2025-06-08 18:00:20,758 - INFO - 第 1 页获取到 100 条记录
2025-06-08 18:00:20,758 - INFO - Request Parameters - Page 2:
2025-06-08 18:00:20,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:20,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:21,259 - INFO - Response - Page 2:
2025-06-08 18:00:21,459 - INFO - 第 2 页获取到 100 条记录
2025-06-08 18:00:21,459 - INFO - Request Parameters - Page 3:
2025-06-08 18:00:21,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:21,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:21,962 - INFO - Response - Page 3:
2025-06-08 18:00:22,170 - INFO - 第 3 页获取到 100 条记录
2025-06-08 18:00:22,170 - INFO - Request Parameters - Page 4:
2025-06-08 18:00:22,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:22,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:22,617 - INFO - Response - Page 4:
2025-06-08 18:00:22,820 - INFO - 第 4 页获取到 100 条记录
2025-06-08 18:00:22,820 - INFO - Request Parameters - Page 5:
2025-06-08 18:00:22,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:22,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:23,348 - INFO - Response - Page 5:
2025-06-08 18:00:23,549 - INFO - 第 5 页获取到 100 条记录
2025-06-08 18:00:23,549 - INFO - Request Parameters - Page 6:
2025-06-08 18:00:23,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:23,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:24,193 - INFO - Response - Page 6:
2025-06-08 18:00:24,395 - INFO - 第 6 页获取到 100 条记录
2025-06-08 18:00:24,395 - INFO - Request Parameters - Page 7:
2025-06-08 18:00:24,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:24,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:24,793 - INFO - Response - Page 7:
2025-06-08 18:00:24,999 - INFO - 第 7 页获取到 56 条记录
2025-06-08 18:00:24,999 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 18:00:24,999 - INFO - 获取到 656 条表单数据
2025-06-08 18:00:24,999 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 18:00:25,015 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:25,015 - INFO - 开始处理日期: 2025-05
2025-06-08 18:00:25,015 - INFO - Request Parameters - Page 1:
2025-06-08 18:00:25,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:25,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:25,521 - INFO - Response - Page 1:
2025-06-08 18:00:25,721 - INFO - 第 1 页获取到 100 条记录
2025-06-08 18:00:25,721 - INFO - Request Parameters - Page 2:
2025-06-08 18:00:25,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:25,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:26,237 - INFO - Response - Page 2:
2025-06-08 18:00:26,438 - INFO - 第 2 页获取到 100 条记录
2025-06-08 18:00:26,438 - INFO - Request Parameters - Page 3:
2025-06-08 18:00:26,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:26,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:26,914 - INFO - Response - Page 3:
2025-06-08 18:00:27,118 - INFO - 第 3 页获取到 100 条记录
2025-06-08 18:00:27,118 - INFO - Request Parameters - Page 4:
2025-06-08 18:00:27,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:27,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:27,615 - INFO - Response - Page 4:
2025-06-08 18:00:27,815 - INFO - 第 4 页获取到 100 条记录
2025-06-08 18:00:27,815 - INFO - Request Parameters - Page 5:
2025-06-08 18:00:27,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:27,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:28,324 - INFO - Response - Page 5:
2025-06-08 18:00:28,533 - INFO - 第 5 页获取到 100 条记录
2025-06-08 18:00:28,533 - INFO - Request Parameters - Page 6:
2025-06-08 18:00:28,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:28,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:28,984 - INFO - Response - Page 6:
2025-06-08 18:00:29,187 - INFO - 第 6 页获取到 100 条记录
2025-06-08 18:00:29,187 - INFO - Request Parameters - Page 7:
2025-06-08 18:00:29,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:29,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:29,561 - INFO - Response - Page 7:
2025-06-08 18:00:29,764 - INFO - 第 7 页获取到 38 条记录
2025-06-08 18:00:29,764 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 18:00:29,764 - INFO - 获取到 638 条表单数据
2025-06-08 18:00:29,764 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 18:00:29,764 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-06-08 18:00:30,262 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-06-08 18:00:30,262 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8688.74, 'new_value': 8140.22}, {'field': 'offline_amount', 'old_value': 141366.88, 'new_value': 141938.5}, {'field': 'total_amount', 'old_value': 150055.62, 'new_value': 150078.72}]
2025-06-08 18:00:30,278 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-06-08 18:00:30,723 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-06-08 18:00:30,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21487.73, 'new_value': 20589.5}, {'field': 'offline_amount', 'old_value': 339454.66, 'new_value': 341016.52}, {'field': 'total_amount', 'old_value': 360942.39, 'new_value': 361606.02}]
2025-06-08 18:00:30,728 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-06-08 18:00:31,209 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-06-08 18:00:31,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14288.74, 'new_value': 13715.71}, {'field': 'offline_amount', 'old_value': 76801.1, 'new_value': 77371.79}, {'field': 'total_amount', 'old_value': 91089.84, 'new_value': 91087.5}]
2025-06-08 18:00:31,212 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:31,213 - INFO - 开始处理日期: 2025-06
2025-06-08 18:00:31,213 - INFO - Request Parameters - Page 1:
2025-06-08 18:00:31,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:31,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:31,721 - INFO - Response - Page 1:
2025-06-08 18:00:31,924 - INFO - 第 1 页获取到 100 条记录
2025-06-08 18:00:31,924 - INFO - Request Parameters - Page 2:
2025-06-08 18:00:31,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:31,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:32,438 - INFO - Response - Page 2:
2025-06-08 18:00:32,643 - INFO - 第 2 页获取到 100 条记录
2025-06-08 18:00:32,643 - INFO - Request Parameters - Page 3:
2025-06-08 18:00:32,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:32,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:33,467 - INFO - Response - Page 3:
2025-06-08 18:00:33,667 - INFO - 第 3 页获取到 100 条记录
2025-06-08 18:00:33,668 - INFO - Request Parameters - Page 4:
2025-06-08 18:00:33,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:33,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:34,189 - INFO - Response - Page 4:
2025-06-08 18:00:34,389 - INFO - 第 4 页获取到 100 条记录
2025-06-08 18:00:34,389 - INFO - Request Parameters - Page 5:
2025-06-08 18:00:34,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:34,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:34,938 - INFO - Response - Page 5:
2025-06-08 18:00:35,140 - INFO - 第 5 页获取到 100 条记录
2025-06-08 18:00:35,140 - INFO - Request Parameters - Page 6:
2025-06-08 18:00:35,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:35,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:36,085 - INFO - Response - Page 6:
2025-06-08 18:00:36,285 - INFO - 第 6 页获取到 100 条记录
2025-06-08 18:00:36,285 - INFO - Request Parameters - Page 7:
2025-06-08 18:00:36,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 18:00:36,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 18:00:36,650 - INFO - Response - Page 7:
2025-06-08 18:00:36,850 - INFO - 第 7 页获取到 19 条记录
2025-06-08 18:00:36,850 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 18:00:36,850 - INFO - 获取到 619 条表单数据
2025-06-08 18:00:36,862 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 18:00:36,869 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMC1
2025-06-08 18:00:37,277 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMC1
2025-06-08 18:00:37,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7480.81, 'new_value': 36699.81}, {'field': 'total_amount', 'old_value': 7480.81, 'new_value': 36699.81}, {'field': 'order_count', 'old_value': 7, 'new_value': 11}]
2025-06-08 18:00:37,277 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMH1
2025-06-08 18:00:37,742 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMH1
2025-06-08 18:00:37,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2797.0, 'new_value': 3806.0}, {'field': 'total_amount', 'old_value': 4996.0, 'new_value': 6005.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-06-08 18:00:37,742 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM7
2025-06-08 18:00:38,195 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM7
2025-06-08 18:00:38,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35634.0, 'new_value': 46522.0}, {'field': 'total_amount', 'old_value': 35634.0, 'new_value': 46522.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-06-08 18:00:38,195 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM8
2025-06-08 18:00:38,615 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM8
2025-06-08 18:00:38,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7870.0, 'new_value': 11750.0}, {'field': 'total_amount', 'old_value': 7870.0, 'new_value': 11750.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-06-08 18:00:38,630 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMH
2025-06-08 18:00:39,097 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMH
2025-06-08 18:00:39,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1800.0, 'new_value': 6820.0}, {'field': 'total_amount', 'old_value': 1800.0, 'new_value': 6820.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-06-08 18:00:39,098 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBML
2025-06-08 18:00:39,627 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBML
2025-06-08 18:00:39,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37496.89, 'new_value': 45374.89}, {'field': 'total_amount', 'old_value': 37496.89, 'new_value': 45374.89}, {'field': 'order_count', 'old_value': 203, 'new_value': 248}]
2025-06-08 18:00:39,627 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMR
2025-06-08 18:00:40,040 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMR
2025-06-08 18:00:40,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3131.0, 'new_value': 4605.0}, {'field': 'total_amount', 'old_value': 3131.0, 'new_value': 4605.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 113}]
2025-06-08 18:00:40,040 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMS
2025-06-08 18:00:40,484 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMS
2025-06-08 18:00:40,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11740.0, 'new_value': 21540.0}, {'field': 'total_amount', 'old_value': 11740.0, 'new_value': 21540.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-06-08 18:00:40,484 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMU
2025-06-08 18:00:40,953 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMU
2025-06-08 18:00:40,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81774.17, 'new_value': 94328.17}, {'field': 'total_amount', 'old_value': 81774.17, 'new_value': 94328.17}, {'field': 'order_count', 'old_value': 596, 'new_value': 642}]
2025-06-08 18:00:40,953 - INFO - 日期 2025-06 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:40,953 - INFO - 数据同步完成！更新: 12 条，插入: 0 条，错误: 0 条
2025-06-08 18:00:40,953 - INFO - =================同步完成====================
2025-06-08 21:00:02,301 - INFO - =================使用默认全量同步=============
2025-06-08 21:00:03,910 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-08 21:00:03,910 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-08 21:00:03,941 - INFO - 开始处理日期: 2025-01
2025-06-08 21:00:03,941 - INFO - Request Parameters - Page 1:
2025-06-08 21:00:03,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:03,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:05,230 - INFO - Response - Page 1:
2025-06-08 21:00:05,434 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:00:05,434 - INFO - Request Parameters - Page 2:
2025-06-08 21:00:05,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:05,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:05,981 - INFO - Response - Page 2:
2025-06-08 21:00:06,184 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:00:06,184 - INFO - Request Parameters - Page 3:
2025-06-08 21:00:06,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:06,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:06,668 - INFO - Response - Page 3:
2025-06-08 21:00:06,871 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:00:06,871 - INFO - Request Parameters - Page 4:
2025-06-08 21:00:06,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:06,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:07,355 - INFO - Response - Page 4:
2025-06-08 21:00:07,558 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:00:07,558 - INFO - Request Parameters - Page 5:
2025-06-08 21:00:07,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:07,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:08,080 - INFO - Response - Page 5:
2025-06-08 21:00:08,280 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:00:08,280 - INFO - Request Parameters - Page 6:
2025-06-08 21:00:08,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:08,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:08,757 - INFO - Response - Page 6:
2025-06-08 21:00:08,957 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:00:08,957 - INFO - Request Parameters - Page 7:
2025-06-08 21:00:08,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:08,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:09,486 - INFO - Response - Page 7:
2025-06-08 21:00:09,689 - INFO - 第 7 页获取到 82 条记录
2025-06-08 21:00:09,689 - INFO - 查询完成，共获取到 682 条记录
2025-06-08 21:00:09,689 - INFO - 获取到 682 条表单数据
2025-06-08 21:00:09,689 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-08 21:00:09,704 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:09,704 - INFO - 开始处理日期: 2025-02
2025-06-08 21:00:09,704 - INFO - Request Parameters - Page 1:
2025-06-08 21:00:09,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:09,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:10,235 - INFO - Response - Page 1:
2025-06-08 21:00:10,438 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:00:10,438 - INFO - Request Parameters - Page 2:
2025-06-08 21:00:10,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:10,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:10,954 - INFO - Response - Page 2:
2025-06-08 21:00:11,157 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:00:11,157 - INFO - Request Parameters - Page 3:
2025-06-08 21:00:11,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:11,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:11,610 - INFO - Response - Page 3:
2025-06-08 21:00:11,810 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:00:11,810 - INFO - Request Parameters - Page 4:
2025-06-08 21:00:11,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:11,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:12,293 - INFO - Response - Page 4:
2025-06-08 21:00:12,496 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:00:12,496 - INFO - Request Parameters - Page 5:
2025-06-08 21:00:12,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:12,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:12,996 - INFO - Response - Page 5:
2025-06-08 21:00:13,199 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:00:13,199 - INFO - Request Parameters - Page 6:
2025-06-08 21:00:13,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:13,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:13,918 - INFO - Response - Page 6:
2025-06-08 21:00:14,121 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:00:14,121 - INFO - Request Parameters - Page 7:
2025-06-08 21:00:14,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:14,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:14,590 - INFO - Response - Page 7:
2025-06-08 21:00:14,793 - INFO - 第 7 页获取到 70 条记录
2025-06-08 21:00:14,793 - INFO - 查询完成，共获取到 670 条记录
2025-06-08 21:00:14,793 - INFO - 获取到 670 条表单数据
2025-06-08 21:00:14,793 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-08 21:00:14,808 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:14,808 - INFO - 开始处理日期: 2025-03
2025-06-08 21:00:14,808 - INFO - Request Parameters - Page 1:
2025-06-08 21:00:14,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:14,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:15,276 - INFO - Response - Page 1:
2025-06-08 21:00:15,479 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:00:15,479 - INFO - Request Parameters - Page 2:
2025-06-08 21:00:15,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:15,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:15,961 - INFO - Response - Page 2:
2025-06-08 21:00:16,164 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:00:16,164 - INFO - Request Parameters - Page 3:
2025-06-08 21:00:16,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:16,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:16,617 - INFO - Response - Page 3:
2025-06-08 21:00:16,821 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:00:16,821 - INFO - Request Parameters - Page 4:
2025-06-08 21:00:16,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:16,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:17,274 - INFO - Response - Page 4:
2025-06-08 21:00:17,477 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:00:17,477 - INFO - Request Parameters - Page 5:
2025-06-08 21:00:17,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:17,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:17,945 - INFO - Response - Page 5:
2025-06-08 21:00:18,148 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:00:18,148 - INFO - Request Parameters - Page 6:
2025-06-08 21:00:18,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:18,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:18,710 - INFO - Response - Page 6:
2025-06-08 21:00:18,913 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:00:18,913 - INFO - Request Parameters - Page 7:
2025-06-08 21:00:18,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:18,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:19,378 - INFO - Response - Page 7:
2025-06-08 21:00:19,582 - INFO - 第 7 页获取到 61 条记录
2025-06-08 21:00:19,582 - INFO - 查询完成，共获取到 661 条记录
2025-06-08 21:00:19,582 - INFO - 获取到 661 条表单数据
2025-06-08 21:00:19,582 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-08 21:00:19,597 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:19,597 - INFO - 开始处理日期: 2025-04
2025-06-08 21:00:19,597 - INFO - Request Parameters - Page 1:
2025-06-08 21:00:19,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:19,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:20,300 - INFO - Response - Page 1:
2025-06-08 21:00:20,503 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:00:20,503 - INFO - Request Parameters - Page 2:
2025-06-08 21:00:20,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:20,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:21,003 - INFO - Response - Page 2:
2025-06-08 21:00:21,206 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:00:21,206 - INFO - Request Parameters - Page 3:
2025-06-08 21:00:21,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:21,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:21,737 - INFO - Response - Page 3:
2025-06-08 21:00:21,940 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:00:21,940 - INFO - Request Parameters - Page 4:
2025-06-08 21:00:21,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:21,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:22,417 - INFO - Response - Page 4:
2025-06-08 21:00:22,625 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:00:22,625 - INFO - Request Parameters - Page 5:
2025-06-08 21:00:22,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:22,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:23,125 - INFO - Response - Page 5:
2025-06-08 21:00:23,328 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:00:23,328 - INFO - Request Parameters - Page 6:
2025-06-08 21:00:23,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:23,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:23,827 - INFO - Response - Page 6:
2025-06-08 21:00:24,031 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:00:24,031 - INFO - Request Parameters - Page 7:
2025-06-08 21:00:24,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:24,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:24,515 - INFO - Response - Page 7:
2025-06-08 21:00:24,718 - INFO - 第 7 页获取到 56 条记录
2025-06-08 21:00:24,718 - INFO - 查询完成，共获取到 656 条记录
2025-06-08 21:00:24,718 - INFO - 获取到 656 条表单数据
2025-06-08 21:00:24,718 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-08 21:00:24,734 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:24,734 - INFO - 开始处理日期: 2025-05
2025-06-08 21:00:24,734 - INFO - Request Parameters - Page 1:
2025-06-08 21:00:24,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:24,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:25,291 - INFO - Response - Page 1:
2025-06-08 21:00:25,492 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:00:25,492 - INFO - Request Parameters - Page 2:
2025-06-08 21:00:25,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:25,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:25,949 - INFO - Response - Page 2:
2025-06-08 21:00:26,150 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:00:26,150 - INFO - Request Parameters - Page 3:
2025-06-08 21:00:26,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:26,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:26,664 - INFO - Response - Page 3:
2025-06-08 21:00:26,864 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:00:26,864 - INFO - Request Parameters - Page 4:
2025-06-08 21:00:26,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:26,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:27,366 - INFO - Response - Page 4:
2025-06-08 21:00:27,566 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:00:27,566 - INFO - Request Parameters - Page 5:
2025-06-08 21:00:27,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:27,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:28,057 - INFO - Response - Page 5:
2025-06-08 21:00:28,260 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:00:28,260 - INFO - Request Parameters - Page 6:
2025-06-08 21:00:28,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:28,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:28,677 - INFO - Response - Page 6:
2025-06-08 21:00:28,877 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:00:28,877 - INFO - Request Parameters - Page 7:
2025-06-08 21:00:28,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:28,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:29,280 - INFO - Response - Page 7:
2025-06-08 21:00:29,481 - INFO - 第 7 页获取到 38 条记录
2025-06-08 21:00:29,481 - INFO - 查询完成，共获取到 638 条记录
2025-06-08 21:00:29,481 - INFO - 获取到 638 条表单数据
2025-06-08 21:00:29,494 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-08 21:00:29,502 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:29,502 - INFO - 开始处理日期: 2025-06
2025-06-08 21:00:29,502 - INFO - Request Parameters - Page 1:
2025-06-08 21:00:29,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:29,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:29,995 - INFO - Response - Page 1:
2025-06-08 21:00:30,196 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:00:30,196 - INFO - Request Parameters - Page 2:
2025-06-08 21:00:30,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:30,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:30,674 - INFO - Response - Page 2:
2025-06-08 21:00:30,877 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:00:30,877 - INFO - Request Parameters - Page 3:
2025-06-08 21:00:30,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:30,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:31,376 - INFO - Response - Page 3:
2025-06-08 21:00:31,579 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:00:31,579 - INFO - Request Parameters - Page 4:
2025-06-08 21:00:31,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:31,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:32,072 - INFO - Response - Page 4:
2025-06-08 21:00:32,275 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:00:32,275 - INFO - Request Parameters - Page 5:
2025-06-08 21:00:32,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:32,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:32,843 - INFO - Response - Page 5:
2025-06-08 21:00:33,043 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:00:33,043 - INFO - Request Parameters - Page 6:
2025-06-08 21:00:33,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:33,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:33,577 - INFO - Response - Page 6:
2025-06-08 21:00:33,778 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:00:33,778 - INFO - Request Parameters - Page 7:
2025-06-08 21:00:33,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:00:33,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:00:34,123 - INFO - Response - Page 7:
2025-06-08 21:00:34,323 - INFO - 第 7 页获取到 19 条记录
2025-06-08 21:00:34,323 - INFO - 查询完成，共获取到 619 条记录
2025-06-08 21:00:34,323 - INFO - 获取到 619 条表单数据
2025-06-08 21:00:34,335 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-08 21:00:34,336 - INFO - 开始更新记录 - 表单实例ID: FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06
2025-06-08 21:00:34,870 - INFO - 更新表单数据成功: FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06
2025-06-08 21:00:34,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21679.0, 'new_value': 24115.0}, {'field': 'offline_amount', 'old_value': 28705.0, 'new_value': 32927.0}, {'field': 'total_amount', 'old_value': 50384.0, 'new_value': 57042.0}, {'field': 'order_count', 'old_value': 1083, 'new_value': 1238}]
2025-06-08 21:00:34,872 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5
2025-06-08 21:00:35,300 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5
2025-06-08 21:00:35,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28582.0, 'new_value': 31422.0}, {'field': 'total_amount', 'old_value': 28582.0, 'new_value': 31422.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-08 21:00:35,309 - INFO - 日期 2025-06 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:35,309 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-06-08 21:00:35,311 - INFO - =================同步完成====================
