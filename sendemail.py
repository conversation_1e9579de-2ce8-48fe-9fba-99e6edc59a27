import smtplib
import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email.header import Header
from email import encoders
import os
from load_config import load_config
import configparser
import logging

def send_email(subject, html_body, attachment_path=None):
    """
    发送邮件
    
    Args:
        subject: 邮件主题
        html_body: HTML格式的邮件正文
        attachment_path: 附件路径(可选)
    """
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        # 获取邮件配置
        sender = config.get('EmailConfig', 'sender_email').strip()
        smtp_server = config.get('EmailConfig', 'smtp_server').strip()
        smtp_port = config.getint('EmailConfig', 'smtp_port')
        password = config.get('EmailConfig', 'sender_password').strip()
        
        # 处理收件人列表
        receiver_str = config.get('EmailConfig', 'receivers').strip()
        receiver_emails = [email.strip() for email in receiver_str.split(',') if email.strip()]
        
        if not receiver_emails:
            raise ValueError("收件人列表不能为空")
        
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['Subject'] = Header(subject, 'utf-8')
        msg['From'] = sender  # 发件人不需要特殊编码
        msg['To'] = ','.join(receiver_emails)  # 收件人直接用逗号连接
        
        # 添加HTML正文
        msg.attach(MIMEText(html_body, 'html', 'utf-8'))

        # 添加附件（如果提供了附件路径）
        if attachment_path and os.path.exists(attachment_path):
            with open(attachment_path, 'rb') as f:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(f.read())
                encoders.encode_base64(part)
                part.add_header('Content-Disposition', f'attachment; filename={os.path.basename(attachment_path)}')
                msg.attach(part)
        
        # SMTP发送
        smtp = smtplib.SMTP_SSL(smtp_server, 465)  # QQ邮箱使用SSL，端口465
        smtp.login(sender, password)
        
        # 发送邮件
        smtp.sendmail(sender, receiver_emails, msg.as_string())
        smtp.quit()
        
        logging.info("邮件发送成功")
        return True
        
    except Exception as e:
        error_msg = f"发送邮件失败: {str(e)}"
        logging.error(error_msg)
        print(error_msg)  # 添加控制台输出，方便调试
        return False

if __name__ == "__main__":
    # 测试发送
    subject = "测试邮件"
    html_body = """
    <html>
        <body>
            <h1>测试邮件</h1>
            <p>这是一封测试邮件。</p>
        </body>
    </html>
    """
    # 获取当天日期并构造日志文件名
    log_date = datetime.datetime.now().strftime('%Y%m%d')
    log_file = f'log{log_date}.log'
    
    # 发送邮件并附加日志文件
    send_email(subject, html_body, attachment_path=log_file)