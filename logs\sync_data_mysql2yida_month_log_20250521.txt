2025-05-21 00:00:02,090 - INFO - =================使用默认全量同步=============
2025-05-21 00:00:03,544 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 00:00:03,544 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 00:00:03,573 - INFO - 开始处理日期: 2025-01
2025-05-21 00:00:03,576 - INFO - Request Parameters - Page 1:
2025-05-21 00:00:03,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:03,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:04,906 - INFO - Response - Page 1:
2025-05-21 00:00:05,107 - INFO - 第 1 页获取到 100 条记录
2025-05-21 00:00:05,107 - INFO - Request Parameters - Page 2:
2025-05-21 00:00:05,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:05,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:05,631 - INFO - Response - Page 2:
2025-05-21 00:00:05,832 - INFO - 第 2 页获取到 100 条记录
2025-05-21 00:00:05,832 - INFO - Request Parameters - Page 3:
2025-05-21 00:00:05,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:05,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:06,339 - INFO - Response - Page 3:
2025-05-21 00:00:06,540 - INFO - 第 3 页获取到 100 条记录
2025-05-21 00:00:06,540 - INFO - Request Parameters - Page 4:
2025-05-21 00:00:06,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:06,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:07,057 - INFO - Response - Page 4:
2025-05-21 00:00:07,257 - INFO - 第 4 页获取到 100 条记录
2025-05-21 00:00:07,257 - INFO - Request Parameters - Page 5:
2025-05-21 00:00:07,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:07,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:07,769 - INFO - Response - Page 5:
2025-05-21 00:00:07,969 - INFO - 第 5 页获取到 100 条记录
2025-05-21 00:00:07,969 - INFO - Request Parameters - Page 6:
2025-05-21 00:00:07,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:07,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:08,414 - INFO - Response - Page 6:
2025-05-21 00:00:08,614 - INFO - 第 6 页获取到 100 条记录
2025-05-21 00:00:08,614 - INFO - Request Parameters - Page 7:
2025-05-21 00:00:08,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:08,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:09,055 - INFO - Response - Page 7:
2025-05-21 00:00:09,255 - INFO - 第 7 页获取到 82 条记录
2025-05-21 00:00:09,255 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 00:00:09,255 - INFO - 获取到 682 条表单数据
2025-05-21 00:00:09,268 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 00:00:09,279 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 00:00:09,280 - INFO - 开始处理日期: 2025-02
2025-05-21 00:00:09,280 - INFO - Request Parameters - Page 1:
2025-05-21 00:00:09,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:09,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:10,002 - INFO - Response - Page 1:
2025-05-21 00:00:10,202 - INFO - 第 1 页获取到 100 条记录
2025-05-21 00:00:10,202 - INFO - Request Parameters - Page 2:
2025-05-21 00:00:10,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:10,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:10,670 - INFO - Response - Page 2:
2025-05-21 00:00:10,871 - INFO - 第 2 页获取到 100 条记录
2025-05-21 00:00:10,871 - INFO - Request Parameters - Page 3:
2025-05-21 00:00:10,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:10,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:11,492 - INFO - Response - Page 3:
2025-05-21 00:00:11,692 - INFO - 第 3 页获取到 100 条记录
2025-05-21 00:00:11,692 - INFO - Request Parameters - Page 4:
2025-05-21 00:00:11,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:11,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:12,183 - INFO - Response - Page 4:
2025-05-21 00:00:12,383 - INFO - 第 4 页获取到 100 条记录
2025-05-21 00:00:12,383 - INFO - Request Parameters - Page 5:
2025-05-21 00:00:12,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:12,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:12,871 - INFO - Response - Page 5:
2025-05-21 00:00:13,071 - INFO - 第 5 页获取到 100 条记录
2025-05-21 00:00:13,071 - INFO - Request Parameters - Page 6:
2025-05-21 00:00:13,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:13,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:13,578 - INFO - Response - Page 6:
2025-05-21 00:00:13,778 - INFO - 第 6 页获取到 100 条记录
2025-05-21 00:00:13,778 - INFO - Request Parameters - Page 7:
2025-05-21 00:00:13,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:13,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:14,258 - INFO - Response - Page 7:
2025-05-21 00:00:14,459 - INFO - 第 7 页获取到 70 条记录
2025-05-21 00:00:14,459 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 00:00:14,459 - INFO - 获取到 670 条表单数据
2025-05-21 00:00:14,471 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 00:00:14,483 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 00:00:14,483 - INFO - 开始处理日期: 2025-03
2025-05-21 00:00:14,483 - INFO - Request Parameters - Page 1:
2025-05-21 00:00:14,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:14,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:14,944 - INFO - Response - Page 1:
2025-05-21 00:00:15,144 - INFO - 第 1 页获取到 100 条记录
2025-05-21 00:00:15,144 - INFO - Request Parameters - Page 2:
2025-05-21 00:00:15,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:15,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:15,630 - INFO - Response - Page 2:
2025-05-21 00:00:15,831 - INFO - 第 2 页获取到 100 条记录
2025-05-21 00:00:15,831 - INFO - Request Parameters - Page 3:
2025-05-21 00:00:15,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:15,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:16,308 - INFO - Response - Page 3:
2025-05-21 00:00:16,508 - INFO - 第 3 页获取到 100 条记录
2025-05-21 00:00:16,508 - INFO - Request Parameters - Page 4:
2025-05-21 00:00:16,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:16,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:16,957 - INFO - Response - Page 4:
2025-05-21 00:00:17,158 - INFO - 第 4 页获取到 100 条记录
2025-05-21 00:00:17,158 - INFO - Request Parameters - Page 5:
2025-05-21 00:00:17,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:17,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:17,590 - INFO - Response - Page 5:
2025-05-21 00:00:17,791 - INFO - 第 5 页获取到 100 条记录
2025-05-21 00:00:17,791 - INFO - Request Parameters - Page 6:
2025-05-21 00:00:17,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:17,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:18,309 - INFO - Response - Page 6:
2025-05-21 00:00:18,510 - INFO - 第 6 页获取到 100 条记录
2025-05-21 00:00:18,510 - INFO - Request Parameters - Page 7:
2025-05-21 00:00:18,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:18,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:18,982 - INFO - Response - Page 7:
2025-05-21 00:00:19,182 - INFO - 第 7 页获取到 61 条记录
2025-05-21 00:00:19,182 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 00:00:19,182 - INFO - 获取到 661 条表单数据
2025-05-21 00:00:19,195 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 00:00:19,207 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 00:00:19,207 - INFO - 开始处理日期: 2025-04
2025-05-21 00:00:19,207 - INFO - Request Parameters - Page 1:
2025-05-21 00:00:19,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:19,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:19,749 - INFO - Response - Page 1:
2025-05-21 00:00:19,949 - INFO - 第 1 页获取到 100 条记录
2025-05-21 00:00:19,949 - INFO - Request Parameters - Page 2:
2025-05-21 00:00:19,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:19,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:20,409 - INFO - Response - Page 2:
2025-05-21 00:00:20,609 - INFO - 第 2 页获取到 100 条记录
2025-05-21 00:00:20,609 - INFO - Request Parameters - Page 3:
2025-05-21 00:00:20,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:20,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:21,191 - INFO - Response - Page 3:
2025-05-21 00:00:21,392 - INFO - 第 3 页获取到 100 条记录
2025-05-21 00:00:21,392 - INFO - Request Parameters - Page 4:
2025-05-21 00:00:21,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:21,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:21,959 - INFO - Response - Page 4:
2025-05-21 00:00:22,159 - INFO - 第 4 页获取到 100 条记录
2025-05-21 00:00:22,159 - INFO - Request Parameters - Page 5:
2025-05-21 00:00:22,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:22,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:22,768 - INFO - Response - Page 5:
2025-05-21 00:00:22,968 - INFO - 第 5 页获取到 100 条记录
2025-05-21 00:00:22,968 - INFO - Request Parameters - Page 6:
2025-05-21 00:00:22,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:22,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:23,468 - INFO - Response - Page 6:
2025-05-21 00:00:23,668 - INFO - 第 6 页获取到 100 条记录
2025-05-21 00:00:23,668 - INFO - Request Parameters - Page 7:
2025-05-21 00:00:23,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:23,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:24,097 - INFO - Response - Page 7:
2025-05-21 00:00:24,297 - INFO - 第 7 页获取到 56 条记录
2025-05-21 00:00:24,297 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 00:00:24,297 - INFO - 获取到 656 条表单数据
2025-05-21 00:00:24,310 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 00:00:24,321 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 00:00:24,322 - INFO - 开始处理日期: 2025-05
2025-05-21 00:00:24,322 - INFO - Request Parameters - Page 1:
2025-05-21 00:00:24,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:24,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:24,838 - INFO - Response - Page 1:
2025-05-21 00:00:25,038 - INFO - 第 1 页获取到 100 条记录
2025-05-21 00:00:25,039 - INFO - Request Parameters - Page 2:
2025-05-21 00:00:25,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:25,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:25,793 - INFO - Response - Page 2:
2025-05-21 00:00:25,993 - INFO - 第 2 页获取到 100 条记录
2025-05-21 00:00:25,993 - INFO - Request Parameters - Page 3:
2025-05-21 00:00:25,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:25,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:26,575 - INFO - Response - Page 3:
2025-05-21 00:00:26,777 - INFO - 第 3 页获取到 100 条记录
2025-05-21 00:00:26,777 - INFO - Request Parameters - Page 4:
2025-05-21 00:00:26,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:26,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:27,385 - INFO - Response - Page 4:
2025-05-21 00:00:27,585 - INFO - 第 4 页获取到 100 条记录
2025-05-21 00:00:27,585 - INFO - Request Parameters - Page 5:
2025-05-21 00:00:27,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:27,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:28,107 - INFO - Response - Page 5:
2025-05-21 00:00:28,307 - INFO - 第 5 页获取到 100 条记录
2025-05-21 00:00:28,307 - INFO - Request Parameters - Page 6:
2025-05-21 00:00:28,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:28,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:28,844 - INFO - Response - Page 6:
2025-05-21 00:00:29,044 - INFO - 第 6 页获取到 100 条记录
2025-05-21 00:00:29,044 - INFO - Request Parameters - Page 7:
2025-05-21 00:00:29,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:00:29,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:00:29,463 - INFO - Response - Page 7:
2025-05-21 00:00:29,664 - INFO - 第 7 页获取到 28 条记录
2025-05-21 00:00:29,664 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 00:00:29,664 - INFO - 获取到 628 条表单数据
2025-05-21 00:00:29,676 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 00:00:29,677 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-21 00:00:30,175 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-21 00:00:30,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92112.0, 'new_value': 97282.0}, {'field': 'offline_amount', 'old_value': 98575.28, 'new_value': 104848.28}, {'field': 'total_amount', 'old_value': 190687.28, 'new_value': 202130.28}, {'field': 'order_count', 'old_value': 4070, 'new_value': 4316}]
2025-05-21 00:00:30,176 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-21 00:00:30,635 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-21 00:00:30,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14479.0, 'new_value': 15378.0}, {'field': 'total_amount', 'old_value': 14479.0, 'new_value': 15378.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-21 00:00:30,636 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-21 00:00:31,065 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-21 00:00:31,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62595.0, 'new_value': 64717.0}, {'field': 'offline_amount', 'old_value': 80569.0, 'new_value': 85547.0}, {'field': 'total_amount', 'old_value': 143164.0, 'new_value': 150264.0}, {'field': 'order_count', 'old_value': 3373, 'new_value': 3518}]
2025-05-21 00:00:31,066 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-21 00:00:31,495 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-21 00:00:31,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59811.0, 'new_value': 63193.0}, {'field': 'total_amount', 'old_value': 59811.0, 'new_value': 63193.0}, {'field': 'order_count', 'old_value': 517, 'new_value': 550}]
2025-05-21 00:00:31,496 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-21 00:00:31,935 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-21 00:00:31,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24353.0, 'new_value': 24552.0}, {'field': 'total_amount', 'old_value': 25803.0, 'new_value': 26002.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 92}]
2025-05-21 00:00:31,936 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-21 00:00:32,279 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-21 00:00:32,279 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6820.18, 'new_value': 6917.88}, {'field': 'offline_amount', 'old_value': 80439.42, 'new_value': 89704.52}, {'field': 'total_amount', 'old_value': 87259.6, 'new_value': 96622.4}, {'field': 'order_count', 'old_value': 2064, 'new_value': 2287}]
2025-05-21 00:00:32,280 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-21 00:00:32,822 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-21 00:00:32,822 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5035.03, 'new_value': 5253.41}, {'field': 'offline_amount', 'old_value': 149637.07, 'new_value': 157588.07}, {'field': 'total_amount', 'old_value': 154672.1, 'new_value': 162841.48}, {'field': 'order_count', 'old_value': 1019, 'new_value': 1086}]
2025-05-21 00:00:32,823 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-21 00:00:33,297 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-21 00:00:33,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67367.35, 'new_value': 72024.06}, {'field': 'offline_amount', 'old_value': 873935.74, 'new_value': 918072.12}, {'field': 'total_amount', 'old_value': 941303.09, 'new_value': 990096.18}, {'field': 'order_count', 'old_value': 7581, 'new_value': 8002}]
2025-05-21 00:00:33,298 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-21 00:00:33,818 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-21 00:00:33,819 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66534.75, 'new_value': 68977.75}, {'field': 'total_amount', 'old_value': 66534.75, 'new_value': 68977.75}, {'field': 'order_count', 'old_value': 371, 'new_value': 388}]
2025-05-21 00:00:33,820 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-21 00:00:34,301 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-21 00:00:34,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21729.0, 'new_value': 23078.8}, {'field': 'offline_amount', 'old_value': 17605.6, 'new_value': 18060.6}, {'field': 'total_amount', 'old_value': 39334.6, 'new_value': 41139.4}, {'field': 'order_count', 'old_value': 208, 'new_value': 220}]
2025-05-21 00:00:34,302 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-21 00:00:34,759 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-21 00:00:34,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12608.26, 'new_value': 13203.27}, {'field': 'offline_amount', 'old_value': 207667.84, 'new_value': 216960.64}, {'field': 'total_amount', 'old_value': 220276.1, 'new_value': 230163.91}, {'field': 'order_count', 'old_value': 12021, 'new_value': 12652}]
2025-05-21 00:00:34,759 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-21 00:00:35,153 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-21 00:00:35,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1384210.47, 'new_value': 1442849.38}, {'field': 'total_amount', 'old_value': 1384210.47, 'new_value': 1442849.38}, {'field': 'order_count', 'old_value': 11168, 'new_value': 11728}]
2025-05-21 00:00:35,153 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-21 00:00:35,601 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-21 00:00:35,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40626.34, 'new_value': 43967.53}, {'field': 'offline_amount', 'old_value': 24646.0, 'new_value': 26962.0}, {'field': 'total_amount', 'old_value': 65272.34, 'new_value': 70929.53}, {'field': 'order_count', 'old_value': 811, 'new_value': 877}]
2025-05-21 00:00:35,603 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-21 00:00:36,003 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-21 00:00:36,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18221.74, 'new_value': 18808.63}, {'field': 'total_amount', 'old_value': 19621.74, 'new_value': 20208.63}, {'field': 'order_count', 'old_value': 372, 'new_value': 387}]
2025-05-21 00:00:36,005 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-21 00:00:36,545 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-21 00:00:36,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 564048.82, 'new_value': 595829.9}, {'field': 'total_amount', 'old_value': 564048.82, 'new_value': 595829.9}, {'field': 'order_count', 'old_value': 3876, 'new_value': 4131}]
2025-05-21 00:00:36,546 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-21 00:00:37,004 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-21 00:00:37,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37743.32, 'new_value': 39626.12}, {'field': 'offline_amount', 'old_value': 268074.1, 'new_value': 285511.15}, {'field': 'total_amount', 'old_value': 305817.42, 'new_value': 325137.27}, {'field': 'order_count', 'old_value': 1924, 'new_value': 2057}]
2025-05-21 00:00:37,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-21 00:00:37,456 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-21 00:00:37,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63895.0, 'new_value': 65845.0}, {'field': 'total_amount', 'old_value': 63895.0, 'new_value': 65845.0}, {'field': 'order_count', 'old_value': 1887, 'new_value': 1944}]
2025-05-21 00:00:37,457 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-21 00:00:37,904 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-21 00:00:37,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148108.1, 'new_value': 154550.2}, {'field': 'total_amount', 'old_value': 148108.1, 'new_value': 154550.2}, {'field': 'order_count', 'old_value': 1887, 'new_value': 1978}]
2025-05-21 00:00:37,905 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-21 00:00:38,361 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-21 00:00:38,361 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31684.84, 'new_value': 33185.04}, {'field': 'offline_amount', 'old_value': 867590.89, 'new_value': 905102.45}, {'field': 'total_amount', 'old_value': 899275.73, 'new_value': 938287.49}, {'field': 'order_count', 'old_value': 4333, 'new_value': 4549}]
2025-05-21 00:00:38,361 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-21 00:00:38,868 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-21 00:00:38,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43003.85, 'new_value': 43482.85}, {'field': 'total_amount', 'old_value': 43003.85, 'new_value': 43482.85}, {'field': 'order_count', 'old_value': 161, 'new_value': 164}]
2025-05-21 00:00:38,869 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-21 00:00:39,290 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-21 00:00:39,291 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158030.12, 'new_value': 166961.33}, {'field': 'offline_amount', 'old_value': 300946.92, 'new_value': 311946.92}, {'field': 'total_amount', 'old_value': 458977.04, 'new_value': 478908.25}, {'field': 'order_count', 'old_value': 1111, 'new_value': 1166}]
2025-05-21 00:00:39,293 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-21 00:00:39,833 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-21 00:00:39,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244594.0, 'new_value': 262868.0}, {'field': 'total_amount', 'old_value': 249318.0, 'new_value': 267592.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 77}]
2025-05-21 00:00:39,834 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-21 00:00:40,289 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-21 00:00:40,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3179.72, 'new_value': 4047.22}, {'field': 'offline_amount', 'old_value': 29506.81, 'new_value': 33094.86}, {'field': 'total_amount', 'old_value': 32686.53, 'new_value': 37142.08}, {'field': 'order_count', 'old_value': 1288, 'new_value': 1472}]
2025-05-21 00:00:40,290 - INFO - 日期 2025-05 处理完成 - 更新: 23 条，插入: 0 条，错误: 0 条
2025-05-21 00:00:40,290 - INFO - 数据同步完成！更新: 23 条，插入: 0 条，错误: 0 条
2025-05-21 00:00:40,292 - INFO - =================同步完成====================
2025-05-21 03:00:02,042 - INFO - =================使用默认全量同步=============
2025-05-21 03:00:03,460 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 03:00:03,460 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 03:00:03,490 - INFO - 开始处理日期: 2025-01
2025-05-21 03:00:03,493 - INFO - Request Parameters - Page 1:
2025-05-21 03:00:03,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:03,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:04,374 - INFO - Response - Page 1:
2025-05-21 03:00:04,575 - INFO - 第 1 页获取到 100 条记录
2025-05-21 03:00:04,575 - INFO - Request Parameters - Page 2:
2025-05-21 03:00:04,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:04,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:05,132 - INFO - Response - Page 2:
2025-05-21 03:00:05,334 - INFO - 第 2 页获取到 100 条记录
2025-05-21 03:00:05,334 - INFO - Request Parameters - Page 3:
2025-05-21 03:00:05,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:05,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:05,931 - INFO - Response - Page 3:
2025-05-21 03:00:06,131 - INFO - 第 3 页获取到 100 条记录
2025-05-21 03:00:06,131 - INFO - Request Parameters - Page 4:
2025-05-21 03:00:06,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:06,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:06,994 - INFO - Response - Page 4:
2025-05-21 03:00:07,194 - INFO - 第 4 页获取到 100 条记录
2025-05-21 03:00:07,194 - INFO - Request Parameters - Page 5:
2025-05-21 03:00:07,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:07,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:07,729 - INFO - Response - Page 5:
2025-05-21 03:00:07,929 - INFO - 第 5 页获取到 100 条记录
2025-05-21 03:00:07,929 - INFO - Request Parameters - Page 6:
2025-05-21 03:00:07,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:07,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:08,425 - INFO - Response - Page 6:
2025-05-21 03:00:08,625 - INFO - 第 6 页获取到 100 条记录
2025-05-21 03:00:08,625 - INFO - Request Parameters - Page 7:
2025-05-21 03:00:08,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:08,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:09,047 - INFO - Response - Page 7:
2025-05-21 03:00:09,247 - INFO - 第 7 页获取到 82 条记录
2025-05-21 03:00:09,247 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 03:00:09,247 - INFO - 获取到 682 条表单数据
2025-05-21 03:00:09,260 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 03:00:09,274 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 03:00:09,274 - INFO - 开始处理日期: 2025-02
2025-05-21 03:00:09,274 - INFO - Request Parameters - Page 1:
2025-05-21 03:00:09,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:09,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:09,821 - INFO - Response - Page 1:
2025-05-21 03:00:10,021 - INFO - 第 1 页获取到 100 条记录
2025-05-21 03:00:10,021 - INFO - Request Parameters - Page 2:
2025-05-21 03:00:10,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:10,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:10,540 - INFO - Response - Page 2:
2025-05-21 03:00:10,740 - INFO - 第 2 页获取到 100 条记录
2025-05-21 03:00:10,740 - INFO - Request Parameters - Page 3:
2025-05-21 03:00:10,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:10,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:11,296 - INFO - Response - Page 3:
2025-05-21 03:00:11,497 - INFO - 第 3 页获取到 100 条记录
2025-05-21 03:00:11,497 - INFO - Request Parameters - Page 4:
2025-05-21 03:00:11,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:11,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:12,006 - INFO - Response - Page 4:
2025-05-21 03:00:12,206 - INFO - 第 4 页获取到 100 条记录
2025-05-21 03:00:12,206 - INFO - Request Parameters - Page 5:
2025-05-21 03:00:12,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:12,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:12,718 - INFO - Response - Page 5:
2025-05-21 03:00:12,918 - INFO - 第 5 页获取到 100 条记录
2025-05-21 03:00:12,918 - INFO - Request Parameters - Page 6:
2025-05-21 03:00:12,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:12,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:13,497 - INFO - Response - Page 6:
2025-05-21 03:00:13,697 - INFO - 第 6 页获取到 100 条记录
2025-05-21 03:00:13,697 - INFO - Request Parameters - Page 7:
2025-05-21 03:00:13,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:13,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:14,120 - INFO - Response - Page 7:
2025-05-21 03:00:14,320 - INFO - 第 7 页获取到 70 条记录
2025-05-21 03:00:14,320 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 03:00:14,320 - INFO - 获取到 670 条表单数据
2025-05-21 03:00:14,334 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 03:00:14,347 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 03:00:14,347 - INFO - 开始处理日期: 2025-03
2025-05-21 03:00:14,347 - INFO - Request Parameters - Page 1:
2025-05-21 03:00:14,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:14,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:14,823 - INFO - Response - Page 1:
2025-05-21 03:00:15,025 - INFO - 第 1 页获取到 100 条记录
2025-05-21 03:00:15,025 - INFO - Request Parameters - Page 2:
2025-05-21 03:00:15,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:15,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:15,550 - INFO - Response - Page 2:
2025-05-21 03:00:15,751 - INFO - 第 2 页获取到 100 条记录
2025-05-21 03:00:15,751 - INFO - Request Parameters - Page 3:
2025-05-21 03:00:15,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:15,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:16,239 - INFO - Response - Page 3:
2025-05-21 03:00:16,440 - INFO - 第 3 页获取到 100 条记录
2025-05-21 03:00:16,440 - INFO - Request Parameters - Page 4:
2025-05-21 03:00:16,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:16,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:16,923 - INFO - Response - Page 4:
2025-05-21 03:00:17,124 - INFO - 第 4 页获取到 100 条记录
2025-05-21 03:00:17,124 - INFO - Request Parameters - Page 5:
2025-05-21 03:00:17,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:17,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:17,621 - INFO - Response - Page 5:
2025-05-21 03:00:17,822 - INFO - 第 5 页获取到 100 条记录
2025-05-21 03:00:17,822 - INFO - Request Parameters - Page 6:
2025-05-21 03:00:17,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:17,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:18,301 - INFO - Response - Page 6:
2025-05-21 03:00:18,501 - INFO - 第 6 页获取到 100 条记录
2025-05-21 03:00:18,501 - INFO - Request Parameters - Page 7:
2025-05-21 03:00:18,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:18,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:19,047 - INFO - Response - Page 7:
2025-05-21 03:00:19,247 - INFO - 第 7 页获取到 61 条记录
2025-05-21 03:00:19,247 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 03:00:19,247 - INFO - 获取到 661 条表单数据
2025-05-21 03:00:19,259 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 03:00:19,271 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 03:00:19,271 - INFO - 开始处理日期: 2025-04
2025-05-21 03:00:19,271 - INFO - Request Parameters - Page 1:
2025-05-21 03:00:19,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:19,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:19,808 - INFO - Response - Page 1:
2025-05-21 03:00:20,008 - INFO - 第 1 页获取到 100 条记录
2025-05-21 03:00:20,008 - INFO - Request Parameters - Page 2:
2025-05-21 03:00:20,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:20,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:20,563 - INFO - Response - Page 2:
2025-05-21 03:00:20,763 - INFO - 第 2 页获取到 100 条记录
2025-05-21 03:00:20,763 - INFO - Request Parameters - Page 3:
2025-05-21 03:00:20,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:20,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:21,230 - INFO - Response - Page 3:
2025-05-21 03:00:21,431 - INFO - 第 3 页获取到 100 条记录
2025-05-21 03:00:21,431 - INFO - Request Parameters - Page 4:
2025-05-21 03:00:21,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:21,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:21,942 - INFO - Response - Page 4:
2025-05-21 03:00:22,142 - INFO - 第 4 页获取到 100 条记录
2025-05-21 03:00:22,142 - INFO - Request Parameters - Page 5:
2025-05-21 03:00:22,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:22,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:22,696 - INFO - Response - Page 5:
2025-05-21 03:00:22,896 - INFO - 第 5 页获取到 100 条记录
2025-05-21 03:00:22,896 - INFO - Request Parameters - Page 6:
2025-05-21 03:00:22,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:22,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:23,413 - INFO - Response - Page 6:
2025-05-21 03:00:23,613 - INFO - 第 6 页获取到 100 条记录
2025-05-21 03:00:23,613 - INFO - Request Parameters - Page 7:
2025-05-21 03:00:23,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:23,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:24,063 - INFO - Response - Page 7:
2025-05-21 03:00:24,265 - INFO - 第 7 页获取到 56 条记录
2025-05-21 03:00:24,265 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 03:00:24,265 - INFO - 获取到 656 条表单数据
2025-05-21 03:00:24,277 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 03:00:24,289 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 03:00:24,289 - INFO - 开始处理日期: 2025-05
2025-05-21 03:00:24,289 - INFO - Request Parameters - Page 1:
2025-05-21 03:00:24,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:24,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:24,791 - INFO - Response - Page 1:
2025-05-21 03:00:24,991 - INFO - 第 1 页获取到 100 条记录
2025-05-21 03:00:24,991 - INFO - Request Parameters - Page 2:
2025-05-21 03:00:24,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:24,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:25,526 - INFO - Response - Page 2:
2025-05-21 03:00:25,727 - INFO - 第 2 页获取到 100 条记录
2025-05-21 03:00:25,727 - INFO - Request Parameters - Page 3:
2025-05-21 03:00:25,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:25,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:26,218 - INFO - Response - Page 3:
2025-05-21 03:00:26,418 - INFO - 第 3 页获取到 100 条记录
2025-05-21 03:00:26,418 - INFO - Request Parameters - Page 4:
2025-05-21 03:00:26,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:26,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:26,919 - INFO - Response - Page 4:
2025-05-21 03:00:27,119 - INFO - 第 4 页获取到 100 条记录
2025-05-21 03:00:27,119 - INFO - Request Parameters - Page 5:
2025-05-21 03:00:27,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:27,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:27,590 - INFO - Response - Page 5:
2025-05-21 03:00:27,790 - INFO - 第 5 页获取到 100 条记录
2025-05-21 03:00:27,790 - INFO - Request Parameters - Page 6:
2025-05-21 03:00:27,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:27,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:28,350 - INFO - Response - Page 6:
2025-05-21 03:00:28,550 - INFO - 第 6 页获取到 100 条记录
2025-05-21 03:00:28,550 - INFO - Request Parameters - Page 7:
2025-05-21 03:00:28,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:00:28,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:00:28,964 - INFO - Response - Page 7:
2025-05-21 03:00:29,164 - INFO - 第 7 页获取到 28 条记录
2025-05-21 03:00:29,164 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 03:00:29,164 - INFO - 获取到 628 条表单数据
2025-05-21 03:00:29,176 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 03:00:29,187 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 03:00:29,187 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 03:00:29,188 - INFO - =================同步完成====================
2025-05-21 06:00:01,933 - INFO - =================使用默认全量同步=============
2025-05-21 06:00:03,344 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 06:00:03,345 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 06:00:03,371 - INFO - 开始处理日期: 2025-01
2025-05-21 06:00:03,374 - INFO - Request Parameters - Page 1:
2025-05-21 06:00:03,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:03,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:04,680 - INFO - Response - Page 1:
2025-05-21 06:00:04,880 - INFO - 第 1 页获取到 100 条记录
2025-05-21 06:00:04,880 - INFO - Request Parameters - Page 2:
2025-05-21 06:00:04,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:04,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:05,521 - INFO - Response - Page 2:
2025-05-21 06:00:05,721 - INFO - 第 2 页获取到 100 条记录
2025-05-21 06:00:05,721 - INFO - Request Parameters - Page 3:
2025-05-21 06:00:05,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:05,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:06,223 - INFO - Response - Page 3:
2025-05-21 06:00:06,423 - INFO - 第 3 页获取到 100 条记录
2025-05-21 06:00:06,423 - INFO - Request Parameters - Page 4:
2025-05-21 06:00:06,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:06,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:06,947 - INFO - Response - Page 4:
2025-05-21 06:00:07,148 - INFO - 第 4 页获取到 100 条记录
2025-05-21 06:00:07,148 - INFO - Request Parameters - Page 5:
2025-05-21 06:00:07,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:07,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:07,667 - INFO - Response - Page 5:
2025-05-21 06:00:07,867 - INFO - 第 5 页获取到 100 条记录
2025-05-21 06:00:07,867 - INFO - Request Parameters - Page 6:
2025-05-21 06:00:07,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:07,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:08,355 - INFO - Response - Page 6:
2025-05-21 06:00:08,555 - INFO - 第 6 页获取到 100 条记录
2025-05-21 06:00:08,555 - INFO - Request Parameters - Page 7:
2025-05-21 06:00:08,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:08,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:09,023 - INFO - Response - Page 7:
2025-05-21 06:00:09,225 - INFO - 第 7 页获取到 82 条记录
2025-05-21 06:00:09,225 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 06:00:09,225 - INFO - 获取到 682 条表单数据
2025-05-21 06:00:09,236 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 06:00:09,247 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 06:00:09,247 - INFO - 开始处理日期: 2025-02
2025-05-21 06:00:09,247 - INFO - Request Parameters - Page 1:
2025-05-21 06:00:09,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:09,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:09,781 - INFO - Response - Page 1:
2025-05-21 06:00:09,982 - INFO - 第 1 页获取到 100 条记录
2025-05-21 06:00:09,982 - INFO - Request Parameters - Page 2:
2025-05-21 06:00:09,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:09,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:10,515 - INFO - Response - Page 2:
2025-05-21 06:00:10,715 - INFO - 第 2 页获取到 100 条记录
2025-05-21 06:00:10,715 - INFO - Request Parameters - Page 3:
2025-05-21 06:00:10,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:10,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:11,228 - INFO - Response - Page 3:
2025-05-21 06:00:11,428 - INFO - 第 3 页获取到 100 条记录
2025-05-21 06:00:11,428 - INFO - Request Parameters - Page 4:
2025-05-21 06:00:11,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:11,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:11,939 - INFO - Response - Page 4:
2025-05-21 06:00:12,140 - INFO - 第 4 页获取到 100 条记录
2025-05-21 06:00:12,140 - INFO - Request Parameters - Page 5:
2025-05-21 06:00:12,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:12,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:12,590 - INFO - Response - Page 5:
2025-05-21 06:00:12,791 - INFO - 第 5 页获取到 100 条记录
2025-05-21 06:00:12,791 - INFO - Request Parameters - Page 6:
2025-05-21 06:00:12,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:12,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:13,288 - INFO - Response - Page 6:
2025-05-21 06:00:13,488 - INFO - 第 6 页获取到 100 条记录
2025-05-21 06:00:13,488 - INFO - Request Parameters - Page 7:
2025-05-21 06:00:13,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:13,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:13,935 - INFO - Response - Page 7:
2025-05-21 06:00:14,135 - INFO - 第 7 页获取到 70 条记录
2025-05-21 06:00:14,135 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 06:00:14,135 - INFO - 获取到 670 条表单数据
2025-05-21 06:00:14,146 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 06:00:14,158 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 06:00:14,158 - INFO - 开始处理日期: 2025-03
2025-05-21 06:00:14,158 - INFO - Request Parameters - Page 1:
2025-05-21 06:00:14,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:14,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:14,663 - INFO - Response - Page 1:
2025-05-21 06:00:14,864 - INFO - 第 1 页获取到 100 条记录
2025-05-21 06:00:14,864 - INFO - Request Parameters - Page 2:
2025-05-21 06:00:14,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:14,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:15,416 - INFO - Response - Page 2:
2025-05-21 06:00:15,616 - INFO - 第 2 页获取到 100 条记录
2025-05-21 06:00:15,616 - INFO - Request Parameters - Page 3:
2025-05-21 06:00:15,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:15,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:16,129 - INFO - Response - Page 3:
2025-05-21 06:00:16,329 - INFO - 第 3 页获取到 100 条记录
2025-05-21 06:00:16,329 - INFO - Request Parameters - Page 4:
2025-05-21 06:00:16,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:16,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:16,785 - INFO - Response - Page 4:
2025-05-21 06:00:16,986 - INFO - 第 4 页获取到 100 条记录
2025-05-21 06:00:16,986 - INFO - Request Parameters - Page 5:
2025-05-21 06:00:16,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:16,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:17,506 - INFO - Response - Page 5:
2025-05-21 06:00:17,706 - INFO - 第 5 页获取到 100 条记录
2025-05-21 06:00:17,706 - INFO - Request Parameters - Page 6:
2025-05-21 06:00:17,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:17,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:18,271 - INFO - Response - Page 6:
2025-05-21 06:00:18,473 - INFO - 第 6 页获取到 100 条记录
2025-05-21 06:00:18,473 - INFO - Request Parameters - Page 7:
2025-05-21 06:00:18,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:18,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:18,916 - INFO - Response - Page 7:
2025-05-21 06:00:19,116 - INFO - 第 7 页获取到 61 条记录
2025-05-21 06:00:19,116 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 06:00:19,116 - INFO - 获取到 661 条表单数据
2025-05-21 06:00:19,128 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 06:00:19,140 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 06:00:19,140 - INFO - 开始处理日期: 2025-04
2025-05-21 06:00:19,140 - INFO - Request Parameters - Page 1:
2025-05-21 06:00:19,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:19,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:19,817 - INFO - Response - Page 1:
2025-05-21 06:00:20,018 - INFO - 第 1 页获取到 100 条记录
2025-05-21 06:00:20,018 - INFO - Request Parameters - Page 2:
2025-05-21 06:00:20,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:20,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:20,544 - INFO - Response - Page 2:
2025-05-21 06:00:20,744 - INFO - 第 2 页获取到 100 条记录
2025-05-21 06:00:20,744 - INFO - Request Parameters - Page 3:
2025-05-21 06:00:20,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:20,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:21,242 - INFO - Response - Page 3:
2025-05-21 06:00:21,442 - INFO - 第 3 页获取到 100 条记录
2025-05-21 06:00:21,442 - INFO - Request Parameters - Page 4:
2025-05-21 06:00:21,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:21,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:21,984 - INFO - Response - Page 4:
2025-05-21 06:00:22,185 - INFO - 第 4 页获取到 100 条记录
2025-05-21 06:00:22,185 - INFO - Request Parameters - Page 5:
2025-05-21 06:00:22,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:22,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:22,788 - INFO - Response - Page 5:
2025-05-21 06:00:22,988 - INFO - 第 5 页获取到 100 条记录
2025-05-21 06:00:22,988 - INFO - Request Parameters - Page 6:
2025-05-21 06:00:22,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:22,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:23,442 - INFO - Response - Page 6:
2025-05-21 06:00:23,643 - INFO - 第 6 页获取到 100 条记录
2025-05-21 06:00:23,643 - INFO - Request Parameters - Page 7:
2025-05-21 06:00:23,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:23,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:24,082 - INFO - Response - Page 7:
2025-05-21 06:00:24,283 - INFO - 第 7 页获取到 56 条记录
2025-05-21 06:00:24,283 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 06:00:24,283 - INFO - 获取到 656 条表单数据
2025-05-21 06:00:24,296 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 06:00:24,308 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 06:00:24,308 - INFO - 开始处理日期: 2025-05
2025-05-21 06:00:24,309 - INFO - Request Parameters - Page 1:
2025-05-21 06:00:24,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:24,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:24,813 - INFO - Response - Page 1:
2025-05-21 06:00:25,014 - INFO - 第 1 页获取到 100 条记录
2025-05-21 06:00:25,014 - INFO - Request Parameters - Page 2:
2025-05-21 06:00:25,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:25,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:25,561 - INFO - Response - Page 2:
2025-05-21 06:00:25,762 - INFO - 第 2 页获取到 100 条记录
2025-05-21 06:00:25,762 - INFO - Request Parameters - Page 3:
2025-05-21 06:00:25,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:25,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:26,262 - INFO - Response - Page 3:
2025-05-21 06:00:26,462 - INFO - 第 3 页获取到 100 条记录
2025-05-21 06:00:26,462 - INFO - Request Parameters - Page 4:
2025-05-21 06:00:26,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:26,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:27,010 - INFO - Response - Page 4:
2025-05-21 06:00:27,211 - INFO - 第 4 页获取到 100 条记录
2025-05-21 06:00:27,211 - INFO - Request Parameters - Page 5:
2025-05-21 06:00:27,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:27,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:27,687 - INFO - Response - Page 5:
2025-05-21 06:00:27,888 - INFO - 第 5 页获取到 100 条记录
2025-05-21 06:00:27,888 - INFO - Request Parameters - Page 6:
2025-05-21 06:00:27,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:27,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:28,421 - INFO - Response - Page 6:
2025-05-21 06:00:28,621 - INFO - 第 6 页获取到 100 条记录
2025-05-21 06:00:28,621 - INFO - Request Parameters - Page 7:
2025-05-21 06:00:28,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:00:28,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:00:29,001 - INFO - Response - Page 7:
2025-05-21 06:00:29,201 - INFO - 第 7 页获取到 28 条记录
2025-05-21 06:00:29,201 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 06:00:29,201 - INFO - 获取到 628 条表单数据
2025-05-21 06:00:29,213 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 06:00:29,223 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-21 06:00:29,660 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-21 06:00:29,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63263.0, 'new_value': 65638.0}, {'field': 'total_amount', 'old_value': 65113.0, 'new_value': 67488.0}, {'field': 'order_count', 'old_value': 375, 'new_value': 391}]
2025-05-21 06:00:29,661 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-21 06:00:29,661 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-21 06:00:29,663 - INFO - =================同步完成====================
2025-05-21 09:00:02,058 - INFO - =================使用默认全量同步=============
2025-05-21 09:00:03,465 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 09:00:03,466 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 09:00:03,493 - INFO - 开始处理日期: 2025-01
2025-05-21 09:00:03,496 - INFO - Request Parameters - Page 1:
2025-05-21 09:00:03,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:03,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:04,676 - INFO - Response - Page 1:
2025-05-21 09:00:04,876 - INFO - 第 1 页获取到 100 条记录
2025-05-21 09:00:04,876 - INFO - Request Parameters - Page 2:
2025-05-21 09:00:04,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:04,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:05,478 - INFO - Response - Page 2:
2025-05-21 09:00:05,678 - INFO - 第 2 页获取到 100 条记录
2025-05-21 09:00:05,678 - INFO - Request Parameters - Page 3:
2025-05-21 09:00:05,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:05,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:06,178 - INFO - Response - Page 3:
2025-05-21 09:00:06,378 - INFO - 第 3 页获取到 100 条记录
2025-05-21 09:00:06,378 - INFO - Request Parameters - Page 4:
2025-05-21 09:00:06,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:06,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:06,991 - INFO - Response - Page 4:
2025-05-21 09:00:07,191 - INFO - 第 4 页获取到 100 条记录
2025-05-21 09:00:07,191 - INFO - Request Parameters - Page 5:
2025-05-21 09:00:07,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:07,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:07,819 - INFO - Response - Page 5:
2025-05-21 09:00:08,019 - INFO - 第 5 页获取到 100 条记录
2025-05-21 09:00:08,019 - INFO - Request Parameters - Page 6:
2025-05-21 09:00:08,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:08,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:08,879 - INFO - Response - Page 6:
2025-05-21 09:00:09,080 - INFO - 第 6 页获取到 100 条记录
2025-05-21 09:00:09,080 - INFO - Request Parameters - Page 7:
2025-05-21 09:00:09,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:09,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:09,563 - INFO - Response - Page 7:
2025-05-21 09:00:09,763 - INFO - 第 7 页获取到 82 条记录
2025-05-21 09:00:09,763 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 09:00:09,763 - INFO - 获取到 682 条表单数据
2025-05-21 09:00:09,775 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 09:00:09,787 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 09:00:09,787 - INFO - 开始处理日期: 2025-02
2025-05-21 09:00:09,788 - INFO - Request Parameters - Page 1:
2025-05-21 09:00:09,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:09,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:10,278 - INFO - Response - Page 1:
2025-05-21 09:00:10,479 - INFO - 第 1 页获取到 100 条记录
2025-05-21 09:00:10,479 - INFO - Request Parameters - Page 2:
2025-05-21 09:00:10,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:10,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:11,026 - INFO - Response - Page 2:
2025-05-21 09:00:11,226 - INFO - 第 2 页获取到 100 条记录
2025-05-21 09:00:11,226 - INFO - Request Parameters - Page 3:
2025-05-21 09:00:11,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:11,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:11,752 - INFO - Response - Page 3:
2025-05-21 09:00:11,953 - INFO - 第 3 页获取到 100 条记录
2025-05-21 09:00:11,953 - INFO - Request Parameters - Page 4:
2025-05-21 09:00:11,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:11,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:12,579 - INFO - Response - Page 4:
2025-05-21 09:00:12,780 - INFO - 第 4 页获取到 100 条记录
2025-05-21 09:00:12,780 - INFO - Request Parameters - Page 5:
2025-05-21 09:00:12,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:12,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:13,335 - INFO - Response - Page 5:
2025-05-21 09:00:13,536 - INFO - 第 5 页获取到 100 条记录
2025-05-21 09:00:13,536 - INFO - Request Parameters - Page 6:
2025-05-21 09:00:13,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:13,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:14,173 - INFO - Response - Page 6:
2025-05-21 09:00:14,373 - INFO - 第 6 页获取到 100 条记录
2025-05-21 09:00:14,373 - INFO - Request Parameters - Page 7:
2025-05-21 09:00:14,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:14,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:14,881 - INFO - Response - Page 7:
2025-05-21 09:00:15,081 - INFO - 第 7 页获取到 70 条记录
2025-05-21 09:00:15,081 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 09:00:15,081 - INFO - 获取到 670 条表单数据
2025-05-21 09:00:15,094 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 09:00:15,106 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 09:00:15,106 - INFO - 开始处理日期: 2025-03
2025-05-21 09:00:15,106 - INFO - Request Parameters - Page 1:
2025-05-21 09:00:15,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:15,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:15,547 - INFO - Response - Page 1:
2025-05-21 09:00:15,747 - INFO - 第 1 页获取到 100 条记录
2025-05-21 09:00:15,747 - INFO - Request Parameters - Page 2:
2025-05-21 09:00:15,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:15,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:16,255 - INFO - Response - Page 2:
2025-05-21 09:00:16,456 - INFO - 第 2 页获取到 100 条记录
2025-05-21 09:00:16,456 - INFO - Request Parameters - Page 3:
2025-05-21 09:00:16,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:16,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:16,957 - INFO - Response - Page 3:
2025-05-21 09:00:17,159 - INFO - 第 3 页获取到 100 条记录
2025-05-21 09:00:17,159 - INFO - Request Parameters - Page 4:
2025-05-21 09:00:17,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:17,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:17,706 - INFO - Response - Page 4:
2025-05-21 09:00:17,908 - INFO - 第 4 页获取到 100 条记录
2025-05-21 09:00:17,908 - INFO - Request Parameters - Page 5:
2025-05-21 09:00:17,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:17,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:18,403 - INFO - Response - Page 5:
2025-05-21 09:00:18,603 - INFO - 第 5 页获取到 100 条记录
2025-05-21 09:00:18,603 - INFO - Request Parameters - Page 6:
2025-05-21 09:00:18,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:18,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:19,068 - INFO - Response - Page 6:
2025-05-21 09:00:19,268 - INFO - 第 6 页获取到 100 条记录
2025-05-21 09:00:19,268 - INFO - Request Parameters - Page 7:
2025-05-21 09:00:19,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:19,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:19,713 - INFO - Response - Page 7:
2025-05-21 09:00:19,914 - INFO - 第 7 页获取到 61 条记录
2025-05-21 09:00:19,914 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 09:00:19,914 - INFO - 获取到 661 条表单数据
2025-05-21 09:00:19,926 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 09:00:19,938 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 09:00:19,939 - INFO - 开始处理日期: 2025-04
2025-05-21 09:00:19,939 - INFO - Request Parameters - Page 1:
2025-05-21 09:00:19,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:19,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:20,496 - INFO - Response - Page 1:
2025-05-21 09:00:20,697 - INFO - 第 1 页获取到 100 条记录
2025-05-21 09:00:20,697 - INFO - Request Parameters - Page 2:
2025-05-21 09:00:20,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:20,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:21,181 - INFO - Response - Page 2:
2025-05-21 09:00:21,381 - INFO - 第 2 页获取到 100 条记录
2025-05-21 09:00:21,381 - INFO - Request Parameters - Page 3:
2025-05-21 09:00:21,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:21,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:21,859 - INFO - Response - Page 3:
2025-05-21 09:00:22,059 - INFO - 第 3 页获取到 100 条记录
2025-05-21 09:00:22,059 - INFO - Request Parameters - Page 4:
2025-05-21 09:00:22,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:22,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:22,726 - INFO - Response - Page 4:
2025-05-21 09:00:22,926 - INFO - 第 4 页获取到 100 条记录
2025-05-21 09:00:22,926 - INFO - Request Parameters - Page 5:
2025-05-21 09:00:22,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:22,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:23,375 - INFO - Response - Page 5:
2025-05-21 09:00:23,576 - INFO - 第 5 页获取到 100 条记录
2025-05-21 09:00:23,576 - INFO - Request Parameters - Page 6:
2025-05-21 09:00:23,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:23,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:24,206 - INFO - Response - Page 6:
2025-05-21 09:00:24,407 - INFO - 第 6 页获取到 100 条记录
2025-05-21 09:00:24,407 - INFO - Request Parameters - Page 7:
2025-05-21 09:00:24,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:24,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:24,954 - INFO - Response - Page 7:
2025-05-21 09:00:25,154 - INFO - 第 7 页获取到 56 条记录
2025-05-21 09:00:25,154 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 09:00:25,154 - INFO - 获取到 656 条表单数据
2025-05-21 09:00:25,165 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 09:00:25,176 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 09:00:25,176 - INFO - 开始处理日期: 2025-05
2025-05-21 09:00:25,177 - INFO - Request Parameters - Page 1:
2025-05-21 09:00:25,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:25,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:25,858 - INFO - Response - Page 1:
2025-05-21 09:00:26,058 - INFO - 第 1 页获取到 100 条记录
2025-05-21 09:00:26,058 - INFO - Request Parameters - Page 2:
2025-05-21 09:00:26,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:26,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:26,588 - INFO - Response - Page 2:
2025-05-21 09:00:26,788 - INFO - 第 2 页获取到 100 条记录
2025-05-21 09:00:26,788 - INFO - Request Parameters - Page 3:
2025-05-21 09:00:26,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:26,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:27,310 - INFO - Response - Page 3:
2025-05-21 09:00:27,511 - INFO - 第 3 页获取到 100 条记录
2025-05-21 09:00:27,511 - INFO - Request Parameters - Page 4:
2025-05-21 09:00:27,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:27,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:28,018 - INFO - Response - Page 4:
2025-05-21 09:00:28,218 - INFO - 第 4 页获取到 100 条记录
2025-05-21 09:00:28,218 - INFO - Request Parameters - Page 5:
2025-05-21 09:00:28,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:28,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:28,894 - INFO - Response - Page 5:
2025-05-21 09:00:29,094 - INFO - 第 5 页获取到 100 条记录
2025-05-21 09:00:29,094 - INFO - Request Parameters - Page 6:
2025-05-21 09:00:29,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:29,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:29,675 - INFO - Response - Page 6:
2025-05-21 09:00:29,875 - INFO - 第 6 页获取到 100 条记录
2025-05-21 09:00:29,875 - INFO - Request Parameters - Page 7:
2025-05-21 09:00:29,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:00:29,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:00:30,274 - INFO - Response - Page 7:
2025-05-21 09:00:30,474 - INFO - 第 7 页获取到 28 条记录
2025-05-21 09:00:30,474 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 09:00:30,474 - INFO - 获取到 628 条表单数据
2025-05-21 09:00:30,486 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 09:00:30,488 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-21 09:00:30,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-21 09:00:30,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52759.0, 'new_value': 53768.0}, {'field': 'total_amount', 'old_value': 57843.32, 'new_value': 58852.32}, {'field': 'order_count', 'old_value': 447, 'new_value': 448}]
2025-05-21 09:00:30,923 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-21 09:00:31,532 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-21 09:00:31,533 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43693.11, 'new_value': 46410.0}, {'field': 'offline_amount', 'old_value': 80964.75, 'new_value': 83595.31}, {'field': 'total_amount', 'old_value': 124657.86, 'new_value': 130005.31}, {'field': 'order_count', 'old_value': 4278, 'new_value': 4478}]
2025-05-21 09:00:31,533 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-21 09:00:31,933 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-21 09:00:31,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244218.0, 'new_value': 251316.0}, {'field': 'total_amount', 'old_value': 244218.0, 'new_value': 251316.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 150}]
2025-05-21 09:00:31,933 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-21 09:00:32,404 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-21 09:00:32,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22039.4, 'new_value': 22539.4}, {'field': 'total_amount', 'old_value': 22104.95, 'new_value': 22604.95}, {'field': 'order_count', 'old_value': 196, 'new_value': 198}]
2025-05-21 09:00:32,404 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-21 09:00:32,927 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-21 09:00:32,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151038.65, 'new_value': 166272.65}, {'field': 'offline_amount', 'old_value': 86382.0, 'new_value': 92368.0}, {'field': 'total_amount', 'old_value': 237420.65, 'new_value': 258640.65}, {'field': 'order_count', 'old_value': 1309, 'new_value': 1415}]
2025-05-21 09:00:32,928 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-21 09:00:33,385 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-21 09:00:33,385 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25522.66, 'new_value': 33746.15}, {'field': 'offline_amount', 'old_value': 118208.0, 'new_value': 136903.0}, {'field': 'total_amount', 'old_value': 143730.66, 'new_value': 170649.15}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-05-21 09:00:33,385 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-21 09:00:33,771 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-21 09:00:33,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43317.0, 'new_value': 44114.0}, {'field': 'total_amount', 'old_value': 43665.0, 'new_value': 44462.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-05-21 09:00:33,772 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-21 09:00:34,230 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-21 09:00:34,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 665336.0, 'new_value': 696716.0}, {'field': 'total_amount', 'old_value': 665336.0, 'new_value': 696716.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 122}]
2025-05-21 09:00:34,230 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-21 09:00:34,659 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-21 09:00:34,659 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 175711.64, 'new_value': 187852.05}, {'field': 'offline_amount', 'old_value': 92836.25, 'new_value': 96326.25}, {'field': 'total_amount', 'old_value': 268547.89, 'new_value': 284178.3}, {'field': 'order_count', 'old_value': 1002, 'new_value': 1055}]
2025-05-21 09:00:34,660 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-21 09:00:35,076 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-21 09:00:35,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16887.47, 'new_value': 18687.45}, {'field': 'offline_amount', 'old_value': 230050.03, 'new_value': 254741.59}, {'field': 'total_amount', 'old_value': 246937.5, 'new_value': 273429.04}, {'field': 'order_count', 'old_value': 1171, 'new_value': 1271}]
2025-05-21 09:00:35,077 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-21 09:00:35,480 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-21 09:00:35,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27315.49, 'new_value': 29087.6}, {'field': 'offline_amount', 'old_value': 438807.11, 'new_value': 457092.01}, {'field': 'total_amount', 'old_value': 466122.6, 'new_value': 486179.61}, {'field': 'order_count', 'old_value': 2556, 'new_value': 2647}]
2025-05-21 09:00:35,481 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-21 09:00:36,056 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-21 09:00:36,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267435.0, 'new_value': 272468.0}, {'field': 'total_amount', 'old_value': 267435.0, 'new_value': 272468.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 220}]
2025-05-21 09:00:36,057 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-21 09:00:36,525 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-21 09:00:36,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248560.0, 'new_value': 256484.4}, {'field': 'total_amount', 'old_value': 248560.0, 'new_value': 256484.4}, {'field': 'order_count', 'old_value': 2761, 'new_value': 2831}]
2025-05-21 09:00:36,526 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-21 09:00:37,190 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-21 09:00:37,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200098.8, 'new_value': 202627.0}, {'field': 'total_amount', 'old_value': 200098.8, 'new_value': 202627.0}, {'field': 'order_count', 'old_value': 347, 'new_value': 354}]
2025-05-21 09:00:37,190 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-21 09:00:37,697 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-21 09:00:37,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405875.11, 'new_value': 429306.25}, {'field': 'total_amount', 'old_value': 484694.58, 'new_value': 508125.72}, {'field': 'order_count', 'old_value': 2087, 'new_value': 2159}]
2025-05-21 09:00:37,698 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-21 09:00:38,126 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-21 09:00:38,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163702.0, 'new_value': 168529.0}, {'field': 'total_amount', 'old_value': 163835.0, 'new_value': 168662.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 115}]
2025-05-21 09:00:38,126 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-21 09:00:38,565 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-21 09:00:38,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84205.19, 'new_value': 88008.19}, {'field': 'total_amount', 'old_value': 89816.71, 'new_value': 93619.71}, {'field': 'order_count', 'old_value': 8041, 'new_value': 8411}]
2025-05-21 09:00:38,566 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-21 09:00:38,992 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-21 09:00:38,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63008.0, 'new_value': 64325.0}, {'field': 'total_amount', 'old_value': 63008.0, 'new_value': 64325.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 85}]
2025-05-21 09:00:38,994 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-21 09:00:39,480 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-21 09:00:39,480 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57717.68, 'new_value': 59971.8}, {'field': 'offline_amount', 'old_value': 204228.89, 'new_value': 210554.14}, {'field': 'total_amount', 'old_value': 261946.57, 'new_value': 270525.94}, {'field': 'order_count', 'old_value': 3316, 'new_value': 3378}]
2025-05-21 09:00:39,480 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-21 09:00:39,942 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-21 09:00:39,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55061.9, 'new_value': 56522.9}, {'field': 'total_amount', 'old_value': 55061.9, 'new_value': 56522.9}, {'field': 'order_count', 'old_value': 145, 'new_value': 1606}]
2025-05-21 09:00:39,942 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-21 09:00:40,410 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-21 09:00:40,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 560204.1, 'new_value': 587248.15}, {'field': 'total_amount', 'old_value': 560204.1, 'new_value': 587248.15}, {'field': 'order_count', 'old_value': 1482, 'new_value': 1539}]
2025-05-21 09:00:40,410 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-21 09:00:40,997 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-21 09:00:40,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 559290.0, 'new_value': 627468.0}, {'field': 'total_amount', 'old_value': 559290.0, 'new_value': 627468.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 94}]
2025-05-21 09:00:40,998 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-21 09:00:41,471 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-21 09:00:41,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 900.0, 'new_value': 1252.0}, {'field': 'offline_amount', 'old_value': 452048.0, 'new_value': 518851.0}, {'field': 'total_amount', 'old_value': 452948.0, 'new_value': 520103.0}, {'field': 'order_count', 'old_value': 214, 'new_value': 231}]
2025-05-21 09:00:41,472 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-21 09:00:41,938 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-21 09:00:41,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156743.0, 'new_value': 162855.0}, {'field': 'total_amount', 'old_value': 156743.0, 'new_value': 162855.0}, {'field': 'order_count', 'old_value': 237, 'new_value': 245}]
2025-05-21 09:00:41,939 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-21 09:00:42,546 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-21 09:00:42,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235878.0, 'new_value': 256970.0}, {'field': 'total_amount', 'old_value': 235878.0, 'new_value': 256970.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 63}]
2025-05-21 09:00:42,547 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-21 09:00:43,018 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-21 09:00:43,019 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 577731.0, 'new_value': 647234.0}, {'field': 'total_amount', 'old_value': 577731.0, 'new_value': 647234.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 152}]
2025-05-21 09:00:43,019 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-21 09:00:43,486 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-21 09:00:43,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64161.0, 'new_value': 65496.0}, {'field': 'total_amount', 'old_value': 64161.0, 'new_value': 65496.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 163}]
2025-05-21 09:00:43,486 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-21 09:00:43,882 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-21 09:00:43,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157547.9, 'new_value': 167625.9}, {'field': 'total_amount', 'old_value': 157547.9, 'new_value': 167625.9}, {'field': 'order_count', 'old_value': 866, 'new_value': 924}]
2025-05-21 09:00:43,882 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-21 09:00:44,304 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-21 09:00:44,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8720.0, 'new_value': 8853.8}, {'field': 'total_amount', 'old_value': 24020.0, 'new_value': 24153.8}, {'field': 'order_count', 'old_value': 136, 'new_value': 138}]
2025-05-21 09:00:44,305 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-21 09:00:44,904 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-21 09:00:44,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104560.1, 'new_value': 106603.5}, {'field': 'total_amount', 'old_value': 104560.1, 'new_value': 106603.5}, {'field': 'order_count', 'old_value': 198, 'new_value': 203}]
2025-05-21 09:00:44,904 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-21 09:00:45,368 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-21 09:00:45,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88482.8, 'new_value': 96279.8}, {'field': 'offline_amount', 'old_value': 62371.56, 'new_value': 64885.56}, {'field': 'total_amount', 'old_value': 150854.36, 'new_value': 161165.36}, {'field': 'order_count', 'old_value': 1007, 'new_value': 1084}]
2025-05-21 09:00:45,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-21 09:00:45,820 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-21 09:00:45,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 743513.0, 'new_value': 765716.0}, {'field': 'total_amount', 'old_value': 743513.0, 'new_value': 765716.0}, {'field': 'order_count', 'old_value': 822, 'new_value': 852}]
2025-05-21 09:00:45,820 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-21 09:00:46,247 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-21 09:00:46,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 509707.0, 'new_value': 531203.0}, {'field': 'total_amount', 'old_value': 509707.0, 'new_value': 531203.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-05-21 09:00:46,248 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-21 09:00:46,688 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-21 09:00:46,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 263301.31, 'new_value': 276102.45}, {'field': 'offline_amount', 'old_value': 1001804.46, 'new_value': 1046282.83}, {'field': 'total_amount', 'old_value': 1265105.77, 'new_value': 1322385.28}, {'field': 'order_count', 'old_value': 6341, 'new_value': 6632}]
2025-05-21 09:00:46,688 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-21 09:00:47,146 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-21 09:00:47,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104626.9, 'new_value': 111383.0}, {'field': 'offline_amount', 'old_value': 86127.8, 'new_value': 89164.8}, {'field': 'total_amount', 'old_value': 190754.7, 'new_value': 200547.8}, {'field': 'order_count', 'old_value': 4487, 'new_value': 4709}]
2025-05-21 09:00:47,147 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-21 09:00:47,632 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-21 09:00:47,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1830000.0, 'new_value': 1880000.0}, {'field': 'total_amount', 'old_value': 1830000.0, 'new_value': 1880000.0}, {'field': 'order_count', 'old_value': 276, 'new_value': 277}]
2025-05-21 09:00:47,632 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-21 09:00:48,070 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-21 09:00:48,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205254.93, 'new_value': 215771.73}, {'field': 'total_amount', 'old_value': 205254.93, 'new_value': 215771.73}, {'field': 'order_count', 'old_value': 1253, 'new_value': 1315}]
2025-05-21 09:00:48,070 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-21 09:00:48,476 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-21 09:00:48,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174667.94, 'new_value': 186075.52}, {'field': 'offline_amount', 'old_value': 597086.45, 'new_value': 625971.55}, {'field': 'total_amount', 'old_value': 771754.39, 'new_value': 812047.07}, {'field': 'order_count', 'old_value': 4486, 'new_value': 4751}]
2025-05-21 09:00:48,476 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-21 09:00:48,934 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-21 09:00:48,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24668.75, 'new_value': 26229.85}, {'field': 'offline_amount', 'old_value': 279567.4, 'new_value': 291771.45}, {'field': 'total_amount', 'old_value': 304236.15, 'new_value': 318001.3}, {'field': 'order_count', 'old_value': 9357, 'new_value': 9448}]
2025-05-21 09:00:48,935 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-21 09:00:49,371 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-21 09:00:49,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 480000.0, 'new_value': 485000.0}, {'field': 'total_amount', 'old_value': 480000.0, 'new_value': 485000.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 144}]
2025-05-21 09:00:49,371 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-21 09:00:49,849 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-21 09:00:49,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 460000.0, 'new_value': 465000.0}, {'field': 'total_amount', 'old_value': 460000.0, 'new_value': 465000.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 143}]
2025-05-21 09:00:49,849 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-21 09:00:50,270 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-21 09:00:50,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2948674.0, 'new_value': 2998674.0}, {'field': 'total_amount', 'old_value': 2948674.0, 'new_value': 2998674.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 297}]
2025-05-21 09:00:50,271 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-21 09:00:50,693 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-21 09:00:50,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53495.1, 'new_value': 54802.1}, {'field': 'total_amount', 'old_value': 54300.1, 'new_value': 55607.1}, {'field': 'order_count', 'old_value': 16280, 'new_value': 16288}]
2025-05-21 09:00:50,694 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-21 09:00:51,173 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-21 09:00:51,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1600431.0, 'new_value': 1639214.0}, {'field': 'total_amount', 'old_value': 1600431.0, 'new_value': 1639214.0}, {'field': 'order_count', 'old_value': 6181, 'new_value': 6391}]
2025-05-21 09:00:51,173 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-21 09:00:51,590 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-21 09:00:51,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248302.16, 'new_value': 251890.16}, {'field': 'total_amount', 'old_value': 248302.16, 'new_value': 251890.16}, {'field': 'order_count', 'old_value': 1385, 'new_value': 1407}]
2025-05-21 09:00:51,590 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-21 09:00:52,031 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-21 09:00:52,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46832.73, 'new_value': 49423.28}, {'field': 'offline_amount', 'old_value': 36813.34, 'new_value': 38007.6}, {'field': 'total_amount', 'old_value': 83646.07, 'new_value': 87430.88}, {'field': 'order_count', 'old_value': 7082, 'new_value': 7368}]
2025-05-21 09:00:52,032 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-21 09:00:52,457 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-21 09:00:52,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74742.56, 'new_value': 80518.56}, {'field': 'total_amount', 'old_value': 74742.56, 'new_value': 80518.56}, {'field': 'order_count', 'old_value': 3857, 'new_value': 4162}]
2025-05-21 09:00:52,457 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-21 09:00:52,896 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-21 09:00:52,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 464001.0, 'new_value': 530938.0}, {'field': 'total_amount', 'old_value': 464001.0, 'new_value': 530938.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 83}]
2025-05-21 09:00:52,896 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-21 09:00:53,328 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-21 09:00:53,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 501962.0, 'new_value': 557503.0}, {'field': 'total_amount', 'old_value': 501962.0, 'new_value': 557503.0}, {'field': 'order_count', 'old_value': 384, 'new_value': 402}]
2025-05-21 09:00:53,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-21 09:00:53,780 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-21 09:00:53,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82332.19, 'new_value': 89050.01}, {'field': 'total_amount', 'old_value': 89561.26, 'new_value': 96279.08}, {'field': 'order_count', 'old_value': 487, 'new_value': 512}]
2025-05-21 09:00:53,781 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-21 09:00:54,235 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-21 09:00:54,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2399.0, 'new_value': 3026.0}, {'field': 'offline_amount', 'old_value': 45158.0, 'new_value': 46261.0}, {'field': 'total_amount', 'old_value': 47557.0, 'new_value': 49287.0}, {'field': 'order_count', 'old_value': 376, 'new_value': 394}]
2025-05-21 09:00:54,236 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-21 09:00:54,690 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-21 09:00:54,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 838998.0, 'new_value': 1238187.0}, {'field': 'total_amount', 'old_value': 838998.0, 'new_value': 1238187.0}, {'field': 'order_count', 'old_value': 3664, 'new_value': 4858}]
2025-05-21 09:00:54,691 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-21 09:00:55,159 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-21 09:00:55,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227056.0, 'new_value': 236908.0}, {'field': 'total_amount', 'old_value': 227056.0, 'new_value': 236908.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 293}]
2025-05-21 09:00:55,159 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-21 09:00:55,628 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-21 09:00:55,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112043.84, 'new_value': 116695.07}, {'field': 'total_amount', 'old_value': 112043.84, 'new_value': 116695.07}, {'field': 'order_count', 'old_value': 7708, 'new_value': 8033}]
2025-05-21 09:00:55,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-21 09:00:56,095 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-21 09:00:56,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79710.0, 'new_value': 82744.0}, {'field': 'total_amount', 'old_value': 79710.0, 'new_value': 82744.0}, {'field': 'order_count', 'old_value': 5391, 'new_value': 5599}]
2025-05-21 09:00:56,096 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-21 09:00:56,580 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-21 09:00:56,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48735.71, 'new_value': 50590.03}, {'field': 'total_amount', 'old_value': 54095.68, 'new_value': 55950.0}, {'field': 'order_count', 'old_value': 846, 'new_value': 864}]
2025-05-21 09:00:56,582 - INFO - 日期 2025-05 处理完成 - 更新: 56 条，插入: 0 条，错误: 0 条
2025-05-21 09:00:56,582 - INFO - 数据同步完成！更新: 56 条，插入: 0 条，错误: 0 条
2025-05-21 09:00:56,584 - INFO - =================同步完成====================
2025-05-21 12:00:01,959 - INFO - =================使用默认全量同步=============
2025-05-21 12:00:03,377 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 12:00:03,378 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 12:00:03,405 - INFO - 开始处理日期: 2025-01
2025-05-21 12:00:03,407 - INFO - Request Parameters - Page 1:
2025-05-21 12:00:03,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:03,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:04,639 - INFO - Response - Page 1:
2025-05-21 12:00:04,839 - INFO - 第 1 页获取到 100 条记录
2025-05-21 12:00:04,839 - INFO - Request Parameters - Page 2:
2025-05-21 12:00:04,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:04,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:05,372 - INFO - Response - Page 2:
2025-05-21 12:00:05,572 - INFO - 第 2 页获取到 100 条记录
2025-05-21 12:00:05,572 - INFO - Request Parameters - Page 3:
2025-05-21 12:00:05,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:05,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:06,022 - INFO - Response - Page 3:
2025-05-21 12:00:06,223 - INFO - 第 3 页获取到 100 条记录
2025-05-21 12:00:06,223 - INFO - Request Parameters - Page 4:
2025-05-21 12:00:06,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:06,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:06,740 - INFO - Response - Page 4:
2025-05-21 12:00:06,940 - INFO - 第 4 页获取到 100 条记录
2025-05-21 12:00:06,940 - INFO - Request Parameters - Page 5:
2025-05-21 12:00:06,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:06,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:07,446 - INFO - Response - Page 5:
2025-05-21 12:00:07,646 - INFO - 第 5 页获取到 100 条记录
2025-05-21 12:00:07,646 - INFO - Request Parameters - Page 6:
2025-05-21 12:00:07,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:07,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:08,188 - INFO - Response - Page 6:
2025-05-21 12:00:08,389 - INFO - 第 6 页获取到 100 条记录
2025-05-21 12:00:08,389 - INFO - Request Parameters - Page 7:
2025-05-21 12:00:08,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:08,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:08,865 - INFO - Response - Page 7:
2025-05-21 12:00:09,066 - INFO - 第 7 页获取到 82 条记录
2025-05-21 12:00:09,066 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 12:00:09,066 - INFO - 获取到 682 条表单数据
2025-05-21 12:00:09,078 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 12:00:09,089 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 12:00:09,090 - INFO - 开始处理日期: 2025-02
2025-05-21 12:00:09,090 - INFO - Request Parameters - Page 1:
2025-05-21 12:00:09,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:09,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:09,565 - INFO - Response - Page 1:
2025-05-21 12:00:09,767 - INFO - 第 1 页获取到 100 条记录
2025-05-21 12:00:09,767 - INFO - Request Parameters - Page 2:
2025-05-21 12:00:09,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:09,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:10,340 - INFO - Response - Page 2:
2025-05-21 12:00:10,541 - INFO - 第 2 页获取到 100 条记录
2025-05-21 12:00:10,541 - INFO - Request Parameters - Page 3:
2025-05-21 12:00:10,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:10,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:11,095 - INFO - Response - Page 3:
2025-05-21 12:00:11,295 - INFO - 第 3 页获取到 100 条记录
2025-05-21 12:00:11,295 - INFO - Request Parameters - Page 4:
2025-05-21 12:00:11,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:11,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:11,814 - INFO - Response - Page 4:
2025-05-21 12:00:12,015 - INFO - 第 4 页获取到 100 条记录
2025-05-21 12:00:12,015 - INFO - Request Parameters - Page 5:
2025-05-21 12:00:12,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:12,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:12,621 - INFO - Response - Page 5:
2025-05-21 12:00:12,821 - INFO - 第 5 页获取到 100 条记录
2025-05-21 12:00:12,821 - INFO - Request Parameters - Page 6:
2025-05-21 12:00:12,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:12,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:13,300 - INFO - Response - Page 6:
2025-05-21 12:00:13,500 - INFO - 第 6 页获取到 100 条记录
2025-05-21 12:00:13,500 - INFO - Request Parameters - Page 7:
2025-05-21 12:00:13,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:13,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:14,006 - INFO - Response - Page 7:
2025-05-21 12:00:14,206 - INFO - 第 7 页获取到 70 条记录
2025-05-21 12:00:14,206 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 12:00:14,206 - INFO - 获取到 670 条表单数据
2025-05-21 12:00:14,219 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 12:00:14,229 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 12:00:14,230 - INFO - 开始处理日期: 2025-03
2025-05-21 12:00:14,230 - INFO - Request Parameters - Page 1:
2025-05-21 12:00:14,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:14,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:14,754 - INFO - Response - Page 1:
2025-05-21 12:00:14,954 - INFO - 第 1 页获取到 100 条记录
2025-05-21 12:00:14,954 - INFO - Request Parameters - Page 2:
2025-05-21 12:00:14,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:14,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:15,511 - INFO - Response - Page 2:
2025-05-21 12:00:15,711 - INFO - 第 2 页获取到 100 条记录
2025-05-21 12:00:15,711 - INFO - Request Parameters - Page 3:
2025-05-21 12:00:15,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:15,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:16,209 - INFO - Response - Page 3:
2025-05-21 12:00:16,409 - INFO - 第 3 页获取到 100 条记录
2025-05-21 12:00:16,409 - INFO - Request Parameters - Page 4:
2025-05-21 12:00:16,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:16,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:16,931 - INFO - Response - Page 4:
2025-05-21 12:00:17,131 - INFO - 第 4 页获取到 100 条记录
2025-05-21 12:00:17,131 - INFO - Request Parameters - Page 5:
2025-05-21 12:00:17,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:17,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:17,691 - INFO - Response - Page 5:
2025-05-21 12:00:17,892 - INFO - 第 5 页获取到 100 条记录
2025-05-21 12:00:17,892 - INFO - Request Parameters - Page 6:
2025-05-21 12:00:17,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:17,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:18,367 - INFO - Response - Page 6:
2025-05-21 12:00:18,567 - INFO - 第 6 页获取到 100 条记录
2025-05-21 12:00:18,567 - INFO - Request Parameters - Page 7:
2025-05-21 12:00:18,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:18,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:19,054 - INFO - Response - Page 7:
2025-05-21 12:00:19,254 - INFO - 第 7 页获取到 61 条记录
2025-05-21 12:00:19,254 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 12:00:19,254 - INFO - 获取到 661 条表单数据
2025-05-21 12:00:19,266 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 12:00:19,277 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 12:00:19,277 - INFO - 开始处理日期: 2025-04
2025-05-21 12:00:19,278 - INFO - Request Parameters - Page 1:
2025-05-21 12:00:19,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:19,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:19,859 - INFO - Response - Page 1:
2025-05-21 12:00:20,059 - INFO - 第 1 页获取到 100 条记录
2025-05-21 12:00:20,059 - INFO - Request Parameters - Page 2:
2025-05-21 12:00:20,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:20,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:20,539 - INFO - Response - Page 2:
2025-05-21 12:00:20,739 - INFO - 第 2 页获取到 100 条记录
2025-05-21 12:00:20,739 - INFO - Request Parameters - Page 3:
2025-05-21 12:00:20,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:20,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:21,300 - INFO - Response - Page 3:
2025-05-21 12:00:21,500 - INFO - 第 3 页获取到 100 条记录
2025-05-21 12:00:21,500 - INFO - Request Parameters - Page 4:
2025-05-21 12:00:21,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:21,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:22,024 - INFO - Response - Page 4:
2025-05-21 12:00:22,224 - INFO - 第 4 页获取到 100 条记录
2025-05-21 12:00:22,224 - INFO - Request Parameters - Page 5:
2025-05-21 12:00:22,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:22,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:22,682 - INFO - Response - Page 5:
2025-05-21 12:00:22,882 - INFO - 第 5 页获取到 100 条记录
2025-05-21 12:00:22,882 - INFO - Request Parameters - Page 6:
2025-05-21 12:00:22,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:22,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:23,434 - INFO - Response - Page 6:
2025-05-21 12:00:23,635 - INFO - 第 6 页获取到 100 条记录
2025-05-21 12:00:23,635 - INFO - Request Parameters - Page 7:
2025-05-21 12:00:23,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:23,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:24,092 - INFO - Response - Page 7:
2025-05-21 12:00:24,292 - INFO - 第 7 页获取到 56 条记录
2025-05-21 12:00:24,292 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 12:00:24,292 - INFO - 获取到 656 条表单数据
2025-05-21 12:00:24,305 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 12:00:24,316 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 12:00:24,316 - INFO - 开始处理日期: 2025-05
2025-05-21 12:00:24,316 - INFO - Request Parameters - Page 1:
2025-05-21 12:00:24,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:24,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:24,806 - INFO - Response - Page 1:
2025-05-21 12:00:25,006 - INFO - 第 1 页获取到 100 条记录
2025-05-21 12:00:25,006 - INFO - Request Parameters - Page 2:
2025-05-21 12:00:25,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:25,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:25,501 - INFO - Response - Page 2:
2025-05-21 12:00:25,701 - INFO - 第 2 页获取到 100 条记录
2025-05-21 12:00:25,701 - INFO - Request Parameters - Page 3:
2025-05-21 12:00:25,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:25,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:26,203 - INFO - Response - Page 3:
2025-05-21 12:00:26,403 - INFO - 第 3 页获取到 100 条记录
2025-05-21 12:00:26,403 - INFO - Request Parameters - Page 4:
2025-05-21 12:00:26,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:26,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:26,979 - INFO - Response - Page 4:
2025-05-21 12:00:27,181 - INFO - 第 4 页获取到 100 条记录
2025-05-21 12:00:27,181 - INFO - Request Parameters - Page 5:
2025-05-21 12:00:27,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:27,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:27,675 - INFO - Response - Page 5:
2025-05-21 12:00:27,875 - INFO - 第 5 页获取到 100 条记录
2025-05-21 12:00:27,875 - INFO - Request Parameters - Page 6:
2025-05-21 12:00:27,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:27,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:28,390 - INFO - Response - Page 6:
2025-05-21 12:00:28,591 - INFO - 第 6 页获取到 100 条记录
2025-05-21 12:00:28,591 - INFO - Request Parameters - Page 7:
2025-05-21 12:00:28,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:00:28,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:00:28,979 - INFO - Response - Page 7:
2025-05-21 12:00:29,180 - INFO - 第 7 页获取到 28 条记录
2025-05-21 12:00:29,180 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 12:00:29,180 - INFO - 获取到 628 条表单数据
2025-05-21 12:00:29,191 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 12:00:29,192 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-21 12:00:29,723 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-21 12:00:29,724 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1745.0, 'new_value': 1752.0}, {'field': 'offline_amount', 'old_value': 31960.0, 'new_value': 33193.0}, {'field': 'total_amount', 'old_value': 33705.0, 'new_value': 34945.0}, {'field': 'order_count', 'old_value': 452, 'new_value': 470}]
2025-05-21 12:00:29,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-21 12:00:30,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-21 12:00:30,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281565.0, 'new_value': 313753.0}, {'field': 'total_amount', 'old_value': 281565.0, 'new_value': 313753.0}, {'field': 'order_count', 'old_value': 212, 'new_value': 233}]
2025-05-21 12:00:30,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-21 12:00:30,688 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-21 12:00:30,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7600000.0, 'new_value': 8000000.0}, {'field': 'total_amount', 'old_value': 7700000.0, 'new_value': 8100000.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-21 12:00:30,688 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-21 12:00:31,097 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-21 12:00:31,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33390.0, 'new_value': 37630.0}, {'field': 'total_amount', 'old_value': 34980.0, 'new_value': 39220.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 148}]
2025-05-21 12:00:31,098 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-21 12:00:31,722 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-21 12:00:31,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208035.0, 'new_value': 209035.0}, {'field': 'total_amount', 'old_value': 208035.0, 'new_value': 209035.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-21 12:00:31,722 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-21 12:00:32,329 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-21 12:00:32,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135817.56, 'new_value': 145388.48}, {'field': 'total_amount', 'old_value': 135817.56, 'new_value': 145388.48}, {'field': 'order_count', 'old_value': 5362, 'new_value': 5678}]
2025-05-21 12:00:32,330 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-21 12:00:32,809 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-21 12:00:32,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 564576.98, 'new_value': 592551.98}, {'field': 'total_amount', 'old_value': 564576.98, 'new_value': 592551.98}, {'field': 'order_count', 'old_value': 1681, 'new_value': 1788}]
2025-05-21 12:00:32,809 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-21 12:00:33,290 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-21 12:00:33,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31617.32, 'new_value': 33917.0}, {'field': 'total_amount', 'old_value': 31617.32, 'new_value': 33917.0}, {'field': 'order_count', 'old_value': 6148, 'new_value': 6610}]
2025-05-21 12:00:33,291 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-21 12:00:33,765 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-21 12:00:33,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48611.17, 'new_value': 53633.95}, {'field': 'total_amount', 'old_value': 53199.89, 'new_value': 58222.67}, {'field': 'order_count', 'old_value': 1906, 'new_value': 2075}]
2025-05-21 12:00:33,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-21 12:00:34,328 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-21 12:00:34,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23602.33, 'new_value': 25925.33}, {'field': 'offline_amount', 'old_value': 12149.03, 'new_value': 12872.03}, {'field': 'total_amount', 'old_value': 35751.36, 'new_value': 38797.36}, {'field': 'order_count', 'old_value': 1834, 'new_value': 2001}]
2025-05-21 12:00:34,329 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-21 12:00:34,799 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-21 12:00:34,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44208.78, 'new_value': 46477.78}, {'field': 'total_amount', 'old_value': 44208.78, 'new_value': 46477.78}, {'field': 'order_count', 'old_value': 97, 'new_value': 100}]
2025-05-21 12:00:34,800 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-21 12:00:35,374 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-21 12:00:35,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266390.0, 'new_value': 285910.0}, {'field': 'total_amount', 'old_value': 266390.0, 'new_value': 285910.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 166}]
2025-05-21 12:00:35,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-21 12:00:35,910 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-21 12:00:35,910 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 322072.0, 'new_value': 337107.0}, {'field': 'offline_amount', 'old_value': 246578.0, 'new_value': 249787.0}, {'field': 'total_amount', 'old_value': 568650.0, 'new_value': 586894.0}, {'field': 'order_count', 'old_value': 614, 'new_value': 637}]
2025-05-21 12:00:35,910 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-21 12:00:36,538 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-21 12:00:36,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72036.0, 'new_value': 77930.0}, {'field': 'total_amount', 'old_value': 72036.0, 'new_value': 77930.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 93}]
2025-05-21 12:00:36,539 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-21 12:00:36,951 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-21 12:00:36,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37520.0, 'new_value': 39740.0}, {'field': 'total_amount', 'old_value': 41640.0, 'new_value': 43860.0}, {'field': 'order_count', 'old_value': 407, 'new_value': 430}]
2025-05-21 12:00:36,951 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-21 12:00:37,499 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-21 12:00:37,499 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2084.6, 'new_value': 2157.6}, {'field': 'total_amount', 'old_value': 27013.4, 'new_value': 27086.4}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-21 12:00:37,500 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-21 12:00:37,969 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-21 12:00:37,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37875.3, 'new_value': 40123.1}, {'field': 'total_amount', 'old_value': 41835.3, 'new_value': 44083.1}, {'field': 'order_count', 'old_value': 304, 'new_value': 315}]
2025-05-21 12:00:37,969 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-21 12:00:38,452 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-21 12:00:38,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145066.0, 'new_value': 151406.0}, {'field': 'total_amount', 'old_value': 145066.0, 'new_value': 151406.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 44}]
2025-05-21 12:00:38,452 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-21 12:00:38,923 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-21 12:00:38,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44575.22, 'new_value': 46537.04}, {'field': 'offline_amount', 'old_value': 86456.45, 'new_value': 92021.45}, {'field': 'total_amount', 'old_value': 131031.67, 'new_value': 138558.49}, {'field': 'order_count', 'old_value': 1531, 'new_value': 1614}]
2025-05-21 12:00:38,924 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-21 12:00:39,369 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-21 12:00:39,369 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16846.76, 'new_value': 17850.44}, {'field': 'offline_amount', 'old_value': 20314.2, 'new_value': 21920.0}, {'field': 'total_amount', 'old_value': 37160.96, 'new_value': 39770.44}, {'field': 'order_count', 'old_value': 1806, 'new_value': 1932}]
2025-05-21 12:00:39,369 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-21 12:00:39,781 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-21 12:00:39,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257649.7, 'new_value': 263557.3}, {'field': 'total_amount', 'old_value': 372669.4, 'new_value': 378577.0}, {'field': 'order_count', 'old_value': 2732, 'new_value': 2864}]
2025-05-21 12:00:39,781 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-21 12:00:40,324 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-21 12:00:40,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79050.0, 'new_value': 83262.0}, {'field': 'total_amount', 'old_value': 79050.0, 'new_value': 83262.0}, {'field': 'order_count', 'old_value': 4281, 'new_value': 4515}]
2025-05-21 12:00:40,324 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-21 12:00:40,795 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-21 12:00:40,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316743.48, 'new_value': 341340.48}, {'field': 'total_amount', 'old_value': 316743.48, 'new_value': 341340.48}, {'field': 'order_count', 'old_value': 352, 'new_value': 367}]
2025-05-21 12:00:40,795 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-21 12:00:41,219 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-21 12:00:41,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111493.35, 'new_value': 117444.18}, {'field': 'total_amount', 'old_value': 111493.35, 'new_value': 117444.18}, {'field': 'order_count', 'old_value': 1269, 'new_value': 1350}]
2025-05-21 12:00:41,220 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-21 12:00:41,732 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-21 12:00:41,732 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 860163.0, 'new_value': 906330.0}, {'field': 'offline_amount', 'old_value': 263447.0, 'new_value': 274329.0}, {'field': 'total_amount', 'old_value': 1123610.0, 'new_value': 1180659.0}, {'field': 'order_count', 'old_value': 1292, 'new_value': 1361}]
2025-05-21 12:00:41,732 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-21 12:00:42,282 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-21 12:00:42,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10371.93, 'new_value': 10976.65}, {'field': 'offline_amount', 'old_value': 29374.79, 'new_value': 31062.64}, {'field': 'total_amount', 'old_value': 39746.72, 'new_value': 42039.29}, {'field': 'order_count', 'old_value': 697, 'new_value': 738}]
2025-05-21 12:00:42,282 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-21 12:00:42,673 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-21 12:00:42,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6877.0, 'new_value': 45877.0}, {'field': 'total_amount', 'old_value': 44477.0, 'new_value': 83477.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 15}]
2025-05-21 12:00:42,673 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-21 12:00:43,077 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-21 12:00:43,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 185174.11, 'new_value': 193650.04}, {'field': 'offline_amount', 'old_value': 14213.15, 'new_value': 14634.15}, {'field': 'total_amount', 'old_value': 199387.26, 'new_value': 208284.19}, {'field': 'order_count', 'old_value': 4218, 'new_value': 4479}]
2025-05-21 12:00:43,077 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-21 12:00:43,558 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-21 12:00:43,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6369.05, 'new_value': 6438.95}, {'field': 'offline_amount', 'old_value': 75208.7, 'new_value': 75223.7}, {'field': 'total_amount', 'old_value': 81577.75, 'new_value': 81662.65}, {'field': 'order_count', 'old_value': 1785, 'new_value': 1790}]
2025-05-21 12:00:43,559 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-21 12:00:44,139 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-21 12:00:44,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202400.0, 'new_value': 230200.0}, {'field': 'total_amount', 'old_value': 232400.0, 'new_value': 260200.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 42}]
2025-05-21 12:00:44,140 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-21 12:00:44,554 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-21 12:00:44,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263620.89, 'new_value': 293620.89}, {'field': 'total_amount', 'old_value': 263620.89, 'new_value': 293620.89}, {'field': 'order_count', 'old_value': 45, 'new_value': 50}]
2025-05-21 12:00:44,554 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-21 12:00:45,012 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-21 12:00:45,012 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214200.13, 'new_value': 252280.13}, {'field': 'total_amount', 'old_value': 253560.13, 'new_value': 291640.13}, {'field': 'order_count', 'old_value': 37, 'new_value': 42}]
2025-05-21 12:00:45,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-21 12:00:45,420 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-21 12:00:45,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 637767.0, 'new_value': 657794.3}, {'field': 'total_amount', 'old_value': 680403.2, 'new_value': 700430.5}, {'field': 'order_count', 'old_value': 73, 'new_value': 75}]
2025-05-21 12:00:45,420 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-21 12:00:45,896 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-21 12:00:45,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44079.6, 'new_value': 45197.6}, {'field': 'total_amount', 'old_value': 59474.3, 'new_value': 60592.3}, {'field': 'order_count', 'old_value': 641, 'new_value': 643}]
2025-05-21 12:00:45,897 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-21 12:00:46,397 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-21 12:00:46,398 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49587.0, 'new_value': 54587.0}, {'field': 'total_amount', 'old_value': 119864.0, 'new_value': 124864.0}, {'field': 'order_count', 'old_value': 1570, 'new_value': 1647}]
2025-05-21 12:00:46,398 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-21 12:00:46,942 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-21 12:00:46,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16404.0, 'new_value': 18583.0}, {'field': 'total_amount', 'old_value': 22205.0, 'new_value': 24384.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-05-21 12:00:46,943 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-21 12:00:47,346 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-21 12:00:47,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146674.0, 'new_value': 153181.0}, {'field': 'total_amount', 'old_value': 146674.0, 'new_value': 153181.0}, {'field': 'order_count', 'old_value': 276, 'new_value': 293}]
2025-05-21 12:00:47,346 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-21 12:00:47,813 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-21 12:00:47,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142702.19, 'new_value': 145786.18}, {'field': 'total_amount', 'old_value': 142702.19, 'new_value': 145786.18}, {'field': 'order_count', 'old_value': 189, 'new_value': 202}]
2025-05-21 12:00:47,814 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-21 12:00:48,254 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-21 12:00:48,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10195.0, 'new_value': 11323.0}, {'field': 'total_amount', 'old_value': 10195.0, 'new_value': 11323.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-05-21 12:00:48,254 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-21 12:00:48,687 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-21 12:00:48,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30985.74, 'new_value': 34428.86}, {'field': 'total_amount', 'old_value': 30985.74, 'new_value': 34428.86}, {'field': 'order_count', 'old_value': 2338, 'new_value': 2543}]
2025-05-21 12:00:48,687 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-21 12:00:49,162 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-21 12:00:49,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 142455.15, 'new_value': 151167.53}, {'field': 'offline_amount', 'old_value': 24174.16, 'new_value': 25709.66}, {'field': 'total_amount', 'old_value': 166629.31, 'new_value': 176877.19}, {'field': 'order_count', 'old_value': 605, 'new_value': 644}]
2025-05-21 12:00:49,162 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-21 12:00:49,646 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-21 12:00:49,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 135653.0, 'new_value': 141944.0}, {'field': 'offline_amount', 'old_value': 50031.42, 'new_value': 52263.22}, {'field': 'total_amount', 'old_value': 185684.42, 'new_value': 194207.22}, {'field': 'order_count', 'old_value': 1145, 'new_value': 1200}]
2025-05-21 12:00:49,646 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-21 12:00:50,113 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-21 12:00:50,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3576.0, 'new_value': 5376.0}, {'field': 'offline_amount', 'old_value': 32210.0, 'new_value': 35612.0}, {'field': 'total_amount', 'old_value': 35786.0, 'new_value': 40988.0}, {'field': 'order_count', 'old_value': 162, 'new_value': 181}]
2025-05-21 12:00:50,113 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-21 12:00:50,631 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-21 12:00:50,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67850.69, 'new_value': 71635.89}, {'field': 'total_amount', 'old_value': 67850.69, 'new_value': 71635.89}, {'field': 'order_count', 'old_value': 1799, 'new_value': 1908}]
2025-05-21 12:00:50,631 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-21 12:00:51,054 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-21 12:00:51,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52575.3, 'new_value': 54567.12}, {'field': 'total_amount', 'old_value': 52575.3, 'new_value': 54567.12}, {'field': 'order_count', 'old_value': 92, 'new_value': 96}]
2025-05-21 12:00:51,055 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-21 12:00:51,500 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-21 12:00:51,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8513.0, 'new_value': 8893.0}, {'field': 'total_amount', 'old_value': 8513.0, 'new_value': 8893.0}, {'field': 'order_count', 'old_value': 302, 'new_value': 303}]
2025-05-21 12:00:51,500 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-21 12:00:52,016 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-21 12:00:52,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167724.1, 'new_value': 185169.1}, {'field': 'total_amount', 'old_value': 167724.1, 'new_value': 185169.1}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-21 12:00:52,017 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-21 12:00:52,389 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-21 12:00:52,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125607.0, 'new_value': 131883.0}, {'field': 'offline_amount', 'old_value': 54787.72, 'new_value': 56594.72}, {'field': 'total_amount', 'old_value': 180394.72, 'new_value': 188477.72}, {'field': 'order_count', 'old_value': 1267, 'new_value': 1330}]
2025-05-21 12:00:52,390 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-21 12:00:52,772 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-21 12:00:52,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41597.18, 'new_value': 43593.9}, {'field': 'offline_amount', 'old_value': 413704.57, 'new_value': 435599.85}, {'field': 'total_amount', 'old_value': 455301.75, 'new_value': 479193.75}, {'field': 'order_count', 'old_value': 1453, 'new_value': 1528}]
2025-05-21 12:00:52,772 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-21 12:00:53,213 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-21 12:00:53,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8505.43, 'new_value': 8932.87}, {'field': 'offline_amount', 'old_value': 137021.91, 'new_value': 143364.33}, {'field': 'total_amount', 'old_value': 145527.34, 'new_value': 152297.2}, {'field': 'order_count', 'old_value': 1576, 'new_value': 1652}]
2025-05-21 12:00:53,213 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-21 12:00:53,724 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-21 12:00:53,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41144.3, 'new_value': 45007.3}, {'field': 'total_amount', 'old_value': 41156.2, 'new_value': 45019.2}, {'field': 'order_count', 'old_value': 234, 'new_value': 253}]
2025-05-21 12:00:53,725 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-21 12:00:54,196 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-21 12:00:54,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65646.0, 'new_value': 70452.0}, {'field': 'total_amount', 'old_value': 120654.0, 'new_value': 125460.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-21 12:00:54,196 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-21 12:00:54,653 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-21 12:00:54,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161656.0, 'new_value': 166165.0}, {'field': 'total_amount', 'old_value': 161656.0, 'new_value': 166165.0}, {'field': 'order_count', 'old_value': 820, 'new_value': 855}]
2025-05-21 12:00:54,653 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-21 12:00:55,134 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-21 12:00:55,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157925.82, 'new_value': 167568.99}, {'field': 'total_amount', 'old_value': 157925.82, 'new_value': 167568.99}, {'field': 'order_count', 'old_value': 521, 'new_value': 559}]
2025-05-21 12:00:55,134 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-21 12:00:55,574 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-21 12:00:55,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62748.5, 'new_value': 65793.54}, {'field': 'total_amount', 'old_value': 67557.45, 'new_value': 70602.49}, {'field': 'order_count', 'old_value': 3870, 'new_value': 4062}]
2025-05-21 12:00:55,575 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-21 12:00:56,023 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-21 12:00:56,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63271.91, 'new_value': 67276.28}, {'field': 'offline_amount', 'old_value': 33344.51, 'new_value': 34769.16}, {'field': 'total_amount', 'old_value': 96616.42, 'new_value': 102045.44}, {'field': 'order_count', 'old_value': 3291, 'new_value': 3490}]
2025-05-21 12:00:56,023 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-21 12:00:56,523 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-21 12:00:56,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110502.0, 'new_value': 116766.0}, {'field': 'total_amount', 'old_value': 110502.0, 'new_value': 116766.0}, {'field': 'order_count', 'old_value': 2741, 'new_value': 2909}]
2025-05-21 12:00:56,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-21 12:00:56,972 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-21 12:00:56,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65889.92, 'new_value': 68447.1}, {'field': 'offline_amount', 'old_value': 675429.34, 'new_value': 710564.98}, {'field': 'total_amount', 'old_value': 741319.26, 'new_value': 779012.08}, {'field': 'order_count', 'old_value': 2400, 'new_value': 2534}]
2025-05-21 12:00:56,972 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-21 12:00:57,417 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-21 12:00:57,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24416.0, 'new_value': 25937.0}, {'field': 'total_amount', 'old_value': 24416.0, 'new_value': 25937.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-21 12:00:57,418 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-21 12:00:57,909 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-21 12:00:57,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17437.79, 'new_value': 18633.79}, {'field': 'total_amount', 'old_value': 17437.79, 'new_value': 18633.79}, {'field': 'order_count', 'old_value': 93, 'new_value': 102}]
2025-05-21 12:00:57,909 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-21 12:00:58,405 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-21 12:00:58,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128110.0, 'new_value': 135407.0}, {'field': 'total_amount', 'old_value': 128110.0, 'new_value': 135407.0}, {'field': 'order_count', 'old_value': 4748, 'new_value': 5040}]
2025-05-21 12:00:58,406 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-21 12:00:58,795 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-21 12:00:58,795 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 542012.29, 'new_value': 570958.51}, {'field': 'total_amount', 'old_value': 542012.29, 'new_value': 570958.51}]
2025-05-21 12:00:58,795 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-21 12:00:59,231 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-21 12:00:59,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42721.0, 'new_value': 44784.0}, {'field': 'total_amount', 'old_value': 42721.0, 'new_value': 44784.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 55}]
2025-05-21 12:00:59,231 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-21 12:00:59,622 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-21 12:00:59,622 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10919.0, 'new_value': 20357.0}, {'field': 'offline_amount', 'old_value': 354780.0, 'new_value': 404662.0}, {'field': 'total_amount', 'old_value': 365699.0, 'new_value': 425019.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 78}]
2025-05-21 12:00:59,623 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-21 12:01:00,089 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-21 12:01:00,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69371.0, 'new_value': 78370.0}, {'field': 'total_amount', 'old_value': 69371.0, 'new_value': 78370.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-21 12:01:00,089 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-21 12:01:00,549 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-21 12:01:00,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104390.48, 'new_value': 112114.75}, {'field': 'total_amount', 'old_value': 104390.48, 'new_value': 112114.75}, {'field': 'order_count', 'old_value': 3849, 'new_value': 4098}]
2025-05-21 12:01:00,549 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-21 12:01:01,092 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-21 12:01:01,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103049.0, 'new_value': 104387.0}, {'field': 'total_amount', 'old_value': 154723.0, 'new_value': 156061.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-21 12:01:01,092 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-21 12:01:01,568 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-21 12:01:01,569 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4310.0, 'new_value': 4360.0}, {'field': 'offline_amount', 'old_value': 14102.0, 'new_value': 15134.0}, {'field': 'total_amount', 'old_value': 18412.0, 'new_value': 19494.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 117}]
2025-05-21 12:01:01,569 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-21 12:01:02,056 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-21 12:01:02,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 224937.63, 'new_value': 243498.99}, {'field': 'offline_amount', 'old_value': 791.0, 'new_value': 809.0}, {'field': 'total_amount', 'old_value': 225728.63, 'new_value': 244307.99}, {'field': 'order_count', 'old_value': 2596, 'new_value': 2785}]
2025-05-21 12:01:02,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-21 12:01:02,454 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-21 12:01:02,454 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3423.44, 'new_value': 3457.25}, {'field': 'offline_amount', 'old_value': 107146.62, 'new_value': 114273.91}, {'field': 'total_amount', 'old_value': 110570.06, 'new_value': 117731.16}, {'field': 'order_count', 'old_value': 508, 'new_value': 543}]
2025-05-21 12:01:02,454 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-21 12:01:02,880 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-21 12:01:02,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49716.0, 'new_value': 50514.0}, {'field': 'total_amount', 'old_value': 49716.0, 'new_value': 50514.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 96}]
2025-05-21 12:01:02,880 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-21 12:01:03,420 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-21 12:01:03,420 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55963.85, 'new_value': 61768.29}, {'field': 'offline_amount', 'old_value': 380280.3, 'new_value': 411580.3}, {'field': 'total_amount', 'old_value': 436244.15, 'new_value': 473348.59}, {'field': 'order_count', 'old_value': 637, 'new_value': 686}]
2025-05-21 12:01:03,420 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-21 12:01:03,838 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-21 12:01:03,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137296.05, 'new_value': 145761.62}, {'field': 'total_amount', 'old_value': 137296.05, 'new_value': 145761.62}, {'field': 'order_count', 'old_value': 777, 'new_value': 826}]
2025-05-21 12:01:03,838 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-21 12:01:04,260 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-21 12:01:04,261 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86018.7, 'new_value': 92155.32}, {'field': 'offline_amount', 'old_value': 71196.1, 'new_value': 76346.56}, {'field': 'total_amount', 'old_value': 157214.8, 'new_value': 168501.88}, {'field': 'order_count', 'old_value': 5656, 'new_value': 6081}]
2025-05-21 12:01:04,261 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-21 12:01:04,920 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-21 12:01:04,920 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146801.83, 'new_value': 154306.52}, {'field': 'offline_amount', 'old_value': 356343.33, 'new_value': 368930.5}, {'field': 'total_amount', 'old_value': 503145.16, 'new_value': 523237.02}, {'field': 'order_count', 'old_value': 3628, 'new_value': 3790}]
2025-05-21 12:01:04,921 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-21 12:01:05,442 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-21 12:01:05,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23143.7, 'new_value': 24483.7}, {'field': 'total_amount', 'old_value': 23143.7, 'new_value': 24483.7}, {'field': 'order_count', 'old_value': 126, 'new_value': 135}]
2025-05-21 12:01:05,443 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-21 12:01:05,934 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-21 12:01:05,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132989.0, 'new_value': 135126.0}, {'field': 'total_amount', 'old_value': 132989.0, 'new_value': 135126.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-21 12:01:05,934 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-21 12:01:06,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-21 12:01:06,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1371681.6, 'new_value': 1438606.28}, {'field': 'total_amount', 'old_value': 1425126.7, 'new_value': 1492051.38}, {'field': 'order_count', 'old_value': 2504, 'new_value': 2660}]
2025-05-21 12:01:06,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-21 12:01:06,770 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-21 12:01:06,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6412.0, 'new_value': 7544.0}, {'field': 'total_amount', 'old_value': 6412.0, 'new_value': 7544.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-21 12:01:06,770 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-21 12:01:07,195 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-21 12:01:07,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31398.9, 'new_value': 32174.9}, {'field': 'total_amount', 'old_value': 31398.9, 'new_value': 32174.9}, {'field': 'order_count', 'old_value': 142, 'new_value': 145}]
2025-05-21 12:01:07,195 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-21 12:01:07,604 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-21 12:01:07,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149832.0, 'new_value': 156759.0}, {'field': 'total_amount', 'old_value': 149832.0, 'new_value': 156759.0}, {'field': 'order_count', 'old_value': 10394, 'new_value': 10579}]
2025-05-21 12:01:07,605 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-21 12:01:08,155 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-21 12:01:08,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45255.0, 'new_value': 48567.0}, {'field': 'total_amount', 'old_value': 73901.0, 'new_value': 77213.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-21 12:01:08,155 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-21 12:01:08,587 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-21 12:01:08,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17117.0, 'new_value': 17525.0}, {'field': 'total_amount', 'old_value': 17117.0, 'new_value': 17525.0}, {'field': 'order_count', 'old_value': 295, 'new_value': 302}]
2025-05-21 12:01:08,587 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-21 12:01:08,999 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-21 12:01:08,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102434.0, 'new_value': 104136.0}, {'field': 'total_amount', 'old_value': 102436.0, 'new_value': 104138.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-21 12:01:09,000 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-21 12:01:09,471 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-21 12:01:09,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4936.84, 'new_value': 5335.44}, {'field': 'offline_amount', 'old_value': 12533.01, 'new_value': 13207.79}, {'field': 'total_amount', 'old_value': 17469.85, 'new_value': 18543.23}, {'field': 'order_count', 'old_value': 617, 'new_value': 646}]
2025-05-21 12:01:09,471 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-21 12:01:09,918 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-21 12:01:09,918 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137552.51, 'new_value': 146823.82}, {'field': 'offline_amount', 'old_value': 108811.23, 'new_value': 117228.13}, {'field': 'total_amount', 'old_value': 246363.74, 'new_value': 264051.95}, {'field': 'order_count', 'old_value': 2179, 'new_value': 2324}]
2025-05-21 12:01:09,918 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-21 12:01:10,474 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-21 12:01:10,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60114.82, 'new_value': 60706.81}, {'field': 'total_amount', 'old_value': 60118.12, 'new_value': 60710.11}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-21 12:01:10,474 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-21 12:01:10,937 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-21 12:01:10,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 275394.2, 'new_value': 299481.6}, {'field': 'offline_amount', 'old_value': 68279.8, 'new_value': 79876.4}, {'field': 'total_amount', 'old_value': 343674.0, 'new_value': 379358.0}, {'field': 'order_count', 'old_value': 431, 'new_value': 476}]
2025-05-21 12:01:10,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-21 12:01:11,597 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-21 12:01:11,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21984.0, 'new_value': 23131.0}, {'field': 'total_amount', 'old_value': 21984.0, 'new_value': 23131.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 67}]
2025-05-21 12:01:11,597 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-21 12:01:12,091 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-21 12:01:12,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57462.0, 'new_value': 61574.0}, {'field': 'total_amount', 'old_value': 57462.0, 'new_value': 61574.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 51}]
2025-05-21 12:01:12,091 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-21 12:01:12,563 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-21 12:01:12,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130487.0, 'new_value': 138953.0}, {'field': 'total_amount', 'old_value': 138062.8, 'new_value': 146528.8}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-21 12:01:12,563 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-21 12:01:13,153 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-21 12:01:13,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168002.62, 'new_value': 179555.18}, {'field': 'total_amount', 'old_value': 174918.28, 'new_value': 186470.84}, {'field': 'order_count', 'old_value': 3636, 'new_value': 3897}]
2025-05-21 12:01:13,153 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-21 12:01:13,572 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-21 12:01:13,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34476.26, 'new_value': 36556.39}, {'field': 'offline_amount', 'old_value': 75009.16, 'new_value': 77388.17}, {'field': 'total_amount', 'old_value': 109485.42, 'new_value': 113944.56}, {'field': 'order_count', 'old_value': 3977, 'new_value': 4140}]
2025-05-21 12:01:13,572 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-21 12:01:13,984 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-21 12:01:13,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390182.79, 'new_value': 420589.09}, {'field': 'total_amount', 'old_value': 390182.79, 'new_value': 420589.09}, {'field': 'order_count', 'old_value': 3933, 'new_value': 4256}]
2025-05-21 12:01:13,984 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-21 12:01:14,381 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-21 12:01:14,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69092.39, 'new_value': 69622.29}, {'field': 'total_amount', 'old_value': 72861.49, 'new_value': 73391.39}, {'field': 'order_count', 'old_value': 379, 'new_value': 383}]
2025-05-21 12:01:14,382 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-21 12:01:14,791 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-21 12:01:14,792 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40470.0, 'new_value': 45013.0}, {'field': 'offline_amount', 'old_value': 139342.0, 'new_value': 143705.0}, {'field': 'total_amount', 'old_value': 179812.0, 'new_value': 188718.0}, {'field': 'order_count', 'old_value': 3880, 'new_value': 4099}]
2025-05-21 12:01:14,792 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-21 12:01:15,277 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-21 12:01:15,277 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10930.68, 'new_value': 11719.46}, {'field': 'offline_amount', 'old_value': 215806.6, 'new_value': 240999.34}, {'field': 'total_amount', 'old_value': 226737.28, 'new_value': 252718.8}, {'field': 'order_count', 'old_value': 1580, 'new_value': 1739}]
2025-05-21 12:01:15,277 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-21 12:01:15,846 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-21 12:01:15,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81661.64, 'new_value': 85461.64}, {'field': 'total_amount', 'old_value': 87001.64, 'new_value': 90801.64}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-21 12:01:15,846 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-21 12:01:16,301 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-21 12:01:16,301 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115341.09, 'new_value': 122270.55}, {'field': 'offline_amount', 'old_value': 95744.45, 'new_value': 97863.45}, {'field': 'total_amount', 'old_value': 211085.54, 'new_value': 220134.0}, {'field': 'order_count', 'old_value': 2103, 'new_value': 2204}]
2025-05-21 12:01:16,301 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-21 12:01:16,779 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-21 12:01:16,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 485697.04, 'new_value': 486176.04}, {'field': 'offline_amount', 'old_value': 208913.1, 'new_value': 208918.1}, {'field': 'total_amount', 'old_value': 694610.14, 'new_value': 695094.14}, {'field': 'order_count', 'old_value': 6228, 'new_value': 6233}]
2025-05-21 12:01:16,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-21 12:01:17,372 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-21 12:01:17,373 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35756.6, 'new_value': 35825.6}, {'field': 'total_amount', 'old_value': 36299.6, 'new_value': 36368.6}, {'field': 'order_count', 'old_value': 157, 'new_value': 158}]
2025-05-21 12:01:17,373 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-21 12:01:18,002 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-21 12:01:18,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58969.6, 'new_value': 62560.5}, {'field': 'offline_amount', 'old_value': 3692.85, 'new_value': 4383.95}, {'field': 'total_amount', 'old_value': 62662.45, 'new_value': 66944.45}, {'field': 'order_count', 'old_value': 192, 'new_value': 205}]
2025-05-21 12:01:18,002 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-21 12:01:18,382 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-21 12:01:18,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56402.42, 'new_value': 59252.04}, {'field': 'total_amount', 'old_value': 56402.42, 'new_value': 59252.04}, {'field': 'order_count', 'old_value': 1566, 'new_value': 1661}]
2025-05-21 12:01:18,382 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-21 12:01:18,816 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-21 12:01:18,817 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6845.6, 'new_value': 7064.4}, {'field': 'offline_amount', 'old_value': 37935.0, 'new_value': 39055.0}, {'field': 'total_amount', 'old_value': 44780.6, 'new_value': 46119.4}, {'field': 'order_count', 'old_value': 57, 'new_value': 60}]
2025-05-21 12:01:18,817 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-21 12:01:19,294 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-21 12:01:19,294 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96090.68, 'new_value': 101250.34}, {'field': 'offline_amount', 'old_value': 330561.33, 'new_value': 349649.35}, {'field': 'total_amount', 'old_value': 426652.01, 'new_value': 450899.69}, {'field': 'order_count', 'old_value': 2440, 'new_value': 2525}]
2025-05-21 12:01:19,294 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-21 12:01:19,698 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-21 12:01:19,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 342922.56, 'new_value': 353315.82}, {'field': 'total_amount', 'old_value': 342922.56, 'new_value': 353315.82}, {'field': 'order_count', 'old_value': 453, 'new_value': 472}]
2025-05-21 12:01:19,698 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-21 12:01:20,124 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-21 12:01:20,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12026.47, 'new_value': 12822.36}, {'field': 'offline_amount', 'old_value': 323176.66, 'new_value': 339811.32}, {'field': 'total_amount', 'old_value': 335203.13, 'new_value': 352633.68}, {'field': 'order_count', 'old_value': 1378, 'new_value': 1468}]
2025-05-21 12:01:20,124 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-21 12:01:20,569 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-21 12:01:20,569 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71517.0, 'new_value': 73084.0}, {'field': 'offline_amount', 'old_value': 58742.46, 'new_value': 62757.46}, {'field': 'total_amount', 'old_value': 130259.46, 'new_value': 135841.46}, {'field': 'order_count', 'old_value': 155, 'new_value': 164}]
2025-05-21 12:01:20,570 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-21 12:01:20,982 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-21 12:01:20,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131403.2, 'new_value': 135176.0}, {'field': 'total_amount', 'old_value': 131403.2, 'new_value': 135176.0}, {'field': 'order_count', 'old_value': 305, 'new_value': 312}]
2025-05-21 12:01:20,983 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-21 12:01:21,403 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-21 12:01:21,403 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 178785.42, 'new_value': 190680.87}, {'field': 'total_amount', 'old_value': 251629.51, 'new_value': 263524.96}, {'field': 'order_count', 'old_value': 2707, 'new_value': 2807}]
2025-05-21 12:01:21,403 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-21 12:01:21,832 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-21 12:01:21,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78293.15, 'new_value': 80127.41}, {'field': 'total_amount', 'old_value': 78293.15, 'new_value': 80127.41}, {'field': 'order_count', 'old_value': 3000, 'new_value': 3075}]
2025-05-21 12:01:21,832 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-21 12:01:22,300 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-21 12:01:22,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65762.1, 'new_value': 67198.94}, {'field': 'total_amount', 'old_value': 65762.1, 'new_value': 67198.94}, {'field': 'order_count', 'old_value': 61, 'new_value': 68}]
2025-05-21 12:01:22,300 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-21 12:01:22,776 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-21 12:01:22,776 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8269.41, 'new_value': 8867.21}, {'field': 'total_amount', 'old_value': 19790.39, 'new_value': 20388.19}, {'field': 'order_count', 'old_value': 84, 'new_value': 87}]
2025-05-21 12:01:22,776 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-21 12:01:23,223 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-21 12:01:23,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37162.0, 'new_value': 37548.0}, {'field': 'total_amount', 'old_value': 37162.0, 'new_value': 37548.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-05-21 12:01:23,223 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-21 12:01:23,688 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-21 12:01:23,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92462.09, 'new_value': 94714.07}, {'field': 'total_amount', 'old_value': 92462.09, 'new_value': 94714.07}, {'field': 'order_count', 'old_value': 621, 'new_value': 632}]
2025-05-21 12:01:23,688 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-21 12:01:24,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-21 12:01:24,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45309.0, 'new_value': 48851.0}, {'field': 'total_amount', 'old_value': 45309.0, 'new_value': 48851.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-05-21 12:01:24,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-21 12:01:24,611 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-21 12:01:24,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4773.0, 'new_value': 4863.0}, {'field': 'total_amount', 'old_value': 4773.0, 'new_value': 4863.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-21 12:01:24,612 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-21 12:01:25,005 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-21 12:01:25,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82461.3, 'new_value': 84361.6}, {'field': 'total_amount', 'old_value': 82461.3, 'new_value': 84361.6}, {'field': 'order_count', 'old_value': 251, 'new_value': 258}]
2025-05-21 12:01:25,005 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-21 12:01:25,426 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-21 12:01:25,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9687.29, 'new_value': 10228.39}, {'field': 'offline_amount', 'old_value': 97930.33, 'new_value': 102383.13}, {'field': 'total_amount', 'old_value': 107617.62, 'new_value': 112611.52}, {'field': 'order_count', 'old_value': 2780, 'new_value': 2921}]
2025-05-21 12:01:25,427 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-21 12:01:25,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-21 12:01:25,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138574.03, 'new_value': 149717.18}, {'field': 'offline_amount', 'old_value': 279673.74, 'new_value': 289755.54}, {'field': 'total_amount', 'old_value': 418247.77, 'new_value': 439472.72}, {'field': 'order_count', 'old_value': 11460, 'new_value': 12132}]
2025-05-21 12:01:25,875 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-21 12:01:26,315 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-21 12:01:26,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36559.0, 'new_value': 38405.0}, {'field': 'total_amount', 'old_value': 36559.0, 'new_value': 38405.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 100}]
2025-05-21 12:01:26,315 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-21 12:01:26,788 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-21 12:01:26,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6517.27, 'new_value': 6926.22}, {'field': 'offline_amount', 'old_value': 23842.0, 'new_value': 24001.0}, {'field': 'total_amount', 'old_value': 30359.27, 'new_value': 30927.22}, {'field': 'order_count', 'old_value': 166, 'new_value': 173}]
2025-05-21 12:01:26,789 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-21 12:01:27,226 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-21 12:01:27,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 881014.94, 'new_value': 901305.94}, {'field': 'total_amount', 'old_value': 881014.94, 'new_value': 901305.94}, {'field': 'order_count', 'old_value': 542, 'new_value': 547}]
2025-05-21 12:01:27,226 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-21 12:01:27,731 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-21 12:01:27,731 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27924.0, 'new_value': 30717.0}, {'field': 'total_amount', 'old_value': 27924.0, 'new_value': 30717.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 95}]
2025-05-21 12:01:27,731 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-21 12:01:28,185 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-21 12:01:28,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38729.27, 'new_value': 39090.07}, {'field': 'total_amount', 'old_value': 38926.07, 'new_value': 39286.87}, {'field': 'order_count', 'old_value': 329, 'new_value': 333}]
2025-05-21 12:01:28,186 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-21 12:01:29,529 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-21 12:01:29,529 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4319.0, 'new_value': 4554.0}, {'field': 'offline_amount', 'old_value': 19777.2, 'new_value': 20333.8}, {'field': 'total_amount', 'old_value': 24096.2, 'new_value': 24887.8}, {'field': 'order_count', 'old_value': 954, 'new_value': 988}]
2025-05-21 12:01:29,529 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-21 12:01:30,005 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-21 12:01:30,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76299.72, 'new_value': 77346.86}, {'field': 'total_amount', 'old_value': 76299.72, 'new_value': 77346.86}, {'field': 'order_count', 'old_value': 268, 'new_value': 280}]
2025-05-21 12:01:30,005 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-21 12:01:30,524 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-21 12:01:30,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 973000.0, 'new_value': 1008000.0}, {'field': 'total_amount', 'old_value': 973000.0, 'new_value': 1008000.0}, {'field': 'order_count', 'old_value': 338, 'new_value': 339}]
2025-05-21 12:01:30,524 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-21 12:01:31,000 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-21 12:01:31,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14158.09, 'new_value': 15032.94}, {'field': 'offline_amount', 'old_value': 24892.63, 'new_value': 26004.18}, {'field': 'total_amount', 'old_value': 39050.72, 'new_value': 41037.12}, {'field': 'order_count', 'old_value': 1715, 'new_value': 1822}]
2025-05-21 12:01:31,001 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-21 12:01:31,430 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-21 12:01:31,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159071.6, 'new_value': 169323.7}, {'field': 'total_amount', 'old_value': 159071.6, 'new_value': 169323.7}, {'field': 'order_count', 'old_value': 587, 'new_value': 627}]
2025-05-21 12:01:31,430 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-21 12:01:31,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-21 12:01:31,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40003.0, 'new_value': 41006.0}, {'field': 'total_amount', 'old_value': 42047.0, 'new_value': 43050.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 176}]
2025-05-21 12:01:31,875 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-21 12:01:32,359 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-21 12:01:32,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5628.0, 'new_value': 5727.0}, {'field': 'total_amount', 'old_value': 5628.0, 'new_value': 5727.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-21 12:01:32,359 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-21 12:01:32,762 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-21 12:01:32,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227170.97, 'new_value': 238726.72}, {'field': 'total_amount', 'old_value': 227170.97, 'new_value': 238726.72}, {'field': 'order_count', 'old_value': 6148, 'new_value': 6479}]
2025-05-21 12:01:32,763 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-21 12:01:33,226 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-21 12:01:33,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113874.57, 'new_value': 122161.68}, {'field': 'total_amount', 'old_value': 113874.57, 'new_value': 122161.68}, {'field': 'order_count', 'old_value': 562, 'new_value': 603}]
2025-05-21 12:01:33,226 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-21 12:01:33,726 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-21 12:01:33,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17787.1, 'new_value': 18564.92}, {'field': 'offline_amount', 'old_value': 31565.02, 'new_value': 33312.58}, {'field': 'total_amount', 'old_value': 49352.12, 'new_value': 51877.5}, {'field': 'order_count', 'old_value': 1773, 'new_value': 1863}]
2025-05-21 12:01:33,727 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-21 12:01:34,226 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-21 12:01:34,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53784.0, 'new_value': 57502.0}, {'field': 'total_amount', 'old_value': 56192.0, 'new_value': 59910.0}, {'field': 'order_count', 'old_value': 234, 'new_value': 247}]
2025-05-21 12:01:34,226 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-21 12:01:34,673 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-21 12:01:34,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42756.0, 'new_value': 49158.0}, {'field': 'total_amount', 'old_value': 42756.0, 'new_value': 49158.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-21 12:01:34,673 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-21 12:01:35,104 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-21 12:01:35,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18283.7, 'new_value': 18754.7}, {'field': 'offline_amount', 'old_value': 46609.46, 'new_value': 48064.46}, {'field': 'total_amount', 'old_value': 64893.16, 'new_value': 66819.16}, {'field': 'order_count', 'old_value': 734, 'new_value': 757}]
2025-05-21 12:01:35,105 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-21 12:01:35,610 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-21 12:01:35,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76575.3, 'new_value': 83198.6}, {'field': 'offline_amount', 'old_value': 105275.79, 'new_value': 113709.79}, {'field': 'total_amount', 'old_value': 181851.09, 'new_value': 196908.39}, {'field': 'order_count', 'old_value': 1240, 'new_value': 1345}]
2025-05-21 12:01:35,610 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-21 12:01:36,075 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-21 12:01:36,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76390.94, 'new_value': 80755.03}, {'field': 'offline_amount', 'old_value': 56815.73, 'new_value': 59330.48}, {'field': 'total_amount', 'old_value': 133206.67, 'new_value': 140085.51}, {'field': 'order_count', 'old_value': 5552, 'new_value': 5871}]
2025-05-21 12:01:36,076 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-21 12:01:36,490 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-21 12:01:36,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7462.11, 'new_value': 7748.81}, {'field': 'offline_amount', 'old_value': 70984.5, 'new_value': 73469.17}, {'field': 'total_amount', 'old_value': 78446.61, 'new_value': 81217.98}, {'field': 'order_count', 'old_value': 2184, 'new_value': 2262}]
2025-05-21 12:01:36,491 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-21 12:01:36,930 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-21 12:01:36,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15816.61, 'new_value': 17035.51}, {'field': 'offline_amount', 'old_value': 31122.54, 'new_value': 32848.1}, {'field': 'total_amount', 'old_value': 46939.15, 'new_value': 49883.61}, {'field': 'order_count', 'old_value': 2471, 'new_value': 2637}]
2025-05-21 12:01:36,930 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-21 12:01:37,444 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-21 12:01:37,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89970.0, 'new_value': 93720.0}, {'field': 'total_amount', 'old_value': 89970.0, 'new_value': 93720.0}, {'field': 'order_count', 'old_value': 4498, 'new_value': 4571}]
2025-05-21 12:01:37,444 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-21 12:01:37,867 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-21 12:01:37,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44825.88, 'new_value': 47343.97}, {'field': 'offline_amount', 'old_value': 189225.63, 'new_value': 200879.63}, {'field': 'total_amount', 'old_value': 234051.51, 'new_value': 248223.6}, {'field': 'order_count', 'old_value': 7256, 'new_value': 7697}]
2025-05-21 12:01:37,867 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-21 12:01:38,366 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-21 12:01:38,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25615.7, 'new_value': 26215.7}, {'field': 'total_amount', 'old_value': 27402.6, 'new_value': 28002.6}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-21 12:01:38,367 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-21 12:01:38,791 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-21 12:01:38,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7014.68, 'new_value': 7639.84}, {'field': 'offline_amount', 'old_value': 9793.72, 'new_value': 10218.83}, {'field': 'total_amount', 'old_value': 16808.4, 'new_value': 17858.67}, {'field': 'order_count', 'old_value': 1319, 'new_value': 1412}]
2025-05-21 12:01:38,792 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-21 12:01:39,255 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-21 12:01:39,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40759.0, 'new_value': 42747.0}, {'field': 'total_amount', 'old_value': 41108.0, 'new_value': 43096.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 77}]
2025-05-21 12:01:39,255 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-21 12:01:39,697 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-21 12:01:39,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4899527.0, 'new_value': 5117351.0}, {'field': 'total_amount', 'old_value': 4899527.0, 'new_value': 5117351.0}, {'field': 'order_count', 'old_value': 82227, 'new_value': 86212}]
2025-05-21 12:01:39,697 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-21 12:01:40,232 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-21 12:01:40,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42294.0, 'new_value': 43229.0}, {'field': 'total_amount', 'old_value': 42294.0, 'new_value': 43229.0}, {'field': 'order_count', 'old_value': 298, 'new_value': 307}]
2025-05-21 12:01:40,233 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-21 12:01:40,705 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-21 12:01:40,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39675.9, 'new_value': 41801.11}, {'field': 'offline_amount', 'old_value': 323714.27, 'new_value': 332111.74}, {'field': 'total_amount', 'old_value': 363390.17, 'new_value': 373912.85}, {'field': 'order_count', 'old_value': 3034, 'new_value': 3125}]
2025-05-21 12:01:40,705 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-21 12:01:41,218 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-21 12:01:41,218 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44959.98, 'new_value': 45253.98}, {'field': 'offline_amount', 'old_value': 357017.5, 'new_value': 387017.5}, {'field': 'total_amount', 'old_value': 401977.48, 'new_value': 432271.48}, {'field': 'order_count', 'old_value': 3135, 'new_value': 3321}]
2025-05-21 12:01:41,218 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-21 12:01:41,683 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-21 12:01:41,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95365.58, 'new_value': 97879.48}, {'field': 'total_amount', 'old_value': 95365.58, 'new_value': 97879.48}, {'field': 'order_count', 'old_value': 2766, 'new_value': 2858}]
2025-05-21 12:01:41,683 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-21 12:01:42,110 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-21 12:01:42,110 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91820.16, 'new_value': 98790.36}, {'field': 'offline_amount', 'old_value': 212297.63, 'new_value': 220425.81}, {'field': 'total_amount', 'old_value': 304117.79, 'new_value': 319216.17}, {'field': 'order_count', 'old_value': 3619, 'new_value': 3801}]
2025-05-21 12:01:42,110 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-21 12:01:42,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-21 12:01:42,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24689.38, 'new_value': 25042.18}, {'field': 'total_amount', 'old_value': 24689.38, 'new_value': 25042.18}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-21 12:01:42,613 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-21 12:01:43,061 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-21 12:01:43,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90927.0, 'new_value': 95427.0}, {'field': 'total_amount', 'old_value': 90927.0, 'new_value': 95427.0}, {'field': 'order_count', 'old_value': 3367, 'new_value': 3368}]
2025-05-21 12:01:43,061 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-21 12:01:43,534 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-21 12:01:43,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67395.09, 'new_value': 70702.86}, {'field': 'offline_amount', 'old_value': 32133.26, 'new_value': 34441.41}, {'field': 'total_amount', 'old_value': 99528.35, 'new_value': 105144.27}, {'field': 'order_count', 'old_value': 6302, 'new_value': 6569}]
2025-05-21 12:01:43,534 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-21 12:01:44,009 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-21 12:01:44,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23576.07, 'new_value': 24476.07}, {'field': 'total_amount', 'old_value': 23576.07, 'new_value': 24476.07}, {'field': 'order_count', 'old_value': 2296, 'new_value': 2297}]
2025-05-21 12:01:44,010 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-21 12:01:44,440 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-21 12:01:44,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123964.56, 'new_value': 138465.16}, {'field': 'total_amount', 'old_value': 123964.56, 'new_value': 138465.16}, {'field': 'order_count', 'old_value': 216, 'new_value': 233}]
2025-05-21 12:01:44,441 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-21 12:01:44,860 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-21 12:01:44,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168936.0, 'new_value': 180096.0}, {'field': 'total_amount', 'old_value': 168936.0, 'new_value': 180096.0}, {'field': 'order_count', 'old_value': 14078, 'new_value': 15008}]
2025-05-21 12:01:44,861 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-21 12:01:45,276 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-21 12:01:45,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27144.0, 'new_value': 37704.0}, {'field': 'total_amount', 'old_value': 27144.0, 'new_value': 37704.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-21 12:01:45,276 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-21 12:01:45,687 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-21 12:01:45,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88613.98, 'new_value': 93137.98}, {'field': 'total_amount', 'old_value': 88613.98, 'new_value': 93137.98}, {'field': 'order_count', 'old_value': 766, 'new_value': 803}]
2025-05-21 12:01:45,688 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-21 12:01:46,193 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-21 12:01:46,193 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73784.68, 'new_value': 77383.87}, {'field': 'offline_amount', 'old_value': 198912.46, 'new_value': 206222.46}, {'field': 'total_amount', 'old_value': 272697.14, 'new_value': 283606.33}, {'field': 'order_count', 'old_value': 8941, 'new_value': 9331}]
2025-05-21 12:01:46,193 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-21 12:01:46,683 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-21 12:01:46,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9564.0, 'new_value': 9932.0}, {'field': 'total_amount', 'old_value': 9564.0, 'new_value': 9932.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-21 12:01:46,683 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-21 12:01:47,073 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-21 12:01:47,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5030100.0, 'new_value': 5067100.0}, {'field': 'total_amount', 'old_value': 5030100.0, 'new_value': 5067100.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-05-21 12:01:47,074 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-21 12:01:47,588 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-21 12:01:47,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35644.0, 'new_value': 36282.0}, {'field': 'total_amount', 'old_value': 35644.0, 'new_value': 36282.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-21 12:01:47,588 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-21 12:01:48,005 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-21 12:01:48,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49586.0, 'new_value': 69032.8}, {'field': 'total_amount', 'old_value': 56510.0, 'new_value': 75956.8}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-21 12:01:48,005 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-21 12:01:48,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-21 12:01:48,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 907.2, 'new_value': 1546.5}, {'field': 'offline_amount', 'old_value': 37188.8, 'new_value': 37502.0}, {'field': 'total_amount', 'old_value': 38096.0, 'new_value': 39048.5}, {'field': 'order_count', 'old_value': 120, 'new_value': 123}]
2025-05-21 12:01:48,427 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-21 12:01:49,024 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-21 12:01:49,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241526.95, 'new_value': 254578.61}, {'field': 'offline_amount', 'old_value': 14048.85, 'new_value': 15884.77}, {'field': 'total_amount', 'old_value': 255575.8, 'new_value': 270463.38}, {'field': 'order_count', 'old_value': 10096, 'new_value': 10595}]
2025-05-21 12:01:49,024 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-21 12:01:49,573 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-21 12:01:49,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16569.5, 'new_value': 18108.1}, {'field': 'offline_amount', 'old_value': 88023.4, 'new_value': 96818.4}, {'field': 'total_amount', 'old_value': 104592.9, 'new_value': 114926.5}, {'field': 'order_count', 'old_value': 144, 'new_value': 156}]
2025-05-21 12:01:49,573 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-21 12:01:49,997 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-21 12:01:49,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35177.75, 'new_value': 36568.95}, {'field': 'total_amount', 'old_value': 35177.75, 'new_value': 36568.95}, {'field': 'order_count', 'old_value': 1543, 'new_value': 1611}]
2025-05-21 12:01:49,997 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-21 12:01:50,390 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-21 12:01:50,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 375439.0, 'new_value': 385439.0}, {'field': 'total_amount', 'old_value': 384257.99, 'new_value': 394257.99}, {'field': 'order_count', 'old_value': 70, 'new_value': 71}]
2025-05-21 12:01:50,390 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-21 12:01:50,803 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-21 12:01:50,803 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98992.63, 'new_value': 100511.83}, {'field': 'offline_amount', 'old_value': 215613.96, 'new_value': 225627.75}, {'field': 'total_amount', 'old_value': 314606.59, 'new_value': 326139.58}, {'field': 'order_count', 'old_value': 3970, 'new_value': 4152}]
2025-05-21 12:01:50,803 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-21 12:01:51,268 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-21 12:01:51,268 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80232.1, 'new_value': 86731.8}, {'field': 'total_amount', 'old_value': 195197.35, 'new_value': 201697.05}, {'field': 'order_count', 'old_value': 5061, 'new_value': 5267}]
2025-05-21 12:01:51,268 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-21 12:01:51,756 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-21 12:01:51,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204432.0, 'new_value': 216586.0}, {'field': 'total_amount', 'old_value': 204432.0, 'new_value': 216586.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 103}]
2025-05-21 12:01:51,756 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-21 12:01:52,220 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-21 12:01:52,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65928.8, 'new_value': 68139.8}, {'field': 'total_amount', 'old_value': 65928.8, 'new_value': 68139.8}, {'field': 'order_count', 'old_value': 487, 'new_value': 499}]
2025-05-21 12:01:52,221 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-21 12:01:52,685 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-21 12:01:52,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 323141.0, 'new_value': 333777.0}, {'field': 'total_amount', 'old_value': 323141.0, 'new_value': 333777.0}, {'field': 'order_count', 'old_value': 8700, 'new_value': 9076}]
2025-05-21 12:01:52,686 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-21 12:01:53,206 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-21 12:01:53,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321664.79, 'new_value': 336664.79}, {'field': 'total_amount', 'old_value': 321664.79, 'new_value': 336664.79}, {'field': 'order_count', 'old_value': 623, 'new_value': 624}]
2025-05-21 12:01:53,206 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-21 12:01:53,619 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-21 12:01:53,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81821.05, 'new_value': 88150.66}, {'field': 'total_amount', 'old_value': 81821.05, 'new_value': 88150.66}, {'field': 'order_count', 'old_value': 2348, 'new_value': 2579}]
2025-05-21 12:01:53,620 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-21 12:01:54,122 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-21 12:01:54,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14751.0, 'new_value': 15080.0}, {'field': 'total_amount', 'old_value': 14751.0, 'new_value': 15080.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-21 12:01:54,123 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-21 12:01:54,532 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-21 12:01:54,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24230.0, 'new_value': 25475.0}, {'field': 'total_amount', 'old_value': 24230.0, 'new_value': 25475.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 248}]
2025-05-21 12:01:54,532 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-21 12:01:55,004 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-21 12:01:55,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33809.38, 'new_value': 36214.79}, {'field': 'total_amount', 'old_value': 33809.38, 'new_value': 36214.79}, {'field': 'order_count', 'old_value': 136, 'new_value': 146}]
2025-05-21 12:01:55,005 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-21 12:01:55,489 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-21 12:01:55,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149104.59, 'new_value': 160065.59}, {'field': 'total_amount', 'old_value': 149104.59, 'new_value': 160065.59}, {'field': 'order_count', 'old_value': 6278, 'new_value': 6765}]
2025-05-21 12:01:55,490 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-21 12:01:55,928 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-21 12:01:55,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42100.84, 'new_value': 45586.1}, {'field': 'offline_amount', 'old_value': 25905.87, 'new_value': 27494.43}, {'field': 'total_amount', 'old_value': 68006.71, 'new_value': 73080.53}, {'field': 'order_count', 'old_value': 3724, 'new_value': 3974}]
2025-05-21 12:01:55,928 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-21 12:01:56,323 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-21 12:01:56,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61841.0, 'new_value': 64596.0}, {'field': 'total_amount', 'old_value': 66337.0, 'new_value': 69092.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-21 12:01:56,324 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-21 12:01:56,823 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-21 12:01:56,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85225.0, 'new_value': 86224.0}, {'field': 'total_amount', 'old_value': 106482.41, 'new_value': 107481.41}, {'field': 'order_count', 'old_value': 88, 'new_value': 89}]
2025-05-21 12:01:56,824 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-21 12:01:57,352 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-21 12:01:57,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65086.0, 'new_value': 65286.0}, {'field': 'total_amount', 'old_value': 65086.0, 'new_value': 65286.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-21 12:01:57,352 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-21 12:01:57,814 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-21 12:01:57,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25008.0, 'new_value': 25166.0}, {'field': 'total_amount', 'old_value': 25008.0, 'new_value': 25166.0}, {'field': 'order_count', 'old_value': 234, 'new_value': 237}]
2025-05-21 12:01:57,814 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-21 12:01:58,277 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-21 12:01:58,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17995.18, 'new_value': 18041.18}, {'field': 'total_amount', 'old_value': 17995.18, 'new_value': 18041.18}, {'field': 'order_count', 'old_value': 149, 'new_value': 152}]
2025-05-21 12:01:58,278 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-21 12:01:58,807 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-21 12:01:58,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319452.04, 'new_value': 347703.24}, {'field': 'total_amount', 'old_value': 319452.04, 'new_value': 347703.24}, {'field': 'order_count', 'old_value': 1147, 'new_value': 1249}]
2025-05-21 12:01:58,807 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-21 12:01:59,208 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-21 12:01:59,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244586.8, 'new_value': 253775.6}, {'field': 'total_amount', 'old_value': 244586.8, 'new_value': 253775.6}, {'field': 'order_count', 'old_value': 6075, 'new_value': 6375}]
2025-05-21 12:01:59,209 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-21 12:01:59,707 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-21 12:01:59,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29851.63, 'new_value': 31103.98}, {'field': 'total_amount', 'old_value': 29851.63, 'new_value': 31103.98}, {'field': 'order_count', 'old_value': 3819, 'new_value': 3991}]
2025-05-21 12:01:59,707 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-21 12:02:00,214 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-21 12:02:00,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26222.79, 'new_value': 27210.71}, {'field': 'offline_amount', 'old_value': 48783.86, 'new_value': 53026.96}, {'field': 'total_amount', 'old_value': 75006.65, 'new_value': 80237.67}, {'field': 'order_count', 'old_value': 639, 'new_value': 677}]
2025-05-21 12:02:00,214 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-21 12:02:00,683 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-21 12:02:00,683 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19343.66, 'new_value': 20661.17}, {'field': 'offline_amount', 'old_value': 25455.41, 'new_value': 26544.87}, {'field': 'total_amount', 'old_value': 44799.07, 'new_value': 47206.04}, {'field': 'order_count', 'old_value': 2009, 'new_value': 2104}]
2025-05-21 12:02:00,684 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-21 12:02:01,159 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-21 12:02:01,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65636.32, 'new_value': 68009.27}, {'field': 'offline_amount', 'old_value': 103012.01, 'new_value': 105286.41}, {'field': 'total_amount', 'old_value': 168648.33, 'new_value': 173295.68}, {'field': 'order_count', 'old_value': 1690, 'new_value': 1750}]
2025-05-21 12:02:01,159 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-21 12:02:01,568 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-21 12:02:01,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61309.0, 'new_value': 67679.0}, {'field': 'total_amount', 'old_value': 66510.0, 'new_value': 72880.0}, {'field': 'order_count', 'old_value': 194, 'new_value': 209}]
2025-05-21 12:02:01,568 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-21 12:02:02,058 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-21 12:02:02,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1281799.27, 'new_value': 1355564.27}, {'field': 'offline_amount', 'old_value': 148855.3, 'new_value': 153405.3}, {'field': 'total_amount', 'old_value': 1430654.57, 'new_value': 1508969.57}, {'field': 'order_count', 'old_value': 4969, 'new_value': 5149}]
2025-05-21 12:02:02,058 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-21 12:02:02,480 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-21 12:02:02,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24718.0, 'new_value': 25918.0}, {'field': 'total_amount', 'old_value': 26094.0, 'new_value': 27294.0}, {'field': 'order_count', 'old_value': 2725, 'new_value': 2726}]
2025-05-21 12:02:02,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-21 12:02:02,902 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-21 12:02:02,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243628.8, 'new_value': 243678.8}, {'field': 'total_amount', 'old_value': 243628.8, 'new_value': 243678.8}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-05-21 12:02:02,903 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-21 12:02:03,418 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-21 12:02:03,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8711.0, 'new_value': 9719.0}, {'field': 'total_amount', 'old_value': 8711.0, 'new_value': 9719.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 73}]
2025-05-21 12:02:03,418 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-21 12:02:03,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-21 12:02:03,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11982.4, 'new_value': 12421.3}, {'field': 'offline_amount', 'old_value': 38108.9, 'new_value': 38736.9}, {'field': 'total_amount', 'old_value': 50091.3, 'new_value': 51158.2}, {'field': 'order_count', 'old_value': 140, 'new_value': 147}]
2025-05-21 12:02:03,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-21 12:02:04,303 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-21 12:02:04,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353404.36, 'new_value': 367260.88}, {'field': 'total_amount', 'old_value': 353404.36, 'new_value': 367260.88}, {'field': 'order_count', 'old_value': 1746, 'new_value': 1828}]
2025-05-21 12:02:04,304 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-21 12:02:04,705 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-21 12:02:04,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8659.0, 'new_value': 8913.0}, {'field': 'total_amount', 'old_value': 10665.0, 'new_value': 10919.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 104}]
2025-05-21 12:02:04,705 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-21 12:02:05,128 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-21 12:02:05,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23572.0, 'new_value': 24077.0}, {'field': 'total_amount', 'old_value': 23572.0, 'new_value': 24077.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-05-21 12:02:05,128 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-21 12:02:05,529 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-21 12:02:05,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127853.0, 'new_value': 131551.0}, {'field': 'total_amount', 'old_value': 127853.0, 'new_value': 131551.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-21 12:02:05,529 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-21 12:02:06,005 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-21 12:02:06,005 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77837.0, 'new_value': 79968.6}, {'field': 'offline_amount', 'old_value': 117702.2, 'new_value': 118909.2}, {'field': 'total_amount', 'old_value': 195539.2, 'new_value': 198877.8}, {'field': 'order_count', 'old_value': 3933, 'new_value': 4000}]
2025-05-21 12:02:06,005 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-21 12:02:06,469 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-21 12:02:06,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379735.75, 'new_value': 393357.02}, {'field': 'total_amount', 'old_value': 379735.75, 'new_value': 393357.02}, {'field': 'order_count', 'old_value': 5135, 'new_value': 5348}]
2025-05-21 12:02:06,469 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-21 12:02:06,904 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-21 12:02:06,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8005.3, 'new_value': 9210.3}, {'field': 'offline_amount', 'old_value': 22138.9, 'new_value': 25310.9}, {'field': 'total_amount', 'old_value': 30144.2, 'new_value': 34521.2}, {'field': 'order_count', 'old_value': 75, 'new_value': 85}]
2025-05-21 12:02:06,905 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-21 12:02:07,423 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-21 12:02:07,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 449507.34, 'new_value': 490506.94}, {'field': 'total_amount', 'old_value': 450745.83, 'new_value': 491745.43}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1144}]
2025-05-21 12:02:07,423 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-21 12:02:07,881 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-21 12:02:07,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133250.0, 'new_value': 139510.0}, {'field': 'total_amount', 'old_value': 133251.0, 'new_value': 139511.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-21 12:02:07,882 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-21 12:02:08,401 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-21 12:02:08,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70392.22, 'new_value': 73549.53}, {'field': 'total_amount', 'old_value': 70392.22, 'new_value': 73549.53}, {'field': 'order_count', 'old_value': 2178, 'new_value': 2267}]
2025-05-21 12:02:08,401 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-21 12:02:08,843 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-21 12:02:08,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7320.75, 'new_value': 7722.15}, {'field': 'offline_amount', 'old_value': 24393.45, 'new_value': 25092.29}, {'field': 'total_amount', 'old_value': 31714.2, 'new_value': 32814.44}, {'field': 'order_count', 'old_value': 1109, 'new_value': 1145}]
2025-05-21 12:02:08,843 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-21 12:02:09,252 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-21 12:02:09,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 935016.0, 'new_value': 962895.0}, {'field': 'total_amount', 'old_value': 935016.0, 'new_value': 962895.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 116}]
2025-05-21 12:02:09,252 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-21 12:02:09,736 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-21 12:02:09,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34668.67, 'new_value': 34787.1}, {'field': 'offline_amount', 'old_value': 41288.0, 'new_value': 46860.37}, {'field': 'total_amount', 'old_value': 75956.67, 'new_value': 81647.47}, {'field': 'order_count', 'old_value': 256, 'new_value': 276}]
2025-05-21 12:02:09,736 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-21 12:02:10,152 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-21 12:02:10,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120643.44, 'new_value': 123480.04}, {'field': 'total_amount', 'old_value': 120643.44, 'new_value': 123480.04}, {'field': 'order_count', 'old_value': 3069, 'new_value': 3159}]
2025-05-21 12:02:10,152 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-21 12:02:10,628 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-21 12:02:10,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59400.0, 'new_value': 72000.0}, {'field': 'total_amount', 'old_value': 59400.0, 'new_value': 72000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-21 12:02:10,628 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-21 12:02:11,083 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-21 12:02:11,084 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26444.96, 'new_value': 27853.69}, {'field': 'offline_amount', 'old_value': 255896.83, 'new_value': 264503.62}, {'field': 'total_amount', 'old_value': 282341.79, 'new_value': 292357.31}, {'field': 'order_count', 'old_value': 6674, 'new_value': 6790}]
2025-05-21 12:02:11,084 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-21 12:02:11,531 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-21 12:02:11,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 261095.89, 'new_value': 276698.03}, {'field': 'total_amount', 'old_value': 261095.89, 'new_value': 276698.03}, {'field': 'order_count', 'old_value': 2408, 'new_value': 2559}]
2025-05-21 12:02:11,531 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-21 12:02:12,027 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-21 12:02:12,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60770.0, 'new_value': 60840.0}, {'field': 'total_amount', 'old_value': 60770.0, 'new_value': 60840.0}, {'field': 'order_count', 'old_value': 294, 'new_value': 296}]
2025-05-21 12:02:12,027 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-21 12:02:12,432 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-21 12:02:12,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 251860.92, 'new_value': 269192.92}, {'field': 'offline_amount', 'old_value': 6459.5, 'new_value': 6665.5}, {'field': 'total_amount', 'old_value': 258320.42, 'new_value': 275858.42}, {'field': 'order_count', 'old_value': 2199, 'new_value': 2373}]
2025-05-21 12:02:12,432 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-21 12:02:12,924 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-21 12:02:12,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16527.0, 'new_value': 16726.0}, {'field': 'total_amount', 'old_value': 16527.0, 'new_value': 16726.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-05-21 12:02:12,924 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-21 12:02:13,432 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-21 12:02:13,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347466.3, 'new_value': 358305.9}, {'field': 'total_amount', 'old_value': 347466.3, 'new_value': 358305.9}, {'field': 'order_count', 'old_value': 1734, 'new_value': 1788}]
2025-05-21 12:02:13,433 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-21 12:02:13,853 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-21 12:02:13,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20531.4, 'new_value': 22096.4}, {'field': 'total_amount', 'old_value': 20531.4, 'new_value': 22096.4}, {'field': 'order_count', 'old_value': 571, 'new_value': 609}]
2025-05-21 12:02:13,853 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-21 12:02:14,317 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-21 12:02:14,317 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107941.82, 'new_value': 113971.3}, {'field': 'offline_amount', 'old_value': 43826.58, 'new_value': 45448.2}, {'field': 'total_amount', 'old_value': 151768.4, 'new_value': 159419.5}, {'field': 'order_count', 'old_value': 9328, 'new_value': 9763}]
2025-05-21 12:02:14,317 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-21 12:02:14,798 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-21 12:02:14,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11024.6, 'new_value': 11099.4}, {'field': 'total_amount', 'old_value': 11453.6, 'new_value': 11528.4}, {'field': 'order_count', 'old_value': 148, 'new_value': 150}]
2025-05-21 12:02:14,798 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-21 12:02:15,242 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-21 12:02:15,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6176.1, 'new_value': 6219.1}, {'field': 'offline_amount', 'old_value': 30612.1, 'new_value': 31865.95}, {'field': 'total_amount', 'old_value': 36788.2, 'new_value': 38085.05}, {'field': 'order_count', 'old_value': 421, 'new_value': 441}]
2025-05-21 12:02:15,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-21 12:02:15,676 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-21 12:02:15,676 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26269.0, 'new_value': 27519.0}, {'field': 'total_amount', 'old_value': 31829.0, 'new_value': 33079.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 262}]
2025-05-21 12:02:15,676 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-21 12:02:16,134 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-21 12:02:16,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13301.0, 'new_value': 13589.0}, {'field': 'total_amount', 'old_value': 27559.0, 'new_value': 27847.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-05-21 12:02:16,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-21 12:02:16,634 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-21 12:02:16,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164789.0, 'new_value': 168281.0}, {'field': 'total_amount', 'old_value': 164789.0, 'new_value': 168281.0}, {'field': 'order_count', 'old_value': 193, 'new_value': 196}]
2025-05-21 12:02:16,635 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-21 12:02:17,105 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-21 12:02:17,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 576783.44, 'new_value': 594099.93}, {'field': 'total_amount', 'old_value': 576783.44, 'new_value': 594099.93}, {'field': 'order_count', 'old_value': 11001, 'new_value': 11286}]
2025-05-21 12:02:17,105 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-21 12:02:17,585 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-21 12:02:17,585 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66656.15, 'new_value': 70521.97}, {'field': 'offline_amount', 'old_value': 182200.55, 'new_value': 189720.12}, {'field': 'total_amount', 'old_value': 248856.7, 'new_value': 260242.09}, {'field': 'order_count', 'old_value': 11767, 'new_value': 12436}]
2025-05-21 12:02:17,585 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-21 12:02:18,016 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-21 12:02:18,017 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 12050.97}, {'field': 'total_amount', 'old_value': 219476.38, 'new_value': 231527.35}, {'field': 'order_count', 'old_value': 9268, 'new_value': 9769}]
2025-05-21 12:02:18,017 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-21 12:02:18,519 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-21 12:02:18,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23864.98, 'new_value': 25211.72}, {'field': 'total_amount', 'old_value': 23864.98, 'new_value': 25211.72}, {'field': 'order_count', 'old_value': 878, 'new_value': 934}]
2025-05-21 12:02:18,519 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-21 12:02:18,991 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-21 12:02:18,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 570476.73, 'new_value': 596138.61}, {'field': 'total_amount', 'old_value': 570476.73, 'new_value': 596138.61}, {'field': 'order_count', 'old_value': 4213, 'new_value': 4427}]
2025-05-21 12:02:18,991 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-21 12:02:19,391 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-21 12:02:19,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169607.0, 'new_value': 175317.0}, {'field': 'total_amount', 'old_value': 169607.0, 'new_value': 175317.0}, {'field': 'order_count', 'old_value': 518, 'new_value': 535}]
2025-05-21 12:02:19,392 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-21 12:02:19,889 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-21 12:02:19,889 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46667.37, 'new_value': 49538.65}, {'field': 'offline_amount', 'old_value': 354827.66, 'new_value': 371837.65}, {'field': 'total_amount', 'old_value': 401495.03, 'new_value': 421376.3}, {'field': 'order_count', 'old_value': 1883, 'new_value': 1992}]
2025-05-21 12:02:19,889 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-21 12:02:20,255 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-21 12:02:20,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1395.7, 'new_value': 1466.59}, {'field': 'offline_amount', 'old_value': 19259.57, 'new_value': 19725.11}, {'field': 'total_amount', 'old_value': 20655.27, 'new_value': 21191.7}, {'field': 'order_count', 'old_value': 731, 'new_value': 753}]
2025-05-21 12:02:20,256 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-21 12:02:20,721 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-21 12:02:20,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 677305.0, 'new_value': 703141.0}, {'field': 'total_amount', 'old_value': 677305.0, 'new_value': 703141.0}, {'field': 'order_count', 'old_value': 3030, 'new_value': 3161}]
2025-05-21 12:02:20,721 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-21 12:02:21,179 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-21 12:02:21,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26918.0, 'new_value': 28068.0}, {'field': 'total_amount', 'old_value': 26918.0, 'new_value': 28068.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-21 12:02:21,179 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-21 12:02:21,689 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-21 12:02:21,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4987.48, 'new_value': 5272.82}, {'field': 'offline_amount', 'old_value': 289013.44, 'new_value': 300164.54}, {'field': 'total_amount', 'old_value': 294000.92, 'new_value': 305437.36}, {'field': 'order_count', 'old_value': 14281, 'new_value': 14794}]
2025-05-21 12:02:21,690 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-21 12:02:22,161 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-21 12:02:22,161 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48290.13, 'new_value': 51019.6}, {'field': 'offline_amount', 'old_value': 70872.66, 'new_value': 73338.88}, {'field': 'total_amount', 'old_value': 119162.79, 'new_value': 124358.48}, {'field': 'order_count', 'old_value': 5493, 'new_value': 5744}]
2025-05-21 12:02:22,161 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-21 12:02:22,550 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-21 12:02:22,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291021.93, 'new_value': 301074.62}, {'field': 'total_amount', 'old_value': 313185.05, 'new_value': 323237.74}, {'field': 'order_count', 'old_value': 13172, 'new_value': 13651}]
2025-05-21 12:02:22,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-21 12:02:22,984 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-21 12:02:22,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23123.75, 'new_value': 24163.82}, {'field': 'offline_amount', 'old_value': 188157.54, 'new_value': 193606.54}, {'field': 'total_amount', 'old_value': 211281.29, 'new_value': 217770.36}, {'field': 'order_count', 'old_value': 6608, 'new_value': 6829}]
2025-05-21 12:02:22,984 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-21 12:02:23,404 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-21 12:02:23,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312411.43, 'new_value': 316611.43}, {'field': 'total_amount', 'old_value': 312411.43, 'new_value': 316611.43}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2221}]
2025-05-21 12:02:23,404 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-21 12:02:23,909 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-21 12:02:23,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77129.0, 'new_value': 79974.0}, {'field': 'total_amount', 'old_value': 92333.0, 'new_value': 95178.0}, {'field': 'order_count', 'old_value': 2088, 'new_value': 2162}]
2025-05-21 12:02:23,910 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-21 12:02:24,358 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-21 12:02:24,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78556.0, 'new_value': 81650.0}, {'field': 'total_amount', 'old_value': 78556.0, 'new_value': 81650.0}, {'field': 'order_count', 'old_value': 534, 'new_value': 555}]
2025-05-21 12:02:24,358 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-21 12:02:24,810 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-21 12:02:24,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117190.0, 'new_value': 118459.0}, {'field': 'total_amount', 'old_value': 117190.0, 'new_value': 118459.0}, {'field': 'order_count', 'old_value': 3744, 'new_value': 3783}]
2025-05-21 12:02:24,811 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-21 12:02:25,374 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-21 12:02:25,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7416.0, 'new_value': 10416.0}, {'field': 'total_amount', 'old_value': 7416.0, 'new_value': 10416.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-21 12:02:25,374 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-21 12:02:25,782 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-21 12:02:25,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93712.32, 'new_value': 100387.16}, {'field': 'offline_amount', 'old_value': 294613.62, 'new_value': 300275.44}, {'field': 'total_amount', 'old_value': 388325.94, 'new_value': 400662.6}, {'field': 'order_count', 'old_value': 3260, 'new_value': 3429}]
2025-05-21 12:02:25,782 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-21 12:02:26,224 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-21 12:02:26,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 540700.0, 'new_value': 576700.0}, {'field': 'total_amount', 'old_value': 540700.0, 'new_value': 576700.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-05-21 12:02:26,225 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-21 12:02:26,706 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-21 12:02:26,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71600.81, 'new_value': 76100.07}, {'field': 'offline_amount', 'old_value': 84727.01, 'new_value': 89888.01}, {'field': 'total_amount', 'old_value': 156327.82, 'new_value': 165988.08}, {'field': 'order_count', 'old_value': 6307, 'new_value': 6698}]
2025-05-21 12:02:26,707 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-21 12:02:27,190 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-21 12:02:27,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 197810.86, 'new_value': 202931.72}, {'field': 'offline_amount', 'old_value': 85202.78, 'new_value': 85402.78}, {'field': 'total_amount', 'old_value': 283013.64, 'new_value': 288334.5}, {'field': 'order_count', 'old_value': 503, 'new_value': 514}]
2025-05-21 12:02:27,191 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-21 12:02:27,701 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-21 12:02:27,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189623.26, 'new_value': 195813.35}, {'field': 'total_amount', 'old_value': 208796.69, 'new_value': 214986.78}, {'field': 'order_count', 'old_value': 4320, 'new_value': 4421}]
2025-05-21 12:02:27,701 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-21 12:02:28,091 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-21 12:02:28,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46209.0, 'new_value': 47471.0}, {'field': 'total_amount', 'old_value': 46209.0, 'new_value': 47471.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 102}]
2025-05-21 12:02:28,091 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-21 12:02:28,558 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-21 12:02:28,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287570.6, 'new_value': 293196.0}, {'field': 'total_amount', 'old_value': 287570.6, 'new_value': 293196.0}, {'field': 'order_count', 'old_value': 352, 'new_value': 362}]
2025-05-21 12:02:28,559 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-21 12:02:29,024 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-21 12:02:29,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 536670.4, 'new_value': 567104.72}, {'field': 'total_amount', 'old_value': 536670.4, 'new_value': 567104.72}, {'field': 'order_count', 'old_value': 4101, 'new_value': 4326}]
2025-05-21 12:02:29,024 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-21 12:02:29,472 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-21 12:02:29,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62544.77, 'new_value': 65298.32}, {'field': 'total_amount', 'old_value': 62544.77, 'new_value': 65298.32}, {'field': 'order_count', 'old_value': 3547, 'new_value': 3696}]
2025-05-21 12:02:29,473 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-21 12:02:29,962 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-21 12:02:29,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28011.0, 'new_value': 28210.0}, {'field': 'total_amount', 'old_value': 28011.0, 'new_value': 28210.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-21 12:02:29,962 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-21 12:02:30,384 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-21 12:02:30,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45166.45, 'new_value': 47265.45}, {'field': 'offline_amount', 'old_value': 36556.33, 'new_value': 37550.33}, {'field': 'total_amount', 'old_value': 81722.78, 'new_value': 84815.78}, {'field': 'order_count', 'old_value': 1613, 'new_value': 1671}]
2025-05-21 12:02:30,384 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-21 12:02:30,851 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-21 12:02:30,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11458.45, 'new_value': 11864.45}, {'field': 'offline_amount', 'old_value': 211774.0, 'new_value': 225425.0}, {'field': 'total_amount', 'old_value': 223232.45, 'new_value': 237289.45}, {'field': 'order_count', 'old_value': 1187, 'new_value': 1258}]
2025-05-21 12:02:30,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-21 12:02:31,333 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-21 12:02:31,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144400.0, 'new_value': 163600.0}, {'field': 'total_amount', 'old_value': 144400.0, 'new_value': 163600.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-05-21 12:02:31,333 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-21 12:02:31,811 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-21 12:02:31,811 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-21 12:02:31,811 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-21 12:02:32,238 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-21 12:02:32,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27662.7, 'new_value': 28818.7}, {'field': 'total_amount', 'old_value': 27662.7, 'new_value': 28818.7}, {'field': 'order_count', 'old_value': 158, 'new_value': 166}]
2025-05-21 12:02:32,238 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-21 12:02:32,672 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-21 12:02:32,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78266.83, 'new_value': 78774.83}, {'field': 'total_amount', 'old_value': 78266.83, 'new_value': 78774.83}, {'field': 'order_count', 'old_value': 101, 'new_value': 102}]
2025-05-21 12:02:32,672 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-21 12:02:33,144 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-21 12:02:33,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251376.0, 'new_value': 264836.0}, {'field': 'total_amount', 'old_value': 265151.0, 'new_value': 278611.0}, {'field': 'order_count', 'old_value': 5558, 'new_value': 5918}]
2025-05-21 12:02:33,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-21 12:02:33,605 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-21 12:02:33,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68125.37, 'new_value': 69911.04}, {'field': 'offline_amount', 'old_value': 152966.49, 'new_value': 157611.75}, {'field': 'total_amount', 'old_value': 221091.86, 'new_value': 227522.79}, {'field': 'order_count', 'old_value': 4058, 'new_value': 4216}]
2025-05-21 12:02:33,605 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-21 12:02:34,117 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-21 12:02:34,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228775.0, 'new_value': 263914.0}, {'field': 'total_amount', 'old_value': 232875.0, 'new_value': 268014.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 179}]
2025-05-21 12:02:34,117 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-21 12:02:34,627 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-21 12:02:34,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 574895.01, 'new_value': 592622.9}, {'field': 'total_amount', 'old_value': 574895.01, 'new_value': 592622.9}, {'field': 'order_count', 'old_value': 6678, 'new_value': 6911}]
2025-05-21 12:02:34,627 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-21 12:02:35,056 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-21 12:02:35,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395129.6, 'new_value': 404925.2}, {'field': 'total_amount', 'old_value': 569322.2, 'new_value': 579117.8}, {'field': 'order_count', 'old_value': 3847, 'new_value': 3856}]
2025-05-21 12:02:35,056 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-21 12:02:35,447 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-21 12:02:35,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153738.0, 'new_value': 155654.0}, {'field': 'total_amount', 'old_value': 153738.0, 'new_value': 155654.0}, {'field': 'order_count', 'old_value': 2567, 'new_value': 2601}]
2025-05-21 12:02:35,448 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-21 12:02:35,904 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-21 12:02:35,904 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147727.34, 'new_value': 152087.49}, {'field': 'total_amount', 'old_value': 147727.34, 'new_value': 152087.49}, {'field': 'order_count', 'old_value': 6214, 'new_value': 6406}]
2025-05-21 12:02:35,904 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-21 12:02:36,345 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-21 12:02:36,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 429151.0, 'new_value': 459045.0}, {'field': 'total_amount', 'old_value': 429151.0, 'new_value': 459045.0}, {'field': 'order_count', 'old_value': 377, 'new_value': 404}]
2025-05-21 12:02:36,346 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-21 12:02:36,810 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-21 12:02:36,810 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203830.32, 'new_value': 218999.92}, {'field': 'offline_amount', 'old_value': 121705.59, 'new_value': 124609.49}, {'field': 'total_amount', 'old_value': 325535.91, 'new_value': 343609.41}, {'field': 'order_count', 'old_value': 2842, 'new_value': 2901}]
2025-05-21 12:02:36,811 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-21 12:02:37,300 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-21 12:02:37,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183858.18, 'new_value': 192651.5}, {'field': 'total_amount', 'old_value': 183858.18, 'new_value': 192651.5}, {'field': 'order_count', 'old_value': 1399, 'new_value': 1473}]
2025-05-21 12:02:37,300 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-21 12:02:37,759 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-21 12:02:37,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72289.6, 'new_value': 73432.5}, {'field': 'total_amount', 'old_value': 74018.4, 'new_value': 75161.3}, {'field': 'order_count', 'old_value': 456, 'new_value': 464}]
2025-05-21 12:02:37,759 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-21 12:02:38,215 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-21 12:02:38,215 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41501.6, 'new_value': 51501.6}, {'field': 'offline_amount', 'old_value': 83495.7, 'new_value': 83538.68}, {'field': 'total_amount', 'old_value': 124997.3, 'new_value': 135040.28}, {'field': 'order_count', 'old_value': 3558, 'new_value': 3753}]
2025-05-21 12:02:38,215 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-21 12:02:38,715 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-21 12:02:38,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43112.0, 'new_value': 44480.0}, {'field': 'total_amount', 'old_value': 43112.0, 'new_value': 44480.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 102}]
2025-05-21 12:02:38,716 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-21 12:02:39,152 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-21 12:02:39,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155376.9, 'new_value': 168073.11}, {'field': 'offline_amount', 'old_value': 20993.6, 'new_value': 21449.2}, {'field': 'total_amount', 'old_value': 176370.5, 'new_value': 189522.31}, {'field': 'order_count', 'old_value': 8566, 'new_value': 8917}]
2025-05-21 12:02:39,153 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-21 12:02:39,594 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-21 12:02:39,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70335.0, 'new_value': 75068.0}, {'field': 'offline_amount', 'old_value': 833262.0, 'new_value': 872629.0}, {'field': 'total_amount', 'old_value': 903597.0, 'new_value': 947697.0}, {'field': 'order_count', 'old_value': 22123, 'new_value': 23564}]
2025-05-21 12:02:39,595 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-21 12:02:40,043 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-21 12:02:40,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305348.08, 'new_value': 320632.61}, {'field': 'total_amount', 'old_value': 318858.56, 'new_value': 334143.09}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1090}]
2025-05-21 12:02:40,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-21 12:02:40,552 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-21 12:02:40,553 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28909.0, 'new_value': 29908.0}, {'field': 'offline_amount', 'old_value': 185893.0, 'new_value': 220754.0}, {'field': 'total_amount', 'old_value': 214802.0, 'new_value': 250662.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 229}]
2025-05-21 12:02:40,553 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-21 12:02:41,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-21 12:02:41,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29684.0, 'new_value': 29963.0}, {'field': 'total_amount', 'old_value': 29684.0, 'new_value': 29963.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-21 12:02:41,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-21 12:02:41,459 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-21 12:02:41,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13500.0, 'new_value': 18700.0}, {'field': 'total_amount', 'old_value': 13500.0, 'new_value': 18700.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-21 12:02:41,459 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-21 12:02:41,886 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-21 12:02:41,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28479.16, 'new_value': 30046.96}, {'field': 'offline_amount', 'old_value': 39033.54, 'new_value': 40033.54}, {'field': 'total_amount', 'old_value': 67512.7, 'new_value': 70080.5}, {'field': 'order_count', 'old_value': 3234, 'new_value': 3380}]
2025-05-21 12:02:41,886 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-21 12:02:42,380 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-21 12:02:42,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17408.95, 'new_value': 18261.45}, {'field': 'offline_amount', 'old_value': 12622.16, 'new_value': 13315.16}, {'field': 'total_amount', 'old_value': 30031.11, 'new_value': 31576.61}, {'field': 'order_count', 'old_value': 1297, 'new_value': 1360}]
2025-05-21 12:02:42,380 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-21 12:02:42,861 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-21 12:02:42,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237102.0, 'new_value': 246772.0}, {'field': 'total_amount', 'old_value': 237102.0, 'new_value': 246772.0}, {'field': 'order_count', 'old_value': 358, 'new_value': 375}]
2025-05-21 12:02:42,861 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-21 12:02:43,303 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-21 12:02:43,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26878.0, 'new_value': 27133.0}, {'field': 'total_amount', 'old_value': 26878.0, 'new_value': 27133.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 151}]
2025-05-21 12:02:43,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-21 12:02:43,888 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-21 12:02:43,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36110.6, 'new_value': 36610.6}, {'field': 'total_amount', 'old_value': 50906.8, 'new_value': 51406.8}, {'field': 'order_count', 'old_value': 532, 'new_value': 533}]
2025-05-21 12:02:43,888 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-21 12:02:44,307 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-21 12:02:44,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86553.09, 'new_value': 91853.9}, {'field': 'offline_amount', 'old_value': 167112.15, 'new_value': 172871.66}, {'field': 'total_amount', 'old_value': 253665.24, 'new_value': 264725.56}, {'field': 'order_count', 'old_value': 7609, 'new_value': 8000}]
2025-05-21 12:02:44,307 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-21 12:02:44,721 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-21 12:02:44,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80944.0, 'new_value': 85704.0}, {'field': 'total_amount', 'old_value': 80944.0, 'new_value': 85704.0}, {'field': 'order_count', 'old_value': 348, 'new_value': 368}]
2025-05-21 12:02:44,721 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-21 12:02:45,137 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-21 12:02:45,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133462.8, 'new_value': 142320.66}, {'field': 'offline_amount', 'old_value': 38348.77, 'new_value': 39224.79}, {'field': 'total_amount', 'old_value': 171811.57, 'new_value': 181545.45}, {'field': 'order_count', 'old_value': 9687, 'new_value': 10230}]
2025-05-21 12:02:45,137 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-21 12:02:45,557 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-21 12:02:45,557 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90227.8, 'new_value': 97106.5}, {'field': 'offline_amount', 'old_value': 26452.4, 'new_value': 27327.4}, {'field': 'total_amount', 'old_value': 116680.2, 'new_value': 124433.9}, {'field': 'order_count', 'old_value': 9487, 'new_value': 10108}]
2025-05-21 12:02:45,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-21 12:02:45,967 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-21 12:02:45,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211662.69, 'new_value': 224802.39}, {'field': 'total_amount', 'old_value': 234150.09, 'new_value': 247289.79}, {'field': 'order_count', 'old_value': 1267, 'new_value': 1343}]
2025-05-21 12:02:45,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-21 12:02:46,419 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-21 12:02:46,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135970.25, 'new_value': 142928.24}, {'field': 'total_amount', 'old_value': 135970.25, 'new_value': 142928.24}, {'field': 'order_count', 'old_value': 6973, 'new_value': 7318}]
2025-05-21 12:02:46,420 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-21 12:02:46,859 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-21 12:02:46,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126382.1, 'new_value': 134537.8}, {'field': 'total_amount', 'old_value': 126382.1, 'new_value': 134537.8}, {'field': 'order_count', 'old_value': 561, 'new_value': 597}]
2025-05-21 12:02:46,859 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-21 12:02:47,339 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-21 12:02:47,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110151.7, 'new_value': 117263.4}, {'field': 'total_amount', 'old_value': 110151.7, 'new_value': 117263.4}, {'field': 'order_count', 'old_value': 3041, 'new_value': 3231}]
2025-05-21 12:02:47,340 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-21 12:02:47,778 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-21 12:02:47,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8607.0, 'new_value': 9010.0}, {'field': 'total_amount', 'old_value': 17040.0, 'new_value': 17443.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 94}]
2025-05-21 12:02:47,778 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-21 12:02:48,260 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-21 12:02:48,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122898.13, 'new_value': 130396.83}, {'field': 'offline_amount', 'old_value': 228896.69, 'new_value': 241239.85}, {'field': 'total_amount', 'old_value': 351794.82, 'new_value': 371636.68}, {'field': 'order_count', 'old_value': 2876, 'new_value': 3075}]
2025-05-21 12:02:48,261 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-21 12:02:48,690 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-21 12:02:48,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82195.1, 'new_value': 86047.9}, {'field': 'total_amount', 'old_value': 82195.1, 'new_value': 86047.9}, {'field': 'order_count', 'old_value': 395, 'new_value': 415}]
2025-05-21 12:02:48,690 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-21 12:02:49,173 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-21 12:02:49,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32181.56, 'new_value': 34753.32}, {'field': 'offline_amount', 'old_value': 20818.97, 'new_value': 21146.25}, {'field': 'total_amount', 'old_value': 53000.53, 'new_value': 55899.57}, {'field': 'order_count', 'old_value': 2306, 'new_value': 2421}]
2025-05-21 12:02:49,174 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-21 12:02:49,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-21 12:02:49,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11975.49, 'new_value': 12819.56}, {'field': 'offline_amount', 'old_value': 28066.4, 'new_value': 28907.2}, {'field': 'total_amount', 'old_value': 40041.89, 'new_value': 41726.76}, {'field': 'order_count', 'old_value': 1588, 'new_value': 1649}]
2025-05-21 12:02:49,602 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-21 12:02:50,042 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-21 12:02:50,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26500.0, 'new_value': 30500.0}, {'field': 'total_amount', 'old_value': 26500.0, 'new_value': 30500.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-21 12:02:50,042 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-21 12:02:50,515 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-21 12:02:50,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284456.0, 'new_value': 288994.9}, {'field': 'total_amount', 'old_value': 284456.0, 'new_value': 288994.9}]
2025-05-21 12:02:50,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-21 12:02:51,047 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-21 12:02:51,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71962.0, 'new_value': 74202.0}, {'field': 'total_amount', 'old_value': 71962.0, 'new_value': 74202.0}, {'field': 'order_count', 'old_value': 1689, 'new_value': 1690}]
2025-05-21 12:02:51,048 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-21 12:02:51,454 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-21 12:02:51,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69687.02, 'new_value': 73067.55}, {'field': 'total_amount', 'old_value': 70947.45, 'new_value': 74327.98}, {'field': 'order_count', 'old_value': 321, 'new_value': 340}]
2025-05-21 12:02:51,454 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-21 12:02:51,943 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-21 12:02:51,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105600.0, 'new_value': 108600.0}, {'field': 'total_amount', 'old_value': 105600.0, 'new_value': 108600.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-21 12:02:51,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-21 12:02:52,374 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-21 12:02:52,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 615450.01, 'new_value': 647361.47}, {'field': 'total_amount', 'old_value': 615450.01, 'new_value': 647361.47}, {'field': 'order_count', 'old_value': 4817, 'new_value': 5113}]
2025-05-21 12:02:52,374 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-21 12:02:52,840 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-21 12:02:52,841 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19503.4, 'new_value': 20284.3}, {'field': 'offline_amount', 'old_value': 141085.0, 'new_value': 152960.1}, {'field': 'total_amount', 'old_value': 160588.4, 'new_value': 173244.4}, {'field': 'order_count', 'old_value': 5069, 'new_value': 5345}]
2025-05-21 12:02:52,841 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-21 12:02:53,331 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-21 12:02:53,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 443082.0, 'new_value': 454483.0}, {'field': 'total_amount', 'old_value': 443082.0, 'new_value': 454483.0}, {'field': 'order_count', 'old_value': 2845, 'new_value': 2976}]
2025-05-21 12:02:53,331 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-21 12:02:53,761 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-21 12:02:53,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3720.0, 'new_value': 4255.0}, {'field': 'total_amount', 'old_value': 10148.0, 'new_value': 10683.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 102}]
2025-05-21 12:02:53,762 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-21 12:02:54,231 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-21 12:02:54,231 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41802.96, 'new_value': 43756.5}, {'field': 'offline_amount', 'old_value': 38998.02, 'new_value': 41250.1}, {'field': 'total_amount', 'old_value': 80800.98, 'new_value': 85006.6}, {'field': 'order_count', 'old_value': 4128, 'new_value': 4337}]
2025-05-21 12:02:54,231 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-21 12:02:54,688 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-21 12:02:54,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19847.24, 'new_value': 20530.86}, {'field': 'offline_amount', 'old_value': 22343.38, 'new_value': 23027.08}, {'field': 'total_amount', 'old_value': 42190.62, 'new_value': 43557.94}, {'field': 'order_count', 'old_value': 2016, 'new_value': 2089}]
2025-05-21 12:02:54,688 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-21 12:02:55,177 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-21 12:02:55,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2768.0, 'new_value': 2875.0}, {'field': 'offline_amount', 'old_value': 24468.2, 'new_value': 24766.2}, {'field': 'total_amount', 'old_value': 27236.2, 'new_value': 27641.2}, {'field': 'order_count', 'old_value': 992, 'new_value': 1009}]
2025-05-21 12:02:55,177 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-21 12:02:55,637 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-21 12:02:55,637 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77924.76, 'new_value': 83453.34}, {'field': 'offline_amount', 'old_value': 87348.34, 'new_value': 92555.62}, {'field': 'total_amount', 'old_value': 165273.1, 'new_value': 176008.96}, {'field': 'order_count', 'old_value': 4172, 'new_value': 4436}]
2025-05-21 12:02:55,637 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-21 12:02:56,096 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-21 12:02:56,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 747192.0, 'new_value': 794693.0}, {'field': 'total_amount', 'old_value': 747192.0, 'new_value': 794693.0}, {'field': 'order_count', 'old_value': 869, 'new_value': 925}]
2025-05-21 12:02:56,096 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-21 12:02:56,585 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-21 12:02:56,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157433.5, 'new_value': 165899.5}, {'field': 'total_amount', 'old_value': 163383.8, 'new_value': 171849.8}, {'field': 'order_count', 'old_value': 305, 'new_value': 319}]
2025-05-21 12:02:56,585 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-21 12:02:57,086 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-21 12:02:57,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34309.35, 'new_value': 35522.05}, {'field': 'offline_amount', 'old_value': 86790.0, 'new_value': 89949.0}, {'field': 'total_amount', 'old_value': 121099.35, 'new_value': 125471.05}, {'field': 'order_count', 'old_value': 1386, 'new_value': 1438}]
2025-05-21 12:02:57,086 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-21 12:02:57,556 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-21 12:02:57,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106362.0, 'new_value': 110806.0}, {'field': 'offline_amount', 'old_value': 76334.0, 'new_value': 79030.0}, {'field': 'total_amount', 'old_value': 182696.0, 'new_value': 189836.0}, {'field': 'order_count', 'old_value': 2318, 'new_value': 2435}]
2025-05-21 12:02:57,556 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-21 12:02:58,022 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-21 12:02:58,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7032.4, 'new_value': 7487.4}, {'field': 'offline_amount', 'old_value': 17226.23, 'new_value': 18603.21}, {'field': 'total_amount', 'old_value': 24258.63, 'new_value': 26090.61}, {'field': 'order_count', 'old_value': 251, 'new_value': 273}]
2025-05-21 12:02:58,023 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-21 12:02:58,481 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-21 12:02:58,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111010.0, 'new_value': 131010.0}, {'field': 'total_amount', 'old_value': 119688.46, 'new_value': 139688.46}, {'field': 'order_count', 'old_value': 60, 'new_value': 61}]
2025-05-21 12:02:58,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-21 12:02:58,932 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-21 12:02:58,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26164.62, 'new_value': 27600.62}, {'field': 'offline_amount', 'old_value': 24171.0, 'new_value': 26078.1}, {'field': 'total_amount', 'old_value': 50335.62, 'new_value': 53678.72}, {'field': 'order_count', 'old_value': 218, 'new_value': 236}]
2025-05-21 12:02:58,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-21 12:02:59,421 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-21 12:02:59,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169907.5, 'new_value': 180172.5}, {'field': 'total_amount', 'old_value': 169907.5, 'new_value': 180172.5}, {'field': 'order_count', 'old_value': 838, 'new_value': 888}]
2025-05-21 12:02:59,422 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-21 12:02:59,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-21 12:02:59,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4253.0, 'new_value': 4618.0}, {'field': 'offline_amount', 'old_value': 16946.0, 'new_value': 17404.0}, {'field': 'total_amount', 'old_value': 21199.0, 'new_value': 22022.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 175}]
2025-05-21 12:02:59,930 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-21 12:03:00,430 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-21 12:03:00,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39762.12, 'new_value': 42759.14}, {'field': 'total_amount', 'old_value': 43926.12, 'new_value': 46923.14}, {'field': 'order_count', 'old_value': 384, 'new_value': 414}]
2025-05-21 12:03:00,430 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-21 12:03:00,930 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-21 12:03:00,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144337.1, 'new_value': 157311.6}, {'field': 'total_amount', 'old_value': 144337.1, 'new_value': 157311.6}, {'field': 'order_count', 'old_value': 527, 'new_value': 582}]
2025-05-21 12:03:00,930 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-21 12:03:01,401 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-21 12:03:01,402 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55448.0, 'new_value': 56866.0}, {'field': 'offline_amount', 'old_value': 233289.0, 'new_value': 248289.0}, {'field': 'total_amount', 'old_value': 288737.0, 'new_value': 305155.0}, {'field': 'order_count', 'old_value': 1158, 'new_value': 1216}]
2025-05-21 12:03:01,402 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-21 12:03:01,913 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-21 12:03:01,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1238187.0, 'new_value': 865147.0}, {'field': 'total_amount', 'old_value': 1238187.0, 'new_value': 865147.0}, {'field': 'order_count', 'old_value': 4858, 'new_value': 3779}]
2025-05-21 12:03:01,913 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-21 12:03:02,410 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-21 12:03:02,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10542933.0, 'new_value': 10942122.0}, {'field': 'total_amount', 'old_value': 10542933.0, 'new_value': 10942122.0}, {'field': 'order_count', 'old_value': 32375, 'new_value': 33569}]
2025-05-21 12:03:02,410 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-21 12:03:02,852 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-21 12:03:02,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2881632.39, 'new_value': 3030320.39}, {'field': 'total_amount', 'old_value': 2881632.39, 'new_value': 3030320.39}, {'field': 'order_count', 'old_value': 5005, 'new_value': 5240}]
2025-05-21 12:03:02,852 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-21 12:03:03,256 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-21 12:03:03,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120271.94, 'new_value': 127983.49}, {'field': 'total_amount', 'old_value': 127711.58, 'new_value': 135423.13}, {'field': 'order_count', 'old_value': 8887, 'new_value': 9416}]
2025-05-21 12:03:03,256 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-21 12:03:03,747 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-21 12:03:03,748 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248394.0, 'new_value': 267001.0}, {'field': 'total_amount', 'old_value': 248394.0, 'new_value': 267001.0}, {'field': 'order_count', 'old_value': 5159, 'new_value': 5572}]
2025-05-21 12:03:03,748 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-21 12:03:04,160 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-21 12:03:04,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173291.0, 'new_value': 184836.0}, {'field': 'total_amount', 'old_value': 173291.0, 'new_value': 184836.0}, {'field': 'order_count', 'old_value': 386, 'new_value': 406}]
2025-05-21 12:03:04,160 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-21 12:03:04,622 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-21 12:03:04,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54164.44, 'new_value': 58395.44}, {'field': 'total_amount', 'old_value': 91037.34, 'new_value': 95268.34}, {'field': 'order_count', 'old_value': 1975, 'new_value': 1976}]
2025-05-21 12:03:04,623 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-21 12:03:05,112 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-21 12:03:05,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100868.7, 'new_value': 125771.7}, {'field': 'total_amount', 'old_value': 244523.48, 'new_value': 269426.48}]
2025-05-21 12:03:05,113 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-21 12:03:05,496 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-21 12:03:05,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265536.4, 'new_value': 274578.0}, {'field': 'total_amount', 'old_value': 265536.4, 'new_value': 274578.0}, {'field': 'order_count', 'old_value': 5769, 'new_value': 5979}]
2025-05-21 12:03:05,496 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-21 12:03:05,929 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-21 12:03:05,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42016.0, 'new_value': 43547.5}, {'field': 'total_amount', 'old_value': 42016.0, 'new_value': 43547.5}, {'field': 'order_count', 'old_value': 217, 'new_value': 227}]
2025-05-21 12:03:05,929 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-21 12:03:06,378 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-21 12:03:06,378 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159312.0, 'new_value': 162821.0}, {'field': 'offline_amount', 'old_value': 130015.0, 'new_value': 141615.0}, {'field': 'total_amount', 'old_value': 289327.0, 'new_value': 304436.0}, {'field': 'order_count', 'old_value': 788, 'new_value': 831}]
2025-05-21 12:03:06,378 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-21 12:03:06,870 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-21 12:03:06,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 532138.13, 'new_value': 589321.51}, {'field': 'total_amount', 'old_value': 532138.13, 'new_value': 589321.51}, {'field': 'order_count', 'old_value': 3307, 'new_value': 3430}]
2025-05-21 12:03:06,870 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-21 12:03:07,275 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-21 12:03:07,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 372503.0, 'new_value': 385678.0}, {'field': 'total_amount', 'old_value': 372503.0, 'new_value': 385678.0}, {'field': 'order_count', 'old_value': 8432, 'new_value': 8729}]
2025-05-21 12:03:07,276 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-21 12:03:07,700 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-21 12:03:07,701 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107911.0, 'new_value': 115021.0}, {'field': 'total_amount', 'old_value': 107911.0, 'new_value': 115021.0}, {'field': 'order_count', 'old_value': 7954, 'new_value': 8476}]
2025-05-21 12:03:07,701 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-21 12:03:08,199 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-21 12:03:08,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65121.2, 'new_value': 66261.2}, {'field': 'total_amount', 'old_value': 65374.2, 'new_value': 66514.2}, {'field': 'order_count', 'old_value': 966, 'new_value': 981}]
2025-05-21 12:03:08,200 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-21 12:03:08,654 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-21 12:03:08,654 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3377.6, 'new_value': 3734.6}, {'field': 'offline_amount', 'old_value': 18151.0, 'new_value': 18676.0}, {'field': 'total_amount', 'old_value': 21528.6, 'new_value': 22410.6}, {'field': 'order_count', 'old_value': 510, 'new_value': 542}]
2025-05-21 12:03:08,654 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-21 12:03:09,046 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-21 12:03:09,046 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148252.77, 'new_value': 154767.28}, {'field': 'total_amount', 'old_value': 148252.77, 'new_value': 154767.28}, {'field': 'order_count', 'old_value': 10457, 'new_value': 11049}]
2025-05-21 12:03:09,046 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-21 12:03:09,525 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-21 12:03:09,526 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19269.6, 'new_value': 21517.4}, {'field': 'offline_amount', 'old_value': 46867.1, 'new_value': 49938.6}, {'field': 'total_amount', 'old_value': 66136.7, 'new_value': 71456.0}, {'field': 'order_count', 'old_value': 2518, 'new_value': 2690}]
2025-05-21 12:03:09,526 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-21 12:03:09,953 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-21 12:03:09,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28754.83, 'new_value': 29928.8}, {'field': 'total_amount', 'old_value': 28754.83, 'new_value': 29928.8}, {'field': 'order_count', 'old_value': 1326, 'new_value': 1380}]
2025-05-21 12:03:09,953 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-21 12:03:10,379 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-21 12:03:10,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3902780.51, 'new_value': 4075207.51}, {'field': 'total_amount', 'old_value': 3902780.51, 'new_value': 4075207.51}, {'field': 'order_count', 'old_value': 80023, 'new_value': 83827}]
2025-05-21 12:03:10,379 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-21 12:03:10,851 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-21 12:03:10,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19833.26, 'new_value': 20817.93}, {'field': 'total_amount', 'old_value': 19833.26, 'new_value': 20817.93}, {'field': 'order_count', 'old_value': 90, 'new_value': 94}]
2025-05-21 12:03:10,851 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-21 12:03:11,335 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-21 12:03:11,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 433469.26, 'new_value': 452625.59}, {'field': 'total_amount', 'old_value': 439015.62, 'new_value': 458171.95}, {'field': 'order_count', 'old_value': 4864, 'new_value': 4989}]
2025-05-21 12:03:11,335 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-21 12:03:11,900 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-21 12:03:11,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147781.06, 'new_value': 157557.05}, {'field': 'total_amount', 'old_value': 147781.06, 'new_value': 157557.05}, {'field': 'order_count', 'old_value': 2811, 'new_value': 2960}]
2025-05-21 12:03:11,900 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-21 12:03:12,420 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-21 12:03:12,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248515.0, 'new_value': 265245.0}, {'field': 'total_amount', 'old_value': 248515.0, 'new_value': 265245.0}, {'field': 'order_count', 'old_value': 5400, 'new_value': 5756}]
2025-05-21 12:03:12,421 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-21 12:03:12,885 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-21 12:03:12,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59891.06, 'new_value': 63514.27}, {'field': 'total_amount', 'old_value': 59891.06, 'new_value': 63514.27}, {'field': 'order_count', 'old_value': 6078, 'new_value': 6434}]
2025-05-21 12:03:12,885 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-21 12:03:13,317 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-21 12:03:13,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24643.0, 'new_value': 25242.0}, {'field': 'total_amount', 'old_value': 24670.9, 'new_value': 25269.9}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-21 12:03:13,318 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-21 12:03:13,767 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-21 12:03:13,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102876.59, 'new_value': 107147.36}, {'field': 'total_amount', 'old_value': 102876.59, 'new_value': 107147.36}, {'field': 'order_count', 'old_value': 2593, 'new_value': 2711}]
2025-05-21 12:03:13,768 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-21 12:03:14,237 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-21 12:03:14,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23389.0, 'new_value': 25684.0}, {'field': 'total_amount', 'old_value': 23389.0, 'new_value': 25684.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 84}]
2025-05-21 12:03:14,237 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-21 12:03:14,714 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-21 12:03:14,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77078.37, 'new_value': 81895.96}, {'field': 'offline_amount', 'old_value': 336644.9, 'new_value': 347929.7}, {'field': 'total_amount', 'old_value': 413723.27, 'new_value': 429825.66}, {'field': 'order_count', 'old_value': 2845, 'new_value': 3001}]
2025-05-21 12:03:14,714 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-21 12:03:15,135 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-21 12:03:15,135 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42388.89, 'new_value': 46421.57}, {'field': 'total_amount', 'old_value': 69831.61, 'new_value': 73864.29}, {'field': 'order_count', 'old_value': 4572, 'new_value': 4816}]
2025-05-21 12:03:15,136 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-21 12:03:15,582 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-21 12:03:15,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73142.68, 'new_value': 81382.0}, {'field': 'total_amount', 'old_value': 119559.49, 'new_value': 127798.81}, {'field': 'order_count', 'old_value': 7864, 'new_value': 8396}]
2025-05-21 12:03:15,582 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-21 12:03:16,054 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-21 12:03:16,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 904854.39, 'new_value': 947718.11}, {'field': 'total_amount', 'old_value': 904854.39, 'new_value': 947718.11}, {'field': 'order_count', 'old_value': 2650, 'new_value': 2790}]
2025-05-21 12:03:16,054 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-21 12:03:16,522 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-21 12:03:16,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132996.8, 'new_value': 142765.8}, {'field': 'total_amount', 'old_value': 132996.8, 'new_value': 142765.8}, {'field': 'order_count', 'old_value': 4676, 'new_value': 4981}]
2025-05-21 12:03:16,522 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-21 12:03:16,971 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-21 12:03:16,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 832298.93, 'new_value': 870441.3}, {'field': 'total_amount', 'old_value': 832298.93, 'new_value': 870441.3}, {'field': 'order_count', 'old_value': 2922, 'new_value': 3080}]
2025-05-21 12:03:16,972 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-21 12:03:17,429 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-21 12:03:17,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600085.4, 'new_value': 662757.5}, {'field': 'total_amount', 'old_value': 600085.4, 'new_value': 662757.5}, {'field': 'order_count', 'old_value': 1837, 'new_value': 1939}]
2025-05-21 12:03:17,429 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-21 12:03:17,872 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-21 12:03:17,872 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37047.0, 'new_value': 37346.0}, {'field': 'total_amount', 'old_value': 37047.0, 'new_value': 37346.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-21 12:03:17,872 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-21 12:03:18,398 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-21 12:03:18,398 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15560.0, 'new_value': 18158.0}, {'field': 'offline_amount', 'old_value': 17960.0, 'new_value': 20558.0}, {'field': 'total_amount', 'old_value': 33520.0, 'new_value': 38716.0}, {'field': 'order_count', 'old_value': 15598, 'new_value': 18196}]
2025-05-21 12:03:18,398 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-21 12:03:18,861 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-21 12:03:18,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1621.2, 'new_value': 2844.0}, {'field': 'offline_amount', 'old_value': 34227.62, 'new_value': 39103.62}, {'field': 'total_amount', 'old_value': 35848.82, 'new_value': 41947.62}, {'field': 'order_count', 'old_value': 266, 'new_value': 304}]
2025-05-21 12:03:18,861 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-21 12:03:19,254 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-21 12:03:19,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243583.46, 'new_value': 257232.26}, {'field': 'total_amount', 'old_value': 243583.46, 'new_value': 257232.26}, {'field': 'order_count', 'old_value': 670, 'new_value': 713}]
2025-05-21 12:03:19,254 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-21 12:03:19,879 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-21 12:03:19,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94697.0, 'new_value': 102172.0}, {'field': 'total_amount', 'old_value': 94777.0, 'new_value': 102252.0}, {'field': 'order_count', 'old_value': 9155, 'new_value': 9968}]
2025-05-21 12:03:19,879 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-21 12:03:20,394 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-21 12:03:20,394 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14841.0, 'new_value': 15029.0}, {'field': 'offline_amount', 'old_value': 45815.0, 'new_value': 47959.0}, {'field': 'total_amount', 'old_value': 60656.0, 'new_value': 62988.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 101}]
2025-05-21 12:03:20,394 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-21 12:03:20,875 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-21 12:03:20,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101694.0, 'new_value': 109077.0}, {'field': 'offline_amount', 'old_value': 66029.0, 'new_value': 72258.0}, {'field': 'total_amount', 'old_value': 167723.0, 'new_value': 181335.0}, {'field': 'order_count', 'old_value': 6993, 'new_value': 7485}]
2025-05-21 12:03:20,876 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-21 12:03:21,361 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-21 12:03:21,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88124.0, 'new_value': 92071.0}, {'field': 'total_amount', 'old_value': 88124.0, 'new_value': 92071.0}, {'field': 'order_count', 'old_value': 451, 'new_value': 477}]
2025-05-21 12:03:21,361 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-21 12:03:21,823 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-21 12:03:21,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108693.0, 'new_value': 110651.0}, {'field': 'total_amount', 'old_value': 108693.0, 'new_value': 110651.0}, {'field': 'order_count', 'old_value': 432, 'new_value': 444}]
2025-05-21 12:03:21,823 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-21 12:03:22,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-21 12:03:22,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188385.0, 'new_value': 200485.0}, {'field': 'total_amount', 'old_value': 188385.0, 'new_value': 200485.0}, {'field': 'order_count', 'old_value': 449, 'new_value': 476}]
2025-05-21 12:03:22,265 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-21 12:03:22,708 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-21 12:03:22,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38593.0, 'new_value': 40293.0}, {'field': 'total_amount', 'old_value': 38593.0, 'new_value': 40293.0}, {'field': 'order_count', 'old_value': 739, 'new_value': 776}]
2025-05-21 12:03:22,709 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-21 12:03:23,124 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-21 12:03:23,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142429.0, 'new_value': 152905.0}, {'field': 'total_amount', 'old_value': 142429.0, 'new_value': 152905.0}, {'field': 'order_count', 'old_value': 14922, 'new_value': 16058}]
2025-05-21 12:03:23,125 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-21 12:03:23,604 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-21 12:03:23,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96421.0, 'new_value': 99332.0}, {'field': 'total_amount', 'old_value': 96421.0, 'new_value': 99332.0}, {'field': 'order_count', 'old_value': 876, 'new_value': 920}]
2025-05-21 12:03:23,604 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-21 12:03:24,041 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-21 12:03:24,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9420.0, 'new_value': 11220.0}, {'field': 'total_amount', 'old_value': 9420.0, 'new_value': 11220.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-21 12:03:24,042 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-21 12:03:24,482 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-21 12:03:24,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4403.0, 'new_value': 4677.0}, {'field': 'total_amount', 'old_value': 4403.0, 'new_value': 4677.0}, {'field': 'order_count', 'old_value': 587, 'new_value': 588}]
2025-05-21 12:03:24,482 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-21 12:03:24,930 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-21 12:03:24,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34816.0, 'new_value': 36816.0}, {'field': 'total_amount', 'old_value': 34816.0, 'new_value': 36816.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-21 12:03:24,930 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-21 12:03:25,382 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-21 12:03:25,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4709.6, 'new_value': 4937.6}, {'field': 'total_amount', 'old_value': 4709.6, 'new_value': 4937.6}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-21 12:03:25,382 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-21 12:03:25,805 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-21 12:03:25,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33111.0, 'new_value': 33791.0}, {'field': 'total_amount', 'old_value': 71517.4, 'new_value': 72197.4}, {'field': 'order_count', 'old_value': 97, 'new_value': 98}]
2025-05-21 12:03:25,806 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-21 12:03:26,221 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-21 12:03:26,221 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99351.87, 'new_value': 105740.47}, {'field': 'offline_amount', 'old_value': 516239.26, 'new_value': 556365.11}, {'field': 'total_amount', 'old_value': 615591.13, 'new_value': 662105.58}, {'field': 'order_count', 'old_value': 1436, 'new_value': 1520}]
2025-05-21 12:03:26,221 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG
2025-05-21 12:03:26,629 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG
2025-05-21 12:03:26,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1149.0, 'new_value': 3238.0}, {'field': 'total_amount', 'old_value': 1149.0, 'new_value': 3238.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-21 12:03:26,629 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-21 12:03:27,091 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-21 12:03:27,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64293.6, 'new_value': 64856.6}, {'field': 'total_amount', 'old_value': 64293.6, 'new_value': 64856.6}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-21 12:03:27,091 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-21 12:03:27,536 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-21 12:03:27,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65868.2, 'new_value': 70016.08}, {'field': 'offline_amount', 'old_value': 637565.99, 'new_value': 689487.6}, {'field': 'total_amount', 'old_value': 701559.86, 'new_value': 757629.35}, {'field': 'order_count', 'old_value': 3358, 'new_value': 3593}]
2025-05-21 12:03:27,536 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-21 12:03:27,982 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-21 12:03:27,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80376.0, 'new_value': 89085.0}, {'field': 'total_amount', 'old_value': 80376.0, 'new_value': 89085.0}, {'field': 'order_count', 'old_value': 274, 'new_value': 301}]
2025-05-21 12:03:27,983 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-21 12:03:28,485 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-21 12:03:28,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60650.0, 'new_value': 66632.0}, {'field': 'total_amount', 'old_value': 65968.0, 'new_value': 71950.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-05-21 12:03:28,485 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-21 12:03:28,968 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-21 12:03:28,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13428070.9, 'new_value': 14093218.76}, {'field': 'total_amount', 'old_value': 13428070.9, 'new_value': 14093218.76}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-21 12:03:28,968 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-21 12:03:29,505 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-21 12:03:29,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32531.36, 'new_value': 36377.36}, {'field': 'total_amount', 'old_value': 36530.36, 'new_value': 40376.36}, {'field': 'order_count', 'old_value': 2409, 'new_value': 2519}]
2025-05-21 12:03:29,505 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-21 12:03:30,137 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-21 12:03:30,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140604.13, 'new_value': 146798.13}, {'field': 'total_amount', 'old_value': 140604.13, 'new_value': 146798.13}, {'field': 'order_count', 'old_value': 14550, 'new_value': 15272}]
2025-05-21 12:03:30,138 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-21 12:03:30,622 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-21 12:03:30,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16127.0, 'new_value': 16676.0}, {'field': 'total_amount', 'old_value': 16127.0, 'new_value': 16676.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 106}]
2025-05-21 12:03:30,623 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-21 12:03:31,137 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-21 12:03:31,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33466.28, 'new_value': 36587.58}, {'field': 'total_amount', 'old_value': 33466.28, 'new_value': 36587.58}, {'field': 'order_count', 'old_value': 1616, 'new_value': 1816}]
2025-05-21 12:03:31,138 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-21 12:03:31,639 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-21 12:03:31,640 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9532.0, 'new_value': 19859.14}, {'field': 'offline_amount', 'old_value': 41032.29, 'new_value': 45504.29}, {'field': 'total_amount', 'old_value': 50564.29, 'new_value': 65363.43}, {'field': 'order_count', 'old_value': 240, 'new_value': 299}]
2025-05-21 12:03:31,640 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-21 12:03:32,055 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-21 12:03:32,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20294.0, 'new_value': 34650.0}, {'field': 'total_amount', 'old_value': 20294.0, 'new_value': 34650.0}, {'field': 'order_count', 'old_value': 1059, 'new_value': 1702}]
2025-05-21 12:03:32,055 - INFO - 日期 2025-05 处理完成 - 更新: 391 条，插入: 0 条，错误: 0 条
2025-05-21 12:03:32,055 - INFO - 数据同步完成！更新: 391 条，插入: 0 条，错误: 0 条
2025-05-21 12:03:32,057 - INFO - =================同步完成====================
2025-05-21 15:00:01,941 - INFO - =================使用默认全量同步=============
2025-05-21 15:00:03,385 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 15:00:03,386 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 15:00:03,414 - INFO - 开始处理日期: 2025-01
2025-05-21 15:00:03,417 - INFO - Request Parameters - Page 1:
2025-05-21 15:00:03,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:03,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:04,810 - INFO - Response - Page 1:
2025-05-21 15:00:05,011 - INFO - 第 1 页获取到 100 条记录
2025-05-21 15:00:05,011 - INFO - Request Parameters - Page 2:
2025-05-21 15:00:05,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:05,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:05,557 - INFO - Response - Page 2:
2025-05-21 15:00:05,758 - INFO - 第 2 页获取到 100 条记录
2025-05-21 15:00:05,758 - INFO - Request Parameters - Page 3:
2025-05-21 15:00:05,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:05,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:06,253 - INFO - Response - Page 3:
2025-05-21 15:00:06,453 - INFO - 第 3 页获取到 100 条记录
2025-05-21 15:00:06,453 - INFO - Request Parameters - Page 4:
2025-05-21 15:00:06,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:06,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:06,973 - INFO - Response - Page 4:
2025-05-21 15:00:07,175 - INFO - 第 4 页获取到 100 条记录
2025-05-21 15:00:07,175 - INFO - Request Parameters - Page 5:
2025-05-21 15:00:07,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:07,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:07,699 - INFO - Response - Page 5:
2025-05-21 15:00:07,899 - INFO - 第 5 页获取到 100 条记录
2025-05-21 15:00:07,899 - INFO - Request Parameters - Page 6:
2025-05-21 15:00:07,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:07,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:08,513 - INFO - Response - Page 6:
2025-05-21 15:00:08,714 - INFO - 第 6 页获取到 100 条记录
2025-05-21 15:00:08,714 - INFO - Request Parameters - Page 7:
2025-05-21 15:00:08,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:08,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:09,248 - INFO - Response - Page 7:
2025-05-21 15:00:09,448 - INFO - 第 7 页获取到 82 条记录
2025-05-21 15:00:09,448 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 15:00:09,448 - INFO - 获取到 682 条表单数据
2025-05-21 15:00:09,460 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 15:00:09,470 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 15:00:09,470 - INFO - 开始处理日期: 2025-02
2025-05-21 15:00:09,471 - INFO - Request Parameters - Page 1:
2025-05-21 15:00:09,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:09,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:10,035 - INFO - Response - Page 1:
2025-05-21 15:00:10,235 - INFO - 第 1 页获取到 100 条记录
2025-05-21 15:00:10,235 - INFO - Request Parameters - Page 2:
2025-05-21 15:00:10,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:10,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:10,981 - INFO - Response - Page 2:
2025-05-21 15:00:11,181 - INFO - 第 2 页获取到 100 条记录
2025-05-21 15:00:11,181 - INFO - Request Parameters - Page 3:
2025-05-21 15:00:11,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:11,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:11,714 - INFO - Response - Page 3:
2025-05-21 15:00:11,914 - INFO - 第 3 页获取到 100 条记录
2025-05-21 15:00:11,914 - INFO - Request Parameters - Page 4:
2025-05-21 15:00:11,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:11,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:12,369 - INFO - Response - Page 4:
2025-05-21 15:00:12,569 - INFO - 第 4 页获取到 100 条记录
2025-05-21 15:00:12,569 - INFO - Request Parameters - Page 5:
2025-05-21 15:00:12,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:12,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:13,096 - INFO - Response - Page 5:
2025-05-21 15:00:13,297 - INFO - 第 5 页获取到 100 条记录
2025-05-21 15:00:13,297 - INFO - Request Parameters - Page 6:
2025-05-21 15:00:13,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:13,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:13,779 - INFO - Response - Page 6:
2025-05-21 15:00:13,979 - INFO - 第 6 页获取到 100 条记录
2025-05-21 15:00:13,979 - INFO - Request Parameters - Page 7:
2025-05-21 15:00:13,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:13,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:14,428 - INFO - Response - Page 7:
2025-05-21 15:00:14,629 - INFO - 第 7 页获取到 70 条记录
2025-05-21 15:00:14,629 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 15:00:14,629 - INFO - 获取到 670 条表单数据
2025-05-21 15:00:14,640 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 15:00:14,652 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 15:00:14,652 - INFO - 开始处理日期: 2025-03
2025-05-21 15:00:14,652 - INFO - Request Parameters - Page 1:
2025-05-21 15:00:14,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:14,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:15,158 - INFO - Response - Page 1:
2025-05-21 15:00:15,358 - INFO - 第 1 页获取到 100 条记录
2025-05-21 15:00:15,358 - INFO - Request Parameters - Page 2:
2025-05-21 15:00:15,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:15,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:16,010 - INFO - Response - Page 2:
2025-05-21 15:00:16,211 - INFO - 第 2 页获取到 100 条记录
2025-05-21 15:00:16,211 - INFO - Request Parameters - Page 3:
2025-05-21 15:00:16,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:16,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:16,698 - INFO - Response - Page 3:
2025-05-21 15:00:16,898 - INFO - 第 3 页获取到 100 条记录
2025-05-21 15:00:16,898 - INFO - Request Parameters - Page 4:
2025-05-21 15:00:16,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:16,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:17,370 - INFO - Response - Page 4:
2025-05-21 15:00:17,570 - INFO - 第 4 页获取到 100 条记录
2025-05-21 15:00:17,570 - INFO - Request Parameters - Page 5:
2025-05-21 15:00:17,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:17,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:18,161 - INFO - Response - Page 5:
2025-05-21 15:00:18,362 - INFO - 第 5 页获取到 100 条记录
2025-05-21 15:00:18,362 - INFO - Request Parameters - Page 6:
2025-05-21 15:00:18,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:18,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:18,858 - INFO - Response - Page 6:
2025-05-21 15:00:19,058 - INFO - 第 6 页获取到 100 条记录
2025-05-21 15:00:19,058 - INFO - Request Parameters - Page 7:
2025-05-21 15:00:19,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:19,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:19,566 - INFO - Response - Page 7:
2025-05-21 15:00:19,766 - INFO - 第 7 页获取到 61 条记录
2025-05-21 15:00:19,766 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 15:00:19,766 - INFO - 获取到 661 条表单数据
2025-05-21 15:00:19,778 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 15:00:19,790 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 15:00:19,790 - INFO - 开始处理日期: 2025-04
2025-05-21 15:00:19,791 - INFO - Request Parameters - Page 1:
2025-05-21 15:00:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:20,321 - INFO - Response - Page 1:
2025-05-21 15:00:20,521 - INFO - 第 1 页获取到 100 条记录
2025-05-21 15:00:20,521 - INFO - Request Parameters - Page 2:
2025-05-21 15:00:20,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:20,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:21,048 - INFO - Response - Page 2:
2025-05-21 15:00:21,249 - INFO - 第 2 页获取到 100 条记录
2025-05-21 15:00:21,249 - INFO - Request Parameters - Page 3:
2025-05-21 15:00:21,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:21,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:21,760 - INFO - Response - Page 3:
2025-05-21 15:00:21,961 - INFO - 第 3 页获取到 100 条记录
2025-05-21 15:00:21,961 - INFO - Request Parameters - Page 4:
2025-05-21 15:00:21,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:21,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:22,706 - INFO - Response - Page 4:
2025-05-21 15:00:22,906 - INFO - 第 4 页获取到 100 条记录
2025-05-21 15:00:22,906 - INFO - Request Parameters - Page 5:
2025-05-21 15:00:22,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:22,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:23,386 - INFO - Response - Page 5:
2025-05-21 15:00:23,587 - INFO - 第 5 页获取到 100 条记录
2025-05-21 15:00:23,587 - INFO - Request Parameters - Page 6:
2025-05-21 15:00:23,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:23,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:24,028 - INFO - Response - Page 6:
2025-05-21 15:00:24,228 - INFO - 第 6 页获取到 100 条记录
2025-05-21 15:00:24,228 - INFO - Request Parameters - Page 7:
2025-05-21 15:00:24,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:24,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:24,729 - INFO - Response - Page 7:
2025-05-21 15:00:24,929 - INFO - 第 7 页获取到 56 条记录
2025-05-21 15:00:24,929 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 15:00:24,930 - INFO - 获取到 656 条表单数据
2025-05-21 15:00:24,943 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 15:00:24,955 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 15:00:24,956 - INFO - 开始处理日期: 2025-05
2025-05-21 15:00:24,956 - INFO - Request Parameters - Page 1:
2025-05-21 15:00:24,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:24,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:25,464 - INFO - Response - Page 1:
2025-05-21 15:00:25,664 - INFO - 第 1 页获取到 100 条记录
2025-05-21 15:00:25,664 - INFO - Request Parameters - Page 2:
2025-05-21 15:00:25,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:25,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:26,212 - INFO - Response - Page 2:
2025-05-21 15:00:26,413 - INFO - 第 2 页获取到 100 条记录
2025-05-21 15:00:26,413 - INFO - Request Parameters - Page 3:
2025-05-21 15:00:26,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:26,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:26,896 - INFO - Response - Page 3:
2025-05-21 15:00:27,096 - INFO - 第 3 页获取到 100 条记录
2025-05-21 15:00:27,096 - INFO - Request Parameters - Page 4:
2025-05-21 15:00:27,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:27,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:27,629 - INFO - Response - Page 4:
2025-05-21 15:00:27,829 - INFO - 第 4 页获取到 100 条记录
2025-05-21 15:00:27,829 - INFO - Request Parameters - Page 5:
2025-05-21 15:00:27,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:27,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:28,358 - INFO - Response - Page 5:
2025-05-21 15:00:28,559 - INFO - 第 5 页获取到 100 条记录
2025-05-21 15:00:28,559 - INFO - Request Parameters - Page 6:
2025-05-21 15:00:28,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:28,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:29,068 - INFO - Response - Page 6:
2025-05-21 15:00:29,268 - INFO - 第 6 页获取到 100 条记录
2025-05-21 15:00:29,268 - INFO - Request Parameters - Page 7:
2025-05-21 15:00:29,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:00:29,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:00:29,628 - INFO - Response - Page 7:
2025-05-21 15:00:29,829 - INFO - 第 7 页获取到 28 条记录
2025-05-21 15:00:29,829 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 15:00:29,829 - INFO - 获取到 628 条表单数据
2025-05-21 15:00:29,841 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 15:00:29,841 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-21 15:00:30,325 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-21 15:00:30,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21311.5, 'new_value': 22614.9}, {'field': 'total_amount', 'old_value': 21311.5, 'new_value': 22614.9}, {'field': 'order_count', 'old_value': 151, 'new_value': 158}]
2025-05-21 15:00:30,328 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-21 15:00:30,770 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-21 15:00:30,771 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21919.2, 'new_value': 22257.2}, {'field': 'offline_amount', 'old_value': 122874.54, 'new_value': 128335.54}, {'field': 'total_amount', 'old_value': 144793.74, 'new_value': 150592.74}, {'field': 'order_count', 'old_value': 206, 'new_value': 211}]
2025-05-21 15:00:30,771 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-21 15:00:31,237 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-21 15:00:31,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40790.61, 'new_value': 42991.76}, {'field': 'offline_amount', 'old_value': 533421.34, 'new_value': 560632.59}, {'field': 'total_amount', 'old_value': 574211.95, 'new_value': 603624.35}, {'field': 'order_count', 'old_value': 2431, 'new_value': 2556}]
2025-05-21 15:00:31,242 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-21 15:00:31,720 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-21 15:00:31,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304601.24, 'new_value': 319395.21}, {'field': 'total_amount', 'old_value': 304601.24, 'new_value': 319395.21}, {'field': 'order_count', 'old_value': 1473, 'new_value': 1559}]
2025-05-21 15:00:31,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-21 15:00:32,200 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-21 15:00:32,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1880000.0, 'new_value': 1930000.0}, {'field': 'total_amount', 'old_value': 1880000.0, 'new_value': 1930000.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 278}]
2025-05-21 15:00:32,201 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-21 15:00:32,668 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-21 15:00:32,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 485000.0, 'new_value': 490000.0}, {'field': 'total_amount', 'old_value': 485000.0, 'new_value': 490000.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 145}]
2025-05-21 15:00:32,668 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-21 15:00:33,268 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-21 15:00:33,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 465000.0, 'new_value': 470000.0}, {'field': 'total_amount', 'old_value': 465000.0, 'new_value': 470000.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 144}]
2025-05-21 15:00:33,268 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-21 15:00:33,715 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-21 15:00:33,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2998674.0, 'new_value': 3048674.0}, {'field': 'total_amount', 'old_value': 2998674.0, 'new_value': 3048674.0}, {'field': 'order_count', 'old_value': 297, 'new_value': 298}]
2025-05-21 15:00:33,717 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-21 15:00:34,209 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-21 15:00:34,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 547590.94, 'new_value': 586113.97}, {'field': 'total_amount', 'old_value': 547590.94, 'new_value': 586113.97}, {'field': 'order_count', 'old_value': 2762, 'new_value': 2977}]
2025-05-21 15:00:34,210 - INFO - 日期 2025-05 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-05-21 15:00:34,210 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-05-21 15:00:34,212 - INFO - =================同步完成====================
2025-05-21 18:00:02,019 - INFO - =================使用默认全量同步=============
2025-05-21 18:00:03,441 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 18:00:03,441 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 18:00:03,472 - INFO - 开始处理日期: 2025-01
2025-05-21 18:00:03,472 - INFO - Request Parameters - Page 1:
2025-05-21 18:00:03,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:03,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:04,347 - INFO - Response - Page 1:
2025-05-21 18:00:04,550 - INFO - 第 1 页获取到 100 条记录
2025-05-21 18:00:04,550 - INFO - Request Parameters - Page 2:
2025-05-21 18:00:04,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:04,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:05,659 - INFO - Response - Page 2:
2025-05-21 18:00:05,863 - INFO - 第 2 页获取到 100 条记录
2025-05-21 18:00:05,863 - INFO - Request Parameters - Page 3:
2025-05-21 18:00:05,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:05,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:06,394 - INFO - Response - Page 3:
2025-05-21 18:00:06,597 - INFO - 第 3 页获取到 100 条记录
2025-05-21 18:00:06,597 - INFO - Request Parameters - Page 4:
2025-05-21 18:00:06,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:06,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:07,159 - INFO - Response - Page 4:
2025-05-21 18:00:07,363 - INFO - 第 4 页获取到 100 条记录
2025-05-21 18:00:07,363 - INFO - Request Parameters - Page 5:
2025-05-21 18:00:07,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:07,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:07,847 - INFO - Response - Page 5:
2025-05-21 18:00:08,050 - INFO - 第 5 页获取到 100 条记录
2025-05-21 18:00:08,050 - INFO - Request Parameters - Page 6:
2025-05-21 18:00:08,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:08,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:08,550 - INFO - Response - Page 6:
2025-05-21 18:00:08,753 - INFO - 第 6 页获取到 100 条记录
2025-05-21 18:00:08,753 - INFO - Request Parameters - Page 7:
2025-05-21 18:00:08,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:08,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:09,175 - INFO - Response - Page 7:
2025-05-21 18:00:09,378 - INFO - 第 7 页获取到 82 条记录
2025-05-21 18:00:09,378 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 18:00:09,378 - INFO - 获取到 682 条表单数据
2025-05-21 18:00:09,378 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 18:00:09,394 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:00:09,394 - INFO - 开始处理日期: 2025-02
2025-05-21 18:00:09,394 - INFO - Request Parameters - Page 1:
2025-05-21 18:00:09,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:09,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:09,956 - INFO - Response - Page 1:
2025-05-21 18:00:10,159 - INFO - 第 1 页获取到 100 条记录
2025-05-21 18:00:10,159 - INFO - Request Parameters - Page 2:
2025-05-21 18:00:10,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:10,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:10,706 - INFO - Response - Page 2:
2025-05-21 18:00:10,909 - INFO - 第 2 页获取到 100 条记录
2025-05-21 18:00:10,909 - INFO - Request Parameters - Page 3:
2025-05-21 18:00:10,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:10,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:11,456 - INFO - Response - Page 3:
2025-05-21 18:00:11,659 - INFO - 第 3 页获取到 100 条记录
2025-05-21 18:00:11,659 - INFO - Request Parameters - Page 4:
2025-05-21 18:00:11,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:11,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:12,144 - INFO - Response - Page 4:
2025-05-21 18:00:12,347 - INFO - 第 4 页获取到 100 条记录
2025-05-21 18:00:12,347 - INFO - Request Parameters - Page 5:
2025-05-21 18:00:12,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:12,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:12,831 - INFO - Response - Page 5:
2025-05-21 18:00:13,034 - INFO - 第 5 页获取到 100 条记录
2025-05-21 18:00:13,034 - INFO - Request Parameters - Page 6:
2025-05-21 18:00:13,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:13,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:13,534 - INFO - Response - Page 6:
2025-05-21 18:00:13,738 - INFO - 第 6 页获取到 100 条记录
2025-05-21 18:00:13,738 - INFO - Request Parameters - Page 7:
2025-05-21 18:00:13,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:13,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:14,222 - INFO - Response - Page 7:
2025-05-21 18:00:14,425 - INFO - 第 7 页获取到 70 条记录
2025-05-21 18:00:14,425 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 18:00:14,425 - INFO - 获取到 670 条表单数据
2025-05-21 18:00:14,425 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 18:00:14,441 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:00:14,441 - INFO - 开始处理日期: 2025-03
2025-05-21 18:00:14,441 - INFO - Request Parameters - Page 1:
2025-05-21 18:00:14,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:14,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:14,956 - INFO - Response - Page 1:
2025-05-21 18:00:15,159 - INFO - 第 1 页获取到 100 条记录
2025-05-21 18:00:15,159 - INFO - Request Parameters - Page 2:
2025-05-21 18:00:15,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:15,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:15,878 - INFO - Response - Page 2:
2025-05-21 18:00:16,081 - INFO - 第 2 页获取到 100 条记录
2025-05-21 18:00:16,081 - INFO - Request Parameters - Page 3:
2025-05-21 18:00:16,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:16,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:16,597 - INFO - Response - Page 3:
2025-05-21 18:00:16,800 - INFO - 第 3 页获取到 100 条记录
2025-05-21 18:00:16,800 - INFO - Request Parameters - Page 4:
2025-05-21 18:00:16,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:16,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:17,300 - INFO - Response - Page 4:
2025-05-21 18:00:17,503 - INFO - 第 4 页获取到 100 条记录
2025-05-21 18:00:17,503 - INFO - Request Parameters - Page 5:
2025-05-21 18:00:17,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:17,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:18,050 - INFO - Response - Page 5:
2025-05-21 18:00:18,253 - INFO - 第 5 页获取到 100 条记录
2025-05-21 18:00:18,253 - INFO - Request Parameters - Page 6:
2025-05-21 18:00:18,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:18,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:18,909 - INFO - Response - Page 6:
2025-05-21 18:00:19,112 - INFO - 第 6 页获取到 100 条记录
2025-05-21 18:00:19,112 - INFO - Request Parameters - Page 7:
2025-05-21 18:00:19,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:19,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:19,550 - INFO - Response - Page 7:
2025-05-21 18:00:19,753 - INFO - 第 7 页获取到 61 条记录
2025-05-21 18:00:19,753 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 18:00:19,753 - INFO - 获取到 661 条表单数据
2025-05-21 18:00:19,753 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 18:00:19,769 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:00:19,769 - INFO - 开始处理日期: 2025-04
2025-05-21 18:00:19,769 - INFO - Request Parameters - Page 1:
2025-05-21 18:00:19,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:19,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:20,316 - INFO - Response - Page 1:
2025-05-21 18:00:20,519 - INFO - 第 1 页获取到 100 条记录
2025-05-21 18:00:20,519 - INFO - Request Parameters - Page 2:
2025-05-21 18:00:20,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:20,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:20,956 - INFO - Response - Page 2:
2025-05-21 18:00:21,159 - INFO - 第 2 页获取到 100 条记录
2025-05-21 18:00:21,159 - INFO - Request Parameters - Page 3:
2025-05-21 18:00:21,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:21,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:21,659 - INFO - Response - Page 3:
2025-05-21 18:00:21,862 - INFO - 第 3 页获取到 100 条记录
2025-05-21 18:00:21,862 - INFO - Request Parameters - Page 4:
2025-05-21 18:00:21,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:21,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:22,503 - INFO - Response - Page 4:
2025-05-21 18:00:22,706 - INFO - 第 4 页获取到 100 条记录
2025-05-21 18:00:22,706 - INFO - Request Parameters - Page 5:
2025-05-21 18:00:22,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:22,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:23,362 - INFO - Response - Page 5:
2025-05-21 18:00:23,566 - INFO - 第 5 页获取到 100 条记录
2025-05-21 18:00:23,566 - INFO - Request Parameters - Page 6:
2025-05-21 18:00:23,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:23,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:24,034 - INFO - Response - Page 6:
2025-05-21 18:00:24,237 - INFO - 第 6 页获取到 100 条记录
2025-05-21 18:00:24,237 - INFO - Request Parameters - Page 7:
2025-05-21 18:00:24,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:24,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:24,769 - INFO - Response - Page 7:
2025-05-21 18:00:24,972 - INFO - 第 7 页获取到 56 条记录
2025-05-21 18:00:24,972 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 18:00:24,972 - INFO - 获取到 656 条表单数据
2025-05-21 18:00:24,972 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 18:00:24,987 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:00:24,987 - INFO - 开始处理日期: 2025-05
2025-05-21 18:00:24,987 - INFO - Request Parameters - Page 1:
2025-05-21 18:00:24,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:24,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:25,519 - INFO - Response - Page 1:
2025-05-21 18:00:25,722 - INFO - 第 1 页获取到 100 条记录
2025-05-21 18:00:25,722 - INFO - Request Parameters - Page 2:
2025-05-21 18:00:25,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:25,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:26,175 - INFO - Response - Page 2:
2025-05-21 18:00:26,378 - INFO - 第 2 页获取到 100 条记录
2025-05-21 18:00:26,378 - INFO - Request Parameters - Page 3:
2025-05-21 18:00:26,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:26,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:26,925 - INFO - Response - Page 3:
2025-05-21 18:00:27,128 - INFO - 第 3 页获取到 100 条记录
2025-05-21 18:00:27,128 - INFO - Request Parameters - Page 4:
2025-05-21 18:00:27,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:27,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:27,659 - INFO - Response - Page 4:
2025-05-21 18:00:27,862 - INFO - 第 4 页获取到 100 条记录
2025-05-21 18:00:27,862 - INFO - Request Parameters - Page 5:
2025-05-21 18:00:27,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:27,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:28,378 - INFO - Response - Page 5:
2025-05-21 18:00:28,581 - INFO - 第 5 页获取到 100 条记录
2025-05-21 18:00:28,581 - INFO - Request Parameters - Page 6:
2025-05-21 18:00:28,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:28,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:29,097 - INFO - Response - Page 6:
2025-05-21 18:00:29,316 - INFO - 第 6 页获取到 100 条记录
2025-05-21 18:00:29,316 - INFO - Request Parameters - Page 7:
2025-05-21 18:00:29,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:00:29,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:00:29,816 - INFO - Response - Page 7:
2025-05-21 18:00:30,019 - INFO - 第 7 页获取到 28 条记录
2025-05-21 18:00:30,019 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 18:00:30,019 - INFO - 获取到 628 条表单数据
2025-05-21 18:00:30,019 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 18:00:30,019 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-21 18:00:30,566 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-21 18:00:30,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69280.94, 'new_value': 73997.98}, {'field': 'total_amount', 'old_value': 95620.87, 'new_value': 100337.91}, {'field': 'order_count', 'old_value': 2186, 'new_value': 2298}]
2025-05-21 18:00:30,566 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-21 18:00:31,034 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-21 18:00:31,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3310.0, 'new_value': 3428.97}, {'field': 'offline_amount', 'old_value': 66253.51, 'new_value': 68051.29}, {'field': 'total_amount', 'old_value': 69563.51, 'new_value': 71480.26}, {'field': 'order_count', 'old_value': 2673, 'new_value': 2771}]
2025-05-21 18:00:31,034 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-21 18:00:31,503 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-21 18:00:31,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76159.79, 'new_value': 77955.51}, {'field': 'total_amount', 'old_value': 76159.79, 'new_value': 77955.51}, {'field': 'order_count', 'old_value': 2873, 'new_value': 2956}]
2025-05-21 18:00:31,503 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-21 18:00:31,987 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-21 18:00:31,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75658.55, 'new_value': 80680.18}, {'field': 'offline_amount', 'old_value': 42361.97, 'new_value': 42971.37}, {'field': 'total_amount', 'old_value': 118020.52, 'new_value': 123651.55}, {'field': 'order_count', 'old_value': 6654, 'new_value': 6970}]
2025-05-21 18:00:31,987 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-21 18:00:32,425 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-21 18:00:32,425 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35969.02, 'new_value': 38042.22}, {'field': 'offline_amount', 'old_value': 41109.98, 'new_value': 42340.11}, {'field': 'total_amount', 'old_value': 77079.0, 'new_value': 80382.33}, {'field': 'order_count', 'old_value': 3977, 'new_value': 4162}]
2025-05-21 18:00:32,425 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-21 18:00:32,878 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-21 18:00:32,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7386.4, 'new_value': 7794.67}, {'field': 'offline_amount', 'old_value': 98344.34, 'new_value': 102196.21}, {'field': 'total_amount', 'old_value': 105730.74, 'new_value': 109990.88}, {'field': 'order_count', 'old_value': 1700, 'new_value': 1773}]
2025-05-21 18:00:32,878 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-21 18:00:33,300 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-21 18:00:33,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55465.81, 'new_value': 58092.49}, {'field': 'total_amount', 'old_value': 55465.81, 'new_value': 58092.49}, {'field': 'order_count', 'old_value': 2537, 'new_value': 2655}]
2025-05-21 18:00:33,300 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-21 18:00:33,769 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-21 18:00:33,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9425.5, 'new_value': 10274.8}, {'field': 'total_amount', 'old_value': 10135.5, 'new_value': 10984.8}, {'field': 'order_count', 'old_value': 29, 'new_value': 33}]
2025-05-21 18:00:33,769 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-21 18:00:34,300 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-21 18:00:34,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2033.66, 'new_value': 2131.42}, {'field': 'offline_amount', 'old_value': 18808.42, 'new_value': 19533.72}, {'field': 'total_amount', 'old_value': 20842.08, 'new_value': 21665.14}, {'field': 'order_count', 'old_value': 947, 'new_value': 983}]
2025-05-21 18:00:34,300 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-21 18:00:34,878 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-21 18:00:34,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1008000.0, 'new_value': 1018000.0}, {'field': 'total_amount', 'old_value': 1008000.0, 'new_value': 1018000.0}]
2025-05-21 18:00:34,878 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-21 18:00:35,394 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-21 18:00:35,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 690123.16, 'new_value': 710878.16}, {'field': 'total_amount', 'old_value': 690123.16, 'new_value': 710878.16}, {'field': 'order_count', 'old_value': 2698, 'new_value': 2816}]
2025-05-21 18:00:35,394 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-21 18:00:35,784 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-21 18:00:35,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23914.0, 'new_value': 24706.0}, {'field': 'offline_amount', 'old_value': 197097.9, 'new_value': 205245.4}, {'field': 'total_amount', 'old_value': 221011.9, 'new_value': 229951.4}, {'field': 'order_count', 'old_value': 1758, 'new_value': 1831}]
2025-05-21 18:00:35,784 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-21 18:00:36,284 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-21 18:00:36,284 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107245.08, 'new_value': 112497.08}, {'field': 'total_amount', 'old_value': 107245.08, 'new_value': 112497.08}, {'field': 'order_count', 'old_value': 9699, 'new_value': 10148}]
2025-05-21 18:00:36,284 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-21 18:00:36,815 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-21 18:00:36,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96957.1, 'new_value': 99540.1}, {'field': 'total_amount', 'old_value': 96957.1, 'new_value': 99540.1}, {'field': 'order_count', 'old_value': 990, 'new_value': 1009}]
2025-05-21 18:00:36,815 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-21 18:00:37,269 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-21 18:00:37,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48609.0, 'new_value': 50002.0}, {'field': 'total_amount', 'old_value': 48609.0, 'new_value': 50002.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-05-21 18:00:37,269 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-21 18:00:37,737 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-21 18:00:37,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71299.74, 'new_value': 75489.74}, {'field': 'total_amount', 'old_value': 71299.74, 'new_value': 75489.74}, {'field': 'order_count', 'old_value': 1957, 'new_value': 2009}]
2025-05-21 18:00:37,737 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-21 18:00:38,300 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-21 18:00:38,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95427.0, 'new_value': 94123.0}, {'field': 'total_amount', 'old_value': 95427.0, 'new_value': 94123.0}, {'field': 'order_count', 'old_value': 3368, 'new_value': 3482}]
2025-05-21 18:00:38,300 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-21 18:00:38,784 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-21 18:00:38,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24476.07, 'new_value': 24594.07}, {'field': 'total_amount', 'old_value': 24476.07, 'new_value': 24594.07}, {'field': 'order_count', 'old_value': 2297, 'new_value': 2392}]
2025-05-21 18:00:38,784 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-21 18:00:39,253 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-21 18:00:39,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34473.3, 'new_value': 35799.3}, {'field': 'offline_amount', 'old_value': 23549.64, 'new_value': 24518.64}, {'field': 'total_amount', 'old_value': 58022.94, 'new_value': 60317.94}, {'field': 'order_count', 'old_value': 7712, 'new_value': 8039}]
2025-05-21 18:00:39,253 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-21 18:00:39,737 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-21 18:00:39,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10759.0, 'new_value': 12559.0}, {'field': 'total_amount', 'old_value': 10759.0, 'new_value': 12559.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-21 18:00:39,737 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-21 18:00:40,300 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-21 18:00:40,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85109.72, 'new_value': 88018.57}, {'field': 'offline_amount', 'old_value': 245166.42, 'new_value': 262136.05}, {'field': 'total_amount', 'old_value': 330276.14, 'new_value': 350154.62}, {'field': 'order_count', 'old_value': 4022, 'new_value': 4256}]
2025-05-21 18:00:40,300 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-21 18:00:40,753 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-21 18:00:40,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5067100.0, 'new_value': 5092100.0}, {'field': 'total_amount', 'old_value': 5067100.0, 'new_value': 5092100.0}]
2025-05-21 18:00:40,753 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-21 18:00:41,222 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-21 18:00:41,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37191.5, 'new_value': 38732.5}, {'field': 'total_amount', 'old_value': 37191.5, 'new_value': 38732.5}, {'field': 'order_count', 'old_value': 1879, 'new_value': 1956}]
2025-05-21 18:00:41,222 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-21 18:00:41,659 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-21 18:00:41,659 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24342.2, 'new_value': 28346.72}, {'field': 'offline_amount', 'old_value': 188772.66, 'new_value': 201186.2}, {'field': 'total_amount', 'old_value': 213114.86, 'new_value': 229532.92}, {'field': 'order_count', 'old_value': 3340, 'new_value': 3704}]
2025-05-21 18:00:41,659 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-21 18:00:42,144 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-21 18:00:42,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32807.0, 'new_value': 34164.0}, {'field': 'total_amount', 'old_value': 32807.0, 'new_value': 34164.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 188}]
2025-05-21 18:00:42,144 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-21 18:00:42,644 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-21 18:00:42,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385439.0, 'new_value': 386019.0}, {'field': 'total_amount', 'old_value': 394257.99, 'new_value': 394837.99}, {'field': 'order_count', 'old_value': 71, 'new_value': 72}]
2025-05-21 18:00:42,644 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-21 18:00:43,144 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-21 18:00:43,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29948.0, 'new_value': 30877.0}, {'field': 'offline_amount', 'old_value': 219697.0, 'new_value': 233197.0}, {'field': 'total_amount', 'old_value': 249645.0, 'new_value': 264074.0}, {'field': 'order_count', 'old_value': 223, 'new_value': 239}]
2025-05-21 18:00:43,144 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-21 18:00:43,487 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-21 18:00:43,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235396.74, 'new_value': 250162.46}, {'field': 'total_amount', 'old_value': 268476.95, 'new_value': 283242.67}, {'field': 'order_count', 'old_value': 11292, 'new_value': 11914}]
2025-05-21 18:00:43,487 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-21 18:00:43,940 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-21 18:00:43,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336664.79, 'new_value': 341525.3}, {'field': 'total_amount', 'old_value': 336664.79, 'new_value': 341525.3}, {'field': 'order_count', 'old_value': 624, 'new_value': 653}]
2025-05-21 18:00:43,940 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-21 18:00:44,456 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-21 18:00:44,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29001.7, 'new_value': 35038.45}, {'field': 'offline_amount', 'old_value': 106795.14, 'new_value': 109095.38}, {'field': 'total_amount', 'old_value': 135796.84, 'new_value': 144133.83}, {'field': 'order_count', 'old_value': 7616, 'new_value': 8039}]
2025-05-21 18:00:44,456 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-21 18:00:44,925 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-21 18:00:44,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25918.0, 'new_value': 26033.0}, {'field': 'total_amount', 'old_value': 27294.0, 'new_value': 27409.0}, {'field': 'order_count', 'old_value': 2726, 'new_value': 2852}]
2025-05-21 18:00:44,925 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-21 18:00:45,362 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-21 18:00:45,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62390.0, 'new_value': 64990.0}, {'field': 'total_amount', 'old_value': 62390.0, 'new_value': 64990.0}, {'field': 'order_count', 'old_value': 443, 'new_value': 457}]
2025-05-21 18:00:45,362 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-21 18:00:45,831 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-21 18:00:45,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6338.87, 'new_value': 6579.81}, {'field': 'offline_amount', 'old_value': 98024.0, 'new_value': 102022.3}, {'field': 'total_amount', 'old_value': 104362.87, 'new_value': 108602.11}, {'field': 'order_count', 'old_value': 5637, 'new_value': 5822}]
2025-05-21 18:00:45,831 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-21 18:00:46,284 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-21 18:00:46,284 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13475.05, 'new_value': 16061.5}, {'field': 'offline_amount', 'old_value': 108402.41, 'new_value': 109024.71}, {'field': 'total_amount', 'old_value': 121877.46, 'new_value': 125086.21}, {'field': 'order_count', 'old_value': 3612, 'new_value': 3720}]
2025-05-21 18:00:46,284 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-21 18:00:46,784 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-21 18:00:46,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80570.5, 'new_value': 83241.3}, {'field': 'total_amount', 'old_value': 80570.5, 'new_value': 83241.3}, {'field': 'order_count', 'old_value': 3956, 'new_value': 4101}]
2025-05-21 18:00:46,784 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-21 18:00:47,222 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-21 18:00:47,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93862.49, 'new_value': 100641.07}, {'field': 'total_amount', 'old_value': 169934.93, 'new_value': 176713.51}, {'field': 'order_count', 'old_value': 7256, 'new_value': 7562}]
2025-05-21 18:00:47,222 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-21 18:00:47,690 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-21 18:00:47,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 708595.0, 'new_value': 744284.0}, {'field': 'total_amount', 'old_value': 708595.0, 'new_value': 744284.0}, {'field': 'order_count', 'old_value': 1525, 'new_value': 1600}]
2025-05-21 18:00:47,690 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-21 18:00:48,190 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-21 18:00:48,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65014.0, 'new_value': 69834.0}, {'field': 'total_amount', 'old_value': 65014.0, 'new_value': 69834.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-21 18:00:48,190 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-21 18:00:48,659 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-21 18:00:48,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 316611.43, 'new_value': 322619.1}, {'field': 'total_amount', 'old_value': 316611.43, 'new_value': 322619.1}, {'field': 'order_count', 'old_value': 2221, 'new_value': 2308}]
2025-05-21 18:00:48,659 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-21 18:00:49,190 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-21 18:00:49,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144214.78, 'new_value': 156033.21}, {'field': 'total_amount', 'old_value': 144214.78, 'new_value': 156033.21}, {'field': 'order_count', 'old_value': 1858, 'new_value': 1993}]
2025-05-21 18:00:49,190 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-21 18:00:49,644 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-21 18:00:49,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19174.0, 'new_value': 20462.0}, {'field': 'total_amount', 'old_value': 19174.0, 'new_value': 20462.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-21 18:00:49,644 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-21 18:00:50,097 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-21 18:00:50,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205082.46, 'new_value': 234342.46}, {'field': 'total_amount', 'old_value': 205082.46, 'new_value': 234342.46}, {'field': 'order_count', 'old_value': 37, 'new_value': 41}]
2025-05-21 18:00:50,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-21 18:00:50,550 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-21 18:00:50,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8063.67, 'new_value': 9038.67}, {'field': 'total_amount', 'old_value': 8063.67, 'new_value': 9038.67}, {'field': 'order_count', 'old_value': 242, 'new_value': 267}]
2025-05-21 18:00:50,550 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-21 18:00:51,065 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-21 18:00:51,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245668.5, 'new_value': 246417.5}, {'field': 'total_amount', 'old_value': 245668.5, 'new_value': 246417.5}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-21 18:00:51,065 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-21 18:00:51,550 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-21 18:00:51,550 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11372.0, 'new_value': 11872.0}, {'field': 'offline_amount', 'old_value': 6709.0, 'new_value': 7708.0}, {'field': 'total_amount', 'old_value': 18081.0, 'new_value': 19580.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 70}]
2025-05-21 18:00:51,550 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-21 18:00:51,956 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-21 18:00:51,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4395.0, 'new_value': 4794.0}, {'field': 'total_amount', 'old_value': 9993.0, 'new_value': 10392.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-21 18:00:51,956 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-21 18:00:52,409 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-21 18:00:52,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 228800.11, 'new_value': 241244.86}, {'field': 'offline_amount', 'old_value': 164560.83, 'new_value': 172210.21}, {'field': 'total_amount', 'old_value': 393360.94, 'new_value': 413455.07}, {'field': 'order_count', 'old_value': 15794, 'new_value': 16589}]
2025-05-21 18:00:52,409 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-21 18:00:52,894 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-21 18:00:52,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124508.16, 'new_value': 131095.16}, {'field': 'total_amount', 'old_value': 124508.16, 'new_value': 131095.16}, {'field': 'order_count', 'old_value': 1061, 'new_value': 1104}]
2025-05-21 18:00:52,909 - INFO - 日期 2025-05 处理完成 - 更新: 48 条，插入: 0 条，错误: 0 条
2025-05-21 18:00:52,909 - INFO - 数据同步完成！更新: 48 条，插入: 0 条，错误: 0 条
2025-05-21 18:00:52,909 - INFO - =================同步完成====================
2025-05-21 21:00:01,843 - INFO - =================使用默认全量同步=============
2025-05-21 21:00:03,234 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-21 21:00:03,234 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-21 21:00:03,265 - INFO - 开始处理日期: 2025-01
2025-05-21 21:00:03,265 - INFO - Request Parameters - Page 1:
2025-05-21 21:00:03,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:03,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:04,327 - INFO - Response - Page 1:
2025-05-21 21:00:04,531 - INFO - 第 1 页获取到 100 条记录
2025-05-21 21:00:04,531 - INFO - Request Parameters - Page 2:
2025-05-21 21:00:04,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:04,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:05,374 - INFO - Response - Page 2:
2025-05-21 21:00:05,577 - INFO - 第 2 页获取到 100 条记录
2025-05-21 21:00:05,577 - INFO - Request Parameters - Page 3:
2025-05-21 21:00:05,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:05,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:06,093 - INFO - Response - Page 3:
2025-05-21 21:00:06,296 - INFO - 第 3 页获取到 100 条记录
2025-05-21 21:00:06,296 - INFO - Request Parameters - Page 4:
2025-05-21 21:00:06,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:06,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:06,812 - INFO - Response - Page 4:
2025-05-21 21:00:07,015 - INFO - 第 4 页获取到 100 条记录
2025-05-21 21:00:07,015 - INFO - Request Parameters - Page 5:
2025-05-21 21:00:07,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:07,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:07,515 - INFO - Response - Page 5:
2025-05-21 21:00:07,718 - INFO - 第 5 页获取到 100 条记录
2025-05-21 21:00:07,718 - INFO - Request Parameters - Page 6:
2025-05-21 21:00:07,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:07,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:08,186 - INFO - Response - Page 6:
2025-05-21 21:00:08,389 - INFO - 第 6 页获取到 100 条记录
2025-05-21 21:00:08,389 - INFO - Request Parameters - Page 7:
2025-05-21 21:00:08,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:08,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:08,827 - INFO - Response - Page 7:
2025-05-21 21:00:09,030 - INFO - 第 7 页获取到 82 条记录
2025-05-21 21:00:09,030 - INFO - 查询完成，共获取到 682 条记录
2025-05-21 21:00:09,030 - INFO - 获取到 682 条表单数据
2025-05-21 21:00:09,030 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-21 21:00:09,046 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 21:00:09,046 - INFO - 开始处理日期: 2025-02
2025-05-21 21:00:09,046 - INFO - Request Parameters - Page 1:
2025-05-21 21:00:09,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:09,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:09,530 - INFO - Response - Page 1:
2025-05-21 21:00:09,733 - INFO - 第 1 页获取到 100 条记录
2025-05-21 21:00:09,733 - INFO - Request Parameters - Page 2:
2025-05-21 21:00:09,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:09,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:10,202 - INFO - Response - Page 2:
2025-05-21 21:00:10,405 - INFO - 第 2 页获取到 100 条记录
2025-05-21 21:00:10,405 - INFO - Request Parameters - Page 3:
2025-05-21 21:00:10,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:10,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:10,842 - INFO - Response - Page 3:
2025-05-21 21:00:11,045 - INFO - 第 3 页获取到 100 条记录
2025-05-21 21:00:11,045 - INFO - Request Parameters - Page 4:
2025-05-21 21:00:11,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:11,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:11,608 - INFO - Response - Page 4:
2025-05-21 21:00:11,811 - INFO - 第 4 页获取到 100 条记录
2025-05-21 21:00:11,811 - INFO - Request Parameters - Page 5:
2025-05-21 21:00:11,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:11,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:12,389 - INFO - Response - Page 5:
2025-05-21 21:00:12,592 - INFO - 第 5 页获取到 100 条记录
2025-05-21 21:00:12,592 - INFO - Request Parameters - Page 6:
2025-05-21 21:00:12,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:12,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:13,139 - INFO - Response - Page 6:
2025-05-21 21:00:13,342 - INFO - 第 6 页获取到 100 条记录
2025-05-21 21:00:13,342 - INFO - Request Parameters - Page 7:
2025-05-21 21:00:13,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:13,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:13,779 - INFO - Response - Page 7:
2025-05-21 21:00:13,983 - INFO - 第 7 页获取到 70 条记录
2025-05-21 21:00:13,983 - INFO - 查询完成，共获取到 670 条记录
2025-05-21 21:00:13,983 - INFO - 获取到 670 条表单数据
2025-05-21 21:00:13,983 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-21 21:00:13,998 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 21:00:13,998 - INFO - 开始处理日期: 2025-03
2025-05-21 21:00:13,998 - INFO - Request Parameters - Page 1:
2025-05-21 21:00:13,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:13,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:14,483 - INFO - Response - Page 1:
2025-05-21 21:00:14,686 - INFO - 第 1 页获取到 100 条记录
2025-05-21 21:00:14,686 - INFO - Request Parameters - Page 2:
2025-05-21 21:00:14,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:14,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:15,248 - INFO - Response - Page 2:
2025-05-21 21:00:15,451 - INFO - 第 2 页获取到 100 条记录
2025-05-21 21:00:15,451 - INFO - Request Parameters - Page 3:
2025-05-21 21:00:15,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:15,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:15,904 - INFO - Response - Page 3:
2025-05-21 21:00:16,107 - INFO - 第 3 页获取到 100 条记录
2025-05-21 21:00:16,107 - INFO - Request Parameters - Page 4:
2025-05-21 21:00:16,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:16,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:16,810 - INFO - Response - Page 4:
2025-05-21 21:00:17,013 - INFO - 第 4 页获取到 100 条记录
2025-05-21 21:00:17,013 - INFO - Request Parameters - Page 5:
2025-05-21 21:00:17,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:17,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:17,482 - INFO - Response - Page 5:
2025-05-21 21:00:17,685 - INFO - 第 5 页获取到 100 条记录
2025-05-21 21:00:17,685 - INFO - Request Parameters - Page 6:
2025-05-21 21:00:17,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:17,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:18,123 - INFO - Response - Page 6:
2025-05-21 21:00:18,326 - INFO - 第 6 页获取到 100 条记录
2025-05-21 21:00:18,326 - INFO - Request Parameters - Page 7:
2025-05-21 21:00:18,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:18,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:18,873 - INFO - Response - Page 7:
2025-05-21 21:00:19,076 - INFO - 第 7 页获取到 61 条记录
2025-05-21 21:00:19,076 - INFO - 查询完成，共获取到 661 条记录
2025-05-21 21:00:19,076 - INFO - 获取到 661 条表单数据
2025-05-21 21:00:19,076 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-21 21:00:19,091 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 21:00:19,091 - INFO - 开始处理日期: 2025-04
2025-05-21 21:00:19,091 - INFO - Request Parameters - Page 1:
2025-05-21 21:00:19,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:19,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:19,560 - INFO - Response - Page 1:
2025-05-21 21:00:19,763 - INFO - 第 1 页获取到 100 条记录
2025-05-21 21:00:19,763 - INFO - Request Parameters - Page 2:
2025-05-21 21:00:19,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:19,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:20,310 - INFO - Response - Page 2:
2025-05-21 21:00:20,513 - INFO - 第 2 页获取到 100 条记录
2025-05-21 21:00:20,513 - INFO - Request Parameters - Page 3:
2025-05-21 21:00:20,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:20,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:21,013 - INFO - Response - Page 3:
2025-05-21 21:00:21,216 - INFO - 第 3 页获取到 100 条记录
2025-05-21 21:00:21,216 - INFO - Request Parameters - Page 4:
2025-05-21 21:00:21,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:21,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:21,685 - INFO - Response - Page 4:
2025-05-21 21:00:21,888 - INFO - 第 4 页获取到 100 条记录
2025-05-21 21:00:21,888 - INFO - Request Parameters - Page 5:
2025-05-21 21:00:21,888 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:21,888 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:22,325 - INFO - Response - Page 5:
2025-05-21 21:00:22,528 - INFO - 第 5 页获取到 100 条记录
2025-05-21 21:00:22,528 - INFO - Request Parameters - Page 6:
2025-05-21 21:00:22,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:22,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:23,060 - INFO - Response - Page 6:
2025-05-21 21:00:23,263 - INFO - 第 6 页获取到 100 条记录
2025-05-21 21:00:23,263 - INFO - Request Parameters - Page 7:
2025-05-21 21:00:23,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:23,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:23,731 - INFO - Response - Page 7:
2025-05-21 21:00:23,935 - INFO - 第 7 页获取到 56 条记录
2025-05-21 21:00:23,935 - INFO - 查询完成，共获取到 656 条记录
2025-05-21 21:00:23,935 - INFO - 获取到 656 条表单数据
2025-05-21 21:00:23,935 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-21 21:00:23,950 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 21:00:23,950 - INFO - 开始处理日期: 2025-05
2025-05-21 21:00:23,950 - INFO - Request Parameters - Page 1:
2025-05-21 21:00:23,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:23,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:24,638 - INFO - Response - Page 1:
2025-05-21 21:00:24,841 - INFO - 第 1 页获取到 100 条记录
2025-05-21 21:00:24,841 - INFO - Request Parameters - Page 2:
2025-05-21 21:00:24,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:24,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:25,387 - INFO - Response - Page 2:
2025-05-21 21:00:25,591 - INFO - 第 2 页获取到 100 条记录
2025-05-21 21:00:25,591 - INFO - Request Parameters - Page 3:
2025-05-21 21:00:25,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:25,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:26,075 - INFO - Response - Page 3:
2025-05-21 21:00:26,278 - INFO - 第 3 页获取到 100 条记录
2025-05-21 21:00:26,278 - INFO - Request Parameters - Page 4:
2025-05-21 21:00:26,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:26,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:26,809 - INFO - Response - Page 4:
2025-05-21 21:00:27,012 - INFO - 第 4 页获取到 100 条记录
2025-05-21 21:00:27,012 - INFO - Request Parameters - Page 5:
2025-05-21 21:00:27,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:27,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:27,512 - INFO - Response - Page 5:
2025-05-21 21:00:27,715 - INFO - 第 5 页获取到 100 条记录
2025-05-21 21:00:27,715 - INFO - Request Parameters - Page 6:
2025-05-21 21:00:27,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:27,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:28,262 - INFO - Response - Page 6:
2025-05-21 21:00:28,465 - INFO - 第 6 页获取到 100 条记录
2025-05-21 21:00:28,465 - INFO - Request Parameters - Page 7:
2025-05-21 21:00:28,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:00:28,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:00:28,840 - INFO - Response - Page 7:
2025-05-21 21:00:29,043 - INFO - 第 7 页获取到 28 条记录
2025-05-21 21:00:29,043 - INFO - 查询完成，共获取到 628 条记录
2025-05-21 21:00:29,043 - INFO - 获取到 628 条表单数据
2025-05-21 21:00:29,043 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-21 21:00:29,043 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-21 21:00:29,590 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-21 21:00:29,590 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64717.0, 'new_value': 68004.0}, {'field': 'offline_amount', 'old_value': 85547.0, 'new_value': 89624.0}, {'field': 'total_amount', 'old_value': 150264.0, 'new_value': 157628.0}, {'field': 'order_count', 'old_value': 3518, 'new_value': 3678}]
2025-05-21 21:00:29,606 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-21 21:00:29,606 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-21 21:00:29,606 - INFO - =================同步完成====================
