2025-05-14 00:00:03,603 - INFO - =================使用默认全量同步=============
2025-05-14 00:00:04,979 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 00:00:04,979 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 00:00:05,010 - INFO - 开始处理日期: 2025-01
2025-05-14 00:00:05,010 - INFO - Request Parameters - Page 1:
2025-05-14 00:00:05,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:05,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:05,980 - INFO - Response - Page 1:
2025-05-14 00:00:06,183 - INFO - 第 1 页获取到 100 条记录
2025-05-14 00:00:06,183 - INFO - Request Parameters - Page 2:
2025-05-14 00:00:06,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:06,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:07,106 - INFO - Response - Page 2:
2025-05-14 00:00:07,309 - INFO - 第 2 页获取到 100 条记录
2025-05-14 00:00:07,309 - INFO - Request Parameters - Page 3:
2025-05-14 00:00:07,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:07,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:07,809 - INFO - Response - Page 3:
2025-05-14 00:00:08,013 - INFO - 第 3 页获取到 100 条记录
2025-05-14 00:00:08,013 - INFO - Request Parameters - Page 4:
2025-05-14 00:00:08,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:08,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:08,466 - INFO - Response - Page 4:
2025-05-14 00:00:08,669 - INFO - 第 4 页获取到 100 条记录
2025-05-14 00:00:08,669 - INFO - Request Parameters - Page 5:
2025-05-14 00:00:08,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:08,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:09,154 - INFO - Response - Page 5:
2025-05-14 00:00:09,357 - INFO - 第 5 页获取到 100 条记录
2025-05-14 00:00:09,357 - INFO - Request Parameters - Page 6:
2025-05-14 00:00:09,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:09,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:09,873 - INFO - Response - Page 6:
2025-05-14 00:00:10,077 - INFO - 第 6 页获取到 100 条记录
2025-05-14 00:00:10,077 - INFO - Request Parameters - Page 7:
2025-05-14 00:00:10,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:10,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:10,546 - INFO - Response - Page 7:
2025-05-14 00:00:10,749 - INFO - 第 7 页获取到 82 条记录
2025-05-14 00:00:10,749 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 00:00:10,749 - INFO - 获取到 682 条表单数据
2025-05-14 00:00:10,749 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 00:00:10,765 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 00:00:10,765 - INFO - 开始处理日期: 2025-02
2025-05-14 00:00:10,765 - INFO - Request Parameters - Page 1:
2025-05-14 00:00:10,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:10,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:11,281 - INFO - Response - Page 1:
2025-05-14 00:00:11,484 - INFO - 第 1 页获取到 100 条记录
2025-05-14 00:00:11,484 - INFO - Request Parameters - Page 2:
2025-05-14 00:00:11,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:11,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:12,141 - INFO - Response - Page 2:
2025-05-14 00:00:12,344 - INFO - 第 2 页获取到 100 条记录
2025-05-14 00:00:12,344 - INFO - Request Parameters - Page 3:
2025-05-14 00:00:12,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:12,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:12,923 - INFO - Response - Page 3:
2025-05-14 00:00:13,126 - INFO - 第 3 页获取到 100 条记录
2025-05-14 00:00:13,126 - INFO - Request Parameters - Page 4:
2025-05-14 00:00:13,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:13,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:13,579 - INFO - Response - Page 4:
2025-05-14 00:00:13,783 - INFO - 第 4 页获取到 100 条记录
2025-05-14 00:00:13,783 - INFO - Request Parameters - Page 5:
2025-05-14 00:00:13,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:13,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:14,283 - INFO - Response - Page 5:
2025-05-14 00:00:14,486 - INFO - 第 5 页获取到 100 条记录
2025-05-14 00:00:14,486 - INFO - Request Parameters - Page 6:
2025-05-14 00:00:14,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:14,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:14,955 - INFO - Response - Page 6:
2025-05-14 00:00:15,159 - INFO - 第 6 页获取到 100 条记录
2025-05-14 00:00:15,159 - INFO - Request Parameters - Page 7:
2025-05-14 00:00:15,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:15,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:15,550 - INFO - Response - Page 7:
2025-05-14 00:00:15,753 - INFO - 第 7 页获取到 70 条记录
2025-05-14 00:00:15,753 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 00:00:15,753 - INFO - 获取到 670 条表单数据
2025-05-14 00:00:15,753 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 00:00:15,769 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 00:00:15,769 - INFO - 开始处理日期: 2025-03
2025-05-14 00:00:15,769 - INFO - Request Parameters - Page 1:
2025-05-14 00:00:15,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:15,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:16,238 - INFO - Response - Page 1:
2025-05-14 00:00:16,441 - INFO - 第 1 页获取到 100 条记录
2025-05-14 00:00:16,441 - INFO - Request Parameters - Page 2:
2025-05-14 00:00:16,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:16,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:16,879 - INFO - Response - Page 2:
2025-05-14 00:00:17,082 - INFO - 第 2 页获取到 100 条记录
2025-05-14 00:00:17,082 - INFO - Request Parameters - Page 3:
2025-05-14 00:00:17,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:17,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:17,598 - INFO - Response - Page 3:
2025-05-14 00:00:17,801 - INFO - 第 3 页获取到 100 条记录
2025-05-14 00:00:17,801 - INFO - Request Parameters - Page 4:
2025-05-14 00:00:17,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:17,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:18,271 - INFO - Response - Page 4:
2025-05-14 00:00:18,474 - INFO - 第 4 页获取到 100 条记录
2025-05-14 00:00:18,474 - INFO - Request Parameters - Page 5:
2025-05-14 00:00:18,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:18,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:18,959 - INFO - Response - Page 5:
2025-05-14 00:00:19,162 - INFO - 第 5 页获取到 100 条记录
2025-05-14 00:00:19,162 - INFO - Request Parameters - Page 6:
2025-05-14 00:00:19,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:19,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:19,600 - INFO - Response - Page 6:
2025-05-14 00:00:19,803 - INFO - 第 6 页获取到 100 条记录
2025-05-14 00:00:19,803 - INFO - Request Parameters - Page 7:
2025-05-14 00:00:19,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:19,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:20,225 - INFO - Response - Page 7:
2025-05-14 00:00:20,428 - INFO - 第 7 页获取到 61 条记录
2025-05-14 00:00:20,428 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 00:00:20,428 - INFO - 获取到 661 条表单数据
2025-05-14 00:00:20,428 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 00:00:20,444 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 00:00:20,444 - INFO - 开始处理日期: 2025-04
2025-05-14 00:00:20,444 - INFO - Request Parameters - Page 1:
2025-05-14 00:00:20,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:20,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:20,929 - INFO - Response - Page 1:
2025-05-14 00:00:21,132 - INFO - 第 1 页获取到 100 条记录
2025-05-14 00:00:21,132 - INFO - Request Parameters - Page 2:
2025-05-14 00:00:21,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:21,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:21,632 - INFO - Response - Page 2:
2025-05-14 00:00:21,836 - INFO - 第 2 页获取到 100 条记录
2025-05-14 00:00:21,836 - INFO - Request Parameters - Page 3:
2025-05-14 00:00:21,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:21,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:22,367 - INFO - Response - Page 3:
2025-05-14 00:00:22,571 - INFO - 第 3 页获取到 100 条记录
2025-05-14 00:00:22,571 - INFO - Request Parameters - Page 4:
2025-05-14 00:00:22,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:22,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:23,055 - INFO - Response - Page 4:
2025-05-14 00:00:23,259 - INFO - 第 4 页获取到 100 条记录
2025-05-14 00:00:23,259 - INFO - Request Parameters - Page 5:
2025-05-14 00:00:23,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:23,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:23,697 - INFO - Response - Page 5:
2025-05-14 00:00:23,900 - INFO - 第 5 页获取到 100 条记录
2025-05-14 00:00:23,900 - INFO - Request Parameters - Page 6:
2025-05-14 00:00:23,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:23,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:24,353 - INFO - Response - Page 6:
2025-05-14 00:00:24,557 - INFO - 第 6 页获取到 100 条记录
2025-05-14 00:00:24,557 - INFO - Request Parameters - Page 7:
2025-05-14 00:00:24,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:24,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:24,994 - INFO - Response - Page 7:
2025-05-14 00:00:25,198 - INFO - 第 7 页获取到 54 条记录
2025-05-14 00:00:25,198 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 00:00:25,198 - INFO - 获取到 654 条表单数据
2025-05-14 00:00:25,198 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 00:00:25,213 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 00:00:25,213 - INFO - 开始处理日期: 2025-05
2025-05-14 00:00:25,213 - INFO - Request Parameters - Page 1:
2025-05-14 00:00:25,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:25,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:25,651 - INFO - Response - Page 1:
2025-05-14 00:00:25,854 - INFO - 第 1 页获取到 100 条记录
2025-05-14 00:00:25,854 - INFO - Request Parameters - Page 2:
2025-05-14 00:00:25,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:25,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:26,417 - INFO - Response - Page 2:
2025-05-14 00:00:26,621 - INFO - 第 2 页获取到 100 条记录
2025-05-14 00:00:26,621 - INFO - Request Parameters - Page 3:
2025-05-14 00:00:26,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:26,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:27,090 - INFO - Response - Page 3:
2025-05-14 00:00:27,293 - INFO - 第 3 页获取到 100 条记录
2025-05-14 00:00:27,293 - INFO - Request Parameters - Page 4:
2025-05-14 00:00:27,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:27,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:27,825 - INFO - Response - Page 4:
2025-05-14 00:00:28,028 - INFO - 第 4 页获取到 100 条记录
2025-05-14 00:00:28,028 - INFO - Request Parameters - Page 5:
2025-05-14 00:00:28,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:28,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:28,544 - INFO - Response - Page 5:
2025-05-14 00:00:28,747 - INFO - 第 5 页获取到 100 条记录
2025-05-14 00:00:28,747 - INFO - Request Parameters - Page 6:
2025-05-14 00:00:28,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:28,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:29,373 - INFO - Response - Page 6:
2025-05-14 00:00:29,576 - INFO - 第 6 页获取到 100 条记录
2025-05-14 00:00:29,576 - INFO - Request Parameters - Page 7:
2025-05-14 00:00:29,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 00:00:29,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 00:00:29,904 - INFO - Response - Page 7:
2025-05-14 00:00:30,108 - INFO - 第 7 页获取到 25 条记录
2025-05-14 00:00:30,108 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 00:00:30,108 - INFO - 获取到 625 条表单数据
2025-05-14 00:00:30,108 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 00:00:30,108 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-14 00:00:30,514 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-14 00:00:30,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22380.0, 'new_value': 29657.0}, {'field': 'total_amount', 'old_value': 22380.0, 'new_value': 29657.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-14 00:00:30,514 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-14 00:00:30,936 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-14 00:00:30,936 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62675.0, 'new_value': 66425.0}, {'field': 'offline_amount', 'old_value': 59511.28, 'new_value': 67205.28}, {'field': 'total_amount', 'old_value': 122186.28, 'new_value': 133630.28}, {'field': 'order_count', 'old_value': 2592, 'new_value': 2844}]
2025-05-14 00:00:30,936 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-14 00:00:31,296 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-14 00:00:31,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 860.9, 'new_value': 1030.9}, {'field': 'total_amount', 'old_value': 25681.7, 'new_value': 25851.7}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-14 00:00:31,296 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-14 00:00:31,797 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-14 00:00:31,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 520386.0, 'new_value': 600050.0}, {'field': 'offline_amount', 'old_value': 154533.0, 'new_value': 174494.0}, {'field': 'total_amount', 'old_value': 674919.0, 'new_value': 774544.0}, {'field': 'order_count', 'old_value': 715, 'new_value': 861}]
2025-05-14 00:00:31,797 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-14 00:00:32,234 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-14 00:00:32,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143750.0, 'new_value': 154350.0}, {'field': 'total_amount', 'old_value': 143750.0, 'new_value': 154350.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 25}]
2025-05-14 00:00:32,234 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-14 00:00:32,657 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-14 00:00:32,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185745.89, 'new_value': 201535.89}, {'field': 'total_amount', 'old_value': 185745.89, 'new_value': 201535.89}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-14 00:00:32,657 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-14 00:00:33,110 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-14 00:00:33,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151690.13, 'new_value': 156190.13}, {'field': 'total_amount', 'old_value': 191050.13, 'new_value': 195550.13}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-14 00:00:33,110 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-14 00:00:33,501 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-14 00:00:33,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15527.14, 'new_value': 18532.77}, {'field': 'total_amount', 'old_value': 15527.14, 'new_value': 18532.77}, {'field': 'order_count', 'old_value': 1207, 'new_value': 1415}]
2025-05-14 00:00:33,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-14 00:00:33,970 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-14 00:00:33,970 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2344.0, 'new_value': 3576.0}, {'field': 'offline_amount', 'old_value': 20067.0, 'new_value': 24824.0}, {'field': 'total_amount', 'old_value': 22411.0, 'new_value': 28400.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 119}]
2025-05-14 00:00:33,970 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-14 00:00:34,408 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-14 00:00:34,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37978.88, 'new_value': 42448.38}, {'field': 'total_amount', 'old_value': 37978.88, 'new_value': 42448.38}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-05-14 00:00:34,408 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-14 00:00:34,830 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-14 00:00:34,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2045.0, 'new_value': 3225.0}, {'field': 'total_amount', 'old_value': 2045.0, 'new_value': 3225.0}, {'field': 'order_count', 'old_value': 294, 'new_value': 296}]
2025-05-14 00:00:34,830 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-14 00:00:35,268 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-14 00:00:35,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23174.6, 'new_value': 26826.5}, {'field': 'total_amount', 'old_value': 23176.6, 'new_value': 26828.5}, {'field': 'order_count', 'old_value': 132, 'new_value': 152}]
2025-05-14 00:00:35,268 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-14 00:00:35,674 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-14 00:00:35,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24345.21, 'new_value': 25921.2}, {'field': 'offline_amount', 'old_value': 332404.12, 'new_value': 358397.34}, {'field': 'total_amount', 'old_value': 356749.33, 'new_value': 384318.54}, {'field': 'order_count', 'old_value': 1451, 'new_value': 1560}]
2025-05-14 00:00:35,674 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-14 00:00:36,065 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-14 00:00:36,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38364.0, 'new_value': 44573.1}, {'field': 'offline_amount', 'old_value': 20350.54, 'new_value': 24529.51}, {'field': 'total_amount', 'old_value': 58714.54, 'new_value': 69102.61}, {'field': 'order_count', 'old_value': 1959, 'new_value': 2313}]
2025-05-14 00:00:36,065 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-14 00:00:36,425 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-14 00:00:36,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20013.0, 'new_value': 27116.0}, {'field': 'total_amount', 'old_value': 20013.0, 'new_value': 27116.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 30}]
2025-05-14 00:00:36,425 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-14 00:00:36,800 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-14 00:00:36,800 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66143.0, 'new_value': 89054.0}, {'field': 'offline_amount', 'old_value': 37604.0, 'new_value': 37643.0}, {'field': 'total_amount', 'old_value': 103747.0, 'new_value': 126697.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 47}]
2025-05-14 00:00:36,800 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-14 00:00:37,223 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-14 00:00:37,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1355.62, 'new_value': 1688.84}, {'field': 'offline_amount', 'old_value': 68752.66, 'new_value': 76499.97}, {'field': 'total_amount', 'old_value': 70108.28, 'new_value': 78188.81}, {'field': 'order_count', 'old_value': 301, 'new_value': 352}]
2025-05-14 00:00:37,223 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-14 00:00:37,660 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-14 00:00:37,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91139.0, 'new_value': 91520.0}, {'field': 'total_amount', 'old_value': 98714.8, 'new_value': 99095.8}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-14 00:00:37,660 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-14 00:00:38,130 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-14 00:00:38,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59137.4, 'new_value': 60765.2}, {'field': 'total_amount', 'old_value': 59137.4, 'new_value': 60765.2}, {'field': 'order_count', 'old_value': 176, 'new_value': 183}]
2025-05-14 00:00:38,130 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-14 00:00:38,536 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-14 00:00:38,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22966.0, 'new_value': 23157.0}, {'field': 'total_amount', 'old_value': 22966.0, 'new_value': 23157.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-05-14 00:00:38,536 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-14 00:00:39,036 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-14 00:00:39,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 957510.58, 'new_value': 1012239.65}, {'field': 'total_amount', 'old_value': 957510.58, 'new_value': 1012239.65}, {'field': 'order_count', 'old_value': 7174, 'new_value': 7692}]
2025-05-14 00:00:39,036 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-14 00:00:39,506 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-14 00:00:39,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54180.0, 'new_value': 52497.7}, {'field': 'offline_amount', 'old_value': 64560.63, 'new_value': 67728.33}, {'field': 'total_amount', 'old_value': 118740.63, 'new_value': 120226.03}, {'field': 'order_count', 'old_value': 827, 'new_value': 821}]
2025-05-14 00:00:39,506 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-14 00:00:39,943 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-14 00:00:39,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42645.61, 'new_value': 50427.63}, {'field': 'offline_amount', 'old_value': 31263.82, 'new_value': 37755.5}, {'field': 'total_amount', 'old_value': 73909.43, 'new_value': 88183.13}, {'field': 'order_count', 'old_value': 2980, 'new_value': 3601}]
2025-05-14 00:00:39,943 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-14 00:00:40,413 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-14 00:00:40,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4559.45, 'new_value': 4845.03}, {'field': 'offline_amount', 'old_value': 47636.9, 'new_value': 49470.87}, {'field': 'total_amount', 'old_value': 52196.35, 'new_value': 54315.9}, {'field': 'order_count', 'old_value': 1547, 'new_value': 1603}]
2025-05-14 00:00:40,413 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-14 00:00:40,960 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-14 00:00:40,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48710.0, 'new_value': 54830.0}, {'field': 'total_amount', 'old_value': 48710.0, 'new_value': 54830.0}, {'field': 'order_count', 'old_value': 2530, 'new_value': 2845}]
2025-05-14 00:00:40,960 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-14 00:00:41,335 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-14 00:00:41,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3638.42, 'new_value': 3953.93}, {'field': 'offline_amount', 'old_value': 7481.74, 'new_value': 7715.64}, {'field': 'total_amount', 'old_value': 11120.16, 'new_value': 11669.57}, {'field': 'order_count', 'old_value': 824, 'new_value': 868}]
2025-05-14 00:00:41,335 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-14 00:00:41,695 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-14 00:00:41,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133171.78, 'new_value': 151416.56}, {'field': 'total_amount', 'old_value': 193693.88, 'new_value': 211938.66}, {'field': 'order_count', 'old_value': 2224, 'new_value': 2453}]
2025-05-14 00:00:41,695 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-14 00:00:42,179 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-14 00:00:42,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48998.6, 'new_value': 51029.8}, {'field': 'total_amount', 'old_value': 48998.6, 'new_value': 51029.8}, {'field': 'order_count', 'old_value': 367, 'new_value': 384}]
2025-05-14 00:00:42,179 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-14 00:00:42,617 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-14 00:00:42,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 700.0, 'new_value': 850.0}, {'field': 'offline_amount', 'old_value': 12286.7, 'new_value': 13185.5}, {'field': 'total_amount', 'old_value': 12986.7, 'new_value': 14035.5}, {'field': 'order_count', 'old_value': 256, 'new_value': 270}]
2025-05-14 00:00:42,617 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-14 00:00:43,180 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-14 00:00:43,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43082.73, 'new_value': 45851.13}, {'field': 'offline_amount', 'old_value': 76708.71, 'new_value': 77564.71}, {'field': 'total_amount', 'old_value': 119791.44, 'new_value': 123415.84}, {'field': 'order_count', 'old_value': 1160, 'new_value': 1204}]
2025-05-14 00:00:43,180 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-14 00:00:43,649 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-14 00:00:43,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4148.0, 'new_value': 5594.0}, {'field': 'total_amount', 'old_value': 4148.0, 'new_value': 5594.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 39}]
2025-05-14 00:00:43,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-14 00:00:44,056 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-14 00:00:44,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17891.0, 'new_value': 18460.0}, {'field': 'total_amount', 'old_value': 17891.0, 'new_value': 18460.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 78}]
2025-05-14 00:00:44,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-14 00:00:44,525 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-14 00:00:44,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263159.12, 'new_value': 273603.02}, {'field': 'total_amount', 'old_value': 263159.12, 'new_value': 273603.02}, {'field': 'order_count', 'old_value': 3481, 'new_value': 3642}]
2025-05-14 00:00:44,525 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-14 00:00:44,947 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-14 00:00:44,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49854.25, 'new_value': 51718.25}, {'field': 'total_amount', 'old_value': 49854.25, 'new_value': 51718.25}, {'field': 'order_count', 'old_value': 1503, 'new_value': 1558}]
2025-05-14 00:00:44,947 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-14 00:00:45,369 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-14 00:00:45,369 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18275.63, 'new_value': 19089.39}, {'field': 'offline_amount', 'old_value': 189546.44, 'new_value': 195254.79}, {'field': 'total_amount', 'old_value': 207822.07, 'new_value': 214344.18}, {'field': 'order_count', 'old_value': 4841, 'new_value': 4997}]
2025-05-14 00:00:45,369 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-14 00:00:45,792 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-14 00:00:45,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257132.9, 'new_value': 268505.7}, {'field': 'total_amount', 'old_value': 257132.9, 'new_value': 268505.7}, {'field': 'order_count', 'old_value': 1279, 'new_value': 1323}]
2025-05-14 00:00:45,807 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-14 00:00:46,229 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-14 00:00:46,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82037.0, 'new_value': 84875.14}, {'field': 'offline_amount', 'old_value': 29733.34, 'new_value': 30807.51}, {'field': 'total_amount', 'old_value': 111770.34, 'new_value': 115682.65}, {'field': 'order_count', 'old_value': 7005, 'new_value': 7264}]
2025-05-14 00:00:46,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-14 00:00:46,589 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-14 00:00:46,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420169.74, 'new_value': 431958.4}, {'field': 'total_amount', 'old_value': 420169.74, 'new_value': 431958.4}, {'field': 'order_count', 'old_value': 8510, 'new_value': 8669}]
2025-05-14 00:00:46,589 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-14 00:00:46,980 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-14 00:00:46,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38184.53, 'new_value': 44390.49}, {'field': 'offline_amount', 'old_value': 103698.25, 'new_value': 123382.17}, {'field': 'total_amount', 'old_value': 141882.78, 'new_value': 167772.66}, {'field': 'order_count', 'old_value': 6469, 'new_value': 7650}]
2025-05-14 00:00:46,980 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-14 00:00:47,512 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-14 00:00:47,512 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145742.88, 'new_value': 154441.42}, {'field': 'total_amount', 'old_value': 145742.88, 'new_value': 154441.42}, {'field': 'order_count', 'old_value': 6029, 'new_value': 6409}]
2025-05-14 00:00:47,512 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-14 00:00:47,996 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-14 00:00:47,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129193.0, 'new_value': 133153.0}, {'field': 'total_amount', 'old_value': 129193.0, 'new_value': 133153.0}, {'field': 'order_count', 'old_value': 382, 'new_value': 394}]
2025-05-14 00:00:47,996 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-14 00:00:48,419 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-14 00:00:48,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382018.01, 'new_value': 401221.29}, {'field': 'total_amount', 'old_value': 382018.01, 'new_value': 401221.29}, {'field': 'order_count', 'old_value': 2547, 'new_value': 2695}]
2025-05-14 00:00:48,419 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-14 00:00:48,810 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-14 00:00:48,810 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34544.71, 'new_value': 36592.49}, {'field': 'offline_amount', 'old_value': 48891.32, 'new_value': 50685.22}, {'field': 'total_amount', 'old_value': 83436.03, 'new_value': 87277.71}, {'field': 'order_count', 'old_value': 3828, 'new_value': 4023}]
2025-05-14 00:00:48,810 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-14 00:00:49,185 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-14 00:00:49,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13008.77, 'new_value': 14194.56}, {'field': 'offline_amount', 'old_value': 136078.94, 'new_value': 140222.74}, {'field': 'total_amount', 'old_value': 149087.71, 'new_value': 154417.3}, {'field': 'order_count', 'old_value': 4561, 'new_value': 4747}]
2025-05-14 00:00:49,185 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-14 00:00:49,670 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-14 00:00:49,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18093.2, 'new_value': 19658.2}, {'field': 'total_amount', 'old_value': 18093.2, 'new_value': 19658.2}, {'field': 'order_count', 'old_value': 103, 'new_value': 112}]
2025-05-14 00:00:49,685 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-14 00:00:50,029 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-14 00:00:50,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127604.0, 'new_value': 145283.0}, {'field': 'total_amount', 'old_value': 131704.0, 'new_value': 149383.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 103}]
2025-05-14 00:00:50,029 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-14 00:00:50,592 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-14 00:00:50,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72595.37, 'new_value': 75653.07}, {'field': 'total_amount', 'old_value': 91634.97, 'new_value': 94692.67}, {'field': 'order_count', 'old_value': 2613, 'new_value': 2709}]
2025-05-14 00:00:50,592 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-14 00:00:51,124 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-14 00:00:51,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-14 00:00:51,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-14 00:00:51,562 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-14 00:00:51,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18072.0, 'new_value': 19380.0}, {'field': 'total_amount', 'old_value': 18072.0, 'new_value': 19380.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 96}]
2025-05-14 00:00:51,562 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-14 00:00:52,000 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-14 00:00:52,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8970.0, 'new_value': 9570.0}, {'field': 'total_amount', 'old_value': 8970.0, 'new_value': 9570.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-14 00:00:52,000 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-14 00:00:52,375 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-14 00:00:52,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20237.39, 'new_value': 22015.88}, {'field': 'offline_amount', 'old_value': 15181.24, 'new_value': 15628.45}, {'field': 'total_amount', 'old_value': 35418.63, 'new_value': 37644.33}, {'field': 'order_count', 'old_value': 1528, 'new_value': 1628}]
2025-05-14 00:00:52,375 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-14 00:00:52,750 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-14 00:00:52,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8373.6, 'new_value': 10047.7}, {'field': 'offline_amount', 'old_value': 81371.2, 'new_value': 95108.0}, {'field': 'total_amount', 'old_value': 89744.8, 'new_value': 105155.7}, {'field': 'order_count', 'old_value': 2752, 'new_value': 3182}]
2025-05-14 00:00:52,750 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-14 00:00:53,219 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-14 00:00:53,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293157.47, 'new_value': 317607.47}, {'field': 'total_amount', 'old_value': 293157.47, 'new_value': 317607.47}, {'field': 'order_count', 'old_value': 28555, 'new_value': 4106}]
2025-05-14 00:00:53,219 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-14 00:00:53,641 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-14 00:00:53,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194564.0, 'new_value': 197444.0}, {'field': 'total_amount', 'old_value': 195560.0, 'new_value': 198440.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-05-14 00:00:53,641 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-14 00:00:54,064 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-14 00:00:54,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 781.56, 'new_value': 1144.36}, {'field': 'offline_amount', 'old_value': 6124.03, 'new_value': 9755.88}, {'field': 'total_amount', 'old_value': 6905.59, 'new_value': 10900.24}, {'field': 'order_count', 'old_value': 297, 'new_value': 455}]
2025-05-14 00:00:54,064 - INFO - 日期 2025-05 处理完成 - 更新: 55 条，插入: 0 条，错误: 0 条
2025-05-14 00:00:54,064 - INFO - 数据同步完成！更新: 55 条，插入: 0 条，错误: 0 条
2025-05-14 00:00:54,064 - INFO - =================同步完成====================
2025-05-14 03:00:04,427 - INFO - =================使用默认全量同步=============
2025-05-14 03:00:05,756 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 03:00:05,756 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 03:00:05,787 - INFO - 开始处理日期: 2025-01
2025-05-14 03:00:05,787 - INFO - Request Parameters - Page 1:
2025-05-14 03:00:05,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:05,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:06,819 - INFO - Response - Page 1:
2025-05-14 03:00:07,022 - INFO - 第 1 页获取到 100 条记录
2025-05-14 03:00:07,022 - INFO - Request Parameters - Page 2:
2025-05-14 03:00:07,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:07,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:07,804 - INFO - Response - Page 2:
2025-05-14 03:00:08,007 - INFO - 第 2 页获取到 100 条记录
2025-05-14 03:00:08,007 - INFO - Request Parameters - Page 3:
2025-05-14 03:00:08,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:08,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:08,492 - INFO - Response - Page 3:
2025-05-14 03:00:08,695 - INFO - 第 3 页获取到 100 条记录
2025-05-14 03:00:08,695 - INFO - Request Parameters - Page 4:
2025-05-14 03:00:08,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:08,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:09,227 - INFO - Response - Page 4:
2025-05-14 03:00:09,430 - INFO - 第 4 页获取到 100 条记录
2025-05-14 03:00:09,430 - INFO - Request Parameters - Page 5:
2025-05-14 03:00:09,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:09,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:10,025 - INFO - Response - Page 5:
2025-05-14 03:00:10,228 - INFO - 第 5 页获取到 100 条记录
2025-05-14 03:00:10,228 - INFO - Request Parameters - Page 6:
2025-05-14 03:00:10,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:10,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:10,713 - INFO - Response - Page 6:
2025-05-14 03:00:10,916 - INFO - 第 6 页获取到 100 条记录
2025-05-14 03:00:10,916 - INFO - Request Parameters - Page 7:
2025-05-14 03:00:10,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:10,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:11,448 - INFO - Response - Page 7:
2025-05-14 03:00:11,651 - INFO - 第 7 页获取到 82 条记录
2025-05-14 03:00:11,651 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 03:00:11,651 - INFO - 获取到 682 条表单数据
2025-05-14 03:00:11,651 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 03:00:11,666 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 03:00:11,666 - INFO - 开始处理日期: 2025-02
2025-05-14 03:00:11,666 - INFO - Request Parameters - Page 1:
2025-05-14 03:00:11,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:11,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:12,182 - INFO - Response - Page 1:
2025-05-14 03:00:12,386 - INFO - 第 1 页获取到 100 条记录
2025-05-14 03:00:12,386 - INFO - Request Parameters - Page 2:
2025-05-14 03:00:12,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:12,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:12,917 - INFO - Response - Page 2:
2025-05-14 03:00:13,121 - INFO - 第 2 页获取到 100 条记录
2025-05-14 03:00:13,121 - INFO - Request Parameters - Page 3:
2025-05-14 03:00:13,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:13,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:13,668 - INFO - Response - Page 3:
2025-05-14 03:00:13,871 - INFO - 第 3 页获取到 100 条记录
2025-05-14 03:00:13,871 - INFO - Request Parameters - Page 4:
2025-05-14 03:00:13,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:13,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:14,340 - INFO - Response - Page 4:
2025-05-14 03:00:14,544 - INFO - 第 4 页获取到 100 条记录
2025-05-14 03:00:14,544 - INFO - Request Parameters - Page 5:
2025-05-14 03:00:14,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:14,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:15,044 - INFO - Response - Page 5:
2025-05-14 03:00:15,247 - INFO - 第 5 页获取到 100 条记录
2025-05-14 03:00:15,247 - INFO - Request Parameters - Page 6:
2025-05-14 03:00:15,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:15,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:15,716 - INFO - Response - Page 6:
2025-05-14 03:00:15,920 - INFO - 第 6 页获取到 100 条记录
2025-05-14 03:00:15,920 - INFO - Request Parameters - Page 7:
2025-05-14 03:00:15,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:15,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:16,389 - INFO - Response - Page 7:
2025-05-14 03:00:16,592 - INFO - 第 7 页获取到 70 条记录
2025-05-14 03:00:16,592 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 03:00:16,592 - INFO - 获取到 670 条表单数据
2025-05-14 03:00:16,592 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 03:00:16,608 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 03:00:16,608 - INFO - 开始处理日期: 2025-03
2025-05-14 03:00:16,608 - INFO - Request Parameters - Page 1:
2025-05-14 03:00:16,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:16,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:17,108 - INFO - Response - Page 1:
2025-05-14 03:00:17,311 - INFO - 第 1 页获取到 100 条记录
2025-05-14 03:00:17,311 - INFO - Request Parameters - Page 2:
2025-05-14 03:00:17,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:17,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:17,890 - INFO - Response - Page 2:
2025-05-14 03:00:18,093 - INFO - 第 2 页获取到 100 条记录
2025-05-14 03:00:18,093 - INFO - Request Parameters - Page 3:
2025-05-14 03:00:18,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:18,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:18,609 - INFO - Response - Page 3:
2025-05-14 03:00:18,813 - INFO - 第 3 页获取到 100 条记录
2025-05-14 03:00:18,813 - INFO - Request Parameters - Page 4:
2025-05-14 03:00:18,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:18,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:19,266 - INFO - Response - Page 4:
2025-05-14 03:00:19,469 - INFO - 第 4 页获取到 100 条记录
2025-05-14 03:00:19,469 - INFO - Request Parameters - Page 5:
2025-05-14 03:00:19,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:19,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:19,970 - INFO - Response - Page 5:
2025-05-14 03:00:20,173 - INFO - 第 5 页获取到 100 条记录
2025-05-14 03:00:20,173 - INFO - Request Parameters - Page 6:
2025-05-14 03:00:20,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:20,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:20,767 - INFO - Response - Page 6:
2025-05-14 03:00:20,970 - INFO - 第 6 页获取到 100 条记录
2025-05-14 03:00:20,970 - INFO - Request Parameters - Page 7:
2025-05-14 03:00:20,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:20,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:21,361 - INFO - Response - Page 7:
2025-05-14 03:00:21,565 - INFO - 第 7 页获取到 61 条记录
2025-05-14 03:00:21,565 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 03:00:21,565 - INFO - 获取到 661 条表单数据
2025-05-14 03:00:21,565 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 03:00:21,580 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 03:00:21,580 - INFO - 开始处理日期: 2025-04
2025-05-14 03:00:21,580 - INFO - Request Parameters - Page 1:
2025-05-14 03:00:21,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:21,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:22,128 - INFO - Response - Page 1:
2025-05-14 03:00:22,331 - INFO - 第 1 页获取到 100 条记录
2025-05-14 03:00:22,331 - INFO - Request Parameters - Page 2:
2025-05-14 03:00:22,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:22,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:22,769 - INFO - Response - Page 2:
2025-05-14 03:00:22,972 - INFO - 第 2 页获取到 100 条记录
2025-05-14 03:00:22,972 - INFO - Request Parameters - Page 3:
2025-05-14 03:00:22,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:22,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:23,425 - INFO - Response - Page 3:
2025-05-14 03:00:23,629 - INFO - 第 3 页获取到 100 条记录
2025-05-14 03:00:23,629 - INFO - Request Parameters - Page 4:
2025-05-14 03:00:23,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:23,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:24,114 - INFO - Response - Page 4:
2025-05-14 03:00:24,317 - INFO - 第 4 页获取到 100 条记录
2025-05-14 03:00:24,317 - INFO - Request Parameters - Page 5:
2025-05-14 03:00:24,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:24,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:24,864 - INFO - Response - Page 5:
2025-05-14 03:00:25,067 - INFO - 第 5 页获取到 100 条记录
2025-05-14 03:00:25,067 - INFO - Request Parameters - Page 6:
2025-05-14 03:00:25,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:25,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:25,599 - INFO - Response - Page 6:
2025-05-14 03:00:25,802 - INFO - 第 6 页获取到 100 条记录
2025-05-14 03:00:25,802 - INFO - Request Parameters - Page 7:
2025-05-14 03:00:25,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:25,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:26,287 - INFO - Response - Page 7:
2025-05-14 03:00:26,490 - INFO - 第 7 页获取到 54 条记录
2025-05-14 03:00:26,490 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 03:00:26,490 - INFO - 获取到 654 条表单数据
2025-05-14 03:00:26,490 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 03:00:26,506 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 03:00:26,506 - INFO - 开始处理日期: 2025-05
2025-05-14 03:00:26,506 - INFO - Request Parameters - Page 1:
2025-05-14 03:00:26,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:26,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:26,991 - INFO - Response - Page 1:
2025-05-14 03:00:27,194 - INFO - 第 1 页获取到 100 条记录
2025-05-14 03:00:27,194 - INFO - Request Parameters - Page 2:
2025-05-14 03:00:27,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:27,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:27,647 - INFO - Response - Page 2:
2025-05-14 03:00:27,851 - INFO - 第 2 页获取到 100 条记录
2025-05-14 03:00:27,851 - INFO - Request Parameters - Page 3:
2025-05-14 03:00:27,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:27,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:28,336 - INFO - Response - Page 3:
2025-05-14 03:00:28,539 - INFO - 第 3 页获取到 100 条记录
2025-05-14 03:00:28,539 - INFO - Request Parameters - Page 4:
2025-05-14 03:00:28,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:28,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:29,133 - INFO - Response - Page 4:
2025-05-14 03:00:29,336 - INFO - 第 4 页获取到 100 条记录
2025-05-14 03:00:29,336 - INFO - Request Parameters - Page 5:
2025-05-14 03:00:29,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:29,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:29,774 - INFO - Response - Page 5:
2025-05-14 03:00:29,977 - INFO - 第 5 页获取到 100 条记录
2025-05-14 03:00:29,977 - INFO - Request Parameters - Page 6:
2025-05-14 03:00:29,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:29,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:30,478 - INFO - Response - Page 6:
2025-05-14 03:00:30,681 - INFO - 第 6 页获取到 100 条记录
2025-05-14 03:00:30,681 - INFO - Request Parameters - Page 7:
2025-05-14 03:00:30,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 03:00:30,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 03:00:31,009 - INFO - Response - Page 7:
2025-05-14 03:00:31,213 - INFO - 第 7 页获取到 25 条记录
2025-05-14 03:00:31,213 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 03:00:31,213 - INFO - 获取到 625 条表单数据
2025-05-14 03:00:31,213 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 03:00:31,213 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-14 03:00:31,604 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-14 03:00:31,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10671.2, 'new_value': 12189.2}, {'field': 'offline_amount', 'old_value': 55145.66, 'new_value': 69197.66}, {'field': 'total_amount', 'old_value': 65816.86, 'new_value': 81386.86}, {'field': 'order_count', 'old_value': 124, 'new_value': 135}]
2025-05-14 03:00:31,604 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-14 03:00:31,604 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-14 03:00:31,604 - INFO - =================同步完成====================
2025-05-14 06:00:03,560 - INFO - =================使用默认全量同步=============
2025-05-14 06:00:04,905 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 06:00:04,905 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 06:00:04,936 - INFO - 开始处理日期: 2025-01
2025-05-14 06:00:04,936 - INFO - Request Parameters - Page 1:
2025-05-14 06:00:04,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:04,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:06,000 - INFO - Response - Page 1:
2025-05-14 06:00:06,203 - INFO - 第 1 页获取到 100 条记录
2025-05-14 06:00:06,203 - INFO - Request Parameters - Page 2:
2025-05-14 06:00:06,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:06,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:07,032 - INFO - Response - Page 2:
2025-05-14 06:00:07,235 - INFO - 第 2 页获取到 100 条记录
2025-05-14 06:00:07,235 - INFO - Request Parameters - Page 3:
2025-05-14 06:00:07,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:07,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:07,736 - INFO - Response - Page 3:
2025-05-14 06:00:07,939 - INFO - 第 3 页获取到 100 条记录
2025-05-14 06:00:07,939 - INFO - Request Parameters - Page 4:
2025-05-14 06:00:07,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:07,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:08,377 - INFO - Response - Page 4:
2025-05-14 06:00:08,580 - INFO - 第 4 页获取到 100 条记录
2025-05-14 06:00:08,580 - INFO - Request Parameters - Page 5:
2025-05-14 06:00:08,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:08,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:09,033 - INFO - Response - Page 5:
2025-05-14 06:00:09,237 - INFO - 第 5 页获取到 100 条记录
2025-05-14 06:00:09,237 - INFO - Request Parameters - Page 6:
2025-05-14 06:00:09,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:09,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:09,706 - INFO - Response - Page 6:
2025-05-14 06:00:09,909 - INFO - 第 6 页获取到 100 条记录
2025-05-14 06:00:09,909 - INFO - Request Parameters - Page 7:
2025-05-14 06:00:09,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:09,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:10,378 - INFO - Response - Page 7:
2025-05-14 06:00:10,581 - INFO - 第 7 页获取到 82 条记录
2025-05-14 06:00:10,581 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 06:00:10,581 - INFO - 获取到 682 条表单数据
2025-05-14 06:00:10,581 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 06:00:10,597 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 06:00:10,597 - INFO - 开始处理日期: 2025-02
2025-05-14 06:00:10,597 - INFO - Request Parameters - Page 1:
2025-05-14 06:00:10,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:10,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:11,051 - INFO - Response - Page 1:
2025-05-14 06:00:11,254 - INFO - 第 1 页获取到 100 条记录
2025-05-14 06:00:11,254 - INFO - Request Parameters - Page 2:
2025-05-14 06:00:11,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:11,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:11,707 - INFO - Response - Page 2:
2025-05-14 06:00:11,911 - INFO - 第 2 页获取到 100 条记录
2025-05-14 06:00:11,911 - INFO - Request Parameters - Page 3:
2025-05-14 06:00:11,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:11,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:12,380 - INFO - Response - Page 3:
2025-05-14 06:00:12,583 - INFO - 第 3 页获取到 100 条记录
2025-05-14 06:00:12,583 - INFO - Request Parameters - Page 4:
2025-05-14 06:00:12,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:12,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:13,115 - INFO - Response - Page 4:
2025-05-14 06:00:13,318 - INFO - 第 4 页获取到 100 条记录
2025-05-14 06:00:13,318 - INFO - Request Parameters - Page 5:
2025-05-14 06:00:13,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:13,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:13,803 - INFO - Response - Page 5:
2025-05-14 06:00:14,006 - INFO - 第 5 页获取到 100 条记录
2025-05-14 06:00:14,006 - INFO - Request Parameters - Page 6:
2025-05-14 06:00:14,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:14,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:14,538 - INFO - Response - Page 6:
2025-05-14 06:00:14,741 - INFO - 第 6 页获取到 100 条记录
2025-05-14 06:00:14,741 - INFO - Request Parameters - Page 7:
2025-05-14 06:00:14,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:14,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:15,210 - INFO - Response - Page 7:
2025-05-14 06:00:15,413 - INFO - 第 7 页获取到 70 条记录
2025-05-14 06:00:15,413 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 06:00:15,413 - INFO - 获取到 670 条表单数据
2025-05-14 06:00:15,413 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 06:00:15,429 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 06:00:15,429 - INFO - 开始处理日期: 2025-03
2025-05-14 06:00:15,429 - INFO - Request Parameters - Page 1:
2025-05-14 06:00:15,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:15,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:15,914 - INFO - Response - Page 1:
2025-05-14 06:00:16,117 - INFO - 第 1 页获取到 100 条记录
2025-05-14 06:00:16,117 - INFO - Request Parameters - Page 2:
2025-05-14 06:00:16,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:16,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:16,570 - INFO - Response - Page 2:
2025-05-14 06:00:16,774 - INFO - 第 2 页获取到 100 条记录
2025-05-14 06:00:16,774 - INFO - Request Parameters - Page 3:
2025-05-14 06:00:16,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:16,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:17,227 - INFO - Response - Page 3:
2025-05-14 06:00:17,430 - INFO - 第 3 页获取到 100 条记录
2025-05-14 06:00:17,430 - INFO - Request Parameters - Page 4:
2025-05-14 06:00:17,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:17,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:17,915 - INFO - Response - Page 4:
2025-05-14 06:00:18,118 - INFO - 第 4 页获取到 100 条记录
2025-05-14 06:00:18,118 - INFO - Request Parameters - Page 5:
2025-05-14 06:00:18,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:18,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:18,572 - INFO - Response - Page 5:
2025-05-14 06:00:18,775 - INFO - 第 5 页获取到 100 条记录
2025-05-14 06:00:18,775 - INFO - Request Parameters - Page 6:
2025-05-14 06:00:18,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:18,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:19,229 - INFO - Response - Page 6:
2025-05-14 06:00:19,448 - INFO - 第 6 页获取到 100 条记录
2025-05-14 06:00:19,448 - INFO - Request Parameters - Page 7:
2025-05-14 06:00:19,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:19,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:19,854 - INFO - Response - Page 7:
2025-05-14 06:00:20,057 - INFO - 第 7 页获取到 61 条记录
2025-05-14 06:00:20,057 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 06:00:20,057 - INFO - 获取到 661 条表单数据
2025-05-14 06:00:20,057 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 06:00:20,073 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 06:00:20,073 - INFO - 开始处理日期: 2025-04
2025-05-14 06:00:20,073 - INFO - Request Parameters - Page 1:
2025-05-14 06:00:20,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:20,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:20,636 - INFO - Response - Page 1:
2025-05-14 06:00:20,839 - INFO - 第 1 页获取到 100 条记录
2025-05-14 06:00:20,839 - INFO - Request Parameters - Page 2:
2025-05-14 06:00:20,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:20,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:21,355 - INFO - Response - Page 2:
2025-05-14 06:00:21,559 - INFO - 第 2 页获取到 100 条记录
2025-05-14 06:00:21,559 - INFO - Request Parameters - Page 3:
2025-05-14 06:00:21,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:21,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:22,012 - INFO - Response - Page 3:
2025-05-14 06:00:22,215 - INFO - 第 3 页获取到 100 条记录
2025-05-14 06:00:22,215 - INFO - Request Parameters - Page 4:
2025-05-14 06:00:22,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:22,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:22,653 - INFO - Response - Page 4:
2025-05-14 06:00:22,857 - INFO - 第 4 页获取到 100 条记录
2025-05-14 06:00:22,857 - INFO - Request Parameters - Page 5:
2025-05-14 06:00:22,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:22,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:23,373 - INFO - Response - Page 5:
2025-05-14 06:00:23,576 - INFO - 第 5 页获取到 100 条记录
2025-05-14 06:00:23,576 - INFO - Request Parameters - Page 6:
2025-05-14 06:00:23,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:23,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:24,045 - INFO - Response - Page 6:
2025-05-14 06:00:24,248 - INFO - 第 6 页获取到 100 条记录
2025-05-14 06:00:24,248 - INFO - Request Parameters - Page 7:
2025-05-14 06:00:24,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:24,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:24,764 - INFO - Response - Page 7:
2025-05-14 06:00:24,967 - INFO - 第 7 页获取到 54 条记录
2025-05-14 06:00:24,967 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 06:00:24,967 - INFO - 获取到 654 条表单数据
2025-05-14 06:00:24,967 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 06:00:24,983 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 06:00:24,983 - INFO - 开始处理日期: 2025-05
2025-05-14 06:00:24,983 - INFO - Request Parameters - Page 1:
2025-05-14 06:00:24,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:24,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:25,577 - INFO - Response - Page 1:
2025-05-14 06:00:25,781 - INFO - 第 1 页获取到 100 条记录
2025-05-14 06:00:25,781 - INFO - Request Parameters - Page 2:
2025-05-14 06:00:25,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:25,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:26,297 - INFO - Response - Page 2:
2025-05-14 06:00:26,500 - INFO - 第 2 页获取到 100 条记录
2025-05-14 06:00:26,500 - INFO - Request Parameters - Page 3:
2025-05-14 06:00:26,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:26,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:26,953 - INFO - Response - Page 3:
2025-05-14 06:00:27,157 - INFO - 第 3 页获取到 100 条记录
2025-05-14 06:00:27,157 - INFO - Request Parameters - Page 4:
2025-05-14 06:00:27,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:27,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:27,626 - INFO - Response - Page 4:
2025-05-14 06:00:27,829 - INFO - 第 4 页获取到 100 条记录
2025-05-14 06:00:27,829 - INFO - Request Parameters - Page 5:
2025-05-14 06:00:27,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:27,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:28,298 - INFO - Response - Page 5:
2025-05-14 06:00:28,501 - INFO - 第 5 页获取到 100 条记录
2025-05-14 06:00:28,501 - INFO - Request Parameters - Page 6:
2025-05-14 06:00:28,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:28,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:29,002 - INFO - Response - Page 6:
2025-05-14 06:00:29,205 - INFO - 第 6 页获取到 100 条记录
2025-05-14 06:00:29,205 - INFO - Request Parameters - Page 7:
2025-05-14 06:00:29,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 06:00:29,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 06:00:29,518 - INFO - Response - Page 7:
2025-05-14 06:00:29,721 - INFO - 第 7 页获取到 25 条记录
2025-05-14 06:00:29,721 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 06:00:29,721 - INFO - 获取到 625 条表单数据
2025-05-14 06:00:29,721 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 06:00:29,737 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 06:00:29,737 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 06:00:29,737 - INFO - =================同步完成====================
2025-05-14 09:00:01,897 - INFO - =================使用默认全量同步=============
2025-05-14 09:00:03,272 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 09:00:03,272 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 09:00:03,303 - INFO - 开始处理日期: 2025-01
2025-05-14 09:00:03,303 - INFO - Request Parameters - Page 1:
2025-05-14 09:00:03,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:03,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:04,725 - INFO - Response - Page 1:
2025-05-14 09:00:04,928 - INFO - 第 1 页获取到 100 条记录
2025-05-14 09:00:04,928 - INFO - Request Parameters - Page 2:
2025-05-14 09:00:04,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:04,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:05,475 - INFO - Response - Page 2:
2025-05-14 09:00:05,678 - INFO - 第 2 页获取到 100 条记录
2025-05-14 09:00:05,678 - INFO - Request Parameters - Page 3:
2025-05-14 09:00:05,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:05,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:06,178 - INFO - Response - Page 3:
2025-05-14 09:00:06,382 - INFO - 第 3 页获取到 100 条记录
2025-05-14 09:00:06,382 - INFO - Request Parameters - Page 4:
2025-05-14 09:00:06,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:06,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:06,882 - INFO - Response - Page 4:
2025-05-14 09:00:07,085 - INFO - 第 4 页获取到 100 条记录
2025-05-14 09:00:07,085 - INFO - Request Parameters - Page 5:
2025-05-14 09:00:07,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:07,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:07,569 - INFO - Response - Page 5:
2025-05-14 09:00:07,772 - INFO - 第 5 页获取到 100 条记录
2025-05-14 09:00:07,772 - INFO - Request Parameters - Page 6:
2025-05-14 09:00:07,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:07,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:08,303 - INFO - Response - Page 6:
2025-05-14 09:00:08,507 - INFO - 第 6 页获取到 100 条记录
2025-05-14 09:00:08,507 - INFO - Request Parameters - Page 7:
2025-05-14 09:00:08,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:08,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:08,960 - INFO - Response - Page 7:
2025-05-14 09:00:09,163 - INFO - 第 7 页获取到 82 条记录
2025-05-14 09:00:09,163 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 09:00:09,163 - INFO - 获取到 682 条表单数据
2025-05-14 09:00:09,163 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 09:00:09,178 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 09:00:09,178 - INFO - 开始处理日期: 2025-02
2025-05-14 09:00:09,178 - INFO - Request Parameters - Page 1:
2025-05-14 09:00:09,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:09,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:09,647 - INFO - Response - Page 1:
2025-05-14 09:00:09,850 - INFO - 第 1 页获取到 100 条记录
2025-05-14 09:00:09,850 - INFO - Request Parameters - Page 2:
2025-05-14 09:00:09,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:09,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:10,335 - INFO - Response - Page 2:
2025-05-14 09:00:10,538 - INFO - 第 2 页获取到 100 条记录
2025-05-14 09:00:10,538 - INFO - Request Parameters - Page 3:
2025-05-14 09:00:10,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:10,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:11,006 - INFO - Response - Page 3:
2025-05-14 09:00:11,225 - INFO - 第 3 页获取到 100 条记录
2025-05-14 09:00:11,225 - INFO - Request Parameters - Page 4:
2025-05-14 09:00:11,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:11,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:11,647 - INFO - Response - Page 4:
2025-05-14 09:00:11,850 - INFO - 第 4 页获取到 100 条记录
2025-05-14 09:00:11,850 - INFO - Request Parameters - Page 5:
2025-05-14 09:00:11,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:11,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:12,397 - INFO - Response - Page 5:
2025-05-14 09:00:12,600 - INFO - 第 5 页获取到 100 条记录
2025-05-14 09:00:12,600 - INFO - Request Parameters - Page 6:
2025-05-14 09:00:12,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:12,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:13,116 - INFO - Response - Page 6:
2025-05-14 09:00:13,319 - INFO - 第 6 页获取到 100 条记录
2025-05-14 09:00:13,319 - INFO - Request Parameters - Page 7:
2025-05-14 09:00:13,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:13,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:13,788 - INFO - Response - Page 7:
2025-05-14 09:00:13,991 - INFO - 第 7 页获取到 70 条记录
2025-05-14 09:00:13,991 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 09:00:13,991 - INFO - 获取到 670 条表单数据
2025-05-14 09:00:13,991 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 09:00:14,006 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 09:00:14,006 - INFO - 开始处理日期: 2025-03
2025-05-14 09:00:14,006 - INFO - Request Parameters - Page 1:
2025-05-14 09:00:14,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:14,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:14,538 - INFO - Response - Page 1:
2025-05-14 09:00:14,741 - INFO - 第 1 页获取到 100 条记录
2025-05-14 09:00:14,741 - INFO - Request Parameters - Page 2:
2025-05-14 09:00:14,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:14,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:15,241 - INFO - Response - Page 2:
2025-05-14 09:00:15,444 - INFO - 第 2 页获取到 100 条记录
2025-05-14 09:00:15,444 - INFO - Request Parameters - Page 3:
2025-05-14 09:00:15,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:15,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:15,960 - INFO - Response - Page 3:
2025-05-14 09:00:16,163 - INFO - 第 3 页获取到 100 条记录
2025-05-14 09:00:16,163 - INFO - Request Parameters - Page 4:
2025-05-14 09:00:16,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:16,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:16,663 - INFO - Response - Page 4:
2025-05-14 09:00:16,866 - INFO - 第 4 页获取到 100 条记录
2025-05-14 09:00:16,866 - INFO - Request Parameters - Page 5:
2025-05-14 09:00:16,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:16,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:17,303 - INFO - Response - Page 5:
2025-05-14 09:00:17,506 - INFO - 第 5 页获取到 100 条记录
2025-05-14 09:00:17,506 - INFO - Request Parameters - Page 6:
2025-05-14 09:00:17,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:17,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:17,960 - INFO - Response - Page 6:
2025-05-14 09:00:18,163 - INFO - 第 6 页获取到 100 条记录
2025-05-14 09:00:18,163 - INFO - Request Parameters - Page 7:
2025-05-14 09:00:18,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:18,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:18,585 - INFO - Response - Page 7:
2025-05-14 09:00:18,788 - INFO - 第 7 页获取到 61 条记录
2025-05-14 09:00:18,788 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 09:00:18,788 - INFO - 获取到 661 条表单数据
2025-05-14 09:00:18,788 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 09:00:18,803 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 09:00:18,803 - INFO - 开始处理日期: 2025-04
2025-05-14 09:00:18,803 - INFO - Request Parameters - Page 1:
2025-05-14 09:00:18,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:18,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:19,319 - INFO - Response - Page 1:
2025-05-14 09:00:19,522 - INFO - 第 1 页获取到 100 条记录
2025-05-14 09:00:19,522 - INFO - Request Parameters - Page 2:
2025-05-14 09:00:19,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:19,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:20,053 - INFO - Response - Page 2:
2025-05-14 09:00:20,256 - INFO - 第 2 页获取到 100 条记录
2025-05-14 09:00:20,256 - INFO - Request Parameters - Page 3:
2025-05-14 09:00:20,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:20,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:20,756 - INFO - Response - Page 3:
2025-05-14 09:00:20,960 - INFO - 第 3 页获取到 100 条记录
2025-05-14 09:00:20,960 - INFO - Request Parameters - Page 4:
2025-05-14 09:00:20,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:20,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:21,444 - INFO - Response - Page 4:
2025-05-14 09:00:21,647 - INFO - 第 4 页获取到 100 条记录
2025-05-14 09:00:21,647 - INFO - Request Parameters - Page 5:
2025-05-14 09:00:21,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:21,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:22,178 - INFO - Response - Page 5:
2025-05-14 09:00:22,381 - INFO - 第 5 页获取到 100 条记录
2025-05-14 09:00:22,381 - INFO - Request Parameters - Page 6:
2025-05-14 09:00:22,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:22,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:22,881 - INFO - Response - Page 6:
2025-05-14 09:00:23,085 - INFO - 第 6 页获取到 100 条记录
2025-05-14 09:00:23,085 - INFO - Request Parameters - Page 7:
2025-05-14 09:00:23,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:23,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:23,506 - INFO - Response - Page 7:
2025-05-14 09:00:23,710 - INFO - 第 7 页获取到 54 条记录
2025-05-14 09:00:23,710 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 09:00:23,710 - INFO - 获取到 654 条表单数据
2025-05-14 09:00:23,710 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 09:00:23,725 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 09:00:23,725 - INFO - 开始处理日期: 2025-05
2025-05-14 09:00:23,725 - INFO - Request Parameters - Page 1:
2025-05-14 09:00:23,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:23,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:24,256 - INFO - Response - Page 1:
2025-05-14 09:00:24,460 - INFO - 第 1 页获取到 100 条记录
2025-05-14 09:00:24,460 - INFO - Request Parameters - Page 2:
2025-05-14 09:00:24,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:24,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:24,928 - INFO - Response - Page 2:
2025-05-14 09:00:25,131 - INFO - 第 2 页获取到 100 条记录
2025-05-14 09:00:25,131 - INFO - Request Parameters - Page 3:
2025-05-14 09:00:25,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:25,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:25,585 - INFO - Response - Page 3:
2025-05-14 09:00:25,803 - INFO - 第 3 页获取到 100 条记录
2025-05-14 09:00:25,803 - INFO - Request Parameters - Page 4:
2025-05-14 09:00:25,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:25,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:26,288 - INFO - Response - Page 4:
2025-05-14 09:00:26,491 - INFO - 第 4 页获取到 100 条记录
2025-05-14 09:00:26,491 - INFO - Request Parameters - Page 5:
2025-05-14 09:00:26,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:26,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:26,928 - INFO - Response - Page 5:
2025-05-14 09:00:27,131 - INFO - 第 5 页获取到 100 条记录
2025-05-14 09:00:27,131 - INFO - Request Parameters - Page 6:
2025-05-14 09:00:27,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:27,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:27,647 - INFO - Response - Page 6:
2025-05-14 09:00:27,850 - INFO - 第 6 页获取到 100 条记录
2025-05-14 09:00:27,850 - INFO - Request Parameters - Page 7:
2025-05-14 09:00:27,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 09:00:27,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 09:00:28,210 - INFO - Response - Page 7:
2025-05-14 09:00:28,413 - INFO - 第 7 页获取到 25 条记录
2025-05-14 09:00:28,413 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 09:00:28,413 - INFO - 获取到 625 条表单数据
2025-05-14 09:00:28,413 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 09:00:28,413 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-14 09:00:28,881 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-14 09:00:28,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82311.4, 'new_value': 91149.17}, {'field': 'total_amount', 'old_value': 82311.4, 'new_value': 91149.17}, {'field': 'order_count', 'old_value': 3476, 'new_value': 3789}]
2025-05-14 09:00:28,881 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-14 09:00:29,335 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-14 09:00:29,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176772.0, 'new_value': 215067.48}, {'field': 'total_amount', 'old_value': 176772.0, 'new_value': 215067.48}, {'field': 'order_count', 'old_value': 197, 'new_value': 242}]
2025-05-14 09:00:29,335 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-14 09:00:29,788 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-14 09:00:29,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3439.0, 'new_value': 6319.0}, {'field': 'total_amount', 'old_value': 3439.0, 'new_value': 6319.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-14 09:00:29,788 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-14 09:00:30,381 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-14 09:00:30,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 411020.2, 'new_value': 429073.6}, {'field': 'total_amount', 'old_value': 412277.6, 'new_value': 430331.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-05-14 09:00:30,381 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-14 09:00:30,881 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-14 09:00:30,881 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 117, 'new_value': 121}]
2025-05-14 09:00:30,881 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-14 09:00:31,413 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-14 09:00:31,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37464.0, 'new_value': 39680.0}, {'field': 'total_amount', 'old_value': 37464.0, 'new_value': 39680.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 340}]
2025-05-14 09:00:31,413 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-14 09:00:31,850 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-14 09:00:31,850 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1050.0, 'new_value': 1100.0}, {'field': 'offline_amount', 'old_value': 15507.0, 'new_value': 16681.0}, {'field': 'total_amount', 'old_value': 16557.0, 'new_value': 17781.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 66}]
2025-05-14 09:00:31,850 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-14 09:00:32,288 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-14 09:00:32,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3145.02, 'new_value': 3219.94}, {'field': 'offline_amount', 'old_value': 57202.77, 'new_value': 58428.97}, {'field': 'total_amount', 'old_value': 60347.79, 'new_value': 61648.91}, {'field': 'order_count', 'old_value': 1400, 'new_value': 1441}]
2025-05-14 09:00:32,288 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-14 09:00:32,709 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-14 09:00:32,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25417.96, 'new_value': 25427.86}, {'field': 'total_amount', 'old_value': 25417.96, 'new_value': 25427.86}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-14 09:00:32,709 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-14 09:00:33,147 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-14 09:00:33,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3419.32, 'new_value': 3807.37}, {'field': 'offline_amount', 'old_value': 92241.02, 'new_value': 99641.03}, {'field': 'total_amount', 'old_value': 95660.34, 'new_value': 103448.4}, {'field': 'order_count', 'old_value': 654, 'new_value': 702}]
2025-05-14 09:00:33,147 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-14 09:00:33,553 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-14 09:00:33,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182722.0, 'new_value': 186421.0}, {'field': 'total_amount', 'old_value': 182722.0, 'new_value': 186421.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-14 09:00:33,553 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-14 09:00:34,038 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-14 09:00:34,038 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37001.45, 'new_value': 41700.69}, {'field': 'offline_amount', 'old_value': 563272.04, 'new_value': 603573.52}, {'field': 'total_amount', 'old_value': 600273.49, 'new_value': 645274.21}, {'field': 'order_count', 'old_value': 4661, 'new_value': 5073}]
2025-05-14 09:00:34,038 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-14 09:00:34,506 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-14 09:00:34,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46112.98, 'new_value': 47908.98}, {'field': 'total_amount', 'old_value': 46112.98, 'new_value': 47908.98}, {'field': 'order_count', 'old_value': 251, 'new_value': 260}]
2025-05-14 09:00:34,506 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-14 09:00:34,944 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-14 09:00:34,944 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15492.4, 'new_value': 16362.2}, {'field': 'offline_amount', 'old_value': 12506.7, 'new_value': 13085.7}, {'field': 'total_amount', 'old_value': 27999.1, 'new_value': 29447.9}, {'field': 'order_count', 'old_value': 151, 'new_value': 158}]
2025-05-14 09:00:34,944 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-14 09:00:35,413 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-14 09:00:35,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7616.8, 'new_value': 8200.3}, {'field': 'offline_amount', 'old_value': 129414.34, 'new_value': 138391.94}, {'field': 'total_amount', 'old_value': 137031.14, 'new_value': 146592.24}, {'field': 'order_count', 'old_value': 7259, 'new_value': 7857}]
2025-05-14 09:00:35,413 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-14 09:00:36,006 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-14 09:00:36,006 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25407.49, 'new_value': 27628.1}, {'field': 'offline_amount', 'old_value': 15536.0, 'new_value': 17118.0}, {'field': 'total_amount', 'old_value': 40943.49, 'new_value': 44746.1}, {'field': 'order_count', 'old_value': 524, 'new_value': 567}]
2025-05-14 09:00:36,006 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-14 09:00:36,475 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-14 09:00:36,475 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24384.82, 'new_value': 26352.02}, {'field': 'offline_amount', 'old_value': 173826.39, 'new_value': 185119.6}, {'field': 'total_amount', 'old_value': 198211.21, 'new_value': 211471.62}, {'field': 'order_count', 'old_value': 1225, 'new_value': 1309}]
2025-05-14 09:00:36,475 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-14 09:00:36,913 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-14 09:00:36,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99068.5, 'new_value': 107113.55}, {'field': 'total_amount', 'old_value': 99068.5, 'new_value': 107113.55}, {'field': 'order_count', 'old_value': 542, 'new_value': 587}]
2025-05-14 09:00:36,913 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-14 09:00:37,397 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-14 09:00:37,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53993.0, 'new_value': 55105.0}, {'field': 'total_amount', 'old_value': 53993.0, 'new_value': 55105.0}, {'field': 'order_count', 'old_value': 1602, 'new_value': 1635}]
2025-05-14 09:00:37,397 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-14 09:00:37,772 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-14 09:00:37,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13281.0, 'new_value': 13976.0}, {'field': 'total_amount', 'old_value': 13281.0, 'new_value': 13976.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 74}]
2025-05-14 09:00:37,772 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-14 09:00:38,225 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-14 09:00:38,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1380000.0, 'new_value': 1430000.0}, {'field': 'total_amount', 'old_value': 1380000.0, 'new_value': 1430000.0}, {'field': 'order_count', 'old_value': 269, 'new_value': 270}]
2025-05-14 09:00:38,225 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-14 09:00:38,725 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-14 09:00:38,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100859.8, 'new_value': 106305.5}, {'field': 'total_amount', 'old_value': 100859.8, 'new_value': 106305.5}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1355}]
2025-05-14 09:00:38,725 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-14 09:00:39,163 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-14 09:00:39,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27107.0, 'new_value': 31005.85}, {'field': 'total_amount', 'old_value': 27107.0, 'new_value': 31005.85}, {'field': 'order_count', 'old_value': 106, 'new_value': 118}]
2025-05-14 09:00:39,163 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-14 09:00:39,600 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-14 09:00:39,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 435000.0, 'new_value': 440000.0}, {'field': 'total_amount', 'old_value': 435000.0, 'new_value': 440000.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 137}]
2025-05-14 09:00:39,600 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-14 09:00:40,616 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-14 09:00:40,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 415000.0, 'new_value': 420000.0}, {'field': 'total_amount', 'old_value': 415000.0, 'new_value': 420000.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 136}]
2025-05-14 09:00:40,616 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-14 09:00:41,053 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-14 09:00:41,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2498674.0, 'new_value': 2548674.0}, {'field': 'total_amount', 'old_value': 2498674.0, 'new_value': 2548674.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 290}]
2025-05-14 09:00:41,069 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-14 09:00:41,538 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-14 09:00:41,538 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44326.65, 'new_value': 49017.13}, {'field': 'total_amount', 'old_value': 51555.72, 'new_value': 56246.2}, {'field': 'order_count', 'old_value': 290, 'new_value': 320}]
2025-05-14 09:00:41,538 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-14 09:00:42,006 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-14 09:00:42,006 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 605.0, 'new_value': 1165.0}, {'field': 'offline_amount', 'old_value': 32855.0, 'new_value': 34339.0}, {'field': 'total_amount', 'old_value': 33460.0, 'new_value': 35504.0}, {'field': 'order_count', 'old_value': 254, 'new_value': 279}]
2025-05-14 09:00:42,006 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-14 09:00:42,413 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-14 09:00:42,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 436960.22, 'new_value': 447839.85}, {'field': 'total_amount', 'old_value': 436960.22, 'new_value': 447839.85}, {'field': 'order_count', 'old_value': 2241, 'new_value': 2323}]
2025-05-14 09:00:42,413 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-14 09:00:42,850 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-14 09:00:42,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76955.46, 'new_value': 80360.43}, {'field': 'total_amount', 'old_value': 76955.46, 'new_value': 80360.43}, {'field': 'order_count', 'old_value': 5233, 'new_value': 5489}]
2025-05-14 09:00:42,850 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-14 09:00:43,319 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-14 09:00:43,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317485.0, 'new_value': 321929.0}, {'field': 'total_amount', 'old_value': 317485.0, 'new_value': 321929.0}, {'field': 'order_count', 'old_value': 7186, 'new_value': 7288}]
2025-05-14 09:00:43,319 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-14 09:00:43,772 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-14 09:00:43,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35312.41, 'new_value': 38483.94}, {'field': 'total_amount', 'old_value': 35312.41, 'new_value': 38483.94}, {'field': 'order_count', 'old_value': 532, 'new_value': 567}]
2025-05-14 09:00:43,772 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-14 09:00:44,256 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-14 09:00:44,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156047.0, 'new_value': 172807.0}, {'field': 'total_amount', 'old_value': 156047.0, 'new_value': 172807.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 43}]
2025-05-14 09:00:44,256 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-14 09:00:44,819 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-14 09:00:44,819 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24926.98, 'new_value': 28116.93}, {'field': 'total_amount', 'old_value': 43804.45, 'new_value': 46994.4}, {'field': 'order_count', 'old_value': 2810, 'new_value': 3037}]
2025-05-14 09:00:44,819 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-14 09:00:45,334 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-14 09:00:45,334 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39120.37, 'new_value': 45935.37}, {'field': 'total_amount', 'old_value': 69528.51, 'new_value': 76343.51}, {'field': 'order_count', 'old_value': 4508, 'new_value': 4989}]
2025-05-14 09:00:45,334 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-14 09:00:45,803 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-14 09:00:45,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50302.0, 'new_value': 53771.0}, {'field': 'total_amount', 'old_value': 50302.0, 'new_value': 53771.0}, {'field': 'order_count', 'old_value': 163, 'new_value': 180}]
2025-05-14 09:00:45,803 - INFO - 日期 2025-05 处理完成 - 更新: 36 条，插入: 0 条，错误: 0 条
2025-05-14 09:00:45,803 - INFO - 数据同步完成！更新: 36 条，插入: 0 条，错误: 0 条
2025-05-14 09:00:45,819 - INFO - =================同步完成====================
2025-05-14 12:00:01,836 - INFO - =================使用默认全量同步=============
2025-05-14 12:00:03,180 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 12:00:03,180 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 12:00:03,211 - INFO - 开始处理日期: 2025-01
2025-05-14 12:00:03,211 - INFO - Request Parameters - Page 1:
2025-05-14 12:00:03,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:03,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:04,524 - INFO - Response - Page 1:
2025-05-14 12:00:04,727 - INFO - 第 1 页获取到 100 条记录
2025-05-14 12:00:04,727 - INFO - Request Parameters - Page 2:
2025-05-14 12:00:04,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:04,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:05,258 - INFO - Response - Page 2:
2025-05-14 12:00:05,461 - INFO - 第 2 页获取到 100 条记录
2025-05-14 12:00:05,461 - INFO - Request Parameters - Page 3:
2025-05-14 12:00:05,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:05,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:05,946 - INFO - Response - Page 3:
2025-05-14 12:00:06,149 - INFO - 第 3 页获取到 100 条记录
2025-05-14 12:00:06,149 - INFO - Request Parameters - Page 4:
2025-05-14 12:00:06,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:06,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:06,617 - INFO - Response - Page 4:
2025-05-14 12:00:06,821 - INFO - 第 4 页获取到 100 条记录
2025-05-14 12:00:06,821 - INFO - Request Parameters - Page 5:
2025-05-14 12:00:06,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:06,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:07,258 - INFO - Response - Page 5:
2025-05-14 12:00:07,461 - INFO - 第 5 页获取到 100 条记录
2025-05-14 12:00:07,461 - INFO - Request Parameters - Page 6:
2025-05-14 12:00:07,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:07,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:07,961 - INFO - Response - Page 6:
2025-05-14 12:00:08,164 - INFO - 第 6 页获取到 100 条记录
2025-05-14 12:00:08,164 - INFO - Request Parameters - Page 7:
2025-05-14 12:00:08,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:08,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:08,586 - INFO - Response - Page 7:
2025-05-14 12:00:08,789 - INFO - 第 7 页获取到 82 条记录
2025-05-14 12:00:08,789 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 12:00:08,789 - INFO - 获取到 682 条表单数据
2025-05-14 12:00:08,789 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 12:00:08,805 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 12:00:08,805 - INFO - 开始处理日期: 2025-02
2025-05-14 12:00:08,805 - INFO - Request Parameters - Page 1:
2025-05-14 12:00:08,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:08,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:09,274 - INFO - Response - Page 1:
2025-05-14 12:00:09,477 - INFO - 第 1 页获取到 100 条记录
2025-05-14 12:00:09,477 - INFO - Request Parameters - Page 2:
2025-05-14 12:00:09,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:09,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:09,961 - INFO - Response - Page 2:
2025-05-14 12:00:10,164 - INFO - 第 2 页获取到 100 条记录
2025-05-14 12:00:10,164 - INFO - Request Parameters - Page 3:
2025-05-14 12:00:10,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:10,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:10,602 - INFO - Response - Page 3:
2025-05-14 12:00:10,805 - INFO - 第 3 页获取到 100 条记录
2025-05-14 12:00:10,805 - INFO - Request Parameters - Page 4:
2025-05-14 12:00:10,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:10,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:11,289 - INFO - Response - Page 4:
2025-05-14 12:00:11,492 - INFO - 第 4 页获取到 100 条记录
2025-05-14 12:00:11,492 - INFO - Request Parameters - Page 5:
2025-05-14 12:00:11,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:11,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:12,117 - INFO - Response - Page 5:
2025-05-14 12:00:12,321 - INFO - 第 5 页获取到 100 条记录
2025-05-14 12:00:12,321 - INFO - Request Parameters - Page 6:
2025-05-14 12:00:12,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:12,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:12,774 - INFO - Response - Page 6:
2025-05-14 12:00:12,977 - INFO - 第 6 页获取到 100 条记录
2025-05-14 12:00:12,977 - INFO - Request Parameters - Page 7:
2025-05-14 12:00:12,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:12,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:13,446 - INFO - Response - Page 7:
2025-05-14 12:00:13,649 - INFO - 第 7 页获取到 70 条记录
2025-05-14 12:00:13,649 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 12:00:13,649 - INFO - 获取到 670 条表单数据
2025-05-14 12:00:13,649 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 12:00:13,664 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 12:00:13,664 - INFO - 开始处理日期: 2025-03
2025-05-14 12:00:13,664 - INFO - Request Parameters - Page 1:
2025-05-14 12:00:13,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:13,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:14,227 - INFO - Response - Page 1:
2025-05-14 12:00:14,430 - INFO - 第 1 页获取到 100 条记录
2025-05-14 12:00:14,430 - INFO - Request Parameters - Page 2:
2025-05-14 12:00:14,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:14,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:14,946 - INFO - Response - Page 2:
2025-05-14 12:00:15,149 - INFO - 第 2 页获取到 100 条记录
2025-05-14 12:00:15,149 - INFO - Request Parameters - Page 3:
2025-05-14 12:00:15,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:15,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:15,617 - INFO - Response - Page 3:
2025-05-14 12:00:15,821 - INFO - 第 3 页获取到 100 条记录
2025-05-14 12:00:15,821 - INFO - Request Parameters - Page 4:
2025-05-14 12:00:15,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:15,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:16,321 - INFO - Response - Page 4:
2025-05-14 12:00:16,524 - INFO - 第 4 页获取到 100 条记录
2025-05-14 12:00:16,524 - INFO - Request Parameters - Page 5:
2025-05-14 12:00:16,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:16,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:17,055 - INFO - Response - Page 5:
2025-05-14 12:00:17,258 - INFO - 第 5 页获取到 100 条记录
2025-05-14 12:00:17,258 - INFO - Request Parameters - Page 6:
2025-05-14 12:00:17,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:17,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:17,789 - INFO - Response - Page 6:
2025-05-14 12:00:17,992 - INFO - 第 6 页获取到 100 条记录
2025-05-14 12:00:17,992 - INFO - Request Parameters - Page 7:
2025-05-14 12:00:17,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:17,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:18,492 - INFO - Response - Page 7:
2025-05-14 12:00:18,696 - INFO - 第 7 页获取到 61 条记录
2025-05-14 12:00:18,696 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 12:00:18,696 - INFO - 获取到 661 条表单数据
2025-05-14 12:00:18,696 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 12:00:18,711 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 12:00:18,711 - INFO - 开始处理日期: 2025-04
2025-05-14 12:00:18,711 - INFO - Request Parameters - Page 1:
2025-05-14 12:00:18,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:18,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:19,196 - INFO - Response - Page 1:
2025-05-14 12:00:19,399 - INFO - 第 1 页获取到 100 条记录
2025-05-14 12:00:19,399 - INFO - Request Parameters - Page 2:
2025-05-14 12:00:19,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:19,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:19,867 - INFO - Response - Page 2:
2025-05-14 12:00:20,071 - INFO - 第 2 页获取到 100 条记录
2025-05-14 12:00:20,071 - INFO - Request Parameters - Page 3:
2025-05-14 12:00:20,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:20,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:20,664 - INFO - Response - Page 3:
2025-05-14 12:00:20,867 - INFO - 第 3 页获取到 100 条记录
2025-05-14 12:00:20,867 - INFO - Request Parameters - Page 4:
2025-05-14 12:00:20,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:20,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:21,305 - INFO - Response - Page 4:
2025-05-14 12:00:21,508 - INFO - 第 4 页获取到 100 条记录
2025-05-14 12:00:21,508 - INFO - Request Parameters - Page 5:
2025-05-14 12:00:21,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:21,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:22,008 - INFO - Response - Page 5:
2025-05-14 12:00:22,211 - INFO - 第 5 页获取到 100 条记录
2025-05-14 12:00:22,211 - INFO - Request Parameters - Page 6:
2025-05-14 12:00:22,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:22,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:22,727 - INFO - Response - Page 6:
2025-05-14 12:00:22,930 - INFO - 第 6 页获取到 100 条记录
2025-05-14 12:00:22,930 - INFO - Request Parameters - Page 7:
2025-05-14 12:00:22,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:22,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:23,367 - INFO - Response - Page 7:
2025-05-14 12:00:23,571 - INFO - 第 7 页获取到 54 条记录
2025-05-14 12:00:23,571 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 12:00:23,571 - INFO - 获取到 654 条表单数据
2025-05-14 12:00:23,571 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 12:00:23,586 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-05-14 12:00:24,086 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MRI
2025-05-14 12:00:24,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11517.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 121475.51, 'new_value': 121609.9}, {'field': 'total_amount', 'old_value': 132992.51, 'new_value': 121609.9}]
2025-05-14 12:00:24,086 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-05-14 12:00:24,539 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MOJ
2025-05-14 12:00:24,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64623.54, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 60140.71, 'new_value': 152039.0}, {'field': 'total_amount', 'old_value': 124764.25, 'new_value': 152039.0}]
2025-05-14 12:00:24,555 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-05-14 12:00:25,008 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MC6
2025-05-14 12:00:25,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203776.58, 'new_value': 202879.34}, {'field': 'total_amount', 'old_value': 203776.58, 'new_value': 202879.34}, {'field': 'order_count', 'old_value': 22585, 'new_value': 22594}]
2025-05-14 12:00:25,008 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-05-14 12:00:25,477 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-05-14 12:00:25,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5602.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 136931.0, 'new_value': 140875.0}, {'field': 'total_amount', 'old_value': 142533.0, 'new_value': 140875.0}]
2025-05-14 12:00:25,477 - INFO - 日期 2025-04 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-05-14 12:00:25,477 - INFO - 开始处理日期: 2025-05
2025-05-14 12:00:25,477 - INFO - Request Parameters - Page 1:
2025-05-14 12:00:25,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:25,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:25,977 - INFO - Response - Page 1:
2025-05-14 12:00:26,180 - INFO - 第 1 页获取到 100 条记录
2025-05-14 12:00:26,180 - INFO - Request Parameters - Page 2:
2025-05-14 12:00:26,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:26,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:26,664 - INFO - Response - Page 2:
2025-05-14 12:00:26,867 - INFO - 第 2 页获取到 100 条记录
2025-05-14 12:00:26,867 - INFO - Request Parameters - Page 3:
2025-05-14 12:00:26,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:26,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:27,305 - INFO - Response - Page 3:
2025-05-14 12:00:27,508 - INFO - 第 3 页获取到 100 条记录
2025-05-14 12:00:27,508 - INFO - Request Parameters - Page 4:
2025-05-14 12:00:27,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:27,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:28,024 - INFO - Response - Page 4:
2025-05-14 12:00:28,227 - INFO - 第 4 页获取到 100 条记录
2025-05-14 12:00:28,227 - INFO - Request Parameters - Page 5:
2025-05-14 12:00:28,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:28,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:28,774 - INFO - Response - Page 5:
2025-05-14 12:00:28,977 - INFO - 第 5 页获取到 100 条记录
2025-05-14 12:00:28,977 - INFO - Request Parameters - Page 6:
2025-05-14 12:00:28,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:28,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:29,461 - INFO - Response - Page 6:
2025-05-14 12:00:29,664 - INFO - 第 6 页获取到 100 条记录
2025-05-14 12:00:29,664 - INFO - Request Parameters - Page 7:
2025-05-14 12:00:29,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 12:00:29,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 12:00:30,008 - INFO - Response - Page 7:
2025-05-14 12:00:30,211 - INFO - 第 7 页获取到 25 条记录
2025-05-14 12:00:30,211 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 12:00:30,211 - INFO - 获取到 625 条表单数据
2025-05-14 12:00:30,211 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 12:00:30,211 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-14 12:00:30,695 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-14 12:00:30,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1115.0, 'new_value': 1235.0}, {'field': 'offline_amount', 'old_value': 23835.0, 'new_value': 24705.0}, {'field': 'total_amount', 'old_value': 24950.0, 'new_value': 25940.0}, {'field': 'order_count', 'old_value': 313, 'new_value': 327}]
2025-05-14 12:00:30,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-14 12:00:31,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-14 12:00:31,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175735.0, 'new_value': 191415.0}, {'field': 'total_amount', 'old_value': 175735.0, 'new_value': 191415.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 135}]
2025-05-14 12:00:31,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-14 12:00:31,664 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-14 12:00:31,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5400000.0, 'new_value': 5600000.0}, {'field': 'total_amount', 'old_value': 5500000.0, 'new_value': 5700000.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-14 12:00:31,664 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-14 12:00:32,086 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-14 12:00:32,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14840.0, 'new_value': 20140.0}, {'field': 'total_amount', 'old_value': 16430.0, 'new_value': 21730.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 82}]
2025-05-14 12:00:32,086 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-14 12:00:32,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-14 12:00:32,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376638.98, 'new_value': 376711.98}, {'field': 'total_amount', 'old_value': 376638.98, 'new_value': 376711.98}, {'field': 'order_count', 'old_value': 1122, 'new_value': 26087}]
2025-05-14 12:00:32,602 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-14 12:00:33,086 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-14 12:00:33,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14206.7, 'new_value': 14679.7}, {'field': 'total_amount', 'old_value': 14206.7, 'new_value': 14679.7}, {'field': 'order_count', 'old_value': 90, 'new_value': 97}]
2025-05-14 12:00:33,086 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-14 12:00:33,649 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-14 12:00:33,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15550.33, 'new_value': 19932.43}, {'field': 'total_amount', 'old_value': 15550.33, 'new_value': 19932.43}, {'field': 'order_count', 'old_value': 2923, 'new_value': 3785}]
2025-05-14 12:00:33,649 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-14 12:00:34,102 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-14 12:00:34,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22421.15, 'new_value': 30674.96}, {'field': 'total_amount', 'old_value': 25505.76, 'new_value': 33759.57}, {'field': 'order_count', 'old_value': 886, 'new_value': 1193}]
2025-05-14 12:00:34,102 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-14 12:00:34,570 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-14 12:00:34,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27868.64, 'new_value': 33027.62}, {'field': 'total_amount', 'old_value': 27868.64, 'new_value': 33027.62}, {'field': 'order_count', 'old_value': 60, 'new_value': 68}]
2025-05-14 12:00:34,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-14 12:00:35,039 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-14 12:00:35,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144020.0, 'new_value': 157220.0}, {'field': 'total_amount', 'old_value': 144020.0, 'new_value': 157220.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 85}]
2025-05-14 12:00:35,039 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-14 12:00:35,508 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-14 12:00:35,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81001.0, 'new_value': 81421.0}, {'field': 'total_amount', 'old_value': 89200.0, 'new_value': 89620.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-14 12:00:35,508 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-14 12:00:35,992 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-14 12:00:35,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54890.0, 'new_value': 57049.0}, {'field': 'total_amount', 'old_value': 54890.0, 'new_value': 57049.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 63}]
2025-05-14 12:00:36,008 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-14 12:00:36,461 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-14 12:00:36,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19520.0, 'new_value': 22220.0}, {'field': 'total_amount', 'old_value': 23640.0, 'new_value': 26340.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 257}]
2025-05-14 12:00:36,461 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-14 12:00:36,945 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-14 12:00:36,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11443.0, 'new_value': 18343.0}, {'field': 'total_amount', 'old_value': 11443.0, 'new_value': 18343.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-14 12:00:36,945 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-14 12:00:37,414 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-14 12:00:37,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16825.0, 'new_value': 21805.0}, {'field': 'total_amount', 'old_value': 16825.0, 'new_value': 21805.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-14 12:00:37,414 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-14 12:00:37,961 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-14 12:00:37,961 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 204, 'new_value': 225}]
2025-05-14 12:00:37,961 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-14 12:00:38,570 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-14 12:00:38,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27771.13, 'new_value': 30142.23}, {'field': 'offline_amount', 'old_value': 55876.6, 'new_value': 60038.0}, {'field': 'total_amount', 'old_value': 83647.73, 'new_value': 90180.23}, {'field': 'order_count', 'old_value': 1036, 'new_value': 1109}]
2025-05-14 12:00:38,570 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-14 12:00:39,008 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-14 12:00:39,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11777.65, 'new_value': 12458.51}, {'field': 'offline_amount', 'old_value': 13840.43, 'new_value': 14745.81}, {'field': 'total_amount', 'old_value': 25618.08, 'new_value': 27204.32}, {'field': 'order_count', 'old_value': 1220, 'new_value': 1304}]
2025-05-14 12:00:39,008 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-14 12:00:39,461 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-14 12:00:39,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185570.7, 'new_value': 192669.9}, {'field': 'total_amount', 'old_value': 300590.4, 'new_value': 307689.6}, {'field': 'order_count', 'old_value': 1882, 'new_value': 1993}]
2025-05-14 12:00:39,461 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-14 12:00:39,899 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-14 12:00:39,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49289.0, 'new_value': 53814.0}, {'field': 'total_amount', 'old_value': 49289.0, 'new_value': 53814.0}, {'field': 'order_count', 'old_value': 2671, 'new_value': 2902}]
2025-05-14 12:00:39,899 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-14 12:00:40,367 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-14 12:00:40,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37830.0, 'new_value': 38066.0}, {'field': 'total_amount', 'old_value': 37830.0, 'new_value': 38066.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-05-14 12:00:40,367 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-14 12:00:40,914 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-14 12:00:40,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67703.99, 'new_value': 73379.85}, {'field': 'total_amount', 'old_value': 67703.99, 'new_value': 73379.85}, {'field': 'order_count', 'old_value': 755, 'new_value': 829}]
2025-05-14 12:00:40,914 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-14 12:00:41,320 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-14 12:00:41,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6163.66, 'new_value': 6529.02}, {'field': 'offline_amount', 'old_value': 20431.28, 'new_value': 21721.98}, {'field': 'total_amount', 'old_value': 26594.94, 'new_value': 28251.0}, {'field': 'order_count', 'old_value': 453, 'new_value': 488}]
2025-05-14 12:00:41,320 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-14 12:00:41,852 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-14 12:00:41,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55527.59, 'new_value': 67354.54}, {'field': 'offline_amount', 'old_value': 7282.95, 'new_value': 10099.15}, {'field': 'total_amount', 'old_value': 62810.54, 'new_value': 77453.69}, {'field': 'order_count', 'old_value': 2300, 'new_value': 2776}]
2025-05-14 12:00:41,852 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-14 12:00:42,367 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-14 12:00:42,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154350.0, 'new_value': 174350.0}, {'field': 'total_amount', 'old_value': 154350.0, 'new_value': 174350.0}]
2025-05-14 12:00:42,367 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-14 12:00:42,867 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-14 12:00:42,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201535.89, 'new_value': 221535.89}, {'field': 'total_amount', 'old_value': 201535.89, 'new_value': 221535.89}]
2025-05-14 12:00:42,867 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-14 12:00:43,242 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-14 12:00:43,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156190.13, 'new_value': 176190.13}, {'field': 'total_amount', 'old_value': 195550.13, 'new_value': 215550.13}]
2025-05-14 12:00:43,242 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-14 12:00:43,680 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-14 12:00:43,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30401.6, 'new_value': 30459.6}, {'field': 'total_amount', 'old_value': 44932.4, 'new_value': 44990.4}, {'field': 'order_count', 'old_value': 563, 'new_value': 564}]
2025-05-14 12:00:43,680 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-14 12:00:44,180 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-14 12:00:44,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27580.0, 'new_value': 32580.0}, {'field': 'total_amount', 'old_value': 76975.0, 'new_value': 81975.0}, {'field': 'order_count', 'old_value': 935, 'new_value': 1006}]
2025-05-14 12:00:44,180 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-14 12:00:44,633 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-14 12:00:44,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3611.0, 'new_value': 4321.0}, {'field': 'offline_amount', 'old_value': 11211.0, 'new_value': 11310.0}, {'field': 'total_amount', 'old_value': 14822.0, 'new_value': 15631.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 45}]
2025-05-14 12:00:44,633 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-14 12:00:45,289 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-14 12:00:45,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96310.0, 'new_value': 108612.0}, {'field': 'total_amount', 'old_value': 96310.0, 'new_value': 108612.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 190}]
2025-05-14 12:00:45,289 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-14 12:00:45,758 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-14 12:00:45,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54257.67, 'new_value': 56259.44}, {'field': 'total_amount', 'old_value': 54257.67, 'new_value': 56259.44}, {'field': 'order_count', 'old_value': 1867, 'new_value': 2084}]
2025-05-14 12:00:45,758 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-14 12:00:46,164 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-14 12:00:46,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99877.38, 'new_value': 102895.36}, {'field': 'offline_amount', 'old_value': 17328.0, 'new_value': 17793.92}, {'field': 'total_amount', 'old_value': 117205.38, 'new_value': 120689.28}, {'field': 'order_count', 'old_value': 415, 'new_value': 430}]
2025-05-14 12:00:46,164 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-14 12:00:46,586 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-14 12:00:46,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102619.0, 'new_value': 106783.0}, {'field': 'offline_amount', 'old_value': 35927.66, 'new_value': 37287.66}, {'field': 'total_amount', 'old_value': 138546.66, 'new_value': 144070.66}, {'field': 'order_count', 'old_value': 837, 'new_value': 869}]
2025-05-14 12:00:46,586 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-14 12:00:46,992 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-14 12:00:46,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28000.62, 'new_value': 27842.65}, {'field': 'total_amount', 'old_value': 49209.02, 'new_value': 49051.05}, {'field': 'order_count', 'old_value': 2294, 'new_value': 2442}]
2025-05-14 12:00:46,992 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-14 12:00:47,461 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-14 12:00:47,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43668.56, 'new_value': 46995.32}, {'field': 'total_amount', 'old_value': 43668.56, 'new_value': 46995.32}, {'field': 'order_count', 'old_value': 1122, 'new_value': 1211}]
2025-05-14 12:00:47,461 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-14 12:00:47,945 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-14 12:00:47,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100445.0, 'new_value': 103341.0}, {'field': 'total_amount', 'old_value': 100445.0, 'new_value': 103341.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-14 12:00:47,945 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-14 12:00:48,383 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-14 12:00:48,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80467.0, 'new_value': 84219.0}, {'field': 'offline_amount', 'old_value': 34797.14, 'new_value': 36365.22}, {'field': 'total_amount', 'old_value': 115264.14, 'new_value': 120584.22}, {'field': 'order_count', 'old_value': 805, 'new_value': 843}]
2025-05-14 12:00:48,383 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-14 12:00:48,883 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-14 12:00:48,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27305.01, 'new_value': 29095.97}, {'field': 'offline_amount', 'old_value': 273985.86, 'new_value': 293398.48}, {'field': 'total_amount', 'old_value': 301290.87, 'new_value': 322494.45}, {'field': 'order_count', 'old_value': 933, 'new_value': 992}]
2025-05-14 12:00:48,883 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-14 12:00:49,289 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-14 12:00:49,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5791.84, 'new_value': 6205.64}, {'field': 'offline_amount', 'old_value': 89537.26, 'new_value': 96240.85}, {'field': 'total_amount', 'old_value': 95329.1, 'new_value': 102446.49}, {'field': 'order_count', 'old_value': 1050, 'new_value': 1107}]
2025-05-14 12:00:49,289 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-14 12:00:49,773 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-14 12:00:49,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33390.0, 'new_value': 34902.0}, {'field': 'offline_amount', 'old_value': 32274.0, 'new_value': 33696.0}, {'field': 'total_amount', 'old_value': 65664.0, 'new_value': 68598.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-05-14 12:00:49,773 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-14 12:00:50,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-14 12:00:50,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35589.0, 'new_value': 36498.0}, {'field': 'total_amount', 'old_value': 39717.32, 'new_value': 40626.32}, {'field': 'order_count', 'old_value': 429, 'new_value': 430}]
2025-05-14 12:00:50,227 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-14 12:00:50,695 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-14 12:00:50,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99573.95, 'new_value': 105941.94}, {'field': 'total_amount', 'old_value': 99573.95, 'new_value': 105941.94}, {'field': 'order_count', 'old_value': 308, 'new_value': 328}]
2025-05-14 12:00:50,695 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-14 12:00:51,102 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-14 12:00:51,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8.0, 'new_value': 3213.44}, {'field': 'offline_amount', 'old_value': 47605.57, 'new_value': 47770.67}, {'field': 'total_amount', 'old_value': 47613.57, 'new_value': 50984.11}, {'field': 'order_count', 'old_value': 2660, 'new_value': 2865}]
2025-05-14 12:00:51,102 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-14 12:00:51,555 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-14 12:00:51,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68883.0, 'new_value': 73864.0}, {'field': 'total_amount', 'old_value': 68883.0, 'new_value': 73864.0}, {'field': 'order_count', 'old_value': 1642, 'new_value': 1780}]
2025-05-14 12:00:51,555 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-14 12:00:52,008 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-14 12:00:52,008 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 124, 'new_value': 132}]
2025-05-14 12:00:52,008 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-14 12:00:52,477 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-14 12:00:52,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41900.12, 'new_value': 44672.49}, {'field': 'offline_amount', 'old_value': 455012.01, 'new_value': 483806.31}, {'field': 'total_amount', 'old_value': 496912.13, 'new_value': 528478.8}, {'field': 'order_count', 'old_value': 1624, 'new_value': 1715}]
2025-05-14 12:00:52,477 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-14 12:00:52,914 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-14 12:00:52,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13876.0, 'new_value': 13670.0}, {'field': 'total_amount', 'old_value': 13876.0, 'new_value': 13670.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-05-14 12:00:52,914 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-14 12:00:53,367 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-14 12:00:53,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13070.03, 'new_value': 13419.17}, {'field': 'total_amount', 'old_value': 13070.03, 'new_value': 13419.17}, {'field': 'order_count', 'old_value': 56, 'new_value': 59}]
2025-05-14 12:00:53,367 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-14 12:00:53,820 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-14 12:00:53,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75706.0, 'new_value': 83750.0}, {'field': 'total_amount', 'old_value': 75706.0, 'new_value': 83750.0}, {'field': 'order_count', 'old_value': 2755, 'new_value': 3063}]
2025-05-14 12:00:53,820 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-14 12:00:54,320 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-14 12:00:54,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 357097.78, 'new_value': 379947.9}, {'field': 'total_amount', 'old_value': 357097.78, 'new_value': 379947.9}, {'field': 'order_count', 'old_value': 2575, 'new_value': 2767}]
2025-05-14 12:00:54,320 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-14 12:00:54,883 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-14 12:00:54,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 259950.0, 'new_value': 270215.0}, {'field': 'total_amount', 'old_value': 274553.0, 'new_value': 284818.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-05-14 12:00:54,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-14 12:00:55,320 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-14 12:00:55,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44986.0, 'new_value': 53685.0}, {'field': 'total_amount', 'old_value': 44986.0, 'new_value': 53685.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-14 12:00:55,320 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-14 12:00:55,883 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-14 12:00:55,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63542.44, 'new_value': 70208.95}, {'field': 'total_amount', 'old_value': 63542.44, 'new_value': 70208.95}, {'field': 'order_count', 'old_value': 2350, 'new_value': 2590}]
2025-05-14 12:00:55,883 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-14 12:00:56,258 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-14 12:00:56,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23640.71, 'new_value': 25438.24}, {'field': 'offline_amount', 'old_value': 60769.0, 'new_value': 64122.08}, {'field': 'total_amount', 'old_value': 84409.71, 'new_value': 89560.32}, {'field': 'order_count', 'old_value': 2808, 'new_value': 3017}]
2025-05-14 12:00:56,273 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-14 12:00:56,789 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-14 12:00:56,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12518.58, 'new_value': 12757.98}, {'field': 'total_amount', 'old_value': 12518.58, 'new_value': 12757.98}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-14 12:00:56,789 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-14 12:00:57,273 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-14 12:00:57,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4073.0, 'new_value': 4172.0}, {'field': 'offline_amount', 'old_value': 7350.0, 'new_value': 8411.0}, {'field': 'total_amount', 'old_value': 11423.0, 'new_value': 12583.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 79}]
2025-05-14 12:00:57,273 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-14 12:00:57,758 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-14 12:00:57,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123117.78, 'new_value': 137562.06}, {'field': 'total_amount', 'old_value': 123874.78, 'new_value': 138319.06}, {'field': 'order_count', 'old_value': 1427, 'new_value': 1622}]
2025-05-14 12:00:57,758 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-14 12:00:58,273 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-14 12:00:58,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209486.0, 'new_value': 212084.0}, {'field': 'total_amount', 'old_value': 209486.0, 'new_value': 212084.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 97}]
2025-05-14 12:00:58,273 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-14 12:00:58,742 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-14 12:00:58,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46359.0, 'new_value': 46656.0}, {'field': 'total_amount', 'old_value': 46359.0, 'new_value': 46656.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-14 12:00:58,742 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-14 12:00:59,164 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-14 12:00:59,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32240.28, 'new_value': 37977.28}, {'field': 'offline_amount', 'old_value': 207133.3, 'new_value': 233942.3}, {'field': 'total_amount', 'old_value': 239373.58, 'new_value': 271919.58}, {'field': 'order_count', 'old_value': 394, 'new_value': 425}]
2025-05-14 12:00:59,164 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-14 12:00:59,617 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-14 12:00:59,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88175.71, 'new_value': 93082.33}, {'field': 'total_amount', 'old_value': 88175.71, 'new_value': 93082.33}, {'field': 'order_count', 'old_value': 460, 'new_value': 492}]
2025-05-14 12:00:59,617 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-14 12:01:00,164 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-14 12:01:00,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45998.44, 'new_value': 52127.91}, {'field': 'offline_amount', 'old_value': 41781.59, 'new_value': 47023.66}, {'field': 'total_amount', 'old_value': 87780.03, 'new_value': 99151.57}, {'field': 'order_count', 'old_value': 2978, 'new_value': 3385}]
2025-05-14 12:01:00,164 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-14 12:01:00,742 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-14 12:01:00,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91749.54, 'new_value': 98517.86}, {'field': 'offline_amount', 'old_value': 264341.18, 'new_value': 275662.63}, {'field': 'total_amount', 'old_value': 356090.72, 'new_value': 374180.49}, {'field': 'order_count', 'old_value': 2375, 'new_value': 2535}]
2025-05-14 12:01:00,742 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-14 12:01:01,195 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-14 12:01:01,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16625.7, 'new_value': 17669.7}, {'field': 'total_amount', 'old_value': 16625.7, 'new_value': 17669.7}, {'field': 'order_count', 'old_value': 91, 'new_value': 98}]
2025-05-14 12:01:01,195 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-14 12:01:01,695 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-14 12:01:01,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17413.15, 'new_value': 17426.65}, {'field': 'total_amount', 'old_value': 17478.7, 'new_value': 17492.2}, {'field': 'order_count', 'old_value': 172, 'new_value': 173}]
2025-05-14 12:01:01,695 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-14 12:01:02,164 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-14 12:01:02,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76889.0, 'new_value': 84983.0}, {'field': 'total_amount', 'old_value': 76889.0, 'new_value': 84983.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 23}]
2025-05-14 12:01:02,164 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-14 12:01:02,633 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-14 12:01:02,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37165.04, 'new_value': 35712.14}, {'field': 'total_amount', 'old_value': 37165.04, 'new_value': 35712.14}, {'field': 'order_count', 'old_value': 1492, 'new_value': 1625}]
2025-05-14 12:01:02,633 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-14 12:01:03,055 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-14 12:01:03,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 976749.1, 'new_value': 1028862.1}, {'field': 'total_amount', 'old_value': 1030194.2, 'new_value': 1082307.2}, {'field': 'order_count', 'old_value': 1759, 'new_value': 1835}]
2025-05-14 12:01:03,055 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-14 12:01:03,555 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-14 12:01:03,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67822.65, 'new_value': 77159.65}, {'field': 'offline_amount', 'old_value': 46203.0, 'new_value': 48431.0}, {'field': 'total_amount', 'old_value': 114025.65, 'new_value': 125590.65}, {'field': 'order_count', 'old_value': 616, 'new_value': 673}]
2025-05-14 12:01:03,555 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-14 12:01:03,977 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-14 12:01:03,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23622.9, 'new_value': 24481.9}, {'field': 'total_amount', 'old_value': 23622.9, 'new_value': 24481.9}, {'field': 'order_count', 'old_value': 105, 'new_value': 108}]
2025-05-14 12:01:03,977 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-14 12:01:04,430 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-14 12:01:04,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92723.0, 'new_value': 102192.0}, {'field': 'total_amount', 'old_value': 92723.0, 'new_value': 102192.0}, {'field': 'order_count', 'old_value': 9142, 'new_value': 9323}]
2025-05-14 12:01:04,430 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-14 12:01:04,852 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-14 12:01:04,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13224.0, 'new_value': 13637.0}, {'field': 'total_amount', 'old_value': 13224.0, 'new_value': 13637.0}, {'field': 'order_count', 'old_value': 228, 'new_value': 235}]
2025-05-14 12:01:04,852 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-14 12:01:05,289 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-14 12:01:05,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73226.0, 'new_value': 74988.0}, {'field': 'total_amount', 'old_value': 73227.0, 'new_value': 74989.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-14 12:01:05,289 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-14 12:01:05,805 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-14 12:01:05,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3149.06, 'new_value': 3406.06}, {'field': 'offline_amount', 'old_value': 7825.07, 'new_value': 8654.64}, {'field': 'total_amount', 'old_value': 10974.13, 'new_value': 12060.7}, {'field': 'order_count', 'old_value': 384, 'new_value': 422}]
2025-05-14 12:01:05,805 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-14 12:01:06,258 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-14 12:01:06,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84490.32, 'new_value': 91909.42}, {'field': 'offline_amount', 'old_value': 64266.26, 'new_value': 74152.26}, {'field': 'total_amount', 'old_value': 148756.58, 'new_value': 166061.68}, {'field': 'order_count', 'old_value': 1279, 'new_value': 1413}]
2025-05-14 12:01:06,258 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-14 12:01:06,711 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-14 12:01:06,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28913.26, 'new_value': 35972.11}, {'field': 'total_amount', 'old_value': 28916.56, 'new_value': 35975.41}, {'field': 'order_count', 'old_value': 20, 'new_value': 25}]
2025-05-14 12:01:06,711 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-14 12:01:07,164 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-14 12:01:07,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 189583.9, 'new_value': 197999.9}, {'field': 'offline_amount', 'old_value': 41040.0, 'new_value': 43692.0}, {'field': 'total_amount', 'old_value': 230623.9, 'new_value': 241691.9}, {'field': 'order_count', 'old_value': 281, 'new_value': 298}]
2025-05-14 12:01:07,164 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-14 12:01:07,617 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-14 12:01:07,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18718.0, 'new_value': 18857.0}, {'field': 'total_amount', 'old_value': 18718.0, 'new_value': 18857.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-14 12:01:07,617 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-14 12:01:08,023 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-14 12:01:08,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34462.0, 'new_value': 46847.0}, {'field': 'total_amount', 'old_value': 34462.0, 'new_value': 46847.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 31}]
2025-05-14 12:01:08,023 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-14 12:01:08,508 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-14 12:01:08,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93594.46, 'new_value': 105598.58}, {'field': 'total_amount', 'old_value': 100510.12, 'new_value': 112514.24}, {'field': 'order_count', 'old_value': 1975, 'new_value': 2231}]
2025-05-14 12:01:08,508 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-14 12:01:08,961 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-14 12:01:08,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20593.83, 'new_value': 22195.22}, {'field': 'offline_amount', 'old_value': 53535.41, 'new_value': 55430.5}, {'field': 'total_amount', 'old_value': 74129.24, 'new_value': 77625.72}, {'field': 'order_count', 'old_value': 2629, 'new_value': 2780}]
2025-05-14 12:01:08,961 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-14 12:01:09,383 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-14 12:01:09,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236721.19, 'new_value': 253595.75}, {'field': 'total_amount', 'old_value': 236721.19, 'new_value': 253595.75}, {'field': 'order_count', 'old_value': 2337, 'new_value': 2515}]
2025-05-14 12:01:09,383 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-14 12:01:09,867 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-14 12:01:09,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56993.39, 'new_value': 57023.29}, {'field': 'total_amount', 'old_value': 60762.49, 'new_value': 60792.39}, {'field': 'order_count', 'old_value': 312, 'new_value': 313}]
2025-05-14 12:01:09,867 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-14 12:01:10,445 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-14 12:01:10,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24459.0, 'new_value': 30157.0}, {'field': 'offline_amount', 'old_value': 92742.0, 'new_value': 94825.0}, {'field': 'total_amount', 'old_value': 117201.0, 'new_value': 124982.0}, {'field': 'order_count', 'old_value': 2401, 'new_value': 2611}]
2025-05-14 12:01:10,445 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-14 12:01:10,992 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-14 12:01:10,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48057.0, 'new_value': 49053.0}, {'field': 'total_amount', 'old_value': 53198.0, 'new_value': 54194.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-14 12:01:10,992 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-14 12:01:11,445 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-14 12:01:11,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76261.0, 'new_value': 96459.0}, {'field': 'total_amount', 'old_value': 102389.0, 'new_value': 122587.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-05-14 12:01:11,445 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-14 12:01:11,883 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-14 12:01:11,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75268.46, 'new_value': 79942.14}, {'field': 'offline_amount', 'old_value': 70704.45, 'new_value': 73103.45}, {'field': 'total_amount', 'old_value': 145972.91, 'new_value': 153045.59}, {'field': 'order_count', 'old_value': 1420, 'new_value': 1507}]
2025-05-14 12:01:11,883 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-14 12:01:12,351 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-14 12:01:12,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 430418.04, 'new_value': 431413.04}, {'field': 'offline_amount', 'old_value': 140255.9, 'new_value': 143599.9}, {'field': 'total_amount', 'old_value': 570673.94, 'new_value': 575012.94}, {'field': 'order_count', 'old_value': 5388, 'new_value': 5426}]
2025-05-14 12:01:12,351 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-14 12:01:12,820 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-14 12:01:12,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118.0, 'new_value': 532.0}, {'field': 'total_amount', 'old_value': 25700.6, 'new_value': 26114.6}, {'field': 'order_count', 'old_value': 104, 'new_value': 110}]
2025-05-14 12:01:12,820 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-14 12:01:13,258 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-14 12:01:13,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38423.8, 'new_value': 43423.8}, {'field': 'offline_amount', 'old_value': 1934.05, 'new_value': 2637.55}, {'field': 'total_amount', 'old_value': 40357.85, 'new_value': 46061.35}, {'field': 'order_count', 'old_value': 108, 'new_value': 123}]
2025-05-14 12:01:13,258 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-14 12:01:13,758 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-14 12:01:13,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69606.0, 'new_value': 75605.0}, {'field': 'total_amount', 'old_value': 93228.48, 'new_value': 99227.48}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-14 12:01:13,758 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-14 12:01:14,180 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-14 12:01:14,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39312.08, 'new_value': 41823.89}, {'field': 'total_amount', 'old_value': 39312.08, 'new_value': 41823.89}, {'field': 'order_count', 'old_value': 1066, 'new_value': 1131}]
2025-05-14 12:01:14,180 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-14 12:01:14,648 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-14 12:01:14,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4992.4, 'new_value': 5180.4}, {'field': 'offline_amount', 'old_value': 26579.0, 'new_value': 28139.0}, {'field': 'total_amount', 'old_value': 31571.4, 'new_value': 33319.4}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-14 12:01:14,648 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-14 12:01:15,133 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-14 12:01:15,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51459.87, 'new_value': 64797.44}, {'field': 'offline_amount', 'old_value': 204741.29, 'new_value': 231378.28}, {'field': 'total_amount', 'old_value': 256201.16, 'new_value': 296175.72}, {'field': 'order_count', 'old_value': 1296, 'new_value': 1660}]
2025-05-14 12:01:15,133 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-14 12:01:15,680 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-14 12:01:15,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190258.51, 'new_value': 212883.43}, {'field': 'total_amount', 'old_value': 190258.51, 'new_value': 212883.43}, {'field': 'order_count', 'old_value': 319, 'new_value': 341}]
2025-05-14 12:01:15,680 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-14 12:01:16,133 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-14 12:01:16,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33925.0, 'new_value': 35060.0}, {'field': 'total_amount', 'old_value': 34273.0, 'new_value': 35408.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 71}]
2025-05-14 12:01:16,133 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-14 12:01:16,617 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-14 12:01:16,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7591.57, 'new_value': 8041.99}, {'field': 'offline_amount', 'old_value': 235412.52, 'new_value': 243976.69}, {'field': 'total_amount', 'old_value': 243004.09, 'new_value': 252018.68}, {'field': 'order_count', 'old_value': 943, 'new_value': 987}]
2025-05-14 12:01:16,617 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-14 12:01:17,070 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-14 12:01:17,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 456527.0, 'new_value': 481718.0}, {'field': 'total_amount', 'old_value': 456527.0, 'new_value': 481718.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 85}]
2025-05-14 12:01:17,070 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-14 12:01:17,601 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-14 12:01:17,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44534.0, 'new_value': 44819.0}, {'field': 'offline_amount', 'old_value': 38454.12, 'new_value': 43657.12}, {'field': 'total_amount', 'old_value': 82988.12, 'new_value': 88476.12}, {'field': 'order_count', 'old_value': 96, 'new_value': 105}]
2025-05-14 12:01:17,601 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-14 12:01:18,055 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-14 12:01:18,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91117.8, 'new_value': 94776.4}, {'field': 'total_amount', 'old_value': 91117.8, 'new_value': 94776.4}, {'field': 'order_count', 'old_value': 201, 'new_value': 212}]
2025-05-14 12:01:18,055 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-14 12:01:18,476 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-14 12:01:18,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117703.5, 'new_value': 123292.46}, {'field': 'offline_amount', 'old_value': 71755.38, 'new_value': 72814.38}, {'field': 'total_amount', 'old_value': 189458.88, 'new_value': 196106.84}, {'field': 'order_count', 'old_value': 722, 'new_value': 747}]
2025-05-14 12:01:18,476 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-14 12:01:18,914 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-14 12:01:18,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 119132.75, 'new_value': 122400.96}, {'field': 'total_amount', 'old_value': 179995.35, 'new_value': 183263.56}, {'field': 'order_count', 'old_value': 2071, 'new_value': 2126}]
2025-05-14 12:01:18,914 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-14 12:01:19,305 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-14 12:01:19,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6620.0, 'new_value': 6819.0}, {'field': 'total_amount', 'old_value': 6620.0, 'new_value': 6819.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-14 12:01:19,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-14 12:01:19,742 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-14 12:01:19,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55972.0, 'new_value': 57280.1}, {'field': 'total_amount', 'old_value': 55972.0, 'new_value': 57280.1}, {'field': 'order_count', 'old_value': 2134, 'new_value': 2184}]
2025-05-14 12:01:19,742 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-14 12:01:20,180 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-14 12:01:20,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11768.79, 'new_value': 12051.83}, {'field': 'offline_amount', 'old_value': 154683.91, 'new_value': 160981.65}, {'field': 'total_amount', 'old_value': 166452.7, 'new_value': 173033.48}, {'field': 'order_count', 'old_value': 780, 'new_value': 813}]
2025-05-14 12:01:20,195 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-14 12:01:20,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-14 12:01:20,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17017.98, 'new_value': 19066.62}, {'field': 'offline_amount', 'old_value': 308906.76, 'new_value': 320092.36}, {'field': 'total_amount', 'old_value': 325924.74, 'new_value': 339158.98}, {'field': 'order_count', 'old_value': 1834, 'new_value': 1890}]
2025-05-14 12:01:20,711 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-14 12:01:21,164 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-14 12:01:21,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36680.4, 'new_value': 43589.1}, {'field': 'total_amount', 'old_value': 36680.4, 'new_value': 43589.1}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-05-14 12:01:21,164 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-14 12:01:21,601 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-14 12:01:21,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7053.7, 'new_value': 7776.82}, {'field': 'total_amount', 'old_value': 13195.74, 'new_value': 13918.86}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-05-14 12:01:21,601 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-14 12:01:22,055 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-14 12:01:22,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25661.0, 'new_value': 26357.0}, {'field': 'total_amount', 'old_value': 25661.0, 'new_value': 26357.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-05-14 12:01:22,055 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-14 12:01:22,570 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-14 12:01:22,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70289.46, 'new_value': 73305.95}, {'field': 'total_amount', 'old_value': 70289.46, 'new_value': 73305.95}, {'field': 'order_count', 'old_value': 454, 'new_value': 470}]
2025-05-14 12:01:22,570 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-14 12:01:22,992 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-14 12:01:22,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13947.12, 'new_value': 13510.78}, {'field': 'total_amount', 'old_value': 14870.47, 'new_value': 14434.13}, {'field': 'order_count', 'old_value': 626, 'new_value': 653}]
2025-05-14 12:01:22,992 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-14 12:01:23,461 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-14 12:01:23,461 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-14 12:01:23,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-14 12:01:23,930 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-14 12:01:23,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2968.0, 'new_value': 3078.0}, {'field': 'total_amount', 'old_value': 2968.0, 'new_value': 3078.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-14 12:01:23,930 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-14 12:01:24,430 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-14 12:01:24,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5742.95, 'new_value': 6654.02}, {'field': 'offline_amount', 'old_value': 58065.15, 'new_value': 66551.84}, {'field': 'total_amount', 'old_value': 63808.1, 'new_value': 73205.86}, {'field': 'order_count', 'old_value': 1567, 'new_value': 1810}]
2025-05-14 12:01:24,430 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-14 12:01:24,883 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-14 12:01:24,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77827.93, 'new_value': 84615.18}, {'field': 'offline_amount', 'old_value': 203721.21, 'new_value': 211006.65}, {'field': 'total_amount', 'old_value': 281549.14, 'new_value': 295621.83}, {'field': 'order_count', 'old_value': 7066, 'new_value': 7564}]
2025-05-14 12:01:24,883 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-14 12:01:25,383 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-14 12:01:25,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29231.0, 'new_value': 29719.0}, {'field': 'total_amount', 'old_value': 29231.0, 'new_value': 29719.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 74}]
2025-05-14 12:01:25,383 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-14 12:01:25,867 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-14 12:01:25,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4307.66, 'new_value': 4977.0}, {'field': 'offline_amount', 'old_value': 19267.0, 'new_value': 19684.0}, {'field': 'total_amount', 'old_value': 23574.66, 'new_value': 24661.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 119}]
2025-05-14 12:01:25,867 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-14 12:01:26,320 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-14 12:01:26,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 858464.94, 'new_value': 860464.94}, {'field': 'total_amount', 'old_value': 858464.94, 'new_value': 860464.94}, {'field': 'order_count', 'old_value': 533, 'new_value': 534}]
2025-05-14 12:01:26,320 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-14 12:01:26,930 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-14 12:01:26,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25602.62, 'new_value': 26919.52}, {'field': 'total_amount', 'old_value': 25799.42, 'new_value': 27116.32}, {'field': 'order_count', 'old_value': 225, 'new_value': 240}]
2025-05-14 12:01:26,930 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-14 12:01:27,383 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-14 12:01:27,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2568.0, 'new_value': 2740.0}, {'field': 'offline_amount', 'old_value': 12754.1, 'new_value': 13428.2}, {'field': 'total_amount', 'old_value': 15322.1, 'new_value': 16168.2}, {'field': 'order_count', 'old_value': 621, 'new_value': 657}]
2025-05-14 12:01:27,383 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-14 12:01:27,836 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-14 12:01:27,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54837.02, 'new_value': 58536.64}, {'field': 'total_amount', 'old_value': 54837.02, 'new_value': 58536.64}, {'field': 'order_count', 'old_value': 181, 'new_value': 192}]
2025-05-14 12:01:27,836 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-14 12:01:28,273 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-14 12:01:28,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 628000.0, 'new_value': 631000.0}, {'field': 'total_amount', 'old_value': 628000.0, 'new_value': 631000.0}, {'field': 'order_count', 'old_value': 331, 'new_value': 332}]
2025-05-14 12:01:28,273 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-14 12:01:28,726 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-14 12:01:28,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46240.0, 'new_value': 48220.0}, {'field': 'total_amount', 'old_value': 46240.0, 'new_value': 48220.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-14 12:01:28,726 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-14 12:01:29,195 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-14 12:01:29,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8903.3, 'new_value': 9514.34}, {'field': 'offline_amount', 'old_value': 15423.01, 'new_value': 16844.26}, {'field': 'total_amount', 'old_value': 24326.31, 'new_value': 26358.6}, {'field': 'order_count', 'old_value': 1004, 'new_value': 1100}]
2025-05-14 12:01:29,195 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-14 12:01:29,679 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-14 12:01:29,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106956.9, 'new_value': 113870.7}, {'field': 'total_amount', 'old_value': 106956.9, 'new_value': 113870.7}, {'field': 'order_count', 'old_value': 381, 'new_value': 405}]
2025-05-14 12:01:29,679 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-14 12:01:30,164 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-14 12:01:30,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 894.0, 'new_value': 1516.0}, {'field': 'total_amount', 'old_value': 31176.0, 'new_value': 31798.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 125}]
2025-05-14 12:01:30,164 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-14 12:01:30,695 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-14 12:01:30,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190675.0, 'new_value': 192029.0}, {'field': 'total_amount', 'old_value': 190675.0, 'new_value': 192029.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 161}]
2025-05-14 12:01:30,695 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-14 12:01:31,133 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-14 12:01:31,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141072.9, 'new_value': 145655.4}, {'field': 'total_amount', 'old_value': 155944.9, 'new_value': 160527.4}, {'field': 'order_count', 'old_value': 1164, 'new_value': 1219}]
2025-05-14 12:01:31,133 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-14 12:01:31,633 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-14 12:01:31,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139269.93, 'new_value': 152263.02}, {'field': 'total_amount', 'old_value': 139269.93, 'new_value': 152263.02}, {'field': 'order_count', 'old_value': 3708, 'new_value': 4047}]
2025-05-14 12:01:31,633 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-14 12:01:32,195 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-14 12:01:32,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184596.6, 'new_value': 189051.6}, {'field': 'total_amount', 'old_value': 184596.6, 'new_value': 189051.6}, {'field': 'order_count', 'old_value': 2089, 'new_value': 2133}]
2025-05-14 12:01:32,195 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-14 12:01:32,664 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-14 12:01:32,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20530.0, 'new_value': 21210.0}, {'field': 'total_amount', 'old_value': 20530.0, 'new_value': 21210.0}]
2025-05-14 12:01:32,664 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-14 12:01:33,101 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-14 12:01:33,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81887.75, 'new_value': 84917.55}, {'field': 'total_amount', 'old_value': 81887.75, 'new_value': 84917.55}, {'field': 'order_count', 'old_value': 391, 'new_value': 409}]
2025-05-14 12:01:33,101 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-14 12:01:33,539 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-14 12:01:33,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11266.05, 'new_value': 12292.36}, {'field': 'offline_amount', 'old_value': 20416.46, 'new_value': 22151.36}, {'field': 'total_amount', 'old_value': 31682.51, 'new_value': 34443.72}, {'field': 'order_count', 'old_value': 1141, 'new_value': 1240}]
2025-05-14 12:01:33,539 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-14 12:01:33,976 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-14 12:01:33,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33689.0, 'new_value': 36985.0}, {'field': 'total_amount', 'old_value': 36097.0, 'new_value': 39393.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 156}]
2025-05-14 12:01:33,976 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-14 12:01:34,523 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-14 12:01:34,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71826.99, 'new_value': 71778.75}, {'field': 'total_amount', 'old_value': 71826.99, 'new_value': 71778.75}, {'field': 'order_count', 'old_value': 6556, 'new_value': 6566}]
2025-05-14 12:01:34,523 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-14 12:01:34,961 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-14 12:01:34,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31873.04, 'new_value': 33880.37}, {'field': 'total_amount', 'old_value': 45678.74, 'new_value': 47686.07}, {'field': 'order_count', 'old_value': 507, 'new_value': 529}]
2025-05-14 12:01:34,961 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-14 12:01:35,336 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-14 12:01:35,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155435.4, 'new_value': 159256.3}, {'field': 'total_amount', 'old_value': 155435.4, 'new_value': 159256.3}, {'field': 'order_count', 'old_value': 249, 'new_value': 258}]
2025-05-14 12:01:35,336 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-14 12:01:35,773 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-14 12:01:35,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40044.8, 'new_value': 42599.48}, {'field': 'offline_amount', 'old_value': 302311.84, 'new_value': 315311.84}, {'field': 'total_amount', 'old_value': 342356.64, 'new_value': 357911.32}, {'field': 'order_count', 'old_value': 1375, 'new_value': 1505}]
2025-05-14 12:01:35,773 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-14 12:01:36,211 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-14 12:01:36,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26633.0, 'new_value': 44922.0}, {'field': 'total_amount', 'old_value': 26633.0, 'new_value': 44922.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 9}]
2025-05-14 12:01:36,211 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-14 12:01:36,648 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-14 12:01:36,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122469.0, 'new_value': 124446.0}, {'field': 'total_amount', 'old_value': 122469.0, 'new_value': 124446.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 84}]
2025-05-14 12:01:36,648 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-14 12:01:37,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-14 12:01:37,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59593.17, 'new_value': 61543.07}, {'field': 'total_amount', 'old_value': 65204.69, 'new_value': 67154.59}, {'field': 'order_count', 'old_value': 5544, 'new_value': 5871}]
2025-05-14 12:01:37,101 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-14 12:01:37,570 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-14 12:01:37,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8827.24, 'new_value': 9667.05}, {'field': 'offline_amount', 'old_value': 18904.69, 'new_value': 20613.96}, {'field': 'total_amount', 'old_value': 27731.93, 'new_value': 30281.01}, {'field': 'order_count', 'old_value': 1457, 'new_value': 1603}]
2025-05-14 12:01:37,570 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-14 12:01:38,070 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-14 12:01:38,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77895.4, 'new_value': 79306.3}, {'field': 'total_amount', 'old_value': 77895.4, 'new_value': 79306.3}, {'field': 'order_count', 'old_value': 756, 'new_value': 777}]
2025-05-14 12:01:38,070 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-14 12:01:38,617 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-14 12:01:38,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54830.0, 'new_value': 59030.0}, {'field': 'total_amount', 'old_value': 54830.0, 'new_value': 59030.0}, {'field': 'order_count', 'old_value': 2845, 'new_value': 3157}]
2025-05-14 12:01:38,617 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-14 12:01:39,039 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-14 12:01:39,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22155.52, 'new_value': 23597.07}, {'field': 'offline_amount', 'old_value': 123603.89, 'new_value': 132915.14}, {'field': 'total_amount', 'old_value': 145759.41, 'new_value': 156512.21}, {'field': 'order_count', 'old_value': 4407, 'new_value': 4751}]
2025-05-14 12:01:39,039 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-14 12:01:39,476 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-14 12:01:39,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18948.2, 'new_value': 19535.1}, {'field': 'total_amount', 'old_value': 20735.1, 'new_value': 21322.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-14 12:01:39,476 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-14 12:01:39,914 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-14 12:01:39,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27065.0, 'new_value': 30257.0}, {'field': 'total_amount', 'old_value': 27414.0, 'new_value': 30606.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-05-14 12:01:39,929 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-14 12:01:40,336 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-14 12:01:40,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3014253.0, 'new_value': 3317813.0}, {'field': 'total_amount', 'old_value': 3014253.0, 'new_value': 3317813.0}, {'field': 'order_count', 'old_value': 51594, 'new_value': 55592}]
2025-05-14 12:01:40,336 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-14 12:01:40,836 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-14 12:01:40,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49268.0, 'new_value': 49666.0}, {'field': 'total_amount', 'old_value': 49268.0, 'new_value': 49666.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 61}]
2025-05-14 12:01:40,836 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-14 12:01:41,445 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-14 12:01:41,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30608.0, 'new_value': 31391.0}, {'field': 'total_amount', 'old_value': 30608.0, 'new_value': 31391.0}, {'field': 'order_count', 'old_value': 205, 'new_value': 211}]
2025-05-14 12:01:41,445 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-14 12:01:41,914 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-14 12:01:41,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26471.73, 'new_value': 28082.03}, {'field': 'offline_amount', 'old_value': 231905.89, 'new_value': 238712.66}, {'field': 'total_amount', 'old_value': 258377.62, 'new_value': 266794.69}, {'field': 'order_count', 'old_value': 2136, 'new_value': 2218}]
2025-05-14 12:01:41,914 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-14 12:01:42,383 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-14 12:01:42,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238319.5, 'new_value': 248142.5}, {'field': 'total_amount', 'old_value': 279306.48, 'new_value': 289129.48}, {'field': 'order_count', 'old_value': 2123, 'new_value': 2217}]
2025-05-14 12:01:42,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-14 12:01:42,836 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-14 12:01:42,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63802.58, 'new_value': 66946.21}, {'field': 'total_amount', 'old_value': 63802.58, 'new_value': 66946.21}, {'field': 'order_count', 'old_value': 1850, 'new_value': 1940}]
2025-05-14 12:01:42,836 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-14 12:01:43,258 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-14 12:01:43,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42207.74, 'new_value': 42522.74}, {'field': 'total_amount', 'old_value': 42207.74, 'new_value': 42522.74}, {'field': 'order_count', 'old_value': 1451, 'new_value': 1559}]
2025-05-14 12:01:43,258 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-14 12:01:43,711 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-14 12:01:43,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63101.0, 'new_value': 67827.0}, {'field': 'total_amount', 'old_value': 63101.0, 'new_value': 67827.0}, {'field': 'order_count', 'old_value': 2302, 'new_value': 2457}]
2025-05-14 12:01:43,711 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-14 12:01:44,164 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-14 12:01:44,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45409.92, 'new_value': 47854.98}, {'field': 'offline_amount', 'old_value': 24255.76, 'new_value': 24896.56}, {'field': 'total_amount', 'old_value': 69665.68, 'new_value': 72751.54}, {'field': 'order_count', 'old_value': 4473, 'new_value': 4663}]
2025-05-14 12:01:44,164 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-14 12:01:44,586 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-14 12:01:44,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227852.92, 'new_value': 233446.69}, {'field': 'total_amount', 'old_value': 227852.92, 'new_value': 233446.69}, {'field': 'order_count', 'old_value': 1051, 'new_value': 1084}]
2025-05-14 12:01:44,586 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-14 12:01:45,054 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-14 12:01:45,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15228.07, 'new_value': 15077.07}, {'field': 'total_amount', 'old_value': 15228.07, 'new_value': 15077.07}, {'field': 'order_count', 'old_value': 1423, 'new_value': 1505}]
2025-05-14 12:01:45,054 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-14 12:01:45,508 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-14 12:01:45,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80189.76, 'new_value': 85592.82}, {'field': 'total_amount', 'old_value': 80189.76, 'new_value': 85592.82}, {'field': 'order_count', 'old_value': 133, 'new_value': 140}]
2025-05-14 12:01:45,508 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-14 12:01:46,039 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-14 12:01:46,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103488.0, 'new_value': 113256.0}, {'field': 'total_amount', 'old_value': 103488.0, 'new_value': 113256.0}, {'field': 'order_count', 'old_value': 8624, 'new_value': 9438}]
2025-05-14 12:01:46,039 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-14 12:01:46,492 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-14 12:01:46,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61004.7, 'new_value': 63116.7}, {'field': 'total_amount', 'old_value': 61004.7, 'new_value': 63116.7}, {'field': 'order_count', 'old_value': 529, 'new_value': 544}]
2025-05-14 12:01:46,492 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-14 12:01:46,929 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-14 12:01:46,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45509.69, 'new_value': 48466.92}, {'field': 'offline_amount', 'old_value': 138759.99, 'new_value': 144293.16}, {'field': 'total_amount', 'old_value': 184269.68, 'new_value': 192760.08}, {'field': 'order_count', 'old_value': 5828, 'new_value': 6144}]
2025-05-14 12:01:46,929 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-14 12:01:47,461 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-14 12:01:47,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176575.0, 'new_value': 217751.0}, {'field': 'total_amount', 'old_value': 176575.0, 'new_value': 217751.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-14 12:01:47,461 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-14 12:01:47,898 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-14 12:01:47,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27885.2, 'new_value': 26073.4}, {'field': 'total_amount', 'old_value': 27885.2, 'new_value': 26073.4}, {'field': 'order_count', 'old_value': 275, 'new_value': 279}]
2025-05-14 12:01:47,898 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-14 12:01:48,382 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-14 12:01:48,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4549700.0, 'new_value': 4574700.0}, {'field': 'total_amount', 'old_value': 4549700.0, 'new_value': 4574700.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-05-14 12:01:48,382 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-14 12:01:48,851 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-14 12:01:48,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25792.4, 'new_value': 27827.4}, {'field': 'total_amount', 'old_value': 25792.4, 'new_value': 27827.4}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-14 12:01:48,851 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-14 12:01:49,320 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-14 12:01:49,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25496.5, 'new_value': 23574.5}, {'field': 'total_amount', 'old_value': 25496.5, 'new_value': 23574.5}, {'field': 'order_count', 'old_value': 1057, 'new_value': 1160}]
2025-05-14 12:01:49,320 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-14 12:01:49,726 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-14 12:01:49,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26183.9, 'new_value': 28040.6}, {'field': 'total_amount', 'old_value': 26705.1, 'new_value': 28561.8}, {'field': 'order_count', 'old_value': 87, 'new_value': 89}]
2025-05-14 12:01:49,726 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-14 12:01:50,132 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-14 12:01:50,132 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 155706.65, 'new_value': 167181.63}, {'field': 'offline_amount', 'old_value': 10051.55, 'new_value': 10253.95}, {'field': 'total_amount', 'old_value': 165758.2, 'new_value': 177435.58}, {'field': 'order_count', 'old_value': 6121, 'new_value': 6578}]
2025-05-14 12:01:50,132 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-14 12:01:50,601 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-14 12:01:50,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15620.5, 'new_value': 16569.5}, {'field': 'offline_amount', 'old_value': 60434.8, 'new_value': 61923.8}, {'field': 'total_amount', 'old_value': 76055.3, 'new_value': 78493.3}, {'field': 'order_count', 'old_value': 106, 'new_value': 111}]
2025-05-14 12:01:50,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-14 12:01:51,054 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-14 12:01:51,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22324.55, 'new_value': 24521.15}, {'field': 'total_amount', 'old_value': 22324.55, 'new_value': 24521.15}, {'field': 'order_count', 'old_value': 989, 'new_value': 1076}]
2025-05-14 12:01:51,054 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-14 12:01:51,476 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-14 12:01:51,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26121.0, 'new_value': 25620.0}, {'field': 'total_amount', 'old_value': 26121.0, 'new_value': 25620.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 132}]
2025-05-14 12:01:51,476 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-14 12:01:51,867 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-14 12:01:51,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190545.0, 'new_value': 175545.0}, {'field': 'total_amount', 'old_value': 199363.99, 'new_value': 184363.99}, {'field': 'order_count', 'old_value': 33, 'new_value': 32}]
2025-05-14 12:01:51,867 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-14 12:01:52,367 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-14 12:01:52,367 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63058.56, 'new_value': 66897.64}, {'field': 'offline_amount', 'old_value': 152994.81, 'new_value': 158974.55}, {'field': 'total_amount', 'old_value': 216053.37, 'new_value': 225872.19}, {'field': 'order_count', 'old_value': 2561, 'new_value': 2719}]
2025-05-14 12:01:52,367 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-14 12:01:52,836 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-14 12:01:52,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109698.0, 'new_value': 98627.0}, {'field': 'total_amount', 'old_value': 123752.0, 'new_value': 112681.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 104}]
2025-05-14 12:01:52,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-14 12:01:53,304 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-14 12:01:53,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49661.75, 'new_value': 55844.95}, {'field': 'total_amount', 'old_value': 122387.45, 'new_value': 128570.65}, {'field': 'order_count', 'old_value': 3123, 'new_value': 3330}]
2025-05-14 12:01:53,304 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-14 12:01:53,773 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-14 12:01:53,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131860.0, 'new_value': 133526.0}, {'field': 'total_amount', 'old_value': 131860.0, 'new_value': 133526.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-05-14 12:01:53,773 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-14 12:01:54,289 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-14 12:01:54,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195433.0, 'new_value': 216382.0}, {'field': 'total_amount', 'old_value': 195433.0, 'new_value': 216382.0}, {'field': 'order_count', 'old_value': 5031, 'new_value': 5769}]
2025-05-14 12:01:54,289 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-14 12:01:54,757 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-14 12:01:54,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258531.68, 'new_value': 239855.61}, {'field': 'total_amount', 'old_value': 258531.68, 'new_value': 239855.61}, {'field': 'order_count', 'old_value': 444, 'new_value': 457}]
2025-05-14 12:01:54,757 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-14 12:01:55,242 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-14 12:01:55,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38743.94, 'new_value': 41316.35}, {'field': 'offline_amount', 'old_value': 139104.66, 'new_value': 144760.94}, {'field': 'total_amount', 'old_value': 177848.6, 'new_value': 186077.29}, {'field': 'order_count', 'old_value': 2545, 'new_value': 2588}]
2025-05-14 12:01:55,242 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-14 12:01:55,773 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-14 12:01:55,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42644.2, 'new_value': 44315.2}, {'field': 'total_amount', 'old_value': 42644.2, 'new_value': 44315.2}, {'field': 'order_count', 'old_value': 113, 'new_value': 119}]
2025-05-14 12:01:55,773 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-14 12:01:56,273 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-14 12:01:56,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44012.95, 'new_value': 54187.79}, {'field': 'total_amount', 'old_value': 44012.95, 'new_value': 54187.79}, {'field': 'order_count', 'old_value': 1208, 'new_value': 1505}]
2025-05-14 12:01:56,273 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-14 12:01:56,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-14 12:01:56,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17028.0, 'new_value': 17895.0}, {'field': 'total_amount', 'old_value': 17028.0, 'new_value': 17895.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 175}]
2025-05-14 12:01:56,711 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-14 12:01:57,148 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-14 12:01:57,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20332.45, 'new_value': 22163.75}, {'field': 'total_amount', 'old_value': 20332.45, 'new_value': 22163.75}, {'field': 'order_count', 'old_value': 82, 'new_value': 90}]
2025-05-14 12:01:57,148 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-14 12:01:57,617 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-14 12:01:57,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85684.5, 'new_value': 95951.91}, {'field': 'total_amount', 'old_value': 85684.5, 'new_value': 95951.91}, {'field': 'order_count', 'old_value': 3500, 'new_value': 3971}]
2025-05-14 12:01:57,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-14 12:01:58,117 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-14 12:01:58,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26376.53, 'new_value': 28440.43}, {'field': 'offline_amount', 'old_value': 17040.16, 'new_value': 18282.49}, {'field': 'total_amount', 'old_value': 43416.69, 'new_value': 46722.92}, {'field': 'order_count', 'old_value': 2376, 'new_value': 2564}]
2025-05-14 12:01:58,117 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-14 12:01:58,523 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-14 12:01:58,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39837.0, 'new_value': 42601.0}, {'field': 'total_amount', 'old_value': 40448.0, 'new_value': 43212.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-14 12:01:58,523 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-14 12:01:58,929 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-14 12:01:58,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8924.6, 'new_value': 9374.53}, {'field': 'total_amount', 'old_value': 72179.6, 'new_value': 72629.53}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-05-14 12:01:58,929 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-14 12:01:59,367 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-14 12:01:59,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44156.0, 'new_value': 44106.0}, {'field': 'total_amount', 'old_value': 44156.0, 'new_value': 44106.0}]
2025-05-14 12:01:59,367 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-14 12:01:59,851 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-14 12:01:59,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18746.0, 'new_value': 18884.0}, {'field': 'total_amount', 'old_value': 18746.0, 'new_value': 18884.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 174}]
2025-05-14 12:01:59,851 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-14 12:02:00,257 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-14 12:02:00,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9947.38, 'new_value': 10032.18}, {'field': 'total_amount', 'old_value': 9947.38, 'new_value': 10032.18}, {'field': 'order_count', 'old_value': 90, 'new_value': 94}]
2025-05-14 12:02:00,257 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-14 12:02:00,679 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-14 12:02:00,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 432140.6, 'new_value': 444256.7}, {'field': 'total_amount', 'old_value': 432140.6, 'new_value': 444256.7}, {'field': 'order_count', 'old_value': 1103, 'new_value': 1161}]
2025-05-14 12:02:00,679 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-14 12:02:01,195 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-14 12:02:01,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216665.77, 'new_value': 226207.59}, {'field': 'total_amount', 'old_value': 216665.77, 'new_value': 226207.59}, {'field': 'order_count', 'old_value': 773, 'new_value': 812}]
2025-05-14 12:02:01,195 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-14 12:02:01,617 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-14 12:02:01,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163516.5, 'new_value': 173233.5}, {'field': 'total_amount', 'old_value': 163516.5, 'new_value': 173233.5}, {'field': 'order_count', 'old_value': 3856, 'new_value': 4178}]
2025-05-14 12:02:01,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-14 12:02:02,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-14 12:02:02,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18853.47, 'new_value': 20412.66}, {'field': 'total_amount', 'old_value': 18853.47, 'new_value': 20412.66}, {'field': 'order_count', 'old_value': 2400, 'new_value': 2609}]
2025-05-14 12:02:02,070 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-14 12:02:02,492 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-14 12:02:02,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15386.61, 'new_value': 16108.79}, {'field': 'offline_amount', 'old_value': 30478.5, 'new_value': 32934.18}, {'field': 'total_amount', 'old_value': 45865.11, 'new_value': 49042.97}, {'field': 'order_count', 'old_value': 392, 'new_value': 416}]
2025-05-14 12:02:02,492 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-14 12:02:03,039 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-14 12:02:03,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 351689.0, 'new_value': 373529.0}, {'field': 'total_amount', 'old_value': 351689.0, 'new_value': 373529.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-05-14 12:02:03,039 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-14 12:02:03,492 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-14 12:02:03,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12472.51, 'new_value': 13610.1}, {'field': 'offline_amount', 'old_value': 17072.4, 'new_value': 17988.6}, {'field': 'total_amount', 'old_value': 29544.91, 'new_value': 31598.7}, {'field': 'order_count', 'old_value': 1286, 'new_value': 1381}]
2025-05-14 12:02:03,492 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-14 12:02:04,039 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-14 12:02:04,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32128.0, 'new_value': 37818.0}, {'field': 'total_amount', 'old_value': 37329.0, 'new_value': 43019.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 131}]
2025-05-14 12:02:04,039 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-14 12:02:04,492 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-14 12:02:04,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 727168.4, 'new_value': 866902.53}, {'field': 'offline_amount', 'old_value': 136815.3, 'new_value': 137798.3}, {'field': 'total_amount', 'old_value': 863983.7, 'new_value': 1004700.83}, {'field': 'order_count', 'old_value': 3009, 'new_value': 3525}]
2025-05-14 12:02:04,492 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-14 12:02:04,961 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-14 12:02:04,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15409.0, 'new_value': 15236.0}, {'field': 'total_amount', 'old_value': 16785.0, 'new_value': 16612.0}, {'field': 'order_count', 'old_value': 1745, 'new_value': 1849}]
2025-05-14 12:02:04,976 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-14 12:02:05,445 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-14 12:02:05,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149735.0, 'new_value': 171937.2}, {'field': 'total_amount', 'old_value': 149735.0, 'new_value': 171937.2}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-14 12:02:05,445 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-14 12:02:05,961 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-14 12:02:05,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11772.0, 'new_value': 12671.0}, {'field': 'total_amount', 'old_value': 11772.0, 'new_value': 12671.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-14 12:02:05,961 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-14 12:02:06,445 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-14 12:02:06,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8662.4, 'new_value': 9999.2}, {'field': 'offline_amount', 'old_value': 27217.2, 'new_value': 32554.2}, {'field': 'total_amount', 'old_value': 35879.6, 'new_value': 42553.4}, {'field': 'order_count', 'old_value': 93, 'new_value': 102}]
2025-05-14 12:02:06,445 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-14 12:02:06,820 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-14 12:02:06,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246004.04, 'new_value': 258248.04}, {'field': 'total_amount', 'old_value': 246004.04, 'new_value': 258248.04}, {'field': 'order_count', 'old_value': 1245, 'new_value': 1287}]
2025-05-14 12:02:06,820 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-14 12:02:07,320 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-14 12:02:07,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5277.6, 'new_value': 5729.6}, {'field': 'total_amount', 'old_value': 5793.6, 'new_value': 6245.6}, {'field': 'order_count', 'old_value': 59, 'new_value': 63}]
2025-05-14 12:02:07,320 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-14 12:02:07,742 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-14 12:02:07,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350528.0, 'new_value': 374426.0}, {'field': 'total_amount', 'old_value': 350968.0, 'new_value': 374866.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 158}]
2025-05-14 12:02:07,742 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-14 12:02:08,226 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-14 12:02:08,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120673.0, 'new_value': 122872.0}, {'field': 'total_amount', 'old_value': 120673.0, 'new_value': 122872.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-05-14 12:02:08,226 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-14 12:02:08,679 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-14 12:02:08,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56665.3, 'new_value': 57479.2}, {'field': 'offline_amount', 'old_value': 84437.1, 'new_value': 85828.4}, {'field': 'total_amount', 'old_value': 141102.4, 'new_value': 143307.6}, {'field': 'order_count', 'old_value': 2842, 'new_value': 2886}]
2025-05-14 12:02:08,679 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-14 12:02:09,164 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-14 12:02:09,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6304.4, 'new_value': 6403.4}, {'field': 'total_amount', 'old_value': 12474.3, 'new_value': 12573.3}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-05-14 12:02:09,164 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-14 12:02:09,570 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-14 12:02:09,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103424.0, 'new_value': 111181.0}, {'field': 'total_amount', 'old_value': 103424.0, 'new_value': 111181.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 177}]
2025-05-14 12:02:09,570 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-14 12:02:10,039 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-14 12:02:10,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312336.4, 'new_value': 329210.8}, {'field': 'total_amount', 'old_value': 312863.41, 'new_value': 329737.81}, {'field': 'order_count', 'old_value': 722, 'new_value': 767}]
2025-05-14 12:02:10,039 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-14 12:02:10,492 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-14 12:02:10,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81630.0, 'new_value': 87890.0}, {'field': 'total_amount', 'old_value': 81630.0, 'new_value': 87890.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-14 12:02:10,492 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-14 12:02:10,992 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-14 12:02:10,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4702.86, 'new_value': 5099.4}, {'field': 'offline_amount', 'old_value': 16601.38, 'new_value': 17161.88}, {'field': 'total_amount', 'old_value': 21304.24, 'new_value': 22261.28}, {'field': 'order_count', 'old_value': 764, 'new_value': 792}]
2025-05-14 12:02:10,992 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-14 12:02:11,460 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-14 12:02:11,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 705172.0, 'new_value': 720372.0}, {'field': 'total_amount', 'old_value': 705172.0, 'new_value': 720372.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 81}]
2025-05-14 12:02:11,460 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-14 12:02:11,898 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-14 12:02:11,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23438.11, 'new_value': 27482.11}, {'field': 'offline_amount', 'old_value': 28798.78, 'new_value': 28839.61}, {'field': 'total_amount', 'old_value': 52236.89, 'new_value': 56321.72}, {'field': 'order_count', 'old_value': 175, 'new_value': 180}]
2025-05-14 12:02:11,898 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-14 12:02:12,523 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-14 12:02:12,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149227.0, 'new_value': 152331.0}, {'field': 'total_amount', 'old_value': 149227.0, 'new_value': 152331.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-14 12:02:12,523 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-14 12:02:12,976 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-14 12:02:12,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85570.16, 'new_value': 87327.32}, {'field': 'total_amount', 'old_value': 85570.16, 'new_value': 87327.32}, {'field': 'order_count', 'old_value': 2147, 'new_value': 2211}]
2025-05-14 12:02:12,992 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-14 12:02:13,382 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-14 12:02:13,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190799.47, 'new_value': 198006.85}, {'field': 'total_amount', 'old_value': 190799.47, 'new_value': 198006.85}, {'field': 'order_count', 'old_value': 1561, 'new_value': 1654}]
2025-05-14 12:02:13,382 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-14 12:02:13,898 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-14 12:02:13,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52377.0, 'new_value': 52435.0}, {'field': 'total_amount', 'old_value': 52377.0, 'new_value': 52435.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 245}]
2025-05-14 12:02:13,898 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-14 12:02:14,335 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-14 12:02:14,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 180859.22, 'new_value': 188254.12}, {'field': 'offline_amount', 'old_value': 5710.0, 'new_value': 5770.0}, {'field': 'total_amount', 'old_value': 186569.22, 'new_value': 194024.12}, {'field': 'order_count', 'old_value': 1425, 'new_value': 1498}]
2025-05-14 12:02:14,335 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-14 12:02:14,867 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-14 12:02:14,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11582.0, 'new_value': 12010.0}, {'field': 'total_amount', 'old_value': 11582.0, 'new_value': 12010.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-05-14 12:02:14,867 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-14 12:02:15,304 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-14 12:02:15,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10584.5, 'new_value': 11138.1}, {'field': 'total_amount', 'old_value': 10584.5, 'new_value': 11138.1}, {'field': 'order_count', 'old_value': 362, 'new_value': 385}]
2025-05-14 12:02:15,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-14 12:02:15,945 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-14 12:02:15,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7450.7, 'new_value': 7465.7}, {'field': 'total_amount', 'old_value': 7879.7, 'new_value': 7894.7}, {'field': 'order_count', 'old_value': 114, 'new_value': 115}]
2025-05-14 12:02:15,945 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-14 12:02:16,414 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-14 12:02:16,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19751.9, 'new_value': 20494.6}, {'field': 'total_amount', 'old_value': 24391.4, 'new_value': 25134.1}, {'field': 'order_count', 'old_value': 267, 'new_value': 277}]
2025-05-14 12:02:16,414 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-14 12:02:16,851 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-14 12:02:16,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2314.0, 'new_value': 2479.0}, {'field': 'offline_amount', 'old_value': 19955.0, 'new_value': 20680.0}, {'field': 'total_amount', 'old_value': 22269.0, 'new_value': 23159.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 180}]
2025-05-14 12:02:16,851 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-14 12:02:17,351 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-14 12:02:17,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8021.0, 'new_value': 8247.0}, {'field': 'total_amount', 'old_value': 21848.0, 'new_value': 22074.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-05-14 12:02:17,351 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-14 12:02:17,820 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-14 12:02:17,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113532.0, 'new_value': 118369.0}, {'field': 'total_amount', 'old_value': 113532.0, 'new_value': 118369.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 134}]
2025-05-14 12:02:17,820 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-14 12:02:18,273 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-14 12:02:18,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 408499.0, 'new_value': 446053.0}, {'field': 'total_amount', 'old_value': 408499.0, 'new_value': 446053.0}, {'field': 'order_count', 'old_value': 844, 'new_value': 922}]
2025-05-14 12:02:18,273 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-14 12:02:18,757 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-14 12:02:18,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14618.66, 'new_value': 15965.79}, {'field': 'total_amount', 'old_value': 14618.66, 'new_value': 15965.79}, {'field': 'order_count', 'old_value': 547, 'new_value': 593}]
2025-05-14 12:02:18,757 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-14 12:02:19,195 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-14 12:02:19,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382430.06, 'new_value': 404739.92}, {'field': 'total_amount', 'old_value': 382430.06, 'new_value': 404739.92}, {'field': 'order_count', 'old_value': 2746, 'new_value': 2933}]
2025-05-14 12:02:19,195 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-14 12:02:19,695 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-14 12:02:19,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30828.76, 'new_value': 33133.12}, {'field': 'offline_amount', 'old_value': 243734.56, 'new_value': 255320.26}, {'field': 'total_amount', 'old_value': 274563.32, 'new_value': 288453.38}, {'field': 'order_count', 'old_value': 1168, 'new_value': 1259}]
2025-05-14 12:02:19,695 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-14 12:02:20,148 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-14 12:02:20,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382276.0, 'new_value': 409104.0}, {'field': 'total_amount', 'old_value': 382276.0, 'new_value': 409104.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 106}]
2025-05-14 12:02:20,148 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-14 12:02:20,617 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-14 12:02:20,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 782.57, 'new_value': 853.09}, {'field': 'offline_amount', 'old_value': 15617.2, 'new_value': 15996.77}, {'field': 'total_amount', 'old_value': 16399.77, 'new_value': 16849.86}, {'field': 'order_count', 'old_value': 577, 'new_value': 594}]
2025-05-14 12:02:20,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-14 12:02:21,039 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-14 12:02:21,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488797.0, 'new_value': 514422.0}, {'field': 'total_amount', 'old_value': 488797.0, 'new_value': 514422.0}, {'field': 'order_count', 'old_value': 2205, 'new_value': 2321}]
2025-05-14 12:02:21,039 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-14 12:02:21,492 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-14 12:02:21,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4729.0, 'new_value': 8689.0}, {'field': 'total_amount', 'old_value': 4729.0, 'new_value': 8689.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-14 12:02:21,492 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-14 12:02:21,992 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-14 12:02:21,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3651.42, 'new_value': 3738.92}, {'field': 'offline_amount', 'old_value': 217453.34, 'new_value': 227382.94}, {'field': 'total_amount', 'old_value': 221104.76, 'new_value': 231121.86}, {'field': 'order_count', 'old_value': 9890, 'new_value': 10368}]
2025-05-14 12:02:21,992 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-14 12:02:22,382 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-14 12:02:22,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46417.0, 'new_value': 48781.0}, {'field': 'total_amount', 'old_value': 46417.0, 'new_value': 48781.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 125}]
2025-05-14 12:02:22,382 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-14 12:02:22,835 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-14 12:02:22,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191720.05, 'new_value': 201413.46}, {'field': 'total_amount', 'old_value': 213882.43, 'new_value': 223575.84}, {'field': 'order_count', 'old_value': 8839, 'new_value': 9255}]
2025-05-14 12:02:22,835 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-14 12:02:23,304 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-14 12:02:23,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7584.6, 'new_value': 7621.5}, {'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 7000.0}, {'field': 'total_amount', 'old_value': 12584.6, 'new_value': 14621.5}, {'field': 'order_count', 'old_value': 116, 'new_value': 118}]
2025-05-14 12:02:23,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-14 12:02:23,742 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-14 12:02:23,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212256.01, 'new_value': 216756.01}, {'field': 'total_amount', 'old_value': 212256.01, 'new_value': 216756.01}, {'field': 'order_count', 'old_value': 1349, 'new_value': 1350}]
2025-05-14 12:02:23,757 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-14 12:02:24,289 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-14 12:02:24,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82137.2, 'new_value': 86698.3}, {'field': 'total_amount', 'old_value': 82137.2, 'new_value': 86698.3}, {'field': 'order_count', 'old_value': 149, 'new_value': 160}]
2025-05-14 12:02:24,289 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-14 12:02:24,742 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-14 12:02:24,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67121.0, 'new_value': 67718.0}, {'field': 'total_amount', 'old_value': 82325.0, 'new_value': 82922.0}, {'field': 'order_count', 'old_value': 1777, 'new_value': 1851}]
2025-05-14 12:02:24,742 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-14 12:02:25,179 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-14 12:02:25,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57863.0, 'new_value': 61455.0}, {'field': 'total_amount', 'old_value': 57863.0, 'new_value': 61455.0}, {'field': 'order_count', 'old_value': 318, 'new_value': 351}]
2025-05-14 12:02:25,179 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-14 12:02:25,679 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-14 12:02:25,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97476.07, 'new_value': 97021.07}, {'field': 'total_amount', 'old_value': 97476.07, 'new_value': 97021.07}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1243}]
2025-05-14 12:02:25,679 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-14 12:02:26,132 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-14 12:02:26,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13307.0, 'new_value': 15706.0}, {'field': 'total_amount', 'old_value': 13307.0, 'new_value': 15706.0}]
2025-05-14 12:02:26,132 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-14 12:02:26,664 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-14 12:02:26,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93183.0, 'new_value': 94461.0}, {'field': 'total_amount', 'old_value': 93183.0, 'new_value': 94461.0}, {'field': 'order_count', 'old_value': 2990, 'new_value': 3031}]
2025-05-14 12:02:26,664 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-14 12:02:27,117 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-14 12:02:27,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3230.0, 'new_value': 6818.0}, {'field': 'total_amount', 'old_value': 3230.0, 'new_value': 6818.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 22}]
2025-05-14 12:02:27,117 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-14 12:02:27,679 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-14 12:02:27,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45124.57, 'new_value': 51528.36}, {'field': 'offline_amount', 'old_value': 235698.76, 'new_value': 239682.48}, {'field': 'total_amount', 'old_value': 280823.33, 'new_value': 291210.84}, {'field': 'order_count', 'old_value': 1899, 'new_value': 2071}]
2025-05-14 12:02:27,679 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-14 12:02:28,226 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-14 12:02:28,226 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42897.56, 'new_value': 47187.92}, {'field': 'offline_amount', 'old_value': 54957.03, 'new_value': 59425.7}, {'field': 'total_amount', 'old_value': 97854.59, 'new_value': 106613.62}, {'field': 'order_count', 'old_value': 3964, 'new_value': 4313}]
2025-05-14 12:02:28,226 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-14 12:02:28,742 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-14 12:02:28,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154019.52, 'new_value': 157963.06}, {'field': 'total_amount', 'old_value': 203125.56, 'new_value': 207069.1}, {'field': 'order_count', 'old_value': 353, 'new_value': 363}]
2025-05-14 12:02:28,742 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-14 12:02:29,273 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-14 12:02:29,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131415.07, 'new_value': 135894.31}, {'field': 'total_amount', 'old_value': 150588.5, 'new_value': 155067.74}, {'field': 'order_count', 'old_value': 3184, 'new_value': 3264}]
2025-05-14 12:02:29,273 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-14 12:02:29,804 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-14 12:02:29,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31998.0, 'new_value': 32357.0}, {'field': 'total_amount', 'old_value': 31998.0, 'new_value': 32357.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 67}]
2025-05-14 12:02:29,804 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-14 12:02:30,242 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-14 12:02:30,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37926.0, 'new_value': 50206.0}, {'field': 'total_amount', 'old_value': 37926.0, 'new_value': 50206.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-14 12:02:30,242 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-14 12:02:30,679 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-14 12:02:30,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61621.0, 'new_value': 63431.0}, {'field': 'offline_amount', 'old_value': 48425.24, 'new_value': 49538.86}, {'field': 'total_amount', 'old_value': 110046.24, 'new_value': 112969.86}, {'field': 'order_count', 'old_value': 718, 'new_value': 737}]
2025-05-14 12:02:30,679 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-14 12:02:31,132 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-14 12:02:31,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205054.1, 'new_value': 217325.7}, {'field': 'total_amount', 'old_value': 205054.1, 'new_value': 217325.7}, {'field': 'order_count', 'old_value': 245, 'new_value': 267}]
2025-05-14 12:02:31,132 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-14 12:02:31,601 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-14 12:02:31,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42491.0, 'new_value': 47204.0}, {'field': 'total_amount', 'old_value': 42491.0, 'new_value': 47204.0}, {'field': 'order_count', 'old_value': 964, 'new_value': 1091}]
2025-05-14 12:02:31,601 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-14 12:02:32,101 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-14 12:02:32,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 522679.0, 'new_value': 544003.0}, {'field': 'total_amount', 'old_value': 522679.0, 'new_value': 544003.0}, {'field': 'order_count', 'old_value': 584, 'new_value': 624}]
2025-05-14 12:02:32,101 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-14 12:02:32,554 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-14 12:02:32,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 374086.58, 'new_value': 392117.58}, {'field': 'total_amount', 'old_value': 374086.58, 'new_value': 392117.58}, {'field': 'order_count', 'old_value': 2818, 'new_value': 2965}]
2025-05-14 12:02:32,554 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-14 12:02:33,101 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-14 12:02:33,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352028.0, 'new_value': 373026.0}, {'field': 'total_amount', 'old_value': 352028.0, 'new_value': 373026.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-14 12:02:33,101 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-14 12:02:33,538 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-14 12:02:33,538 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 196406.12, 'new_value': 202645.83}, {'field': 'offline_amount', 'old_value': 678428.55, 'new_value': 710847.77}, {'field': 'total_amount', 'old_value': 874834.67, 'new_value': 913493.6}, {'field': 'order_count', 'old_value': 4363, 'new_value': 4576}]
2025-05-14 12:02:33,538 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-14 12:02:33,992 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-14 12:02:33,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44067.97, 'new_value': 45631.39}, {'field': 'total_amount', 'old_value': 44067.97, 'new_value': 45631.39}, {'field': 'order_count', 'old_value': 2507, 'new_value': 2607}]
2025-05-14 12:02:33,992 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-14 12:02:34,445 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-14 12:02:34,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22937.0, 'new_value': 23336.0}, {'field': 'total_amount', 'old_value': 22937.0, 'new_value': 23336.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-14 12:02:34,445 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-14 12:02:34,976 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-14 12:02:34,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29260.23, 'new_value': 31252.23}, {'field': 'offline_amount', 'old_value': 26354.33, 'new_value': 27420.33}, {'field': 'total_amount', 'old_value': 55614.56, 'new_value': 58672.56}, {'field': 'order_count', 'old_value': 1098, 'new_value': 1161}]
2025-05-14 12:02:34,976 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-14 12:02:35,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-14 12:02:35,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7188.45, 'new_value': 7942.45}, {'field': 'offline_amount', 'old_value': 141323.0, 'new_value': 150301.0}, {'field': 'total_amount', 'old_value': 148511.45, 'new_value': 158243.45}, {'field': 'order_count', 'old_value': 727, 'new_value': 781}]
2025-05-14 12:02:35,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-14 12:02:35,867 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-14 12:02:35,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66186.8, 'new_value': 71103.0}, {'field': 'offline_amount', 'old_value': 56692.2, 'new_value': 59705.7}, {'field': 'total_amount', 'old_value': 122879.0, 'new_value': 130808.7}, {'field': 'order_count', 'old_value': 2884, 'new_value': 3082}]
2025-05-14 12:02:35,867 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-14 12:02:36,413 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-14 12:02:36,413 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-14 12:02:36,413 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-14 12:02:36,945 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-14 12:02:36,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158446.5, 'new_value': 160182.5}, {'field': 'total_amount', 'old_value': 158446.5, 'new_value': 160182.5}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-14 12:02:36,945 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-14 12:02:37,413 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-14 12:02:37,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146479.19, 'new_value': 150285.19}, {'field': 'total_amount', 'old_value': 146479.19, 'new_value': 150285.19}, {'field': 'order_count', 'old_value': 889, 'new_value': 917}]
2025-05-14 12:02:37,413 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-14 12:02:37,992 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-14 12:02:37,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24033.83, 'new_value': 27716.83}, {'field': 'total_amount', 'old_value': 24033.83, 'new_value': 27716.83}, {'field': 'order_count', 'old_value': 72, 'new_value': 75}]
2025-05-14 12:02:37,992 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-14 12:02:38,445 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-14 12:02:38,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23068.05, 'new_value': 22558.04}, {'field': 'offline_amount', 'old_value': 626024.4, 'new_value': 629167.83}, {'field': 'total_amount', 'old_value': 649092.45, 'new_value': 651725.87}, {'field': 'order_count', 'old_value': 2930, 'new_value': 2936}]
2025-05-14 12:02:38,445 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-14 12:02:38,913 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-14 12:02:38,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157615.0, 'new_value': 170394.0}, {'field': 'total_amount', 'old_value': 171390.0, 'new_value': 184169.0}, {'field': 'order_count', 'old_value': 3257, 'new_value': 3778}]
2025-05-14 12:02:38,913 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-14 12:02:39,335 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-14 12:02:39,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39308.59, 'new_value': 42850.58}, {'field': 'offline_amount', 'old_value': 113165.37, 'new_value': 115036.26}, {'field': 'total_amount', 'old_value': 152473.96, 'new_value': 157886.84}, {'field': 'order_count', 'old_value': 2519, 'new_value': 2656}]
2025-05-14 12:02:39,335 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-14 12:02:39,835 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-14 12:02:39,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145283.0, 'new_value': 161183.0}, {'field': 'total_amount', 'old_value': 149383.0, 'new_value': 165283.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 111}]
2025-05-14 12:02:39,835 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-14 12:02:40,226 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-14 12:02:40,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395600.18, 'new_value': 413767.75}, {'field': 'total_amount', 'old_value': 395600.18, 'new_value': 413767.75}, {'field': 'order_count', 'old_value': 4400, 'new_value': 4675}]
2025-05-14 12:02:40,226 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-14 12:02:40,679 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-14 12:02:40,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311041.8, 'new_value': 315141.0}, {'field': 'total_amount', 'old_value': 454775.2, 'new_value': 458874.4}, {'field': 'order_count', 'old_value': 3041, 'new_value': 3045}]
2025-05-14 12:02:40,679 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-14 12:02:41,132 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-14 12:02:41,132 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111835.99, 'new_value': 118816.86}, {'field': 'offline_amount', 'old_value': 418393.79, 'new_value': 435912.22}, {'field': 'total_amount', 'old_value': 530229.78, 'new_value': 554729.08}, {'field': 'order_count', 'old_value': 2914, 'new_value': 3085}]
2025-05-14 12:02:41,132 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-14 12:02:41,617 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-14 12:02:41,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16366.81, 'new_value': 17397.29}, {'field': 'offline_amount', 'old_value': 207254.97, 'new_value': 207295.97}, {'field': 'total_amount', 'old_value': 223621.78, 'new_value': 224693.26}, {'field': 'order_count', 'old_value': 1266, 'new_value': 8827}]
2025-05-14 12:02:41,617 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-14 12:02:42,085 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-14 12:02:42,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117548.0, 'new_value': 121539.0}, {'field': 'total_amount', 'old_value': 117548.0, 'new_value': 121539.0}, {'field': 'order_count', 'old_value': 1945, 'new_value': 1982}]
2025-05-14 12:02:42,085 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-14 12:02:42,507 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-14 12:02:42,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105341.02, 'new_value': 109248.35}, {'field': 'total_amount', 'old_value': 105341.02, 'new_value': 109248.35}, {'field': 'order_count', 'old_value': 4391, 'new_value': 4566}]
2025-05-14 12:02:42,507 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-14 12:02:42,976 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-14 12:02:42,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242176.0, 'new_value': 258690.0}, {'field': 'total_amount', 'old_value': 242176.0, 'new_value': 258690.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 248}]
2025-05-14 12:02:42,976 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-14 12:02:43,460 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-14 12:02:43,460 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145882.55, 'new_value': 151980.65}, {'field': 'offline_amount', 'old_value': 89333.91, 'new_value': 93880.31}, {'field': 'total_amount', 'old_value': 235216.46, 'new_value': 245860.96}, {'field': 'order_count', 'old_value': 2105, 'new_value': 2234}]
2025-05-14 12:02:43,460 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-14 12:02:43,929 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-14 12:02:43,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123827.71, 'new_value': 130136.65}, {'field': 'total_amount', 'old_value': 123827.71, 'new_value': 130136.65}, {'field': 'order_count', 'old_value': 910, 'new_value': 960}]
2025-05-14 12:02:43,929 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-14 12:02:44,382 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-14 12:02:44,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53630.8, 'new_value': 55500.6}, {'field': 'total_amount', 'old_value': 55159.6, 'new_value': 57029.4}, {'field': 'order_count', 'old_value': 335, 'new_value': 345}]
2025-05-14 12:02:44,382 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-14 12:02:44,773 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-14 12:02:44,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34084.0, 'new_value': 34664.0}, {'field': 'total_amount', 'old_value': 34084.0, 'new_value': 34664.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-05-14 12:02:44,773 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-14 12:02:45,273 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-14 12:02:45,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100117.94, 'new_value': 100458.94}, {'field': 'offline_amount', 'old_value': 7551.3, 'new_value': 18013.3}, {'field': 'total_amount', 'old_value': 107669.24, 'new_value': 118472.24}, {'field': 'order_count', 'old_value': 4753, 'new_value': 5574}]
2025-05-14 12:02:45,273 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-14 12:02:45,788 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-14 12:02:45,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42915.0, 'new_value': 46697.0}, {'field': 'offline_amount', 'old_value': 529485.0, 'new_value': 570903.0}, {'field': 'total_amount', 'old_value': 572400.0, 'new_value': 617600.0}, {'field': 'order_count', 'old_value': 13200, 'new_value': 14476}]
2025-05-14 12:02:45,788 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-14 12:02:46,320 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-14 12:02:46,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210040.58, 'new_value': 223353.46}, {'field': 'total_amount', 'old_value': 210040.58, 'new_value': 223353.46}, {'field': 'order_count', 'old_value': 716, 'new_value': 754}]
2025-05-14 12:02:46,320 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-14 12:02:46,882 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-14 12:02:46,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17940.0, 'new_value': 19629.0}, {'field': 'offline_amount', 'old_value': 100713.0, 'new_value': 105339.0}, {'field': 'total_amount', 'old_value': 118653.0, 'new_value': 124968.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 134}]
2025-05-14 12:02:46,882 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-14 12:02:47,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-14 12:02:47,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20852.0, 'new_value': 21675.0}, {'field': 'total_amount', 'old_value': 20852.0, 'new_value': 21675.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-14 12:02:47,351 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-14 12:02:47,835 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-14 12:02:47,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17066.68, 'new_value': 17776.83}, {'field': 'offline_amount', 'old_value': 26257.1, 'new_value': 28257.1}, {'field': 'total_amount', 'old_value': 43323.78, 'new_value': 46033.93}, {'field': 'order_count', 'old_value': 1930, 'new_value': 2075}]
2025-05-14 12:02:47,835 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-14 12:02:48,304 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-14 12:02:48,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10820.54, 'new_value': 11673.54}, {'field': 'offline_amount', 'old_value': 9012.9, 'new_value': 9425.4}, {'field': 'total_amount', 'old_value': 19833.44, 'new_value': 21098.94}, {'field': 'order_count', 'old_value': 887, 'new_value': 961}]
2025-05-14 12:02:48,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-14 12:02:48,757 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-14 12:02:48,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167739.0, 'new_value': 182815.0}, {'field': 'total_amount', 'old_value': 167739.0, 'new_value': 182815.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 259}]
2025-05-14 12:02:48,757 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-14 12:02:49,195 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-14 12:02:49,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19380.0, 'new_value': 21888.0}, {'field': 'total_amount', 'old_value': 19380.0, 'new_value': 21888.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 111}]
2025-05-14 12:02:49,195 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-14 12:02:49,710 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-14 12:02:49,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28209.7, 'new_value': 28259.6}, {'field': 'total_amount', 'old_value': 37964.2, 'new_value': 38014.1}, {'field': 'order_count', 'old_value': 425, 'new_value': 426}]
2025-05-14 12:02:49,710 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-14 12:02:50,116 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-14 12:02:50,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49422.22, 'new_value': 54294.7}, {'field': 'offline_amount', 'old_value': 109756.87, 'new_value': 115852.82}, {'field': 'total_amount', 'old_value': 159179.09, 'new_value': 170147.52}, {'field': 'order_count', 'old_value': 4866, 'new_value': 5290}]
2025-05-14 12:02:50,116 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-14 12:02:50,585 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-14 12:02:50,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50514.0, 'new_value': 53678.0}, {'field': 'total_amount', 'old_value': 50514.0, 'new_value': 53678.0}, {'field': 'order_count', 'old_value': 212, 'new_value': 227}]
2025-05-14 12:02:50,585 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-14 12:02:51,085 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-14 12:02:51,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30962.1, 'new_value': 31449.1}, {'field': 'total_amount', 'old_value': 31390.1, 'new_value': 31877.1}, {'field': 'order_count', 'old_value': 9235, 'new_value': 9238}]
2025-05-14 12:02:51,085 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-14 12:02:51,554 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-14 12:02:51,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61583.94, 'new_value': 65182.36}, {'field': 'total_amount', 'old_value': 61591.94, 'new_value': 65190.36}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-14 12:02:51,554 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-14 12:02:52,054 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-14 12:02:52,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80289.66, 'new_value': 86765.64}, {'field': 'offline_amount', 'old_value': 28844.75, 'new_value': 29878.45}, {'field': 'total_amount', 'old_value': 109134.41, 'new_value': 116644.09}, {'field': 'order_count', 'old_value': 6093, 'new_value': 6520}]
2025-05-14 12:02:52,054 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-14 12:02:52,523 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-14 12:02:52,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49615.9, 'new_value': 57740.4}, {'field': 'offline_amount', 'old_value': 18463.8, 'new_value': 20256.3}, {'field': 'total_amount', 'old_value': 68079.7, 'new_value': 77996.7}, {'field': 'order_count', 'old_value': 5638, 'new_value': 6470}]
2025-05-14 12:02:52,523 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-14 12:02:53,070 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-14 12:02:53,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143327.32, 'new_value': 147809.92}, {'field': 'total_amount', 'old_value': 165814.72, 'new_value': 170297.32}, {'field': 'order_count', 'old_value': 886, 'new_value': 917}]
2025-05-14 12:02:53,070 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-14 12:02:53,476 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-14 12:02:53,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93048.6, 'new_value': 98894.8}, {'field': 'total_amount', 'old_value': 93048.6, 'new_value': 98894.8}, {'field': 'order_count', 'old_value': 4721, 'new_value': 5019}]
2025-05-14 12:02:53,476 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-14 12:02:53,945 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-14 12:02:53,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84039.9, 'new_value': 87443.8}, {'field': 'total_amount', 'old_value': 84039.9, 'new_value': 87443.8}, {'field': 'order_count', 'old_value': 388, 'new_value': 404}]
2025-05-14 12:02:53,945 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-14 12:02:54,398 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-14 12:02:54,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89769.1, 'new_value': 91049.2}, {'field': 'total_amount', 'old_value': 89769.1, 'new_value': 91049.2}, {'field': 'order_count', 'old_value': 2473, 'new_value': 2508}]
2025-05-14 12:02:54,398 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-14 12:02:54,866 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-14 12:02:54,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3748.0, 'new_value': 4500.0}, {'field': 'total_amount', 'old_value': 11435.0, 'new_value': 12187.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 62}]
2025-05-14 12:02:54,866 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-14 12:02:55,320 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-14 12:02:55,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79363.64, 'new_value': 82873.57}, {'field': 'offline_amount', 'old_value': 157579.55, 'new_value': 163255.75}, {'field': 'total_amount', 'old_value': 236943.19, 'new_value': 246129.32}, {'field': 'order_count', 'old_value': 1851, 'new_value': 1953}]
2025-05-14 12:02:55,320 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-14 12:02:55,773 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-14 12:02:55,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1209877.0, 'new_value': 1256894.0}, {'field': 'total_amount', 'old_value': 1209877.0, 'new_value': 1256894.0}, {'field': 'order_count', 'old_value': 4571, 'new_value': 4757}]
2025-05-14 12:02:55,788 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-14 12:02:56,351 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-14 12:02:56,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56262.0, 'new_value': 60005.6}, {'field': 'total_amount', 'old_value': 56262.0, 'new_value': 60005.6}, {'field': 'order_count', 'old_value': 263, 'new_value': 283}]
2025-05-14 12:02:56,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-14 12:02:56,757 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-14 12:02:56,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8504.38, 'new_value': 8923.66}, {'field': 'offline_amount', 'old_value': 20830.9, 'new_value': 21488.6}, {'field': 'total_amount', 'old_value': 29335.28, 'new_value': 30412.26}, {'field': 'order_count', 'old_value': 1163, 'new_value': 1201}]
2025-05-14 12:02:56,757 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-14 12:02:57,257 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-14 12:02:57,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205724.87, 'new_value': 211314.84}, {'field': 'total_amount', 'old_value': 205724.87, 'new_value': 211314.84}, {'field': 'order_count', 'old_value': 1066, 'new_value': 1102}]
2025-05-14 12:02:57,257 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-14 12:02:57,710 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-14 12:02:57,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22604.15, 'new_value': 25215.72}, {'field': 'offline_amount', 'old_value': 27923.12, 'new_value': 28937.46}, {'field': 'total_amount', 'old_value': 50527.27, 'new_value': 54153.18}, {'field': 'order_count', 'old_value': 3839, 'new_value': 4158}]
2025-05-14 12:02:57,710 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-14 12:02:58,179 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-14 12:02:58,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233415.5, 'new_value': 233455.4}, {'field': 'total_amount', 'old_value': 233415.5, 'new_value': 233455.4}]
2025-05-14 12:02:58,179 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-14 12:02:58,820 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-14 12:02:58,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42398.0, 'new_value': 42399.0}, {'field': 'total_amount', 'old_value': 42398.0, 'new_value': 42399.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 1683}]
2025-05-14 12:02:58,820 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-14 12:02:59,382 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-14 12:02:59,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45397.28, 'new_value': 52255.56}, {'field': 'total_amount', 'old_value': 45397.28, 'new_value': 52255.56}, {'field': 'order_count', 'old_value': 2122, 'new_value': 2454}]
2025-05-14 12:02:59,382 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-14 12:02:59,851 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-14 12:02:59,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49537.23, 'new_value': 51315.33}, {'field': 'total_amount', 'old_value': 50797.66, 'new_value': 52575.76}, {'field': 'order_count', 'old_value': 237, 'new_value': 242}]
2025-05-14 12:02:59,851 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-14 12:03:00,366 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-14 12:03:00,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305497.0, 'new_value': 361633.0}, {'field': 'total_amount', 'old_value': 305497.0, 'new_value': 361633.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 46}]
2025-05-14 12:03:00,366 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-14 12:03:00,820 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-14 12:03:00,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347459.0, 'new_value': 366179.0}, {'field': 'total_amount', 'old_value': 347459.0, 'new_value': 366179.0}, {'field': 'order_count', 'old_value': 216, 'new_value': 230}]
2025-05-14 12:03:00,820 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-14 12:03:01,304 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-14 12:03:01,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 438691.49, 'new_value': 461166.94}, {'field': 'total_amount', 'old_value': 438691.49, 'new_value': 461166.94}, {'field': 'order_count', 'old_value': 3270, 'new_value': 3498}]
2025-05-14 12:03:01,304 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-14 12:03:01,741 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-14 12:03:01,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10047.7, 'new_value': 10460.0}, {'field': 'offline_amount', 'old_value': 95108.0, 'new_value': 100821.4}, {'field': 'total_amount', 'old_value': 105155.7, 'new_value': 111281.4}, {'field': 'order_count', 'old_value': 3182, 'new_value': 3385}]
2025-05-14 12:03:01,741 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-14 12:03:02,226 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-14 12:03:02,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230829.0, 'new_value': 244234.0}, {'field': 'total_amount', 'old_value': 230829.0, 'new_value': 244234.0}, {'field': 'order_count', 'old_value': 1976, 'new_value': 2087}]
2025-05-14 12:03:02,226 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-14 12:03:02,648 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-14 12:03:02,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28504.58, 'new_value': 30403.86}, {'field': 'offline_amount', 'old_value': 21360.08, 'new_value': 24565.76}, {'field': 'total_amount', 'old_value': 49864.66, 'new_value': 54969.62}, {'field': 'order_count', 'old_value': 2656, 'new_value': 2884}]
2025-05-14 12:03:02,648 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-14 12:03:03,101 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-14 12:03:03,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12689.27, 'new_value': 13973.24}, {'field': 'offline_amount', 'old_value': 13890.15, 'new_value': 15613.05}, {'field': 'total_amount', 'old_value': 26579.42, 'new_value': 29586.29}, {'field': 'order_count', 'old_value': 1229, 'new_value': 1393}]
2025-05-14 12:03:03,101 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-14 12:03:03,616 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-14 12:03:03,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1939.0, 'new_value': 2025.0}, {'field': 'offline_amount', 'old_value': 17811.6, 'new_value': 18232.6}, {'field': 'total_amount', 'old_value': 19750.6, 'new_value': 20257.6}, {'field': 'order_count', 'old_value': 721, 'new_value': 740}]
2025-05-14 12:03:03,616 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-14 12:03:04,054 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-14 12:03:04,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47620.29, 'new_value': 51903.36}, {'field': 'offline_amount', 'old_value': 54471.72, 'new_value': 59768.51}, {'field': 'total_amount', 'old_value': 102092.01, 'new_value': 111671.87}, {'field': 'order_count', 'old_value': 2540, 'new_value': 2790}]
2025-05-14 12:03:04,054 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-14 12:03:04,538 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-14 12:03:04,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45991.0, 'new_value': 52890.0}, {'field': 'total_amount', 'old_value': 45991.0, 'new_value': 52890.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-14 12:03:04,538 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-14 12:03:05,007 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-14 12:03:05,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481218.0, 'new_value': 531556.0}, {'field': 'total_amount', 'old_value': 481218.0, 'new_value': 531556.0}, {'field': 'order_count', 'old_value': 575, 'new_value': 622}]
2025-05-14 12:03:05,007 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-14 12:03:05,476 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-14 12:03:05,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21872.35, 'new_value': 23559.15}, {'field': 'offline_amount', 'old_value': 57151.0, 'new_value': 61835.0}, {'field': 'total_amount', 'old_value': 79023.35, 'new_value': 85394.15}, {'field': 'order_count', 'old_value': 849, 'new_value': 907}]
2025-05-14 12:03:05,476 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-14 12:03:05,945 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-14 12:03:05,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68669.0, 'new_value': 72883.0}, {'field': 'offline_amount', 'old_value': 50823.0, 'new_value': 53892.0}, {'field': 'total_amount', 'old_value': 119492.0, 'new_value': 126775.0}, {'field': 'order_count', 'old_value': 1456, 'new_value': 1555}]
2025-05-14 12:03:05,945 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-14 12:03:06,413 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-14 12:03:06,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5423.4, 'new_value': 5729.4}, {'field': 'offline_amount', 'old_value': 11687.76, 'new_value': 11908.1}, {'field': 'total_amount', 'old_value': 17111.16, 'new_value': 17637.5}, {'field': 'order_count', 'old_value': 174, 'new_value': 183}]
2025-05-14 12:03:06,413 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-14 12:03:06,866 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-14 12:03:06,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5678.48, 'new_value': 6202.5}, {'field': 'offline_amount', 'old_value': 63597.0, 'new_value': 66073.0}, {'field': 'total_amount', 'old_value': 69275.48, 'new_value': 72275.5}, {'field': 'order_count', 'old_value': 34, 'new_value': 38}]
2025-05-14 12:03:06,866 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-14 12:03:07,335 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-14 12:03:07,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14001.62, 'new_value': 15987.62}, {'field': 'offline_amount', 'old_value': 18443.8, 'new_value': 20532.6}, {'field': 'total_amount', 'old_value': 32445.42, 'new_value': 36520.22}, {'field': 'order_count', 'old_value': 128, 'new_value': 145}]
2025-05-14 12:03:07,335 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-14 12:03:07,866 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-14 12:03:07,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116302.5, 'new_value': 122857.5}, {'field': 'total_amount', 'old_value': 116302.5, 'new_value': 122857.5}, {'field': 'order_count', 'old_value': 590, 'new_value': 614}]
2025-05-14 12:03:07,866 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-14 12:03:08,288 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-14 12:03:08,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3094.0, 'new_value': 3177.0}, {'field': 'offline_amount', 'old_value': 9774.0, 'new_value': 11334.0}, {'field': 'total_amount', 'old_value': 12868.0, 'new_value': 14511.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 133}]
2025-05-14 12:03:08,288 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-14 12:03:08,726 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-14 12:03:08,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23580.09, 'new_value': 24579.09}, {'field': 'total_amount', 'old_value': 26827.09, 'new_value': 27826.09}, {'field': 'order_count', 'old_value': 257, 'new_value': 272}]
2025-05-14 12:03:08,726 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-14 12:03:09,179 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-14 12:03:09,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83979.78, 'new_value': 92936.88}, {'field': 'total_amount', 'old_value': 83979.78, 'new_value': 92936.88}, {'field': 'order_count', 'old_value': 284, 'new_value': 313}]
2025-05-14 12:03:09,179 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-14 12:03:09,741 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-14 12:03:09,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8632.0, 'new_value': 10459.0}, {'field': 'total_amount', 'old_value': 8632.0, 'new_value': 10459.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-05-14 12:03:09,741 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-14 12:03:10,163 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-14 12:03:10,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38299.0, 'new_value': 40676.0}, {'field': 'offline_amount', 'old_value': 130182.0, 'new_value': 139906.0}, {'field': 'total_amount', 'old_value': 168481.0, 'new_value': 180582.0}, {'field': 'order_count', 'old_value': 758, 'new_value': 808}]
2025-05-14 12:03:10,163 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-14 12:03:10,616 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-14 12:03:10,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 624001.0, 'new_value': 650117.0}, {'field': 'total_amount', 'old_value': 624001.0, 'new_value': 650117.0}, {'field': 'order_count', 'old_value': 2663, 'new_value': 2778}]
2025-05-14 12:03:10,616 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-14 12:03:11,054 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-14 12:03:11,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8071954.0, 'new_value': 8396794.0}, {'field': 'total_amount', 'old_value': 8071954.0, 'new_value': 8396794.0}, {'field': 'order_count', 'old_value': 23794, 'new_value': 24938}]
2025-05-14 12:03:11,054 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-14 12:03:11,507 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-14 12:03:11,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2050780.4, 'new_value': 2148144.99}, {'field': 'total_amount', 'old_value': 2050780.4, 'new_value': 2148144.99}, {'field': 'order_count', 'old_value': 3420, 'new_value': 3586}]
2025-05-14 12:03:11,507 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-14 12:03:11,929 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-14 12:03:11,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74508.81, 'new_value': 81139.79}, {'field': 'total_amount', 'old_value': 81948.45, 'new_value': 88579.43}, {'field': 'order_count', 'old_value': 5631, 'new_value': 6124}]
2025-05-14 12:03:11,929 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-14 12:03:12,382 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-14 12:03:12,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147325.0, 'new_value': 161518.0}, {'field': 'total_amount', 'old_value': 147325.0, 'new_value': 161518.0}, {'field': 'order_count', 'old_value': 3077, 'new_value': 3402}]
2025-05-14 12:03:12,382 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-14 12:03:12,866 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-14 12:03:12,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132421.0, 'new_value': 137415.0}, {'field': 'total_amount', 'old_value': 132421.0, 'new_value': 137415.0}, {'field': 'order_count', 'old_value': 264, 'new_value': 277}]
2025-05-14 12:03:12,866 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-14 12:03:13,319 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-14 12:03:13,319 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132297.5, 'new_value': 148387.35}, {'field': 'offline_amount', 'old_value': 108866.85, 'new_value': 120349.87}, {'field': 'total_amount', 'old_value': 241164.35, 'new_value': 268737.22}, {'field': 'order_count', 'old_value': 9461, 'new_value': 10585}]
2025-05-14 12:03:13,319 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-14 12:03:13,835 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-14 12:03:13,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35260.97, 'new_value': 39122.97}, {'field': 'total_amount', 'old_value': 60306.66, 'new_value': 64168.66}, {'field': 'order_count', 'old_value': 1342, 'new_value': 1343}]
2025-05-14 12:03:13,835 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-14 12:03:14,319 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-14 12:03:14,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161686.0, 'new_value': 171436.0}, {'field': 'total_amount', 'old_value': 161686.0, 'new_value': 171436.0}, {'field': 'order_count', 'old_value': 184, 'new_value': 196}]
2025-05-14 12:03:14,319 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-14 12:03:14,788 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-14 12:03:14,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88424.41, 'new_value': 96722.05}, {'field': 'total_amount', 'old_value': 165415.11, 'new_value': 173712.75}, {'field': 'order_count', 'old_value': 158, 'new_value': 172}]
2025-05-14 12:03:14,788 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-14 12:03:15,366 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-14 12:03:15,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209647.0, 'new_value': 214845.0}, {'field': 'total_amount', 'old_value': 209647.0, 'new_value': 214845.0}, {'field': 'order_count', 'old_value': 4531, 'new_value': 4643}]
2025-05-14 12:03:15,366 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-14 12:03:15,866 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-14 12:03:15,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24518.0, 'new_value': 29957.0}, {'field': 'total_amount', 'old_value': 24518.0, 'new_value': 29957.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 150}]
2025-05-14 12:03:15,866 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-14 12:03:16,304 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-14 12:03:16,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85116.0, 'new_value': 92962.0}, {'field': 'offline_amount', 'old_value': 69620.0, 'new_value': 71878.0}, {'field': 'total_amount', 'old_value': 154736.0, 'new_value': 164840.0}, {'field': 'order_count', 'old_value': 503, 'new_value': 533}]
2025-05-14 12:03:16,304 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-14 12:03:16,757 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-14 12:03:16,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55924.0, 'new_value': 57892.0}, {'field': 'total_amount', 'old_value': 55924.0, 'new_value': 57892.0}, {'field': 'order_count', 'old_value': 3831, 'new_value': 3987}]
2025-05-14 12:03:16,757 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-14 12:03:17,226 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-14 12:03:17,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62461.0, 'new_value': 68916.0}, {'field': 'total_amount', 'old_value': 62461.0, 'new_value': 68916.0}, {'field': 'order_count', 'old_value': 4545, 'new_value': 5041}]
2025-05-14 12:03:17,226 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-14 12:03:17,694 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-14 12:03:17,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21077.0, 'new_value': 25670.0}, {'field': 'total_amount', 'old_value': 21077.0, 'new_value': 25670.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-14 12:03:17,694 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-14 12:03:18,210 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-14 12:03:18,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113.0, 'new_value': 166.6}, {'field': 'offline_amount', 'old_value': 48404.2, 'new_value': 48618.2}, {'field': 'total_amount', 'old_value': 48517.2, 'new_value': 48784.8}, {'field': 'order_count', 'old_value': 744, 'new_value': 750}]
2025-05-14 12:03:18,210 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-14 12:03:18,632 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-14 12:03:18,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2019.0, 'new_value': 2389.0}, {'field': 'offline_amount', 'old_value': 8921.0, 'new_value': 11308.0}, {'field': 'total_amount', 'old_value': 10940.0, 'new_value': 13697.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 334}]
2025-05-14 12:03:18,632 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-14 12:03:19,179 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-14 12:03:19,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86637.9, 'new_value': 101263.31}, {'field': 'total_amount', 'old_value': 86637.9, 'new_value': 101263.31}, {'field': 'order_count', 'old_value': 5999, 'new_value': 7081}]
2025-05-14 12:03:19,179 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-14 12:03:19,616 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-14 12:03:19,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1235250.0}, {'field': 'total_amount', 'old_value': 300000.0, 'new_value': 1535250.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-14 12:03:19,616 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-14 12:03:20,038 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-14 12:03:20,038 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9974.1, 'new_value': 11178.1}, {'field': 'offline_amount', 'old_value': 28930.4, 'new_value': 31351.0}, {'field': 'total_amount', 'old_value': 38904.5, 'new_value': 42529.1}, {'field': 'order_count', 'old_value': 1460, 'new_value': 1576}]
2025-05-14 12:03:20,038 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-14 12:03:20,601 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-14 12:03:20,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16903.68, 'new_value': 19926.36}, {'field': 'total_amount', 'old_value': 16903.68, 'new_value': 19926.36}, {'field': 'order_count', 'old_value': 793, 'new_value': 921}]
2025-05-14 12:03:20,616 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-14 12:03:21,069 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-14 12:03:21,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2465464.39, 'new_value': 2628495.39}, {'field': 'total_amount', 'old_value': 2465464.39, 'new_value': 2628495.39}, {'field': 'order_count', 'old_value': 50953, 'new_value': 54545}]
2025-05-14 12:03:21,069 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-14 12:03:21,538 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-14 12:03:21,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13600.29, 'new_value': 14456.29}, {'field': 'total_amount', 'old_value': 13600.29, 'new_value': 14456.29}, {'field': 'order_count', 'old_value': 56, 'new_value': 58}]
2025-05-14 12:03:21,538 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-14 12:03:21,991 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-14 12:03:21,991 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317607.47, 'new_value': 332626.47}, {'field': 'total_amount', 'old_value': 317607.47, 'new_value': 332626.47}, {'field': 'order_count', 'old_value': 4106, 'new_value': 4231}]
2025-05-14 12:03:21,991 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-14 12:03:22,444 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-14 12:03:22,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85523.96, 'new_value': 95574.12}, {'field': 'total_amount', 'old_value': 85523.96, 'new_value': 95574.12}, {'field': 'order_count', 'old_value': 1725, 'new_value': 1890}]
2025-05-14 12:03:22,460 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-14 12:03:22,898 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-14 12:03:22,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149110.0, 'new_value': 164605.0}, {'field': 'total_amount', 'old_value': 149110.0, 'new_value': 164605.0}, {'field': 'order_count', 'old_value': 3207, 'new_value': 3574}]
2025-05-14 12:03:22,898 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-14 12:03:23,398 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-14 12:03:23,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37516.31, 'new_value': 43184.74}, {'field': 'total_amount', 'old_value': 37516.31, 'new_value': 43184.74}, {'field': 'order_count', 'old_value': 3597, 'new_value': 4159}]
2025-05-14 12:03:23,398 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-14 12:03:23,819 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-14 12:03:23,819 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 27.9}, {'field': 'offline_amount', 'old_value': 19578.0, 'new_value': 20637.0}, {'field': 'total_amount', 'old_value': 19578.0, 'new_value': 20664.9}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-05-14 12:03:23,819 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-14 12:03:24,288 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-14 12:03:24,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67334.5, 'new_value': 70968.0}, {'field': 'total_amount', 'old_value': 67334.5, 'new_value': 70968.0}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1802}]
2025-05-14 12:03:24,288 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-14 12:03:24,773 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-14 12:03:24,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14112.0, 'new_value': 14993.0}, {'field': 'total_amount', 'old_value': 14112.0, 'new_value': 14993.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-05-14 12:03:24,773 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-14 12:03:25,210 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-14 12:03:25,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45391.14, 'new_value': 49257.23}, {'field': 'offline_amount', 'old_value': 248957.2, 'new_value': 257222.8}, {'field': 'total_amount', 'old_value': 294348.34, 'new_value': 306480.03}, {'field': 'order_count', 'old_value': 1809, 'new_value': 1936}]
2025-05-14 12:03:25,210 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-14 12:03:25,694 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-14 12:03:25,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42288.0, 'new_value': 45938.0}, {'field': 'total_amount', 'old_value': 44138.0, 'new_value': 47788.0}, {'field': 'order_count', 'old_value': 257, 'new_value': 277}]
2025-05-14 12:03:25,694 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-14 12:03:26,132 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-14 12:03:26,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 597087.54, 'new_value': 631030.38}, {'field': 'total_amount', 'old_value': 597087.54, 'new_value': 631030.38}, {'field': 'order_count', 'old_value': 1777, 'new_value': 1887}]
2025-05-14 12:03:26,132 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-14 12:03:26,632 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-14 12:03:26,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 555932.85, 'new_value': 592367.2}, {'field': 'total_amount', 'old_value': 555932.85, 'new_value': 592367.2}, {'field': 'order_count', 'old_value': 1949, 'new_value': 2076}]
2025-05-14 12:03:26,632 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-14 12:03:27,163 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-14 12:03:27,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399911.57, 'new_value': 428343.26}, {'field': 'total_amount', 'old_value': 399911.57, 'new_value': 428343.26}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1367}]
2025-05-14 12:03:27,163 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-14 12:03:27,616 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-14 12:03:27,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19711.0, 'new_value': 23990.0}, {'field': 'total_amount', 'old_value': 19711.0, 'new_value': 23990.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-14 12:03:27,616 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-14 12:03:28,116 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-14 12:03:28,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9081.0, 'new_value': 9709.0}, {'field': 'offline_amount', 'old_value': 11481.0, 'new_value': 12109.0}, {'field': 'total_amount', 'old_value': 20562.0, 'new_value': 21818.0}, {'field': 'order_count', 'old_value': 9119, 'new_value': 9747}]
2025-05-14 12:03:28,116 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-14 12:03:28,647 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-14 12:03:28,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22198.02, 'new_value': 23247.02}, {'field': 'total_amount', 'old_value': 23208.92, 'new_value': 24257.92}, {'field': 'order_count', 'old_value': 169, 'new_value': 180}]
2025-05-14 12:03:28,647 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-14 12:03:29,085 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-14 12:03:29,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171340.81, 'new_value': 177658.32}, {'field': 'total_amount', 'old_value': 171340.81, 'new_value': 177658.32}, {'field': 'order_count', 'old_value': 476, 'new_value': 496}]
2025-05-14 12:03:29,085 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-14 12:03:29,538 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-14 12:03:29,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51522.0, 'new_value': 59705.0}, {'field': 'total_amount', 'old_value': 51602.0, 'new_value': 59785.0}, {'field': 'order_count', 'old_value': 5304, 'new_value': 6170}]
2025-05-14 12:03:29,538 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-14 12:03:29,976 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-14 12:03:29,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57339.0, 'new_value': 63008.0}, {'field': 'offline_amount', 'old_value': 35669.0, 'new_value': 41088.0}, {'field': 'total_amount', 'old_value': 93008.0, 'new_value': 104096.0}, {'field': 'order_count', 'old_value': 3928, 'new_value': 4399}]
2025-05-14 12:03:29,976 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-14 12:03:30,397 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-14 12:03:30,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54458.0, 'new_value': 57469.0}, {'field': 'total_amount', 'old_value': 54458.0, 'new_value': 57469.0}, {'field': 'order_count', 'old_value': 265, 'new_value': 293}]
2025-05-14 12:03:30,397 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-14 12:03:30,913 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-14 12:03:30,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69096.0, 'new_value': 72032.0}, {'field': 'total_amount', 'old_value': 69096.0, 'new_value': 72032.0}, {'field': 'order_count', 'old_value': 272, 'new_value': 286}]
2025-05-14 12:03:30,913 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-14 12:03:31,351 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-14 12:03:31,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100085.0, 'new_value': 115785.0}, {'field': 'total_amount', 'old_value': 100085.0, 'new_value': 115785.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 278}]
2025-05-14 12:03:31,351 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-14 12:03:31,851 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-14 12:03:31,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22636.0, 'new_value': 24848.0}, {'field': 'total_amount', 'old_value': 22636.0, 'new_value': 24848.0}, {'field': 'order_count', 'old_value': 433, 'new_value': 476}]
2025-05-14 12:03:31,851 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-14 12:03:32,288 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-14 12:03:32,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78713.0, 'new_value': 89807.0}, {'field': 'total_amount', 'old_value': 78713.0, 'new_value': 89807.0}, {'field': 'order_count', 'old_value': 8217, 'new_value': 9397}]
2025-05-14 12:03:32,288 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-14 12:03:32,819 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-14 12:03:32,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67861.0, 'new_value': 72024.0}, {'field': 'total_amount', 'old_value': 67861.0, 'new_value': 72024.0}, {'field': 'order_count', 'old_value': 580, 'new_value': 631}]
2025-05-14 12:03:32,819 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-14 12:03:33,272 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-14 12:03:33,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88271.53, 'new_value': 90502.65}, {'field': 'total_amount', 'old_value': 88271.53, 'new_value': 90502.65}, {'field': 'order_count', 'old_value': 666, 'new_value': 691}]
2025-05-14 12:03:33,272 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-14 12:03:33,772 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-14 12:03:33,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1760.0, 'new_value': 2420.0}, {'field': 'total_amount', 'old_value': 1760.0, 'new_value': 2420.0}, {'field': 'order_count', 'old_value': 583, 'new_value': 584}]
2025-05-14 12:03:33,772 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-14 12:03:34,194 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-14 12:03:34,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26040.0, 'new_value': 29039.0}, {'field': 'total_amount', 'old_value': 26040.0, 'new_value': 29039.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-14 12:03:34,194 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-14 12:03:34,694 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-14 12:03:34,694 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10881.0, 'new_value': 12511.0}, {'field': 'total_amount', 'old_value': 46174.0, 'new_value': 47804.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-05-14 12:03:34,694 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-14 12:03:35,085 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-14 12:03:35,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27304.15, 'new_value': 29064.46}, {'field': 'total_amount', 'old_value': 27304.15, 'new_value': 29064.46}, {'field': 'order_count', 'old_value': 427, 'new_value': 462}]
2025-05-14 12:03:35,085 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-14 12:03:35,585 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-14 12:03:35,585 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69755.15, 'new_value': 74625.83}, {'field': 'offline_amount', 'old_value': 345455.8, 'new_value': 360991.38}, {'field': 'total_amount', 'old_value': 415210.95, 'new_value': 435617.21}, {'field': 'order_count', 'old_value': 922, 'new_value': 985}]
2025-05-14 12:03:35,585 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-14 12:03:36,022 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-14 12:03:36,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40561.16, 'new_value': 43730.62}, {'field': 'offline_amount', 'old_value': 387559.86, 'new_value': 423366.49}, {'field': 'total_amount', 'old_value': 426246.69, 'new_value': 465222.78}, {'field': 'order_count', 'old_value': 2045, 'new_value': 2230}]
2025-05-14 12:03:36,022 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-14 12:03:36,444 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-14 12:03:36,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1458.95, 'new_value': 1557.95}, {'field': 'total_amount', 'old_value': 1458.95, 'new_value': 1557.95}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-14 12:03:36,444 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-14 12:03:36,897 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-14 12:03:36,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22029.0, 'new_value': 23481.0}, {'field': 'total_amount', 'old_value': 27347.0, 'new_value': 28799.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-14 12:03:36,897 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-14 12:03:37,351 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-14 12:03:37,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8772035.88, 'new_value': 9437183.74}, {'field': 'total_amount', 'old_value': 8772035.88, 'new_value': 9437183.74}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-14 12:03:37,351 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-14 12:03:37,851 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-14 12:03:37,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24281.36, 'new_value': 26744.36}, {'field': 'total_amount', 'old_value': 28280.36, 'new_value': 30743.36}, {'field': 'order_count', 'old_value': 1890, 'new_value': 2044}]
2025-05-14 12:03:37,851 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-14 12:03:38,319 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-14 12:03:38,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88707.81, 'new_value': 94807.17}, {'field': 'total_amount', 'old_value': 88707.81, 'new_value': 94807.17}, {'field': 'order_count', 'old_value': 9089, 'new_value': 9787}]
2025-05-14 12:03:38,319 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-14 12:03:38,772 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-14 12:03:38,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17969.43, 'new_value': 19633.02}, {'field': 'total_amount', 'old_value': 17969.43, 'new_value': 19633.02}, {'field': 'order_count', 'old_value': 676, 'new_value': 752}]
2025-05-14 12:03:38,788 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-14 12:03:39,163 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-14 12:03:39,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10575.93, 'new_value': 13006.96}, {'field': 'total_amount', 'old_value': 10575.93, 'new_value': 13006.96}, {'field': 'order_count', 'old_value': 57, 'new_value': 77}]
2025-05-14 12:03:39,163 - INFO - 日期 2025-05 处理完成 - 更新: 401 条，插入: 0 条，错误: 0 条
2025-05-14 12:03:39,163 - INFO - 数据同步完成！更新: 405 条，插入: 0 条，错误: 0 条
2025-05-14 12:03:39,163 - INFO - =================同步完成====================
2025-05-14 15:00:01,888 - INFO - =================使用默认全量同步=============
2025-05-14 15:00:03,232 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 15:00:03,232 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 15:00:03,263 - INFO - 开始处理日期: 2025-01
2025-05-14 15:00:03,278 - INFO - Request Parameters - Page 1:
2025-05-14 15:00:03,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:03,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:04,513 - INFO - Response - Page 1:
2025-05-14 15:00:04,716 - INFO - 第 1 页获取到 100 条记录
2025-05-14 15:00:04,716 - INFO - Request Parameters - Page 2:
2025-05-14 15:00:04,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:04,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:05,232 - INFO - Response - Page 2:
2025-05-14 15:00:05,435 - INFO - 第 2 页获取到 100 条记录
2025-05-14 15:00:05,435 - INFO - Request Parameters - Page 3:
2025-05-14 15:00:05,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:05,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:05,950 - INFO - Response - Page 3:
2025-05-14 15:00:06,153 - INFO - 第 3 页获取到 100 条记录
2025-05-14 15:00:06,153 - INFO - Request Parameters - Page 4:
2025-05-14 15:00:06,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:06,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:06,700 - INFO - Response - Page 4:
2025-05-14 15:00:06,903 - INFO - 第 4 页获取到 100 条记录
2025-05-14 15:00:06,903 - INFO - Request Parameters - Page 5:
2025-05-14 15:00:06,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:06,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:07,435 - INFO - Response - Page 5:
2025-05-14 15:00:07,638 - INFO - 第 5 页获取到 100 条记录
2025-05-14 15:00:07,638 - INFO - Request Parameters - Page 6:
2025-05-14 15:00:07,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:07,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:08,107 - INFO - Response - Page 6:
2025-05-14 15:00:08,310 - INFO - 第 6 页获取到 100 条记录
2025-05-14 15:00:08,310 - INFO - Request Parameters - Page 7:
2025-05-14 15:00:08,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:08,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:08,778 - INFO - Response - Page 7:
2025-05-14 15:00:08,982 - INFO - 第 7 页获取到 82 条记录
2025-05-14 15:00:08,982 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 15:00:08,982 - INFO - 获取到 682 条表单数据
2025-05-14 15:00:08,982 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 15:00:08,997 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 15:00:08,997 - INFO - 开始处理日期: 2025-02
2025-05-14 15:00:08,997 - INFO - Request Parameters - Page 1:
2025-05-14 15:00:08,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:08,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:09,544 - INFO - Response - Page 1:
2025-05-14 15:00:09,747 - INFO - 第 1 页获取到 100 条记录
2025-05-14 15:00:09,747 - INFO - Request Parameters - Page 2:
2025-05-14 15:00:09,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:09,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:10,278 - INFO - Response - Page 2:
2025-05-14 15:00:10,482 - INFO - 第 2 页获取到 100 条记录
2025-05-14 15:00:10,482 - INFO - Request Parameters - Page 3:
2025-05-14 15:00:10,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:10,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:10,966 - INFO - Response - Page 3:
2025-05-14 15:00:11,169 - INFO - 第 3 页获取到 100 条记录
2025-05-14 15:00:11,169 - INFO - Request Parameters - Page 4:
2025-05-14 15:00:11,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:11,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:11,607 - INFO - Response - Page 4:
2025-05-14 15:00:11,810 - INFO - 第 4 页获取到 100 条记录
2025-05-14 15:00:11,810 - INFO - Request Parameters - Page 5:
2025-05-14 15:00:11,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:11,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:12,419 - INFO - Response - Page 5:
2025-05-14 15:00:12,622 - INFO - 第 5 页获取到 100 条记录
2025-05-14 15:00:12,622 - INFO - Request Parameters - Page 6:
2025-05-14 15:00:12,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:12,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:13,138 - INFO - Response - Page 6:
2025-05-14 15:00:13,341 - INFO - 第 6 页获取到 100 条记录
2025-05-14 15:00:13,341 - INFO - Request Parameters - Page 7:
2025-05-14 15:00:13,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:13,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:13,778 - INFO - Response - Page 7:
2025-05-14 15:00:13,982 - INFO - 第 7 页获取到 70 条记录
2025-05-14 15:00:13,982 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 15:00:13,982 - INFO - 获取到 670 条表单数据
2025-05-14 15:00:13,982 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 15:00:13,997 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 15:00:13,997 - INFO - 开始处理日期: 2025-03
2025-05-14 15:00:13,997 - INFO - Request Parameters - Page 1:
2025-05-14 15:00:13,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:13,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:14,482 - INFO - Response - Page 1:
2025-05-14 15:00:14,685 - INFO - 第 1 页获取到 100 条记录
2025-05-14 15:00:14,685 - INFO - Request Parameters - Page 2:
2025-05-14 15:00:14,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:14,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:15,153 - INFO - Response - Page 2:
2025-05-14 15:00:15,356 - INFO - 第 2 页获取到 100 条记录
2025-05-14 15:00:15,356 - INFO - Request Parameters - Page 3:
2025-05-14 15:00:15,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:15,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:15,872 - INFO - Response - Page 3:
2025-05-14 15:00:16,075 - INFO - 第 3 页获取到 100 条记录
2025-05-14 15:00:16,075 - INFO - Request Parameters - Page 4:
2025-05-14 15:00:16,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:16,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:16,575 - INFO - Response - Page 4:
2025-05-14 15:00:16,778 - INFO - 第 4 页获取到 100 条记录
2025-05-14 15:00:16,778 - INFO - Request Parameters - Page 5:
2025-05-14 15:00:16,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:16,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:17,356 - INFO - Response - Page 5:
2025-05-14 15:00:17,560 - INFO - 第 5 页获取到 100 条记录
2025-05-14 15:00:17,560 - INFO - Request Parameters - Page 6:
2025-05-14 15:00:17,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:17,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:18,216 - INFO - Response - Page 6:
2025-05-14 15:00:18,419 - INFO - 第 6 页获取到 100 条记录
2025-05-14 15:00:18,419 - INFO - Request Parameters - Page 7:
2025-05-14 15:00:18,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:18,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:18,825 - INFO - Response - Page 7:
2025-05-14 15:00:19,028 - INFO - 第 7 页获取到 61 条记录
2025-05-14 15:00:19,028 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 15:00:19,028 - INFO - 获取到 661 条表单数据
2025-05-14 15:00:19,028 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 15:00:19,044 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 15:00:19,044 - INFO - 开始处理日期: 2025-04
2025-05-14 15:00:19,044 - INFO - Request Parameters - Page 1:
2025-05-14 15:00:19,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:19,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:19,591 - INFO - Response - Page 1:
2025-05-14 15:00:19,794 - INFO - 第 1 页获取到 100 条记录
2025-05-14 15:00:19,794 - INFO - Request Parameters - Page 2:
2025-05-14 15:00:19,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:19,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:20,325 - INFO - Response - Page 2:
2025-05-14 15:00:20,528 - INFO - 第 2 页获取到 100 条记录
2025-05-14 15:00:20,528 - INFO - Request Parameters - Page 3:
2025-05-14 15:00:20,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:20,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:21,013 - INFO - Response - Page 3:
2025-05-14 15:00:21,231 - INFO - 第 3 页获取到 100 条记录
2025-05-14 15:00:21,231 - INFO - Request Parameters - Page 4:
2025-05-14 15:00:21,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:21,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:21,731 - INFO - Response - Page 4:
2025-05-14 15:00:21,935 - INFO - 第 4 页获取到 100 条记录
2025-05-14 15:00:21,935 - INFO - Request Parameters - Page 5:
2025-05-14 15:00:21,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:21,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:22,419 - INFO - Response - Page 5:
2025-05-14 15:00:22,622 - INFO - 第 5 页获取到 100 条记录
2025-05-14 15:00:22,622 - INFO - Request Parameters - Page 6:
2025-05-14 15:00:22,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:22,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:23,122 - INFO - Response - Page 6:
2025-05-14 15:00:23,325 - INFO - 第 6 页获取到 100 条记录
2025-05-14 15:00:23,325 - INFO - Request Parameters - Page 7:
2025-05-14 15:00:23,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:23,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:23,700 - INFO - Response - Page 7:
2025-05-14 15:00:23,903 - INFO - 第 7 页获取到 54 条记录
2025-05-14 15:00:23,903 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 15:00:23,903 - INFO - 获取到 654 条表单数据
2025-05-14 15:00:23,903 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 15:00:23,903 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-05-14 15:00:24,466 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M83
2025-05-14 15:00:24,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1900000.0, 'new_value': 1999597.2}, {'field': 'total_amount', 'old_value': 1900000.0, 'new_value': 1999597.2}, {'field': 'order_count', 'old_value': 355, 'new_value': 30}]
2025-05-14 15:00:24,466 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-14 15:00:24,466 - INFO - 开始处理日期: 2025-05
2025-05-14 15:00:24,466 - INFO - Request Parameters - Page 1:
2025-05-14 15:00:24,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:24,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:24,997 - INFO - Response - Page 1:
2025-05-14 15:00:25,200 - INFO - 第 1 页获取到 100 条记录
2025-05-14 15:00:25,200 - INFO - Request Parameters - Page 2:
2025-05-14 15:00:25,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:25,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:25,747 - INFO - Response - Page 2:
2025-05-14 15:00:25,950 - INFO - 第 2 页获取到 100 条记录
2025-05-14 15:00:25,950 - INFO - Request Parameters - Page 3:
2025-05-14 15:00:25,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:25,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:26,560 - INFO - Response - Page 3:
2025-05-14 15:00:26,763 - INFO - 第 3 页获取到 100 条记录
2025-05-14 15:00:26,763 - INFO - Request Parameters - Page 4:
2025-05-14 15:00:26,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:26,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:27,356 - INFO - Response - Page 4:
2025-05-14 15:00:27,560 - INFO - 第 4 页获取到 100 条记录
2025-05-14 15:00:27,560 - INFO - Request Parameters - Page 5:
2025-05-14 15:00:27,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:27,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:28,122 - INFO - Response - Page 5:
2025-05-14 15:00:28,325 - INFO - 第 5 页获取到 100 条记录
2025-05-14 15:00:28,325 - INFO - Request Parameters - Page 6:
2025-05-14 15:00:28,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:28,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:28,841 - INFO - Response - Page 6:
2025-05-14 15:00:29,044 - INFO - 第 6 页获取到 100 条记录
2025-05-14 15:00:29,044 - INFO - Request Parameters - Page 7:
2025-05-14 15:00:29,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 15:00:29,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 15:00:29,372 - INFO - Response - Page 7:
2025-05-14 15:00:29,575 - INFO - 第 7 页获取到 25 条记录
2025-05-14 15:00:29,575 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 15:00:29,575 - INFO - 获取到 625 条表单数据
2025-05-14 15:00:29,575 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 15:00:29,591 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-14 15:00:30,060 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-14 15:00:30,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126083.53, 'new_value': 131852.87}, {'field': 'offline_amount', 'old_value': 232000.0, 'new_value': 237000.0}, {'field': 'total_amount', 'old_value': 358083.53, 'new_value': 368852.87}, {'field': 'order_count', 'old_value': 763, 'new_value': 797}]
2025-05-14 15:00:30,060 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-14 15:00:30,481 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-14 15:00:30,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105735.0, 'new_value': 108388.8}, {'field': 'total_amount', 'old_value': 111685.3, 'new_value': 114339.1}, {'field': 'order_count', 'old_value': 217, 'new_value': 224}]
2025-05-14 15:00:30,481 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-14 15:00:30,919 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-14 15:00:30,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76960.0, 'new_value': 85884.0}, {'field': 'total_amount', 'old_value': 76960.0, 'new_value': 85884.0}, {'field': 'order_count', 'old_value': 2703, 'new_value': 2997}]
2025-05-14 15:00:30,919 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-14 15:00:31,388 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-14 15:00:31,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360876.71, 'new_value': 385086.01}, {'field': 'total_amount', 'old_value': 360876.71, 'new_value': 385086.01}, {'field': 'order_count', 'old_value': 1661, 'new_value': 1789}]
2025-05-14 15:00:31,388 - INFO - 日期 2025-05 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-05-14 15:00:31,388 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-05-14 15:00:31,388 - INFO - =================同步完成====================
2025-05-14 18:00:01,989 - INFO - =================使用默认全量同步=============
2025-05-14 18:00:03,318 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 18:00:03,318 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 18:00:03,333 - INFO - 开始处理日期: 2025-01
2025-05-14 18:00:03,349 - INFO - Request Parameters - Page 1:
2025-05-14 18:00:03,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:03,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:04,364 - INFO - Response - Page 1:
2025-05-14 18:00:04,568 - INFO - 第 1 页获取到 100 条记录
2025-05-14 18:00:04,568 - INFO - Request Parameters - Page 2:
2025-05-14 18:00:04,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:04,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:05,333 - INFO - Response - Page 2:
2025-05-14 18:00:05,536 - INFO - 第 2 页获取到 100 条记录
2025-05-14 18:00:05,536 - INFO - Request Parameters - Page 3:
2025-05-14 18:00:05,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:05,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:06,036 - INFO - Response - Page 3:
2025-05-14 18:00:06,239 - INFO - 第 3 页获取到 100 条记录
2025-05-14 18:00:06,239 - INFO - Request Parameters - Page 4:
2025-05-14 18:00:06,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:06,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:06,661 - INFO - Response - Page 4:
2025-05-14 18:00:06,864 - INFO - 第 4 页获取到 100 条记录
2025-05-14 18:00:06,864 - INFO - Request Parameters - Page 5:
2025-05-14 18:00:06,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:06,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:07,302 - INFO - Response - Page 5:
2025-05-14 18:00:07,505 - INFO - 第 5 页获取到 100 条记录
2025-05-14 18:00:07,505 - INFO - Request Parameters - Page 6:
2025-05-14 18:00:07,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:07,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:08,005 - INFO - Response - Page 6:
2025-05-14 18:00:08,208 - INFO - 第 6 页获取到 100 条记录
2025-05-14 18:00:08,208 - INFO - Request Parameters - Page 7:
2025-05-14 18:00:08,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:08,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:08,896 - INFO - Response - Page 7:
2025-05-14 18:00:09,099 - INFO - 第 7 页获取到 82 条记录
2025-05-14 18:00:09,099 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 18:00:09,099 - INFO - 获取到 682 条表单数据
2025-05-14 18:00:09,099 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 18:00:09,114 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 18:00:09,114 - INFO - 开始处理日期: 2025-02
2025-05-14 18:00:09,114 - INFO - Request Parameters - Page 1:
2025-05-14 18:00:09,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:09,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:09,583 - INFO - Response - Page 1:
2025-05-14 18:00:09,786 - INFO - 第 1 页获取到 100 条记录
2025-05-14 18:00:09,786 - INFO - Request Parameters - Page 2:
2025-05-14 18:00:09,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:09,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:10,380 - INFO - Response - Page 2:
2025-05-14 18:00:10,583 - INFO - 第 2 页获取到 100 条记录
2025-05-14 18:00:10,583 - INFO - Request Parameters - Page 3:
2025-05-14 18:00:10,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:10,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:11,068 - INFO - Response - Page 3:
2025-05-14 18:00:11,271 - INFO - 第 3 页获取到 100 条记录
2025-05-14 18:00:11,271 - INFO - Request Parameters - Page 4:
2025-05-14 18:00:11,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:11,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:11,755 - INFO - Response - Page 4:
2025-05-14 18:00:11,958 - INFO - 第 4 页获取到 100 条记录
2025-05-14 18:00:11,958 - INFO - Request Parameters - Page 5:
2025-05-14 18:00:11,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:11,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:12,427 - INFO - Response - Page 5:
2025-05-14 18:00:12,630 - INFO - 第 5 页获取到 100 条记录
2025-05-14 18:00:12,630 - INFO - Request Parameters - Page 6:
2025-05-14 18:00:12,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:12,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:13,130 - INFO - Response - Page 6:
2025-05-14 18:00:13,333 - INFO - 第 6 页获取到 100 条记录
2025-05-14 18:00:13,333 - INFO - Request Parameters - Page 7:
2025-05-14 18:00:13,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:13,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:13,755 - INFO - Response - Page 7:
2025-05-14 18:00:13,958 - INFO - 第 7 页获取到 70 条记录
2025-05-14 18:00:13,958 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 18:00:13,958 - INFO - 获取到 670 条表单数据
2025-05-14 18:00:13,958 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 18:00:13,974 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 18:00:13,974 - INFO - 开始处理日期: 2025-03
2025-05-14 18:00:13,974 - INFO - Request Parameters - Page 1:
2025-05-14 18:00:13,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:13,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:14,489 - INFO - Response - Page 1:
2025-05-14 18:00:14,693 - INFO - 第 1 页获取到 100 条记录
2025-05-14 18:00:14,693 - INFO - Request Parameters - Page 2:
2025-05-14 18:00:14,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:14,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:15,161 - INFO - Response - Page 2:
2025-05-14 18:00:15,364 - INFO - 第 2 页获取到 100 条记录
2025-05-14 18:00:15,364 - INFO - Request Parameters - Page 3:
2025-05-14 18:00:15,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:15,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:15,818 - INFO - Response - Page 3:
2025-05-14 18:00:16,021 - INFO - 第 3 页获取到 100 条记录
2025-05-14 18:00:16,021 - INFO - Request Parameters - Page 4:
2025-05-14 18:00:16,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:16,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:17,114 - INFO - Response - Page 4:
2025-05-14 18:00:17,318 - INFO - 第 4 页获取到 100 条记录
2025-05-14 18:00:17,318 - INFO - Request Parameters - Page 5:
2025-05-14 18:00:17,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:17,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:17,989 - INFO - Response - Page 5:
2025-05-14 18:00:18,192 - INFO - 第 5 页获取到 100 条记录
2025-05-14 18:00:18,192 - INFO - Request Parameters - Page 6:
2025-05-14 18:00:18,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:18,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:18,692 - INFO - Response - Page 6:
2025-05-14 18:00:18,896 - INFO - 第 6 页获取到 100 条记录
2025-05-14 18:00:18,896 - INFO - Request Parameters - Page 7:
2025-05-14 18:00:18,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:18,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:19,302 - INFO - Response - Page 7:
2025-05-14 18:00:19,505 - INFO - 第 7 页获取到 61 条记录
2025-05-14 18:00:19,505 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 18:00:19,505 - INFO - 获取到 661 条表单数据
2025-05-14 18:00:19,505 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 18:00:19,521 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 18:00:19,521 - INFO - 开始处理日期: 2025-04
2025-05-14 18:00:19,521 - INFO - Request Parameters - Page 1:
2025-05-14 18:00:19,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:19,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:19,989 - INFO - Response - Page 1:
2025-05-14 18:00:20,192 - INFO - 第 1 页获取到 100 条记录
2025-05-14 18:00:20,192 - INFO - Request Parameters - Page 2:
2025-05-14 18:00:20,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:20,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:20,708 - INFO - Response - Page 2:
2025-05-14 18:00:20,911 - INFO - 第 2 页获取到 100 条记录
2025-05-14 18:00:20,911 - INFO - Request Parameters - Page 3:
2025-05-14 18:00:20,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:20,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:21,333 - INFO - Response - Page 3:
2025-05-14 18:00:21,536 - INFO - 第 3 页获取到 100 条记录
2025-05-14 18:00:21,536 - INFO - Request Parameters - Page 4:
2025-05-14 18:00:21,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:21,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:22,021 - INFO - Response - Page 4:
2025-05-14 18:00:22,224 - INFO - 第 4 页获取到 100 条记录
2025-05-14 18:00:22,224 - INFO - Request Parameters - Page 5:
2025-05-14 18:00:22,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:22,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:22,692 - INFO - Response - Page 5:
2025-05-14 18:00:22,896 - INFO - 第 5 页获取到 100 条记录
2025-05-14 18:00:22,896 - INFO - Request Parameters - Page 6:
2025-05-14 18:00:22,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:22,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:23,380 - INFO - Response - Page 6:
2025-05-14 18:00:23,583 - INFO - 第 6 页获取到 100 条记录
2025-05-14 18:00:23,583 - INFO - Request Parameters - Page 7:
2025-05-14 18:00:23,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:23,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:24,005 - INFO - Response - Page 7:
2025-05-14 18:00:24,208 - INFO - 第 7 页获取到 54 条记录
2025-05-14 18:00:24,208 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 18:00:24,208 - INFO - 获取到 654 条表单数据
2025-05-14 18:00:24,208 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 18:00:24,208 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-05-14 18:00:24,692 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MQ9
2025-05-14 18:00:24,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 435542.02, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1475.0, 'new_value': 419707.0}, {'field': 'total_amount', 'old_value': 437017.02, 'new_value': 419707.0}, {'field': 'order_count', 'old_value': 5269, 'new_value': 5120}]
2025-05-14 18:00:24,692 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-05-14 18:00:25,208 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MX9
2025-05-14 18:00:25,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202446.21, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 27048.11, 'new_value': 228382.55}, {'field': 'total_amount', 'old_value': 229494.32, 'new_value': 228382.55}, {'field': 'order_count', 'old_value': 6611, 'new_value': 30}]
2025-05-14 18:00:25,208 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-05-14 18:00:25,661 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-05-14 18:00:25,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21578.68, 'new_value': 20724.27}, {'field': 'offline_amount', 'old_value': 410942.9, 'new_value': 386254.5}, {'field': 'total_amount', 'old_value': 432521.58, 'new_value': 406978.77}, {'field': 'order_count', 'old_value': 3110, 'new_value': 2951}]
2025-05-14 18:00:25,661 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-05-14 18:00:26,177 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M2B
2025-05-14 18:00:26,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47944.06, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 46763.02, 'new_value': 95457.19}, {'field': 'total_amount', 'old_value': 94707.08, 'new_value': 95457.19}, {'field': 'order_count', 'old_value': 3393, 'new_value': 30}]
2025-05-14 18:00:26,177 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ6
2025-05-14 18:00:26,725 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ6
2025-05-14 18:00:26,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28588.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 188560.0, 'new_value': 282148.0}, {'field': 'total_amount', 'old_value': 217148.0, 'new_value': 282148.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-14 18:00:26,725 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-05-14 18:00:27,192 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MF2
2025-05-14 18:00:27,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49087.95, 'new_value': 56715.86}, {'field': 'total_amount', 'old_value': 49087.95, 'new_value': 56715.86}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-14 18:00:27,192 - INFO - 日期 2025-04 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-05-14 18:00:27,192 - INFO - 开始处理日期: 2025-05
2025-05-14 18:00:27,192 - INFO - Request Parameters - Page 1:
2025-05-14 18:00:27,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:27,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:27,755 - INFO - Response - Page 1:
2025-05-14 18:00:27,974 - INFO - 第 1 页获取到 100 条记录
2025-05-14 18:00:27,974 - INFO - Request Parameters - Page 2:
2025-05-14 18:00:27,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:27,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:28,474 - INFO - Response - Page 2:
2025-05-14 18:00:28,677 - INFO - 第 2 页获取到 100 条记录
2025-05-14 18:00:28,677 - INFO - Request Parameters - Page 3:
2025-05-14 18:00:28,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:28,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:29,146 - INFO - Response - Page 3:
2025-05-14 18:00:29,349 - INFO - 第 3 页获取到 100 条记录
2025-05-14 18:00:29,349 - INFO - Request Parameters - Page 4:
2025-05-14 18:00:29,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:29,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:29,817 - INFO - Response - Page 4:
2025-05-14 18:00:30,021 - INFO - 第 4 页获取到 100 条记录
2025-05-14 18:00:30,021 - INFO - Request Parameters - Page 5:
2025-05-14 18:00:30,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:30,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:30,521 - INFO - Response - Page 5:
2025-05-14 18:00:30,724 - INFO - 第 5 页获取到 100 条记录
2025-05-14 18:00:30,724 - INFO - Request Parameters - Page 6:
2025-05-14 18:00:30,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:30,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:31,239 - INFO - Response - Page 6:
2025-05-14 18:00:31,442 - INFO - 第 6 页获取到 100 条记录
2025-05-14 18:00:31,442 - INFO - Request Parameters - Page 7:
2025-05-14 18:00:31,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 18:00:31,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 18:00:31,755 - INFO - Response - Page 7:
2025-05-14 18:00:31,958 - INFO - 第 7 页获取到 25 条记录
2025-05-14 18:00:31,958 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 18:00:31,958 - INFO - 获取到 625 条表单数据
2025-05-14 18:00:31,958 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 18:00:31,958 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-14 18:00:32,458 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-14 18:00:32,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376711.98, 'new_value': 401603.98}, {'field': 'total_amount', 'old_value': 376711.98, 'new_value': 401603.98}, {'field': 'order_count', 'old_value': 26087, 'new_value': 1195}]
2025-05-14 18:00:32,458 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-14 18:00:32,880 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-14 18:00:32,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14679.7, 'new_value': 15265.7}, {'field': 'total_amount', 'old_value': 14679.7, 'new_value': 15265.7}, {'field': 'order_count', 'old_value': 97, 'new_value': 102}]
2025-05-14 18:00:32,880 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-14 18:00:33,396 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-14 18:00:33,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 226206.0, 'new_value': 248214.0}, {'field': 'offline_amount', 'old_value': 127856.0, 'new_value': 135515.0}, {'field': 'total_amount', 'old_value': 354062.0, 'new_value': 383729.0}, {'field': 'order_count', 'old_value': 382, 'new_value': 415}]
2025-05-14 18:00:33,396 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-14 18:00:34,036 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-14 18:00:34,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12184.48, 'new_value': 20393.18}, {'field': 'offline_amount', 'old_value': 30401.82, 'new_value': 44560.41}, {'field': 'total_amount', 'old_value': 42586.3, 'new_value': 64953.59}, {'field': 'order_count', 'old_value': 1001, 'new_value': 1490}]
2025-05-14 18:00:34,036 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-14 18:00:34,458 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-14 18:00:34,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2361.78, 'new_value': 2464.78}, {'field': 'offline_amount', 'old_value': 43643.57, 'new_value': 46464.13}, {'field': 'total_amount', 'old_value': 46005.35, 'new_value': 48928.91}, {'field': 'order_count', 'old_value': 1734, 'new_value': 1859}]
2025-05-14 18:00:34,458 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-14 18:00:34,942 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-14 18:00:34,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49423.59, 'new_value': 55154.23}, {'field': 'offline_amount', 'old_value': 26193.27, 'new_value': 26826.63}, {'field': 'total_amount', 'old_value': 75616.86, 'new_value': 81980.86}, {'field': 'order_count', 'old_value': 4349, 'new_value': 4698}]
2025-05-14 18:00:34,942 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-14 18:00:35,489 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-14 18:00:35,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21208.4, 'new_value': 23024.88}, {'field': 'offline_amount', 'old_value': 27842.65, 'new_value': 29336.09}, {'field': 'total_amount', 'old_value': 49051.05, 'new_value': 52360.97}, {'field': 'order_count', 'old_value': 2442, 'new_value': 2622}]
2025-05-14 18:00:35,489 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-14 18:00:35,942 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-14 18:00:35,942 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5175.98, 'new_value': 5519.58}, {'field': 'offline_amount', 'old_value': 64408.98, 'new_value': 68841.45}, {'field': 'total_amount', 'old_value': 69584.96, 'new_value': 74361.03}, {'field': 'order_count', 'old_value': 1085, 'new_value': 1170}]
2025-05-14 18:00:35,942 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-14 18:00:36,364 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-14 18:00:36,364 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44573.1, 'new_value': 40568.95}, {'field': 'offline_amount', 'old_value': 24529.51, 'new_value': 22697.11}, {'field': 'total_amount', 'old_value': 69102.61, 'new_value': 63266.06}, {'field': 'order_count', 'old_value': 2313, 'new_value': 2109}]
2025-05-14 18:00:36,364 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-14 18:00:36,833 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-14 18:00:36,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35712.14, 'new_value': 38038.09}, {'field': 'total_amount', 'old_value': 35712.14, 'new_value': 38038.09}, {'field': 'order_count', 'old_value': 1625, 'new_value': 1743}]
2025-05-14 18:00:36,833 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-14 18:00:37,286 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-14 18:00:37,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7431.7, 'new_value': 9015.5}, {'field': 'total_amount', 'old_value': 7973.7, 'new_value': 9557.5}, {'field': 'order_count', 'old_value': 13, 'new_value': 24}]
2025-05-14 18:00:37,286 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-14 18:00:37,817 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-14 18:00:37,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481718.0, 'new_value': 442326.0}, {'field': 'total_amount', 'old_value': 481718.0, 'new_value': 442326.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 80}]
2025-05-14 18:00:37,817 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-14 18:00:38,255 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-14 18:00:38,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 923.35, 'new_value': 1077.79}, {'field': 'offline_amount', 'old_value': 13510.78, 'new_value': 13880.54}, {'field': 'total_amount', 'old_value': 14434.13, 'new_value': 14958.33}, {'field': 'order_count', 'old_value': 653, 'new_value': 674}]
2025-05-14 18:00:38,255 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-14 18:00:38,677 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-14 18:00:38,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1410.7, 'new_value': 1656.7}, {'field': 'total_amount', 'old_value': 1410.7, 'new_value': 1656.7}, {'field': 'order_count', 'old_value': 128, 'new_value': 154}]
2025-05-14 18:00:38,677 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-14 18:00:39,161 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-14 18:00:39,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 631000.0, 'new_value': 668000.0}, {'field': 'total_amount', 'old_value': 631000.0, 'new_value': 668000.0}]
2025-05-14 18:00:39,161 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-14 18:00:39,614 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-14 18:00:39,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7606.9, 'new_value': 7616.8}, {'field': 'total_amount', 'old_value': 7606.9, 'new_value': 7616.8}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-14 18:00:39,614 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-14 18:00:40,114 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-14 18:00:40,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 481420.16, 'new_value': 509528.16}, {'field': 'total_amount', 'old_value': 481420.16, 'new_value': 509528.16}, {'field': 'order_count', 'old_value': 1776, 'new_value': 1906}]
2025-05-14 18:00:40,114 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-14 18:00:40,583 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-14 18:00:40,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28115.9, 'new_value': 28145.8}, {'field': 'total_amount', 'old_value': 28115.9, 'new_value': 28145.8}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-14 18:00:40,583 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-14 18:00:41,052 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-14 18:00:41,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14872.0, 'new_value': 15198.6}, {'field': 'offline_amount', 'old_value': 145655.4, 'new_value': 149306.0}, {'field': 'total_amount', 'old_value': 160527.4, 'new_value': 164504.6}, {'field': 'order_count', 'old_value': 1219, 'new_value': 1265}]
2025-05-14 18:00:41,052 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-14 18:00:41,505 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-14 18:00:41,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21210.0, 'new_value': 24690.0}, {'field': 'total_amount', 'old_value': 21210.0, 'new_value': 24690.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-14 18:00:41,505 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-14 18:00:42,067 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-14 18:00:42,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71778.75, 'new_value': 77270.75}, {'field': 'total_amount', 'old_value': 71778.75, 'new_value': 77270.75}, {'field': 'order_count', 'old_value': 6566, 'new_value': 7057}]
2025-05-14 18:00:42,067 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-14 18:00:42,520 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-14 18:00:42,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79306.3, 'new_value': 76659.3}, {'field': 'total_amount', 'old_value': 79306.3, 'new_value': 76659.3}, {'field': 'order_count', 'old_value': 777, 'new_value': 790}]
2025-05-14 18:00:42,520 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-14 18:00:43,005 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-14 18:00:43,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42522.74, 'new_value': 46711.74}, {'field': 'total_amount', 'old_value': 42522.74, 'new_value': 46711.74}, {'field': 'order_count', 'old_value': 1559, 'new_value': 1608}]
2025-05-14 18:00:43,005 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-14 18:00:43,442 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-14 18:00:43,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67827.0, 'new_value': 66221.0}, {'field': 'total_amount', 'old_value': 67827.0, 'new_value': 66221.0}, {'field': 'order_count', 'old_value': 2457, 'new_value': 2565}]
2025-05-14 18:00:43,442 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-14 18:00:43,958 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-14 18:00:43,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15077.07, 'new_value': 16376.07}, {'field': 'total_amount', 'old_value': 15077.07, 'new_value': 16376.07}, {'field': 'order_count', 'old_value': 1505, 'new_value': 1623}]
2025-05-14 18:00:43,958 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-14 18:00:44,364 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-14 18:00:44,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32000.0, 'new_value': 36000.0}, {'field': 'total_amount', 'old_value': 32000.0, 'new_value': 36000.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-14 18:00:44,364 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-14 18:00:44,802 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-14 18:00:44,802 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20014.7, 'new_value': 20890.6}, {'field': 'offline_amount', 'old_value': 16267.62, 'new_value': 17105.24}, {'field': 'total_amount', 'old_value': 36282.32, 'new_value': 37995.84}, {'field': 'order_count', 'old_value': 5856, 'new_value': 6136}]
2025-05-14 18:00:44,802 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-14 18:00:45,239 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-14 18:00:45,239 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53704.71, 'new_value': 63502.76}, {'field': 'offline_amount', 'old_value': 143295.53, 'new_value': 177011.56}, {'field': 'total_amount', 'old_value': 197000.24, 'new_value': 240514.32}, {'field': 'order_count', 'old_value': 1943, 'new_value': 2601}]
2025-05-14 18:00:45,239 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-14 18:00:45,630 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-14 18:00:45,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26073.4, 'new_value': 26516.0}, {'field': 'total_amount', 'old_value': 26073.4, 'new_value': 26516.0}, {'field': 'order_count', 'old_value': 279, 'new_value': 284}]
2025-05-14 18:00:45,630 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-14 18:00:46,177 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-14 18:00:46,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4574700.0, 'new_value': 4585700.0}, {'field': 'total_amount', 'old_value': 4574700.0, 'new_value': 4585700.0}]
2025-05-14 18:00:46,177 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-14 18:00:46,770 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-14 18:00:46,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23574.5, 'new_value': 25453.5}, {'field': 'total_amount', 'old_value': 23574.5, 'new_value': 25453.5}, {'field': 'order_count', 'old_value': 1160, 'new_value': 1260}]
2025-05-14 18:00:46,770 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-14 18:00:47,224 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-14 18:00:47,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8262.59, 'new_value': 8915.53}, {'field': 'offline_amount', 'old_value': 129408.78, 'new_value': 133437.68}, {'field': 'total_amount', 'old_value': 137671.37, 'new_value': 142353.21}, {'field': 'order_count', 'old_value': 1755, 'new_value': 1841}]
2025-05-14 18:00:47,224 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-14 18:00:47,692 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-14 18:00:47,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25620.0, 'new_value': 26108.0}, {'field': 'total_amount', 'old_value': 25620.0, 'new_value': 26108.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 135}]
2025-05-14 18:00:47,692 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-14 18:00:48,239 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-14 18:00:48,239 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14054.0, 'new_value': 15812.0}, {'field': 'offline_amount', 'old_value': 98627.0, 'new_value': 130317.0}, {'field': 'total_amount', 'old_value': 112681.0, 'new_value': 146129.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 133}]
2025-05-14 18:00:48,239 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-14 18:00:48,692 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-14 18:00:48,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142764.79, 'new_value': 155006.12}, {'field': 'total_amount', 'old_value': 175845.0, 'new_value': 188086.33}, {'field': 'order_count', 'old_value': 7246, 'new_value': 7780}]
2025-05-14 18:00:48,692 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-14 18:00:49,192 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-14 18:00:49,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 239855.61, 'new_value': 251339.45}, {'field': 'total_amount', 'old_value': 239855.61, 'new_value': 251339.45}, {'field': 'order_count', 'old_value': 457, 'new_value': 478}]
2025-05-14 18:00:49,192 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-14 18:00:49,724 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-14 18:00:49,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44106.0, 'new_value': 45574.0}, {'field': 'total_amount', 'old_value': 44106.0, 'new_value': 45574.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-14 18:00:49,724 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-14 18:00:50,208 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-14 18:00:50,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75353.74, 'new_value': 81069.74}, {'field': 'total_amount', 'old_value': 88562.46, 'new_value': 94278.46}, {'field': 'order_count', 'old_value': 5033, 'new_value': 5371}]
2025-05-14 18:00:50,208 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-14 18:00:50,677 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-14 18:00:50,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15236.0, 'new_value': 16632.0}, {'field': 'total_amount', 'old_value': 16612.0, 'new_value': 18008.0}, {'field': 'order_count', 'old_value': 1849, 'new_value': 1964}]
2025-05-14 18:00:50,677 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-14 18:00:51,286 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-14 18:00:51,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44010.0, 'new_value': 45590.0}, {'field': 'total_amount', 'old_value': 44010.0, 'new_value': 45590.0}, {'field': 'order_count', 'old_value': 309, 'new_value': 325}]
2025-05-14 18:00:51,286 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-14 18:00:51,708 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-14 18:00:51,708 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3642.32, 'new_value': 4680.54}, {'field': 'offline_amount', 'old_value': 54004.1, 'new_value': 67267.8}, {'field': 'total_amount', 'old_value': 57646.42, 'new_value': 71948.34}, {'field': 'order_count', 'old_value': 3737, 'new_value': 4333}]
2025-05-14 18:00:51,708 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-14 18:00:52,224 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-14 18:00:52,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9998.23, 'new_value': 10100.03}, {'field': 'offline_amount', 'old_value': 75950.26, 'new_value': 78962.41}, {'field': 'total_amount', 'old_value': 85948.49, 'new_value': 89062.44}, {'field': 'order_count', 'old_value': 2512, 'new_value': 2608}]
2025-05-14 18:00:52,224 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-14 18:00:52,661 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-14 18:00:52,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51558.0, 'new_value': 55321.9}, {'field': 'total_amount', 'old_value': 51558.0, 'new_value': 55321.9}, {'field': 'order_count', 'old_value': 2539, 'new_value': 2723}]
2025-05-14 18:00:52,677 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-14 18:00:53,099 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-14 18:00:53,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9485.22, 'new_value': 23846.5}, {'field': 'offline_amount', 'old_value': 78593.71, 'new_value': 87367.21}, {'field': 'total_amount', 'old_value': 88078.93, 'new_value': 111213.71}, {'field': 'order_count', 'old_value': 3746, 'new_value': 4802}]
2025-05-14 18:00:53,099 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-14 18:00:53,520 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-14 18:00:53,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 446053.0, 'new_value': 491905.0}, {'field': 'total_amount', 'old_value': 446053.0, 'new_value': 491905.0}, {'field': 'order_count', 'old_value': 922, 'new_value': 1027}]
2025-05-14 18:00:53,520 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-14 18:00:54,020 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMJA
2025-05-14 18:00:54,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8560.0, 'new_value': 9165.0}, {'field': 'total_amount', 'old_value': 8560.0, 'new_value': 9165.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 56}]
2025-05-14 18:00:54,020 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-14 18:00:54,458 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-14 18:00:54,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7609.0, 'new_value': 8889.0}, {'field': 'total_amount', 'old_value': 7609.0, 'new_value': 8889.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-14 18:00:54,458 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-14 18:00:54,942 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-14 18:00:54,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216756.01, 'new_value': 233997.47}, {'field': 'total_amount', 'old_value': 216756.01, 'new_value': 233997.47}, {'field': 'order_count', 'old_value': 1350, 'new_value': 1548}]
2025-05-14 18:00:54,942 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E
2025-05-14 18:00:55,427 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E
2025-05-14 18:00:55,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102364.0, 'new_value': 225896.0}, {'field': 'total_amount', 'old_value': 102364.0, 'new_value': 225896.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 474}]
2025-05-14 18:00:55,427 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-14 18:00:55,817 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-14 18:00:55,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61455.0, 'new_value': 58553.0}, {'field': 'total_amount', 'old_value': 61455.0, 'new_value': 58553.0}, {'field': 'order_count', 'old_value': 351, 'new_value': 375}]
2025-05-14 18:00:55,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-14 18:00:56,255 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-14 18:00:56,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97021.07, 'new_value': 101090.92}, {'field': 'total_amount', 'old_value': 97021.07, 'new_value': 101090.92}, {'field': 'order_count', 'old_value': 1243, 'new_value': 1317}]
2025-05-14 18:00:56,255 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-14 18:00:56,692 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-14 18:00:56,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60191.34, 'new_value': 68350.34}, {'field': 'total_amount', 'old_value': 60191.34, 'new_value': 68350.34}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-14 18:00:56,692 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-14 18:00:57,145 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-14 18:00:57,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184800.0, 'new_value': 189100.0}, {'field': 'total_amount', 'old_value': 184800.0, 'new_value': 189100.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-14 18:00:57,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-14 18:00:57,583 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-14 18:00:57,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4561.67, 'new_value': 4773.67}, {'field': 'total_amount', 'old_value': 4561.67, 'new_value': 4773.67}, {'field': 'order_count', 'old_value': 144, 'new_value': 156}]
2025-05-14 18:00:57,583 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-14 18:00:58,083 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-14 18:00:58,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160182.5, 'new_value': 170756.5}, {'field': 'total_amount', 'old_value': 160182.5, 'new_value': 170756.5}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-05-14 18:00:58,083 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-14 18:00:58,661 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-14 18:00:58,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5894.0, 'new_value': 7274.0}, {'field': 'offline_amount', 'old_value': 4894.0, 'new_value': 5131.0}, {'field': 'total_amount', 'old_value': 10788.0, 'new_value': 12405.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 47}]
2025-05-14 18:00:58,661 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-14 18:00:59,145 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-14 18:00:59,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 899.0, 'new_value': 1298.0}, {'field': 'total_amount', 'old_value': 6497.0, 'new_value': 6896.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-14 18:00:59,145 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-14 18:00:59,692 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-14 18:00:59,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90502.65, 'new_value': 90130.24}, {'field': 'total_amount', 'old_value': 90502.65, 'new_value': 90130.24}, {'field': 'order_count', 'old_value': 691, 'new_value': 732}]
2025-05-14 18:00:59,692 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-14 18:01:00,114 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-14 18:01:00,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11945.0, 'new_value': 12717.0}, {'field': 'total_amount', 'old_value': 11945.0, 'new_value': 12717.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 74}]
2025-05-14 18:01:00,114 - INFO - 日期 2025-05 处理完成 - 更新: 59 条，插入: 0 条，错误: 0 条
2025-05-14 18:01:00,114 - INFO - 数据同步完成！更新: 65 条，插入: 0 条，错误: 0 条
2025-05-14 18:01:00,114 - INFO - =================同步完成====================
2025-05-14 21:00:02,011 - INFO - =================使用默认全量同步=============
2025-05-14 21:00:03,355 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-14 21:00:03,355 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-14 21:00:03,370 - INFO - 开始处理日期: 2025-01
2025-05-14 21:00:03,386 - INFO - Request Parameters - Page 1:
2025-05-14 21:00:03,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:03,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:04,527 - INFO - Response - Page 1:
2025-05-14 21:00:04,730 - INFO - 第 1 页获取到 100 条记录
2025-05-14 21:00:04,730 - INFO - Request Parameters - Page 2:
2025-05-14 21:00:04,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:04,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:05,183 - INFO - Response - Page 2:
2025-05-14 21:00:05,386 - INFO - 第 2 页获取到 100 条记录
2025-05-14 21:00:05,386 - INFO - Request Parameters - Page 3:
2025-05-14 21:00:05,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:05,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:05,917 - INFO - Response - Page 3:
2025-05-14 21:00:06,120 - INFO - 第 3 页获取到 100 条记录
2025-05-14 21:00:06,120 - INFO - Request Parameters - Page 4:
2025-05-14 21:00:06,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:06,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:06,651 - INFO - Response - Page 4:
2025-05-14 21:00:06,855 - INFO - 第 4 页获取到 100 条记录
2025-05-14 21:00:06,855 - INFO - Request Parameters - Page 5:
2025-05-14 21:00:06,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:06,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:07,292 - INFO - Response - Page 5:
2025-05-14 21:00:07,495 - INFO - 第 5 页获取到 100 条记录
2025-05-14 21:00:07,495 - INFO - Request Parameters - Page 6:
2025-05-14 21:00:07,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:07,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:07,933 - INFO - Response - Page 6:
2025-05-14 21:00:08,136 - INFO - 第 6 页获取到 100 条记录
2025-05-14 21:00:08,136 - INFO - Request Parameters - Page 7:
2025-05-14 21:00:08,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:08,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:08,605 - INFO - Response - Page 7:
2025-05-14 21:00:08,808 - INFO - 第 7 页获取到 82 条记录
2025-05-14 21:00:08,808 - INFO - 查询完成，共获取到 682 条记录
2025-05-14 21:00:08,808 - INFO - 获取到 682 条表单数据
2025-05-14 21:00:08,808 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-14 21:00:08,823 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 21:00:08,823 - INFO - 开始处理日期: 2025-02
2025-05-14 21:00:08,823 - INFO - Request Parameters - Page 1:
2025-05-14 21:00:08,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:08,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:09,355 - INFO - Response - Page 1:
2025-05-14 21:00:09,558 - INFO - 第 1 页获取到 100 条记录
2025-05-14 21:00:09,558 - INFO - Request Parameters - Page 2:
2025-05-14 21:00:09,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:09,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:09,980 - INFO - Response - Page 2:
2025-05-14 21:00:10,183 - INFO - 第 2 页获取到 100 条记录
2025-05-14 21:00:10,183 - INFO - Request Parameters - Page 3:
2025-05-14 21:00:10,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:10,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:10,714 - INFO - Response - Page 3:
2025-05-14 21:00:10,917 - INFO - 第 3 页获取到 100 条记录
2025-05-14 21:00:10,917 - INFO - Request Parameters - Page 4:
2025-05-14 21:00:10,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:10,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:11,605 - INFO - Response - Page 4:
2025-05-14 21:00:11,808 - INFO - 第 4 页获取到 100 条记录
2025-05-14 21:00:11,808 - INFO - Request Parameters - Page 5:
2025-05-14 21:00:11,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:11,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:12,276 - INFO - Response - Page 5:
2025-05-14 21:00:12,480 - INFO - 第 5 页获取到 100 条记录
2025-05-14 21:00:12,480 - INFO - Request Parameters - Page 6:
2025-05-14 21:00:12,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:12,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:13,073 - INFO - Response - Page 6:
2025-05-14 21:00:13,276 - INFO - 第 6 页获取到 100 条记录
2025-05-14 21:00:13,276 - INFO - Request Parameters - Page 7:
2025-05-14 21:00:13,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:13,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:13,730 - INFO - Response - Page 7:
2025-05-14 21:00:13,933 - INFO - 第 7 页获取到 70 条记录
2025-05-14 21:00:13,933 - INFO - 查询完成，共获取到 670 条记录
2025-05-14 21:00:13,933 - INFO - 获取到 670 条表单数据
2025-05-14 21:00:13,933 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-14 21:00:13,948 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 21:00:13,948 - INFO - 开始处理日期: 2025-03
2025-05-14 21:00:13,948 - INFO - Request Parameters - Page 1:
2025-05-14 21:00:13,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:13,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:14,433 - INFO - Response - Page 1:
2025-05-14 21:00:14,636 - INFO - 第 1 页获取到 100 条记录
2025-05-14 21:00:14,636 - INFO - Request Parameters - Page 2:
2025-05-14 21:00:14,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:14,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:15,167 - INFO - Response - Page 2:
2025-05-14 21:00:15,370 - INFO - 第 2 页获取到 100 条记录
2025-05-14 21:00:15,370 - INFO - Request Parameters - Page 3:
2025-05-14 21:00:15,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:15,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:15,792 - INFO - Response - Page 3:
2025-05-14 21:00:15,995 - INFO - 第 3 页获取到 100 条记录
2025-05-14 21:00:15,995 - INFO - Request Parameters - Page 4:
2025-05-14 21:00:15,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:15,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:16,480 - INFO - Response - Page 4:
2025-05-14 21:00:16,683 - INFO - 第 4 页获取到 100 条记录
2025-05-14 21:00:16,683 - INFO - Request Parameters - Page 5:
2025-05-14 21:00:16,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:16,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:17,183 - INFO - Response - Page 5:
2025-05-14 21:00:17,386 - INFO - 第 5 页获取到 100 条记录
2025-05-14 21:00:17,386 - INFO - Request Parameters - Page 6:
2025-05-14 21:00:17,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:17,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:18,026 - INFO - Response - Page 6:
2025-05-14 21:00:18,230 - INFO - 第 6 页获取到 100 条记录
2025-05-14 21:00:18,230 - INFO - Request Parameters - Page 7:
2025-05-14 21:00:18,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:18,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:18,651 - INFO - Response - Page 7:
2025-05-14 21:00:18,855 - INFO - 第 7 页获取到 61 条记录
2025-05-14 21:00:18,855 - INFO - 查询完成，共获取到 661 条记录
2025-05-14 21:00:18,855 - INFO - 获取到 661 条表单数据
2025-05-14 21:00:18,855 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-14 21:00:18,870 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 21:00:18,870 - INFO - 开始处理日期: 2025-04
2025-05-14 21:00:18,870 - INFO - Request Parameters - Page 1:
2025-05-14 21:00:18,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:18,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:19,526 - INFO - Response - Page 1:
2025-05-14 21:00:19,730 - INFO - 第 1 页获取到 100 条记录
2025-05-14 21:00:19,730 - INFO - Request Parameters - Page 2:
2025-05-14 21:00:19,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:19,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:20,198 - INFO - Response - Page 2:
2025-05-14 21:00:20,401 - INFO - 第 2 页获取到 100 条记录
2025-05-14 21:00:20,401 - INFO - Request Parameters - Page 3:
2025-05-14 21:00:20,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:20,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:20,917 - INFO - Response - Page 3:
2025-05-14 21:00:21,120 - INFO - 第 3 页获取到 100 条记录
2025-05-14 21:00:21,120 - INFO - Request Parameters - Page 4:
2025-05-14 21:00:21,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:21,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:21,589 - INFO - Response - Page 4:
2025-05-14 21:00:21,792 - INFO - 第 4 页获取到 100 条记录
2025-05-14 21:00:21,792 - INFO - Request Parameters - Page 5:
2025-05-14 21:00:21,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:21,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:22,230 - INFO - Response - Page 5:
2025-05-14 21:00:22,433 - INFO - 第 5 页获取到 100 条记录
2025-05-14 21:00:22,433 - INFO - Request Parameters - Page 6:
2025-05-14 21:00:22,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:22,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:22,854 - INFO - Response - Page 6:
2025-05-14 21:00:23,058 - INFO - 第 6 页获取到 100 条记录
2025-05-14 21:00:23,058 - INFO - Request Parameters - Page 7:
2025-05-14 21:00:23,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:23,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:23,495 - INFO - Response - Page 7:
2025-05-14 21:00:23,698 - INFO - 第 7 页获取到 54 条记录
2025-05-14 21:00:23,698 - INFO - 查询完成，共获取到 654 条记录
2025-05-14 21:00:23,698 - INFO - 获取到 654 条表单数据
2025-05-14 21:00:23,698 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-14 21:00:23,714 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-14 21:00:23,714 - INFO - 开始处理日期: 2025-05
2025-05-14 21:00:23,714 - INFO - Request Parameters - Page 1:
2025-05-14 21:00:23,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:23,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:24,214 - INFO - Response - Page 1:
2025-05-14 21:00:24,417 - INFO - 第 1 页获取到 100 条记录
2025-05-14 21:00:24,417 - INFO - Request Parameters - Page 2:
2025-05-14 21:00:24,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:24,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:24,886 - INFO - Response - Page 2:
2025-05-14 21:00:25,089 - INFO - 第 2 页获取到 100 条记录
2025-05-14 21:00:25,089 - INFO - Request Parameters - Page 3:
2025-05-14 21:00:25,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:25,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:25,558 - INFO - Response - Page 3:
2025-05-14 21:00:25,761 - INFO - 第 3 页获取到 100 条记录
2025-05-14 21:00:25,761 - INFO - Request Parameters - Page 4:
2025-05-14 21:00:25,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:25,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:26,198 - INFO - Response - Page 4:
2025-05-14 21:00:26,401 - INFO - 第 4 页获取到 100 条记录
2025-05-14 21:00:26,401 - INFO - Request Parameters - Page 5:
2025-05-14 21:00:26,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:26,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:26,870 - INFO - Response - Page 5:
2025-05-14 21:00:27,073 - INFO - 第 5 页获取到 100 条记录
2025-05-14 21:00:27,073 - INFO - Request Parameters - Page 6:
2025-05-14 21:00:27,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:27,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:27,495 - INFO - Response - Page 6:
2025-05-14 21:00:27,698 - INFO - 第 6 页获取到 100 条记录
2025-05-14 21:00:27,698 - INFO - Request Parameters - Page 7:
2025-05-14 21:00:27,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-14 21:00:27,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-14 21:00:28,026 - INFO - Response - Page 7:
2025-05-14 21:00:28,229 - INFO - 第 7 页获取到 25 条记录
2025-05-14 21:00:28,229 - INFO - 查询完成，共获取到 625 条记录
2025-05-14 21:00:28,229 - INFO - 获取到 625 条表单数据
2025-05-14 21:00:28,229 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-14 21:00:28,229 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-14 21:00:28,714 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-14 21:00:28,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44227.0, 'new_value': 47335.0}, {'field': 'offline_amount', 'old_value': 55981.0, 'new_value': 60104.0}, {'field': 'total_amount', 'old_value': 100208.0, 'new_value': 107439.0}, {'field': 'order_count', 'old_value': 2373, 'new_value': 2549}]
2025-05-14 21:00:28,729 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-14 21:00:28,729 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-14 21:00:28,729 - INFO - =================同步完成====================
