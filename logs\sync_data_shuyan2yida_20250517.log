2025-05-17 08:00:05,538 - INFO - ==================================================
2025-05-17 08:00:05,538 - INFO - 程序启动 - 版本 v1.0.0
2025-05-17 08:00:05,538 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250517.log
2025-05-17 08:00:05,538 - INFO - ==================================================
2025-05-17 08:00:05,539 - INFO - 程序入口点: __main__
2025-05-17 08:00:05,539 - INFO - ==================================================
2025-05-17 08:00:05,539 - INFO - 程序启动 - 版本 v1.0.1
2025-05-17 08:00:05,539 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250517.log
2025-05-17 08:00:05,539 - INFO - ==================================================
2025-05-17 08:00:05,836 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-17 08:00:05,837 - INFO - sales_data表已存在，无需创建
2025-05-17 08:00:05,838 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-17 08:00:05,838 - INFO - DataSyncManager初始化完成
2025-05-17 08:00:05,838 - INFO - 未提供日期参数，使用默认值
2025-05-17 08:00:05,838 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-17 08:00:05,838 - INFO - 开始综合数据同步流程...
2025-05-17 08:00:05,838 - INFO - 正在获取数衍平台日销售数据...
2025-05-17 08:00:05,839 - INFO - 查询数衍平台数据，时间段为: 2025-03-17, 2025-05-16
2025-05-17 08:00:05,839 - INFO - 正在获取********至********的数据
2025-05-17 08:00:05,839 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:05,839 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '88CA624100D67440DA280A20173C1A6C'}
2025-05-17 08:00:09,343 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:09,357 - INFO - 过滤后保留 1561 条记录
2025-05-17 08:00:11,359 - INFO - 正在获取********至********的数据
2025-05-17 08:00:11,359 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:11,360 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6ED95C1439FF649BF70DAD58FDF79747'}
2025-05-17 08:00:14,001 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:14,015 - INFO - 过滤后保留 1559 条记录
2025-05-17 08:00:16,016 - INFO - 正在获取********至********的数据
2025-05-17 08:00:16,016 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:16,017 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9870F0592B5B99F6712F58D2C2EDEF20'}
2025-05-17 08:00:18,423 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:18,437 - INFO - 过滤后保留 1515 条记录
2025-05-17 08:00:20,439 - INFO - 正在获取********至********的数据
2025-05-17 08:00:20,439 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:20,440 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4475DC342E3629E7355792FC864B5397'}
2025-05-17 08:00:22,792 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:22,804 - INFO - 过滤后保留 1500 条记录
2025-05-17 08:00:24,806 - INFO - 正在获取********至********的数据
2025-05-17 08:00:24,806 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:24,806 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '44D2A58D64BC707717BEFBC925E19F08'}
2025-05-17 08:00:27,165 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:27,177 - INFO - 过滤后保留 1506 条记录
2025-05-17 08:00:29,179 - INFO - 正在获取********至********的数据
2025-05-17 08:00:29,179 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:29,180 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AC3146975E236C01A48266D411A6BC1B'}
2025-05-17 08:00:31,809 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:31,822 - INFO - 过滤后保留 1482 条记录
2025-05-17 08:00:33,823 - INFO - 正在获取********至********的数据
2025-05-17 08:00:33,823 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:33,823 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0DF7E2B0DF6C5D78D9437FBE668AE83E'}
2025-05-17 08:00:36,545 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:36,560 - INFO - 过滤后保留 1490 条记录
2025-05-17 08:00:38,560 - INFO - 正在获取********至********的数据
2025-05-17 08:00:38,560 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:38,561 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '095C096CA698B3516A2ACF723E67E1A4'}
2025-05-17 08:00:40,947 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:40,959 - INFO - 过滤后保留 1473 条记录
2025-05-17 08:00:42,961 - INFO - 正在获取********至********的数据
2025-05-17 08:00:42,961 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-17 08:00:42,962 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '226AC2BE4354A96324CCEC81ED3DB128'}
2025-05-17 08:00:44,740 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-17 08:00:44,748 - INFO - 过滤后保留 1047 条记录
2025-05-17 08:00:46,750 - INFO - 开始保存数据到SQLite数据库，共 13133 条记录待处理
2025-05-17 08:00:47,298 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=78655ECA4A32471AB7842F8DE2018120, sale_time=2025-04-21
2025-05-17 08:00:47,299 - INFO - 变更字段: recommend_amount: 21297.0 -> 44796.0, amount: 21297 -> 44796, count: 4 -> 5, instore_amount: 21297.0 -> 44796.0, instore_count: 4 -> 5
2025-05-17 08:00:47,380 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O, sale_time=2025-05-03
2025-05-17 08:00:47,380 - INFO - 变更字段: amount: -16981 -> -17209
2025-05-17 08:00:47,462 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-05-11
2025-05-17 08:00:47,462 - INFO - 变更字段: recommend_amount: 1881.0 -> 2320.0, daily_bill_amount: 0.0 -> 2320.0
2025-05-17 08:00:47,469 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-05-06
2025-05-17 08:00:47,469 - INFO - 变更字段: recommend_amount: 3868.0 -> 4128.0, amount: 3868 -> 4128, count: 18 -> 19, instore_amount: 3868.0 -> 4128.0, instore_count: 18 -> 19
2025-05-17 08:00:47,473 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-05-10
2025-05-17 08:00:47,473 - INFO - 变更字段: amount: 14081 -> 14296, count: 111 -> 112, instore_amount: 6822.3 -> 7037.3, instore_count: 41 -> 42
2025-05-17 08:00:47,486 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-15
2025-05-17 08:00:47,487 - INFO - 变更字段: recommend_amount: 0.0 -> 4903.9, daily_bill_amount: 0.0 -> 4903.9
2025-05-17 08:00:47,495 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-05-15
2025-05-17 08:00:47,495 - INFO - 变更字段: amount: 3276 -> 3272, online_amount: 1649.75 -> 1645.75
2025-05-17 08:00:47,497 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-05-15
2025-05-17 08:00:47,497 - INFO - 变更字段: count: 39 -> 40, online_count: 24 -> 25
2025-05-17 08:00:47,500 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G, sale_time=2025-05-15
2025-05-17 08:00:47,500 - INFO - 变更字段: recommend_amount: 0.0 -> 5414.0, daily_bill_amount: 0.0 -> 5414.0
2025-05-17 08:00:47,503 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-15
2025-05-17 08:00:47,503 - INFO - 变更字段: recommend_amount: 0.0 -> 8948.8, daily_bill_amount: 0.0 -> 8948.8
2025-05-17 08:00:47,504 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S, sale_time=2025-05-15
2025-05-17 08:00:47,504 - INFO - 变更字段: recommend_amount: 7372.0 -> 11163.0, amount: 7372 -> 11163, count: 23 -> 42, instore_amount: 7372.0 -> 11163.0, instore_count: 23 -> 42
2025-05-17 08:00:47,508 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-15
2025-05-17 08:00:47,509 - INFO - 变更字段: count: 115 -> 116, online_count: 81 -> 82
2025-05-17 08:00:47,512 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-15
2025-05-17 08:00:47,512 - INFO - 变更字段: recommend_amount: 7621.4 -> 7643.3, amount: 7621 -> 7643, count: 122 -> 123, instore_amount: 7451.11 -> 7473.01, instore_count: 120 -> 121
2025-05-17 08:00:47,512 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-15
2025-05-17 08:00:47,513 - INFO - 变更字段: amount: 5052 -> 5124, count: 262 -> 266, online_amount: 2345.71 -> 2417.47, online_count: 112 -> 116
2025-05-17 08:00:47,515 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-15
2025-05-17 08:00:47,515 - INFO - 变更字段: recommend_amount: 5721.32 -> 5766.82, amount: 5721 -> 5766, count: 262 -> 264, instore_amount: 1575.96 -> 1586.96, instore_count: 70 -> 71, online_amount: 4389.84 -> 4424.34, online_count: 192 -> 193
2025-05-17 08:00:47,516 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-05-15
2025-05-17 08:00:47,516 - INFO - 变更字段: amount: 2628 -> 2637, count: 67 -> 68, instore_amount: 1216.02 -> 1224.7, instore_count: 15 -> 16
2025-05-17 08:00:47,517 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-15
2025-05-17 08:00:47,518 - INFO - 变更字段: recommend_amount: 0.0 -> 19069.28, daily_bill_amount: 0.0 -> 19069.28
2025-05-17 08:00:47,521 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-15
2025-05-17 08:00:47,521 - INFO - 变更字段: amount: 32235 -> 32528, count: 221 -> 222, instore_amount: 22970.74 -> 23263.74, instore_count: 102 -> 103
2025-05-17 08:00:47,521 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-14
2025-05-17 08:00:47,521 - INFO - 变更字段: recommend_amount: 24138.56 -> 30174.0, daily_bill_amount: 24138.56 -> 30174.0
2025-05-17 08:00:47,523 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-15
2025-05-17 08:00:47,523 - INFO - 变更字段: amount: 4668 -> 4654
2025-05-17 08:00:47,524 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-15
2025-05-17 08:00:47,524 - INFO - 变更字段: count: 177 -> 178, online_amount: 4044.5 -> 4044.51, online_count: 128 -> 129
2025-05-17 08:00:47,525 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-05-15
2025-05-17 08:00:47,525 - INFO - 变更字段: amount: 579 -> 570
2025-05-17 08:00:47,527 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-05-15
2025-05-17 08:00:47,528 - INFO - 变更字段: amount: 1053 -> 1090, count: 12 -> 13, online_amount: 327.42 -> 364.34, online_count: 5 -> 6
2025-05-17 08:00:47,529 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEQ2M9E710I86N3H2U1H1001EQ7, sale_time=2025-05-15
2025-05-17 08:00:47,530 - INFO - 变更字段: amount: 25 -> 102, count: 3 -> 4, instore_amount: 0.0 -> 76.2, instore_count: 0 -> 1
2025-05-17 08:00:47,531 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-05-15
2025-05-17 08:00:47,532 - INFO - 变更字段: daily_bill_amount: 0.0 -> 1253.0
2025-05-17 08:00:47,532 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-05-13
2025-05-17 08:00:47,532 - INFO - 变更字段: daily_bill_amount: 0.0 -> 398.0
2025-05-17 08:00:47,533 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-05-15
2025-05-17 08:00:47,533 - INFO - 变更字段: recommend_amount: 0.0 -> 3554.0, daily_bill_amount: 0.0 -> 3554.0
2025-05-17 08:00:47,534 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-05-15
2025-05-17 08:00:47,534 - INFO - 变更字段: amount: 3140 -> 2966
2025-05-17 08:00:47,537 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-15
2025-05-17 08:00:47,537 - INFO - 变更字段: amount: 6647 -> 6732, count: 397 -> 403, instore_amount: 5159.14 -> 5272.35, instore_count: 296 -> 305, online_amount: 1554.75 -> 1548.43, online_count: 101 -> 98
2025-05-17 08:00:47,540 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-05-15
2025-05-17 08:00:47,541 - INFO - 变更字段: amount: 13439 -> 15887, count: 108 -> 120, instore_amount: 11184.2 -> 13632.0, instore_count: 45 -> 57
2025-05-17 08:00:47,541 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-05-14
2025-05-17 08:00:47,541 - INFO - 变更字段: amount: 18337 -> 19165, count: 121 -> 122, instore_amount: 16606.5 -> 17434.5, instore_count: 69 -> 70
2025-05-17 08:00:47,544 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-05-15
2025-05-17 08:00:47,544 - INFO - 变更字段: recommend_amount: 6028.07 -> 6040.87, amount: 6028 -> 6040, count: 329 -> 330, instore_amount: 3586.62 -> 3599.42, instore_count: 193 -> 194
2025-05-17 08:00:47,545 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-15
2025-05-17 08:00:47,545 - INFO - 变更字段: amount: 17244 -> 17515, count: 229 -> 231, instore_amount: 16117.37 -> 16388.27, instore_count: 165 -> 167
2025-05-17 08:00:47,547 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-15
2025-05-17 08:00:47,547 - INFO - 变更字段: amount: 28023 -> 29599, count: 50 -> 51, instore_amount: 27868.24 -> 29444.24, instore_count: 49 -> 50
2025-05-17 08:00:47,548 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-15
2025-05-17 08:00:47,549 - INFO - 变更字段: recommend_amount: 0.0 -> 43647.7, daily_bill_amount: 0.0 -> 43647.7
2025-05-17 08:00:47,555 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIPK0KE3MN6E7AERKQ83JB001UND, sale_time=2025-05-15
2025-05-17 08:00:47,555 - INFO - 变更字段: amount: 5264 -> 5285, count: 212 -> 214, instore_amount: 1589.55 -> 1610.15, instore_count: 70 -> 72
2025-05-17 08:00:47,558 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-05-15
2025-05-17 08:00:47,558 - INFO - 变更字段: amount: 1421 -> 1419
2025-05-17 08:00:47,780 - INFO - SQLite数据保存完成，统计信息：
2025-05-17 08:00:47,781 - INFO - - 总记录数: 13133
2025-05-17 08:00:47,781 - INFO - - 成功插入: 209
2025-05-17 08:00:47,782 - INFO - - 成功更新: 37
2025-05-17 08:00:47,782 - INFO - - 无需更新: 12887
2025-05-17 08:00:47,782 - INFO - - 处理失败: 0
2025-05-17 08:00:53,243 - INFO - 数据已保存到Excel文件: 数衍平台数据导出_20250517.xlsx
2025-05-17 08:00:53,250 - INFO - 成功获取数衍平台数据，共 13133 条记录
2025-05-17 08:00:53,250 - INFO - 正在更新SQLite月度汇总数据...
2025-05-17 08:00:53,258 - INFO - 月度数据sqllite清空完成
2025-05-17 08:00:53,483 - INFO - 月度汇总数据更新完成，处理了 1189 条汇总记录
2025-05-17 08:00:53,484 - INFO - 成功更新月度汇总数据，共 1189 条记录
2025-05-17 08:00:53,484 - INFO - 正在获取宜搭日销售表单数据...
2025-05-17 08:00:53,485 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-17 00:00:00 至 2025-05-16 23:59:59
2025-05-17 08:00:53,485 - INFO - 查询分段 1: 2025-03-17 至 2025-03-23
2025-05-17 08:00:53,485 - INFO - 查询日期范围: 2025-03-17 至 2025-03-23，使用分页查询，每页 100 条记录
2025-05-17 08:00:53,485 - INFO - Request Parameters - Page 1:
2025-05-17 08:00:53,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:00:53,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:00:57,880 - INFO - API请求耗时: 4394ms
2025-05-17 08:00:57,881 - INFO - Response - Page 1
2025-05-17 08:00:57,881 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:00:58,381 - INFO - Request Parameters - Page 2:
2025-05-17 08:00:58,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:00:58,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:00:59,035 - INFO - API请求耗时: 653ms
2025-05-17 08:00:59,036 - INFO - Response - Page 2
2025-05-17 08:00:59,036 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:00:59,537 - INFO - Request Parameters - Page 3:
2025-05-17 08:00:59,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:00:59,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:00,304 - INFO - API请求耗时: 766ms
2025-05-17 08:01:00,304 - INFO - Response - Page 3
2025-05-17 08:01:00,305 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:01:00,806 - INFO - Request Parameters - Page 4:
2025-05-17 08:01:00,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:00,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:01,389 - INFO - API请求耗时: 582ms
2025-05-17 08:01:01,390 - INFO - Response - Page 4
2025-05-17 08:01:01,390 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:01:01,891 - INFO - Request Parameters - Page 5:
2025-05-17 08:01:01,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:01,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:04,659 - INFO - API请求耗时: 2768ms
2025-05-17 08:01:04,659 - INFO - Response - Page 5
2025-05-17 08:01:04,660 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:01:05,161 - INFO - Request Parameters - Page 6:
2025-05-17 08:01:05,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:05,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:05,881 - INFO - API请求耗时: 720ms
2025-05-17 08:01:05,881 - INFO - Response - Page 6
2025-05-17 08:01:05,882 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:01:06,382 - INFO - Request Parameters - Page 7:
2025-05-17 08:01:06,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:06,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:07,100 - INFO - API请求耗时: 717ms
2025-05-17 08:01:07,101 - INFO - Response - Page 7
2025-05-17 08:01:07,101 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:01:07,601 - INFO - Request Parameters - Page 8:
2025-05-17 08:01:07,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:07,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:08,245 - INFO - API请求耗时: 644ms
2025-05-17 08:01:08,246 - INFO - Response - Page 8
2025-05-17 08:01:08,246 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:01:08,748 - INFO - Request Parameters - Page 9:
2025-05-17 08:01:08,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:08,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:09,437 - INFO - API请求耗时: 687ms
2025-05-17 08:01:09,437 - INFO - Response - Page 9
2025-05-17 08:01:09,438 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:01:09,939 - INFO - Request Parameters - Page 10:
2025-05-17 08:01:09,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:09,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:10,553 - INFO - API请求耗时: 613ms
2025-05-17 08:01:10,554 - INFO - Response - Page 10
2025-05-17 08:01:10,554 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:01:11,055 - INFO - Request Parameters - Page 11:
2025-05-17 08:01:11,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:11,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:11,708 - INFO - API请求耗时: 652ms
2025-05-17 08:01:11,709 - INFO - Response - Page 11
2025-05-17 08:01:11,710 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:01:12,210 - INFO - Request Parameters - Page 12:
2025-05-17 08:01:12,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:12,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:12,861 - INFO - API请求耗时: 649ms
2025-05-17 08:01:12,861 - INFO - Response - Page 12
2025-05-17 08:01:12,862 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:01:13,363 - INFO - Request Parameters - Page 13:
2025-05-17 08:01:13,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:13,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:14,097 - INFO - API请求耗时: 733ms
2025-05-17 08:01:14,098 - INFO - Response - Page 13
2025-05-17 08:01:14,098 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:01:14,600 - INFO - Request Parameters - Page 14:
2025-05-17 08:01:14,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:14,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:15,279 - INFO - API请求耗时: 678ms
2025-05-17 08:01:15,280 - INFO - Response - Page 14
2025-05-17 08:01:15,280 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:01:15,780 - INFO - Request Parameters - Page 15:
2025-05-17 08:01:15,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:15,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:16,443 - INFO - API请求耗时: 661ms
2025-05-17 08:01:16,443 - INFO - Response - Page 15
2025-05-17 08:01:16,444 - INFO - 第 15 页获取到 100 条记录
2025-05-17 08:01:16,945 - INFO - Request Parameters - Page 16:
2025-05-17 08:01:16,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:16,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742140800485, 1742659200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:17,661 - INFO - API请求耗时: 716ms
2025-05-17 08:01:17,662 - INFO - Response - Page 16
2025-05-17 08:01:17,662 - INFO - 第 16 页获取到 65 条记录
2025-05-17 08:01:17,662 - INFO - 查询完成，共获取到 1565 条记录
2025-05-17 08:01:17,663 - INFO - 分段 1 查询成功，获取到 1565 条记录
2025-05-17 08:01:18,664 - INFO - 查询分段 2: 2025-03-24 至 2025-03-30
2025-05-17 08:01:18,664 - INFO - 查询日期范围: 2025-03-24 至 2025-03-30，使用分页查询，每页 100 条记录
2025-05-17 08:01:18,665 - INFO - Request Parameters - Page 1:
2025-05-17 08:01:18,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:18,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:19,339 - INFO - API请求耗时: 674ms
2025-05-17 08:01:19,339 - INFO - Response - Page 1
2025-05-17 08:01:19,340 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:01:19,841 - INFO - Request Parameters - Page 2:
2025-05-17 08:01:19,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:19,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:20,484 - INFO - API请求耗时: 642ms
2025-05-17 08:01:20,484 - INFO - Response - Page 2
2025-05-17 08:01:20,485 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:01:20,986 - INFO - Request Parameters - Page 3:
2025-05-17 08:01:20,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:20,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:21,605 - INFO - API请求耗时: 618ms
2025-05-17 08:01:21,606 - INFO - Response - Page 3
2025-05-17 08:01:21,606 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:01:22,107 - INFO - Request Parameters - Page 4:
2025-05-17 08:01:22,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:22,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:22,796 - INFO - API请求耗时: 688ms
2025-05-17 08:01:22,797 - INFO - Response - Page 4
2025-05-17 08:01:22,797 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:01:23,299 - INFO - Request Parameters - Page 5:
2025-05-17 08:01:23,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:23,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:23,918 - INFO - API请求耗时: 618ms
2025-05-17 08:01:23,919 - INFO - Response - Page 5
2025-05-17 08:01:23,920 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:01:24,421 - INFO - Request Parameters - Page 6:
2025-05-17 08:01:24,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:24,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:24,996 - INFO - API请求耗时: 574ms
2025-05-17 08:01:24,997 - INFO - Response - Page 6
2025-05-17 08:01:24,997 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:01:25,499 - INFO - Request Parameters - Page 7:
2025-05-17 08:01:25,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:25,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:26,074 - INFO - API请求耗时: 574ms
2025-05-17 08:01:26,074 - INFO - Response - Page 7
2025-05-17 08:01:26,075 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:01:26,576 - INFO - Request Parameters - Page 8:
2025-05-17 08:01:26,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:26,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:27,301 - INFO - API请求耗时: 724ms
2025-05-17 08:01:27,302 - INFO - Response - Page 8
2025-05-17 08:01:27,302 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:01:27,804 - INFO - Request Parameters - Page 9:
2025-05-17 08:01:27,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:27,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:28,489 - INFO - API请求耗时: 685ms
2025-05-17 08:01:28,490 - INFO - Response - Page 9
2025-05-17 08:01:28,490 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:01:28,990 - INFO - Request Parameters - Page 10:
2025-05-17 08:01:28,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:28,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:29,683 - INFO - API请求耗时: 691ms
2025-05-17 08:01:29,683 - INFO - Response - Page 10
2025-05-17 08:01:29,684 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:01:30,185 - INFO - Request Parameters - Page 11:
2025-05-17 08:01:30,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:30,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:30,854 - INFO - API请求耗时: 668ms
2025-05-17 08:01:30,854 - INFO - Response - Page 11
2025-05-17 08:01:30,855 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:01:31,355 - INFO - Request Parameters - Page 12:
2025-05-17 08:01:31,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:31,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:32,053 - INFO - API请求耗时: 697ms
2025-05-17 08:01:32,054 - INFO - Response - Page 12
2025-05-17 08:01:32,054 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:01:32,554 - INFO - Request Parameters - Page 13:
2025-05-17 08:01:32,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:32,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:33,161 - INFO - API请求耗时: 606ms
2025-05-17 08:01:33,161 - INFO - Response - Page 13
2025-05-17 08:01:33,162 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:01:33,663 - INFO - Request Parameters - Page 14:
2025-05-17 08:01:33,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:33,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600485, 1743264000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:34,165 - INFO - API请求耗时: 501ms
2025-05-17 08:01:34,165 - INFO - Response - Page 14
2025-05-17 08:01:34,166 - INFO - 第 14 页获取到 37 条记录
2025-05-17 08:01:34,166 - INFO - 查询完成，共获取到 1337 条记录
2025-05-17 08:01:34,166 - INFO - 分段 2 查询成功，获取到 1337 条记录
2025-05-17 08:01:35,167 - INFO - 查询分段 3: 2025-03-31 至 2025-04-06
2025-05-17 08:01:35,167 - INFO - 查询日期范围: 2025-03-31 至 2025-04-06，使用分页查询，每页 100 条记录
2025-05-17 08:01:35,168 - INFO - Request Parameters - Page 1:
2025-05-17 08:01:35,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:35,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:35,908 - INFO - API请求耗时: 740ms
2025-05-17 08:01:35,909 - INFO - Response - Page 1
2025-05-17 08:01:35,909 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:01:36,409 - INFO - Request Parameters - Page 2:
2025-05-17 08:01:36,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:36,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:37,094 - INFO - API请求耗时: 684ms
2025-05-17 08:01:37,094 - INFO - Response - Page 2
2025-05-17 08:01:37,095 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:01:37,596 - INFO - Request Parameters - Page 3:
2025-05-17 08:01:37,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:37,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:38,174 - INFO - API请求耗时: 577ms
2025-05-17 08:01:38,174 - INFO - Response - Page 3
2025-05-17 08:01:38,175 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:01:38,675 - INFO - Request Parameters - Page 4:
2025-05-17 08:01:38,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:38,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:39,314 - INFO - API请求耗时: 638ms
2025-05-17 08:01:39,315 - INFO - Response - Page 4
2025-05-17 08:01:39,315 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:01:39,816 - INFO - Request Parameters - Page 5:
2025-05-17 08:01:39,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:39,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:40,537 - INFO - API请求耗时: 720ms
2025-05-17 08:01:40,537 - INFO - Response - Page 5
2025-05-17 08:01:40,538 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:01:41,038 - INFO - Request Parameters - Page 6:
2025-05-17 08:01:41,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:41,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:41,656 - INFO - API请求耗时: 617ms
2025-05-17 08:01:41,656 - INFO - Response - Page 6
2025-05-17 08:01:41,657 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:01:42,157 - INFO - Request Parameters - Page 7:
2025-05-17 08:01:42,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:42,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:42,813 - INFO - API请求耗时: 654ms
2025-05-17 08:01:42,813 - INFO - Response - Page 7
2025-05-17 08:01:42,814 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:01:43,314 - INFO - Request Parameters - Page 8:
2025-05-17 08:01:43,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:43,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:44,010 - INFO - API请求耗时: 694ms
2025-05-17 08:01:44,011 - INFO - Response - Page 8
2025-05-17 08:01:44,011 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:01:44,512 - INFO - Request Parameters - Page 9:
2025-05-17 08:01:44,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:44,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:45,129 - INFO - API请求耗时: 616ms
2025-05-17 08:01:45,130 - INFO - Response - Page 9
2025-05-17 08:01:45,130 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:01:45,631 - INFO - Request Parameters - Page 10:
2025-05-17 08:01:45,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:45,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:46,276 - INFO - API请求耗时: 643ms
2025-05-17 08:01:46,276 - INFO - Response - Page 10
2025-05-17 08:01:46,276 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:01:46,777 - INFO - Request Parameters - Page 11:
2025-05-17 08:01:46,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:46,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:47,379 - INFO - API请求耗时: 601ms
2025-05-17 08:01:47,380 - INFO - Response - Page 11
2025-05-17 08:01:47,380 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:01:47,881 - INFO - Request Parameters - Page 12:
2025-05-17 08:01:47,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:47,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:48,470 - INFO - API请求耗时: 588ms
2025-05-17 08:01:48,470 - INFO - Response - Page 12
2025-05-17 08:01:48,471 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:01:48,971 - INFO - Request Parameters - Page 13:
2025-05-17 08:01:48,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:48,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:49,728 - INFO - API请求耗时: 756ms
2025-05-17 08:01:49,729 - INFO - Response - Page 13
2025-05-17 08:01:49,729 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:01:50,229 - INFO - Request Parameters - Page 14:
2025-05-17 08:01:50,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:50,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:50,941 - INFO - API请求耗时: 711ms
2025-05-17 08:01:50,942 - INFO - Response - Page 14
2025-05-17 08:01:50,943 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:01:51,444 - INFO - Request Parameters - Page 15:
2025-05-17 08:01:51,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:51,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:52,097 - INFO - API请求耗时: 651ms
2025-05-17 08:01:52,097 - INFO - Response - Page 15
2025-05-17 08:01:52,098 - INFO - 第 15 页获取到 100 条记录
2025-05-17 08:01:52,598 - INFO - Request Parameters - Page 16:
2025-05-17 08:01:52,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:52,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743350400485, 1743868800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:53,100 - INFO - API请求耗时: 501ms
2025-05-17 08:01:53,101 - INFO - Response - Page 16
2025-05-17 08:01:53,101 - INFO - 第 16 页获取到 11 条记录
2025-05-17 08:01:53,101 - INFO - 查询完成，共获取到 1511 条记录
2025-05-17 08:01:53,102 - INFO - 分段 3 查询成功，获取到 1511 条记录
2025-05-17 08:01:54,102 - INFO - 查询分段 4: 2025-04-07 至 2025-04-13
2025-05-17 08:01:54,102 - INFO - 查询日期范围: 2025-04-07 至 2025-04-13，使用分页查询，每页 100 条记录
2025-05-17 08:01:54,103 - INFO - Request Parameters - Page 1:
2025-05-17 08:01:54,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:54,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:54,811 - INFO - API请求耗时: 706ms
2025-05-17 08:01:54,811 - INFO - Response - Page 1
2025-05-17 08:01:54,811 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:01:55,311 - INFO - Request Parameters - Page 2:
2025-05-17 08:01:55,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:55,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:55,928 - INFO - API请求耗时: 615ms
2025-05-17 08:01:55,928 - INFO - Response - Page 2
2025-05-17 08:01:55,929 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:01:56,430 - INFO - Request Parameters - Page 3:
2025-05-17 08:01:56,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:56,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:57,115 - INFO - API请求耗时: 685ms
2025-05-17 08:01:57,115 - INFO - Response - Page 3
2025-05-17 08:01:57,116 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:01:57,617 - INFO - Request Parameters - Page 4:
2025-05-17 08:01:57,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:57,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:58,263 - INFO - API请求耗时: 645ms
2025-05-17 08:01:58,263 - INFO - Response - Page 4
2025-05-17 08:01:58,264 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:01:58,765 - INFO - Request Parameters - Page 5:
2025-05-17 08:01:58,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:58,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:01:59,484 - INFO - API请求耗时: 719ms
2025-05-17 08:01:59,485 - INFO - Response - Page 5
2025-05-17 08:01:59,485 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:01:59,986 - INFO - Request Parameters - Page 6:
2025-05-17 08:01:59,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:01:59,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:00,614 - INFO - API请求耗时: 627ms
2025-05-17 08:02:00,614 - INFO - Response - Page 6
2025-05-17 08:02:00,615 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:02:01,116 - INFO - Request Parameters - Page 7:
2025-05-17 08:02:01,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:01,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:01,697 - INFO - API请求耗时: 580ms
2025-05-17 08:02:01,698 - INFO - Response - Page 7
2025-05-17 08:02:01,698 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:02:02,199 - INFO - Request Parameters - Page 8:
2025-05-17 08:02:02,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:02,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:02,818 - INFO - API请求耗时: 618ms
2025-05-17 08:02:02,818 - INFO - Response - Page 8
2025-05-17 08:02:02,819 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:02:03,320 - INFO - Request Parameters - Page 9:
2025-05-17 08:02:03,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:03,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:03,957 - INFO - API请求耗时: 636ms
2025-05-17 08:02:03,957 - INFO - Response - Page 9
2025-05-17 08:02:03,958 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:02:04,459 - INFO - Request Parameters - Page 10:
2025-05-17 08:02:04,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:04,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:05,340 - INFO - API请求耗时: 880ms
2025-05-17 08:02:05,340 - INFO - Response - Page 10
2025-05-17 08:02:05,341 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:02:05,842 - INFO - Request Parameters - Page 11:
2025-05-17 08:02:05,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:05,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:06,525 - INFO - API请求耗时: 683ms
2025-05-17 08:02:06,526 - INFO - Response - Page 11
2025-05-17 08:02:06,526 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:02:07,027 - INFO - Request Parameters - Page 12:
2025-05-17 08:02:07,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:07,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:07,593 - INFO - API请求耗时: 564ms
2025-05-17 08:02:07,594 - INFO - Response - Page 12
2025-05-17 08:02:07,594 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:02:08,095 - INFO - Request Parameters - Page 13:
2025-05-17 08:02:08,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:08,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:08,759 - INFO - API请求耗时: 663ms
2025-05-17 08:02:08,760 - INFO - Response - Page 13
2025-05-17 08:02:08,760 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:02:09,260 - INFO - Request Parameters - Page 14:
2025-05-17 08:02:09,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:09,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:09,858 - INFO - API请求耗时: 598ms
2025-05-17 08:02:09,859 - INFO - Response - Page 14
2025-05-17 08:02:09,859 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:02:10,360 - INFO - Request Parameters - Page 15:
2025-05-17 08:02:10,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:10,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:11,044 - INFO - API请求耗时: 683ms
2025-05-17 08:02:11,045 - INFO - Response - Page 15
2025-05-17 08:02:11,045 - INFO - 第 15 页获取到 100 条记录
2025-05-17 08:02:11,546 - INFO - Request Parameters - Page 16:
2025-05-17 08:02:11,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:11,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200485, 1744473600485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:11,872 - INFO - API请求耗时: 325ms
2025-05-17 08:02:11,873 - INFO - Response - Page 16
2025-05-17 08:02:11,873 - INFO - 第 16 页获取到 6 条记录
2025-05-17 08:02:11,873 - INFO - 查询完成，共获取到 1506 条记录
2025-05-17 08:02:11,874 - INFO - 分段 4 查询成功，获取到 1506 条记录
2025-05-17 08:02:12,875 - INFO - 查询分段 5: 2025-04-14 至 2025-04-20
2025-05-17 08:02:12,875 - INFO - 查询日期范围: 2025-04-14 至 2025-04-20，使用分页查询，每页 100 条记录
2025-05-17 08:02:12,876 - INFO - Request Parameters - Page 1:
2025-05-17 08:02:12,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:12,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:13,512 - INFO - API请求耗时: 636ms
2025-05-17 08:02:13,512 - INFO - Response - Page 1
2025-05-17 08:02:13,513 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:02:14,013 - INFO - Request Parameters - Page 2:
2025-05-17 08:02:14,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:14,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:14,606 - INFO - API请求耗时: 593ms
2025-05-17 08:02:14,607 - INFO - Response - Page 2
2025-05-17 08:02:14,608 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:02:15,109 - INFO - Request Parameters - Page 3:
2025-05-17 08:02:15,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:15,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:15,733 - INFO - API请求耗时: 623ms
2025-05-17 08:02:15,733 - INFO - Response - Page 3
2025-05-17 08:02:15,734 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:02:16,235 - INFO - Request Parameters - Page 4:
2025-05-17 08:02:16,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:16,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:16,889 - INFO - API请求耗时: 653ms
2025-05-17 08:02:16,890 - INFO - Response - Page 4
2025-05-17 08:02:16,890 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:02:17,392 - INFO - Request Parameters - Page 5:
2025-05-17 08:02:17,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:17,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:18,180 - INFO - API请求耗时: 787ms
2025-05-17 08:02:18,180 - INFO - Response - Page 5
2025-05-17 08:02:18,181 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:02:18,682 - INFO - Request Parameters - Page 6:
2025-05-17 08:02:18,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:18,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:19,256 - INFO - API请求耗时: 571ms
2025-05-17 08:02:19,256 - INFO - Response - Page 6
2025-05-17 08:02:19,257 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:02:19,758 - INFO - Request Parameters - Page 7:
2025-05-17 08:02:19,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:19,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:20,406 - INFO - API请求耗时: 647ms
2025-05-17 08:02:20,407 - INFO - Response - Page 7
2025-05-17 08:02:20,407 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:02:20,907 - INFO - Request Parameters - Page 8:
2025-05-17 08:02:20,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:20,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:21,734 - INFO - API请求耗时: 826ms
2025-05-17 08:02:21,735 - INFO - Response - Page 8
2025-05-17 08:02:21,735 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:02:22,236 - INFO - Request Parameters - Page 9:
2025-05-17 08:02:22,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:22,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:22,866 - INFO - API请求耗时: 629ms
2025-05-17 08:02:22,867 - INFO - Response - Page 9
2025-05-17 08:02:22,867 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:02:23,368 - INFO - Request Parameters - Page 10:
2025-05-17 08:02:23,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:23,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:24,063 - INFO - API请求耗时: 694ms
2025-05-17 08:02:24,064 - INFO - Response - Page 10
2025-05-17 08:02:24,064 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:02:24,565 - INFO - Request Parameters - Page 11:
2025-05-17 08:02:24,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:24,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:25,150 - INFO - API请求耗时: 584ms
2025-05-17 08:02:25,151 - INFO - Response - Page 11
2025-05-17 08:02:25,151 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:02:25,653 - INFO - Request Parameters - Page 12:
2025-05-17 08:02:25,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:25,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:26,282 - INFO - API请求耗时: 628ms
2025-05-17 08:02:26,283 - INFO - Response - Page 12
2025-05-17 08:02:26,284 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:02:26,785 - INFO - Request Parameters - Page 13:
2025-05-17 08:02:26,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:26,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:27,372 - INFO - API请求耗时: 586ms
2025-05-17 08:02:27,372 - INFO - Response - Page 13
2025-05-17 08:02:27,373 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:02:27,874 - INFO - Request Parameters - Page 14:
2025-05-17 08:02:27,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:27,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:28,482 - INFO - API请求耗时: 606ms
2025-05-17 08:02:28,482 - INFO - Response - Page 14
2025-05-17 08:02:28,482 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:02:28,984 - INFO - Request Parameters - Page 15:
2025-05-17 08:02:28,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:28,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:29,580 - INFO - API请求耗时: 595ms
2025-05-17 08:02:29,580 - INFO - Response - Page 15
2025-05-17 08:02:29,581 - INFO - 第 15 页获取到 100 条记录
2025-05-17 08:02:30,082 - INFO - Request Parameters - Page 16:
2025-05-17 08:02:30,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:30,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744560000485, 1745078400485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:30,427 - INFO - API请求耗时: 345ms
2025-05-17 08:02:30,427 - INFO - Response - Page 16
2025-05-17 08:02:30,428 - INFO - 第 16 页获取到 7 条记录
2025-05-17 08:02:30,428 - INFO - 查询完成，共获取到 1507 条记录
2025-05-17 08:02:30,428 - INFO - 分段 5 查询成功，获取到 1507 条记录
2025-05-17 08:02:31,429 - INFO - 查询分段 6: 2025-04-21 至 2025-04-27
2025-05-17 08:02:31,429 - INFO - 查询日期范围: 2025-04-21 至 2025-04-27，使用分页查询，每页 100 条记录
2025-05-17 08:02:31,430 - INFO - Request Parameters - Page 1:
2025-05-17 08:02:31,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:31,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:32,120 - INFO - API请求耗时: 688ms
2025-05-17 08:02:32,121 - INFO - Response - Page 1
2025-05-17 08:02:32,121 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:02:32,621 - INFO - Request Parameters - Page 2:
2025-05-17 08:02:32,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:32,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:33,286 - INFO - API请求耗时: 665ms
2025-05-17 08:02:33,287 - INFO - Response - Page 2
2025-05-17 08:02:33,287 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:02:33,788 - INFO - Request Parameters - Page 3:
2025-05-17 08:02:33,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:33,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:34,398 - INFO - API请求耗时: 609ms
2025-05-17 08:02:34,399 - INFO - Response - Page 3
2025-05-17 08:02:34,399 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:02:34,900 - INFO - Request Parameters - Page 4:
2025-05-17 08:02:34,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:34,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:35,565 - INFO - API请求耗时: 664ms
2025-05-17 08:02:35,566 - INFO - Response - Page 4
2025-05-17 08:02:35,566 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:02:36,067 - INFO - Request Parameters - Page 5:
2025-05-17 08:02:36,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:36,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:36,637 - INFO - API请求耗时: 569ms
2025-05-17 08:02:36,637 - INFO - Response - Page 5
2025-05-17 08:02:36,638 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:02:37,138 - INFO - Request Parameters - Page 6:
2025-05-17 08:02:37,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:37,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:37,722 - INFO - API请求耗时: 582ms
2025-05-17 08:02:37,722 - INFO - Response - Page 6
2025-05-17 08:02:37,723 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:02:38,224 - INFO - Request Parameters - Page 7:
2025-05-17 08:02:38,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:38,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:39,043 - INFO - API请求耗时: 817ms
2025-05-17 08:02:39,043 - INFO - Response - Page 7
2025-05-17 08:02:39,044 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:02:39,544 - INFO - Request Parameters - Page 8:
2025-05-17 08:02:39,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:39,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:40,172 - INFO - API请求耗时: 627ms
2025-05-17 08:02:40,173 - INFO - Response - Page 8
2025-05-17 08:02:40,173 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:02:40,673 - INFO - Request Parameters - Page 9:
2025-05-17 08:02:40,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:40,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:41,290 - INFO - API请求耗时: 616ms
2025-05-17 08:02:41,290 - INFO - Response - Page 9
2025-05-17 08:02:41,291 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:02:41,792 - INFO - Request Parameters - Page 10:
2025-05-17 08:02:41,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:41,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:42,595 - INFO - API请求耗时: 803ms
2025-05-17 08:02:42,595 - INFO - Response - Page 10
2025-05-17 08:02:42,596 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:02:43,096 - INFO - Request Parameters - Page 11:
2025-05-17 08:02:43,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:43,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:43,787 - INFO - API请求耗时: 689ms
2025-05-17 08:02:43,788 - INFO - Response - Page 11
2025-05-17 08:02:43,788 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:02:44,290 - INFO - Request Parameters - Page 12:
2025-05-17 08:02:44,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:44,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:44,939 - INFO - API请求耗时: 648ms
2025-05-17 08:02:44,940 - INFO - Response - Page 12
2025-05-17 08:02:44,941 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:02:45,442 - INFO - Request Parameters - Page 13:
2025-05-17 08:02:45,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:45,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:46,055 - INFO - API请求耗时: 612ms
2025-05-17 08:02:46,056 - INFO - Response - Page 13
2025-05-17 08:02:46,056 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:02:46,558 - INFO - Request Parameters - Page 14:
2025-05-17 08:02:46,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:46,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:47,153 - INFO - API请求耗时: 594ms
2025-05-17 08:02:47,154 - INFO - Response - Page 14
2025-05-17 08:02:47,154 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:02:47,655 - INFO - Request Parameters - Page 15:
2025-05-17 08:02:47,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:47,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800485, 1745683200485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:48,302 - INFO - API请求耗时: 647ms
2025-05-17 08:02:48,303 - INFO - Response - Page 15
2025-05-17 08:02:48,303 - INFO - 第 15 页获取到 85 条记录
2025-05-17 08:02:48,304 - INFO - 查询完成，共获取到 1485 条记录
2025-05-17 08:02:48,304 - INFO - 分段 6 查询成功，获取到 1485 条记录
2025-05-17 08:02:49,305 - INFO - 查询分段 7: 2025-04-28 至 2025-05-04
2025-05-17 08:02:49,305 - INFO - 查询日期范围: 2025-04-28 至 2025-05-04，使用分页查询，每页 100 条记录
2025-05-17 08:02:49,306 - INFO - Request Parameters - Page 1:
2025-05-17 08:02:49,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:49,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:50,017 - INFO - API请求耗时: 711ms
2025-05-17 08:02:50,017 - INFO - Response - Page 1
2025-05-17 08:02:50,018 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:02:50,519 - INFO - Request Parameters - Page 2:
2025-05-17 08:02:50,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:50,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:51,160 - INFO - API请求耗时: 640ms
2025-05-17 08:02:51,160 - INFO - Response - Page 2
2025-05-17 08:02:51,161 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:02:51,662 - INFO - Request Parameters - Page 3:
2025-05-17 08:02:51,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:51,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:52,313 - INFO - API请求耗时: 650ms
2025-05-17 08:02:52,313 - INFO - Response - Page 3
2025-05-17 08:02:52,314 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:02:52,814 - INFO - Request Parameters - Page 4:
2025-05-17 08:02:52,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:52,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:53,476 - INFO - API请求耗时: 661ms
2025-05-17 08:02:53,476 - INFO - Response - Page 4
2025-05-17 08:02:53,477 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:02:53,978 - INFO - Request Parameters - Page 5:
2025-05-17 08:02:53,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:53,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:54,629 - INFO - API请求耗时: 650ms
2025-05-17 08:02:54,629 - INFO - Response - Page 5
2025-05-17 08:02:54,630 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:02:55,131 - INFO - Request Parameters - Page 6:
2025-05-17 08:02:55,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:55,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:55,755 - INFO - API请求耗时: 623ms
2025-05-17 08:02:55,755 - INFO - Response - Page 6
2025-05-17 08:02:55,756 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:02:56,257 - INFO - Request Parameters - Page 7:
2025-05-17 08:02:56,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:56,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:56,859 - INFO - API请求耗时: 601ms
2025-05-17 08:02:56,859 - INFO - Response - Page 7
2025-05-17 08:02:56,860 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:02:57,361 - INFO - Request Parameters - Page 8:
2025-05-17 08:02:57,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:57,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:57,987 - INFO - API请求耗时: 625ms
2025-05-17 08:02:57,987 - INFO - Response - Page 8
2025-05-17 08:02:57,988 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:02:58,489 - INFO - Request Parameters - Page 9:
2025-05-17 08:02:58,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:58,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:02:59,083 - INFO - API请求耗时: 592ms
2025-05-17 08:02:59,083 - INFO - Response - Page 9
2025-05-17 08:02:59,084 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:02:59,585 - INFO - Request Parameters - Page 10:
2025-05-17 08:02:59,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:02:59,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:00,436 - INFO - API请求耗时: 851ms
2025-05-17 08:03:00,437 - INFO - Response - Page 10
2025-05-17 08:03:00,438 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:03:00,939 - INFO - Request Parameters - Page 11:
2025-05-17 08:03:00,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:00,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:01,700 - INFO - API请求耗时: 760ms
2025-05-17 08:03:01,700 - INFO - Response - Page 11
2025-05-17 08:03:01,701 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:03:02,202 - INFO - Request Parameters - Page 12:
2025-05-17 08:03:02,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:02,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:02,834 - INFO - API请求耗时: 631ms
2025-05-17 08:03:02,834 - INFO - Response - Page 12
2025-05-17 08:03:02,834 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:03:03,336 - INFO - Request Parameters - Page 13:
2025-05-17 08:03:03,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:03,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:03,948 - INFO - API请求耗时: 610ms
2025-05-17 08:03:03,948 - INFO - Response - Page 13
2025-05-17 08:03:03,949 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:03:04,450 - INFO - Request Parameters - Page 14:
2025-05-17 08:03:04,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:04,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:05,044 - INFO - API请求耗时: 594ms
2025-05-17 08:03:05,044 - INFO - Response - Page 14
2025-05-17 08:03:05,045 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:03:05,546 - INFO - Request Parameters - Page 15:
2025-05-17 08:03:05,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:05,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:06,142 - INFO - API请求耗时: 595ms
2025-05-17 08:03:06,142 - INFO - Response - Page 15
2025-05-17 08:03:06,143 - INFO - 第 15 页获取到 100 条记录
2025-05-17 08:03:06,643 - INFO - Request Parameters - Page 16:
2025-05-17 08:03:06,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:06,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:07,247 - INFO - API请求耗时: 603ms
2025-05-17 08:03:07,247 - INFO - Response - Page 16
2025-05-17 08:03:07,248 - INFO - 第 16 页获取到 100 条记录
2025-05-17 08:03:07,748 - INFO - Request Parameters - Page 17:
2025-05-17 08:03:07,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:07,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:08,391 - INFO - API请求耗时: 643ms
2025-05-17 08:03:08,392 - INFO - Response - Page 17
2025-05-17 08:03:08,392 - INFO - 第 17 页获取到 100 条记录
2025-05-17 08:03:08,895 - INFO - Request Parameters - Page 18:
2025-05-17 08:03:08,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:08,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:09,506 - INFO - API请求耗时: 610ms
2025-05-17 08:03:09,506 - INFO - Response - Page 18
2025-05-17 08:03:09,507 - INFO - 第 18 页获取到 100 条记录
2025-05-17 08:03:10,008 - INFO - Request Parameters - Page 19:
2025-05-17 08:03:10,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:10,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:10,588 - INFO - API请求耗时: 579ms
2025-05-17 08:03:10,588 - INFO - Response - Page 19
2025-05-17 08:03:10,589 - INFO - 第 19 页获取到 100 条记录
2025-05-17 08:03:11,089 - INFO - Request Parameters - Page 20:
2025-05-17 08:03:11,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:11,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:11,697 - INFO - API请求耗时: 606ms
2025-05-17 08:03:11,698 - INFO - Response - Page 20
2025-05-17 08:03:11,698 - INFO - 第 20 页获取到 100 条记录
2025-05-17 08:03:12,200 - INFO - Request Parameters - Page 21:
2025-05-17 08:03:12,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:12,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:12,820 - INFO - API请求耗时: 619ms
2025-05-17 08:03:12,821 - INFO - Response - Page 21
2025-05-17 08:03:12,822 - INFO - 第 21 页获取到 100 条记录
2025-05-17 08:03:13,322 - INFO - Request Parameters - Page 22:
2025-05-17 08:03:13,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:13,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745769600485, 1746288000485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:13,705 - INFO - API请求耗时: 382ms
2025-05-17 08:03:13,705 - INFO - Response - Page 22
2025-05-17 08:03:13,706 - INFO - 第 22 页获取到 9 条记录
2025-05-17 08:03:13,706 - INFO - 查询完成，共获取到 2109 条记录
2025-05-17 08:03:13,706 - INFO - 分段 7 查询成功，获取到 2109 条记录
2025-05-17 08:03:14,706 - INFO - 查询分段 8: 2025-05-05 至 2025-05-11
2025-05-17 08:03:14,706 - INFO - 查询日期范围: 2025-05-05 至 2025-05-11，使用分页查询，每页 100 条记录
2025-05-17 08:03:14,707 - INFO - Request Parameters - Page 1:
2025-05-17 08:03:14,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:14,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:15,391 - INFO - API请求耗时: 684ms
2025-05-17 08:03:15,391 - INFO - Response - Page 1
2025-05-17 08:03:15,392 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:03:15,893 - INFO - Request Parameters - Page 2:
2025-05-17 08:03:15,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:15,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:16,506 - INFO - API请求耗时: 612ms
2025-05-17 08:03:16,506 - INFO - Response - Page 2
2025-05-17 08:03:16,507 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:03:17,008 - INFO - Request Parameters - Page 3:
2025-05-17 08:03:17,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:17,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:17,606 - INFO - API请求耗时: 597ms
2025-05-17 08:03:17,607 - INFO - Response - Page 3
2025-05-17 08:03:17,607 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:03:18,109 - INFO - Request Parameters - Page 4:
2025-05-17 08:03:18,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:18,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:18,749 - INFO - API请求耗时: 640ms
2025-05-17 08:03:18,750 - INFO - Response - Page 4
2025-05-17 08:03:18,750 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:03:19,252 - INFO - Request Parameters - Page 5:
2025-05-17 08:03:19,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:19,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:20,064 - INFO - API请求耗时: 811ms
2025-05-17 08:03:20,065 - INFO - Response - Page 5
2025-05-17 08:03:20,065 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:03:20,565 - INFO - Request Parameters - Page 6:
2025-05-17 08:03:20,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:20,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:21,242 - INFO - API请求耗时: 676ms
2025-05-17 08:03:21,242 - INFO - Response - Page 6
2025-05-17 08:03:21,243 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:03:21,744 - INFO - Request Parameters - Page 7:
2025-05-17 08:03:21,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:21,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:22,418 - INFO - API请求耗时: 674ms
2025-05-17 08:03:22,418 - INFO - Response - Page 7
2025-05-17 08:03:22,419 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:03:22,920 - INFO - Request Parameters - Page 8:
2025-05-17 08:03:22,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:22,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:23,618 - INFO - API请求耗时: 696ms
2025-05-17 08:03:23,618 - INFO - Response - Page 8
2025-05-17 08:03:23,619 - INFO - 第 8 页获取到 100 条记录
2025-05-17 08:03:24,120 - INFO - Request Parameters - Page 9:
2025-05-17 08:03:24,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:24,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:24,748 - INFO - API请求耗时: 628ms
2025-05-17 08:03:24,748 - INFO - Response - Page 9
2025-05-17 08:03:24,749 - INFO - 第 9 页获取到 100 条记录
2025-05-17 08:03:25,250 - INFO - Request Parameters - Page 10:
2025-05-17 08:03:25,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:25,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:26,152 - INFO - API请求耗时: 900ms
2025-05-17 08:03:26,153 - INFO - Response - Page 10
2025-05-17 08:03:26,153 - INFO - 第 10 页获取到 100 条记录
2025-05-17 08:03:26,653 - INFO - Request Parameters - Page 11:
2025-05-17 08:03:26,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:26,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:27,305 - INFO - API请求耗时: 651ms
2025-05-17 08:03:27,306 - INFO - Response - Page 11
2025-05-17 08:03:27,306 - INFO - 第 11 页获取到 100 条记录
2025-05-17 08:03:27,807 - INFO - Request Parameters - Page 12:
2025-05-17 08:03:27,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:27,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:28,409 - INFO - API请求耗时: 601ms
2025-05-17 08:03:28,409 - INFO - Response - Page 12
2025-05-17 08:03:28,410 - INFO - 第 12 页获取到 100 条记录
2025-05-17 08:03:28,910 - INFO - Request Parameters - Page 13:
2025-05-17 08:03:28,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:28,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:29,520 - INFO - API请求耗时: 608ms
2025-05-17 08:03:29,521 - INFO - Response - Page 13
2025-05-17 08:03:29,521 - INFO - 第 13 页获取到 100 条记录
2025-05-17 08:03:30,022 - INFO - Request Parameters - Page 14:
2025-05-17 08:03:30,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:30,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:30,660 - INFO - API请求耗时: 636ms
2025-05-17 08:03:30,660 - INFO - Response - Page 14
2025-05-17 08:03:30,660 - INFO - 第 14 页获取到 100 条记录
2025-05-17 08:03:31,160 - INFO - Request Parameters - Page 15:
2025-05-17 08:03:31,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:31,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400485, 1746892800485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:31,743 - INFO - API请求耗时: 581ms
2025-05-17 08:03:31,743 - INFO - Response - Page 15
2025-05-17 08:03:31,744 - INFO - 第 15 页获取到 80 条记录
2025-05-17 08:03:31,744 - INFO - 查询完成，共获取到 1480 条记录
2025-05-17 08:03:31,744 - INFO - 分段 8 查询成功，获取到 1480 条记录
2025-05-17 08:03:32,745 - INFO - 查询分段 9: 2025-05-12 至 2025-05-16
2025-05-17 08:03:32,745 - INFO - 查询日期范围: 2025-05-12 至 2025-05-16，使用分页查询，每页 100 条记录
2025-05-17 08:03:32,746 - INFO - Request Parameters - Page 1:
2025-05-17 08:03:32,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:32,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:33,399 - INFO - API请求耗时: 652ms
2025-05-17 08:03:33,400 - INFO - Response - Page 1
2025-05-17 08:03:33,400 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:03:33,900 - INFO - Request Parameters - Page 2:
2025-05-17 08:03:33,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:33,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:34,506 - INFO - API请求耗时: 605ms
2025-05-17 08:03:34,506 - INFO - Response - Page 2
2025-05-17 08:03:34,507 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:03:35,007 - INFO - Request Parameters - Page 3:
2025-05-17 08:03:35,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:35,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:35,590 - INFO - API请求耗时: 582ms
2025-05-17 08:03:35,590 - INFO - Response - Page 3
2025-05-17 08:03:35,591 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:03:36,092 - INFO - Request Parameters - Page 4:
2025-05-17 08:03:36,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:36,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:36,741 - INFO - API请求耗时: 648ms
2025-05-17 08:03:36,741 - INFO - Response - Page 4
2025-05-17 08:03:36,742 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:03:37,242 - INFO - Request Parameters - Page 5:
2025-05-17 08:03:37,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:37,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:37,869 - INFO - API请求耗时: 626ms
2025-05-17 08:03:37,870 - INFO - Response - Page 5
2025-05-17 08:03:37,870 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:03:38,372 - INFO - Request Parameters - Page 6:
2025-05-17 08:03:38,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:38,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:39,076 - INFO - API请求耗时: 703ms
2025-05-17 08:03:39,076 - INFO - Response - Page 6
2025-05-17 08:03:39,077 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:03:39,578 - INFO - Request Parameters - Page 7:
2025-05-17 08:03:39,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:03:39,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746979200485, 1747411199485], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:03:39,959 - INFO - API请求耗时: 380ms
2025-05-17 08:03:39,959 - INFO - Response - Page 7
2025-05-17 08:03:39,960 - INFO - 第 7 页获取到 15 条记录
2025-05-17 08:03:39,960 - INFO - 查询完成，共获取到 615 条记录
2025-05-17 08:03:39,960 - INFO - 分段 9 查询成功，获取到 615 条记录
2025-05-17 08:03:40,961 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 13115 条记录，失败 0 次
2025-05-17 08:03:40,961 - INFO - 成功获取宜搭日销售表单数据，共 13115 条记录
2025-05-17 08:03:40,962 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-17 08:03:40,962 - INFO - 开始对比和同步日销售数据...
2025-05-17 08:03:41,338 - INFO - 成功创建宜搭日销售数据索引，共 10974 条记录
2025-05-17 08:03:41,338 - INFO - 开始处理数衍数据，共 13133 条记录
2025-05-17 08:03:41,855 - INFO - 更新表单数据成功: FINST-2K666OB1WWFV7II0CY5SZ393LW9V3YY859PAMV3
2025-05-17 08:03:41,855 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_********, 变更字段: [{'field': 'amount', 'old_value': 301.0, 'new_value': 1167.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 301.0, 'new_value': 1167.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-05-17 08:03:42,405 - INFO - 更新表单数据成功: FINST-Z7B66WA1531VFXZIEUORV8DDLYE02RWE5W8AM1H
2025-05-17 08:03:42,406 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9626.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9626.0}]
2025-05-17 08:03:42,944 - INFO - 更新表单数据成功: FINST-Z7B66WA1531VFXZIEUORV8DDLYE02SWE5W8AMQH
2025-05-17 08:03:42,945 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250501, 变更字段: [{'field': 'amount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 1419.5, 'new_value': 1581.5}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-05-17 08:03:43,376 - INFO - 更新表单数据成功: FINST-7PF66H71ABGVUW3ICWTAHDCROW72352Z79PAMC2
2025-05-17 08:03:43,376 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_20250503, 变更字段: [{'field': 'amount', 'old_value': -16981.14, 'new_value': -17209.14}]
2025-05-17 08:03:43,783 - INFO - 更新表单数据成功: FINST-RI766091000V5POQB9TRQ4O09BXB25PH5W8AMSF1
2025-05-17 08:03:43,783 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_20250501, 变更字段: [{'field': 'amount', 'old_value': 6249.22, 'new_value': 6358.820000000001}, {'field': 'count', 'old_value': 167, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 6246.12, 'new_value': 6355.72}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 163}]
2025-05-17 08:03:44,227 - INFO - 更新表单数据成功: FINST-VOC66Y91G21VRAO3BDVH58NY3EWM24EK5W8AMBL
2025-05-17 08:03:44,227 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 12376.85, 'new_value': 12478.25}, {'field': 'amount', 'old_value': 12376.85, 'new_value': 12478.25}, {'field': 'count', 'old_value': 198, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 11714.91, 'new_value': 11816.31}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 188}]
2025-05-17 08:03:44,660 - INFO - 更新表单数据成功: FINST-VOC66Y91G21VRAO3BDVH58NY3EWM24EK5W8AMGL
2025-05-17 08:03:44,660 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9242.69}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9242.69}]
2025-05-17 08:03:45,053 - INFO - 更新表单数据成功: FINST-VOC66Y91G21VRAO3BDVH58NY3EWM24EK5W8AMHM
2025-05-17 08:03:45,053 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 6532.11, 'new_value': 6544.51}, {'field': 'amount', 'old_value': 6532.110000000001, 'new_value': 6544.51}, {'field': 'count', 'old_value': 258, 'new_value': 259}, {'field': 'onlineAmount', 'old_value': 4291.19, 'new_value': 4303.59}, {'field': 'onlineCount', 'old_value': 160, 'new_value': 161}]
2025-05-17 08:03:45,524 - INFO - 更新表单数据成功: FINST-KLF66RD1Y5ZURETCC3I535JWYEFU3QLS5W8AMFE
2025-05-17 08:03:45,525 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250501, 变更字段: [{'field': 'amount', 'old_value': 52072.530000000006, 'new_value': 52131.93}, {'field': 'count', 'old_value': 1166, 'new_value': 1168}, {'field': 'instoreAmount', 'old_value': 49767.94, 'new_value': 49827.34}, {'field': 'instoreCount', 'old_value': 1121, 'new_value': 1123}]
2025-05-17 08:03:45,980 - INFO - 更新表单数据成功: FINST-KLF66RD1Y5ZURETCC3I535JWYEFU3QLS5W8AMKE
2025-05-17 08:03:45,981 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 21282.2, 'new_value': 22246.2}, {'field': 'amount', 'old_value': 21282.199999999997, 'new_value': 22246.199999999997}, {'field': 'count', 'old_value': 82, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 21511.1, 'new_value': 22475.1}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 84}]
2025-05-17 08:03:46,412 - INFO - 更新表单数据成功: FINST-E3G66QA1VO1V3ZSQ7U24556VVM5D3VZX5W8AM1W
2025-05-17 08:03:46,413 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_20250501, 变更字段: [{'field': 'count', 'old_value': 52, 'new_value': 64}, {'field': 'instoreCount', 'old_value': 40, 'new_value': 52}]
2025-05-17 08:03:46,830 - INFO - 更新表单数据成功: FINST-E3G66QA1VO1V3ZSQ7U24556VVM5D3VZX5W8AMBW
2025-05-17 08:03:46,831 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 1630.55, 'new_value': 1718.55}, {'field': 'dailyBillAmount', 'old_value': 1630.55, 'new_value': 1718.55}]
2025-05-17 08:03:47,374 - INFO - 更新表单数据成功: FINST-LFA66G91HBGVRHCZ5R1A79KXRZ9U191899PAMB3
2025-05-17 08:03:47,375 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1881.0, 'new_value': 2320.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2320.0}]
2025-05-17 08:03:47,832 - INFO - 更新表单数据成功: FINST-X8D66N81GZFVKKRM94YJOD14IFW03QMH89PAML5
2025-05-17 08:03:47,833 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_20250506, 变更字段: [{'field': 'recommendAmount', 'old_value': 3868.0, 'new_value': 4128.0}, {'field': 'amount', 'old_value': 3868.0, 'new_value': 4128.0}, {'field': 'count', 'old_value': 18, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 3868.0, 'new_value': 4128.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 19}]
2025-05-17 08:03:48,230 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAMC2
2025-05-17 08:03:48,230 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_20250510, 变更字段: [{'field': 'amount', 'old_value': 14081.1, 'new_value': 14296.1}, {'field': 'count', 'old_value': 111, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 6822.3, 'new_value': 7037.3}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 42}]
2025-05-17 08:03:48,731 - INFO - 更新表单数据成功: FINST-1PF66VA1DGGVN0LJ7OOIG7NR3ITF2LW7D1QAMTC
2025-05-17 08:03:48,732 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4903.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4903.9}]
2025-05-17 08:03:49,127 - INFO - 更新表单数据成功: FINST-1PF66VA1DGGVN0LJ7OOIG7NR3ITF2LW7D1QAMOE
2025-05-17 08:03:49,127 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_20250515, 变更字段: [{'field': 'amount', 'old_value': 3276.67, 'new_value': 3272.67}, {'field': 'onlineAmount', 'old_value': 1649.75, 'new_value': 1645.75}]
2025-05-17 08:03:49,604 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAMW
2025-05-17 08:03:49,605 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_20250515, 变更字段: [{'field': 'amount', 'old_value': 753.82, 'new_value': 753.83}, {'field': 'count', 'old_value': 39, 'new_value': 40}, {'field': 'onlineAmount', 'old_value': 480.2, 'new_value': 480.21}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 25}]
2025-05-17 08:03:50,078 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAMF1
2025-05-17 08:03:50,079 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5414.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5414.0}]
2025-05-17 08:03:50,492 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAMV1
2025-05-17 08:03:50,492 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8948.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8948.8}]
2025-05-17 08:03:50,921 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAMZ1
2025-05-17 08:03:50,921 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 7372.0, 'new_value': 11163.0}, {'field': 'amount', 'old_value': 7372.0, 'new_value': 11163.0}, {'field': 'count', 'old_value': 23, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 7372.0, 'new_value': 11163.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 42}]
2025-05-17 08:03:51,459 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X21MAD1QAMV2
2025-05-17 08:03:51,460 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 2034.08, 'new_value': 2034.09}, {'field': 'amount', 'old_value': 2034.08, 'new_value': 2034.09}, {'field': 'count', 'old_value': 115, 'new_value': 116}, {'field': 'onlineAmount', 'old_value': 1316.71, 'new_value': 1316.72}, {'field': 'onlineCount', 'old_value': 81, 'new_value': 82}]
2025-05-17 08:03:51,892 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X21MAD1QAMF3
2025-05-17 08:03:51,892 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 7621.4, 'new_value': 7643.3}, {'field': 'amount', 'old_value': 7621.4, 'new_value': 7643.3}, {'field': 'count', 'old_value': 122, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 7451.11, 'new_value': 7473.01}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 121}]
2025-05-17 08:03:52,304 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2RADD1QAMK5
2025-05-17 08:03:52,304 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250515, 变更字段: [{'field': 'amount', 'old_value': 5052.52, 'new_value': 5124.28}, {'field': 'count', 'old_value': 262, 'new_value': 266}, {'field': 'onlineAmount', 'old_value': 2345.71, 'new_value': 2417.47}, {'field': 'onlineCount', 'old_value': 112, 'new_value': 116}]
2025-05-17 08:03:52,718 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAM06
2025-05-17 08:03:52,718 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 5721.32, 'new_value': 5766.82}, {'field': 'amount', 'old_value': 5721.320000000001, 'new_value': 5766.820000000001}, {'field': 'count', 'old_value': 262, 'new_value': 264}, {'field': 'instoreAmount', 'old_value': 1575.96, 'new_value': 1586.96}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 71}, {'field': 'onlineAmount', 'old_value': 4389.84, 'new_value': 4424.34}, {'field': 'onlineCount', 'old_value': 192, 'new_value': 193}]
2025-05-17 08:03:53,162 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAM36
2025-05-17 08:03:53,163 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_20250515, 变更字段: [{'field': 'amount', 'old_value': 2628.5, 'new_value': 2637.18}, {'field': 'count', 'old_value': 67, 'new_value': 68}, {'field': 'instoreAmount', 'old_value': 1216.02, 'new_value': 1224.7}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 16}]
2025-05-17 08:03:53,667 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAM96
2025-05-17 08:03:53,667 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19069.28}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19069.28}]
2025-05-17 08:03:54,131 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAMR6
2025-05-17 08:03:54,132 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250515, 变更字段: [{'field': 'amount', 'old_value': 32235.94, 'new_value': 32528.940000000002}, {'field': 'count', 'old_value': 221, 'new_value': 222}, {'field': 'instoreAmount', 'old_value': 22970.74, 'new_value': 23263.74}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 103}]
2025-05-17 08:03:54,583 - INFO - 更新表单数据成功: FINST-W4G66DA1CYFVU7R6E7XYM9LI10RK3YD599PAM04
2025-05-17 08:03:54,584 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 24138.56, 'new_value': 30174.0}, {'field': 'dailyBillAmount', 'old_value': 24138.56, 'new_value': 30174.0}]
2025-05-17 08:03:54,986 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAM27
2025-05-17 08:03:54,986 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250515, 变更字段: [{'field': 'amount', 'old_value': 4668.78, 'new_value': 4654.28}]
2025-05-17 08:03:55,550 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAM77
2025-05-17 08:03:55,551 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250515, 变更字段: [{'field': 'amount', 'old_value': 12939.84, 'new_value': 12939.85}, {'field': 'count', 'old_value': 177, 'new_value': 178}, {'field': 'onlineAmount', 'old_value': 4044.5, 'new_value': 4044.51}, {'field': 'onlineCount', 'old_value': 128, 'new_value': 129}]
2025-05-17 08:03:56,004 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAM97
2025-05-17 08:03:56,013 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_20250515, 变更字段: [{'field': 'amount', 'old_value': 579.4000000000001, 'new_value': 570.4000000000001}]
2025-05-17 08:03:56,426 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAMO7
2025-05-17 08:03:56,427 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_20250515, 变更字段: [{'field': 'amount', 'old_value': 1053.09, 'new_value': 1090.01}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'onlineAmount', 'old_value': 327.42, 'new_value': 364.34}, {'field': 'onlineCount', 'old_value': 5, 'new_value': 6}]
2025-05-17 08:03:56,812 - INFO - 更新表单数据成功: FINST-GNC66E91KBGV4E2R7BB6L7DSAK1K2SADD1QAMY7
2025-05-17 08:03:56,813 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_20250515, 变更字段: [{'field': 'amount', 'old_value': 25.900000000000034, 'new_value': 102.10000000000002}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 76.2}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 1}]
2025-05-17 08:03:57,282 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMM5
2025-05-17 08:03:57,282 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_20250515, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1253.0}]
2025-05-17 08:03:57,690 - INFO - 更新表单数据成功: FINST-LFA66G91HBGVRHCZ5R1A79KXRZ9U191899PAM93
2025-05-17 08:03:57,690 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_20250513, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 398.0}]
2025-05-17 08:03:58,120 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMS5
2025-05-17 08:03:58,121 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3554.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3554.0}]
2025-05-17 08:03:58,640 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAM16
2025-05-17 08:03:58,641 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_20250515, 变更字段: [{'field': 'amount', 'old_value': 3140.5299999999997, 'new_value': 2966.5299999999997}]
2025-05-17 08:03:59,118 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAMH6
2025-05-17 08:03:59,118 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250515, 变更字段: [{'field': 'amount', 'old_value': 6647.76, 'new_value': 6732.65}, {'field': 'count', 'old_value': 397, 'new_value': 403}, {'field': 'instoreAmount', 'old_value': 5159.14, 'new_value': 5272.35}, {'field': 'instoreCount', 'old_value': 296, 'new_value': 305}, {'field': 'onlineAmount', 'old_value': 1554.75, 'new_value': 1548.43}, {'field': 'onlineCount', 'old_value': 101, 'new_value': 98}]
2025-05-17 08:03:59,602 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3TYFD1QAM07
2025-05-17 08:03:59,603 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_20250515, 变更字段: [{'field': 'amount', 'old_value': 13439.400000000001, 'new_value': 15887.2}, {'field': 'count', 'old_value': 108, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 11184.2, 'new_value': 13632.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 57}]
2025-05-17 08:04:00,105 - INFO - 更新表单数据成功: FINST-7PF66CC177GV1506DEX589OTZ8C13S9D99PAMF1
2025-05-17 08:04:00,105 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_20250514, 变更字段: [{'field': 'amount', 'old_value': 18337.4, 'new_value': 19165.4}, {'field': 'count', 'old_value': 121, 'new_value': 122}, {'field': 'instoreAmount', 'old_value': 16606.5, 'new_value': 17434.5}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 70}]
2025-05-17 08:04:00,547 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3UYFD1QAMM7
2025-05-17 08:04:00,548 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 6028.07, 'new_value': 6040.87}, {'field': 'amount', 'old_value': 6028.07, 'new_value': 6040.87}, {'field': 'count', 'old_value': 329, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 3586.62, 'new_value': 3599.42}, {'field': 'instoreCount', 'old_value': 193, 'new_value': 194}]
2025-05-17 08:04:01,002 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3UYFD1QAMS7
2025-05-17 08:04:01,002 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250515, 变更字段: [{'field': 'amount', 'old_value': 17244.82, 'new_value': 17515.719999999998}, {'field': 'count', 'old_value': 229, 'new_value': 231}, {'field': 'instoreAmount', 'old_value': 16117.37, 'new_value': 16388.27}, {'field': 'instoreCount', 'old_value': 165, 'new_value': 167}]
2025-05-17 08:04:01,543 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3UYFD1QAM28
2025-05-17 08:04:01,543 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250515, 变更字段: [{'field': 'amount', 'old_value': 28023.04, 'new_value': 29599.04}, {'field': 'count', 'old_value': 50, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 27868.24, 'new_value': 29444.24}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 50}]
2025-05-17 08:04:02,004 - INFO - 更新表单数据成功: FINST-80B66291XFGV0SLOF44C6878H6IB3UYFD1QAMA8
2025-05-17 08:04:02,004 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 43647.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 43647.7}]
2025-05-17 08:04:02,494 - INFO - 更新表单数据成功: FINST-7PF66BA1J9GVZXM8E8AW6DL1G51V34IID1QAM9D
2025-05-17 08:04:02,494 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_20250515, 变更字段: [{'field': 'amount', 'old_value': 5264.89, 'new_value': 5285.49}, {'field': 'count', 'old_value': 212, 'new_value': 214}, {'field': 'instoreAmount', 'old_value': 1589.55, 'new_value': 1610.15}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 72}]
2025-05-17 08:04:03,004 - INFO - 更新表单数据成功: FINST-7PF66BA1J9GVZXM8E8AW6DL1G51V34IID1QAMHD
2025-05-17 08:04:03,004 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_20250515, 变更字段: [{'field': 'amount', 'old_value': 1421.5500000000002, 'new_value': 1419.5500000000002}]
2025-05-17 08:04:03,075 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-17 08:04:03,488 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-17 08:04:06,491 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-17 08:04:06,866 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-17 08:04:09,870 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-17 08:04:10,263 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-17 08:04:13,266 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-17 08:04:13,655 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-17 08:04:16,658 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-17 08:04:17,046 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-17 08:04:20,050 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-17 08:04:20,452 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-17 08:04:23,455 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-17 08:04:23,893 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-17 08:04:26,895 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-17 08:04:27,340 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-17 08:04:30,344 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-17 08:04:30,775 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-17 08:04:33,778 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-17 08:04:34,216 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-17 08:04:37,219 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-17 08:04:37,657 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-17 08:04:40,660 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-17 08:04:41,100 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-17 08:04:44,103 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-17 08:04:44,523 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-17 08:04:47,526 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-17 08:04:47,952 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-17 08:04:50,955 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-17 08:04:51,512 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-17 08:04:54,514 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-17 08:04:55,054 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-17 08:04:58,057 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-17 08:04:58,454 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-17 08:05:01,457 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-17 08:05:01,968 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-17 08:05:04,972 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-17 08:05:05,448 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-17 08:05:08,451 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-17 08:05:08,976 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-17 08:05:11,979 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-17 08:05:12,411 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-17 08:05:15,413 - INFO - 正在批量插入每日数据，批次 22/22，共 59 条记录
2025-05-17 08:05:15,715 - INFO - 批量插入每日数据成功，批次 22，59 条记录
2025-05-17 08:05:18,717 - INFO - 批量插入每日数据完成: 总计 2159 条，成功 2159 条，失败 0 条
2025-05-17 08:05:18,721 - INFO - 批量插入日销售数据完成，共 2159 条记录
2025-05-17 08:05:18,721 - INFO - 日销售数据同步完成！更新: 47 条，插入: 2159 条，错误: 0 条，跳过: 10927 条
2025-05-17 08:05:18,721 - INFO - 正在获取宜搭月销售表单数据...
2025-05-17 08:05:18,722 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-17 08:05:18,722 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-17 08:05:18,722 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:18,722 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:18,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:18,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:19,524 - INFO - API请求耗时: 801ms
2025-05-17 08:05:19,524 - INFO - Response - Page 1
2025-05-17 08:05:19,525 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:19,525 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:19,525 - WARNING - 月度分段 1 查询返回空数据
2025-05-17 08:05:19,526 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-17 08:05:19,526 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:19,526 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:19,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:19,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:19,789 - INFO - API请求耗时: 263ms
2025-05-17 08:05:19,790 - INFO - Response - Page 1
2025-05-17 08:05:19,790 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:19,791 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:19,791 - WARNING - 单月查询返回空数据: 2024-05
2025-05-17 08:05:20,292 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-17 08:05:20,292 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:20,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:20,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:20,548 - INFO - API请求耗时: 256ms
2025-05-17 08:05:20,549 - INFO - Response - Page 1
2025-05-17 08:05:20,549 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:20,549 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:20,549 - WARNING - 单月查询返回空数据: 2024-06
2025-05-17 08:05:21,051 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:21,051 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:21,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:21,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:21,244 - INFO - API请求耗时: 191ms
2025-05-17 08:05:21,245 - INFO - Response - Page 1
2025-05-17 08:05:21,245 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:21,245 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:21,246 - WARNING - 单月查询返回空数据: 2024-07
2025-05-17 08:05:22,747 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-17 08:05:22,747 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:22,748 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:22,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:22,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:22,959 - INFO - API请求耗时: 211ms
2025-05-17 08:05:22,959 - INFO - Response - Page 1
2025-05-17 08:05:22,960 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:22,960 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:22,960 - WARNING - 月度分段 2 查询返回空数据
2025-05-17 08:05:22,960 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-17 08:05:22,961 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:22,961 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:22,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:22,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:23,162 - INFO - API请求耗时: 200ms
2025-05-17 08:05:23,163 - INFO - Response - Page 1
2025-05-17 08:05:23,163 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:23,163 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:23,164 - WARNING - 单月查询返回空数据: 2024-08
2025-05-17 08:05:23,664 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-17 08:05:23,664 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:23,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:23,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:23,858 - INFO - API请求耗时: 193ms
2025-05-17 08:05:23,859 - INFO - Response - Page 1
2025-05-17 08:05:23,859 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:23,859 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:23,860 - WARNING - 单月查询返回空数据: 2024-09
2025-05-17 08:05:24,361 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:24,361 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:24,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:24,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:24,582 - INFO - API请求耗时: 220ms
2025-05-17 08:05:24,582 - INFO - Response - Page 1
2025-05-17 08:05:24,583 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-17 08:05:24,583 - INFO - 查询完成，共获取到 0 条记录
2025-05-17 08:05:24,583 - WARNING - 单月查询返回空数据: 2024-10
2025-05-17 08:05:26,084 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-17 08:05:26,085 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:26,085 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:26,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:26,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:26,760 - INFO - API请求耗时: 675ms
2025-05-17 08:05:26,761 - INFO - Response - Page 1
2025-05-17 08:05:26,761 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:05:27,261 - INFO - Request Parameters - Page 2:
2025-05-17 08:05:27,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:27,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:27,847 - INFO - API请求耗时: 584ms
2025-05-17 08:05:27,848 - INFO - Response - Page 2
2025-05-17 08:05:27,848 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:05:28,349 - INFO - Request Parameters - Page 3:
2025-05-17 08:05:28,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:28,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:28,795 - INFO - API请求耗时: 444ms
2025-05-17 08:05:28,795 - INFO - Response - Page 3
2025-05-17 08:05:28,796 - INFO - 第 3 页获取到 48 条记录
2025-05-17 08:05:28,796 - INFO - 查询完成，共获取到 248 条记录
2025-05-17 08:05:28,796 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-17 08:05:29,797 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-17 08:05:29,797 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-17 08:05:29,798 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:29,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:29,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:30,372 - INFO - API请求耗时: 573ms
2025-05-17 08:05:30,373 - INFO - Response - Page 1
2025-05-17 08:05:30,374 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:05:30,875 - INFO - Request Parameters - Page 2:
2025-05-17 08:05:30,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:30,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:31,503 - INFO - API请求耗时: 628ms
2025-05-17 08:05:31,504 - INFO - Response - Page 2
2025-05-17 08:05:31,505 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:05:32,005 - INFO - Request Parameters - Page 3:
2025-05-17 08:05:32,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:32,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:32,582 - INFO - API请求耗时: 575ms
2025-05-17 08:05:32,582 - INFO - Response - Page 3
2025-05-17 08:05:32,583 - INFO - 第 3 页获取到 100 条记录
2025-05-17 08:05:33,084 - INFO - Request Parameters - Page 4:
2025-05-17 08:05:33,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:33,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:33,606 - INFO - API请求耗时: 521ms
2025-05-17 08:05:33,606 - INFO - Response - Page 4
2025-05-17 08:05:33,607 - INFO - 第 4 页获取到 100 条记录
2025-05-17 08:05:34,108 - INFO - Request Parameters - Page 5:
2025-05-17 08:05:34,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:34,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:34,666 - INFO - API请求耗时: 557ms
2025-05-17 08:05:34,667 - INFO - Response - Page 5
2025-05-17 08:05:34,668 - INFO - 第 5 页获取到 100 条记录
2025-05-17 08:05:35,168 - INFO - Request Parameters - Page 6:
2025-05-17 08:05:35,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:35,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:35,835 - INFO - API请求耗时: 666ms
2025-05-17 08:05:35,835 - INFO - Response - Page 6
2025-05-17 08:05:35,836 - INFO - 第 6 页获取到 100 条记录
2025-05-17 08:05:36,336 - INFO - Request Parameters - Page 7:
2025-05-17 08:05:36,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:36,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:36,835 - INFO - API请求耗时: 498ms
2025-05-17 08:05:36,836 - INFO - Response - Page 7
2025-05-17 08:05:36,836 - INFO - 第 7 页获取到 100 条记录
2025-05-17 08:05:37,338 - INFO - Request Parameters - Page 8:
2025-05-17 08:05:37,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:37,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:37,677 - INFO - API请求耗时: 338ms
2025-05-17 08:05:37,678 - INFO - Response - Page 8
2025-05-17 08:05:37,678 - INFO - 第 8 页获取到 16 条记录
2025-05-17 08:05:37,678 - INFO - 查询完成，共获取到 716 条记录
2025-05-17 08:05:37,679 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-17 08:05:38,680 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-17 08:05:38,680 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-17 08:05:38,681 - INFO - Request Parameters - Page 1:
2025-05-17 08:05:38,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:38,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:39,302 - INFO - API请求耗时: 621ms
2025-05-17 08:05:39,303 - INFO - Response - Page 1
2025-05-17 08:05:39,303 - INFO - 第 1 页获取到 100 条记录
2025-05-17 08:05:39,803 - INFO - Request Parameters - Page 2:
2025-05-17 08:05:39,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:39,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:40,419 - INFO - API请求耗时: 615ms
2025-05-17 08:05:40,420 - INFO - Response - Page 2
2025-05-17 08:05:40,421 - INFO - 第 2 页获取到 100 条记录
2025-05-17 08:05:40,922 - INFO - Request Parameters - Page 3:
2025-05-17 08:05:40,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-17 08:05:40,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-17 08:05:41,327 - INFO - API请求耗时: 404ms
2025-05-17 08:05:41,327 - INFO - Response - Page 3
2025-05-17 08:05:41,328 - INFO - 第 3 页获取到 24 条记录
2025-05-17 08:05:41,328 - INFO - 查询完成，共获取到 224 条记录
2025-05-17 08:05:41,328 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-17 08:05:42,329 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-17 08:05:42,329 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-17 08:05:42,330 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-17 08:05:42,330 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-17 08:05:42,339 - INFO - 成功获取SQLite月度汇总数据，共 1189 条记录
2025-05-17 08:05:42,398 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-17 08:05:42,855 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-17 08:05:42,856 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 98102.51, 'new_value': 106827.62}, {'field': 'dailyBillAmount', 'old_value': 98102.51, 'new_value': 106827.62}, {'field': 'amount', 'old_value': 2978.5, 'new_value': 3210.7}, {'field': 'count', 'old_value': 45, 'new_value': 48}, {'field': 'onlineAmount', 'old_value': 3054.5, 'new_value': 3286.7}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 48}]
2025-05-17 08:05:43,334 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-17 08:05:43,334 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 268379.08, 'new_value': 287481.47}, {'field': 'dailyBillAmount', 'old_value': 268379.08, 'new_value': 287481.47}, {'field': 'amount', 'old_value': 144283.9, 'new_value': 157265.1}, {'field': 'count', 'old_value': 1327, 'new_value': 1453}, {'field': 'instoreAmount', 'old_value': 58824.3, 'new_value': 61638.2}, {'field': 'instoreCount', 'old_value': 428, 'new_value': 449}, {'field': 'onlineAmount', 'old_value': 85751.0, 'new_value': 95918.3}, {'field': 'onlineCount', 'old_value': 899, 'new_value': 1004}]
2025-05-17 08:05:43,827 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-17 08:05:43,828 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172482.91, 'new_value': 184641.61000000002}, {'field': 'dailyBillAmount', 'old_value': 172482.91, 'new_value': 184641.61000000002}, {'field': 'amount', 'old_value': 174212.9, 'new_value': 186518.5}, {'field': 'count', 'old_value': 1170, 'new_value': 1251}, {'field': 'instoreAmount', 'old_value': 163524.6, 'new_value': 175235.9}, {'field': 'instoreCount', 'old_value': 1012, 'new_value': 1087}, {'field': 'onlineAmount', 'old_value': 10906.5, 'new_value': 11500.8}, {'field': 'onlineCount', 'old_value': 158, 'new_value': 164}]
2025-05-17 08:05:44,264 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-17 08:05:44,265 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 404886.88, 'new_value': 428892.39}, {'field': 'dailyBillAmount', 'old_value': 404886.88, 'new_value': 428892.39}, {'field': 'amount', 'old_value': 306743.0, 'new_value': 322418.65}, {'field': 'count', 'old_value': 1463, 'new_value': 1538}, {'field': 'instoreAmount', 'old_value': 306743.0, 'new_value': 322418.65}, {'field': 'instoreCount', 'old_value': 1463, 'new_value': 1538}]
2025-05-17 08:05:44,742 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-17 08:05:44,742 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 311488.14, 'new_value': 333504.84}, {'field': 'dailyBillAmount', 'old_value': 311488.14, 'new_value': 333504.84}, {'field': 'amount', 'old_value': 489017.0, 'new_value': 535946.0}, {'field': 'count', 'old_value': 1684, 'new_value': 1831}, {'field': 'instoreAmount', 'old_value': 490267.0, 'new_value': 537196.0}, {'field': 'instoreCount', 'old_value': 1684, 'new_value': 1831}]
2025-05-17 08:05:45,224 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-17 08:05:45,224 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46976.7, 'new_value': 47567.7}, {'field': 'dailyBillAmount', 'old_value': 46976.7, 'new_value': 47567.7}, {'field': 'amount', 'old_value': 61394.51, 'new_value': 62437.01}, {'field': 'count', 'old_value': 169, 'new_value': 186}, {'field': 'instoreAmount', 'old_value': 37857.1, 'new_value': 37950.7}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 33}, {'field': 'onlineAmount', 'old_value': 27175.920000000002, 'new_value': 28124.82}, {'field': 'onlineCount', 'old_value': 138, 'new_value': 153}]
2025-05-17 08:05:45,695 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-17 08:05:45,695 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151308.9, 'new_value': 155285.9}, {'field': 'amount', 'old_value': 151308.9, 'new_value': 155285.9}, {'field': 'count', 'old_value': 84, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 151308.9, 'new_value': 155285.9}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 88}]
2025-05-17 08:05:46,168 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-17 08:05:46,168 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 452627.01, 'new_value': 476528.19}, {'field': 'dailyBillAmount', 'old_value': 452627.01, 'new_value': 476528.19}, {'field': 'amount', 'old_value': 449509.16000000003, 'new_value': 470993.96}, {'field': 'count', 'old_value': 3293, 'new_value': 3434}, {'field': 'instoreAmount', 'old_value': 353575.36, 'new_value': 373343.06}, {'field': 'instoreCount', 'old_value': 1480, 'new_value': 1575}, {'field': 'onlineAmount', 'old_value': 98941.67, 'new_value': 100683.17}, {'field': 'onlineCount', 'old_value': 1813, 'new_value': 1859}]
2025-05-17 08:05:46,618 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-05-17 08:05:46,618 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 158877.23, 'new_value': 175997.54}, {'field': 'amount', 'old_value': 158871.39, 'new_value': 175991.7}, {'field': 'count', 'old_value': 6585, 'new_value': 7380}, {'field': 'onlineAmount', 'old_value': 162392.08000000002, 'new_value': 180013.38}, {'field': 'onlineCount', 'old_value': 6585, 'new_value': 7380}]
2025-05-17 08:05:47,121 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-17 08:05:47,121 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 458946.16, 'new_value': 486206.89}, {'field': 'dailyBillAmount', 'old_value': 458946.16, 'new_value': 486206.89}, {'field': 'amount', 'old_value': 100963.71, 'new_value': 104657.71}, {'field': 'count', 'old_value': 517, 'new_value': 546}, {'field': 'instoreAmount', 'old_value': 100963.71, 'new_value': 104657.71}, {'field': 'instoreCount', 'old_value': 517, 'new_value': 546}]
2025-05-17 08:05:47,607 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9M0A
2025-05-17 08:05:47,607 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 455148.0, 'new_value': 478647.0}, {'field': 'amount', 'old_value': 455148.0, 'new_value': 478647.0}, {'field': 'count', 'old_value': 87, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 455148.0, 'new_value': 478647.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 88}]
2025-05-17 08:05:48,135 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-17 08:05:48,136 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 367778.64, 'new_value': 410108.56}, {'field': 'dailyBillAmount', 'old_value': 367778.64, 'new_value': 410108.56}]
2025-05-17 08:05:48,625 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-17 08:05:48,626 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 71594.3, 'new_value': 79106.3}, {'field': 'count', 'old_value': 216, 'new_value': 229}, {'field': 'instoreAmount', 'old_value': 71595.1, 'new_value': 79107.1}, {'field': 'instoreCount', 'old_value': 216, 'new_value': 229}]
2025-05-17 08:05:49,022 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-17 08:05:49,022 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 612863.6, 'new_value': 642216.78}, {'field': 'dailyBillAmount', 'old_value': 612863.6, 'new_value': 642216.78}, {'field': 'amount', 'old_value': -286534.48, 'new_value': -274842.98}, {'field': 'count', 'old_value': 654, 'new_value': 706}, {'field': 'instoreAmount', 'old_value': 392935.24, 'new_value': 423117.49}, {'field': 'instoreCount', 'old_value': 654, 'new_value': 706}]
2025-05-17 08:05:49,445 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-17 08:05:49,445 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 181751.0, 'new_value': 201837.0}, {'field': 'amount', 'old_value': 181751.0, 'new_value': 201837.0}, {'field': 'count', 'old_value': 762, 'new_value': 817}, {'field': 'instoreAmount', 'old_value': 181751.0, 'new_value': 201837.0}, {'field': 'instoreCount', 'old_value': 762, 'new_value': 817}]
2025-05-17 08:05:49,901 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-17 08:05:49,901 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247789.98, 'new_value': 265401.94}, {'field': 'amount', 'old_value': 247789.98, 'new_value': 265401.94}, {'field': 'count', 'old_value': 866, 'new_value': 925}, {'field': 'instoreAmount', 'old_value': 247789.98, 'new_value': 265401.94}, {'field': 'instoreCount', 'old_value': 866, 'new_value': 925}]
2025-05-17 08:05:50,295 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-17 08:05:50,296 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 114001.55, 'new_value': 122950.35}, {'field': 'dailyBillAmount', 'old_value': 114001.55, 'new_value': 122950.35}, {'field': 'amount', 'old_value': 9120.1, 'new_value': 9564.9}, {'field': 'count', 'old_value': 69, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 10730.9, 'new_value': 11175.7}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 74}]
2025-05-17 08:05:50,720 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-17 08:05:50,720 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65153.119999999995, 'new_value': 68221.34}, {'field': 'dailyBillAmount', 'old_value': 65153.119999999995, 'new_value': 68221.34}, {'field': 'amount', 'old_value': 42766.81, 'new_value': 44173.91}, {'field': 'count', 'old_value': 621, 'new_value': 645}, {'field': 'instoreAmount', 'old_value': 44313.01, 'new_value': 45772.01}, {'field': 'instoreCount', 'old_value': 621, 'new_value': 645}]
2025-05-17 08:05:51,216 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-17 08:05:51,217 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87772.87, 'new_value': 93885.56}, {'field': 'dailyBillAmount', 'old_value': 44034.55, 'new_value': 49691.62}, {'field': 'amount', 'old_value': 87772.01, 'new_value': 93884.7}, {'field': 'count', 'old_value': 3009, 'new_value': 3244}, {'field': 'instoreAmount', 'old_value': 77622.63, 'new_value': 82854.68}, {'field': 'instoreCount', 'old_value': 2734, 'new_value': 2944}, {'field': 'onlineAmount', 'old_value': 10150.24, 'new_value': 11030.88}, {'field': 'onlineCount', 'old_value': 275, 'new_value': 300}]
2025-05-17 08:05:51,662 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-17 08:05:51,663 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146193.22, 'new_value': 156971.22}, {'field': 'dailyBillAmount', 'old_value': 142735.0, 'new_value': 153513.0}, {'field': 'amount', 'old_value': 117939.22, 'new_value': 125545.22}, {'field': 'count', 'old_value': 120, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 117810.0, 'new_value': 125416.0}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 130}]
2025-05-17 08:05:52,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-17 08:05:52,115 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 317912.3, 'new_value': 340448.21}, {'field': 'dailyBillAmount', 'old_value': 317407.75, 'new_value': 339943.66}, {'field': 'amount', 'old_value': 317912.3, 'new_value': 340448.21}, {'field': 'count', 'old_value': 294, 'new_value': 316}, {'field': 'instoreAmount', 'old_value': 317913.3, 'new_value': 340449.21}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 316}]
2025-05-17 08:05:52,593 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-17 08:05:52,593 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30725.0, 'new_value': 31607.0}, {'field': 'count', 'old_value': 48, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 30725.0, 'new_value': 31607.0}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 49}]
2025-05-17 08:05:53,007 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-17 08:05:53,007 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67769.8, 'new_value': 70966.4}, {'field': 'dailyBillAmount', 'old_value': 67769.8, 'new_value': 70966.4}, {'field': 'amount', 'old_value': 77524.6, 'new_value': 80721.2}, {'field': 'count', 'old_value': 195, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 77529.2, 'new_value': 80725.8}, {'field': 'instoreCount', 'old_value': 195, 'new_value': 206}]
2025-05-17 08:05:53,467 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-17 08:05:53,467 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102604.87, 'new_value': 111545.87}, {'field': 'amount', 'old_value': 102604.87, 'new_value': 111545.87}, {'field': 'count', 'old_value': 120, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 102731.87, 'new_value': 111672.87}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 132}]
2025-05-17 08:05:53,902 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-17 08:05:53,903 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126162.22, 'new_value': 142045.12}, {'field': 'dailyBillAmount', 'old_value': 126162.22, 'new_value': 142045.12}, {'field': 'amount', 'old_value': 139066.35, 'new_value': 149928.25}, {'field': 'count', 'old_value': 931, 'new_value': 1005}, {'field': 'instoreAmount', 'old_value': 139664.35, 'new_value': 150948.25}, {'field': 'instoreCount', 'old_value': 931, 'new_value': 1005}]
2025-05-17 08:05:54,411 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-17 08:05:54,411 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88410.82, 'new_value': 95905.1}, {'field': 'dailyBillAmount', 'old_value': 88410.82, 'new_value': 95905.1}, {'field': 'amount', 'old_value': 8768.98, 'new_value': 9321.05}, {'field': 'count', 'old_value': 811, 'new_value': 859}, {'field': 'instoreAmount', 'old_value': 12153.34, 'new_value': 12749.71}, {'field': 'instoreCount', 'old_value': 811, 'new_value': 859}]
2025-05-17 08:05:54,972 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-17 08:05:54,973 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 153210.16, 'new_value': 165704.26}, {'field': 'amount', 'old_value': 153207.47, 'new_value': 165701.57}, {'field': 'count', 'old_value': 3898, 'new_value': 4193}, {'field': 'instoreAmount', 'old_value': 148403.51, 'new_value': 160826.61}, {'field': 'instoreCount', 'old_value': 3758, 'new_value': 4044}, {'field': 'onlineAmount', 'old_value': 7109.33, 'new_value': 7347.33}, {'field': 'onlineCount', 'old_value': 140, 'new_value': 149}]
2025-05-17 08:05:55,495 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-17 08:05:55,496 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 134525.97, 'new_value': 143716.65}, {'field': 'dailyBillAmount', 'old_value': 134525.97, 'new_value': 143716.65}, {'field': 'amount', 'old_value': 134525.97, 'new_value': 143716.65}, {'field': 'count', 'old_value': 427, 'new_value': 453}, {'field': 'instoreAmount', 'old_value': 134525.97, 'new_value': 143716.65}, {'field': 'instoreCount', 'old_value': 427, 'new_value': 453}]
2025-05-17 08:05:55,978 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-17 08:05:55,979 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117164.43000000001, 'new_value': 126654.26}, {'field': 'dailyBillAmount', 'old_value': 117164.43000000001, 'new_value': 126654.26}, {'field': 'amount', 'old_value': 34136.8, 'new_value': 37945.8}, {'field': 'count', 'old_value': 86, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 34136.8, 'new_value': 37945.8}, {'field': 'instoreCount', 'old_value': 86, 'new_value': 95}]
2025-05-17 08:05:56,424 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-17 08:05:56,424 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 246562.49, 'new_value': 260873.76}, {'field': 'dailyBillAmount', 'old_value': 246562.49, 'new_value': 260873.76}, {'field': 'amount', 'old_value': 100725.7, 'new_value': 106928.9}, {'field': 'count', 'old_value': 386, 'new_value': 408}, {'field': 'instoreAmount', 'old_value': 100725.96, 'new_value': 106929.16}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 408}]
2025-05-17 08:05:56,846 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFK
2025-05-17 08:05:56,846 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15459.0, 'new_value': 15618.0}, {'field': 'amount', 'old_value': 15459.0, 'new_value': 15618.0}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 15459.0, 'new_value': 15618.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 14}]
2025-05-17 08:05:57,284 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-17 08:05:57,284 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55140.6, 'new_value': 58501.38}, {'field': 'dailyBillAmount', 'old_value': 55140.6, 'new_value': 58501.38}, {'field': 'amount', 'old_value': 16909.67, 'new_value': 17641.0}, {'field': 'count', 'old_value': 616, 'new_value': 646}, {'field': 'instoreAmount', 'old_value': 4176.39, 'new_value': 4286.8099999999995}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 106}, {'field': 'onlineAmount', 'old_value': 12972.3, 'new_value': 13593.21}, {'field': 'onlineCount', 'old_value': 515, 'new_value': 540}]
2025-05-17 08:05:57,834 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-17 08:05:57,834 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86515.83, 'new_value': 91962.41}, {'field': 'dailyBillAmount', 'old_value': 86515.83, 'new_value': 91962.41}, {'field': 'amount', 'old_value': 13977.84, 'new_value': 14611.64}, {'field': 'count', 'old_value': 342, 'new_value': 360}, {'field': 'instoreAmount', 'old_value': 11777.61, 'new_value': 12411.41}, {'field': 'instoreCount', 'old_value': 299, 'new_value': 317}]
2025-05-17 08:05:58,281 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-17 08:05:58,281 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 10153.03, 'new_value': 10518.03}, {'field': 'dailyBillAmount', 'old_value': 10153.03, 'new_value': 10518.03}, {'field': 'amount', 'old_value': 10102.18, 'new_value': 10507.18}, {'field': 'count', 'old_value': 360, 'new_value': 374}, {'field': 'instoreAmount', 'old_value': 10469.78, 'new_value': 10874.78}, {'field': 'instoreCount', 'old_value': 360, 'new_value': 374}]
2025-05-17 08:05:58,748 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-17 08:05:58,749 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28330.11, 'new_value': 29914.77}, {'field': 'dailyBillAmount', 'old_value': 28330.11, 'new_value': 29914.77}, {'field': 'amount', 'old_value': 17945.77, 'new_value': 18868.64}, {'field': 'count', 'old_value': 998, 'new_value': 1057}, {'field': 'instoreAmount', 'old_value': 8984.36, 'new_value': 9366.13}, {'field': 'instoreCount', 'old_value': 385, 'new_value': 410}, {'field': 'onlineAmount', 'old_value': 9348.72, 'new_value': 9914.119999999999}, {'field': 'onlineCount', 'old_value': 613, 'new_value': 647}]
2025-05-17 08:05:59,138 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM5G
2025-05-17 08:05:59,138 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDR5M9BI657Q2OV4FVC7BK001485_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 399.0, 'new_value': 799.0}, {'field': 'dailyBillAmount', 'old_value': 399.0, 'new_value': 799.0}, {'field': 'amount', 'old_value': 749.0, 'new_value': 1149.0}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 749.0, 'new_value': 1149.0}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-05-17 08:05:59,602 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-17 08:05:59,602 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 189780.26, 'new_value': 205473.21}, {'field': 'dailyBillAmount', 'old_value': 189780.26, 'new_value': 205473.21}, {'field': 'amount', 'old_value': 88260.41, 'new_value': 95716.27}, {'field': 'count', 'old_value': 360, 'new_value': 388}, {'field': 'instoreAmount', 'old_value': 91443.52, 'new_value': 98942.52}, {'field': 'instoreCount', 'old_value': 360, 'new_value': 388}]
2025-05-17 08:06:00,260 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-17 08:06:00,261 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 11335.78, 'new_value': 11394.68}, {'field': 'count', 'old_value': 85, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 11410.02, 'new_value': 11468.92}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 88}]
2025-05-17 08:06:00,676 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-17 08:06:00,676 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121819.92, 'new_value': 132258.66}, {'field': 'dailyBillAmount', 'old_value': 121819.92, 'new_value': 132258.66}, {'field': 'amount', 'old_value': 58530.61, 'new_value': 62962.5}, {'field': 'count', 'old_value': 2522, 'new_value': 2702}, {'field': 'instoreAmount', 'old_value': 59758.74, 'new_value': 64334.229999999996}, {'field': 'instoreCount', 'old_value': 2522, 'new_value': 2702}]
2025-05-17 08:06:01,144 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-17 08:06:01,144 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 269217.9, 'new_value': 284578.4}, {'field': 'dailyBillAmount', 'old_value': 269217.9, 'new_value': 284578.4}, {'field': 'amount', 'old_value': 269217.9, 'new_value': 284578.4}, {'field': 'count', 'old_value': 330, 'new_value': 351}, {'field': 'instoreAmount', 'old_value': 269217.9, 'new_value': 284578.4}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 351}]
2025-05-17 08:06:01,595 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-17 08:06:01,596 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 130355.49, 'new_value': 136751.77}, {'field': 'dailyBillAmount', 'old_value': 130355.49, 'new_value': 136751.77}, {'field': 'amount', 'old_value': 76178.11, 'new_value': 79953.01}, {'field': 'count', 'old_value': 196, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 77229.91, 'new_value': 81004.81}, {'field': 'instoreCount', 'old_value': 196, 'new_value': 206}]
2025-05-17 08:06:02,044 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-17 08:06:02,044 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30352.0, 'new_value': 32863.0}, {'field': 'dailyBillAmount', 'old_value': 30352.0, 'new_value': 32863.0}, {'field': 'amount', 'old_value': 30352.0, 'new_value': 32863.0}, {'field': 'count', 'old_value': 592, 'new_value': 647}, {'field': 'instoreAmount', 'old_value': 30391.0, 'new_value': 32902.0}, {'field': 'instoreCount', 'old_value': 592, 'new_value': 647}]
2025-05-17 08:06:02,489 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-17 08:06:02,490 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53063.52, 'new_value': 56870.75}, {'field': 'dailyBillAmount', 'old_value': 53063.52, 'new_value': 56870.75}, {'field': 'amount', 'old_value': 55514.08, 'new_value': 59121.3}, {'field': 'count', 'old_value': 2905, 'new_value': 3105}, {'field': 'instoreAmount', 'old_value': 25896.39, 'new_value': 27951.43}, {'field': 'instoreCount', 'old_value': 1281, 'new_value': 1395}, {'field': 'onlineAmount', 'old_value': 30374.83, 'new_value': 31927.68}, {'field': 'onlineCount', 'old_value': 1624, 'new_value': 1710}]
2025-05-17 08:06:02,975 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-17 08:06:02,975 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18808.829999999998, 'new_value': 19481.73}, {'field': 'dailyBillAmount', 'old_value': 18808.829999999998, 'new_value': 19481.73}, {'field': 'amount', 'old_value': 26198.0, 'new_value': 27246.52}, {'field': 'count', 'old_value': 777, 'new_value': 811}, {'field': 'instoreAmount', 'old_value': 23465.23, 'new_value': 24395.73}, {'field': 'instoreCount', 'old_value': 667, 'new_value': 696}, {'field': 'onlineAmount', 'old_value': 2755.57, 'new_value': 2873.59}, {'field': 'onlineCount', 'old_value': 110, 'new_value': 115}]
2025-05-17 08:06:03,371 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-17 08:06:03,372 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40116.18, 'new_value': 42426.58}, {'field': 'dailyBillAmount', 'old_value': 40116.18, 'new_value': 42426.58}, {'field': 'amount', 'old_value': 40096.62, 'new_value': 42406.59}, {'field': 'count', 'old_value': 1532, 'new_value': 1632}, {'field': 'instoreAmount', 'old_value': 26062.02, 'new_value': 27519.02}, {'field': 'instoreCount', 'old_value': 908, 'new_value': 969}, {'field': 'onlineAmount', 'old_value': 14122.02, 'new_value': 14975.73}, {'field': 'onlineCount', 'old_value': 624, 'new_value': 663}]
2025-05-17 08:06:03,899 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-17 08:06:03,899 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 40660.61, 'new_value': 43811.24}, {'field': 'count', 'old_value': 479, 'new_value': 521}, {'field': 'instoreAmount', 'old_value': 41087.51, 'new_value': 44238.14}, {'field': 'instoreCount', 'old_value': 479, 'new_value': 521}]
2025-05-17 08:06:04,321 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-17 08:06:04,321 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45845.7, 'new_value': 47669.8}, {'field': 'amount', 'old_value': 45845.2, 'new_value': 47669.3}, {'field': 'count', 'old_value': 1112, 'new_value': 1167}, {'field': 'instoreAmount', 'old_value': 46493.9, 'new_value': 48318.0}, {'field': 'instoreCount', 'old_value': 1112, 'new_value': 1167}]
2025-05-17 08:06:04,762 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-17 08:06:04,762 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 208216.52, 'new_value': 217919.82}, {'field': 'dailyBillAmount', 'old_value': 208216.52, 'new_value': 217919.82}, {'field': 'amount', 'old_value': 58422.82, 'new_value': 63077.32}, {'field': 'count', 'old_value': 216, 'new_value': 234}, {'field': 'instoreAmount', 'old_value': 58422.82, 'new_value': 63077.32}, {'field': 'instoreCount', 'old_value': 216, 'new_value': 234}]
2025-05-17 08:06:05,174 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-17 08:06:05,174 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65824.88, 'new_value': 69244.62}, {'field': 'dailyBillAmount', 'old_value': 65824.88, 'new_value': 69244.62}, {'field': 'amount', 'old_value': 63697.08, 'new_value': 67116.82}, {'field': 'count', 'old_value': 200, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 65592.71, 'new_value': 69251.45}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 209}]
2025-05-17 08:06:05,674 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-17 08:06:05,674 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 35499.0, 'new_value': 39448.0}, {'field': 'dailyBillAmount', 'old_value': 35499.0, 'new_value': 39448.0}, {'field': 'amount', 'old_value': 44424.0, 'new_value': 46895.0}, {'field': 'count', 'old_value': 80, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 47771.0, 'new_value': 50993.0}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 87}]
2025-05-17 08:06:06,143 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-17 08:06:06,143 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55074.15, 'new_value': 56970.65}, {'field': 'dailyBillAmount', 'old_value': 52580.95, 'new_value': 54477.45}, {'field': 'amount', 'old_value': 55073.45, 'new_value': 56622.25}, {'field': 'count', 'old_value': 154, 'new_value': 159}, {'field': 'instoreAmount', 'old_value': 60901.45, 'new_value': 62450.25}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 159}]
2025-05-17 08:06:06,558 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-17 08:06:06,559 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73104.23, 'new_value': 77505.78}, {'field': 'dailyBillAmount', 'old_value': 73104.23, 'new_value': 77505.78}, {'field': 'amount', 'old_value': 40706.58, 'new_value': 42867.56}, {'field': 'count', 'old_value': 1125, 'new_value': 1185}, {'field': 'instoreAmount', 'old_value': 34700.92, 'new_value': 36461.44}, {'field': 'instoreCount', 'old_value': 950, 'new_value': 997}, {'field': 'onlineAmount', 'old_value': 6181.96, 'new_value': 6592.32}, {'field': 'onlineCount', 'old_value': 175, 'new_value': 188}]
2025-05-17 08:06:06,952 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-17 08:06:06,952 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105593.55, 'new_value': 111840.47}, {'field': 'dailyBillAmount', 'old_value': 101390.16, 'new_value': 107598.14}, {'field': 'amount', 'old_value': 105593.55, 'new_value': 111840.47}, {'field': 'count', 'old_value': 1301, 'new_value': 1374}, {'field': 'instoreAmount', 'old_value': 101106.0, 'new_value': 107169.45}, {'field': 'instoreCount', 'old_value': 1253, 'new_value': 1323}, {'field': 'onlineAmount', 'old_value': 4487.55, 'new_value': 4671.0199999999995}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 51}]
2025-05-17 08:06:07,403 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-17 08:06:07,404 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44805.39, 'new_value': 47897.59}, {'field': 'dailyBillAmount', 'old_value': 44805.39, 'new_value': 47897.59}, {'field': 'amount', 'old_value': 64832.659999999996, 'new_value': 67963.66}, {'field': 'count', 'old_value': 265, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 62777.32, 'new_value': 65869.02}, {'field': 'instoreCount', 'old_value': 238, 'new_value': 257}, {'field': 'onlineAmount', 'old_value': 2055.34, 'new_value': 2094.64}, {'field': 'onlineCount', 'old_value': 27, 'new_value': 28}]
2025-05-17 08:06:07,887 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-17 08:06:07,887 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124319.5, 'new_value': 135409.4}, {'field': 'dailyBillAmount', 'old_value': 124319.5, 'new_value': 135409.4}, {'field': 'amount', 'old_value': 130508.7, 'new_value': 140557.6}, {'field': 'count', 'old_value': 474, 'new_value': 509}, {'field': 'instoreAmount', 'old_value': 132436.7, 'new_value': 142737.6}, {'field': 'instoreCount', 'old_value': 474, 'new_value': 509}]
2025-05-17 08:06:08,359 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-17 08:06:08,359 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32036.0, 'new_value': 32512.0}, {'field': 'dailyBillAmount', 'old_value': 32036.0, 'new_value': 32512.0}, {'field': 'amount', 'old_value': 30941.0, 'new_value': 31417.0}, {'field': 'count', 'old_value': 67, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 31297.0, 'new_value': 31773.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 70}]
2025-05-17 08:06:08,826 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-17 08:06:08,827 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14858.5, 'new_value': 15648.9}, {'field': 'dailyBillAmount', 'old_value': 14858.5, 'new_value': 15648.9}, {'field': 'amount', 'old_value': 11573.31, 'new_value': 12246.11}, {'field': 'count', 'old_value': 532, 'new_value': 555}, {'field': 'instoreAmount', 'old_value': 11719.16, 'new_value': 12414.56}, {'field': 'instoreCount', 'old_value': 532, 'new_value': 555}]
2025-05-17 08:06:09,284 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-17 08:06:09,285 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25208.26, 'new_value': 27294.42}, {'field': 'amount', 'old_value': 25206.79, 'new_value': 27292.95}, {'field': 'count', 'old_value': 1373, 'new_value': 1481}, {'field': 'instoreAmount', 'old_value': 30375.920000000002, 'new_value': 32698.55}, {'field': 'instoreCount', 'old_value': 1373, 'new_value': 1481}]
2025-05-17 08:06:09,719 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-17 08:06:09,719 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80526.75, 'new_value': 85714.86}, {'field': 'dailyBillAmount', 'old_value': 80526.75, 'new_value': 85714.86}, {'field': 'amount', 'old_value': 64234.2, 'new_value': 68233.0}, {'field': 'count', 'old_value': 254, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 64234.2, 'new_value': 68233.0}, {'field': 'instoreCount', 'old_value': 254, 'new_value': 273}]
2025-05-17 08:06:10,212 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-17 08:06:10,212 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231667.22999999998, 'new_value': 244735.3}, {'field': 'dailyBillAmount', 'old_value': 231667.22999999998, 'new_value': 244735.3}, {'field': 'amount', 'old_value': 149290.66, 'new_value': 153824.56}, {'field': 'count', 'old_value': 1745, 'new_value': 1805}, {'field': 'instoreAmount', 'old_value': 58460.12, 'new_value': 61328.02}, {'field': 'instoreCount', 'old_value': 685, 'new_value': 723}, {'field': 'onlineAmount', 'old_value': 90830.54, 'new_value': 92496.54}, {'field': 'onlineCount', 'old_value': 1060, 'new_value': 1082}]
2025-05-17 08:06:10,779 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-17 08:06:10,779 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 145744.96, 'new_value': 153501.91999999998}, {'field': 'dailyBillAmount', 'old_value': 145744.96, 'new_value': 153501.91999999998}, {'field': 'amount', 'old_value': 151492.8, 'new_value': 159585.69999999998}, {'field': 'count', 'old_value': 893, 'new_value': 942}, {'field': 'instoreAmount', 'old_value': 152272.69999999998, 'new_value': 160365.6}, {'field': 'instoreCount', 'old_value': 893, 'new_value': 942}]
2025-05-17 08:06:11,242 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-17 08:06:11,242 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42732.89, 'new_value': 48459.89}, {'field': 'amount', 'old_value': 42732.89, 'new_value': 48459.89}, {'field': 'count', 'old_value': 19, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 42732.89, 'new_value': 48459.89}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 22}]
2025-05-17 08:06:11,756 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-17 08:06:11,756 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99689.76, 'new_value': 107672.38}, {'field': 'dailyBillAmount', 'old_value': 99689.76, 'new_value': 107672.38}, {'field': 'amount', 'old_value': 66876.66, 'new_value': 70717.86}, {'field': 'count', 'old_value': 776, 'new_value': 819}, {'field': 'instoreAmount', 'old_value': 59474.48, 'new_value': 63064.08}, {'field': 'instoreCount', 'old_value': 528, 'new_value': 561}, {'field': 'onlineAmount', 'old_value': 8064.35, 'new_value': 8315.95}, {'field': 'onlineCount', 'old_value': 248, 'new_value': 258}]
2025-05-17 08:06:12,176 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-17 08:06:12,177 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102810.74, 'new_value': 114215.05}, {'field': 'dailyBillAmount', 'old_value': 98212.99, 'new_value': 109409.3}, {'field': 'amount', 'old_value': 102810.74, 'new_value': 114215.05}, {'field': 'count', 'old_value': 439, 'new_value': 483}, {'field': 'instoreAmount', 'old_value': 102810.74, 'new_value': 114215.05}, {'field': 'instoreCount', 'old_value': 439, 'new_value': 483}]
2025-05-17 08:06:12,595 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-17 08:06:12,595 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 13695.38, 'new_value': 14607.18}, {'field': 'dailyBillAmount', 'old_value': 13695.38, 'new_value': 14607.18}, {'field': 'amount', 'old_value': 16352.380000000001, 'new_value': 17313.18}, {'field': 'count', 'old_value': 483, 'new_value': 515}, {'field': 'instoreAmount', 'old_value': 16352.380000000001, 'new_value': 17313.18}, {'field': 'instoreCount', 'old_value': 483, 'new_value': 515}]
2025-05-17 08:06:13,062 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-17 08:06:13,063 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172651.2, 'new_value': 187069.2}, {'field': 'amount', 'old_value': 172651.2, 'new_value': 187069.2}, {'field': 'count', 'old_value': 256, 'new_value': 278}, {'field': 'instoreAmount', 'old_value': 172651.2, 'new_value': 187069.2}, {'field': 'instoreCount', 'old_value': 256, 'new_value': 278}]
2025-05-17 08:06:13,482 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-17 08:06:13,482 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30680.9, 'new_value': 31867.4}, {'field': 'amount', 'old_value': 30680.9, 'new_value': 31867.4}, {'field': 'count', 'old_value': 249, 'new_value': 258}, {'field': 'instoreAmount', 'old_value': 30680.9, 'new_value': 31867.4}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 258}]
2025-05-17 08:06:13,918 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-17 08:06:13,919 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 221255.0, 'new_value': 229468.0}, {'field': 'amount', 'old_value': 221255.0, 'new_value': 229468.0}, {'field': 'count', 'old_value': 44, 'new_value': 46}, {'field': 'instoreAmount', 'old_value': 221255.0, 'new_value': 229468.0}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 46}]
2025-05-17 08:06:14,311 - INFO - 更新表单数据成功: FINST-LLF66FD1Y3AVWSRCCPLOLDIRPAOX3YH3YGHAMGB
2025-05-17 08:06:14,311 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_7930744A602B4DF1A0EB88515999F5E5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 6359.27, 'new_value': 6949.27}, {'field': 'dailyBillAmount', 'old_value': 19522.92, 'new_value': 26602.64}, {'field': 'amount', 'old_value': 6359.27, 'new_value': 6949.27}, {'field': 'count', 'old_value': 45, 'new_value': 47}, {'field': 'instoreAmount', 'old_value': 6359.27, 'new_value': 6949.27}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 47}]
2025-05-17 08:06:14,727 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-17 08:06:14,728 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 22823.600000000002, 'new_value': 23989.8}, {'field': 'count', 'old_value': 303, 'new_value': 315}, {'field': 'instoreAmount', 'old_value': 22823.600000000002, 'new_value': 23989.8}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 315}]
2025-05-17 08:06:15,182 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-17 08:06:15,183 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30410.4, 'new_value': 31577.4}, {'field': 'dailyBillAmount', 'old_value': 30410.4, 'new_value': 31577.4}, {'field': 'amount', 'old_value': 30410.4, 'new_value': 31577.4}, {'field': 'count', 'old_value': 36, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 30410.4, 'new_value': 31577.4}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 37}]
2025-05-17 08:06:15,587 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-17 08:06:15,588 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 272004.96, 'new_value': 285505.88}, {'field': 'dailyBillAmount', 'old_value': 272004.96, 'new_value': 285505.88}, {'field': 'amount', 'old_value': 284635.96, 'new_value': 298136.88}, {'field': 'count', 'old_value': 899, 'new_value': 939}, {'field': 'instoreAmount', 'old_value': 284635.96, 'new_value': 298136.88}, {'field': 'instoreCount', 'old_value': 899, 'new_value': 939}]
2025-05-17 08:06:16,001 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-17 08:06:16,001 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 207607.0, 'new_value': 262406.0}, {'field': 'dailyBillAmount', 'old_value': 207607.0, 'new_value': 262406.0}, {'field': 'amount', 'old_value': 610813.31, 'new_value': 665272.43}, {'field': 'count', 'old_value': 797, 'new_value': 855}, {'field': 'instoreAmount', 'old_value': 610813.48, 'new_value': 665272.6}, {'field': 'instoreCount', 'old_value': 797, 'new_value': 855}]
2025-05-17 08:06:16,403 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMH01
2025-05-17 08:06:16,403 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-05, 变更字段: [{'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-05-17 08:06:16,869 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-17 08:06:16,869 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 88582.4, 'new_value': 93486.3}, {'field': 'dailyBillAmount', 'old_value': 88582.4, 'new_value': 93486.3}, {'field': 'amount', 'old_value': 20270.2, 'new_value': 20791.2}, {'field': 'count', 'old_value': 79, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 20271.7, 'new_value': 20792.7}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 81}]
2025-05-17 08:06:17,296 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-17 08:06:17,296 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129046.89, 'new_value': 135828.04}, {'field': 'amount', 'old_value': 129046.42, 'new_value': 135827.57}, {'field': 'count', 'old_value': 1286, 'new_value': 1355}, {'field': 'instoreAmount', 'old_value': 84730.37, 'new_value': 87816.70999999999}, {'field': 'instoreCount', 'old_value': 758, 'new_value': 786}, {'field': 'onlineAmount', 'old_value': 46022.64, 'new_value': 49770.55}, {'field': 'onlineCount', 'old_value': 528, 'new_value': 569}]
2025-05-17 08:06:17,751 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-17 08:06:17,751 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 193101.0, 'new_value': 206481.47}, {'field': 'dailyBillAmount', 'old_value': 193101.0, 'new_value': 206481.47}, {'field': 'amount', 'old_value': 17873.57, 'new_value': 18567.74}, {'field': 'count', 'old_value': 591, 'new_value': 616}, {'field': 'instoreAmount', 'old_value': 20165.35, 'new_value': 21294.53}, {'field': 'instoreCount', 'old_value': 591, 'new_value': 616}]
2025-05-17 08:06:18,177 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-17 08:06:18,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204147.3, 'new_value': 213288.42}, {'field': 'dailyBillAmount', 'old_value': 204147.3, 'new_value': 213288.42}, {'field': 'amount', 'old_value': 101313.62, 'new_value': 105854.79000000001}, {'field': 'count', 'old_value': 2229, 'new_value': 2314}, {'field': 'instoreAmount', 'old_value': 85975.90000000001, 'new_value': 89823.49}, {'field': 'instoreCount', 'old_value': 1892, 'new_value': 1963}, {'field': 'onlineAmount', 'old_value': 17028.07, 'new_value': 17721.65}, {'field': 'onlineCount', 'old_value': 337, 'new_value': 351}]
2025-05-17 08:06:18,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-17 08:06:18,646 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 196932.3, 'new_value': 203707.8}, {'field': 'amount', 'old_value': 196930.7, 'new_value': 203706.2}, {'field': 'count', 'old_value': 763, 'new_value': 800}, {'field': 'instoreAmount', 'old_value': 199659.6, 'new_value': 206435.1}, {'field': 'instoreCount', 'old_value': 763, 'new_value': 800}]
2025-05-17 08:06:19,078 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-17 08:06:19,078 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 341196.47, 'new_value': 358363.72}, {'field': 'dailyBillAmount', 'old_value': 341196.47, 'new_value': 358363.72}, {'field': 'amount', 'old_value': 321012.91, 'new_value': 328762.98}, {'field': 'count', 'old_value': 6063, 'new_value': 6247}, {'field': 'instoreAmount', 'old_value': 300844.65, 'new_value': 308609.72}, {'field': 'instoreCount', 'old_value': 5670, 'new_value': 5854}]
2025-05-17 08:06:19,632 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-17 08:06:19,633 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165004.27, 'new_value': 175412.67}, {'field': 'amount', 'old_value': 132719.0, 'new_value': 143127.4}, {'field': 'count', 'old_value': 3183, 'new_value': 3454}, {'field': 'instoreAmount', 'old_value': 119205.1, 'new_value': 128641.3}, {'field': 'instoreCount', 'old_value': 2570, 'new_value': 2795}, {'field': 'onlineAmount', 'old_value': 13673.7, 'new_value': 14645.9}, {'field': 'onlineCount', 'old_value': 613, 'new_value': 659}]
2025-05-17 08:06:20,056 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-17 08:06:20,056 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 36637.27, 'new_value': 35680.77}, {'field': 'count', 'old_value': 809, 'new_value': 817}, {'field': 'instoreAmount', 'old_value': 1377.0, 'new_value': 1756.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 35}, {'field': 'onlineAmount', 'old_value': 44734.729999999996, 'new_value': 44790.729999999996}, {'field': 'onlineCount', 'old_value': 779, 'new_value': 782}]
2025-05-17 08:06:20,483 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-17 08:06:20,483 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 96676.23, 'new_value': 104175.17}, {'field': 'count', 'old_value': 6454, 'new_value': 7130}, {'field': 'instoreAmount', 'old_value': 79617.78, 'new_value': 86969.76}, {'field': 'instoreCount', 'old_value': 5161, 'new_value': 5801}, {'field': 'onlineAmount', 'old_value': 19323.34, 'new_value': 19619.510000000002}, {'field': 'onlineCount', 'old_value': 1293, 'new_value': 1329}]
2025-05-17 08:06:20,966 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-17 08:06:20,966 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 171565.86, 'new_value': 178877.62}, {'field': 'dailyBillAmount', 'old_value': 171565.86, 'new_value': 178877.62}, {'field': 'amount', 'old_value': 166402.54, 'new_value': 173004.74}, {'field': 'count', 'old_value': 4825, 'new_value': 5030}, {'field': 'instoreAmount', 'old_value': 167592.65, 'new_value': 174194.85}, {'field': 'instoreCount', 'old_value': 4825, 'new_value': 5030}]
2025-05-17 08:06:21,474 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-17 08:06:21,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47246.340000000004, 'new_value': 50627.46}, {'field': 'amount', 'old_value': 47246.340000000004, 'new_value': 50627.46}, {'field': 'count', 'old_value': 2604, 'new_value': 2767}, {'field': 'instoreAmount', 'old_value': 30722.68, 'new_value': 31915.95}, {'field': 'instoreCount', 'old_value': 1719, 'new_value': 1795}, {'field': 'onlineAmount', 'old_value': 16523.66, 'new_value': 18711.51}, {'field': 'onlineCount', 'old_value': 885, 'new_value': 972}]
2025-05-17 08:06:21,885 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-17 08:06:21,885 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84565.04, 'new_value': 88455.2}, {'field': 'dailyBillAmount', 'old_value': 84565.04, 'new_value': 88455.2}, {'field': 'amount', 'old_value': 17434.260000000002, 'new_value': 18316.43}, {'field': 'count', 'old_value': 621, 'new_value': 657}, {'field': 'instoreAmount', 'old_value': 17926.88, 'new_value': 18975.52}, {'field': 'instoreCount', 'old_value': 621, 'new_value': 657}]
2025-05-17 08:06:22,296 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-17 08:06:22,297 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70470.47, 'new_value': 75511.70999999999}, {'field': 'dailyBillAmount', 'old_value': 70470.47, 'new_value': 75511.70999999999}, {'field': 'amount', 'old_value': 59488.9, 'new_value': 63006.340000000004}, {'field': 'count', 'old_value': 2944, 'new_value': 3143}, {'field': 'instoreAmount', 'old_value': 13620.2, 'new_value': 14413.04}, {'field': 'instoreCount', 'old_value': 963, 'new_value': 1023}, {'field': 'onlineAmount', 'old_value': 46768.14, 'new_value': 49559.54}, {'field': 'onlineCount', 'old_value': 1981, 'new_value': 2120}]
2025-05-17 08:06:22,801 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-17 08:06:22,802 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 61678.85, 'new_value': 64870.3}, {'field': 'amount', 'old_value': 61678.04, 'new_value': 64869.49}, {'field': 'count', 'old_value': 1707, 'new_value': 1789}, {'field': 'instoreAmount', 'old_value': 59569.6, 'new_value': 62654.65}, {'field': 'instoreCount', 'old_value': 1664, 'new_value': 1744}, {'field': 'onlineAmount', 'old_value': 2753.29, 'new_value': 2859.69}, {'field': 'onlineCount', 'old_value': 43, 'new_value': 45}]
2025-05-17 08:06:23,310 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-17 08:06:23,310 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 89296.05, 'new_value': 96017.87}, {'field': 'count', 'old_value': 3155, 'new_value': 3406}, {'field': 'instoreAmount', 'old_value': 89813.67, 'new_value': 96733.89}, {'field': 'instoreCount', 'old_value': 3126, 'new_value': 3377}]
2025-05-17 08:06:23,900 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-17 08:06:23,900 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115841.2, 'new_value': 121580.1}, {'field': 'dailyBillAmount', 'old_value': 115841.2, 'new_value': 121580.1}, {'field': 'amount', 'old_value': 75386.48, 'new_value': 79365.84}, {'field': 'count', 'old_value': 6706, 'new_value': 6947}, {'field': 'instoreAmount', 'old_value': 4858.91, 'new_value': 5352.91}, {'field': 'instoreCount', 'old_value': 246, 'new_value': 267}, {'field': 'onlineAmount', 'old_value': 74767.58, 'new_value': 78297.34}, {'field': 'onlineCount', 'old_value': 6460, 'new_value': 6680}]
2025-05-17 08:06:24,293 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-17 08:06:24,293 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 102337.74, 'new_value': 107261.9}, {'field': 'dailyBillAmount', 'old_value': 102337.74, 'new_value': 107261.9}, {'field': 'amount', 'old_value': 86765.28, 'new_value': 90394.5}, {'field': 'count', 'old_value': 2966, 'new_value': 3120}, {'field': 'instoreAmount', 'old_value': 49660.95, 'new_value': 51731.5}, {'field': 'instoreCount', 'old_value': 2173, 'new_value': 2285}, {'field': 'onlineAmount', 'old_value': 43059.41, 'new_value': 45402.61}, {'field': 'onlineCount', 'old_value': 793, 'new_value': 835}]
2025-05-17 08:06:24,792 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-17 08:06:24,792 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'count', 'old_value': 27, 'new_value': 30}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 30}]
2025-05-17 08:06:25,200 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-17 08:06:25,201 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33699.15, 'new_value': 36369.59}, {'field': 'dailyBillAmount', 'old_value': 33699.15, 'new_value': 36369.59}, {'field': 'amount', 'old_value': 48421.84, 'new_value': 51423.12}, {'field': 'count', 'old_value': 1887, 'new_value': 2010}, {'field': 'instoreAmount', 'old_value': 15619.61, 'new_value': 16663.63}, {'field': 'instoreCount', 'old_value': 668, 'new_value': 727}, {'field': 'onlineAmount', 'old_value': 33457.24, 'new_value': 35458.1}, {'field': 'onlineCount', 'old_value': 1219, 'new_value': 1283}]
2025-05-17 08:06:25,666 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-17 08:06:25,666 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60193.270000000004, 'new_value': 63258.43}, {'field': 'dailyBillAmount', 'old_value': 60193.270000000004, 'new_value': 63258.43}, {'field': 'amount', 'old_value': 61920.979999999996, 'new_value': 65091.06}, {'field': 'count', 'old_value': 2207, 'new_value': 2324}, {'field': 'instoreAmount', 'old_value': 61920.979999999996, 'new_value': 65091.06}, {'field': 'instoreCount', 'old_value': 2207, 'new_value': 2324}]
2025-05-17 08:06:26,113 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-17 08:06:26,114 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 218531.0, 'new_value': 225983.0}, {'field': 'dailyBillAmount', 'old_value': 218531.0, 'new_value': 225983.0}, {'field': 'amount', 'old_value': 226637.0, 'new_value': 234189.0}, {'field': 'count', 'old_value': 188, 'new_value': 196}, {'field': 'instoreAmount', 'old_value': 246363.0, 'new_value': 254689.0}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 196}]
2025-05-17 08:06:26,604 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-17 08:06:26,604 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 166477.2, 'new_value': 170031.2}, {'field': 'dailyBillAmount', 'old_value': 166477.2, 'new_value': 170031.2}, {'field': 'amount', 'old_value': 174121.16, 'new_value': 180301.66}, {'field': 'count', 'old_value': 316, 'new_value': 332}, {'field': 'instoreAmount', 'old_value': 176036.86, 'new_value': 182217.36}, {'field': 'instoreCount', 'old_value': 316, 'new_value': 332}]
2025-05-17 08:06:27,086 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-17 08:06:27,086 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51670.0, 'new_value': 54252.0}, {'field': 'dailyBillAmount', 'old_value': 51670.0, 'new_value': 54252.0}, {'field': 'amount', 'old_value': 26257.0, 'new_value': 26656.0}, {'field': 'count', 'old_value': 71, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 27420.0, 'new_value': 27819.0}, {'field': 'instoreCount', 'old_value': 71, 'new_value': 72}]
2025-05-17 08:06:27,479 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-17 08:06:27,479 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44583.0, 'new_value': 46993.0}, {'field': 'dailyBillAmount', 'old_value': 25700.0, 'new_value': 31642.0}, {'field': 'amount', 'old_value': 42767.0, 'new_value': 44738.0}, {'field': 'count', 'old_value': 53, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 42767.0, 'new_value': 44738.0}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 56}]
2025-05-17 08:06:27,959 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-17 08:06:27,960 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47173.7, 'new_value': 49133.7}, {'field': 'amount', 'old_value': 47171.5, 'new_value': 49131.5}, {'field': 'count', 'old_value': 129, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 47173.7, 'new_value': 49133.7}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 133}]
2025-05-17 08:06:28,464 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-17 08:06:28,464 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 331330.0, 'new_value': 358627.0}, {'field': 'dailyBillAmount', 'old_value': 331330.0, 'new_value': 358627.0}, {'field': 'amount', 'old_value': 393759.0, 'new_value': 421056.0}, {'field': 'count', 'old_value': 50, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 393759.0, 'new_value': 421056.0}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 53}]
2025-05-17 08:06:28,872 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-17 08:06:28,872 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14028.0, 'new_value': 14862.0}, {'field': 'amount', 'old_value': 14028.0, 'new_value': 14862.0}, {'field': 'count', 'old_value': 21, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 14028.0, 'new_value': 14862.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 23}]
2025-05-17 08:06:29,289 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-17 08:06:29,289 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 125542.6, 'new_value': 142225.7}, {'field': 'dailyBillAmount', 'old_value': 125542.6, 'new_value': 142225.7}, {'field': 'amount', 'old_value': 217599.6, 'new_value': 230619.4}, {'field': 'count', 'old_value': 276, 'new_value': 293}, {'field': 'instoreAmount', 'old_value': 224102.06, 'new_value': 237961.16}, {'field': 'instoreCount', 'old_value': 276, 'new_value': 293}]
2025-05-17 08:06:29,743 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-17 08:06:29,744 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 71338.81, 'new_value': 74887.11}, {'field': 'dailyBillAmount', 'old_value': 71338.81, 'new_value': 74887.11}, {'field': 'amount', 'old_value': 9798.640000000001, 'new_value': 9964.54}, {'field': 'count', 'old_value': 93, 'new_value': 95}, {'field': 'instoreAmount', 'old_value': 9538.44, 'new_value': 9704.44}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 60}]
2025-05-17 08:06:30,258 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-17 08:06:30,258 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23795.0, 'new_value': 23954.0}, {'field': 'dailyBillAmount', 'old_value': 23795.0, 'new_value': 23954.0}, {'field': 'amount', 'old_value': 27448.0, 'new_value': 27786.0}, {'field': 'count', 'old_value': 88, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 27448.0, 'new_value': 27786.0}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 90}]
2025-05-17 08:06:30,694 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-17 08:06:30,694 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21238.2, 'new_value': 23291.7}, {'field': 'amount', 'old_value': 21238.2, 'new_value': 23291.7}, {'field': 'count', 'old_value': 124, 'new_value': 134}, {'field': 'instoreAmount', 'old_value': 21576.2, 'new_value': 23629.7}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 134}]
2025-05-17 08:06:31,169 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-17 08:06:31,169 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 4966.0, 'new_value': 5230.0}, {'field': 'dailyBillAmount', 'old_value': 4966.0, 'new_value': 5230.0}, {'field': 'amount', 'old_value': 28540.0, 'new_value': 29615.0}, {'field': 'count', 'old_value': 85, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 29315.0, 'new_value': 30390.0}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 89}]
2025-05-17 08:06:31,635 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-17 08:06:31,635 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 513327.12, 'new_value': 540658.92}, {'field': 'dailyBillAmount', 'old_value': 513327.12, 'new_value': 540658.92}, {'field': 'amount', 'old_value': 34485.93, 'new_value': 35306.93}, {'field': 'count', 'old_value': 312, 'new_value': 323}, {'field': 'instoreAmount', 'old_value': 28641.53, 'new_value': 29304.329999999998}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 226}, {'field': 'onlineAmount', 'old_value': 6656.92, 'new_value': 6815.13}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 97}]
2025-05-17 08:06:32,093 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-17 08:06:32,094 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52467.0, 'new_value': 54715.0}, {'field': 'amount', 'old_value': 52267.0, 'new_value': 54515.0}, {'field': 'count', 'old_value': 63, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 52766.0, 'new_value': 55014.0}, {'field': 'instoreCount', 'old_value': 63, 'new_value': 66}]
2025-05-17 08:06:32,546 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-17 08:06:32,546 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14867.36, 'new_value': 15163.96}, {'field': 'amount', 'old_value': 14866.66, 'new_value': 15163.26}, {'field': 'count', 'old_value': 58, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 14867.36, 'new_value': 15163.96}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 60}]
2025-05-17 08:06:32,973 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-17 08:06:32,973 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29345.0, 'new_value': 30037.0}, {'field': 'dailyBillAmount', 'old_value': 29345.0, 'new_value': 30037.0}, {'field': 'amount', 'old_value': 29371.0, 'new_value': 30063.0}, {'field': 'count', 'old_value': 72, 'new_value': 73}, {'field': 'instoreAmount', 'old_value': 29821.0, 'new_value': 30513.0}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 73}]
2025-05-17 08:06:33,419 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-17 08:06:33,419 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 231180.63, 'new_value': 239799.41}, {'field': 'dailyBillAmount', 'old_value': 215857.66, 'new_value': 224018.52}, {'field': 'amount', 'old_value': 229404.1, 'new_value': 238022.88}, {'field': 'count', 'old_value': 447, 'new_value': 462}, {'field': 'instoreAmount', 'old_value': 231436.22, 'new_value': 240055.0}, {'field': 'instoreCount', 'old_value': 447, 'new_value': 462}]
2025-05-17 08:06:33,862 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-17 08:06:33,863 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48470.0, 'new_value': 48644.0}, {'field': 'amount', 'old_value': 48470.0, 'new_value': 48644.0}, {'field': 'count', 'old_value': 211, 'new_value': 214}, {'field': 'instoreAmount', 'old_value': 48528.0, 'new_value': 48702.0}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 214}]
2025-05-17 08:06:34,272 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-17 08:06:34,272 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 6557.5, 'new_value': 14144.5}, {'field': 'dailyBillAmount', 'old_value': 6557.5, 'new_value': 14144.5}, {'field': 'amount', 'old_value': 9820.02, 'new_value': 17407.02}, {'field': 'count', 'old_value': 57, 'new_value': 113}, {'field': 'instoreAmount', 'old_value': 9820.02, 'new_value': 17407.02}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 113}]
2025-05-17 08:06:34,662 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-17 08:06:34,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 24924.07, 'new_value': 25340.87}, {'field': 'count', 'old_value': 2414, 'new_value': 2462}, {'field': 'instoreAmount', 'old_value': 26623.82, 'new_value': 27089.32}, {'field': 'instoreCount', 'old_value': 2414, 'new_value': 2462}]
2025-05-17 08:06:35,119 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-17 08:06:35,119 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 385029.7, 'new_value': 405896.0}, {'field': 'dailyBillAmount', 'old_value': 385029.7, 'new_value': 405896.0}, {'field': 'amount', 'old_value': 397956.05, 'new_value': 419195.08}, {'field': 'count', 'old_value': 3574, 'new_value': 3808}, {'field': 'instoreAmount', 'old_value': 306667.17, 'new_value': 322110.19}, {'field': 'instoreCount', 'old_value': 1440, 'new_value': 1522}, {'field': 'onlineAmount', 'old_value': 94012.64, 'new_value': 100172.04}, {'field': 'onlineCount', 'old_value': 2134, 'new_value': 2286}]
2025-05-17 08:06:35,593 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-17 08:06:35,593 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118728.76, 'new_value': 124017.66}, {'field': 'amount', 'old_value': 118728.76, 'new_value': 124017.66}, {'field': 'count', 'old_value': 783, 'new_value': 819}, {'field': 'instoreAmount', 'old_value': 118837.76, 'new_value': 124126.66}, {'field': 'instoreCount', 'old_value': 783, 'new_value': 819}]
2025-05-17 08:06:36,059 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-17 08:06:36,059 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55456.83, 'new_value': 60086.27}, {'field': 'dailyBillAmount', 'old_value': 55456.83, 'new_value': 60086.27}, {'field': 'amount', 'old_value': 65788.01, 'new_value': 70771.64}, {'field': 'count', 'old_value': 2736, 'new_value': 3088}, {'field': 'instoreAmount', 'old_value': 33502.68, 'new_value': 36306.909999999996}, {'field': 'instoreCount', 'old_value': 1543, 'new_value': 1798}, {'field': 'onlineAmount', 'old_value': 32859.62, 'new_value': 35108.54}, {'field': 'onlineCount', 'old_value': 1193, 'new_value': 1290}]
2025-05-17 08:06:36,507 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-17 08:06:36,507 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21449.0, 'new_value': 28647.0}, {'field': 'amount', 'old_value': 21449.0, 'new_value': 28647.0}, {'field': 'count', 'old_value': 14, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 21449.0, 'new_value': 28647.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 16}]
2025-05-17 08:06:37,000 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-17 08:06:37,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82049.16, 'new_value': 85252.39}, {'field': 'dailyBillAmount', 'old_value': 82049.16, 'new_value': 85252.39}, {'field': 'amount', 'old_value': 37797.9, 'new_value': 39807.88}, {'field': 'count', 'old_value': 2417, 'new_value': 2541}, {'field': 'instoreAmount', 'old_value': 5980.9, 'new_value': 6077.8}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 249}, {'field': 'onlineAmount', 'old_value': 31817.0, 'new_value': 33730.08}, {'field': 'onlineCount', 'old_value': 2177, 'new_value': 2292}]
2025-05-17 08:06:37,432 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-17 08:06:37,432 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 212070.74, 'new_value': 220245.93}, {'field': 'dailyBillAmount', 'old_value': 212070.74, 'new_value': 220245.93}, {'field': 'amount', 'old_value': 199759.36, 'new_value': 207466.86}, {'field': 'count', 'old_value': 1633, 'new_value': 1725}, {'field': 'instoreAmount', 'old_value': 148565.15, 'new_value': 153550.25}, {'field': 'instoreCount', 'old_value': 715, 'new_value': 740}, {'field': 'onlineAmount', 'old_value': 51194.409999999996, 'new_value': 53916.81}, {'field': 'onlineCount', 'old_value': 918, 'new_value': 985}]
2025-05-17 08:06:37,885 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-17 08:06:37,885 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 245660.68, 'new_value': 253693.25}, {'field': 'dailyBillAmount', 'old_value': 245660.68, 'new_value': 253693.25}, {'field': 'amount', 'old_value': 251795.9, 'new_value': 260172.19999999998}, {'field': 'count', 'old_value': 1490, 'new_value': 1554}, {'field': 'instoreAmount', 'old_value': 229772.7, 'new_value': 236946.3}, {'field': 'instoreCount', 'old_value': 1254, 'new_value': 1306}, {'field': 'onlineAmount', 'old_value': 26523.4, 'new_value': 27726.100000000002}, {'field': 'onlineCount', 'old_value': 236, 'new_value': 248}]
2025-05-17 08:06:38,317 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-17 08:06:38,317 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 613822.28, 'new_value': 651004.44}, {'field': 'dailyBillAmount', 'old_value': 613822.28, 'new_value': 651004.44}, {'field': 'amount', 'old_value': 685856.84, 'new_value': 719696.86}, {'field': 'count', 'old_value': 3542, 'new_value': 3745}, {'field': 'instoreAmount', 'old_value': 520544.02, 'new_value': 544420.58}, {'field': 'instoreCount', 'old_value': 2024, 'new_value': 2132}, {'field': 'onlineAmount', 'old_value': 169853.64, 'new_value': 180051.24}, {'field': 'onlineCount', 'old_value': 1518, 'new_value': 1613}]
2025-05-17 08:06:38,738 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-17 08:06:38,739 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 184344.31, 'new_value': 194347.56}, {'field': 'dailyBillAmount', 'old_value': 184344.31, 'new_value': 194347.56}, {'field': 'amount', 'old_value': 259039.38999999998, 'new_value': 272931.51}, {'field': 'count', 'old_value': 1244, 'new_value': 1318}, {'field': 'instoreAmount', 'old_value': 243131.2, 'new_value': 256276.32}, {'field': 'instoreCount', 'old_value': 985, 'new_value': 1045}, {'field': 'onlineAmount', 'old_value': 16112.99, 'new_value': 16859.99}, {'field': 'onlineCount', 'old_value': 259, 'new_value': 273}]
2025-05-17 08:06:39,176 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-17 08:06:39,177 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 245296.43000000002, 'new_value': 256886.66}, {'field': 'dailyBillAmount', 'old_value': 245296.43000000002, 'new_value': 256886.66}, {'field': 'amount', 'old_value': 231996.6, 'new_value': 242901.3}, {'field': 'count', 'old_value': 1008, 'new_value': 1065}, {'field': 'instoreAmount', 'old_value': 235894.3, 'new_value': 246799.0}, {'field': 'instoreCount', 'old_value': 1008, 'new_value': 1065}]
2025-05-17 08:06:39,529 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-17 08:06:39,530 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 500436.92, 'new_value': 522978.84}, {'field': 'amount', 'old_value': 500436.92, 'new_value': 522978.84}, {'field': 'count', 'old_value': 3880, 'new_value': 4078}, {'field': 'instoreAmount', 'old_value': 500436.92, 'new_value': 522978.84}, {'field': 'instoreCount', 'old_value': 3880, 'new_value': 4078}]
2025-05-17 08:06:39,904 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-17 08:06:39,905 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'amount', 'old_value': 480313.74, 'new_value': 507854.37}, {'field': 'count', 'old_value': 3246, 'new_value': 3477}, {'field': 'instoreAmount', 'old_value': 267404.7, 'new_value': 279415.6}, {'field': 'instoreCount', 'old_value': 1358, 'new_value': 1438}, {'field': 'onlineAmount', 'old_value': 218502.9, 'new_value': 234810.9}, {'field': 'onlineCount', 'old_value': 1888, 'new_value': 2039}]
2025-05-17 08:06:40,335 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-17 08:06:40,335 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 236626.33, 'new_value': 249686.13}, {'field': 'dailyBillAmount', 'old_value': 236626.33, 'new_value': 249686.13}, {'field': 'amount', 'old_value': 308928.34, 'new_value': 321252.25}, {'field': 'count', 'old_value': 3245, 'new_value': 3406}, {'field': 'instoreAmount', 'old_value': 218552.82, 'new_value': 224693.82}, {'field': 'instoreCount', 'old_value': 1448, 'new_value': 1498}, {'field': 'onlineAmount', 'old_value': 91356.08, 'new_value': 97656.47}, {'field': 'onlineCount', 'old_value': 1797, 'new_value': 1908}]
2025-05-17 08:06:40,732 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-17 08:06:40,733 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 315640.13, 'new_value': 330579.52}, {'field': 'dailyBillAmount', 'old_value': 315640.13, 'new_value': 330579.52}, {'field': 'amount', 'old_value': 319818.69, 'new_value': 334940.68}, {'field': 'count', 'old_value': 2980, 'new_value': 3157}, {'field': 'instoreAmount', 'old_value': 278707.68, 'new_value': 291555.9}, {'field': 'instoreCount', 'old_value': 1491, 'new_value': 1587}, {'field': 'onlineAmount', 'old_value': 41885.67, 'new_value': 44250.34}, {'field': 'onlineCount', 'old_value': 1489, 'new_value': 1570}]
2025-05-17 08:06:41,199 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-17 08:06:41,200 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 84693.8, 'new_value': 90790.8}, {'field': 'amount', 'old_value': 84693.3, 'new_value': 90790.3}, {'field': 'count', 'old_value': 369, 'new_value': 397}, {'field': 'instoreAmount', 'old_value': 84693.8, 'new_value': 90790.8}, {'field': 'instoreCount', 'old_value': 369, 'new_value': 397}]
2025-05-17 08:06:41,644 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-17 08:06:41,645 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 233067.59, 'new_value': 242525.79}, {'field': 'dailyBillAmount', 'old_value': 233067.59, 'new_value': 242525.79}, {'field': 'amount', 'old_value': -163976.68, 'new_value': -172493.98}, {'field': 'count', 'old_value': 651, 'new_value': 679}, {'field': 'instoreAmount', 'old_value': 4753.6, 'new_value': 4827.6}, {'field': 'instoreCount', 'old_value': 207, 'new_value': 210}, {'field': 'onlineAmount', 'old_value': 13669.72, 'new_value': 14379.42}, {'field': 'onlineCount', 'old_value': 444, 'new_value': 469}]
2025-05-17 08:06:42,097 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-17 08:06:42,097 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 382361.52999999997, 'new_value': 401430.81}, {'field': 'dailyBillAmount', 'old_value': 382361.52999999997, 'new_value': 401430.81}, {'field': 'amount', 'old_value': 308103.94, 'new_value': 319193.82}, {'field': 'count', 'old_value': 1247, 'new_value': 1299}, {'field': 'instoreAmount', 'old_value': 308103.94, 'new_value': 319193.82}, {'field': 'instoreCount', 'old_value': 1247, 'new_value': 1299}]
2025-05-17 08:06:42,497 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-17 08:06:42,497 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'amount', 'old_value': 106674.1, 'new_value': 110227.9}, {'field': 'count', 'old_value': 445, 'new_value': 463}, {'field': 'instoreAmount', 'old_value': 111762.8, 'new_value': 115316.6}, {'field': 'instoreCount', 'old_value': 429, 'new_value': 447}]
2025-05-17 08:06:42,924 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-17 08:06:42,924 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194877.30000000002, 'new_value': 204032.44}, {'field': 'dailyBillAmount', 'old_value': 194877.30000000002, 'new_value': 204032.44}, {'field': 'amount', 'old_value': 190370.35, 'new_value': 198692.16}, {'field': 'count', 'old_value': 1223, 'new_value': 1289}, {'field': 'instoreAmount', 'old_value': 180050.52, 'new_value': 187935.74}, {'field': 'instoreCount', 'old_value': 958, 'new_value': 1009}, {'field': 'onlineAmount', 'old_value': 10462.59, 'new_value': 10899.18}, {'field': 'onlineCount', 'old_value': 265, 'new_value': 280}]
2025-05-17 08:06:43,372 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-17 08:06:43,372 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 212106.27, 'new_value': 221577.72}, {'field': 'dailyBillAmount', 'old_value': 212106.27, 'new_value': 221577.72}, {'field': 'amount', 'old_value': 93511.9, 'new_value': 97888.57}, {'field': 'count', 'old_value': 1385, 'new_value': 1471}, {'field': 'instoreAmount', 'old_value': 55496.96, 'new_value': 57760.42}, {'field': 'instoreCount', 'old_value': 386, 'new_value': 412}, {'field': 'onlineAmount', 'old_value': 38016.68, 'new_value': 40130.07}, {'field': 'onlineCount', 'old_value': 999, 'new_value': 1059}]
2025-05-17 08:06:43,816 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-17 08:06:43,816 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 38794.0, 'new_value': 43024.0}, {'field': 'count', 'old_value': 23, 'new_value': 24}, {'field': 'instoreAmount', 'old_value': 38794.0, 'new_value': 43024.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}]
2025-05-17 08:06:44,229 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-17 08:06:44,229 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82157.69, 'new_value': 88634.07}, {'field': 'amount', 'old_value': 82152.29000000001, 'new_value': 88627.85}, {'field': 'count', 'old_value': 3658, 'new_value': 3951}, {'field': 'instoreAmount', 'old_value': 31726.35, 'new_value': 33904.71}, {'field': 'instoreCount', 'old_value': 1218, 'new_value': 1319}, {'field': 'onlineAmount', 'old_value': 54303.9, 'new_value': 58844.520000000004}, {'field': 'onlineCount', 'old_value': 2440, 'new_value': 2632}]
2025-05-17 08:06:44,679 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-17 08:06:44,679 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29033.9, 'new_value': 29765.9}, {'field': 'amount', 'old_value': 29033.9, 'new_value': 29765.9}, {'field': 'count', 'old_value': 130, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 29033.9, 'new_value': 29765.9}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 133}]
2025-05-17 08:06:45,116 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-17 08:06:45,117 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 249368.77, 'new_value': 261257.53}, {'field': 'dailyBillAmount', 'old_value': 249368.77, 'new_value': 261257.53}, {'field': 'amount', 'old_value': 97443.0, 'new_value': 101926.0}, {'field': 'count', 'old_value': 1814, 'new_value': 1894}, {'field': 'instoreAmount', 'old_value': 98378.4, 'new_value': 102893.1}, {'field': 'instoreCount', 'old_value': 1814, 'new_value': 1894}]
2025-05-17 08:06:45,529 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-17 08:06:45,529 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99778.36, 'new_value': 104193.44}, {'field': 'amount', 'old_value': 99778.36, 'new_value': 104193.44}, {'field': 'count', 'old_value': 2371, 'new_value': 2480}, {'field': 'instoreAmount', 'old_value': 99778.36, 'new_value': 104193.44}, {'field': 'instoreCount', 'old_value': 2371, 'new_value': 2480}]
2025-05-17 08:06:46,015 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-17 08:06:46,015 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 16839.97, 'new_value': 18073.69}, {'field': 'amount', 'old_value': 16838.43, 'new_value': 18072.15}, {'field': 'count', 'old_value': 959, 'new_value': 1027}, {'field': 'instoreAmount', 'old_value': 10026.96, 'new_value': 10386.66}, {'field': 'instoreCount', 'old_value': 492, 'new_value': 511}, {'field': 'onlineAmount', 'old_value': 7021.21, 'new_value': 8106.98}, {'field': 'onlineCount', 'old_value': 467, 'new_value': 516}]
2025-05-17 08:06:46,452 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-17 08:06:46,453 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28743.600000000002, 'new_value': 29821.5}, {'field': 'amount', 'old_value': 28743.600000000002, 'new_value': 29821.5}, {'field': 'count', 'old_value': 72, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 28743.600000000002, 'new_value': 29821.5}, {'field': 'instoreCount', 'old_value': 72, 'new_value': 74}]
2025-05-17 08:06:46,880 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG81
2025-05-17 08:06:46,881 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ08R852VK83IKSIOCDI7KE001FRF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45068.66, 'new_value': 48115.66}, {'field': 'dailyBillAmount', 'old_value': 45068.66, 'new_value': 48115.66}, {'field': 'amount', 'old_value': 52270.33, 'new_value': 55465.23}, {'field': 'count', 'old_value': 2014, 'new_value': 2141}, {'field': 'instoreAmount', 'old_value': 52586.23, 'new_value': 55781.13}, {'field': 'instoreCount', 'old_value': 2014, 'new_value': 2141}]
2025-05-17 08:06:47,320 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-17 08:06:47,320 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 110501.70999999999, 'new_value': 117090.59}, {'field': 'dailyBillAmount', 'old_value': 92519.5, 'new_value': 97970.5}, {'field': 'amount', 'old_value': 110501.03, 'new_value': 117089.91}, {'field': 'count', 'old_value': 1545, 'new_value': 1666}, {'field': 'instoreAmount', 'old_value': 106763.3, 'new_value': 112891.3}, {'field': 'instoreCount', 'old_value': 1367, 'new_value': 1466}, {'field': 'onlineAmount', 'old_value': 3925.61, 'new_value': 4386.49}, {'field': 'onlineCount', 'old_value': 178, 'new_value': 200}]
2025-05-17 08:06:47,730 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-17 08:06:47,730 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17125.16, 'new_value': 17967.6}, {'field': 'amount', 'old_value': 17124.36, 'new_value': 17966.8}, {'field': 'count', 'old_value': 729, 'new_value': 763}, {'field': 'instoreAmount', 'old_value': 14643.16, 'new_value': 15298.5}, {'field': 'instoreCount', 'old_value': 660, 'new_value': 689}, {'field': 'onlineAmount', 'old_value': 2522.2, 'new_value': 2709.3}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 74}]
2025-05-17 08:06:48,167 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-17 08:06:48,167 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 234118.63, 'new_value': 247139.73}, {'field': 'dailyBillAmount', 'old_value': 234118.63, 'new_value': 247139.73}, {'field': 'amount', 'old_value': 299570.94, 'new_value': 316867.45}, {'field': 'count', 'old_value': 3029, 'new_value': 3274}, {'field': 'instoreAmount', 'old_value': 283348.84, 'new_value': 299387.04}, {'field': 'instoreCount', 'old_value': 2078, 'new_value': 2260}, {'field': 'onlineAmount', 'old_value': 23993.75, 'new_value': 25301.38}, {'field': 'onlineCount', 'old_value': 951, 'new_value': 1014}]
2025-05-17 08:06:48,666 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-17 08:06:48,667 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96444.44, 'new_value': 100464.0}, {'field': 'dailyBillAmount', 'old_value': 96444.44, 'new_value': 100464.0}, {'field': 'amount', 'old_value': 23432.49, 'new_value': 24766.99}, {'field': 'count', 'old_value': 372, 'new_value': 401}, {'field': 'instoreAmount', 'old_value': 14296.73, 'new_value': 15189.53}, {'field': 'instoreCount', 'old_value': 193, 'new_value': 206}, {'field': 'onlineAmount', 'old_value': 9779.05, 'new_value': 10436.55}, {'field': 'onlineCount', 'old_value': 179, 'new_value': 195}]
2025-05-17 08:06:49,117 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-17 08:06:49,117 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 93657.86, 'new_value': 99816.68}, {'field': 'amount', 'old_value': 93657.27, 'new_value': 99815.22}, {'field': 'count', 'old_value': 5374, 'new_value': 5698}, {'field': 'instoreAmount', 'old_value': 58076.71, 'new_value': 61904.67}, {'field': 'instoreCount', 'old_value': 3309, 'new_value': 3506}, {'field': 'onlineAmount', 'old_value': 36928.92, 'new_value': 39259.78}, {'field': 'onlineCount', 'old_value': 2065, 'new_value': 2192}]
2025-05-17 08:06:49,613 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-17 08:06:49,613 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48702.12, 'new_value': 51941.35}, {'field': 'amount', 'old_value': 48700.86, 'new_value': 51940.090000000004}, {'field': 'count', 'old_value': 2940, 'new_value': 3124}, {'field': 'instoreAmount', 'old_value': 23455.46, 'new_value': 25098.93}, {'field': 'instoreCount', 'old_value': 1298, 'new_value': 1393}, {'field': 'onlineAmount', 'old_value': 26401.16, 'new_value': 28011.31}, {'field': 'onlineCount', 'old_value': 1642, 'new_value': 1731}]
2025-05-17 08:06:50,032 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-17 08:06:50,032 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 94016.26, 'new_value': 97940.56}, {'field': 'count', 'old_value': 917, 'new_value': 968}, {'field': 'instoreAmount', 'old_value': 94130.76, 'new_value': 98055.06}, {'field': 'instoreCount', 'old_value': 917, 'new_value': 968}]
2025-05-17 08:06:50,549 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-17 08:06:50,550 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80174.82, 'new_value': 85547.47}, {'field': 'dailyBillAmount', 'old_value': 82716.84, 'new_value': 88285.71}, {'field': 'amount', 'old_value': 80173.94, 'new_value': 85546.59}, {'field': 'count', 'old_value': 1543, 'new_value': 1655}, {'field': 'instoreAmount', 'old_value': 76826.58, 'new_value': 82059.76}, {'field': 'instoreCount', 'old_value': 1303, 'new_value': 1400}, {'field': 'onlineAmount', 'old_value': 3382.6, 'new_value': 3560.07}, {'field': 'onlineCount', 'old_value': 240, 'new_value': 255}]
2025-05-17 08:06:51,023 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-17 08:06:51,024 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 115053.42, 'new_value': 125532.17}, {'field': 'dailyBillAmount', 'old_value': 115053.42, 'new_value': 125532.17}, {'field': 'amount', 'old_value': 14969.85, 'new_value': 16304.02}, {'field': 'count', 'old_value': 577, 'new_value': 636}, {'field': 'instoreAmount', 'old_value': 17584.38, 'new_value': 19033.8}, {'field': 'instoreCount', 'old_value': 577, 'new_value': 636}]
2025-05-17 08:06:51,425 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-17 08:06:51,425 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 287965.39, 'new_value': 313274.29}, {'field': 'dailyBillAmount', 'old_value': 287965.39, 'new_value': 313274.29}, {'field': 'amount', 'old_value': 27299.7, 'new_value': 29730.9}, {'field': 'count', 'old_value': 132, 'new_value': 146}, {'field': 'instoreAmount', 'old_value': 27480.5, 'new_value': 29911.7}, {'field': 'instoreCount', 'old_value': 132, 'new_value': 146}]
2025-05-17 08:06:51,854 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-17 08:06:51,855 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 10725.88, 'new_value': 11575.02}, {'field': 'count', 'old_value': 549, 'new_value': 594}, {'field': 'onlineAmount', 'old_value': 10812.14, 'new_value': 11661.28}, {'field': 'onlineCount', 'old_value': 549, 'new_value': 594}]
2025-05-17 08:06:52,289 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-17 08:06:52,290 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 152912.15, 'new_value': 170438.02}, {'field': 'amount', 'old_value': 152758.37, 'new_value': 170284.24}, {'field': 'count', 'old_value': 1648, 'new_value': 1856}, {'field': 'instoreAmount', 'old_value': 144769.5, 'new_value': 160918.2}, {'field': 'instoreCount', 'old_value': 1400, 'new_value': 1560}, {'field': 'onlineAmount', 'old_value': 10048.04, 'new_value': 11669.26}, {'field': 'onlineCount', 'old_value': 248, 'new_value': 296}]
2025-05-17 08:06:52,765 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-17 08:06:52,766 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105281.53, 'new_value': 115858.97}, {'field': 'dailyBillAmount', 'old_value': 101481.06, 'new_value': 112058.5}, {'field': 'amount', 'old_value': 86732.24, 'new_value': 93500.22}, {'field': 'count', 'old_value': 3191, 'new_value': 3419}, {'field': 'instoreAmount', 'old_value': 41999.28, 'new_value': 44362.97}, {'field': 'instoreCount', 'old_value': 1460, 'new_value': 1570}, {'field': 'onlineAmount', 'old_value': 45760.840000000004, 'new_value': 50165.62}, {'field': 'onlineCount', 'old_value': 1731, 'new_value': 1849}]
2025-05-17 08:06:53,185 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-17 08:06:53,185 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26801.149999999998, 'new_value': 33123.049999999996}, {'field': 'dailyBillAmount', 'old_value': 26801.149999999998, 'new_value': 33123.049999999996}, {'field': 'amount', 'old_value': 1092.11, 'new_value': 1737.3}, {'field': 'count', 'old_value': 45, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 1092.11, 'new_value': 1737.3}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 81}]
2025-05-17 08:06:53,612 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-17 08:06:53,613 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 3863.02, 'new_value': 4100.64}, {'field': 'count', 'old_value': 166, 'new_value': 177}, {'field': 'onlineAmount', 'old_value': 3863.02, 'new_value': 4100.64}, {'field': 'onlineCount', 'old_value': 166, 'new_value': 177}]
2025-05-17 08:06:54,037 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-17 08:06:54,038 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64290.46, 'new_value': 68886.15}, {'field': 'dailyBillAmount', 'old_value': 33534.82, 'new_value': 35698.22}, {'field': 'amount', 'old_value': 64290.46, 'new_value': 68886.15}, {'field': 'count', 'old_value': 1575, 'new_value': 1691}, {'field': 'instoreAmount', 'old_value': 35585.340000000004, 'new_value': 37899.95}, {'field': 'instoreCount', 'old_value': 858, 'new_value': 916}, {'field': 'onlineAmount', 'old_value': 30342.9, 'new_value': 32623.98}, {'field': 'onlineCount', 'old_value': 717, 'new_value': 775}]
2025-05-17 08:06:54,497 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-17 08:06:54,497 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20099.77, 'new_value': 24019.18}, {'field': 'amount', 'old_value': 20099.77, 'new_value': 24019.18}, {'field': 'count', 'old_value': 749, 'new_value': 886}, {'field': 'instoreAmount', 'old_value': 20309.97, 'new_value': 24271.18}, {'field': 'instoreCount', 'old_value': 749, 'new_value': 886}]
2025-05-17 08:06:54,950 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-17 08:06:54,950 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30776.739999999998, 'new_value': 33547.0}, {'field': 'dailyBillAmount', 'old_value': 30776.739999999998, 'new_value': 33547.0}, {'field': 'amount', 'old_value': 26750.37, 'new_value': 28705.31}, {'field': 'count', 'old_value': 1159, 'new_value': 1238}, {'field': 'instoreAmount', 'old_value': 16382.02, 'new_value': 17534.11}, {'field': 'instoreCount', 'old_value': 561, 'new_value': 592}, {'field': 'onlineAmount', 'old_value': 10393.46, 'new_value': 11196.31}, {'field': 'onlineCount', 'old_value': 598, 'new_value': 646}]
2025-05-17 08:06:55,377 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-17 08:06:55,377 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52753.81, 'new_value': 56856.55}, {'field': 'amount', 'old_value': 52753.81, 'new_value': 56856.55}, {'field': 'count', 'old_value': 1541, 'new_value': 1675}, {'field': 'instoreAmount', 'old_value': 21830.75, 'new_value': 23076.18}, {'field': 'instoreCount', 'old_value': 747, 'new_value': 808}, {'field': 'onlineAmount', 'old_value': 30956.06, 'new_value': 33813.37}, {'field': 'onlineCount', 'old_value': 794, 'new_value': 867}]
2025-05-17 08:06:55,804 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-17 08:06:55,804 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32706.3, 'new_value': 34009.39}, {'field': 'amount', 'old_value': 32705.4, 'new_value': 34008.49}, {'field': 'count', 'old_value': 749, 'new_value': 788}, {'field': 'instoreAmount', 'old_value': 26409.5, 'new_value': 27381.0}, {'field': 'instoreCount', 'old_value': 609, 'new_value': 638}, {'field': 'onlineAmount', 'old_value': 6629.33, 'new_value': 6960.92}, {'field': 'onlineCount', 'old_value': 140, 'new_value': 150}]
2025-05-17 08:06:56,215 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-17 08:06:56,216 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 149873.59, 'new_value': 159179.31}, {'field': 'dailyBillAmount', 'old_value': 149873.59, 'new_value': 159179.31}, {'field': 'amount', 'old_value': 98997.59, 'new_value': 104876.81999999999}, {'field': 'count', 'old_value': 2458, 'new_value': 2609}, {'field': 'instoreAmount', 'old_value': 64108.12, 'new_value': 67359.32}, {'field': 'instoreCount', 'old_value': 1258, 'new_value': 1324}, {'field': 'onlineAmount', 'old_value': 42541.19, 'new_value': 45759.28}, {'field': 'onlineCount', 'old_value': 1200, 'new_value': 1285}]
2025-05-17 08:06:56,642 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-17 08:06:56,642 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 437097.46, 'new_value': 463860.85}, {'field': 'dailyBillAmount', 'old_value': 437097.46, 'new_value': 463860.85}, {'field': 'amount', 'old_value': 413328.1, 'new_value': 430852.8}, {'field': 'count', 'old_value': 2406, 'new_value': 2526}, {'field': 'instoreAmount', 'old_value': 306892.1, 'new_value': 316466.1}, {'field': 'instoreCount', 'old_value': 1912, 'new_value': 1996}, {'field': 'onlineAmount', 'old_value': 106437.9, 'new_value': 114388.6}, {'field': 'onlineCount', 'old_value': 494, 'new_value': 530}]
2025-05-17 08:06:57,109 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-17 08:06:57,110 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 647790.48, 'new_value': 689511.63}, {'field': 'amount', 'old_value': 647789.98, 'new_value': 689511.13}, {'field': 'count', 'old_value': 2244, 'new_value': 2383}, {'field': 'instoreAmount', 'old_value': 647790.48, 'new_value': 689511.63}, {'field': 'instoreCount', 'old_value': 2244, 'new_value': 2383}]
2025-05-17 08:06:57,602 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-17 08:06:57,602 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 376301.33999999997, 'new_value': 404428.18}, {'field': 'dailyBillAmount', 'old_value': 334836.8, 'new_value': 361155.64}, {'field': 'amount', 'old_value': 376301.34, 'new_value': 404428.18}, {'field': 'count', 'old_value': 2290, 'new_value': 2454}, {'field': 'instoreAmount', 'old_value': 342036.41000000003, 'new_value': 368479.65}, {'field': 'instoreCount', 'old_value': 1451, 'new_value': 1560}, {'field': 'onlineAmount', 'old_value': 34481.58, 'new_value': 36165.18}, {'field': 'onlineCount', 'old_value': 839, 'new_value': 894}]
2025-05-17 08:06:58,043 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-17 08:06:58,044 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 402887.35000000003, 'new_value': 419447.18}, {'field': 'dailyBillAmount', 'old_value': 387125.2, 'new_value': 400240.36}, {'field': 'amount', 'old_value': 402887.35000000003, 'new_value': 419447.18}, {'field': 'count', 'old_value': 880, 'new_value': 936}, {'field': 'instoreAmount', 'old_value': 380540.7, 'new_value': 394988.7}, {'field': 'instoreCount', 'old_value': 693, 'new_value': 734}, {'field': 'onlineAmount', 'old_value': 22473.93, 'new_value': 24585.760000000002}, {'field': 'onlineCount', 'old_value': 187, 'new_value': 202}]
2025-05-17 08:06:58,473 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-17 08:06:58,474 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 428043.13, 'new_value': 460318.83999999997}, {'field': 'amount', 'old_value': 428043.13, 'new_value': 460318.84}, {'field': 'count', 'old_value': 2079, 'new_value': 2252}, {'field': 'instoreAmount', 'old_value': 405637.2, 'new_value': 436372.79}, {'field': 'instoreCount', 'old_value': 1443, 'new_value': 1575}, {'field': 'onlineAmount', 'old_value': 22451.5, 'new_value': 23991.62}, {'field': 'onlineCount', 'old_value': 636, 'new_value': 677}]
2025-05-17 08:06:58,910 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-17 08:06:58,910 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 507240.66000000003, 'new_value': 548102.41}, {'field': 'dailyBillAmount', 'old_value': 507240.66000000003, 'new_value': 548102.41}, {'field': 'amount', 'old_value': 453829.28, 'new_value': 495620.54}, {'field': 'count', 'old_value': 2200, 'new_value': 2406}, {'field': 'instoreAmount', 'old_value': 419417.61, 'new_value': 457215.88}, {'field': 'instoreCount', 'old_value': 1850, 'new_value': 2016}, {'field': 'onlineAmount', 'old_value': 34666.54, 'new_value': 38721.94}, {'field': 'onlineCount', 'old_value': 350, 'new_value': 390}]
2025-05-17 08:06:59,392 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-17 08:06:59,393 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95365.44, 'new_value': 116686.44}, {'field': 'dailyBillAmount', 'old_value': 94020.44, 'new_value': 115279.89}, {'field': 'amount', 'old_value': 93205.86, 'new_value': 114526.86}, {'field': 'count', 'old_value': 169, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 93205.86, 'new_value': 114526.86}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 188}]
2025-05-17 08:06:59,929 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-17 08:06:59,930 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95210.78, 'new_value': 103205.28}, {'field': 'dailyBillAmount', 'old_value': 95210.78, 'new_value': 103205.28}, {'field': 'amount', 'old_value': 79526.52, 'new_value': 86632.52}, {'field': 'count', 'old_value': 142, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 77605.2, 'new_value': 84711.2}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 143}]
2025-05-17 08:07:00,442 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-17 08:07:00,442 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14224.23, 'new_value': 14864.63}, {'field': 'amount', 'old_value': 14224.23, 'new_value': 14864.63}, {'field': 'count', 'old_value': 292, 'new_value': 305}, {'field': 'instoreAmount', 'old_value': 14224.23, 'new_value': 14864.63}, {'field': 'instoreCount', 'old_value': 292, 'new_value': 305}]
2025-05-17 08:07:01,019 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-17 08:07:01,020 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57074.5, 'new_value': 62205.95}, {'field': 'amount', 'old_value': 57074.5, 'new_value': 62205.95}, {'field': 'count', 'old_value': 471, 'new_value': 518}, {'field': 'instoreAmount', 'old_value': 57177.22, 'new_value': 62390.51}, {'field': 'instoreCount', 'old_value': 471, 'new_value': 518}]
2025-05-17 08:07:01,482 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-17 08:07:01,482 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 176502.19, 'new_value': 191796.65}, {'field': 'dailyBillAmount', 'old_value': 176502.19, 'new_value': 191796.65}, {'field': 'amount', 'old_value': 195486.03, 'new_value': 210659.19}, {'field': 'count', 'old_value': 5048, 'new_value': 5518}, {'field': 'instoreAmount', 'old_value': 185735.82, 'new_value': 199933.82}, {'field': 'instoreCount', 'old_value': 4551, 'new_value': 4988}, {'field': 'onlineAmount', 'old_value': 13100.5, 'new_value': 14213.91}, {'field': 'onlineCount', 'old_value': 497, 'new_value': 530}]
2025-05-17 08:07:01,982 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-17 08:07:01,983 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52308.82, 'new_value': 55950.86}, {'field': 'dailyBillAmount', 'old_value': 52308.82, 'new_value': 55950.86}, {'field': 'amount', 'old_value': 52889.82, 'new_value': 56549.86}, {'field': 'count', 'old_value': 48, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 52889.82, 'new_value': 56549.86}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 52}]
2025-05-17 08:07:02,390 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-17 08:07:02,391 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 501626.02, 'new_value': 536742.82}, {'field': 'dailyBillAmount', 'old_value': 501626.02, 'new_value': 536742.82}, {'field': 'amount', 'old_value': 454822.15, 'new_value': 490204.55}, {'field': 'count', 'old_value': 1170, 'new_value': 1245}, {'field': 'instoreAmount', 'old_value': 474194.46, 'new_value': 509311.26}, {'field': 'instoreCount', 'old_value': 974, 'new_value': 1034}, {'field': 'onlineAmount', 'old_value': 4372.22, 'new_value': 4637.82}, {'field': 'onlineCount', 'old_value': 196, 'new_value': 211}]
2025-05-17 08:07:02,796 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-17 08:07:02,796 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 742834.46, 'new_value': 796959.15}, {'field': 'amount', 'old_value': 742834.46, 'new_value': 796959.15}, {'field': 'count', 'old_value': 2401, 'new_value': 2566}, {'field': 'instoreAmount', 'old_value': 744045.46, 'new_value': 798170.15}, {'field': 'instoreCount', 'old_value': 2401, 'new_value': 2566}]
2025-05-17 08:07:03,233 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-17 08:07:03,234 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 500011.58999999997, 'new_value': 595413.47}, {'field': 'dailyBillAmount', 'old_value': 500011.58999999997, 'new_value': 595413.47}, {'field': 'amount', 'old_value': 437439.76, 'new_value': 479024.47}, {'field': 'count', 'old_value': 1643, 'new_value': 1779}, {'field': 'instoreAmount', 'old_value': 425915.71, 'new_value': 466615.42}, {'field': 'instoreCount', 'old_value': 961, 'new_value': 1051}, {'field': 'onlineAmount', 'old_value': 20464.420000000002, 'new_value': 21843.32}, {'field': 'onlineCount', 'old_value': 682, 'new_value': 728}]
2025-05-17 08:07:03,789 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-17 08:07:03,789 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1135090.29, 'new_value': 1206442.29}, {'field': 'dailyBillAmount', 'old_value': 1135090.29, 'new_value': 1206442.29}, {'field': 'amount', 'old_value': 1176566.0, 'new_value': 1249110.0}, {'field': 'count', 'old_value': 3403, 'new_value': 3567}, {'field': 'instoreAmount', 'old_value': 1176566.0, 'new_value': 1249110.0}, {'field': 'instoreCount', 'old_value': 3403, 'new_value': 3567}]
2025-05-17 08:07:04,264 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-17 08:07:04,264 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174794.69, 'new_value': 186845.64}, {'field': 'dailyBillAmount', 'old_value': 174794.69, 'new_value': 186845.64}, {'field': 'amount', 'old_value': 172841.32, 'new_value': 184898.27}, {'field': 'count', 'old_value': 911, 'new_value': 999}, {'field': 'instoreAmount', 'old_value': 167149.6, 'new_value': 178522.8}, {'field': 'instoreCount', 'old_value': 758, 'new_value': 834}, {'field': 'onlineAmount', 'old_value': 9200.09, 'new_value': 9883.84}, {'field': 'onlineCount', 'old_value': 153, 'new_value': 165}]
2025-05-17 08:07:04,687 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-17 08:07:04,687 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 579239.97, 'new_value': 623236.66}, {'field': 'dailyBillAmount', 'old_value': 579239.97, 'new_value': 623236.66}, {'field': 'amount', 'old_value': 626161.7000000001, 'new_value': 670158.39}, {'field': 'count', 'old_value': 2533, 'new_value': 2743}, {'field': 'instoreAmount', 'old_value': 626162.15, 'new_value': 670158.84}, {'field': 'instoreCount', 'old_value': 2533, 'new_value': 2743}]
2025-05-17 08:07:05,232 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-17 08:07:05,232 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 247650.34, 'new_value': 264552.14}, {'field': 'dailyBillAmount', 'old_value': 247650.34, 'new_value': 264552.14}, {'field': 'amount', 'old_value': 403265.34, 'new_value': 433989.3}, {'field': 'count', 'old_value': 669, 'new_value': 728}, {'field': 'instoreAmount', 'old_value': 400361.78, 'new_value': 430935.78}, {'field': 'instoreCount', 'old_value': 646, 'new_value': 703}, {'field': 'onlineAmount', 'old_value': 3169.0, 'new_value': 3319.0}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 25}]
2025-05-17 08:07:05,687 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-17 08:07:05,688 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146888.87, 'new_value': 153164.5}, {'field': 'dailyBillAmount', 'old_value': 146888.87, 'new_value': 153164.5}, {'field': 'amount', 'old_value': 176557.3, 'new_value': 182388.3}, {'field': 'count', 'old_value': 1233, 'new_value': 1279}, {'field': 'instoreAmount', 'old_value': 179143.3, 'new_value': 185103.3}, {'field': 'instoreCount', 'old_value': 1233, 'new_value': 1279}]
2025-05-17 08:07:06,103 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-17 08:07:06,103 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78890.44, 'new_value': 88892.57}, {'field': 'dailyBillAmount', 'old_value': 78890.44, 'new_value': 88892.57}, {'field': 'amount', 'old_value': 57125.96, 'new_value': 66488.45999999999}, {'field': 'count', 'old_value': 374, 'new_value': 427}, {'field': 'instoreAmount', 'old_value': 56376.0, 'new_value': 65777.0}, {'field': 'instoreCount', 'old_value': 334, 'new_value': 386}, {'field': 'onlineAmount', 'old_value': 1890.96, 'new_value': 1894.46}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 41}]
2025-05-17 08:07:06,583 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-17 08:07:06,584 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 91301.39, 'new_value': 100082.5}, {'field': 'count', 'old_value': 4306, 'new_value': 4738}, {'field': 'instoreAmount', 'old_value': 48902.09, 'new_value': 53663.74}, {'field': 'instoreCount', 'old_value': 2454, 'new_value': 2740}, {'field': 'onlineAmount', 'old_value': 45166.89, 'new_value': 49370.83}, {'field': 'onlineCount', 'old_value': 1852, 'new_value': 1998}]
2025-05-17 08:07:07,033 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-17 08:07:07,033 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 127902.36, 'new_value': 134784.27}, {'field': 'amount', 'old_value': 127898.55, 'new_value': 134780.16}, {'field': 'count', 'old_value': 2326, 'new_value': 2483}, {'field': 'instoreAmount', 'old_value': 121811.84, 'new_value': 128213.38}, {'field': 'instoreCount', 'old_value': 2211, 'new_value': 2356}, {'field': 'onlineAmount', 'old_value': 6090.52, 'new_value': 6570.89}, {'field': 'onlineCount', 'old_value': 115, 'new_value': 127}]
2025-05-17 08:07:07,465 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-17 08:07:07,465 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18949.4, 'new_value': 19474.0}, {'field': 'amount', 'old_value': 18949.4, 'new_value': 19474.0}, {'field': 'count', 'old_value': 128, 'new_value': 136}, {'field': 'instoreAmount', 'old_value': 18949.4, 'new_value': 19474.0}, {'field': 'instoreCount', 'old_value': 128, 'new_value': 136}]
2025-05-17 08:07:07,897 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-17 08:07:07,898 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 9285.3, 'new_value': 9385.3}, {'field': 'dailyBillAmount', 'old_value': 9285.3, 'new_value': 9385.3}, {'field': 'amount', 'old_value': 31719.2, 'new_value': 32846.1}, {'field': 'count', 'old_value': 281, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 31938.5, 'new_value': 33065.4}, {'field': 'instoreCount', 'old_value': 281, 'new_value': 297}]
2025-05-17 08:07:08,345 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-17 08:07:08,345 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 28116.0, 'new_value': 31721.0}, {'field': 'dailyBillAmount', 'old_value': 28116.0, 'new_value': 31721.0}]
2025-05-17 08:07:08,753 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-17 08:07:08,754 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82618.4, 'new_value': 93934.5}, {'field': 'dailyBillAmount', 'old_value': 82618.4, 'new_value': 93934.5}, {'field': 'amount', 'old_value': 98509.84999999999, 'new_value': 105316.87}, {'field': 'count', 'old_value': 3030, 'new_value': 3220}, {'field': 'instoreAmount', 'old_value': 95429.05, 'new_value': 102251.37}, {'field': 'instoreCount', 'old_value': 2906, 'new_value': 3090}, {'field': 'onlineAmount', 'old_value': 4505.22, 'new_value': 4883.82}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 130}]
2025-05-17 08:07:09,151 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-17 08:07:09,151 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29845.7, 'new_value': 31496.8}, {'field': 'dailyBillAmount', 'old_value': 29845.7, 'new_value': 31496.8}, {'field': 'amount', 'old_value': 29603.3, 'new_value': 31254.4}, {'field': 'count', 'old_value': 168, 'new_value': 178}, {'field': 'instoreAmount', 'old_value': 31388.5, 'new_value': 33039.6}, {'field': 'instoreCount', 'old_value': 168, 'new_value': 178}]
2025-05-17 08:07:09,581 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-17 08:07:09,581 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 41132.82, 'new_value': 44024.13}, {'field': 'dailyBillAmount', 'old_value': 41132.82, 'new_value': 44024.13}]
2025-05-17 08:07:09,969 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-17 08:07:09,969 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 29444.370000000003, 'new_value': 31797.54}, {'field': 'amount', 'old_value': 29444.37, 'new_value': 31797.54}, {'field': 'count', 'old_value': 1677, 'new_value': 1816}, {'field': 'instoreAmount', 'old_value': 29940.14, 'new_value': 32332.59}, {'field': 'instoreCount', 'old_value': 1677, 'new_value': 1816}]
2025-05-17 08:07:10,418 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-17 08:07:10,419 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46931.15, 'new_value': 50426.01}, {'field': 'dailyBillAmount', 'old_value': 46931.15, 'new_value': 50426.01}, {'field': 'amount', 'old_value': 48280.13, 'new_value': 51921.93}, {'field': 'count', 'old_value': 2331, 'new_value': 2526}, {'field': 'instoreAmount', 'old_value': 44958.1, 'new_value': 48415.4}, {'field': 'instoreCount', 'old_value': 2192, 'new_value': 2377}, {'field': 'onlineAmount', 'old_value': 3354.12, 'new_value': 3571.7799999999997}, {'field': 'onlineCount', 'old_value': 139, 'new_value': 149}]
2025-05-17 08:07:10,910 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-17 08:07:10,910 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33049.23, 'new_value': 35538.9}, {'field': 'amount', 'old_value': 33049.23, 'new_value': 35538.9}, {'field': 'count', 'old_value': 1591, 'new_value': 1720}, {'field': 'instoreAmount', 'old_value': 21068.78, 'new_value': 22558.19}, {'field': 'instoreCount', 'old_value': 1062, 'new_value': 1155}, {'field': 'onlineAmount', 'old_value': 12041.45, 'new_value': 13041.71}, {'field': 'onlineCount', 'old_value': 529, 'new_value': 565}]
2025-05-17 08:07:11,356 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-17 08:07:11,357 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23427.1, 'new_value': 25232.98}, {'field': 'dailyBillAmount', 'old_value': 23427.1, 'new_value': 25232.98}, {'field': 'amount', 'old_value': 16665.03, 'new_value': 17987.93}, {'field': 'count', 'old_value': 674, 'new_value': 727}, {'field': 'instoreAmount', 'old_value': 16854.43, 'new_value': 18177.33}, {'field': 'instoreCount', 'old_value': 674, 'new_value': 727}]
2025-05-17 08:07:11,792 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-17 08:07:11,793 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43565.31, 'new_value': 46459.05}, {'field': 'amount', 'old_value': 43560.34, 'new_value': 46454.08}, {'field': 'count', 'old_value': 2651, 'new_value': 2867}, {'field': 'instoreAmount', 'old_value': 10563.75, 'new_value': 11525.19}, {'field': 'instoreCount', 'old_value': 617, 'new_value': 726}, {'field': 'onlineAmount', 'old_value': 33993.96, 'new_value': 35934.86}, {'field': 'onlineCount', 'old_value': 2034, 'new_value': 2141}]
2025-05-17 08:07:12,231 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-17 08:07:12,232 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 78348.71, 'new_value': 83109.42}, {'field': 'dailyBillAmount', 'old_value': 78348.71, 'new_value': 83109.42}, {'field': 'amount', 'old_value': 65301.04, 'new_value': 69297.24}, {'field': 'count', 'old_value': 652, 'new_value': 688}, {'field': 'instoreAmount', 'old_value': 65301.04, 'new_value': 69297.24}, {'field': 'instoreCount', 'old_value': 652, 'new_value': 688}]
2025-05-17 08:07:12,692 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-17 08:07:12,693 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59520.89, 'new_value': 64452.89}, {'field': 'dailyBillAmount', 'old_value': 59520.89, 'new_value': 64452.89}, {'field': 'amount', 'old_value': 64921.8, 'new_value': 70556.8}, {'field': 'count', 'old_value': 275, 'new_value': 298}, {'field': 'instoreAmount', 'old_value': 64921.8, 'new_value': 70556.8}, {'field': 'instoreCount', 'old_value': 275, 'new_value': 298}]
2025-05-17 08:07:13,131 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-17 08:07:13,132 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45198.7, 'new_value': 49463.7}, {'field': 'dailyBillAmount', 'old_value': 45198.7, 'new_value': 49463.7}, {'field': 'amount', 'old_value': 37456.97, 'new_value': 40232.31}, {'field': 'count', 'old_value': 199, 'new_value': 212}, {'field': 'instoreAmount', 'old_value': 38199.97, 'new_value': 41502.31}, {'field': 'instoreCount', 'old_value': 199, 'new_value': 212}]
2025-05-17 08:07:13,544 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-17 08:07:13,544 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82029.0, 'new_value': 87003.0}, {'field': 'amount', 'old_value': 82029.0, 'new_value': 87003.0}, {'field': 'count', 'old_value': 857, 'new_value': 910}, {'field': 'instoreAmount', 'old_value': 82029.0, 'new_value': 87003.0}, {'field': 'instoreCount', 'old_value': 857, 'new_value': 910}]
2025-05-17 08:07:13,998 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-17 08:07:13,998 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17046.86, 'new_value': 18989.33}, {'field': 'dailyBillAmount', 'old_value': 17046.86, 'new_value': 18989.33}, {'field': 'amount', 'old_value': 1922.62, 'new_value': 2005.6299999999999}, {'field': 'count', 'old_value': 101, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 2303.51, 'new_value': 2418.31}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 115}]
2025-05-17 08:07:14,501 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-17 08:07:14,501 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 14035.7, 'new_value': 15294.76}, {'field': 'dailyBillAmount', 'old_value': 14035.7, 'new_value': 15294.76}, {'field': 'amount', 'old_value': 14785.630000000001, 'new_value': 16044.69}, {'field': 'count', 'old_value': 395, 'new_value': 426}, {'field': 'instoreAmount', 'old_value': 14827.77, 'new_value': 16086.83}, {'field': 'instoreCount', 'old_value': 394, 'new_value': 425}]
2025-05-17 08:07:14,980 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-17 08:07:14,981 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32302.1, 'new_value': 33948.2}, {'field': 'dailyBillAmount', 'old_value': 32302.1, 'new_value': 33948.2}, {'field': 'amount', 'old_value': 43748.7, 'new_value': 45912.6}, {'field': 'count', 'old_value': 173, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 44000.7, 'new_value': 46164.6}, {'field': 'instoreCount', 'old_value': 173, 'new_value': 181}]
2025-05-17 08:07:15,396 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-17 08:07:15,397 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 26298.0, 'new_value': 27445.0}, {'field': 'dailyBillAmount', 'old_value': 26298.0, 'new_value': 27445.0}, {'field': 'amount', 'old_value': 29821.0, 'new_value': 30943.0}, {'field': 'count', 'old_value': 150, 'new_value': 157}, {'field': 'instoreAmount', 'old_value': 29835.0, 'new_value': 30957.0}, {'field': 'instoreCount', 'old_value': 150, 'new_value': 157}]
2025-05-17 08:07:15,829 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-17 08:07:15,830 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46625.48, 'new_value': 49930.03}, {'field': 'dailyBillAmount', 'old_value': 46625.48, 'new_value': 49930.03}, {'field': 'amount', 'old_value': 40931.32, 'new_value': 44004.78}, {'field': 'count', 'old_value': 1388, 'new_value': 1495}, {'field': 'instoreAmount', 'old_value': 37457.36, 'new_value': 40319.92}, {'field': 'instoreCount', 'old_value': 1227, 'new_value': 1324}, {'field': 'onlineAmount', 'old_value': 3510.4, 'new_value': 3721.2999999999997}, {'field': 'onlineCount', 'old_value': 161, 'new_value': 171}]
2025-05-17 08:07:16,401 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-17 08:07:16,401 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'amount', 'old_value': 18375.01, 'new_value': 21477.81}, {'field': 'count', 'old_value': 111, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 18439.61, 'new_value': 21542.41}, {'field': 'instoreCount', 'old_value': 108, 'new_value': 126}]
2025-05-17 08:07:16,984 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-17 08:07:16,984 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122304.22, 'new_value': 132996.78}, {'field': 'dailyBillAmount', 'old_value': 122304.22, 'new_value': 132996.78}, {'field': 'amount', 'old_value': 128391.3, 'new_value': 139504.9}, {'field': 'count', 'old_value': 918, 'new_value': 963}, {'field': 'instoreAmount', 'old_value': 122912.7, 'new_value': 133877.7}, {'field': 'instoreCount', 'old_value': 818, 'new_value': 860}, {'field': 'onlineAmount', 'old_value': 6350.6, 'new_value': 6602.2}, {'field': 'onlineCount', 'old_value': 100, 'new_value': 103}]
2025-05-17 08:07:16,985 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-17 08:07:16,985 - INFO - 正在批量插入月度数据，批次 1/1，共 1 条记录
2025-05-17 08:07:17,127 - INFO - 批量插入月度数据成功，批次 1，1 条记录
2025-05-17 08:07:20,128 - INFO - 批量插入月度数据完成: 总计 1 条，成功 1 条，失败 0 条
2025-05-17 08:07:20,128 - INFO - 批量插入月销售数据完成，共 1 条记录
2025-05-17 08:07:20,129 - INFO - 月销售数据同步完成！更新: 208 条，插入: 1 条，错误: 0 条，跳过: 980 条
2025-05-17 08:07:20,129 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-17 08:07:20,686 - INFO - 月度汇总数据已导出到Excel文件: 数衍平台月度数据_20250517.xlsx
2025-05-17 08:07:20,687 - INFO - 综合数据同步流程完成！
2025-05-17 08:07:20,734 - INFO - 综合数据同步完成
2025-05-17 08:07:20,734 - INFO - ==================================================
2025-05-17 08:07:20,735 - INFO - 程序退出
2025-05-17 08:07:20,735 - INFO - ==================================================
