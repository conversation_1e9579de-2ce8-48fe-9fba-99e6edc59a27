# MySQL与宜搭数据同步通用框架

## 项目概述

这是一个通用的MySQL与宜搭数据同步框架，支持新增、更新场景的数据同步。通过配置文件驱动，可以快速配置不同的数据同步需求，无需修改代码即可适应不同的业务场景。

## 主要特性

- ✅ **配置驱动**: 通过JSON配置文件定义所有同步参数
- ✅ **智能对比**: 基于键字段自动识别新增和更新数据
- ✅ **批量处理**: 支持批量新增和逐条更新，提高同步效率
- ✅ **完善日志**: 详细的同步过程日志，便于问题排查
- ✅ **错误处理**: 完善的异常处理机制，确保同步稳定性
- ✅ **类型转换**: 自动处理日期、数值等字段类型转换
- ✅ **多种启动方式**: 提供命令行、交互式、快速启动等多种使用方式

## 快速开始

### 1. 环境准备
确保已安装必要的依赖包：
```bash
pip install pymysql alibabacloud-dingtalk
```

### 2. 创建配置文件
```bash
# 创建配置文件模板
python quick_start.py --create-config my_config.json
```

### 3. 修改配置文件
根据实际需求修改 `my_config.json` 中的配置参数。

### 4. 执行同步
```bash
# 使用自定义配置同步
python quick_start.py --config my_config.json

# 或使用预设配置
python quick_start.py --type sales    # 销售数据同步
python quick_start.py --type device   # 设备数据同步
```

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `mysql2yida_sync_framework.py` | 主框架文件，包含所有核心功能 |
| `quick_start.py` | 快速启动脚本，支持命令行参数 |
| `example_usage.py` | 交互式使用示例 |
| `test_sync_framework.py` | 框架功能测试脚本 |
| `sync_config_example.json` | 销售数据同步配置示例 |
| `sync_config_devices.json` | 设备数据同步配置示例 |
| `MySQL2Yida同步框架使用说明.md` | 详细使用说明文档 |

## 配置文件结构

```json
{
  "db_config": {
    "host": "数据库地址",
    "port": 3306,
    "user": "用户名",
    "password": "密码",
    "database": "数据库名",
    "charset": "utf8mb4"
  },
  "yida_config": {
    "APP_TYPE": "宜搭应用类型",
    "SYSTEM_TOKEN": "系统令牌",
    "USER_ID": "用户ID",
    "LANGUAGE": "zh_CN",
    "FORM_UUID": "表单UUID"
  },
  "sql_query": "SELECT语句",
  "field_mapping": {
    "mysql_field": "yida_field_id"
  },
  "key_fields": ["用于对比的键字段"],
  "compare_fields": ["需要比较更新的字段"]
}
```

## 使用场景

### 场景1：销售数据同步
- 从MySQL销售记录表同步数据到宜搭销售报表
- 支持按项目、店铺、日期等维度汇总
- 自动识别新增和更新的销售数据

### 场景2：设备数据同步
- 从MySQL设备表同步设备信息到宜搭设备管理表单
- 支持设备状态、类型等信息的实时同步
- 基于设备ID和店铺ID进行数据对比

### 场景3：自定义业务数据同步
- 通过配置文件快速适配其他业务场景
- 支持复杂的SQL查询和字段映射
- 灵活的数据对比和更新策略

## 同步流程

1. **加载配置** → 读取配置文件或使用默认配置
2. **连接数据库** → 建立MySQL和宜搭的连接
3. **获取数据** → 从MySQL和宜搭分别获取数据
4. **生成键值** → 基于键字段生成唯一标识
5. **数据对比** → 比较两边数据找出差异
6. **分类处理** → 区分新增和更新数据
7. **执行同步** → 批量新增 + 逐条更新
8. **记录日志** → 详细记录同步结果

## 测试验证

运行测试脚本验证框架功能：
```bash
python test_sync_framework.py
```

测试内容包括：
- 配置文件创建和加载
- 数据键生成功能
- MySQL到宜搭数据格式转换
- 数据差异比较功能
- 错误处理机制

## 注意事项

1. **数据库权限**: 确保MySQL用户有相应表的读取权限
2. **宜搭权限**: 确保宜搭用户有表单的读写权限
3. **字段映射**: 宜搭字段ID需要从表单设计器中获取
4. **数据类型**: 日期字段会自动转换为时间戳格式
5. **批量大小**: 可根据数据量调整批处理大小
6. **网络稳定**: 确保网络连接稳定，避免同步中断

## 扩展开发

### 自定义数据处理
继承 `MySQL2YidaSyncClient` 类，重写相关方法：
```python
class CustomSyncClient(MySQL2YidaSyncClient):
    def _convert_mysql_to_yida_format(self, mysql_data):
        # 自定义数据转换逻辑
        pass
```

### 多表单同步
创建多个配置文件，分别对应不同的表单：
```bash
python quick_start.py --config table1_config.json
python quick_start.py --config table2_config.json
```

### 定时同步
结合系统定时任务实现自动同步：
```bash
# Linux crontab示例
0 */6 * * * cd /path/to/project && python quick_start.py --type sales
```

## 常见问题

**Q: 连接数据库失败怎么办？**
A: 检查数据库配置信息，确认网络连通性和用户权限。

**Q: 宜搭字段ID如何获取？**
A: 在宜搭表单设计器中，选择字段后在右侧属性面板可以看到字段ID。

**Q: 如何处理大量数据同步？**
A: 可以调整批处理大小，或者在SQL查询中添加时间范围限制。

**Q: 同步失败如何排查？**
A: 查看logs目录下的日志文件，包含详细的错误信息和同步过程。

## 技术支持

如有问题或建议，请查看详细文档：`MySQL2Yida同步框架使用说明.md`
