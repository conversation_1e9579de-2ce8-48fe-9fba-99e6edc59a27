# 设备信息获取脚本 - 简化版

## 功能概述

本脚本已简化为直接获取设备信息并保存到MySQL数据库，无需用户交互选择。

## 主要功能

- ✅ 自动获取所有店铺的设备信息
- ✅ 自动保存到MySQL数据库
- ✅ 支持增量更新（避免重复数据）
- ✅ 完整的错误处理和日志记录
- ✅ 实时显示处理进度

## 数据库配置

在 `get_shuyan_devices.py` 文件中修改数据库配置：

```python
DB_CONFIG = {
    'host': 'localhost',      # 数据库主机地址
    'port': 3306,            # 数据库端口
    'user': 'root',          # 数据库用户名
    'password': '123456',    # 数据库密码
    'database': 'yida_devices', # 数据库名称
    'charset': 'utf8mb4'     # 字符集
}
```

## 使用方法

### 1. 安装依赖

```bash
pip install pymysql requests
```

### 2. 运行脚本

```bash
python get_shuyan_devices.py
```

### 3. 运行流程

脚本会自动执行以下步骤：

1. **测试数据库连接** - 验证数据库连接是否正常
2. **获取设备信息** - 逐个获取每个店铺的设备数据
3. **保存到数据库** - 实时保存每个店铺的数据
4. **显示统计信息** - 显示最终保存的记录数量

## 输出示例

```
正在测试数据库连接...
数据库连接成功
开始获取 7 个店铺的设备信息并保存到数据库...
使用字段映射配置进行数据转换

[1/7] 正在获取广州维多利广场的设备信息...
成功获取到 15 条设备信息
成功插入/更新 15 条设备记录到数据库
等待2秒后继续下一个店铺...

[2/7] 正在获取武汉国金天地的设备信息...
成功获取到 12 条设备信息
成功插入/更新 12 条设备记录到数据库
等待2秒后继续下一个店铺...

...

所有店铺数据获取完成，共保存 89 条设备记录到数据库
数据同步完成！
```

## 数据库表结构

设备信息保存在 `device_info` 表中，包含以下字段：

- `id` - 主键ID
- `project_name` - 项目名称
- `shop_entity_id` - 店铺实体ID
- `device_id` - 设备标识
- `device_type` - 设备类型描述
- `device_type_code` - 设备类型代码
- `device_state` - 设备状态描述
- `device_state_code` - 设备状态代码
- `is_deleted` - 删除状态描述
- `is_deleted_code` - 删除状态代码
- `install_date` - 安装日期
- `sync_time` - 数据同步时间

## 数据更新机制

- 使用 `ON DUPLICATE KEY UPDATE` 语句
- 基于设备ID和店铺ID的唯一约束
- 支持增量更新，避免重复数据
- 每次更新会刷新 `sync_time` 字段

## 日志记录

所有操作日志记录在 `api_call_log_YYYYMMDD.txt` 文件中，包括：
- API调用信息
- 数据库操作结果
- 错误信息

## 注意事项

1. 确保MySQL数据库服务正在运行
2. 确保数据库连接配置正确
3. 确保 `device_info` 表已存在
4. 脚本会自动处理重复数据，无需手动清理

## 故障排除

### 数据库连接失败
- 检查数据库服务是否启动
- 验证连接配置是否正确
- 确认数据库用户权限

### API调用失败
- 检查网络连接
- 验证API密钥是否有效
- 查看日志文件获取详细错误信息

## 查询示例

```sql
-- 查看所有设备
SELECT * FROM device_info ORDER BY sync_time DESC;

-- 按项目统计设备数量
SELECT project_name, COUNT(*) as device_count 
FROM device_info 
GROUP BY project_name;

-- 查看特定状态的设备
SELECT * FROM device_info WHERE device_state = '在用';
``` 