# -*- coding: utf-8 -*-
"""
MySQL与宜搭数据同步框架测试脚本
"""
import os
import sys
import logging
from mysql2yida_sync_framework import SyncConfig

def test_config_creation():
    """测试配置文件创建"""
    print("=== 测试配置文件创建 ===")
    
    # 创建默认配置
    config = SyncConfig()
    
    # 保存配置文件
    test_config_file = "test_config.json"
    config.save_to_file(test_config_file)
    
    if os.path.exists(test_config_file):
        print("✓ 配置文件创建成功")
        
        # 测试从文件加载配置
        config2 = SyncConfig(test_config_file)
        print("✓ 配置文件加载成功")
        
        # 清理测试文件
        os.remove(test_config_file)
        print("✓ 测试文件清理完成")
    else:
        print("✗ 配置文件创建失败")

def test_key_generation():
    """测试键生成功能"""
    print("\n=== 测试键生成功能 ===")

    # 创建一个测试用的同步客户端类，不需要实际连接数据库
    class TestSyncClient:
        def __init__(self, config):
            self.config = config

        def _generate_key(self, data: dict, is_mysql_data: bool = True) -> str:
            """生成数据记录的唯一键"""
            key_parts = []
            for field in self.config.key_fields:
                if is_mysql_data:
                    # MySQL数据直接使用字段名
                    value = str(data.get(field, ''))
                else:
                    # 宜搭数据需要通过字段映射获取
                    yida_field = self.config.field_mapping.get(field)
                    if yida_field:
                        form_data = data.get('formData', {})
                        value = str(form_data.get(yida_field, ''))
                    else:
                        value = ''
                key_parts.append(value)
            return '_'.join(key_parts)

    config = SyncConfig()
    sync_client = TestSyncClient(config)

    # 测试MySQL数据键生成
    mysql_data = {
        'project_code': 'PRJ001',
        'store_code': 'STORE001',
        'sales_date': '2025-01'
    }

    mysql_key = sync_client._generate_key(mysql_data, is_mysql_data=True)
    print(f"MySQL数据键: {mysql_key}")

    # 测试宜搭数据键生成
    yida_data = {
        'formData': {
            'textField_m9tojheo': 'PRJ001',
            'textField_m9tojheq': 'STORE001',
            'dateField_m9tojheu': '2025-01'
        }
    }

    yida_key = sync_client._generate_key(yida_data, is_mysql_data=False)
    print(f"宜搭数据键: {yida_key}")

    if mysql_key == yida_key:
        print("✓ 键生成功能正常")
    else:
        print("✗ 键生成功能异常")

def test_data_conversion():
    """测试数据转换功能"""
    print("\n=== 测试数据转换功能 ===")

    # 创建测试用的转换函数
    def convert_mysql_to_yida_format(mysql_data: dict, field_mapping: dict) -> dict:
        """将MySQL数据转换为宜搭表单格式"""
        from datetime import datetime
        yida_data = {}
        for mysql_field, yida_field in field_mapping.items():
            if mysql_field in mysql_data:
                value = mysql_data[mysql_field]

                # 处理日期字段
                if 'date' in mysql_field.lower() and isinstance(value, str):
                    try:
                        # 将日期字符串转换为时间戳（毫秒）
                        if len(value) == 7:  # YYYY-MM格式
                            value = value + '-01'  # 转换为YYYY-MM-DD
                        dt = datetime.strptime(value, '%Y-%m-%d')
                        value = int(dt.timestamp() * 1000)
                    except:
                        pass

                yida_data[yida_field] = value

        return yida_data

    config = SyncConfig()

    # 测试MySQL数据转换为宜搭格式
    mysql_data = {
        'project_code': 'PRJ001',
        'project_name': '测试项目',
        'store_code': 'STORE001',
        'store_name': '测试店铺',
        'sales_date': '2025-01',
        'online_amount': 1000.50,
        'offline_amount': 500.25,
        'total_amount': 1500.75,
        'order_count': 10
    }

    yida_data = convert_mysql_to_yida_format(mysql_data, config.field_mapping)
    print(f"转换后的宜搭数据: {yida_data}")

    # 检查字段映射是否正确
    expected_fields = len(config.field_mapping)
    actual_fields = len(yida_data)

    if expected_fields == actual_fields:
        print("✓ 数据转换功能正常")
    else:
        print(f"✗ 数据转换功能异常，期望{expected_fields}个字段，实际{actual_fields}个字段")

def test_data_comparison():
    """测试数据比较功能"""
    print("\n=== 测试数据比较功能 ===")

    # 创建测试用的数据比较函数
    def is_data_different(mysql_data: dict, yida_data: dict, compare_fields: list, field_mapping: dict) -> bool:
        """比较MySQL数据和宜搭数据是否有差异"""
        form_data = yida_data.get('formData', {})

        for field in compare_fields:
            yida_field = field_mapping.get(field)
            if not yida_field:
                continue

            mysql_value = mysql_data.get(field)
            yida_value = form_data.get(yida_field)

            # 数值类型比较
            if isinstance(mysql_value, (int, float)) and isinstance(yida_value, (int, float)):
                if abs(mysql_value - yida_value) > 0.01:  # 允许小数点误差
                    return True
            # 字符串类型比较
            elif str(mysql_value) != str(yida_value):
                return True

        return False

    config = SyncConfig()

    # 测试相同数据
    mysql_data1 = {
        'online_amount': 1000.50,
        'offline_amount': 500.25,
        'total_amount': 1500.75,
        'order_count': 10
    }

    yida_data1 = {
        'formData': {
            'numberField_m9tojhev': 1000.50,
            'numberField_m9tojhew': 500.25,
            'numberField_m9tojhex': 1500.75,
            'numberField_m9tojhey': 10
        }
    }

    is_different1 = is_data_different(mysql_data1, yida_data1, config.compare_fields, config.field_mapping)
    print(f"相同数据比较结果: {is_different1} (应为False)")

    # 测试不同数据
    yida_data2 = {
        'formData': {
            'numberField_m9tojhev': 1000.50,
            'numberField_m9tojhew': 500.25,
            'numberField_m9tojhex': 1500.75,
            'numberField_m9tojhey': 15  # 不同的值
        }
    }

    is_different2 = is_data_different(mysql_data1, yida_data2, config.compare_fields, config.field_mapping)
    print(f"不同数据比较结果: {is_different2} (应为True)")

    if not is_different1 and is_different2:
        print("✓ 数据比较功能正常")
    else:
        print("✗ 数据比较功能异常")

def test_dry_run():
    """测试干运行模式（不实际连接数据库）"""
    print("\n=== 测试干运行模式 ===")

    try:
        # 使用测试配置
        config = SyncConfig()

        # 修改配置为无效连接，测试错误处理
        config.db_config['host'] = 'invalid_host'

        print("尝试创建同步客户端（预期会失败）...")
        try:
            from mysql2yida_sync_framework import MySQL2YidaSyncClient
            _ = MySQL2YidaSyncClient(config)
            print("✗ 应该连接失败但却成功了")
        except Exception as e:
            print(f"✓ 预期的连接失败: {str(e)}")

    except Exception as e:
        print(f"测试过程中出现异常: {str(e)}")

def main():
    """主测试函数"""
    print("MySQL与宜搭数据同步框架测试")
    print("=" * 50)
    
    # 设置日志级别
    logging.basicConfig(level=logging.ERROR)
    
    try:
        test_config_creation()
        test_key_generation()
        test_data_conversion()
        test_data_comparison()
        test_dry_run()
        
        print("\n" + "=" * 50)
        print("测试完成！")
        
    except Exception as e:
        print(f"\n测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
