2025-06-01 00:00:02,660 - INFO - =================使用默认全量同步=============
2025-06-01 00:00:04,216 - INFO - MySQL查询成功，共获取 3305 条记录
2025-06-01 00:00:04,217 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-06-01 00:00:04,245 - INFO - 开始处理日期: 2025-01
2025-06-01 00:00:04,247 - INFO - Request Parameters - Page 1:
2025-06-01 00:00:04,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:04,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:06,048 - INFO - Response - Page 1:
2025-06-01 00:00:06,248 - INFO - 第 1 页获取到 100 条记录
2025-06-01 00:00:06,248 - INFO - Request Parameters - Page 2:
2025-06-01 00:00:06,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:06,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:06,787 - INFO - Response - Page 2:
2025-06-01 00:00:06,987 - INFO - 第 2 页获取到 100 条记录
2025-06-01 00:00:06,987 - INFO - Request Parameters - Page 3:
2025-06-01 00:00:06,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:06,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:07,643 - INFO - Response - Page 3:
2025-06-01 00:00:07,843 - INFO - 第 3 页获取到 100 条记录
2025-06-01 00:00:07,843 - INFO - Request Parameters - Page 4:
2025-06-01 00:00:07,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:07,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:08,306 - INFO - Response - Page 4:
2025-06-01 00:00:08,506 - INFO - 第 4 页获取到 100 条记录
2025-06-01 00:00:08,506 - INFO - Request Parameters - Page 5:
2025-06-01 00:00:08,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:08,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:08,961 - INFO - Response - Page 5:
2025-06-01 00:00:09,161 - INFO - 第 5 页获取到 100 条记录
2025-06-01 00:00:09,161 - INFO - Request Parameters - Page 6:
2025-06-01 00:00:09,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:09,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:09,689 - INFO - Response - Page 6:
2025-06-01 00:00:09,889 - INFO - 第 6 页获取到 100 条记录
2025-06-01 00:00:09,889 - INFO - Request Parameters - Page 7:
2025-06-01 00:00:09,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:09,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:10,348 - INFO - Response - Page 7:
2025-06-01 00:00:10,549 - INFO - 第 7 页获取到 82 条记录
2025-06-01 00:00:10,549 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 00:00:10,549 - INFO - 获取到 682 条表单数据
2025-06-01 00:00:10,561 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 00:00:10,572 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 00:00:10,572 - INFO - 开始处理日期: 2025-02
2025-06-01 00:00:10,572 - INFO - Request Parameters - Page 1:
2025-06-01 00:00:10,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:10,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:11,053 - INFO - Response - Page 1:
2025-06-01 00:00:11,253 - INFO - 第 1 页获取到 100 条记录
2025-06-01 00:00:11,253 - INFO - Request Parameters - Page 2:
2025-06-01 00:00:11,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:11,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:11,730 - INFO - Response - Page 2:
2025-06-01 00:00:11,930 - INFO - 第 2 页获取到 100 条记录
2025-06-01 00:00:11,930 - INFO - Request Parameters - Page 3:
2025-06-01 00:00:11,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:11,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:12,504 - INFO - Response - Page 3:
2025-06-01 00:00:12,704 - INFO - 第 3 页获取到 100 条记录
2025-06-01 00:00:12,704 - INFO - Request Parameters - Page 4:
2025-06-01 00:00:12,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:12,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:13,177 - INFO - Response - Page 4:
2025-06-01 00:00:13,377 - INFO - 第 4 页获取到 100 条记录
2025-06-01 00:00:13,377 - INFO - Request Parameters - Page 5:
2025-06-01 00:00:13,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:13,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:13,885 - INFO - Response - Page 5:
2025-06-01 00:00:14,086 - INFO - 第 5 页获取到 100 条记录
2025-06-01 00:00:14,086 - INFO - Request Parameters - Page 6:
2025-06-01 00:00:14,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:14,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:14,581 - INFO - Response - Page 6:
2025-06-01 00:00:14,782 - INFO - 第 6 页获取到 100 条记录
2025-06-01 00:00:14,782 - INFO - Request Parameters - Page 7:
2025-06-01 00:00:14,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:14,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:15,227 - INFO - Response - Page 7:
2025-06-01 00:00:15,427 - INFO - 第 7 页获取到 70 条记录
2025-06-01 00:00:15,428 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 00:00:15,428 - INFO - 获取到 670 条表单数据
2025-06-01 00:00:15,440 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 00:00:15,450 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 00:00:15,451 - INFO - 开始处理日期: 2025-03
2025-06-01 00:00:15,452 - INFO - Request Parameters - Page 1:
2025-06-01 00:00:15,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:15,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:15,899 - INFO - Response - Page 1:
2025-06-01 00:00:16,099 - INFO - 第 1 页获取到 100 条记录
2025-06-01 00:00:16,099 - INFO - Request Parameters - Page 2:
2025-06-01 00:00:16,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:16,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:16,520 - INFO - Response - Page 2:
2025-06-01 00:00:16,720 - INFO - 第 2 页获取到 100 条记录
2025-06-01 00:00:16,720 - INFO - Request Parameters - Page 3:
2025-06-01 00:00:16,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:16,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:17,165 - INFO - Response - Page 3:
2025-06-01 00:00:17,365 - INFO - 第 3 页获取到 100 条记录
2025-06-01 00:00:17,365 - INFO - Request Parameters - Page 4:
2025-06-01 00:00:17,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:17,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:17,890 - INFO - Response - Page 4:
2025-06-01 00:00:18,090 - INFO - 第 4 页获取到 100 条记录
2025-06-01 00:00:18,090 - INFO - Request Parameters - Page 5:
2025-06-01 00:00:18,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:18,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:18,599 - INFO - Response - Page 5:
2025-06-01 00:00:18,799 - INFO - 第 5 页获取到 100 条记录
2025-06-01 00:00:18,799 - INFO - Request Parameters - Page 6:
2025-06-01 00:00:18,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:18,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:19,305 - INFO - Response - Page 6:
2025-06-01 00:00:19,505 - INFO - 第 6 页获取到 100 条记录
2025-06-01 00:00:19,505 - INFO - Request Parameters - Page 7:
2025-06-01 00:00:19,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:19,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:20,007 - INFO - Response - Page 7:
2025-06-01 00:00:20,207 - INFO - 第 7 页获取到 61 条记录
2025-06-01 00:00:20,207 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 00:00:20,207 - INFO - 获取到 661 条表单数据
2025-06-01 00:00:20,220 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 00:00:20,233 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 00:00:20,234 - INFO - 开始处理日期: 2025-04
2025-06-01 00:00:20,234 - INFO - Request Parameters - Page 1:
2025-06-01 00:00:20,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:20,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:20,758 - INFO - Response - Page 1:
2025-06-01 00:00:20,958 - INFO - 第 1 页获取到 100 条记录
2025-06-01 00:00:20,958 - INFO - Request Parameters - Page 2:
2025-06-01 00:00:20,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:20,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:21,408 - INFO - Response - Page 2:
2025-06-01 00:00:21,608 - INFO - 第 2 页获取到 100 条记录
2025-06-01 00:00:21,608 - INFO - Request Parameters - Page 3:
2025-06-01 00:00:21,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:21,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:22,091 - INFO - Response - Page 3:
2025-06-01 00:00:22,291 - INFO - 第 3 页获取到 100 条记录
2025-06-01 00:00:22,291 - INFO - Request Parameters - Page 4:
2025-06-01 00:00:22,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:22,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:22,786 - INFO - Response - Page 4:
2025-06-01 00:00:22,986 - INFO - 第 4 页获取到 100 条记录
2025-06-01 00:00:22,986 - INFO - Request Parameters - Page 5:
2025-06-01 00:00:22,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:22,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:23,516 - INFO - Response - Page 5:
2025-06-01 00:00:23,717 - INFO - 第 5 页获取到 100 条记录
2025-06-01 00:00:23,717 - INFO - Request Parameters - Page 6:
2025-06-01 00:00:23,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:23,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:24,237 - INFO - Response - Page 6:
2025-06-01 00:00:24,438 - INFO - 第 6 页获取到 100 条记录
2025-06-01 00:00:24,438 - INFO - Request Parameters - Page 7:
2025-06-01 00:00:24,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:24,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:24,934 - INFO - Response - Page 7:
2025-06-01 00:00:25,134 - INFO - 第 7 页获取到 56 条记录
2025-06-01 00:00:25,134 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 00:00:25,134 - INFO - 获取到 656 条表单数据
2025-06-01 00:00:25,148 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 00:00:25,159 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 00:00:25,159 - INFO - 开始处理日期: 2025-05
2025-06-01 00:00:25,159 - INFO - Request Parameters - Page 1:
2025-06-01 00:00:25,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:25,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:25,667 - INFO - Response - Page 1:
2025-06-01 00:00:25,868 - INFO - 第 1 页获取到 100 条记录
2025-06-01 00:00:25,868 - INFO - Request Parameters - Page 2:
2025-06-01 00:00:25,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:25,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:26,401 - INFO - Response - Page 2:
2025-06-01 00:00:26,602 - INFO - 第 2 页获取到 100 条记录
2025-06-01 00:00:26,602 - INFO - Request Parameters - Page 3:
2025-06-01 00:00:26,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:26,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:27,038 - INFO - Response - Page 3:
2025-06-01 00:00:27,238 - INFO - 第 3 页获取到 100 条记录
2025-06-01 00:00:27,238 - INFO - Request Parameters - Page 4:
2025-06-01 00:00:27,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:27,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:27,722 - INFO - Response - Page 4:
2025-06-01 00:00:27,923 - INFO - 第 4 页获取到 100 条记录
2025-06-01 00:00:27,923 - INFO - Request Parameters - Page 5:
2025-06-01 00:00:27,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:27,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:28,519 - INFO - Response - Page 5:
2025-06-01 00:00:28,719 - INFO - 第 5 页获取到 100 条记录
2025-06-01 00:00:28,719 - INFO - Request Parameters - Page 6:
2025-06-01 00:00:28,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:28,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:29,363 - INFO - Response - Page 6:
2025-06-01 00:00:29,564 - INFO - 第 6 页获取到 100 条记录
2025-06-01 00:00:29,564 - INFO - Request Parameters - Page 7:
2025-06-01 00:00:29,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 00:00:29,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 00:00:30,054 - INFO - Response - Page 7:
2025-06-01 00:00:30,255 - INFO - 第 7 页获取到 36 条记录
2025-06-01 00:00:30,255 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 00:00:30,255 - INFO - 获取到 636 条表单数据
2025-06-01 00:00:30,268 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 00:00:30,268 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-06-01 00:00:30,777 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-06-01 00:00:30,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62460.0, 'new_value': 63140.0}, {'field': 'total_amount', 'old_value': 66580.0, 'new_value': 67260.0}, {'field': 'order_count', 'old_value': 647, 'new_value': 656}]
2025-06-01 00:00:30,778 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-06-01 00:00:31,268 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-06-01 00:00:31,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53500.9, 'new_value': 54040.4}, {'field': 'total_amount', 'old_value': 57460.9, 'new_value': 58000.4}, {'field': 'order_count', 'old_value': 483, 'new_value': 511}]
2025-06-01 00:00:31,269 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-06-01 00:00:31,718 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-06-01 00:00:31,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7804.5, 'new_value': 8393.85}, {'field': 'offline_amount', 'old_value': 86275.7, 'new_value': 99154.7}, {'field': 'total_amount', 'old_value': 94080.2, 'new_value': 107548.55}, {'field': 'order_count', 'old_value': 2128, 'new_value': 2292}]
2025-06-01 00:00:31,718 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-06-01 00:00:32,335 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-06-01 00:00:32,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15922.7, 'new_value': 16098.7}, {'field': 'offline_amount', 'old_value': 61115.6, 'new_value': 68329.6}, {'field': 'total_amount', 'old_value': 77038.3, 'new_value': 84428.3}, {'field': 'order_count', 'old_value': 748, 'new_value': 793}]
2025-06-01 00:00:32,335 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-06-01 00:00:32,779 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-06-01 00:00:32,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35234.2, 'new_value': 36308.2}, {'field': 'offline_amount', 'old_value': 203614.02, 'new_value': 208738.9}, {'field': 'total_amount', 'old_value': 238848.22, 'new_value': 245047.1}, {'field': 'order_count', 'old_value': 313, 'new_value': 322}]
2025-06-01 00:00:32,780 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-06-01 00:00:33,183 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-06-01 00:00:33,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16964.0, 'new_value': 17754.0}, {'field': 'total_amount', 'old_value': 16964.0, 'new_value': 17754.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-01 00:00:33,184 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-06-01 00:00:33,572 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-06-01 00:00:33,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61589.44, 'new_value': 65302.46}, {'field': 'offline_amount', 'old_value': 834082.18, 'new_value': 880213.68}, {'field': 'total_amount', 'old_value': 895671.62, 'new_value': 945516.14}, {'field': 'order_count', 'old_value': 3686, 'new_value': 3861}]
2025-06-01 00:00:33,573 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-06-01 00:00:34,055 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-06-01 00:00:34,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73612.0, 'new_value': 76100.0}, {'field': 'total_amount', 'old_value': 80701.16, 'new_value': 83189.16}, {'field': 'order_count', 'old_value': 471, 'new_value': 475}]
2025-06-01 00:00:34,056 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-06-01 00:00:34,551 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-06-01 00:00:34,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71004.16, 'new_value': 74060.31}, {'field': 'offline_amount', 'old_value': 116847.13, 'new_value': 122162.41}, {'field': 'total_amount', 'old_value': 187851.29, 'new_value': 196222.72}, {'field': 'order_count', 'old_value': 6539, 'new_value': 6800}]
2025-06-01 00:00:34,552 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-06-01 00:00:35,036 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-06-01 00:00:35,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379253.0, 'new_value': 395751.0}, {'field': 'total_amount', 'old_value': 379253.0, 'new_value': 395751.0}, {'field': 'order_count', 'old_value': 243, 'new_value': 247}]
2025-06-01 00:00:35,036 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-06-01 00:00:35,472 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-06-01 00:00:35,472 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241804.37, 'new_value': 250198.22}, {'field': 'offline_amount', 'old_value': 479257.22, 'new_value': 519813.61}, {'field': 'total_amount', 'old_value': 721061.59, 'new_value': 770011.83}, {'field': 'order_count', 'old_value': 5471, 'new_value': 5720}]
2025-06-01 00:00:35,472 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-06-01 00:00:35,944 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-06-01 00:00:35,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32486.47, 'new_value': 33365.47}, {'field': 'total_amount', 'old_value': 32486.47, 'new_value': 33365.47}, {'field': 'order_count', 'old_value': 195, 'new_value': 202}]
2025-06-01 00:00:35,945 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-06-01 00:00:36,374 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-06-01 00:00:36,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25274.85, 'new_value': 31400.45}, {'field': 'total_amount', 'old_value': 25340.4, 'new_value': 31466.0}, {'field': 'order_count', 'old_value': 234, 'new_value': 248}]
2025-06-01 00:00:36,374 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-06-01 00:00:36,991 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-06-01 00:00:36,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9300.0, 'new_value': 9703.0}, {'field': 'total_amount', 'old_value': 9300.0, 'new_value': 9703.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-06-01 00:00:36,992 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-06-01 00:00:37,448 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-06-01 00:00:37,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41665.9, 'new_value': 44139.9}, {'field': 'total_amount', 'old_value': 41665.9, 'new_value': 44139.9}, {'field': 'order_count', 'old_value': 186, 'new_value': 197}]
2025-06-01 00:00:37,449 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-06-01 00:00:37,940 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-06-01 00:00:37,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199572.8, 'new_value': 204166.4}, {'field': 'total_amount', 'old_value': 207424.6, 'new_value': 212018.2}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-01 00:00:37,941 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-06-01 00:00:38,356 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-06-01 00:00:38,356 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55958.0, 'new_value': 58423.03}, {'field': 'offline_amount', 'old_value': 104631.85, 'new_value': 110171.5}, {'field': 'total_amount', 'old_value': 160589.85, 'new_value': 168594.53}, {'field': 'order_count', 'old_value': 5945, 'new_value': 6229}]
2025-06-01 00:00:38,356 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-06-01 00:00:38,740 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-06-01 00:00:38,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81221.49, 'new_value': 85800.19}, {'field': 'total_amount', 'old_value': 84990.59, 'new_value': 89569.29}, {'field': 'order_count', 'old_value': 423, 'new_value': 459}]
2025-06-01 00:00:38,740 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-06-01 00:00:39,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-06-01 00:00:39,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127568.64, 'new_value': 132437.24}, {'field': 'total_amount', 'old_value': 132908.64, 'new_value': 137777.24}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-06-01 00:00:39,186 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-06-01 00:00:39,653 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-06-01 00:00:39,653 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 539352.04, 'new_value': 585207.58}, {'field': 'offline_amount', 'old_value': 251102.9, 'new_value': 283832.2}, {'field': 'total_amount', 'old_value': 790454.94, 'new_value': 869039.78}, {'field': 'order_count', 'old_value': 6869, 'new_value': 7420}]
2025-06-01 00:00:39,653 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-06-01 00:00:40,060 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-06-01 00:00:40,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44142.5, 'new_value': 47389.36}, {'field': 'offline_amount', 'old_value': 580.0, 'new_value': 585.0}, {'field': 'total_amount', 'old_value': 44722.5, 'new_value': 47974.36}, {'field': 'order_count', 'old_value': 182, 'new_value': 196}]
2025-06-01 00:00:40,060 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-06-01 00:00:40,454 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-06-01 00:00:40,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182155.0, 'new_value': 190364.0}, {'field': 'total_amount', 'old_value': 220839.17, 'new_value': 229048.17}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-06-01 00:00:40,454 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-06-01 00:00:40,885 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-06-01 00:00:40,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11517.0, 'new_value': 11892.9}, {'field': 'offline_amount', 'old_value': 57650.1, 'new_value': 59930.1}, {'field': 'total_amount', 'old_value': 69167.1, 'new_value': 71823.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 93}]
2025-06-01 00:00:40,886 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-06-01 00:00:41,293 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-06-01 00:00:41,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64427.0, 'new_value': 72584.0}, {'field': 'total_amount', 'old_value': 64775.0, 'new_value': 72932.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 147}]
2025-06-01 00:00:41,293 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-06-01 00:00:41,682 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-06-01 00:00:41,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 966128.0, 'new_value': 984924.0}, {'field': 'total_amount', 'old_value': 966128.0, 'new_value': 984924.0}, {'field': 'order_count', 'old_value': 181, 'new_value': 189}]
2025-06-01 00:00:41,683 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-06-01 00:00:42,214 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-06-01 00:00:42,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191974.2, 'new_value': 206474.2}, {'field': 'total_amount', 'old_value': 191974.2, 'new_value': 206474.2}, {'field': 'order_count', 'old_value': 440, 'new_value': 476}]
2025-06-01 00:00:42,214 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-06-01 00:00:42,649 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-06-01 00:00:42,649 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 282756.39, 'new_value': 303289.2}, {'field': 'offline_amount', 'old_value': 116608.84, 'new_value': 122644.65}, {'field': 'total_amount', 'old_value': 399365.23, 'new_value': 425933.85}, {'field': 'order_count', 'old_value': 1670, 'new_value': 1811}]
2025-06-01 00:00:42,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-06-01 00:00:43,111 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-06-01 00:00:43,111 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 224669.49, 'new_value': 243774.51}, {'field': 'total_amount', 'old_value': 345104.33, 'new_value': 364209.35}, {'field': 'order_count', 'old_value': 3662, 'new_value': 3797}]
2025-06-01 00:00:43,111 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-06-01 00:00:43,541 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-06-01 00:00:43,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108034.51, 'new_value': 114876.34}, {'field': 'total_amount', 'old_value': 108034.51, 'new_value': 114876.34}, {'field': 'order_count', 'old_value': 4147, 'new_value': 4391}]
2025-06-01 00:00:43,541 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-06-01 00:00:43,938 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-06-01 00:00:43,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26146.02, 'new_value': 26666.42}, {'field': 'offline_amount', 'old_value': 331419.0, 'new_value': 348908.8}, {'field': 'total_amount', 'old_value': 357565.02, 'new_value': 375575.22}, {'field': 'order_count', 'old_value': 1691, 'new_value': 1767}]
2025-06-01 00:00:43,938 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-06-01 00:00:44,330 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-06-01 00:00:44,331 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46554.22, 'new_value': 47831.55}, {'field': 'offline_amount', 'old_value': 626902.18, 'new_value': 665533.75}, {'field': 'total_amount', 'old_value': 673456.4, 'new_value': 713365.3}, {'field': 'order_count', 'old_value': 3517, 'new_value': 3672}]
2025-06-01 00:00:44,331 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-06-01 00:00:44,830 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-06-01 00:00:44,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11291.98, 'new_value': 12244.26}, {'field': 'total_amount', 'old_value': 28772.11, 'new_value': 29724.39}, {'field': 'order_count', 'old_value': 123, 'new_value': 128}]
2025-06-01 00:00:44,831 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-06-01 00:00:45,224 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-06-01 00:00:45,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11800.0, 'new_value': 20080.0}, {'field': 'total_amount', 'old_value': 11800.0, 'new_value': 20080.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-01 00:00:45,225 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-06-01 00:00:45,616 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-06-01 00:00:45,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57850.0, 'new_value': 58724.0}, {'field': 'total_amount', 'old_value': 57850.0, 'new_value': 58724.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-01 00:00:45,616 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-06-01 00:00:45,985 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-06-01 00:00:45,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115866.8, 'new_value': 122704.1}, {'field': 'total_amount', 'old_value': 115866.8, 'new_value': 122704.1}, {'field': 'order_count', 'old_value': 367, 'new_value': 395}]
2025-06-01 00:00:45,985 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-06-01 00:00:46,396 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-06-01 00:00:46,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50629.0, 'new_value': 53552.0}, {'field': 'total_amount', 'old_value': 50629.0, 'new_value': 53552.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 144}]
2025-06-01 00:00:46,396 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-06-01 00:00:46,975 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-06-01 00:00:46,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40768.0, 'new_value': 42190.0}, {'field': 'total_amount', 'old_value': 40768.0, 'new_value': 42190.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 131}]
2025-06-01 00:00:46,975 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-06-01 00:00:47,436 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-06-01 00:00:47,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96484.0, 'new_value': 102483.0}, {'field': 'total_amount', 'old_value': 96484.0, 'new_value': 102483.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-01 00:00:47,437 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-06-01 00:00:47,899 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-06-01 00:00:47,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58738.0, 'new_value': 59854.0}, {'field': 'total_amount', 'old_value': 61892.0, 'new_value': 63008.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 241}]
2025-06-01 00:00:47,899 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-06-01 00:00:48,370 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-06-01 00:00:48,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2021132.53, 'new_value': 2122395.11}, {'field': 'total_amount', 'old_value': 2021132.53, 'new_value': 2122395.11}, {'field': 'order_count', 'old_value': 18263, 'new_value': 18973}]
2025-06-01 00:00:48,371 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-06-01 00:00:48,866 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-06-01 00:00:48,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252320.26, 'new_value': 267326.86}, {'field': 'total_amount', 'old_value': 252326.26, 'new_value': 267332.86}, {'field': 'order_count', 'old_value': 474, 'new_value': 499}]
2025-06-01 00:00:48,867 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-06-01 00:00:49,280 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-06-01 00:00:49,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 543201.0, 'new_value': 582755.87}, {'field': 'total_amount', 'old_value': 697835.3, 'new_value': 737390.17}, {'field': 'order_count', 'old_value': 2930, 'new_value': 3069}]
2025-06-01 00:00:49,281 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-06-01 00:00:49,683 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-06-01 00:00:49,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158558.0, 'new_value': 159457.0}, {'field': 'total_amount', 'old_value': 158558.0, 'new_value': 159457.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-01 00:00:49,683 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-06-01 00:00:50,104 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-06-01 00:00:50,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214828.0, 'new_value': 226603.0}, {'field': 'total_amount', 'old_value': 214961.0, 'new_value': 226736.0}, {'field': 'order_count', 'old_value': 156, 'new_value': 163}]
2025-06-01 00:00:50,105 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-06-01 00:00:50,511 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-06-01 00:00:50,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117973.52, 'new_value': 124723.52}, {'field': 'total_amount', 'old_value': 123585.04, 'new_value': 130335.04}, {'field': 'order_count', 'old_value': 11248, 'new_value': 11866}]
2025-06-01 00:00:50,512 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-06-01 00:00:50,898 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-06-01 00:00:50,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16399.89, 'new_value': 17515.79}, {'field': 'offline_amount', 'old_value': 107796.73, 'new_value': 115263.32}, {'field': 'total_amount', 'old_value': 124196.62, 'new_value': 132779.11}, {'field': 'order_count', 'old_value': 3231, 'new_value': 3466}]
2025-06-01 00:00:50,898 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-06-01 00:00:51,319 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-06-01 00:00:51,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85138.37, 'new_value': 85501.37}, {'field': 'total_amount', 'old_value': 117013.27, 'new_value': 117376.27}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-01 00:00:51,319 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-06-01 00:00:51,773 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-06-01 00:00:51,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13766.13, 'new_value': 14290.58}, {'field': 'offline_amount', 'old_value': 13785.11, 'new_value': 14771.46}, {'field': 'total_amount', 'old_value': 27551.24, 'new_value': 29062.04}, {'field': 'order_count', 'old_value': 2286, 'new_value': 2395}]
2025-06-01 00:00:51,774 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-06-01 00:00:52,222 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-06-01 00:00:52,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66948.57, 'new_value': 69379.99}, {'field': 'offline_amount', 'old_value': 438467.66, 'new_value': 468169.11}, {'field': 'total_amount', 'old_value': 505416.23, 'new_value': 537549.1}, {'field': 'order_count', 'old_value': 4234, 'new_value': 4480}]
2025-06-01 00:00:52,223 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-06-01 00:00:52,657 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-06-01 00:00:52,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101381.33, 'new_value': 104319.61}, {'field': 'offline_amount', 'old_value': 42816.91, 'new_value': 46088.07}, {'field': 'total_amount', 'old_value': 144198.24, 'new_value': 150407.68}, {'field': 'order_count', 'old_value': 8914, 'new_value': 9236}]
2025-06-01 00:00:52,658 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-06-01 00:00:53,122 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-06-01 00:00:53,123 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 423345.79, 'new_value': 450711.87}, {'field': 'total_amount', 'old_value': 423345.79, 'new_value': 450711.87}, {'field': 'order_count', 'old_value': 2145, 'new_value': 2262}]
2025-06-01 00:00:53,123 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-06-01 00:00:53,596 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-06-01 00:00:53,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77580.0, 'new_value': 89580.0}, {'field': 'total_amount', 'old_value': 77580.0, 'new_value': 89580.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-01 00:00:53,596 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-06-01 00:00:53,973 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-06-01 00:00:53,973 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117255.94, 'new_value': 120210.14}, {'field': 'offline_amount', 'old_value': 289574.5, 'new_value': 304653.39}, {'field': 'total_amount', 'old_value': 406830.44, 'new_value': 424863.53}, {'field': 'order_count', 'old_value': 13851, 'new_value': 14378}]
2025-06-01 00:00:53,974 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-06-01 00:00:54,433 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-06-01 00:00:54,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59997.5, 'new_value': 63372.9}, {'field': 'total_amount', 'old_value': 62297.8, 'new_value': 65673.2}, {'field': 'order_count', 'old_value': 192, 'new_value': 206}]
2025-06-01 00:00:54,433 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-06-01 00:00:54,828 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-06-01 00:00:54,829 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21653.9, 'new_value': 22613.8}, {'field': 'offline_amount', 'old_value': 128007.5, 'new_value': 129105.5}, {'field': 'total_amount', 'old_value': 149661.4, 'new_value': 151719.3}, {'field': 'order_count', 'old_value': 207, 'new_value': 211}]
2025-06-01 00:00:54,829 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-06-01 00:00:55,263 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-06-01 00:00:55,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85312.65, 'new_value': 89201.75}, {'field': 'total_amount', 'old_value': 85312.65, 'new_value': 89201.75}, {'field': 'order_count', 'old_value': 622, 'new_value': 651}]
2025-06-01 00:00:55,263 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-06-01 00:00:55,695 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-06-01 00:00:55,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94765.78, 'new_value': 96732.66}, {'field': 'offline_amount', 'old_value': 281935.29, 'new_value': 299326.7}, {'field': 'total_amount', 'old_value': 376701.07, 'new_value': 396059.36}, {'field': 'order_count', 'old_value': 4366, 'new_value': 4599}]
2025-06-01 00:00:55,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-06-01 00:00:56,080 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-06-01 00:00:56,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76658.9, 'new_value': 80680.9}, {'field': 'total_amount', 'old_value': 76658.9, 'new_value': 80680.9}, {'field': 'order_count', 'old_value': 1658, 'new_value': 1665}]
2025-06-01 00:00:56,080 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-06-01 00:00:56,551 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-06-01 00:00:56,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17153.0, 'new_value': 17502.0}, {'field': 'total_amount', 'old_value': 17153.0, 'new_value': 17502.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-06-01 00:00:56,551 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-06-01 00:00:56,992 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-06-01 00:00:56,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51961.17, 'new_value': 54648.42}, {'field': 'total_amount', 'old_value': 51961.17, 'new_value': 54648.42}, {'field': 'order_count', 'old_value': 208, 'new_value': 220}]
2025-06-01 00:00:56,993 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-06-01 00:00:57,389 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-06-01 00:00:57,389 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36191.62, 'new_value': 45065.75}, {'field': 'offline_amount', 'old_value': 121608.48, 'new_value': 141003.48}, {'field': 'total_amount', 'old_value': 157800.1, 'new_value': 186069.23}, {'field': 'order_count', 'old_value': 123, 'new_value': 142}]
2025-06-01 00:00:57,389 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-06-01 00:00:57,764 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-06-01 00:00:57,764 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31027.0, 'new_value': 32207.0}, {'field': 'total_amount', 'old_value': 31027.0, 'new_value': 32207.0}, {'field': 'order_count', 'old_value': 334, 'new_value': 348}]
2025-06-01 00:00:57,764 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-06-01 00:00:58,162 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-06-01 00:00:58,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 744916.0, 'new_value': 774417.05}, {'field': 'total_amount', 'old_value': 744916.0, 'new_value': 774417.05}, {'field': 'order_count', 'old_value': 2163, 'new_value': 2251}]
2025-06-01 00:00:58,162 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-06-01 00:00:58,568 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-06-01 00:00:58,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 945677.0, 'new_value': 987974.0}, {'field': 'total_amount', 'old_value': 945677.0, 'new_value': 987974.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 143}]
2025-06-01 00:00:58,569 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-06-01 00:00:58,976 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-06-01 00:00:58,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91437.72, 'new_value': 95233.15}, {'field': 'offline_amount', 'old_value': 128046.49, 'new_value': 136303.59}, {'field': 'total_amount', 'old_value': 219484.21, 'new_value': 231536.74}, {'field': 'order_count', 'old_value': 2308, 'new_value': 2421}]
2025-06-01 00:00:58,977 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-06-01 00:00:59,403 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-06-01 00:00:59,404 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12655.0, 'new_value': 13703.0}, {'field': 'total_amount', 'old_value': 14663.0, 'new_value': 15711.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 152}]
2025-06-01 00:00:59,404 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-06-01 00:00:59,768 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-06-01 00:00:59,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 651619.0, 'new_value': 687048.0}, {'field': 'total_amount', 'old_value': 654091.0, 'new_value': 689520.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 319}]
2025-06-01 00:00:59,768 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-06-01 00:01:00,256 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-06-01 00:01:00,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30788.0, 'new_value': 32906.0}, {'field': 'total_amount', 'old_value': 30788.0, 'new_value': 32906.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 153}]
2025-06-01 00:01:00,257 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-06-01 00:01:00,695 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-06-01 00:01:00,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 135157.3, 'new_value': 159119.7}, {'field': 'offline_amount', 'old_value': 159913.0, 'new_value': 179234.1}, {'field': 'total_amount', 'old_value': 295070.3, 'new_value': 338353.8}, {'field': 'order_count', 'old_value': 5950, 'new_value': 6815}]
2025-06-01 00:01:00,695 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-06-01 00:01:01,388 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-06-01 00:01:01,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 540449.87, 'new_value': 569215.06}, {'field': 'total_amount', 'old_value': 540449.87, 'new_value': 569215.06}, {'field': 'order_count', 'old_value': 7520, 'new_value': 7870}]
2025-06-01 00:01:01,389 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-06-01 00:01:01,865 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-06-01 00:01:01,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12029.7, 'new_value': 12177.7}, {'field': 'total_amount', 'old_value': 40537.6, 'new_value': 40685.6}, {'field': 'order_count', 'old_value': 112, 'new_value': 114}]
2025-06-01 00:01:01,866 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-06-01 00:01:02,278 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-06-01 00:01:02,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220296.0, 'new_value': 229975.0}, {'field': 'total_amount', 'old_value': 220296.0, 'new_value': 229975.0}, {'field': 'order_count', 'old_value': 3393, 'new_value': 3407}]
2025-06-01 00:01:02,278 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-06-01 00:01:02,662 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-06-01 00:01:02,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102614.68, 'new_value': 107221.73}, {'field': 'total_amount', 'old_value': 102614.68, 'new_value': 107221.73}, {'field': 'order_count', 'old_value': 3200, 'new_value': 3357}]
2025-06-01 00:01:02,663 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-06-01 00:01:03,166 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-06-01 00:01:03,167 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11510.9, 'new_value': 11996.99}, {'field': 'offline_amount', 'old_value': 36161.21, 'new_value': 38910.21}, {'field': 'total_amount', 'old_value': 47672.11, 'new_value': 50907.2}, {'field': 'order_count', 'old_value': 1665, 'new_value': 1764}]
2025-06-01 00:01:03,167 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-06-01 00:01:03,547 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-06-01 00:01:03,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43071.57, 'new_value': 44865.59}, {'field': 'offline_amount', 'old_value': 357430.73, 'new_value': 386804.97}, {'field': 'total_amount', 'old_value': 400502.3, 'new_value': 431670.56}, {'field': 'order_count', 'old_value': 9156, 'new_value': 9875}]
2025-06-01 00:01:03,548 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-06-01 00:01:03,991 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-06-01 00:01:03,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376374.46, 'new_value': 400909.34}, {'field': 'total_amount', 'old_value': 376374.46, 'new_value': 400909.34}, {'field': 'order_count', 'old_value': 3702, 'new_value': 3876}]
2025-06-01 00:01:03,992 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-06-01 00:01:04,449 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-06-01 00:01:04,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83076.0, 'new_value': 91753.0}, {'field': 'total_amount', 'old_value': 83076.0, 'new_value': 91753.0}, {'field': 'order_count', 'old_value': 377, 'new_value': 413}]
2025-06-01 00:01:04,450 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-06-01 00:01:04,935 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-06-01 00:01:04,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 467442.7, 'new_value': 499709.0}, {'field': 'total_amount', 'old_value': 467442.7, 'new_value': 499709.0}, {'field': 'order_count', 'old_value': 2285, 'new_value': 2408}]
2025-06-01 00:01:04,936 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-06-01 00:01:05,380 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-06-01 00:01:05,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156158.1, 'new_value': 162135.52}, {'field': 'offline_amount', 'old_value': 62003.46, 'new_value': 66762.3}, {'field': 'total_amount', 'old_value': 218161.56, 'new_value': 228897.82}, {'field': 'order_count', 'old_value': 13172, 'new_value': 13784}]
2025-06-01 00:01:05,380 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-06-01 00:01:05,809 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-06-01 00:01:05,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12408.9, 'new_value': 13599.8}, {'field': 'total_amount', 'old_value': 12837.9, 'new_value': 14028.8}, {'field': 'order_count', 'old_value': 170, 'new_value': 195}]
2025-06-01 00:01:05,809 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-06-01 00:01:06,259 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-06-01 00:01:06,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237488.0, 'new_value': 243564.0}, {'field': 'total_amount', 'old_value': 237488.0, 'new_value': 243564.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 268}]
2025-06-01 00:01:06,259 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-06-01 00:01:06,686 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-06-01 00:01:06,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 788417.06, 'new_value': 853282.5}, {'field': 'total_amount', 'old_value': 788417.06, 'new_value': 853282.5}, {'field': 'order_count', 'old_value': 14982, 'new_value': 16103}]
2025-06-01 00:01:06,686 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-06-01 00:01:07,172 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-06-01 00:01:07,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 313681.16, 'new_value': 330673.37}, {'field': 'total_amount', 'old_value': 325732.13, 'new_value': 342724.34}, {'field': 'order_count', 'old_value': 14142, 'new_value': 14839}]
2025-06-01 00:01:07,173 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-06-01 00:01:07,577 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-06-01 00:01:07,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 781641.0, 'new_value': 800952.0}, {'field': 'total_amount', 'old_value': 781641.0, 'new_value': 800952.0}, {'field': 'order_count', 'old_value': 193, 'new_value': 197}]
2025-06-01 00:01:07,578 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-06-01 00:01:08,088 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-06-01 00:01:08,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 849349.67, 'new_value': 905344.3}, {'field': 'total_amount', 'old_value': 849349.67, 'new_value': 905344.3}, {'field': 'order_count', 'old_value': 5999, 'new_value': 6390}]
2025-06-01 00:01:08,088 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-06-01 00:01:08,553 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-06-01 00:01:08,553 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8487.23, 'new_value': 8855.03}, {'field': 'offline_amount', 'old_value': 430559.14, 'new_value': 459194.56}, {'field': 'total_amount', 'old_value': 439046.37, 'new_value': 468049.59}, {'field': 'order_count', 'old_value': 20921, 'new_value': 22104}]
2025-06-01 00:01:08,553 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-06-01 00:01:09,006 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-06-01 00:01:09,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89906.0, 'new_value': 93402.0}, {'field': 'total_amount', 'old_value': 89906.0, 'new_value': 93402.0}, {'field': 'order_count', 'old_value': 252, 'new_value': 262}]
2025-06-01 00:01:09,006 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-06-01 00:01:09,474 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-06-01 00:01:09,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75971.6, 'new_value': 79257.64}, {'field': 'offline_amount', 'old_value': 92527.44, 'new_value': 97317.39}, {'field': 'total_amount', 'old_value': 168499.04, 'new_value': 176575.03}, {'field': 'order_count', 'old_value': 7786, 'new_value': 8136}]
2025-06-01 00:01:09,475 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-06-01 00:01:09,866 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-06-01 00:01:09,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155700.0, 'new_value': 171645.0}, {'field': 'total_amount', 'old_value': 155700.0, 'new_value': 171645.0}, {'field': 'order_count', 'old_value': 4977, 'new_value': 5487}]
2025-06-01 00:01:09,867 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-06-01 00:01:10,260 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-06-01 00:01:10,260 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146597.43, 'new_value': 146661.43}, {'field': 'offline_amount', 'old_value': 396507.28, 'new_value': 428024.28}, {'field': 'total_amount', 'old_value': 543104.71, 'new_value': 574685.71}, {'field': 'order_count', 'old_value': 4805, 'new_value': 4953}]
2025-06-01 00:01:10,261 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-06-01 00:01:10,694 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-06-01 00:01:10,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1042308.0, 'new_value': 1094611.0}, {'field': 'total_amount', 'old_value': 1042308.0, 'new_value': 1094611.0}, {'field': 'order_count', 'old_value': 51161, 'new_value': 51201}]
2025-06-01 00:01:10,694 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-06-01 00:01:11,151 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-06-01 00:01:11,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116800.0, 'new_value': 146800.0}, {'field': 'total_amount', 'old_value': 116800.0, 'new_value': 146800.0}]
2025-06-01 00:01:11,152 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-06-01 00:01:11,568 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-06-01 00:01:11,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37718.7, 'new_value': 39413.7}, {'field': 'total_amount', 'old_value': 37718.7, 'new_value': 39413.7}, {'field': 'order_count', 'old_value': 225, 'new_value': 237}]
2025-06-01 00:01:11,569 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-06-01 00:01:12,080 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-06-01 00:01:12,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280860.74, 'new_value': 300116.74}, {'field': 'total_amount', 'old_value': 280860.74, 'new_value': 300116.74}, {'field': 'order_count', 'old_value': 1725, 'new_value': 1829}]
2025-06-01 00:01:12,080 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-06-01 00:01:12,524 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-06-01 00:01:12,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87412.86, 'new_value': 88686.66}, {'field': 'offline_amount', 'old_value': 224195.33, 'new_value': 237400.5}, {'field': 'total_amount', 'old_value': 311608.19, 'new_value': 326087.16}, {'field': 'order_count', 'old_value': 6127, 'new_value': 6353}]
2025-06-01 00:01:12,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-06-01 00:01:12,991 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-06-01 00:01:12,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 284472.73, 'new_value': 296579.12}, {'field': 'offline_amount', 'old_value': 836513.71, 'new_value': 886321.18}, {'field': 'total_amount', 'old_value': 1120986.44, 'new_value': 1182900.3}, {'field': 'order_count', 'old_value': 6814, 'new_value': 7125}]
2025-06-01 00:01:12,992 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-06-01 00:01:13,392 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-06-01 00:01:13,392 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42044.81, 'new_value': 44587.32}, {'field': 'offline_amount', 'old_value': 400997.85, 'new_value': 429200.85}, {'field': 'total_amount', 'old_value': 443042.66, 'new_value': 473788.17}, {'field': 'order_count', 'old_value': 10360, 'new_value': 10538}]
2025-06-01 00:01:13,393 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-06-01 00:01:13,850 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-06-01 00:01:13,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205995.32, 'new_value': 216937.59}, {'field': 'total_amount', 'old_value': 205995.32, 'new_value': 216937.59}, {'field': 'order_count', 'old_value': 8773, 'new_value': 9215}]
2025-06-01 00:01:13,850 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-06-01 00:01:14,300 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-06-01 00:01:14,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94051.2, 'new_value': 98149.6}, {'field': 'total_amount', 'old_value': 96514.8, 'new_value': 100613.2}, {'field': 'order_count', 'old_value': 610, 'new_value': 638}]
2025-06-01 00:01:14,300 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-06-01 00:01:14,713 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-06-01 00:01:14,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57274.0, 'new_value': 60828.0}, {'field': 'total_amount', 'old_value': 57274.0, 'new_value': 60828.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 145}]
2025-06-01 00:01:14,713 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-06-01 00:01:15,162 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-06-01 00:01:15,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 359.0, 'new_value': 728.0}, {'field': 'offline_amount', 'old_value': 39705.0, 'new_value': 43409.0}, {'field': 'total_amount', 'old_value': 40064.0, 'new_value': 44137.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 66}]
2025-06-01 00:01:15,162 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-06-01 00:01:15,598 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-06-01 00:01:15,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43556.75, 'new_value': 46211.86}, {'field': 'offline_amount', 'old_value': 54243.85, 'new_value': 56243.85}, {'field': 'total_amount', 'old_value': 97800.6, 'new_value': 102455.71}, {'field': 'order_count', 'old_value': 4795, 'new_value': 4964}]
2025-06-01 00:01:15,599 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-06-01 00:01:16,110 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-06-01 00:01:16,110 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49613.43, 'new_value': 51053.55}, {'field': 'offline_amount', 'old_value': 27621.69, 'new_value': 29191.12}, {'field': 'total_amount', 'old_value': 77235.12, 'new_value': 80244.67}, {'field': 'order_count', 'old_value': 3416, 'new_value': 3543}]
2025-06-01 00:01:16,110 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-06-01 00:01:16,552 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-06-01 00:01:16,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16870.51, 'new_value': 17250.4}, {'field': 'offline_amount', 'old_value': 39287.4, 'new_value': 41703.2}, {'field': 'total_amount', 'old_value': 56157.91, 'new_value': 58953.6}, {'field': 'order_count', 'old_value': 2245, 'new_value': 2358}]
2025-06-01 00:01:16,553 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-06-01 00:01:16,969 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-06-01 00:01:16,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129009.0, 'new_value': 129911.0}, {'field': 'total_amount', 'old_value': 129009.0, 'new_value': 129911.0}, {'field': 'order_count', 'old_value': 1700, 'new_value': 1701}]
2025-06-01 00:01:16,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-06-01 00:01:17,380 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-06-01 00:01:17,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 689758.66, 'new_value': 692656.66}, {'field': 'total_amount', 'old_value': 749758.66, 'new_value': 752656.66}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-06-01 00:01:17,381 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-06-01 00:01:17,833 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-06-01 00:01:17,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 672942.15, 'new_value': 694628.15}, {'field': 'total_amount', 'old_value': 672942.15, 'new_value': 694628.15}, {'field': 'order_count', 'old_value': 596, 'new_value': 625}]
2025-06-01 00:01:17,835 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-06-01 00:01:18,318 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-06-01 00:01:18,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80372.1, 'new_value': 86052.1}, {'field': 'total_amount', 'old_value': 80651.9, 'new_value': 86331.9}, {'field': 'order_count', 'old_value': 1190, 'new_value': 1278}]
2025-06-01 00:01:18,319 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-06-01 00:01:18,717 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-06-01 00:01:18,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 519511.0, 'new_value': 541346.0}, {'field': 'total_amount', 'old_value': 525468.0, 'new_value': 547303.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 120}]
2025-06-01 00:01:18,717 - INFO - 日期 2025-05 处理完成 - 更新: 109 条，插入: 0 条，错误: 0 条
2025-06-01 00:01:18,717 - INFO - 数据同步完成！更新: 109 条，插入: 0 条，错误: 0 条
2025-06-01 00:01:18,720 - INFO - =================同步完成====================
2025-06-01 03:00:02,676 - INFO - =================使用默认全量同步=============
2025-06-01 03:00:04,207 - INFO - MySQL查询成功，共获取 3305 条记录
2025-06-01 03:00:04,207 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-06-01 03:00:04,238 - INFO - 开始处理日期: 2025-01
2025-06-01 03:00:04,238 - INFO - Request Parameters - Page 1:
2025-06-01 03:00:04,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:04,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:05,348 - INFO - Response - Page 1:
2025-06-01 03:00:05,551 - INFO - 第 1 页获取到 100 条记录
2025-06-01 03:00:05,551 - INFO - Request Parameters - Page 2:
2025-06-01 03:00:05,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:05,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:06,348 - INFO - Response - Page 2:
2025-06-01 03:00:06,551 - INFO - 第 2 页获取到 100 条记录
2025-06-01 03:00:06,551 - INFO - Request Parameters - Page 3:
2025-06-01 03:00:06,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:06,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:07,066 - INFO - Response - Page 3:
2025-06-01 03:00:07,270 - INFO - 第 3 页获取到 100 条记录
2025-06-01 03:00:07,270 - INFO - Request Parameters - Page 4:
2025-06-01 03:00:07,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:07,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:07,801 - INFO - Response - Page 4:
2025-06-01 03:00:08,004 - INFO - 第 4 页获取到 100 条记录
2025-06-01 03:00:08,004 - INFO - Request Parameters - Page 5:
2025-06-01 03:00:08,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:08,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:08,551 - INFO - Response - Page 5:
2025-06-01 03:00:08,754 - INFO - 第 5 页获取到 100 条记录
2025-06-01 03:00:08,754 - INFO - Request Parameters - Page 6:
2025-06-01 03:00:08,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:08,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:09,254 - INFO - Response - Page 6:
2025-06-01 03:00:09,457 - INFO - 第 6 页获取到 100 条记录
2025-06-01 03:00:09,457 - INFO - Request Parameters - Page 7:
2025-06-01 03:00:09,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:09,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:09,957 - INFO - Response - Page 7:
2025-06-01 03:00:10,160 - INFO - 第 7 页获取到 82 条记录
2025-06-01 03:00:10,160 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 03:00:10,160 - INFO - 获取到 682 条表单数据
2025-06-01 03:00:10,160 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 03:00:10,176 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 03:00:10,176 - INFO - 开始处理日期: 2025-02
2025-06-01 03:00:10,176 - INFO - Request Parameters - Page 1:
2025-06-01 03:00:10,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:10,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:10,801 - INFO - Response - Page 1:
2025-06-01 03:00:11,004 - INFO - 第 1 页获取到 100 条记录
2025-06-01 03:00:11,004 - INFO - Request Parameters - Page 2:
2025-06-01 03:00:11,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:11,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:11,644 - INFO - Response - Page 2:
2025-06-01 03:00:11,848 - INFO - 第 2 页获取到 100 条记录
2025-06-01 03:00:11,848 - INFO - Request Parameters - Page 3:
2025-06-01 03:00:11,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:11,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:12,316 - INFO - Response - Page 3:
2025-06-01 03:00:12,519 - INFO - 第 3 页获取到 100 条记录
2025-06-01 03:00:12,519 - INFO - Request Parameters - Page 4:
2025-06-01 03:00:12,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:12,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:13,019 - INFO - Response - Page 4:
2025-06-01 03:00:13,223 - INFO - 第 4 页获取到 100 条记录
2025-06-01 03:00:13,223 - INFO - Request Parameters - Page 5:
2025-06-01 03:00:13,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:13,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:13,941 - INFO - Response - Page 5:
2025-06-01 03:00:14,144 - INFO - 第 5 页获取到 100 条记录
2025-06-01 03:00:14,144 - INFO - Request Parameters - Page 6:
2025-06-01 03:00:14,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:14,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:14,613 - INFO - Response - Page 6:
2025-06-01 03:00:14,816 - INFO - 第 6 页获取到 100 条记录
2025-06-01 03:00:14,816 - INFO - Request Parameters - Page 7:
2025-06-01 03:00:14,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:14,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:15,254 - INFO - Response - Page 7:
2025-06-01 03:00:15,457 - INFO - 第 7 页获取到 70 条记录
2025-06-01 03:00:15,457 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 03:00:15,457 - INFO - 获取到 670 条表单数据
2025-06-01 03:00:15,457 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 03:00:15,473 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 03:00:15,473 - INFO - 开始处理日期: 2025-03
2025-06-01 03:00:15,473 - INFO - Request Parameters - Page 1:
2025-06-01 03:00:15,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:15,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:15,973 - INFO - Response - Page 1:
2025-06-01 03:00:16,176 - INFO - 第 1 页获取到 100 条记录
2025-06-01 03:00:16,176 - INFO - Request Parameters - Page 2:
2025-06-01 03:00:16,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:16,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:16,691 - INFO - Response - Page 2:
2025-06-01 03:00:16,894 - INFO - 第 2 页获取到 100 条记录
2025-06-01 03:00:16,894 - INFO - Request Parameters - Page 3:
2025-06-01 03:00:16,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:16,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:17,426 - INFO - Response - Page 3:
2025-06-01 03:00:17,629 - INFO - 第 3 页获取到 100 条记录
2025-06-01 03:00:17,629 - INFO - Request Parameters - Page 4:
2025-06-01 03:00:17,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:17,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:18,113 - INFO - Response - Page 4:
2025-06-01 03:00:18,316 - INFO - 第 4 页获取到 100 条记录
2025-06-01 03:00:18,316 - INFO - Request Parameters - Page 5:
2025-06-01 03:00:18,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:18,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:18,879 - INFO - Response - Page 5:
2025-06-01 03:00:19,082 - INFO - 第 5 页获取到 100 条记录
2025-06-01 03:00:19,082 - INFO - Request Parameters - Page 6:
2025-06-01 03:00:19,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:19,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:19,598 - INFO - Response - Page 6:
2025-06-01 03:00:19,801 - INFO - 第 6 页获取到 100 条记录
2025-06-01 03:00:19,801 - INFO - Request Parameters - Page 7:
2025-06-01 03:00:19,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:19,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:20,238 - INFO - Response - Page 7:
2025-06-01 03:00:20,441 - INFO - 第 7 页获取到 61 条记录
2025-06-01 03:00:20,441 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 03:00:20,441 - INFO - 获取到 661 条表单数据
2025-06-01 03:00:20,441 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 03:00:20,457 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 03:00:20,457 - INFO - 开始处理日期: 2025-04
2025-06-01 03:00:20,457 - INFO - Request Parameters - Page 1:
2025-06-01 03:00:20,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:20,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:21,082 - INFO - Response - Page 1:
2025-06-01 03:00:21,285 - INFO - 第 1 页获取到 100 条记录
2025-06-01 03:00:21,285 - INFO - Request Parameters - Page 2:
2025-06-01 03:00:21,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:21,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:21,801 - INFO - Response - Page 2:
2025-06-01 03:00:22,004 - INFO - 第 2 页获取到 100 条记录
2025-06-01 03:00:22,004 - INFO - Request Parameters - Page 3:
2025-06-01 03:00:22,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:22,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:22,473 - INFO - Response - Page 3:
2025-06-01 03:00:22,676 - INFO - 第 3 页获取到 100 条记录
2025-06-01 03:00:22,676 - INFO - Request Parameters - Page 4:
2025-06-01 03:00:22,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:22,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:23,207 - INFO - Response - Page 4:
2025-06-01 03:00:23,410 - INFO - 第 4 页获取到 100 条记录
2025-06-01 03:00:23,410 - INFO - Request Parameters - Page 5:
2025-06-01 03:00:23,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:23,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:23,972 - INFO - Response - Page 5:
2025-06-01 03:00:24,176 - INFO - 第 5 页获取到 100 条记录
2025-06-01 03:00:24,176 - INFO - Request Parameters - Page 6:
2025-06-01 03:00:24,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:24,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:24,722 - INFO - Response - Page 6:
2025-06-01 03:00:24,926 - INFO - 第 6 页获取到 100 条记录
2025-06-01 03:00:24,926 - INFO - Request Parameters - Page 7:
2025-06-01 03:00:24,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:24,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:25,316 - INFO - Response - Page 7:
2025-06-01 03:00:25,519 - INFO - 第 7 页获取到 56 条记录
2025-06-01 03:00:25,519 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 03:00:25,519 - INFO - 获取到 656 条表单数据
2025-06-01 03:00:25,519 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 03:00:25,535 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 03:00:25,535 - INFO - 开始处理日期: 2025-05
2025-06-01 03:00:25,535 - INFO - Request Parameters - Page 1:
2025-06-01 03:00:25,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:25,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:26,051 - INFO - Response - Page 1:
2025-06-01 03:00:26,254 - INFO - 第 1 页获取到 100 条记录
2025-06-01 03:00:26,254 - INFO - Request Parameters - Page 2:
2025-06-01 03:00:26,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:26,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:26,832 - INFO - Response - Page 2:
2025-06-01 03:00:27,035 - INFO - 第 2 页获取到 100 条记录
2025-06-01 03:00:27,035 - INFO - Request Parameters - Page 3:
2025-06-01 03:00:27,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:27,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:27,566 - INFO - Response - Page 3:
2025-06-01 03:00:27,769 - INFO - 第 3 页获取到 100 条记录
2025-06-01 03:00:27,769 - INFO - Request Parameters - Page 4:
2025-06-01 03:00:27,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:27,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:28,316 - INFO - Response - Page 4:
2025-06-01 03:00:28,519 - INFO - 第 4 页获取到 100 条记录
2025-06-01 03:00:28,519 - INFO - Request Parameters - Page 5:
2025-06-01 03:00:28,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:28,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:28,957 - INFO - Response - Page 5:
2025-06-01 03:00:29,160 - INFO - 第 5 页获取到 100 条记录
2025-06-01 03:00:29,160 - INFO - Request Parameters - Page 6:
2025-06-01 03:00:29,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:29,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:29,660 - INFO - Response - Page 6:
2025-06-01 03:00:29,863 - INFO - 第 6 页获取到 100 条记录
2025-06-01 03:00:29,863 - INFO - Request Parameters - Page 7:
2025-06-01 03:00:29,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 03:00:29,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 03:00:30,426 - INFO - Response - Page 7:
2025-06-01 03:00:30,629 - INFO - 第 7 页获取到 36 条记录
2025-06-01 03:00:30,629 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 03:00:30,629 - INFO - 获取到 636 条表单数据
2025-06-01 03:00:30,629 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 03:00:30,644 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 03:00:30,644 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 03:00:30,644 - INFO - =================同步完成====================
2025-06-01 06:00:02,582 - INFO - =================使用默认全量同步=============
2025-06-01 06:00:04,114 - INFO - MySQL查询成功，共获取 3305 条记录
2025-06-01 06:00:04,114 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-06-01 06:00:04,129 - INFO - 开始处理日期: 2025-01
2025-06-01 06:00:04,145 - INFO - Request Parameters - Page 1:
2025-06-01 06:00:04,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:04,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:05,395 - INFO - Response - Page 1:
2025-06-01 06:00:05,598 - INFO - 第 1 页获取到 100 条记录
2025-06-01 06:00:05,598 - INFO - Request Parameters - Page 2:
2025-06-01 06:00:05,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:05,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:06,114 - INFO - Response - Page 2:
2025-06-01 06:00:06,317 - INFO - 第 2 页获取到 100 条记录
2025-06-01 06:00:06,317 - INFO - Request Parameters - Page 3:
2025-06-01 06:00:06,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:06,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:06,864 - INFO - Response - Page 3:
2025-06-01 06:00:07,067 - INFO - 第 3 页获取到 100 条记录
2025-06-01 06:00:07,067 - INFO - Request Parameters - Page 4:
2025-06-01 06:00:07,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:07,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:07,598 - INFO - Response - Page 4:
2025-06-01 06:00:07,801 - INFO - 第 4 页获取到 100 条记录
2025-06-01 06:00:07,801 - INFO - Request Parameters - Page 5:
2025-06-01 06:00:07,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:07,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:08,332 - INFO - Response - Page 5:
2025-06-01 06:00:08,535 - INFO - 第 5 页获取到 100 条记录
2025-06-01 06:00:08,535 - INFO - Request Parameters - Page 6:
2025-06-01 06:00:08,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:08,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:09,082 - INFO - Response - Page 6:
2025-06-01 06:00:09,285 - INFO - 第 6 页获取到 100 条记录
2025-06-01 06:00:09,285 - INFO - Request Parameters - Page 7:
2025-06-01 06:00:09,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:09,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:09,770 - INFO - Response - Page 7:
2025-06-01 06:00:09,973 - INFO - 第 7 页获取到 82 条记录
2025-06-01 06:00:09,973 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 06:00:09,973 - INFO - 获取到 682 条表单数据
2025-06-01 06:00:09,973 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 06:00:09,989 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 06:00:09,989 - INFO - 开始处理日期: 2025-02
2025-06-01 06:00:09,989 - INFO - Request Parameters - Page 1:
2025-06-01 06:00:09,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:09,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:10,520 - INFO - Response - Page 1:
2025-06-01 06:00:10,723 - INFO - 第 1 页获取到 100 条记录
2025-06-01 06:00:10,723 - INFO - Request Parameters - Page 2:
2025-06-01 06:00:10,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:10,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:11,270 - INFO - Response - Page 2:
2025-06-01 06:00:11,473 - INFO - 第 2 页获取到 100 条记录
2025-06-01 06:00:11,473 - INFO - Request Parameters - Page 3:
2025-06-01 06:00:11,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:11,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:12,082 - INFO - Response - Page 3:
2025-06-01 06:00:12,285 - INFO - 第 3 页获取到 100 条记录
2025-06-01 06:00:12,285 - INFO - Request Parameters - Page 4:
2025-06-01 06:00:12,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:12,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:12,801 - INFO - Response - Page 4:
2025-06-01 06:00:13,004 - INFO - 第 4 页获取到 100 条记录
2025-06-01 06:00:13,004 - INFO - Request Parameters - Page 5:
2025-06-01 06:00:13,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:13,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:13,520 - INFO - Response - Page 5:
2025-06-01 06:00:13,723 - INFO - 第 5 页获取到 100 条记录
2025-06-01 06:00:13,723 - INFO - Request Parameters - Page 6:
2025-06-01 06:00:13,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:13,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:14,207 - INFO - Response - Page 6:
2025-06-01 06:00:14,410 - INFO - 第 6 页获取到 100 条记录
2025-06-01 06:00:14,410 - INFO - Request Parameters - Page 7:
2025-06-01 06:00:14,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:14,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:14,864 - INFO - Response - Page 7:
2025-06-01 06:00:15,067 - INFO - 第 7 页获取到 70 条记录
2025-06-01 06:00:15,067 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 06:00:15,067 - INFO - 获取到 670 条表单数据
2025-06-01 06:00:15,067 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 06:00:15,082 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 06:00:15,082 - INFO - 开始处理日期: 2025-03
2025-06-01 06:00:15,082 - INFO - Request Parameters - Page 1:
2025-06-01 06:00:15,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:15,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:15,567 - INFO - Response - Page 1:
2025-06-01 06:00:15,770 - INFO - 第 1 页获取到 100 条记录
2025-06-01 06:00:15,770 - INFO - Request Parameters - Page 2:
2025-06-01 06:00:15,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:15,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:16,270 - INFO - Response - Page 2:
2025-06-01 06:00:16,473 - INFO - 第 2 页获取到 100 条记录
2025-06-01 06:00:16,473 - INFO - Request Parameters - Page 3:
2025-06-01 06:00:16,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:16,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:16,973 - INFO - Response - Page 3:
2025-06-01 06:00:17,176 - INFO - 第 3 页获取到 100 条记录
2025-06-01 06:00:17,176 - INFO - Request Parameters - Page 4:
2025-06-01 06:00:17,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:17,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:17,739 - INFO - Response - Page 4:
2025-06-01 06:00:17,942 - INFO - 第 4 页获取到 100 条记录
2025-06-01 06:00:17,942 - INFO - Request Parameters - Page 5:
2025-06-01 06:00:17,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:17,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:18,457 - INFO - Response - Page 5:
2025-06-01 06:00:18,660 - INFO - 第 5 页获取到 100 条记录
2025-06-01 06:00:18,660 - INFO - Request Parameters - Page 6:
2025-06-01 06:00:18,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:18,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:19,145 - INFO - Response - Page 6:
2025-06-01 06:00:19,348 - INFO - 第 6 页获取到 100 条记录
2025-06-01 06:00:19,348 - INFO - Request Parameters - Page 7:
2025-06-01 06:00:19,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:19,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:19,801 - INFO - Response - Page 7:
2025-06-01 06:00:20,004 - INFO - 第 7 页获取到 61 条记录
2025-06-01 06:00:20,004 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 06:00:20,004 - INFO - 获取到 661 条表单数据
2025-06-01 06:00:20,004 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 06:00:20,020 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 06:00:20,020 - INFO - 开始处理日期: 2025-04
2025-06-01 06:00:20,020 - INFO - Request Parameters - Page 1:
2025-06-01 06:00:20,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:20,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:20,629 - INFO - Response - Page 1:
2025-06-01 06:00:20,832 - INFO - 第 1 页获取到 100 条记录
2025-06-01 06:00:20,832 - INFO - Request Parameters - Page 2:
2025-06-01 06:00:20,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:20,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:21,301 - INFO - Response - Page 2:
2025-06-01 06:00:21,504 - INFO - 第 2 页获取到 100 条记录
2025-06-01 06:00:21,504 - INFO - Request Parameters - Page 3:
2025-06-01 06:00:21,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:21,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:22,020 - INFO - Response - Page 3:
2025-06-01 06:00:22,223 - INFO - 第 3 页获取到 100 条记录
2025-06-01 06:00:22,223 - INFO - Request Parameters - Page 4:
2025-06-01 06:00:22,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:22,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:22,770 - INFO - Response - Page 4:
2025-06-01 06:00:22,973 - INFO - 第 4 页获取到 100 条记录
2025-06-01 06:00:22,973 - INFO - Request Parameters - Page 5:
2025-06-01 06:00:22,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:22,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:23,504 - INFO - Response - Page 5:
2025-06-01 06:00:23,707 - INFO - 第 5 页获取到 100 条记录
2025-06-01 06:00:23,707 - INFO - Request Parameters - Page 6:
2025-06-01 06:00:23,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:23,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:24,254 - INFO - Response - Page 6:
2025-06-01 06:00:24,457 - INFO - 第 6 页获取到 100 条记录
2025-06-01 06:00:24,457 - INFO - Request Parameters - Page 7:
2025-06-01 06:00:24,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:24,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:24,895 - INFO - Response - Page 7:
2025-06-01 06:00:25,098 - INFO - 第 7 页获取到 56 条记录
2025-06-01 06:00:25,098 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 06:00:25,098 - INFO - 获取到 656 条表单数据
2025-06-01 06:00:25,098 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 06:00:25,113 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 06:00:25,113 - INFO - 开始处理日期: 2025-05
2025-06-01 06:00:25,113 - INFO - Request Parameters - Page 1:
2025-06-01 06:00:25,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:25,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:25,645 - INFO - Response - Page 1:
2025-06-01 06:00:25,848 - INFO - 第 1 页获取到 100 条记录
2025-06-01 06:00:25,848 - INFO - Request Parameters - Page 2:
2025-06-01 06:00:25,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:25,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:26,317 - INFO - Response - Page 2:
2025-06-01 06:00:26,520 - INFO - 第 2 页获取到 100 条记录
2025-06-01 06:00:26,520 - INFO - Request Parameters - Page 3:
2025-06-01 06:00:26,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:26,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:27,051 - INFO - Response - Page 3:
2025-06-01 06:00:27,254 - INFO - 第 3 页获取到 100 条记录
2025-06-01 06:00:27,254 - INFO - Request Parameters - Page 4:
2025-06-01 06:00:27,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:27,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:27,707 - INFO - Response - Page 4:
2025-06-01 06:00:27,910 - INFO - 第 4 页获取到 100 条记录
2025-06-01 06:00:27,910 - INFO - Request Parameters - Page 5:
2025-06-01 06:00:27,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:27,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:28,395 - INFO - Response - Page 5:
2025-06-01 06:00:28,598 - INFO - 第 5 页获取到 100 条记录
2025-06-01 06:00:28,598 - INFO - Request Parameters - Page 6:
2025-06-01 06:00:28,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:28,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:29,113 - INFO - Response - Page 6:
2025-06-01 06:00:29,317 - INFO - 第 6 页获取到 100 条记录
2025-06-01 06:00:29,317 - INFO - Request Parameters - Page 7:
2025-06-01 06:00:29,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 06:00:29,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 06:00:29,738 - INFO - Response - Page 7:
2025-06-01 06:00:29,942 - INFO - 第 7 页获取到 36 条记录
2025-06-01 06:00:29,942 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 06:00:29,942 - INFO - 获取到 636 条表单数据
2025-06-01 06:00:29,942 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 06:00:29,957 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 06:00:29,957 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 06:00:29,957 - INFO - =================同步完成====================
2025-06-01 09:00:02,542 - INFO - =================使用默认全量同步=============
2025-06-01 09:00:04,089 - INFO - MySQL查询成功，共获取 3305 条记录
2025-06-01 09:00:04,089 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-06-01 09:00:04,104 - INFO - 开始处理日期: 2025-01
2025-06-01 09:00:04,120 - INFO - Request Parameters - Page 1:
2025-06-01 09:00:04,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:04,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:05,386 - INFO - Response - Page 1:
2025-06-01 09:00:05,589 - INFO - 第 1 页获取到 100 条记录
2025-06-01 09:00:05,589 - INFO - Request Parameters - Page 2:
2025-06-01 09:00:05,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:05,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:06,151 - INFO - Response - Page 2:
2025-06-01 09:00:06,354 - INFO - 第 2 页获取到 100 条记录
2025-06-01 09:00:06,354 - INFO - Request Parameters - Page 3:
2025-06-01 09:00:06,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:06,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:06,885 - INFO - Response - Page 3:
2025-06-01 09:00:07,089 - INFO - 第 3 页获取到 100 条记录
2025-06-01 09:00:07,089 - INFO - Request Parameters - Page 4:
2025-06-01 09:00:07,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:07,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:07,620 - INFO - Response - Page 4:
2025-06-01 09:00:07,823 - INFO - 第 4 页获取到 100 条记录
2025-06-01 09:00:07,823 - INFO - Request Parameters - Page 5:
2025-06-01 09:00:07,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:07,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:08,401 - INFO - Response - Page 5:
2025-06-01 09:00:08,604 - INFO - 第 5 页获取到 100 条记录
2025-06-01 09:00:08,604 - INFO - Request Parameters - Page 6:
2025-06-01 09:00:08,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:08,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:09,245 - INFO - Response - Page 6:
2025-06-01 09:00:09,448 - INFO - 第 6 页获取到 100 条记录
2025-06-01 09:00:09,448 - INFO - Request Parameters - Page 7:
2025-06-01 09:00:09,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:09,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:09,885 - INFO - Response - Page 7:
2025-06-01 09:00:10,089 - INFO - 第 7 页获取到 82 条记录
2025-06-01 09:00:10,089 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 09:00:10,089 - INFO - 获取到 682 条表单数据
2025-06-01 09:00:10,089 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 09:00:10,104 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 09:00:10,104 - INFO - 开始处理日期: 2025-02
2025-06-01 09:00:10,104 - INFO - Request Parameters - Page 1:
2025-06-01 09:00:10,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:10,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:10,682 - INFO - Response - Page 1:
2025-06-01 09:00:10,885 - INFO - 第 1 页获取到 100 条记录
2025-06-01 09:00:10,885 - INFO - Request Parameters - Page 2:
2025-06-01 09:00:10,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:10,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:11,401 - INFO - Response - Page 2:
2025-06-01 09:00:11,604 - INFO - 第 2 页获取到 100 条记录
2025-06-01 09:00:11,604 - INFO - Request Parameters - Page 3:
2025-06-01 09:00:11,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:11,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:12,198 - INFO - Response - Page 3:
2025-06-01 09:00:12,401 - INFO - 第 3 页获取到 100 条记录
2025-06-01 09:00:12,401 - INFO - Request Parameters - Page 4:
2025-06-01 09:00:12,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:12,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:12,948 - INFO - Response - Page 4:
2025-06-01 09:00:13,151 - INFO - 第 4 页获取到 100 条记录
2025-06-01 09:00:13,151 - INFO - Request Parameters - Page 5:
2025-06-01 09:00:13,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:13,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:13,698 - INFO - Response - Page 5:
2025-06-01 09:00:13,901 - INFO - 第 5 页获取到 100 条记录
2025-06-01 09:00:13,901 - INFO - Request Parameters - Page 6:
2025-06-01 09:00:13,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:13,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:14,464 - INFO - Response - Page 6:
2025-06-01 09:00:14,667 - INFO - 第 6 页获取到 100 条记录
2025-06-01 09:00:14,667 - INFO - Request Parameters - Page 7:
2025-06-01 09:00:14,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:14,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:15,135 - INFO - Response - Page 7:
2025-06-01 09:00:15,339 - INFO - 第 7 页获取到 70 条记录
2025-06-01 09:00:15,339 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 09:00:15,339 - INFO - 获取到 670 条表单数据
2025-06-01 09:00:15,339 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 09:00:15,354 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 09:00:15,354 - INFO - 开始处理日期: 2025-03
2025-06-01 09:00:15,354 - INFO - Request Parameters - Page 1:
2025-06-01 09:00:15,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:15,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:15,885 - INFO - Response - Page 1:
2025-06-01 09:00:16,089 - INFO - 第 1 页获取到 100 条记录
2025-06-01 09:00:16,089 - INFO - Request Parameters - Page 2:
2025-06-01 09:00:16,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:16,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:16,635 - INFO - Response - Page 2:
2025-06-01 09:00:16,839 - INFO - 第 2 页获取到 100 条记录
2025-06-01 09:00:16,839 - INFO - Request Parameters - Page 3:
2025-06-01 09:00:16,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:16,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:17,370 - INFO - Response - Page 3:
2025-06-01 09:00:17,573 - INFO - 第 3 页获取到 100 条记录
2025-06-01 09:00:17,573 - INFO - Request Parameters - Page 4:
2025-06-01 09:00:17,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:17,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:18,214 - INFO - Response - Page 4:
2025-06-01 09:00:18,417 - INFO - 第 4 页获取到 100 条记录
2025-06-01 09:00:18,417 - INFO - Request Parameters - Page 5:
2025-06-01 09:00:18,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:18,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:18,885 - INFO - Response - Page 5:
2025-06-01 09:00:19,089 - INFO - 第 5 页获取到 100 条记录
2025-06-01 09:00:19,089 - INFO - Request Parameters - Page 6:
2025-06-01 09:00:19,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:19,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:19,589 - INFO - Response - Page 6:
2025-06-01 09:00:19,792 - INFO - 第 6 页获取到 100 条记录
2025-06-01 09:00:19,792 - INFO - Request Parameters - Page 7:
2025-06-01 09:00:19,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:19,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:20,292 - INFO - Response - Page 7:
2025-06-01 09:00:20,495 - INFO - 第 7 页获取到 61 条记录
2025-06-01 09:00:20,495 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 09:00:20,495 - INFO - 获取到 661 条表单数据
2025-06-01 09:00:20,495 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 09:00:20,510 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 09:00:20,510 - INFO - 开始处理日期: 2025-04
2025-06-01 09:00:20,510 - INFO - Request Parameters - Page 1:
2025-06-01 09:00:20,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:20,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:21,120 - INFO - Response - Page 1:
2025-06-01 09:00:21,323 - INFO - 第 1 页获取到 100 条记录
2025-06-01 09:00:21,323 - INFO - Request Parameters - Page 2:
2025-06-01 09:00:21,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:21,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:21,823 - INFO - Response - Page 2:
2025-06-01 09:00:22,026 - INFO - 第 2 页获取到 100 条记录
2025-06-01 09:00:22,026 - INFO - Request Parameters - Page 3:
2025-06-01 09:00:22,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:22,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:22,495 - INFO - Response - Page 3:
2025-06-01 09:00:22,698 - INFO - 第 3 页获取到 100 条记录
2025-06-01 09:00:22,698 - INFO - Request Parameters - Page 4:
2025-06-01 09:00:22,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:22,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:23,229 - INFO - Response - Page 4:
2025-06-01 09:00:23,432 - INFO - 第 4 页获取到 100 条记录
2025-06-01 09:00:23,432 - INFO - Request Parameters - Page 5:
2025-06-01 09:00:23,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:23,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:23,979 - INFO - Response - Page 5:
2025-06-01 09:00:24,182 - INFO - 第 5 页获取到 100 条记录
2025-06-01 09:00:24,182 - INFO - Request Parameters - Page 6:
2025-06-01 09:00:24,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:24,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:24,651 - INFO - Response - Page 6:
2025-06-01 09:00:24,854 - INFO - 第 6 页获取到 100 条记录
2025-06-01 09:00:24,854 - INFO - Request Parameters - Page 7:
2025-06-01 09:00:24,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:24,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:25,370 - INFO - Response - Page 7:
2025-06-01 09:00:25,573 - INFO - 第 7 页获取到 56 条记录
2025-06-01 09:00:25,573 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 09:00:25,573 - INFO - 获取到 656 条表单数据
2025-06-01 09:00:25,573 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 09:00:25,588 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 09:00:25,588 - INFO - 开始处理日期: 2025-05
2025-06-01 09:00:25,588 - INFO - Request Parameters - Page 1:
2025-06-01 09:00:25,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:25,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:26,104 - INFO - Response - Page 1:
2025-06-01 09:00:26,307 - INFO - 第 1 页获取到 100 条记录
2025-06-01 09:00:26,307 - INFO - Request Parameters - Page 2:
2025-06-01 09:00:26,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:26,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:26,870 - INFO - Response - Page 2:
2025-06-01 09:00:27,073 - INFO - 第 2 页获取到 100 条记录
2025-06-01 09:00:27,073 - INFO - Request Parameters - Page 3:
2025-06-01 09:00:27,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:27,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:27,604 - INFO - Response - Page 3:
2025-06-01 09:00:27,807 - INFO - 第 3 页获取到 100 条记录
2025-06-01 09:00:27,807 - INFO - Request Parameters - Page 4:
2025-06-01 09:00:27,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:27,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:28,323 - INFO - Response - Page 4:
2025-06-01 09:00:28,526 - INFO - 第 4 页获取到 100 条记录
2025-06-01 09:00:28,526 - INFO - Request Parameters - Page 5:
2025-06-01 09:00:28,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:28,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:29,042 - INFO - Response - Page 5:
2025-06-01 09:00:29,245 - INFO - 第 5 页获取到 100 条记录
2025-06-01 09:00:29,245 - INFO - Request Parameters - Page 6:
2025-06-01 09:00:29,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:29,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:29,667 - INFO - Response - Page 6:
2025-06-01 09:00:29,870 - INFO - 第 6 页获取到 100 条记录
2025-06-01 09:00:29,870 - INFO - Request Parameters - Page 7:
2025-06-01 09:00:29,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 09:00:29,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 09:00:30,307 - INFO - Response - Page 7:
2025-06-01 09:00:30,510 - INFO - 第 7 页获取到 36 条记录
2025-06-01 09:00:30,510 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 09:00:30,510 - INFO - 获取到 636 条表单数据
2025-06-01 09:00:30,510 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 09:00:30,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-06-01 09:00:31,135 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-06-01 09:00:31,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 427293.0, 'new_value': 434304.0}, {'field': 'total_amount', 'old_value': 427293.0, 'new_value': 434304.0}, {'field': 'order_count', 'old_value': 321, 'new_value': 327}]
2025-06-01 09:00:31,135 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-06-01 09:00:31,651 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-06-01 09:00:31,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12100080.0, 'new_value': 12400080.0}, {'field': 'total_amount', 'old_value': 12200080.0, 'new_value': 12500080.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 65}]
2025-06-01 09:00:31,667 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-06-01 09:00:32,151 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-06-01 09:00:32,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486200.0, 'new_value': 517660.0}, {'field': 'total_amount', 'old_value': 486200.0, 'new_value': 517660.0}, {'field': 'order_count', 'old_value': 299, 'new_value': 319}]
2025-06-01 09:00:32,151 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-06-01 09:00:32,557 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-06-01 09:00:32,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128536.0, 'new_value': 131190.0}, {'field': 'total_amount', 'old_value': 128536.0, 'new_value': 131190.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 150}]
2025-06-01 09:00:32,557 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMLC
2025-06-01 09:00:33,026 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMLC
2025-06-01 09:00:33,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19498.0, 'new_value': 30697.0}, {'field': 'total_amount', 'old_value': 19498.0, 'new_value': 30697.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-01 09:00:33,026 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-06-01 09:00:33,542 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-06-01 09:00:33,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68034.97, 'new_value': 69317.42}, {'field': 'offline_amount', 'old_value': 135299.35, 'new_value': 137753.55}, {'field': 'total_amount', 'old_value': 203334.32, 'new_value': 207070.97}, {'field': 'order_count', 'old_value': 2331, 'new_value': 2369}]
2025-06-01 09:00:33,542 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-06-01 09:00:34,073 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-06-01 09:00:34,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24773.9, 'new_value': 25780.93}, {'field': 'offline_amount', 'old_value': 30097.82, 'new_value': 31407.75}, {'field': 'total_amount', 'old_value': 54871.72, 'new_value': 57188.68}, {'field': 'order_count', 'old_value': 2738, 'new_value': 2850}]
2025-06-01 09:00:34,073 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-06-01 09:00:34,542 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-06-01 09:00:34,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44920.0, 'new_value': 48400.0}, {'field': 'total_amount', 'old_value': 44920.0, 'new_value': 48400.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-01 09:00:34,542 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-06-01 09:00:34,979 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-06-01 09:00:34,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125662.7, 'new_value': 129647.7}, {'field': 'total_amount', 'old_value': 125662.7, 'new_value': 129647.7}, {'field': 'order_count', 'old_value': 6948, 'new_value': 7159}]
2025-06-01 09:00:34,979 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-06-01 09:00:35,385 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-06-01 09:00:35,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64919.5, 'new_value': 68519.5}, {'field': 'total_amount', 'old_value': 69419.5, 'new_value': 73019.5}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-01 09:00:35,385 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-06-01 09:00:35,823 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-06-01 09:00:35,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199570.0, 'new_value': 203224.0}, {'field': 'total_amount', 'old_value': 199570.0, 'new_value': 203224.0}, {'field': 'order_count', 'old_value': 422, 'new_value': 431}]
2025-06-01 09:00:35,823 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-06-01 09:00:36,276 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-06-01 09:00:36,276 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202277.9, 'new_value': 213897.86}, {'field': 'offline_amount', 'old_value': 33769.52, 'new_value': 35809.41}, {'field': 'total_amount', 'old_value': 236047.42, 'new_value': 249707.27}, {'field': 'order_count', 'old_value': 877, 'new_value': 922}]
2025-06-01 09:00:36,276 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-06-01 09:00:36,792 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-06-01 09:00:36,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10744.0, 'new_value': 10942.0}, {'field': 'total_amount', 'old_value': 13037.0, 'new_value': 13235.0}, {'field': 'order_count', 'old_value': 259, 'new_value': 265}]
2025-06-01 09:00:36,792 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-06-01 09:00:37,292 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-06-01 09:00:37,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109426.28, 'new_value': 112813.28}, {'field': 'total_amount', 'old_value': 109426.28, 'new_value': 112813.28}, {'field': 'order_count', 'old_value': 2987, 'new_value': 3068}]
2025-06-01 09:00:37,292 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-06-01 09:00:37,745 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-06-01 09:00:37,745 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183737.0, 'new_value': 190795.0}, {'field': 'offline_amount', 'old_value': 79837.18, 'new_value': 82544.33}, {'field': 'total_amount', 'old_value': 263574.18, 'new_value': 273339.33}, {'field': 'order_count', 'old_value': 1862, 'new_value': 1920}]
2025-06-01 09:00:37,745 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-06-01 09:00:38,213 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-06-01 09:00:38,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13376.31, 'new_value': 14101.03}, {'field': 'offline_amount', 'old_value': 207433.65, 'new_value': 212520.17}, {'field': 'total_amount', 'old_value': 220809.96, 'new_value': 226621.2}, {'field': 'order_count', 'old_value': 2440, 'new_value': 2502}]
2025-06-01 09:00:38,213 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-06-01 09:00:38,698 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-06-01 09:00:38,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244148.19, 'new_value': 258051.97}, {'field': 'total_amount', 'old_value': 244148.19, 'new_value': 258051.97}, {'field': 'order_count', 'old_value': 842, 'new_value': 887}]
2025-06-01 09:00:38,698 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-06-01 09:00:39,167 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-06-01 09:00:39,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174923.0, 'new_value': 180733.0}, {'field': 'total_amount', 'old_value': 174923.0, 'new_value': 180733.0}, {'field': 'order_count', 'old_value': 4448, 'new_value': 4597}]
2025-06-01 09:00:39,167 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-06-01 09:00:39,604 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-06-01 09:00:39,604 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204944.0, 'new_value': 209010.0}, {'field': 'total_amount', 'old_value': 204944.0, 'new_value': 209010.0}, {'field': 'order_count', 'old_value': 7751, 'new_value': 7893}]
2025-06-01 09:00:39,604 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-06-01 09:00:40,026 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-06-01 09:00:40,026 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161545.68, 'new_value': 163501.68}, {'field': 'offline_amount', 'old_value': 619267.17, 'new_value': 646800.17}, {'field': 'total_amount', 'old_value': 780812.85, 'new_value': 810301.85}, {'field': 'order_count', 'old_value': 1004, 'new_value': 1034}]
2025-06-01 09:00:40,026 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-06-01 09:00:40,526 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-06-01 09:00:40,526 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1987248.38, 'new_value': 2132577.08}, {'field': 'total_amount', 'old_value': 2040693.48, 'new_value': 2186022.18}, {'field': 'order_count', 'old_value': 3760, 'new_value': 3968}]
2025-06-01 09:00:40,526 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-06-01 09:00:40,979 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-06-01 09:00:40,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21817.0, 'new_value': 22861.0}, {'field': 'total_amount', 'old_value': 21817.0, 'new_value': 22861.0}, {'field': 'order_count', 'old_value': 376, 'new_value': 394}]
2025-06-01 09:00:40,979 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-06-01 09:00:41,495 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-06-01 09:00:41,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10307.95, 'new_value': 10558.15}, {'field': 'offline_amount', 'old_value': 20930.47, 'new_value': 21893.07}, {'field': 'total_amount', 'old_value': 31238.42, 'new_value': 32451.22}, {'field': 'order_count', 'old_value': 1048, 'new_value': 1091}]
2025-06-01 09:00:41,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-06-01 09:00:41,979 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-06-01 09:00:41,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 215045.33, 'new_value': 221296.19}, {'field': 'offline_amount', 'old_value': 172104.31, 'new_value': 175123.31}, {'field': 'total_amount', 'old_value': 387149.64, 'new_value': 396419.5}, {'field': 'order_count', 'old_value': 3525, 'new_value': 3616}]
2025-06-01 09:00:41,979 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-06-01 09:00:42,432 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-06-01 09:00:42,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 440578.9, 'new_value': 456266.4}, {'field': 'offline_amount', 'old_value': 120604.1, 'new_value': 127736.1}, {'field': 'total_amount', 'old_value': 561183.0, 'new_value': 584002.5}, {'field': 'order_count', 'old_value': 20243, 'new_value': 20273}]
2025-06-01 09:00:42,448 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-06-01 09:00:42,932 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-06-01 09:00:42,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100327.6, 'new_value': 102748.6}, {'field': 'total_amount', 'old_value': 112876.65, 'new_value': 115297.65}, {'field': 'order_count', 'old_value': 345, 'new_value': 352}]
2025-06-01 09:00:42,932 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-06-01 09:00:43,432 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-06-01 09:00:43,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21298.14, 'new_value': 23111.88}, {'field': 'offline_amount', 'old_value': 518060.64, 'new_value': 540649.47}, {'field': 'total_amount', 'old_value': 539358.78, 'new_value': 563761.35}, {'field': 'order_count', 'old_value': 2171, 'new_value': 2267}]
2025-06-01 09:00:43,432 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-06-01 09:00:43,901 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-06-01 09:00:43,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53564.31, 'new_value': 54309.01}, {'field': 'total_amount', 'old_value': 54085.91, 'new_value': 54830.61}, {'field': 'order_count', 'old_value': 460, 'new_value': 464}]
2025-06-01 09:00:43,901 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-06-01 09:00:44,479 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-06-01 09:00:44,479 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6999.0, 'new_value': 7138.0}, {'field': 'offline_amount', 'old_value': 32005.5, 'new_value': 32954.7}, {'field': 'total_amount', 'old_value': 39004.5, 'new_value': 40092.7}, {'field': 'order_count', 'old_value': 1480, 'new_value': 1521}]
2025-06-01 09:00:44,479 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-06-01 09:00:44,916 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-06-01 09:00:44,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224774.6, 'new_value': 230669.5}, {'field': 'total_amount', 'old_value': 224774.6, 'new_value': 230669.5}, {'field': 'order_count', 'old_value': 825, 'new_value': 849}]
2025-06-01 09:00:44,916 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-06-01 09:00:45,432 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-06-01 09:00:45,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360270.2, 'new_value': 395199.5}, {'field': 'total_amount', 'old_value': 360270.2, 'new_value': 395199.5}, {'field': 'order_count', 'old_value': 3688, 'new_value': 4000}]
2025-06-01 09:00:45,432 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-06-01 09:00:45,901 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-06-01 09:00:45,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27438.94, 'new_value': 28390.73}, {'field': 'offline_amount', 'old_value': 52428.88, 'new_value': 54068.88}, {'field': 'total_amount', 'old_value': 79867.82, 'new_value': 82459.61}, {'field': 'order_count', 'old_value': 2886, 'new_value': 2975}]
2025-06-01 09:00:45,901 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-06-01 09:00:46,401 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-06-01 09:00:46,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90513.0, 'new_value': 92527.0}, {'field': 'total_amount', 'old_value': 92921.0, 'new_value': 94935.0}, {'field': 'order_count', 'old_value': 384, 'new_value': 393}]
2025-06-01 09:00:46,401 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-06-01 09:00:46,901 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-06-01 09:00:46,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21783.2, 'new_value': 22645.6}, {'field': 'offline_amount', 'old_value': 69641.38, 'new_value': 72584.68}, {'field': 'total_amount', 'old_value': 91424.58, 'new_value': 95230.28}, {'field': 'order_count', 'old_value': 1037, 'new_value': 1077}]
2025-06-01 09:00:46,901 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-06-01 09:00:47,338 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-06-01 09:00:47,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113745.1, 'new_value': 120379.6}, {'field': 'offline_amount', 'old_value': 165254.3, 'new_value': 174592.3}, {'field': 'total_amount', 'old_value': 278999.4, 'new_value': 294971.9}, {'field': 'order_count', 'old_value': 1761, 'new_value': 1829}]
2025-06-01 09:00:47,338 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-06-01 09:00:47,776 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-06-01 09:00:47,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85586.0, 'new_value': 90215.0}, {'field': 'total_amount', 'old_value': 85586.0, 'new_value': 90215.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 147}]
2025-06-01 09:00:47,776 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-06-01 09:00:48,323 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-06-01 09:00:48,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140015.79, 'new_value': 146885.04}, {'field': 'total_amount', 'old_value': 140015.79, 'new_value': 146885.04}, {'field': 'order_count', 'old_value': 4084, 'new_value': 4262}]
2025-06-01 09:00:48,323 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-06-01 09:00:48,698 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-06-01 09:00:48,698 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138468.34, 'new_value': 145809.85}, {'field': 'offline_amount', 'old_value': 319455.84, 'new_value': 329510.89}, {'field': 'total_amount', 'old_value': 457924.18, 'new_value': 475320.74}, {'field': 'order_count', 'old_value': 5386, 'new_value': 5579}]
2025-06-01 09:00:48,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-06-01 09:00:49,229 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-06-01 09:00:49,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26784.2, 'new_value': 27213.2}, {'field': 'total_amount', 'old_value': 26784.2, 'new_value': 27213.2}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-01 09:00:49,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-06-01 09:00:49,698 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-06-01 09:00:49,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54044.2, 'new_value': 56192.0}, {'field': 'total_amount', 'old_value': 54044.2, 'new_value': 56192.0}, {'field': 'order_count', 'old_value': 2406, 'new_value': 2484}]
2025-06-01 09:00:49,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-06-01 09:00:50,166 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-06-01 09:00:50,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131464.35, 'new_value': 147016.65}, {'field': 'total_amount', 'old_value': 299289.85, 'new_value': 314842.15}, {'field': 'order_count', 'old_value': 7967, 'new_value': 8290}]
2025-06-01 09:00:50,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-06-01 09:00:50,588 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-06-01 09:00:50,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68338.32, 'new_value': 71308.51}, {'field': 'offline_amount', 'old_value': 40384.92, 'new_value': 41739.44}, {'field': 'total_amount', 'old_value': 108723.24, 'new_value': 113047.95}, {'field': 'order_count', 'old_value': 5921, 'new_value': 6127}]
2025-06-01 09:00:50,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-06-01 09:00:51,120 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-06-01 09:00:51,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23605.08, 'new_value': 25142.08}, {'field': 'total_amount', 'old_value': 23605.08, 'new_value': 25142.08}, {'field': 'order_count', 'old_value': 207, 'new_value': 224}]
2025-06-01 09:00:51,120 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-06-01 09:00:51,588 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-06-01 09:00:51,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 479046.68, 'new_value': 498678.76}, {'field': 'total_amount', 'old_value': 479046.68, 'new_value': 498678.76}, {'field': 'order_count', 'old_value': 1708, 'new_value': 1772}]
2025-06-01 09:00:51,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-06-01 09:00:52,010 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-06-01 09:00:52,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368400.7, 'new_value': 391136.3}, {'field': 'total_amount', 'old_value': 379676.9, 'new_value': 402412.5}, {'field': 'order_count', 'old_value': 9598, 'new_value': 9822}]
2025-06-01 09:00:52,010 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-06-01 09:00:52,463 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-06-01 09:00:52,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47209.37, 'new_value': 48561.96}, {'field': 'total_amount', 'old_value': 47209.37, 'new_value': 48561.96}, {'field': 'order_count', 'old_value': 6113, 'new_value': 6289}]
2025-06-01 09:00:52,463 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-06-01 09:00:52,932 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-06-01 09:00:52,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108837.0, 'new_value': 112480.0}, {'field': 'total_amount', 'old_value': 114038.0, 'new_value': 117681.0}, {'field': 'order_count', 'old_value': 332, 'new_value': 340}]
2025-06-01 09:00:52,932 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-06-01 09:00:53,401 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-06-01 09:00:53,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321299.1, 'new_value': 321331.1}, {'field': 'total_amount', 'old_value': 321299.1, 'new_value': 321331.1}, {'field': 'order_count', 'old_value': 85, 'new_value': 86}]
2025-06-01 09:00:53,416 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-06-01 09:00:53,838 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-06-01 09:00:53,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200825.0, 'new_value': 202805.0}, {'field': 'total_amount', 'old_value': 200826.0, 'new_value': 202806.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-06-01 09:00:53,838 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-06-01 09:00:54,323 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-06-01 09:00:54,323 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48769.68, 'new_value': 49034.44}, {'field': 'offline_amount', 'old_value': 62165.37, 'new_value': 66447.35}, {'field': 'total_amount', 'old_value': 110935.05, 'new_value': 115481.79}, {'field': 'order_count', 'old_value': 391, 'new_value': 409}]
2025-06-01 09:00:54,323 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-06-01 09:00:54,791 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-06-01 09:00:54,791 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344668.0, 'new_value': 354081.0}, {'field': 'total_amount', 'old_value': 344668.0, 'new_value': 354081.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 98}]
2025-06-01 09:00:54,791 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-06-01 09:00:55,229 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-06-01 09:00:55,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 362205.92, 'new_value': 388651.92}, {'field': 'offline_amount', 'old_value': 13503.5, 'new_value': 13815.5}, {'field': 'total_amount', 'old_value': 375709.42, 'new_value': 402467.42}, {'field': 'order_count', 'old_value': 3241, 'new_value': 3433}]
2025-06-01 09:00:55,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-06-01 09:00:55,682 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-06-01 09:00:55,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31651.8, 'new_value': 32853.0}, {'field': 'total_amount', 'old_value': 31674.3, 'new_value': 32875.5}, {'field': 'order_count', 'old_value': 911, 'new_value': 944}]
2025-06-01 09:00:55,682 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-06-01 09:00:56,135 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-06-01 09:00:56,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57325.19, 'new_value': 58566.89}, {'field': 'total_amount', 'old_value': 65269.19, 'new_value': 66510.89}, {'field': 'order_count', 'old_value': 607, 'new_value': 619}]
2025-06-01 09:00:56,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-06-01 09:00:56,666 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-06-01 09:00:56,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38588.85, 'new_value': 39681.85}, {'field': 'total_amount', 'old_value': 38588.85, 'new_value': 39681.85}, {'field': 'order_count', 'old_value': 1448, 'new_value': 1484}]
2025-06-01 09:00:56,666 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-06-01 09:00:57,135 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-06-01 09:00:57,135 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 946878.0, 'new_value': 975594.0}, {'field': 'total_amount', 'old_value': 946878.0, 'new_value': 975594.0}, {'field': 'order_count', 'old_value': 4303, 'new_value': 4465}]
2025-06-01 09:00:57,135 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-06-01 09:00:57,604 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-06-01 09:00:57,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10590.51, 'new_value': 11408.31}, {'field': 'offline_amount', 'old_value': 21300.0, 'new_value': 23800.0}, {'field': 'total_amount', 'old_value': 31890.51, 'new_value': 35208.31}, {'field': 'order_count', 'old_value': 164, 'new_value': 177}]
2025-06-01 09:00:57,604 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-06-01 09:00:58,104 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-06-01 09:00:58,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143663.7, 'new_value': 146716.1}, {'field': 'total_amount', 'old_value': 143663.7, 'new_value': 146716.1}, {'field': 'order_count', 'old_value': 284, 'new_value': 289}]
2025-06-01 09:00:58,104 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-06-01 09:00:58,651 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-06-01 09:00:58,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128032.6, 'new_value': 135789.6}, {'field': 'offline_amount', 'old_value': 82497.98, 'new_value': 91535.98}, {'field': 'total_amount', 'old_value': 210530.58, 'new_value': 227325.58}, {'field': 'order_count', 'old_value': 1420, 'new_value': 1523}]
2025-06-01 09:00:58,651 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-06-01 09:00:59,182 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-06-01 09:00:59,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 389009.5, 'new_value': 410743.9}, {'field': 'total_amount', 'old_value': 389009.5, 'new_value': 410743.9}, {'field': 'order_count', 'old_value': 481, 'new_value': 507}]
2025-06-01 09:00:59,182 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-06-01 09:00:59,682 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-06-01 09:00:59,682 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 361124.88, 'new_value': 377777.1}, {'field': 'offline_amount', 'old_value': 1455169.94, 'new_value': 1536497.6}, {'field': 'total_amount', 'old_value': 1816294.82, 'new_value': 1914274.7}, {'field': 'order_count', 'old_value': 9082, 'new_value': 9514}]
2025-06-01 09:00:59,682 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-06-01 09:01:00,245 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-06-01 09:01:00,245 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156015.4, 'new_value': 162258.1}, {'field': 'offline_amount', 'old_value': 129287.2, 'new_value': 134156.9}, {'field': 'total_amount', 'old_value': 285302.6, 'new_value': 296415.0}, {'field': 'order_count', 'old_value': 6718, 'new_value': 7006}]
2025-06-01 09:01:00,245 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-06-01 09:01:00,729 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-06-01 09:01:00,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 695096.0, 'new_value': 718247.0}, {'field': 'total_amount', 'old_value': 695096.0, 'new_value': 718247.0}, {'field': 'order_count', 'old_value': 617, 'new_value': 636}]
2025-06-01 09:01:00,729 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-06-01 09:01:01,182 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-06-01 09:01:01,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 295226.56, 'new_value': 309216.76}, {'field': 'offline_amount', 'old_value': 162686.06, 'new_value': 171041.36}, {'field': 'total_amount', 'old_value': 457912.62, 'new_value': 480258.12}, {'field': 'order_count', 'old_value': 3371, 'new_value': 3444}]
2025-06-01 09:01:01,182 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-06-01 09:01:01,635 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-06-01 09:01:01,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118514.0, 'new_value': 120167.0}, {'field': 'offline_amount', 'old_value': 1376983.0, 'new_value': 1433030.0}, {'field': 'total_amount', 'old_value': 1495497.0, 'new_value': 1553197.0}, {'field': 'order_count', 'old_value': 37021, 'new_value': 38082}]
2025-06-01 09:01:01,635 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-06-01 09:01:02,073 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-06-01 09:01:02,073 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132644.1, 'new_value': 132660.1}, {'field': 'total_amount', 'old_value': 133449.1, 'new_value': 133465.1}, {'field': 'order_count', 'old_value': 16325, 'new_value': 24336}]
2025-06-01 09:01:02,073 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-06-01 09:01:02,494 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-06-01 09:01:02,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199678.48, 'new_value': 204006.66}, {'field': 'total_amount', 'old_value': 199678.48, 'new_value': 204006.66}, {'field': 'order_count', 'old_value': 10049, 'new_value': 10249}]
2025-06-01 09:01:02,494 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-06-01 09:01:02,963 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-06-01 09:01:02,963 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179438.7, 'new_value': 187473.1}, {'field': 'total_amount', 'old_value': 179438.7, 'new_value': 187473.1}, {'field': 'order_count', 'old_value': 816, 'new_value': 850}]
2025-06-01 09:01:02,963 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-06-01 09:01:03,448 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-06-01 09:01:03,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147022.0, 'new_value': 159530.0}, {'field': 'total_amount', 'old_value': 147022.0, 'new_value': 159530.0}, {'field': 'order_count', 'old_value': 4054, 'new_value': 4408}]
2025-06-01 09:01:03,448 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-06-01 09:01:04,041 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-06-01 09:01:04,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11474.0, 'new_value': 11895.0}, {'field': 'total_amount', 'old_value': 24187.0, 'new_value': 24608.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 144}]
2025-06-01 09:01:04,041 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-06-01 09:01:04,557 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-06-01 09:01:04,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2088296.0, 'new_value': 2186775.0}, {'field': 'total_amount', 'old_value': 2088296.0, 'new_value': 2186775.0}, {'field': 'order_count', 'old_value': 8451, 'new_value': 8947}]
2025-06-01 09:01:04,557 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-06-01 09:01:04,979 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-06-01 09:01:04,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124731.8, 'new_value': 128838.2}, {'field': 'total_amount', 'old_value': 124731.8, 'new_value': 128838.2}, {'field': 'order_count', 'old_value': 590, 'new_value': 607}]
2025-06-01 09:01:04,979 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-06-01 09:01:05,416 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-06-01 09:01:05,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290188.65, 'new_value': 300871.65}, {'field': 'total_amount', 'old_value': 290188.65, 'new_value': 300871.65}, {'field': 'order_count', 'old_value': 1658, 'new_value': 1707}]
2025-06-01 09:01:05,416 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-06-01 09:01:05,901 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-06-01 09:01:05,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 428317.9, 'new_value': 447011.9}, {'field': 'total_amount', 'old_value': 428317.9, 'new_value': 447011.9}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-06-01 09:01:05,901 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-06-01 09:01:06,401 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-06-01 09:01:06,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105094.56, 'new_value': 108350.56}, {'field': 'total_amount', 'old_value': 105094.56, 'new_value': 108350.56}, {'field': 'order_count', 'old_value': 5374, 'new_value': 5537}]
2025-06-01 09:01:06,401 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-06-01 09:01:06,885 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-06-01 09:01:06,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64384.48, 'new_value': 66101.15}, {'field': 'offline_amount', 'old_value': 67462.94, 'new_value': 69173.64}, {'field': 'total_amount', 'old_value': 131847.42, 'new_value': 135274.79}, {'field': 'order_count', 'old_value': 6582, 'new_value': 6759}]
2025-06-01 09:01:06,885 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-06-01 09:01:07,323 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-06-01 09:01:07,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1182982.0, 'new_value': 1252428.0}, {'field': 'total_amount', 'old_value': 1182982.0, 'new_value': 1252428.0}, {'field': 'order_count', 'old_value': 1352, 'new_value': 1419}]
2025-06-01 09:01:07,323 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-06-01 09:01:07,823 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-06-01 09:01:07,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251387.4, 'new_value': 262527.4}, {'field': 'total_amount', 'old_value': 257337.7, 'new_value': 268477.7}, {'field': 'order_count', 'old_value': 487, 'new_value': 504}]
2025-06-01 09:01:07,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-06-01 09:01:08,244 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-06-01 09:01:08,244 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50611.95, 'new_value': 52370.15}, {'field': 'offline_amount', 'old_value': 153930.0, 'new_value': 159845.0}, {'field': 'total_amount', 'old_value': 204541.95, 'new_value': 212215.15}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2308}]
2025-06-01 09:01:08,244 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-06-01 09:01:08,698 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-06-01 09:01:08,698 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162456.0, 'new_value': 170697.0}, {'field': 'offline_amount', 'old_value': 109551.0, 'new_value': 112949.0}, {'field': 'total_amount', 'old_value': 272007.0, 'new_value': 283646.0}, {'field': 'order_count', 'old_value': 3686, 'new_value': 3847}]
2025-06-01 09:01:08,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-06-01 09:01:09,276 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-06-01 09:01:09,276 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8801.7, 'new_value': 8969.7}, {'field': 'offline_amount', 'old_value': 25921.6, 'new_value': 26652.6}, {'field': 'total_amount', 'old_value': 34723.3, 'new_value': 35622.3}, {'field': 'order_count', 'old_value': 347, 'new_value': 351}]
2025-06-01 09:01:09,276 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-06-01 09:01:09,713 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-06-01 09:01:09,713 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12963.54, 'new_value': 13230.14}, {'field': 'total_amount', 'old_value': 208809.54, 'new_value': 209076.14}, {'field': 'order_count', 'old_value': 91, 'new_value': 92}]
2025-06-01 09:01:09,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-06-01 09:01:10,198 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-06-01 09:01:10,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264764.5, 'new_value': 270554.5}, {'field': 'total_amount', 'old_value': 264764.5, 'new_value': 270554.5}, {'field': 'order_count', 'old_value': 1282, 'new_value': 1306}]
2025-06-01 09:01:10,198 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-06-01 09:01:10,682 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-06-01 09:01:10,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63510.3, 'new_value': 66036.32}, {'field': 'total_amount', 'old_value': 70329.34, 'new_value': 72855.36}, {'field': 'order_count', 'old_value': 671, 'new_value': 700}]
2025-06-01 09:01:10,682 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-06-01 09:01:11,198 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-06-01 09:01:11,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235888.34, 'new_value': 239758.84}, {'field': 'total_amount', 'old_value': 235888.34, 'new_value': 239758.84}, {'field': 'order_count', 'old_value': 919, 'new_value': 931}]
2025-06-01 09:01:11,198 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-06-01 09:01:11,635 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-06-01 09:01:11,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 226860.0, 'new_value': 241339.0}, {'field': 'offline_amount', 'old_value': 205571.0, 'new_value': 213714.0}, {'field': 'total_amount', 'old_value': 432431.0, 'new_value': 455053.0}, {'field': 'order_count', 'old_value': 1237, 'new_value': 1304}]
2025-06-01 09:01:11,635 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-06-01 09:01:12,166 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-06-01 09:01:12,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114437.0, 'new_value': 120943.0}, {'field': 'total_amount', 'old_value': 114437.0, 'new_value': 120943.0}, {'field': 'order_count', 'old_value': 7689, 'new_value': 8113}]
2025-06-01 09:01:12,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-06-01 09:01:12,635 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-06-01 09:01:12,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178771.0, 'new_value': 182169.0}, {'field': 'total_amount', 'old_value': 183510.0, 'new_value': 186908.0}, {'field': 'order_count', 'old_value': 13630, 'new_value': 13833}]
2025-06-01 09:01:12,635 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-06-01 09:01:13,166 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-06-01 09:01:13,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37837.0, 'new_value': 37892.0}, {'field': 'total_amount', 'old_value': 37837.0, 'new_value': 37892.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-06-01 09:01:13,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-06-01 09:01:13,588 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-06-01 09:01:13,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3043405.75, 'new_value': 4910455.75}, {'field': 'total_amount', 'old_value': 4608655.75, 'new_value': 6475705.75}, {'field': 'order_count', 'old_value': 12, 'new_value': 17}]
2025-06-01 09:01:13,588 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-06-01 09:01:14,073 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-06-01 09:01:14,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33638.1, 'new_value': 35637.1}, {'field': 'offline_amount', 'old_value': 75687.6, 'new_value': 77165.2}, {'field': 'total_amount', 'old_value': 109325.7, 'new_value': 112802.3}, {'field': 'order_count', 'old_value': 4116, 'new_value': 4243}]
2025-06-01 09:01:14,073 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-06-01 09:01:14,541 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-06-01 09:01:14,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29483.52, 'new_value': 29756.52}, {'field': 'total_amount', 'old_value': 29483.52, 'new_value': 29756.52}, {'field': 'order_count', 'old_value': 136, 'new_value': 139}]
2025-06-01 09:01:14,541 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-06-01 09:01:15,010 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-06-01 09:01:15,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34839.0, 'new_value': 37939.0}, {'field': 'total_amount', 'old_value': 34920.7, 'new_value': 38020.7}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-01 09:01:15,010 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-06-01 09:01:15,432 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-06-01 09:01:15,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232697.8, 'new_value': 237365.8}, {'field': 'total_amount', 'old_value': 232697.8, 'new_value': 237365.8}, {'field': 'order_count', 'old_value': 8146, 'new_value': 8316}]
2025-06-01 09:01:15,448 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-06-01 09:01:15,901 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-06-01 09:01:15,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358253.26, 'new_value': 376029.96}, {'field': 'total_amount', 'old_value': 358253.26, 'new_value': 376029.96}, {'field': 'order_count', 'old_value': 976, 'new_value': 1021}]
2025-06-01 09:01:15,901 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-06-01 09:01:16,369 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-06-01 09:01:16,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29916.0, 'new_value': 33204.0}, {'field': 'total_amount', 'old_value': 45504.0, 'new_value': 48792.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-01 09:01:16,385 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-06-01 09:01:16,869 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-06-01 09:01:16,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65262.0, 'new_value': 66740.0}, {'field': 'total_amount', 'old_value': 65262.0, 'new_value': 66740.0}, {'field': 'order_count', 'old_value': 1251, 'new_value': 1280}]
2025-06-01 09:01:16,869 - INFO - 开始更新记录 - 表单实例ID: FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B
2025-06-01 09:01:17,338 - INFO - 更新表单数据成功: FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B
2025-06-01 09:01:17,338 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12090.0, 'new_value': 38764.0}, {'field': 'offline_amount', 'old_value': 321.0, 'new_value': 456.0}, {'field': 'total_amount', 'old_value': 12411.0, 'new_value': 39220.0}, {'field': 'order_count', 'old_value': 594, 'new_value': 1931}]
2025-06-01 09:01:17,338 - INFO - 日期 2025-05 处理完成 - 更新: 98 条，插入: 0 条，错误: 0 条
2025-06-01 09:01:17,338 - INFO - 数据同步完成！更新: 98 条，插入: 0 条，错误: 0 条
2025-06-01 09:01:17,338 - INFO - =================同步完成====================
2025-06-01 12:00:02,556 - INFO - =================使用默认全量同步=============
2025-06-01 12:00:04,103 - INFO - MySQL查询成功，共获取 3307 条记录
2025-06-01 12:00:04,104 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-01 12:00:04,134 - INFO - 开始处理日期: 2025-01
2025-06-01 12:00:04,138 - INFO - Request Parameters - Page 1:
2025-06-01 12:00:04,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:04,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:05,196 - INFO - Response - Page 1:
2025-06-01 12:00:05,404 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:00:05,404 - INFO - Request Parameters - Page 2:
2025-06-01 12:00:05,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:05,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:06,099 - INFO - Response - Page 2:
2025-06-01 12:00:06,299 - INFO - 第 2 页获取到 100 条记录
2025-06-01 12:00:06,299 - INFO - Request Parameters - Page 3:
2025-06-01 12:00:06,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:06,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:07,084 - INFO - Response - Page 3:
2025-06-01 12:00:07,284 - INFO - 第 3 页获取到 100 条记录
2025-06-01 12:00:07,284 - INFO - Request Parameters - Page 4:
2025-06-01 12:00:07,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:07,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:07,803 - INFO - Response - Page 4:
2025-06-01 12:00:08,006 - INFO - 第 4 页获取到 100 条记录
2025-06-01 12:00:08,006 - INFO - Request Parameters - Page 5:
2025-06-01 12:00:08,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:08,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:08,489 - INFO - Response - Page 5:
2025-06-01 12:00:08,692 - INFO - 第 5 页获取到 100 条记录
2025-06-01 12:00:08,692 - INFO - Request Parameters - Page 6:
2025-06-01 12:00:08,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:08,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:09,193 - INFO - Response - Page 6:
2025-06-01 12:00:09,394 - INFO - 第 6 页获取到 100 条记录
2025-06-01 12:00:09,394 - INFO - Request Parameters - Page 7:
2025-06-01 12:00:09,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:09,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:09,894 - INFO - Response - Page 7:
2025-06-01 12:00:10,094 - INFO - 第 7 页获取到 82 条记录
2025-06-01 12:00:10,094 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 12:00:10,094 - INFO - 获取到 682 条表单数据
2025-06-01 12:00:10,107 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 12:00:10,121 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 12:00:10,122 - INFO - 开始处理日期: 2025-02
2025-06-01 12:00:10,122 - INFO - Request Parameters - Page 1:
2025-06-01 12:00:10,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:10,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:10,611 - INFO - Response - Page 1:
2025-06-01 12:00:10,813 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:00:10,813 - INFO - Request Parameters - Page 2:
2025-06-01 12:00:10,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:10,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:11,272 - INFO - Response - Page 2:
2025-06-01 12:00:11,475 - INFO - 第 2 页获取到 100 条记录
2025-06-01 12:00:11,475 - INFO - Request Parameters - Page 3:
2025-06-01 12:00:11,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:11,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:11,963 - INFO - Response - Page 3:
2025-06-01 12:00:12,163 - INFO - 第 3 页获取到 100 条记录
2025-06-01 12:00:12,163 - INFO - Request Parameters - Page 4:
2025-06-01 12:00:12,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:12,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:12,659 - INFO - Response - Page 4:
2025-06-01 12:00:12,860 - INFO - 第 4 页获取到 100 条记录
2025-06-01 12:00:12,860 - INFO - Request Parameters - Page 5:
2025-06-01 12:00:12,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:12,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:13,354 - INFO - Response - Page 5:
2025-06-01 12:00:13,555 - INFO - 第 5 页获取到 100 条记录
2025-06-01 12:00:13,555 - INFO - Request Parameters - Page 6:
2025-06-01 12:00:13,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:13,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:14,063 - INFO - Response - Page 6:
2025-06-01 12:00:14,265 - INFO - 第 6 页获取到 100 条记录
2025-06-01 12:00:14,265 - INFO - Request Parameters - Page 7:
2025-06-01 12:00:14,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:14,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:14,708 - INFO - Response - Page 7:
2025-06-01 12:00:14,909 - INFO - 第 7 页获取到 70 条记录
2025-06-01 12:00:14,909 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 12:00:14,910 - INFO - 获取到 670 条表单数据
2025-06-01 12:00:14,923 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 12:00:14,936 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 12:00:14,936 - INFO - 开始处理日期: 2025-03
2025-06-01 12:00:14,936 - INFO - Request Parameters - Page 1:
2025-06-01 12:00:14,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:14,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:15,447 - INFO - Response - Page 1:
2025-06-01 12:00:15,648 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:00:15,648 - INFO - Request Parameters - Page 2:
2025-06-01 12:00:15,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:15,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:16,337 - INFO - Response - Page 2:
2025-06-01 12:00:16,537 - INFO - 第 2 页获取到 100 条记录
2025-06-01 12:00:16,537 - INFO - Request Parameters - Page 3:
2025-06-01 12:00:16,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:16,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:17,011 - INFO - Response - Page 3:
2025-06-01 12:00:17,211 - INFO - 第 3 页获取到 100 条记录
2025-06-01 12:00:17,211 - INFO - Request Parameters - Page 4:
2025-06-01 12:00:17,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:17,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:17,717 - INFO - Response - Page 4:
2025-06-01 12:00:17,917 - INFO - 第 4 页获取到 100 条记录
2025-06-01 12:00:17,917 - INFO - Request Parameters - Page 5:
2025-06-01 12:00:17,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:17,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:18,397 - INFO - Response - Page 5:
2025-06-01 12:00:18,599 - INFO - 第 5 页获取到 100 条记录
2025-06-01 12:00:18,599 - INFO - Request Parameters - Page 6:
2025-06-01 12:00:18,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:18,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:19,187 - INFO - Response - Page 6:
2025-06-01 12:00:19,388 - INFO - 第 6 页获取到 100 条记录
2025-06-01 12:00:19,388 - INFO - Request Parameters - Page 7:
2025-06-01 12:00:19,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:19,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:19,862 - INFO - Response - Page 7:
2025-06-01 12:00:20,062 - INFO - 第 7 页获取到 61 条记录
2025-06-01 12:00:20,062 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 12:00:20,062 - INFO - 获取到 661 条表单数据
2025-06-01 12:00:20,075 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 12:00:20,090 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 12:00:20,090 - INFO - 开始处理日期: 2025-04
2025-06-01 12:00:20,090 - INFO - Request Parameters - Page 1:
2025-06-01 12:00:20,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:20,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:20,633 - INFO - Response - Page 1:
2025-06-01 12:00:20,834 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:00:20,834 - INFO - Request Parameters - Page 2:
2025-06-01 12:00:20,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:20,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:21,393 - INFO - Response - Page 2:
2025-06-01 12:00:21,594 - INFO - 第 2 页获取到 100 条记录
2025-06-01 12:00:21,594 - INFO - Request Parameters - Page 3:
2025-06-01 12:00:21,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:21,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:22,142 - INFO - Response - Page 3:
2025-06-01 12:00:22,343 - INFO - 第 3 页获取到 100 条记录
2025-06-01 12:00:22,343 - INFO - Request Parameters - Page 4:
2025-06-01 12:00:22,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:22,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:22,794 - INFO - Response - Page 4:
2025-06-01 12:00:22,994 - INFO - 第 4 页获取到 100 条记录
2025-06-01 12:00:22,994 - INFO - Request Parameters - Page 5:
2025-06-01 12:00:22,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:22,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:23,460 - INFO - Response - Page 5:
2025-06-01 12:00:23,660 - INFO - 第 5 页获取到 100 条记录
2025-06-01 12:00:23,660 - INFO - Request Parameters - Page 6:
2025-06-01 12:00:23,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:23,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:24,187 - INFO - Response - Page 6:
2025-06-01 12:00:24,388 - INFO - 第 6 页获取到 100 条记录
2025-06-01 12:00:24,388 - INFO - Request Parameters - Page 7:
2025-06-01 12:00:24,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:24,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:24,821 - INFO - Response - Page 7:
2025-06-01 12:00:25,021 - INFO - 第 7 页获取到 56 条记录
2025-06-01 12:00:25,021 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 12:00:25,021 - INFO - 获取到 656 条表单数据
2025-06-01 12:00:25,035 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 12:00:25,048 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 12:00:25,048 - INFO - 开始处理日期: 2025-05
2025-06-01 12:00:25,048 - INFO - Request Parameters - Page 1:
2025-06-01 12:00:25,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:25,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:25,887 - INFO - Response - Page 1:
2025-06-01 12:00:26,087 - INFO - 第 1 页获取到 100 条记录
2025-06-01 12:00:26,087 - INFO - Request Parameters - Page 2:
2025-06-01 12:00:26,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:26,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:26,616 - INFO - Response - Page 2:
2025-06-01 12:00:26,816 - INFO - 第 2 页获取到 100 条记录
2025-06-01 12:00:26,816 - INFO - Request Parameters - Page 3:
2025-06-01 12:00:26,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:26,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:27,339 - INFO - Response - Page 3:
2025-06-01 12:00:27,539 - INFO - 第 3 页获取到 100 条记录
2025-06-01 12:00:27,539 - INFO - Request Parameters - Page 4:
2025-06-01 12:00:27,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:27,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:28,059 - INFO - Response - Page 4:
2025-06-01 12:00:28,260 - INFO - 第 4 页获取到 100 条记录
2025-06-01 12:00:28,260 - INFO - Request Parameters - Page 5:
2025-06-01 12:00:28,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:28,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:28,774 - INFO - Response - Page 5:
2025-06-01 12:00:28,974 - INFO - 第 5 页获取到 100 条记录
2025-06-01 12:00:28,974 - INFO - Request Parameters - Page 6:
2025-06-01 12:00:28,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:28,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:29,516 - INFO - Response - Page 6:
2025-06-01 12:00:29,717 - INFO - 第 6 页获取到 100 条记录
2025-06-01 12:00:29,717 - INFO - Request Parameters - Page 7:
2025-06-01 12:00:29,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:00:29,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:00:30,105 - INFO - Response - Page 7:
2025-06-01 12:00:30,306 - INFO - 第 7 页获取到 36 条记录
2025-06-01 12:00:30,306 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 12:00:30,306 - INFO - 获取到 636 条表单数据
2025-06-01 12:00:30,321 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 12:00:30,321 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-06-01 12:00:30,787 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-06-01 12:00:30,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2637.0, 'new_value': 2827.0}, {'field': 'offline_amount', 'old_value': 48343.0, 'new_value': 48883.0}, {'field': 'total_amount', 'old_value': 50980.0, 'new_value': 51710.0}, {'field': 'order_count', 'old_value': 699, 'new_value': 707}]
2025-06-01 12:00:30,787 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC1SAZUPWCXCKCYHBKMKFBZ2I5M8U4AMM6
2025-06-01 12:00:31,214 - INFO - 更新表单数据成功: FINST-KLF66WC1SAZUPWCXCKCYHBKMKFBZ2I5M8U4AMM6
2025-06-01 12:00:31,215 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350000.0, 'new_value': 708000.0}, {'field': 'total_amount', 'old_value': 350000.0, 'new_value': 708000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-01 12:00:31,215 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-06-01 12:00:31,660 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-06-01 12:00:31,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62275.0, 'new_value': 63335.0}, {'field': 'total_amount', 'old_value': 63865.0, 'new_value': 64925.0}]
2025-06-01 12:00:31,660 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-06-01 12:00:32,107 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-06-01 12:00:32,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227566.02, 'new_value': 233860.74}, {'field': 'total_amount', 'old_value': 227566.02, 'new_value': 233860.74}, {'field': 'order_count', 'old_value': 8317, 'new_value': 8567}]
2025-06-01 12:00:32,107 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-06-01 12:00:32,581 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-06-01 12:00:32,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 869622.98, 'new_value': 909415.98}, {'field': 'total_amount', 'old_value': 869622.98, 'new_value': 909415.98}, {'field': 'order_count', 'old_value': 2735, 'new_value': 2871}]
2025-06-01 12:00:32,581 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-06-01 12:00:33,054 - INFO - 更新表单数据成功: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-06-01 12:00:33,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43002.0, 'new_value': 48942.0}, {'field': 'total_amount', 'old_value': 44682.0, 'new_value': 50622.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-01 12:00:33,054 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-06-01 12:00:33,462 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-06-01 12:00:33,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132220.0, 'new_value': 167040.0}, {'field': 'total_amount', 'old_value': 132220.0, 'new_value': 167040.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-06-01 12:00:33,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-06-01 12:00:33,880 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-06-01 12:00:33,880 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40660.0, 'new_value': 41451.0}, {'field': 'offline_amount', 'old_value': 18600.64, 'new_value': 18965.64}, {'field': 'total_amount', 'old_value': 59260.64, 'new_value': 60416.64}, {'field': 'order_count', 'old_value': 2948, 'new_value': 3003}]
2025-06-01 12:00:33,880 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-06-01 12:00:34,332 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-06-01 12:00:34,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58887.78, 'new_value': 59418.78}, {'field': 'total_amount', 'old_value': 58887.78, 'new_value': 59418.78}, {'field': 'order_count', 'old_value': 139, 'new_value': 145}]
2025-06-01 12:00:34,334 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-06-01 12:00:34,937 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-06-01 12:00:34,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94001.0, 'new_value': 107001.0}, {'field': 'total_amount', 'old_value': 102200.0, 'new_value': 115200.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-06-01 12:00:34,937 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-06-01 12:00:35,463 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-06-01 12:00:35,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132313.0, 'new_value': 146135.0}, {'field': 'total_amount', 'old_value': 132313.0, 'new_value': 146135.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-06-01 12:00:35,464 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-06-01 12:00:35,905 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-06-01 12:00:35,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52101.0, 'new_value': 53100.0}, {'field': 'total_amount', 'old_value': 52101.0, 'new_value': 53100.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-01 12:00:35,906 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-06-01 12:00:36,321 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-06-01 12:00:36,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67057.0, 'new_value': 67675.0}, {'field': 'total_amount', 'old_value': 67057.0, 'new_value': 67675.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-01 12:00:36,321 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-06-01 12:00:36,834 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-06-01 12:00:36,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332991.8, 'new_value': 357128.6}, {'field': 'total_amount', 'old_value': 448011.5, 'new_value': 472148.3}, {'field': 'order_count', 'old_value': 3873, 'new_value': 4176}]
2025-06-01 12:00:36,834 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-06-01 12:00:37,261 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-06-01 12:00:37,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82004.0, 'new_value': 85004.0}, {'field': 'total_amount', 'old_value': 82303.92, 'new_value': 85303.92}, {'field': 'order_count', 'old_value': 126, 'new_value': 131}]
2025-06-01 12:00:37,261 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-06-01 12:00:37,684 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-06-01 12:00:37,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 509246.71, 'new_value': 517575.71}, {'field': 'total_amount', 'old_value': 542429.71, 'new_value': 550758.71}, {'field': 'order_count', 'old_value': 511, 'new_value': 520}]
2025-06-01 12:00:37,684 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-06-01 12:00:38,144 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-06-01 12:00:38,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169908.26, 'new_value': 172098.96}, {'field': 'total_amount', 'old_value': 169908.26, 'new_value': 172098.96}, {'field': 'order_count', 'old_value': 2009, 'new_value': 2033}]
2025-06-01 12:00:38,144 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-06-01 12:00:38,640 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-06-01 12:00:38,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1403040.0, 'new_value': 1463218.0}, {'field': 'offline_amount', 'old_value': 408417.0, 'new_value': 418205.0}, {'field': 'total_amount', 'old_value': 1811457.0, 'new_value': 1881423.0}, {'field': 'order_count', 'old_value': 2069, 'new_value': 2138}]
2025-06-01 12:00:38,641 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-06-01 12:00:39,129 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-06-01 12:00:39,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98713.0, 'new_value': 102311.0}, {'field': 'offline_amount', 'old_value': 127312.0, 'new_value': 131412.0}, {'field': 'total_amount', 'old_value': 226025.0, 'new_value': 233723.0}, {'field': 'order_count', 'old_value': 5041, 'new_value': 5205}]
2025-06-01 12:00:39,129 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-06-01 12:00:39,543 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-06-01 12:00:39,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294405.0, 'new_value': 303305.0}, {'field': 'total_amount', 'old_value': 324405.0, 'new_value': 333305.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-06-01 12:00:39,544 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-06-01 12:00:40,023 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-06-01 12:00:40,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401268.89, 'new_value': 408468.89}, {'field': 'total_amount', 'old_value': 441268.89, 'new_value': 448468.89}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-06-01 12:00:40,023 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-06-01 12:00:40,455 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-06-01 12:00:40,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352730.13, 'new_value': 387230.13}, {'field': 'total_amount', 'old_value': 422090.13, 'new_value': 456590.13}, {'field': 'order_count', 'old_value': 62, 'new_value': 67}]
2025-06-01 12:00:40,457 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-06-01 12:00:40,945 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-06-01 12:00:40,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1042526.5, 'new_value': 1112702.3}, {'field': 'total_amount', 'old_value': 1102222.5, 'new_value': 1172398.3}, {'field': 'order_count', 'old_value': 106, 'new_value': 111}]
2025-06-01 12:00:40,945 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-06-01 12:00:41,420 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-06-01 12:00:41,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202881.04, 'new_value': 207919.98}, {'field': 'total_amount', 'old_value': 202881.04, 'new_value': 207919.98}, {'field': 'order_count', 'old_value': 324, 'new_value': 333}]
2025-06-01 12:00:41,421 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-06-01 12:00:41,881 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-06-01 12:00:41,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94834.0, 'new_value': 97262.0}, {'field': 'total_amount', 'old_value': 94834.0, 'new_value': 97262.0}, {'field': 'order_count', 'old_value': 807, 'new_value': 827}]
2025-06-01 12:00:41,882 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-06-01 12:00:42,437 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-06-01 12:00:42,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50990.62, 'new_value': 51276.92}, {'field': 'total_amount', 'old_value': 50990.62, 'new_value': 51276.92}, {'field': 'order_count', 'old_value': 3741, 'new_value': 3770}]
2025-06-01 12:00:42,437 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-06-01 12:00:42,845 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-06-01 12:00:42,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 191609.0, 'new_value': 197128.0}, {'field': 'offline_amount', 'old_value': 73073.63, 'new_value': 76675.83}, {'field': 'total_amount', 'old_value': 264682.63, 'new_value': 273803.83}, {'field': 'order_count', 'old_value': 1740, 'new_value': 1795}]
2025-06-01 12:00:42,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-06-01 12:00:43,358 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-06-01 12:00:43,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48601.0, 'new_value': 51042.0}, {'field': 'total_amount', 'old_value': 58746.0, 'new_value': 61187.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 286}]
2025-06-01 12:00:43,359 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-06-01 12:00:43,826 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-06-01 12:00:43,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81551.6, 'new_value': 82775.1}, {'field': 'total_amount', 'old_value': 81551.6, 'new_value': 82775.1}, {'field': 'order_count', 'old_value': 151, 'new_value': 153}]
2025-06-01 12:00:43,826 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-06-01 12:00:44,247 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-06-01 12:00:44,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249561.3, 'new_value': 254733.3}, {'field': 'total_amount', 'old_value': 249561.3, 'new_value': 254733.3}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-06-01 12:00:44,247 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-06-01 12:00:44,751 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-06-01 12:00:44,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58983.7, 'new_value': 60533.5}, {'field': 'total_amount', 'old_value': 59064.5, 'new_value': 60614.3}, {'field': 'order_count', 'old_value': 353, 'new_value': 364}]
2025-06-01 12:00:44,751 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-06-01 12:00:45,210 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-06-01 12:00:45,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 201348.0, 'new_value': 207648.0}, {'field': 'offline_amount', 'old_value': 101119.0, 'new_value': 110911.0}, {'field': 'total_amount', 'old_value': 302467.0, 'new_value': 318559.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 98}]
2025-06-01 12:00:45,210 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-06-01 12:00:45,708 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-06-01 12:00:45,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36443.0, 'new_value': 37333.0}, {'field': 'total_amount', 'old_value': 38143.0, 'new_value': 39033.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 148}]
2025-06-01 12:00:45,708 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-06-01 12:00:46,213 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-06-01 12:00:46,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8140.22, 'new_value': 8688.74}, {'field': 'offline_amount', 'old_value': 126934.22, 'new_value': 141366.88}, {'field': 'total_amount', 'old_value': 135074.44, 'new_value': 150055.62}, {'field': 'order_count', 'old_value': 3261, 'new_value': 3525}]
2025-06-01 12:00:46,213 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-06-01 12:00:46,648 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-06-01 12:00:46,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 265699.0, 'new_value': 276539.0}, {'field': 'total_amount', 'old_value': 265699.0, 'new_value': 276539.0}, {'field': 'order_count', 'old_value': 1399, 'new_value': 1446}]
2025-06-01 12:00:46,648 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-06-01 12:00:47,095 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-06-01 12:00:47,095 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105504.57, 'new_value': 108938.88}, {'field': 'offline_amount', 'old_value': 51905.31, 'new_value': 52880.56}, {'field': 'total_amount', 'old_value': 157409.88, 'new_value': 161819.44}, {'field': 'order_count', 'old_value': 5506, 'new_value': 5663}]
2025-06-01 12:00:47,095 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-06-01 12:00:47,494 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-06-01 12:00:47,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10525.61, 'new_value': 159718.81}, {'field': 'total_amount', 'old_value': 10525.61, 'new_value': 159718.81}, {'field': 'order_count', 'old_value': 271, 'new_value': 262}]
2025-06-01 12:00:47,494 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-06-01 12:00:47,928 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-06-01 12:00:47,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102328.17, 'new_value': 105179.38}, {'field': 'offline_amount', 'old_value': 1044168.85, 'new_value': 1102521.37}, {'field': 'total_amount', 'old_value': 1146497.02, 'new_value': 1207700.75}, {'field': 'order_count', 'old_value': 3604, 'new_value': 3793}]
2025-06-01 12:00:47,928 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-06-01 12:00:48,431 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-06-01 12:00:48,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 828646.5, 'new_value': 867587.0}, {'field': 'total_amount', 'old_value': 828646.5, 'new_value': 867587.0}, {'field': 'order_count', 'old_value': 5040, 'new_value': 5282}]
2025-06-01 12:00:48,431 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-06-01 12:00:48,889 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-06-01 12:00:48,889 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66348.0, 'new_value': 67348.0}, {'field': 'total_amount', 'old_value': 66348.0, 'new_value': 67348.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 81}]
2025-06-01 12:00:48,889 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-06-01 12:00:49,443 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-06-01 12:00:49,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35032.0, 'new_value': 35598.0}, {'field': 'offline_amount', 'old_value': 553941.2, 'new_value': 561911.2}, {'field': 'total_amount', 'old_value': 588973.2, 'new_value': 597509.2}, {'field': 'order_count', 'old_value': 100, 'new_value': 104}]
2025-06-01 12:00:49,443 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-06-01 12:00:49,910 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-06-01 12:00:49,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100453.0, 'new_value': 105601.0}, {'field': 'total_amount', 'old_value': 100453.0, 'new_value': 105601.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-06-01 12:00:49,910 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-06-01 12:00:50,396 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-06-01 12:00:50,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169757.09, 'new_value': 172775.5}, {'field': 'total_amount', 'old_value': 169757.09, 'new_value': 172775.5}, {'field': 'order_count', 'old_value': 6152, 'new_value': 6262}]
2025-06-01 12:00:50,396 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-06-01 12:00:50,812 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-06-01 12:00:50,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177692.0, 'new_value': 182290.0}, {'field': 'offline_amount', 'old_value': 80905.0, 'new_value': 82263.0}, {'field': 'total_amount', 'old_value': 258597.0, 'new_value': 264553.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 99}]
2025-06-01 12:00:50,812 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-06-01 12:00:51,483 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-06-01 12:00:51,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23259.4, 'new_value': 23658.9}, {'field': 'total_amount', 'old_value': 23259.4, 'new_value': 23658.9}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-01 12:00:51,483 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-06-01 12:00:51,885 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-06-01 12:00:51,885 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4902.11, 'new_value': 5122.02}, {'field': 'offline_amount', 'old_value': 160998.85, 'new_value': 163295.05}, {'field': 'total_amount', 'old_value': 165900.96, 'new_value': 168417.07}, {'field': 'order_count', 'old_value': 772, 'new_value': 794}]
2025-06-01 12:00:51,885 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-06-01 12:00:52,337 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-06-01 12:00:52,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53927.0, 'new_value': 54445.0}, {'field': 'total_amount', 'old_value': 53927.0, 'new_value': 54445.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 123}]
2025-06-01 12:00:52,338 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-06-01 12:00:52,749 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-06-01 12:00:52,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215215.92, 'new_value': 224526.5}, {'field': 'total_amount', 'old_value': 215215.92, 'new_value': 224526.5}, {'field': 'order_count', 'old_value': 1244, 'new_value': 1290}]
2025-06-01 12:00:52,749 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-06-01 12:00:53,374 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-06-01 12:00:53,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8066.71, 'new_value': 8574.75}, {'field': 'offline_amount', 'old_value': 242469.0, 'new_value': 252504.67}, {'field': 'total_amount', 'old_value': 250535.71, 'new_value': 261079.42}, {'field': 'order_count', 'old_value': 1574, 'new_value': 1627}]
2025-06-01 12:00:53,374 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-06-01 12:00:53,866 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-06-01 12:00:53,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211211.0, 'new_value': 214960.0}, {'field': 'total_amount', 'old_value': 211211.0, 'new_value': 214960.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-06-01 12:00:53,867 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-06-01 12:00:54,306 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-06-01 12:00:54,306 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 302254.45, 'new_value': 310215.05}, {'field': 'offline_amount', 'old_value': 143189.0, 'new_value': 149540.0}, {'field': 'total_amount', 'old_value': 445443.45, 'new_value': 459755.05}, {'field': 'order_count', 'old_value': 2235, 'new_value': 2293}]
2025-06-01 12:00:54,307 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-06-01 12:00:54,780 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-06-01 12:00:54,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132098.0, 'new_value': 140339.0}, {'field': 'total_amount', 'old_value': 132101.0, 'new_value': 140342.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-06-01 12:00:54,780 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-06-01 12:00:55,275 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-06-01 12:00:55,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34704.0, 'new_value': 35619.0}, {'field': 'total_amount', 'old_value': 34704.0, 'new_value': 35619.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 94}]
2025-06-01 12:00:55,275 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-06-01 12:00:55,740 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-06-01 12:00:55,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74703.0, 'new_value': 75002.0}, {'field': 'total_amount', 'old_value': 75452.0, 'new_value': 75751.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-06-01 12:00:55,740 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-06-01 12:00:56,188 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-06-01 12:00:56,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71819.0, 'new_value': 79717.0}, {'field': 'offline_amount', 'old_value': 199929.0, 'new_value': 202645.0}, {'field': 'total_amount', 'old_value': 271748.0, 'new_value': 282362.0}, {'field': 'order_count', 'old_value': 6085, 'new_value': 6311}]
2025-06-01 12:00:56,188 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-06-01 12:00:56,739 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-06-01 12:00:56,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16367.25, 'new_value': 17050.82}, {'field': 'offline_amount', 'old_value': 337982.28, 'new_value': 355481.32}, {'field': 'total_amount', 'old_value': 354349.53, 'new_value': 372532.14}, {'field': 'order_count', 'old_value': 2436, 'new_value': 2546}]
2025-06-01 12:00:56,739 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-06-01 12:00:57,164 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-06-01 12:00:57,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445880.8, 'new_value': 482286.8}, {'field': 'total_amount', 'old_value': 472422.8, 'new_value': 508828.8}, {'field': 'order_count', 'old_value': 103, 'new_value': 108}]
2025-06-01 12:00:57,164 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-06-01 12:00:57,637 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-06-01 12:00:57,637 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 181430.68, 'new_value': 189157.03}, {'field': 'offline_amount', 'old_value': 128857.45, 'new_value': 132200.45}, {'field': 'total_amount', 'old_value': 310288.13, 'new_value': 321357.48}, {'field': 'order_count', 'old_value': 3110, 'new_value': 3233}]
2025-06-01 12:00:57,637 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-06-01 12:00:58,189 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-06-01 12:00:58,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80908.41, 'new_value': 85120.81}, {'field': 'total_amount', 'old_value': 80908.41, 'new_value': 85120.81}, {'field': 'order_count', 'old_value': 2352, 'new_value': 2459}]
2025-06-01 12:00:58,190 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-06-01 12:00:58,645 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-06-01 12:00:58,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168704.09, 'new_value': 172817.53}, {'field': 'offline_amount', 'old_value': 514628.66, 'new_value': 545628.96}, {'field': 'total_amount', 'old_value': 683332.75, 'new_value': 718446.49}, {'field': 'order_count', 'old_value': 3171, 'new_value': 3299}]
2025-06-01 12:00:58,646 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-06-01 12:00:59,105 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-06-01 12:00:59,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 492727.83, 'new_value': 504704.13}, {'field': 'total_amount', 'old_value': 492727.83, 'new_value': 504704.13}, {'field': 'order_count', 'old_value': 622, 'new_value': 657}]
2025-06-01 12:00:59,106 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-06-01 12:00:59,478 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-06-01 12:00:59,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98886.0, 'new_value': 100374.0}, {'field': 'offline_amount', 'old_value': 99121.23, 'new_value': 100381.23}, {'field': 'total_amount', 'old_value': 198007.23, 'new_value': 200755.23}, {'field': 'order_count', 'old_value': 239, 'new_value': 242}]
2025-06-01 12:00:59,478 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-06-01 12:00:59,948 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-06-01 12:00:59,948 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114817.15, 'new_value': 117876.25}, {'field': 'offline_amount', 'old_value': 1416283.64, 'new_value': 1479386.77}, {'field': 'total_amount', 'old_value': 1531100.79, 'new_value': 1597263.02}, {'field': 'order_count', 'old_value': 12276, 'new_value': 12712}]
2025-06-01 12:00:59,949 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-06-01 12:01:00,377 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-06-01 12:01:00,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2830.0}, {'field': 'total_amount', 'old_value': 51861.0, 'new_value': 54691.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 106}]
2025-06-01 12:01:00,378 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-06-01 12:01:00,776 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-06-01 12:01:00,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122300.48, 'new_value': 125412.08}, {'field': 'total_amount', 'old_value': 122300.48, 'new_value': 125412.08}, {'field': 'order_count', 'old_value': 900, 'new_value': 925}]
2025-06-01 12:01:00,777 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-06-01 12:01:01,257 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-06-01 12:01:01,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93477.59, 'new_value': 99445.05}, {'field': 'total_amount', 'old_value': 93477.59, 'new_value': 99445.05}, {'field': 'order_count', 'old_value': 536, 'new_value': 566}]
2025-06-01 12:01:01,257 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-06-01 12:01:01,710 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-06-01 12:01:01,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8239.0, 'new_value': 8539.0}, {'field': 'total_amount', 'old_value': 8239.0, 'new_value': 8539.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-06-01 12:01:01,711 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-06-01 12:01:02,159 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-06-01 12:01:02,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 244079.69, 'new_value': 252578.69}, {'field': 'offline_amount', 'old_value': 410075.99, 'new_value': 431279.99}, {'field': 'total_amount', 'old_value': 654155.68, 'new_value': 683858.68}, {'field': 'order_count', 'old_value': 18872, 'new_value': 19651}]
2025-06-01 12:01:02,159 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-06-01 12:01:02,673 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-06-01 12:01:02,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9769.69, 'new_value': 9889.49}, {'field': 'total_amount', 'old_value': 47157.69, 'new_value': 47277.49}, {'field': 'order_count', 'old_value': 246, 'new_value': 248}]
2025-06-01 12:01:02,674 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-06-01 12:01:03,192 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-06-01 12:01:03,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98084.36, 'new_value': 100356.26}, {'field': 'total_amount', 'old_value': 98084.36, 'new_value': 100356.26}, {'field': 'order_count', 'old_value': 373, 'new_value': 387}]
2025-06-01 12:01:03,192 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-06-01 12:01:03,723 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-06-01 12:01:03,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33091.4, 'new_value': 34842.4}, {'field': 'offline_amount', 'old_value': 24481.5, 'new_value': 25957.5}, {'field': 'total_amount', 'old_value': 57572.9, 'new_value': 60799.9}, {'field': 'order_count', 'old_value': 314, 'new_value': 330}]
2025-06-01 12:01:03,723 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-06-01 12:01:04,183 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-06-01 12:01:04,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77011.0, 'new_value': 79891.0}, {'field': 'total_amount', 'old_value': 77011.0, 'new_value': 79891.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-06-01 12:01:04,183 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-06-01 12:01:04,621 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-06-01 12:01:04,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 330996.0, 'new_value': 354188.0}, {'field': 'total_amount', 'old_value': 330996.0, 'new_value': 354188.0}, {'field': 'order_count', 'old_value': 278, 'new_value': 288}]
2025-06-01 12:01:04,621 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-06-01 12:01:05,161 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-06-01 12:01:05,161 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20589.5, 'new_value': 21487.73}, {'field': 'offline_amount', 'old_value': 327604.04, 'new_value': 339454.66}, {'field': 'total_amount', 'old_value': 348193.54, 'new_value': 360942.39}, {'field': 'order_count', 'old_value': 19319, 'new_value': 19833}]
2025-06-01 12:01:05,161 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-06-01 12:01:05,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-06-01 12:01:05,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7932.0, 'new_value': 9787.0}, {'field': 'total_amount', 'old_value': 7932.0, 'new_value': 9787.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 43}]
2025-06-01 12:01:05,717 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-06-01 12:01:06,206 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-06-01 12:01:06,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 349095.07, 'new_value': 358769.42}, {'field': 'total_amount', 'old_value': 349095.07, 'new_value': 358769.42}, {'field': 'order_count', 'old_value': 9773, 'new_value': 10041}]
2025-06-01 12:01:06,207 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-06-01 12:01:06,719 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-06-01 12:01:06,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59252.18, 'new_value': 60480.51}, {'field': 'offline_amount', 'old_value': 41496.0, 'new_value': 42338.0}, {'field': 'total_amount', 'old_value': 100748.18, 'new_value': 102818.51}, {'field': 'order_count', 'old_value': 1247, 'new_value': 1276}]
2025-06-01 12:01:06,720 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-06-01 12:01:07,126 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-06-01 12:01:07,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173987.92, 'new_value': 177343.39}, {'field': 'total_amount', 'old_value': 173987.92, 'new_value': 177343.39}, {'field': 'order_count', 'old_value': 871, 'new_value': 893}]
2025-06-01 12:01:07,126 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-06-01 12:01:07,622 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-06-01 12:01:07,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75810.0, 'new_value': 77694.0}, {'field': 'total_amount', 'old_value': 75810.0, 'new_value': 77694.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-01 12:01:07,623 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-06-01 12:01:08,064 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-06-01 12:01:08,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 9836.0, 'new_value': 39836.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-06-01 12:01:08,064 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-06-01 12:01:08,494 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-06-01 12:01:08,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116437.07, 'new_value': 120637.6}, {'field': 'offline_amount', 'old_value': 85979.69, 'new_value': 87475.09}, {'field': 'total_amount', 'old_value': 202416.76, 'new_value': 208112.69}, {'field': 'order_count', 'old_value': 8648, 'new_value': 8893}]
2025-06-01 12:01:08,495 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-06-01 12:01:08,921 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-06-01 12:01:08,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135960.0, 'new_value': 140480.0}, {'field': 'total_amount', 'old_value': 135960.0, 'new_value': 140480.0}, {'field': 'order_count', 'old_value': 6685, 'new_value': 6790}]
2025-06-01 12:01:08,921 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-06-01 12:01:09,389 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-06-01 12:01:09,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61656.0, 'new_value': 64504.0}, {'field': 'total_amount', 'old_value': 62005.0, 'new_value': 64853.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 119}]
2025-06-01 12:01:09,389 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-06-01 12:01:09,865 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-06-01 12:01:09,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7562088.0, 'new_value': 7960112.0}, {'field': 'total_amount', 'old_value': 7562088.0, 'new_value': 7960112.0}, {'field': 'order_count', 'old_value': 128047, 'new_value': 133242}]
2025-06-01 12:01:09,865 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-06-01 12:01:10,382 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-06-01 12:01:10,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56502.0, 'new_value': 58169.0}, {'field': 'total_amount', 'old_value': 56502.0, 'new_value': 58169.0}, {'field': 'order_count', 'old_value': 407, 'new_value': 425}]
2025-06-01 12:01:10,382 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-06-01 12:01:10,937 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-06-01 12:01:10,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 542513.5, 'new_value': 571234.5}, {'field': 'total_amount', 'old_value': 590623.48, 'new_value': 619344.48}, {'field': 'order_count', 'old_value': 4696, 'new_value': 4914}]
2025-06-01 12:01:10,938 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-06-01 12:01:11,360 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-06-01 12:01:11,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44050.08, 'new_value': 46747.08}, {'field': 'total_amount', 'old_value': 44050.08, 'new_value': 46747.08}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-06-01 12:01:11,361 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-06-01 12:01:11,909 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-06-01 12:01:11,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147208.0, 'new_value': 155707.0}, {'field': 'total_amount', 'old_value': 147208.0, 'new_value': 155707.0}, {'field': 'order_count', 'old_value': 5435, 'new_value': 5746}]
2025-06-01 12:01:11,909 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-06-01 12:01:12,375 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-06-01 12:01:12,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37547.07, 'new_value': 39196.07}, {'field': 'total_amount', 'old_value': 37547.07, 'new_value': 39196.07}, {'field': 'order_count', 'old_value': 3579, 'new_value': 3737}]
2025-06-01 12:01:12,376 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-06-01 12:01:12,846 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-06-01 12:01:12,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 272952.0, 'new_value': 281148.0}, {'field': 'total_amount', 'old_value': 272952.0, 'new_value': 281148.0}, {'field': 'order_count', 'old_value': 22746, 'new_value': 23429}]
2025-06-01 12:01:12,846 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-06-01 12:01:13,307 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-06-01 12:01:13,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123883.98, 'new_value': 128934.88}, {'field': 'total_amount', 'old_value': 123883.98, 'new_value': 128934.88}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1171}]
2025-06-01 12:01:13,308 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-06-01 12:01:13,832 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-06-01 12:01:13,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54087.0, 'new_value': 75946.96}, {'field': 'total_amount', 'old_value': 54087.0, 'new_value': 75946.96}, {'field': 'order_count', 'old_value': 17604, 'new_value': 17847}]
2025-06-01 12:01:13,833 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-06-01 12:01:14,312 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-06-01 12:01:14,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51085.5, 'new_value': 52050.8}, {'field': 'total_amount', 'old_value': 51085.5, 'new_value': 52050.8}, {'field': 'order_count', 'old_value': 76, 'new_value': 78}]
2025-06-01 12:01:14,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-06-01 12:01:14,734 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-06-01 12:01:14,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106398.8, 'new_value': 160784.8}, {'field': 'total_amount', 'old_value': 113748.8, 'new_value': 168134.8}, {'field': 'order_count', 'old_value': 51, 'new_value': 69}]
2025-06-01 12:01:14,735 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-06-01 12:01:15,171 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-06-01 12:01:15,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 370611.71, 'new_value': 393735.52}, {'field': 'offline_amount', 'old_value': 22787.94, 'new_value': 23619.34}, {'field': 'total_amount', 'old_value': 393399.65, 'new_value': 417354.86}, {'field': 'order_count', 'old_value': 15552, 'new_value': 16477}]
2025-06-01 12:01:15,171 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-06-01 12:01:15,631 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-06-01 12:01:15,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 462295.0, 'new_value': 485188.0}, {'field': 'total_amount', 'old_value': 471113.99, 'new_value': 494006.99}, {'field': 'order_count', 'old_value': 84, 'new_value': 89}]
2025-06-01 12:01:15,631 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-06-01 12:01:16,085 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-06-01 12:01:16,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149221.58, 'new_value': 156161.95}, {'field': 'offline_amount', 'old_value': 317341.91, 'new_value': 335233.04}, {'field': 'total_amount', 'old_value': 466563.49, 'new_value': 491394.99}, {'field': 'order_count', 'old_value': 6020, 'new_value': 6238}]
2025-06-01 12:01:16,085 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-06-01 12:01:16,517 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-06-01 12:01:16,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376382.4, 'new_value': 432924.4}, {'field': 'total_amount', 'old_value': 376382.4, 'new_value': 432924.4}, {'field': 'order_count', 'old_value': 160, 'new_value': 178}]
2025-06-01 12:01:16,517 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-06-01 12:01:16,988 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-06-01 12:01:16,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24341.5, 'new_value': 26380.55}, {'field': 'total_amount', 'old_value': 26641.5, 'new_value': 28680.55}, {'field': 'order_count', 'old_value': 513, 'new_value': 556}]
2025-06-01 12:01:16,989 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-06-01 12:01:17,489 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-06-01 12:01:17,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32988.0, 'new_value': 33746.0}, {'field': 'total_amount', 'old_value': 32988.0, 'new_value': 33746.0}, {'field': 'order_count', 'old_value': 319, 'new_value': 327}]
2025-06-01 12:01:17,489 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-06-01 12:01:17,961 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-06-01 12:01:17,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96808.0, 'new_value': 100286.0}, {'field': 'total_amount', 'old_value': 106719.0, 'new_value': 110197.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 65}]
2025-06-01 12:01:17,961 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-06-01 12:01:18,461 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-06-01 12:01:18,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141003.48, 'new_value': 191160.48}, {'field': 'total_amount', 'old_value': 186069.23, 'new_value': 236226.23}, {'field': 'order_count', 'old_value': 142, 'new_value': 1407}]
2025-06-01 12:01:18,462 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-06-01 12:01:18,983 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-06-01 12:01:18,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107894.0, 'new_value': 111693.0}, {'field': 'total_amount', 'old_value': 107894.0, 'new_value': 111693.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-06-01 12:01:18,984 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-06-01 12:01:19,445 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-06-01 12:01:19,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30631.35, 'new_value': 31715.33}, {'field': 'offline_amount', 'old_value': 39402.5, 'new_value': 39843.77}, {'field': 'total_amount', 'old_value': 70033.85, 'new_value': 71559.1}, {'field': 'order_count', 'old_value': 3171, 'new_value': 3244}]
2025-06-01 12:01:19,445 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-06-01 12:01:19,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-06-01 12:01:19,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1979351.61, 'new_value': 2066596.6}, {'field': 'offline_amount', 'old_value': 263908.59, 'new_value': 264107.59}, {'field': 'total_amount', 'old_value': 2243260.2, 'new_value': 2330704.19}, {'field': 'order_count', 'old_value': 7890, 'new_value': 8168}]
2025-06-01 12:01:19,931 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-06-01 12:01:20,424 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-06-01 12:01:20,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40613.0, 'new_value': 42567.0}, {'field': 'total_amount', 'old_value': 41989.0, 'new_value': 43943.0}, {'field': 'order_count', 'old_value': 4207, 'new_value': 4392}]
2025-06-01 12:01:20,424 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-06-01 12:01:20,861 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-06-01 12:01:20,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13605.0, 'new_value': 14144.0}, {'field': 'total_amount', 'old_value': 13605.0, 'new_value': 14144.0}, {'field': 'order_count', 'old_value': 693, 'new_value': 697}]
2025-06-01 12:01:20,861 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-06-01 12:01:21,304 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-06-01 12:01:21,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164350.0, 'new_value': 187348.0}, {'field': 'total_amount', 'old_value': 164350.0, 'new_value': 187348.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-06-01 12:01:21,304 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-06-01 12:01:21,834 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-06-01 12:01:21,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19484.5, 'new_value': 20289.6}, {'field': 'offline_amount', 'old_value': 47224.9, 'new_value': 47661.8}, {'field': 'total_amount', 'old_value': 66709.4, 'new_value': 67951.4}, {'field': 'order_count', 'old_value': 244, 'new_value': 253}]
2025-06-01 12:01:21,834 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-06-01 12:01:22,311 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-06-01 12:01:22,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 539914.48, 'new_value': 554847.48}, {'field': 'total_amount', 'old_value': 539914.48, 'new_value': 554847.48}, {'field': 'order_count', 'old_value': 2782, 'new_value': 2842}]
2025-06-01 12:01:22,312 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-06-01 12:01:22,728 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-06-01 12:01:22,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 726234.12, 'new_value': 751418.06}, {'field': 'total_amount', 'old_value': 728147.17, 'new_value': 753331.11}]
2025-06-01 12:01:22,729 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-06-01 12:01:23,208 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-06-01 12:01:23,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1137320.0, 'new_value': 1145710.0}, {'field': 'total_amount', 'old_value': 1137320.0, 'new_value': 1145710.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 139}]
2025-06-01 12:01:23,209 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-06-01 12:01:23,659 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-06-01 12:01:23,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164663.59, 'new_value': 175985.07}, {'field': 'total_amount', 'old_value': 164663.59, 'new_value': 175985.07}, {'field': 'order_count', 'old_value': 4290, 'new_value': 4540}]
2025-06-01 12:01:23,659 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-06-01 12:01:24,119 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-06-01 12:01:24,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25034.0, 'new_value': 26185.0}, {'field': 'total_amount', 'old_value': 25034.0, 'new_value': 26185.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 146}]
2025-06-01 12:01:24,120 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-06-01 12:01:24,591 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-06-01 12:01:24,591 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 499709.0, 'new_value': 500078.9}, {'field': 'total_amount', 'old_value': 499709.0, 'new_value': 500078.9}, {'field': 'order_count', 'old_value': 2408, 'new_value': 2793}]
2025-06-01 12:01:24,592 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-06-01 12:01:25,094 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-06-01 12:01:25,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8325.0, 'new_value': 8900.0}, {'field': 'offline_amount', 'old_value': 37159.0, 'new_value': 40061.0}, {'field': 'total_amount', 'old_value': 45484.0, 'new_value': 48961.0}, {'field': 'order_count', 'old_value': 347, 'new_value': 367}]
2025-06-01 12:01:25,095 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-06-01 12:01:25,498 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-06-01 12:01:25,498 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17301.6, 'new_value': 17430.6}, {'field': 'offline_amount', 'old_value': 50171.8, 'new_value': 71524.8}, {'field': 'total_amount', 'old_value': 67473.4, 'new_value': 88955.4}, {'field': 'order_count', 'old_value': 111, 'new_value': 114}]
2025-06-01 12:01:25,498 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-06-01 12:01:26,000 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-06-01 12:01:26,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110046.05, 'new_value': 113961.58}, {'field': 'offline_amount', 'old_value': 270141.74, 'new_value': 275572.64}, {'field': 'total_amount', 'old_value': 380187.79, 'new_value': 389534.22}, {'field': 'order_count', 'old_value': 18826, 'new_value': 19189}]
2025-06-01 12:01:26,000 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-06-01 12:01:26,451 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-06-01 12:01:26,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 846317.7, 'new_value': 879457.81}, {'field': 'total_amount', 'old_value': 846317.7, 'new_value': 879457.81}, {'field': 'order_count', 'old_value': 6420, 'new_value': 6637}]
2025-06-01 12:01:26,452 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-06-01 12:01:26,944 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-06-01 12:01:26,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233211.0, 'new_value': 248616.0}, {'field': 'total_amount', 'old_value': 233211.0, 'new_value': 248616.0}, {'field': 'order_count', 'old_value': 741, 'new_value': 789}]
2025-06-01 12:01:26,944 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-06-01 12:01:27,532 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-06-01 12:01:27,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74777.18, 'new_value': 77544.65}, {'field': 'offline_amount', 'old_value': 520576.14, 'new_value': 554113.06}, {'field': 'total_amount', 'old_value': 595353.32, 'new_value': 631657.71}, {'field': 'order_count', 'old_value': 2913, 'new_value': 3067}]
2025-06-01 12:01:27,532 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-06-01 12:01:27,922 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-06-01 12:01:27,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2010.1, 'new_value': 2171.86}, {'field': 'offline_amount', 'old_value': 23789.13, 'new_value': 25827.51}, {'field': 'total_amount', 'old_value': 25799.23, 'new_value': 27999.37}, {'field': 'order_count', 'old_value': 928, 'new_value': 997}]
2025-06-01 12:01:27,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-06-01 12:01:28,486 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-06-01 12:01:28,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51231.0, 'new_value': 51430.0}, {'field': 'total_amount', 'old_value': 51231.0, 'new_value': 51430.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-01 12:01:28,486 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-06-01 12:01:28,940 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-06-01 12:01:28,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60365.33, 'new_value': 62759.73}, {'field': 'offline_amount', 'old_value': 404363.93, 'new_value': 420767.53}, {'field': 'total_amount', 'old_value': 464729.26, 'new_value': 483527.26}, {'field': 'order_count', 'old_value': 2974, 'new_value': 3083}]
2025-06-01 12:01:28,940 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-06-01 12:01:29,377 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-06-01 12:01:29,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256361.83, 'new_value': 263270.83}, {'field': 'total_amount', 'old_value': 256361.83, 'new_value': 263270.83}, {'field': 'order_count', 'old_value': 1422, 'new_value': 1461}]
2025-06-01 12:01:29,377 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-06-01 12:01:29,863 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-06-01 12:01:29,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80560.0, 'new_value': 90934.0}, {'field': 'total_amount', 'old_value': 80560.0, 'new_value': 90934.0}, {'field': 'order_count', 'old_value': 2371, 'new_value': 2677}]
2025-06-01 12:01:29,863 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-06-01 12:01:30,364 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-06-01 12:01:30,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 435636.89, 'new_value': 465506.53}, {'field': 'total_amount', 'old_value': 457800.01, 'new_value': 487669.65}, {'field': 'order_count', 'old_value': 19439, 'new_value': 20518}]
2025-06-01 12:01:30,364 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-06-01 12:01:30,815 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-06-01 12:01:30,815 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34297.88, 'new_value': 35162.48}, {'field': 'offline_amount', 'old_value': 266373.84, 'new_value': 282756.04}, {'field': 'total_amount', 'old_value': 300671.72, 'new_value': 317918.52}, {'field': 'order_count', 'old_value': 9467, 'new_value': 9981}]
2025-06-01 12:01:30,815 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-06-01 12:01:31,298 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-06-01 12:01:31,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119198.0, 'new_value': 128016.0}, {'field': 'total_amount', 'old_value': 119198.0, 'new_value': 128016.0}, {'field': 'order_count', 'old_value': 803, 'new_value': 833}]
2025-06-01 12:01:31,298 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-06-01 12:01:31,792 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-06-01 12:01:31,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114594.38, 'new_value': 118872.47}, {'field': 'offline_amount', 'old_value': 125298.1, 'new_value': 128803.01}, {'field': 'total_amount', 'old_value': 239892.48, 'new_value': 247675.48}, {'field': 'order_count', 'old_value': 9822, 'new_value': 10122}]
2025-06-01 12:01:31,793 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-06-01 12:01:32,211 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-06-01 12:01:32,211 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 235485.94, 'new_value': 296465.59}, {'field': 'offline_amount', 'old_value': 117841.62, 'new_value': 136221.74}, {'field': 'total_amount', 'old_value': 353327.56, 'new_value': 432687.33}, {'field': 'order_count', 'old_value': 651, 'new_value': 766}]
2025-06-01 12:01:32,211 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-06-01 12:01:32,625 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-06-01 12:01:32,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271756.81, 'new_value': 304962.77}, {'field': 'total_amount', 'old_value': 290930.24, 'new_value': 324136.2}, {'field': 'order_count', 'old_value': 6009, 'new_value': 6544}]
2025-06-01 12:01:32,625 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-06-01 12:01:33,161 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-06-01 12:01:33,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61343.0, 'new_value': 63054.0}, {'field': 'total_amount', 'old_value': 61343.0, 'new_value': 63054.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 142}]
2025-06-01 12:01:33,162 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-06-01 12:01:33,666 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-06-01 12:01:33,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 819045.72, 'new_value': 865097.72}, {'field': 'total_amount', 'old_value': 819045.72, 'new_value': 865097.72}, {'field': 'order_count', 'old_value': 6413, 'new_value': 6701}]
2025-06-01 12:01:33,666 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-06-01 12:01:34,091 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-06-01 12:01:34,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33060.0, 'new_value': 33447.0}, {'field': 'total_amount', 'old_value': 33060.0, 'new_value': 33447.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 203}]
2025-06-01 12:01:34,091 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-06-01 12:01:34,564 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-06-01 12:01:34,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 790561.0, 'new_value': 836053.0}, {'field': 'total_amount', 'old_value': 790561.0, 'new_value': 836053.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 92}]
2025-06-01 12:01:34,565 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-06-01 12:01:35,083 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-06-01 12:01:35,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89728.18, 'new_value': 96697.91}, {'field': 'total_amount', 'old_value': 89728.18, 'new_value': 96697.91}, {'field': 'order_count', 'old_value': 5244, 'new_value': 5626}]
2025-06-01 12:01:35,083 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-06-01 12:01:35,545 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-06-01 12:01:35,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66625.41, 'new_value': 69232.41}, {'field': 'offline_amount', 'old_value': 53236.75, 'new_value': 53868.75}, {'field': 'total_amount', 'old_value': 119862.16, 'new_value': 123101.16}, {'field': 'order_count', 'old_value': 2415, 'new_value': 2479}]
2025-06-01 12:01:35,545 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-06-01 12:01:36,144 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-06-01 12:01:36,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17891.45, 'new_value': 18362.45}, {'field': 'offline_amount', 'old_value': 337154.0, 'new_value': 348565.0}, {'field': 'total_amount', 'old_value': 355045.45, 'new_value': 366927.45}, {'field': 'order_count', 'old_value': 1903, 'new_value': 1958}]
2025-06-01 12:01:36,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-06-01 12:01:36,534 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-06-01 12:01:36,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2480000.0, 'new_value': 2630000.0}, {'field': 'total_amount', 'old_value': 2480000.0, 'new_value': 2630000.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 288}]
2025-06-01 12:01:36,535 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-06-01 12:01:37,026 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-06-01 12:01:37,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218160.3, 'new_value': 228615.0}, {'field': 'total_amount', 'old_value': 218160.3, 'new_value': 228615.0}, {'field': 'order_count', 'old_value': 2820, 'new_value': 2944}]
2025-06-01 12:01:37,027 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-06-01 12:01:37,457 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-06-01 12:01:37,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98338.53, 'new_value': 99091.53}, {'field': 'total_amount', 'old_value': 98338.53, 'new_value': 99091.53}, {'field': 'order_count', 'old_value': 130, 'new_value': 131}]
2025-06-01 12:01:37,457 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-06-01 12:01:37,938 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-06-01 12:01:37,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50691.25, 'new_value': 52791.75}, {'field': 'offline_amount', 'old_value': 1294667.69, 'new_value': 1380661.18}, {'field': 'total_amount', 'old_value': 1345358.94, 'new_value': 1433452.93}, {'field': 'order_count', 'old_value': 6674, 'new_value': 6947}]
2025-06-01 12:01:37,938 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-06-01 12:01:38,437 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-06-01 12:01:38,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382064.0, 'new_value': 400618.0}, {'field': 'total_amount', 'old_value': 409154.0, 'new_value': 427708.0}, {'field': 'order_count', 'old_value': 8927, 'new_value': 9296}]
2025-06-01 12:01:38,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-06-01 12:01:38,962 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-06-01 12:01:38,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4800.0, 'new_value': 6430.0}, {'field': 'offline_amount', 'old_value': 348646.0, 'new_value': 368646.0}, {'field': 'total_amount', 'old_value': 353446.0, 'new_value': 375076.0}, {'field': 'order_count', 'old_value': 262, 'new_value': 277}]
2025-06-01 12:01:38,962 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-06-01 12:01:39,422 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-06-01 12:01:39,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64418.85, 'new_value': 66881.9}, {'field': 'total_amount', 'old_value': 67859.65, 'new_value': 70322.7}, {'field': 'order_count', 'old_value': 253, 'new_value': 263}]
2025-06-01 12:01:39,422 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-06-01 12:01:39,863 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-06-01 12:01:39,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123532.0, 'new_value': 154364.0}, {'field': 'total_amount', 'old_value': 123532.0, 'new_value': 154364.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 19}]
2025-06-01 12:01:39,878 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-06-01 12:01:40,322 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-06-01 12:01:40,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 803497.42, 'new_value': 841817.83}, {'field': 'total_amount', 'old_value': 803497.42, 'new_value': 841817.83}, {'field': 'order_count', 'old_value': 9409, 'new_value': 9728}]
2025-06-01 12:01:40,322 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-06-01 12:01:40,801 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-06-01 12:01:40,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 497973.2, 'new_value': 587367.4}, {'field': 'total_amount', 'old_value': 717105.1, 'new_value': 806499.3}, {'field': 'order_count', 'old_value': 4969, 'new_value': 5058}]
2025-06-01 12:01:40,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-06-01 12:01:41,234 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-06-01 12:01:41,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202468.0, 'new_value': 227996.0}, {'field': 'total_amount', 'old_value': 202468.0, 'new_value': 227996.0}, {'field': 'order_count', 'old_value': 3388, 'new_value': 3732}]
2025-06-01 12:01:41,235 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-06-01 12:01:41,694 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-06-01 12:01:41,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271935.9, 'new_value': 285325.26}, {'field': 'total_amount', 'old_value': 271935.9, 'new_value': 285325.26}, {'field': 'order_count', 'old_value': 2131, 'new_value': 2230}]
2025-06-01 12:01:41,694 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-06-01 12:01:42,119 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-06-01 12:01:42,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 545000.0, 'new_value': 555000.0}, {'field': 'total_amount', 'old_value': 545000.0, 'new_value': 555000.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 155}]
2025-06-01 12:01:42,120 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-06-01 12:01:42,551 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-06-01 12:01:42,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 525000.0, 'new_value': 535000.0}, {'field': 'total_amount', 'old_value': 525000.0, 'new_value': 535000.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 154}]
2025-06-01 12:01:42,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-06-01 12:01:42,984 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-06-01 12:01:42,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3598674.0, 'new_value': 3748674.0}, {'field': 'total_amount', 'old_value': 3598674.0, 'new_value': 3748674.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 308}]
2025-06-01 12:01:42,985 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-06-01 12:01:43,461 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-06-01 12:01:43,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 455159.63, 'new_value': 472136.81}, {'field': 'total_amount', 'old_value': 468670.11, 'new_value': 485647.29}, {'field': 'order_count', 'old_value': 1492, 'new_value': 1538}]
2025-06-01 12:01:43,461 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-06-01 12:01:43,829 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-06-01 12:01:43,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64081.0, 'new_value': 72355.0}, {'field': 'offline_amount', 'old_value': 290396.0, 'new_value': 291940.0}, {'field': 'total_amount', 'old_value': 354477.0, 'new_value': 364295.0}, {'field': 'order_count', 'old_value': 314, 'new_value': 323}]
2025-06-01 12:01:43,830 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-06-01 12:01:44,288 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-06-01 12:01:44,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55486.0, 'new_value': 65886.0}, {'field': 'total_amount', 'old_value': 55486.0, 'new_value': 65886.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-01 12:01:44,288 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-06-01 12:01:44,801 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-06-01 12:01:44,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27334.82, 'new_value': 28419.82}, {'field': 'offline_amount', 'old_value': 18039.94, 'new_value': 18891.94}, {'field': 'total_amount', 'old_value': 45374.76, 'new_value': 47311.76}, {'field': 'order_count', 'old_value': 1884, 'new_value': 1959}]
2025-06-01 12:01:44,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-06-01 12:01:45,283 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-06-01 12:01:45,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343540.0, 'new_value': 352778.0}, {'field': 'total_amount', 'old_value': 343540.0, 'new_value': 352778.0}, {'field': 'order_count', 'old_value': 517, 'new_value': 529}]
2025-06-01 12:01:45,283 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-06-01 12:01:45,722 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-06-01 12:01:45,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43036.5, 'new_value': 45946.0}, {'field': 'total_amount', 'old_value': 43036.5, 'new_value': 45946.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 201}]
2025-06-01 12:01:45,722 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-06-01 12:01:46,286 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-06-01 12:01:46,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23762.5, 'new_value': 25217.1}, {'field': 'offline_amount', 'old_value': 58608.7, 'new_value': 64809.3}, {'field': 'total_amount', 'old_value': 82371.2, 'new_value': 90026.4}, {'field': 'order_count', 'old_value': 683, 'new_value': 725}]
2025-06-01 12:01:46,286 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-06-01 12:01:46,850 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-06-01 12:01:46,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 144650.1, 'new_value': 148328.9}, {'field': 'offline_amount', 'old_value': 243668.64, 'new_value': 255414.39}, {'field': 'total_amount', 'old_value': 388318.74, 'new_value': 403743.29}, {'field': 'order_count', 'old_value': 12148, 'new_value': 12517}]
2025-06-01 12:01:46,851 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-06-01 12:01:47,367 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-06-01 12:01:47,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125099.0, 'new_value': 131284.0}, {'field': 'total_amount', 'old_value': 125099.0, 'new_value': 131284.0}, {'field': 'order_count', 'old_value': 531, 'new_value': 556}]
2025-06-01 12:01:47,367 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-06-01 12:01:47,826 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-06-01 12:01:47,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152099.0, 'new_value': 152717.0}, {'field': 'total_amount', 'old_value': 152099.0, 'new_value': 152717.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-01 12:01:47,826 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-06-01 12:01:48,281 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-06-01 12:01:48,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 199958.37, 'new_value': 207423.4}, {'field': 'offline_amount', 'old_value': 58693.9, 'new_value': 61808.4}, {'field': 'total_amount', 'old_value': 258652.27, 'new_value': 269231.8}, {'field': 'order_count', 'old_value': 14830, 'new_value': 15408}]
2025-06-01 12:01:48,281 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-06-01 12:01:48,750 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-06-01 12:01:48,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302300.13, 'new_value': 328188.73}, {'field': 'total_amount', 'old_value': 324787.53, 'new_value': 350676.13}, {'field': 'order_count', 'old_value': 1837, 'new_value': 1949}]
2025-06-01 12:01:48,750 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-06-01 12:01:49,233 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-06-01 12:01:49,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 234414.14, 'new_value': 245480.09}, {'field': 'offline_amount', 'old_value': 415923.03, 'new_value': 425923.03}, {'field': 'total_amount', 'old_value': 650337.17, 'new_value': 671403.12}, {'field': 'order_count', 'old_value': 1662, 'new_value': 1722}]
2025-06-01 12:01:49,234 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-06-01 12:01:49,701 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-06-01 12:01:49,701 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 185039.69, 'new_value': 192600.05}, {'field': 'offline_amount', 'old_value': 347856.27, 'new_value': 369883.37}, {'field': 'total_amount', 'old_value': 532895.96, 'new_value': 562483.42}, {'field': 'order_count', 'old_value': 4470, 'new_value': 4679}]
2025-06-01 12:01:49,701 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-06-01 12:01:50,193 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-06-01 12:01:50,193 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65281.17, 'new_value': 66939.91}, {'field': 'offline_amount', 'old_value': 49840.27, 'new_value': 53536.13}, {'field': 'total_amount', 'old_value': 115121.44, 'new_value': 120476.04}, {'field': 'order_count', 'old_value': 9752, 'new_value': 10180}]
2025-06-01 12:01:50,194 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-06-01 12:01:50,623 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-06-01 12:01:50,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94280.44, 'new_value': 96722.08}, {'field': 'total_amount', 'old_value': 103067.78, 'new_value': 105509.42}, {'field': 'order_count', 'old_value': 488, 'new_value': 502}]
2025-06-01 12:01:50,623 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-06-01 12:01:51,072 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-06-01 12:01:51,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 883530.99, 'new_value': 932396.56}, {'field': 'total_amount', 'old_value': 883530.99, 'new_value': 932396.56}, {'field': 'order_count', 'old_value': 7256, 'new_value': 7554}]
2025-06-01 12:01:51,072 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-06-01 12:01:51,503 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-06-01 12:01:51,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29452.5, 'new_value': 31562.7}, {'field': 'offline_amount', 'old_value': 232914.1, 'new_value': 242089.8}, {'field': 'total_amount', 'old_value': 262366.6, 'new_value': 273652.5}, {'field': 'order_count', 'old_value': 8284, 'new_value': 8665}]
2025-06-01 12:01:51,503 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-06-01 12:01:52,034 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-06-01 12:01:52,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 567666.0, 'new_value': 583747.0}, {'field': 'total_amount', 'old_value': 567666.0, 'new_value': 583747.0}, {'field': 'order_count', 'old_value': 4181, 'new_value': 4420}]
2025-06-01 12:01:52,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-06-01 12:01:52,607 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-06-01 12:01:52,607 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7763.0, 'new_value': 8325.0}, {'field': 'offline_amount', 'old_value': 5806.0, 'new_value': 6075.0}, {'field': 'total_amount', 'old_value': 13569.0, 'new_value': 14400.0}, {'field': 'order_count', 'old_value': 147, 'new_value': 153}]
2025-06-01 12:01:52,607 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-06-01 12:01:53,119 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-06-01 12:01:53,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4282.0, 'new_value': 4529.0}, {'field': 'offline_amount', 'old_value': 31687.8, 'new_value': 34291.4}, {'field': 'total_amount', 'old_value': 35969.8, 'new_value': 38820.4}, {'field': 'order_count', 'old_value': 1305, 'new_value': 1408}]
2025-06-01 12:01:53,119 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-06-01 12:01:53,640 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-06-01 12:01:53,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112280.0, 'new_value': 116160.0}, {'field': 'total_amount', 'old_value': 112280.0, 'new_value': 116160.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-01 12:01:53,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-06-01 12:01:54,109 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-06-01 12:01:54,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158322.0, 'new_value': 172122.0}, {'field': 'total_amount', 'old_value': 158322.0, 'new_value': 172122.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-06-01 12:01:54,109 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-06-01 12:01:54,545 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-06-01 12:01:54,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146580.0, 'new_value': 150812.22}, {'field': 'total_amount', 'old_value': 153809.07, 'new_value': 158041.29}, {'field': 'order_count', 'old_value': 818, 'new_value': 838}]
2025-06-01 12:01:54,545 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-06-01 12:01:55,057 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-06-01 12:01:55,057 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40703.66, 'new_value': 41632.66}, {'field': 'offline_amount', 'old_value': 38476.12, 'new_value': 39741.12}, {'field': 'total_amount', 'old_value': 79179.78, 'new_value': 81373.78}, {'field': 'order_count', 'old_value': 347, 'new_value': 360}]
2025-06-01 12:01:55,057 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-06-01 12:01:55,489 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-06-01 12:01:55,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9259.0, 'new_value': 11121.0}, {'field': 'offline_amount', 'old_value': 61637.7, 'new_value': 63619.7}, {'field': 'total_amount', 'old_value': 70896.7, 'new_value': 74740.7}, {'field': 'order_count', 'old_value': 569, 'new_value': 592}]
2025-06-01 12:01:55,489 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-06-01 12:01:55,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-06-01 12:01:55,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5898.0, 'new_value': 6238.0}, {'field': 'offline_amount', 'old_value': 24283.0, 'new_value': 25263.0}, {'field': 'total_amount', 'old_value': 30181.0, 'new_value': 31501.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 236}]
2025-06-01 12:01:55,923 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-06-01 12:01:56,422 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-06-01 12:01:56,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75046.0, 'new_value': 78398.0}, {'field': 'offline_amount', 'old_value': 385033.0, 'new_value': 402389.0}, {'field': 'total_amount', 'old_value': 460079.0, 'new_value': 480787.0}, {'field': 'order_count', 'old_value': 1810, 'new_value': 1895}]
2025-06-01 12:01:56,423 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-06-01 12:01:56,873 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-06-01 12:01:56,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1201666.0, 'new_value': 1281300.0}, {'field': 'total_amount', 'old_value': 1201666.0, 'new_value': 1281300.0}, {'field': 'order_count', 'old_value': 5308, 'new_value': 5680}]
2025-06-01 12:01:56,874 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-06-01 12:01:57,344 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-06-01 12:01:57,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14154606.0, 'new_value': 14797154.0}, {'field': 'total_amount', 'old_value': 14154606.0, 'new_value': 14797154.0}, {'field': 'order_count', 'old_value': 45869, 'new_value': 48288}]
2025-06-01 12:01:57,344 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-06-01 12:01:57,873 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-06-01 12:01:57,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4227508.51, 'new_value': 4452277.95}, {'field': 'total_amount', 'old_value': 4227508.51, 'new_value': 4452277.95}, {'field': 'order_count', 'old_value': 7314, 'new_value': 7709}]
2025-06-01 12:01:57,874 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-06-01 12:01:58,321 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-06-01 12:01:58,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195133.04, 'new_value': 202241.13}, {'field': 'total_amount', 'old_value': 202572.68, 'new_value': 209680.77}, {'field': 'order_count', 'old_value': 14326, 'new_value': 14814}]
2025-06-01 12:01:58,322 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-06-01 12:01:58,782 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-06-01 12:01:58,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 334665.92, 'new_value': 345105.11}, {'field': 'offline_amount', 'old_value': 237346.45, 'new_value': 251103.15}, {'field': 'total_amount', 'old_value': 572012.37, 'new_value': 596208.26}, {'field': 'order_count', 'old_value': 23271, 'new_value': 24221}]
2025-06-01 12:01:58,783 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-06-01 12:01:59,330 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-06-01 12:01:59,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343843.0, 'new_value': 375328.0}, {'field': 'total_amount', 'old_value': 343843.0, 'new_value': 375328.0}, {'field': 'order_count', 'old_value': 424, 'new_value': 449}]
2025-06-01 12:01:59,331 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-06-01 12:01:59,827 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-06-01 12:01:59,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91974.0, 'new_value': 109566.0}, {'field': 'total_amount', 'old_value': 99954.0, 'new_value': 117546.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-06-01 12:01:59,827 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-06-01 12:02:00,244 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-06-01 12:02:00,244 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220534.6, 'new_value': 231043.6}, {'field': 'total_amount', 'old_value': 380750.38, 'new_value': 391259.38}, {'field': 'order_count', 'old_value': 292, 'new_value': 308}]
2025-06-01 12:02:00,245 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-06-01 12:02:00,722 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-06-01 12:02:00,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367508.0, 'new_value': 423471.5}, {'field': 'total_amount', 'old_value': 367508.0, 'new_value': 423471.5}, {'field': 'order_count', 'old_value': 8018, 'new_value': 9280}]
2025-06-01 12:02:00,722 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-06-01 12:02:01,198 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-06-01 12:02:01,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62717.9, 'new_value': 64046.9}, {'field': 'total_amount', 'old_value': 62717.9, 'new_value': 64046.9}, {'field': 'order_count', 'old_value': 342, 'new_value': 352}]
2025-06-01 12:02:01,198 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-06-01 12:02:01,655 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-06-01 12:02:01,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 887164.18, 'new_value': 909024.14}, {'field': 'total_amount', 'old_value': 887164.18, 'new_value': 909024.14}, {'field': 'order_count', 'old_value': 4752, 'new_value': 4995}]
2025-06-01 12:02:01,655 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-06-01 12:02:02,111 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-06-01 12:02:02,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158501.4, 'new_value': 169507.03}, {'field': 'total_amount', 'old_value': 158501.4, 'new_value': 169507.03}, {'field': 'order_count', 'old_value': 11121, 'new_value': 11879}]
2025-06-01 12:02:02,111 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-06-01 12:02:02,550 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-06-01 12:02:02,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 489081.0, 'new_value': 545237.0}, {'field': 'total_amount', 'old_value': 489081.0, 'new_value': 545237.0}, {'field': 'order_count', 'old_value': 11073, 'new_value': 12339}]
2025-06-01 12:02:02,550 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-06-01 12:02:03,009 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-06-01 12:02:03,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81280.0, 'new_value': 102280.0}, {'field': 'total_amount', 'old_value': 81280.0, 'new_value': 102280.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-06-01 12:02:03,009 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-06-01 12:02:03,507 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-06-01 12:02:03,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76766.3, 'new_value': 78870.04}, {'field': 'total_amount', 'old_value': 84635.35, 'new_value': 86739.09}, {'field': 'order_count', 'old_value': 1546, 'new_value': 1573}]
2025-06-01 12:02:03,507 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-06-01 12:02:04,057 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-06-01 12:02:04,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6096951.17, 'new_value': 6467554.17}, {'field': 'total_amount', 'old_value': 6096951.17, 'new_value': 6467554.17}, {'field': 'order_count', 'old_value': 124058, 'new_value': 130238}]
2025-06-01 12:02:04,057 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-06-01 12:02:04,536 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-06-01 12:02:04,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169492.9, 'new_value': 219492.9}, {'field': 'total_amount', 'old_value': 169492.9, 'new_value': 219492.9}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-01 12:02:04,536 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-06-01 12:02:04,976 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-06-01 12:02:04,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 614143.39, 'new_value': 662302.39}, {'field': 'total_amount', 'old_value': 619689.75, 'new_value': 667848.75}, {'field': 'order_count', 'old_value': 6239, 'new_value': 6459}]
2025-06-01 12:02:04,976 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-06-01 12:02:05,609 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-06-01 12:02:05,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245131.85, 'new_value': 251602.71}, {'field': 'total_amount', 'old_value': 245131.85, 'new_value': 251602.71}, {'field': 'order_count', 'old_value': 4418, 'new_value': 4542}]
2025-06-01 12:02:05,609 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-06-01 12:02:06,098 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-06-01 12:02:06,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 410149.0, 'new_value': 417245.0}, {'field': 'total_amount', 'old_value': 410149.0, 'new_value': 417245.0}, {'field': 'order_count', 'old_value': 8803, 'new_value': 8961}]
2025-06-01 12:02:06,098 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-06-01 12:02:06,599 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-06-01 12:02:06,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 609270.0, 'new_value': 612858.0}, {'field': 'total_amount', 'old_value': 609270.0, 'new_value': 612858.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 112}]
2025-06-01 12:02:06,600 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-06-01 12:02:06,984 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-06-01 12:02:06,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152506.5, 'new_value': 159823.08}, {'field': 'total_amount', 'old_value': 157474.7, 'new_value': 164791.28}, {'field': 'order_count', 'old_value': 3999, 'new_value': 4157}]
2025-06-01 12:02:06,985 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-06-01 12:02:07,501 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-06-01 12:02:07,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 25000.0}, {'field': 'total_amount', 'old_value': 11206.0, 'new_value': 36206.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-06-01 12:02:07,501 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-06-01 12:02:07,942 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-06-01 12:02:07,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100062.0, 'new_value': 102792.0}, {'field': 'total_amount', 'old_value': 101912.0, 'new_value': 104642.0}, {'field': 'order_count', 'old_value': 571, 'new_value': 586}]
2025-06-01 12:02:07,942 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-06-01 12:02:08,336 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-06-01 12:02:08,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69652.49, 'new_value': 72677.66}, {'field': 'total_amount', 'old_value': 110129.88, 'new_value': 113155.05}, {'field': 'order_count', 'old_value': 7278, 'new_value': 7467}]
2025-06-01 12:02:08,336 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-06-01 12:02:08,851 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-06-01 12:02:08,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121258.63, 'new_value': 129927.96}, {'field': 'total_amount', 'old_value': 195030.81, 'new_value': 203700.14}, {'field': 'order_count', 'old_value': 12924, 'new_value': 13259}]
2025-06-01 12:02:08,851 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-06-01 12:02:09,285 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-06-01 12:02:09,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1383755.89, 'new_value': 1448766.92}, {'field': 'total_amount', 'old_value': 1383755.89, 'new_value': 1448766.92}, {'field': 'order_count', 'old_value': 4095, 'new_value': 4280}]
2025-06-01 12:02:09,285 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-06-01 12:02:09,755 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-06-01 12:02:09,755 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-06-01 12:02:09,755 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-06-01 12:02:10,216 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-06-01 12:02:10,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 867011.99, 'new_value': 925884.28}, {'field': 'total_amount', 'old_value': 867011.99, 'new_value': 925884.28}, {'field': 'order_count', 'old_value': 4781, 'new_value': 5011}]
2025-06-01 12:02:10,216 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-06-01 12:02:10,740 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-06-01 12:02:10,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 960773.91, 'new_value': 982953.81}, {'field': 'total_amount', 'old_value': 960773.91, 'new_value': 982953.81}, {'field': 'order_count', 'old_value': 2679, 'new_value': 2748}]
2025-06-01 12:02:10,740 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-06-01 12:02:11,173 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-06-01 12:02:11,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150028.0, 'new_value': 155360.0}, {'field': 'total_amount', 'old_value': 150028.0, 'new_value': 155360.0}, {'field': 'order_count', 'old_value': 708, 'new_value': 737}]
2025-06-01 12:02:11,174 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-06-01 12:02:11,627 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-06-01 12:02:11,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164012.0, 'new_value': 167842.0}, {'field': 'total_amount', 'old_value': 178369.0, 'new_value': 182199.0}, {'field': 'order_count', 'old_value': 673, 'new_value': 698}]
2025-06-01 12:02:11,628 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-06-01 12:02:12,124 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-06-01 12:02:12,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341585.0, 'new_value': 351685.0}, {'field': 'total_amount', 'old_value': 341585.0, 'new_value': 351685.0}, {'field': 'order_count', 'old_value': 804, 'new_value': 827}]
2025-06-01 12:02:12,124 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-06-01 12:02:12,528 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-06-01 12:02:12,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251221.0, 'new_value': 254410.0}, {'field': 'total_amount', 'old_value': 251221.0, 'new_value': 254410.0}, {'field': 'order_count', 'old_value': 26422, 'new_value': 26684}]
2025-06-01 12:02:12,528 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-06-01 12:02:13,003 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-06-01 12:02:13,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132337.0, 'new_value': 134694.0}, {'field': 'total_amount', 'old_value': 132337.0, 'new_value': 134694.0}, {'field': 'order_count', 'old_value': 1399, 'new_value': 1439}]
2025-06-01 12:02:13,003 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-06-01 12:02:13,485 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-06-01 12:02:13,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48989.0, 'new_value': 50609.0}, {'field': 'total_amount', 'old_value': 89114.4, 'new_value': 90734.4}, {'field': 'order_count', 'old_value': 2811, 'new_value': 2814}]
2025-06-01 12:02:13,485 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-06-01 12:02:13,911 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-06-01 12:02:13,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57746.66, 'new_value': 58631.66}, {'field': 'total_amount', 'old_value': 57746.66, 'new_value': 58631.66}, {'field': 'order_count', 'old_value': 977, 'new_value': 993}]
2025-06-01 12:02:13,912 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-06-01 12:02:14,355 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-06-01 12:02:14,355 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 148680.37, 'new_value': 161630.61}, {'field': 'offline_amount', 'old_value': 789572.91, 'new_value': 806836.03}, {'field': 'total_amount', 'old_value': 938253.28, 'new_value': 968466.64}, {'field': 'order_count', 'old_value': 2227, 'new_value': 2316}]
2025-06-01 12:02:14,356 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-06-01 12:02:15,117 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-06-01 12:02:15,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 558.0}, {'field': 'offline_amount', 'old_value': 138471.0, 'new_value': 141900.0}, {'field': 'total_amount', 'old_value': 138471.0, 'new_value': 142458.0}, {'field': 'order_count', 'old_value': 428, 'new_value': 445}]
2025-06-01 12:02:15,118 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-06-01 12:02:15,600 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-06-01 12:02:15,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116697.0, 'new_value': 125859.0}, {'field': 'total_amount', 'old_value': 122015.0, 'new_value': 131177.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-06-01 12:02:15,601 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-06-01 12:02:16,157 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-06-01 12:02:16,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215679.34, 'new_value': 222394.91}, {'field': 'total_amount', 'old_value': 215679.34, 'new_value': 222394.91}, {'field': 'order_count', 'old_value': 22838, 'new_value': 23587}]
2025-06-01 12:02:16,157 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-06-01 12:02:16,648 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-06-01 12:02:16,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20241.0, 'new_value': 20913.0}, {'field': 'total_amount', 'old_value': 20241.0, 'new_value': 20913.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 134}]
2025-06-01 12:02:16,649 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-06-01 12:02:17,111 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-06-01 12:02:17,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10856.44, 'new_value': 21307.98}, {'field': 'total_amount', 'old_value': 85511.34, 'new_value': 95962.88}, {'field': 'order_count', 'old_value': 5135, 'new_value': 5499}]
2025-06-01 12:02:17,112 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-06-01 12:02:17,594 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-06-01 12:02:17,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10367.63, 'new_value': 12254.97}, {'field': 'offline_amount', 'old_value': 61256.86, 'new_value': 64364.56}, {'field': 'total_amount', 'old_value': 71624.49, 'new_value': 76619.53}, {'field': 'order_count', 'old_value': 3065, 'new_value': 3087}]
2025-06-01 12:02:17,595 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-06-01 12:02:18,012 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-06-01 12:02:18,012 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71038.29, 'new_value': 77219.31}, {'field': 'offline_amount', 'old_value': 69674.97, 'new_value': 71206.97}, {'field': 'total_amount', 'old_value': 140713.26, 'new_value': 148426.28}, {'field': 'order_count', 'old_value': 634, 'new_value': 663}]
2025-06-01 12:02:18,012 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-06-01 12:02:18,506 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-06-01 12:02:18,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7142.95, 'new_value': 7452.95}, {'field': 'total_amount', 'old_value': 7142.95, 'new_value': 7452.95}, {'field': 'order_count', 'old_value': 399, 'new_value': 420}]
2025-06-01 12:02:18,506 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-06-01 12:02:18,971 - INFO - 更新表单数据成功: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-06-01 12:02:18,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45000.0, 'new_value': 55000.0}, {'field': 'total_amount', 'old_value': 45000.0, 'new_value': 55000.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-01 12:02:18,972 - INFO - 日期 2025-05 处理完成 - 更新: 229 条，插入: 0 条，错误: 0 条
2025-06-01 12:02:18,973 - INFO - 开始处理日期: 2025-06
2025-06-01 12:02:18,973 - INFO - Request Parameters - Page 1:
2025-06-01 12:02:18,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 12:02:18,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 12:02:19,202 - INFO - Response - Page 1:
2025-06-01 12:02:19,402 - INFO - 查询完成，共获取到 0 条记录
2025-06-01 12:02:19,402 - INFO - 获取到 0 条表单数据
2025-06-01 12:02:19,404 - INFO - 当前日期 2025-06 有 2 条MySQL数据需要处理
2025-06-01 12:02:19,404 - INFO - 开始批量插入 2 条新记录
2025-06-01 12:02:19,637 - INFO - 批量插入响应状态码: 200
2025-06-01 12:02:19,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 04:02:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '110', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5CFCF17A-3721-71C6-ACE3-39151CEB7770', 'x-acs-trace-id': '457cd0d05481f4e98913dea3d763c244', 'etag': '1VKrfE4PU5MER6SNhd743hw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 12:02:19,637 - INFO - 批量插入响应体: {'result': ['FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21', 'FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMQ21']}
2025-06-01 12:02:19,637 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-01 12:02:19,638 - INFO - 成功插入的数据ID: ['FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21', 'FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMQ21']
2025-06-01 12:02:22,639 - INFO - 批量插入完成，共 2 条记录
2025-06-01 12:02:22,639 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-01 12:02:22,639 - INFO - 数据同步完成！更新: 229 条，插入: 2 条，错误: 0 条
2025-06-01 12:02:22,640 - INFO - =================同步完成====================
2025-06-01 15:00:02,558 - INFO - =================使用默认全量同步=============
2025-06-01 15:00:04,043 - INFO - MySQL查询成功，共获取 3309 条记录
2025-06-01 15:00:04,043 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-01 15:00:04,074 - INFO - 开始处理日期: 2025-01
2025-06-01 15:00:04,074 - INFO - Request Parameters - Page 1:
2025-06-01 15:00:04,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:04,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:04,996 - INFO - Response - Page 1:
2025-06-01 15:00:05,199 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:00:05,199 - INFO - Request Parameters - Page 2:
2025-06-01 15:00:05,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:05,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:06,152 - INFO - Response - Page 2:
2025-06-01 15:00:06,355 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:00:06,355 - INFO - Request Parameters - Page 3:
2025-06-01 15:00:06,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:06,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:06,839 - INFO - Response - Page 3:
2025-06-01 15:00:07,043 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:00:07,043 - INFO - Request Parameters - Page 4:
2025-06-01 15:00:07,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:07,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:07,574 - INFO - Response - Page 4:
2025-06-01 15:00:07,777 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:00:07,777 - INFO - Request Parameters - Page 5:
2025-06-01 15:00:07,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:07,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:08,355 - INFO - Response - Page 5:
2025-06-01 15:00:08,558 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:00:08,558 - INFO - Request Parameters - Page 6:
2025-06-01 15:00:08,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:08,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:09,089 - INFO - Response - Page 6:
2025-06-01 15:00:09,293 - INFO - 第 6 页获取到 100 条记录
2025-06-01 15:00:09,293 - INFO - Request Parameters - Page 7:
2025-06-01 15:00:09,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:09,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:09,793 - INFO - Response - Page 7:
2025-06-01 15:00:09,996 - INFO - 第 7 页获取到 82 条记录
2025-06-01 15:00:09,996 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 15:00:09,996 - INFO - 获取到 682 条表单数据
2025-06-01 15:00:09,996 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 15:00:10,011 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:00:10,011 - INFO - 开始处理日期: 2025-02
2025-06-01 15:00:10,011 - INFO - Request Parameters - Page 1:
2025-06-01 15:00:10,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:10,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:10,527 - INFO - Response - Page 1:
2025-06-01 15:00:10,730 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:00:10,730 - INFO - Request Parameters - Page 2:
2025-06-01 15:00:10,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:10,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:11,261 - INFO - Response - Page 2:
2025-06-01 15:00:11,464 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:00:11,464 - INFO - Request Parameters - Page 3:
2025-06-01 15:00:11,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:11,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:12,074 - INFO - Response - Page 3:
2025-06-01 15:00:12,277 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:00:12,277 - INFO - Request Parameters - Page 4:
2025-06-01 15:00:12,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:12,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:12,730 - INFO - Response - Page 4:
2025-06-01 15:00:12,933 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:00:12,933 - INFO - Request Parameters - Page 5:
2025-06-01 15:00:12,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:12,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:13,496 - INFO - Response - Page 5:
2025-06-01 15:00:13,699 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:00:13,699 - INFO - Request Parameters - Page 6:
2025-06-01 15:00:13,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:13,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:14,199 - INFO - Response - Page 6:
2025-06-01 15:00:14,402 - INFO - 第 6 页获取到 100 条记录
2025-06-01 15:00:14,402 - INFO - Request Parameters - Page 7:
2025-06-01 15:00:14,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:14,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:14,871 - INFO - Response - Page 7:
2025-06-01 15:00:15,074 - INFO - 第 7 页获取到 70 条记录
2025-06-01 15:00:15,074 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 15:00:15,074 - INFO - 获取到 670 条表单数据
2025-06-01 15:00:15,074 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 15:00:15,089 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:00:15,089 - INFO - 开始处理日期: 2025-03
2025-06-01 15:00:15,089 - INFO - Request Parameters - Page 1:
2025-06-01 15:00:15,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:15,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:15,621 - INFO - Response - Page 1:
2025-06-01 15:00:15,824 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:00:15,824 - INFO - Request Parameters - Page 2:
2025-06-01 15:00:15,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:15,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:16,277 - INFO - Response - Page 2:
2025-06-01 15:00:16,480 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:00:16,480 - INFO - Request Parameters - Page 3:
2025-06-01 15:00:16,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:16,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:17,058 - INFO - Response - Page 3:
2025-06-01 15:00:17,277 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:00:17,277 - INFO - Request Parameters - Page 4:
2025-06-01 15:00:17,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:17,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:17,761 - INFO - Response - Page 4:
2025-06-01 15:00:17,964 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:00:17,964 - INFO - Request Parameters - Page 5:
2025-06-01 15:00:17,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:17,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:18,589 - INFO - Response - Page 5:
2025-06-01 15:00:18,793 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:00:18,793 - INFO - Request Parameters - Page 6:
2025-06-01 15:00:18,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:18,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:19,261 - INFO - Response - Page 6:
2025-06-01 15:00:19,464 - INFO - 第 6 页获取到 100 条记录
2025-06-01 15:00:19,464 - INFO - Request Parameters - Page 7:
2025-06-01 15:00:19,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:19,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:19,918 - INFO - Response - Page 7:
2025-06-01 15:00:20,121 - INFO - 第 7 页获取到 61 条记录
2025-06-01 15:00:20,121 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 15:00:20,121 - INFO - 获取到 661 条表单数据
2025-06-01 15:00:20,136 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 15:00:20,136 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:00:20,136 - INFO - 开始处理日期: 2025-04
2025-06-01 15:00:20,136 - INFO - Request Parameters - Page 1:
2025-06-01 15:00:20,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:20,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:20,714 - INFO - Response - Page 1:
2025-06-01 15:00:20,918 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:00:20,918 - INFO - Request Parameters - Page 2:
2025-06-01 15:00:20,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:20,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:21,417 - INFO - Response - Page 2:
2025-06-01 15:00:21,621 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:00:21,621 - INFO - Request Parameters - Page 3:
2025-06-01 15:00:21,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:21,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:22,121 - INFO - Response - Page 3:
2025-06-01 15:00:22,324 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:00:22,324 - INFO - Request Parameters - Page 4:
2025-06-01 15:00:22,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:22,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:22,761 - INFO - Response - Page 4:
2025-06-01 15:00:22,964 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:00:22,964 - INFO - Request Parameters - Page 5:
2025-06-01 15:00:22,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:22,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:23,464 - INFO - Response - Page 5:
2025-06-01 15:00:23,667 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:00:23,667 - INFO - Request Parameters - Page 6:
2025-06-01 15:00:23,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:23,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:24,136 - INFO - Response - Page 6:
2025-06-01 15:00:24,355 - INFO - 第 6 页获取到 100 条记录
2025-06-01 15:00:24,355 - INFO - Request Parameters - Page 7:
2025-06-01 15:00:24,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:24,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:24,949 - INFO - Response - Page 7:
2025-06-01 15:00:25,152 - INFO - 第 7 页获取到 56 条记录
2025-06-01 15:00:25,152 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 15:00:25,152 - INFO - 获取到 656 条表单数据
2025-06-01 15:00:25,152 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 15:00:25,167 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 15:00:25,167 - INFO - 开始处理日期: 2025-05
2025-06-01 15:00:25,167 - INFO - Request Parameters - Page 1:
2025-06-01 15:00:25,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:25,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:25,636 - INFO - Response - Page 1:
2025-06-01 15:00:25,839 - INFO - 第 1 页获取到 100 条记录
2025-06-01 15:00:25,839 - INFO - Request Parameters - Page 2:
2025-06-01 15:00:25,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:25,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:26,308 - INFO - Response - Page 2:
2025-06-01 15:00:26,511 - INFO - 第 2 页获取到 100 条记录
2025-06-01 15:00:26,511 - INFO - Request Parameters - Page 3:
2025-06-01 15:00:26,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:26,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:26,964 - INFO - Response - Page 3:
2025-06-01 15:00:27,167 - INFO - 第 3 页获取到 100 条记录
2025-06-01 15:00:27,167 - INFO - Request Parameters - Page 4:
2025-06-01 15:00:27,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:27,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:27,761 - INFO - Response - Page 4:
2025-06-01 15:00:27,964 - INFO - 第 4 页获取到 100 条记录
2025-06-01 15:00:27,964 - INFO - Request Parameters - Page 5:
2025-06-01 15:00:27,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:27,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:28,542 - INFO - Response - Page 5:
2025-06-01 15:00:28,746 - INFO - 第 5 页获取到 100 条记录
2025-06-01 15:00:28,746 - INFO - Request Parameters - Page 6:
2025-06-01 15:00:28,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:28,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:29,371 - INFO - Response - Page 6:
2025-06-01 15:00:29,574 - INFO - 第 6 页获取到 100 条记录
2025-06-01 15:00:29,574 - INFO - Request Parameters - Page 7:
2025-06-01 15:00:29,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:29,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:29,949 - INFO - Response - Page 7:
2025-06-01 15:00:30,152 - INFO - 第 7 页获取到 36 条记录
2025-06-01 15:00:30,152 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 15:00:30,152 - INFO - 获取到 636 条表单数据
2025-06-01 15:00:30,152 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 15:00:30,152 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-06-01 15:00:30,605 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-06-01 15:00:30,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 506204.0, 'new_value': 516821.0}, {'field': 'offline_amount', 'old_value': 387659.0, 'new_value': 402292.0}, {'field': 'total_amount', 'old_value': 893863.0, 'new_value': 919113.0}, {'field': 'order_count', 'old_value': 972, 'new_value': 997}]
2025-06-01 15:00:30,605 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-06-01 15:00:31,074 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-06-01 15:00:31,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1513000.0, 'new_value': 1583000.0}, {'field': 'total_amount', 'old_value': 1513000.0, 'new_value': 1583000.0}, {'field': 'order_count', 'old_value': 349, 'new_value': 350}]
2025-06-01 15:00:31,074 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-06-01 15:00:31,527 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-06-01 15:00:31,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154315.0, 'new_value': 178085.0}, {'field': 'total_amount', 'old_value': 154315.0, 'new_value': 178085.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-01 15:00:31,527 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-06-01 15:00:32,042 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-06-01 15:00:32,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81046.0, 'new_value': 88936.0}, {'field': 'total_amount', 'old_value': 81046.0, 'new_value': 88936.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 92}]
2025-06-01 15:00:32,042 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-06-01 15:00:32,542 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-06-01 15:00:32,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14359.0, 'new_value': 16159.0}, {'field': 'total_amount', 'old_value': 14359.0, 'new_value': 16159.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-01 15:00:32,542 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-06-01 15:00:33,152 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-06-01 15:00:33,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7923000.0, 'new_value': 10428900.0}, {'field': 'total_amount', 'old_value': 7923000.0, 'new_value': 10428900.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 74}]
2025-06-01 15:00:33,152 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-06-01 15:00:33,683 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-06-01 15:00:33,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 498831.23, 'new_value': 541558.94}, {'field': 'total_amount', 'old_value': 498831.23, 'new_value': 541558.94}, {'field': 'order_count', 'old_value': 915, 'new_value': 951}]
2025-06-01 15:00:33,683 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-06-01 15:00:34,121 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-06-01 15:00:34,121 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32853.0, 'new_value': 32873.0}, {'field': 'total_amount', 'old_value': 32875.5, 'new_value': 32895.5}]
2025-06-01 15:00:34,121 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-06-01 15:00:34,574 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-06-01 15:00:34,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1106735.0, 'new_value': 1132631.0}, {'field': 'total_amount', 'old_value': 1106735.0, 'new_value': 1132631.0}, {'field': 'order_count', 'old_value': 2347, 'new_value': 2412}]
2025-06-01 15:00:34,574 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-06-01 15:00:35,058 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-06-01 15:00:35,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110368.0, 'new_value': 130112.0}, {'field': 'total_amount', 'old_value': 125572.0, 'new_value': 145316.0}, {'field': 'order_count', 'old_value': 2915, 'new_value': 3384}]
2025-06-01 15:00:35,058 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-06-01 15:00:35,511 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-06-01 15:00:35,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127605.61, 'new_value': 129931.31}, {'field': 'offline_amount', 'old_value': 138035.96, 'new_value': 141423.35}, {'field': 'total_amount', 'old_value': 265641.57, 'new_value': 271354.66}, {'field': 'order_count', 'old_value': 6743, 'new_value': 6880}]
2025-06-01 15:00:35,511 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-06-01 15:00:36,042 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-06-01 15:00:36,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108505.62, 'new_value': 111194.93}, {'field': 'offline_amount', 'old_value': 1094453.88, 'new_value': 1133524.84}, {'field': 'total_amount', 'old_value': 1201085.17, 'new_value': 1242845.44}, {'field': 'order_count', 'old_value': 5652, 'new_value': 5808}]
2025-06-01 15:00:36,042 - INFO - 开始更新记录 - 表单实例ID: FINST-I6E66WA1XITVHDFID4U3U5UO4PEC3YIDUVBBM4G
2025-06-01 15:00:36,496 - INFO - 更新表单数据成功: FINST-I6E66WA1XITVHDFID4U3U5UO4PEC3YIDUVBBM4G
2025-06-01 15:00:36,496 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3286.0, 'new_value': 9696.0}, {'field': 'offline_amount', 'old_value': 3603.0, 'new_value': 14039.0}, {'field': 'total_amount', 'old_value': 6889.0, 'new_value': 23735.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 50}]
2025-06-01 15:00:36,496 - INFO - 日期 2025-05 处理完成 - 更新: 13 条，插入: 0 条，错误: 0 条
2025-06-01 15:00:36,496 - INFO - 开始处理日期: 2025-06
2025-06-01 15:00:36,496 - INFO - Request Parameters - Page 1:
2025-06-01 15:00:36,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 15:00:36,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 15:00:36,730 - INFO - Response - Page 1:
2025-06-01 15:00:36,933 - INFO - 第 1 页获取到 2 条记录
2025-06-01 15:00:36,933 - INFO - 查询完成，共获取到 2 条记录
2025-06-01 15:00:36,933 - INFO - 获取到 2 条表单数据
2025-06-01 15:00:36,933 - INFO - 当前日期 2025-06 有 4 条MySQL数据需要处理
2025-06-01 15:00:36,933 - INFO - 开始批量插入 2 条新记录
2025-06-01 15:00:37,074 - INFO - 批量插入响应状态码: 200
2025-06-01 15:00:37,074 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 07:00:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-6035-7262-8E8D-54DCEB38E068', 'x-acs-trace-id': '0941b4a7c9c32708d8b82a15bde827ff', 'etag': '1cV1DTqqL8EE2FPcE8LxulQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 15:00:37,074 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM7I', 'FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM8I']}
2025-06-01 15:00:37,074 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-01 15:00:37,074 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM7I', 'FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM8I']
2025-06-01 15:00:40,089 - INFO - 批量插入完成，共 2 条记录
2025-06-01 15:00:40,089 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-01 15:00:40,089 - INFO - 数据同步完成！更新: 13 条，插入: 2 条，错误: 0 条
2025-06-01 15:00:40,089 - INFO - =================同步完成====================
2025-06-01 18:00:02,136 - INFO - =================使用默认全量同步=============
2025-06-01 18:00:03,667 - INFO - MySQL查询成功，共获取 3309 条记录
2025-06-01 18:00:03,667 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-01 18:00:03,699 - INFO - 开始处理日期: 2025-01
2025-06-01 18:00:03,699 - INFO - Request Parameters - Page 1:
2025-06-01 18:00:03,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:03,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:04,933 - INFO - Response - Page 1:
2025-06-01 18:00:05,136 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:00:05,136 - INFO - Request Parameters - Page 2:
2025-06-01 18:00:05,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:05,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:05,761 - INFO - Response - Page 2:
2025-06-01 18:00:05,964 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:00:05,964 - INFO - Request Parameters - Page 3:
2025-06-01 18:00:05,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:05,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:06,527 - INFO - Response - Page 3:
2025-06-01 18:00:06,730 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:00:06,730 - INFO - Request Parameters - Page 4:
2025-06-01 18:00:06,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:06,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:07,261 - INFO - Response - Page 4:
2025-06-01 18:00:07,464 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:00:07,464 - INFO - Request Parameters - Page 5:
2025-06-01 18:00:07,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:07,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:08,027 - INFO - Response - Page 5:
2025-06-01 18:00:08,230 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:00:08,230 - INFO - Request Parameters - Page 6:
2025-06-01 18:00:08,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:08,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:08,761 - INFO - Response - Page 6:
2025-06-01 18:00:08,964 - INFO - 第 6 页获取到 100 条记录
2025-06-01 18:00:08,964 - INFO - Request Parameters - Page 7:
2025-06-01 18:00:08,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:08,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:09,542 - INFO - Response - Page 7:
2025-06-01 18:00:09,745 - INFO - 第 7 页获取到 82 条记录
2025-06-01 18:00:09,745 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 18:00:09,745 - INFO - 获取到 682 条表单数据
2025-06-01 18:00:09,745 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 18:00:09,761 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:09,761 - INFO - 开始处理日期: 2025-02
2025-06-01 18:00:09,761 - INFO - Request Parameters - Page 1:
2025-06-01 18:00:09,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:09,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:10,542 - INFO - Response - Page 1:
2025-06-01 18:00:10,745 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:00:10,745 - INFO - Request Parameters - Page 2:
2025-06-01 18:00:10,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:10,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:11,230 - INFO - Response - Page 2:
2025-06-01 18:00:11,433 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:00:11,433 - INFO - Request Parameters - Page 3:
2025-06-01 18:00:11,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:11,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:12,073 - INFO - Response - Page 3:
2025-06-01 18:00:12,277 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:00:12,277 - INFO - Request Parameters - Page 4:
2025-06-01 18:00:12,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:12,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:12,839 - INFO - Response - Page 4:
2025-06-01 18:00:13,042 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:00:13,042 - INFO - Request Parameters - Page 5:
2025-06-01 18:00:13,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:13,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:13,558 - INFO - Response - Page 5:
2025-06-01 18:00:13,761 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:00:13,761 - INFO - Request Parameters - Page 6:
2025-06-01 18:00:13,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:13,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:14,402 - INFO - Response - Page 6:
2025-06-01 18:00:14,605 - INFO - 第 6 页获取到 100 条记录
2025-06-01 18:00:14,605 - INFO - Request Parameters - Page 7:
2025-06-01 18:00:14,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:14,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:15,089 - INFO - Response - Page 7:
2025-06-01 18:00:15,292 - INFO - 第 7 页获取到 70 条记录
2025-06-01 18:00:15,292 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 18:00:15,292 - INFO - 获取到 670 条表单数据
2025-06-01 18:00:15,292 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 18:00:15,308 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:15,308 - INFO - 开始处理日期: 2025-03
2025-06-01 18:00:15,308 - INFO - Request Parameters - Page 1:
2025-06-01 18:00:15,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:15,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:15,902 - INFO - Response - Page 1:
2025-06-01 18:00:16,105 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:00:16,105 - INFO - Request Parameters - Page 2:
2025-06-01 18:00:16,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:16,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:16,714 - INFO - Response - Page 2:
2025-06-01 18:00:16,917 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:00:16,917 - INFO - Request Parameters - Page 3:
2025-06-01 18:00:16,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:16,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:17,417 - INFO - Response - Page 3:
2025-06-01 18:00:17,620 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:00:17,620 - INFO - Request Parameters - Page 4:
2025-06-01 18:00:17,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:17,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:18,136 - INFO - Response - Page 4:
2025-06-01 18:00:18,339 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:00:18,339 - INFO - Request Parameters - Page 5:
2025-06-01 18:00:18,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:18,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:18,902 - INFO - Response - Page 5:
2025-06-01 18:00:19,105 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:00:19,105 - INFO - Request Parameters - Page 6:
2025-06-01 18:00:19,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:19,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:19,636 - INFO - Response - Page 6:
2025-06-01 18:00:19,839 - INFO - 第 6 页获取到 100 条记录
2025-06-01 18:00:19,839 - INFO - Request Parameters - Page 7:
2025-06-01 18:00:19,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:19,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:20,261 - INFO - Response - Page 7:
2025-06-01 18:00:20,464 - INFO - 第 7 页获取到 61 条记录
2025-06-01 18:00:20,464 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 18:00:20,464 - INFO - 获取到 661 条表单数据
2025-06-01 18:00:20,464 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 18:00:20,480 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:20,480 - INFO - 开始处理日期: 2025-04
2025-06-01 18:00:20,480 - INFO - Request Parameters - Page 1:
2025-06-01 18:00:20,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:20,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:21,323 - INFO - Response - Page 1:
2025-06-01 18:00:21,527 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:00:21,527 - INFO - Request Parameters - Page 2:
2025-06-01 18:00:21,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:21,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:22,027 - INFO - Response - Page 2:
2025-06-01 18:00:22,230 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:00:22,230 - INFO - Request Parameters - Page 3:
2025-06-01 18:00:22,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:22,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:22,777 - INFO - Response - Page 3:
2025-06-01 18:00:22,980 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:00:22,980 - INFO - Request Parameters - Page 4:
2025-06-01 18:00:22,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:22,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:23,480 - INFO - Response - Page 4:
2025-06-01 18:00:23,683 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:00:23,683 - INFO - Request Parameters - Page 5:
2025-06-01 18:00:23,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:23,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:24,198 - INFO - Response - Page 5:
2025-06-01 18:00:24,402 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:00:24,402 - INFO - Request Parameters - Page 6:
2025-06-01 18:00:24,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:24,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:24,917 - INFO - Response - Page 6:
2025-06-01 18:00:25,120 - INFO - 第 6 页获取到 100 条记录
2025-06-01 18:00:25,120 - INFO - Request Parameters - Page 7:
2025-06-01 18:00:25,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:25,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:25,558 - INFO - Response - Page 7:
2025-06-01 18:00:25,776 - INFO - 第 7 页获取到 56 条记录
2025-06-01 18:00:25,776 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 18:00:25,776 - INFO - 获取到 656 条表单数据
2025-06-01 18:00:25,776 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 18:00:25,792 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:25,792 - INFO - 开始处理日期: 2025-05
2025-06-01 18:00:25,792 - INFO - Request Parameters - Page 1:
2025-06-01 18:00:25,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:25,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:26,277 - INFO - Response - Page 1:
2025-06-01 18:00:26,480 - INFO - 第 1 页获取到 100 条记录
2025-06-01 18:00:26,480 - INFO - Request Parameters - Page 2:
2025-06-01 18:00:26,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:26,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:26,980 - INFO - Response - Page 2:
2025-06-01 18:00:27,183 - INFO - 第 2 页获取到 100 条记录
2025-06-01 18:00:27,183 - INFO - Request Parameters - Page 3:
2025-06-01 18:00:27,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:27,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:27,651 - INFO - Response - Page 3:
2025-06-01 18:00:27,855 - INFO - 第 3 页获取到 100 条记录
2025-06-01 18:00:27,855 - INFO - Request Parameters - Page 4:
2025-06-01 18:00:27,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:27,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:28,370 - INFO - Response - Page 4:
2025-06-01 18:00:28,573 - INFO - 第 4 页获取到 100 条记录
2025-06-01 18:00:28,573 - INFO - Request Parameters - Page 5:
2025-06-01 18:00:28,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:28,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:29,042 - INFO - Response - Page 5:
2025-06-01 18:00:29,245 - INFO - 第 5 页获取到 100 条记录
2025-06-01 18:00:29,245 - INFO - Request Parameters - Page 6:
2025-06-01 18:00:29,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:29,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:29,745 - INFO - Response - Page 6:
2025-06-01 18:00:29,948 - INFO - 第 6 页获取到 100 条记录
2025-06-01 18:00:29,948 - INFO - Request Parameters - Page 7:
2025-06-01 18:00:29,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:29,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:30,339 - INFO - Response - Page 7:
2025-06-01 18:00:30,542 - INFO - 第 7 页获取到 36 条记录
2025-06-01 18:00:30,542 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 18:00:30,542 - INFO - 获取到 636 条表单数据
2025-06-01 18:00:30,542 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 18:00:30,542 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-06-01 18:00:31,011 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-06-01 18:00:31,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103896.13, 'new_value': 108414.13}, {'field': 'total_amount', 'old_value': 103896.13, 'new_value': 108414.13}, {'field': 'order_count', 'old_value': 4055, 'new_value': 4213}]
2025-06-01 18:00:31,011 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-06-01 18:00:31,495 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-06-01 18:00:31,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137568.63, 'new_value': 145483.63}, {'field': 'total_amount', 'old_value': 150083.94, 'new_value': 157998.94}, {'field': 'order_count', 'old_value': 2548, 'new_value': 2653}]
2025-06-01 18:00:31,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-06-01 18:00:32,105 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-06-01 18:00:32,105 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42825.27, 'new_value': 42830.55}, {'field': 'total_amount', 'old_value': 76393.49, 'new_value': 76398.77}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-06-01 18:00:32,105 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-06-01 18:00:32,667 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-06-01 18:00:32,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 919552.94, 'new_value': 969552.94}, {'field': 'total_amount', 'old_value': 919552.94, 'new_value': 969552.94}, {'field': 'order_count', 'old_value': 554, 'new_value': 555}]
2025-06-01 18:00:32,667 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-06-01 18:00:33,136 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-06-01 18:00:33,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35809.0, 'new_value': 37609.0}, {'field': 'total_amount', 'old_value': 35809.0, 'new_value': 37609.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-01 18:00:33,136 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-06-01 18:00:33,573 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-06-01 18:00:33,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51628.0, 'new_value': 56128.0}, {'field': 'total_amount', 'old_value': 51628.0, 'new_value': 56128.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-06-01 18:00:33,573 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-06-01 18:00:34,058 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-06-01 18:00:34,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168304.54, 'new_value': 177459.54}, {'field': 'total_amount', 'old_value': 168304.54, 'new_value': 177459.54}, {'field': 'order_count', 'old_value': 14970, 'new_value': 15588}]
2025-06-01 18:00:34,073 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-06-01 18:00:34,558 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-06-01 18:00:34,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338486.0, 'new_value': 351340.0}, {'field': 'total_amount', 'old_value': 377207.0, 'new_value': 390061.0}, {'field': 'order_count', 'old_value': 348, 'new_value': 351}]
2025-06-01 18:00:34,558 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-06-01 18:00:35,026 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-06-01 18:00:35,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102814.0, 'new_value': 112472.0}, {'field': 'total_amount', 'old_value': 102814.0, 'new_value': 112472.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-06-01 18:00:35,026 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-06-01 18:00:35,480 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-06-01 18:00:35,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 422283.34, 'new_value': 446654.62}, {'field': 'total_amount', 'old_value': 422283.34, 'new_value': 446654.62}, {'field': 'order_count', 'old_value': 3083, 'new_value': 3246}]
2025-06-01 18:00:35,480 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-06-01 18:00:36,026 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-06-01 18:00:36,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31860.0, 'new_value': 36860.0}, {'field': 'total_amount', 'old_value': 31860.0, 'new_value': 36860.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 39}]
2025-06-01 18:00:36,026 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-06-01 18:00:36,511 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-06-01 18:00:36,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354285.46, 'new_value': 372285.46}, {'field': 'total_amount', 'old_value': 355756.46, 'new_value': 373756.46}, {'field': 'order_count', 'old_value': 60, 'new_value': 70}]
2025-06-01 18:00:36,511 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-06-01 18:00:36,886 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-06-01 18:00:36,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1572900.0, 'new_value': 1625900.0}, {'field': 'total_amount', 'old_value': 1572900.0, 'new_value': 1625900.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 80}]
2025-06-01 18:00:36,886 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-06-01 18:00:37,386 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-06-01 18:00:37,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14868.67, 'new_value': 15653.67}, {'field': 'total_amount', 'old_value': 14868.67, 'new_value': 15653.67}, {'field': 'order_count', 'old_value': 416, 'new_value': 441}]
2025-06-01 18:00:37,386 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-06-01 18:00:37,870 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-06-01 18:00:37,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94394.0, 'new_value': 125860.0}, {'field': 'total_amount', 'old_value': 94394.0, 'new_value': 125860.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 33}]
2025-06-01 18:00:37,870 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-06-01 18:00:38,308 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-06-01 18:00:38,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91000.0, 'new_value': 98800.0}, {'field': 'total_amount', 'old_value': 91000.0, 'new_value': 98800.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-01 18:00:38,323 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-06-01 18:00:38,808 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-06-01 18:00:38,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182153.82, 'new_value': 191811.82}, {'field': 'total_amount', 'old_value': 182153.82, 'new_value': 191811.82}, {'field': 'order_count', 'old_value': 1528, 'new_value': 1584}]
2025-06-01 18:00:38,808 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-06-01 18:00:39,355 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-06-01 18:00:39,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110874.0, 'new_value': 121708.0}, {'field': 'total_amount', 'old_value': 110876.0, 'new_value': 121710.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-06-01 18:00:39,355 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-06-01 18:00:39,792 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-06-01 18:00:39,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13620.0, 'new_value': 14740.0}, {'field': 'total_amount', 'old_value': 13620.0, 'new_value': 14740.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-01 18:00:39,792 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-06-01 18:00:40,308 - INFO - 更新表单数据成功: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-06-01 18:00:40,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229010.0, 'new_value': 231654.0}, {'field': 'total_amount', 'old_value': 229010.0, 'new_value': 231654.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 195}]
2025-06-01 18:00:40,308 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-06-01 18:00:40,886 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-06-01 18:00:40,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25784.46, 'new_value': 27640.21}, {'field': 'total_amount', 'old_value': 25784.46, 'new_value': 27640.21}, {'field': 'order_count', 'old_value': 1073, 'new_value': 1148}]
2025-06-01 18:00:40,886 - INFO - 日期 2025-05 处理完成 - 更新: 21 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:40,886 - INFO - 开始处理日期: 2025-06
2025-06-01 18:00:40,886 - INFO - Request Parameters - Page 1:
2025-06-01 18:00:40,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 18:00:40,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 18:00:41,136 - INFO - Response - Page 1:
2025-06-01 18:00:41,339 - INFO - 第 1 页获取到 4 条记录
2025-06-01 18:00:41,339 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 18:00:41,339 - INFO - 获取到 4 条表单数据
2025-06-01 18:00:41,339 - INFO - 当前日期 2025-06 有 4 条MySQL数据需要处理
2025-06-01 18:00:41,339 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:41,339 - INFO - 数据同步完成！更新: 21 条，插入: 0 条，错误: 0 条
2025-06-01 18:00:41,339 - INFO - =================同步完成====================
2025-06-01 21:00:02,640 - INFO - =================使用默认全量同步=============
2025-06-01 21:00:04,140 - INFO - MySQL查询成功，共获取 3310 条记录
2025-06-01 21:00:04,140 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-01 21:00:04,171 - INFO - 开始处理日期: 2025-01
2025-06-01 21:00:04,171 - INFO - Request Parameters - Page 1:
2025-06-01 21:00:04,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:04,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:05,155 - INFO - Response - Page 1:
2025-06-01 21:00:05,358 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:00:05,358 - INFO - Request Parameters - Page 2:
2025-06-01 21:00:05,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:05,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:05,921 - INFO - Response - Page 2:
2025-06-01 21:00:06,124 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:00:06,124 - INFO - Request Parameters - Page 3:
2025-06-01 21:00:06,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:06,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:06,765 - INFO - Response - Page 3:
2025-06-01 21:00:06,968 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:00:06,968 - INFO - Request Parameters - Page 4:
2025-06-01 21:00:06,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:06,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:07,452 - INFO - Response - Page 4:
2025-06-01 21:00:07,655 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:00:07,655 - INFO - Request Parameters - Page 5:
2025-06-01 21:00:07,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:07,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:08,108 - INFO - Response - Page 5:
2025-06-01 21:00:08,311 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:00:08,311 - INFO - Request Parameters - Page 6:
2025-06-01 21:00:08,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:08,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:08,765 - INFO - Response - Page 6:
2025-06-01 21:00:08,968 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:00:08,968 - INFO - Request Parameters - Page 7:
2025-06-01 21:00:08,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:08,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:09,530 - INFO - Response - Page 7:
2025-06-01 21:00:09,733 - INFO - 第 7 页获取到 82 条记录
2025-06-01 21:00:09,733 - INFO - 查询完成，共获取到 682 条记录
2025-06-01 21:00:09,733 - INFO - 获取到 682 条表单数据
2025-06-01 21:00:09,733 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-01 21:00:09,749 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:00:09,749 - INFO - 开始处理日期: 2025-02
2025-06-01 21:00:09,749 - INFO - Request Parameters - Page 1:
2025-06-01 21:00:09,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:09,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:10,452 - INFO - Response - Page 1:
2025-06-01 21:00:10,655 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:00:10,655 - INFO - Request Parameters - Page 2:
2025-06-01 21:00:10,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:10,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:11,108 - INFO - Response - Page 2:
2025-06-01 21:00:11,311 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:00:11,311 - INFO - Request Parameters - Page 3:
2025-06-01 21:00:11,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:11,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:11,890 - INFO - Response - Page 3:
2025-06-01 21:00:12,093 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:00:12,093 - INFO - Request Parameters - Page 4:
2025-06-01 21:00:12,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:12,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:12,671 - INFO - Response - Page 4:
2025-06-01 21:00:12,874 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:00:12,874 - INFO - Request Parameters - Page 5:
2025-06-01 21:00:12,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:12,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:13,374 - INFO - Response - Page 5:
2025-06-01 21:00:13,577 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:00:13,577 - INFO - Request Parameters - Page 6:
2025-06-01 21:00:13,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:13,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:14,108 - INFO - Response - Page 6:
2025-06-01 21:00:14,311 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:00:14,311 - INFO - Request Parameters - Page 7:
2025-06-01 21:00:14,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:14,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:14,827 - INFO - Response - Page 7:
2025-06-01 21:00:15,030 - INFO - 第 7 页获取到 70 条记录
2025-06-01 21:00:15,030 - INFO - 查询完成，共获取到 670 条记录
2025-06-01 21:00:15,030 - INFO - 获取到 670 条表单数据
2025-06-01 21:00:15,030 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-01 21:00:15,046 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:00:15,046 - INFO - 开始处理日期: 2025-03
2025-06-01 21:00:15,046 - INFO - Request Parameters - Page 1:
2025-06-01 21:00:15,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:15,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:15,499 - INFO - Response - Page 1:
2025-06-01 21:00:15,702 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:00:15,702 - INFO - Request Parameters - Page 2:
2025-06-01 21:00:15,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:15,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:16,171 - INFO - Response - Page 2:
2025-06-01 21:00:16,374 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:00:16,374 - INFO - Request Parameters - Page 3:
2025-06-01 21:00:16,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:16,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:16,936 - INFO - Response - Page 3:
2025-06-01 21:00:17,139 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:00:17,139 - INFO - Request Parameters - Page 4:
2025-06-01 21:00:17,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:17,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:17,624 - INFO - Response - Page 4:
2025-06-01 21:00:17,827 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:00:17,827 - INFO - Request Parameters - Page 5:
2025-06-01 21:00:17,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:17,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:18,436 - INFO - Response - Page 5:
2025-06-01 21:00:18,639 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:00:18,639 - INFO - Request Parameters - Page 6:
2025-06-01 21:00:18,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:18,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:19,218 - INFO - Response - Page 6:
2025-06-01 21:00:19,421 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:00:19,421 - INFO - Request Parameters - Page 7:
2025-06-01 21:00:19,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:19,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:19,827 - INFO - Response - Page 7:
2025-06-01 21:00:20,030 - INFO - 第 7 页获取到 61 条记录
2025-06-01 21:00:20,030 - INFO - 查询完成，共获取到 661 条记录
2025-06-01 21:00:20,030 - INFO - 获取到 661 条表单数据
2025-06-01 21:00:20,030 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-01 21:00:20,046 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:00:20,046 - INFO - 开始处理日期: 2025-04
2025-06-01 21:00:20,046 - INFO - Request Parameters - Page 1:
2025-06-01 21:00:20,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:20,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:20,639 - INFO - Response - Page 1:
2025-06-01 21:00:20,843 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:00:20,843 - INFO - Request Parameters - Page 2:
2025-06-01 21:00:20,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:20,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:21,358 - INFO - Response - Page 2:
2025-06-01 21:00:21,561 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:00:21,561 - INFO - Request Parameters - Page 3:
2025-06-01 21:00:21,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:21,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:22,061 - INFO - Response - Page 3:
2025-06-01 21:00:22,264 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:00:22,264 - INFO - Request Parameters - Page 4:
2025-06-01 21:00:22,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:22,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:22,718 - INFO - Response - Page 4:
2025-06-01 21:00:22,921 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:00:22,921 - INFO - Request Parameters - Page 5:
2025-06-01 21:00:22,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:22,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:23,421 - INFO - Response - Page 5:
2025-06-01 21:00:23,624 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:00:23,624 - INFO - Request Parameters - Page 6:
2025-06-01 21:00:23,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:23,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:24,233 - INFO - Response - Page 6:
2025-06-01 21:00:24,436 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:00:24,436 - INFO - Request Parameters - Page 7:
2025-06-01 21:00:24,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:24,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:24,874 - INFO - Response - Page 7:
2025-06-01 21:00:25,077 - INFO - 第 7 页获取到 56 条记录
2025-06-01 21:00:25,077 - INFO - 查询完成，共获取到 656 条记录
2025-06-01 21:00:25,077 - INFO - 获取到 656 条表单数据
2025-06-01 21:00:25,077 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-01 21:00:25,093 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-01 21:00:25,093 - INFO - 开始处理日期: 2025-05
2025-06-01 21:00:25,093 - INFO - Request Parameters - Page 1:
2025-06-01 21:00:25,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:25,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:25,702 - INFO - Response - Page 1:
2025-06-01 21:00:25,905 - INFO - 第 1 页获取到 100 条记录
2025-06-01 21:00:25,905 - INFO - Request Parameters - Page 2:
2025-06-01 21:00:25,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:25,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:26,499 - INFO - Response - Page 2:
2025-06-01 21:00:26,702 - INFO - 第 2 页获取到 100 条记录
2025-06-01 21:00:26,702 - INFO - Request Parameters - Page 3:
2025-06-01 21:00:26,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:26,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:27,343 - INFO - Response - Page 3:
2025-06-01 21:00:27,546 - INFO - 第 3 页获取到 100 条记录
2025-06-01 21:00:27,546 - INFO - Request Parameters - Page 4:
2025-06-01 21:00:27,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:27,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:28,061 - INFO - Response - Page 4:
2025-06-01 21:00:28,264 - INFO - 第 4 页获取到 100 条记录
2025-06-01 21:00:28,264 - INFO - Request Parameters - Page 5:
2025-06-01 21:00:28,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:28,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:28,702 - INFO - Response - Page 5:
2025-06-01 21:00:28,905 - INFO - 第 5 页获取到 100 条记录
2025-06-01 21:00:28,905 - INFO - Request Parameters - Page 6:
2025-06-01 21:00:28,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:28,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:29,389 - INFO - Response - Page 6:
2025-06-01 21:00:29,593 - INFO - 第 6 页获取到 100 条记录
2025-06-01 21:00:29,593 - INFO - Request Parameters - Page 7:
2025-06-01 21:00:29,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:29,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:29,983 - INFO - Response - Page 7:
2025-06-01 21:00:30,186 - INFO - 第 7 页获取到 36 条记录
2025-06-01 21:00:30,186 - INFO - 查询完成，共获取到 636 条记录
2025-06-01 21:00:30,186 - INFO - 获取到 636 条表单数据
2025-06-01 21:00:30,186 - INFO - 当前日期 2025-05 有 636 条MySQL数据需要处理
2025-06-01 21:00:30,202 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-06-01 21:00:30,639 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-06-01 21:00:30,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15588.0, 'new_value': 43040.0}, {'field': 'offline_amount', 'old_value': 33204.0, 'new_value': 21740.0}, {'field': 'total_amount', 'old_value': 48792.0, 'new_value': 64780.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-06-01 21:00:30,639 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-01 21:00:30,639 - INFO - 开始处理日期: 2025-06
2025-06-01 21:00:30,639 - INFO - Request Parameters - Page 1:
2025-06-01 21:00:30,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-01 21:00:30,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-01 21:00:30,874 - INFO - Response - Page 1:
2025-06-01 21:00:31,077 - INFO - 第 1 页获取到 4 条记录
2025-06-01 21:00:31,077 - INFO - 查询完成，共获取到 4 条记录
2025-06-01 21:00:31,077 - INFO - 获取到 4 条表单数据
2025-06-01 21:00:31,077 - INFO - 当前日期 2025-06 有 5 条MySQL数据需要处理
2025-06-01 21:00:31,077 - INFO - 开始批量插入 1 条新记录
2025-06-01 21:00:31,218 - INFO - 批量插入响应状态码: 200
2025-06-01 21:00:31,218 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 13:00:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EC6C594F-5341-703C-940A-0C7DF6AAA244', 'x-acs-trace-id': 'd81ea19a5cc69eb57c0df7c1d09be61a', 'etag': '6zdh9ja6+Vd5UzjW2Lv7GHQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-01 21:00:31,218 - INFO - 批量插入响应体: {'result': ['FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06']}
2025-06-01 21:00:31,218 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-01 21:00:31,218 - INFO - 成功插入的数据ID: ['FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06']
2025-06-01 21:00:34,233 - INFO - 批量插入完成，共 1 条记录
2025-06-01 21:00:34,233 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-01 21:00:34,233 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 0 条
2025-06-01 21:00:34,233 - INFO - =================同步完成====================
