2025-06-22 01:30:33,579 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 01:30:33,579 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 01:30:33,579 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 01:30:33,657 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 0 条记录
2025-06-22 01:30:33,657 - ERROR - 未获取到MySQL数据
2025-06-22 01:31:33,672 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 01:31:33,672 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 01:31:33,672 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 01:31:33,797 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 52 条记录
2025-06-22 01:31:33,797 - INFO - 获取到 1 个日期需要处理: ['2025-06-21']
2025-06-22 01:31:33,797 - INFO - 开始处理日期: 2025-06-21
2025-06-22 01:31:33,797 - INFO - Request Parameters - Page 1:
2025-06-22 01:31:33,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 01:31:33,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 01:31:39,860 - INFO - Response - Page 1:
2025-06-22 01:31:39,860 - INFO - 第 1 页获取到 45 条记录
2025-06-22 01:31:40,375 - INFO - 查询完成，共获取到 45 条记录
2025-06-22 01:31:40,375 - INFO - 获取到 45 条表单数据
2025-06-22 01:31:40,375 - INFO - 当前日期 2025-06-21 有 46 条MySQL数据需要处理
2025-06-22 01:31:40,375 - INFO - 开始批量插入 1 条新记录
2025-06-22 01:31:40,531 - INFO - 批量插入响应状态码: 200
2025-06-22 01:31:40,531 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 17:31:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7B1641DC-CD1C-74BA-A8DE-5E22C6181BA5', 'x-acs-trace-id': '346439f898a298beed4a7f36a7a27910', 'etag': '6dK6q/UFrMtDaK5yC6zYFjg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 01:31:40,531 - INFO - 批量插入响应体: {'result': ['FINST-NWE664C1HZHW4W2GEVF3N555ASVW3HDZMI6CMM8']}
2025-06-22 01:31:40,531 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-22 01:31:40,531 - INFO - 成功插入的数据ID: ['FINST-NWE664C1HZHW4W2GEVF3N555ASVW3HDZMI6CMM8']
2025-06-22 01:31:45,547 - INFO - 批量插入完成，共 1 条记录
2025-06-22 01:31:45,547 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 01:31:45,547 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 01:31:45,547 - INFO - 同步完成
2025-06-22 04:30:33,943 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 04:30:33,943 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 04:30:33,943 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 04:30:34,084 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 5 条记录
2025-06-22 04:30:34,084 - INFO - 获取到 1 个日期需要处理: ['2025-06-21']
2025-06-22 04:30:34,084 - INFO - 开始处理日期: 2025-06-21
2025-06-22 04:30:34,084 - INFO - Request Parameters - Page 1:
2025-06-22 04:30:34,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 04:30:34,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 04:30:42,193 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A15EFF7D-A1D4-718C-A589-AC63CDC06611 Response: {'code': 'ServiceUnavailable', 'requestid': 'A15EFF7D-A1D4-718C-A589-AC63CDC06611', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A15EFF7D-A1D4-718C-A589-AC63CDC06611)
2025-06-22 04:30:42,193 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-22 04:31:42,208 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 04:31:42,208 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 04:31:42,208 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 04:31:42,333 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 136 条记录
2025-06-22 04:31:42,333 - INFO - 获取到 1 个日期需要处理: ['2025-06-21']
2025-06-22 04:31:42,333 - INFO - 开始处理日期: 2025-06-21
2025-06-22 04:31:42,333 - INFO - Request Parameters - Page 1:
2025-06-22 04:31:42,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 04:31:42,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 04:31:42,989 - INFO - Response - Page 1:
2025-06-22 04:31:42,989 - INFO - 第 1 页获取到 46 条记录
2025-06-22 04:31:43,505 - INFO - 查询完成，共获取到 46 条记录
2025-06-22 04:31:43,505 - INFO - 获取到 46 条表单数据
2025-06-22 04:31:43,505 - INFO - 当前日期 2025-06-21 有 128 条MySQL数据需要处理
2025-06-22 04:31:43,505 - INFO - 开始批量插入 82 条新记录
2025-06-22 04:31:43,771 - INFO - 批量插入响应状态码: 200
2025-06-22 04:31:43,771 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 20:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-5AC6-7C8A-9914-AE1B178CC1A2', 'x-acs-trace-id': '4858b1ba05f9defad6be77e566309315', 'etag': '26eCDNde+2OIfBnGULSEqrQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 04:31:43,771 - INFO - 批量插入响应体: {'result': ['FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM83', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM93', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMA3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMB3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMC3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMD3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CME3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMF3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMG3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMH3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMI3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMJ3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMK3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CML3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMM3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMN3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMO3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMP3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMQ3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMR3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMS3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMT3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMU3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMV3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMW3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMX3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMY3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMZ3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM04', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM14', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM24', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM34', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM44', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM54', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM64', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM74', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM84', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM94', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMA4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMB4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMC4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMD4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CME4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMF4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMG4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMH4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMI4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMJ4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMK4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CML4']}
2025-06-22 04:31:43,771 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-22 04:31:43,771 - INFO - 成功插入的数据ID: ['FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM83', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM93', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMA3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMB3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMC3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMD3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CME3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMF3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMG3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMH3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMI3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMJ3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMK3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CML3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMM3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMN3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMO3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMP3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMQ3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMR3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMS3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMT3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMU3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMV3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMW3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMX3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMY3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMZ3', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM04', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM14', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM24', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM34', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM44', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM54', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM64', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM74', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM84', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CM94', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMA4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMB4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMC4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMD4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CME4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMF4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMG4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMH4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMI4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMJ4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CMK4', 'FINST-RI7660916ZHWTD95AH12M7Y2KFHE2B9J2P6CML4']
2025-06-22 04:31:48,989 - INFO - 批量插入响应状态码: 200
2025-06-22 04:31:48,989 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 20:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1548', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2EC6D83A-2E4E-79B6-ABA1-FEE641C37D78', 'x-acs-trace-id': 'a82bbdff5c9cdac092813fd276f3ca6d', 'etag': '1YeRBAaI4y7xLAhhzAwvgaQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 04:31:48,989 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM13', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM23', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM33', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM43', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM53', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM63', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM73', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM83', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM93', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMA3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMB3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMC3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMD3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CME3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMF3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMG3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMH3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMI3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMJ3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMK3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CML3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMM3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMN3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMO3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMP3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMQ3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMR3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMS3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMT3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMU3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMV3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMW3']}
2025-06-22 04:31:48,989 - INFO - 批量插入表单数据成功，批次 2，共 32 条记录
2025-06-22 04:31:48,989 - INFO - 成功插入的数据ID: ['FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM13', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM23', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM33', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM43', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM53', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM63', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM73', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM83', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CM93', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMA3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMB3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMC3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMD3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CME3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMF3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMG3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMH3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMI3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMJ3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMK3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CML3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMM3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMN3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMO3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMP3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMQ3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMR3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMS3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMT3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMU3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMV3', 'FINST-8SG66JA12BIWLZ7GBXTME8CRYNAZ1JAN2P6CMW3']
2025-06-22 04:31:54,005 - INFO - 批量插入完成，共 82 条记录
2025-06-22 04:31:54,005 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 82 条，错误: 0 条
2025-06-22 04:31:54,005 - INFO - 数据同步完成！更新: 0 条，插入: 82 条，错误: 0 条
2025-06-22 04:31:54,005 - INFO - 同步完成
2025-06-22 07:30:33,635 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 07:30:33,635 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 07:30:33,635 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 07:30:33,760 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 6 条记录
2025-06-22 07:30:33,760 - INFO - 获取到 1 个日期需要处理: ['2025-06-21']
2025-06-22 07:30:33,760 - INFO - 开始处理日期: 2025-06-21
2025-06-22 07:30:33,776 - INFO - Request Parameters - Page 1:
2025-06-22 07:30:33,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 07:30:33,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 07:30:39,807 - INFO - Response - Page 1:
2025-06-22 07:30:39,807 - INFO - 第 1 页获取到 50 条记录
2025-06-22 07:30:40,322 - INFO - Request Parameters - Page 2:
2025-06-22 07:30:40,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 07:30:40,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 07:30:48,416 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 551C109A-D8AF-7BF9-8839-F73CA54604E3 Response: {'code': 'ServiceUnavailable', 'requestid': '551C109A-D8AF-7BF9-8839-F73CA54604E3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 551C109A-D8AF-7BF9-8839-F73CA54604E3)
2025-06-22 07:30:48,416 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-22 07:31:48,431 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 07:31:48,431 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 07:31:48,431 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 07:31:48,556 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 137 条记录
2025-06-22 07:31:48,556 - INFO - 获取到 1 个日期需要处理: ['2025-06-21']
2025-06-22 07:31:48,556 - INFO - 开始处理日期: 2025-06-21
2025-06-22 07:31:48,556 - INFO - Request Parameters - Page 1:
2025-06-22 07:31:48,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 07:31:48,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 07:31:49,291 - INFO - Response - Page 1:
2025-06-22 07:31:49,291 - INFO - 第 1 页获取到 50 条记录
2025-06-22 07:31:49,791 - INFO - Request Parameters - Page 2:
2025-06-22 07:31:49,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 07:31:49,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 07:31:50,431 - INFO - Response - Page 2:
2025-06-22 07:31:50,431 - INFO - 第 2 页获取到 50 条记录
2025-06-22 07:31:50,947 - INFO - Request Parameters - Page 3:
2025-06-22 07:31:50,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 07:31:50,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 07:31:51,572 - INFO - Response - Page 3:
2025-06-22 07:31:51,572 - INFO - 第 3 页获取到 28 条记录
2025-06-22 07:31:52,072 - INFO - 查询完成，共获取到 128 条记录
2025-06-22 07:31:52,072 - INFO - 获取到 128 条表单数据
2025-06-22 07:31:52,072 - INFO - 当前日期 2025-06-21 有 129 条MySQL数据需要处理
2025-06-22 07:31:52,072 - INFO - 开始批量插入 1 条新记录
2025-06-22 07:31:52,228 - INFO - 批量插入响应状态码: 200
2025-06-22 07:31:52,228 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 21 Jun 2025 23:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D4BA54B-B12E-7D17-BD5F-47F9985C3746', 'x-acs-trace-id': '9cd0302ddc9580885cbb469a8e4f54bb', 'etag': '6nq3SpnKnxdTlcCoZp/YLAg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 07:31:52,228 - INFO - 批量插入响应体: {'result': ['FINST-A17661C1Z5IWX63684JE878B62I22L67IV6CMV2']}
2025-06-22 07:31:52,228 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-22 07:31:52,228 - INFO - 成功插入的数据ID: ['FINST-A17661C1Z5IWX63684JE878B62I22L67IV6CMV2']
2025-06-22 07:31:57,244 - INFO - 批量插入完成，共 1 条记录
2025-06-22 07:31:57,244 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 07:31:57,244 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 07:31:57,244 - INFO - 同步完成
2025-06-22 10:30:33,593 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 10:30:33,593 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 10:30:33,593 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 10:30:33,749 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 92 条记录
2025-06-22 10:30:33,749 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 10:30:33,749 - INFO - 开始处理日期: 2025-06-21
2025-06-22 10:30:33,749 - INFO - Request Parameters - Page 1:
2025-06-22 10:30:33,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:30:33,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:30:39,952 - INFO - Response - Page 1:
2025-06-22 10:30:39,952 - INFO - 第 1 页获取到 50 条记录
2025-06-22 10:30:40,452 - INFO - Request Parameters - Page 2:
2025-06-22 10:30:40,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:30:40,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:30:48,561 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D39BEB99-C192-7114-811C-88C49A0F5C32 Response: {'code': 'ServiceUnavailable', 'requestid': 'D39BEB99-C192-7114-811C-88C49A0F5C32', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D39BEB99-C192-7114-811C-88C49A0F5C32)
2025-06-22 10:30:48,561 - INFO - 开始处理日期: 2025-06-22
2025-06-22 10:30:48,561 - INFO - Request Parameters - Page 1:
2025-06-22 10:30:48,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:30:48,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:30:49,015 - INFO - Response - Page 1:
2025-06-22 10:30:49,015 - INFO - 查询完成，共获取到 0 条记录
2025-06-22 10:30:49,015 - INFO - 获取到 0 条表单数据
2025-06-22 10:30:49,015 - INFO - 当前日期 2025-06-22 有 2 条MySQL数据需要处理
2025-06-22 10:30:49,015 - INFO - 开始批量插入 2 条新记录
2025-06-22 10:30:49,171 - INFO - 批量插入响应状态码: 200
2025-06-22 10:30:49,171 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:30:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9792EC13-400A-7AFF-B4E9-D79F5B08F30D', 'x-acs-trace-id': '3c6b78f35068a02d580017de76217e5c', 'etag': '1sg3JIFixMGquVpy+F9NM2Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:30:49,171 - INFO - 批量插入响应体: {'result': ['FINST-XL666BD1KZHWKRCPACFFACOV991132XBW17CM67', 'FINST-XL666BD1KZHWKRCPACFFACOV991132XBW17CM77']}
2025-06-22 10:30:49,171 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-22 10:30:49,171 - INFO - 成功插入的数据ID: ['FINST-XL666BD1KZHWKRCPACFFACOV991132XBW17CM67', 'FINST-XL666BD1KZHWKRCPACFFACOV991132XBW17CM77']
2025-06-22 10:30:54,186 - INFO - 批量插入完成，共 2 条记录
2025-06-22 10:30:54,186 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-22 10:30:54,186 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 1 条
2025-06-22 10:31:54,202 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 10:31:54,202 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 10:31:54,202 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 10:31:54,342 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 417 条记录
2025-06-22 10:31:54,342 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 10:31:54,342 - INFO - 开始处理日期: 2025-06-21
2025-06-22 10:31:54,342 - INFO - Request Parameters - Page 1:
2025-06-22 10:31:54,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:31:54,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:31:55,092 - INFO - Response - Page 1:
2025-06-22 10:31:55,092 - INFO - 第 1 页获取到 50 条记录
2025-06-22 10:31:55,608 - INFO - Request Parameters - Page 2:
2025-06-22 10:31:55,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:31:55,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:31:56,249 - INFO - Response - Page 2:
2025-06-22 10:31:56,249 - INFO - 第 2 页获取到 50 条记录
2025-06-22 10:31:56,749 - INFO - Request Parameters - Page 3:
2025-06-22 10:31:56,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:31:56,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:31:57,311 - INFO - Response - Page 3:
2025-06-22 10:31:57,311 - INFO - 第 3 页获取到 29 条记录
2025-06-22 10:31:57,811 - INFO - 查询完成，共获取到 129 条记录
2025-06-22 10:31:57,811 - INFO - 获取到 129 条表单数据
2025-06-22 10:31:57,811 - INFO - 当前日期 2025-06-21 有 403 条MySQL数据需要处理
2025-06-22 10:31:57,811 - INFO - 开始批量插入 274 条新记录
2025-06-22 10:31:58,030 - INFO - 批量插入响应状态码: 200
2025-06-22 10:31:58,030 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2377', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '58110CAB-7FEE-7180-B778-5FE684443CAA', 'x-acs-trace-id': 'a395e43751eedc36f200abb52c7753d2', 'etag': '25J4aXBS1w6qybLzkf36EXw7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:31:58,030 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM2', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM3', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM4', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM5', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM6', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM7', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM8', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM9', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMA', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMB', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMC', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMD', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CME', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMF', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMG', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMH', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMI', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMJ', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMK', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CML', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMM', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMN', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMO', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMP', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMQ', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMR', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMS', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMT', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMU', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMV', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMW', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMX', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMY', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMZ', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM01', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM11', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM21', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM31', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM41', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM51', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM61', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM71', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM81', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM91', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMA1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMB1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMC1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMD1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CME1']}
2025-06-22 10:31:58,030 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-22 10:31:58,030 - INFO - 成功插入的数据ID: ['FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM2', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM3', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM4', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM5', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM6', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM7', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM8', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CM9', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMA', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMB', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMC', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMD', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CME', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMF', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMG', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMH', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMI', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMJ', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMK', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CML', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMM', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMN', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMO', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV302TX17CMP', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMQ', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMR', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMS', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMT', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMU', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMV', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMW', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMX', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMY', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMZ', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM01', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM11', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM21', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM31', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM41', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM51', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM61', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM71', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM81', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CM91', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMA1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMB1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMC1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CMD1', 'FINST-2FD66I71GNIW2CA9F0RV85MMACSV312TX17CME1']
2025-06-22 10:32:03,264 - INFO - 批量插入响应状态码: 200
2025-06-22 10:32:03,264 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0201C0AD-9119-7424-83C4-10D2675CE8F6', 'x-acs-trace-id': '9d737d3ad2ec2414e03312a90f9e90cb', 'etag': '28k3TYrGKlyxIhh8CkqvXVA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:32:03,264 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMIB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMJB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMKB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMLB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMMB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMNB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMOB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMPB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMQB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMRB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMSB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMTB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMUB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMVB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMWB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMXB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMYB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMZB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM0C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM1C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM2C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM3C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM4C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM5C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM6C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM7C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM8C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM9C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMAC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMBC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMCC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMDC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMEC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMFC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMGC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMHC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMIC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMJC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMKC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMLC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMMC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMNC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMOC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMPC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMQC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMRC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMSC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMTC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMUC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMVC']}
2025-06-22 10:32:03,264 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-22 10:32:03,264 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMIB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMJB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMKB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMLB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMMB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMNB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMOB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMPB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMQB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMRB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMSB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMTB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMUB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMVB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMWB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMXB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMYB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMZB', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM0C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM1C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM2C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM3C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM4C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM5C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM6C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM7C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM8C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM9C', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMAC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMBC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMCC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMDC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMEC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMFC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMGC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMHC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMIC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMJC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMKC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMLC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMMC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMNC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMOC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMPC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMQC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMRC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMSC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMTC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMUC', 'FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CMVC']
2025-06-22 10:32:08,530 - INFO - 批量插入响应状态码: 200
2025-06-22 10:32:08,530 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6528D39F-45C1-7845-B415-6FF9ED1727D7', 'x-acs-trace-id': 'c3301c987ca3683737ffacd477966a0f', 'etag': '2R6+T+Tga2RFSX8ylRgLOGA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:32:08,530 - INFO - 批量插入响应体: {'result': ['FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM46', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM56', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM66', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM76', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM86', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM96', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMA6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMB6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMC6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMD6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CME6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMF6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMG6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMH6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMI6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMJ6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMK6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CML6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMM6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMN6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMO6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMP6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMQ6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMR6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMS6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMT6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMU6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMV6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMW6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMX6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMY6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMZ6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM07', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM17', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM27', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM37', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM47', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM57', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM67', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM77', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM87', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM97', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMA7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMB7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMC7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMD7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CME7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMF7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMG7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMH7']}
2025-06-22 10:32:08,530 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-22 10:32:08,530 - INFO - 成功插入的数据ID: ['FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM46', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM56', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM66', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM76', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM86', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CM96', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMA6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMB6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMC6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMD6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CME6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMF6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMG6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22I51Y17CMH6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMI6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMJ6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMK6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CML6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMM6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMN6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMO6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMP6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMQ6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMR6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMS6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMT6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMU6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMV6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMW6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMX6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMY6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMZ6', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM07', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM17', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM27', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM37', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM47', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM57', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM67', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM77', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM87', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CM97', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMA7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMB7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMC7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMD7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CME7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMF7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMG7', 'FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMH7']
2025-06-22 10:32:13,780 - INFO - 批量插入响应状态码: 200
2025-06-22 10:32:13,780 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2379', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6801E8E8-FAF1-717F-967E-9829460827A1', 'x-acs-trace-id': '0fb699bc0bb3767ce74a90fc8543f012', 'etag': '2bGCxJGVmgf6M0EsXzatJZg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:32:13,780 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM3', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM4', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM5', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM6', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM7', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM8', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM9', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMA', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMB', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMC', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMD', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CME', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMF', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMG', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMH', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMI', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMJ', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMK', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CML', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMM', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMN', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMO', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMP', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMQ', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMR', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMS', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMT', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMU', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMV', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMW', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMX', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMY', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMZ', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM01', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM11', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM21', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM31', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM41', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM51', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM61', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM71', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM81', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM91', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMA1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMB1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMC1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMD1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CME1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMF1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMG1']}
2025-06-22 10:32:13,780 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-22 10:32:13,780 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM3', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM4', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM5', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM6', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM7', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM8', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM9', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMA', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMB', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMC', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMD', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CME', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMF', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMG', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMH', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMI', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMJ', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMK', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CML', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMM', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMN', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMO', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMP', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMQ', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMR', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMS', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMT', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMU', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMV', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMW', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMX', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMY', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMZ', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM01', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM11', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM21', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM31', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM41', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM51', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM61', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM71', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM81', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CM91', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMA1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMB1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMC1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMD1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CME1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMF1', 'FINST-YPE66RB1GNIW14HTBEBX1AVZ2N1H2J75Y17CMG1']
2025-06-22 10:32:19,014 - INFO - 批量插入响应状态码: 200
2025-06-22 10:32:19,014 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9A15C312-87C0-72C5-BEA4-1B781A1A0AFD', 'x-acs-trace-id': '5bdaa763d94ddc6a39162035eca647a7', 'etag': '2KviWyiUYIvNN18VOV90Xeg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:32:19,014 - INFO - 批量插入响应体: {'result': ['FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM29', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM39', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM49', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM59', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM69', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM79', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM89', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM99', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMA9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMB9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMC9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMD9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CME9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMF9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMG9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMH9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMI9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMJ9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMK9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CML9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMM9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMN9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMO9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMP9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMQ9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMR9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMS9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMT9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMU9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMV9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMW9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMX9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMY9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMZ9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM0A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM1A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM2A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM3A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM4A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM5A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM6A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CM7A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CM8A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CM9A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMAA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMBA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMCA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMDA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMEA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMFA']}
2025-06-22 10:32:19,014 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-22 10:32:19,014 - INFO - 成功插入的数据ID: ['FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM29', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM39', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM49', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM59', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM69', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM79', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM89', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM99', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMA9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMB9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMC9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMD9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CME9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMF9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMG9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMH9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMI9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMJ9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMK9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CML9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMM9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMN9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMO9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMP9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMQ9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMR9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMS9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMT9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMU9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMV9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMW9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMX9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMY9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CMZ9', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM0A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM1A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM2A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM3A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM4A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM5A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2N89Y17CM6A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CM7A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CM8A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CM9A', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMAA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMBA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMCA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMDA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMEA', 'FINST-NU966I81X2IWL3E0FOQGMAX0BF4P2O89Y17CMFA']
2025-06-22 10:32:24,233 - INFO - 批量插入响应状态码: 200
2025-06-22 10:32:24,233 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 02:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1140', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DB9ECE85-29ED-76AA-93E9-3943CE976D38', 'x-acs-trace-id': 'a6932f9e5ae159ee6c36a940e4fe629a', 'etag': '1qEDBaIcH4icmgKD5W8QTNw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 10:32:24,233 - INFO - 批量插入响应体: {'result': ['FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM0', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM1', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM2', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM3', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM4', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM5', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM6', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM7', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM8', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM9', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMA', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMB', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMC', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMD', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CME', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMF', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMG', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMH', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMI', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMJ', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMK', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CML', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMM', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMN']}
2025-06-22 10:32:24,233 - INFO - 批量插入表单数据成功，批次 6，共 24 条记录
2025-06-22 10:32:24,233 - INFO - 成功插入的数据ID: ['FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM0', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM1', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM2', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM3', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM4', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM5', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM6', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM7', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM8', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CM9', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMA', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMB', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMC', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMD', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CME', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMF', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMG', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMH', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMI', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMJ', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMK', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CML', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMM', 'FINST-B1D66U61UNIWOGO4ESBMP5G109UA3U9DY17CMN']
2025-06-22 10:32:29,248 - INFO - 批量插入完成，共 274 条记录
2025-06-22 10:32:29,248 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 274 条，错误: 0 条
2025-06-22 10:32:29,248 - INFO - 开始处理日期: 2025-06-22
2025-06-22 10:32:29,248 - INFO - Request Parameters - Page 1:
2025-06-22 10:32:29,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 10:32:29,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 10:32:29,670 - INFO - Response - Page 1:
2025-06-22 10:32:29,670 - INFO - 第 1 页获取到 2 条记录
2025-06-22 10:32:30,170 - INFO - 查询完成，共获取到 2 条记录
2025-06-22 10:32:30,170 - INFO - 获取到 2 条表单数据
2025-06-22 10:32:30,170 - INFO - 当前日期 2025-06-22 有 2 条MySQL数据需要处理
2025-06-22 10:32:30,170 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 10:32:30,170 - INFO - 数据同步完成！更新: 0 条，插入: 274 条，错误: 0 条
2025-06-22 10:32:30,170 - INFO - 同步完成
2025-06-22 13:30:33,581 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 13:30:33,581 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 13:30:33,581 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 13:30:33,722 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 134 条记录
2025-06-22 13:30:33,722 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 13:30:33,722 - INFO - 开始处理日期: 2025-06-21
2025-06-22 13:30:33,722 - INFO - Request Parameters - Page 1:
2025-06-22 13:30:33,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:30:33,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:30:41,847 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3BC659F1-B20F-72C2-95A1-0C750CBA75CD Response: {'code': 'ServiceUnavailable', 'requestid': '3BC659F1-B20F-72C2-95A1-0C750CBA75CD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3BC659F1-B20F-72C2-95A1-0C750CBA75CD)
2025-06-22 13:30:41,847 - INFO - 开始处理日期: 2025-06-22
2025-06-22 13:30:41,847 - INFO - Request Parameters - Page 1:
2025-06-22 13:30:41,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:30:41,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:30:42,409 - INFO - Response - Page 1:
2025-06-22 13:30:42,409 - INFO - 第 1 页获取到 2 条记录
2025-06-22 13:30:42,909 - INFO - 查询完成，共获取到 2 条记录
2025-06-22 13:30:42,909 - INFO - 获取到 2 条表单数据
2025-06-22 13:30:42,909 - INFO - 当前日期 2025-06-22 有 3 条MySQL数据需要处理
2025-06-22 13:30:42,909 - INFO - 开始批量插入 1 条新记录
2025-06-22 13:30:43,081 - INFO - 批量插入响应状态码: 200
2025-06-22 13:30:43,081 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 05:30:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C77C6D70-BF57-7599-98FE-EF7A6443899C', 'x-acs-trace-id': 'd2a6cb2efc529f05afe794595d9961e9', 'etag': '68IYeMYaQLXDRFl2cdUpT5Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 13:30:43,081 - INFO - 批量插入响应体: {'result': ['FINST-I3F66991VZHW4W4Z5HEZ7BH4K3SP3PLOB87CMK6']}
2025-06-22 13:30:43,081 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-22 13:30:43,081 - INFO - 成功插入的数据ID: ['FINST-I3F66991VZHW4W4Z5HEZ7BH4K3SP3PLOB87CMK6']
2025-06-22 13:30:48,097 - INFO - 批量插入完成，共 1 条记录
2025-06-22 13:30:48,097 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 13:30:48,097 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-06-22 13:31:48,112 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 13:31:48,112 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 13:31:48,112 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 13:31:48,252 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 492 条记录
2025-06-22 13:31:48,252 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 13:31:48,252 - INFO - 开始处理日期: 2025-06-21
2025-06-22 13:31:48,252 - INFO - Request Parameters - Page 1:
2025-06-22 13:31:48,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:48,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:49,065 - INFO - Response - Page 1:
2025-06-22 13:31:49,065 - INFO - 第 1 页获取到 50 条记录
2025-06-22 13:31:49,580 - INFO - Request Parameters - Page 2:
2025-06-22 13:31:49,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:49,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:50,315 - INFO - Response - Page 2:
2025-06-22 13:31:50,315 - INFO - 第 2 页获取到 50 条记录
2025-06-22 13:31:50,830 - INFO - Request Parameters - Page 3:
2025-06-22 13:31:50,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:50,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:51,471 - INFO - Response - Page 3:
2025-06-22 13:31:51,471 - INFO - 第 3 页获取到 50 条记录
2025-06-22 13:31:51,971 - INFO - Request Parameters - Page 4:
2025-06-22 13:31:51,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:51,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:52,659 - INFO - Response - Page 4:
2025-06-22 13:31:52,659 - INFO - 第 4 页获取到 50 条记录
2025-06-22 13:31:53,159 - INFO - Request Parameters - Page 5:
2025-06-22 13:31:53,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:53,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:53,862 - INFO - Response - Page 5:
2025-06-22 13:31:53,862 - INFO - 第 5 页获取到 50 条记录
2025-06-22 13:31:54,377 - INFO - Request Parameters - Page 6:
2025-06-22 13:31:54,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:54,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:54,987 - INFO - Response - Page 6:
2025-06-22 13:31:54,987 - INFO - 第 6 页获取到 50 条记录
2025-06-22 13:31:55,487 - INFO - Request Parameters - Page 7:
2025-06-22 13:31:55,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:55,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:56,159 - INFO - Response - Page 7:
2025-06-22 13:31:56,159 - INFO - 第 7 页获取到 50 条记录
2025-06-22 13:31:56,674 - INFO - Request Parameters - Page 8:
2025-06-22 13:31:56,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:56,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:57,315 - INFO - Response - Page 8:
2025-06-22 13:31:57,315 - INFO - 第 8 页获取到 50 条记录
2025-06-22 13:31:57,830 - INFO - Request Parameters - Page 9:
2025-06-22 13:31:57,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:31:57,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:31:58,268 - INFO - Response - Page 9:
2025-06-22 13:31:58,268 - INFO - 第 9 页获取到 3 条记录
2025-06-22 13:31:58,784 - INFO - 查询完成，共获取到 403 条记录
2025-06-22 13:31:58,784 - INFO - 获取到 403 条表单数据
2025-06-22 13:31:58,784 - INFO - 当前日期 2025-06-21 有 476 条MySQL数据需要处理
2025-06-22 13:31:58,799 - INFO - 开始批量插入 73 条新记录
2025-06-22 13:31:59,034 - INFO - 批量插入响应状态码: 200
2025-06-22 13:31:59,034 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 05:31:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4A72AEEE-EDF5-75CD-9466-558E293C9044', 'x-acs-trace-id': '738a366a64a330e54a15d94c830d5a71', 'etag': '2EJbmLYRHGi4xkN72UrR8ug2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 13:31:59,034 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM5B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM6B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM7B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM8B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM9B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMAB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMBB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMCB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMDB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMEB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMFB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMGB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMHB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMIB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMJB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMKB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMLB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMMB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMNB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMOB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMPB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMQB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMRB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMSB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMTB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMUB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMVB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMWB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMXB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMYB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMZB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM0C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM1C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM2C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM3C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM4C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM5C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM6C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM7C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM8C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM9C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMAC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMBC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMCC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMDC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMEC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMFC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMGC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMHC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Z7BD87CMIC']}
2025-06-22 13:31:59,034 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-22 13:31:59,034 - INFO - 成功插入的数据ID: ['FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM5B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM6B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM7B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM8B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM9B', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMAB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMBB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMCB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMDB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMEB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMFB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMGB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMHB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMIB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMJB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMKB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMLB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMMB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMNB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMOB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMPB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMQB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMRB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMSB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMTB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMUB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMVB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMWB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMXB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMYB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMZB', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM0C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM1C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM2C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM3C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM4C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM5C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM6C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM7C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM8C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CM9C', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMAC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMBC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMCC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMDC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMEC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMFC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMGC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Y7BD87CMHC', 'FINST-QZE668D13ZHWO6S0E23WZ80X6BFN2Z7BD87CMIC']
2025-06-22 13:32:04,315 - INFO - 批量插入响应状态码: 200
2025-06-22 13:32:04,315 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 05:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1116', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AB850B8A-A14D-7082-8F66-A0C9C0BAC5C3', 'x-acs-trace-id': '6b11a589e20830f4423aab55380ab91b', 'etag': '15BHWtX6v6I/60Kd/SOXXMQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 13:32:04,315 - INFO - 批量插入响应体: {'result': ['FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMCC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMDC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMEC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMFC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMGC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMHC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMIC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMJC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMKC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMLC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMMC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMNC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMOC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMPC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMQC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMRC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMSC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMTC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMUC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMVC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMWC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMXC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMYC']}
2025-06-22 13:32:04,315 - INFO - 批量插入表单数据成功，批次 2，共 23 条记录
2025-06-22 13:32:04,315 - INFO - 成功插入的数据ID: ['FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMCC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMDC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMEC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMFC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMGC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMHC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMIC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMJC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMKC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMLC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMMC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMNC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMOC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMPC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMQC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMRC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMSC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMTC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMUC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMVC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMWC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMXC', 'FINST-CJ966Q712ZHWRQRWCDX0EC37IBTU2IAFD87CMYC']
2025-06-22 13:32:09,330 - INFO - 批量插入完成，共 73 条记录
2025-06-22 13:32:09,330 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 73 条，错误: 0 条
2025-06-22 13:32:09,330 - INFO - 开始处理日期: 2025-06-22
2025-06-22 13:32:09,330 - INFO - Request Parameters - Page 1:
2025-06-22 13:32:09,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 13:32:09,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 13:32:09,799 - INFO - Response - Page 1:
2025-06-22 13:32:09,799 - INFO - 第 1 页获取到 3 条记录
2025-06-22 13:32:10,315 - INFO - 查询完成，共获取到 3 条记录
2025-06-22 13:32:10,315 - INFO - 获取到 3 条表单数据
2025-06-22 13:32:10,315 - INFO - 当前日期 2025-06-22 有 3 条MySQL数据需要处理
2025-06-22 13:32:10,315 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 13:32:10,315 - INFO - 数据同步完成！更新: 0 条，插入: 73 条，错误: 0 条
2025-06-22 13:32:10,315 - INFO - 同步完成
2025-06-22 16:30:33,601 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 16:30:33,601 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 16:30:33,601 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 16:30:33,741 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 160 条记录
2025-06-22 16:30:33,741 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 16:30:33,741 - INFO - 开始处理日期: 2025-06-21
2025-06-22 16:30:33,741 - INFO - Request Parameters - Page 1:
2025-06-22 16:30:33,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:30:33,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:30:41,850 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE7B9FF8-5CF5-72B4-A85A-354A9FC6C60A Response: {'code': 'ServiceUnavailable', 'requestid': 'BE7B9FF8-5CF5-72B4-A85A-354A9FC6C60A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE7B9FF8-5CF5-72B4-A85A-354A9FC6C60A)
2025-06-22 16:30:41,850 - INFO - 开始处理日期: 2025-06-22
2025-06-22 16:30:41,850 - INFO - Request Parameters - Page 1:
2025-06-22 16:30:41,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:30:41,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:30:47,616 - INFO - Response - Page 1:
2025-06-22 16:30:47,616 - INFO - 第 1 页获取到 3 条记录
2025-06-22 16:30:48,116 - INFO - 查询完成，共获取到 3 条记录
2025-06-22 16:30:48,116 - INFO - 获取到 3 条表单数据
2025-06-22 16:30:48,116 - INFO - 当前日期 2025-06-22 有 3 条MySQL数据需要处理
2025-06-22 16:30:48,116 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 16:30:48,116 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-22 16:31:48,131 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 16:31:48,131 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 16:31:48,131 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 16:31:48,272 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 518 条记录
2025-06-22 16:31:48,272 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 16:31:48,272 - INFO - 开始处理日期: 2025-06-21
2025-06-22 16:31:48,272 - INFO - Request Parameters - Page 1:
2025-06-22 16:31:48,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:48,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:49,022 - INFO - Response - Page 1:
2025-06-22 16:31:49,022 - INFO - 第 1 页获取到 50 条记录
2025-06-22 16:31:49,522 - INFO - Request Parameters - Page 2:
2025-06-22 16:31:49,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:49,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:50,194 - INFO - Response - Page 2:
2025-06-22 16:31:50,194 - INFO - 第 2 页获取到 50 条记录
2025-06-22 16:31:50,709 - INFO - Request Parameters - Page 3:
2025-06-22 16:31:50,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:50,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:51,366 - INFO - Response - Page 3:
2025-06-22 16:31:51,366 - INFO - 第 3 页获取到 50 条记录
2025-06-22 16:31:51,866 - INFO - Request Parameters - Page 4:
2025-06-22 16:31:51,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:51,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:52,584 - INFO - Response - Page 4:
2025-06-22 16:31:52,584 - INFO - 第 4 页获取到 50 条记录
2025-06-22 16:31:53,100 - INFO - Request Parameters - Page 5:
2025-06-22 16:31:53,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:53,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:53,741 - INFO - Response - Page 5:
2025-06-22 16:31:53,741 - INFO - 第 5 页获取到 50 条记录
2025-06-22 16:31:54,256 - INFO - Request Parameters - Page 6:
2025-06-22 16:31:54,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:54,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:55,037 - INFO - Response - Page 6:
2025-06-22 16:31:55,037 - INFO - 第 6 页获取到 50 条记录
2025-06-22 16:31:55,553 - INFO - Request Parameters - Page 7:
2025-06-22 16:31:55,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:55,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:56,162 - INFO - Response - Page 7:
2025-06-22 16:31:56,162 - INFO - 第 7 页获取到 50 条记录
2025-06-22 16:31:56,678 - INFO - Request Parameters - Page 8:
2025-06-22 16:31:56,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:56,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:57,381 - INFO - Response - Page 8:
2025-06-22 16:31:57,381 - INFO - 第 8 页获取到 50 条记录
2025-06-22 16:31:57,897 - INFO - Request Parameters - Page 9:
2025-06-22 16:31:57,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:57,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:58,631 - INFO - Response - Page 9:
2025-06-22 16:31:58,631 - INFO - 第 9 页获取到 50 条记录
2025-06-22 16:31:59,147 - INFO - Request Parameters - Page 10:
2025-06-22 16:31:59,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:31:59,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:31:59,725 - INFO - Response - Page 10:
2025-06-22 16:31:59,725 - INFO - 第 10 页获取到 26 条记录
2025-06-22 16:32:00,241 - INFO - 查询完成，共获取到 476 条记录
2025-06-22 16:32:00,241 - INFO - 获取到 476 条表单数据
2025-06-22 16:32:00,241 - INFO - 当前日期 2025-06-21 有 502 条MySQL数据需要处理
2025-06-22 16:32:00,256 - INFO - 开始批量插入 26 条新记录
2025-06-22 16:32:00,475 - INFO - 批量插入响应状态码: 200
2025-06-22 16:32:00,475 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 08:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1260', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3978E065-3362-72FC-BB46-4131400CDF82', 'x-acs-trace-id': '7ce1da0f407411c8c4b95e37c613e1ad', 'etag': '1nfWBRfcBbdoilUF75uiRbw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 16:32:00,475 - INFO - 批量插入响应体: {'result': ['FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMP1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMQ1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMR1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMS1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMT1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMU1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMV1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMW1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMX1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMY1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMZ1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM02', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM12', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM22', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM32', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM42', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM52', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM62', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM72', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM82', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM92', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMA2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMB2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMC2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMD2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CME2']}
2025-06-22 16:32:00,475 - INFO - 批量插入表单数据成功，批次 1，共 26 条记录
2025-06-22 16:32:00,475 - INFO - 成功插入的数据ID: ['FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMP1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMQ1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMR1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMS1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMT1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMU1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMV1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82TPTSE7CMW1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMX1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMY1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMZ1', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM02', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM12', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM22', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM32', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM42', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM52', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM62', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM72', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM82', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CM92', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMA2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMB2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMC2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CMD2', 'FINST-3RE66ZB120IWF9V29K5U7BG05OY82UPTSE7CME2']
2025-06-22 16:32:05,491 - INFO - 批量插入完成，共 26 条记录
2025-06-22 16:32:05,491 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 26 条，错误: 0 条
2025-06-22 16:32:05,491 - INFO - 开始处理日期: 2025-06-22
2025-06-22 16:32:05,491 - INFO - Request Parameters - Page 1:
2025-06-22 16:32:05,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 16:32:05,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 16:32:05,897 - INFO - Response - Page 1:
2025-06-22 16:32:05,897 - INFO - 第 1 页获取到 3 条记录
2025-06-22 16:32:06,397 - INFO - 查询完成，共获取到 3 条记录
2025-06-22 16:32:06,397 - INFO - 获取到 3 条表单数据
2025-06-22 16:32:06,397 - INFO - 当前日期 2025-06-22 有 3 条MySQL数据需要处理
2025-06-22 16:32:06,397 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 16:32:06,397 - INFO - 数据同步完成！更新: 0 条，插入: 26 条，错误: 0 条
2025-06-22 16:32:06,397 - INFO - 同步完成
2025-06-22 19:30:34,256 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 19:30:34,256 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 19:30:34,256 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 19:30:34,397 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 177 条记录
2025-06-22 19:30:34,397 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 19:30:34,397 - INFO - 开始处理日期: 2025-06-21
2025-06-22 19:30:34,397 - INFO - Request Parameters - Page 1:
2025-06-22 19:30:34,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:30:34,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:30:42,523 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B12741FE-4E46-75FB-B672-DD4776494928 Response: {'code': 'ServiceUnavailable', 'requestid': 'B12741FE-4E46-75FB-B672-DD4776494928', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B12741FE-4E46-75FB-B672-DD4776494928)
2025-06-22 19:30:42,523 - INFO - 开始处理日期: 2025-06-22
2025-06-22 19:30:42,523 - INFO - Request Parameters - Page 1:
2025-06-22 19:30:42,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:30:42,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:30:42,977 - INFO - Response - Page 1:
2025-06-22 19:30:42,977 - INFO - 第 1 页获取到 3 条记录
2025-06-22 19:30:43,492 - INFO - 查询完成，共获取到 3 条记录
2025-06-22 19:30:43,492 - INFO - 获取到 3 条表单数据
2025-06-22 19:30:43,492 - INFO - 当前日期 2025-06-22 有 4 条MySQL数据需要处理
2025-06-22 19:30:43,492 - INFO - 开始批量插入 1 条新记录
2025-06-22 19:30:43,664 - INFO - 批量插入响应状态码: 200
2025-06-22 19:30:43,664 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 11:30:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AEBA26BC-1C5F-7109-85EE-36AD77120906', 'x-acs-trace-id': '4adec4b01ff4b7c506cef8e081208fb4', 'etag': '6y2U+VzYRQAZVCGV6YLAz/A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 19:30:43,664 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1A5IW6MCYBU53Q96804FR3LDN6L7CMRF']}
2025-06-22 19:30:43,664 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-22 19:30:43,664 - INFO - 成功插入的数据ID: ['FINST-LR5668B1A5IW6MCYBU53Q96804FR3LDN6L7CMRF']
2025-06-22 19:30:48,681 - INFO - 批量插入完成，共 1 条记录
2025-06-22 19:30:48,681 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 19:30:48,681 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-06-22 19:31:48,706 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 19:31:48,706 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 19:31:48,706 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 19:31:48,847 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 535 条记录
2025-06-22 19:31:48,847 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 19:31:48,847 - INFO - 开始处理日期: 2025-06-21
2025-06-22 19:31:48,847 - INFO - Request Parameters - Page 1:
2025-06-22 19:31:48,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:31:48,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:31:54,301 - INFO - Response - Page 1:
2025-06-22 19:31:54,301 - INFO - 第 1 页获取到 50 条记录
2025-06-22 19:31:54,817 - INFO - Request Parameters - Page 2:
2025-06-22 19:31:54,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:31:54,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:31:55,489 - INFO - Response - Page 2:
2025-06-22 19:31:55,489 - INFO - 第 2 页获取到 50 条记录
2025-06-22 19:31:56,005 - INFO - Request Parameters - Page 3:
2025-06-22 19:31:56,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:31:56,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:31:56,723 - INFO - Response - Page 3:
2025-06-22 19:31:56,723 - INFO - 第 3 页获取到 50 条记录
2025-06-22 19:31:57,224 - INFO - Request Parameters - Page 4:
2025-06-22 19:31:57,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:31:57,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:31:57,864 - INFO - Response - Page 4:
2025-06-22 19:31:57,864 - INFO - 第 4 页获取到 50 条记录
2025-06-22 19:31:58,364 - INFO - Request Parameters - Page 5:
2025-06-22 19:31:58,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:31:58,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:31:59,036 - INFO - Response - Page 5:
2025-06-22 19:31:59,036 - INFO - 第 5 页获取到 50 条记录
2025-06-22 19:31:59,552 - INFO - Request Parameters - Page 6:
2025-06-22 19:31:59,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:31:59,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:00,193 - INFO - Response - Page 6:
2025-06-22 19:32:00,193 - INFO - 第 6 页获取到 50 条记录
2025-06-22 19:32:00,693 - INFO - Request Parameters - Page 7:
2025-06-22 19:32:00,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:32:00,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:01,365 - INFO - Response - Page 7:
2025-06-22 19:32:01,365 - INFO - 第 7 页获取到 50 条记录
2025-06-22 19:32:01,881 - INFO - Request Parameters - Page 8:
2025-06-22 19:32:01,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:32:01,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:02,537 - INFO - Response - Page 8:
2025-06-22 19:32:02,537 - INFO - 第 8 页获取到 50 条记录
2025-06-22 19:32:03,053 - INFO - Request Parameters - Page 9:
2025-06-22 19:32:03,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:32:03,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:03,709 - INFO - Response - Page 9:
2025-06-22 19:32:03,709 - INFO - 第 9 页获取到 50 条记录
2025-06-22 19:32:04,225 - INFO - Request Parameters - Page 10:
2025-06-22 19:32:04,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:32:04,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:04,803 - INFO - Response - Page 10:
2025-06-22 19:32:04,803 - INFO - 第 10 页获取到 50 条记录
2025-06-22 19:32:05,303 - INFO - Request Parameters - Page 11:
2025-06-22 19:32:05,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:32:05,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:05,741 - INFO - Response - Page 11:
2025-06-22 19:32:05,741 - INFO - 第 11 页获取到 2 条记录
2025-06-22 19:32:06,256 - INFO - 查询完成，共获取到 502 条记录
2025-06-22 19:32:06,256 - INFO - 获取到 502 条表单数据
2025-06-22 19:32:06,256 - INFO - 当前日期 2025-06-21 有 518 条MySQL数据需要处理
2025-06-22 19:32:06,272 - INFO - 开始批量插入 16 条新记录
2025-06-22 19:32:06,444 - INFO - 批量插入响应状态码: 200
2025-06-22 19:32:06,444 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 11:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '780', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2F492687-07BF-7DCA-A4CC-B1E963881E1A', 'x-acs-trace-id': 'd534be90f27221b0d1003b21e34e29a4', 'etag': '7Fb6j68NSiYyjQBsqpUEmQw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 19:32:06,444 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMJC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMKC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMLC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMMC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMNC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMOC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMPC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMQC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMRC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMSC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMTC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMUC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMVC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMWC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMXC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMYC']}
2025-06-22 19:32:06,444 - INFO - 批量插入表单数据成功，批次 1，共 16 条记录
2025-06-22 19:32:06,444 - INFO - 成功插入的数据ID: ['FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMJC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMKC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMLC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMMC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMNC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMOC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMPC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMQC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMRC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMSC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMTC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMUC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMVC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMWC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMXC', 'FINST-SWC66P91ZZHWUOBHD6KBQ6JH2X3E3T8F8L7CMYC']
2025-06-22 19:32:11,460 - INFO - 批量插入完成，共 16 条记录
2025-06-22 19:32:11,460 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 16 条，错误: 0 条
2025-06-22 19:32:11,460 - INFO - 开始处理日期: 2025-06-22
2025-06-22 19:32:11,460 - INFO - Request Parameters - Page 1:
2025-06-22 19:32:11,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 19:32:11,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 19:32:11,929 - INFO - Response - Page 1:
2025-06-22 19:32:11,929 - INFO - 第 1 页获取到 4 条记录
2025-06-22 19:32:12,445 - INFO - 查询完成，共获取到 4 条记录
2025-06-22 19:32:12,445 - INFO - 获取到 4 条表单数据
2025-06-22 19:32:12,445 - INFO - 当前日期 2025-06-22 有 4 条MySQL数据需要处理
2025-06-22 19:32:12,445 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-22 19:32:12,445 - INFO - 数据同步完成！更新: 0 条，插入: 16 条，错误: 0 条
2025-06-22 19:32:12,445 - INFO - 同步完成
2025-06-22 22:30:33,710 - INFO - 使用默认增量同步（当天更新数据）
2025-06-22 22:30:33,710 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-22 22:30:33,710 - INFO - 查询参数: ('2025-06-22',)
2025-06-22 22:30:33,851 - INFO - MySQL查询成功，增量数据（日期: 2025-06-22），共获取 190 条记录
2025-06-22 22:30:33,851 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 22:30:33,866 - INFO - 开始处理日期: 2025-06-21
2025-06-22 22:30:33,866 - INFO - Request Parameters - Page 1:
2025-06-22 22:30:33,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:30:33,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:30:41,993 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8BB8A5B-3088-7AC6-877F-62A82C2C9862 Response: {'code': 'ServiceUnavailable', 'requestid': 'B8BB8A5B-3088-7AC6-877F-62A82C2C9862', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8BB8A5B-3088-7AC6-877F-62A82C2C9862)
2025-06-22 22:30:41,993 - INFO - 开始处理日期: 2025-06-22
2025-06-22 22:30:41,993 - INFO - Request Parameters - Page 1:
2025-06-22 22:30:41,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:30:41,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:30:42,680 - INFO - Response - Page 1:
2025-06-22 22:30:42,680 - INFO - 第 1 页获取到 4 条记录
2025-06-22 22:30:43,196 - INFO - 查询完成，共获取到 4 条记录
2025-06-22 22:30:43,196 - INFO - 获取到 4 条表单数据
2025-06-22 22:30:43,196 - INFO - 当前日期 2025-06-22 有 15 条MySQL数据需要处理
2025-06-22 22:30:43,196 - INFO - 开始批量插入 11 条新记录
2025-06-22 22:30:43,352 - INFO - 批量插入响应状态码: 200
2025-06-22 22:30:43,352 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 14:30:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D165010F-A992-700A-8656-A0241875255D', 'x-acs-trace-id': '70cdf6525e56e09d028b3cb7a7f49c67', 'etag': '5qZ5fRMC8DJOnZiFU6/kcBA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 22:30:43,352 - INFO - 批量插入响应体: {'result': ['FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM3C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM4C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM5C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM6C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM7C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM8C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM9C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMAC', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMBC', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMCC', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMDC']}
2025-06-22 22:30:43,352 - INFO - 批量插入表单数据成功，批次 1，共 11 条记录
2025-06-22 22:30:43,352 - INFO - 成功插入的数据ID: ['FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM3C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM4C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM5C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM6C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM7C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM8C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CM9C', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMAC', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMBC', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMCC', 'FINST-QZE668D14ZHWATZZ6NF5756MWD7S3B13MR7CMDC']
2025-06-22 22:30:48,369 - INFO - 批量插入完成，共 11 条记录
2025-06-22 22:30:48,369 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 11 条，错误: 0 条
2025-06-22 22:30:48,369 - INFO - 数据同步完成！更新: 0 条，插入: 11 条，错误: 1 条
2025-06-22 22:31:48,395 - INFO - 开始同步昨天与今天的销售数据: 2025-06-21 至 2025-06-22
2025-06-22 22:31:48,395 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-22 22:31:48,395 - INFO - 查询参数: ('2025-06-21', '2025-06-22')
2025-06-22 22:31:48,535 - INFO - MySQL查询成功，时间段: 2025-06-21 至 2025-06-22，共获取 549 条记录
2025-06-22 22:31:48,535 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-22 22:31:48,535 - INFO - 开始处理日期: 2025-06-21
2025-06-22 22:31:48,535 - INFO - Request Parameters - Page 1:
2025-06-22 22:31:48,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:31:48,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:31:49,223 - INFO - Response - Page 1:
2025-06-22 22:31:49,223 - INFO - 第 1 页获取到 50 条记录
2025-06-22 22:31:49,739 - INFO - Request Parameters - Page 2:
2025-06-22 22:31:49,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:31:49,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:31:55,880 - INFO - Response - Page 2:
2025-06-22 22:31:55,880 - INFO - 第 2 页获取到 50 条记录
2025-06-22 22:31:56,380 - INFO - Request Parameters - Page 3:
2025-06-22 22:31:56,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:31:56,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:31:57,021 - INFO - Response - Page 3:
2025-06-22 22:31:57,021 - INFO - 第 3 页获取到 50 条记录
2025-06-22 22:31:57,537 - INFO - Request Parameters - Page 4:
2025-06-22 22:31:57,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:31:57,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:31:58,225 - INFO - Response - Page 4:
2025-06-22 22:31:58,225 - INFO - 第 4 页获取到 50 条记录
2025-06-22 22:31:58,740 - INFO - Request Parameters - Page 5:
2025-06-22 22:31:58,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:31:58,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:31:59,381 - INFO - Response - Page 5:
2025-06-22 22:31:59,381 - INFO - 第 5 页获取到 50 条记录
2025-06-22 22:31:59,897 - INFO - Request Parameters - Page 6:
2025-06-22 22:31:59,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:31:59,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:00,522 - INFO - Response - Page 6:
2025-06-22 22:32:00,522 - INFO - 第 6 页获取到 50 条记录
2025-06-22 22:32:01,022 - INFO - Request Parameters - Page 7:
2025-06-22 22:32:01,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:32:01,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:01,616 - INFO - Response - Page 7:
2025-06-22 22:32:01,616 - INFO - 第 7 页获取到 50 条记录
2025-06-22 22:32:02,116 - INFO - Request Parameters - Page 8:
2025-06-22 22:32:02,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:32:02,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:02,803 - INFO - Response - Page 8:
2025-06-22 22:32:02,803 - INFO - 第 8 页获取到 50 条记录
2025-06-22 22:32:03,304 - INFO - Request Parameters - Page 9:
2025-06-22 22:32:03,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:32:03,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:03,929 - INFO - Response - Page 9:
2025-06-22 22:32:03,929 - INFO - 第 9 页获取到 50 条记录
2025-06-22 22:32:04,429 - INFO - Request Parameters - Page 10:
2025-06-22 22:32:04,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:32:04,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:05,101 - INFO - Response - Page 10:
2025-06-22 22:32:05,101 - INFO - 第 10 页获取到 50 条记录
2025-06-22 22:32:05,601 - INFO - Request Parameters - Page 11:
2025-06-22 22:32:05,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:32:05,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:06,148 - INFO - Response - Page 11:
2025-06-22 22:32:06,148 - INFO - 第 11 页获取到 18 条记录
2025-06-22 22:32:06,663 - INFO - 查询完成，共获取到 518 条记录
2025-06-22 22:32:06,663 - INFO - 获取到 518 条表单数据
2025-06-22 22:32:06,663 - INFO - 当前日期 2025-06-21 有 519 条MySQL数据需要处理
2025-06-22 22:32:06,679 - INFO - 开始批量插入 1 条新记录
2025-06-22 22:32:06,835 - INFO - 批量插入响应状态码: 200
2025-06-22 22:32:06,835 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 14:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '943C9467-7A8B-799C-B454-0541AFE1A8CB', 'x-acs-trace-id': 'b61a1f2aabfc97923cf132e4fc854c68', 'etag': '6vtgAMaowmGX8czUzrhq5aQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 22:32:06,835 - INFO - 批量插入响应体: {'result': ['FINST-X2F66HC1JZHWN147FOXEM8P79E0121GVNR7CMB7']}
2025-06-22 22:32:06,835 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-22 22:32:06,835 - INFO - 成功插入的数据ID: ['FINST-X2F66HC1JZHWN147FOXEM8P79E0121GVNR7CMB7']
2025-06-22 22:32:11,852 - INFO - 批量插入完成，共 1 条记录
2025-06-22 22:32:11,852 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 22:32:11,852 - INFO - 开始处理日期: 2025-06-22
2025-06-22 22:32:11,852 - INFO - Request Parameters - Page 1:
2025-06-22 22:32:11,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-22 22:32:11,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-22 22:32:12,368 - INFO - Response - Page 1:
2025-06-22 22:32:12,368 - INFO - 第 1 页获取到 15 条记录
2025-06-22 22:32:12,883 - INFO - 查询完成，共获取到 15 条记录
2025-06-22 22:32:12,883 - INFO - 获取到 15 条表单数据
2025-06-22 22:32:12,883 - INFO - 当前日期 2025-06-22 有 16 条MySQL数据需要处理
2025-06-22 22:32:12,883 - INFO - 开始批量插入 1 条新记录
2025-06-22 22:32:13,055 - INFO - 批量插入响应状态码: 200
2025-06-22 22:32:13,055 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 14:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D43ED3FF-FC52-7AEA-B483-74A4A2683956', 'x-acs-trace-id': '6a8a5c8ff4a1155cfdce221bf47c83ce', 'etag': '6DiCLU5Zorw4ytk62BXS4mg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-22 22:32:13,055 - INFO - 批量插入响应体: {'result': ['FINST-2PF66CD1JLIWOH0P6LVPPAPARJSK3V80OR7CM61']}
2025-06-22 22:32:13,055 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-22 22:32:13,055 - INFO - 成功插入的数据ID: ['FINST-2PF66CD1JLIWOH0P6LVPPAPARJSK3V80OR7CM61']
2025-06-22 22:32:18,072 - INFO - 批量插入完成，共 1 条记录
2025-06-22 22:32:18,072 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-22 22:32:18,072 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-06-22 22:32:18,072 - INFO - 同步完成
