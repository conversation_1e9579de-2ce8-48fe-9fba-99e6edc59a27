2025-07-01 01:30:33,607 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 01:30:33,607 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 01:30:33,607 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 01:30:33,685 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 0 条记录
2025-07-01 01:30:33,685 - ERROR - 未获取到MySQL数据
2025-07-01 01:31:33,700 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 01:31:33,700 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 01:31:33,700 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 01:31:33,825 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 52 条记录
2025-07-01 01:31:33,825 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 01:31:33,825 - INFO - 开始处理日期: 2025-06-30
2025-07-01 01:31:33,825 - INFO - Request Parameters - Page 1:
2025-07-01 01:31:33,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 01:31:33,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 01:31:41,950 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 82403570-A0B4-738B-BEE0-636813745F60 Response: {'code': 'ServiceUnavailable', 'requestid': '82403570-A0B4-738B-BEE0-636813745F60', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 82403570-A0B4-738B-BEE0-636813745F60)
2025-07-01 01:31:41,950 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-01 01:31:41,950 - INFO - 同步完成
2025-07-01 04:30:34,253 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 04:30:34,253 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 04:30:34,253 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 04:30:34,378 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 1 条记录
2025-07-01 04:30:34,394 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 04:30:34,394 - INFO - 开始处理日期: 2025-06-30
2025-07-01 04:30:34,394 - INFO - Request Parameters - Page 1:
2025-07-01 04:30:34,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 04:30:34,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 04:30:42,503 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A0E3EB14-5D8A-709C-AA91-BE0D24067DB1 Response: {'code': 'ServiceUnavailable', 'requestid': 'A0E3EB14-5D8A-709C-AA91-BE0D24067DB1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A0E3EB14-5D8A-709C-AA91-BE0D24067DB1)
2025-07-01 04:30:42,503 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-01 04:31:42,519 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 04:31:42,519 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 04:31:42,519 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 04:31:42,644 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 109 条记录
2025-07-01 04:31:42,644 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 04:31:42,644 - INFO - 开始处理日期: 2025-06-30
2025-07-01 04:31:42,644 - INFO - Request Parameters - Page 1:
2025-07-01 04:31:42,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 04:31:42,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 04:31:43,206 - INFO - Response - Page 1:
2025-07-01 04:31:43,206 - INFO - 第 1 页获取到 7 条记录
2025-07-01 04:31:43,706 - INFO - 查询完成，共获取到 7 条记录
2025-07-01 04:31:43,706 - INFO - 获取到 7 条表单数据
2025-07-01 04:31:43,706 - INFO - 当前日期 2025-06-30 有 107 条MySQL数据需要处理
2025-07-01 04:31:43,706 - INFO - 开始批量插入 100 条新记录
2025-07-01 04:31:43,972 - INFO - 批量插入响应状态码: 200
2025-07-01 04:31:43,972 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 20:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '49C1DCC4-A671-72FA-BEBA-A5D99999CB58', 'x-acs-trace-id': 'f3a21e582e5c67f92d4a444e7f8caede', 'etag': '2cv3cw19xuRMjDlKX3NE20Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 04:31:43,972 - INFO - 批量插入响应体: {'result': ['FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMQ3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMR3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMS3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMT3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMU3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMV3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMW3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMX3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMY3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMZ3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM04', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM14', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM24', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM34', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM44', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM54', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM64', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM74', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM84', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM94', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMA4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMB4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMC4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMD4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCME4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMF4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMG4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMH4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMI4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMJ4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMK4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCML4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMM4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMN4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMO4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMP4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMQ4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMR4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMS4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMT4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMU4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMV4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMW4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMX4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMY4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMZ4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM05', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM15', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM25', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM35']}
2025-07-01 04:31:43,972 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-01 04:31:43,972 - INFO - 成功插入的数据ID: ['FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMQ3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMR3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMS3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMT3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMU3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMV3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMW3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMX3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMY3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMZ3', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM04', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM14', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM24', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM34', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM44', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM54', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM64', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM74', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM84', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM94', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMA4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMB4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMC4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMD4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCME4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMF4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMG4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMH4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMI4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMJ4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMK4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCML4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMM4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMN4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMO4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMP4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMQ4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMR4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMS4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMT4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMU4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMV4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMW4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMX4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMY4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMZ4', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM05', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM15', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM25', 'FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCM35']
2025-07-01 04:31:49,222 - INFO - 批量插入响应状态码: 200
2025-07-01 04:31:49,222 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 30 Jun 2025 20:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A181D366-FBE5-732F-9135-D3028553F8C7', 'x-acs-trace-id': 'f01d37080d9c5e8be75bf51ec320bc03', 'etag': '2pv54rMQ8wqz2SGS98VBIbQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 04:31:49,222 - INFO - 批量插入响应体: {'result': ['FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMTK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMUK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMVK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMWK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMXK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMYK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMZK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM0L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM1L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM2L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM3L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM4L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM5L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM6L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM7L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM8L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM9L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMAL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMBL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMCL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMDL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMEL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMFL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMGL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMHL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMIL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMJL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMKL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMLL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMML', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMNL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMOL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMPL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMQL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMRL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMSL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMTL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMUL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMVL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMWL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMXL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMYL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMZL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM0M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM1M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM2M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM3M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM4M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM5M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM6M']}
2025-07-01 04:31:49,222 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-01 04:31:49,222 - INFO - 成功插入的数据ID: ['FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMTK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMUK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMVK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMWK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMXK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMYK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMZK', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM0L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM1L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM2L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM3L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM4L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM5L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM6L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM7L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM8L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM9L', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMAL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMBL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMCL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMDL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMEL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMFL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMGL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMHL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMIL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMJL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMKL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMLL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMML', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMNL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMOL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMPL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMQL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMRL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMSL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMTL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMUL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMVL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMWL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMXL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMYL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMZL', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM0M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM1M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM2M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM3M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM4M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM5M', 'FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCM6M']
2025-07-01 04:31:54,237 - INFO - 批量插入完成，共 100 条记录
2025-07-01 04:31:54,237 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 100 条，错误: 0 条
2025-07-01 04:31:54,237 - INFO - 数据同步完成！更新: 0 条，插入: 100 条，错误: 0 条
2025-07-01 04:31:54,237 - INFO - 同步完成
2025-07-01 07:30:33,903 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 07:30:33,903 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 07:30:33,903 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 07:30:34,043 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 1 条记录
2025-07-01 07:30:34,043 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 07:30:34,043 - INFO - 开始处理日期: 2025-06-30
2025-07-01 07:30:34,043 - INFO - Request Parameters - Page 1:
2025-07-01 07:30:34,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 07:30:34,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 07:30:40,387 - INFO - Response - Page 1:
2025-07-01 07:30:40,387 - INFO - 第 1 页获取到 50 条记录
2025-07-01 07:30:40,902 - INFO - Request Parameters - Page 2:
2025-07-01 07:30:40,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 07:30:40,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 07:30:49,012 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 97AB7301-09E7-7730-94C3-54AB6AE9B598 Response: {'code': 'ServiceUnavailable', 'requestid': '97AB7301-09E7-7730-94C3-54AB6AE9B598', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 97AB7301-09E7-7730-94C3-54AB6AE9B598)
2025-07-01 07:30:49,012 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-01 07:31:49,027 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 07:31:49,027 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 07:31:49,027 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 07:31:49,152 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 109 条记录
2025-07-01 07:31:49,152 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 07:31:49,152 - INFO - 开始处理日期: 2025-06-30
2025-07-01 07:31:49,152 - INFO - Request Parameters - Page 1:
2025-07-01 07:31:49,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 07:31:49,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 07:31:49,824 - INFO - Response - Page 1:
2025-07-01 07:31:49,824 - INFO - 第 1 页获取到 50 条记录
2025-07-01 07:31:50,324 - INFO - Request Parameters - Page 2:
2025-07-01 07:31:50,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 07:31:50,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 07:31:51,027 - INFO - Response - Page 2:
2025-07-01 07:31:51,027 - INFO - 第 2 页获取到 50 条记录
2025-07-01 07:31:51,543 - INFO - Request Parameters - Page 3:
2025-07-01 07:31:51,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 07:31:51,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 07:31:52,043 - INFO - Response - Page 3:
2025-07-01 07:31:52,043 - INFO - 第 3 页获取到 7 条记录
2025-07-01 07:31:52,543 - INFO - 查询完成，共获取到 107 条记录
2025-07-01 07:31:52,543 - INFO - 获取到 107 条表单数据
2025-07-01 07:31:52,543 - INFO - 当前日期 2025-06-30 有 107 条MySQL数据需要处理
2025-07-01 07:31:52,543 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 07:31:52,543 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 07:31:52,543 - INFO - 同步完成
2025-07-01 10:30:34,076 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 10:30:34,076 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 10:30:34,076 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 10:30:34,217 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 88 条记录
2025-07-01 10:30:34,217 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 10:30:34,217 - INFO - 开始处理日期: 2025-06-30
2025-07-01 10:30:34,217 - INFO - Request Parameters - Page 1:
2025-07-01 10:30:34,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 10:30:34,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 10:30:41,264 - INFO - Response - Page 1:
2025-07-01 10:30:41,264 - INFO - 第 1 页获取到 50 条记录
2025-07-01 10:30:41,779 - INFO - Request Parameters - Page 2:
2025-07-01 10:30:41,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 10:30:41,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 10:30:42,467 - INFO - Response - Page 2:
2025-07-01 10:30:42,467 - INFO - 第 2 页获取到 50 条记录
2025-07-01 10:30:42,983 - INFO - Request Parameters - Page 3:
2025-07-01 10:30:42,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 10:30:42,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 10:30:43,498 - INFO - Response - Page 3:
2025-07-01 10:30:43,498 - INFO - 第 3 页获取到 7 条记录
2025-07-01 10:30:44,014 - INFO - 查询完成，共获取到 107 条记录
2025-07-01 10:30:44,014 - INFO - 获取到 107 条表单数据
2025-07-01 10:30:44,014 - INFO - 当前日期 2025-06-30 有 87 条MySQL数据需要处理
2025-07-01 10:30:44,014 - INFO - 开始批量插入 86 条新记录
2025-07-01 10:30:44,264 - INFO - 批量插入响应状态码: 200
2025-07-01 10:30:44,264 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 02:30:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2379', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2F72EB62-9B43-7EEB-9302-45A68618E054', 'x-acs-trace-id': '8e37d2887e48c5607a8a0a7bd4f2b628', 'etag': '2vfM0fDo4Kur+pUXZgHKPhQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 10:30:44,264 - INFO - 批量插入响应体: {'result': ['FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM3', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM4', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM5', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM6', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM7', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM8', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM9', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMA', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMB', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMC', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMD', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCME', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMF', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMG', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMH', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMI', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMJ', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMK', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCML', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMM', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMN', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMO', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMP', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMQ', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMR', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMS', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMT', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMU', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMV', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMW', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMX', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMY', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMZ', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM01', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM11', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM21', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM31', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM41', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM51', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM61', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM71', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM81', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM91', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMA1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMB1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMC1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMD1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCME1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMF1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMG1']}
2025-07-01 10:30:44,264 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-01 10:30:44,264 - INFO - 成功插入的数据ID: ['FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM3', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM4', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM5', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM6', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM7', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM8', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCM9', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMA', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMB', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMC', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMD', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCME', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMF', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMG', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMH', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMI', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMJ', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMK', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCML', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMM', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMN', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMO', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMP', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMQ', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMR', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMS', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMT', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMU', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMV', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMW', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ33TVUWJCMX', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMY', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMZ', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM01', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM11', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM21', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM31', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM41', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM51', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM61', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM71', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM81', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCM91', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMA1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMB1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMC1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMD1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCME1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMF1', 'FINST-X3766I914TRWPBM0ETNHJ8PJCZVJ34TVUWJCMG1']
2025-07-01 10:30:49,498 - INFO - 批量插入响应状态码: 200
2025-07-01 10:30:49,498 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 02:30:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1706', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '006A0FD9-E932-7B5D-9618-8336B5362387', 'x-acs-trace-id': '5e2deee4f5cd5ebf9214c1417156cfda', 'etag': '1cGFzwR0tTh9BNCTZWeodxA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 10:30:49,498 - INFO - 批量插入响应体: {'result': ['FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM2', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM3', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM4', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM5', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM6', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM7', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM8', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM9', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMA', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMB', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMC', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMD', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCME', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMF', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMG', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMH', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMI', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMJ', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMK', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCML', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMM', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMN', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMO', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMP', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMQ', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMR', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMS', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMT', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMU', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMV', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMW', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMX', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMY', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMZ', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM01', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM11']}
2025-07-01 10:30:49,498 - INFO - 批量插入表单数据成功，批次 2，共 36 条记录
2025-07-01 10:30:49,498 - INFO - 成功插入的数据ID: ['FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM2', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM3', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM4', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM5', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM6', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM7', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM8', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM9', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMA', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMB', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMC', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMD', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCME', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMF', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMG', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMH', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMI', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMJ', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMK', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCML', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMM', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMN', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMO', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMP', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMQ', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMR', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMS', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMT', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMU', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMV', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMW', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMX', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMY', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMZ', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM01', 'FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCM11']
2025-07-01 10:30:54,514 - INFO - 批量插入完成，共 86 条记录
2025-07-01 10:30:54,514 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 86 条，错误: 0 条
2025-07-01 10:30:54,514 - INFO - 数据同步完成！更新: 0 条，插入: 86 条，错误: 0 条
2025-07-01 10:31:54,529 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 10:31:54,529 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 10:31:54,529 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 10:31:54,670 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 336 条记录
2025-07-01 10:31:54,670 - INFO - 获取到 1 个日期需要处理: ['2025-06-30']
2025-07-01 10:31:54,670 - INFO - 开始处理日期: 2025-06-30
2025-07-01 10:31:54,670 - INFO - Request Parameters - Page 1:
2025-07-01 10:31:54,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 10:31:54,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 10:32:02,795 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 852FA090-20DA-761A-A8A0-EC9D44F4EC48 Response: {'code': 'ServiceUnavailable', 'requestid': '852FA090-20DA-761A-A8A0-EC9D44F4EC48', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 852FA090-20DA-761A-A8A0-EC9D44F4EC48)
2025-07-01 10:32:02,795 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-01 10:32:02,795 - INFO - 同步完成
2025-07-01 13:30:34,053 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 13:30:34,053 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 13:30:34,053 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 13:30:34,194 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 163 条记录
2025-07-01 13:30:34,194 - INFO - 获取到 6 个日期需要处理: ['2025-06-05', '2025-06-17', '2025-06-27', '2025-06-28', '2025-06-30', '2025-07-01']
2025-07-01 13:30:34,194 - INFO - 开始处理日期: 2025-06-05
2025-07-01 13:30:34,194 - INFO - Request Parameters - Page 1:
2025-07-01 13:30:34,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:34,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749052800000, 1749139199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:40,225 - INFO - Response - Page 1:
2025-07-01 13:30:40,225 - INFO - 第 1 页获取到 50 条记录
2025-07-01 13:30:40,741 - INFO - Request Parameters - Page 2:
2025-07-01 13:30:40,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:40,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749052800000, 1749139199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:48,850 - ERROR - 处理日期 2025-06-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EA1C03CD-6B27-7870-B0BD-C47E7065DFF7 Response: {'code': 'ServiceUnavailable', 'requestid': 'EA1C03CD-6B27-7870-B0BD-C47E7065DFF7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EA1C03CD-6B27-7870-B0BD-C47E7065DFF7)
2025-07-01 13:30:48,850 - INFO - 开始处理日期: 2025-06-17
2025-07-01 13:30:48,850 - INFO - Request Parameters - Page 1:
2025-07-01 13:30:48,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:48,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:50,428 - INFO - Response - Page 1:
2025-07-01 13:30:50,428 - INFO - 第 1 页获取到 50 条记录
2025-07-01 13:30:50,944 - INFO - Request Parameters - Page 2:
2025-07-01 13:30:50,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:50,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:51,631 - INFO - Response - Page 2:
2025-07-01 13:30:51,631 - INFO - 第 2 页获取到 50 条记录
2025-07-01 13:30:52,147 - INFO - Request Parameters - Page 3:
2025-07-01 13:30:52,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:52,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:52,819 - INFO - Response - Page 3:
2025-07-01 13:30:52,819 - INFO - 第 3 页获取到 50 条记录
2025-07-01 13:30:53,334 - INFO - Request Parameters - Page 4:
2025-07-01 13:30:53,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:53,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:54,037 - INFO - Response - Page 4:
2025-07-01 13:30:54,037 - INFO - 第 4 页获取到 50 条记录
2025-07-01 13:30:54,537 - INFO - Request Parameters - Page 5:
2025-07-01 13:30:54,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:54,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:55,178 - INFO - Response - Page 5:
2025-07-01 13:30:55,178 - INFO - 第 5 页获取到 50 条记录
2025-07-01 13:30:55,694 - INFO - Request Parameters - Page 6:
2025-07-01 13:30:55,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:55,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:56,397 - INFO - Response - Page 6:
2025-07-01 13:30:56,397 - INFO - 第 6 页获取到 50 条记录
2025-07-01 13:30:56,912 - INFO - Request Parameters - Page 7:
2025-07-01 13:30:56,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:56,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:57,615 - INFO - Response - Page 7:
2025-07-01 13:30:57,615 - INFO - 第 7 页获取到 50 条记录
2025-07-01 13:30:58,131 - INFO - Request Parameters - Page 8:
2025-07-01 13:30:58,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:58,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:58,834 - INFO - Response - Page 8:
2025-07-01 13:30:58,834 - INFO - 第 8 页获取到 50 条记录
2025-07-01 13:30:59,350 - INFO - Request Parameters - Page 9:
2025-07-01 13:30:59,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:30:59,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:30:59,944 - INFO - Response - Page 9:
2025-07-01 13:30:59,944 - INFO - 第 9 页获取到 50 条记录
2025-07-01 13:31:00,444 - INFO - Request Parameters - Page 10:
2025-07-01 13:31:00,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:00,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:01,084 - INFO - Response - Page 10:
2025-07-01 13:31:01,084 - INFO - 第 10 页获取到 50 条记录
2025-07-01 13:31:01,584 - INFO - Request Parameters - Page 11:
2025-07-01 13:31:01,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:01,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:02,225 - INFO - Response - Page 11:
2025-07-01 13:31:02,225 - INFO - 第 11 页获取到 38 条记录
2025-07-01 13:31:02,740 - INFO - 查询完成，共获取到 538 条记录
2025-07-01 13:31:02,740 - INFO - 获取到 538 条表单数据
2025-07-01 13:31:02,740 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-07-01 13:31:02,740 - INFO - 开始更新记录 - 表单实例ID: FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMO8
2025-07-01 13:31:03,272 - INFO - 更新表单数据成功: FINST-NS766991GVDWNDYGDKYG9D6Z8NEK387Z4C1CMO8
2025-07-01 13:31:03,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5740.2, 'new_value': 2279.6}, {'field': 'total_amount', 'old_value': 5740.2, 'new_value': 2279.6}, {'field': 'order_count', 'old_value': 129, 'new_value': 62}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/5f599053436f497896dd8aa392105ad3.jpg?Expires=2060151182&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=A9JZ2ksXNA2l7YDo6UZQdGG%2FKSE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/b26f3b19234f40cbb8d5f7eee681b6fa.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=v9Vy5M8dhezDbDDG5zpUdC2DpFs%3D'}]
2025-07-01 13:31:03,272 - INFO - 日期 2025-06-17 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-01 13:31:03,272 - INFO - 开始处理日期: 2025-06-27
2025-07-01 13:31:03,272 - INFO - Request Parameters - Page 1:
2025-07-01 13:31:03,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:03,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:03,897 - INFO - Response - Page 1:
2025-07-01 13:31:03,897 - INFO - 第 1 页获取到 50 条记录
2025-07-01 13:31:04,412 - INFO - Request Parameters - Page 2:
2025-07-01 13:31:04,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:04,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:05,100 - INFO - Response - Page 2:
2025-07-01 13:31:05,100 - INFO - 第 2 页获取到 50 条记录
2025-07-01 13:31:05,615 - INFO - Request Parameters - Page 3:
2025-07-01 13:31:05,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:05,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:06,287 - INFO - Response - Page 3:
2025-07-01 13:31:06,287 - INFO - 第 3 页获取到 50 条记录
2025-07-01 13:31:06,803 - INFO - Request Parameters - Page 4:
2025-07-01 13:31:06,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:06,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:07,475 - INFO - Response - Page 4:
2025-07-01 13:31:07,475 - INFO - 第 4 页获取到 50 条记录
2025-07-01 13:31:07,990 - INFO - Request Parameters - Page 5:
2025-07-01 13:31:07,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:07,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:08,631 - INFO - Response - Page 5:
2025-07-01 13:31:08,631 - INFO - 第 5 页获取到 50 条记录
2025-07-01 13:31:09,131 - INFO - Request Parameters - Page 6:
2025-07-01 13:31:09,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:09,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:09,756 - INFO - Response - Page 6:
2025-07-01 13:31:09,756 - INFO - 第 6 页获取到 50 条记录
2025-07-01 13:31:10,256 - INFO - Request Parameters - Page 7:
2025-07-01 13:31:10,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:10,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:10,912 - INFO - Response - Page 7:
2025-07-01 13:31:10,912 - INFO - 第 7 页获取到 50 条记录
2025-07-01 13:31:11,428 - INFO - Request Parameters - Page 8:
2025-07-01 13:31:11,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:11,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:12,115 - INFO - Response - Page 8:
2025-07-01 13:31:12,115 - INFO - 第 8 页获取到 50 条记录
2025-07-01 13:31:12,631 - INFO - Request Parameters - Page 9:
2025-07-01 13:31:12,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:12,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:13,240 - INFO - Response - Page 9:
2025-07-01 13:31:13,240 - INFO - 第 9 页获取到 50 条记录
2025-07-01 13:31:13,740 - INFO - Request Parameters - Page 10:
2025-07-01 13:31:13,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:13,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:14,334 - INFO - Response - Page 10:
2025-07-01 13:31:14,334 - INFO - 第 10 页获取到 27 条记录
2025-07-01 13:31:14,850 - INFO - 查询完成，共获取到 477 条记录
2025-07-01 13:31:14,850 - INFO - 获取到 477 条表单数据
2025-07-01 13:31:14,850 - INFO - 当前日期 2025-06-27 有 1 条MySQL数据需要处理
2025-07-01 13:31:14,850 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM58
2025-07-01 13:31:15,319 - INFO - 更新表单数据成功: FINST-DOA66K91V8OWASIV7TVZT7NR5G6M2P56LMFCM58
2025-07-01 13:31:15,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23960.0, 'new_value': 32352.0}, {'field': 'total_amount', 'old_value': 23960.0, 'new_value': 32352.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1d6f0691c98e4be4a340e5efd59a1578.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=o3eR3Ehp%2FYk%2FJBoOLCiOYy7fr0g%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/507b8f89f68f4e01b96acc07f9a985e8.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=IhF8WpyM6ONrZ%2BiWPl6vYVaKE%2Bs%3D'}]
2025-07-01 13:31:15,319 - INFO - 日期 2025-06-27 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-01 13:31:15,319 - INFO - 开始处理日期: 2025-06-28
2025-07-01 13:31:15,319 - INFO - Request Parameters - Page 1:
2025-07-01 13:31:15,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:15,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:16,006 - INFO - Response - Page 1:
2025-07-01 13:31:16,006 - INFO - 第 1 页获取到 50 条记录
2025-07-01 13:31:16,522 - INFO - Request Parameters - Page 2:
2025-07-01 13:31:16,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:16,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:17,193 - INFO - Response - Page 2:
2025-07-01 13:31:17,193 - INFO - 第 2 页获取到 50 条记录
2025-07-01 13:31:17,693 - INFO - Request Parameters - Page 3:
2025-07-01 13:31:17,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:17,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:18,350 - INFO - Response - Page 3:
2025-07-01 13:31:18,350 - INFO - 第 3 页获取到 50 条记录
2025-07-01 13:31:18,865 - INFO - Request Parameters - Page 4:
2025-07-01 13:31:18,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:18,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:19,553 - INFO - Response - Page 4:
2025-07-01 13:31:19,553 - INFO - 第 4 页获取到 50 条记录
2025-07-01 13:31:20,068 - INFO - Request Parameters - Page 5:
2025-07-01 13:31:20,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:20,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:20,725 - INFO - Response - Page 5:
2025-07-01 13:31:20,725 - INFO - 第 5 页获取到 50 条记录
2025-07-01 13:31:21,225 - INFO - Request Parameters - Page 6:
2025-07-01 13:31:21,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:21,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:21,881 - INFO - Response - Page 6:
2025-07-01 13:31:21,881 - INFO - 第 6 页获取到 50 条记录
2025-07-01 13:31:22,381 - INFO - Request Parameters - Page 7:
2025-07-01 13:31:22,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:22,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:23,068 - INFO - Response - Page 7:
2025-07-01 13:31:23,068 - INFO - 第 7 页获取到 50 条记录
2025-07-01 13:31:23,568 - INFO - Request Parameters - Page 8:
2025-07-01 13:31:23,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:23,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:24,365 - INFO - Response - Page 8:
2025-07-01 13:31:24,365 - INFO - 第 8 页获取到 50 条记录
2025-07-01 13:31:24,865 - INFO - Request Parameters - Page 9:
2025-07-01 13:31:24,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:24,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:25,522 - INFO - Response - Page 9:
2025-07-01 13:31:25,522 - INFO - 第 9 页获取到 50 条记录
2025-07-01 13:31:26,022 - INFO - Request Parameters - Page 10:
2025-07-01 13:31:26,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:26,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:26,631 - INFO - Response - Page 10:
2025-07-01 13:31:26,631 - INFO - 第 10 页获取到 29 条记录
2025-07-01 13:31:27,147 - INFO - 查询完成，共获取到 479 条记录
2025-07-01 13:31:27,147 - INFO - 获取到 479 条表单数据
2025-07-01 13:31:27,147 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-07-01 13:31:27,147 - INFO - 开始更新记录 - 表单实例ID: FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMBQ
2025-07-01 13:31:27,709 - INFO - 更新表单数据成功: FINST-YPE66RB1V4OWJFS6CRFL0BQNJIZ93DYGZ1HCMBQ
2025-07-01 13:31:27,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 19630.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 19630.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/a350d7209a5347e6b440d93500654944.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=YmwY0WdFKU9fir13nL4jQjj5WBA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/a4b146cf9408440a9ec806bce060128f.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=oMlydG2RhwmhrPGLzApc%2FT5Y8d8%3D'}]
2025-07-01 13:31:27,709 - INFO - 开始更新记录 - 表单实例ID: FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMXW
2025-07-01 13:31:28,287 - INFO - 更新表单数据成功: FINST-IQG66AD1Y4OWP04EAE9V8A1P0XBQ2ZVCZ1HCMXW
2025-07-01 13:31:28,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50240.0, 'new_value': 64704.0}, {'field': 'total_amount', 'old_value': 50240.0, 'new_value': 64704.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e3eca4c2d61b453d85ce0d36f8c451d3.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=TbsiPAzhtkqIQ8ldWudNtP3C61U%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1dfd14b89c004a14ac2f3d31320c9804.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=88uHRdcq%2BiDnE2Hf%2FdjHLgLJFrg%3D'}]
2025-07-01 13:31:28,287 - INFO - 日期 2025-06-28 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-07-01 13:31:28,287 - INFO - 开始处理日期: 2025-06-30
2025-07-01 13:31:28,287 - INFO - Request Parameters - Page 1:
2025-07-01 13:31:28,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:28,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:28,990 - INFO - Response - Page 1:
2025-07-01 13:31:28,990 - INFO - 第 1 页获取到 50 条记录
2025-07-01 13:31:29,506 - INFO - Request Parameters - Page 2:
2025-07-01 13:31:29,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:29,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:30,162 - INFO - Response - Page 2:
2025-07-01 13:31:30,162 - INFO - 第 2 页获取到 50 条记录
2025-07-01 13:31:30,678 - INFO - Request Parameters - Page 3:
2025-07-01 13:31:30,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:30,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:31,303 - INFO - Response - Page 3:
2025-07-01 13:31:31,303 - INFO - 第 3 页获取到 50 条记录
2025-07-01 13:31:31,818 - INFO - Request Parameters - Page 4:
2025-07-01 13:31:31,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:31,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:32,443 - INFO - Response - Page 4:
2025-07-01 13:31:32,443 - INFO - 第 4 页获取到 43 条记录
2025-07-01 13:31:32,943 - INFO - 查询完成，共获取到 193 条记录
2025-07-01 13:31:32,943 - INFO - 获取到 193 条表单数据
2025-07-01 13:31:32,943 - INFO - 当前日期 2025-06-30 有 153 条MySQL数据需要处理
2025-07-01 13:31:32,943 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMQ4
2025-07-01 13:31:33,490 - INFO - 更新表单数据成功: FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMQ4
2025-07-01 13:31:33,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35566.0, 'new_value': 75566.0}, {'field': 'offline_amount', 'old_value': 12122.0, 'new_value': 22122.0}, {'field': 'total_amount', 'old_value': 47688.0, 'new_value': 97688.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 53}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/fef282744a57474c9251a44a7cafa6ff.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=W%2F19%2FePf0ku3AU0bRLyJjiy6nxI%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1204c704597b4ea0ac792a957ee2ec87.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=CMQxRux9S1%2F5oGwQj4eCHLVPxRo%3D'}]
2025-07-01 13:31:33,490 - INFO - 开始更新记录 - 表单实例ID: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMEL
2025-07-01 13:31:34,037 - INFO - 更新表单数据成功: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMEL
2025-07-01 13:31:34,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 58700.0}, {'field': 'offline_amount', 'old_value': 8700.0, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 8700.0, 'new_value': 58700.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/979eb1c3c03c4d6eb194a77b47004856.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=9OPnt%2FewJvKcMKbN%2FDA809hdxPo%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/fb37c2dd234e4e289e6b1280e313afec.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=9xTCrN14pPpGjGLzdJscIyAQ988%3D'}]
2025-07-01 13:31:34,037 - INFO - 开始更新记录 - 表单实例ID: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMGL
2025-07-01 13:31:34,740 - INFO - 更新表单数据成功: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMGL
2025-07-01 13:31:34,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 9800.0, 'new_value': 39800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 14}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/055034bd0de34fd7a576b68b6683cf7b.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=EASx2kEaakNIHAyfRfg%2F98XwoiU%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/34dd5f844f83488eb1c35dcbb5d25daf.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=9XpLmqsLJ%2FK0gNiF0J991t2KQ2E%3D'}]
2025-07-01 13:31:34,740 - INFO - 开始更新记录 - 表单实例ID: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMFL
2025-07-01 13:31:35,256 - INFO - 更新表单数据成功: FINST-XL666BD1SNOWSYF5FWWOXAH9XN4W3R5B1KJCMFL
2025-07-01 13:31:35,256 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 7900.0, 'new_value': 37900.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e1e7561d4c664ccd8f40408fbc95ddc0.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=eHqX7mnAY37c9ZaoXiAFZn%2BwPJc%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/6b3377c5b25245b48bd6953502751907.jpg?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=26KK5byjCw8u0ORLoe6Hg33D89k%3D'}]
2025-07-01 13:31:35,256 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCML4
2025-07-01 13:31:35,818 - INFO - 更新表单数据成功: FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCML4
2025-07-01 13:31:35,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 45000.0}, {'field': 'total_amount', 'old_value': 680.0, 'new_value': 45680.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/d1b4505ba006475b8eb4ed3dbfc88909.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=rF25oFdrnA6t7c9z35l0h7Zmo60%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e432a77b31cb4314b4a3623a3e018a70.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=vZ2WXRNzy%2BZnBlueqBb7GuJzXiA%3D'}]
2025-07-01 13:31:35,818 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMR
2025-07-01 13:31:36,350 - INFO - 更新表单数据成功: FINST-V7966QC13QRWFSBIB859G7TJEO6P2LUZUWJCMR
2025-07-01 13:31:36,350 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 31012, 'new_value': 3101}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9ac4f0160be648dba328ae74821d4f48.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=EtXUI7iHPmAMELt0cEnuQyWCmy0%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/6fea3d4317494d78901a1d56d060295f.png?Expires=2066284449&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=aMG4EmdkCBvx1CALXtVImNQUWmM%3D'}]
2025-07-01 13:31:36,350 - INFO - 开始批量插入 61 条新记录
2025-07-01 13:31:36,600 - INFO - 批量插入响应状态码: 200
2025-07-01 13:31:36,600 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:31:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '68A70E4E-D68A-7A8C-997F-671313EE9726', 'x-acs-trace-id': 'ffff2fdf772ba40dd01e1120441f1003', 'etag': '2ElW9j+sUShXJyFD2geooxg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:31:36,600 - INFO - 批量插入响应体: {'result': ['FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMF4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMG4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMH4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMI4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMJ4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMK4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCML4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMM4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMN4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMO4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMP4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMQ4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMR4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMS4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMT4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMU4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMV4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMW4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMX4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMY4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMZ4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM05', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM15', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM25', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM35', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM45', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM55', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM65', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM75', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM85', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM95', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMA5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMB5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMC5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMD5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCME5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMF5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMG5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMH5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMI5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMJ5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMK5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCML5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMM5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMN5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMO5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMP5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMQ5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMR5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMS5']}
2025-07-01 13:31:36,600 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-01 13:31:36,600 - INFO - 成功插入的数据ID: ['FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMF4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMG4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMH4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMI4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMJ4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMK4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCML4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMM4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMN4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMO4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMP4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMQ4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMR4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMS4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMT4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMU4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMV4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMW4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMX4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMY4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMZ4', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM05', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM15', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM25', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM35', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM45', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM55', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM65', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM75', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM85', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCM95', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMA5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMB5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMC5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMD5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCME5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMF5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN23LHB3KCMG5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMH5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMI5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMJ5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMK5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCML5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMM5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMN5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMO5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMP5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMQ5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMR5', 'FINST-VOC66Y91GQRWBNOICVLCQA5NONWN24LHB3KCMS5']
2025-07-01 13:31:41,771 - INFO - 批量插入响应状态码: 200
2025-07-01 13:31:41,771 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:31:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '529', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F1B69245-466C-7494-A901-9F14E888A31F', 'x-acs-trace-id': 'c226ee695187e919c440d210a5e9f7c6', 'etag': '5/RGkYsNu26mn1fxurHsPIQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:31:41,771 - INFO - 批量插入响应体: {'result': ['FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCM8', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCM9', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMA', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMB', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMC', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMD', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCME', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMF', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMG', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMH', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMI']}
2025-07-01 13:31:41,771 - INFO - 批量插入表单数据成功，批次 2，共 11 条记录
2025-07-01 13:31:41,771 - INFO - 成功插入的数据ID: ['FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCM8', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCM9', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMA', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMB', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMC', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMD', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCME', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMF', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMG', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMH', 'FINST-MLF662B1WTRWIUWK9LCE4BL1D60P3UKLB3KCMI']
2025-07-01 13:31:46,787 - INFO - 批量插入完成，共 61 条记录
2025-07-01 13:31:46,787 - INFO - 日期 2025-06-30 处理完成 - 更新: 6 条，插入: 61 条，错误: 0 条
2025-07-01 13:31:46,787 - INFO - 开始处理日期: 2025-07-01
2025-07-01 13:31:46,787 - INFO - Request Parameters - Page 1:
2025-07-01 13:31:46,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:31:46,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:31:47,193 - INFO - Response - Page 1:
2025-07-01 13:31:47,193 - INFO - 查询完成，共获取到 0 条记录
2025-07-01 13:31:47,193 - INFO - 获取到 0 条表单数据
2025-07-01 13:31:47,193 - INFO - 当前日期 2025-07-01 有 1 条MySQL数据需要处理
2025-07-01 13:31:47,193 - INFO - 开始批量插入 1 条新记录
2025-07-01 13:31:47,350 - INFO - 批量插入响应状态码: 200
2025-07-01 13:31:47,350 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:31:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8BAE971E-4069-7128-8893-D069B30E63DA', 'x-acs-trace-id': 'c6578cdff14a74a22435eb7560921097', 'etag': '5wlxiPFtR1sj8uOsrSpteRA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:31:47,350 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA10URW1G7XAD31N86IEPER3XVPB3KCMG']}
2025-07-01 13:31:47,350 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-01 13:31:47,350 - INFO - 成功插入的数据ID: ['FINST-FD966QA10URW1G7XAD31N86IEPER3XVPB3KCMG']
2025-07-01 13:31:52,365 - INFO - 批量插入完成，共 1 条记录
2025-07-01 13:31:52,365 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-01 13:31:52,365 - INFO - 数据同步完成！更新: 10 条，插入: 62 条，错误: 1 条
2025-07-01 13:32:52,380 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 13:32:52,380 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 13:32:52,380 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 13:32:52,521 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 516 条记录
2025-07-01 13:32:52,521 - INFO - 获取到 2 个日期需要处理: ['2025-06-30', '2025-07-01']
2025-07-01 13:32:52,537 - INFO - 开始处理日期: 2025-06-30
2025-07-01 13:32:52,537 - INFO - Request Parameters - Page 1:
2025-07-01 13:32:52,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:32:52,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:32:53,240 - INFO - Response - Page 1:
2025-07-01 13:32:53,240 - INFO - 第 1 页获取到 50 条记录
2025-07-01 13:32:53,755 - INFO - Request Parameters - Page 2:
2025-07-01 13:32:53,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:32:53,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:32:54,474 - INFO - Response - Page 2:
2025-07-01 13:32:54,474 - INFO - 第 2 页获取到 50 条记录
2025-07-01 13:32:54,990 - INFO - Request Parameters - Page 3:
2025-07-01 13:32:54,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:32:54,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:32:55,615 - INFO - Response - Page 3:
2025-07-01 13:32:55,615 - INFO - 第 3 页获取到 50 条记录
2025-07-01 13:32:56,115 - INFO - Request Parameters - Page 4:
2025-07-01 13:32:56,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:32:56,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:32:56,755 - INFO - Response - Page 4:
2025-07-01 13:32:56,755 - INFO - 第 4 页获取到 50 条记录
2025-07-01 13:32:57,255 - INFO - Request Parameters - Page 5:
2025-07-01 13:32:57,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:32:57,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:32:57,912 - INFO - Response - Page 5:
2025-07-01 13:32:57,912 - INFO - 第 5 页获取到 50 条记录
2025-07-01 13:32:58,412 - INFO - Request Parameters - Page 6:
2025-07-01 13:32:58,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:32:58,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:32:58,927 - INFO - Response - Page 6:
2025-07-01 13:32:58,927 - INFO - 第 6 页获取到 4 条记录
2025-07-01 13:32:59,443 - INFO - 查询完成，共获取到 254 条记录
2025-07-01 13:32:59,443 - INFO - 获取到 254 条表单数据
2025-07-01 13:32:59,443 - INFO - 当前日期 2025-06-30 有 504 条MySQL数据需要处理
2025-07-01 13:32:59,443 - INFO - 开始批量插入 250 条新记录
2025-07-01 13:32:59,693 - INFO - 批量插入响应状态码: 200
2025-07-01 13:32:59,693 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:32:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8EA55DF7-5F30-712D-8DF6-ED9CA159BAE6', 'x-acs-trace-id': '15e308b3d42253ebd0f16f81ed57612e', 'etag': '2KdGjTLHBzS+FBXR7cSrmdw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:32:59,693 - INFO - 批量插入响应体: {'result': ['FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM31', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM41', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM51', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM61', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM71', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM81', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM91', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMA1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMB1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMC1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMD1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCME1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMF1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMG1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMH1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMI1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMJ1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMK1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCML1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMM1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMN1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMO1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMP1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMQ1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMR1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMS1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMT1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMU1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMV1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMW1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMX1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMY1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMZ1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM02', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM12', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM22', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM32', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM42', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM52', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM62', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM72', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM82', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM92', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMA2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMB2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMC2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMD2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCME2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMF2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMG2']}
2025-07-01 13:32:59,693 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-01 13:32:59,693 - INFO - 成功插入的数据ID: ['FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM31', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM41', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM51', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM61', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM71', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM81', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM91', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMA1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMB1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMC1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMD1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCME1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMF1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMG1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMH1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMI1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMJ1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMK1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCML1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMM1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMN1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMO1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMP1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMQ1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMR1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMS1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMT1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMU1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMV1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMW1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMX1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMY1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMZ1', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM02', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM12', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM22', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM32', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM42', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM52', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM62', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM72', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM82', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCM92', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMA2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMB2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMC2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMD2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCME2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMF2', 'FINST-OY8665C10QRW7PYC9MBOIC6NAJ9L3BP9D3KCMG2']
2025-07-01 13:33:04,943 - INFO - 批量插入响应状态码: 200
2025-07-01 13:33:04,943 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:33:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2404', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2471EF0-23EA-7C71-9228-A648D96C306B', 'x-acs-trace-id': '22b1e8a797e9315b8bd2c644cd478445', 'etag': '2fOGiT2W9NaeWGn/Pvoe8Eg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:33:04,943 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMS', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMT', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMU', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMV', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMW', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMX', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMY', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMZ', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM01', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM11', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM21', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM31', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM41', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM51', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM61', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM71', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM81', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM91', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMA1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMB1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMC1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMD1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCME1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMF1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMG1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMH1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMI1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMJ1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMK1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCML1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMM1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMN1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMO1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMP1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMQ1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMR1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMS1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMT1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMU1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMV1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMW1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMX1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMY1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMZ1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM02', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM12', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM22', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM32', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM42', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM52']}
2025-07-01 13:33:04,943 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-01 13:33:04,943 - INFO - 成功插入的数据ID: ['FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMS', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMT', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMU', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMV', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMW', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMX', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N928RDD3KCMY', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMZ', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM01', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM11', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM21', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM31', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM41', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM51', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM61', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM71', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM81', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM91', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMA1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMB1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMC1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMD1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCME1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMF1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMG1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMH1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMI1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMJ1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMK1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCML1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMM1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMN1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMO1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMP1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMQ1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMR1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMS1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMT1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMU1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMV1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMW1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMX1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMY1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCMZ1', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM02', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM12', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM22', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM32', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM42', 'FINST-E3G66QA1PVRWNFV67POSK97Y25N929RDD3KCM52']
2025-07-01 13:33:10,193 - INFO - 批量插入响应状态码: 200
2025-07-01 13:33:10,193 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:33:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2380', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DECC18F9-2A09-776E-84FD-1ADDF65D9B5B', 'x-acs-trace-id': 'bf3e055cbf8ad02e5296a21219681d08', 'etag': '2EIFUXk3F9qUkTdQGn2d6Mg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:33:10,193 - INFO - 批量插入响应体: {'result': ['FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM4', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM5', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM6', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM7', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM8', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM9', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMA', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMB', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMC', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMD', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCME', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMF', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMG', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMH', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMI', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMJ', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMK', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCML', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMM', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMN', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMO', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMP', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMQ', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMR', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMS', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMT', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMU', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMV', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMW', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMX', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMY', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMZ', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM01', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM11', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM21', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM31', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM41', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM51', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM61', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM71', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM81', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM91', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMA1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMB1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMC1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMD1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCME1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMF1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMG1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMH1']}
2025-07-01 13:33:10,193 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-01 13:33:10,193 - INFO - 成功插入的数据ID: ['FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM4', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM5', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM6', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM7', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM8', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM9', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMA', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMB', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMC', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMD', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCME', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMF', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMG', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMH', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMI', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMJ', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMK', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCML', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMM', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMN', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMO', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMP', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMQ', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMR', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMS', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMT', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMU', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMV', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMW', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMX', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMY', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMZ', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM01', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM11', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM21', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM31', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM41', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM51', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM61', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM71', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM81', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCM91', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMA1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMB1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMC1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMD1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCME1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMF1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMG1', 'FINST-F3G66Q61XTRW321FCZ5E27RK3LRJ3TSHD3KCMH1']
2025-07-01 13:33:15,427 - INFO - 批量插入响应状态码: 200
2025-07-01 13:33:15,427 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:33:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '81810A19-D46D-7E3E-B90E-639319D63A07', 'x-acs-trace-id': '11e707e0339dc6a2a16e29119389b537', 'etag': '2qFbdlyGkaa2iLSOHi4wNPA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:33:15,427 - INFO - 批量插入响应体: {'result': ['FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM55', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM65', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM75', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM85', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM95', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMA5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMB5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMC5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMD5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCME5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMF5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMG5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMH5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMI5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMJ5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMK5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCML5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMM5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMN5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMO5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMP5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMQ5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMR5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMS5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMT5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMU5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMV5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMW5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMX5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMY5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMZ5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM06', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM16', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM26', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM36', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM46', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM56', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM66', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM76', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM86', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM96', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMA6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMB6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMC6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMD6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCME6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMF6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMG6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMH6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMI6']}
2025-07-01 13:33:15,427 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-01 13:33:15,427 - INFO - 成功插入的数据ID: ['FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM55', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM65', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM75', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM85', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM95', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMA5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMB5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMC5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMD5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCME5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMF5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMG5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMH5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMI5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMJ5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMK5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCML5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMM5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMN5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMO5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMP5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMQ5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMR5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMS5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMT5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMU5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMV5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMW5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMX5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMY5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMZ5', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM06', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM16', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM26', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM36', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM46', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM56', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM66', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM76', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM86', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCM96', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMA6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMB6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMC6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMD6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCME6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMF6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMG6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMH6', 'FINST-3RE66ZB1ODRWFCUTEFOPEDR8EWT027ULD3KCMI6']
2025-07-01 13:33:20,661 - INFO - 批量插入响应状态码: 200
2025-07-01 13:33:20,661 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 05:33:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2387', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '010DBE6F-495D-7CC5-98C8-D8416D7372BB', 'x-acs-trace-id': '7d4b9ee2addaf4518a14d1e12f8cceb4', 'etag': '2/4Wk92UESG0yOI0vLVSosQ7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 13:33:20,661 - INFO - 批量插入响应体: {'result': ['FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMB', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMC', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMD', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCME', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMF', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMG', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMH', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMI', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMJ', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMK', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCML', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMM', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMN', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMO', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMP', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMQ', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMR', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMS', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMT', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMU', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMV', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMW', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMX', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMY', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMZ', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM01', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM11', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM21', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM31', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM41', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM51', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM61', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM71', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM81', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM91', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMA1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMB1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMC1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMD1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCME1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMF1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMG1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMH1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMI1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMJ1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMK1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCML1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMM1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMN1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMO1']}
2025-07-01 13:33:20,661 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-01 13:33:20,661 - INFO - 成功插入的数据ID: ['FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMB', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMC', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMD', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCME', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMF', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMG', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMH', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMI', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMJ', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMK', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCML', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMM', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMN', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMO', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMP', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMQ', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMR', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMS', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMT', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMU', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMV', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMW', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMX', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMY', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMZ', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM01', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM11', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM21', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM31', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM41', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM51', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM61', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM71', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM81', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM91', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMA1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMB1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMC1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMD1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCME1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMF1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMG1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMH1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMI1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMJ1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMK1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCML1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMM1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMN1', 'FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCMO1']
2025-07-01 13:33:25,677 - INFO - 批量插入完成，共 250 条记录
2025-07-01 13:33:25,677 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 250 条，错误: 0 条
2025-07-01 13:33:25,677 - INFO - 开始处理日期: 2025-07-01
2025-07-01 13:33:25,677 - INFO - Request Parameters - Page 1:
2025-07-01 13:33:25,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 13:33:25,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 13:33:26,115 - INFO - Response - Page 1:
2025-07-01 13:33:26,115 - INFO - 第 1 页获取到 1 条记录
2025-07-01 13:33:26,630 - INFO - 查询完成，共获取到 1 条记录
2025-07-01 13:33:26,630 - INFO - 获取到 1 条表单数据
2025-07-01 13:33:26,630 - INFO - 当前日期 2025-07-01 有 1 条MySQL数据需要处理
2025-07-01 13:33:26,630 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 13:33:26,630 - INFO - 数据同步完成！更新: 0 条，插入: 250 条，错误: 0 条
2025-07-01 13:33:26,630 - INFO - 同步完成
2025-07-01 16:30:33,581 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 16:30:33,581 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 16:30:33,581 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 16:30:33,722 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 167 条记录
2025-07-01 16:30:33,722 - INFO - 获取到 6 个日期需要处理: ['2025-06-05', '2025-06-17', '2025-06-27', '2025-06-28', '2025-06-30', '2025-07-01']
2025-07-01 16:30:33,722 - INFO - 开始处理日期: 2025-06-05
2025-07-01 16:30:33,737 - INFO - Request Parameters - Page 1:
2025-07-01 16:30:33,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:33,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749052800000, 1749139199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:41,847 - ERROR - 处理日期 2025-06-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B92EE7EE-A5D0-72CD-A948-D1E5ECB13D1C Response: {'code': 'ServiceUnavailable', 'requestid': 'B92EE7EE-A5D0-72CD-A948-D1E5ECB13D1C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B92EE7EE-A5D0-72CD-A948-D1E5ECB13D1C)
2025-07-01 16:30:41,847 - INFO - 开始处理日期: 2025-06-17
2025-07-01 16:30:41,847 - INFO - Request Parameters - Page 1:
2025-07-01 16:30:41,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:41,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:48,175 - INFO - Response - Page 1:
2025-07-01 16:30:48,175 - INFO - 第 1 页获取到 50 条记录
2025-07-01 16:30:48,675 - INFO - Request Parameters - Page 2:
2025-07-01 16:30:48,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:48,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:49,362 - INFO - Response - Page 2:
2025-07-01 16:30:49,362 - INFO - 第 2 页获取到 50 条记录
2025-07-01 16:30:49,878 - INFO - Request Parameters - Page 3:
2025-07-01 16:30:49,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:49,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:50,581 - INFO - Response - Page 3:
2025-07-01 16:30:50,581 - INFO - 第 3 页获取到 50 条记录
2025-07-01 16:30:51,097 - INFO - Request Parameters - Page 4:
2025-07-01 16:30:51,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:51,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:51,862 - INFO - Response - Page 4:
2025-07-01 16:30:51,862 - INFO - 第 4 页获取到 50 条记录
2025-07-01 16:30:52,362 - INFO - Request Parameters - Page 5:
2025-07-01 16:30:52,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:52,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:53,034 - INFO - Response - Page 5:
2025-07-01 16:30:53,034 - INFO - 第 5 页获取到 50 条记录
2025-07-01 16:30:53,534 - INFO - Request Parameters - Page 6:
2025-07-01 16:30:53,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:53,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:54,253 - INFO - Response - Page 6:
2025-07-01 16:30:54,253 - INFO - 第 6 页获取到 50 条记录
2025-07-01 16:30:54,768 - INFO - Request Parameters - Page 7:
2025-07-01 16:30:54,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:54,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:55,456 - INFO - Response - Page 7:
2025-07-01 16:30:55,456 - INFO - 第 7 页获取到 50 条记录
2025-07-01 16:30:55,956 - INFO - Request Parameters - Page 8:
2025-07-01 16:30:55,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:55,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:56,690 - INFO - Response - Page 8:
2025-07-01 16:30:56,690 - INFO - 第 8 页获取到 50 条记录
2025-07-01 16:30:57,190 - INFO - Request Parameters - Page 9:
2025-07-01 16:30:57,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:57,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:57,831 - INFO - Response - Page 9:
2025-07-01 16:30:57,831 - INFO - 第 9 页获取到 50 条记录
2025-07-01 16:30:58,347 - INFO - Request Parameters - Page 10:
2025-07-01 16:30:58,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:58,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:30:59,003 - INFO - Response - Page 10:
2025-07-01 16:30:59,003 - INFO - 第 10 页获取到 50 条记录
2025-07-01 16:30:59,503 - INFO - Request Parameters - Page 11:
2025-07-01 16:30:59,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:30:59,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:00,143 - INFO - Response - Page 11:
2025-07-01 16:31:00,143 - INFO - 第 11 页获取到 38 条记录
2025-07-01 16:31:00,643 - INFO - 查询完成，共获取到 538 条记录
2025-07-01 16:31:00,643 - INFO - 获取到 538 条表单数据
2025-07-01 16:31:00,643 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-07-01 16:31:00,643 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 16:31:00,643 - INFO - 开始处理日期: 2025-06-27
2025-07-01 16:31:00,643 - INFO - Request Parameters - Page 1:
2025-07-01 16:31:00,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:00,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:01,331 - INFO - Response - Page 1:
2025-07-01 16:31:01,331 - INFO - 第 1 页获取到 50 条记录
2025-07-01 16:31:01,847 - INFO - Request Parameters - Page 2:
2025-07-01 16:31:01,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:01,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:02,487 - INFO - Response - Page 2:
2025-07-01 16:31:02,487 - INFO - 第 2 页获取到 50 条记录
2025-07-01 16:31:03,003 - INFO - Request Parameters - Page 3:
2025-07-01 16:31:03,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:03,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:03,706 - INFO - Response - Page 3:
2025-07-01 16:31:03,706 - INFO - 第 3 页获取到 50 条记录
2025-07-01 16:31:04,206 - INFO - Request Parameters - Page 4:
2025-07-01 16:31:04,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:04,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:04,847 - INFO - Response - Page 4:
2025-07-01 16:31:04,847 - INFO - 第 4 页获取到 50 条记录
2025-07-01 16:31:05,362 - INFO - Request Parameters - Page 5:
2025-07-01 16:31:05,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:05,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:05,987 - INFO - Response - Page 5:
2025-07-01 16:31:05,987 - INFO - 第 5 页获取到 50 条记录
2025-07-01 16:31:06,503 - INFO - Request Parameters - Page 6:
2025-07-01 16:31:06,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:06,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:07,190 - INFO - Response - Page 6:
2025-07-01 16:31:07,190 - INFO - 第 6 页获取到 50 条记录
2025-07-01 16:31:07,706 - INFO - Request Parameters - Page 7:
2025-07-01 16:31:07,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:07,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:08,347 - INFO - Response - Page 7:
2025-07-01 16:31:08,347 - INFO - 第 7 页获取到 50 条记录
2025-07-01 16:31:08,862 - INFO - Request Parameters - Page 8:
2025-07-01 16:31:08,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:08,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:09,534 - INFO - Response - Page 8:
2025-07-01 16:31:09,534 - INFO - 第 8 页获取到 50 条记录
2025-07-01 16:31:10,034 - INFO - Request Parameters - Page 9:
2025-07-01 16:31:10,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:10,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:10,659 - INFO - Response - Page 9:
2025-07-01 16:31:10,659 - INFO - 第 9 页获取到 50 条记录
2025-07-01 16:31:11,175 - INFO - Request Parameters - Page 10:
2025-07-01 16:31:11,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:11,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:11,847 - INFO - Response - Page 10:
2025-07-01 16:31:11,847 - INFO - 第 10 页获取到 27 条记录
2025-07-01 16:31:12,362 - INFO - 查询完成，共获取到 477 条记录
2025-07-01 16:31:12,362 - INFO - 获取到 477 条表单数据
2025-07-01 16:31:12,362 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-07-01 16:31:12,362 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMRG
2025-07-01 16:31:13,050 - INFO - 更新表单数据成功: FINST-KLF66WC1N8OWAGVTEHWVXC3RT9BU3A1YKMFCMRG
2025-07-01 16:31:13,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41169.0, 'new_value': 96169.0}, {'field': 'total_amount', 'old_value': 48386.0, 'new_value': 103386.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-01 16:31:13,050 - INFO - 日期 2025-06-27 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-01 16:31:13,050 - INFO - 开始处理日期: 2025-06-28
2025-07-01 16:31:13,050 - INFO - Request Parameters - Page 1:
2025-07-01 16:31:13,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:13,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:13,722 - INFO - Response - Page 1:
2025-07-01 16:31:13,722 - INFO - 第 1 页获取到 50 条记录
2025-07-01 16:31:14,237 - INFO - Request Parameters - Page 2:
2025-07-01 16:31:14,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:14,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:14,956 - INFO - Response - Page 2:
2025-07-01 16:31:14,956 - INFO - 第 2 页获取到 50 条记录
2025-07-01 16:31:15,472 - INFO - Request Parameters - Page 3:
2025-07-01 16:31:15,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:15,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:16,237 - INFO - Response - Page 3:
2025-07-01 16:31:16,237 - INFO - 第 3 页获取到 50 条记录
2025-07-01 16:31:16,753 - INFO - Request Parameters - Page 4:
2025-07-01 16:31:16,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:16,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:17,440 - INFO - Response - Page 4:
2025-07-01 16:31:17,440 - INFO - 第 4 页获取到 50 条记录
2025-07-01 16:31:17,956 - INFO - Request Parameters - Page 5:
2025-07-01 16:31:17,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:17,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:18,628 - INFO - Response - Page 5:
2025-07-01 16:31:18,628 - INFO - 第 5 页获取到 50 条记录
2025-07-01 16:31:19,143 - INFO - Request Parameters - Page 6:
2025-07-01 16:31:19,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:19,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:19,878 - INFO - Response - Page 6:
2025-07-01 16:31:19,878 - INFO - 第 6 页获取到 50 条记录
2025-07-01 16:31:20,393 - INFO - Request Parameters - Page 7:
2025-07-01 16:31:20,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:20,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:21,065 - INFO - Response - Page 7:
2025-07-01 16:31:21,065 - INFO - 第 7 页获取到 50 条记录
2025-07-01 16:31:21,581 - INFO - Request Parameters - Page 8:
2025-07-01 16:31:21,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:21,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:22,315 - INFO - Response - Page 8:
2025-07-01 16:31:22,315 - INFO - 第 8 页获取到 50 条记录
2025-07-01 16:31:22,815 - INFO - Request Parameters - Page 9:
2025-07-01 16:31:22,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:22,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:23,518 - INFO - Response - Page 9:
2025-07-01 16:31:23,518 - INFO - 第 9 页获取到 50 条记录
2025-07-01 16:31:24,034 - INFO - Request Parameters - Page 10:
2025-07-01 16:31:24,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:24,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:24,675 - INFO - Response - Page 10:
2025-07-01 16:31:24,675 - INFO - 第 10 页获取到 29 条记录
2025-07-01 16:31:25,175 - INFO - 查询完成，共获取到 479 条记录
2025-07-01 16:31:25,175 - INFO - 获取到 479 条表单数据
2025-07-01 16:31:25,175 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-07-01 16:31:25,175 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 16:31:25,175 - INFO - 开始处理日期: 2025-06-30
2025-07-01 16:31:25,175 - INFO - Request Parameters - Page 1:
2025-07-01 16:31:25,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:25,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:25,846 - INFO - Response - Page 1:
2025-07-01 16:31:25,862 - INFO - 第 1 页获取到 50 条记录
2025-07-01 16:31:26,378 - INFO - Request Parameters - Page 2:
2025-07-01 16:31:26,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:26,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:27,096 - INFO - Response - Page 2:
2025-07-01 16:31:27,096 - INFO - 第 2 页获取到 50 条记录
2025-07-01 16:31:27,612 - INFO - Request Parameters - Page 3:
2025-07-01 16:31:27,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:27,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:28,315 - INFO - Response - Page 3:
2025-07-01 16:31:28,315 - INFO - 第 3 页获取到 50 条记录
2025-07-01 16:31:28,815 - INFO - Request Parameters - Page 4:
2025-07-01 16:31:28,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:28,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:29,518 - INFO - Response - Page 4:
2025-07-01 16:31:29,518 - INFO - 第 4 页获取到 50 条记录
2025-07-01 16:31:30,034 - INFO - Request Parameters - Page 5:
2025-07-01 16:31:30,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:30,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:30,659 - INFO - Response - Page 5:
2025-07-01 16:31:30,659 - INFO - 第 5 页获取到 50 条记录
2025-07-01 16:31:31,175 - INFO - Request Parameters - Page 6:
2025-07-01 16:31:31,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:31,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:31,800 - INFO - Response - Page 6:
2025-07-01 16:31:31,800 - INFO - 第 6 页获取到 50 条记录
2025-07-01 16:31:32,315 - INFO - Request Parameters - Page 7:
2025-07-01 16:31:32,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:32,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:32,987 - INFO - Response - Page 7:
2025-07-01 16:31:32,987 - INFO - 第 7 页获取到 50 条记录
2025-07-01 16:31:33,487 - INFO - Request Parameters - Page 8:
2025-07-01 16:31:33,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:33,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:34,190 - INFO - Response - Page 8:
2025-07-01 16:31:34,190 - INFO - 第 8 页获取到 50 条记录
2025-07-01 16:31:34,706 - INFO - Request Parameters - Page 9:
2025-07-01 16:31:34,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:34,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:35,487 - INFO - Response - Page 9:
2025-07-01 16:31:35,487 - INFO - 第 9 页获取到 50 条记录
2025-07-01 16:31:35,987 - INFO - Request Parameters - Page 10:
2025-07-01 16:31:35,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:35,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:36,628 - INFO - Response - Page 10:
2025-07-01 16:31:36,628 - INFO - 第 10 页获取到 50 条记录
2025-07-01 16:31:37,143 - INFO - Request Parameters - Page 11:
2025-07-01 16:31:37,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:37,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:37,784 - INFO - Response - Page 11:
2025-07-01 16:31:37,784 - INFO - 第 11 页获取到 4 条记录
2025-07-01 16:31:38,284 - INFO - 查询完成，共获取到 504 条记录
2025-07-01 16:31:38,284 - INFO - 获取到 504 条表单数据
2025-07-01 16:31:38,284 - INFO - 当前日期 2025-06-30 有 156 条MySQL数据需要处理
2025-07-01 16:31:38,284 - INFO - 开始批量插入 3 条新记录
2025-07-01 16:31:38,440 - INFO - 批量插入响应状态码: 200
2025-07-01 16:31:38,440 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 08:31:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DBFBABC1-A5BD-7A18-BBE4-FE297C6D9985', 'x-acs-trace-id': '11ba1663bd4c350a35b01bf41f1604c1', 'etag': '13Hc9/km1wTCzytnhGPXQpA3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 16:31:38,440 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71MQRWRCQGAW19QA3KGJOL2HE0R9KCMC', 'FINST-L5766E71MQRWRCQGAW19QA3KGJOL2HE0R9KCMD', 'FINST-L5766E71MQRWRCQGAW19QA3KGJOL2HE0R9KCME']}
2025-07-01 16:31:38,440 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-01 16:31:38,440 - INFO - 成功插入的数据ID: ['FINST-L5766E71MQRWRCQGAW19QA3KGJOL2HE0R9KCMC', 'FINST-L5766E71MQRWRCQGAW19QA3KGJOL2HE0R9KCMD', 'FINST-L5766E71MQRWRCQGAW19QA3KGJOL2HE0R9KCME']
2025-07-01 16:31:43,456 - INFO - 批量插入完成，共 3 条记录
2025-07-01 16:31:43,456 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-01 16:31:43,456 - INFO - 开始处理日期: 2025-07-01
2025-07-01 16:31:43,456 - INFO - Request Parameters - Page 1:
2025-07-01 16:31:43,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:31:43,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:31:43,940 - INFO - Response - Page 1:
2025-07-01 16:31:43,940 - INFO - 第 1 页获取到 1 条记录
2025-07-01 16:31:44,440 - INFO - 查询完成，共获取到 1 条记录
2025-07-01 16:31:44,440 - INFO - 获取到 1 条表单数据
2025-07-01 16:31:44,440 - INFO - 当前日期 2025-07-01 有 1 条MySQL数据需要处理
2025-07-01 16:31:44,440 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 16:31:44,440 - INFO - 数据同步完成！更新: 1 条，插入: 3 条，错误: 1 条
2025-07-01 16:32:44,455 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 16:32:44,455 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 16:32:44,455 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 16:32:44,596 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 529 条记录
2025-07-01 16:32:44,596 - INFO - 获取到 2 个日期需要处理: ['2025-06-30', '2025-07-01']
2025-07-01 16:32:44,596 - INFO - 开始处理日期: 2025-06-30
2025-07-01 16:32:44,596 - INFO - Request Parameters - Page 1:
2025-07-01 16:32:44,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:44,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:45,424 - INFO - Response - Page 1:
2025-07-01 16:32:45,424 - INFO - 第 1 页获取到 50 条记录
2025-07-01 16:32:45,924 - INFO - Request Parameters - Page 2:
2025-07-01 16:32:45,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:45,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:46,565 - INFO - Response - Page 2:
2025-07-01 16:32:46,565 - INFO - 第 2 页获取到 50 条记录
2025-07-01 16:32:47,080 - INFO - Request Parameters - Page 3:
2025-07-01 16:32:47,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:47,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:47,768 - INFO - Response - Page 3:
2025-07-01 16:32:47,768 - INFO - 第 3 页获取到 50 条记录
2025-07-01 16:32:48,283 - INFO - Request Parameters - Page 4:
2025-07-01 16:32:48,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:48,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:49,018 - INFO - Response - Page 4:
2025-07-01 16:32:49,018 - INFO - 第 4 页获取到 50 条记录
2025-07-01 16:32:49,533 - INFO - Request Parameters - Page 5:
2025-07-01 16:32:49,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:49,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:50,252 - INFO - Response - Page 5:
2025-07-01 16:32:50,252 - INFO - 第 5 页获取到 50 条记录
2025-07-01 16:32:50,768 - INFO - Request Parameters - Page 6:
2025-07-01 16:32:50,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:50,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:51,424 - INFO - Response - Page 6:
2025-07-01 16:32:51,424 - INFO - 第 6 页获取到 50 条记录
2025-07-01 16:32:51,924 - INFO - Request Parameters - Page 7:
2025-07-01 16:32:51,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:51,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:52,596 - INFO - Response - Page 7:
2025-07-01 16:32:52,596 - INFO - 第 7 页获取到 50 条记录
2025-07-01 16:32:53,096 - INFO - Request Parameters - Page 8:
2025-07-01 16:32:53,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:53,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:53,783 - INFO - Response - Page 8:
2025-07-01 16:32:53,783 - INFO - 第 8 页获取到 50 条记录
2025-07-01 16:32:54,283 - INFO - Request Parameters - Page 9:
2025-07-01 16:32:54,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:54,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:55,049 - INFO - Response - Page 9:
2025-07-01 16:32:55,049 - INFO - 第 9 页获取到 50 条记录
2025-07-01 16:32:55,549 - INFO - Request Parameters - Page 10:
2025-07-01 16:32:55,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:55,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:56,158 - INFO - Response - Page 10:
2025-07-01 16:32:56,158 - INFO - 第 10 页获取到 50 条记录
2025-07-01 16:32:56,674 - INFO - Request Parameters - Page 11:
2025-07-01 16:32:56,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:32:56,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:32:57,174 - INFO - Response - Page 11:
2025-07-01 16:32:57,174 - INFO - 第 11 页获取到 7 条记录
2025-07-01 16:32:57,674 - INFO - 查询完成，共获取到 507 条记录
2025-07-01 16:32:57,674 - INFO - 获取到 507 条表单数据
2025-07-01 16:32:57,674 - INFO - 当前日期 2025-06-30 有 517 条MySQL数据需要处理
2025-07-01 16:32:57,690 - INFO - 开始批量插入 10 条新记录
2025-07-01 16:32:57,861 - INFO - 批量插入响应状态码: 200
2025-07-01 16:32:57,861 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 08:32:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '490', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '875AF7D7-65C0-7B10-8259-D64DDA0AC857', 'x-acs-trace-id': '9e8909adbd5bf56d9f601ba21ba0160a', 'etag': '43+0bL0/jN0Yn5dICO8EkwA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 16:32:57,861 - INFO - 批量插入响应体: {'result': ['FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCMY', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCMZ', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM01', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM11', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM21', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM31', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM41', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM51', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM61', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM71']}
2025-07-01 16:32:57,861 - INFO - 批量插入表单数据成功，批次 1，共 10 条记录
2025-07-01 16:32:57,861 - INFO - 成功插入的数据ID: ['FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCMY', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCMZ', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM01', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM11', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM21', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM31', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM41', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM51', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM61', 'FINST-CPC66T919RRWIQOGB2CBGBBE1NP634OPS9KCM71']
2025-07-01 16:33:02,877 - INFO - 批量插入完成，共 10 条记录
2025-07-01 16:33:02,877 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 10 条，错误: 0 条
2025-07-01 16:33:02,877 - INFO - 开始处理日期: 2025-07-01
2025-07-01 16:33:02,877 - INFO - Request Parameters - Page 1:
2025-07-01 16:33:02,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 16:33:02,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 16:33:03,315 - INFO - Response - Page 1:
2025-07-01 16:33:03,315 - INFO - 第 1 页获取到 1 条记录
2025-07-01 16:33:03,830 - INFO - 查询完成，共获取到 1 条记录
2025-07-01 16:33:03,830 - INFO - 获取到 1 条表单数据
2025-07-01 16:33:03,830 - INFO - 当前日期 2025-07-01 有 1 条MySQL数据需要处理
2025-07-01 16:33:03,830 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 16:33:03,830 - INFO - 数据同步完成！更新: 0 条，插入: 10 条，错误: 0 条
2025-07-01 16:33:03,830 - INFO - 同步完成
2025-07-01 19:30:34,185 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 19:30:34,185 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 19:30:34,185 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 19:30:34,326 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 174 条记录
2025-07-01 19:30:34,326 - INFO - 获取到 9 个日期需要处理: ['2025-06-05', '2025-06-07', '2025-06-11', '2025-06-17', '2025-06-21', '2025-06-27', '2025-06-28', '2025-06-30', '2025-07-01']
2025-07-01 19:30:34,326 - INFO - 开始处理日期: 2025-06-05
2025-07-01 19:30:34,326 - INFO - Request Parameters - Page 1:
2025-07-01 19:30:34,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:34,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749052800000, 1749139199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:42,469 - ERROR - 处理日期 2025-06-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CCA202C1-096D-7E3A-B684-BC1053A35381 Response: {'code': 'ServiceUnavailable', 'requestid': 'CCA202C1-096D-7E3A-B684-BC1053A35381', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CCA202C1-096D-7E3A-B684-BC1053A35381)
2025-07-01 19:30:42,469 - INFO - 开始处理日期: 2025-06-07
2025-07-01 19:30:42,469 - INFO - Request Parameters - Page 1:
2025-07-01 19:30:42,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:42,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:43,173 - INFO - Response - Page 1:
2025-07-01 19:30:43,173 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:30:43,673 - INFO - Request Parameters - Page 2:
2025-07-01 19:30:43,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:43,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:49,832 - INFO - Response - Page 2:
2025-07-01 19:30:49,832 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:30:50,332 - INFO - Request Parameters - Page 3:
2025-07-01 19:30:50,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:50,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:51,051 - INFO - Response - Page 3:
2025-07-01 19:30:51,051 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:30:51,567 - INFO - Request Parameters - Page 4:
2025-07-01 19:30:51,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:51,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:52,255 - INFO - Response - Page 4:
2025-07-01 19:30:52,255 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:30:52,770 - INFO - Request Parameters - Page 5:
2025-07-01 19:30:52,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:52,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:53,411 - INFO - Response - Page 5:
2025-07-01 19:30:53,411 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:30:53,927 - INFO - Request Parameters - Page 6:
2025-07-01 19:30:53,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:53,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:54,552 - INFO - Response - Page 6:
2025-07-01 19:30:54,552 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:30:55,053 - INFO - Request Parameters - Page 7:
2025-07-01 19:30:55,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:55,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:55,725 - INFO - Response - Page 7:
2025-07-01 19:30:55,725 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:30:56,225 - INFO - Request Parameters - Page 8:
2025-07-01 19:30:56,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:56,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:56,913 - INFO - Response - Page 8:
2025-07-01 19:30:56,913 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:30:57,413 - INFO - Request Parameters - Page 9:
2025-07-01 19:30:57,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:57,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:58,054 - INFO - Response - Page 9:
2025-07-01 19:30:58,054 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:30:58,554 - INFO - Request Parameters - Page 10:
2025-07-01 19:30:58,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:58,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:30:59,226 - INFO - Response - Page 10:
2025-07-01 19:30:59,226 - INFO - 第 10 页获取到 50 条记录
2025-07-01 19:30:59,742 - INFO - Request Parameters - Page 11:
2025-07-01 19:30:59,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:30:59,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:00,445 - INFO - Response - Page 11:
2025-07-01 19:31:00,445 - INFO - 第 11 页获取到 50 条记录
2025-07-01 19:31:00,961 - INFO - Request Parameters - Page 12:
2025-07-01 19:31:00,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:00,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:01,790 - INFO - Response - Page 12:
2025-07-01 19:31:01,790 - INFO - 第 12 页获取到 50 条记录
2025-07-01 19:31:02,290 - INFO - Request Parameters - Page 13:
2025-07-01 19:31:02,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:02,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:02,790 - INFO - Response - Page 13:
2025-07-01 19:31:02,790 - INFO - 第 13 页获取到 10 条记录
2025-07-01 19:31:03,306 - INFO - 查询完成，共获取到 610 条记录
2025-07-01 19:31:03,306 - INFO - 获取到 610 条表单数据
2025-07-01 19:31:03,306 - INFO - 当前日期 2025-06-07 有 1 条MySQL数据需要处理
2025-07-01 19:31:03,306 - INFO - 开始更新记录 - 表单实例ID: FINST-FPB66VB1S82WSKRT95NW1C377OS033PEEXOBM75
2025-07-01 19:31:03,869 - INFO - 更新表单数据成功: FINST-FPB66VB1S82WSKRT95NW1C377OS033PEEXOBM75
2025-07-01 19:31:03,869 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6173.82, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 539.8, 'new_value': 6964.62}, {'field': 'total_amount', 'old_value': 6713.62, 'new_value': 6964.62}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/075b1d4864934946b3e13031c41d2199.jpg?Expires=2060149888&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=s70dzHWx2dbXWKgvdBQVRuTDEnk%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1ea346b4a0a444479238b200b3ef8ed4.jpg?Expires=2066720548&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=uVjW%2FMxLmBuhtIEXq8qwR6DIwnw%3D'}]
2025-07-01 19:31:03,869 - INFO - 日期 2025-06-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-01 19:31:03,869 - INFO - 开始处理日期: 2025-06-11
2025-07-01 19:31:03,869 - INFO - Request Parameters - Page 1:
2025-07-01 19:31:03,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:03,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:04,603 - INFO - Response - Page 1:
2025-07-01 19:31:04,603 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:31:05,119 - INFO - Request Parameters - Page 2:
2025-07-01 19:31:05,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:05,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:05,776 - INFO - Response - Page 2:
2025-07-01 19:31:05,776 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:31:06,291 - INFO - Request Parameters - Page 3:
2025-07-01 19:31:06,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:06,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:06,948 - INFO - Response - Page 3:
2025-07-01 19:31:06,948 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:31:07,448 - INFO - Request Parameters - Page 4:
2025-07-01 19:31:07,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:07,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:08,230 - INFO - Response - Page 4:
2025-07-01 19:31:08,230 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:31:08,761 - INFO - Request Parameters - Page 5:
2025-07-01 19:31:08,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:08,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:09,465 - INFO - Response - Page 5:
2025-07-01 19:31:09,465 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:31:09,980 - INFO - Request Parameters - Page 6:
2025-07-01 19:31:09,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:09,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:10,637 - INFO - Response - Page 6:
2025-07-01 19:31:10,637 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:31:11,153 - INFO - Request Parameters - Page 7:
2025-07-01 19:31:11,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:11,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:11,841 - INFO - Response - Page 7:
2025-07-01 19:31:11,841 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:31:12,341 - INFO - Request Parameters - Page 8:
2025-07-01 19:31:12,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:12,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:13,091 - INFO - Response - Page 8:
2025-07-01 19:31:13,091 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:31:13,607 - INFO - Request Parameters - Page 9:
2025-07-01 19:31:13,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:13,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:14,326 - INFO - Response - Page 9:
2025-07-01 19:31:14,326 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:31:14,842 - INFO - Request Parameters - Page 10:
2025-07-01 19:31:14,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:14,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:15,514 - INFO - Response - Page 10:
2025-07-01 19:31:15,514 - INFO - 第 10 页获取到 50 条记录
2025-07-01 19:31:16,030 - INFO - Request Parameters - Page 11:
2025-07-01 19:31:16,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:16,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:16,592 - INFO - Response - Page 11:
2025-07-01 19:31:16,592 - INFO - 第 11 页获取到 17 条记录
2025-07-01 19:31:17,108 - INFO - 查询完成，共获取到 517 条记录
2025-07-01 19:31:17,108 - INFO - 获取到 517 条表单数据
2025-07-01 19:31:17,108 - INFO - 当前日期 2025-06-11 有 1 条MySQL数据需要处理
2025-07-01 19:31:17,108 - INFO - 开始更新记录 - 表单实例ID: FINST-FPB66VB1VN7WRQUG7FPN942I9GA536EMJRSBMA1
2025-07-01 19:31:17,718 - INFO - 更新表单数据成功: FINST-FPB66VB1VN7WRQUG7FPN942I9GA536EMJRSBMA1
2025-07-01 19:31:17,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4574.26, 'new_value': 4574.56}, {'field': 'offline_amount', 'old_value': 1427.84, 'new_value': 1429.87}, {'field': 'total_amount', 'old_value': 6002.1, 'new_value': 6004.43}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-01 19:31:17,718 - INFO - 日期 2025-06-11 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-01 19:31:17,718 - INFO - 开始处理日期: 2025-06-17
2025-07-01 19:31:17,718 - INFO - Request Parameters - Page 1:
2025-07-01 19:31:17,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:17,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:18,390 - INFO - Response - Page 1:
2025-07-01 19:31:18,390 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:31:18,906 - INFO - Request Parameters - Page 2:
2025-07-01 19:31:18,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:18,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:19,625 - INFO - Response - Page 2:
2025-07-01 19:31:19,625 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:31:20,141 - INFO - Request Parameters - Page 3:
2025-07-01 19:31:20,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:20,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:20,922 - INFO - Response - Page 3:
2025-07-01 19:31:20,922 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:31:21,438 - INFO - Request Parameters - Page 4:
2025-07-01 19:31:21,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:21,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:22,110 - INFO - Response - Page 4:
2025-07-01 19:31:22,126 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:31:22,626 - INFO - Request Parameters - Page 5:
2025-07-01 19:31:22,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:22,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:23,329 - INFO - Response - Page 5:
2025-07-01 19:31:23,329 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:31:23,830 - INFO - Request Parameters - Page 6:
2025-07-01 19:31:23,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:23,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:24,549 - INFO - Response - Page 6:
2025-07-01 19:31:24,564 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:31:25,080 - INFO - Request Parameters - Page 7:
2025-07-01 19:31:25,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:25,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:25,752 - INFO - Response - Page 7:
2025-07-01 19:31:25,752 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:31:26,253 - INFO - Request Parameters - Page 8:
2025-07-01 19:31:26,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:26,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:26,878 - INFO - Response - Page 8:
2025-07-01 19:31:26,878 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:31:27,394 - INFO - Request Parameters - Page 9:
2025-07-01 19:31:27,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:27,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:28,066 - INFO - Response - Page 9:
2025-07-01 19:31:28,066 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:31:28,566 - INFO - Request Parameters - Page 10:
2025-07-01 19:31:28,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:28,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:29,238 - INFO - Response - Page 10:
2025-07-01 19:31:29,238 - INFO - 第 10 页获取到 50 条记录
2025-07-01 19:31:29,738 - INFO - Request Parameters - Page 11:
2025-07-01 19:31:29,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:29,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:30,332 - INFO - Response - Page 11:
2025-07-01 19:31:30,332 - INFO - 第 11 页获取到 38 条记录
2025-07-01 19:31:30,832 - INFO - 查询完成，共获取到 538 条记录
2025-07-01 19:31:30,832 - INFO - 获取到 538 条表单数据
2025-07-01 19:31:30,832 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-07-01 19:31:30,832 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 19:31:30,832 - INFO - 开始处理日期: 2025-06-21
2025-07-01 19:31:30,832 - INFO - Request Parameters - Page 1:
2025-07-01 19:31:30,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:30,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:31,505 - INFO - Response - Page 1:
2025-07-01 19:31:31,505 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:31:32,020 - INFO - Request Parameters - Page 2:
2025-07-01 19:31:32,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:32,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:32,693 - INFO - Response - Page 2:
2025-07-01 19:31:32,693 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:31:33,208 - INFO - Request Parameters - Page 3:
2025-07-01 19:31:33,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:33,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:33,834 - INFO - Response - Page 3:
2025-07-01 19:31:33,834 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:31:34,349 - INFO - Request Parameters - Page 4:
2025-07-01 19:31:34,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:34,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:35,022 - INFO - Response - Page 4:
2025-07-01 19:31:35,022 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:31:35,522 - INFO - Request Parameters - Page 5:
2025-07-01 19:31:35,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:35,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:36,194 - INFO - Response - Page 5:
2025-07-01 19:31:36,194 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:31:36,710 - INFO - Request Parameters - Page 6:
2025-07-01 19:31:36,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:36,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:37,366 - INFO - Response - Page 6:
2025-07-01 19:31:37,366 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:31:37,866 - INFO - Request Parameters - Page 7:
2025-07-01 19:31:37,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:37,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:38,570 - INFO - Response - Page 7:
2025-07-01 19:31:38,570 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:31:39,070 - INFO - Request Parameters - Page 8:
2025-07-01 19:31:39,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:39,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:39,727 - INFO - Response - Page 8:
2025-07-01 19:31:39,727 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:31:40,242 - INFO - Request Parameters - Page 9:
2025-07-01 19:31:40,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:40,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:40,930 - INFO - Response - Page 9:
2025-07-01 19:31:40,930 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:31:41,430 - INFO - Request Parameters - Page 10:
2025-07-01 19:31:41,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:41,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:42,087 - INFO - Response - Page 10:
2025-07-01 19:31:42,087 - INFO - 第 10 页获取到 50 条记录
2025-07-01 19:31:42,587 - INFO - Request Parameters - Page 11:
2025-07-01 19:31:42,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:42,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:43,150 - INFO - Response - Page 11:
2025-07-01 19:31:43,150 - INFO - 第 11 页获取到 20 条记录
2025-07-01 19:31:43,666 - INFO - 查询完成，共获取到 520 条记录
2025-07-01 19:31:43,666 - INFO - 获取到 520 条表单数据
2025-07-01 19:31:43,666 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-07-01 19:31:43,666 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM4C
2025-07-01 19:31:44,213 - INFO - 更新表单数据成功: FINST-7PF66CC1LZHWKV2BFLPVWABERMVM343XX17CM4C
2025-07-01 19:31:44,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7519.19, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 1570.9, 'new_value': 10057.09}, {'field': 'total_amount', 'old_value': 9090.09, 'new_value': 10057.09}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e7814b6694794740918b922f81281aac.jpg?Expires=2060149888&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=zWXaLYTBkEZkW7qDr8IYD88R4AE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/45f055389bcb421ca916e710b46a3b69.jpg?Expires=2066720548&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Y%2BrbSxOsuQ7cOemp%2FOw%2B25VZ23E%3D'}]
2025-07-01 19:31:44,213 - INFO - 日期 2025-06-21 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-01 19:31:44,213 - INFO - 开始处理日期: 2025-06-27
2025-07-01 19:31:44,213 - INFO - Request Parameters - Page 1:
2025-07-01 19:31:44,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:44,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:44,901 - INFO - Response - Page 1:
2025-07-01 19:31:44,901 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:31:45,416 - INFO - Request Parameters - Page 2:
2025-07-01 19:31:45,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:45,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:46,057 - INFO - Response - Page 2:
2025-07-01 19:31:46,057 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:31:46,573 - INFO - Request Parameters - Page 3:
2025-07-01 19:31:46,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:46,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:47,198 - INFO - Response - Page 3:
2025-07-01 19:31:47,198 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:31:47,714 - INFO - Request Parameters - Page 4:
2025-07-01 19:31:47,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:47,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:48,636 - INFO - Response - Page 4:
2025-07-01 19:31:48,636 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:31:49,137 - INFO - Request Parameters - Page 5:
2025-07-01 19:31:49,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:49,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:49,809 - INFO - Response - Page 5:
2025-07-01 19:31:49,809 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:31:50,325 - INFO - Request Parameters - Page 6:
2025-07-01 19:31:50,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:50,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:51,012 - INFO - Response - Page 6:
2025-07-01 19:31:51,012 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:31:51,513 - INFO - Request Parameters - Page 7:
2025-07-01 19:31:51,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:51,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:52,107 - INFO - Response - Page 7:
2025-07-01 19:31:52,107 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:31:52,622 - INFO - Request Parameters - Page 8:
2025-07-01 19:31:52,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:52,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:53,279 - INFO - Response - Page 8:
2025-07-01 19:31:53,279 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:31:53,779 - INFO - Request Parameters - Page 9:
2025-07-01 19:31:53,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:53,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:54,451 - INFO - Response - Page 9:
2025-07-01 19:31:54,451 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:31:54,967 - INFO - Request Parameters - Page 10:
2025-07-01 19:31:54,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:54,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:55,577 - INFO - Response - Page 10:
2025-07-01 19:31:55,577 - INFO - 第 10 页获取到 27 条记录
2025-07-01 19:31:56,092 - INFO - 查询完成，共获取到 477 条记录
2025-07-01 19:31:56,092 - INFO - 获取到 477 条表单数据
2025-07-01 19:31:56,092 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-07-01 19:31:56,092 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 19:31:56,092 - INFO - 开始处理日期: 2025-06-28
2025-07-01 19:31:56,092 - INFO - Request Parameters - Page 1:
2025-07-01 19:31:56,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:56,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:56,796 - INFO - Response - Page 1:
2025-07-01 19:31:56,796 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:31:57,312 - INFO - Request Parameters - Page 2:
2025-07-01 19:31:57,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:57,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:57,968 - INFO - Response - Page 2:
2025-07-01 19:31:57,968 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:31:58,484 - INFO - Request Parameters - Page 3:
2025-07-01 19:31:58,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:58,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:31:59,187 - INFO - Response - Page 3:
2025-07-01 19:31:59,187 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:31:59,703 - INFO - Request Parameters - Page 4:
2025-07-01 19:31:59,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:31:59,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:00,375 - INFO - Response - Page 4:
2025-07-01 19:32:00,375 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:32:00,891 - INFO - Request Parameters - Page 5:
2025-07-01 19:32:00,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:00,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:01,595 - INFO - Response - Page 5:
2025-07-01 19:32:01,595 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:32:02,111 - INFO - Request Parameters - Page 6:
2025-07-01 19:32:02,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:02,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:02,798 - INFO - Response - Page 6:
2025-07-01 19:32:02,798 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:32:03,298 - INFO - Request Parameters - Page 7:
2025-07-01 19:32:03,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:03,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:03,924 - INFO - Response - Page 7:
2025-07-01 19:32:03,924 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:32:04,440 - INFO - Request Parameters - Page 8:
2025-07-01 19:32:04,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:04,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:05,127 - INFO - Response - Page 8:
2025-07-01 19:32:05,127 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:32:05,643 - INFO - Request Parameters - Page 9:
2025-07-01 19:32:05,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:05,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:06,315 - INFO - Response - Page 9:
2025-07-01 19:32:06,315 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:32:06,831 - INFO - Request Parameters - Page 10:
2025-07-01 19:32:06,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:06,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:07,409 - INFO - Response - Page 10:
2025-07-01 19:32:07,409 - INFO - 第 10 页获取到 29 条记录
2025-07-01 19:32:07,910 - INFO - 查询完成，共获取到 479 条记录
2025-07-01 19:32:07,910 - INFO - 获取到 479 条表单数据
2025-07-01 19:32:07,910 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-07-01 19:32:07,910 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 19:32:07,910 - INFO - 开始处理日期: 2025-06-30
2025-07-01 19:32:07,910 - INFO - Request Parameters - Page 1:
2025-07-01 19:32:07,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:07,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:08,644 - INFO - Response - Page 1:
2025-07-01 19:32:08,644 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:32:09,160 - INFO - Request Parameters - Page 2:
2025-07-01 19:32:09,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:09,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:09,817 - INFO - Response - Page 2:
2025-07-01 19:32:09,817 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:32:10,333 - INFO - Request Parameters - Page 3:
2025-07-01 19:32:10,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:10,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:10,958 - INFO - Response - Page 3:
2025-07-01 19:32:10,958 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:32:11,474 - INFO - Request Parameters - Page 4:
2025-07-01 19:32:11,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:11,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:12,083 - INFO - Response - Page 4:
2025-07-01 19:32:12,083 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:32:12,599 - INFO - Request Parameters - Page 5:
2025-07-01 19:32:12,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:12,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:13,287 - INFO - Response - Page 5:
2025-07-01 19:32:13,287 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:32:13,787 - INFO - Request Parameters - Page 6:
2025-07-01 19:32:13,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:13,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:14,537 - INFO - Response - Page 6:
2025-07-01 19:32:14,537 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:32:15,053 - INFO - Request Parameters - Page 7:
2025-07-01 19:32:15,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:15,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:15,710 - INFO - Response - Page 7:
2025-07-01 19:32:15,710 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:32:16,210 - INFO - Request Parameters - Page 8:
2025-07-01 19:32:16,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:16,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:17,007 - INFO - Response - Page 8:
2025-07-01 19:32:17,007 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:32:17,523 - INFO - Request Parameters - Page 9:
2025-07-01 19:32:17,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:17,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:18,211 - INFO - Response - Page 9:
2025-07-01 19:32:18,211 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:32:18,726 - INFO - Request Parameters - Page 10:
2025-07-01 19:32:18,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:18,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:19,399 - INFO - Response - Page 10:
2025-07-01 19:32:19,399 - INFO - 第 10 页获取到 50 条记录
2025-07-01 19:32:19,914 - INFO - Request Parameters - Page 11:
2025-07-01 19:32:19,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:19,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:20,540 - INFO - Response - Page 11:
2025-07-01 19:32:20,540 - INFO - 第 11 页获取到 17 条记录
2025-07-01 19:32:21,056 - INFO - 查询完成，共获取到 517 条记录
2025-07-01 19:32:21,056 - INFO - 获取到 517 条表单数据
2025-07-01 19:32:21,056 - INFO - 当前日期 2025-06-30 有 160 条MySQL数据需要处理
2025-07-01 19:32:21,056 - INFO - 开始更新记录 - 表单实例ID: FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMV3
2025-07-01 19:32:21,603 - INFO - 更新表单数据成功: FINST-BD766BC1P4RWPTT6FL22OBGPMAC533471KJCMV3
2025-07-01 19:32:21,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27744.0, 'new_value': 32642.6}, {'field': 'total_amount', 'old_value': 27744.0, 'new_value': 32642.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-01 19:32:21,603 - INFO - 开始批量插入 3 条新记录
2025-07-01 19:32:21,759 - INFO - 批量插入响应状态码: 200
2025-07-01 19:32:21,759 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 11:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6BC3E24C-EBD0-7354-944D-5A05546BE551', 'x-acs-trace-id': '6ddc316ac5ec7b96d9fc8ca8406d8b36', 'etag': '1L8NYGvnxvusrSlRLQmfn0Q3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 19:32:21,759 - INFO - 批量插入响应体: {'result': ['FINST-1T666B91XQRWQ6ANCXMMJ5EEDHHY2GXE7GKCM8', 'FINST-1T666B91XQRWQ6ANCXMMJ5EEDHHY2GXE7GKCM9', 'FINST-1T666B91XQRWQ6ANCXMMJ5EEDHHY2GXE7GKCMA']}
2025-07-01 19:32:21,759 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-01 19:32:21,759 - INFO - 成功插入的数据ID: ['FINST-1T666B91XQRWQ6ANCXMMJ5EEDHHY2GXE7GKCM8', 'FINST-1T666B91XQRWQ6ANCXMMJ5EEDHHY2GXE7GKCM9', 'FINST-1T666B91XQRWQ6ANCXMMJ5EEDHHY2GXE7GKCMA']
2025-07-01 19:32:26,777 - INFO - 批量插入完成，共 3 条记录
2025-07-01 19:32:26,777 - INFO - 日期 2025-06-30 处理完成 - 更新: 1 条，插入: 3 条，错误: 0 条
2025-07-01 19:32:26,777 - INFO - 开始处理日期: 2025-07-01
2025-07-01 19:32:26,777 - INFO - Request Parameters - Page 1:
2025-07-01 19:32:26,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:32:26,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:32:27,230 - INFO - Response - Page 1:
2025-07-01 19:32:27,230 - INFO - 第 1 页获取到 1 条记录
2025-07-01 19:32:27,746 - INFO - 查询完成，共获取到 1 条记录
2025-07-01 19:32:27,746 - INFO - 获取到 1 条表单数据
2025-07-01 19:32:27,746 - INFO - 当前日期 2025-07-01 有 1 条MySQL数据需要处理
2025-07-01 19:32:27,746 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 19:32:27,746 - INFO - 数据同步完成！更新: 4 条，插入: 3 条，错误: 1 条
2025-07-01 19:33:27,785 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 19:33:27,785 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 19:33:27,785 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 19:33:27,942 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 539 条记录
2025-07-01 19:33:27,942 - INFO - 获取到 2 个日期需要处理: ['2025-06-30', '2025-07-01']
2025-07-01 19:33:27,942 - INFO - 开始处理日期: 2025-06-30
2025-07-01 19:33:27,942 - INFO - Request Parameters - Page 1:
2025-07-01 19:33:27,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:27,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:28,661 - INFO - Response - Page 1:
2025-07-01 19:33:28,661 - INFO - 第 1 页获取到 50 条记录
2025-07-01 19:33:29,161 - INFO - Request Parameters - Page 2:
2025-07-01 19:33:29,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:29,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:29,849 - INFO - Response - Page 2:
2025-07-01 19:33:29,849 - INFO - 第 2 页获取到 50 条记录
2025-07-01 19:33:30,364 - INFO - Request Parameters - Page 3:
2025-07-01 19:33:30,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:30,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:31,068 - INFO - Response - Page 3:
2025-07-01 19:33:31,068 - INFO - 第 3 页获取到 50 条记录
2025-07-01 19:33:31,568 - INFO - Request Parameters - Page 4:
2025-07-01 19:33:31,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:31,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:32,303 - INFO - Response - Page 4:
2025-07-01 19:33:32,303 - INFO - 第 4 页获取到 50 条记录
2025-07-01 19:33:32,818 - INFO - Request Parameters - Page 5:
2025-07-01 19:33:32,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:32,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:33,459 - INFO - Response - Page 5:
2025-07-01 19:33:33,459 - INFO - 第 5 页获取到 50 条记录
2025-07-01 19:33:33,975 - INFO - Request Parameters - Page 6:
2025-07-01 19:33:33,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:33,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:34,632 - INFO - Response - Page 6:
2025-07-01 19:33:34,632 - INFO - 第 6 页获取到 50 条记录
2025-07-01 19:33:35,148 - INFO - Request Parameters - Page 7:
2025-07-01 19:33:35,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:35,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:35,804 - INFO - Response - Page 7:
2025-07-01 19:33:35,804 - INFO - 第 7 页获取到 50 条记录
2025-07-01 19:33:36,304 - INFO - Request Parameters - Page 8:
2025-07-01 19:33:36,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:36,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:36,898 - INFO - Response - Page 8:
2025-07-01 19:33:36,898 - INFO - 第 8 页获取到 50 条记录
2025-07-01 19:33:37,414 - INFO - Request Parameters - Page 9:
2025-07-01 19:33:37,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:37,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:38,164 - INFO - Response - Page 9:
2025-07-01 19:33:38,164 - INFO - 第 9 页获取到 50 条记录
2025-07-01 19:33:38,665 - INFO - Request Parameters - Page 10:
2025-07-01 19:33:38,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:38,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:39,368 - INFO - Response - Page 10:
2025-07-01 19:33:39,368 - INFO - 第 10 页获取到 50 条记录
2025-07-01 19:33:39,884 - INFO - Request Parameters - Page 11:
2025-07-01 19:33:39,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:39,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:40,447 - INFO - Response - Page 11:
2025-07-01 19:33:40,447 - INFO - 第 11 页获取到 20 条记录
2025-07-01 19:33:40,962 - INFO - 查询完成，共获取到 520 条记录
2025-07-01 19:33:40,962 - INFO - 获取到 520 条表单数据
2025-07-01 19:33:40,962 - INFO - 当前日期 2025-06-30 有 526 条MySQL数据需要处理
2025-07-01 19:33:40,978 - INFO - 开始批量插入 6 条新记录
2025-07-01 19:33:41,150 - INFO - 批量插入响应状态码: 200
2025-07-01 19:33:41,150 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 11:33:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '294', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '99440167-5AD6-798B-A54A-4A9BC988C039', 'x-acs-trace-id': '0c344f9de84c38753fef39d40383a926', 'etag': '2JpMvVOZftqpLQBMpc5/uTw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 19:33:41,150 - INFO - 批量插入响应体: {'result': ['FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMK', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCML', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMM', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMN', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMO', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMP']}
2025-07-01 19:33:41,150 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-07-01 19:33:41,150 - INFO - 成功插入的数据ID: ['FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMK', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCML', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMM', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMN', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMO', 'FINST-OLF665811URW70HKFP1LBAFUWYXZ2W549GKCMP']
2025-07-01 19:33:46,168 - INFO - 批量插入完成，共 6 条记录
2025-07-01 19:33:46,168 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-07-01 19:33:46,168 - INFO - 开始处理日期: 2025-07-01
2025-07-01 19:33:46,168 - INFO - Request Parameters - Page 1:
2025-07-01 19:33:46,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 19:33:46,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 19:33:46,621 - INFO - Response - Page 1:
2025-07-01 19:33:46,621 - INFO - 第 1 页获取到 1 条记录
2025-07-01 19:33:47,137 - INFO - 查询完成，共获取到 1 条记录
2025-07-01 19:33:47,137 - INFO - 获取到 1 条表单数据
2025-07-01 19:33:47,137 - INFO - 当前日期 2025-07-01 有 1 条MySQL数据需要处理
2025-07-01 19:33:47,137 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 19:33:47,137 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 0 条
2025-07-01 19:33:47,137 - INFO - 同步完成
2025-07-01 22:30:33,781 - INFO - 使用默认增量同步（当天更新数据）
2025-07-01 22:30:33,781 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-01 22:30:33,781 - INFO - 查询参数: ('2025-07-01',)
2025-07-01 22:30:33,938 - INFO - MySQL查询成功，增量数据（日期: 2025-07-01），共获取 183 条记录
2025-07-01 22:30:33,938 - INFO - 获取到 9 个日期需要处理: ['2025-06-05', '2025-06-07', '2025-06-11', '2025-06-17', '2025-06-21', '2025-06-27', '2025-06-28', '2025-06-30', '2025-07-01']
2025-07-01 22:30:33,938 - INFO - 开始处理日期: 2025-06-05
2025-07-01 22:30:33,938 - INFO - Request Parameters - Page 1:
2025-07-01 22:30:33,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:33,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749052800000, 1749139199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:42,066 - ERROR - 处理日期 2025-06-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5CE7C9F4-99F6-7D35-80CD-C3C6AC28191F Response: {'code': 'ServiceUnavailable', 'requestid': '5CE7C9F4-99F6-7D35-80CD-C3C6AC28191F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5CE7C9F4-99F6-7D35-80CD-C3C6AC28191F)
2025-07-01 22:30:42,066 - INFO - 开始处理日期: 2025-06-07
2025-07-01 22:30:42,066 - INFO - Request Parameters - Page 1:
2025-07-01 22:30:42,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:42,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:49,116 - INFO - Response - Page 1:
2025-07-01 22:30:49,116 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:30:49,616 - INFO - Request Parameters - Page 2:
2025-07-01 22:30:49,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:49,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:50,257 - INFO - Response - Page 2:
2025-07-01 22:30:50,257 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:30:50,772 - INFO - Request Parameters - Page 3:
2025-07-01 22:30:50,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:50,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:51,476 - INFO - Response - Page 3:
2025-07-01 22:30:51,476 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:30:51,976 - INFO - Request Parameters - Page 4:
2025-07-01 22:30:51,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:51,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:52,648 - INFO - Response - Page 4:
2025-07-01 22:30:52,648 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:30:53,148 - INFO - Request Parameters - Page 5:
2025-07-01 22:30:53,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:53,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:53,821 - INFO - Response - Page 5:
2025-07-01 22:30:53,821 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:30:54,336 - INFO - Request Parameters - Page 6:
2025-07-01 22:30:54,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:54,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:54,962 - INFO - Response - Page 6:
2025-07-01 22:30:54,962 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:30:55,477 - INFO - Request Parameters - Page 7:
2025-07-01 22:30:55,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:55,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:56,150 - INFO - Response - Page 7:
2025-07-01 22:30:56,150 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:30:56,665 - INFO - Request Parameters - Page 8:
2025-07-01 22:30:56,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:56,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:57,338 - INFO - Response - Page 8:
2025-07-01 22:30:57,338 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:30:57,838 - INFO - Request Parameters - Page 9:
2025-07-01 22:30:57,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:57,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:58,526 - INFO - Response - Page 9:
2025-07-01 22:30:58,526 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:30:59,026 - INFO - Request Parameters - Page 10:
2025-07-01 22:30:59,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:30:59,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:30:59,776 - INFO - Response - Page 10:
2025-07-01 22:30:59,776 - INFO - 第 10 页获取到 50 条记录
2025-07-01 22:31:00,292 - INFO - Request Parameters - Page 11:
2025-07-01 22:31:00,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:00,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:00,964 - INFO - Response - Page 11:
2025-07-01 22:31:00,964 - INFO - 第 11 页获取到 50 条记录
2025-07-01 22:31:01,464 - INFO - Request Parameters - Page 12:
2025-07-01 22:31:01,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:01,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:02,136 - INFO - Response - Page 12:
2025-07-01 22:31:02,152 - INFO - 第 12 页获取到 50 条记录
2025-07-01 22:31:02,668 - INFO - Request Parameters - Page 13:
2025-07-01 22:31:02,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:02,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 13, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749225600000, 1749311999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:03,231 - INFO - Response - Page 13:
2025-07-01 22:31:03,231 - INFO - 第 13 页获取到 10 条记录
2025-07-01 22:31:03,731 - INFO - 查询完成，共获取到 610 条记录
2025-07-01 22:31:03,731 - INFO - 获取到 610 条表单数据
2025-07-01 22:31:03,731 - INFO - 当前日期 2025-06-07 有 1 条MySQL数据需要处理
2025-07-01 22:31:03,731 - INFO - 日期 2025-06-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:31:03,731 - INFO - 开始处理日期: 2025-06-11
2025-07-01 22:31:03,731 - INFO - Request Parameters - Page 1:
2025-07-01 22:31:03,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:03,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:04,419 - INFO - Response - Page 1:
2025-07-01 22:31:04,419 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:31:04,934 - INFO - Request Parameters - Page 2:
2025-07-01 22:31:04,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:04,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:05,653 - INFO - Response - Page 2:
2025-07-01 22:31:05,653 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:31:06,154 - INFO - Request Parameters - Page 3:
2025-07-01 22:31:06,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:06,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:06,873 - INFO - Response - Page 3:
2025-07-01 22:31:06,873 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:31:07,388 - INFO - Request Parameters - Page 4:
2025-07-01 22:31:07,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:07,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:08,076 - INFO - Response - Page 4:
2025-07-01 22:31:08,076 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:31:08,592 - INFO - Request Parameters - Page 5:
2025-07-01 22:31:08,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:08,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:09,295 - INFO - Response - Page 5:
2025-07-01 22:31:09,295 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:31:09,796 - INFO - Request Parameters - Page 6:
2025-07-01 22:31:09,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:09,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:10,515 - INFO - Response - Page 6:
2025-07-01 22:31:10,515 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:31:11,031 - INFO - Request Parameters - Page 7:
2025-07-01 22:31:11,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:11,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:11,671 - INFO - Response - Page 7:
2025-07-01 22:31:11,671 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:31:12,187 - INFO - Request Parameters - Page 8:
2025-07-01 22:31:12,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:12,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:12,844 - INFO - Response - Page 8:
2025-07-01 22:31:12,844 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:31:13,344 - INFO - Request Parameters - Page 9:
2025-07-01 22:31:13,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:13,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:13,969 - INFO - Response - Page 9:
2025-07-01 22:31:13,969 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:31:14,485 - INFO - Request Parameters - Page 10:
2025-07-01 22:31:14,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:14,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:15,110 - INFO - Response - Page 10:
2025-07-01 22:31:15,110 - INFO - 第 10 页获取到 50 条记录
2025-07-01 22:31:15,626 - INFO - Request Parameters - Page 11:
2025-07-01 22:31:15,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:15,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749571200000, 1749657599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:16,204 - INFO - Response - Page 11:
2025-07-01 22:31:16,204 - INFO - 第 11 页获取到 17 条记录
2025-07-01 22:31:16,720 - INFO - 查询完成，共获取到 517 条记录
2025-07-01 22:31:16,720 - INFO - 获取到 517 条表单数据
2025-07-01 22:31:16,720 - INFO - 当前日期 2025-06-11 有 1 条MySQL数据需要处理
2025-07-01 22:31:16,720 - INFO - 日期 2025-06-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:31:16,720 - INFO - 开始处理日期: 2025-06-17
2025-07-01 22:31:16,720 - INFO - Request Parameters - Page 1:
2025-07-01 22:31:16,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:16,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:17,392 - INFO - Response - Page 1:
2025-07-01 22:31:17,392 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:31:17,908 - INFO - Request Parameters - Page 2:
2025-07-01 22:31:17,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:17,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:18,674 - INFO - Response - Page 2:
2025-07-01 22:31:18,674 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:31:19,190 - INFO - Request Parameters - Page 3:
2025-07-01 22:31:19,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:19,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:19,909 - INFO - Response - Page 3:
2025-07-01 22:31:19,909 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:31:20,409 - INFO - Request Parameters - Page 4:
2025-07-01 22:31:20,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:20,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:21,066 - INFO - Response - Page 4:
2025-07-01 22:31:21,066 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:31:21,566 - INFO - Request Parameters - Page 5:
2025-07-01 22:31:21,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:21,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:22,222 - INFO - Response - Page 5:
2025-07-01 22:31:22,222 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:31:22,738 - INFO - Request Parameters - Page 6:
2025-07-01 22:31:22,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:22,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:23,410 - INFO - Response - Page 6:
2025-07-01 22:31:23,410 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:31:23,926 - INFO - Request Parameters - Page 7:
2025-07-01 22:31:23,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:23,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:24,583 - INFO - Response - Page 7:
2025-07-01 22:31:24,583 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:31:25,083 - INFO - Request Parameters - Page 8:
2025-07-01 22:31:25,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:25,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:25,755 - INFO - Response - Page 8:
2025-07-01 22:31:25,755 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:31:26,271 - INFO - Request Parameters - Page 9:
2025-07-01 22:31:26,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:26,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:26,959 - INFO - Response - Page 9:
2025-07-01 22:31:26,959 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:31:27,459 - INFO - Request Parameters - Page 10:
2025-07-01 22:31:27,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:27,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:28,147 - INFO - Response - Page 10:
2025-07-01 22:31:28,147 - INFO - 第 10 页获取到 50 条记录
2025-07-01 22:31:28,647 - INFO - Request Parameters - Page 11:
2025-07-01 22:31:28,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:28,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750089600000, 1750175999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:29,210 - INFO - Response - Page 11:
2025-07-01 22:31:29,210 - INFO - 第 11 页获取到 38 条记录
2025-07-01 22:31:29,710 - INFO - 查询完成，共获取到 538 条记录
2025-07-01 22:31:29,710 - INFO - 获取到 538 条表单数据
2025-07-01 22:31:29,710 - INFO - 当前日期 2025-06-17 有 1 条MySQL数据需要处理
2025-07-01 22:31:29,710 - INFO - 日期 2025-06-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:31:29,710 - INFO - 开始处理日期: 2025-06-21
2025-07-01 22:31:29,710 - INFO - Request Parameters - Page 1:
2025-07-01 22:31:29,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:29,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:30,398 - INFO - Response - Page 1:
2025-07-01 22:31:30,398 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:31:30,913 - INFO - Request Parameters - Page 2:
2025-07-01 22:31:30,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:30,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:31,570 - INFO - Response - Page 2:
2025-07-01 22:31:31,570 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:31:32,086 - INFO - Request Parameters - Page 3:
2025-07-01 22:31:32,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:32,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:32,836 - INFO - Response - Page 3:
2025-07-01 22:31:32,836 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:31:33,336 - INFO - Request Parameters - Page 4:
2025-07-01 22:31:33,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:33,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:34,040 - INFO - Response - Page 4:
2025-07-01 22:31:34,040 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:31:34,540 - INFO - Request Parameters - Page 5:
2025-07-01 22:31:34,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:34,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:35,259 - INFO - Response - Page 5:
2025-07-01 22:31:35,259 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:31:35,775 - INFO - Request Parameters - Page 6:
2025-07-01 22:31:35,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:35,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:36,384 - INFO - Response - Page 6:
2025-07-01 22:31:36,384 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:31:36,885 - INFO - Request Parameters - Page 7:
2025-07-01 22:31:36,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:36,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:37,588 - INFO - Response - Page 7:
2025-07-01 22:31:37,588 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:31:38,088 - INFO - Request Parameters - Page 8:
2025-07-01 22:31:38,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:38,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:38,760 - INFO - Response - Page 8:
2025-07-01 22:31:38,760 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:31:39,260 - INFO - Request Parameters - Page 9:
2025-07-01 22:31:39,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:39,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:39,901 - INFO - Response - Page 9:
2025-07-01 22:31:39,901 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:31:40,402 - INFO - Request Parameters - Page 10:
2025-07-01 22:31:40,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:40,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:41,167 - INFO - Response - Page 10:
2025-07-01 22:31:41,167 - INFO - 第 10 页获取到 50 条记录
2025-07-01 22:31:41,668 - INFO - Request Parameters - Page 11:
2025-07-01 22:31:41,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:41,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:42,293 - INFO - Response - Page 11:
2025-07-01 22:31:42,293 - INFO - 第 11 页获取到 20 条记录
2025-07-01 22:31:42,793 - INFO - 查询完成，共获取到 520 条记录
2025-07-01 22:31:42,793 - INFO - 获取到 520 条表单数据
2025-07-01 22:31:42,793 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-07-01 22:31:42,793 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:31:42,793 - INFO - 开始处理日期: 2025-06-27
2025-07-01 22:31:42,793 - INFO - Request Parameters - Page 1:
2025-07-01 22:31:42,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:42,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:43,434 - INFO - Response - Page 1:
2025-07-01 22:31:43,434 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:31:43,934 - INFO - Request Parameters - Page 2:
2025-07-01 22:31:43,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:43,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:44,606 - INFO - Response - Page 2:
2025-07-01 22:31:44,606 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:31:45,122 - INFO - Request Parameters - Page 3:
2025-07-01 22:31:45,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:45,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:45,763 - INFO - Response - Page 3:
2025-07-01 22:31:45,763 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:31:46,279 - INFO - Request Parameters - Page 4:
2025-07-01 22:31:46,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:46,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:46,935 - INFO - Response - Page 4:
2025-07-01 22:31:46,935 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:31:47,436 - INFO - Request Parameters - Page 5:
2025-07-01 22:31:47,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:47,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:48,123 - INFO - Response - Page 5:
2025-07-01 22:31:48,123 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:31:48,624 - INFO - Request Parameters - Page 6:
2025-07-01 22:31:48,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:48,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:49,296 - INFO - Response - Page 6:
2025-07-01 22:31:49,296 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:31:49,796 - INFO - Request Parameters - Page 7:
2025-07-01 22:31:49,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:49,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:50,468 - INFO - Response - Page 7:
2025-07-01 22:31:50,468 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:31:50,984 - INFO - Request Parameters - Page 8:
2025-07-01 22:31:50,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:50,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:51,656 - INFO - Response - Page 8:
2025-07-01 22:31:51,656 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:31:52,172 - INFO - Request Parameters - Page 9:
2025-07-01 22:31:52,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:52,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:52,875 - INFO - Response - Page 9:
2025-07-01 22:31:52,875 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:31:53,375 - INFO - Request Parameters - Page 10:
2025-07-01 22:31:53,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:53,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750953600000, 1751039999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:54,001 - INFO - Response - Page 10:
2025-07-01 22:31:54,001 - INFO - 第 10 页获取到 27 条记录
2025-07-01 22:31:54,501 - INFO - 查询完成，共获取到 477 条记录
2025-07-01 22:31:54,501 - INFO - 获取到 477 条表单数据
2025-07-01 22:31:54,501 - INFO - 当前日期 2025-06-27 有 2 条MySQL数据需要处理
2025-07-01 22:31:54,501 - INFO - 日期 2025-06-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:31:54,501 - INFO - 开始处理日期: 2025-06-28
2025-07-01 22:31:54,501 - INFO - Request Parameters - Page 1:
2025-07-01 22:31:54,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:54,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:55,220 - INFO - Response - Page 1:
2025-07-01 22:31:55,220 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:31:55,736 - INFO - Request Parameters - Page 2:
2025-07-01 22:31:55,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:55,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:56,377 - INFO - Response - Page 2:
2025-07-01 22:31:56,377 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:31:56,892 - INFO - Request Parameters - Page 3:
2025-07-01 22:31:56,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:56,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:57,549 - INFO - Response - Page 3:
2025-07-01 22:31:57,549 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:31:58,065 - INFO - Request Parameters - Page 4:
2025-07-01 22:31:58,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:58,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:58,721 - INFO - Response - Page 4:
2025-07-01 22:31:58,721 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:31:59,237 - INFO - Request Parameters - Page 5:
2025-07-01 22:31:59,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:31:59,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:31:59,878 - INFO - Response - Page 5:
2025-07-01 22:31:59,878 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:32:00,394 - INFO - Request Parameters - Page 6:
2025-07-01 22:32:00,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:00,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:01,050 - INFO - Response - Page 6:
2025-07-01 22:32:01,050 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:32:01,566 - INFO - Request Parameters - Page 7:
2025-07-01 22:32:01,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:01,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:02,223 - INFO - Response - Page 7:
2025-07-01 22:32:02,223 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:32:02,723 - INFO - Request Parameters - Page 8:
2025-07-01 22:32:02,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:02,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:03,379 - INFO - Response - Page 8:
2025-07-01 22:32:03,379 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:32:03,880 - INFO - Request Parameters - Page 9:
2025-07-01 22:32:03,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:03,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:04,536 - INFO - Response - Page 9:
2025-07-01 22:32:04,536 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:32:05,036 - INFO - Request Parameters - Page 10:
2025-07-01 22:32:05,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:05,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751040000000, 1751126399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:05,630 - INFO - Response - Page 10:
2025-07-01 22:32:05,630 - INFO - 第 10 页获取到 29 条记录
2025-07-01 22:32:06,146 - INFO - 查询完成，共获取到 479 条记录
2025-07-01 22:32:06,146 - INFO - 获取到 479 条表单数据
2025-07-01 22:32:06,146 - INFO - 当前日期 2025-06-28 有 2 条MySQL数据需要处理
2025-07-01 22:32:06,146 - INFO - 日期 2025-06-28 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:32:06,146 - INFO - 开始处理日期: 2025-06-30
2025-07-01 22:32:06,146 - INFO - Request Parameters - Page 1:
2025-07-01 22:32:06,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:06,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:06,771 - INFO - Response - Page 1:
2025-07-01 22:32:06,771 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:32:07,272 - INFO - Request Parameters - Page 2:
2025-07-01 22:32:07,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:07,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:07,944 - INFO - Response - Page 2:
2025-07-01 22:32:07,944 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:32:08,460 - INFO - Request Parameters - Page 3:
2025-07-01 22:32:08,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:08,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:09,100 - INFO - Response - Page 3:
2025-07-01 22:32:09,100 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:32:09,601 - INFO - Request Parameters - Page 4:
2025-07-01 22:32:09,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:09,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:10,226 - INFO - Response - Page 4:
2025-07-01 22:32:10,226 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:32:10,742 - INFO - Request Parameters - Page 5:
2025-07-01 22:32:10,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:10,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:11,398 - INFO - Response - Page 5:
2025-07-01 22:32:11,398 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:32:11,914 - INFO - Request Parameters - Page 6:
2025-07-01 22:32:11,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:11,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:12,571 - INFO - Response - Page 6:
2025-07-01 22:32:12,571 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:32:13,071 - INFO - Request Parameters - Page 7:
2025-07-01 22:32:13,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:13,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:13,774 - INFO - Response - Page 7:
2025-07-01 22:32:13,774 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:32:14,290 - INFO - Request Parameters - Page 8:
2025-07-01 22:32:14,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:14,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:14,884 - INFO - Response - Page 8:
2025-07-01 22:32:14,884 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:32:15,384 - INFO - Request Parameters - Page 9:
2025-07-01 22:32:15,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:15,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:16,072 - INFO - Response - Page 9:
2025-07-01 22:32:16,072 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:32:16,588 - INFO - Request Parameters - Page 10:
2025-07-01 22:32:16,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:16,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:17,229 - INFO - Response - Page 10:
2025-07-01 22:32:17,229 - INFO - 第 10 页获取到 50 条记录
2025-07-01 22:32:17,729 - INFO - Request Parameters - Page 11:
2025-07-01 22:32:17,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:17,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:18,338 - INFO - Response - Page 11:
2025-07-01 22:32:18,338 - INFO - 第 11 页获取到 26 条记录
2025-07-01 22:32:18,854 - INFO - 查询完成，共获取到 526 条记录
2025-07-01 22:32:18,854 - INFO - 获取到 526 条表单数据
2025-07-01 22:32:18,854 - INFO - 当前日期 2025-06-30 有 160 条MySQL数据需要处理
2025-07-01 22:32:18,854 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:32:18,854 - INFO - 开始处理日期: 2025-07-01
2025-07-01 22:32:18,854 - INFO - Request Parameters - Page 1:
2025-07-01 22:32:18,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:32:18,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:32:19,292 - INFO - Response - Page 1:
2025-07-01 22:32:19,292 - INFO - 第 1 页获取到 1 条记录
2025-07-01 22:32:19,808 - INFO - 查询完成，共获取到 1 条记录
2025-07-01 22:32:19,808 - INFO - 获取到 1 条表单数据
2025-07-01 22:32:19,808 - INFO - 当前日期 2025-07-01 有 10 条MySQL数据需要处理
2025-07-01 22:32:19,808 - INFO - 开始批量插入 9 条新记录
2025-07-01 22:32:19,964 - INFO - 批量插入响应状态码: 200
2025-07-01 22:32:19,964 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 01 Jul 2025 14:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '435', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '42375D16-83EA-7766-B7FA-E4592DA405FC', 'x-acs-trace-id': '7c7bf567de7b4e838fd93a2235c929d8', 'etag': '4rzPlNHP8MEVK1PSMKZcBAw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-01 22:32:19,964 - INFO - 批量插入响应体: {'result': ['FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCM7', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCM8', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCM9', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMA', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMB', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMC', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMD', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCME', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMF']}
2025-07-01 22:32:19,964 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-07-01 22:32:19,964 - INFO - 成功插入的数据ID: ['FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCM7', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCM8', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCM9', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMA', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMB', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMC', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMD', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCME', 'FINST-V4G66WC1W3SW8SZBCL7E35P7P6CE3RKRMMKCMF']
2025-07-01 22:32:24,982 - INFO - 批量插入完成，共 9 条记录
2025-07-01 22:32:24,982 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-07-01 22:32:24,982 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 1 条
2025-07-01 22:33:25,021 - INFO - 开始同步昨天与今天的销售数据: 2025-06-30 至 2025-07-01
2025-07-01 22:33:25,021 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-01 22:33:25,021 - INFO - 查询参数: ('2025-06-30', '2025-07-01')
2025-07-01 22:33:25,162 - INFO - MySQL查询成功，时间段: 2025-06-30 至 2025-07-01，共获取 548 条记录
2025-07-01 22:33:25,162 - INFO - 获取到 2 个日期需要处理: ['2025-06-30', '2025-07-01']
2025-07-01 22:33:25,177 - INFO - 开始处理日期: 2025-06-30
2025-07-01 22:33:25,177 - INFO - Request Parameters - Page 1:
2025-07-01 22:33:25,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:25,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:25,912 - INFO - Response - Page 1:
2025-07-01 22:33:25,912 - INFO - 第 1 页获取到 50 条记录
2025-07-01 22:33:26,412 - INFO - Request Parameters - Page 2:
2025-07-01 22:33:26,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:26,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:27,100 - INFO - Response - Page 2:
2025-07-01 22:33:27,100 - INFO - 第 2 页获取到 50 条记录
2025-07-01 22:33:27,616 - INFO - Request Parameters - Page 3:
2025-07-01 22:33:27,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:27,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:28,272 - INFO - Response - Page 3:
2025-07-01 22:33:28,272 - INFO - 第 3 页获取到 50 条记录
2025-07-01 22:33:28,788 - INFO - Request Parameters - Page 4:
2025-07-01 22:33:28,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:28,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:29,476 - INFO - Response - Page 4:
2025-07-01 22:33:29,476 - INFO - 第 4 页获取到 50 条记录
2025-07-01 22:33:29,976 - INFO - Request Parameters - Page 5:
2025-07-01 22:33:29,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:29,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:30,648 - INFO - Response - Page 5:
2025-07-01 22:33:30,648 - INFO - 第 5 页获取到 50 条记录
2025-07-01 22:33:31,149 - INFO - Request Parameters - Page 6:
2025-07-01 22:33:31,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:31,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:31,852 - INFO - Response - Page 6:
2025-07-01 22:33:31,852 - INFO - 第 6 页获取到 50 条记录
2025-07-01 22:33:32,352 - INFO - Request Parameters - Page 7:
2025-07-01 22:33:32,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:32,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:32,977 - INFO - Response - Page 7:
2025-07-01 22:33:32,977 - INFO - 第 7 页获取到 50 条记录
2025-07-01 22:33:33,478 - INFO - Request Parameters - Page 8:
2025-07-01 22:33:33,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:33,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:34,181 - INFO - Response - Page 8:
2025-07-01 22:33:34,181 - INFO - 第 8 页获取到 50 条记录
2025-07-01 22:33:34,681 - INFO - Request Parameters - Page 9:
2025-07-01 22:33:34,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:34,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:35,353 - INFO - Response - Page 9:
2025-07-01 22:33:35,353 - INFO - 第 9 页获取到 50 条记录
2025-07-01 22:33:35,854 - INFO - Request Parameters - Page 10:
2025-07-01 22:33:35,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:35,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:36,557 - INFO - Response - Page 10:
2025-07-01 22:33:36,557 - INFO - 第 10 页获取到 50 条记录
2025-07-01 22:33:37,073 - INFO - Request Parameters - Page 11:
2025-07-01 22:33:37,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:37,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:37,667 - INFO - Response - Page 11:
2025-07-01 22:33:37,667 - INFO - 第 11 页获取到 26 条记录
2025-07-01 22:33:38,183 - INFO - 查询完成，共获取到 526 条记录
2025-07-01 22:33:38,183 - INFO - 获取到 526 条表单数据
2025-07-01 22:33:38,183 - INFO - 当前日期 2025-06-30 有 526 条MySQL数据需要处理
2025-07-01 22:33:38,198 - INFO - 日期 2025-06-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:33:38,198 - INFO - 开始处理日期: 2025-07-01
2025-07-01 22:33:38,198 - INFO - Request Parameters - Page 1:
2025-07-01 22:33:38,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-01 22:33:38,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751299200000, 1751385599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-01 22:33:38,792 - INFO - Response - Page 1:
2025-07-01 22:33:38,792 - INFO - 第 1 页获取到 10 条记录
2025-07-01 22:33:39,308 - INFO - 查询完成，共获取到 10 条记录
2025-07-01 22:33:39,308 - INFO - 获取到 10 条表单数据
2025-07-01 22:33:39,308 - INFO - 当前日期 2025-07-01 有 10 条MySQL数据需要处理
2025-07-01 22:33:39,308 - INFO - 日期 2025-07-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:33:39,308 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-01 22:33:39,308 - INFO - 同步完成
