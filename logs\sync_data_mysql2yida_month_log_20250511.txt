2025-05-11 00:00:03,085 - INFO - =================使用默认全量同步=============
2025-05-11 00:00:04,434 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-11 00:00:04,435 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 00:00:04,463 - INFO - 开始处理日期: 2025-01
2025-05-11 00:00:04,467 - INFO - Request Parameters - Page 1:
2025-05-11 00:00:04,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:04,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:05,548 - INFO - Response - Page 1:
2025-05-11 00:00:05,748 - INFO - 第 1 页获取到 100 条记录
2025-05-11 00:00:05,748 - INFO - Request Parameters - Page 2:
2025-05-11 00:00:05,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:05,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:06,466 - INFO - Response - Page 2:
2025-05-11 00:00:06,666 - INFO - 第 2 页获取到 100 条记录
2025-05-11 00:00:06,666 - INFO - Request Parameters - Page 3:
2025-05-11 00:00:06,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:06,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:07,171 - INFO - Response - Page 3:
2025-05-11 00:00:07,371 - INFO - 第 3 页获取到 100 条记录
2025-05-11 00:00:07,371 - INFO - Request Parameters - Page 4:
2025-05-11 00:00:07,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:07,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:07,943 - INFO - Response - Page 4:
2025-05-11 00:00:08,143 - INFO - 第 4 页获取到 100 条记录
2025-05-11 00:00:08,143 - INFO - Request Parameters - Page 5:
2025-05-11 00:00:08,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:08,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:08,717 - INFO - Response - Page 5:
2025-05-11 00:00:08,918 - INFO - 第 5 页获取到 100 条记录
2025-05-11 00:00:08,918 - INFO - Request Parameters - Page 6:
2025-05-11 00:00:08,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:08,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:09,385 - INFO - Response - Page 6:
2025-05-11 00:00:09,585 - INFO - 第 6 页获取到 100 条记录
2025-05-11 00:00:09,585 - INFO - Request Parameters - Page 7:
2025-05-11 00:00:09,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:09,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:10,102 - INFO - Response - Page 7:
2025-05-11 00:00:10,302 - INFO - 第 7 页获取到 82 条记录
2025-05-11 00:00:10,302 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 00:00:10,302 - INFO - 获取到 682 条表单数据
2025-05-11 00:00:10,314 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 00:00:10,327 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 00:00:10,327 - INFO - 开始处理日期: 2025-02
2025-05-11 00:00:10,327 - INFO - Request Parameters - Page 1:
2025-05-11 00:00:10,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:10,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:10,842 - INFO - Response - Page 1:
2025-05-11 00:00:11,042 - INFO - 第 1 页获取到 100 条记录
2025-05-11 00:00:11,042 - INFO - Request Parameters - Page 2:
2025-05-11 00:00:11,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:11,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:11,520 - INFO - Response - Page 2:
2025-05-11 00:00:11,720 - INFO - 第 2 页获取到 100 条记录
2025-05-11 00:00:11,720 - INFO - Request Parameters - Page 3:
2025-05-11 00:00:11,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:11,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:12,180 - INFO - Response - Page 3:
2025-05-11 00:00:12,381 - INFO - 第 3 页获取到 100 条记录
2025-05-11 00:00:12,381 - INFO - Request Parameters - Page 4:
2025-05-11 00:00:12,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:12,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:12,850 - INFO - Response - Page 4:
2025-05-11 00:00:13,051 - INFO - 第 4 页获取到 100 条记录
2025-05-11 00:00:13,051 - INFO - Request Parameters - Page 5:
2025-05-11 00:00:13,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:13,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:13,491 - INFO - Response - Page 5:
2025-05-11 00:00:13,692 - INFO - 第 5 页获取到 100 条记录
2025-05-11 00:00:13,692 - INFO - Request Parameters - Page 6:
2025-05-11 00:00:13,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:13,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:14,191 - INFO - Response - Page 6:
2025-05-11 00:00:14,392 - INFO - 第 6 页获取到 100 条记录
2025-05-11 00:00:14,392 - INFO - Request Parameters - Page 7:
2025-05-11 00:00:14,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:14,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:14,862 - INFO - Response - Page 7:
2025-05-11 00:00:15,063 - INFO - 第 7 页获取到 70 条记录
2025-05-11 00:00:15,063 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 00:00:15,064 - INFO - 获取到 670 条表单数据
2025-05-11 00:00:15,075 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 00:00:15,086 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 00:00:15,086 - INFO - 开始处理日期: 2025-03
2025-05-11 00:00:15,086 - INFO - Request Parameters - Page 1:
2025-05-11 00:00:15,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:15,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:15,542 - INFO - Response - Page 1:
2025-05-11 00:00:15,743 - INFO - 第 1 页获取到 100 条记录
2025-05-11 00:00:15,743 - INFO - Request Parameters - Page 2:
2025-05-11 00:00:15,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:15,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:16,200 - INFO - Response - Page 2:
2025-05-11 00:00:16,400 - INFO - 第 2 页获取到 100 条记录
2025-05-11 00:00:16,400 - INFO - Request Parameters - Page 3:
2025-05-11 00:00:16,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:16,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:16,852 - INFO - Response - Page 3:
2025-05-11 00:00:17,052 - INFO - 第 3 页获取到 100 条记录
2025-05-11 00:00:17,052 - INFO - Request Parameters - Page 4:
2025-05-11 00:00:17,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:17,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:17,533 - INFO - Response - Page 4:
2025-05-11 00:00:17,734 - INFO - 第 4 页获取到 100 条记录
2025-05-11 00:00:17,734 - INFO - Request Parameters - Page 5:
2025-05-11 00:00:17,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:17,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:18,179 - INFO - Response - Page 5:
2025-05-11 00:00:18,379 - INFO - 第 5 页获取到 100 条记录
2025-05-11 00:00:18,379 - INFO - Request Parameters - Page 6:
2025-05-11 00:00:18,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:18,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:18,888 - INFO - Response - Page 6:
2025-05-11 00:00:19,088 - INFO - 第 6 页获取到 100 条记录
2025-05-11 00:00:19,088 - INFO - Request Parameters - Page 7:
2025-05-11 00:00:19,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:19,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:19,549 - INFO - Response - Page 7:
2025-05-11 00:00:19,750 - INFO - 第 7 页获取到 61 条记录
2025-05-11 00:00:19,750 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 00:00:19,750 - INFO - 获取到 661 条表单数据
2025-05-11 00:00:19,762 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 00:00:19,774 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 00:00:19,774 - INFO - 开始处理日期: 2025-04
2025-05-11 00:00:19,774 - INFO - Request Parameters - Page 1:
2025-05-11 00:00:19,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:19,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:20,318 - INFO - Response - Page 1:
2025-05-11 00:00:20,520 - INFO - 第 1 页获取到 100 条记录
2025-05-11 00:00:20,520 - INFO - Request Parameters - Page 2:
2025-05-11 00:00:20,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:20,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:21,073 - INFO - Response - Page 2:
2025-05-11 00:00:21,274 - INFO - 第 2 页获取到 100 条记录
2025-05-11 00:00:21,274 - INFO - Request Parameters - Page 3:
2025-05-11 00:00:21,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:21,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:21,764 - INFO - Response - Page 3:
2025-05-11 00:00:21,964 - INFO - 第 3 页获取到 100 条记录
2025-05-11 00:00:21,964 - INFO - Request Parameters - Page 4:
2025-05-11 00:00:21,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:21,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:22,408 - INFO - Response - Page 4:
2025-05-11 00:00:22,609 - INFO - 第 4 页获取到 100 条记录
2025-05-11 00:00:22,609 - INFO - Request Parameters - Page 5:
2025-05-11 00:00:22,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:22,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:23,086 - INFO - Response - Page 5:
2025-05-11 00:00:23,287 - INFO - 第 5 页获取到 100 条记录
2025-05-11 00:00:23,287 - INFO - Request Parameters - Page 6:
2025-05-11 00:00:23,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:23,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:23,763 - INFO - Response - Page 6:
2025-05-11 00:00:23,964 - INFO - 第 6 页获取到 100 条记录
2025-05-11 00:00:23,964 - INFO - Request Parameters - Page 7:
2025-05-11 00:00:23,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:23,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:24,411 - INFO - Response - Page 7:
2025-05-11 00:00:24,611 - INFO - 第 7 页获取到 54 条记录
2025-05-11 00:00:24,611 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 00:00:24,611 - INFO - 获取到 654 条表单数据
2025-05-11 00:00:24,625 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 00:00:24,637 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 00:00:24,637 - INFO - 开始处理日期: 2025-05
2025-05-11 00:00:24,637 - INFO - Request Parameters - Page 1:
2025-05-11 00:00:24,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:24,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:25,077 - INFO - Response - Page 1:
2025-05-11 00:00:25,277 - INFO - 第 1 页获取到 100 条记录
2025-05-11 00:00:25,277 - INFO - Request Parameters - Page 2:
2025-05-11 00:00:25,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:25,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:25,780 - INFO - Response - Page 2:
2025-05-11 00:00:25,981 - INFO - 第 2 页获取到 100 条记录
2025-05-11 00:00:25,981 - INFO - Request Parameters - Page 3:
2025-05-11 00:00:25,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:25,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:26,407 - INFO - Response - Page 3:
2025-05-11 00:00:26,607 - INFO - 第 3 页获取到 100 条记录
2025-05-11 00:00:26,607 - INFO - Request Parameters - Page 4:
2025-05-11 00:00:26,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:26,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:27,081 - INFO - Response - Page 4:
2025-05-11 00:00:27,281 - INFO - 第 4 页获取到 100 条记录
2025-05-11 00:00:27,281 - INFO - Request Parameters - Page 5:
2025-05-11 00:00:27,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:27,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:27,782 - INFO - Response - Page 5:
2025-05-11 00:00:27,982 - INFO - 第 5 页获取到 100 条记录
2025-05-11 00:00:27,982 - INFO - Request Parameters - Page 6:
2025-05-11 00:00:27,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:27,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:28,455 - INFO - Response - Page 6:
2025-05-11 00:00:28,655 - INFO - 第 6 页获取到 100 条记录
2025-05-11 00:00:28,655 - INFO - Request Parameters - Page 7:
2025-05-11 00:00:28,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 00:00:28,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 00:00:28,978 - INFO - Response - Page 7:
2025-05-11 00:00:29,179 - INFO - 第 7 页获取到 21 条记录
2025-05-11 00:00:29,179 - INFO - 查询完成，共获取到 621 条记录
2025-05-11 00:00:29,179 - INFO - 获取到 621 条表单数据
2025-05-11 00:00:29,191 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-11 00:00:29,191 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-11 00:00:29,601 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-11 00:00:29,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4400000.0, 'new_value': 4600000.0}, {'field': 'total_amount', 'old_value': 4500000.0, 'new_value': 4700000.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-11 00:00:29,602 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-11 00:00:29,942 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-11 00:00:29,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52693.0, 'new_value': 55542.0}, {'field': 'offline_amount', 'old_value': 38769.28, 'new_value': 45234.28}, {'field': 'total_amount', 'old_value': 91462.28, 'new_value': 100776.28}, {'field': 'order_count', 'old_value': 1936, 'new_value': 2135}]
2025-05-11 00:00:29,943 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-11 00:00:30,403 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-11 00:00:30,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19451.2, 'new_value': 21471.1}, {'field': 'total_amount', 'old_value': 23411.2, 'new_value': 25431.1}, {'field': 'order_count', 'old_value': 144, 'new_value': 161}]
2025-05-11 00:00:30,405 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-11 00:00:30,869 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-11 00:00:30,869 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5271.2, 'new_value': 7140.2}, {'field': 'offline_amount', 'old_value': 38472.66, 'new_value': 42110.66}, {'field': 'total_amount', 'old_value': 43743.86, 'new_value': 49250.86}, {'field': 'order_count', 'old_value': 95, 'new_value': 104}]
2025-05-11 00:00:30,869 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-11 00:00:31,248 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-11 00:00:31,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3738.0, 'new_value': 6535.0}, {'field': 'total_amount', 'old_value': 3738.0, 'new_value': 6535.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 16}]
2025-05-11 00:00:31,249 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-11 00:00:31,688 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-11 00:00:31,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18777.97, 'new_value': 20140.73}, {'field': 'offline_amount', 'old_value': 243377.57, 'new_value': 269224.42}, {'field': 'total_amount', 'old_value': 262155.54, 'new_value': 289365.15}, {'field': 'order_count', 'old_value': 1077, 'new_value': 1202}]
2025-05-11 00:00:31,689 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-11 00:00:32,089 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-11 00:00:32,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12210.7, 'new_value': 14330.7}, {'field': 'total_amount', 'old_value': 12210.7, 'new_value': 14330.7}, {'field': 'order_count', 'old_value': 53, 'new_value': 73}]
2025-05-11 00:00:32,089 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-11 00:00:32,533 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-11 00:00:32,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15848.95, 'new_value': 16497.45}, {'field': 'total_amount', 'old_value': 15914.5, 'new_value': 16563.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 157}]
2025-05-11 00:00:32,534 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-11 00:00:33,000 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-11 00:00:33,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17579.81, 'new_value': 21741.17}, {'field': 'total_amount', 'old_value': 54600.04, 'new_value': 58761.4}, {'field': 'order_count', 'old_value': 1946, 'new_value': 2128}]
2025-05-11 00:00:33,001 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-11 00:00:33,379 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-11 00:00:33,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28364.0, 'new_value': 30394.0}, {'field': 'total_amount', 'old_value': 28364.0, 'new_value': 30394.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 58}]
2025-05-11 00:00:33,379 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-11 00:00:33,902 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-11 00:00:33,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91040.79, 'new_value': 91988.89}, {'field': 'offline_amount', 'old_value': 46566.4, 'new_value': 64902.61}, {'field': 'total_amount', 'old_value': 137607.19, 'new_value': 156891.5}, {'field': 'order_count', 'old_value': 526, 'new_value': 590}]
2025-05-11 00:00:33,902 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-11 00:00:34,276 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-11 00:00:34,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41755.17, 'new_value': 47319.02}, {'field': 'total_amount', 'old_value': 41755.17, 'new_value': 47319.02}, {'field': 'order_count', 'old_value': 1585, 'new_value': 1812}]
2025-05-11 00:00:34,276 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-11 00:00:34,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-11 00:00:34,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8058.94, 'new_value': 9577.08}, {'field': 'offline_amount', 'old_value': 104466.16, 'new_value': 122587.22}, {'field': 'total_amount', 'old_value': 112525.1, 'new_value': 132164.3}, {'field': 'order_count', 'old_value': 537, 'new_value': 627}]
2025-05-11 00:00:34,673 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-11 00:00:35,102 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-11 00:00:35,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12773.59, 'new_value': 14418.14}, {'field': 'offline_amount', 'old_value': 215275.5, 'new_value': 257365.85}, {'field': 'total_amount', 'old_value': 228049.09, 'new_value': 271783.99}, {'field': 'order_count', 'old_value': 1420, 'new_value': 1622}]
2025-05-11 00:00:35,102 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-11 00:00:35,532 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-11 00:00:35,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4052.84, 'new_value': 6142.04}, {'field': 'offline_amount', 'old_value': 5514.0, 'new_value': 5583.5}, {'field': 'total_amount', 'old_value': 9566.84, 'new_value': 11725.54}, {'field': 'order_count', 'old_value': 43, 'new_value': 50}]
2025-05-11 00:00:35,532 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-11 00:00:35,943 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-11 00:00:35,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31671.0, 'new_value': 33001.0}, {'field': 'total_amount', 'old_value': 31671.0, 'new_value': 33001.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-11 00:00:35,943 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-11 00:00:36,350 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-11 00:00:36,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46293.0, 'new_value': 51612.6}, {'field': 'total_amount', 'old_value': 46293.0, 'new_value': 51612.6}, {'field': 'order_count', 'old_value': 133, 'new_value': 151}]
2025-05-11 00:00:36,350 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-11 00:00:36,811 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-11 00:00:36,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55238.36, 'new_value': 63172.94}, {'field': 'offline_amount', 'old_value': 154753.56, 'new_value': 174250.05}, {'field': 'total_amount', 'old_value': 209991.92, 'new_value': 237422.99}, {'field': 'order_count', 'old_value': 5171, 'new_value': 5858}]
2025-05-11 00:00:36,812 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-11 00:00:37,162 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-11 00:00:37,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20250.0, 'new_value': 24120.0}, {'field': 'total_amount', 'old_value': 20250.0, 'new_value': 24120.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 58}]
2025-05-11 00:00:37,163 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-11 00:00:37,546 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-11 00:00:37,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17786.0, 'new_value': 19107.0}, {'field': 'total_amount', 'old_value': 17786.0, 'new_value': 19107.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-05-11 00:00:37,547 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-11 00:00:37,913 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-11 00:00:37,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140519.0, 'new_value': 163255.0}, {'field': 'total_amount', 'old_value': 140519.0, 'new_value': 163255.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 130}]
2025-05-11 00:00:37,913 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-11 00:00:38,333 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-11 00:00:38,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2983.0, 'new_value': 3082.0}, {'field': 'total_amount', 'old_value': 2983.0, 'new_value': 3082.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-11 00:00:38,334 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-11 00:00:38,728 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-11 00:00:38,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145783.1, 'new_value': 161759.0}, {'field': 'total_amount', 'old_value': 145783.1, 'new_value': 161759.0}, {'field': 'order_count', 'old_value': 1622, 'new_value': 1813}]
2025-05-11 00:00:38,728 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-11 00:00:39,130 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-11 00:00:39,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 736727.12, 'new_value': 800232.49}, {'field': 'total_amount', 'old_value': 736727.12, 'new_value': 800232.49}, {'field': 'order_count', 'old_value': 5386, 'new_value': 5997}]
2025-05-11 00:00:39,131 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-11 00:00:39,531 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-11 00:00:39,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121909.2, 'new_value': 131662.2}, {'field': 'total_amount', 'old_value': 121909.2, 'new_value': 131662.2}, {'field': 'order_count', 'old_value': 211, 'new_value': 220}]
2025-05-11 00:00:39,532 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-11 00:00:39,941 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-11 00:00:39,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214177.53, 'new_value': 249010.87}, {'field': 'total_amount', 'old_value': 250078.25, 'new_value': 284911.59}, {'field': 'order_count', 'old_value': 996, 'new_value': 1103}]
2025-05-11 00:00:39,941 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-11 00:00:40,305 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-11 00:00:40,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49095.28, 'new_value': 53127.85}, {'field': 'total_amount', 'old_value': 52616.8, 'new_value': 56649.37}, {'field': 'order_count', 'old_value': 4742, 'new_value': 5011}]
2025-05-11 00:00:40,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-11 00:00:40,698 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-11 00:00:40,699 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3131.68, 'new_value': 3847.25}, {'field': 'offline_amount', 'old_value': 32663.47, 'new_value': 37449.83}, {'field': 'total_amount', 'old_value': 35795.15, 'new_value': 41297.08}, {'field': 'order_count', 'old_value': 1116, 'new_value': 1276}]
2025-05-11 00:00:40,699 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-11 00:00:41,182 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-11 00:00:41,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2675.28, 'new_value': 3118.89}, {'field': 'offline_amount', 'old_value': 5905.64, 'new_value': 6466.84}, {'field': 'total_amount', 'old_value': 8580.92, 'new_value': 9585.73}, {'field': 'order_count', 'old_value': 619, 'new_value': 703}]
2025-05-11 00:00:41,182 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-11 00:00:41,659 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-11 00:00:41,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42156.0, 'new_value': 46009.0}, {'field': 'total_amount', 'old_value': 42156.0, 'new_value': 46009.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 56}]
2025-05-11 00:00:41,660 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-11 00:00:42,045 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-11 00:00:42,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19572.33, 'new_value': 23528.83}, {'field': 'offline_amount', 'old_value': 172007.07, 'new_value': 197534.83}, {'field': 'total_amount', 'old_value': 191579.4, 'new_value': 221063.66}, {'field': 'order_count', 'old_value': 1510, 'new_value': 1741}]
2025-05-11 00:00:42,046 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-11 00:00:42,522 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-11 00:00:42,522 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35856.59, 'new_value': 38798.43}, {'field': 'offline_amount', 'old_value': 19348.3, 'new_value': 21678.3}, {'field': 'total_amount', 'old_value': 55204.89, 'new_value': 60476.73}, {'field': 'order_count', 'old_value': 3665, 'new_value': 3933}]
2025-05-11 00:00:42,522 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-11 00:00:42,959 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-11 00:00:42,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158055.02, 'new_value': 181772.5}, {'field': 'total_amount', 'old_value': 158055.02, 'new_value': 181772.5}, {'field': 'order_count', 'old_value': 729, 'new_value': 835}]
2025-05-11 00:00:42,960 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-11 00:00:43,363 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-11 00:00:43,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47595.7, 'new_value': 50763.7}, {'field': 'total_amount', 'old_value': 47595.7, 'new_value': 50763.7}, {'field': 'order_count', 'old_value': 401, 'new_value': 436}]
2025-05-11 00:00:43,363 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-11 00:00:43,752 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-11 00:00:43,752 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38166.69, 'new_value': 41896.42}, {'field': 'offline_amount', 'old_value': 101065.62, 'new_value': 114376.34}, {'field': 'total_amount', 'old_value': 139232.31, 'new_value': 156272.76}, {'field': 'order_count', 'old_value': 4413, 'new_value': 4941}]
2025-05-11 00:00:43,753 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-11 00:00:44,169 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-11 00:00:44,169 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117363.58, 'new_value': 130425.27}, {'field': 'offline_amount', 'old_value': 6762.5, 'new_value': 8513.35}, {'field': 'total_amount', 'old_value': 124126.08, 'new_value': 138938.62}, {'field': 'order_count', 'old_value': 4627, 'new_value': 5150}]
2025-05-11 00:00:44,170 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-11 00:00:44,543 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-11 00:00:44,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13104.2, 'new_value': 15072.5}, {'field': 'offline_amount', 'old_value': 42339.8, 'new_value': 44761.8}, {'field': 'total_amount', 'old_value': 55444.0, 'new_value': 59834.3}, {'field': 'order_count', 'old_value': 77, 'new_value': 91}]
2025-05-11 00:00:44,544 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-11 00:00:44,953 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-11 00:00:44,954 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45511.92, 'new_value': 53199.31}, {'field': 'offline_amount', 'old_value': 112564.03, 'new_value': 127840.65}, {'field': 'total_amount', 'old_value': 158075.95, 'new_value': 181039.96}, {'field': 'order_count', 'old_value': 1821, 'new_value': 2077}]
2025-05-11 00:00:44,954 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-11 00:00:45,363 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-11 00:00:45,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34872.3, 'new_value': 40691.4}, {'field': 'total_amount', 'old_value': 34872.3, 'new_value': 40691.4}, {'field': 'order_count', 'old_value': 273, 'new_value': 309}]
2025-05-11 00:00:45,364 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-11 00:00:45,738 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-11 00:00:45,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27083.11, 'new_value': 31270.91}, {'field': 'offline_amount', 'old_value': 98811.68, 'new_value': 114558.6}, {'field': 'total_amount', 'old_value': 125894.79, 'new_value': 145829.51}, {'field': 'order_count', 'old_value': 1744, 'new_value': 1921}]
2025-05-11 00:00:45,738 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-11 00:00:46,177 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-11 00:00:46,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35278.2, 'new_value': 36980.2}, {'field': 'total_amount', 'old_value': 35278.2, 'new_value': 36980.2}, {'field': 'order_count', 'old_value': 95, 'new_value': 100}]
2025-05-11 00:00:46,178 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-11 00:00:46,552 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-11 00:00:46,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9983.88, 'new_value': 10969.56}, {'field': 'total_amount', 'old_value': 10633.88, 'new_value': 11619.56}, {'field': 'order_count', 'old_value': 201, 'new_value': 221}]
2025-05-11 00:00:46,552 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-11 00:00:46,930 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-11 00:00:46,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7956.0, 'new_value': 10846.0}, {'field': 'total_amount', 'old_value': 7956.0, 'new_value': 10846.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-11 00:00:46,930 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-11 00:00:47,348 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-11 00:00:47,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15728.0, 'new_value': 17054.0}, {'field': 'total_amount', 'old_value': 15728.0, 'new_value': 17054.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 156}]
2025-05-11 00:00:47,348 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-11 00:00:47,866 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-11 00:00:47,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224065.0, 'new_value': 233065.0}, {'field': 'total_amount', 'old_value': 224065.0, 'new_value': 233065.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 33}]
2025-05-11 00:00:47,866 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-11 00:00:48,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-11 00:00:48,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28487.22, 'new_value': 34281.07}, {'field': 'offline_amount', 'old_value': 54990.08, 'new_value': 63590.91}, {'field': 'total_amount', 'old_value': 83477.3, 'new_value': 97871.98}, {'field': 'order_count', 'old_value': 831, 'new_value': 960}]
2025-05-11 00:00:48,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-11 00:00:48,674 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-11 00:00:48,674 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10273.0, 'new_value': 11772.0}, {'field': 'total_amount', 'old_value': 10273.0, 'new_value': 11772.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-11 00:00:48,674 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-11 00:00:49,126 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-11 00:00:49,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4316.6, 'new_value': 5011.6}, {'field': 'total_amount', 'old_value': 4414.6, 'new_value': 5109.6}, {'field': 'order_count', 'old_value': 45, 'new_value': 52}]
2025-05-11 00:00:49,127 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-11 00:00:49,567 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-11 00:00:49,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163803.0, 'new_value': 245288.0}, {'field': 'total_amount', 'old_value': 164243.0, 'new_value': 245728.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 120}]
2025-05-11 00:00:49,567 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-11 00:00:49,956 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-11 00:00:49,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12761.0, 'new_value': 15073.0}, {'field': 'total_amount', 'old_value': 12761.0, 'new_value': 15073.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 64}]
2025-05-11 00:00:49,957 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-11 00:00:50,370 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-11 00:00:50,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40723.7, 'new_value': 47780.9}, {'field': 'offline_amount', 'old_value': 57440.5, 'new_value': 69814.3}, {'field': 'total_amount', 'old_value': 98164.2, 'new_value': 117595.2}, {'field': 'order_count', 'old_value': 1964, 'new_value': 2353}]
2025-05-11 00:00:50,370 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-11 00:00:50,796 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-11 00:00:50,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195517.74, 'new_value': 223666.42}, {'field': 'total_amount', 'old_value': 195517.74, 'new_value': 223666.42}, {'field': 'order_count', 'old_value': 2607, 'new_value': 2965}]
2025-05-11 00:00:50,797 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-11 00:00:51,254 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-11 00:00:51,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82776.0, 'new_value': 87924.0}, {'field': 'total_amount', 'old_value': 82776.0, 'new_value': 87924.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 142}]
2025-05-11 00:00:51,254 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-11 00:00:51,611 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-11 00:00:51,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35111.82, 'new_value': 40605.79}, {'field': 'total_amount', 'old_value': 35111.82, 'new_value': 40605.79}, {'field': 'order_count', 'old_value': 1090, 'new_value': 1237}]
2025-05-11 00:00:51,611 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-11 00:00:51,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-11 00:00:51,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3400.34, 'new_value': 3908.94}, {'field': 'offline_amount', 'old_value': 12080.9, 'new_value': 13987.0}, {'field': 'total_amount', 'old_value': 15481.24, 'new_value': 17895.94}, {'field': 'order_count', 'old_value': 552, 'new_value': 642}]
2025-05-11 00:00:51,980 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-11 00:00:52,550 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-11 00:00:52,550 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15678.77, 'new_value': 16974.6}, {'field': 'offline_amount', 'old_value': 141019.18, 'new_value': 162537.47}, {'field': 'total_amount', 'old_value': 156697.95, 'new_value': 179512.07}, {'field': 'order_count', 'old_value': 3526, 'new_value': 4133}]
2025-05-11 00:00:52,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-11 00:00:52,928 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-11 00:00:52,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196957.7, 'new_value': 217070.3}, {'field': 'total_amount', 'old_value': 196957.7, 'new_value': 217070.3}, {'field': 'order_count', 'old_value': 846, 'new_value': 954}]
2025-05-11 00:00:52,928 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-11 00:00:53,297 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-11 00:00:53,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69068.11, 'new_value': 73712.2}, {'field': 'offline_amount', 'old_value': 22083.59, 'new_value': 24901.93}, {'field': 'total_amount', 'old_value': 91151.7, 'new_value': 98614.13}, {'field': 'order_count', 'old_value': 5808, 'new_value': 6260}]
2025-05-11 00:00:53,297 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-11 00:00:53,683 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-11 00:00:53,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91668.0, 'new_value': 102693.0}, {'field': 'total_amount', 'old_value': 91668.0, 'new_value': 102693.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 103}]
2025-05-11 00:00:53,683 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-11 00:00:54,147 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-11 00:00:54,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314243.28, 'new_value': 360206.94}, {'field': 'total_amount', 'old_value': 314243.28, 'new_value': 360206.94}, {'field': 'order_count', 'old_value': 6330, 'new_value': 7272}]
2025-05-11 00:00:54,148 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-11 00:00:54,543 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-11 00:00:54,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107629.54, 'new_value': 120834.65}, {'field': 'total_amount', 'old_value': 107629.54, 'new_value': 120834.65}, {'field': 'order_count', 'old_value': 4482, 'new_value': 5060}]
2025-05-11 00:00:54,543 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-11 00:00:54,936 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-11 00:00:54,936 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100899.0, 'new_value': 109627.0}, {'field': 'total_amount', 'old_value': 100899.0, 'new_value': 109627.0}, {'field': 'order_count', 'old_value': 291, 'new_value': 320}]
2025-05-11 00:00:54,936 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-11 00:00:55,354 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-11 00:00:55,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 561.42, 'new_value': 684.47}, {'field': 'offline_amount', 'old_value': 12744.01, 'new_value': 14040.34}, {'field': 'total_amount', 'old_value': 13305.43, 'new_value': 14724.81}, {'field': 'order_count', 'old_value': 465, 'new_value': 516}]
2025-05-11 00:00:55,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-11 00:00:55,748 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-11 00:00:55,748 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 297468.7, 'new_value': 328681.0}, {'field': 'total_amount', 'old_value': 297468.7, 'new_value': 328681.0}, {'field': 'order_count', 'old_value': 1927, 'new_value': 2155}]
2025-05-11 00:00:55,749 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-11 00:00:56,144 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-11 00:00:56,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2784.39, 'new_value': 3232.39}, {'field': 'offline_amount', 'old_value': 159334.74, 'new_value': 180922.24}, {'field': 'total_amount', 'old_value': 162119.13, 'new_value': 184154.63}, {'field': 'order_count', 'old_value': 7298, 'new_value': 8265}]
2025-05-11 00:00:56,144 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-11 00:00:56,612 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-11 00:00:56,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33682.0, 'new_value': 40419.0}, {'field': 'total_amount', 'old_value': 33682.0, 'new_value': 40419.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 100}]
2025-05-11 00:00:56,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-11 00:00:57,044 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-11 00:00:57,044 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25005.22, 'new_value': 27628.68}, {'field': 'offline_amount', 'old_value': 37994.05, 'new_value': 41713.13}, {'field': 'total_amount', 'old_value': 62999.27, 'new_value': 69341.81}, {'field': 'order_count', 'old_value': 2885, 'new_value': 3208}]
2025-05-11 00:00:57,045 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-11 00:00:57,454 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-11 00:00:57,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135016.45, 'new_value': 160342.1}, {'field': 'total_amount', 'old_value': 157178.83, 'new_value': 182504.48}, {'field': 'order_count', 'old_value': 6465, 'new_value': 7441}]
2025-05-11 00:00:57,454 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-11 00:00:57,878 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-11 00:00:57,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6745.9, 'new_value': 7136.3}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3000.0}, {'field': 'total_amount', 'old_value': 6745.9, 'new_value': 10136.3}, {'field': 'order_count', 'old_value': 101, 'new_value': 108}]
2025-05-11 00:00:57,879 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-11 00:00:58,345 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-11 00:00:58,346 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8903.95, 'new_value': 9882.87}, {'field': 'offline_amount', 'old_value': 94590.84, 'new_value': 110909.94}, {'field': 'total_amount', 'old_value': 103494.79, 'new_value': 120792.81}, {'field': 'order_count', 'old_value': 3160, 'new_value': 3684}]
2025-05-11 00:00:58,346 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-11 00:00:58,823 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-11 00:00:58,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72993.1, 'new_value': 76458.7}, {'field': 'total_amount', 'old_value': 72993.1, 'new_value': 76458.7}, {'field': 'order_count', 'old_value': 129, 'new_value': 136}]
2025-05-11 00:00:58,824 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-11 00:00:59,210 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-11 00:00:59,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90322.08, 'new_value': 108461.41}, {'field': 'total_amount', 'old_value': 109495.51, 'new_value': 127634.84}, {'field': 'order_count', 'old_value': 2315, 'new_value': 2709}]
2025-05-11 00:00:59,210 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-11 00:00:59,618 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-11 00:00:59,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42067.0, 'new_value': 48313.0}, {'field': 'offline_amount', 'old_value': 36339.22, 'new_value': 42584.44}, {'field': 'total_amount', 'old_value': 78406.22, 'new_value': 90897.44}, {'field': 'order_count', 'old_value': 518, 'new_value': 595}]
2025-05-11 00:00:59,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-11 00:01:00,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-11 00:01:00,010 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399264.0, 'new_value': 426584.0}, {'field': 'total_amount', 'old_value': 399264.0, 'new_value': 426584.0}, {'field': 'order_count', 'old_value': 448, 'new_value': 477}]
2025-05-11 00:01:00,010 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-11 00:01:00,408 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-11 00:01:00,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257563.0, 'new_value': 308034.0}, {'field': 'total_amount', 'old_value': 257563.0, 'new_value': 308034.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-11 00:01:00,408 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-11 00:01:00,852 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-11 00:01:00,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13926.2, 'new_value': 15441.2}, {'field': 'total_amount', 'old_value': 13926.2, 'new_value': 15441.2}, {'field': 'order_count', 'old_value': 76, 'new_value': 86}]
2025-05-11 00:01:00,853 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-11 00:01:01,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-11 00:01:01,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106747.0, 'new_value': 122249.6}, {'field': 'total_amount', 'old_value': 106747.0, 'new_value': 122249.6}, {'field': 'order_count', 'old_value': 589, 'new_value': 669}]
2025-05-11 00:01:01,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-11 00:01:01,566 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-11 00:01:01,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35833.12, 'new_value': 37328.87}, {'field': 'offline_amount', 'old_value': 74664.99, 'new_value': 88959.69}, {'field': 'total_amount', 'old_value': 110498.11, 'new_value': 126288.56}, {'field': 'order_count', 'old_value': 1857, 'new_value': 2109}]
2025-05-11 00:01:01,566 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-11 00:01:02,004 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-11 00:01:02,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12559.88, 'new_value': 14752.56}, {'field': 'offline_amount', 'old_value': 143486.52, 'new_value': 165686.42}, {'field': 'total_amount', 'old_value': 156046.4, 'new_value': 180438.98}, {'field': 'order_count', 'old_value': 914, 'new_value': 1051}]
2025-05-11 00:01:02,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-11 00:01:02,510 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-11 00:01:02,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78091.13, 'new_value': 87920.97}, {'field': 'total_amount', 'old_value': 78091.13, 'new_value': 87920.97}, {'field': 'order_count', 'old_value': 3242, 'new_value': 3659}]
2025-05-11 00:01:02,511 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-11 00:01:02,950 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-11 00:01:02,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91742.34, 'new_value': 105276.38}, {'field': 'total_amount', 'old_value': 91742.34, 'new_value': 105276.38}, {'field': 'order_count', 'old_value': 668, 'new_value': 763}]
2025-05-11 00:01:02,950 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-11 00:01:03,353 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-11 00:01:03,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47301.22, 'new_value': 56548.41}, {'field': 'total_amount', 'old_value': 66340.82, 'new_value': 75588.01}, {'field': 'order_count', 'old_value': 1961, 'new_value': 2195}]
2025-05-11 00:01:03,354 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-11 00:01:03,803 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-11 00:01:03,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15032.0, 'new_value': 17308.0}, {'field': 'total_amount', 'old_value': 15032.0, 'new_value': 17308.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-05-11 00:01:03,804 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-11 00:01:04,197 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-11 00:01:04,197 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16066.68, 'new_value': 17066.68}, {'field': 'offline_amount', 'old_value': 16628.96, 'new_value': 18273.43}, {'field': 'total_amount', 'old_value': 32695.64, 'new_value': 35340.11}, {'field': 'order_count', 'old_value': 1450, 'new_value': 1598}]
2025-05-11 00:01:04,197 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-11 00:01:04,595 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-11 00:01:04,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 209.0, 'new_value': 428.0}, {'field': 'offline_amount', 'old_value': 19017.1, 'new_value': 19097.1}, {'field': 'total_amount', 'old_value': 19226.1, 'new_value': 19525.1}, {'field': 'order_count', 'old_value': 9227, 'new_value': 9229}]
2025-05-11 00:01:04,596 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-11 00:01:04,977 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-11 00:01:04,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86237.7, 'new_value': 102581.61}, {'field': 'offline_amount', 'old_value': 201000.0, 'new_value': 213000.0}, {'field': 'total_amount', 'old_value': 287237.7, 'new_value': 315581.61}, {'field': 'order_count', 'old_value': 559, 'new_value': 633}]
2025-05-11 00:01:04,977 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-11 00:01:05,410 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-11 00:01:05,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16736.37, 'new_value': 18625.83}, {'field': 'offline_amount', 'old_value': 10540.3, 'new_value': 11689.18}, {'field': 'total_amount', 'old_value': 27276.67, 'new_value': 30315.01}, {'field': 'order_count', 'old_value': 1165, 'new_value': 1298}]
2025-05-11 00:01:05,411 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-11 00:01:05,851 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-11 00:01:05,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6376.58, 'new_value': 7198.0}, {'field': 'offline_amount', 'old_value': 15397.0, 'new_value': 17686.5}, {'field': 'total_amount', 'old_value': 21773.58, 'new_value': 24884.5}, {'field': 'order_count', 'old_value': 870, 'new_value': 995}]
2025-05-11 00:01:05,851 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-11 00:01:06,292 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-11 00:01:06,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190346.7, 'new_value': 211893.7}, {'field': 'total_amount', 'old_value': 190346.7, 'new_value': 211893.7}]
2025-05-11 00:01:06,292 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-11 00:01:06,719 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-11 00:01:06,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189220.0, 'new_value': 203107.0}, {'field': 'total_amount', 'old_value': 189220.0, 'new_value': 203107.0}, {'field': 'order_count', 'old_value': 1459, 'new_value': 1650}]
2025-05-11 00:01:06,720 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-11 00:01:07,239 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-11 00:01:07,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112689.0, 'new_value': 123545.0}, {'field': 'total_amount', 'old_value': 112689.0, 'new_value': 123545.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 137}]
2025-05-11 00:01:07,240 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-11 00:01:07,658 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-11 00:01:07,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31650.1, 'new_value': 37410.04}, {'field': 'total_amount', 'old_value': 31650.1, 'new_value': 37410.04}, {'field': 'order_count', 'old_value': 445, 'new_value': 523}]
2025-05-11 00:01:07,659 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-11 00:01:08,108 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-11 00:01:08,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162398.0, 'new_value': 170295.0}, {'field': 'total_amount', 'old_value': 163394.0, 'new_value': 171291.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-05-11 00:01:08,109 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-11 00:01:08,514 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-11 00:01:08,514 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8373.28, 'new_value': 12595.01}, {'field': 'total_amount', 'old_value': 8373.28, 'new_value': 12595.01}, {'field': 'order_count', 'old_value': 240, 'new_value': 409}]
2025-05-11 00:01:08,514 - INFO - 日期 2025-05 处理完成 - 更新: 94 条，插入: 0 条，错误: 0 条
2025-05-11 00:01:08,514 - INFO - 数据同步完成！更新: 94 条，插入: 0 条，错误: 0 条
2025-05-11 00:01:08,516 - INFO - =================同步完成====================
2025-05-11 03:00:03,192 - INFO - =================使用默认全量同步=============
2025-05-11 03:00:04,572 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-11 03:00:04,573 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 03:00:04,602 - INFO - 开始处理日期: 2025-01
2025-05-11 03:00:04,605 - INFO - Request Parameters - Page 1:
2025-05-11 03:00:04,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:04,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:05,606 - INFO - Response - Page 1:
2025-05-11 03:00:05,806 - INFO - 第 1 页获取到 100 条记录
2025-05-11 03:00:05,806 - INFO - Request Parameters - Page 2:
2025-05-11 03:00:05,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:05,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:06,605 - INFO - Response - Page 2:
2025-05-11 03:00:06,806 - INFO - 第 2 页获取到 100 条记录
2025-05-11 03:00:06,806 - INFO - Request Parameters - Page 3:
2025-05-11 03:00:06,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:06,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:07,314 - INFO - Response - Page 3:
2025-05-11 03:00:07,514 - INFO - 第 3 页获取到 100 条记录
2025-05-11 03:00:07,514 - INFO - Request Parameters - Page 4:
2025-05-11 03:00:07,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:07,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:08,103 - INFO - Response - Page 4:
2025-05-11 03:00:08,304 - INFO - 第 4 页获取到 100 条记录
2025-05-11 03:00:08,304 - INFO - Request Parameters - Page 5:
2025-05-11 03:00:08,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:08,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:08,826 - INFO - Response - Page 5:
2025-05-11 03:00:09,028 - INFO - 第 5 页获取到 100 条记录
2025-05-11 03:00:09,028 - INFO - Request Parameters - Page 6:
2025-05-11 03:00:09,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:09,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:09,494 - INFO - Response - Page 6:
2025-05-11 03:00:09,694 - INFO - 第 6 页获取到 100 条记录
2025-05-11 03:00:09,694 - INFO - Request Parameters - Page 7:
2025-05-11 03:00:09,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:09,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:10,139 - INFO - Response - Page 7:
2025-05-11 03:00:10,339 - INFO - 第 7 页获取到 82 条记录
2025-05-11 03:00:10,339 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 03:00:10,339 - INFO - 获取到 682 条表单数据
2025-05-11 03:00:10,351 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 03:00:10,361 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 03:00:10,362 - INFO - 开始处理日期: 2025-02
2025-05-11 03:00:10,362 - INFO - Request Parameters - Page 1:
2025-05-11 03:00:10,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:10,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:10,809 - INFO - Response - Page 1:
2025-05-11 03:00:11,009 - INFO - 第 1 页获取到 100 条记录
2025-05-11 03:00:11,009 - INFO - Request Parameters - Page 2:
2025-05-11 03:00:11,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:11,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:11,509 - INFO - Response - Page 2:
2025-05-11 03:00:11,709 - INFO - 第 2 页获取到 100 条记录
2025-05-11 03:00:11,709 - INFO - Request Parameters - Page 3:
2025-05-11 03:00:11,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:11,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:12,188 - INFO - Response - Page 3:
2025-05-11 03:00:12,389 - INFO - 第 3 页获取到 100 条记录
2025-05-11 03:00:12,389 - INFO - Request Parameters - Page 4:
2025-05-11 03:00:12,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:12,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:12,945 - INFO - Response - Page 4:
2025-05-11 03:00:13,146 - INFO - 第 4 页获取到 100 条记录
2025-05-11 03:00:13,146 - INFO - Request Parameters - Page 5:
2025-05-11 03:00:13,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:13,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:13,701 - INFO - Response - Page 5:
2025-05-11 03:00:13,901 - INFO - 第 5 页获取到 100 条记录
2025-05-11 03:00:13,901 - INFO - Request Parameters - Page 6:
2025-05-11 03:00:13,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:13,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:14,406 - INFO - Response - Page 6:
2025-05-11 03:00:14,606 - INFO - 第 6 页获取到 100 条记录
2025-05-11 03:00:14,606 - INFO - Request Parameters - Page 7:
2025-05-11 03:00:14,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:14,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:15,180 - INFO - Response - Page 7:
2025-05-11 03:00:15,382 - INFO - 第 7 页获取到 70 条记录
2025-05-11 03:00:15,382 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 03:00:15,382 - INFO - 获取到 670 条表单数据
2025-05-11 03:00:15,394 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 03:00:15,407 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 03:00:15,407 - INFO - 开始处理日期: 2025-03
2025-05-11 03:00:15,407 - INFO - Request Parameters - Page 1:
2025-05-11 03:00:15,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:15,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:15,940 - INFO - Response - Page 1:
2025-05-11 03:00:16,140 - INFO - 第 1 页获取到 100 条记录
2025-05-11 03:00:16,140 - INFO - Request Parameters - Page 2:
2025-05-11 03:00:16,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:16,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:16,682 - INFO - Response - Page 2:
2025-05-11 03:00:16,884 - INFO - 第 2 页获取到 100 条记录
2025-05-11 03:00:16,884 - INFO - Request Parameters - Page 3:
2025-05-11 03:00:16,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:16,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:17,344 - INFO - Response - Page 3:
2025-05-11 03:00:17,544 - INFO - 第 3 页获取到 100 条记录
2025-05-11 03:00:17,544 - INFO - Request Parameters - Page 4:
2025-05-11 03:00:17,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:17,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:18,017 - INFO - Response - Page 4:
2025-05-11 03:00:18,218 - INFO - 第 4 页获取到 100 条记录
2025-05-11 03:00:18,218 - INFO - Request Parameters - Page 5:
2025-05-11 03:00:18,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:18,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:18,658 - INFO - Response - Page 5:
2025-05-11 03:00:18,859 - INFO - 第 5 页获取到 100 条记录
2025-05-11 03:00:18,859 - INFO - Request Parameters - Page 6:
2025-05-11 03:00:18,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:18,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:19,459 - INFO - Response - Page 6:
2025-05-11 03:00:19,659 - INFO - 第 6 页获取到 100 条记录
2025-05-11 03:00:19,659 - INFO - Request Parameters - Page 7:
2025-05-11 03:00:19,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:19,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:20,119 - INFO - Response - Page 7:
2025-05-11 03:00:20,319 - INFO - 第 7 页获取到 61 条记录
2025-05-11 03:00:20,319 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 03:00:20,319 - INFO - 获取到 661 条表单数据
2025-05-11 03:00:20,335 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 03:00:20,346 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 03:00:20,346 - INFO - 开始处理日期: 2025-04
2025-05-11 03:00:20,346 - INFO - Request Parameters - Page 1:
2025-05-11 03:00:20,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:20,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:20,806 - INFO - Response - Page 1:
2025-05-11 03:00:21,006 - INFO - 第 1 页获取到 100 条记录
2025-05-11 03:00:21,006 - INFO - Request Parameters - Page 2:
2025-05-11 03:00:21,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:21,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:21,494 - INFO - Response - Page 2:
2025-05-11 03:00:21,695 - INFO - 第 2 页获取到 100 条记录
2025-05-11 03:00:21,695 - INFO - Request Parameters - Page 3:
2025-05-11 03:00:21,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:21,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:22,222 - INFO - Response - Page 3:
2025-05-11 03:00:22,424 - INFO - 第 3 页获取到 100 条记录
2025-05-11 03:00:22,424 - INFO - Request Parameters - Page 4:
2025-05-11 03:00:22,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:22,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:22,903 - INFO - Response - Page 4:
2025-05-11 03:00:23,105 - INFO - 第 4 页获取到 100 条记录
2025-05-11 03:00:23,105 - INFO - Request Parameters - Page 5:
2025-05-11 03:00:23,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:23,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:23,611 - INFO - Response - Page 5:
2025-05-11 03:00:23,811 - INFO - 第 5 页获取到 100 条记录
2025-05-11 03:00:23,811 - INFO - Request Parameters - Page 6:
2025-05-11 03:00:23,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:23,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:24,316 - INFO - Response - Page 6:
2025-05-11 03:00:24,517 - INFO - 第 6 页获取到 100 条记录
2025-05-11 03:00:24,517 - INFO - Request Parameters - Page 7:
2025-05-11 03:00:24,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:24,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:24,948 - INFO - Response - Page 7:
2025-05-11 03:00:25,148 - INFO - 第 7 页获取到 54 条记录
2025-05-11 03:00:25,148 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 03:00:25,148 - INFO - 获取到 654 条表单数据
2025-05-11 03:00:25,163 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 03:00:25,175 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 03:00:25,175 - INFO - 开始处理日期: 2025-05
2025-05-11 03:00:25,175 - INFO - Request Parameters - Page 1:
2025-05-11 03:00:25,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:25,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:25,718 - INFO - Response - Page 1:
2025-05-11 03:00:25,919 - INFO - 第 1 页获取到 100 条记录
2025-05-11 03:00:25,919 - INFO - Request Parameters - Page 2:
2025-05-11 03:00:25,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:25,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:26,433 - INFO - Response - Page 2:
2025-05-11 03:00:26,633 - INFO - 第 2 页获取到 100 条记录
2025-05-11 03:00:26,633 - INFO - Request Parameters - Page 3:
2025-05-11 03:00:26,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:26,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:27,131 - INFO - Response - Page 3:
2025-05-11 03:00:27,333 - INFO - 第 3 页获取到 100 条记录
2025-05-11 03:00:27,333 - INFO - Request Parameters - Page 4:
2025-05-11 03:00:27,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:27,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:27,820 - INFO - Response - Page 4:
2025-05-11 03:00:28,021 - INFO - 第 4 页获取到 100 条记录
2025-05-11 03:00:28,021 - INFO - Request Parameters - Page 5:
2025-05-11 03:00:28,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:28,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:28,525 - INFO - Response - Page 5:
2025-05-11 03:00:28,725 - INFO - 第 5 页获取到 100 条记录
2025-05-11 03:00:28,725 - INFO - Request Parameters - Page 6:
2025-05-11 03:00:28,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:28,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:29,300 - INFO - Response - Page 6:
2025-05-11 03:00:29,500 - INFO - 第 6 页获取到 100 条记录
2025-05-11 03:00:29,500 - INFO - Request Parameters - Page 7:
2025-05-11 03:00:29,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 03:00:29,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 03:00:29,843 - INFO - Response - Page 7:
2025-05-11 03:00:30,043 - INFO - 第 7 页获取到 21 条记录
2025-05-11 03:00:30,043 - INFO - 查询完成，共获取到 621 条记录
2025-05-11 03:00:30,043 - INFO - 获取到 621 条表单数据
2025-05-11 03:00:30,055 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-11 03:00:30,058 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-11 03:00:30,492 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-11 03:00:30,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41733.2, 'new_value': 65439.0}, {'field': 'total_amount', 'old_value': 49309.0, 'new_value': 73014.8}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-11 03:00:30,500 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-11 03:00:30,500 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-11 03:00:30,501 - INFO - =================同步完成====================
2025-05-11 06:00:03,396 - INFO - =================使用默认全量同步=============
2025-05-11 06:00:04,691 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-11 06:00:04,692 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 06:00:04,719 - INFO - 开始处理日期: 2025-01
2025-05-11 06:00:04,722 - INFO - Request Parameters - Page 1:
2025-05-11 06:00:04,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:04,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:05,573 - INFO - Response - Page 1:
2025-05-11 06:00:05,773 - INFO - 第 1 页获取到 100 条记录
2025-05-11 06:00:05,773 - INFO - Request Parameters - Page 2:
2025-05-11 06:00:05,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:05,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:06,577 - INFO - Response - Page 2:
2025-05-11 06:00:06,777 - INFO - 第 2 页获取到 100 条记录
2025-05-11 06:00:06,777 - INFO - Request Parameters - Page 3:
2025-05-11 06:00:06,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:06,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:07,251 - INFO - Response - Page 3:
2025-05-11 06:00:07,452 - INFO - 第 3 页获取到 100 条记录
2025-05-11 06:00:07,452 - INFO - Request Parameters - Page 4:
2025-05-11 06:00:07,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:07,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:07,913 - INFO - Response - Page 4:
2025-05-11 06:00:08,113 - INFO - 第 4 页获取到 100 条记录
2025-05-11 06:00:08,113 - INFO - Request Parameters - Page 5:
2025-05-11 06:00:08,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:08,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:08,577 - INFO - Response - Page 5:
2025-05-11 06:00:08,778 - INFO - 第 5 页获取到 100 条记录
2025-05-11 06:00:08,778 - INFO - Request Parameters - Page 6:
2025-05-11 06:00:08,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:08,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:09,316 - INFO - Response - Page 6:
2025-05-11 06:00:09,517 - INFO - 第 6 页获取到 100 条记录
2025-05-11 06:00:09,517 - INFO - Request Parameters - Page 7:
2025-05-11 06:00:09,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:09,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:10,023 - INFO - Response - Page 7:
2025-05-11 06:00:10,223 - INFO - 第 7 页获取到 82 条记录
2025-05-11 06:00:10,223 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 06:00:10,223 - INFO - 获取到 682 条表单数据
2025-05-11 06:00:10,235 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 06:00:10,248 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 06:00:10,248 - INFO - 开始处理日期: 2025-02
2025-05-11 06:00:10,248 - INFO - Request Parameters - Page 1:
2025-05-11 06:00:10,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:10,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:10,718 - INFO - Response - Page 1:
2025-05-11 06:00:10,919 - INFO - 第 1 页获取到 100 条记录
2025-05-11 06:00:10,919 - INFO - Request Parameters - Page 2:
2025-05-11 06:00:10,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:10,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:11,440 - INFO - Response - Page 2:
2025-05-11 06:00:11,641 - INFO - 第 2 页获取到 100 条记录
2025-05-11 06:00:11,641 - INFO - Request Parameters - Page 3:
2025-05-11 06:00:11,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:11,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:12,151 - INFO - Response - Page 3:
2025-05-11 06:00:12,352 - INFO - 第 3 页获取到 100 条记录
2025-05-11 06:00:12,352 - INFO - Request Parameters - Page 4:
2025-05-11 06:00:12,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:12,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:12,870 - INFO - Response - Page 4:
2025-05-11 06:00:13,070 - INFO - 第 4 页获取到 100 条记录
2025-05-11 06:00:13,070 - INFO - Request Parameters - Page 5:
2025-05-11 06:00:13,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:13,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:13,619 - INFO - Response - Page 5:
2025-05-11 06:00:13,821 - INFO - 第 5 页获取到 100 条记录
2025-05-11 06:00:13,821 - INFO - Request Parameters - Page 6:
2025-05-11 06:00:13,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:13,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:14,315 - INFO - Response - Page 6:
2025-05-11 06:00:14,515 - INFO - 第 6 页获取到 100 条记录
2025-05-11 06:00:14,515 - INFO - Request Parameters - Page 7:
2025-05-11 06:00:14,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:14,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:14,956 - INFO - Response - Page 7:
2025-05-11 06:00:15,156 - INFO - 第 7 页获取到 70 条记录
2025-05-11 06:00:15,156 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 06:00:15,156 - INFO - 获取到 670 条表单数据
2025-05-11 06:00:15,168 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 06:00:15,180 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 06:00:15,180 - INFO - 开始处理日期: 2025-03
2025-05-11 06:00:15,180 - INFO - Request Parameters - Page 1:
2025-05-11 06:00:15,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:15,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:15,731 - INFO - Response - Page 1:
2025-05-11 06:00:15,931 - INFO - 第 1 页获取到 100 条记录
2025-05-11 06:00:15,931 - INFO - Request Parameters - Page 2:
2025-05-11 06:00:15,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:15,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:16,610 - INFO - Response - Page 2:
2025-05-11 06:00:16,811 - INFO - 第 2 页获取到 100 条记录
2025-05-11 06:00:16,811 - INFO - Request Parameters - Page 3:
2025-05-11 06:00:16,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:16,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:17,249 - INFO - Response - Page 3:
2025-05-11 06:00:17,450 - INFO - 第 3 页获取到 100 条记录
2025-05-11 06:00:17,450 - INFO - Request Parameters - Page 4:
2025-05-11 06:00:17,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:17,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:17,980 - INFO - Response - Page 4:
2025-05-11 06:00:18,182 - INFO - 第 4 页获取到 100 条记录
2025-05-11 06:00:18,182 - INFO - Request Parameters - Page 5:
2025-05-11 06:00:18,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:18,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:18,710 - INFO - Response - Page 5:
2025-05-11 06:00:18,910 - INFO - 第 5 页获取到 100 条记录
2025-05-11 06:00:18,910 - INFO - Request Parameters - Page 6:
2025-05-11 06:00:18,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:18,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:19,378 - INFO - Response - Page 6:
2025-05-11 06:00:19,579 - INFO - 第 6 页获取到 100 条记录
2025-05-11 06:00:19,579 - INFO - Request Parameters - Page 7:
2025-05-11 06:00:19,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:19,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:20,056 - INFO - Response - Page 7:
2025-05-11 06:00:20,256 - INFO - 第 7 页获取到 61 条记录
2025-05-11 06:00:20,256 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 06:00:20,256 - INFO - 获取到 661 条表单数据
2025-05-11 06:00:20,269 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 06:00:20,280 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 06:00:20,280 - INFO - 开始处理日期: 2025-04
2025-05-11 06:00:20,280 - INFO - Request Parameters - Page 1:
2025-05-11 06:00:20,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:20,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:20,805 - INFO - Response - Page 1:
2025-05-11 06:00:21,005 - INFO - 第 1 页获取到 100 条记录
2025-05-11 06:00:21,005 - INFO - Request Parameters - Page 2:
2025-05-11 06:00:21,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:21,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:21,485 - INFO - Response - Page 2:
2025-05-11 06:00:21,685 - INFO - 第 2 页获取到 100 条记录
2025-05-11 06:00:21,685 - INFO - Request Parameters - Page 3:
2025-05-11 06:00:21,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:21,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:22,150 - INFO - Response - Page 3:
2025-05-11 06:00:22,351 - INFO - 第 3 页获取到 100 条记录
2025-05-11 06:00:22,351 - INFO - Request Parameters - Page 4:
2025-05-11 06:00:22,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:22,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:22,837 - INFO - Response - Page 4:
2025-05-11 06:00:23,038 - INFO - 第 4 页获取到 100 条记录
2025-05-11 06:00:23,038 - INFO - Request Parameters - Page 5:
2025-05-11 06:00:23,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:23,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:23,474 - INFO - Response - Page 5:
2025-05-11 06:00:23,674 - INFO - 第 5 页获取到 100 条记录
2025-05-11 06:00:23,674 - INFO - Request Parameters - Page 6:
2025-05-11 06:00:23,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:23,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:24,203 - INFO - Response - Page 6:
2025-05-11 06:00:24,405 - INFO - 第 6 页获取到 100 条记录
2025-05-11 06:00:24,405 - INFO - Request Parameters - Page 7:
2025-05-11 06:00:24,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:24,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:24,781 - INFO - Response - Page 7:
2025-05-11 06:00:24,981 - INFO - 第 7 页获取到 54 条记录
2025-05-11 06:00:24,981 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 06:00:24,981 - INFO - 获取到 654 条表单数据
2025-05-11 06:00:24,993 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 06:00:25,005 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 06:00:25,005 - INFO - 开始处理日期: 2025-05
2025-05-11 06:00:25,005 - INFO - Request Parameters - Page 1:
2025-05-11 06:00:25,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:25,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:25,489 - INFO - Response - Page 1:
2025-05-11 06:00:25,691 - INFO - 第 1 页获取到 100 条记录
2025-05-11 06:00:25,691 - INFO - Request Parameters - Page 2:
2025-05-11 06:00:25,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:25,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:26,194 - INFO - Response - Page 2:
2025-05-11 06:00:26,395 - INFO - 第 2 页获取到 100 条记录
2025-05-11 06:00:26,395 - INFO - Request Parameters - Page 3:
2025-05-11 06:00:26,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:26,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:26,964 - INFO - Response - Page 3:
2025-05-11 06:00:27,164 - INFO - 第 3 页获取到 100 条记录
2025-05-11 06:00:27,164 - INFO - Request Parameters - Page 4:
2025-05-11 06:00:27,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:27,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:27,627 - INFO - Response - Page 4:
2025-05-11 06:00:27,827 - INFO - 第 4 页获取到 100 条记录
2025-05-11 06:00:27,827 - INFO - Request Parameters - Page 5:
2025-05-11 06:00:27,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:27,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:28,338 - INFO - Response - Page 5:
2025-05-11 06:00:28,538 - INFO - 第 5 页获取到 100 条记录
2025-05-11 06:00:28,538 - INFO - Request Parameters - Page 6:
2025-05-11 06:00:28,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:28,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:29,179 - INFO - Response - Page 6:
2025-05-11 06:00:29,379 - INFO - 第 6 页获取到 100 条记录
2025-05-11 06:00:29,379 - INFO - Request Parameters - Page 7:
2025-05-11 06:00:29,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 06:00:29,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 06:00:29,753 - INFO - Response - Page 7:
2025-05-11 06:00:29,954 - INFO - 第 7 页获取到 21 条记录
2025-05-11 06:00:29,954 - INFO - 查询完成，共获取到 621 条记录
2025-05-11 06:00:29,954 - INFO - 获取到 621 条表单数据
2025-05-11 06:00:29,964 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-11 06:00:29,974 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-11 06:00:30,449 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-11 06:00:30,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33573.0, 'new_value': 36930.0}, {'field': 'total_amount', 'old_value': 35423.0, 'new_value': 38780.0}, {'field': 'order_count', 'old_value': 203, 'new_value': 223}]
2025-05-11 06:00:30,450 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-11 06:00:30,450 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-11 06:00:30,452 - INFO - =================同步完成====================
2025-05-11 09:00:01,923 - INFO - =================使用默认全量同步=============
2025-05-11 09:00:03,248 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-11 09:00:03,248 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 09:00:03,276 - INFO - 开始处理日期: 2025-01
2025-05-11 09:00:03,278 - INFO - Request Parameters - Page 1:
2025-05-11 09:00:03,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:03,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:04,605 - INFO - Response - Page 1:
2025-05-11 09:00:04,806 - INFO - 第 1 页获取到 100 条记录
2025-05-11 09:00:04,806 - INFO - Request Parameters - Page 2:
2025-05-11 09:00:04,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:04,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:05,370 - INFO - Response - Page 2:
2025-05-11 09:00:05,570 - INFO - 第 2 页获取到 100 条记录
2025-05-11 09:00:05,570 - INFO - Request Parameters - Page 3:
2025-05-11 09:00:05,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:05,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:06,074 - INFO - Response - Page 3:
2025-05-11 09:00:06,274 - INFO - 第 3 页获取到 100 条记录
2025-05-11 09:00:06,274 - INFO - Request Parameters - Page 4:
2025-05-11 09:00:06,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:06,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:06,795 - INFO - Response - Page 4:
2025-05-11 09:00:06,996 - INFO - 第 4 页获取到 100 条记录
2025-05-11 09:00:06,996 - INFO - Request Parameters - Page 5:
2025-05-11 09:00:06,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:06,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:07,494 - INFO - Response - Page 5:
2025-05-11 09:00:07,695 - INFO - 第 5 页获取到 100 条记录
2025-05-11 09:00:07,695 - INFO - Request Parameters - Page 6:
2025-05-11 09:00:07,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:07,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:08,205 - INFO - Response - Page 6:
2025-05-11 09:00:08,405 - INFO - 第 6 页获取到 100 条记录
2025-05-11 09:00:08,405 - INFO - Request Parameters - Page 7:
2025-05-11 09:00:08,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:08,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:08,894 - INFO - Response - Page 7:
2025-05-11 09:00:09,096 - INFO - 第 7 页获取到 82 条记录
2025-05-11 09:00:09,096 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 09:00:09,096 - INFO - 获取到 682 条表单数据
2025-05-11 09:00:09,107 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 09:00:09,118 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 09:00:09,119 - INFO - 开始处理日期: 2025-02
2025-05-11 09:00:09,119 - INFO - Request Parameters - Page 1:
2025-05-11 09:00:09,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:09,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:09,633 - INFO - Response - Page 1:
2025-05-11 09:00:09,833 - INFO - 第 1 页获取到 100 条记录
2025-05-11 09:00:09,833 - INFO - Request Parameters - Page 2:
2025-05-11 09:00:09,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:09,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:10,327 - INFO - Response - Page 2:
2025-05-11 09:00:10,527 - INFO - 第 2 页获取到 100 条记录
2025-05-11 09:00:10,527 - INFO - Request Parameters - Page 3:
2025-05-11 09:00:10,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:10,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:11,092 - INFO - Response - Page 3:
2025-05-11 09:00:11,292 - INFO - 第 3 页获取到 100 条记录
2025-05-11 09:00:11,292 - INFO - Request Parameters - Page 4:
2025-05-11 09:00:11,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:11,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:11,866 - INFO - Response - Page 4:
2025-05-11 09:00:12,066 - INFO - 第 4 页获取到 100 条记录
2025-05-11 09:00:12,066 - INFO - Request Parameters - Page 5:
2025-05-11 09:00:12,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:12,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:12,582 - INFO - Response - Page 5:
2025-05-11 09:00:12,783 - INFO - 第 5 页获取到 100 条记录
2025-05-11 09:00:12,783 - INFO - Request Parameters - Page 6:
2025-05-11 09:00:12,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:12,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:13,267 - INFO - Response - Page 6:
2025-05-11 09:00:13,468 - INFO - 第 6 页获取到 100 条记录
2025-05-11 09:00:13,468 - INFO - Request Parameters - Page 7:
2025-05-11 09:00:13,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:13,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:13,933 - INFO - Response - Page 7:
2025-05-11 09:00:14,135 - INFO - 第 7 页获取到 70 条记录
2025-05-11 09:00:14,135 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 09:00:14,135 - INFO - 获取到 670 条表单数据
2025-05-11 09:00:14,147 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 09:00:14,159 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 09:00:14,160 - INFO - 开始处理日期: 2025-03
2025-05-11 09:00:14,160 - INFO - Request Parameters - Page 1:
2025-05-11 09:00:14,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:14,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:14,683 - INFO - Response - Page 1:
2025-05-11 09:00:14,885 - INFO - 第 1 页获取到 100 条记录
2025-05-11 09:00:14,885 - INFO - Request Parameters - Page 2:
2025-05-11 09:00:14,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:14,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:15,363 - INFO - Response - Page 2:
2025-05-11 09:00:15,564 - INFO - 第 2 页获取到 100 条记录
2025-05-11 09:00:15,564 - INFO - Request Parameters - Page 3:
2025-05-11 09:00:15,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:15,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:16,057 - INFO - Response - Page 3:
2025-05-11 09:00:16,258 - INFO - 第 3 页获取到 100 条记录
2025-05-11 09:00:16,258 - INFO - Request Parameters - Page 4:
2025-05-11 09:00:16,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:16,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:16,752 - INFO - Response - Page 4:
2025-05-11 09:00:16,952 - INFO - 第 4 页获取到 100 条记录
2025-05-11 09:00:16,952 - INFO - Request Parameters - Page 5:
2025-05-11 09:00:16,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:16,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:17,471 - INFO - Response - Page 5:
2025-05-11 09:00:17,672 - INFO - 第 5 页获取到 100 条记录
2025-05-11 09:00:17,672 - INFO - Request Parameters - Page 6:
2025-05-11 09:00:17,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:17,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:18,151 - INFO - Response - Page 6:
2025-05-11 09:00:18,351 - INFO - 第 6 页获取到 100 条记录
2025-05-11 09:00:18,351 - INFO - Request Parameters - Page 7:
2025-05-11 09:00:18,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:18,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:18,762 - INFO - Response - Page 7:
2025-05-11 09:00:18,964 - INFO - 第 7 页获取到 61 条记录
2025-05-11 09:00:18,964 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 09:00:18,964 - INFO - 获取到 661 条表单数据
2025-05-11 09:00:18,976 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 09:00:18,987 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 09:00:18,988 - INFO - 开始处理日期: 2025-04
2025-05-11 09:00:18,988 - INFO - Request Parameters - Page 1:
2025-05-11 09:00:18,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:18,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:19,760 - INFO - Response - Page 1:
2025-05-11 09:00:19,961 - INFO - 第 1 页获取到 100 条记录
2025-05-11 09:00:19,961 - INFO - Request Parameters - Page 2:
2025-05-11 09:00:19,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:19,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:20,434 - INFO - Response - Page 2:
2025-05-11 09:00:20,635 - INFO - 第 2 页获取到 100 条记录
2025-05-11 09:00:20,635 - INFO - Request Parameters - Page 3:
2025-05-11 09:00:20,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:20,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:21,155 - INFO - Response - Page 3:
2025-05-11 09:00:21,355 - INFO - 第 3 页获取到 100 条记录
2025-05-11 09:00:21,355 - INFO - Request Parameters - Page 4:
2025-05-11 09:00:21,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:21,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:21,852 - INFO - Response - Page 4:
2025-05-11 09:00:22,053 - INFO - 第 4 页获取到 100 条记录
2025-05-11 09:00:22,053 - INFO - Request Parameters - Page 5:
2025-05-11 09:00:22,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:22,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:22,550 - INFO - Response - Page 5:
2025-05-11 09:00:22,751 - INFO - 第 5 页获取到 100 条记录
2025-05-11 09:00:22,751 - INFO - Request Parameters - Page 6:
2025-05-11 09:00:22,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:22,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:23,262 - INFO - Response - Page 6:
2025-05-11 09:00:23,463 - INFO - 第 6 页获取到 100 条记录
2025-05-11 09:00:23,463 - INFO - Request Parameters - Page 7:
2025-05-11 09:00:23,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:23,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:23,905 - INFO - Response - Page 7:
2025-05-11 09:00:24,105 - INFO - 第 7 页获取到 54 条记录
2025-05-11 09:00:24,105 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 09:00:24,105 - INFO - 获取到 654 条表单数据
2025-05-11 09:00:24,117 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 09:00:24,129 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 09:00:24,129 - INFO - 开始处理日期: 2025-05
2025-05-11 09:00:24,129 - INFO - Request Parameters - Page 1:
2025-05-11 09:00:24,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:24,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:24,610 - INFO - Response - Page 1:
2025-05-11 09:00:24,810 - INFO - 第 1 页获取到 100 条记录
2025-05-11 09:00:24,810 - INFO - Request Parameters - Page 2:
2025-05-11 09:00:24,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:24,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:25,301 - INFO - Response - Page 2:
2025-05-11 09:00:25,502 - INFO - 第 2 页获取到 100 条记录
2025-05-11 09:00:25,502 - INFO - Request Parameters - Page 3:
2025-05-11 09:00:25,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:25,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:25,981 - INFO - Response - Page 3:
2025-05-11 09:00:26,181 - INFO - 第 3 页获取到 100 条记录
2025-05-11 09:00:26,181 - INFO - Request Parameters - Page 4:
2025-05-11 09:00:26,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:26,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:26,825 - INFO - Response - Page 4:
2025-05-11 09:00:27,025 - INFO - 第 4 页获取到 100 条记录
2025-05-11 09:00:27,025 - INFO - Request Parameters - Page 5:
2025-05-11 09:00:27,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:27,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:27,511 - INFO - Response - Page 5:
2025-05-11 09:00:27,712 - INFO - 第 5 页获取到 100 条记录
2025-05-11 09:00:27,712 - INFO - Request Parameters - Page 6:
2025-05-11 09:00:27,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:27,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:28,214 - INFO - Response - Page 6:
2025-05-11 09:00:28,414 - INFO - 第 6 页获取到 100 条记录
2025-05-11 09:00:28,414 - INFO - Request Parameters - Page 7:
2025-05-11 09:00:28,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 09:00:28,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 09:00:28,734 - INFO - Response - Page 7:
2025-05-11 09:00:28,934 - INFO - 第 7 页获取到 21 条记录
2025-05-11 09:00:28,934 - INFO - 查询完成，共获取到 621 条记录
2025-05-11 09:00:28,934 - INFO - 获取到 621 条表单数据
2025-05-11 09:00:28,946 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-11 09:00:28,947 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-11 09:00:29,440 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-11 09:00:29,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1099.0, 'new_value': 3439.0}, {'field': 'total_amount', 'old_value': 1099.0, 'new_value': 3439.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-11 09:00:29,441 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-11 09:00:29,886 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-11 09:00:29,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28097.0, 'new_value': 31113.0}, {'field': 'total_amount', 'old_value': 28097.0, 'new_value': 31113.0}, {'field': 'order_count', 'old_value': 228, 'new_value': 258}]
2025-05-11 09:00:29,887 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-11 09:00:30,324 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-11 09:00:30,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 800.0, 'new_value': 900.0}, {'field': 'offline_amount', 'old_value': 11234.0, 'new_value': 13473.0}, {'field': 'total_amount', 'old_value': 12034.0, 'new_value': 14373.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 55}]
2025-05-11 09:00:30,325 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-11 09:00:30,708 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-11 09:00:30,708 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2429.34, 'new_value': 2956.48}, {'field': 'offline_amount', 'old_value': 38393.45, 'new_value': 46946.28}, {'field': 'total_amount', 'old_value': 40822.79, 'new_value': 49902.76}, {'field': 'order_count', 'old_value': 930, 'new_value': 1170}]
2025-05-11 09:00:30,709 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-11 09:00:31,199 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-11 09:00:31,199 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19356.66, 'new_value': 21645.34}, {'field': 'offline_amount', 'old_value': 43294.4, 'new_value': 47929.87}, {'field': 'total_amount', 'old_value': 62651.06, 'new_value': 69575.21}, {'field': 'order_count', 'old_value': 2083, 'new_value': 2313}]
2025-05-11 09:00:31,200 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-11 09:00:31,639 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-11 09:00:31,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168888.0, 'new_value': 182885.0}, {'field': 'total_amount', 'old_value': 168888.0, 'new_value': 182885.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 78}]
2025-05-11 09:00:31,640 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-11 09:00:32,218 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-11 09:00:32,218 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2357.72, 'new_value': 2916.29}, {'field': 'offline_amount', 'old_value': 70408.02, 'new_value': 75325.86}, {'field': 'total_amount', 'old_value': 72765.74, 'new_value': 78242.15}, {'field': 'order_count', 'old_value': 484, 'new_value': 531}]
2025-05-11 09:00:32,219 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-11 09:00:32,686 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-11 09:00:32,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51409.0, 'new_value': 60608.0}, {'field': 'total_amount', 'old_value': 75031.48, 'new_value': 84230.48}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-11 09:00:32,687 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-11 09:00:33,089 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-11 09:00:33,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367643.0, 'new_value': 393154.0}, {'field': 'total_amount', 'old_value': 367643.0, 'new_value': 393154.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 62}]
2025-05-11 09:00:33,090 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-11 09:00:33,507 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-11 09:00:33,507 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27590.46, 'new_value': 30611.56}, {'field': 'offline_amount', 'old_value': 431335.48, 'new_value': 480769.43}, {'field': 'total_amount', 'old_value': 458925.94, 'new_value': 511380.99}, {'field': 'order_count', 'old_value': 3450, 'new_value': 3856}]
2025-05-11 09:00:33,507 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-11 09:00:34,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-11 09:00:34,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33033.18, 'new_value': 37217.98}, {'field': 'total_amount', 'old_value': 33033.18, 'new_value': 37217.98}, {'field': 'order_count', 'old_value': 180, 'new_value': 205}]
2025-05-11 09:00:34,062 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-11 09:00:34,552 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-11 09:00:34,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11646.0, 'new_value': 12974.4}, {'field': 'offline_amount', 'old_value': 9605.7, 'new_value': 10838.7}, {'field': 'total_amount', 'old_value': 21251.7, 'new_value': 23813.1}, {'field': 'order_count', 'old_value': 111, 'new_value': 127}]
2025-05-11 09:00:34,553 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-11 09:00:34,971 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-11 09:00:34,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5549.43, 'new_value': 6229.74}, {'field': 'offline_amount', 'old_value': 92699.14, 'new_value': 107292.74}, {'field': 'total_amount', 'old_value': 98248.57, 'new_value': 113522.48}, {'field': 'order_count', 'old_value': 5183, 'new_value': 5962}]
2025-05-11 09:00:34,972 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-11 09:00:35,477 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-11 09:00:35,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19215.3, 'new_value': 21256.88}, {'field': 'offline_amount', 'old_value': 11588.0, 'new_value': 12876.0}, {'field': 'total_amount', 'old_value': 30803.3, 'new_value': 34132.88}, {'field': 'order_count', 'old_value': 392, 'new_value': 436}]
2025-05-11 09:00:35,477 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-11 09:00:36,090 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-11 09:00:36,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54687.0, 'new_value': 56181.0}, {'field': 'total_amount', 'old_value': 54687.0, 'new_value': 56181.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-05-11 09:00:36,090 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-11 09:00:36,624 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-11 09:00:36,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2749800.0, 'new_value': 3099700.0}, {'field': 'total_amount', 'old_value': 2749800.0, 'new_value': 3099700.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-11 09:00:36,625 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-11 09:00:37,155 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-11 09:00:37,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365031.6, 'new_value': 392937.6}, {'field': 'total_amount', 'old_value': 365031.6, 'new_value': 392937.6}, {'field': 'order_count', 'old_value': 884, 'new_value': 977}]
2025-05-11 09:00:37,156 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-11 09:00:37,638 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-11 09:00:37,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127902.0, 'new_value': 133203.0}, {'field': 'total_amount', 'old_value': 127902.0, 'new_value': 133203.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-05-11 09:00:37,639 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-11 09:00:38,084 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-11 09:00:38,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279082.0, 'new_value': 308498.0}, {'field': 'total_amount', 'old_value': 279082.0, 'new_value': 308498.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 81}]
2025-05-11 09:00:38,085 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-11 09:00:38,538 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-11 09:00:38,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17602.0, 'new_value': 20010.4}, {'field': 'offline_amount', 'old_value': 115219.34, 'new_value': 138516.81}, {'field': 'total_amount', 'old_value': 132821.34, 'new_value': 158527.21}, {'field': 'order_count', 'old_value': 820, 'new_value': 974}]
2025-05-11 09:00:38,539 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-11 09:00:39,053 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-11 09:00:39,053 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44154.0, 'new_value': 48010.0}, {'field': 'total_amount', 'old_value': 44154.0, 'new_value': 48010.0}, {'field': 'order_count', 'old_value': 1323, 'new_value': 1432}]
2025-05-11 09:00:39,054 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-11 09:00:39,500 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-11 09:00:39,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180047.1, 'new_value': 187914.3}, {'field': 'total_amount', 'old_value': 180047.1, 'new_value': 187914.3}, {'field': 'order_count', 'old_value': 209, 'new_value': 221}]
2025-05-11 09:00:39,501 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-11 09:00:39,998 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-11 09:00:39,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10288.0, 'new_value': 11302.0}, {'field': 'total_amount', 'old_value': 10288.0, 'new_value': 11302.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-05-11 09:00:39,998 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-11 09:00:40,439 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-11 09:00:40,440 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101399.77, 'new_value': 182934.91}, {'field': 'offline_amount', 'old_value': 544823.81, 'new_value': 549582.91}, {'field': 'total_amount', 'old_value': 646223.58, 'new_value': 732517.82}, {'field': 'order_count', 'old_value': 3109, 'new_value': 3510}]
2025-05-11 09:00:40,440 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-11 09:00:40,947 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-11 09:00:40,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34063.58, 'new_value': 37662.55}, {'field': 'total_amount', 'old_value': 34063.58, 'new_value': 37662.55}, {'field': 'order_count', 'old_value': 1962, 'new_value': 2156}]
2025-05-11 09:00:40,948 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-11 09:00:41,417 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-11 09:00:41,417 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49888.4, 'new_value': 56056.2}, {'field': 'offline_amount', 'old_value': 40775.8, 'new_value': 45989.3}, {'field': 'total_amount', 'old_value': 90664.2, 'new_value': 102045.5}, {'field': 'order_count', 'old_value': 2162, 'new_value': 2436}]
2025-05-11 09:00:41,417 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-11 09:00:41,918 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-11 09:00:41,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1130000.0, 'new_value': 1230000.0}, {'field': 'total_amount', 'old_value': 1130000.0, 'new_value': 1230000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 267}]
2025-05-11 09:00:41,918 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-11 09:00:42,383 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-11 09:00:42,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77065.8, 'new_value': 86282.5}, {'field': 'total_amount', 'old_value': 77065.8, 'new_value': 86282.5}, {'field': 'order_count', 'old_value': 959, 'new_value': 1082}]
2025-05-11 09:00:42,384 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-11 09:00:42,867 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-11 09:00:42,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14975.33, 'new_value': 17876.53}, {'field': 'offline_amount', 'old_value': 454812.58, 'new_value': 511619.12}, {'field': 'total_amount', 'old_value': 469787.91, 'new_value': 529495.65}, {'field': 'order_count', 'old_value': 1992, 'new_value': 2277}]
2025-05-11 09:00:42,867 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-11 09:00:43,375 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-11 09:00:43,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18459.5, 'new_value': 21457.0}, {'field': 'total_amount', 'old_value': 18459.5, 'new_value': 21457.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 83}]
2025-05-11 09:00:43,375 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-11 09:00:43,862 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-11 09:00:43,862 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83382.78, 'new_value': 96541.77}, {'field': 'offline_amount', 'old_value': 300936.32, 'new_value': 341717.48}, {'field': 'total_amount', 'old_value': 384319.1, 'new_value': 438259.25}, {'field': 'order_count', 'old_value': 2017, 'new_value': 2436}]
2025-05-11 09:00:43,862 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-11 09:00:44,328 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-11 09:00:44,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 410000.0, 'new_value': 420000.0}, {'field': 'total_amount', 'old_value': 410000.0, 'new_value': 420000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 134}]
2025-05-11 09:00:44,328 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-11 09:00:44,742 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-11 09:00:44,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390000.0, 'new_value': 400000.0}, {'field': 'total_amount', 'old_value': 390000.0, 'new_value': 400000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 133}]
2025-05-11 09:00:44,742 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-11 09:00:45,185 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-11 09:00:45,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2248674.0, 'new_value': 2348674.0}, {'field': 'total_amount', 'old_value': 2248674.0, 'new_value': 2348674.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 287}]
2025-05-11 09:00:45,186 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-11 09:00:45,605 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-11 09:00:45,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122752.0, 'new_value': 137810.0}, {'field': 'total_amount', 'old_value': 122752.0, 'new_value': 137810.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 199}]
2025-05-11 09:00:45,605 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-11 09:00:46,059 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-11 09:00:46,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41505.0, 'new_value': 44105.0}, {'field': 'total_amount', 'old_value': 41505.0, 'new_value': 44105.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-11 09:00:46,059 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-11 09:00:46,530 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-11 09:00:46,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58605.51, 'new_value': 66607.67}, {'field': 'offline_amount', 'old_value': 22348.27, 'new_value': 25108.03}, {'field': 'total_amount', 'old_value': 80953.78, 'new_value': 91715.7}, {'field': 'order_count', 'old_value': 4549, 'new_value': 5153}]
2025-05-11 09:00:46,530 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-11 09:00:46,971 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-11 09:00:46,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 941696.0, 'new_value': 1043807.0}, {'field': 'total_amount', 'old_value': 941696.0, 'new_value': 1043807.0}, {'field': 'order_count', 'old_value': 3526, 'new_value': 3933}]
2025-05-11 09:00:46,972 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-11 09:00:47,383 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-11 09:00:47,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132749.25, 'new_value': 152971.75}, {'field': 'total_amount', 'old_value': 132749.25, 'new_value': 152971.75}, {'field': 'order_count', 'old_value': 700, 'new_value': 808}]
2025-05-11 09:00:47,384 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-11 09:00:47,826 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-11 09:00:47,826 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16152.94, 'new_value': 18629.87}, {'field': 'offline_amount', 'old_value': 21889.5, 'new_value': 24469.11}, {'field': 'total_amount', 'old_value': 38042.44, 'new_value': 43098.98}, {'field': 'order_count', 'old_value': 2839, 'new_value': 3235}]
2025-05-11 09:00:47,827 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-11 09:00:48,227 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-11 09:00:48,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31997.0, 'new_value': 36674.0}, {'field': 'total_amount', 'old_value': 31997.0, 'new_value': 36674.0}, {'field': 'order_count', 'old_value': 1422, 'new_value': 1684}]
2025-05-11 09:00:48,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-11 09:00:48,638 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-11 09:00:48,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212883.0, 'new_value': 215231.0}, {'field': 'total_amount', 'old_value': 212883.0, 'new_value': 215231.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-11 09:00:48,638 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-11 09:00:49,219 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-11 09:00:49,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1408.0, 'new_value': 1577.0}, {'field': 'offline_amount', 'old_value': 13654.8, 'new_value': 15396.0}, {'field': 'total_amount', 'old_value': 15062.8, 'new_value': 16973.0}, {'field': 'order_count', 'old_value': 548, 'new_value': 623}]
2025-05-11 09:00:49,219 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-11 09:00:49,648 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-11 09:00:49,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 381.0, 'new_value': 454.0}, {'field': 'offline_amount', 'old_value': 26029.0, 'new_value': 28916.0}, {'field': 'total_amount', 'old_value': 26410.0, 'new_value': 29370.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 226}]
2025-05-11 09:00:49,649 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-11 09:00:50,055 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-11 09:00:50,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123545.0, 'new_value': 143408.0}, {'field': 'total_amount', 'old_value': 123545.0, 'new_value': 143408.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 157}]
2025-05-11 09:00:50,056 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-11 09:00:50,517 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-11 09:00:50,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 368012.9, 'new_value': 392804.95}, {'field': 'total_amount', 'old_value': 368012.9, 'new_value': 392804.95}, {'field': 'order_count', 'old_value': 1552, 'new_value': 1795}]
2025-05-11 09:00:50,517 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-11 09:00:50,924 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-11 09:00:50,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56549.08, 'new_value': 64848.53}, {'field': 'total_amount', 'old_value': 56549.08, 'new_value': 64848.53}, {'field': 'order_count', 'old_value': 3846, 'new_value': 4379}]
2025-05-11 09:00:50,924 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-11 09:00:51,453 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-11 09:00:51,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264068.0, 'new_value': 288772.0}, {'field': 'total_amount', 'old_value': 264068.0, 'new_value': 288772.0}, {'field': 'order_count', 'old_value': 5937, 'new_value': 6519}]
2025-05-11 09:00:51,453 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-11 09:00:51,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-11 09:00:51,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42932.0, 'new_value': 48975.0}, {'field': 'total_amount', 'old_value': 42932.0, 'new_value': 48975.0}, {'field': 'order_count', 'old_value': 2956, 'new_value': 3292}]
2025-05-11 09:00:51,869 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-11 09:00:52,398 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-11 09:00:52,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25597.38, 'new_value': 30489.38}, {'field': 'total_amount', 'old_value': 25597.38, 'new_value': 30489.38}, {'field': 'order_count', 'old_value': 341, 'new_value': 481}]
2025-05-11 09:00:52,399 - INFO - 日期 2025-05 处理完成 - 更新: 50 条，插入: 0 条，错误: 0 条
2025-05-11 09:00:52,399 - INFO - 数据同步完成！更新: 50 条，插入: 0 条，错误: 0 条
2025-05-11 09:00:52,401 - INFO - =================同步完成====================
2025-05-11 12:00:01,971 - INFO - =================使用默认全量同步=============
2025-05-11 12:00:03,287 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-11 12:00:03,288 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 12:00:03,314 - INFO - 开始处理日期: 2025-01
2025-05-11 12:00:03,317 - INFO - Request Parameters - Page 1:
2025-05-11 12:00:03,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:03,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:04,414 - INFO - Response - Page 1:
2025-05-11 12:00:04,614 - INFO - 第 1 页获取到 100 条记录
2025-05-11 12:00:04,614 - INFO - Request Parameters - Page 2:
2025-05-11 12:00:04,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:04,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:05,292 - INFO - Response - Page 2:
2025-05-11 12:00:05,492 - INFO - 第 2 页获取到 100 条记录
2025-05-11 12:00:05,492 - INFO - Request Parameters - Page 3:
2025-05-11 12:00:05,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:05,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:06,010 - INFO - Response - Page 3:
2025-05-11 12:00:06,211 - INFO - 第 3 页获取到 100 条记录
2025-05-11 12:00:06,211 - INFO - Request Parameters - Page 4:
2025-05-11 12:00:06,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:06,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:06,941 - INFO - Response - Page 4:
2025-05-11 12:00:07,141 - INFO - 第 4 页获取到 100 条记录
2025-05-11 12:00:07,141 - INFO - Request Parameters - Page 5:
2025-05-11 12:00:07,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:07,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:07,742 - INFO - Response - Page 5:
2025-05-11 12:00:07,942 - INFO - 第 5 页获取到 100 条记录
2025-05-11 12:00:07,942 - INFO - Request Parameters - Page 6:
2025-05-11 12:00:07,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:07,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:08,409 - INFO - Response - Page 6:
2025-05-11 12:00:08,609 - INFO - 第 6 页获取到 100 条记录
2025-05-11 12:00:08,609 - INFO - Request Parameters - Page 7:
2025-05-11 12:00:08,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:08,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:09,065 - INFO - Response - Page 7:
2025-05-11 12:00:09,265 - INFO - 第 7 页获取到 82 条记录
2025-05-11 12:00:09,265 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 12:00:09,265 - INFO - 获取到 682 条表单数据
2025-05-11 12:00:09,277 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 12:00:09,289 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 12:00:09,289 - INFO - 开始处理日期: 2025-02
2025-05-11 12:00:09,290 - INFO - Request Parameters - Page 1:
2025-05-11 12:00:09,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:09,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:09,819 - INFO - Response - Page 1:
2025-05-11 12:00:10,019 - INFO - 第 1 页获取到 100 条记录
2025-05-11 12:00:10,019 - INFO - Request Parameters - Page 2:
2025-05-11 12:00:10,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:10,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:10,636 - INFO - Response - Page 2:
2025-05-11 12:00:10,837 - INFO - 第 2 页获取到 100 条记录
2025-05-11 12:00:10,837 - INFO - Request Parameters - Page 3:
2025-05-11 12:00:10,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:10,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:11,362 - INFO - Response - Page 3:
2025-05-11 12:00:11,562 - INFO - 第 3 页获取到 100 条记录
2025-05-11 12:00:11,562 - INFO - Request Parameters - Page 4:
2025-05-11 12:00:11,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:11,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:12,038 - INFO - Response - Page 4:
2025-05-11 12:00:12,238 - INFO - 第 4 页获取到 100 条记录
2025-05-11 12:00:12,238 - INFO - Request Parameters - Page 5:
2025-05-11 12:00:12,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:12,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:12,732 - INFO - Response - Page 5:
2025-05-11 12:00:12,933 - INFO - 第 5 页获取到 100 条记录
2025-05-11 12:00:12,933 - INFO - Request Parameters - Page 6:
2025-05-11 12:00:12,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:12,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:13,447 - INFO - Response - Page 6:
2025-05-11 12:00:13,647 - INFO - 第 6 页获取到 100 条记录
2025-05-11 12:00:13,647 - INFO - Request Parameters - Page 7:
2025-05-11 12:00:13,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:13,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:14,088 - INFO - Response - Page 7:
2025-05-11 12:00:14,289 - INFO - 第 7 页获取到 70 条记录
2025-05-11 12:00:14,289 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 12:00:14,289 - INFO - 获取到 670 条表单数据
2025-05-11 12:00:14,300 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 12:00:14,311 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 12:00:14,311 - INFO - 开始处理日期: 2025-03
2025-05-11 12:00:14,312 - INFO - Request Parameters - Page 1:
2025-05-11 12:00:14,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:14,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:14,842 - INFO - Response - Page 1:
2025-05-11 12:00:15,043 - INFO - 第 1 页获取到 100 条记录
2025-05-11 12:00:15,043 - INFO - Request Parameters - Page 2:
2025-05-11 12:00:15,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:15,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:15,562 - INFO - Response - Page 2:
2025-05-11 12:00:15,762 - INFO - 第 2 页获取到 100 条记录
2025-05-11 12:00:15,762 - INFO - Request Parameters - Page 3:
2025-05-11 12:00:15,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:15,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:16,237 - INFO - Response - Page 3:
2025-05-11 12:00:16,437 - INFO - 第 3 页获取到 100 条记录
2025-05-11 12:00:16,437 - INFO - Request Parameters - Page 4:
2025-05-11 12:00:16,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:16,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:16,908 - INFO - Response - Page 4:
2025-05-11 12:00:17,109 - INFO - 第 4 页获取到 100 条记录
2025-05-11 12:00:17,109 - INFO - Request Parameters - Page 5:
2025-05-11 12:00:17,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:17,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:17,555 - INFO - Response - Page 5:
2025-05-11 12:00:17,756 - INFO - 第 5 页获取到 100 条记录
2025-05-11 12:00:17,756 - INFO - Request Parameters - Page 6:
2025-05-11 12:00:17,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:17,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:18,175 - INFO - Response - Page 6:
2025-05-11 12:00:18,376 - INFO - 第 6 页获取到 100 条记录
2025-05-11 12:00:18,376 - INFO - Request Parameters - Page 7:
2025-05-11 12:00:18,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:18,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:18,816 - INFO - Response - Page 7:
2025-05-11 12:00:19,016 - INFO - 第 7 页获取到 61 条记录
2025-05-11 12:00:19,016 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 12:00:19,016 - INFO - 获取到 661 条表单数据
2025-05-11 12:00:19,028 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 12:00:19,040 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 12:00:19,040 - INFO - 开始处理日期: 2025-04
2025-05-11 12:00:19,040 - INFO - Request Parameters - Page 1:
2025-05-11 12:00:19,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:19,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:19,545 - INFO - Response - Page 1:
2025-05-11 12:00:19,746 - INFO - 第 1 页获取到 100 条记录
2025-05-11 12:00:19,746 - INFO - Request Parameters - Page 2:
2025-05-11 12:00:19,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:19,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:20,257 - INFO - Response - Page 2:
2025-05-11 12:00:20,458 - INFO - 第 2 页获取到 100 条记录
2025-05-11 12:00:20,458 - INFO - Request Parameters - Page 3:
2025-05-11 12:00:20,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:20,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:20,965 - INFO - Response - Page 3:
2025-05-11 12:00:21,166 - INFO - 第 3 页获取到 100 条记录
2025-05-11 12:00:21,166 - INFO - Request Parameters - Page 4:
2025-05-11 12:00:21,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:21,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:21,747 - INFO - Response - Page 4:
2025-05-11 12:00:21,947 - INFO - 第 4 页获取到 100 条记录
2025-05-11 12:00:21,947 - INFO - Request Parameters - Page 5:
2025-05-11 12:00:21,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:21,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:22,430 - INFO - Response - Page 5:
2025-05-11 12:00:22,630 - INFO - 第 5 页获取到 100 条记录
2025-05-11 12:00:22,630 - INFO - Request Parameters - Page 6:
2025-05-11 12:00:22,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:22,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:23,056 - INFO - Response - Page 6:
2025-05-11 12:00:23,257 - INFO - 第 6 页获取到 100 条记录
2025-05-11 12:00:23,257 - INFO - Request Parameters - Page 7:
2025-05-11 12:00:23,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:23,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:23,693 - INFO - Response - Page 7:
2025-05-11 12:00:23,893 - INFO - 第 7 页获取到 54 条记录
2025-05-11 12:00:23,893 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 12:00:23,893 - INFO - 获取到 654 条表单数据
2025-05-11 12:00:23,906 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 12:00:23,919 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 12:00:23,919 - INFO - 开始处理日期: 2025-05
2025-05-11 12:00:23,919 - INFO - Request Parameters - Page 1:
2025-05-11 12:00:23,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:23,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:24,416 - INFO - Response - Page 1:
2025-05-11 12:00:24,617 - INFO - 第 1 页获取到 100 条记录
2025-05-11 12:00:24,617 - INFO - Request Parameters - Page 2:
2025-05-11 12:00:24,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:24,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:25,080 - INFO - Response - Page 2:
2025-05-11 12:00:25,280 - INFO - 第 2 页获取到 100 条记录
2025-05-11 12:00:25,280 - INFO - Request Parameters - Page 3:
2025-05-11 12:00:25,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:25,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:25,792 - INFO - Response - Page 3:
2025-05-11 12:00:25,992 - INFO - 第 3 页获取到 100 条记录
2025-05-11 12:00:25,992 - INFO - Request Parameters - Page 4:
2025-05-11 12:00:25,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:25,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:26,490 - INFO - Response - Page 4:
2025-05-11 12:00:26,690 - INFO - 第 4 页获取到 100 条记录
2025-05-11 12:00:26,690 - INFO - Request Parameters - Page 5:
2025-05-11 12:00:26,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:26,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:27,204 - INFO - Response - Page 5:
2025-05-11 12:00:27,404 - INFO - 第 5 页获取到 100 条记录
2025-05-11 12:00:27,404 - INFO - Request Parameters - Page 6:
2025-05-11 12:00:27,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:27,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:27,881 - INFO - Response - Page 6:
2025-05-11 12:00:28,082 - INFO - 第 6 页获取到 100 条记录
2025-05-11 12:00:28,082 - INFO - Request Parameters - Page 7:
2025-05-11 12:00:28,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 12:00:28,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 12:00:28,410 - INFO - Response - Page 7:
2025-05-11 12:00:28,610 - INFO - 第 7 页获取到 21 条记录
2025-05-11 12:00:28,610 - INFO - 查询完成，共获取到 621 条记录
2025-05-11 12:00:28,610 - INFO - 获取到 621 条表单数据
2025-05-11 12:00:28,622 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-11 12:00:28,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-11 12:00:29,140 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-11 12:00:29,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86600.0, 'new_value': 123000.0}, {'field': 'total_amount', 'old_value': 86600.0, 'new_value': 123000.0}]
2025-05-11 12:00:29,140 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-11 12:00:29,516 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-11 12:00:29,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 705.0, 'new_value': 795.0}, {'field': 'offline_amount', 'old_value': 20305.0, 'new_value': 22105.0}, {'field': 'total_amount', 'old_value': 21010.0, 'new_value': 22900.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 283}]
2025-05-11 12:00:29,517 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-11 12:00:30,000 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-11 12:00:30,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138229.0, 'new_value': 156517.0}, {'field': 'total_amount', 'old_value': 138229.0, 'new_value': 156517.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 110}]
2025-05-11 12:00:30,000 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-11 12:00:30,495 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-11 12:00:30,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12720.0, 'new_value': 12985.0}, {'field': 'total_amount', 'old_value': 12720.0, 'new_value': 12985.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 49}]
2025-05-11 12:00:30,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-11 12:00:31,022 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-11 12:00:31,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96372.0, 'new_value': 107441.0}, {'field': 'total_amount', 'old_value': 96372.0, 'new_value': 107441.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-11 12:00:31,022 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-11 12:00:31,550 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-11 12:00:31,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56607.73, 'new_value': 63533.34}, {'field': 'total_amount', 'old_value': 56607.73, 'new_value': 63533.34}, {'field': 'order_count', 'old_value': 2559, 'new_value': 2832}]
2025-05-11 12:00:31,550 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-11 12:00:32,016 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-11 12:00:32,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275663.08, 'new_value': 305952.08}, {'field': 'total_amount', 'old_value': 275663.08, 'new_value': 305952.08}, {'field': 'order_count', 'old_value': 832, 'new_value': 922}]
2025-05-11 12:00:32,017 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-11 12:00:32,447 - INFO - 更新表单数据成功: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-11 12:00:32,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16149.0, 'new_value': 19719.0}, {'field': 'total_amount', 'old_value': 16149.0, 'new_value': 19719.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-11 12:00:32,448 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-11 12:00:32,874 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-11 12:00:32,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7759.0, 'new_value': 8387.0}, {'field': 'total_amount', 'old_value': 10656.0, 'new_value': 11284.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-11 12:00:32,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-11 12:00:33,308 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-11 12:00:33,309 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9421.3, 'new_value': 10394.32}, {'field': 'offline_amount', 'old_value': 5524.49, 'new_value': 6197.13}, {'field': 'total_amount', 'old_value': 14945.79, 'new_value': 16591.45}, {'field': 'order_count', 'old_value': 732, 'new_value': 814}]
2025-05-11 12:00:33,309 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-11 12:00:33,759 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-11 12:00:33,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18669.36, 'new_value': 21206.36}, {'field': 'total_amount', 'old_value': 18669.36, 'new_value': 21206.36}, {'field': 'order_count', 'old_value': 43, 'new_value': 48}]
2025-05-11 12:00:33,759 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-11 12:00:34,234 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-11 12:00:34,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97040.0, 'new_value': 114020.0}, {'field': 'total_amount', 'old_value': 97040.0, 'new_value': 114020.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 60}]
2025-05-11 12:00:34,235 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-11 12:00:34,718 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-11 12:00:34,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79001.0, 'new_value': 81001.0}, {'field': 'total_amount', 'old_value': 87200.0, 'new_value': 89200.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-11 12:00:34,718 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-11 12:00:35,232 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-11 12:00:35,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45992.0, 'new_value': 47653.0}, {'field': 'total_amount', 'old_value': 45992.0, 'new_value': 47653.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-05-11 12:00:35,233 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-11 12:00:35,684 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-11 12:00:35,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20400.0, 'new_value': 22380.0}, {'field': 'total_amount', 'old_value': 20400.0, 'new_value': 22380.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-11 12:00:35,685 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-11 12:00:36,187 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-11 12:00:36,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12160.0, 'new_value': 13960.0}, {'field': 'total_amount', 'old_value': 16280.0, 'new_value': 18080.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 180}]
2025-05-11 12:00:36,187 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-11 12:00:36,665 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-11 12:00:36,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 689.9, 'new_value': 860.9}, {'field': 'offline_amount', 'old_value': 23820.8, 'new_value': 24820.8}, {'field': 'total_amount', 'old_value': 24510.7, 'new_value': 25681.7}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-11 12:00:36,665 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-11 12:00:37,101 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-11 12:00:37,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10078.0, 'new_value': 11443.0}, {'field': 'total_amount', 'old_value': 10078.0, 'new_value': 11443.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-11 12:00:37,101 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-11 12:00:37,527 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-11 12:00:37,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5300.0, 'new_value': 16825.0}, {'field': 'total_amount', 'old_value': 5300.0, 'new_value': 16825.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-11 12:00:37,527 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-11 12:00:38,056 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-11 12:00:38,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65569.0, 'new_value': 67387.0}, {'field': 'total_amount', 'old_value': 65569.0, 'new_value': 67387.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-11 12:00:38,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-11 12:00:38,540 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-11 12:00:38,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21374.48, 'new_value': 23025.69}, {'field': 'offline_amount', 'old_value': 42748.2, 'new_value': 45881.8}, {'field': 'total_amount', 'old_value': 64122.68, 'new_value': 68907.49}, {'field': 'order_count', 'old_value': 826, 'new_value': 877}]
2025-05-11 12:00:38,540 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-11 12:00:39,016 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-11 12:00:39,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9734.37, 'new_value': 10401.14}, {'field': 'offline_amount', 'old_value': 10647.09, 'new_value': 11907.36}, {'field': 'total_amount', 'old_value': 20381.46, 'new_value': 22308.5}, {'field': 'order_count', 'old_value': 977, 'new_value': 1056}]
2025-05-11 12:00:39,016 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-11 12:00:39,500 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-11 12:00:39,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 31880.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 31880.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-11 12:00:39,500 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-11 12:00:39,974 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-11 12:00:39,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140119.3, 'new_value': 160807.2}, {'field': 'total_amount', 'old_value': 255139.0, 'new_value': 275826.9}, {'field': 'order_count', 'old_value': 1515, 'new_value': 1636}]
2025-05-11 12:00:39,974 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-11 12:00:40,370 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-11 12:00:40,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37164.0, 'new_value': 41617.0}, {'field': 'total_amount', 'old_value': 37164.0, 'new_value': 41617.0}, {'field': 'order_count', 'old_value': 2028, 'new_value': 2261}]
2025-05-11 12:00:40,370 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-11 12:00:40,894 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-11 12:00:40,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26030.0, 'new_value': 30030.0}, {'field': 'total_amount', 'old_value': 26030.0, 'new_value': 30030.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-05-11 12:00:40,894 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-11 12:00:41,416 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-11 12:00:41,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25580.0, 'new_value': 30080.0}, {'field': 'total_amount', 'old_value': 25580.0, 'new_value': 30080.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-11 12:00:41,416 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-11 12:00:41,907 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-11 12:00:41,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139190.0, 'new_value': 164276.0}, {'field': 'total_amount', 'old_value': 139190.0, 'new_value': 164276.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 177}]
2025-05-11 12:00:41,907 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-11 12:00:42,481 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-11 12:00:42,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47987.08, 'new_value': 53000.1}, {'field': 'total_amount', 'old_value': 47987.08, 'new_value': 53000.1}, {'field': 'order_count', 'old_value': 541, 'new_value': 596}]
2025-05-11 12:00:42,482 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-11 12:00:42,937 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-11 12:00:42,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 382931.0, 'new_value': 422708.0}, {'field': 'offline_amount', 'old_value': 132762.0, 'new_value': 143644.0}, {'field': 'total_amount', 'old_value': 515693.0, 'new_value': 566352.0}, {'field': 'order_count', 'old_value': 588, 'new_value': 666}]
2025-05-11 12:00:42,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-11 12:00:43,455 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-11 12:00:43,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4030.45, 'new_value': 4281.1}, {'field': 'offline_amount', 'old_value': 49429.0, 'new_value': 55376.0}, {'field': 'total_amount', 'old_value': 53459.45, 'new_value': 59657.1}, {'field': 'order_count', 'old_value': 1037, 'new_value': 1175}]
2025-05-11 12:00:43,455 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-11 12:00:43,927 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-11 12:00:43,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126150.0, 'new_value': 131750.0}, {'field': 'total_amount', 'old_value': 126150.0, 'new_value': 131750.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-11 12:00:43,928 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-11 12:00:44,436 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-11 12:00:44,436 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143245.89, 'new_value': 147745.89}, {'field': 'total_amount', 'old_value': 143245.89, 'new_value': 147745.89}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-11 12:00:44,436 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-11 12:00:44,921 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-11 12:00:44,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109890.13, 'new_value': 117690.13}, {'field': 'total_amount', 'old_value': 149250.13, 'new_value': 157050.13}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-11 12:00:44,921 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-11 12:00:45,333 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-11 12:00:45,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 248831.7, 'new_value': 358999.7}, {'field': 'total_amount', 'old_value': 250089.1, 'new_value': 360257.1}, {'field': 'order_count', 'old_value': 33, 'new_value': 43}]
2025-05-11 12:00:45,334 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-11 12:00:45,738 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-11 12:00:45,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12832.7, 'new_value': 13931.4}, {'field': 'offline_amount', 'old_value': 21197.6, 'new_value': 27433.6}, {'field': 'total_amount', 'old_value': 34030.3, 'new_value': 41365.0}, {'field': 'order_count', 'old_value': 498, 'new_value': 544}]
2025-05-11 12:00:45,738 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-11 12:00:46,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-11 12:00:46,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2680.0, 'new_value': 2931.0}, {'field': 'offline_amount', 'old_value': 5764.0, 'new_value': 10232.0}, {'field': 'total_amount', 'old_value': 8444.0, 'new_value': 13163.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 31}]
2025-05-11 12:00:46,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-11 12:00:46,610 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-11 12:00:46,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71629.0, 'new_value': 79734.0}, {'field': 'total_amount', 'old_value': 71629.0, 'new_value': 79734.0}, {'field': 'order_count', 'old_value': 133, 'new_value': 149}]
2025-05-11 12:00:46,610 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-11 12:00:47,033 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-11 12:00:47,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12470.0, 'new_value': 12608.0}, {'field': 'total_amount', 'old_value': 12470.0, 'new_value': 12608.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-11 12:00:47,033 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-11 12:00:47,524 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-11 12:00:47,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75653.49, 'new_value': 92225.1}, {'field': 'total_amount', 'old_value': 75653.49, 'new_value': 92225.1}, {'field': 'order_count', 'old_value': 81, 'new_value': 89}]
2025-05-11 12:00:47,525 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-11 12:00:48,037 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-11 12:00:48,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11573.53, 'new_value': 12912.78}, {'field': 'total_amount', 'old_value': 11573.53, 'new_value': 12912.78}, {'field': 'order_count', 'old_value': 925, 'new_value': 1005}]
2025-05-11 12:00:48,037 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-11 12:00:48,450 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-11 12:00:48,451 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74913.47, 'new_value': 86134.96}, {'field': 'offline_amount', 'old_value': 13226.84, 'new_value': 14886.97}, {'field': 'total_amount', 'old_value': 88140.31, 'new_value': 101021.93}, {'field': 'order_count', 'old_value': 322, 'new_value': 367}]
2025-05-11 12:00:48,451 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-11 12:00:48,891 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-11 12:00:48,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81607.0, 'new_value': 92717.0}, {'field': 'offline_amount', 'old_value': 27744.25, 'new_value': 32769.61}, {'field': 'total_amount', 'old_value': 109351.25, 'new_value': 125486.61}, {'field': 'order_count', 'old_value': 639, 'new_value': 750}]
2025-05-11 12:00:48,891 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-11 12:00:49,376 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-11 12:00:49,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17231.0, 'new_value': 18817.0}, {'field': 'total_amount', 'old_value': 19575.0, 'new_value': 21161.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 85}]
2025-05-11 12:00:49,377 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-11 12:00:49,786 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-11 12:00:49,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2924.0, 'new_value': 3521.0}, {'field': 'total_amount', 'old_value': 3924.0, 'new_value': 4521.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 79}]
2025-05-11 12:00:49,786 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-11 12:00:50,185 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-11 12:00:50,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33003.13, 'new_value': 36812.33}, {'field': 'total_amount', 'old_value': 33003.13, 'new_value': 36812.33}, {'field': 'order_count', 'old_value': 847, 'new_value': 941}]
2025-05-11 12:00:50,186 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-11 12:00:50,648 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-11 12:00:50,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32213.88, 'new_value': 37064.68}, {'field': 'total_amount', 'old_value': 32213.88, 'new_value': 37064.68}, {'field': 'order_count', 'old_value': 54, 'new_value': 62}]
2025-05-11 12:00:50,649 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-11 12:00:51,160 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-11 12:00:51,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1757.0, 'new_value': 2045.0}, {'field': 'total_amount', 'old_value': 1757.0, 'new_value': 2045.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 294}]
2025-05-11 12:00:51,161 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-11 12:00:51,635 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-11 12:00:51,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58413.0, 'new_value': 73075.0}, {'field': 'total_amount', 'old_value': 58413.0, 'new_value': 73075.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 25}]
2025-05-11 12:00:51,635 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-11 12:00:52,114 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-11 12:00:52,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58100.0, 'new_value': 68543.0}, {'field': 'offline_amount', 'old_value': 26304.26, 'new_value': 29325.26}, {'field': 'total_amount', 'old_value': 84404.26, 'new_value': 97868.26}, {'field': 'order_count', 'old_value': 588, 'new_value': 675}]
2025-05-11 12:00:52,114 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-11 12:00:52,565 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-11 12:00:52,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4547.86, 'new_value': 5112.44}, {'field': 'offline_amount', 'old_value': 68212.47, 'new_value': 73797.38}, {'field': 'total_amount', 'old_value': 72760.33, 'new_value': 78909.82}, {'field': 'order_count', 'old_value': 798, 'new_value': 875}]
2025-05-11 12:00:52,566 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-11 12:00:52,994 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-11 12:00:52,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18659.5, 'new_value': 20628.6}, {'field': 'total_amount', 'old_value': 18660.5, 'new_value': 20629.6}, {'field': 'order_count', 'old_value': 103, 'new_value': 115}]
2025-05-11 12:00:52,994 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-11 12:00:53,459 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-11 12:00:53,459 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26910.0, 'new_value': 32310.0}, {'field': 'offline_amount', 'old_value': 25938.0, 'new_value': 27558.0}, {'field': 'total_amount', 'old_value': 52848.0, 'new_value': 59868.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 23}]
2025-05-11 12:00:53,459 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-11 12:00:53,949 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-11 12:00:53,949 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89040.0, 'new_value': 102981.0}, {'field': 'total_amount', 'old_value': 89040.0, 'new_value': 102981.0}, {'field': 'order_count', 'old_value': 425, 'new_value': 482}]
2025-05-11 12:00:53,949 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-11 12:00:54,384 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-11 12:00:54,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75999.63, 'new_value': 86285.53}, {'field': 'total_amount', 'old_value': 75999.63, 'new_value': 86285.53}, {'field': 'order_count', 'old_value': 234, 'new_value': 265}]
2025-05-11 12:00:54,385 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-11 12:00:54,912 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-11 12:00:54,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33091.47, 'new_value': 35415.63}, {'field': 'offline_amount', 'old_value': 15645.46, 'new_value': 18399.01}, {'field': 'total_amount', 'old_value': 48736.93, 'new_value': 53814.64}, {'field': 'order_count', 'old_value': 1605, 'new_value': 1778}]
2025-05-11 12:00:54,912 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-11 12:00:55,481 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-11 12:00:55,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51222.0, 'new_value': 57993.0}, {'field': 'total_amount', 'old_value': 51222.0, 'new_value': 57993.0}, {'field': 'order_count', 'old_value': 1223, 'new_value': 1393}]
2025-05-11 12:00:55,481 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-11 12:00:55,989 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-11 12:00:55,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5508.05, 'new_value': 6571.85}, {'field': 'total_amount', 'old_value': 5508.05, 'new_value': 6571.85}, {'field': 'order_count', 'old_value': 81, 'new_value': 102}]
2025-05-11 12:00:55,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-11 12:00:56,444 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-11 12:00:56,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31100.23, 'new_value': 35078.97}, {'field': 'offline_amount', 'old_value': 312032.57, 'new_value': 354665.61}, {'field': 'total_amount', 'old_value': 343132.8, 'new_value': 389744.58}, {'field': 'order_count', 'old_value': 1069, 'new_value': 1278}]
2025-05-11 12:00:56,444 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-11 12:00:56,938 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-11 12:00:56,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10527.37, 'new_value': 11032.31}, {'field': 'total_amount', 'old_value': 10527.37, 'new_value': 11032.31}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-05-11 12:00:56,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-11 12:00:57,392 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-11 12:00:57,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56287.0, 'new_value': 62000.0}, {'field': 'total_amount', 'old_value': 56287.0, 'new_value': 62000.0}, {'field': 'order_count', 'old_value': 2026, 'new_value': 2234}]
2025-05-11 12:00:57,393 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-11 12:00:57,858 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-11 12:00:57,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263855.79, 'new_value': 292596.54}, {'field': 'total_amount', 'old_value': 263855.79, 'new_value': 292596.54}, {'field': 'order_count', 'old_value': 1895, 'new_value': 2124}]
2025-05-11 12:00:57,858 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-11 12:00:58,540 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-11 12:00:58,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17106.0, 'new_value': 18483.0}, {'field': 'total_amount', 'old_value': 17106.0, 'new_value': 18483.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-11 12:00:58,540 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-11 12:00:58,998 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-11 12:00:58,998 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13243.0, 'new_value': 13362.0}, {'field': 'offline_amount', 'old_value': 203647.0, 'new_value': 210422.0}, {'field': 'total_amount', 'old_value': 216890.0, 'new_value': 223784.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-05-11 12:00:58,998 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-11 12:00:59,393 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-11 12:00:59,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44976.31, 'new_value': 50798.08}, {'field': 'total_amount', 'old_value': 44976.31, 'new_value': 50798.08}, {'field': 'order_count', 'old_value': 1684, 'new_value': 1907}]
2025-05-11 12:00:59,394 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-11 12:00:59,843 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-11 12:00:59,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39881.0, 'new_value': 66143.0}, {'field': 'offline_amount', 'old_value': 28270.0, 'new_value': 30367.0}, {'field': 'total_amount', 'old_value': 68151.0, 'new_value': 96510.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 36}]
2025-05-11 12:00:59,843 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-11 12:01:00,238 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-11 12:01:00,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1223.22, 'new_value': 1355.62}, {'field': 'offline_amount', 'old_value': 59371.78, 'new_value': 65114.0}, {'field': 'total_amount', 'old_value': 60595.0, 'new_value': 66469.62}, {'field': 'order_count', 'old_value': 258, 'new_value': 278}]
2025-05-11 12:01:00,238 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-11 12:01:00,766 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-11 12:01:00,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44976.0, 'new_value': 45716.0}, {'field': 'total_amount', 'old_value': 44976.0, 'new_value': 45716.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 72}]
2025-05-11 12:01:00,767 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-11 12:01:01,218 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-11 12:01:01,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6026.64}, {'field': 'offline_amount', 'old_value': 286073.0, 'new_value': 302367.0}, {'field': 'total_amount', 'old_value': 286073.0, 'new_value': 308393.64}, {'field': 'order_count', 'old_value': 264, 'new_value': 301}]
2025-05-11 12:01:01,219 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-11 12:01:01,640 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-11 12:01:01,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64379.03, 'new_value': 73443.66}, {'field': 'total_amount', 'old_value': 64379.03, 'new_value': 73443.66}, {'field': 'order_count', 'old_value': 334, 'new_value': 378}]
2025-05-11 12:01:01,640 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-11 12:01:02,103 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-11 12:01:02,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71440.54, 'new_value': 79312.4}, {'field': 'offline_amount', 'old_value': 186857.3, 'new_value': 212105.33}, {'field': 'total_amount', 'old_value': 258297.84, 'new_value': 291417.73}, {'field': 'order_count', 'old_value': 1796, 'new_value': 2003}]
2025-05-11 12:01:02,103 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-11 12:01:02,610 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-11 12:01:02,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 725535.9, 'new_value': 837203.3}, {'field': 'total_amount', 'old_value': 725535.9, 'new_value': 837203.3}, {'field': 'order_count', 'old_value': 1254, 'new_value': 1426}]
2025-05-11 12:01:02,610 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-11 12:01:03,159 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-11 12:01:03,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48611.15, 'new_value': 60460.15}, {'field': 'offline_amount', 'old_value': 28380.0, 'new_value': 35721.0}, {'field': 'total_amount', 'old_value': 76991.15, 'new_value': 96181.15}, {'field': 'order_count', 'old_value': 428, 'new_value': 517}]
2025-05-11 12:01:03,159 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-11 12:01:03,595 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-11 12:01:03,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2260.0, 'new_value': 2458.0}, {'field': 'total_amount', 'old_value': 2260.0, 'new_value': 2458.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-11 12:01:03,596 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-11 12:01:04,066 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-11 12:01:04,066 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18072.9, 'new_value': 20408.9}, {'field': 'total_amount', 'old_value': 18072.9, 'new_value': 20408.9}, {'field': 'order_count', 'old_value': 83, 'new_value': 92}]
2025-05-11 12:01:04,067 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-11 12:01:04,588 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-11 12:01:04,588 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27063.0, 'new_value': 28646.0}, {'field': 'offline_amount', 'old_value': 15147.0, 'new_value': 18290.0}, {'field': 'total_amount', 'old_value': 42210.0, 'new_value': 46936.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-11 12:01:04,588 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-11 12:01:05,203 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-11 12:01:05,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10788.0, 'new_value': 11716.0}, {'field': 'total_amount', 'old_value': 10788.0, 'new_value': 11716.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 202}]
2025-05-11 12:01:05,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-11 12:01:05,686 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-11 12:01:05,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59570.0, 'new_value': 61326.0}, {'field': 'total_amount', 'old_value': 59571.0, 'new_value': 61327.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 34}]
2025-05-11 12:01:05,687 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-11 12:01:06,158 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-11 12:01:06,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2165.66, 'new_value': 2619.46}, {'field': 'offline_amount', 'old_value': 5523.74, 'new_value': 6785.29}, {'field': 'total_amount', 'old_value': 7689.4, 'new_value': 9404.75}, {'field': 'order_count', 'old_value': 270, 'new_value': 323}]
2025-05-11 12:01:06,159 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-11 12:01:06,632 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-11 12:01:06,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62382.51, 'new_value': 69428.82}, {'field': 'offline_amount', 'old_value': 48026.0, 'new_value': 54267.2}, {'field': 'total_amount', 'old_value': 110408.51, 'new_value': 123696.02}, {'field': 'order_count', 'old_value': 945, 'new_value': 1057}]
2025-05-11 12:01:06,632 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-11 12:01:07,050 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-11 12:01:07,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3.3}, {'field': 'total_amount', 'old_value': 27610.66, 'new_value': 27613.96}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-11 12:01:07,051 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-11 12:01:07,512 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-11 12:01:07,512 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141528.9, 'new_value': 163139.9}, {'field': 'offline_amount', 'old_value': 25597.0, 'new_value': 29074.0}, {'field': 'total_amount', 'old_value': 167125.9, 'new_value': 192213.9}, {'field': 'order_count', 'old_value': 211, 'new_value': 239}]
2025-05-11 12:01:07,513 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-11 12:01:07,966 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-11 12:01:07,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14328.0, 'new_value': 16504.0}, {'field': 'total_amount', 'old_value': 14328.0, 'new_value': 16504.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 42}]
2025-05-11 12:01:07,966 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-11 12:01:08,427 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-11 12:01:08,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24302.0, 'new_value': 28349.0}, {'field': 'total_amount', 'old_value': 24302.0, 'new_value': 28349.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 22}]
2025-05-11 12:01:08,427 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-11 12:01:08,938 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-11 12:01:08,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3769.1}, {'field': 'total_amount', 'old_value': 51513.99, 'new_value': 55283.09}, {'field': 'order_count', 'old_value': 251, 'new_value': 276}]
2025-05-11 12:01:08,938 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-11 12:01:09,480 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-11 12:01:09,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12590.0, 'new_value': 19446.0}, {'field': 'offline_amount', 'old_value': 74557.0, 'new_value': 78170.0}, {'field': 'total_amount', 'old_value': 87147.0, 'new_value': 97616.0}, {'field': 'order_count', 'old_value': 1723, 'new_value': 1964}]
2025-05-11 12:01:09,481 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-11 12:01:09,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-11 12:01:09,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5092.66, 'new_value': 5906.16}, {'field': 'offline_amount', 'old_value': 104076.8, 'new_value': 119409.0}, {'field': 'total_amount', 'old_value': 109169.46, 'new_value': 125315.16}, {'field': 'order_count', 'old_value': 791, 'new_value': 892}]
2025-05-11 12:01:09,922 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-11 12:01:10,364 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-11 12:01:10,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28729.0, 'new_value': 47304.0}, {'field': 'total_amount', 'old_value': 28987.0, 'new_value': 47562.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-11 12:01:10,364 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-11 12:01:10,848 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-11 12:01:10,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62847.0, 'new_value': 72082.0}, {'field': 'total_amount', 'old_value': 75847.0, 'new_value': 85082.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-05-11 12:01:10,849 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-11 12:01:11,402 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-11 12:01:11,402 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54726.19, 'new_value': 62607.64}, {'field': 'offline_amount', 'old_value': 49959.45, 'new_value': 59625.45}, {'field': 'total_amount', 'old_value': 104685.64, 'new_value': 122233.09}, {'field': 'order_count', 'old_value': 1018, 'new_value': 1189}]
2025-05-11 12:01:11,402 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-11 12:01:11,825 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-11 12:01:11,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 377890.02, 'new_value': 403847.54}, {'field': 'offline_amount', 'old_value': 96098.91, 'new_value': 118802.9}, {'field': 'total_amount', 'old_value': 473988.93, 'new_value': 522650.44}, {'field': 'order_count', 'old_value': 4712, 'new_value': 5054}]
2025-05-11 12:01:11,825 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-11 12:01:12,269 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-11 12:01:12,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20513.6, 'new_value': 22349.6}, {'field': 'offline_amount', 'old_value': 35.0, 'new_value': 40.0}, {'field': 'total_amount', 'old_value': 20548.6, 'new_value': 22389.6}, {'field': 'order_count', 'old_value': 93, 'new_value': 96}]
2025-05-11 12:01:12,269 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-11 12:01:12,734 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-11 12:01:12,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28006.1, 'new_value': 30423.8}, {'field': 'total_amount', 'old_value': 28894.45, 'new_value': 31312.15}, {'field': 'order_count', 'old_value': 73, 'new_value': 80}]
2025-05-11 12:01:12,735 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-11 12:01:13,259 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-11 12:01:13,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27468.07, 'new_value': 33351.87}, {'field': 'total_amount', 'old_value': 27468.07, 'new_value': 33351.87}, {'field': 'order_count', 'old_value': 761, 'new_value': 882}]
2025-05-11 12:01:13,260 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-11 12:01:13,667 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-11 12:01:13,667 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3928.9, 'new_value': 4273.7}, {'field': 'offline_amount', 'old_value': 17341.0, 'new_value': 19860.0}, {'field': 'total_amount', 'old_value': 21269.9, 'new_value': 24133.7}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-05-11 12:01:13,667 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-11 12:01:14,052 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-11 12:01:14,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121962.0, 'new_value': 123942.0}, {'field': 'total_amount', 'old_value': 121962.0, 'new_value': 123942.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-11 12:01:14,052 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-11 12:01:14,571 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-11 12:01:14,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41094.55, 'new_value': 46645.93}, {'field': 'offline_amount', 'old_value': 165515.7, 'new_value': 179656.82}, {'field': 'total_amount', 'old_value': 206610.25, 'new_value': 226302.75}, {'field': 'order_count', 'old_value': 1021, 'new_value': 1090}]
2025-05-11 12:01:14,572 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-11 12:01:14,994 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-11 12:01:14,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115035.13, 'new_value': 128312.73}, {'field': 'total_amount', 'old_value': 115035.13, 'new_value': 128312.73}, {'field': 'order_count', 'old_value': 235, 'new_value': 263}]
2025-05-11 12:01:14,994 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-11 12:01:15,522 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-11 12:01:15,522 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6438.27, 'new_value': 6813.04}, {'field': 'offline_amount', 'old_value': 173026.26, 'new_value': 192529.55}, {'field': 'total_amount', 'old_value': 179464.53, 'new_value': 199342.59}, {'field': 'order_count', 'old_value': 703, 'new_value': 778}]
2025-05-11 12:01:15,522 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-11 12:01:16,000 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-11 12:01:16,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39092.0, 'new_value': 39975.0}, {'field': 'offline_amount', 'old_value': 22463.25, 'new_value': 22983.25}, {'field': 'total_amount', 'old_value': 61555.25, 'new_value': 62958.25}, {'field': 'order_count', 'old_value': 73, 'new_value': 78}]
2025-05-11 12:01:16,000 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-11 12:01:16,448 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-11 12:01:16,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63284.7, 'new_value': 71219.5}, {'field': 'total_amount', 'old_value': 63284.7, 'new_value': 71219.5}, {'field': 'order_count', 'old_value': 130, 'new_value': 151}]
2025-05-11 12:01:16,449 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-11 12:01:16,854 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-11 12:01:16,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115281.82, 'new_value': 117416.85}, {'field': 'offline_amount', 'old_value': 13063.12, 'new_value': 29041.0}, {'field': 'total_amount', 'old_value': 128344.94, 'new_value': 146457.85}, {'field': 'order_count', 'old_value': 869, 'new_value': 1855}]
2025-05-11 12:01:16,854 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-11 12:01:17,326 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-11 12:01:17,326 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4612.0, 'new_value': 5770.0}, {'field': 'total_amount', 'old_value': 4612.0, 'new_value': 5770.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-11 12:01:17,326 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-11 12:01:17,767 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-11 12:01:17,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25679.0, 'new_value': 26991.0}, {'field': 'total_amount', 'old_value': 25679.0, 'new_value': 26991.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-11 12:01:17,767 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-11 12:01:18,197 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-11 12:01:18,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25733.44, 'new_value': 27777.44}, {'field': 'total_amount', 'old_value': 25733.44, 'new_value': 27777.44}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-05-11 12:01:18,198 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-11 12:01:18,767 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-11 12:01:18,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18386.0, 'new_value': 21257.0}, {'field': 'total_amount', 'old_value': 18386.0, 'new_value': 21257.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 42}]
2025-05-11 12:01:18,767 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-11 12:01:19,200 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-11 12:01:19,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52127.78, 'new_value': 58414.5}, {'field': 'total_amount', 'old_value': 52127.78, 'new_value': 58414.5}, {'field': 'order_count', 'old_value': 318, 'new_value': 364}]
2025-05-11 12:01:19,200 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-11 12:01:19,663 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-11 12:01:19,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2438.0, 'new_value': 2838.0}, {'field': 'total_amount', 'old_value': 2438.0, 'new_value': 2838.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-11 12:01:19,663 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-11 12:01:20,139 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-11 12:01:20,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3184.82, 'new_value': 3273.02}, {'field': 'offline_amount', 'old_value': 13151.0, 'new_value': 16968.0}, {'field': 'total_amount', 'old_value': 16335.82, 'new_value': 20241.02}, {'field': 'order_count', 'old_value': 79, 'new_value': 86}]
2025-05-11 12:01:20,139 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-11 12:01:20,557 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-11 12:01:20,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22127.1, 'new_value': 22906.2}, {'field': 'total_amount', 'old_value': 22323.9, 'new_value': 23103.0}, {'field': 'order_count', 'old_value': 181, 'new_value': 196}]
2025-05-11 12:01:20,557 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-11 12:01:21,118 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-11 12:01:21,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2072.0, 'new_value': 2271.0}, {'field': 'offline_amount', 'old_value': 9849.8, 'new_value': 11153.4}, {'field': 'total_amount', 'old_value': 11921.8, 'new_value': 13424.4}, {'field': 'order_count', 'old_value': 469, 'new_value': 534}]
2025-05-11 12:01:21,118 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-11 12:01:21,558 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-11 12:01:21,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47440.42, 'new_value': 49349.02}, {'field': 'total_amount', 'old_value': 47440.42, 'new_value': 49349.02}, {'field': 'order_count', 'old_value': 147, 'new_value': 160}]
2025-05-11 12:01:21,558 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-11 12:01:22,044 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-11 12:01:22,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40600.0, 'new_value': 46240.0}, {'field': 'total_amount', 'old_value': 40600.0, 'new_value': 46240.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-11 12:01:22,044 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-11 12:01:22,516 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-11 12:01:22,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22897.0, 'new_value': 34895.0}, {'field': 'total_amount', 'old_value': 22897.0, 'new_value': 34895.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-11 12:01:22,516 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-11 12:01:23,007 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-11 12:01:23,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77516.6, 'new_value': 88748.6}, {'field': 'total_amount', 'old_value': 77516.6, 'new_value': 88748.6}, {'field': 'order_count', 'old_value': 281, 'new_value': 321}]
2025-05-11 12:01:23,007 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-11 12:01:23,592 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-11 12:01:23,592 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 235.0, 'new_value': 894.0}, {'field': 'offline_amount', 'old_value': 23513.0, 'new_value': 26164.0}, {'field': 'total_amount', 'old_value': 23748.0, 'new_value': 27058.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 106}]
2025-05-11 12:01:23,592 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-11 12:01:24,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-11 12:01:24,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102259.47, 'new_value': 114504.6}, {'field': 'total_amount', 'old_value': 102259.47, 'new_value': 114504.6}, {'field': 'order_count', 'old_value': 2703, 'new_value': 3042}]
2025-05-11 12:01:24,119 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-11 12:01:24,567 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-11 12:01:24,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58853.73, 'new_value': 67426.95}, {'field': 'total_amount', 'old_value': 58853.73, 'new_value': 67426.95}, {'field': 'order_count', 'old_value': 273, 'new_value': 318}]
2025-05-11 12:01:24,568 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-11 12:01:24,992 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-11 12:01:24,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8562.33, 'new_value': 9473.57}, {'field': 'offline_amount', 'old_value': 15483.46, 'new_value': 17119.46}, {'field': 'total_amount', 'old_value': 24045.79, 'new_value': 26593.03}, {'field': 'order_count', 'old_value': 871, 'new_value': 961}]
2025-05-11 12:01:24,992 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-11 12:01:25,458 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-11 12:01:25,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22054.0, 'new_value': 26845.0}, {'field': 'total_amount', 'old_value': 24462.0, 'new_value': 29253.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 113}]
2025-05-11 12:01:25,458 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-11 12:01:25,896 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-11 12:01:25,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33357.0, 'new_value': 34309.0}, {'field': 'total_amount', 'old_value': 33357.0, 'new_value': 34309.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-11 12:01:25,897 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-11 12:01:26,409 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-11 12:01:26,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10463.7, 'new_value': 11433.7}, {'field': 'offline_amount', 'old_value': 22929.56, 'new_value': 26345.82}, {'field': 'total_amount', 'old_value': 33393.26, 'new_value': 37779.52}, {'field': 'order_count', 'old_value': 359, 'new_value': 414}]
2025-05-11 12:01:26,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-11 12:01:26,870 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-11 12:01:26,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40574.0, 'new_value': 45115.5}, {'field': 'offline_amount', 'old_value': 40592.79, 'new_value': 50922.38}, {'field': 'total_amount', 'old_value': 81166.79, 'new_value': 96037.88}, {'field': 'order_count', 'old_value': 556, 'new_value': 647}]
2025-05-11 12:01:26,870 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-11 12:01:27,329 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-11 12:01:27,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34496.55, 'new_value': 38853.37}, {'field': 'offline_amount', 'old_value': 25565.54, 'new_value': 28804.36}, {'field': 'total_amount', 'old_value': 60062.09, 'new_value': 67657.73}, {'field': 'order_count', 'old_value': 2425, 'new_value': 2738}]
2025-05-11 12:01:27,329 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-11 12:01:27,825 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-11 12:01:27,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6216.34, 'new_value': 6964.83}, {'field': 'offline_amount', 'old_value': 13809.11, 'new_value': 15570.81}, {'field': 'total_amount', 'old_value': 20025.45, 'new_value': 22535.64}, {'field': 'order_count', 'old_value': 1055, 'new_value': 1178}]
2025-05-11 12:01:27,825 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-11 12:01:28,291 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-11 12:01:28,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42810.0, 'new_value': 46530.0}, {'field': 'total_amount', 'old_value': 42810.0, 'new_value': 46530.0}, {'field': 'order_count', 'old_value': 2178, 'new_value': 2313}]
2025-05-11 12:01:28,292 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-11 12:01:28,764 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-11 12:01:28,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11773.2, 'new_value': 16269.2}, {'field': 'total_amount', 'old_value': 11773.2, 'new_value': 16269.2}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-11 12:01:28,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-11 12:01:29,209 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-11 12:01:29,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22375.0, 'new_value': 24295.0}, {'field': 'total_amount', 'old_value': 22375.0, 'new_value': 24295.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-11 12:01:29,209 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-11 12:01:29,737 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-11 12:01:29,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2241000.0, 'new_value': 2561045.0}, {'field': 'total_amount', 'old_value': 2241000.0, 'new_value': 2561045.0}, {'field': 'order_count', 'old_value': 37123, 'new_value': 41872}]
2025-05-11 12:01:29,738 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-11 12:01:30,183 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-11 12:01:30,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25142.0, 'new_value': 26569.0}, {'field': 'total_amount', 'old_value': 25142.0, 'new_value': 26569.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 185}]
2025-05-11 12:01:30,183 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-11 12:01:30,695 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-11 12:01:30,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11277.0, 'new_value': 22277.0}, {'field': 'offline_amount', 'old_value': 186264.0, 'new_value': 207888.0}, {'field': 'total_amount', 'old_value': 197541.0, 'new_value': 230165.0}, {'field': 'order_count', 'old_value': 1554, 'new_value': 1805}]
2025-05-11 12:01:30,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-11 12:01:31,141 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-11 12:01:31,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47374.07, 'new_value': 55046.14}, {'field': 'total_amount', 'old_value': 47374.07, 'new_value': 55046.14}, {'field': 'order_count', 'old_value': 1368, 'new_value': 1575}]
2025-05-11 12:01:31,141 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-11 12:01:31,594 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-11 12:01:31,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94139.14, 'new_value': 109462.12}, {'field': 'total_amount', 'old_value': 144249.03, 'new_value': 159572.01}, {'field': 'order_count', 'old_value': 1523, 'new_value': 1754}]
2025-05-11 12:01:31,594 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-11 12:01:32,109 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-11 12:01:32,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15396.98, 'new_value': 18279.98}, {'field': 'total_amount', 'old_value': 15396.98, 'new_value': 18279.98}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-05-11 12:01:32,109 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-11 12:01:32,616 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-11 12:01:32,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51404.0, 'new_value': 56532.0}, {'field': 'total_amount', 'old_value': 51404.0, 'new_value': 56532.0}, {'field': 'order_count', 'old_value': 2042, 'new_value': 2171}]
2025-05-11 12:01:32,616 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-11 12:01:33,068 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-11 12:01:33,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11444.07, 'new_value': 12813.07}, {'field': 'total_amount', 'old_value': 11444.07, 'new_value': 12813.07}, {'field': 'order_count', 'old_value': 1151, 'new_value': 1279}]
2025-05-11 12:01:33,068 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-11 12:01:33,604 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-11 12:01:33,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76512.0, 'new_value': 85164.0}, {'field': 'total_amount', 'old_value': 76512.0, 'new_value': 85164.0}, {'field': 'order_count', 'old_value': 6376, 'new_value': 7097}]
2025-05-11 12:01:33,605 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-11 12:01:34,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-11 12:01:34,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-11 12:01:34,061 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-11 12:01:34,624 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-11 12:01:34,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31293.0, 'new_value': 35192.0}, {'field': 'total_amount', 'old_value': 31293.0, 'new_value': 35192.0}, {'field': 'order_count', 'old_value': 17599, 'new_value': 17600}]
2025-05-11 12:01:34,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-11 12:01:35,124 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-11 12:01:35,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4268400.0, 'new_value': 4349700.0}, {'field': 'total_amount', 'old_value': 4268400.0, 'new_value': 4349700.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-05-11 12:01:35,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-11 12:01:35,593 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-11 12:01:35,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22420.4, 'new_value': 23168.4}, {'field': 'total_amount', 'old_value': 22420.4, 'new_value': 23168.4}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-11 12:01:35,593 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-11 12:01:36,065 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-11 12:01:36,065 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 6924.0}, {'field': 'offline_amount', 'old_value': 18638.0, 'new_value': 18640.0}, {'field': 'total_amount', 'old_value': 18638.0, 'new_value': 25564.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-11 12:01:36,065 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-11 12:01:36,544 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-11 12:01:36,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21129.0, 'new_value': 23057.4}, {'field': 'total_amount', 'old_value': 21650.2, 'new_value': 23578.6}, {'field': 'order_count', 'old_value': 68, 'new_value': 74}]
2025-05-11 12:01:36,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-11 12:01:37,031 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-11 12:01:37,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16329.95, 'new_value': 18608.95}, {'field': 'total_amount', 'old_value': 16329.95, 'new_value': 18608.95}, {'field': 'order_count', 'old_value': 722, 'new_value': 817}]
2025-05-11 12:01:37,031 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-11 12:01:37,481 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-11 12:01:37,482 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53005.5, 'new_value': 62818.6}, {'field': 'total_amount', 'old_value': 97043.35, 'new_value': 106856.45}, {'field': 'order_count', 'old_value': 2428, 'new_value': 2673}]
2025-05-11 12:01:37,482 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-11 12:01:37,947 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-11 12:01:37,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103565.0, 'new_value': 116565.0}, {'field': 'total_amount', 'old_value': 103565.0, 'new_value': 116565.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 51}]
2025-05-11 12:01:37,947 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-11 12:01:38,419 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-11 12:01:38,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3888.0, 'new_value': 7776.0}, {'field': 'total_amount', 'old_value': 3888.0, 'new_value': 7776.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-11 12:01:38,419 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-11 12:01:38,857 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-11 12:01:38,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14145.23, 'new_value': 15778.02}, {'field': 'total_amount', 'old_value': 14145.23, 'new_value': 15778.02}, {'field': 'order_count', 'old_value': 56, 'new_value': 63}]
2025-05-11 12:01:38,857 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-11 12:01:39,365 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-11 12:01:39,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19416.58, 'new_value': 21593.72}, {'field': 'offline_amount', 'old_value': 12679.48, 'new_value': 14227.66}, {'field': 'total_amount', 'old_value': 32096.06, 'new_value': 35821.38}, {'field': 'order_count', 'old_value': 1791, 'new_value': 2000}]
2025-05-11 12:01:39,365 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-11 12:01:39,804 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-11 12:01:39,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24407.0, 'new_value': 29075.0}, {'field': 'total_amount', 'old_value': 24407.0, 'new_value': 29075.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-05-11 12:01:39,804 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-11 12:01:40,273 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-11 12:01:40,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44687.0, 'new_value': 50463.0}, {'field': 'total_amount', 'old_value': 53611.6, 'new_value': 59387.6}, {'field': 'order_count', 'old_value': 48, 'new_value': 54}]
2025-05-11 12:01:40,273 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-11 12:01:40,720 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-11 12:01:40,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41856.0, 'new_value': 43956.0}, {'field': 'total_amount', 'old_value': 41856.0, 'new_value': 43956.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-11 12:01:40,721 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-11 12:01:41,193 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-11 12:01:41,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6841.48, 'new_value': 9435.48}, {'field': 'total_amount', 'old_value': 6841.48, 'new_value': 9435.48}, {'field': 'order_count', 'old_value': 75, 'new_value': 85}]
2025-05-11 12:01:41,194 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-11 12:01:41,689 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-11 12:01:41,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157811.81, 'new_value': 183809.92}, {'field': 'total_amount', 'old_value': 157811.81, 'new_value': 183809.92}, {'field': 'order_count', 'old_value': 569, 'new_value': 657}]
2025-05-11 12:01:41,689 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-11 12:01:42,147 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-11 12:01:42,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104175.9, 'new_value': 118573.2}, {'field': 'total_amount', 'old_value': 104175.9, 'new_value': 118573.2}, {'field': 'order_count', 'old_value': 2802, 'new_value': 3162}]
2025-05-11 12:01:42,147 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-11 12:01:42,585 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-11 12:01:42,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14148.34, 'new_value': 15757.36}, {'field': 'total_amount', 'old_value': 14148.34, 'new_value': 15757.36}, {'field': 'order_count', 'old_value': 1783, 'new_value': 2005}]
2025-05-11 12:01:42,585 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-11 12:01:42,966 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-11 12:01:42,966 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8911.62, 'new_value': 9971.89}, {'field': 'offline_amount', 'old_value': 12770.6, 'new_value': 14744.4}, {'field': 'total_amount', 'old_value': 21682.22, 'new_value': 24716.29}, {'field': 'order_count', 'old_value': 943, 'new_value': 1076}]
2025-05-11 12:01:42,967 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-11 12:01:43,349 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-11 12:01:43,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16272.0, 'new_value': 21170.0}, {'field': 'total_amount', 'old_value': 21473.0, 'new_value': 26371.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 86}]
2025-05-11 12:01:43,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-11 12:01:43,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-11 12:01:43,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 555064.19, 'new_value': 612298.16}, {'field': 'offline_amount', 'old_value': 101214.3, 'new_value': 134066.3}, {'field': 'total_amount', 'old_value': 656278.49, 'new_value': 746364.46}, {'field': 'order_count', 'old_value': 2369, 'new_value': 2659}]
2025-05-11 12:01:43,797 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-11 12:01:44,278 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-11 12:01:44,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68551.0, 'new_value': 89899.0}, {'field': 'total_amount', 'old_value': 68551.0, 'new_value': 89899.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 24}]
2025-05-11 12:01:44,278 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-11 12:01:44,766 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-11 12:01:44,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3434.0, 'new_value': 3929.0}, {'field': 'total_amount', 'old_value': 3434.0, 'new_value': 3929.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-11 12:01:44,766 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-11 12:01:45,239 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-11 12:01:45,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37424.0, 'new_value': 53572.0}, {'field': 'total_amount', 'old_value': 37424.0, 'new_value': 53572.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-05-11 12:01:45,240 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-11 12:01:45,777 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-11 12:01:45,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7842.0, 'new_value': 8010.5}, {'field': 'offline_amount', 'old_value': 21260.9, 'new_value': 21567.9}, {'field': 'total_amount', 'old_value': 29102.9, 'new_value': 29578.4}, {'field': 'order_count', 'old_value': 71, 'new_value': 77}]
2025-05-11 12:01:45,778 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-11 12:01:46,253 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-11 12:01:46,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182807.12, 'new_value': 209524.12}, {'field': 'total_amount', 'old_value': 182807.12, 'new_value': 209524.12}, {'field': 'order_count', 'old_value': 916, 'new_value': 1057}]
2025-05-11 12:01:46,253 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-11 12:01:46,798 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-11 12:01:46,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117606.0, 'new_value': 117636.0}, {'field': 'total_amount', 'old_value': 117606.0, 'new_value': 117636.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-05-11 12:01:46,798 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-11 12:01:47,284 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-11 12:01:47,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5611.4, 'new_value': 5908.4}, {'field': 'offline_amount', 'old_value': 1340.0, 'new_value': 3964.9}, {'field': 'total_amount', 'old_value': 6951.4, 'new_value': 9873.3}, {'field': 'order_count', 'old_value': 42, 'new_value': 48}]
2025-05-11 12:01:47,285 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-11 12:01:47,817 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-11 12:01:47,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 406.56, 'new_value': 527.01}, {'field': 'offline_amount', 'old_value': 233744.06, 'new_value': 263468.16}, {'field': 'total_amount', 'old_value': 234150.62, 'new_value': 263995.17}, {'field': 'order_count', 'old_value': 537, 'new_value': 598}]
2025-05-11 12:01:47,818 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-11 12:01:48,238 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-11 12:01:48,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60250.0, 'new_value': 66510.0}, {'field': 'total_amount', 'old_value': 60250.0, 'new_value': 66510.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-11 12:01:48,239 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-11 12:01:48,661 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-11 12:01:48,661 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9707.69, 'new_value': 23288.9}, {'field': 'offline_amount', 'old_value': 19061.54, 'new_value': 19191.57}, {'field': 'total_amount', 'old_value': 28769.23, 'new_value': 42480.47}, {'field': 'order_count', 'old_value': 113, 'new_value': 137}]
2025-05-11 12:01:48,661 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-11 12:01:49,129 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-11 12:01:49,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63527.88, 'new_value': 72124.72}, {'field': 'total_amount', 'old_value': 63527.88, 'new_value': 72124.72}, {'field': 'order_count', 'old_value': 1615, 'new_value': 1838}]
2025-05-11 12:01:49,130 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-11 12:01:49,654 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-11 12:01:49,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133431.01, 'new_value': 154230.74}, {'field': 'total_amount', 'old_value': 133431.01, 'new_value': 154230.74}, {'field': 'order_count', 'old_value': 1116, 'new_value': 1292}]
2025-05-11 12:01:49,655 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-11 12:01:50,145 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-11 12:01:50,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43394.0, 'new_value': 48644.0}, {'field': 'total_amount', 'old_value': 43394.0, 'new_value': 48644.0}, {'field': 'order_count', 'old_value': 197, 'new_value': 224}]
2025-05-11 12:01:50,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-11 12:01:50,643 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-11 12:01:50,643 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141556.22, 'new_value': 158249.32}, {'field': 'offline_amount', 'old_value': 4998.0, 'new_value': 5154.0}, {'field': 'total_amount', 'old_value': 146554.22, 'new_value': 163403.32}, {'field': 'order_count', 'old_value': 1076, 'new_value': 1226}]
2025-05-11 12:01:50,643 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-11 12:01:51,098 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-11 12:01:51,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6346.0, 'new_value': 7891.0}, {'field': 'total_amount', 'old_value': 6346.0, 'new_value': 7891.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 40}]
2025-05-11 12:01:51,098 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-11 12:01:51,547 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-11 12:01:51,547 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6803.1, 'new_value': 8790.9}, {'field': 'total_amount', 'old_value': 6803.1, 'new_value': 8790.9}, {'field': 'order_count', 'old_value': 265, 'new_value': 303}]
2025-05-11 12:01:51,548 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-11 12:01:52,037 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-11 12:01:52,038 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5738.5, 'new_value': 6173.2}, {'field': 'total_amount', 'old_value': 6167.5, 'new_value': 6602.2}, {'field': 'order_count', 'old_value': 88, 'new_value': 95}]
2025-05-11 12:01:52,038 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-11 12:01:52,435 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-11 12:01:52,435 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3483.5, 'new_value': 4047.5}, {'field': 'offline_amount', 'old_value': 15222.7, 'new_value': 17038.6}, {'field': 'total_amount', 'old_value': 18706.2, 'new_value': 21086.1}, {'field': 'order_count', 'old_value': 201, 'new_value': 224}]
2025-05-11 12:01:52,436 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-11 12:01:52,958 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-11 12:01:52,959 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1498.0, 'new_value': 1719.0}, {'field': 'offline_amount', 'old_value': 11770.0, 'new_value': 12622.0}, {'field': 'total_amount', 'old_value': 13268.0, 'new_value': 14341.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 126}]
2025-05-11 12:01:52,959 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-11 12:01:53,399 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-11 12:01:53,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65827.56, 'new_value': 78593.71}, {'field': 'total_amount', 'old_value': 65827.56, 'new_value': 78593.71}, {'field': 'order_count', 'old_value': 2847, 'new_value': 3342}]
2025-05-11 12:01:53,399 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-11 12:01:53,853 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-11 12:01:53,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4584.0, 'new_value': 6585.0}, {'field': 'offline_amount', 'old_value': 11867.0, 'new_value': 13147.0}, {'field': 'total_amount', 'old_value': 16451.0, 'new_value': 19732.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-05-11 12:01:53,854 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-11 12:01:54,348 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-11 12:01:54,348 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31007.19, 'new_value': 34670.09}, {'field': 'offline_amount', 'old_value': 84179.69, 'new_value': 93403.09}, {'field': 'total_amount', 'old_value': 115186.88, 'new_value': 128073.18}, {'field': 'order_count', 'old_value': 5177, 'new_value': 5737}]
2025-05-11 12:01:54,349 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-11 12:01:54,881 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-11 12:01:54,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10993.63, 'new_value': 11913.43}, {'field': 'total_amount', 'old_value': 10993.63, 'new_value': 11913.43}, {'field': 'order_count', 'old_value': 405, 'new_value': 433}]
2025-05-11 12:01:54,882 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-11 12:01:55,340 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-11 12:01:55,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280683.58, 'new_value': 315532.56}, {'field': 'total_amount', 'old_value': 280683.58, 'new_value': 315532.56}, {'field': 'order_count', 'old_value': 2032, 'new_value': 2282}]
2025-05-11 12:01:55,340 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-11 12:01:55,830 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-11 12:01:55,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22874.06, 'new_value': 26112.89}, {'field': 'offline_amount', 'old_value': 178046.56, 'new_value': 202989.46}, {'field': 'total_amount', 'old_value': 200920.62, 'new_value': 229102.35}, {'field': 'order_count', 'old_value': 818, 'new_value': 942}]
2025-05-11 12:01:55,830 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-11 12:01:56,267 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-11 12:01:56,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 381049.0, 'new_value': 431365.0}, {'field': 'total_amount', 'old_value': 381049.0, 'new_value': 431365.0}, {'field': 'order_count', 'old_value': 1764, 'new_value': 1956}]
2025-05-11 12:01:56,268 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-11 12:01:56,779 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-11 12:01:56,779 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2367.0, 'new_value': 3432.0}, {'field': 'total_amount', 'old_value': 2367.0, 'new_value': 3432.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-11 12:01:56,779 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-11 12:01:57,187 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-11 12:01:57,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73459.5, 'new_value': 85132.5}, {'field': 'total_amount', 'old_value': 73459.5, 'new_value': 85132.5}, {'field': 'order_count', 'old_value': 401, 'new_value': 463}]
2025-05-11 12:01:57,188 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-11 12:01:57,681 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-11 12:01:57,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44477.0, 'new_value': 50129.0}, {'field': 'total_amount', 'old_value': 44477.0, 'new_value': 50129.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 282}]
2025-05-11 12:01:57,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-11 12:01:58,155 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-11 12:01:58,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11809.0, 'new_value': 12108.0}, {'field': 'total_amount', 'old_value': 11809.0, 'new_value': 12108.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-11 12:01:58,156 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-11 12:01:58,643 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-11 12:01:58,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69207.0, 'new_value': 73753.0}, {'field': 'total_amount', 'old_value': 69207.0, 'new_value': 73753.0}, {'field': 'order_count', 'old_value': 2218, 'new_value': 2361}]
2025-05-11 12:01:58,643 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-11 12:01:59,144 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-11 12:01:59,145 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30188.46, 'new_value': 30236.46}, {'field': 'offline_amount', 'old_value': 170680.62, 'new_value': 199273.6}, {'field': 'total_amount', 'old_value': 200869.08, 'new_value': 229510.06}, {'field': 'order_count', 'old_value': 1319, 'new_value': 1448}]
2025-05-11 12:01:59,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-11 12:01:59,663 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-11 12:01:59,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31678.21, 'new_value': 35665.42}, {'field': 'offline_amount', 'old_value': 41096.85, 'new_value': 45505.53}, {'field': 'total_amount', 'old_value': 72775.06, 'new_value': 81170.95}, {'field': 'order_count', 'old_value': 2958, 'new_value': 3314}]
2025-05-11 12:01:59,663 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-11 12:02:00,131 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-11 12:02:00,131 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19659.14, 'new_value': 40868.22}, {'field': 'total_amount', 'old_value': 146214.08, 'new_value': 167423.16}, {'field': 'order_count', 'old_value': 255, 'new_value': 287}]
2025-05-11 12:02:00,131 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-11 12:02:00,645 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-11 12:02:00,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23788.0, 'new_value': 27521.0}, {'field': 'total_amount', 'old_value': 23788.0, 'new_value': 27521.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 54}]
2025-05-11 12:02:00,645 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-11 12:02:01,084 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-11 12:02:01,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29466.0, 'new_value': 34046.0}, {'field': 'total_amount', 'old_value': 29466.0, 'new_value': 34046.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-11 12:02:01,084 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-11 12:02:01,570 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-11 12:02:01,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265367.58, 'new_value': 302782.58}, {'field': 'total_amount', 'old_value': 265367.58, 'new_value': 302782.58}, {'field': 'order_count', 'old_value': 1984, 'new_value': 2272}]
2025-05-11 12:02:01,571 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-11 12:02:02,091 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-11 12:02:02,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13714.0, 'new_value': 19254.0}, {'field': 'total_amount', 'old_value': 13714.0, 'new_value': 19254.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 26}]
2025-05-11 12:02:02,091 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-11 12:02:02,647 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-11 12:02:02,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21362.97, 'new_value': 24294.68}, {'field': 'offline_amount', 'old_value': 19606.29, 'new_value': 21820.59}, {'field': 'total_amount', 'old_value': 40969.26, 'new_value': 46115.27}, {'field': 'order_count', 'old_value': 811, 'new_value': 914}]
2025-05-11 12:02:02,647 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-11 12:02:03,143 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-11 12:02:03,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5656.0, 'new_value': 6000.0}, {'field': 'offline_amount', 'old_value': 98739.0, 'new_value': 113991.0}, {'field': 'total_amount', 'old_value': 104395.0, 'new_value': 119991.0}, {'field': 'order_count', 'old_value': 526, 'new_value': 600}]
2025-05-11 12:02:03,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-11 12:02:03,571 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-11 12:02:03,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39500.0, 'new_value': 64600.0}, {'field': 'total_amount', 'old_value': 39500.0, 'new_value': 64600.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-11 12:02:03,572 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-11 12:02:04,049 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-11 12:02:04,049 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-11 12:02:04,049 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-11 12:02:04,505 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-11 12:02:04,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19914.43, 'new_value': 21759.43}, {'field': 'total_amount', 'old_value': 19914.43, 'new_value': 21759.43}, {'field': 'order_count', 'old_value': 62, 'new_value': 67}]
2025-05-11 12:02:04,505 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-11 12:02:05,035 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-11 12:02:05,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117470.0, 'new_value': 132435.0}, {'field': 'total_amount', 'old_value': 131245.0, 'new_value': 146210.0}, {'field': 'order_count', 'old_value': 2666, 'new_value': 2951}]
2025-05-11 12:02:05,036 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-11 12:02:05,487 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-11 12:02:05,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115670.0, 'new_value': 124705.0}, {'field': 'total_amount', 'old_value': 119770.0, 'new_value': 128805.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 87}]
2025-05-11 12:02:05,487 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-11 12:02:06,031 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-11 12:02:06,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280054.85, 'new_value': 327364.51}, {'field': 'total_amount', 'old_value': 280054.85, 'new_value': 327364.51}, {'field': 'order_count', 'old_value': 3127, 'new_value': 3665}]
2025-05-11 12:02:06,032 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-11 12:02:06,522 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-11 12:02:06,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216140.4, 'new_value': 257273.7}, {'field': 'total_amount', 'old_value': 359873.8, 'new_value': 401007.1}, {'field': 'order_count', 'old_value': 2947, 'new_value': 2988}]
2025-05-11 12:02:06,522 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-11 12:02:07,053 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-11 12:02:07,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87758.0, 'new_value': 100693.0}, {'field': 'total_amount', 'old_value': 87758.0, 'new_value': 100693.0}, {'field': 'order_count', 'old_value': 1442, 'new_value': 1660}]
2025-05-11 12:02:07,054 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-11 12:02:07,523 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-11 12:02:07,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169744.0, 'new_value': 192714.0}, {'field': 'total_amount', 'old_value': 169744.0, 'new_value': 192714.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 186}]
2025-05-11 12:02:07,524 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-11 12:02:08,078 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-11 12:02:08,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106305.52, 'new_value': 119795.45}, {'field': 'offline_amount', 'old_value': 64098.55, 'new_value': 72800.67}, {'field': 'total_amount', 'old_value': 170404.07, 'new_value': 192596.12}, {'field': 'order_count', 'old_value': 1549, 'new_value': 1618}]
2025-05-11 12:02:08,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-11 12:02:08,551 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-11 12:02:08,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 921.8, 'new_value': 1528.8}, {'field': 'offline_amount', 'old_value': 43023.8, 'new_value': 47481.5}, {'field': 'total_amount', 'old_value': 43945.6, 'new_value': 49010.3}, {'field': 'order_count', 'old_value': 259, 'new_value': 292}]
2025-05-11 12:02:08,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-11 12:02:09,035 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-11 12:02:09,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26012.0, 'new_value': 30280.0}, {'field': 'total_amount', 'old_value': 26012.0, 'new_value': 30280.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 62}]
2025-05-11 12:02:09,035 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-11 12:02:09,497 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-11 12:02:09,497 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76552.94, 'new_value': 85799.94}, {'field': 'offline_amount', 'old_value': 6011.3, 'new_value': 6607.3}, {'field': 'total_amount', 'old_value': 82564.24, 'new_value': 92407.24}, {'field': 'order_count', 'old_value': 3201, 'new_value': 3813}]
2025-05-11 12:02:09,497 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-11 12:02:09,941 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-11 12:02:09,941 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30947.0, 'new_value': 34384.0}, {'field': 'offline_amount', 'old_value': 398953.0, 'new_value': 443716.0}, {'field': 'total_amount', 'old_value': 429900.0, 'new_value': 478100.0}, {'field': 'order_count', 'old_value': 9787, 'new_value': 10882}]
2025-05-11 12:02:09,941 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-11 12:02:11,958 - ERROR - 更新表单数据失败: Failed to parse the value as json format, Value: "<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body><script nonce="">
with(document)with(body){
        var script = createElement("script");
        script.setAttribute("exparams","category=&userid=&aplus&yunid=&asid=AQAAAAAhISBorEMQWgAAAACOYFXMmRHC1A==");
        script.setAttribute("id","tb-beacon-aplus");
        script.setAttribute("src",(navigator.userAgent.match(/iPhone|iPad|iPod|Android|AliApp|Yunos|cyclone/i)?"//d.alicdn.com/alilog/mlog/aplus/203219514":"//g.alicdn.com/alilog/mlog/aplus_v2")+".js");
        script.setAttribute("nonce","");
        script.setAttribute("cspx","");
        insertBefore(script, firstChild);
    }
</script>


<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by DingTalk/1.0.0<hr><center>tengine</center>

</body>

</html>

".
2025-05-11 12:02:11,958 - ERROR - 更新记录时发生错误: Failed to parse the value as json format, Value: "<!DOCTYPE HTML PUBLIC "-//IETF//DTD HTML 2.0//EN">

<html>

<head><title>504 Gateway Time-out</title></head>

<body><script nonce="">
with(document)with(body){
        var script = createElement("script");
        script.setAttribute("exparams","category=&userid=&aplus&yunid=&asid=AQAAAAAhISBorEMQWgAAAACOYFXMmRHC1A==");
        script.setAttribute("id","tb-beacon-aplus");
        script.setAttribute("src",(navigator.userAgent.match(/iPhone|iPad|iPod|Android|AliApp|Yunos|cyclone/i)?"//d.alicdn.com/alilog/mlog/aplus/203219514":"//g.alicdn.com/alilog/mlog/aplus_v2")+".js");
        script.setAttribute("nonce","");
        script.setAttribute("cspx","");
        insertBefore(script, firstChild);
    }
</script>


<center><h1>504 Gateway Time-out</h1></center>

<hr/>Powered by DingTalk/1.0.0<hr><center>tengine</center>

</body>

</html>

".
2025-05-11 12:02:11,958 - ERROR - 错误数据 - 键: 100098214_100098600_2025-05, 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-11 12:02:11,958 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-11 12:02:12,387 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-11 12:02:12,387 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14351.0, 'new_value': 14474.0}, {'field': 'offline_amount', 'old_value': 66197.0, 'new_value': 85077.0}, {'field': 'total_amount', 'old_value': 80548.0, 'new_value': 99551.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 103}]
2025-05-11 12:02:12,388 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-11 12:02:12,910 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-11 12:02:12,910 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7805.75, 'new_value': 8771.6}, {'field': 'offline_amount', 'old_value': 7263.5, 'new_value': 7895.5}, {'field': 'total_amount', 'old_value': 15069.25, 'new_value': 16667.1}, {'field': 'order_count', 'old_value': 695, 'new_value': 763}]
2025-05-11 12:02:12,911 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-11 12:02:14,381 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-11 12:02:14,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15224.0, 'new_value': 18047.0}, {'field': 'total_amount', 'old_value': 15224.0, 'new_value': 18047.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 90}]
2025-05-11 12:02:14,381 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-11 12:02:14,867 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-11 12:02:14,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8112.9, 'new_value': 8672.6}, {'field': 'offline_amount', 'old_value': 23537.1, 'new_value': 24845.1}, {'field': 'total_amount', 'old_value': 31650.0, 'new_value': 33517.7}, {'field': 'order_count', 'old_value': 360, 'new_value': 389}]
2025-05-11 12:02:14,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-11 12:02:15,351 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-11 12:02:15,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35765.35, 'new_value': 41021.37}, {'field': 'offline_amount', 'old_value': 79171.85, 'new_value': 90426.75}, {'field': 'total_amount', 'old_value': 114937.2, 'new_value': 131448.12}, {'field': 'order_count', 'old_value': 3563, 'new_value': 4035}]
2025-05-11 12:02:15,351 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-11 12:02:15,803 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-11 12:02:15,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35392.0, 'new_value': 39732.0}, {'field': 'total_amount', 'old_value': 35392.0, 'new_value': 39732.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 171}]
2025-05-11 12:02:15,804 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-11 12:02:16,298 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-11 12:02:16,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92012.59, 'new_value': 107714.32}, {'field': 'total_amount', 'old_value': 114499.99, 'new_value': 130201.72}, {'field': 'order_count', 'old_value': 699, 'new_value': 793}]
2025-05-11 12:02:16,298 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-11 12:02:16,796 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-11 12:02:16,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64289.0, 'new_value': 73587.7}, {'field': 'total_amount', 'old_value': 64289.0, 'new_value': 73587.7}, {'field': 'order_count', 'old_value': 295, 'new_value': 340}]
2025-05-11 12:02:16,797 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-11 12:02:17,229 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-11 12:02:17,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76644.6, 'new_value': 83368.9}, {'field': 'total_amount', 'old_value': 76644.6, 'new_value': 83368.9}, {'field': 'order_count', 'old_value': 2099, 'new_value': 2292}]
2025-05-11 12:02:17,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-11 12:02:17,801 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-11 12:02:17,801 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2488.0, 'new_value': 3748.0}, {'field': 'total_amount', 'old_value': 8960.0, 'new_value': 10220.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 50}]
2025-05-11 12:02:17,801 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-11 12:02:18,278 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-11 12:02:18,279 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58406.64, 'new_value': 67138.84}, {'field': 'offline_amount', 'old_value': 117743.24, 'new_value': 134487.69}, {'field': 'total_amount', 'old_value': 176149.88, 'new_value': 201626.53}, {'field': 'order_count', 'old_value': 1378, 'new_value': 1568}]
2025-05-11 12:02:18,279 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-11 12:02:18,779 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-11 12:02:18,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46131.3, 'new_value': 51010.8}, {'field': 'total_amount', 'old_value': 46131.3, 'new_value': 51010.8}, {'field': 'order_count', 'old_value': 208, 'new_value': 234}]
2025-05-11 12:02:18,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-11 12:02:19,236 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-11 12:02:19,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32134.0, 'new_value': 39994.0}, {'field': 'total_amount', 'old_value': 32134.0, 'new_value': 39994.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-11 12:02:19,236 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-11 12:02:19,758 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-11 12:02:19,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40260.87, 'new_value': 43579.41}, {'field': 'total_amount', 'old_value': 41424.63, 'new_value': 44743.17}, {'field': 'order_count', 'old_value': 189, 'new_value': 205}]
2025-05-11 12:02:19,759 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-11 12:02:20,267 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-11 12:02:20,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262000.0, 'new_value': 291281.0}, {'field': 'total_amount', 'old_value': 262000.0, 'new_value': 291281.0}, {'field': 'order_count', 'old_value': 164, 'new_value': 178}]
2025-05-11 12:02:20,267 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-11 12:02:20,712 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-11 12:02:20,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11400.0, 'new_value': 20400.0}, {'field': 'total_amount', 'old_value': 11400.0, 'new_value': 20400.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-11 12:02:20,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-11 12:02:21,203 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-11 12:02:21,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317077.35, 'new_value': 365549.85}, {'field': 'total_amount', 'old_value': 317077.35, 'new_value': 365549.85}, {'field': 'order_count', 'old_value': 2380, 'new_value': 2719}]
2025-05-11 12:02:21,204 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-11 12:02:21,699 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-11 12:02:21,699 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5841.3, 'new_value': 7218.1}, {'field': 'offline_amount', 'old_value': 62995.6, 'new_value': 72608.1}, {'field': 'total_amount', 'old_value': 68836.9, 'new_value': 79826.2}, {'field': 'order_count', 'old_value': 2136, 'new_value': 2541}]
2025-05-11 12:02:21,699 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-11 12:02:22,134 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-11 12:02:22,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3826.0, 'new_value': 3949.0}, {'field': 'offline_amount', 'old_value': 2199.0, 'new_value': 2359.0}, {'field': 'total_amount', 'old_value': 6025.0, 'new_value': 6308.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-05-11 12:02:22,134 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-11 12:02:22,603 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-11 12:02:22,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21623.21, 'new_value': 23508.82}, {'field': 'offline_amount', 'old_value': 16070.19, 'new_value': 17636.75}, {'field': 'total_amount', 'old_value': 37693.4, 'new_value': 41145.57}, {'field': 'order_count', 'old_value': 2034, 'new_value': 2215}]
2025-05-11 12:02:22,604 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-11 12:02:23,043 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-11 12:02:23,043 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35549.22, 'new_value': 38330.71}, {'field': 'offline_amount', 'old_value': 38651.19, 'new_value': 43598.15}, {'field': 'total_amount', 'old_value': 74200.41, 'new_value': 81928.86}, {'field': 'order_count', 'old_value': 1899, 'new_value': 2087}]
2025-05-11 12:02:23,043 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-11 12:02:23,511 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-11 12:02:23,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 800.0, 'new_value': 11260.0}, {'field': 'total_amount', 'old_value': 800.0, 'new_value': 11260.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-11 12:02:23,512 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-11 12:02:24,006 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-11 12:02:24,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353254.0, 'new_value': 391846.0}, {'field': 'total_amount', 'old_value': 353254.0, 'new_value': 391846.0}, {'field': 'order_count', 'old_value': 422, 'new_value': 468}]
2025-05-11 12:02:24,006 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-11 12:02:24,424 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-11 12:02:24,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93165.0, 'new_value': 99769.0}, {'field': 'total_amount', 'old_value': 99115.3, 'new_value': 105719.3}, {'field': 'order_count', 'old_value': 196, 'new_value': 205}]
2025-05-11 12:02:24,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-11 12:02:24,823 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-11 12:02:24,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41638.0, 'new_value': 57014.0}, {'field': 'total_amount', 'old_value': 41638.0, 'new_value': 57014.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 36}]
2025-05-11 12:02:24,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-11 12:02:25,278 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-11 12:02:25,278 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16696.1, 'new_value': 18205.9}, {'field': 'offline_amount', 'old_value': 43647.0, 'new_value': 50388.0}, {'field': 'total_amount', 'old_value': 60343.1, 'new_value': 68593.9}, {'field': 'order_count', 'old_value': 646, 'new_value': 714}]
2025-05-11 12:02:25,278 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-11 12:02:25,723 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-11 12:02:25,723 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49744.0, 'new_value': 56318.0}, {'field': 'offline_amount', 'old_value': 36527.0, 'new_value': 42050.0}, {'field': 'total_amount', 'old_value': 86271.0, 'new_value': 98368.0}, {'field': 'order_count', 'old_value': 1056, 'new_value': 1207}]
2025-05-11 12:02:25,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-11 12:02:26,266 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-11 12:02:26,267 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4315.4, 'new_value': 4929.4}, {'field': 'offline_amount', 'old_value': 7402.77, 'new_value': 9434.47}, {'field': 'total_amount', 'old_value': 11718.17, 'new_value': 14363.87}, {'field': 'order_count', 'old_value': 135, 'new_value': 150}]
2025-05-11 12:02:26,267 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-11 12:02:26,744 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-11 12:02:26,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4616.66, 'new_value': 4874.68}, {'field': 'offline_amount', 'old_value': 44677.0, 'new_value': 56226.0}, {'field': 'total_amount', 'old_value': 49293.66, 'new_value': 61100.68}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-05-11 12:02:26,744 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-11 12:02:27,244 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-11 12:02:27,245 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33074.1, 'new_value': 38516.64}, {'field': 'total_amount', 'old_value': 33074.1, 'new_value': 38516.64}, {'field': 'order_count', 'old_value': 185, 'new_value': 219}]
2025-05-11 12:02:27,245 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-11 12:02:27,732 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-11 12:02:27,732 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11324.62, 'new_value': 12429.62}, {'field': 'offline_amount', 'old_value': 13493.0, 'new_value': 17167.0}, {'field': 'total_amount', 'old_value': 24817.62, 'new_value': 29596.62}, {'field': 'order_count', 'old_value': 94, 'new_value': 112}]
2025-05-11 12:02:27,732 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-11 12:02:28,216 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-11 12:02:28,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84847.5, 'new_value': 94858.5}, {'field': 'total_amount', 'old_value': 84847.5, 'new_value': 94858.5}, {'field': 'order_count', 'old_value': 430, 'new_value': 482}]
2025-05-11 12:02:28,217 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-11 12:02:28,661 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-11 12:02:28,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18859.57, 'new_value': 20781.58}, {'field': 'total_amount', 'old_value': 21947.57, 'new_value': 23869.58}, {'field': 'order_count', 'old_value': 195, 'new_value': 220}]
2025-05-11 12:02:28,661 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-11 12:02:29,192 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-11 12:02:29,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60862.14, 'new_value': 69010.04}, {'field': 'total_amount', 'old_value': 60862.14, 'new_value': 69010.04}, {'field': 'order_count', 'old_value': 228, 'new_value': 266}]
2025-05-11 12:02:29,192 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-11 12:02:29,647 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-11 12:02:29,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5607.0, 'new_value': 7804.0}, {'field': 'total_amount', 'old_value': 5607.0, 'new_value': 7804.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-11 12:02:29,647 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-11 12:02:30,094 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-11 12:02:30,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28983.0, 'new_value': 33244.0}, {'field': 'offline_amount', 'old_value': 100733.0, 'new_value': 109176.0}, {'field': 'total_amount', 'old_value': 129716.0, 'new_value': 142420.0}, {'field': 'order_count', 'old_value': 574, 'new_value': 639}]
2025-05-11 12:02:30,094 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-11 12:02:30,548 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-11 12:02:30,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 503139.0, 'new_value': 546523.0}, {'field': 'total_amount', 'old_value': 503139.0, 'new_value': 546523.0}, {'field': 'order_count', 'old_value': 2106, 'new_value': 2324}]
2025-05-11 12:02:30,548 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-11 12:02:31,120 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-11 12:02:31,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6457026.0, 'new_value': 7024300.0}, {'field': 'total_amount', 'old_value': 6457026.0, 'new_value': 7024300.0}, {'field': 'order_count', 'old_value': 18681, 'new_value': 20454}]
2025-05-11 12:02:31,120 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-11 12:02:31,544 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-11 12:02:31,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1481700.59, 'new_value': 1682001.71}, {'field': 'total_amount', 'old_value': 1481700.59, 'new_value': 1682001.71}, {'field': 'order_count', 'old_value': 2543, 'new_value': 2852}]
2025-05-11 12:02:31,545 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-11 12:02:32,006 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-11 12:02:32,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60478.17, 'new_value': 67894.42}, {'field': 'total_amount', 'old_value': 60478.17, 'new_value': 67894.42}, {'field': 'order_count', 'old_value': 4149, 'new_value': 4630}]
2025-05-11 12:02:32,006 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-11 12:02:32,554 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-11 12:02:32,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111327.0, 'new_value': 123484.0}, {'field': 'total_amount', 'old_value': 111327.0, 'new_value': 123484.0}, {'field': 'order_count', 'old_value': 2193, 'new_value': 2469}]
2025-05-11 12:02:32,555 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-11 12:02:33,138 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-11 12:02:33,138 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106097.0, 'new_value': 121447.0}, {'field': 'total_amount', 'old_value': 106097.0, 'new_value': 121447.0}, {'field': 'order_count', 'old_value': 206, 'new_value': 241}]
2025-05-11 12:02:33,138 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-11 12:02:33,574 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-11 12:02:33,575 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109560.46, 'new_value': 122603.79}, {'field': 'offline_amount', 'old_value': 90097.98, 'new_value': 98188.53}, {'field': 'total_amount', 'old_value': 199658.44, 'new_value': 220792.32}, {'field': 'order_count', 'old_value': 7631, 'new_value': 8583}]
2025-05-11 12:02:33,575 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-11 12:02:34,008 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-11 12:02:34,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17520.0, 'new_value': 22100.0}, {'field': 'total_amount', 'old_value': 17520.0, 'new_value': 22100.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-11 12:02:34,008 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-11 12:02:34,444 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-11 12:02:34,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48879.7, 'new_value': 67900.7}, {'field': 'total_amount', 'old_value': 126422.11, 'new_value': 145443.11}]
2025-05-11 12:02:34,444 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-11 12:02:34,956 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-11 12:02:34,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168007.6, 'new_value': 192966.3}, {'field': 'total_amount', 'old_value': 168007.6, 'new_value': 192966.3}, {'field': 'order_count', 'old_value': 3640, 'new_value': 4168}]
2025-05-11 12:02:34,956 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-11 12:02:35,422 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-11 12:02:35,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18784.1, 'new_value': 20662.5}, {'field': 'total_amount', 'old_value': 18784.1, 'new_value': 20662.5}, {'field': 'order_count', 'old_value': 105, 'new_value': 115}]
2025-05-11 12:02:35,422 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-11 12:02:35,948 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-11 12:02:35,948 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64789.0, 'new_value': 77587.0}, {'field': 'offline_amount', 'old_value': 48076.0, 'new_value': 57682.0}, {'field': 'total_amount', 'old_value': 112865.0, 'new_value': 135269.0}, {'field': 'order_count', 'old_value': 362, 'new_value': 432}]
2025-05-11 12:02:35,948 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-11 12:02:36,439 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-11 12:02:36,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46490.0, 'new_value': 51182.0}, {'field': 'total_amount', 'old_value': 46490.0, 'new_value': 51182.0}, {'field': 'order_count', 'old_value': 3386, 'new_value': 3733}]
2025-05-11 12:02:36,440 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-11 12:02:36,838 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-11 12:02:36,838 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18664.0, 'new_value': 20888.0}, {'field': 'total_amount', 'old_value': 18664.0, 'new_value': 20888.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-05-11 12:02:36,838 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-11 12:02:37,251 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-11 12:02:37,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35159.2, 'new_value': 42261.2}, {'field': 'total_amount', 'old_value': 35272.2, 'new_value': 42374.2}, {'field': 'order_count', 'old_value': 565, 'new_value': 653}]
2025-05-11 12:02:37,251 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-11 12:02:37,889 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-11 12:02:37,889 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6745.5, 'new_value': 7251.5}, {'field': 'offline_amount', 'old_value': 22296.0, 'new_value': 24925.1}, {'field': 'total_amount', 'old_value': 29041.5, 'new_value': 32176.6}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1212}]
2025-05-11 12:02:37,889 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-11 12:02:38,325 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-11 12:02:38,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2980.0, 'new_value': 12880.0}, {'field': 'total_amount', 'old_value': 2980.0, 'new_value': 12880.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-11 12:02:38,325 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-11 12:02:38,836 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-11 12:02:38,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1768015.39, 'new_value': 2004924.39}, {'field': 'total_amount', 'old_value': 1768015.39, 'new_value': 2004924.39}, {'field': 'order_count', 'old_value': 37960, 'new_value': 42753}]
2025-05-11 12:02:38,836 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-11 12:02:39,312 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-11 12:02:39,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12204.96, 'new_value': 13100.29}, {'field': 'total_amount', 'old_value': 12204.96, 'new_value': 13100.29}, {'field': 'order_count', 'old_value': 45, 'new_value': 50}]
2025-05-11 12:02:39,313 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-11 12:02:39,806 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-11 12:02:39,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219494.03, 'new_value': 244285.47}, {'field': 'total_amount', 'old_value': 219494.03, 'new_value': 244285.47}, {'field': 'order_count', 'old_value': 1328, 'new_value': 1453}]
2025-05-11 12:02:39,806 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-11 12:02:40,372 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-11 12:02:40,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60256.66, 'new_value': 68956.21}, {'field': 'total_amount', 'old_value': 60256.66, 'new_value': 68956.21}, {'field': 'order_count', 'old_value': 1269, 'new_value': 1423}]
2025-05-11 12:02:40,373 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-11 12:02:40,857 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-11 12:02:40,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117444.0, 'new_value': 127444.0}, {'field': 'total_amount', 'old_value': 117444.0, 'new_value': 127444.0}, {'field': 'order_count', 'old_value': 2470, 'new_value': 2700}]
2025-05-11 12:02:40,858 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-11 12:02:41,327 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-11 12:02:41,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100851.0, 'new_value': 132011.0}, {'field': 'total_amount', 'old_value': 100851.0, 'new_value': 132011.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 33}]
2025-05-11 12:02:41,328 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-11 12:02:41,794 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-11 12:02:41,794 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52726.92, 'new_value': 58005.0}, {'field': 'total_amount', 'old_value': 52726.92, 'new_value': 58005.0}, {'field': 'order_count', 'old_value': 1312, 'new_value': 1447}]
2025-05-11 12:02:41,794 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-11 12:02:42,397 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-11 12:02:42,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11072.0, 'new_value': 11816.0}, {'field': 'total_amount', 'old_value': 11072.0, 'new_value': 11816.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 44}]
2025-05-11 12:02:42,397 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-11 12:02:42,898 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-11 12:02:42,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32485.96, 'new_value': 39149.35}, {'field': 'offline_amount', 'old_value': 173016.6, 'new_value': 201980.3}, {'field': 'total_amount', 'old_value': 205502.56, 'new_value': 241129.65}, {'field': 'order_count', 'old_value': 1304, 'new_value': 1513}]
2025-05-11 12:02:42,899 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-11 12:02:43,413 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-11 12:02:43,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13970.31, 'new_value': 17797.47}, {'field': 'total_amount', 'old_value': 32847.78, 'new_value': 36674.94}, {'field': 'order_count', 'old_value': 2098, 'new_value': 2331}]
2025-05-11 12:02:43,414 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-11 12:02:43,933 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-11 12:02:43,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21004.01, 'new_value': 27558.29}, {'field': 'total_amount', 'old_value': 51412.15, 'new_value': 57966.43}, {'field': 'order_count', 'old_value': 3251, 'new_value': 3704}]
2025-05-11 12:02:43,934 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-11 12:02:44,404 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-11 12:02:44,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 435441.47, 'new_value': 489195.27}, {'field': 'total_amount', 'old_value': 435441.47, 'new_value': 489195.27}, {'field': 'order_count', 'old_value': 1324, 'new_value': 1476}]
2025-05-11 12:02:44,404 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-11 12:02:44,885 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-11 12:02:44,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57468.0, 'new_value': 62713.0}, {'field': 'total_amount', 'old_value': 57468.0, 'new_value': 62713.0}, {'field': 'order_count', 'old_value': 2066, 'new_value': 2269}]
2025-05-11 12:02:44,885 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-11 12:02:45,330 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-11 12:02:45,330 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-11 12:02:45,330 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-11 12:02:45,756 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-11 12:02:45,756 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263733.17, 'new_value': 296763.62}, {'field': 'total_amount', 'old_value': 263733.17, 'new_value': 296763.62}, {'field': 'order_count', 'old_value': 1222, 'new_value': 1371}]
2025-05-11 12:02:45,756 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-11 12:02:46,196 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-11 12:02:46,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290281.91, 'new_value': 322727.51}, {'field': 'total_amount', 'old_value': 290281.91, 'new_value': 322727.51}, {'field': 'order_count', 'old_value': 944, 'new_value': 1026}]
2025-05-11 12:02:46,196 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-11 12:02:46,711 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-11 12:02:46,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170295.0, 'new_value': 175222.0}, {'field': 'total_amount', 'old_value': 171291.0, 'new_value': 176218.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-05-11 12:02:46,711 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-11 12:02:47,120 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-11 12:02:47,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124304.8, 'new_value': 142654.77}, {'field': 'total_amount', 'old_value': 124304.8, 'new_value': 142654.77}, {'field': 'order_count', 'old_value': 342, 'new_value': 397}]
2025-05-11 12:02:47,121 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-11 12:02:47,569 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-11 12:02:47,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34865.0, 'new_value': 47500.0}, {'field': 'total_amount', 'old_value': 34865.0, 'new_value': 47500.0}, {'field': 'order_count', 'old_value': 155, 'new_value': 223}]
2025-05-11 12:02:47,569 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-11 12:02:47,983 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-11 12:02:47,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47759.0, 'new_value': 62346.0}, {'field': 'total_amount', 'old_value': 47759.0, 'new_value': 62346.0}, {'field': 'order_count', 'old_value': 216, 'new_value': 248}]
2025-05-11 12:02:47,983 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-11 12:02:48,421 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-11 12:02:48,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60585.0, 'new_value': 74185.0}, {'field': 'total_amount', 'old_value': 60585.0, 'new_value': 74185.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 178}]
2025-05-11 12:02:48,421 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-11 12:02:48,947 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-11 12:02:48,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3088.0, 'new_value': 8288.0}, {'field': 'total_amount', 'old_value': 3088.0, 'new_value': 8288.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-11 12:02:48,947 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-11 12:02:49,409 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-11 12:02:49,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16588.0, 'new_value': 18755.0}, {'field': 'total_amount', 'old_value': 16588.0, 'new_value': 18755.0}, {'field': 'order_count', 'old_value': 315, 'new_value': 357}]
2025-05-11 12:02:49,409 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-11 12:02:49,859 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-11 12:02:49,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60702.0, 'new_value': 65520.0}, {'field': 'total_amount', 'old_value': 60702.0, 'new_value': 65520.0}, {'field': 'order_count', 'old_value': 6362, 'new_value': 6778}]
2025-05-11 12:02:49,860 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-11 12:02:50,329 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-11 12:02:50,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56244.0, 'new_value': 58620.0}, {'field': 'total_amount', 'old_value': 56244.0, 'new_value': 58620.0}, {'field': 'order_count', 'old_value': 449, 'new_value': 493}]
2025-05-11 12:02:50,329 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-11 12:02:50,817 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-11 12:02:50,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20160.0, 'new_value': 22060.0}, {'field': 'total_amount', 'old_value': 20160.0, 'new_value': 22060.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-11 12:02:50,817 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-11 12:02:51,311 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-11 12:02:51,311 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9963.0, 'new_value': 10062.0}, {'field': 'offline_amount', 'old_value': 22119.5, 'new_value': 32598.0}, {'field': 'total_amount', 'old_value': 32082.5, 'new_value': 42660.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 58}]
2025-05-11 12:02:51,311 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-11 12:02:51,690 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-11 12:02:51,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21580.01, 'new_value': 23428.61}, {'field': 'total_amount', 'old_value': 21580.01, 'new_value': 23428.61}, {'field': 'order_count', 'old_value': 332, 'new_value': 356}]
2025-05-11 12:02:51,691 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-11 12:02:52,162 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-11 12:02:52,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52196.33, 'new_value': 59215.29}, {'field': 'offline_amount', 'old_value': 236993.96, 'new_value': 268408.94}, {'field': 'total_amount', 'old_value': 289190.29, 'new_value': 327624.23}, {'field': 'order_count', 'old_value': 667, 'new_value': 753}]
2025-05-11 12:02:52,162 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-11 12:02:52,610 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-11 12:02:52,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24396.6, 'new_value': 29787.6}, {'field': 'total_amount', 'old_value': 24396.6, 'new_value': 29787.6}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-05-11 12:02:52,610 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-11 12:02:53,042 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-11 12:02:53,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28578.59, 'new_value': 32614.16}, {'field': 'offline_amount', 'old_value': 264208.03, 'new_value': 303349.99}, {'field': 'total_amount', 'old_value': 290912.29, 'new_value': 334089.82}, {'field': 'order_count', 'old_value': 1477, 'new_value': 1657}]
2025-05-11 12:02:53,042 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-11 12:02:53,522 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-11 12:02:53,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27578.0, 'new_value': 36698.0}, {'field': 'total_amount', 'old_value': 27578.0, 'new_value': 36698.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 120}]
2025-05-11 12:02:53,522 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-11 12:02:53,975 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-11 12:02:53,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1389.97, 'new_value': 1458.95}, {'field': 'total_amount', 'old_value': 1389.97, 'new_value': 1458.95}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-11 12:02:53,976 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-11 12:02:54,473 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-11 12:02:54,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21223.0, 'new_value': 21731.0}, {'field': 'total_amount', 'old_value': 26541.0, 'new_value': 27049.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-11 12:02:54,473 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-11 12:02:54,929 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-11 12:02:54,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6111444.44, 'new_value': 7441740.16}, {'field': 'total_amount', 'old_value': 6111444.44, 'new_value': 7441740.16}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-11 12:02:54,929 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-11 12:02:55,381 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-11 12:02:55,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65737.54, 'new_value': 75702.46}, {'field': 'total_amount', 'old_value': 65737.54, 'new_value': 75702.46}, {'field': 'order_count', 'old_value': 6817, 'new_value': 7651}]
2025-05-11 12:02:55,381 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-11 12:02:55,895 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-11 12:02:55,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9142.0, 'new_value': 10196.0}, {'field': 'total_amount', 'old_value': 9142.0, 'new_value': 10196.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 60}]
2025-05-11 12:02:55,896 - INFO - 开始批量插入 3 条新记录
2025-05-11 12:02:56,068 - INFO - 批量插入响应状态码: 200
2025-05-11 12:02:56,069 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 11 May 2025 04:02:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B4551EE2-BA11-7AE2-8F65-79B75555775B', 'x-acs-trace-id': '3c6356f8f81004de9700ee177b935de2', 'etag': '10WFmnlTJyXEXMOIMm7xjqg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-11 12:02:56,069 - INFO - 批量插入响应体: {'result': ['FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2', 'FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2', 'FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMM2']}
2025-05-11 12:02:56,069 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-11 12:02:56,069 - INFO - 成功插入的数据ID: ['FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2', 'FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2', 'FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMM2']
2025-05-11 12:02:59,070 - INFO - 批量插入完成，共 3 条记录
2025-05-11 12:02:59,070 - INFO - 日期 2025-05 处理完成 - 更新: 303 条，插入: 3 条，错误: 1 条
2025-05-11 12:02:59,070 - INFO - 数据同步完成！更新: 303 条，插入: 3 条，错误: 1 条
2025-05-11 12:02:59,072 - INFO - =================同步完成====================
2025-05-11 15:00:01,856 - INFO - =================使用默认全量同步=============
2025-05-11 15:00:03,184 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-11 15:00:03,185 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 15:00:03,213 - INFO - 开始处理日期: 2025-01
2025-05-11 15:00:03,216 - INFO - Request Parameters - Page 1:
2025-05-11 15:00:03,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:03,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:04,373 - INFO - Response - Page 1:
2025-05-11 15:00:04,573 - INFO - 第 1 页获取到 100 条记录
2025-05-11 15:00:04,573 - INFO - Request Parameters - Page 2:
2025-05-11 15:00:04,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:04,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:05,370 - INFO - Response - Page 2:
2025-05-11 15:00:05,571 - INFO - 第 2 页获取到 100 条记录
2025-05-11 15:00:05,571 - INFO - Request Parameters - Page 3:
2025-05-11 15:00:05,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:05,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:06,065 - INFO - Response - Page 3:
2025-05-11 15:00:06,265 - INFO - 第 3 页获取到 100 条记录
2025-05-11 15:00:06,265 - INFO - Request Parameters - Page 4:
2025-05-11 15:00:06,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:06,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:06,967 - INFO - Response - Page 4:
2025-05-11 15:00:07,167 - INFO - 第 4 页获取到 100 条记录
2025-05-11 15:00:07,167 - INFO - Request Parameters - Page 5:
2025-05-11 15:00:07,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:07,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:07,670 - INFO - Response - Page 5:
2025-05-11 15:00:07,871 - INFO - 第 5 页获取到 100 条记录
2025-05-11 15:00:07,871 - INFO - Request Parameters - Page 6:
2025-05-11 15:00:07,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:07,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:08,345 - INFO - Response - Page 6:
2025-05-11 15:00:08,545 - INFO - 第 6 页获取到 100 条记录
2025-05-11 15:00:08,545 - INFO - Request Parameters - Page 7:
2025-05-11 15:00:08,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:08,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:09,053 - INFO - Response - Page 7:
2025-05-11 15:00:09,253 - INFO - 第 7 页获取到 82 条记录
2025-05-11 15:00:09,253 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 15:00:09,253 - INFO - 获取到 682 条表单数据
2025-05-11 15:00:09,265 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 15:00:09,276 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 15:00:09,276 - INFO - 开始处理日期: 2025-02
2025-05-11 15:00:09,276 - INFO - Request Parameters - Page 1:
2025-05-11 15:00:09,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:09,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:09,764 - INFO - Response - Page 1:
2025-05-11 15:00:09,964 - INFO - 第 1 页获取到 100 条记录
2025-05-11 15:00:09,964 - INFO - Request Parameters - Page 2:
2025-05-11 15:00:09,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:09,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:10,419 - INFO - Response - Page 2:
2025-05-11 15:00:10,619 - INFO - 第 2 页获取到 100 条记录
2025-05-11 15:00:10,619 - INFO - Request Parameters - Page 3:
2025-05-11 15:00:10,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:10,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:11,151 - INFO - Response - Page 3:
2025-05-11 15:00:11,351 - INFO - 第 3 页获取到 100 条记录
2025-05-11 15:00:11,351 - INFO - Request Parameters - Page 4:
2025-05-11 15:00:11,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:11,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:11,851 - INFO - Response - Page 4:
2025-05-11 15:00:12,051 - INFO - 第 4 页获取到 100 条记录
2025-05-11 15:00:12,051 - INFO - Request Parameters - Page 5:
2025-05-11 15:00:12,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:12,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:12,507 - INFO - Response - Page 5:
2025-05-11 15:00:12,707 - INFO - 第 5 页获取到 100 条记录
2025-05-11 15:00:12,707 - INFO - Request Parameters - Page 6:
2025-05-11 15:00:12,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:12,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:13,196 - INFO - Response - Page 6:
2025-05-11 15:00:13,398 - INFO - 第 6 页获取到 100 条记录
2025-05-11 15:00:13,398 - INFO - Request Parameters - Page 7:
2025-05-11 15:00:13,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:13,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:13,843 - INFO - Response - Page 7:
2025-05-11 15:00:14,043 - INFO - 第 7 页获取到 70 条记录
2025-05-11 15:00:14,043 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 15:00:14,043 - INFO - 获取到 670 条表单数据
2025-05-11 15:00:14,055 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 15:00:14,067 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 15:00:14,067 - INFO - 开始处理日期: 2025-03
2025-05-11 15:00:14,067 - INFO - Request Parameters - Page 1:
2025-05-11 15:00:14,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:14,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:14,574 - INFO - Response - Page 1:
2025-05-11 15:00:14,775 - INFO - 第 1 页获取到 100 条记录
2025-05-11 15:00:14,775 - INFO - Request Parameters - Page 2:
2025-05-11 15:00:14,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:14,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:15,415 - INFO - Response - Page 2:
2025-05-11 15:00:15,615 - INFO - 第 2 页获取到 100 条记录
2025-05-11 15:00:15,615 - INFO - Request Parameters - Page 3:
2025-05-11 15:00:15,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:15,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:16,043 - INFO - Response - Page 3:
2025-05-11 15:00:16,244 - INFO - 第 3 页获取到 100 条记录
2025-05-11 15:00:16,244 - INFO - Request Parameters - Page 4:
2025-05-11 15:00:16,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:16,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:16,972 - INFO - Response - Page 4:
2025-05-11 15:00:17,172 - INFO - 第 4 页获取到 100 条记录
2025-05-11 15:00:17,172 - INFO - Request Parameters - Page 5:
2025-05-11 15:00:17,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:17,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:17,610 - INFO - Response - Page 5:
2025-05-11 15:00:17,810 - INFO - 第 5 页获取到 100 条记录
2025-05-11 15:00:17,810 - INFO - Request Parameters - Page 6:
2025-05-11 15:00:17,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:17,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:18,328 - INFO - Response - Page 6:
2025-05-11 15:00:18,529 - INFO - 第 6 页获取到 100 条记录
2025-05-11 15:00:18,529 - INFO - Request Parameters - Page 7:
2025-05-11 15:00:18,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:18,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:18,950 - INFO - Response - Page 7:
2025-05-11 15:00:19,151 - INFO - 第 7 页获取到 61 条记录
2025-05-11 15:00:19,151 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 15:00:19,151 - INFO - 获取到 661 条表单数据
2025-05-11 15:00:19,162 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 15:00:19,173 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 15:00:19,174 - INFO - 开始处理日期: 2025-04
2025-05-11 15:00:19,174 - INFO - Request Parameters - Page 1:
2025-05-11 15:00:19,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:19,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:19,751 - INFO - Response - Page 1:
2025-05-11 15:00:19,952 - INFO - 第 1 页获取到 100 条记录
2025-05-11 15:00:19,952 - INFO - Request Parameters - Page 2:
2025-05-11 15:00:19,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:19,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:20,368 - INFO - Response - Page 2:
2025-05-11 15:00:20,569 - INFO - 第 2 页获取到 100 条记录
2025-05-11 15:00:20,569 - INFO - Request Parameters - Page 3:
2025-05-11 15:00:20,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:20,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:21,027 - INFO - Response - Page 3:
2025-05-11 15:00:21,229 - INFO - 第 3 页获取到 100 条记录
2025-05-11 15:00:21,229 - INFO - Request Parameters - Page 4:
2025-05-11 15:00:21,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:21,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:21,704 - INFO - Response - Page 4:
2025-05-11 15:00:21,904 - INFO - 第 4 页获取到 100 条记录
2025-05-11 15:00:21,904 - INFO - Request Parameters - Page 5:
2025-05-11 15:00:21,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:21,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:22,385 - INFO - Response - Page 5:
2025-05-11 15:00:22,585 - INFO - 第 5 页获取到 100 条记录
2025-05-11 15:00:22,585 - INFO - Request Parameters - Page 6:
2025-05-11 15:00:22,585 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:22,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:23,071 - INFO - Response - Page 6:
2025-05-11 15:00:23,272 - INFO - 第 6 页获取到 100 条记录
2025-05-11 15:00:23,272 - INFO - Request Parameters - Page 7:
2025-05-11 15:00:23,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:23,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:23,693 - INFO - Response - Page 7:
2025-05-11 15:00:23,895 - INFO - 第 7 页获取到 54 条记录
2025-05-11 15:00:23,895 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 15:00:23,895 - INFO - 获取到 654 条表单数据
2025-05-11 15:00:23,906 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 15:00:23,918 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 15:00:23,918 - INFO - 开始处理日期: 2025-05
2025-05-11 15:00:23,918 - INFO - Request Parameters - Page 1:
2025-05-11 15:00:23,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:23,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:24,337 - INFO - Response - Page 1:
2025-05-11 15:00:24,537 - INFO - 第 1 页获取到 100 条记录
2025-05-11 15:00:24,537 - INFO - Request Parameters - Page 2:
2025-05-11 15:00:24,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:24,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:24,977 - INFO - Response - Page 2:
2025-05-11 15:00:25,178 - INFO - 第 2 页获取到 100 条记录
2025-05-11 15:00:25,178 - INFO - Request Parameters - Page 3:
2025-05-11 15:00:25,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:25,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:25,703 - INFO - Response - Page 3:
2025-05-11 15:00:25,903 - INFO - 第 3 页获取到 100 条记录
2025-05-11 15:00:25,903 - INFO - Request Parameters - Page 4:
2025-05-11 15:00:25,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:25,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:26,344 - INFO - Response - Page 4:
2025-05-11 15:00:26,544 - INFO - 第 4 页获取到 100 条记录
2025-05-11 15:00:26,544 - INFO - Request Parameters - Page 5:
2025-05-11 15:00:26,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:26,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:27,005 - INFO - Response - Page 5:
2025-05-11 15:00:27,205 - INFO - 第 5 页获取到 100 条记录
2025-05-11 15:00:27,205 - INFO - Request Parameters - Page 6:
2025-05-11 15:00:27,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:27,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:27,729 - INFO - Response - Page 6:
2025-05-11 15:00:27,929 - INFO - 第 6 页获取到 100 条记录
2025-05-11 15:00:27,929 - INFO - Request Parameters - Page 7:
2025-05-11 15:00:27,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 15:00:27,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 15:00:28,306 - INFO - Response - Page 7:
2025-05-11 15:00:28,506 - INFO - 第 7 页获取到 24 条记录
2025-05-11 15:00:28,506 - INFO - 查询完成，共获取到 624 条记录
2025-05-11 15:00:28,506 - INFO - 获取到 624 条表单数据
2025-05-11 15:00:28,518 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-11 15:00:28,520 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-11 15:00:29,032 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-11 15:00:29,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7096.0, 'new_value': 8011.0}, {'field': 'total_amount', 'old_value': 7096.0, 'new_value': 8011.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-11 15:00:29,034 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L
2025-05-11 15:00:29,457 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L
2025-05-11 15:00:29,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14511.0, 'new_value': 19071.0}, {'field': 'total_amount', 'old_value': 14511.0, 'new_value': 19071.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-11 15:00:29,458 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-11 15:00:29,844 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-11 15:00:29,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488000.0, 'new_value': 548000.0}, {'field': 'total_amount', 'old_value': 488000.0, 'new_value': 548000.0}, {'field': 'order_count', 'old_value': 328, 'new_value': 329}]
2025-05-11 15:00:29,845 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-11 15:00:30,292 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-11 15:00:30,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7597.0, 'new_value': 7606.9}, {'field': 'total_amount', 'old_value': 7597.0, 'new_value': 7606.9}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-11 15:00:30,292 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-11 15:00:30,753 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-11 15:00:30,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13598.0, 'new_value': 29997.0}, {'field': 'total_amount', 'old_value': 13598.0, 'new_value': 29997.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-11 15:00:30,753 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-11 15:00:31,178 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-11 15:00:31,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104860.0, 'new_value': 123028.1}, {'field': 'total_amount', 'old_value': 119732.0, 'new_value': 137900.1}, {'field': 'order_count', 'old_value': 912, 'new_value': 1034}]
2025-05-11 15:00:31,179 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-11 15:00:31,573 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-11 15:00:31,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19183.0, 'new_value': 22432.0}, {'field': 'total_amount', 'old_value': 19183.0, 'new_value': 22432.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-11 15:00:31,573 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-11 15:00:32,063 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-11 15:00:32,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33791.55, 'new_value': 34683.55}, {'field': 'total_amount', 'old_value': 33791.55, 'new_value': 34683.55}, {'field': 'order_count', 'old_value': 1251, 'new_value': 1305}]
2025-05-11 15:00:32,064 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-11 15:00:32,530 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-11 15:00:32,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24000.0, 'new_value': 28000.0}, {'field': 'total_amount', 'old_value': 24000.0, 'new_value': 28000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-11 15:00:32,531 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-11 15:00:33,003 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-11 15:00:33,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18490.2, 'new_value': 21730.2}, {'field': 'total_amount', 'old_value': 18490.2, 'new_value': 21730.2}, {'field': 'order_count', 'old_value': 187, 'new_value': 216}]
2025-05-11 15:00:33,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-11 15:00:33,475 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-11 15:00:33,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194994.75, 'new_value': 221694.77}, {'field': 'total_amount', 'old_value': 194994.75, 'new_value': 221694.77}, {'field': 'order_count', 'old_value': 331, 'new_value': 369}]
2025-05-11 15:00:33,476 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-11 15:00:33,922 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-11 15:00:33,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11506.0, 'new_value': 12811.0}, {'field': 'total_amount', 'old_value': 12882.0, 'new_value': 14187.0}, {'field': 'order_count', 'old_value': 1416, 'new_value': 1572}]
2025-05-11 15:00:33,923 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-11 15:00:34,391 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-11 15:00:34,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37024.0, 'new_value': 47714.0}, {'field': 'total_amount', 'old_value': 37024.0, 'new_value': 47714.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 13}]
2025-05-11 15:00:34,392 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-11 15:00:34,884 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-11 15:00:34,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52971.0, 'new_value': 59726.0}, {'field': 'total_amount', 'old_value': 68175.0, 'new_value': 74930.0}, {'field': 'order_count', 'old_value': 1358, 'new_value': 1525}]
2025-05-11 15:00:34,884 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-11 15:00:35,335 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-11 15:00:35,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60856.26, 'new_value': 70961.66}, {'field': 'total_amount', 'old_value': 60856.26, 'new_value': 70961.66}, {'field': 'order_count', 'old_value': 861, 'new_value': 940}]
2025-05-11 15:00:35,335 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-11 15:00:35,844 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-11 15:00:35,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2993.37, 'new_value': 3528.27}, {'field': 'total_amount', 'old_value': 2993.37, 'new_value': 3528.27}, {'field': 'order_count', 'old_value': 99, 'new_value': 116}]
2025-05-11 15:00:35,846 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-11 15:00:36,286 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-11 15:00:36,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154766.51, 'new_value': 176683.14}, {'field': 'total_amount', 'old_value': 154766.51, 'new_value': 176683.14}, {'field': 'order_count', 'old_value': 529, 'new_value': 604}]
2025-05-11 15:00:36,288 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-11 15:00:36,732 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-11 15:00:36,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21000.0, 'new_value': 28000.0}, {'field': 'total_amount', 'old_value': 21000.0, 'new_value': 28000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-11 15:00:36,732 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-11 15:00:37,281 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-11 15:00:37,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65442.6, 'new_value': 73651.67}, {'field': 'total_amount', 'old_value': 65442.6, 'new_value': 73651.67}, {'field': 'order_count', 'old_value': 488, 'new_value': 575}]
2025-05-11 15:00:37,281 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-11 15:00:37,700 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-11 15:00:37,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43370.0, 'new_value': 56490.0}, {'field': 'total_amount', 'old_value': 43371.0, 'new_value': 56491.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-11 15:00:37,701 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMM2
2025-05-11 15:00:38,160 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMM2
2025-05-11 15:00:38,160 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 18168.1}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 18168.1}, {'field': 'order_count', 'old_value': 0, 'new_value': 122}]
2025-05-11 15:00:38,160 - INFO - 日期 2025-05 处理完成 - 更新: 21 条，插入: 0 条，错误: 0 条
2025-05-11 15:00:38,160 - INFO - 数据同步完成！更新: 21 条，插入: 0 条，错误: 0 条
2025-05-11 15:00:38,162 - INFO - =================同步完成====================
2025-05-11 18:00:01,969 - INFO - =================使用默认全量同步=============
2025-05-11 18:00:03,284 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-11 18:00:03,285 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 18:00:03,313 - INFO - 开始处理日期: 2025-01
2025-05-11 18:00:03,316 - INFO - Request Parameters - Page 1:
2025-05-11 18:00:03,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:03,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:04,188 - INFO - Response - Page 1:
2025-05-11 18:00:04,388 - INFO - 第 1 页获取到 100 条记录
2025-05-11 18:00:04,388 - INFO - Request Parameters - Page 2:
2025-05-11 18:00:04,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:04,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:05,122 - INFO - Response - Page 2:
2025-05-11 18:00:05,322 - INFO - 第 2 页获取到 100 条记录
2025-05-11 18:00:05,322 - INFO - Request Parameters - Page 3:
2025-05-11 18:00:05,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:05,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:05,869 - INFO - Response - Page 3:
2025-05-11 18:00:06,069 - INFO - 第 3 页获取到 100 条记录
2025-05-11 18:00:06,069 - INFO - Request Parameters - Page 4:
2025-05-11 18:00:06,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:06,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:06,605 - INFO - Response - Page 4:
2025-05-11 18:00:06,806 - INFO - 第 4 页获取到 100 条记录
2025-05-11 18:00:06,806 - INFO - Request Parameters - Page 5:
2025-05-11 18:00:06,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:06,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:07,313 - INFO - Response - Page 5:
2025-05-11 18:00:07,513 - INFO - 第 5 页获取到 100 条记录
2025-05-11 18:00:07,513 - INFO - Request Parameters - Page 6:
2025-05-11 18:00:07,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:07,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:07,953 - INFO - Response - Page 6:
2025-05-11 18:00:08,153 - INFO - 第 6 页获取到 100 条记录
2025-05-11 18:00:08,153 - INFO - Request Parameters - Page 7:
2025-05-11 18:00:08,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:08,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:08,624 - INFO - Response - Page 7:
2025-05-11 18:00:08,824 - INFO - 第 7 页获取到 82 条记录
2025-05-11 18:00:08,824 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 18:00:08,824 - INFO - 获取到 682 条表单数据
2025-05-11 18:00:08,837 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 18:00:08,849 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 18:00:08,849 - INFO - 开始处理日期: 2025-02
2025-05-11 18:00:08,849 - INFO - Request Parameters - Page 1:
2025-05-11 18:00:08,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:08,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:09,323 - INFO - Response - Page 1:
2025-05-11 18:00:09,525 - INFO - 第 1 页获取到 100 条记录
2025-05-11 18:00:09,525 - INFO - Request Parameters - Page 2:
2025-05-11 18:00:09,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:09,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:10,021 - INFO - Response - Page 2:
2025-05-11 18:00:10,221 - INFO - 第 2 页获取到 100 条记录
2025-05-11 18:00:10,221 - INFO - Request Parameters - Page 3:
2025-05-11 18:00:10,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:10,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:10,721 - INFO - Response - Page 3:
2025-05-11 18:00:10,921 - INFO - 第 3 页获取到 100 条记录
2025-05-11 18:00:10,921 - INFO - Request Parameters - Page 4:
2025-05-11 18:00:10,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:10,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:11,423 - INFO - Response - Page 4:
2025-05-11 18:00:11,623 - INFO - 第 4 页获取到 100 条记录
2025-05-11 18:00:11,623 - INFO - Request Parameters - Page 5:
2025-05-11 18:00:11,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:11,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:12,093 - INFO - Response - Page 5:
2025-05-11 18:00:12,294 - INFO - 第 5 页获取到 100 条记录
2025-05-11 18:00:12,294 - INFO - Request Parameters - Page 6:
2025-05-11 18:00:12,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:12,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:12,754 - INFO - Response - Page 6:
2025-05-11 18:00:12,954 - INFO - 第 6 页获取到 100 条记录
2025-05-11 18:00:12,954 - INFO - Request Parameters - Page 7:
2025-05-11 18:00:12,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:12,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:13,437 - INFO - Response - Page 7:
2025-05-11 18:00:13,638 - INFO - 第 7 页获取到 70 条记录
2025-05-11 18:00:13,638 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 18:00:13,638 - INFO - 获取到 670 条表单数据
2025-05-11 18:00:13,650 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 18:00:13,662 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 18:00:13,662 - INFO - 开始处理日期: 2025-03
2025-05-11 18:00:13,663 - INFO - Request Parameters - Page 1:
2025-05-11 18:00:13,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:13,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:14,164 - INFO - Response - Page 1:
2025-05-11 18:00:14,364 - INFO - 第 1 页获取到 100 条记录
2025-05-11 18:00:14,364 - INFO - Request Parameters - Page 2:
2025-05-11 18:00:14,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:14,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:14,850 - INFO - Response - Page 2:
2025-05-11 18:00:15,050 - INFO - 第 2 页获取到 100 条记录
2025-05-11 18:00:15,050 - INFO - Request Parameters - Page 3:
2025-05-11 18:00:15,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:15,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:15,534 - INFO - Response - Page 3:
2025-05-11 18:00:15,735 - INFO - 第 3 页获取到 100 条记录
2025-05-11 18:00:15,735 - INFO - Request Parameters - Page 4:
2025-05-11 18:00:15,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:15,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:16,211 - INFO - Response - Page 4:
2025-05-11 18:00:16,411 - INFO - 第 4 页获取到 100 条记录
2025-05-11 18:00:16,411 - INFO - Request Parameters - Page 5:
2025-05-11 18:00:16,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:16,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:16,945 - INFO - Response - Page 5:
2025-05-11 18:00:17,146 - INFO - 第 5 页获取到 100 条记录
2025-05-11 18:00:17,146 - INFO - Request Parameters - Page 6:
2025-05-11 18:00:17,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:17,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:17,694 - INFO - Response - Page 6:
2025-05-11 18:00:17,895 - INFO - 第 6 页获取到 100 条记录
2025-05-11 18:00:17,895 - INFO - Request Parameters - Page 7:
2025-05-11 18:00:17,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:17,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:18,357 - INFO - Response - Page 7:
2025-05-11 18:00:18,558 - INFO - 第 7 页获取到 61 条记录
2025-05-11 18:00:18,558 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 18:00:18,558 - INFO - 获取到 661 条表单数据
2025-05-11 18:00:18,572 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 18:00:18,587 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 18:00:18,588 - INFO - 开始处理日期: 2025-04
2025-05-11 18:00:18,588 - INFO - Request Parameters - Page 1:
2025-05-11 18:00:18,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:18,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:19,035 - INFO - Response - Page 1:
2025-05-11 18:00:19,235 - INFO - 第 1 页获取到 100 条记录
2025-05-11 18:00:19,235 - INFO - Request Parameters - Page 2:
2025-05-11 18:00:19,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:19,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:19,730 - INFO - Response - Page 2:
2025-05-11 18:00:19,930 - INFO - 第 2 页获取到 100 条记录
2025-05-11 18:00:19,930 - INFO - Request Parameters - Page 3:
2025-05-11 18:00:19,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:19,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:20,391 - INFO - Response - Page 3:
2025-05-11 18:00:20,591 - INFO - 第 3 页获取到 100 条记录
2025-05-11 18:00:20,591 - INFO - Request Parameters - Page 4:
2025-05-11 18:00:20,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:20,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:21,044 - INFO - Response - Page 4:
2025-05-11 18:00:21,244 - INFO - 第 4 页获取到 100 条记录
2025-05-11 18:00:21,244 - INFO - Request Parameters - Page 5:
2025-05-11 18:00:21,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:21,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:21,703 - INFO - Response - Page 5:
2025-05-11 18:00:21,903 - INFO - 第 5 页获取到 100 条记录
2025-05-11 18:00:21,903 - INFO - Request Parameters - Page 6:
2025-05-11 18:00:21,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:21,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:22,529 - INFO - Response - Page 6:
2025-05-11 18:00:22,730 - INFO - 第 6 页获取到 100 条记录
2025-05-11 18:00:22,730 - INFO - Request Parameters - Page 7:
2025-05-11 18:00:22,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:22,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:23,071 - INFO - Response - Page 7:
2025-05-11 18:00:23,271 - INFO - 第 7 页获取到 54 条记录
2025-05-11 18:00:23,271 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 18:00:23,271 - INFO - 获取到 654 条表单数据
2025-05-11 18:00:23,285 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 18:00:23,298 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 18:00:23,298 - INFO - 开始处理日期: 2025-05
2025-05-11 18:00:23,298 - INFO - Request Parameters - Page 1:
2025-05-11 18:00:23,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:23,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:23,850 - INFO - Response - Page 1:
2025-05-11 18:00:24,050 - INFO - 第 1 页获取到 100 条记录
2025-05-11 18:00:24,050 - INFO - Request Parameters - Page 2:
2025-05-11 18:00:24,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:24,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:24,547 - INFO - Response - Page 2:
2025-05-11 18:00:24,747 - INFO - 第 2 页获取到 100 条记录
2025-05-11 18:00:24,747 - INFO - Request Parameters - Page 3:
2025-05-11 18:00:24,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:24,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:25,363 - INFO - Response - Page 3:
2025-05-11 18:00:25,563 - INFO - 第 3 页获取到 100 条记录
2025-05-11 18:00:25,563 - INFO - Request Parameters - Page 4:
2025-05-11 18:00:25,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:25,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:26,034 - INFO - Response - Page 4:
2025-05-11 18:00:26,234 - INFO - 第 4 页获取到 100 条记录
2025-05-11 18:00:26,234 - INFO - Request Parameters - Page 5:
2025-05-11 18:00:26,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:26,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:26,693 - INFO - Response - Page 5:
2025-05-11 18:00:26,894 - INFO - 第 5 页获取到 100 条记录
2025-05-11 18:00:26,894 - INFO - Request Parameters - Page 6:
2025-05-11 18:00:26,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:26,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:27,452 - INFO - Response - Page 6:
2025-05-11 18:00:27,652 - INFO - 第 6 页获取到 100 条记录
2025-05-11 18:00:27,652 - INFO - Request Parameters - Page 7:
2025-05-11 18:00:27,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 18:00:27,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 18:00:27,972 - INFO - Response - Page 7:
2025-05-11 18:00:28,172 - INFO - 第 7 页获取到 24 条记录
2025-05-11 18:00:28,172 - INFO - 查询完成，共获取到 624 条记录
2025-05-11 18:00:28,172 - INFO - 获取到 624 条表单数据
2025-05-11 18:00:28,184 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-11 18:00:28,195 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 18:00:28,196 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 18:00:28,197 - INFO - =================同步完成====================
2025-05-11 21:00:02,056 - INFO - =================使用默认全量同步=============
2025-05-11 21:00:03,388 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-11 21:00:03,389 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-11 21:00:03,416 - INFO - 开始处理日期: 2025-01
2025-05-11 21:00:03,419 - INFO - Request Parameters - Page 1:
2025-05-11 21:00:03,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:03,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:04,503 - INFO - Response - Page 1:
2025-05-11 21:00:04,703 - INFO - 第 1 页获取到 100 条记录
2025-05-11 21:00:04,703 - INFO - Request Parameters - Page 2:
2025-05-11 21:00:04,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:04,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:05,278 - INFO - Response - Page 2:
2025-05-11 21:00:05,478 - INFO - 第 2 页获取到 100 条记录
2025-05-11 21:00:05,478 - INFO - Request Parameters - Page 3:
2025-05-11 21:00:05,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:05,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:05,911 - INFO - Response - Page 3:
2025-05-11 21:00:06,112 - INFO - 第 3 页获取到 100 条记录
2025-05-11 21:00:06,112 - INFO - Request Parameters - Page 4:
2025-05-11 21:00:06,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:06,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:06,607 - INFO - Response - Page 4:
2025-05-11 21:00:06,807 - INFO - 第 4 页获取到 100 条记录
2025-05-11 21:00:06,807 - INFO - Request Parameters - Page 5:
2025-05-11 21:00:06,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:06,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:07,227 - INFO - Response - Page 5:
2025-05-11 21:00:07,427 - INFO - 第 5 页获取到 100 条记录
2025-05-11 21:00:07,427 - INFO - Request Parameters - Page 6:
2025-05-11 21:00:07,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:07,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:07,848 - INFO - Response - Page 6:
2025-05-11 21:00:08,048 - INFO - 第 6 页获取到 100 条记录
2025-05-11 21:00:08,048 - INFO - Request Parameters - Page 7:
2025-05-11 21:00:08,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:08,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:08,502 - INFO - Response - Page 7:
2025-05-11 21:00:08,703 - INFO - 第 7 页获取到 82 条记录
2025-05-11 21:00:08,703 - INFO - 查询完成，共获取到 682 条记录
2025-05-11 21:00:08,703 - INFO - 获取到 682 条表单数据
2025-05-11 21:00:08,715 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-11 21:00:08,726 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 21:00:08,726 - INFO - 开始处理日期: 2025-02
2025-05-11 21:00:08,727 - INFO - Request Parameters - Page 1:
2025-05-11 21:00:08,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:08,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:09,167 - INFO - Response - Page 1:
2025-05-11 21:00:09,368 - INFO - 第 1 页获取到 100 条记录
2025-05-11 21:00:09,368 - INFO - Request Parameters - Page 2:
2025-05-11 21:00:09,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:09,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:09,870 - INFO - Response - Page 2:
2025-05-11 21:00:10,072 - INFO - 第 2 页获取到 100 条记录
2025-05-11 21:00:10,072 - INFO - Request Parameters - Page 3:
2025-05-11 21:00:10,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:10,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:10,571 - INFO - Response - Page 3:
2025-05-11 21:00:10,771 - INFO - 第 3 页获取到 100 条记录
2025-05-11 21:00:10,771 - INFO - Request Parameters - Page 4:
2025-05-11 21:00:10,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:10,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:11,281 - INFO - Response - Page 4:
2025-05-11 21:00:11,481 - INFO - 第 4 页获取到 100 条记录
2025-05-11 21:00:11,481 - INFO - Request Parameters - Page 5:
2025-05-11 21:00:11,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:11,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:11,951 - INFO - Response - Page 5:
2025-05-11 21:00:12,152 - INFO - 第 5 页获取到 100 条记录
2025-05-11 21:00:12,152 - INFO - Request Parameters - Page 6:
2025-05-11 21:00:12,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:12,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:12,614 - INFO - Response - Page 6:
2025-05-11 21:00:12,814 - INFO - 第 6 页获取到 100 条记录
2025-05-11 21:00:12,814 - INFO - Request Parameters - Page 7:
2025-05-11 21:00:12,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:12,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:13,287 - INFO - Response - Page 7:
2025-05-11 21:00:13,489 - INFO - 第 7 页获取到 70 条记录
2025-05-11 21:00:13,489 - INFO - 查询完成，共获取到 670 条记录
2025-05-11 21:00:13,489 - INFO - 获取到 670 条表单数据
2025-05-11 21:00:13,500 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-11 21:00:13,511 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 21:00:13,511 - INFO - 开始处理日期: 2025-03
2025-05-11 21:00:13,512 - INFO - Request Parameters - Page 1:
2025-05-11 21:00:13,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:13,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:13,990 - INFO - Response - Page 1:
2025-05-11 21:00:14,190 - INFO - 第 1 页获取到 100 条记录
2025-05-11 21:00:14,190 - INFO - Request Parameters - Page 2:
2025-05-11 21:00:14,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:14,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:14,682 - INFO - Response - Page 2:
2025-05-11 21:00:14,882 - INFO - 第 2 页获取到 100 条记录
2025-05-11 21:00:14,882 - INFO - Request Parameters - Page 3:
2025-05-11 21:00:14,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:14,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:15,336 - INFO - Response - Page 3:
2025-05-11 21:00:15,537 - INFO - 第 3 页获取到 100 条记录
2025-05-11 21:00:15,537 - INFO - Request Parameters - Page 4:
2025-05-11 21:00:15,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:15,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:16,183 - INFO - Response - Page 4:
2025-05-11 21:00:16,383 - INFO - 第 4 页获取到 100 条记录
2025-05-11 21:00:16,383 - INFO - Request Parameters - Page 5:
2025-05-11 21:00:16,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:16,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:16,824 - INFO - Response - Page 5:
2025-05-11 21:00:17,024 - INFO - 第 5 页获取到 100 条记录
2025-05-11 21:00:17,024 - INFO - Request Parameters - Page 6:
2025-05-11 21:00:17,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:17,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:17,517 - INFO - Response - Page 6:
2025-05-11 21:00:17,717 - INFO - 第 6 页获取到 100 条记录
2025-05-11 21:00:17,717 - INFO - Request Parameters - Page 7:
2025-05-11 21:00:17,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:17,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:18,144 - INFO - Response - Page 7:
2025-05-11 21:00:18,344 - INFO - 第 7 页获取到 61 条记录
2025-05-11 21:00:18,344 - INFO - 查询完成，共获取到 661 条记录
2025-05-11 21:00:18,344 - INFO - 获取到 661 条表单数据
2025-05-11 21:00:18,358 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-11 21:00:18,369 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 21:00:18,370 - INFO - 开始处理日期: 2025-04
2025-05-11 21:00:18,370 - INFO - Request Parameters - Page 1:
2025-05-11 21:00:18,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:18,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:19,252 - INFO - Response - Page 1:
2025-05-11 21:00:19,452 - INFO - 第 1 页获取到 100 条记录
2025-05-11 21:00:19,452 - INFO - Request Parameters - Page 2:
2025-05-11 21:00:19,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:19,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:19,975 - INFO - Response - Page 2:
2025-05-11 21:00:20,176 - INFO - 第 2 页获取到 100 条记录
2025-05-11 21:00:20,176 - INFO - Request Parameters - Page 3:
2025-05-11 21:00:20,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:20,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:20,669 - INFO - Response - Page 3:
2025-05-11 21:00:20,869 - INFO - 第 3 页获取到 100 条记录
2025-05-11 21:00:20,869 - INFO - Request Parameters - Page 4:
2025-05-11 21:00:20,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:20,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:21,332 - INFO - Response - Page 4:
2025-05-11 21:00:21,532 - INFO - 第 4 页获取到 100 条记录
2025-05-11 21:00:21,532 - INFO - Request Parameters - Page 5:
2025-05-11 21:00:21,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:21,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:21,981 - INFO - Response - Page 5:
2025-05-11 21:00:22,181 - INFO - 第 5 页获取到 100 条记录
2025-05-11 21:00:22,181 - INFO - Request Parameters - Page 6:
2025-05-11 21:00:22,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:22,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:22,634 - INFO - Response - Page 6:
2025-05-11 21:00:22,834 - INFO - 第 6 页获取到 100 条记录
2025-05-11 21:00:22,834 - INFO - Request Parameters - Page 7:
2025-05-11 21:00:22,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:22,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:23,198 - INFO - Response - Page 7:
2025-05-11 21:00:23,398 - INFO - 第 7 页获取到 54 条记录
2025-05-11 21:00:23,398 - INFO - 查询完成，共获取到 654 条记录
2025-05-11 21:00:23,398 - INFO - 获取到 654 条表单数据
2025-05-11 21:00:23,410 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-11 21:00:23,421 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-11 21:00:23,421 - INFO - 开始处理日期: 2025-05
2025-05-11 21:00:23,421 - INFO - Request Parameters - Page 1:
2025-05-11 21:00:23,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:23,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:23,907 - INFO - Response - Page 1:
2025-05-11 21:00:24,108 - INFO - 第 1 页获取到 100 条记录
2025-05-11 21:00:24,108 - INFO - Request Parameters - Page 2:
2025-05-11 21:00:24,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:24,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:24,570 - INFO - Response - Page 2:
2025-05-11 21:00:24,770 - INFO - 第 2 页获取到 100 条记录
2025-05-11 21:00:24,770 - INFO - Request Parameters - Page 3:
2025-05-11 21:00:24,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:24,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:25,234 - INFO - Response - Page 3:
2025-05-11 21:00:25,434 - INFO - 第 3 页获取到 100 条记录
2025-05-11 21:00:25,434 - INFO - Request Parameters - Page 4:
2025-05-11 21:00:25,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:25,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:25,884 - INFO - Response - Page 4:
2025-05-11 21:00:26,084 - INFO - 第 4 页获取到 100 条记录
2025-05-11 21:00:26,084 - INFO - Request Parameters - Page 5:
2025-05-11 21:00:26,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:26,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:26,556 - INFO - Response - Page 5:
2025-05-11 21:00:26,756 - INFO - 第 5 页获取到 100 条记录
2025-05-11 21:00:26,756 - INFO - Request Parameters - Page 6:
2025-05-11 21:00:26,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:26,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:27,243 - INFO - Response - Page 6:
2025-05-11 21:00:27,444 - INFO - 第 6 页获取到 100 条记录
2025-05-11 21:00:27,444 - INFO - Request Parameters - Page 7:
2025-05-11 21:00:27,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-11 21:00:27,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-11 21:00:27,798 - INFO - Response - Page 7:
2025-05-11 21:00:27,998 - INFO - 第 7 页获取到 24 条记录
2025-05-11 21:00:27,998 - INFO - 查询完成，共获取到 624 条记录
2025-05-11 21:00:27,998 - INFO - 获取到 624 条表单数据
2025-05-11 21:00:28,010 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-11 21:00:28,012 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-11 21:00:28,506 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-11 21:00:28,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33794.0, 'new_value': 37782.0}, {'field': 'offline_amount', 'old_value': 43188.0, 'new_value': 47301.0}, {'field': 'total_amount', 'old_value': 76982.0, 'new_value': 85083.0}, {'field': 'order_count', 'old_value': 1862, 'new_value': 2017}]
2025-05-11 21:00:28,517 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-11 21:00:28,517 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-11 21:00:28,519 - INFO - =================同步完成====================
