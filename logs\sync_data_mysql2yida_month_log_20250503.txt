2025-05-03 00:00:04,106 - INFO - =================使用默认全量同步=============
2025-05-03 00:00:05,279 - INFO - MySQL查询成功，共获取 3196 条记录
2025-05-03 00:00:05,279 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 00:00:05,310 - INFO - 开始处理日期: 2025-01
2025-05-03 00:00:05,310 - INFO - Request Parameters - Page 1:
2025-05-03 00:00:05,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:05,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:06,264 - INFO - Response - Page 1:
2025-05-03 00:00:06,468 - INFO - 第 1 页获取到 100 条记录
2025-05-03 00:00:06,468 - INFO - Request Parameters - Page 2:
2025-05-03 00:00:06,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:06,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:07,078 - INFO - Response - Page 2:
2025-05-03 00:00:07,281 - INFO - 第 2 页获取到 100 条记录
2025-05-03 00:00:07,281 - INFO - Request Parameters - Page 3:
2025-05-03 00:00:07,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:07,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:07,750 - INFO - Response - Page 3:
2025-05-03 00:00:07,954 - INFO - 第 3 页获取到 100 条记录
2025-05-03 00:00:07,954 - INFO - Request Parameters - Page 4:
2025-05-03 00:00:07,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:07,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:08,360 - INFO - Response - Page 4:
2025-05-03 00:00:08,564 - INFO - 第 4 页获取到 100 条记录
2025-05-03 00:00:08,564 - INFO - Request Parameters - Page 5:
2025-05-03 00:00:08,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:08,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:09,033 - INFO - Response - Page 5:
2025-05-03 00:00:09,236 - INFO - 第 5 页获取到 100 条记录
2025-05-03 00:00:09,236 - INFO - Request Parameters - Page 6:
2025-05-03 00:00:09,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:09,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:09,690 - INFO - Response - Page 6:
2025-05-03 00:00:09,893 - INFO - 第 6 页获取到 100 条记录
2025-05-03 00:00:09,893 - INFO - Request Parameters - Page 7:
2025-05-03 00:00:09,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:09,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:10,472 - INFO - Response - Page 7:
2025-05-03 00:00:10,675 - INFO - 第 7 页获取到 82 条记录
2025-05-03 00:00:10,675 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 00:00:10,675 - INFO - 获取到 682 条表单数据
2025-05-03 00:00:10,675 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 00:00:10,691 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 00:00:10,691 - INFO - 开始处理日期: 2025-02
2025-05-03 00:00:10,691 - INFO - Request Parameters - Page 1:
2025-05-03 00:00:10,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:10,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:11,379 - INFO - Response - Page 1:
2025-05-03 00:00:11,582 - INFO - 第 1 页获取到 100 条记录
2025-05-03 00:00:11,582 - INFO - Request Parameters - Page 2:
2025-05-03 00:00:11,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:11,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:12,067 - INFO - Response - Page 2:
2025-05-03 00:00:12,271 - INFO - 第 2 页获取到 100 条记录
2025-05-03 00:00:12,271 - INFO - Request Parameters - Page 3:
2025-05-03 00:00:12,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:12,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:12,709 - INFO - Response - Page 3:
2025-05-03 00:00:12,912 - INFO - 第 3 页获取到 100 条记录
2025-05-03 00:00:12,912 - INFO - Request Parameters - Page 4:
2025-05-03 00:00:12,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:12,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:13,366 - INFO - Response - Page 4:
2025-05-03 00:00:13,569 - INFO - 第 4 页获取到 100 条记录
2025-05-03 00:00:13,569 - INFO - Request Parameters - Page 5:
2025-05-03 00:00:13,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:13,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:14,023 - INFO - Response - Page 5:
2025-05-03 00:00:14,226 - INFO - 第 5 页获取到 100 条记录
2025-05-03 00:00:14,226 - INFO - Request Parameters - Page 6:
2025-05-03 00:00:14,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:14,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:14,679 - INFO - Response - Page 6:
2025-05-03 00:00:14,883 - INFO - 第 6 页获取到 100 条记录
2025-05-03 00:00:14,883 - INFO - Request Parameters - Page 7:
2025-05-03 00:00:14,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:14,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:15,352 - INFO - Response - Page 7:
2025-05-03 00:00:15,555 - INFO - 第 7 页获取到 70 条记录
2025-05-03 00:00:15,555 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 00:00:15,555 - INFO - 获取到 670 条表单数据
2025-05-03 00:00:15,555 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 00:00:15,571 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 00:00:15,571 - INFO - 开始处理日期: 2025-03
2025-05-03 00:00:15,571 - INFO - Request Parameters - Page 1:
2025-05-03 00:00:15,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:15,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:15,993 - INFO - Response - Page 1:
2025-05-03 00:00:16,197 - INFO - 第 1 页获取到 100 条记录
2025-05-03 00:00:16,197 - INFO - Request Parameters - Page 2:
2025-05-03 00:00:16,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:16,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:16,713 - INFO - Response - Page 2:
2025-05-03 00:00:16,916 - INFO - 第 2 页获取到 100 条记录
2025-05-03 00:00:16,916 - INFO - Request Parameters - Page 3:
2025-05-03 00:00:16,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:16,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:17,385 - INFO - Response - Page 3:
2025-05-03 00:00:17,589 - INFO - 第 3 页获取到 100 条记录
2025-05-03 00:00:17,589 - INFO - Request Parameters - Page 4:
2025-05-03 00:00:17,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:17,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:18,042 - INFO - Response - Page 4:
2025-05-03 00:00:18,246 - INFO - 第 4 页获取到 100 条记录
2025-05-03 00:00:18,246 - INFO - Request Parameters - Page 5:
2025-05-03 00:00:18,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:18,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:18,652 - INFO - Response - Page 5:
2025-05-03 00:00:18,856 - INFO - 第 5 页获取到 100 条记录
2025-05-03 00:00:18,856 - INFO - Request Parameters - Page 6:
2025-05-03 00:00:18,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:18,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:19,309 - INFO - Response - Page 6:
2025-05-03 00:00:19,513 - INFO - 第 6 页获取到 100 条记录
2025-05-03 00:00:19,513 - INFO - Request Parameters - Page 7:
2025-05-03 00:00:19,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:19,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:19,966 - INFO - Response - Page 7:
2025-05-03 00:00:20,170 - INFO - 第 7 页获取到 61 条记录
2025-05-03 00:00:20,170 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 00:00:20,170 - INFO - 获取到 661 条表单数据
2025-05-03 00:00:20,170 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 00:00:20,185 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 00:00:20,185 - INFO - 开始处理日期: 2025-04
2025-05-03 00:00:20,185 - INFO - Request Parameters - Page 1:
2025-05-03 00:00:20,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:20,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:20,686 - INFO - Response - Page 1:
2025-05-03 00:00:20,889 - INFO - 第 1 页获取到 100 条记录
2025-05-03 00:00:20,889 - INFO - Request Parameters - Page 2:
2025-05-03 00:00:20,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:20,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:21,358 - INFO - Response - Page 2:
2025-05-03 00:00:21,562 - INFO - 第 2 页获取到 100 条记录
2025-05-03 00:00:21,562 - INFO - Request Parameters - Page 3:
2025-05-03 00:00:21,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:21,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:22,062 - INFO - Response - Page 3:
2025-05-03 00:00:22,266 - INFO - 第 3 页获取到 100 条记录
2025-05-03 00:00:22,266 - INFO - Request Parameters - Page 4:
2025-05-03 00:00:22,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:22,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:22,813 - INFO - Response - Page 4:
2025-05-03 00:00:23,016 - INFO - 第 4 页获取到 100 条记录
2025-05-03 00:00:23,016 - INFO - Request Parameters - Page 5:
2025-05-03 00:00:23,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:23,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:23,486 - INFO - Response - Page 5:
2025-05-03 00:00:23,689 - INFO - 第 5 页获取到 100 条记录
2025-05-03 00:00:23,689 - INFO - Request Parameters - Page 6:
2025-05-03 00:00:23,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:23,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:24,190 - INFO - Response - Page 6:
2025-05-03 00:00:24,393 - INFO - 第 6 页获取到 100 条记录
2025-05-03 00:00:24,393 - INFO - Request Parameters - Page 7:
2025-05-03 00:00:24,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:24,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:24,753 - INFO - Response - Page 7:
2025-05-03 00:00:24,956 - INFO - 第 7 页获取到 27 条记录
2025-05-03 00:00:24,956 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 00:00:24,956 - INFO - 获取到 627 条表单数据
2025-05-03 00:00:24,956 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 00:00:24,956 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ5
2025-05-03 00:00:25,457 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MJ5
2025-05-03 00:00:25,457 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 405, 'new_value': 7}]
2025-05-03 00:00:25,457 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-03 00:00:25,457 - INFO - 开始处理日期: 2025-05
2025-05-03 00:00:25,457 - INFO - Request Parameters - Page 1:
2025-05-03 00:00:25,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:25,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:25,926 - INFO - Response - Page 1:
2025-05-03 00:00:26,145 - INFO - 第 1 页获取到 100 条记录
2025-05-03 00:00:26,145 - INFO - Request Parameters - Page 2:
2025-05-03 00:00:26,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:26,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:26,739 - INFO - Response - Page 2:
2025-05-03 00:00:26,942 - INFO - 第 2 页获取到 100 条记录
2025-05-03 00:00:26,942 - INFO - Request Parameters - Page 3:
2025-05-03 00:00:26,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:26,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:27,474 - INFO - Response - Page 3:
2025-05-03 00:00:27,678 - INFO - 第 3 页获取到 100 条记录
2025-05-03 00:00:27,678 - INFO - Request Parameters - Page 4:
2025-05-03 00:00:27,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:27,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:28,288 - INFO - Response - Page 4:
2025-05-03 00:00:28,491 - INFO - 第 4 页获取到 100 条记录
2025-05-03 00:00:28,491 - INFO - Request Parameters - Page 5:
2025-05-03 00:00:28,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:28,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:29,007 - INFO - Response - Page 5:
2025-05-03 00:00:29,211 - INFO - 第 5 页获取到 100 条记录
2025-05-03 00:00:29,211 - INFO - Request Parameters - Page 6:
2025-05-03 00:00:29,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 00:00:29,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 00:00:29,570 - INFO - Response - Page 6:
2025-05-03 00:00:29,774 - INFO - 第 6 页获取到 55 条记录
2025-05-03 00:00:29,774 - INFO - 查询完成，共获取到 555 条记录
2025-05-03 00:00:29,774 - INFO - 获取到 555 条表单数据
2025-05-03 00:00:29,774 - INFO - 当前日期 2025-05 有 556 条MySQL数据需要处理
2025-05-03 00:00:29,774 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-03 00:00:30,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-03 00:00:30,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 600000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 600000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 00:00:30,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-03 00:00:30,618 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-03 00:00:30,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43705.49, 'new_value': 82248.07}, {'field': 'total_amount', 'old_value': 43705.49, 'new_value': 82248.07}, {'field': 'order_count', 'old_value': 143, 'new_value': 264}]
2025-05-03 00:00:30,618 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-03 00:00:31,009 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-03 00:00:31,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2897.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2897.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 00:00:31,009 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-03 00:00:31,479 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-03 00:00:31,479 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 903.0, 'new_value': 1747.0}, {'field': 'offline_amount', 'old_value': 677.0, 'new_value': 982.0}, {'field': 'total_amount', 'old_value': 1580.0, 'new_value': 2729.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 129}]
2025-05-03 00:00:31,479 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-03 00:00:31,932 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-03 00:00:31,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1368.0, 'new_value': 1704.0}, {'field': 'total_amount', 'old_value': 1368.0, 'new_value': 1704.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 51}]
2025-05-03 00:00:31,932 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-03 00:00:32,308 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-03 00:00:32,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 438.0, 'new_value': 3438.0}, {'field': 'total_amount', 'old_value': 438.0, 'new_value': 3438.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 6}]
2025-05-03 00:00:32,308 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-03 00:00:32,730 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-03 00:00:32,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24577.0, 'new_value': 42664.0}, {'field': 'total_amount', 'old_value': 24577.0, 'new_value': 42664.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 44}]
2025-05-03 00:00:32,730 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-03 00:00:33,121 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-03 00:00:33,121 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3133.0, 'new_value': 6076.0}, {'field': 'offline_amount', 'old_value': 4021.0, 'new_value': 9042.0}, {'field': 'total_amount', 'old_value': 7154.0, 'new_value': 15118.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 373}]
2025-05-03 00:00:33,121 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-03 00:00:33,528 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-03 00:00:33,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39.7, 'new_value': 12166.4}, {'field': 'total_amount', 'old_value': 39.7, 'new_value': 12166.4}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-03 00:00:33,528 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-03 00:00:33,950 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-03 00:00:33,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3101.14, 'new_value': 6522.74}, {'field': 'total_amount', 'old_value': 3101.14, 'new_value': 6522.74}, {'field': 'order_count', 'old_value': 10, 'new_value': 22}]
2025-05-03 00:00:33,950 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-03 00:00:34,325 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-03 00:00:34,325 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-03 00:00:34,325 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-03 00:00:34,748 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-03 00:00:34,748 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8441.0, 'new_value': 18408.0}, {'field': 'total_amount', 'old_value': 8441.0, 'new_value': 18408.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 101}]
2025-05-03 00:00:34,748 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-03 00:00:35,186 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-03 00:00:35,186 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1783.39, 'new_value': 5461.15}, {'field': 'offline_amount', 'old_value': 37779.8, 'new_value': 77757.05}, {'field': 'total_amount', 'old_value': 39563.19, 'new_value': 83218.2}, {'field': 'order_count', 'old_value': 163, 'new_value': 326}]
2025-05-03 00:00:35,186 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-03 00:00:35,608 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-03 00:00:35,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6406.0, 'new_value': 9473.0}, {'field': 'total_amount', 'old_value': 8756.32, 'new_value': 11823.32}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-05-03 00:00:35,608 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-03 00:00:36,077 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-03 00:00:36,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3602.39, 'new_value': 6132.41}, {'field': 'offline_amount', 'old_value': 50223.3, 'new_value': 82778.1}, {'field': 'total_amount', 'old_value': 53825.69, 'new_value': 88910.51}, {'field': 'order_count', 'old_value': 142, 'new_value': 255}]
2025-05-03 00:00:36,077 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-03 00:00:36,453 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-03 00:00:36,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40499.87, 'new_value': 75907.06}, {'field': 'total_amount', 'old_value': 40499.87, 'new_value': 75907.06}, {'field': 'order_count', 'old_value': 283, 'new_value': 533}]
2025-05-03 00:00:36,453 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-03 00:00:36,875 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-03 00:00:36,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1599.0, 'new_value': 6099.0}, {'field': 'total_amount', 'old_value': 1599.0, 'new_value': 6099.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:00:36,875 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-03 00:00:37,282 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-03 00:00:37,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2495.41, 'new_value': 5109.21}, {'field': 'offline_amount', 'old_value': 8339.42, 'new_value': 13588.29}, {'field': 'total_amount', 'old_value': 10834.83, 'new_value': 18697.5}, {'field': 'order_count', 'old_value': 323, 'new_value': 576}]
2025-05-03 00:00:37,282 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-03 00:00:37,673 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-03 00:00:37,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26564.0, 'new_value': 48039.0}, {'field': 'total_amount', 'old_value': 26564.0, 'new_value': 48039.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 23}]
2025-05-03 00:00:37,673 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-03 00:00:38,142 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-03 00:00:38,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12056.97, 'new_value': 25120.08}, {'field': 'total_amount', 'old_value': 12056.97, 'new_value': 25120.08}, {'field': 'order_count', 'old_value': 59, 'new_value': 122}]
2025-05-03 00:00:38,142 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-03 00:00:38,533 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-03 00:00:38,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1095.0, 'new_value': 2381.0}, {'field': 'total_amount', 'old_value': 1095.0, 'new_value': 2381.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 11}]
2025-05-03 00:00:38,533 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-03 00:00:38,940 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-03 00:00:38,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4724.69, 'new_value': 7553.42}, {'field': 'total_amount', 'old_value': 4724.69, 'new_value': 7553.42}, {'field': 'order_count', 'old_value': 40, 'new_value': 70}]
2025-05-03 00:00:38,940 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-03 00:00:39,393 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-03 00:00:39,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7163.0, 'new_value': 12896.75}, {'field': 'offline_amount', 'old_value': 4956.0, 'new_value': 10791.0}, {'field': 'total_amount', 'old_value': 12119.0, 'new_value': 23687.75}, {'field': 'order_count', 'old_value': 60, 'new_value': 110}]
2025-05-03 00:00:39,393 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-03 00:00:39,831 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-03 00:00:39,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6826.0, 'new_value': 8971.0}, {'field': 'total_amount', 'old_value': 6826.0, 'new_value': 8971.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:00:39,831 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-03 00:00:40,285 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-03 00:00:40,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1407.06, 'new_value': 2982.07}, {'field': 'offline_amount', 'old_value': 8319.48, 'new_value': 14730.35}, {'field': 'total_amount', 'old_value': 9726.54, 'new_value': 17712.42}, {'field': 'order_count', 'old_value': 329, 'new_value': 609}]
2025-05-03 00:00:40,285 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-03 00:00:40,754 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-03 00:00:40,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6999.0, 'new_value': 17896.0}, {'field': 'total_amount', 'old_value': 9680.0, 'new_value': 20577.0}, {'field': 'order_count', 'old_value': 206, 'new_value': 438}]
2025-05-03 00:00:40,754 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-03 00:00:41,161 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-03 00:00:41,161 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 736.1, 'new_value': 1062.37}, {'field': 'offline_amount', 'old_value': 18482.6, 'new_value': 30771.5}, {'field': 'total_amount', 'old_value': 19218.7, 'new_value': 31833.87}, {'field': 'order_count', 'old_value': 114, 'new_value': 195}]
2025-05-03 00:00:41,161 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-03 00:00:41,630 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-03 00:00:41,630 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8333.01, 'new_value': 18992.43}, {'field': 'offline_amount', 'old_value': 7198.0, 'new_value': 11916.0}, {'field': 'total_amount', 'old_value': 15531.01, 'new_value': 30908.43}, {'field': 'order_count', 'old_value': 121, 'new_value': 232}]
2025-05-03 00:00:41,630 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-03 00:00:42,068 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-03 00:00:42,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7899.0, 'new_value': 8482.0}, {'field': 'total_amount', 'old_value': 7899.0, 'new_value': 8482.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-03 00:00:42,068 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-03 00:00:42,490 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-03 00:00:42,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4038.35, 'new_value': 7453.85}, {'field': 'offline_amount', 'old_value': 25195.92, 'new_value': 57127.81}, {'field': 'total_amount', 'old_value': 29234.27, 'new_value': 64581.66}, {'field': 'order_count', 'old_value': 188, 'new_value': 375}]
2025-05-03 00:00:42,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-03 00:00:42,850 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-03 00:00:42,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6813.0, 'new_value': 9709.0}, {'field': 'total_amount', 'old_value': 6813.0, 'new_value': 9709.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 17}]
2025-05-03 00:00:42,850 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-03 00:00:43,272 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-03 00:00:43,272 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57984.0, 'new_value': 136768.0}, {'field': 'total_amount', 'old_value': 57984.0, 'new_value': 136768.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 17}]
2025-05-03 00:00:43,272 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-03 00:00:43,726 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-03 00:00:43,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7811.08, 'new_value': 24888.75}, {'field': 'offline_amount', 'old_value': 14792.46, 'new_value': 19342.26}, {'field': 'total_amount', 'old_value': 22603.54, 'new_value': 44231.01}, {'field': 'order_count', 'old_value': 92, 'new_value': 183}]
2025-05-03 00:00:43,726 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-03 00:00:44,133 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-03 00:00:44,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9519.99, 'new_value': 16790.49}, {'field': 'total_amount', 'old_value': 9519.99, 'new_value': 16790.49}, {'field': 'order_count', 'old_value': 325, 'new_value': 590}]
2025-05-03 00:00:44,133 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-03 00:00:44,539 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-03 00:00:44,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 747.42, 'new_value': 1476.09}, {'field': 'offline_amount', 'old_value': 22328.49, 'new_value': 36254.99}, {'field': 'total_amount', 'old_value': 23075.91, 'new_value': 37731.08}, {'field': 'order_count', 'old_value': 111, 'new_value': 182}]
2025-05-03 00:00:44,539 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-03 00:00:44,962 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-03 00:00:44,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1194.83, 'new_value': 2286.31}, {'field': 'offline_amount', 'old_value': 35973.28, 'new_value': 70127.6}, {'field': 'total_amount', 'old_value': 37168.11, 'new_value': 72413.91}, {'field': 'order_count', 'old_value': 206, 'new_value': 448}]
2025-05-03 00:00:44,962 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-03 00:00:45,415 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-03 00:00:45,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3734.0, 'new_value': 4191.0}, {'field': 'total_amount', 'old_value': 3734.0, 'new_value': 4191.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 11}]
2025-05-03 00:00:45,415 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-03 00:00:45,853 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-03 00:00:45,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1002.7}, {'field': 'total_amount', 'old_value': 1850.1, 'new_value': 2852.8}, {'field': 'order_count', 'old_value': 7, 'new_value': 14}]
2025-05-03 00:00:45,853 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-05-03 00:00:46,260 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-05-03 00:00:46,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 00:00:46,260 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-03 00:00:46,698 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-03 00:00:46,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3673.0, 'new_value': 10783.0}, {'field': 'total_amount', 'old_value': 3673.0, 'new_value': 10783.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-03 00:00:46,698 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-03 00:00:47,120 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-03 00:00:47,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260.0, 'new_value': 440.0}, {'field': 'total_amount', 'old_value': 260.0, 'new_value': 440.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:00:47,120 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-03 00:00:47,527 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-03 00:00:47,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9051.7, 'new_value': 17662.2}, {'field': 'total_amount', 'old_value': 9051.7, 'new_value': 17662.2}, {'field': 'order_count', 'old_value': 27, 'new_value': 50}]
2025-05-03 00:00:47,527 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-03 00:00:47,965 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-03 00:00:47,965 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5932.08, 'new_value': 10832.21}, {'field': 'offline_amount', 'old_value': 28784.64, 'new_value': 54153.35}, {'field': 'total_amount', 'old_value': 34716.72, 'new_value': 64985.56}, {'field': 'order_count', 'old_value': 712, 'new_value': 1337}]
2025-05-03 00:00:47,965 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-03 00:00:48,387 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-03 00:00:48,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1680.0, 'new_value': 5823.0}, {'field': 'total_amount', 'old_value': 1680.0, 'new_value': 5823.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 15}]
2025-05-03 00:00:48,387 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-03 00:00:48,778 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-03 00:00:48,778 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1286.7, 'new_value': 3603.4}, {'field': 'offline_amount', 'old_value': 1424.0, 'new_value': 3210.9}, {'field': 'total_amount', 'old_value': 2710.7, 'new_value': 6814.3}, {'field': 'order_count', 'old_value': 13, 'new_value': 33}]
2025-05-03 00:00:48,778 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-03 00:00:49,232 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-03 00:00:49,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 995.0, 'new_value': 1794.0}, {'field': 'total_amount', 'old_value': 995.0, 'new_value': 1794.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-03 00:00:49,232 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-03 00:00:49,654 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-03 00:00:49,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12120.07, 'new_value': 23424.42}, {'field': 'total_amount', 'old_value': 12120.07, 'new_value': 23424.42}, {'field': 'order_count', 'old_value': 270, 'new_value': 566}]
2025-05-03 00:00:49,654 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-03 00:00:50,139 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-03 00:00:50,139 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32651.3, 'new_value': 63222.0}, {'field': 'total_amount', 'old_value': 32651.3, 'new_value': 63222.0}, {'field': 'order_count', 'old_value': 366, 'new_value': 703}]
2025-05-03 00:00:50,139 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-03 00:00:50,561 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-03 00:00:50,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138732.96, 'new_value': 274706.68}, {'field': 'total_amount', 'old_value': 138732.96, 'new_value': 274706.68}, {'field': 'order_count', 'old_value': 750, 'new_value': 1565}]
2025-05-03 00:00:50,561 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-03 00:00:50,984 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-03 00:00:50,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43788.35, 'new_value': 81829.37}, {'field': 'total_amount', 'old_value': 43788.35, 'new_value': 81829.37}, {'field': 'order_count', 'old_value': 151, 'new_value': 283}]
2025-05-03 00:00:50,984 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-03 00:00:51,390 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-03 00:00:51,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12136.0, 'new_value': 24015.0}, {'field': 'total_amount', 'old_value': 12136.0, 'new_value': 24015.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 14}]
2025-05-03 00:00:51,406 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-03 00:00:51,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-03 00:00:51,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10002.0, 'new_value': 19034.0}, {'field': 'total_amount', 'old_value': 10002.0, 'new_value': 19034.0}, {'field': 'order_count', 'old_value': 930, 'new_value': 1774}]
2025-05-03 00:00:51,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-03 00:00:52,266 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-03 00:00:52,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 191.4, 'new_value': 430.22}, {'field': 'offline_amount', 'old_value': 1072.3, 'new_value': 2166.9}, {'field': 'total_amount', 'old_value': 1263.7, 'new_value': 2597.12}, {'field': 'order_count', 'old_value': 78, 'new_value': 163}]
2025-05-03 00:00:52,266 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-03 00:00:52,657 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-03 00:00:52,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8528.0, 'new_value': 15792.0}, {'field': 'total_amount', 'old_value': 8528.0, 'new_value': 15792.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 18}]
2025-05-03 00:00:52,657 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-03 00:00:53,080 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-03 00:00:53,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3867.0, 'new_value': 9464.0}, {'field': 'total_amount', 'old_value': 3867.0, 'new_value': 9464.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 71}]
2025-05-03 00:00:53,080 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-03 00:00:53,518 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-03 00:00:53,518 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3582.81, 'new_value': 5890.09}, {'field': 'offline_amount', 'old_value': 36260.33, 'new_value': 67148.03}, {'field': 'total_amount', 'old_value': 39843.14, 'new_value': 73038.12}, {'field': 'order_count', 'old_value': 290, 'new_value': 545}]
2025-05-03 00:00:53,518 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-03 00:00:53,940 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-03 00:00:53,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37437.0, 'new_value': 70615.0}, {'field': 'total_amount', 'old_value': 37437.0, 'new_value': 70615.0}, {'field': 'order_count', 'old_value': 263, 'new_value': 538}]
2025-05-03 00:00:53,940 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-03 00:00:54,378 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-03 00:00:54,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1600.0, 'new_value': 9300.0}, {'field': 'total_amount', 'old_value': 1600.0, 'new_value': 9300.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 00:00:54,378 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-03 00:00:54,753 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-03 00:00:54,753 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4897.2, 'new_value': 10869.98}, {'field': 'offline_amount', 'old_value': 3556.2, 'new_value': 7133.06}, {'field': 'total_amount', 'old_value': 8453.4, 'new_value': 18003.04}, {'field': 'order_count', 'old_value': 447, 'new_value': 1355}]
2025-05-03 00:00:54,753 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-03 00:00:55,176 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-03 00:00:55,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31698.31, 'new_value': 54388.05}, {'field': 'total_amount', 'old_value': 31698.31, 'new_value': 54388.05}, {'field': 'order_count', 'old_value': 136, 'new_value': 242}]
2025-05-03 00:00:55,176 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M
2025-05-03 00:00:55,614 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M
2025-05-03 00:00:55,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 255.24, 'new_value': 798.55}, {'field': 'offline_amount', 'old_value': 2783.78, 'new_value': 5988.6}, {'field': 'total_amount', 'old_value': 3039.02, 'new_value': 6787.15}, {'field': 'order_count', 'old_value': 132, 'new_value': 281}]
2025-05-03 00:00:55,614 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-03 00:00:55,973 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-03 00:00:55,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11941.0, 'new_value': 17259.2}, {'field': 'total_amount', 'old_value': 11941.0, 'new_value': 17259.2}, {'field': 'order_count', 'old_value': 102, 'new_value': 149}]
2025-05-03 00:00:55,973 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-03 00:00:56,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-03 00:00:56,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2891.4, 'new_value': 6650.15}, {'field': 'offline_amount', 'old_value': 19545.76, 'new_value': 36320.08}, {'field': 'total_amount', 'old_value': 22437.16, 'new_value': 42970.23}, {'field': 'order_count', 'old_value': 629, 'new_value': 1279}]
2025-05-03 00:00:56,427 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-03 00:00:56,849 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-03 00:00:56,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22121.77, 'new_value': 41299.89}, {'field': 'offline_amount', 'old_value': 1067.8, 'new_value': 2125.1}, {'field': 'total_amount', 'old_value': 23189.57, 'new_value': 43424.99}, {'field': 'order_count', 'old_value': 843, 'new_value': 1561}]
2025-05-03 00:00:56,849 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-03 00:00:57,256 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-03 00:00:57,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4955.0, 'new_value': 12943.0}, {'field': 'total_amount', 'old_value': 5892.6, 'new_value': 13880.6}, {'field': 'order_count', 'old_value': 15, 'new_value': 24}]
2025-05-03 00:00:57,256 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-03 00:00:57,725 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-03 00:00:57,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6387.62, 'new_value': 13368.24}, {'field': 'offline_amount', 'old_value': 21741.51, 'new_value': 38206.87}, {'field': 'total_amount', 'old_value': 28129.13, 'new_value': 51575.11}, {'field': 'order_count', 'old_value': 286, 'new_value': 534}]
2025-05-03 00:00:57,725 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-03 00:00:58,210 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-03 00:00:58,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2865.88, 'new_value': 5711.93}, {'field': 'offline_amount', 'old_value': 21682.7, 'new_value': 37307.54}, {'field': 'total_amount', 'old_value': 24548.58, 'new_value': 43019.47}, {'field': 'order_count', 'old_value': 326, 'new_value': 561}]
2025-05-03 00:00:58,210 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-03 00:00:58,570 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-03 00:00:58,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9389.3, 'new_value': 12853.8}, {'field': 'total_amount', 'old_value': 9389.3, 'new_value': 12853.8}, {'field': 'order_count', 'old_value': 21, 'new_value': 32}]
2025-05-03 00:00:58,570 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-03 00:00:58,961 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-03 00:00:58,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1286.84, 'new_value': 3890.36}, {'field': 'total_amount', 'old_value': 1286.84, 'new_value': 3890.36}, {'field': 'order_count', 'old_value': 28, 'new_value': 78}]
2025-05-03 00:00:58,961 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-03 00:00:59,461 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-03 00:00:59,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488.0, 'new_value': 686.0}, {'field': 'total_amount', 'old_value': 488.0, 'new_value': 686.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-03 00:00:59,461 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-03 00:00:59,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-03 00:00:59,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2100.0, 'new_value': 3193.0}, {'field': 'total_amount', 'old_value': 2100.0, 'new_value': 3193.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 31}]
2025-05-03 00:00:59,931 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-03 00:01:00,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-03 00:01:00,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5477.0, 'new_value': 7754.0}, {'field': 'total_amount', 'old_value': 5477.0, 'new_value': 7754.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-03 00:01:00,337 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-03 00:01:00,791 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-03 00:01:00,791 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2416.0, 'new_value': 5840.0}, {'field': 'total_amount', 'old_value': 2416.0, 'new_value': 5840.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 61}]
2025-05-03 00:01:00,791 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-03 00:01:01,198 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-03 00:01:01,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44905.0, 'new_value': 47643.0}, {'field': 'total_amount', 'old_value': 44905.0, 'new_value': 47643.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-03 00:01:01,198 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-03 00:01:01,573 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-03 00:01:01,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4627.95, 'new_value': 8260.02}, {'field': 'offline_amount', 'old_value': 12891.7, 'new_value': 23258.0}, {'field': 'total_amount', 'old_value': 17519.65, 'new_value': 31518.02}, {'field': 'order_count', 'old_value': 155, 'new_value': 266}]
2025-05-03 00:01:01,573 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-03 00:01:02,011 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-03 00:01:02,011 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87126.05, 'new_value': 161852.08}, {'field': 'offline_amount', 'old_value': 1462.0, 'new_value': 2174.0}, {'field': 'total_amount', 'old_value': 88588.05, 'new_value': 164026.08}, {'field': 'order_count', 'old_value': 262, 'new_value': 520}]
2025-05-03 00:01:02,011 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-03 00:01:02,418 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-03 00:01:02,418 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3199.0, 'new_value': 4498.0}, {'field': 'total_amount', 'old_value': 3199.0, 'new_value': 4498.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:02,418 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-03 00:01:02,824 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-03 00:01:02,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 358.4, 'new_value': 875.1}, {'field': 'offline_amount', 'old_value': 2523.0, 'new_value': 4375.0}, {'field': 'total_amount', 'old_value': 2881.4, 'new_value': 5250.1}, {'field': 'order_count', 'old_value': 10, 'new_value': 20}]
2025-05-03 00:01:02,824 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-03 00:01:03,200 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-03 00:01:03,200 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 871.0, 'new_value': 1540.0}, {'field': 'total_amount', 'old_value': 871.0, 'new_value': 1540.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 18}]
2025-05-03 00:01:03,200 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-03 00:01:03,669 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-03 00:01:03,669 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 242.0, 'new_value': 330.0}, {'field': 'offline_amount', 'old_value': 4510.0, 'new_value': 26478.0}, {'field': 'total_amount', 'old_value': 4752.0, 'new_value': 26808.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 21}]
2025-05-03 00:01:03,669 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-03 00:01:04,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-03 00:01:04,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2960.0, 'new_value': 4865.0}, {'field': 'total_amount', 'old_value': 2960.0, 'new_value': 4865.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 22}]
2025-05-03 00:01:04,060 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-03 00:01:04,467 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-03 00:01:04,467 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10123.2, 'new_value': 16907.5}, {'field': 'offline_amount', 'old_value': 16680.7, 'new_value': 26697.3}, {'field': 'total_amount', 'old_value': 26803.9, 'new_value': 43604.8}, {'field': 'order_count', 'old_value': 536, 'new_value': 872}]
2025-05-03 00:01:04,467 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-03 00:01:04,826 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-03 00:01:04,826 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33691.26, 'new_value': 64452.52}, {'field': 'total_amount', 'old_value': 33691.26, 'new_value': 64452.52}, {'field': 'order_count', 'old_value': 399, 'new_value': 788}]
2025-05-03 00:01:04,826 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-03 00:01:05,233 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-03 00:01:05,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17858.0, 'new_value': 32722.0}, {'field': 'total_amount', 'old_value': 17858.0, 'new_value': 32722.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 48}]
2025-05-03 00:01:05,233 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-03 00:01:05,890 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-03 00:01:05,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28512.26, 'new_value': 61832.34}, {'field': 'total_amount', 'old_value': 28512.26, 'new_value': 61832.34}, {'field': 'order_count', 'old_value': 61, 'new_value': 126}]
2025-05-03 00:01:05,890 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-03 00:01:06,375 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-03 00:01:06,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5459.02, 'new_value': 10811.4}, {'field': 'total_amount', 'old_value': 5459.02, 'new_value': 10811.4}, {'field': 'order_count', 'old_value': 181, 'new_value': 341}]
2025-05-03 00:01:06,375 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-03 00:01:06,782 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-03 00:01:06,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 416.14, 'new_value': 723.08}, {'field': 'offline_amount', 'old_value': 2242.41, 'new_value': 4394.61}, {'field': 'total_amount', 'old_value': 2658.55, 'new_value': 5117.69}, {'field': 'order_count', 'old_value': 105, 'new_value': 191}]
2025-05-03 00:01:06,782 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-03 00:01:07,204 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-03 00:01:07,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14488.69, 'new_value': 24494.78}, {'field': 'total_amount', 'old_value': 14488.69, 'new_value': 24494.78}, {'field': 'order_count', 'old_value': 342, 'new_value': 601}]
2025-05-03 00:01:07,204 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-03 00:01:07,673 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-03 00:01:07,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2821.75, 'new_value': 5407.58}, {'field': 'offline_amount', 'old_value': 29915.35, 'new_value': 53212.22}, {'field': 'total_amount', 'old_value': 32737.1, 'new_value': 58619.8}, {'field': 'order_count', 'old_value': 772, 'new_value': 1393}]
2025-05-03 00:01:07,673 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-03 00:01:08,174 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-03 00:01:08,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38465.6, 'new_value': 73073.9}, {'field': 'total_amount', 'old_value': 38465.6, 'new_value': 73073.9}, {'field': 'order_count', 'old_value': 173, 'new_value': 307}]
2025-05-03 00:01:08,174 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-03 00:01:08,643 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-03 00:01:08,643 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6751.73, 'new_value': 14059.17}, {'field': 'offline_amount', 'old_value': 5495.58, 'new_value': 9017.21}, {'field': 'total_amount', 'old_value': 12247.31, 'new_value': 23076.38}, {'field': 'order_count', 'old_value': 598, 'new_value': 1300}]
2025-05-03 00:01:08,643 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-03 00:01:09,097 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-03 00:01:09,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23800.0, 'new_value': 35822.0}, {'field': 'total_amount', 'old_value': 23800.0, 'new_value': 35822.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-03 00:01:09,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-03 00:01:09,519 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-03 00:01:09,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66001.0, 'new_value': 125549.45}, {'field': 'total_amount', 'old_value': 66001.0, 'new_value': 125549.45}, {'field': 'order_count', 'old_value': 1336, 'new_value': 2499}]
2025-05-03 00:01:09,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-03 00:01:10,019 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-03 00:01:10,019 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18554.95, 'new_value': 35036.13}, {'field': 'total_amount', 'old_value': 18554.95, 'new_value': 35036.13}, {'field': 'order_count', 'old_value': 742, 'new_value': 1379}]
2025-05-03 00:01:10,019 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-03 00:01:10,410 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-03 00:01:10,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40785.52, 'new_value': 79882.62}, {'field': 'total_amount', 'old_value': 40785.52, 'new_value': 79882.62}, {'field': 'order_count', 'old_value': 256, 'new_value': 517}]
2025-05-03 00:01:10,410 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-03 00:01:10,817 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-03 00:01:10,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26946.0, 'new_value': 49011.0}, {'field': 'total_amount', 'old_value': 26946.0, 'new_value': 49011.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 128}]
2025-05-03 00:01:10,817 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-03 00:01:11,302 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-03 00:01:11,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31920.0, 'new_value': 67539.0}, {'field': 'total_amount', 'old_value': 31920.0, 'new_value': 67539.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 22}]
2025-05-03 00:01:11,302 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-03 00:01:11,740 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-03 00:01:11,740 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54.49, 'new_value': 165.73}, {'field': 'offline_amount', 'old_value': 2510.41, 'new_value': 5255.42}, {'field': 'total_amount', 'old_value': 2564.9, 'new_value': 5421.15}, {'field': 'order_count', 'old_value': 98, 'new_value': 195}]
2025-05-03 00:01:11,740 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-03 00:01:12,225 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-03 00:01:12,225 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53280.4, 'new_value': 109253.4}, {'field': 'total_amount', 'old_value': 53280.4, 'new_value': 109253.4}, {'field': 'order_count', 'old_value': 344, 'new_value': 678}]
2025-05-03 00:01:12,225 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-03 00:01:12,647 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-03 00:01:12,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7910.75, 'new_value': 17567.86}, {'field': 'total_amount', 'old_value': 7910.75, 'new_value': 17567.86}, {'field': 'order_count', 'old_value': 43, 'new_value': 95}]
2025-05-03 00:01:12,647 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-03 00:01:13,085 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-03 00:01:13,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3294.6, 'new_value': 6935.47}, {'field': 'offline_amount', 'old_value': 8143.37, 'new_value': 13987.96}, {'field': 'total_amount', 'old_value': 11437.97, 'new_value': 20923.43}, {'field': 'order_count', 'old_value': 515, 'new_value': 937}]
2025-05-03 00:01:13,085 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-03 00:01:13,461 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-03 00:01:13,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27937.17, 'new_value': 50712.91}, {'field': 'total_amount', 'old_value': 27937.17, 'new_value': 50712.91}, {'field': 'order_count', 'old_value': 1085, 'new_value': 1980}]
2025-05-03 00:01:13,461 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-03 00:01:13,899 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-03 00:01:13,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': -24.37, 'new_value': 157.07}, {'field': 'offline_amount', 'old_value': 21579.8, 'new_value': 37073.3}, {'field': 'total_amount', 'old_value': 21555.43, 'new_value': 37230.37}, {'field': 'order_count', 'old_value': 620, 'new_value': 1094}]
2025-05-03 00:01:13,899 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-03 00:01:14,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-03 00:01:14,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26526.3, 'new_value': 41763.4}, {'field': 'total_amount', 'old_value': 26526.3, 'new_value': 41763.4}, {'field': 'order_count', 'old_value': 44, 'new_value': 68}]
2025-05-03 00:01:14,337 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-03 00:01:14,775 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-03 00:01:14,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 19173.43}, {'field': 'total_amount', 'old_value': 25132.68, 'new_value': 44306.11}, {'field': 'order_count', 'old_value': 523, 'new_value': 925}]
2025-05-03 00:01:14,775 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-03 00:01:15,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-03 00:01:15,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33727.0, 'new_value': 114275.0}, {'field': 'total_amount', 'old_value': 33727.0, 'new_value': 114275.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 120}]
2025-05-03 00:01:15,181 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-03 00:01:15,666 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-03 00:01:15,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67990.0, 'new_value': 121383.0}, {'field': 'total_amount', 'old_value': 67990.0, 'new_value': 121383.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 13}]
2025-05-03 00:01:15,666 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-03 00:01:16,088 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-03 00:01:16,088 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 350000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 350000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:16,088 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-03 00:01:16,542 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-03 00:01:16,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29000.0, 'new_value': 38000.0}, {'field': 'total_amount', 'old_value': 29000.0, 'new_value': 38000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:16,542 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-03 00:01:17,074 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-03 00:01:17,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3038.0, 'new_value': 5259.0}, {'field': 'total_amount', 'old_value': 3038.0, 'new_value': 5259.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 26}]
2025-05-03 00:01:17,074 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-03 00:01:17,606 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-03 00:01:17,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20848.23, 'new_value': 22263.93}, {'field': 'offline_amount', 'old_value': 2004.49, 'new_value': 16076.43}, {'field': 'total_amount', 'old_value': 22852.72, 'new_value': 38340.36}, {'field': 'order_count', 'old_value': 291, 'new_value': 497}]
2025-05-03 00:01:17,606 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-03 00:01:18,028 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-03 00:01:18,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45916.7, 'new_value': 84963.83}, {'field': 'total_amount', 'old_value': 45916.7, 'new_value': 84963.83}, {'field': 'order_count', 'old_value': 356, 'new_value': 715}]
2025-05-03 00:01:18,028 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-03 00:01:18,419 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-03 00:01:18,419 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1248.58, 'new_value': 3455.32}, {'field': 'offline_amount', 'old_value': 30819.86, 'new_value': 54939.46}, {'field': 'total_amount', 'old_value': 32068.44, 'new_value': 58394.78}, {'field': 'order_count', 'old_value': 187, 'new_value': 344}]
2025-05-03 00:01:18,419 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-03 00:01:18,841 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-03 00:01:18,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15307.44, 'new_value': 28011.6}, {'field': 'total_amount', 'old_value': 15307.44, 'new_value': 28011.6}, {'field': 'order_count', 'old_value': 603, 'new_value': 1116}]
2025-05-03 00:01:18,841 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-03 00:01:19,279 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-03 00:01:19,279 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15507.69, 'new_value': 29259.52}, {'field': 'total_amount', 'old_value': 15507.69, 'new_value': 29259.52}, {'field': 'order_count', 'old_value': 106, 'new_value': 215}]
2025-05-03 00:01:19,279 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-03 00:01:19,655 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-03 00:01:19,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10654.13}, {'field': 'total_amount', 'old_value': 11620.6, 'new_value': 22274.73}, {'field': 'order_count', 'old_value': 366, 'new_value': 708}]
2025-05-03 00:01:19,655 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-03 00:01:20,093 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-03 00:01:20,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:20,093 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-03 00:01:20,531 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-03 00:01:20,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 80000.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 80000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:20,531 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-03 00:01:20,984 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-03 00:01:20,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300000.0, 'new_value': 600000.0}, {'field': 'total_amount', 'old_value': 300000.0, 'new_value': 600000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:20,984 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-03 00:01:21,360 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-03 00:01:21,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1889.66, 'new_value': 3452.89}, {'field': 'offline_amount', 'old_value': 2637.54, 'new_value': 4911.53}, {'field': 'total_amount', 'old_value': 4527.2, 'new_value': 8364.42}, {'field': 'order_count', 'old_value': 148, 'new_value': 291}]
2025-05-03 00:01:21,360 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-03 00:01:21,782 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-03 00:01:21,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13232.0, 'new_value': 31121.0}, {'field': 'total_amount', 'old_value': 13232.0, 'new_value': 31121.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 57}]
2025-05-03 00:01:21,782 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-03 00:01:22,282 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-03 00:01:22,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2586.27, 'new_value': 5428.37}, {'field': 'offline_amount', 'old_value': 13635.34, 'new_value': 24983.79}, {'field': 'total_amount', 'old_value': 16221.61, 'new_value': 30412.16}, {'field': 'order_count', 'old_value': 595, 'new_value': 1061}]
2025-05-03 00:01:22,282 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-03 00:01:22,689 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-03 00:01:22,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 895.0, 'new_value': 2147.0}, {'field': 'total_amount', 'old_value': 895.0, 'new_value': 2147.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 10}]
2025-05-03 00:01:22,689 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-03 00:01:23,127 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-03 00:01:23,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7922.09, 'new_value': 14208.75}, {'field': 'offline_amount', 'old_value': 4673.83, 'new_value': 8501.12}, {'field': 'total_amount', 'old_value': 12595.92, 'new_value': 22709.87}, {'field': 'order_count', 'old_value': 611, 'new_value': 1140}]
2025-05-03 00:01:23,127 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-03 00:01:23,534 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-03 00:01:23,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13149.0, 'new_value': 26679.05}, {'field': 'offline_amount', 'old_value': 52000.0, 'new_value': 86000.0}, {'field': 'total_amount', 'old_value': 65149.0, 'new_value': 112679.05}, {'field': 'order_count', 'old_value': 92, 'new_value': 168}]
2025-05-03 00:01:23,534 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-03 00:01:23,940 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-03 00:01:23,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2002.23, 'new_value': 3839.7}, {'field': 'offline_amount', 'old_value': 2630.64, 'new_value': 4452.84}, {'field': 'total_amount', 'old_value': 4632.87, 'new_value': 8292.54}, {'field': 'order_count', 'old_value': 163, 'new_value': 316}]
2025-05-03 00:01:23,940 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-03 00:01:24,347 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-03 00:01:24,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 901.37, 'new_value': 1646.26}, {'field': 'offline_amount', 'old_value': 3218.1, 'new_value': 6120.5}, {'field': 'total_amount', 'old_value': 4119.47, 'new_value': 7766.76}, {'field': 'order_count', 'old_value': 151, 'new_value': 304}]
2025-05-03 00:01:24,347 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-03 00:01:24,738 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-03 00:01:24,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45765.0, 'new_value': 81295.0}, {'field': 'total_amount', 'old_value': 45765.0, 'new_value': 81295.0}]
2025-05-03 00:01:24,738 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-03 00:01:25,161 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-03 00:01:25,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44873.0, 'new_value': 46944.0}, {'field': 'total_amount', 'old_value': 44873.0, 'new_value': 46944.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-03 00:01:25,161 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-03 00:01:25,583 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-03 00:01:25,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30987.0, 'new_value': 64396.0}, {'field': 'total_amount', 'old_value': 30987.0, 'new_value': 64396.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 55}]
2025-05-03 00:01:25,583 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-03 00:01:26,052 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-03 00:01:26,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22605.0, 'new_value': 40079.0}, {'field': 'total_amount', 'old_value': 22605.0, 'new_value': 40079.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 492}]
2025-05-03 00:01:26,052 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-03 00:01:26,474 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-03 00:01:26,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163.0, 'new_value': 416.0}, {'field': 'offline_amount', 'old_value': 2470.8, 'new_value': 4775.8}, {'field': 'total_amount', 'old_value': 2633.8, 'new_value': 5191.8}, {'field': 'order_count', 'old_value': 97, 'new_value': 186}]
2025-05-03 00:01:26,474 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-03 00:01:26,881 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-03 00:01:26,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95957.0, 'new_value': 185589.0}, {'field': 'total_amount', 'old_value': 95957.0, 'new_value': 185589.0}, {'field': 'order_count', 'old_value': 396, 'new_value': 759}]
2025-05-03 00:01:26,881 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-03 00:01:27,303 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-03 00:01:27,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270705.32, 'new_value': 524337.89}, {'field': 'total_amount', 'old_value': 270705.32, 'new_value': 524337.89}, {'field': 'order_count', 'old_value': 413, 'new_value': 814}]
2025-05-03 00:01:27,303 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-03 00:01:27,741 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-03 00:01:27,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27657.0, 'new_value': 32609.0}, {'field': 'total_amount', 'old_value': 27657.0, 'new_value': 32609.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-03 00:01:27,741 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-03 00:01:28,148 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-03 00:01:28,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3980.0, 'new_value': 5280.0}, {'field': 'total_amount', 'old_value': 3980.0, 'new_value': 5280.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:28,148 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-03 00:01:28,555 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-03 00:01:28,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162.0, 'new_value': 907.6}, {'field': 'total_amount', 'old_value': 162.0, 'new_value': 907.6}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 00:01:28,555 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-03 00:01:29,024 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-03 00:01:29,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10569.28, 'new_value': 20486.38}, {'field': 'offline_amount', 'old_value': 27600.51, 'new_value': 41869.71}, {'field': 'total_amount', 'old_value': 38169.79, 'new_value': 62356.09}, {'field': 'order_count', 'old_value': 104, 'new_value': 171}]
2025-05-03 00:01:29,024 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-03 00:01:29,446 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-03 00:01:29,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2019.0, 'new_value': 2756.0}, {'field': 'total_amount', 'old_value': 2019.0, 'new_value': 2756.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-03 00:01:29,446 - INFO - 开始批量插入 1 条新记录
2025-05-03 00:01:29,603 - INFO - 批量插入响应状态码: 200
2025-05-03 00:01:29,603 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 02 May 2025 16:01:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D946BF1A-8A52-75BF-85DA-94DC379F512F', 'x-acs-trace-id': '5d99b499856d331bcc357dbccca42375', 'etag': '688k4DWpHZd1nO8pl557Stg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-03 00:01:29,603 - INFO - 批量插入响应体: {'result': ['FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB']}
2025-05-03 00:01:29,603 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-03 00:01:29,603 - INFO - 成功插入的数据ID: ['FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB']
2025-05-03 00:01:32,622 - INFO - 批量插入完成，共 1 条记录
2025-05-03 00:01:32,622 - INFO - 日期 2025-05 处理完成 - 更新: 139 条，插入: 1 条，错误: 0 条
2025-05-03 00:01:32,622 - INFO - 数据同步完成！更新: 140 条，插入: 1 条，错误: 0 条
2025-05-03 00:01:32,622 - INFO - =================同步完成====================
2025-05-03 03:00:04,070 - INFO - =================使用默认全量同步=============
2025-05-03 03:00:05,243 - INFO - MySQL查询成功，共获取 3196 条记录
2025-05-03 03:00:05,243 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 03:00:05,259 - INFO - 开始处理日期: 2025-01
2025-05-03 03:00:05,274 - INFO - Request Parameters - Page 1:
2025-05-03 03:00:05,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:05,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:06,181 - INFO - Response - Page 1:
2025-05-03 03:00:06,385 - INFO - 第 1 页获取到 100 条记录
2025-05-03 03:00:06,385 - INFO - Request Parameters - Page 2:
2025-05-03 03:00:06,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:06,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:07,104 - INFO - Response - Page 2:
2025-05-03 03:00:07,308 - INFO - 第 2 页获取到 100 条记录
2025-05-03 03:00:07,308 - INFO - Request Parameters - Page 3:
2025-05-03 03:00:07,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:07,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:07,793 - INFO - Response - Page 3:
2025-05-03 03:00:07,996 - INFO - 第 3 页获取到 100 条记录
2025-05-03 03:00:07,996 - INFO - Request Parameters - Page 4:
2025-05-03 03:00:07,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:07,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:08,512 - INFO - Response - Page 4:
2025-05-03 03:00:08,715 - INFO - 第 4 页获取到 100 条记录
2025-05-03 03:00:08,715 - INFO - Request Parameters - Page 5:
2025-05-03 03:00:08,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:08,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:09,138 - INFO - Response - Page 5:
2025-05-03 03:00:09,341 - INFO - 第 5 页获取到 100 条记录
2025-05-03 03:00:09,341 - INFO - Request Parameters - Page 6:
2025-05-03 03:00:09,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:09,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:09,795 - INFO - Response - Page 6:
2025-05-03 03:00:09,998 - INFO - 第 6 页获取到 100 条记录
2025-05-03 03:00:09,998 - INFO - Request Parameters - Page 7:
2025-05-03 03:00:09,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:09,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:10,546 - INFO - Response - Page 7:
2025-05-03 03:00:10,749 - INFO - 第 7 页获取到 82 条记录
2025-05-03 03:00:10,749 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 03:00:10,749 - INFO - 获取到 682 条表单数据
2025-05-03 03:00:10,749 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 03:00:10,765 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 03:00:10,765 - INFO - 开始处理日期: 2025-02
2025-05-03 03:00:10,765 - INFO - Request Parameters - Page 1:
2025-05-03 03:00:10,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:10,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:11,218 - INFO - Response - Page 1:
2025-05-03 03:00:11,421 - INFO - 第 1 页获取到 100 条记录
2025-05-03 03:00:11,421 - INFO - Request Parameters - Page 2:
2025-05-03 03:00:11,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:11,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:11,922 - INFO - Response - Page 2:
2025-05-03 03:00:12,125 - INFO - 第 2 页获取到 100 条记录
2025-05-03 03:00:12,125 - INFO - Request Parameters - Page 3:
2025-05-03 03:00:12,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:12,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:12,610 - INFO - Response - Page 3:
2025-05-03 03:00:12,814 - INFO - 第 3 页获取到 100 条记录
2025-05-03 03:00:12,814 - INFO - Request Parameters - Page 4:
2025-05-03 03:00:12,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:12,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:13,330 - INFO - Response - Page 4:
2025-05-03 03:00:13,533 - INFO - 第 4 页获取到 100 条记录
2025-05-03 03:00:13,533 - INFO - Request Parameters - Page 5:
2025-05-03 03:00:13,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:13,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:14,034 - INFO - Response - Page 5:
2025-05-03 03:00:14,237 - INFO - 第 5 页获取到 100 条记录
2025-05-03 03:00:14,237 - INFO - Request Parameters - Page 6:
2025-05-03 03:00:14,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:14,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:14,706 - INFO - Response - Page 6:
2025-05-03 03:00:14,910 - INFO - 第 6 页获取到 100 条记录
2025-05-03 03:00:14,910 - INFO - Request Parameters - Page 7:
2025-05-03 03:00:14,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:14,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:15,363 - INFO - Response - Page 7:
2025-05-03 03:00:15,566 - INFO - 第 7 页获取到 70 条记录
2025-05-03 03:00:15,566 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 03:00:15,566 - INFO - 获取到 670 条表单数据
2025-05-03 03:00:15,566 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 03:00:15,582 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 03:00:15,582 - INFO - 开始处理日期: 2025-03
2025-05-03 03:00:15,582 - INFO - Request Parameters - Page 1:
2025-05-03 03:00:15,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:15,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:16,098 - INFO - Response - Page 1:
2025-05-03 03:00:16,302 - INFO - 第 1 页获取到 100 条记录
2025-05-03 03:00:16,302 - INFO - Request Parameters - Page 2:
2025-05-03 03:00:16,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:16,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:16,818 - INFO - Response - Page 2:
2025-05-03 03:00:17,021 - INFO - 第 2 页获取到 100 条记录
2025-05-03 03:00:17,021 - INFO - Request Parameters - Page 3:
2025-05-03 03:00:17,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:17,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:17,475 - INFO - Response - Page 3:
2025-05-03 03:00:17,678 - INFO - 第 3 页获取到 100 条记录
2025-05-03 03:00:17,678 - INFO - Request Parameters - Page 4:
2025-05-03 03:00:17,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:17,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:18,163 - INFO - Response - Page 4:
2025-05-03 03:00:18,366 - INFO - 第 4 页获取到 100 条记录
2025-05-03 03:00:18,366 - INFO - Request Parameters - Page 5:
2025-05-03 03:00:18,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:18,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:18,820 - INFO - Response - Page 5:
2025-05-03 03:00:19,023 - INFO - 第 5 页获取到 100 条记录
2025-05-03 03:00:19,023 - INFO - Request Parameters - Page 6:
2025-05-03 03:00:19,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:19,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:19,446 - INFO - Response - Page 6:
2025-05-03 03:00:19,649 - INFO - 第 6 页获取到 100 条记录
2025-05-03 03:00:19,649 - INFO - Request Parameters - Page 7:
2025-05-03 03:00:19,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:19,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:20,103 - INFO - Response - Page 7:
2025-05-03 03:00:20,306 - INFO - 第 7 页获取到 61 条记录
2025-05-03 03:00:20,306 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 03:00:20,306 - INFO - 获取到 661 条表单数据
2025-05-03 03:00:20,306 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 03:00:20,322 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 03:00:20,322 - INFO - 开始处理日期: 2025-04
2025-05-03 03:00:20,322 - INFO - Request Parameters - Page 1:
2025-05-03 03:00:20,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:20,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:20,806 - INFO - Response - Page 1:
2025-05-03 03:00:21,010 - INFO - 第 1 页获取到 100 条记录
2025-05-03 03:00:21,010 - INFO - Request Parameters - Page 2:
2025-05-03 03:00:21,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:21,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:21,463 - INFO - Response - Page 2:
2025-05-03 03:00:21,667 - INFO - 第 2 页获取到 100 条记录
2025-05-03 03:00:21,667 - INFO - Request Parameters - Page 3:
2025-05-03 03:00:21,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:21,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:22,136 - INFO - Response - Page 3:
2025-05-03 03:00:22,339 - INFO - 第 3 页获取到 100 条记录
2025-05-03 03:00:22,339 - INFO - Request Parameters - Page 4:
2025-05-03 03:00:22,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:22,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:22,824 - INFO - Response - Page 4:
2025-05-03 03:00:23,028 - INFO - 第 4 页获取到 100 条记录
2025-05-03 03:00:23,028 - INFO - Request Parameters - Page 5:
2025-05-03 03:00:23,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:23,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:23,512 - INFO - Response - Page 5:
2025-05-03 03:00:23,716 - INFO - 第 5 页获取到 100 条记录
2025-05-03 03:00:23,716 - INFO - Request Parameters - Page 6:
2025-05-03 03:00:23,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:23,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:24,248 - INFO - Response - Page 6:
2025-05-03 03:00:24,451 - INFO - 第 6 页获取到 100 条记录
2025-05-03 03:00:24,451 - INFO - Request Parameters - Page 7:
2025-05-03 03:00:24,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:24,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:24,826 - INFO - Response - Page 7:
2025-05-03 03:00:25,030 - INFO - 第 7 页获取到 27 条记录
2025-05-03 03:00:25,030 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 03:00:25,030 - INFO - 获取到 627 条表单数据
2025-05-03 03:00:25,030 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 03:00:25,045 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 03:00:25,045 - INFO - 开始处理日期: 2025-05
2025-05-03 03:00:25,045 - INFO - Request Parameters - Page 1:
2025-05-03 03:00:25,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:25,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:25,561 - INFO - Response - Page 1:
2025-05-03 03:00:25,765 - INFO - 第 1 页获取到 100 条记录
2025-05-03 03:00:25,765 - INFO - Request Parameters - Page 2:
2025-05-03 03:00:25,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:25,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:26,250 - INFO - Response - Page 2:
2025-05-03 03:00:26,453 - INFO - 第 2 页获取到 100 条记录
2025-05-03 03:00:26,453 - INFO - Request Parameters - Page 3:
2025-05-03 03:00:26,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:26,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:26,954 - INFO - Response - Page 3:
2025-05-03 03:00:27,157 - INFO - 第 3 页获取到 100 条记录
2025-05-03 03:00:27,157 - INFO - Request Parameters - Page 4:
2025-05-03 03:00:27,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:27,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:27,657 - INFO - Response - Page 4:
2025-05-03 03:00:27,861 - INFO - 第 4 页获取到 100 条记录
2025-05-03 03:00:27,861 - INFO - Request Parameters - Page 5:
2025-05-03 03:00:27,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:27,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:28,377 - INFO - Response - Page 5:
2025-05-03 03:00:28,580 - INFO - 第 5 页获取到 100 条记录
2025-05-03 03:00:28,580 - INFO - Request Parameters - Page 6:
2025-05-03 03:00:28,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 03:00:28,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 03:00:28,940 - INFO - Response - Page 6:
2025-05-03 03:00:29,143 - INFO - 第 6 页获取到 56 条记录
2025-05-03 03:00:29,143 - INFO - 查询完成，共获取到 556 条记录
2025-05-03 03:00:29,143 - INFO - 获取到 556 条表单数据
2025-05-03 03:00:29,143 - INFO - 当前日期 2025-05 有 556 条MySQL数据需要处理
2025-05-03 03:00:29,143 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-03 03:00:29,691 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-03 03:00:29,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4487.0, 'new_value': 8400.0}, {'field': 'total_amount', 'old_value': 4487.0, 'new_value': 8400.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 20}]
2025-05-03 03:00:29,691 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-03 03:00:30,223 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-03 03:00:30,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28620.5, 'new_value': 48513.6}, {'field': 'total_amount', 'old_value': 28620.5, 'new_value': 48513.6}, {'field': 'order_count', 'old_value': 48, 'new_value': 76}]
2025-05-03 03:00:30,223 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-03 03:00:30,692 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-03 03:00:30,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7011.0, 'new_value': 13992.0}, {'field': 'total_amount', 'old_value': 7011.0, 'new_value': 13992.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 111}]
2025-05-03 03:00:30,692 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-03 03:00:31,130 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-03 03:00:31,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15100.0, 'new_value': 39481.0}, {'field': 'total_amount', 'old_value': 15100.0, 'new_value': 39481.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-03 03:00:31,130 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-03 03:00:31,552 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-03 03:00:31,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7210.0, 'new_value': 14420.0}, {'field': 'total_amount', 'old_value': 7210.0, 'new_value': 14420.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 31}]
2025-05-03 03:00:31,552 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-03 03:00:31,990 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-03 03:00:31,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 531.4, 'new_value': 2347.7}, {'field': 'total_amount', 'old_value': 531.4, 'new_value': 2347.7}, {'field': 'order_count', 'old_value': 6, 'new_value': 33}]
2025-05-03 03:00:31,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-03 03:00:32,444 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-03 03:00:32,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9041.0, 'new_value': 15767.0}, {'field': 'offline_amount', 'old_value': 6615.4, 'new_value': 14663.82}, {'field': 'total_amount', 'old_value': 15656.4, 'new_value': 30430.82}, {'field': 'order_count', 'old_value': 100, 'new_value': 204}]
2025-05-03 03:00:32,444 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-03 03:00:32,897 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-03 03:00:32,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117170.4, 'new_value': 194457.9}, {'field': 'total_amount', 'old_value': 117170.4, 'new_value': 194457.9}, {'field': 'order_count', 'old_value': 123, 'new_value': 216}]
2025-05-03 03:00:32,897 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-03 03:00:33,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-03 03:00:33,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15237.27, 'new_value': 29829.06}, {'field': 'offline_amount', 'old_value': 82029.84, 'new_value': 164717.58}, {'field': 'total_amount', 'old_value': 97267.11, 'new_value': 194546.64}, {'field': 'order_count', 'old_value': 459, 'new_value': 919}]
2025-05-03 03:00:33,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-03 03:00:33,789 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-03 03:00:33,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6103.3, 'new_value': 12071.5}, {'field': 'offline_amount', 'old_value': 5727.9, 'new_value': 10767.8}, {'field': 'total_amount', 'old_value': 11831.2, 'new_value': 22839.3}, {'field': 'order_count', 'old_value': 293, 'new_value': 554}]
2025-05-03 03:00:33,789 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-03 03:00:34,196 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-03 03:00:34,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23196.5, 'new_value': 43936.4}, {'field': 'total_amount', 'old_value': 23196.5, 'new_value': 43936.4}, {'field': 'order_count', 'old_value': 128, 'new_value': 229}]
2025-05-03 03:00:34,211 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-03 03:00:34,712 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-03 03:00:34,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10708.7, 'new_value': 21380.65}, {'field': 'offline_amount', 'old_value': 53140.18, 'new_value': 103451.92}, {'field': 'total_amount', 'old_value': 63848.88, 'new_value': 124832.57}, {'field': 'order_count', 'old_value': 302, 'new_value': 581}]
2025-05-03 03:00:34,712 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-03 03:00:35,228 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-03 03:00:35,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5319.0, 'new_value': 5962.0}, {'field': 'total_amount', 'old_value': 5319.0, 'new_value': 5962.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-03 03:00:35,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-03 03:00:35,619 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-03 03:00:35,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177346.0, 'new_value': 345439.0}, {'field': 'total_amount', 'old_value': 177346.0, 'new_value': 345439.0}, {'field': 'order_count', 'old_value': 624, 'new_value': 1217}]
2025-05-03 03:00:35,619 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-03 03:00:36,026 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-03 03:00:36,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24444.27, 'new_value': 49123.46}, {'field': 'total_amount', 'old_value': 24444.27, 'new_value': 49123.46}, {'field': 'order_count', 'old_value': 127, 'new_value': 237}]
2025-05-03 03:00:36,026 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-03 03:00:36,542 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-03 03:00:36,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1487.48, 'new_value': 2985.42}, {'field': 'offline_amount', 'old_value': 4524.11, 'new_value': 8335.07}, {'field': 'total_amount', 'old_value': 6011.59, 'new_value': 11320.49}, {'field': 'order_count', 'old_value': 415, 'new_value': 810}]
2025-05-03 03:00:36,542 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-03 03:00:36,995 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-03 03:00:36,995 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2639.3, 'new_value': 5286.48}, {'field': 'offline_amount', 'old_value': 34345.1, 'new_value': 63188.9}, {'field': 'total_amount', 'old_value': 36984.4, 'new_value': 68475.38}, {'field': 'order_count', 'old_value': 185, 'new_value': 355}]
2025-05-03 03:00:36,995 - INFO - 日期 2025-05 处理完成 - 更新: 17 条，插入: 0 条，错误: 0 条
2025-05-03 03:00:36,995 - INFO - 数据同步完成！更新: 17 条，插入: 0 条，错误: 0 条
2025-05-03 03:00:36,995 - INFO - =================同步完成====================
2025-05-03 06:00:03,956 - INFO - =================使用默认全量同步=============
2025-05-03 06:00:05,129 - INFO - MySQL查询成功，共获取 3196 条记录
2025-05-03 06:00:05,129 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 06:00:05,160 - INFO - 开始处理日期: 2025-01
2025-05-03 06:00:05,160 - INFO - Request Parameters - Page 1:
2025-05-03 06:00:05,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:05,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:06,115 - INFO - Response - Page 1:
2025-05-03 06:00:06,318 - INFO - 第 1 页获取到 100 条记录
2025-05-03 06:00:06,318 - INFO - Request Parameters - Page 2:
2025-05-03 06:00:06,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:06,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:07,037 - INFO - Response - Page 2:
2025-05-03 06:00:07,241 - INFO - 第 2 页获取到 100 条记录
2025-05-03 06:00:07,241 - INFO - Request Parameters - Page 3:
2025-05-03 06:00:07,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:07,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:07,741 - INFO - Response - Page 3:
2025-05-03 06:00:07,945 - INFO - 第 3 页获取到 100 条记录
2025-05-03 06:00:07,945 - INFO - Request Parameters - Page 4:
2025-05-03 06:00:07,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:07,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:08,398 - INFO - Response - Page 4:
2025-05-03 06:00:08,601 - INFO - 第 4 页获取到 100 条记录
2025-05-03 06:00:08,601 - INFO - Request Parameters - Page 5:
2025-05-03 06:00:08,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:08,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:09,118 - INFO - Response - Page 5:
2025-05-03 06:00:09,321 - INFO - 第 5 页获取到 100 条记录
2025-05-03 06:00:09,321 - INFO - Request Parameters - Page 6:
2025-05-03 06:00:09,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:09,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:09,822 - INFO - Response - Page 6:
2025-05-03 06:00:10,041 - INFO - 第 6 页获取到 100 条记录
2025-05-03 06:00:10,041 - INFO - Request Parameters - Page 7:
2025-05-03 06:00:10,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:10,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:10,510 - INFO - Response - Page 7:
2025-05-03 06:00:10,713 - INFO - 第 7 页获取到 82 条记录
2025-05-03 06:00:10,713 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 06:00:10,713 - INFO - 获取到 682 条表单数据
2025-05-03 06:00:10,713 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 06:00:10,729 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 06:00:10,729 - INFO - 开始处理日期: 2025-02
2025-05-03 06:00:10,729 - INFO - Request Parameters - Page 1:
2025-05-03 06:00:10,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:10,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:11,214 - INFO - Response - Page 1:
2025-05-03 06:00:11,417 - INFO - 第 1 页获取到 100 条记录
2025-05-03 06:00:11,417 - INFO - Request Parameters - Page 2:
2025-05-03 06:00:11,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:11,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:11,902 - INFO - Response - Page 2:
2025-05-03 06:00:12,105 - INFO - 第 2 页获取到 100 条记录
2025-05-03 06:00:12,105 - INFO - Request Parameters - Page 3:
2025-05-03 06:00:12,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:12,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:12,747 - INFO - Response - Page 3:
2025-05-03 06:00:12,950 - INFO - 第 3 页获取到 100 条记录
2025-05-03 06:00:12,950 - INFO - Request Parameters - Page 4:
2025-05-03 06:00:12,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:12,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:13,403 - INFO - Response - Page 4:
2025-05-03 06:00:13,607 - INFO - 第 4 页获取到 100 条记录
2025-05-03 06:00:13,607 - INFO - Request Parameters - Page 5:
2025-05-03 06:00:13,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:13,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:14,076 - INFO - Response - Page 5:
2025-05-03 06:00:14,279 - INFO - 第 5 页获取到 100 条记录
2025-05-03 06:00:14,279 - INFO - Request Parameters - Page 6:
2025-05-03 06:00:14,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:14,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:14,764 - INFO - Response - Page 6:
2025-05-03 06:00:14,968 - INFO - 第 6 页获取到 100 条记录
2025-05-03 06:00:14,968 - INFO - Request Parameters - Page 7:
2025-05-03 06:00:14,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:14,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:15,437 - INFO - Response - Page 7:
2025-05-03 06:00:15,640 - INFO - 第 7 页获取到 70 条记录
2025-05-03 06:00:15,640 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 06:00:15,640 - INFO - 获取到 670 条表单数据
2025-05-03 06:00:15,640 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 06:00:15,656 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 06:00:15,656 - INFO - 开始处理日期: 2025-03
2025-05-03 06:00:15,656 - INFO - Request Parameters - Page 1:
2025-05-03 06:00:15,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:15,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:16,141 - INFO - Response - Page 1:
2025-05-03 06:00:16,344 - INFO - 第 1 页获取到 100 条记录
2025-05-03 06:00:16,344 - INFO - Request Parameters - Page 2:
2025-05-03 06:00:16,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:16,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:16,829 - INFO - Response - Page 2:
2025-05-03 06:00:17,032 - INFO - 第 2 页获取到 100 条记录
2025-05-03 06:00:17,032 - INFO - Request Parameters - Page 3:
2025-05-03 06:00:17,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:17,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:17,549 - INFO - Response - Page 3:
2025-05-03 06:00:17,752 - INFO - 第 3 页获取到 100 条记录
2025-05-03 06:00:17,752 - INFO - Request Parameters - Page 4:
2025-05-03 06:00:17,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:17,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:18,205 - INFO - Response - Page 4:
2025-05-03 06:00:18,409 - INFO - 第 4 页获取到 100 条记录
2025-05-03 06:00:18,409 - INFO - Request Parameters - Page 5:
2025-05-03 06:00:18,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:18,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:18,988 - INFO - Response - Page 5:
2025-05-03 06:00:19,191 - INFO - 第 5 页获取到 100 条记录
2025-05-03 06:00:19,191 - INFO - Request Parameters - Page 6:
2025-05-03 06:00:19,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:19,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:19,707 - INFO - Response - Page 6:
2025-05-03 06:00:19,910 - INFO - 第 6 页获取到 100 条记录
2025-05-03 06:00:19,910 - INFO - Request Parameters - Page 7:
2025-05-03 06:00:19,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:19,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:20,364 - INFO - Response - Page 7:
2025-05-03 06:00:20,567 - INFO - 第 7 页获取到 61 条记录
2025-05-03 06:00:20,567 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 06:00:20,567 - INFO - 获取到 661 条表单数据
2025-05-03 06:00:20,567 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 06:00:20,583 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 06:00:20,583 - INFO - 开始处理日期: 2025-04
2025-05-03 06:00:20,583 - INFO - Request Parameters - Page 1:
2025-05-03 06:00:20,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:20,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:21,162 - INFO - Response - Page 1:
2025-05-03 06:00:21,365 - INFO - 第 1 页获取到 100 条记录
2025-05-03 06:00:21,365 - INFO - Request Parameters - Page 2:
2025-05-03 06:00:21,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:21,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:21,803 - INFO - Response - Page 2:
2025-05-03 06:00:22,006 - INFO - 第 2 页获取到 100 条记录
2025-05-03 06:00:22,006 - INFO - Request Parameters - Page 3:
2025-05-03 06:00:22,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:22,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:22,523 - INFO - Response - Page 3:
2025-05-03 06:00:22,726 - INFO - 第 3 页获取到 100 条记录
2025-05-03 06:00:22,726 - INFO - Request Parameters - Page 4:
2025-05-03 06:00:22,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:22,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:23,242 - INFO - Response - Page 4:
2025-05-03 06:00:23,445 - INFO - 第 4 页获取到 100 条记录
2025-05-03 06:00:23,445 - INFO - Request Parameters - Page 5:
2025-05-03 06:00:23,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:23,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:23,883 - INFO - Response - Page 5:
2025-05-03 06:00:24,087 - INFO - 第 5 页获取到 100 条记录
2025-05-03 06:00:24,087 - INFO - Request Parameters - Page 6:
2025-05-03 06:00:24,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:24,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:24,572 - INFO - Response - Page 6:
2025-05-03 06:00:24,775 - INFO - 第 6 页获取到 100 条记录
2025-05-03 06:00:24,775 - INFO - Request Parameters - Page 7:
2025-05-03 06:00:24,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:24,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:25,166 - INFO - Response - Page 7:
2025-05-03 06:00:25,369 - INFO - 第 7 页获取到 27 条记录
2025-05-03 06:00:25,369 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 06:00:25,369 - INFO - 获取到 627 条表单数据
2025-05-03 06:00:25,369 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 06:00:25,385 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 06:00:25,385 - INFO - 开始处理日期: 2025-05
2025-05-03 06:00:25,385 - INFO - Request Parameters - Page 1:
2025-05-03 06:00:25,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:25,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:25,932 - INFO - Response - Page 1:
2025-05-03 06:00:26,136 - INFO - 第 1 页获取到 100 条记录
2025-05-03 06:00:26,136 - INFO - Request Parameters - Page 2:
2025-05-03 06:00:26,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:26,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:26,652 - INFO - Response - Page 2:
2025-05-03 06:00:26,855 - INFO - 第 2 页获取到 100 条记录
2025-05-03 06:00:26,855 - INFO - Request Parameters - Page 3:
2025-05-03 06:00:26,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:26,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:27,387 - INFO - Response - Page 3:
2025-05-03 06:00:27,590 - INFO - 第 3 页获取到 100 条记录
2025-05-03 06:00:27,590 - INFO - Request Parameters - Page 4:
2025-05-03 06:00:27,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:27,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:28,044 - INFO - Response - Page 4:
2025-05-03 06:00:28,247 - INFO - 第 4 页获取到 100 条记录
2025-05-03 06:00:28,247 - INFO - Request Parameters - Page 5:
2025-05-03 06:00:28,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:28,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:28,842 - INFO - Response - Page 5:
2025-05-03 06:00:29,045 - INFO - 第 5 页获取到 100 条记录
2025-05-03 06:00:29,045 - INFO - Request Parameters - Page 6:
2025-05-03 06:00:29,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 06:00:29,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 06:00:29,624 - INFO - Response - Page 6:
2025-05-03 06:00:29,827 - INFO - 第 6 页获取到 56 条记录
2025-05-03 06:00:29,827 - INFO - 查询完成，共获取到 556 条记录
2025-05-03 06:00:29,827 - INFO - 获取到 556 条表单数据
2025-05-03 06:00:29,827 - INFO - 当前日期 2025-05 有 556 条MySQL数据需要处理
2025-05-03 06:00:29,843 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 06:00:29,843 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 06:00:29,843 - INFO - =================同步完成====================
2025-05-03 09:00:04,092 - INFO - =================使用默认全量同步=============
2025-05-03 09:00:05,265 - INFO - MySQL查询成功，共获取 3200 条记录
2025-05-03 09:00:05,265 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 09:00:05,296 - INFO - 开始处理日期: 2025-01
2025-05-03 09:00:05,296 - INFO - Request Parameters - Page 1:
2025-05-03 09:00:05,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:05,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:06,172 - INFO - Response - Page 1:
2025-05-03 09:00:06,375 - INFO - 第 1 页获取到 100 条记录
2025-05-03 09:00:06,375 - INFO - Request Parameters - Page 2:
2025-05-03 09:00:06,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:06,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:07,189 - INFO - Response - Page 2:
2025-05-03 09:00:07,392 - INFO - 第 2 页获取到 100 条记录
2025-05-03 09:00:07,392 - INFO - Request Parameters - Page 3:
2025-05-03 09:00:07,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:07,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:07,861 - INFO - Response - Page 3:
2025-05-03 09:00:08,065 - INFO - 第 3 页获取到 100 条记录
2025-05-03 09:00:08,065 - INFO - Request Parameters - Page 4:
2025-05-03 09:00:08,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:08,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:08,581 - INFO - Response - Page 4:
2025-05-03 09:00:08,784 - INFO - 第 4 页获取到 100 条记录
2025-05-03 09:00:08,784 - INFO - Request Parameters - Page 5:
2025-05-03 09:00:08,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:08,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:09,347 - INFO - Response - Page 5:
2025-05-03 09:00:09,551 - INFO - 第 5 页获取到 100 条记录
2025-05-03 09:00:09,551 - INFO - Request Parameters - Page 6:
2025-05-03 09:00:09,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:09,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:10,051 - INFO - Response - Page 6:
2025-05-03 09:00:10,255 - INFO - 第 6 页获取到 100 条记录
2025-05-03 09:00:10,255 - INFO - Request Parameters - Page 7:
2025-05-03 09:00:10,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:10,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:10,724 - INFO - Response - Page 7:
2025-05-03 09:00:10,927 - INFO - 第 7 页获取到 82 条记录
2025-05-03 09:00:10,927 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 09:00:10,927 - INFO - 获取到 682 条表单数据
2025-05-03 09:00:10,927 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 09:00:10,943 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 09:00:10,943 - INFO - 开始处理日期: 2025-02
2025-05-03 09:00:10,943 - INFO - Request Parameters - Page 1:
2025-05-03 09:00:10,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:10,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:11,396 - INFO - Response - Page 1:
2025-05-03 09:00:11,600 - INFO - 第 1 页获取到 100 条记录
2025-05-03 09:00:11,600 - INFO - Request Parameters - Page 2:
2025-05-03 09:00:11,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:11,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:12,069 - INFO - Response - Page 2:
2025-05-03 09:00:12,272 - INFO - 第 2 页获取到 100 条记录
2025-05-03 09:00:12,272 - INFO - Request Parameters - Page 3:
2025-05-03 09:00:12,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:12,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:12,726 - INFO - Response - Page 3:
2025-05-03 09:00:12,929 - INFO - 第 3 页获取到 100 条记录
2025-05-03 09:00:12,929 - INFO - Request Parameters - Page 4:
2025-05-03 09:00:12,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:12,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:13,445 - INFO - Response - Page 4:
2025-05-03 09:00:13,649 - INFO - 第 4 页获取到 100 条记录
2025-05-03 09:00:13,649 - INFO - Request Parameters - Page 5:
2025-05-03 09:00:13,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:13,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:14,134 - INFO - Response - Page 5:
2025-05-03 09:00:14,337 - INFO - 第 5 页获取到 100 条记录
2025-05-03 09:00:14,337 - INFO - Request Parameters - Page 6:
2025-05-03 09:00:14,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:14,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:14,853 - INFO - Response - Page 6:
2025-05-03 09:00:15,057 - INFO - 第 6 页获取到 100 条记录
2025-05-03 09:00:15,057 - INFO - Request Parameters - Page 7:
2025-05-03 09:00:15,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:15,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:15,494 - INFO - Response - Page 7:
2025-05-03 09:00:15,698 - INFO - 第 7 页获取到 70 条记录
2025-05-03 09:00:15,698 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 09:00:15,698 - INFO - 获取到 670 条表单数据
2025-05-03 09:00:15,698 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 09:00:15,713 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 09:00:15,713 - INFO - 开始处理日期: 2025-03
2025-05-03 09:00:15,713 - INFO - Request Parameters - Page 1:
2025-05-03 09:00:15,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:15,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:16,245 - INFO - Response - Page 1:
2025-05-03 09:00:16,449 - INFO - 第 1 页获取到 100 条记录
2025-05-03 09:00:16,449 - INFO - Request Parameters - Page 2:
2025-05-03 09:00:16,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:16,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:16,902 - INFO - Response - Page 2:
2025-05-03 09:00:17,106 - INFO - 第 2 页获取到 100 条记录
2025-05-03 09:00:17,106 - INFO - Request Parameters - Page 3:
2025-05-03 09:00:17,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:17,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:17,590 - INFO - Response - Page 3:
2025-05-03 09:00:17,794 - INFO - 第 3 页获取到 100 条记录
2025-05-03 09:00:17,794 - INFO - Request Parameters - Page 4:
2025-05-03 09:00:17,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:17,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:18,279 - INFO - Response - Page 4:
2025-05-03 09:00:18,482 - INFO - 第 4 页获取到 100 条记录
2025-05-03 09:00:18,482 - INFO - Request Parameters - Page 5:
2025-05-03 09:00:18,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:18,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:18,983 - INFO - Response - Page 5:
2025-05-03 09:00:19,186 - INFO - 第 5 页获取到 100 条记录
2025-05-03 09:00:19,186 - INFO - Request Parameters - Page 6:
2025-05-03 09:00:19,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:19,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:19,640 - INFO - Response - Page 6:
2025-05-03 09:00:19,843 - INFO - 第 6 页获取到 100 条记录
2025-05-03 09:00:19,843 - INFO - Request Parameters - Page 7:
2025-05-03 09:00:19,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:19,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:20,281 - INFO - Response - Page 7:
2025-05-03 09:00:20,484 - INFO - 第 7 页获取到 61 条记录
2025-05-03 09:00:20,484 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 09:00:20,484 - INFO - 获取到 661 条表单数据
2025-05-03 09:00:20,484 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 09:00:20,500 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 09:00:20,500 - INFO - 开始处理日期: 2025-04
2025-05-03 09:00:20,500 - INFO - Request Parameters - Page 1:
2025-05-03 09:00:20,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:20,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:20,985 - INFO - Response - Page 1:
2025-05-03 09:00:21,188 - INFO - 第 1 页获取到 100 条记录
2025-05-03 09:00:21,188 - INFO - Request Parameters - Page 2:
2025-05-03 09:00:21,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:21,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:21,720 - INFO - Response - Page 2:
2025-05-03 09:00:21,923 - INFO - 第 2 页获取到 100 条记录
2025-05-03 09:00:21,923 - INFO - Request Parameters - Page 3:
2025-05-03 09:00:21,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:21,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:22,439 - INFO - Response - Page 3:
2025-05-03 09:00:22,643 - INFO - 第 3 页获取到 100 条记录
2025-05-03 09:00:22,643 - INFO - Request Parameters - Page 4:
2025-05-03 09:00:22,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:22,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:23,128 - INFO - Response - Page 4:
2025-05-03 09:00:23,331 - INFO - 第 4 页获取到 100 条记录
2025-05-03 09:00:23,331 - INFO - Request Parameters - Page 5:
2025-05-03 09:00:23,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:23,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:23,753 - INFO - Response - Page 5:
2025-05-03 09:00:23,957 - INFO - 第 5 页获取到 100 条记录
2025-05-03 09:00:23,957 - INFO - Request Parameters - Page 6:
2025-05-03 09:00:23,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:23,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:24,395 - INFO - Response - Page 6:
2025-05-03 09:00:24,598 - INFO - 第 6 页获取到 100 条记录
2025-05-03 09:00:24,598 - INFO - Request Parameters - Page 7:
2025-05-03 09:00:24,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:24,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:24,958 - INFO - Response - Page 7:
2025-05-03 09:00:25,161 - INFO - 第 7 页获取到 27 条记录
2025-05-03 09:00:25,161 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 09:00:25,161 - INFO - 获取到 627 条表单数据
2025-05-03 09:00:25,161 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 09:00:25,177 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 09:00:25,177 - INFO - 开始处理日期: 2025-05
2025-05-03 09:00:25,177 - INFO - Request Parameters - Page 1:
2025-05-03 09:00:25,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:25,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:25,693 - INFO - Response - Page 1:
2025-05-03 09:00:25,896 - INFO - 第 1 页获取到 100 条记录
2025-05-03 09:00:25,896 - INFO - Request Parameters - Page 2:
2025-05-03 09:00:25,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:25,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:26,412 - INFO - Response - Page 2:
2025-05-03 09:00:26,616 - INFO - 第 2 页获取到 100 条记录
2025-05-03 09:00:26,616 - INFO - Request Parameters - Page 3:
2025-05-03 09:00:26,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:26,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:27,069 - INFO - Response - Page 3:
2025-05-03 09:00:27,273 - INFO - 第 3 页获取到 100 条记录
2025-05-03 09:00:27,273 - INFO - Request Parameters - Page 4:
2025-05-03 09:00:27,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:27,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:27,742 - INFO - Response - Page 4:
2025-05-03 09:00:27,945 - INFO - 第 4 页获取到 100 条记录
2025-05-03 09:00:27,945 - INFO - Request Parameters - Page 5:
2025-05-03 09:00:27,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:27,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:28,446 - INFO - Response - Page 5:
2025-05-03 09:00:28,649 - INFO - 第 5 页获取到 100 条记录
2025-05-03 09:00:28,649 - INFO - Request Parameters - Page 6:
2025-05-03 09:00:28,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 09:00:28,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 09:00:29,056 - INFO - Response - Page 6:
2025-05-03 09:00:29,259 - INFO - 第 6 页获取到 56 条记录
2025-05-03 09:00:29,259 - INFO - 查询完成，共获取到 556 条记录
2025-05-03 09:00:29,259 - INFO - 获取到 556 条表单数据
2025-05-03 09:00:29,259 - INFO - 当前日期 2025-05 有 560 条MySQL数据需要处理
2025-05-03 09:00:29,259 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-03 09:00:29,807 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-03 09:00:29,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4876.96, 'new_value': 6940.5}, {'field': 'total_amount', 'old_value': 4876.96, 'new_value': 6940.5}, {'field': 'order_count', 'old_value': 10, 'new_value': 19}]
2025-05-03 09:00:29,807 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-03 09:00:30,307 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-03 09:00:30,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 740.0, 'new_value': 1220.0}, {'field': 'total_amount', 'old_value': 740.0, 'new_value': 1220.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 18}]
2025-05-03 09:00:30,307 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-03 09:00:30,823 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-03 09:00:30,823 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1287.05, 'new_value': 2158.55}, {'field': 'offline_amount', 'old_value': 11095.0, 'new_value': 21671.0}, {'field': 'total_amount', 'old_value': 12382.05, 'new_value': 23829.55}, {'field': 'order_count', 'old_value': 296, 'new_value': 464}]
2025-05-03 09:00:30,823 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-03 09:00:31,308 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-03 09:00:31,308 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2497.2, 'new_value': 4145.3}, {'field': 'offline_amount', 'old_value': 6240.0, 'new_value': 10144.0}, {'field': 'total_amount', 'old_value': 8737.2, 'new_value': 14289.3}, {'field': 'order_count', 'old_value': 121, 'new_value': 268}]
2025-05-03 09:00:31,308 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-03 09:00:31,762 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-03 09:00:31,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 276.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 276.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 09:00:31,762 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-03 09:00:32,325 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-03 09:00:32,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3808.0, 'new_value': 8008.0}, {'field': 'total_amount', 'old_value': 3808.0, 'new_value': 8008.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 53}]
2025-05-03 09:00:32,340 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-03 09:00:32,794 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-03 09:00:32,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100.0, 'new_value': 150.0}, {'field': 'offline_amount', 'old_value': 517.0, 'new_value': 1510.0}, {'field': 'total_amount', 'old_value': 617.0, 'new_value': 1660.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 8}]
2025-05-03 09:00:32,794 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-03 09:00:33,216 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-03 09:00:33,216 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 255.56, 'new_value': 1201.86}, {'field': 'offline_amount', 'old_value': 8218.34, 'new_value': 13487.2}, {'field': 'total_amount', 'old_value': 8473.9, 'new_value': 14689.06}, {'field': 'order_count', 'old_value': 172, 'new_value': 318}]
2025-05-03 09:00:33,216 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-03 09:00:33,670 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-03 09:00:33,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 753.25, 'new_value': 2206.55}, {'field': 'total_amount', 'old_value': 753.25, 'new_value': 2206.55}, {'field': 'order_count', 'old_value': 14, 'new_value': 34}]
2025-05-03 09:00:33,670 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-03 09:00:34,108 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-03 09:00:34,108 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39.9, 'new_value': 238.9}, {'field': 'total_amount', 'old_value': 39.9, 'new_value': 238.9}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 09:00:34,108 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-03 09:00:34,562 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-03 09:00:34,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7035.61, 'new_value': 14295.26}, {'field': 'offline_amount', 'old_value': 40747.45, 'new_value': 73824.31}, {'field': 'total_amount', 'old_value': 47783.06, 'new_value': 88119.57}, {'field': 'order_count', 'old_value': 273, 'new_value': 505}]
2025-05-03 09:00:34,562 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-03 09:00:35,015 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-03 09:00:35,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 905.37, 'new_value': 1259.46}, {'field': 'offline_amount', 'old_value': 10682.92, 'new_value': 20383.1}, {'field': 'total_amount', 'old_value': 11588.29, 'new_value': 21642.56}, {'field': 'order_count', 'old_value': 76, 'new_value': 147}]
2025-05-03 09:00:35,015 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-03 09:00:35,516 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-03 09:00:35,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 698.0, 'new_value': 857.0}, {'field': 'total_amount', 'old_value': 698.0, 'new_value': 857.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-03 09:00:35,516 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-03 09:00:35,938 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-03 09:00:35,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4158.0, 'new_value': 5867.0}, {'field': 'total_amount', 'old_value': 4158.0, 'new_value': 5867.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 29}]
2025-05-03 09:00:35,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-03 09:00:36,454 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-03 09:00:36,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8530.0, 'new_value': 18014.0}, {'field': 'total_amount', 'old_value': 8531.0, 'new_value': 18015.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 09:00:36,454 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-03 09:00:36,939 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-03 09:00:36,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17489.7, 'new_value': 29105.69}, {'field': 'total_amount', 'old_value': 17489.7, 'new_value': 29105.69}, {'field': 'order_count', 'old_value': 97, 'new_value': 107}]
2025-05-03 09:00:36,939 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-03 09:00:37,393 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-03 09:00:37,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 129.0}, {'field': 'total_amount', 'old_value': 1226.0, 'new_value': 1355.0}]
2025-05-03 09:00:37,393 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-03 09:00:37,846 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-03 09:00:37,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107627.52, 'new_value': 179169.52}, {'field': 'offline_amount', 'old_value': 10081.0, 'new_value': 26635.0}, {'field': 'total_amount', 'old_value': 117708.52, 'new_value': 205804.52}, {'field': 'order_count', 'old_value': 1328, 'new_value': 2335}]
2025-05-03 09:00:37,846 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-03 09:00:38,363 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-03 09:00:38,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3233.0, 'new_value': 5897.0}, {'field': 'total_amount', 'old_value': 3234.0, 'new_value': 5898.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 51}]
2025-05-03 09:00:38,363 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-03 09:00:38,847 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-03 09:00:38,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 1480.0}, {'field': 'total_amount', 'old_value': 1540.0, 'new_value': 2020.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-03 09:00:38,847 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-03 09:00:39,348 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-03 09:00:39,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13280.3, 'new_value': 24434.7}, {'field': 'total_amount', 'old_value': 13280.3, 'new_value': 24434.7}, {'field': 'order_count', 'old_value': 22, 'new_value': 49}]
2025-05-03 09:00:39,348 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-03 09:00:39,895 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-03 09:00:39,895 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25380.07, 'new_value': 49310.57}, {'field': 'total_amount', 'old_value': 25380.07, 'new_value': 49310.57}, {'field': 'order_count', 'old_value': 131, 'new_value': 266}]
2025-05-03 09:00:39,895 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-03 09:00:40,381 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-03 09:00:40,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2011.12, 'new_value': 6048.12}, {'field': 'offline_amount', 'old_value': 44129.59, 'new_value': 77927.91}, {'field': 'total_amount', 'old_value': 46140.71, 'new_value': 83976.03}, {'field': 'order_count', 'old_value': 402, 'new_value': 711}]
2025-05-03 09:00:40,381 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-03 09:00:40,850 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-03 09:00:40,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4469.0, 'new_value': 5423.0}, {'field': 'total_amount', 'old_value': 4469.0, 'new_value': 5423.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 10}]
2025-05-03 09:00:40,850 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-03 09:00:41,350 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-03 09:00:41,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4282.56, 'new_value': 8006.56}, {'field': 'total_amount', 'old_value': 4282.56, 'new_value': 8006.56}, {'field': 'order_count', 'old_value': 28, 'new_value': 49}]
2025-05-03 09:00:41,350 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-03 09:00:41,819 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-03 09:00:41,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5288.0, 'new_value': 6920.0}, {'field': 'total_amount', 'old_value': 5288.0, 'new_value': 6920.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 28}]
2025-05-03 09:00:41,819 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-03 09:00:42,273 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-03 09:00:42,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25531.0, 'new_value': 62628.0}, {'field': 'total_amount', 'old_value': 25531.0, 'new_value': 62628.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 43}]
2025-05-03 09:00:42,273 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-03 09:00:42,773 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-03 09:00:42,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10665.8, 'new_value': 26665.8}, {'field': 'total_amount', 'old_value': 11369.54, 'new_value': 27369.54}, {'field': 'order_count', 'old_value': 528, 'new_value': 758}]
2025-05-03 09:00:42,773 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-03 09:00:43,258 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-03 09:00:43,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3236.18, 'new_value': 4969.31}, {'field': 'offline_amount', 'old_value': 1114.0, 'new_value': 1939.0}, {'field': 'total_amount', 'old_value': 4350.18, 'new_value': 6908.31}, {'field': 'order_count', 'old_value': 59, 'new_value': 92}]
2025-05-03 09:00:43,258 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-03 09:00:43,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-03 09:00:43,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1969.9, 'new_value': 2966.6}, {'field': 'total_amount', 'old_value': 1969.9, 'new_value': 2966.6}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 09:00:43,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-03 09:00:44,197 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-03 09:00:44,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13197.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13197.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 13197}]
2025-05-03 09:00:44,197 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-03 09:00:44,697 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-03 09:00:44,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 516.0, 'new_value': 1408.0}, {'field': 'total_amount', 'old_value': 516.0, 'new_value': 1408.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 5}]
2025-05-03 09:00:44,697 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-03 09:00:45,229 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-03 09:00:45,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6357.9, 'new_value': 9221.9}, {'field': 'total_amount', 'old_value': 6491.1, 'new_value': 9355.1}, {'field': 'order_count', 'old_value': 20, 'new_value': 29}]
2025-05-03 09:00:45,229 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-03 09:00:45,698 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-03 09:00:45,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6858.0, 'new_value': 15462.0}, {'field': 'total_amount', 'old_value': 6858.0, 'new_value': 15462.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-03 09:00:45,698 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-03 09:00:46,152 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-03 09:00:46,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2900.99, 'new_value': 4886.38}, {'field': 'total_amount', 'old_value': 2900.99, 'new_value': 4886.38}, {'field': 'order_count', 'old_value': 12, 'new_value': 20}]
2025-05-03 09:00:46,152 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-03 09:00:46,684 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-03 09:00:46,684 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1332.39}, {'field': 'offline_amount', 'old_value': 11027.0, 'new_value': 14723.0}, {'field': 'total_amount', 'old_value': 11027.0, 'new_value': 16055.39}, {'field': 'order_count', 'old_value': 12, 'new_value': 18}]
2025-05-03 09:00:46,684 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-03 09:00:47,122 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-03 09:00:47,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66704.55, 'new_value': 130441.55}, {'field': 'total_amount', 'old_value': 66704.55, 'new_value': 130441.55}, {'field': 'order_count', 'old_value': 184, 'new_value': 326}]
2025-05-03 09:00:47,122 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-03 09:00:47,544 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-03 09:00:47,544 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1334.9, 'new_value': 2299.9}, {'field': 'offline_amount', 'old_value': 38.0, 'new_value': 256.0}, {'field': 'total_amount', 'old_value': 1372.9, 'new_value': 2555.9}, {'field': 'order_count', 'old_value': 6, 'new_value': 13}]
2025-05-03 09:00:47,544 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-03 09:00:48,311 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-03 09:00:48,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28207.28, 'new_value': 49457.14}, {'field': 'total_amount', 'old_value': 28207.28, 'new_value': 49457.14}, {'field': 'order_count', 'old_value': 179, 'new_value': 307}]
2025-05-03 09:00:48,311 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-03 09:00:48,749 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-03 09:00:48,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12029.0, 'new_value': 17856.0}, {'field': 'total_amount', 'old_value': 12029.0, 'new_value': 17856.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 87}]
2025-05-03 09:00:48,749 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-03 09:00:49,249 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-03 09:00:49,249 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156.0, 'new_value': 234.0}, {'field': 'offline_amount', 'old_value': 2152.8, 'new_value': 2983.8}, {'field': 'total_amount', 'old_value': 2308.8, 'new_value': 3217.8}, {'field': 'order_count', 'old_value': 25, 'new_value': 41}]
2025-05-03 09:00:49,249 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-03 09:00:49,718 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-03 09:00:49,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4361.0, 'new_value': 4808.0}, {'field': 'total_amount', 'old_value': 4445.0, 'new_value': 4892.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 38}]
2025-05-03 09:00:49,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-03 09:00:50,141 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-03 09:00:50,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2097.24, 'new_value': 3649.78}, {'field': 'offline_amount', 'old_value': 32264.72, 'new_value': 59022.9}, {'field': 'total_amount', 'old_value': 34361.96, 'new_value': 62672.68}, {'field': 'order_count', 'old_value': 143, 'new_value': 252}]
2025-05-03 09:00:50,141 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-03 09:00:50,594 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-03 09:00:50,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208.1, 'new_value': 501.58}, {'field': 'offline_amount', 'old_value': 31078.8, 'new_value': 53663.2}, {'field': 'total_amount', 'old_value': 31286.9, 'new_value': 54164.78}, {'field': 'order_count', 'old_value': 1345, 'new_value': 2468}]
2025-05-03 09:00:50,594 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-03 09:00:51,063 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-03 09:00:51,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1683.8, 'new_value': 3871.9}, {'field': 'offline_amount', 'old_value': 16525.46, 'new_value': 32672.13}, {'field': 'total_amount', 'old_value': 18209.26, 'new_value': 36544.03}, {'field': 'order_count', 'old_value': 111, 'new_value': 212}]
2025-05-03 09:00:51,063 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-03 09:00:51,580 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-03 09:00:51,580 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10032.0, 'new_value': 18076.0}, {'field': 'total_amount', 'old_value': 10032.0, 'new_value': 18076.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 563}]
2025-05-03 09:00:51,580 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-03 09:00:52,018 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-03 09:00:52,018 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23855.0, 'new_value': 39057.0}, {'field': 'total_amount', 'old_value': 23855.0, 'new_value': 39057.0}, {'field': 'order_count', 'old_value': 785, 'new_value': 1271}]
2025-05-03 09:00:52,018 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-03 09:00:52,518 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-03 09:00:52,518 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72.0, 'new_value': 106.0}, {'field': 'offline_amount', 'old_value': 42043.0, 'new_value': 70896.0}, {'field': 'total_amount', 'old_value': 42115.0, 'new_value': 71002.0}, {'field': 'order_count', 'old_value': 173, 'new_value': 310}]
2025-05-03 09:00:52,518 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-03 09:00:53,034 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-03 09:00:53,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26354.6, 'new_value': 49869.44}, {'field': 'offline_amount', 'old_value': 1277.0, 'new_value': 2854.6}, {'field': 'total_amount', 'old_value': 27631.6, 'new_value': 52724.04}, {'field': 'order_count', 'old_value': 53, 'new_value': 96}]
2025-05-03 09:00:53,050 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-03 09:00:53,519 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-03 09:00:53,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43872.0, 'new_value': 85595.0}, {'field': 'total_amount', 'old_value': 43872.0, 'new_value': 85595.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 529}]
2025-05-03 09:00:53,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-03 09:00:53,988 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-03 09:00:53,988 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1180.0, 'new_value': 1837.0}, {'field': 'total_amount', 'old_value': 1180.0, 'new_value': 1837.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 9}]
2025-05-03 09:00:53,988 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-03 09:00:54,473 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-03 09:00:54,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6204.72, 'new_value': 12530.54}, {'field': 'total_amount', 'old_value': 6204.72, 'new_value': 12530.54}, {'field': 'order_count', 'old_value': 278, 'new_value': 566}]
2025-05-03 09:00:54,473 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-03 09:00:54,911 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-03 09:00:54,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13321.0, 'new_value': 22658.7}, {'field': 'total_amount', 'old_value': 13321.0, 'new_value': 22658.7}, {'field': 'order_count', 'old_value': 146, 'new_value': 254}]
2025-05-03 09:00:54,911 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-03 09:00:55,396 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-03 09:00:55,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1879.0, 'new_value': 6089.5}, {'field': 'total_amount', 'old_value': 1879.0, 'new_value': 6089.5}, {'field': 'order_count', 'old_value': 7, 'new_value': 23}]
2025-05-03 09:00:55,396 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-03 09:00:55,865 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-03 09:00:55,865 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1479.5, 'new_value': 2732.6}, {'field': 'offline_amount', 'old_value': 62578.21, 'new_value': 143767.64}, {'field': 'total_amount', 'old_value': 64057.71, 'new_value': 146500.24}, {'field': 'order_count', 'old_value': 241, 'new_value': 485}]
2025-05-03 09:00:55,865 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-03 09:00:56,303 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-03 09:00:56,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2574.15, 'new_value': 5247.85}, {'field': 'total_amount', 'old_value': 2574.15, 'new_value': 5247.85}, {'field': 'order_count', 'old_value': 13, 'new_value': 22}]
2025-05-03 09:00:56,303 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-03 09:00:56,882 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-03 09:00:56,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26160.0, 'new_value': 33868.0}, {'field': 'total_amount', 'old_value': 26160.0, 'new_value': 33868.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-03 09:00:56,882 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-03 09:00:57,320 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-03 09:00:57,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 31239.8}, {'field': 'offline_amount', 'old_value': 90510.4, 'new_value': 120154.4}, {'field': 'total_amount', 'old_value': 90510.4, 'new_value': 151394.2}, {'field': 'order_count', 'old_value': 90, 'new_value': 778}]
2025-05-03 09:00:57,320 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-03 09:00:57,868 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-03 09:00:57,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18732.0, 'new_value': 35785.0}, {'field': 'total_amount', 'old_value': 18732.0, 'new_value': 35785.0}, {'field': 'order_count', 'old_value': 314, 'new_value': 591}]
2025-05-03 09:00:57,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-03 09:00:58,352 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-03 09:00:58,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7876.0, 'new_value': 14749.5}, {'field': 'total_amount', 'old_value': 8697.8, 'new_value': 15571.3}, {'field': 'order_count', 'old_value': 53, 'new_value': 95}]
2025-05-03 09:00:58,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-03 09:00:58,775 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-03 09:00:58,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5024.0, 'new_value': 8800.0}, {'field': 'total_amount', 'old_value': 5024.0, 'new_value': 8800.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 24}]
2025-05-03 09:00:58,775 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-03 09:00:59,275 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-03 09:00:59,275 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13255.25, 'new_value': 24415.47}, {'field': 'offline_amount', 'old_value': 950.6, 'new_value': 1785.8}, {'field': 'total_amount', 'old_value': 14205.85, 'new_value': 26201.27}, {'field': 'order_count', 'old_value': 500, 'new_value': 952}]
2025-05-03 09:00:59,275 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-03 09:00:59,713 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-03 09:00:59,713 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 963.36, 'new_value': 1926.5}, {'field': 'offline_amount', 'old_value': 1936.0, 'new_value': 3659.5}, {'field': 'total_amount', 'old_value': 2899.36, 'new_value': 5586.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 294}]
2025-05-03 09:00:59,713 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-03 09:01:00,167 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-03 09:01:00,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1238.7, 'new_value': 3559.3}, {'field': 'offline_amount', 'old_value': 4695.4, 'new_value': 10303.1}, {'field': 'total_amount', 'old_value': 5934.1, 'new_value': 13862.4}, {'field': 'order_count', 'old_value': 77, 'new_value': 169}]
2025-05-03 09:01:00,183 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-03 09:01:00,730 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-03 09:01:00,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4438.0, 'new_value': 7170.0}, {'field': 'total_amount', 'old_value': 4438.0, 'new_value': 7170.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 34}]
2025-05-03 09:01:00,730 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-03 09:01:01,184 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-03 09:01:01,184 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4320.0, 'new_value': 7305.0}, {'field': 'total_amount', 'old_value': 4320.0, 'new_value': 7305.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 09:01:01,184 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-03 09:01:01,637 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-03 09:01:01,637 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 23160.1}, {'field': 'total_amount', 'old_value': 22487.4, 'new_value': 45647.5}, {'field': 'order_count', 'old_value': 133, 'new_value': 235}]
2025-05-03 09:01:01,637 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-03 09:01:02,122 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-03 09:01:02,122 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8497.86, 'new_value': 14745.86}, {'field': 'offline_amount', 'old_value': 19031.22, 'new_value': 40699.5}, {'field': 'total_amount', 'old_value': 27529.08, 'new_value': 55445.36}, {'field': 'order_count', 'old_value': 180, 'new_value': 376}]
2025-05-03 09:01:02,122 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-03 09:01:02,685 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-03 09:01:02,685 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1804.0, 'new_value': 2450.0}, {'field': 'total_amount', 'old_value': 1804.0, 'new_value': 2450.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 09:01:02,685 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-03 09:01:03,108 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-03 09:01:03,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4958.0, 'new_value': 9707.0}, {'field': 'total_amount', 'old_value': 4958.0, 'new_value': 9707.0}, {'field': 'order_count', 'old_value': 208, 'new_value': 400}]
2025-05-03 09:01:03,108 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-03 09:01:03,577 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-03 09:01:03,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56894.56, 'new_value': 105398.12}, {'field': 'total_amount', 'old_value': 56894.56, 'new_value': 105398.12}, {'field': 'order_count', 'old_value': 347, 'new_value': 679}]
2025-05-03 09:01:03,577 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-03 09:01:04,030 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-03 09:01:04,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3978.48, 'new_value': 6301.09}, {'field': 'total_amount', 'old_value': 3978.48, 'new_value': 6301.09}, {'field': 'order_count', 'old_value': 19, 'new_value': 30}]
2025-05-03 09:01:04,030 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-03 09:01:04,515 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-03 09:01:04,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63.0, 'new_value': 146.0}, {'field': 'offline_amount', 'old_value': 4042.0, 'new_value': 7026.0}, {'field': 'total_amount', 'old_value': 4105.0, 'new_value': 7172.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 56}]
2025-05-03 09:01:04,515 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-03 09:01:04,953 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-03 09:01:04,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 328.0, 'new_value': 614.0}, {'field': 'offline_amount', 'old_value': 850.0, 'new_value': 1436.0}, {'field': 'total_amount', 'old_value': 1178.0, 'new_value': 2050.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 13}]
2025-05-03 09:01:04,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-03 09:01:05,438 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-03 09:01:05,438 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4971.0, 'new_value': 9273.0}, {'field': 'offline_amount', 'old_value': 10618.0, 'new_value': 22504.0}, {'field': 'total_amount', 'old_value': 15589.0, 'new_value': 31777.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 143}]
2025-05-03 09:01:05,438 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-03 09:01:05,907 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-03 09:01:05,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14026.96, 'new_value': 26634.41}, {'field': 'offline_amount', 'old_value': 17358.65, 'new_value': 34123.95}, {'field': 'total_amount', 'old_value': 31385.61, 'new_value': 60758.36}, {'field': 'order_count', 'old_value': 813, 'new_value': 1938}]
2025-05-03 09:01:05,907 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-03 09:01:06,424 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-03 09:01:06,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16859.0, 'new_value': 34713.0}, {'field': 'total_amount', 'old_value': 16859.0, 'new_value': 34713.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 40}]
2025-05-03 09:01:06,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-03 09:01:06,908 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-03 09:01:06,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41192.85, 'new_value': 66755.0}, {'field': 'total_amount', 'old_value': 41192.85, 'new_value': 66755.0}, {'field': 'order_count', 'old_value': 919, 'new_value': 1476}]
2025-05-03 09:01:06,908 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-03 09:01:07,472 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-03 09:01:07,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2457.9, 'new_value': 4617.4}, {'field': 'total_amount', 'old_value': 2457.9, 'new_value': 4617.4}, {'field': 'order_count', 'old_value': 12, 'new_value': 24}]
2025-05-03 09:01:07,472 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-03 09:01:07,956 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-03 09:01:07,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81743.71, 'new_value': 201549.17}, {'field': 'total_amount', 'old_value': 81743.71, 'new_value': 201549.17}, {'field': 'order_count', 'old_value': 356, 'new_value': 606}]
2025-05-03 09:01:07,956 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-03 09:01:08,426 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-03 09:01:08,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10526.45, 'new_value': 19548.34}, {'field': 'total_amount', 'old_value': 10526.45, 'new_value': 19548.34}, {'field': 'order_count', 'old_value': 715, 'new_value': 1291}]
2025-05-03 09:01:08,426 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-03 09:01:08,817 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-03 09:01:08,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57107.0, 'new_value': 105841.0}, {'field': 'total_amount', 'old_value': 57107.0, 'new_value': 105841.0}, {'field': 'order_count', 'old_value': 1268, 'new_value': 2357}]
2025-05-03 09:01:08,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-03 09:01:09,255 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-03 09:01:09,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7949.0, 'new_value': 13987.0}, {'field': 'total_amount', 'old_value': 7949.0, 'new_value': 13987.0}, {'field': 'order_count', 'old_value': 589, 'new_value': 1045}]
2025-05-03 09:01:09,255 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-03 09:01:09,724 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-03 09:01:09,724 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 19.8}, {'field': 'offline_amount', 'old_value': 9153.6, 'new_value': 14943.6}, {'field': 'total_amount', 'old_value': 9153.6, 'new_value': 14963.4}, {'field': 'order_count', 'old_value': 135, 'new_value': 243}]
2025-05-03 09:01:09,724 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-03 09:01:10,193 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-03 09:01:10,193 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2980.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2980.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 09:01:10,193 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-03 09:01:10,678 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-03 09:01:10,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2547.97, 'new_value': 6047.97}, {'field': 'total_amount', 'old_value': 2547.97, 'new_value': 6047.97}, {'field': 'order_count', 'old_value': 23, 'new_value': 53}]
2025-05-03 09:01:10,678 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-03 09:01:11,132 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-03 09:01:11,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 305264.0, 'new_value': 538970.0}, {'field': 'total_amount', 'old_value': 305264.0, 'new_value': 538970.0}, {'field': 'order_count', 'old_value': 5526, 'new_value': 10129}]
2025-05-03 09:01:11,132 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-03 09:01:11,601 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-03 09:01:11,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49177.0, 'new_value': 83981.0}, {'field': 'total_amount', 'old_value': 49177.0, 'new_value': 83981.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 312}]
2025-05-03 09:01:11,601 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-03 09:01:12,023 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-03 09:01:12,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7699.6, 'new_value': 14902.3}, {'field': 'total_amount', 'old_value': 7699.6, 'new_value': 14902.3}, {'field': 'order_count', 'old_value': 172, 'new_value': 347}]
2025-05-03 09:01:12,023 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-03 09:01:12,492 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-03 09:01:12,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1422.0, 'new_value': 3149.0}, {'field': 'total_amount', 'old_value': 1422.0, 'new_value': 3149.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 13}]
2025-05-03 09:01:12,492 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-03 09:01:12,899 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-03 09:01:12,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6185.0, 'new_value': 10735.0}, {'field': 'total_amount', 'old_value': 6185.0, 'new_value': 10735.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 48}]
2025-05-03 09:01:12,899 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-03 09:01:13,384 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-03 09:01:13,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 950.0, 'new_value': 1750.0}, {'field': 'total_amount', 'old_value': 950.0, 'new_value': 1750.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 240}]
2025-05-03 09:01:13,384 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-03 09:01:13,885 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-03 09:01:13,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4326.08, 'new_value': 8310.24}, {'field': 'total_amount', 'old_value': 4326.08, 'new_value': 8310.24}, {'field': 'order_count', 'old_value': 258, 'new_value': 508}]
2025-05-03 09:01:13,900 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-03 09:01:14,323 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-03 09:01:14,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4631.06, 'new_value': 8738.17}, {'field': 'total_amount', 'old_value': 4631.06, 'new_value': 8738.17}, {'field': 'order_count', 'old_value': 274, 'new_value': 510}]
2025-05-03 09:01:14,323 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-03 09:01:14,745 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-03 09:01:14,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1200.0, 'new_value': 2400.0}, {'field': 'total_amount', 'old_value': 1200.0, 'new_value': 2400.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 8}]
2025-05-03 09:01:14,745 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-03 09:01:15,198 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-03 09:01:15,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1340.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1340.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-03 09:01:15,198 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-03 09:01:15,668 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-03 09:01:15,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2796.0, 'new_value': 6225.0}, {'field': 'total_amount', 'old_value': 2796.0, 'new_value': 6225.0}, {'field': 'order_count', 'old_value': 250, 'new_value': 559}]
2025-05-03 09:01:15,668 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-03 09:01:16,121 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-03 09:01:16,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2453.15, 'new_value': 4829.85}, {'field': 'total_amount', 'old_value': 2453.15, 'new_value': 4829.85}, {'field': 'order_count', 'old_value': 41, 'new_value': 76}]
2025-05-03 09:01:16,121 - INFO - 开始批量插入 4 条新记录
2025-05-03 09:01:16,278 - INFO - 批量插入响应状态码: 200
2025-05-03 09:01:16,278 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 03 May 2025 01:00:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '24AA8704-8D5F-7AE1-8BBD-D88A12A394DB', 'x-acs-trace-id': '39b9728ef3a96ca98ae115e6a8cb12f3', 'etag': '2HZl+Gnhnql7pcJccZxgURw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-03 09:01:16,278 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX', 'FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX', 'FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX', 'FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX']}
2025-05-03 09:01:16,278 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-03 09:01:16,278 - INFO - 成功插入的数据ID: ['FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX', 'FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX', 'FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX', 'FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX']
2025-05-03 09:01:19,297 - INFO - 批量插入完成，共 4 条记录
2025-05-03 09:01:19,297 - INFO - 日期 2025-05 处理完成 - 更新: 98 条，插入: 4 条，错误: 0 条
2025-05-03 09:01:19,297 - INFO - 数据同步完成！更新: 98 条，插入: 4 条，错误: 0 条
2025-05-03 09:01:19,297 - INFO - =================同步完成====================
2025-05-03 12:00:03,884 - INFO - =================使用默认全量同步=============
2025-05-03 12:00:05,072 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-03 12:00:05,072 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 12:00:05,104 - INFO - 开始处理日期: 2025-01
2025-05-03 12:00:05,104 - INFO - Request Parameters - Page 1:
2025-05-03 12:00:05,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:05,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:06,027 - INFO - Response - Page 1:
2025-05-03 12:00:06,230 - INFO - 第 1 页获取到 100 条记录
2025-05-03 12:00:06,230 - INFO - Request Parameters - Page 2:
2025-05-03 12:00:06,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:06,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:06,715 - INFO - Response - Page 2:
2025-05-03 12:00:06,918 - INFO - 第 2 页获取到 100 条记录
2025-05-03 12:00:06,918 - INFO - Request Parameters - Page 3:
2025-05-03 12:00:06,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:06,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:07,653 - INFO - Response - Page 3:
2025-05-03 12:00:07,857 - INFO - 第 3 页获取到 100 条记录
2025-05-03 12:00:07,857 - INFO - Request Parameters - Page 4:
2025-05-03 12:00:07,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:07,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:08,326 - INFO - Response - Page 4:
2025-05-03 12:00:08,529 - INFO - 第 4 页获取到 100 条记录
2025-05-03 12:00:08,529 - INFO - Request Parameters - Page 5:
2025-05-03 12:00:08,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:08,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:09,030 - INFO - Response - Page 5:
2025-05-03 12:00:09,233 - INFO - 第 5 页获取到 100 条记录
2025-05-03 12:00:09,233 - INFO - Request Parameters - Page 6:
2025-05-03 12:00:09,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:09,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:09,687 - INFO - Response - Page 6:
2025-05-03 12:00:09,890 - INFO - 第 6 页获取到 100 条记录
2025-05-03 12:00:09,890 - INFO - Request Parameters - Page 7:
2025-05-03 12:00:09,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:09,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:10,344 - INFO - Response - Page 7:
2025-05-03 12:00:10,547 - INFO - 第 7 页获取到 82 条记录
2025-05-03 12:00:10,547 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 12:00:10,547 - INFO - 获取到 682 条表单数据
2025-05-03 12:00:10,547 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 12:00:10,563 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 12:00:10,563 - INFO - 开始处理日期: 2025-02
2025-05-03 12:00:10,563 - INFO - Request Parameters - Page 1:
2025-05-03 12:00:10,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:10,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:11,063 - INFO - Response - Page 1:
2025-05-03 12:00:11,266 - INFO - 第 1 页获取到 100 条记录
2025-05-03 12:00:11,266 - INFO - Request Parameters - Page 2:
2025-05-03 12:00:11,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:11,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:11,720 - INFO - Response - Page 2:
2025-05-03 12:00:11,923 - INFO - 第 2 页获取到 100 条记录
2025-05-03 12:00:11,923 - INFO - Request Parameters - Page 3:
2025-05-03 12:00:11,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:11,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:12,408 - INFO - Response - Page 3:
2025-05-03 12:00:12,612 - INFO - 第 3 页获取到 100 条记录
2025-05-03 12:00:12,612 - INFO - Request Parameters - Page 4:
2025-05-03 12:00:12,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:12,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:13,143 - INFO - Response - Page 4:
2025-05-03 12:00:13,347 - INFO - 第 4 页获取到 100 条记录
2025-05-03 12:00:13,347 - INFO - Request Parameters - Page 5:
2025-05-03 12:00:13,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:13,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:13,972 - INFO - Response - Page 5:
2025-05-03 12:00:14,176 - INFO - 第 5 页获取到 100 条记录
2025-05-03 12:00:14,176 - INFO - Request Parameters - Page 6:
2025-05-03 12:00:14,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:14,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:14,614 - INFO - Response - Page 6:
2025-05-03 12:00:14,817 - INFO - 第 6 页获取到 100 条记录
2025-05-03 12:00:14,817 - INFO - Request Parameters - Page 7:
2025-05-03 12:00:14,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:14,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:15,302 - INFO - Response - Page 7:
2025-05-03 12:00:15,505 - INFO - 第 7 页获取到 70 条记录
2025-05-03 12:00:15,505 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 12:00:15,505 - INFO - 获取到 670 条表单数据
2025-05-03 12:00:15,505 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 12:00:15,521 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 12:00:15,521 - INFO - 开始处理日期: 2025-03
2025-05-03 12:00:15,521 - INFO - Request Parameters - Page 1:
2025-05-03 12:00:15,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:15,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:16,037 - INFO - Response - Page 1:
2025-05-03 12:00:16,240 - INFO - 第 1 页获取到 100 条记录
2025-05-03 12:00:16,240 - INFO - Request Parameters - Page 2:
2025-05-03 12:00:16,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:16,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:16,678 - INFO - Response - Page 2:
2025-05-03 12:00:16,882 - INFO - 第 2 页获取到 100 条记录
2025-05-03 12:00:16,882 - INFO - Request Parameters - Page 3:
2025-05-03 12:00:16,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:16,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:17,398 - INFO - Response - Page 3:
2025-05-03 12:00:17,601 - INFO - 第 3 页获取到 100 条记录
2025-05-03 12:00:17,601 - INFO - Request Parameters - Page 4:
2025-05-03 12:00:17,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:17,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:18,039 - INFO - Response - Page 4:
2025-05-03 12:00:18,243 - INFO - 第 4 页获取到 100 条记录
2025-05-03 12:00:18,243 - INFO - Request Parameters - Page 5:
2025-05-03 12:00:18,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:18,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:18,743 - INFO - Response - Page 5:
2025-05-03 12:00:18,946 - INFO - 第 5 页获取到 100 条记录
2025-05-03 12:00:18,946 - INFO - Request Parameters - Page 6:
2025-05-03 12:00:18,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:18,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:19,447 - INFO - Response - Page 6:
2025-05-03 12:00:19,650 - INFO - 第 6 页获取到 100 条记录
2025-05-03 12:00:19,650 - INFO - Request Parameters - Page 7:
2025-05-03 12:00:19,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:19,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:20,057 - INFO - Response - Page 7:
2025-05-03 12:00:20,260 - INFO - 第 7 页获取到 61 条记录
2025-05-03 12:00:20,260 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 12:00:20,260 - INFO - 获取到 661 条表单数据
2025-05-03 12:00:20,260 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 12:00:20,276 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 12:00:20,276 - INFO - 开始处理日期: 2025-04
2025-05-03 12:00:20,276 - INFO - Request Parameters - Page 1:
2025-05-03 12:00:20,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:20,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:20,761 - INFO - Response - Page 1:
2025-05-03 12:00:20,964 - INFO - 第 1 页获取到 100 条记录
2025-05-03 12:00:20,964 - INFO - Request Parameters - Page 2:
2025-05-03 12:00:20,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:20,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:21,512 - INFO - Response - Page 2:
2025-05-03 12:00:21,715 - INFO - 第 2 页获取到 100 条记录
2025-05-03 12:00:21,715 - INFO - Request Parameters - Page 3:
2025-05-03 12:00:21,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:21,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:22,184 - INFO - Response - Page 3:
2025-05-03 12:00:22,388 - INFO - 第 3 页获取到 100 条记录
2025-05-03 12:00:22,388 - INFO - Request Parameters - Page 4:
2025-05-03 12:00:22,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:22,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:22,841 - INFO - Response - Page 4:
2025-05-03 12:00:23,045 - INFO - 第 4 页获取到 100 条记录
2025-05-03 12:00:23,045 - INFO - Request Parameters - Page 5:
2025-05-03 12:00:23,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:23,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:23,529 - INFO - Response - Page 5:
2025-05-03 12:00:23,733 - INFO - 第 5 页获取到 100 条记录
2025-05-03 12:00:23,733 - INFO - Request Parameters - Page 6:
2025-05-03 12:00:23,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:23,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:24,202 - INFO - Response - Page 6:
2025-05-03 12:00:24,405 - INFO - 第 6 页获取到 100 条记录
2025-05-03 12:00:24,405 - INFO - Request Parameters - Page 7:
2025-05-03 12:00:24,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:24,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:24,796 - INFO - Response - Page 7:
2025-05-03 12:00:25,000 - INFO - 第 7 页获取到 27 条记录
2025-05-03 12:00:25,000 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 12:00:25,000 - INFO - 获取到 627 条表单数据
2025-05-03 12:00:25,000 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 12:00:25,015 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 12:00:25,015 - INFO - 开始处理日期: 2025-05
2025-05-03 12:00:25,015 - INFO - Request Parameters - Page 1:
2025-05-03 12:00:25,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:25,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:25,485 - INFO - Response - Page 1:
2025-05-03 12:00:25,688 - INFO - 第 1 页获取到 100 条记录
2025-05-03 12:00:25,688 - INFO - Request Parameters - Page 2:
2025-05-03 12:00:25,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:25,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:26,189 - INFO - Response - Page 2:
2025-05-03 12:00:26,392 - INFO - 第 2 页获取到 100 条记录
2025-05-03 12:00:26,392 - INFO - Request Parameters - Page 3:
2025-05-03 12:00:26,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:26,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:26,814 - INFO - Response - Page 3:
2025-05-03 12:00:27,018 - INFO - 第 3 页获取到 100 条记录
2025-05-03 12:00:27,018 - INFO - Request Parameters - Page 4:
2025-05-03 12:00:27,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:27,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:27,502 - INFO - Response - Page 4:
2025-05-03 12:00:27,706 - INFO - 第 4 页获取到 100 条记录
2025-05-03 12:00:27,706 - INFO - Request Parameters - Page 5:
2025-05-03 12:00:27,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:27,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:28,191 - INFO - Response - Page 5:
2025-05-03 12:00:28,394 - INFO - 第 5 页获取到 100 条记录
2025-05-03 12:00:28,394 - INFO - Request Parameters - Page 6:
2025-05-03 12:00:28,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 12:00:28,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 12:00:28,879 - INFO - Response - Page 6:
2025-05-03 12:00:29,082 - INFO - 第 6 页获取到 60 条记录
2025-05-03 12:00:29,082 - INFO - 查询完成，共获取到 560 条记录
2025-05-03 12:00:29,082 - INFO - 获取到 560 条表单数据
2025-05-03 12:00:29,082 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-03 12:00:29,082 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-03 12:00:29,536 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-03 12:00:29,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80.0, 'new_value': 140.0}, {'field': 'offline_amount', 'old_value': 1640.0, 'new_value': 5820.0}, {'field': 'total_amount', 'old_value': 1720.0, 'new_value': 5960.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 84}]
2025-05-03 12:00:29,536 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-03 12:00:29,943 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-03 12:00:29,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17889.0, 'new_value': 39119.0}, {'field': 'total_amount', 'old_value': 17889.0, 'new_value': 39119.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 21}]
2025-05-03 12:00:29,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-03 12:00:30,490 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-03 12:00:30,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265.0, 'new_value': 1060.0}, {'field': 'total_amount', 'old_value': 265.0, 'new_value': 1060.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-03 12:00:30,506 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-03 12:00:30,959 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-03 12:00:30,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9680.0, 'new_value': 19960.0}, {'field': 'total_amount', 'old_value': 9680.0, 'new_value': 19960.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 9}]
2025-05-03 12:00:30,959 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-03 12:00:31,413 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-03 12:00:31,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39380.0, 'new_value': 55803.0}, {'field': 'total_amount', 'old_value': 39479.0, 'new_value': 55902.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 15}]
2025-05-03 12:00:31,413 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-03 12:00:31,913 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-03 12:00:31,913 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16041.0, 'new_value': 31708.0}, {'field': 'offline_amount', 'old_value': 29796.0, 'new_value': 35245.0}, {'field': 'total_amount', 'old_value': 45837.0, 'new_value': 66953.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 62}]
2025-05-03 12:00:31,913 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-03 12:00:32,351 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-03 12:00:32,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8268.0, 'new_value': 14931.0}, {'field': 'total_amount', 'old_value': 8268.0, 'new_value': 14931.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 11}]
2025-05-03 12:00:32,351 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-03 12:00:32,805 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-03 12:00:32,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7956.0, 'new_value': 9185.0}, {'field': 'total_amount', 'old_value': 7956.0, 'new_value': 9185.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-03 12:00:32,805 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-03 12:00:33,259 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-03 12:00:33,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3399.89, 'new_value': 6013.32}, {'field': 'offline_amount', 'old_value': 3639.0, 'new_value': 10089.0}, {'field': 'total_amount', 'old_value': 7038.89, 'new_value': 16102.32}, {'field': 'order_count', 'old_value': 81, 'new_value': 179}]
2025-05-03 12:00:33,259 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-03 12:00:33,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-03 12:00:33,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1556.0, 'new_value': 2651.0}, {'field': 'offline_amount', 'old_value': 2101.0, 'new_value': 4768.0}, {'field': 'total_amount', 'old_value': 3657.0, 'new_value': 7419.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 307}]
2025-05-03 12:00:33,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-03 12:00:34,119 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-03 12:00:34,119 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18760.9, 'new_value': 47641.3}, {'field': 'total_amount', 'old_value': 18760.9, 'new_value': 47641.3}, {'field': 'order_count', 'old_value': 198, 'new_value': 519}]
2025-05-03 12:00:34,119 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-03 12:00:34,682 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-03 12:00:34,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3719.0, 'new_value': 7584.0}, {'field': 'total_amount', 'old_value': 3719.0, 'new_value': 7584.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 389}]
2025-05-03 12:00:34,682 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-03 12:00:35,151 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-03 12:00:35,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6815.49, 'new_value': 9845.89}, {'field': 'total_amount', 'old_value': 6815.49, 'new_value': 9845.89}, {'field': 'order_count', 'old_value': 62, 'new_value': 99}]
2025-05-03 12:00:35,151 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-03 12:00:35,589 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-03 12:00:35,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2000.0, 'new_value': 3000.0}, {'field': 'offline_amount', 'old_value': 776.9, 'new_value': 2925.07}, {'field': 'total_amount', 'old_value': 2776.9, 'new_value': 5925.07}, {'field': 'order_count', 'old_value': 34, 'new_value': 75}]
2025-05-03 12:00:35,589 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-03 12:00:35,996 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-03 12:00:35,996 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 261.0, 'new_value': 851.0}, {'field': 'offline_amount', 'old_value': 1376.0, 'new_value': 2376.0}, {'field': 'total_amount', 'old_value': 1637.0, 'new_value': 3227.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 8}]
2025-05-03 12:00:35,996 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-03 12:00:36,402 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-03 12:00:36,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9626.0, 'new_value': 15504.0}, {'field': 'total_amount', 'old_value': 9626.0, 'new_value': 15504.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 31}]
2025-05-03 12:00:36,402 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-03 12:00:36,919 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-03 12:00:36,919 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14013.62, 'new_value': 27391.66}, {'field': 'offline_amount', 'old_value': 2860.94, 'new_value': 6204.09}, {'field': 'total_amount', 'old_value': 16874.56, 'new_value': 33595.75}, {'field': 'order_count', 'old_value': 56, 'new_value': 118}]
2025-05-03 12:00:36,919 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-03 12:00:37,372 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-03 12:00:37,372 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12263.0, 'new_value': 22934.0}, {'field': 'offline_amount', 'old_value': 5723.9, 'new_value': 10069.2}, {'field': 'total_amount', 'old_value': 17986.9, 'new_value': 33003.2}, {'field': 'order_count', 'old_value': 117, 'new_value': 196}]
2025-05-03 12:00:37,372 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-03 12:00:37,842 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-03 12:00:37,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235.0, 'new_value': 844.0}, {'field': 'total_amount', 'old_value': 353.0, 'new_value': 962.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 18}]
2025-05-03 12:00:37,842 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-03 12:00:38,373 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-03 12:00:38,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3923.16, 'new_value': 7089.43}, {'field': 'total_amount', 'old_value': 3923.16, 'new_value': 7089.43}, {'field': 'order_count', 'old_value': 90, 'new_value': 171}]
2025-05-03 12:00:38,373 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-03 12:00:38,811 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-03 12:00:38,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22108.0, 'new_value': 24805.0}, {'field': 'total_amount', 'old_value': 22108.0, 'new_value': 24805.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-03 12:00:38,811 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-03 12:00:39,296 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-03 12:00:39,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8347.0, 'new_value': 17959.0}, {'field': 'offline_amount', 'old_value': 3090.7, 'new_value': 5903.7}, {'field': 'total_amount', 'old_value': 11437.7, 'new_value': 23862.7}, {'field': 'order_count', 'old_value': 84, 'new_value': 155}]
2025-05-03 12:00:39,296 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-03 12:00:39,750 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-03 12:00:39,750 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2138.37, 'new_value': 3230.55}, {'field': 'offline_amount', 'old_value': 31903.17, 'new_value': 65110.96}, {'field': 'total_amount', 'old_value': 34041.54, 'new_value': 68341.51}, {'field': 'order_count', 'old_value': 107, 'new_value': 219}]
2025-05-03 12:00:39,750 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-03 12:00:40,172 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-03 12:00:40,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 331.95, 'new_value': 687.93}, {'field': 'offline_amount', 'old_value': 9980.73, 'new_value': 18730.96}, {'field': 'total_amount', 'old_value': 10312.68, 'new_value': 19418.89}, {'field': 'order_count', 'old_value': 98, 'new_value': 188}]
2025-05-03 12:00:40,172 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-03 12:00:40,641 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-03 12:00:40,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9101.09, 'new_value': 21070.19}, {'field': 'total_amount', 'old_value': 9101.09, 'new_value': 21070.19}, {'field': 'order_count', 'old_value': 30, 'new_value': 68}]
2025-05-03 12:00:40,641 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-03 12:00:41,095 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-03 12:00:41,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6968.0, 'new_value': 11981.0}, {'field': 'total_amount', 'old_value': 6968.0, 'new_value': 11981.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 272}]
2025-05-03 12:00:41,095 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-03 12:00:41,580 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-03 12:00:41,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4736.0, 'new_value': 4751.0}, {'field': 'total_amount', 'old_value': 4736.0, 'new_value': 4751.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-03 12:00:41,580 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-03 12:00:42,049 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-03 12:00:42,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1108.0, 'new_value': 2502.46}, {'field': 'total_amount', 'old_value': 1108.0, 'new_value': 2502.46}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-03 12:00:42,049 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-03 12:00:42,503 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-03 12:00:42,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6774.0, 'new_value': 11954.0}, {'field': 'total_amount', 'old_value': 6774.0, 'new_value': 11954.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 375}]
2025-05-03 12:00:42,503 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-03 12:00:42,941 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-03 12:00:42,941 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3430.0, 'new_value': 7933.0}, {'field': 'offline_amount', 'old_value': 10401.0, 'new_value': 31633.0}, {'field': 'total_amount', 'old_value': 13831.0, 'new_value': 39566.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 10}]
2025-05-03 12:00:42,941 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-03 12:00:43,410 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-03 12:00:43,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3955.2, 'new_value': 8116.82}, {'field': 'total_amount', 'old_value': 3955.2, 'new_value': 8116.82}, {'field': 'order_count', 'old_value': 158, 'new_value': 315}]
2025-05-03 12:00:43,410 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-03 12:00:43,848 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-03 12:00:43,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9971.62, 'new_value': 17073.8}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 274.0}, {'field': 'total_amount', 'old_value': 9971.62, 'new_value': 17347.8}, {'field': 'order_count', 'old_value': 105, 'new_value': 171}]
2025-05-03 12:00:43,848 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-03 12:00:44,364 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-03 12:00:44,364 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4214.0, 'new_value': 8920.0}, {'field': 'total_amount', 'old_value': 4214.0, 'new_value': 8920.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 15}]
2025-05-03 12:00:44,364 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-03 12:00:44,833 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-03 12:00:44,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20003.0, 'new_value': 50427.0}, {'field': 'total_amount', 'old_value': 20003.0, 'new_value': 50427.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 65}]
2025-05-03 12:00:44,833 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-03 12:00:45,256 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-03 12:00:45,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126883.5, 'new_value': 225482.1}, {'field': 'total_amount', 'old_value': 126883.5, 'new_value': 225482.1}, {'field': 'order_count', 'old_value': 196, 'new_value': 351}]
2025-05-03 12:00:45,256 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-03 12:00:45,694 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-03 12:00:45,694 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4741.0}, {'field': 'total_amount', 'old_value': 4766.0, 'new_value': 9507.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 12:00:45,694 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-03 12:00:46,163 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-03 12:00:46,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2146.0, 'new_value': 4002.0}, {'field': 'total_amount', 'old_value': 2146.0, 'new_value': 4002.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 69}]
2025-05-03 12:00:46,163 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-03 12:00:46,632 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-03 12:00:46,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 456.6, 'new_value': 687.8}, {'field': 'offline_amount', 'old_value': 899.27, 'new_value': 1595.47}, {'field': 'total_amount', 'old_value': 1355.87, 'new_value': 2283.27}, {'field': 'order_count', 'old_value': 47, 'new_value': 74}]
2025-05-03 12:00:46,632 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-03 12:00:47,117 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-03 12:00:47,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8907.26, 'new_value': 16579.06}, {'field': 'offline_amount', 'old_value': 5891.31, 'new_value': 14330.94}, {'field': 'total_amount', 'old_value': 14798.57, 'new_value': 30910.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 234}]
2025-05-03 12:00:47,117 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-03 12:00:47,649 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-03 12:00:47,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8841.18, 'new_value': 9133.45}, {'field': 'total_amount', 'old_value': 8841.18, 'new_value': 9133.45}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-03 12:00:47,649 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-03 12:00:48,102 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-03 12:00:48,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34308.0, 'new_value': 55358.0}, {'field': 'offline_amount', 'old_value': 248.0, 'new_value': 1142.0}, {'field': 'total_amount', 'old_value': 34556.0, 'new_value': 56500.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 69}]
2025-05-03 12:00:48,102 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-03 12:00:48,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-03 12:00:48,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 239.0, 'new_value': 2554.0}, {'field': 'total_amount', 'old_value': 239.0, 'new_value': 2554.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 7}]
2025-05-03 12:00:48,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-03 12:00:48,978 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-03 12:00:48,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6127.0, 'new_value': 7804.0}, {'field': 'total_amount', 'old_value': 6127.0, 'new_value': 7804.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-03 12:00:48,978 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-03 12:00:49,432 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-03 12:00:49,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5000.0, 'new_value': 8180.0}, {'field': 'offline_amount', 'old_value': 95.7, 'new_value': 423.65}, {'field': 'total_amount', 'old_value': 5095.7, 'new_value': 8603.65}, {'field': 'order_count', 'old_value': 17, 'new_value': 29}]
2025-05-03 12:00:49,432 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-03 12:00:49,870 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-03 12:00:49,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3215.49, 'new_value': 8151.54}, {'field': 'total_amount', 'old_value': 3215.49, 'new_value': 8151.54}, {'field': 'order_count', 'old_value': 103, 'new_value': 213}]
2025-05-03 12:00:49,870 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-03 12:00:50,324 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-03 12:00:50,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23080.75, 'new_value': 29898.75}, {'field': 'total_amount', 'old_value': 23080.75, 'new_value': 29898.75}, {'field': 'order_count', 'old_value': 36, 'new_value': 66}]
2025-05-03 12:00:50,324 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-03 12:00:50,793 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-03 12:00:50,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1222.9, 'new_value': 1384.59}, {'field': 'offline_amount', 'old_value': 29780.07, 'new_value': 52214.32}, {'field': 'total_amount', 'old_value': 31002.97, 'new_value': 53598.91}, {'field': 'order_count', 'old_value': 99, 'new_value': 187}]
2025-05-03 12:00:50,793 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-03 12:00:51,262 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-03 12:00:51,262 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10807.0, 'new_value': 14482.0}, {'field': 'offline_amount', 'old_value': 3301.0, 'new_value': 4899.0}, {'field': 'total_amount', 'old_value': 14108.0, 'new_value': 19381.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 22}]
2025-05-03 12:00:51,262 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-03 12:00:51,684 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-03 12:00:51,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99.0, 'new_value': 722.0}, {'field': 'total_amount', 'old_value': 99.0, 'new_value': 722.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 12:00:51,684 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-03 12:00:52,154 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-03 12:00:52,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1640.0, 'new_value': 3823.0}, {'field': 'total_amount', 'old_value': 1640.0, 'new_value': 3823.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 12:00:52,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-03 12:00:52,607 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-03 12:00:52,607 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16683.38, 'new_value': 26823.77}, {'field': 'total_amount', 'old_value': 16683.38, 'new_value': 26823.77}, {'field': 'order_count', 'old_value': 69, 'new_value': 130}]
2025-05-03 12:00:52,607 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-03 12:00:53,076 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-03 12:00:53,076 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 390.73, 'new_value': 698.09}, {'field': 'offline_amount', 'old_value': 198.0, 'new_value': 1197.0}, {'field': 'total_amount', 'old_value': 588.73, 'new_value': 1895.09}, {'field': 'order_count', 'old_value': 8, 'new_value': 17}]
2025-05-03 12:00:53,076 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-03 12:00:53,546 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-03 12:00:53,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 338300.6}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 338300.6}, {'field': 'order_count', 'old_value': 110, 'new_value': 192}]
2025-05-03 12:00:53,546 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L
2025-05-03 12:00:53,968 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L
2025-05-03 12:00:53,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9813.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9813.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 12:00:53,968 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-03 12:00:54,422 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-03 12:00:54,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4846.09, 'new_value': 8348.81}, {'field': 'total_amount', 'old_value': 4846.09, 'new_value': 8348.81}, {'field': 'order_count', 'old_value': 29, 'new_value': 54}]
2025-05-03 12:00:54,422 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-03 12:00:54,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-03 12:00:54,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149.0, 'new_value': 356.0}, {'field': 'offline_amount', 'old_value': 1609.8, 'new_value': 2529.3}, {'field': 'total_amount', 'old_value': 1758.8, 'new_value': 2885.3}, {'field': 'order_count', 'old_value': 61, 'new_value': 110}]
2025-05-03 12:00:54,875 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-03 12:00:55,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-03 12:00:55,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3929.6, 'new_value': 13998.74}, {'field': 'total_amount', 'old_value': 3929.6, 'new_value': 13998.74}, {'field': 'order_count', 'old_value': 9, 'new_value': 21}]
2025-05-03 12:00:55,344 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-03 12:00:55,767 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-03 12:00:55,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70000.0, 'new_value': 140000.0}, {'field': 'total_amount', 'old_value': 70000.0, 'new_value': 140000.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 321}]
2025-05-03 12:00:55,767 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-03 12:00:56,252 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-03 12:00:56,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6532.0, 'new_value': 9012.0}, {'field': 'total_amount', 'old_value': 6532.0, 'new_value': 9012.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-03 12:00:56,252 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-03 12:00:56,721 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL
2025-05-03 12:00:56,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2399.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2399.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 12:00:56,721 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-03 12:00:57,159 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-03 12:00:57,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7360.0, 'new_value': 13040.0}, {'field': 'total_amount', 'old_value': 7360.0, 'new_value': 13040.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-03 12:00:57,159 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-03 12:00:57,581 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-03 12:00:57,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 558.34, 'new_value': 916.15}, {'field': 'offline_amount', 'old_value': 1185.21, 'new_value': 2792.94}, {'field': 'total_amount', 'old_value': 1743.55, 'new_value': 3709.09}, {'field': 'order_count', 'old_value': 57, 'new_value': 131}]
2025-05-03 12:00:57,581 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-03 12:00:58,004 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-03 12:00:58,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8399.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 8399.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-03 12:00:58,004 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-03 12:00:58,457 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-03 12:00:58,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11812.0, 'new_value': 20728.0}, {'field': 'total_amount', 'old_value': 11812.0, 'new_value': 20728.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 80}]
2025-05-03 12:00:58,457 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-03 12:00:58,926 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-03 12:00:58,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2380.0, 'new_value': 4368.0}, {'field': 'total_amount', 'old_value': 2380.0, 'new_value': 4368.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 12:00:58,942 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-03 12:00:59,349 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-03 12:00:59,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3850.0, 'new_value': 5530.0}, {'field': 'total_amount', 'old_value': 3850.0, 'new_value': 5530.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-03 12:00:59,349 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-03 12:00:59,787 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-03 12:00:59,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8567.7, 'new_value': 12982.0}, {'field': 'total_amount', 'old_value': 8567.7, 'new_value': 12982.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 60}]
2025-05-03 12:00:59,787 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-03 12:01:00,272 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-03 12:01:00,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 896.35, 'new_value': 1959.41}, {'field': 'offline_amount', 'old_value': 1579.9, 'new_value': 2722.9}, {'field': 'total_amount', 'old_value': 2476.25, 'new_value': 4682.31}, {'field': 'order_count', 'old_value': 89, 'new_value': 166}]
2025-05-03 12:01:00,272 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-03 12:01:00,741 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-03 12:01:00,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2037.0}, {'field': 'total_amount', 'old_value': 2408.0, 'new_value': 4445.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 19}]
2025-05-03 12:01:00,741 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-03 12:01:01,163 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-03 12:01:01,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8193.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 8193.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-03 12:01:01,163 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-03 12:01:01,570 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-03 12:01:01,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3261.0, 'new_value': 5054.0}, {'field': 'offline_amount', 'old_value': 3565.4, 'new_value': 7245.87}, {'field': 'total_amount', 'old_value': 6826.4, 'new_value': 12299.87}, {'field': 'order_count', 'old_value': 61, 'new_value': 127}]
2025-05-03 12:01:01,570 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-03 12:01:02,055 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-03 12:01:02,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5361.1, 'new_value': 9946.3}, {'field': 'offline_amount', 'old_value': 6143.6, 'new_value': 10568.96}, {'field': 'total_amount', 'old_value': 11504.7, 'new_value': 20515.26}, {'field': 'order_count', 'old_value': 81, 'new_value': 158}]
2025-05-03 12:01:02,055 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-03 12:01:02,602 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-03 12:01:02,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 350.6, 'new_value': 1153.38}, {'field': 'offline_amount', 'old_value': 6595.8, 'new_value': 11592.54}, {'field': 'total_amount', 'old_value': 6946.4, 'new_value': 12745.92}, {'field': 'order_count', 'old_value': 224, 'new_value': 420}]
2025-05-03 12:01:02,602 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-03 12:01:03,071 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-03 12:01:03,071 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 529.72, 'new_value': 877.75}, {'field': 'offline_amount', 'old_value': 1253.41, 'new_value': 2408.42}, {'field': 'total_amount', 'old_value': 1783.13, 'new_value': 3286.17}, {'field': 'order_count', 'old_value': 88, 'new_value': 166}]
2025-05-03 12:01:03,071 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-03 12:01:03,525 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-03 12:01:03,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4054.0, 'new_value': 7569.0}, {'field': 'total_amount', 'old_value': 4054.0, 'new_value': 7569.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-05-03 12:01:03,525 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-03 12:01:03,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-03 12:01:03,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4981.0, 'new_value': 7391.0}, {'field': 'total_amount', 'old_value': 4981.0, 'new_value': 7391.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-03 12:01:03,979 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-03 12:01:04,464 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-03 12:01:04,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9389.06, 'new_value': 16341.51}, {'field': 'total_amount', 'old_value': 9389.06, 'new_value': 16341.51}, {'field': 'order_count', 'old_value': 241, 'new_value': 445}]
2025-05-03 12:01:04,464 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-03 12:01:04,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-03 12:01:04,901 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7118.0, 'new_value': 13962.68}, {'field': 'offline_amount', 'old_value': 12560.0, 'new_value': 26111.2}, {'field': 'total_amount', 'old_value': 19678.0, 'new_value': 40073.88}, {'field': 'order_count', 'old_value': 191, 'new_value': 381}]
2025-05-03 12:01:04,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-03 12:01:05,339 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-03 12:01:05,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1755.8, 'new_value': 5482.28}, {'field': 'total_amount', 'old_value': 1755.8, 'new_value': 5482.28}, {'field': 'order_count', 'old_value': 2, 'new_value': 6}]
2025-05-03 12:01:05,339 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-03 12:01:05,856 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-03 12:01:05,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4964.0, 'new_value': 17177.08}, {'field': 'total_amount', 'old_value': 4964.0, 'new_value': 17177.08}, {'field': 'order_count', 'old_value': 11, 'new_value': 27}]
2025-05-03 12:01:05,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-03 12:01:06,466 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-03 12:01:06,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8712.0, 'new_value': 16584.0}, {'field': 'total_amount', 'old_value': 8712.0, 'new_value': 16584.0}, {'field': 'order_count', 'old_value': 726, 'new_value': 1382}]
2025-05-03 12:01:06,466 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-03 12:01:06,919 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-03 12:01:06,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3860.0, 'new_value': 7159.0}, {'field': 'total_amount', 'old_value': 3860.0, 'new_value': 7159.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 12:01:06,919 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-03 12:01:07,404 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-03 12:01:07,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2198.6, 'new_value': 5375.9}, {'field': 'total_amount', 'old_value': 2198.6, 'new_value': 5375.9}, {'field': 'order_count', 'old_value': 22, 'new_value': 52}]
2025-05-03 12:01:07,404 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-03 12:01:07,873 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-03 12:01:07,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1336000.0, 'new_value': 2038000.0}, {'field': 'total_amount', 'old_value': 1336000.0, 'new_value': 2038000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 12:01:07,873 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-03 12:01:08,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-03 12:01:08,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7341.9, 'new_value': 8648.9}, {'field': 'total_amount', 'old_value': 7341.9, 'new_value': 8648.9}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-03 12:01:08,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-03 12:01:08,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-03 12:01:08,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2298.35, 'new_value': 4596.05}, {'field': 'total_amount', 'old_value': 2298.35, 'new_value': 4596.05}, {'field': 'order_count', 'old_value': 94, 'new_value': 177}]
2025-05-03 12:01:08,765 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-03 12:01:09,250 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-03 12:01:09,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24000.0, 'new_value': 39046.0}, {'field': 'total_amount', 'old_value': 24000.0, 'new_value': 39046.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-03 12:01:09,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-03 12:01:09,735 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-03 12:01:09,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13018.81}, {'field': 'total_amount', 'old_value': 17188.6, 'new_value': 30207.41}, {'field': 'order_count', 'old_value': 393, 'new_value': 715}]
2025-05-03 12:01:09,735 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-03 12:01:10,204 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-03 12:01:10,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32981.28, 'new_value': 64044.98}, {'field': 'total_amount', 'old_value': 32981.28, 'new_value': 64044.98}, {'field': 'order_count', 'old_value': 49, 'new_value': 108}]
2025-05-03 12:01:10,204 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-03 12:01:10,626 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-03 12:01:10,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2856.99, 'new_value': 4785.7}, {'field': 'offline_amount', 'old_value': 2087.18, 'new_value': 3818.73}, {'field': 'total_amount', 'old_value': 4944.17, 'new_value': 8604.43}, {'field': 'order_count', 'old_value': 257, 'new_value': 451}]
2025-05-03 12:01:10,626 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-03 12:01:11,017 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-03 12:01:11,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13896.0, 'new_value': 20463.0}, {'field': 'total_amount', 'old_value': 13896.0, 'new_value': 20463.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-03 12:01:11,033 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-03 12:01:11,424 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-03 12:01:11,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 491.9, 'new_value': 2004.68}, {'field': 'total_amount', 'old_value': 491.9, 'new_value': 2004.68}, {'field': 'order_count', 'old_value': 16, 'new_value': 31}]
2025-05-03 12:01:11,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-03 12:01:11,909 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-03 12:01:11,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24662.76, 'new_value': 49708.96}, {'field': 'total_amount', 'old_value': 24662.76, 'new_value': 49708.96}, {'field': 'order_count', 'old_value': 90, 'new_value': 183}]
2025-05-03 12:01:11,909 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-03 12:01:12,409 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-03 12:01:12,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14304.8, 'new_value': 24776.4}, {'field': 'total_amount', 'old_value': 14304.8, 'new_value': 24776.4}, {'field': 'order_count', 'old_value': 311, 'new_value': 584}]
2025-05-03 12:01:12,409 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-03 12:01:12,879 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-03 12:01:12,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1428.67, 'new_value': 2844.85}, {'field': 'total_amount', 'old_value': 1428.67, 'new_value': 2844.85}, {'field': 'order_count', 'old_value': 184, 'new_value': 370}]
2025-05-03 12:01:12,879 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-03 12:01:13,285 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-03 12:01:13,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 875.4, 'new_value': 2105.42}, {'field': 'offline_amount', 'old_value': 1629.4, 'new_value': 3327.8}, {'field': 'total_amount', 'old_value': 2504.8, 'new_value': 5433.22}, {'field': 'order_count', 'old_value': 99, 'new_value': 207}]
2025-05-03 12:01:13,285 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-03 12:01:13,723 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-03 12:01:13,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3948.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3948.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 9}]
2025-05-03 12:01:13,739 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-03 12:01:14,177 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-03 12:01:14,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15560.0, 'new_value': 15760.0}, {'field': 'total_amount', 'old_value': 15560.0, 'new_value': 15760.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-03 12:01:14,177 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-03 12:01:14,599 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-03 12:01:14,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20951.0, 'new_value': 48402.0}, {'field': 'total_amount', 'old_value': 20951.0, 'new_value': 48402.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 242}]
2025-05-03 12:01:14,599 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-03 12:01:15,022 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-03 12:01:15,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10144.0, 'new_value': 29906.0}, {'field': 'total_amount', 'old_value': 10144.0, 'new_value': 29906.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 15}]
2025-05-03 12:01:15,022 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-03 12:01:15,444 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-03 12:01:15,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6660.0, 'new_value': 11290.0}, {'field': 'total_amount', 'old_value': 6660.0, 'new_value': 11290.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 12:01:15,444 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-03 12:01:15,851 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-03 12:01:15,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 404938.0, 'new_value': 524175.0}, {'field': 'total_amount', 'old_value': 404938.0, 'new_value': 524175.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 61}]
2025-05-03 12:01:15,866 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-03 12:01:16,289 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-03 12:01:16,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46.33, 'new_value': 2883.77}, {'field': 'offline_amount', 'old_value': 4869.3, 'new_value': 5033.52}, {'field': 'total_amount', 'old_value': 4915.63, 'new_value': 7917.29}, {'field': 'order_count', 'old_value': 14, 'new_value': 29}]
2025-05-03 12:01:16,289 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-03 12:01:16,758 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-03 12:01:16,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22992.0, 'new_value': 48960.0}, {'field': 'offline_amount', 'old_value': 2370.0, 'new_value': 2760.0}, {'field': 'total_amount', 'old_value': 25362.0, 'new_value': 51720.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 226}]
2025-05-03 12:01:16,758 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-03 12:01:17,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-03 12:01:17,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1344.0, 'new_value': 2913.0}, {'field': 'total_amount', 'old_value': 1344.0, 'new_value': 2913.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 17}]
2025-05-03 12:01:17,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-03 12:01:17,696 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-03 12:01:17,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 309.0, 'new_value': 972.0}, {'field': 'offline_amount', 'old_value': 2559.5, 'new_value': 5503.8}, {'field': 'total_amount', 'old_value': 2868.5, 'new_value': 6475.8}, {'field': 'order_count', 'old_value': 24, 'new_value': 64}]
2025-05-03 12:01:17,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-03 12:01:18,103 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-03 12:01:18,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 575.0, 'new_value': 1043.0}, {'field': 'offline_amount', 'old_value': 2028.0, 'new_value': 3878.0}, {'field': 'total_amount', 'old_value': 2603.0, 'new_value': 4921.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-03 12:01:18,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-03 12:01:18,463 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-03 12:01:18,463 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1217.7, 'new_value': 1840.5}, {'field': 'total_amount', 'old_value': 1217.7, 'new_value': 1840.5}, {'field': 'order_count', 'old_value': 36, 'new_value': 62}]
2025-05-03 12:01:18,463 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-03 12:01:18,901 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-03 12:01:18,901 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73129.0, 'new_value': 143027.0}, {'field': 'total_amount', 'old_value': 73129.0, 'new_value': 143027.0}, {'field': 'order_count', 'old_value': 269, 'new_value': 658}]
2025-05-03 12:01:18,901 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-03 12:01:19,354 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-03 12:01:19,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5680.0, 'new_value': 21330.0}, {'field': 'total_amount', 'old_value': 5680.0, 'new_value': 21330.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 4}]
2025-05-03 12:01:19,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-03 12:01:19,777 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-03 12:01:19,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 16061.0}, {'field': 'total_amount', 'old_value': 15204.0, 'new_value': 31265.0}, {'field': 'order_count', 'old_value': 358, 'new_value': 736}]
2025-05-03 12:01:19,777 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-03 12:01:20,293 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-03 12:01:20,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10722.0, 'new_value': 21072.0}, {'field': 'total_amount', 'old_value': 10722.0, 'new_value': 21072.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 84}]
2025-05-03 12:01:20,293 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-03 12:01:20,700 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-03 12:01:20,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2800.0, 'new_value': 4100.0}, {'field': 'total_amount', 'old_value': 2800.0, 'new_value': 4100.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 12:01:20,700 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-03 12:01:21,138 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-03 12:01:21,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 199.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 199.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 12:01:21,153 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-03 12:01:21,622 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-03 12:01:21,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 835.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 835.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 12:01:21,622 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-03 12:01:22,139 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-03 12:01:22,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3739.51, 'new_value': 6564.82}, {'field': 'offline_amount', 'old_value': 6984.66, 'new_value': 13324.1}, {'field': 'total_amount', 'old_value': 10724.17, 'new_value': 19888.92}, {'field': 'order_count', 'old_value': 390, 'new_value': 749}]
2025-05-03 12:01:22,139 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-03 12:01:22,592 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-03 12:01:22,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5254.0, 'new_value': 6068.0}, {'field': 'total_amount', 'old_value': 5254.0, 'new_value': 6068.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 13}]
2025-05-03 12:01:22,592 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-03 12:01:23,077 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-03 12:01:23,077 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 468.0, 'new_value': 833.0}, {'field': 'total_amount', 'old_value': 468.0, 'new_value': 833.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 24}]
2025-05-03 12:01:23,077 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-03 12:01:23,609 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-03 12:01:23,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3980.0, 'new_value': 16380.0}, {'field': 'total_amount', 'old_value': 3980.0, 'new_value': 16380.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 5}]
2025-05-03 12:01:23,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-03 12:01:24,141 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-03 12:01:24,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242.0, 'new_value': 2127.0}, {'field': 'total_amount', 'old_value': 242.0, 'new_value': 2127.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-03 12:01:24,141 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-03 12:01:24,626 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-03 12:01:24,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2512.0, 'new_value': 4937.0}, {'field': 'offline_amount', 'old_value': 3102.0, 'new_value': 4906.0}, {'field': 'total_amount', 'old_value': 5614.0, 'new_value': 9843.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 181}]
2025-05-03 12:01:24,626 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-03 12:01:25,126 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-03 12:01:25,126 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 425.0, 'new_value': 982.0}, {'field': 'offline_amount', 'old_value': 13162.0, 'new_value': 24781.0}, {'field': 'total_amount', 'old_value': 13587.0, 'new_value': 25763.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 125}]
2025-05-03 12:01:25,126 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-03 12:01:25,548 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-03 12:01:25,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24960.0, 'new_value': 34217.0}, {'field': 'total_amount', 'old_value': 24960.0, 'new_value': 34217.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 34}]
2025-05-03 12:01:25,548 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-03 12:01:26,112 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-03 12:01:26,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11134.1, 'new_value': 32649.81}, {'field': 'offline_amount', 'old_value': 16073.83, 'new_value': 24091.03}, {'field': 'total_amount', 'old_value': 27207.93, 'new_value': 56740.84}, {'field': 'order_count', 'old_value': 94, 'new_value': 294}]
2025-05-03 12:01:26,112 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-03 12:01:26,596 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-03 12:01:26,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 130000.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 130000.0}]
2025-05-03 12:01:26,596 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-03 12:01:27,050 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-03 12:01:27,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80000.0, 'new_value': 150000.0}, {'field': 'total_amount', 'old_value': 80000.0, 'new_value': 150000.0}]
2025-05-03 12:01:27,050 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-03 12:01:27,488 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-03 12:01:27,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 600000.0, 'new_value': 748674.0}, {'field': 'total_amount', 'old_value': 600000.0, 'new_value': 748674.0}]
2025-05-03 12:01:27,488 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-03 12:01:27,926 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-03 12:01:27,926 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4456.0, 'new_value': 8257.0}, {'field': 'offline_amount', 'old_value': 46144.0, 'new_value': 91143.0}, {'field': 'total_amount', 'old_value': 50600.0, 'new_value': 99400.0}, {'field': 'order_count', 'old_value': 1044, 'new_value': 2080}]
2025-05-03 12:01:27,926 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-03 12:01:28,380 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-03 12:01:28,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24553.58, 'new_value': 48305.71}, {'field': 'total_amount', 'old_value': 24553.58, 'new_value': 48305.71}, {'field': 'order_count', 'old_value': 81, 'new_value': 166}]
2025-05-03 12:01:28,380 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-03 12:01:28,833 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-03 12:01:28,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1864.0, 'new_value': 2682.0}, {'field': 'offline_amount', 'old_value': 13193.0, 'new_value': 20662.0}, {'field': 'total_amount', 'old_value': 15057.0, 'new_value': 23344.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 15}]
2025-05-03 12:01:28,833 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-03 12:01:29,396 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-03 12:01:29,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9967.08, 'new_value': 21645.66}, {'field': 'total_amount', 'old_value': 9967.08, 'new_value': 21645.66}, {'field': 'order_count', 'old_value': 500, 'new_value': 963}]
2025-05-03 12:01:29,396 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-03 12:01:29,803 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-03 12:01:29,803 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10986.1, 'new_value': 19500.9}, {'field': 'total_amount', 'old_value': 10986.1, 'new_value': 19500.9}, {'field': 'order_count', 'old_value': 50, 'new_value': 87}]
2025-05-03 12:01:29,803 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-03 12:01:30,288 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-03 12:01:30,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20916.4, 'new_value': 31585.0}, {'field': 'total_amount', 'old_value': 20916.4, 'new_value': 31585.0}, {'field': 'order_count', 'old_value': 581, 'new_value': 867}]
2025-05-03 12:01:30,288 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-03 12:01:30,726 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-03 12:01:30,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1623.0}, {'field': 'total_amount', 'old_value': 1515.0, 'new_value': 3138.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 12}]
2025-05-03 12:01:30,726 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-03 12:01:31,164 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-03 12:01:31,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6462.8, 'new_value': 13499.3}, {'field': 'total_amount', 'old_value': 6462.8, 'new_value': 13499.3}, {'field': 'order_count', 'old_value': 28, 'new_value': 58}]
2025-05-03 12:01:31,164 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-03 12:01:31,539 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-03 12:01:31,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5631.42, 'new_value': 12888.77}, {'field': 'total_amount', 'old_value': 5631.42, 'new_value': 12888.77}, {'field': 'order_count', 'old_value': 2, 'new_value': 29}]
2025-05-03 12:01:31,539 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-03 12:01:31,993 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-03 12:01:31,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 635.0, 'new_value': 1461.0}, {'field': 'offline_amount', 'old_value': 200.0, 'new_value': 255.0}, {'field': 'total_amount', 'old_value': 835.0, 'new_value': 1716.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 17}]
2025-05-03 12:01:31,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-03 12:01:32,431 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-03 12:01:32,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1520.9, 'new_value': 3266.29}, {'field': 'offline_amount', 'old_value': 1278.66, 'new_value': 2494.25}, {'field': 'total_amount', 'old_value': 2799.56, 'new_value': 5760.54}, {'field': 'order_count', 'old_value': 160, 'new_value': 318}]
2025-05-03 12:01:32,431 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-03 12:01:32,900 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-03 12:01:32,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2776.81, 'new_value': 5635.68}, {'field': 'offline_amount', 'old_value': 4124.58, 'new_value': 8376.97}, {'field': 'total_amount', 'old_value': 6901.39, 'new_value': 14012.65}, {'field': 'order_count', 'old_value': 154, 'new_value': 319}]
2025-05-03 12:01:32,900 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-03 12:01:33,354 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-03 12:01:33,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7499.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7499.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-03 12:01:33,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-03 12:01:33,854 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-03 12:01:33,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75471.0, 'new_value': 129436.0}, {'field': 'total_amount', 'old_value': 75471.0, 'new_value': 129436.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 148}]
2025-05-03 12:01:33,854 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-03 12:01:34,386 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-03 12:01:34,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1525.9, 'new_value': 2252.7}, {'field': 'offline_amount', 'old_value': 22362.0, 'new_value': 42324.0}, {'field': 'total_amount', 'old_value': 23887.9, 'new_value': 44576.7}, {'field': 'order_count', 'old_value': 37, 'new_value': 73}]
2025-05-03 12:01:34,386 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-03 12:01:34,886 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-03 12:01:34,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1980.7, 'new_value': 4253.6}, {'field': 'offline_amount', 'old_value': 2931.0, 'new_value': 5413.0}, {'field': 'total_amount', 'old_value': 4911.7, 'new_value': 9666.6}, {'field': 'order_count', 'old_value': 75, 'new_value': 156}]
2025-05-03 12:01:34,886 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-03 12:01:35,387 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-03 12:01:35,387 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6523.0, 'new_value': 13551.0}, {'field': 'offline_amount', 'old_value': 5187.0, 'new_value': 9712.0}, {'field': 'total_amount', 'old_value': 11710.0, 'new_value': 23263.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 294}]
2025-05-03 12:01:35,387 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-03 12:01:35,903 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-03 12:01:35,903 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 454.8, 'new_value': 1505.8}, {'field': 'offline_amount', 'old_value': 884.84, 'new_value': 1661.84}, {'field': 'total_amount', 'old_value': 1339.64, 'new_value': 3167.64}, {'field': 'order_count', 'old_value': 16, 'new_value': 40}]
2025-05-03 12:01:35,903 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-03 12:01:36,325 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-03 12:01:36,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2639.2}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2858.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5497.2}, {'field': 'order_count', 'old_value': 0, 'new_value': 6}]
2025-05-03 12:01:36,325 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-03 12:01:36,779 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-03 12:01:36,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1194.0, 'new_value': 2064.0}, {'field': 'offline_amount', 'old_value': 2129.0, 'new_value': 3374.0}, {'field': 'total_amount', 'old_value': 3323.0, 'new_value': 5438.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 23}]
2025-05-03 12:01:36,779 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-03 12:01:37,248 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-03 12:01:37,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15370.9, 'new_value': 27551.4}, {'field': 'total_amount', 'old_value': 15370.9, 'new_value': 27551.4}, {'field': 'order_count', 'old_value': 68, 'new_value': 155}]
2025-05-03 12:01:37,264 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-03 12:01:37,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-03 12:01:37,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 312.0, 'new_value': 471.0}, {'field': 'offline_amount', 'old_value': 2375.02, 'new_value': 4547.52}, {'field': 'total_amount', 'old_value': 2687.02, 'new_value': 5018.52}, {'field': 'order_count', 'old_value': 30, 'new_value': 47}]
2025-05-03 12:01:37,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-03 12:01:38,187 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-03 12:01:38,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6823.2, 'new_value': 14564.0}, {'field': 'total_amount', 'old_value': 6823.2, 'new_value': 14564.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 68}]
2025-05-03 12:01:38,187 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-03 12:01:38,640 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-03 12:01:38,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1157.0, 'new_value': 3155.0}, {'field': 'total_amount', 'old_value': 1157.0, 'new_value': 3155.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-03 12:01:38,640 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-03 12:01:38,985 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-03 12:01:38,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19646.0, 'new_value': 26550.0}, {'field': 'total_amount', 'old_value': 19646.0, 'new_value': 26550.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 55}]
2025-05-03 12:01:38,985 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-03 12:01:39,423 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-03 12:01:39,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2466.4, 'new_value': 4909.67}, {'field': 'offline_amount', 'old_value': 5029.15, 'new_value': 11055.2}, {'field': 'total_amount', 'old_value': 7495.55, 'new_value': 15964.87}, {'field': 'order_count', 'old_value': 160, 'new_value': 341}]
2025-05-03 12:01:39,423 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-03 12:01:39,954 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-03 12:01:39,954 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13031.0, 'new_value': 29821.0}, {'field': 'total_amount', 'old_value': 13031.0, 'new_value': 29821.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 23}]
2025-05-03 12:01:39,954 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-03 12:01:40,424 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-03 12:01:40,424 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8975.0, 'new_value': 19763.0}, {'field': 'offline_amount', 'old_value': 5011.0, 'new_value': 10910.0}, {'field': 'total_amount', 'old_value': 13986.0, 'new_value': 30673.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 99}]
2025-05-03 12:01:40,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-03 12:01:40,893 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-03 12:01:40,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4449.0, 'new_value': 7526.0}, {'field': 'total_amount', 'old_value': 4449.0, 'new_value': 7526.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 511}]
2025-05-03 12:01:40,893 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-03 12:01:41,315 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-03 12:01:41,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5280.0, 'new_value': 5483.0}, {'field': 'total_amount', 'old_value': 5280.0, 'new_value': 5483.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-03 12:01:41,315 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-03 12:01:41,816 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-03 12:01:41,816 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 837.9, 'new_value': 1894.1}, {'field': 'offline_amount', 'old_value': 3593.7, 'new_value': 6487.7}, {'field': 'total_amount', 'old_value': 4431.6, 'new_value': 8381.8}, {'field': 'order_count', 'old_value': 162, 'new_value': 304}]
2025-05-03 12:01:41,816 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-03 12:01:42,254 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-03 12:01:42,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6047.97, 'new_value': 7288.64}, {'field': 'total_amount', 'old_value': 6047.97, 'new_value': 7288.64}, {'field': 'order_count', 'old_value': 53, 'new_value': 71}]
2025-05-03 12:01:42,254 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-03 12:01:42,739 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-03 12:01:42,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 632.0, 'new_value': 1280.33}, {'field': 'total_amount', 'old_value': 632.0, 'new_value': 1280.33}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-03 12:01:42,739 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-03 12:01:43,176 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-03 12:01:43,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5827.64, 'new_value': 11041.25}, {'field': 'total_amount', 'old_value': 5827.64, 'new_value': 11041.25}, {'field': 'order_count', 'old_value': 133, 'new_value': 260}]
2025-05-03 12:01:43,176 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-03 12:01:43,646 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-03 12:01:43,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13195.0, 'new_value': 23599.0}, {'field': 'total_amount', 'old_value': 13195.0, 'new_value': 23599.0}, {'field': 'order_count', 'old_value': 277, 'new_value': 537}]
2025-05-03 12:01:43,646 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-03 12:01:44,146 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-03 12:01:44,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5470.32, 'new_value': 9997.09}, {'field': 'total_amount', 'old_value': 5470.32, 'new_value': 9997.09}, {'field': 'order_count', 'old_value': 67, 'new_value': 129}]
2025-05-03 12:01:44,146 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-03 12:01:44,662 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-03 12:01:44,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72513.35, 'new_value': 124753.86}, {'field': 'total_amount', 'old_value': 72513.35, 'new_value': 124753.86}, {'field': 'order_count', 'old_value': 209, 'new_value': 376}]
2025-05-03 12:01:44,662 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-03 12:01:45,085 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-03 12:01:45,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5068.0, 'new_value': 9746.0}, {'field': 'total_amount', 'old_value': 5068.0, 'new_value': 9746.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 327}]
2025-05-03 12:01:45,085 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-03 12:01:45,570 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-03 12:01:45,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49346.7, 'new_value': 92396.48}, {'field': 'total_amount', 'old_value': 49346.7, 'new_value': 92396.48}, {'field': 'order_count', 'old_value': 190, 'new_value': 359}]
2025-05-03 12:01:45,570 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-03 12:01:45,992 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-03 12:01:45,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66887.7, 'new_value': 134383.57}, {'field': 'total_amount', 'old_value': 66887.7, 'new_value': 134383.57}, {'field': 'order_count', 'old_value': 213, 'new_value': 441}]
2025-05-03 12:01:45,992 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-03 12:01:46,493 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-03 12:01:46,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40063.8, 'new_value': 80240.4}, {'field': 'total_amount', 'old_value': 40063.8, 'new_value': 80240.4}, {'field': 'order_count', 'old_value': 126, 'new_value': 272}]
2025-05-03 12:01:46,493 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-03 12:01:47,024 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-03 12:01:47,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2046.0, 'new_value': 3175.0}, {'field': 'total_amount', 'old_value': 2046.0, 'new_value': 3175.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 14}]
2025-05-03 12:01:47,024 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-03 12:01:47,462 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-03 12:01:47,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18092.64, 'new_value': 34922.78}, {'field': 'total_amount', 'old_value': 18092.64, 'new_value': 34922.78}, {'field': 'order_count', 'old_value': 48, 'new_value': 98}]
2025-05-03 12:01:47,462 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-03 12:01:47,822 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-03 12:01:47,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4978.0, 'new_value': 8428.0}, {'field': 'total_amount', 'old_value': 7604.0, 'new_value': 11054.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-03 12:01:47,822 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-03 12:01:48,354 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-03 12:01:48,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4177.0, 'new_value': 7190.0}, {'field': 'offline_amount', 'old_value': 1471.0, 'new_value': 2976.0}, {'field': 'total_amount', 'old_value': 5648.0, 'new_value': 10166.0}, {'field': 'order_count', 'old_value': 220, 'new_value': 408}]
2025-05-03 12:01:48,354 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-03 12:01:48,823 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-03 12:01:48,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3875.0, 'new_value': 8235.0}, {'field': 'total_amount', 'old_value': 3875.0, 'new_value': 8235.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 12:01:48,823 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-03 12:01:49,292 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-03 12:01:49,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1370.0, 'new_value': 11212.0}, {'field': 'total_amount', 'old_value': 1370.0, 'new_value': 11212.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 44}]
2025-05-03 12:01:49,292 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-03 12:01:49,699 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-03 12:01:49,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1137.0, 'new_value': 2783.0}, {'field': 'total_amount', 'old_value': 1137.0, 'new_value': 2783.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 54}]
2025-05-03 12:01:49,699 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-03 12:01:50,137 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-03 12:01:50,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11220.0, 'new_value': 15050.0}, {'field': 'total_amount', 'old_value': 11220.0, 'new_value': 15050.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 121}]
2025-05-03 12:01:50,153 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-03 12:01:50,638 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-03 12:01:50,638 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11643.17, 'new_value': 23298.16}, {'field': 'total_amount', 'old_value': 11643.17, 'new_value': 23298.16}, {'field': 'order_count', 'old_value': 77, 'new_value': 153}]
2025-05-03 12:01:50,638 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-03 12:01:51,107 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-03 12:01:51,107 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6598.0, 'new_value': 9398.0}, {'field': 'total_amount', 'old_value': 6598.0, 'new_value': 9398.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-03 12:01:51,122 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-03 12:01:51,545 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-03 12:01:51,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1360.0, 'new_value': 3040.0}, {'field': 'offline_amount', 'old_value': 3139.0, 'new_value': 4266.0}, {'field': 'total_amount', 'old_value': 4499.0, 'new_value': 7306.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 16}]
2025-05-03 12:01:51,545 - INFO - 开始批量插入 6 条新记录
2025-05-03 12:01:51,686 - INFO - 批量插入响应状态码: 200
2025-05-03 12:01:51,686 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 03 May 2025 04:01:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D9DCED3-C6D4-7465-AB97-543B2D674EDE', 'x-acs-trace-id': 'db396eefb521ce654c28e2441f0e9718', 'etag': '3OfgY4/GVyJXGj30+31n05w0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-03 12:01:51,686 - INFO - 批量插入响应体: {'result': ['FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE']}
2025-05-03 12:01:51,686 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-05-03 12:01:51,686 - INFO - 成功插入的数据ID: ['FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE', 'FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE']
2025-05-03 12:01:54,704 - INFO - 批量插入完成，共 6 条记录
2025-05-03 12:01:54,704 - INFO - 日期 2025-05 处理完成 - 更新: 179 条，插入: 6 条，错误: 0 条
2025-05-03 12:01:54,704 - INFO - 数据同步完成！更新: 179 条，插入: 6 条，错误: 0 条
2025-05-03 12:01:54,704 - INFO - =================同步完成====================
2025-05-03 15:00:03,957 - INFO - =================使用默认全量同步=============
2025-05-03 15:00:05,146 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-03 15:00:05,146 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 15:00:05,177 - INFO - 开始处理日期: 2025-01
2025-05-03 15:00:05,177 - INFO - Request Parameters - Page 1:
2025-05-03 15:00:05,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:05,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:06,350 - INFO - Response - Page 1:
2025-05-03 15:00:06,554 - INFO - 第 1 页获取到 100 条记录
2025-05-03 15:00:06,554 - INFO - Request Parameters - Page 2:
2025-05-03 15:00:06,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:06,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:07,038 - INFO - Response - Page 2:
2025-05-03 15:00:07,242 - INFO - 第 2 页获取到 100 条记录
2025-05-03 15:00:07,242 - INFO - Request Parameters - Page 3:
2025-05-03 15:00:07,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:07,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:07,727 - INFO - Response - Page 3:
2025-05-03 15:00:07,930 - INFO - 第 3 页获取到 100 条记录
2025-05-03 15:00:07,930 - INFO - Request Parameters - Page 4:
2025-05-03 15:00:07,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:07,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:08,430 - INFO - Response - Page 4:
2025-05-03 15:00:08,634 - INFO - 第 4 页获取到 100 条记录
2025-05-03 15:00:08,634 - INFO - Request Parameters - Page 5:
2025-05-03 15:00:08,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:08,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:09,103 - INFO - Response - Page 5:
2025-05-03 15:00:09,306 - INFO - 第 5 页获取到 100 条记录
2025-05-03 15:00:09,306 - INFO - Request Parameters - Page 6:
2025-05-03 15:00:09,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:09,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:09,854 - INFO - Response - Page 6:
2025-05-03 15:00:10,057 - INFO - 第 6 页获取到 100 条记录
2025-05-03 15:00:10,057 - INFO - Request Parameters - Page 7:
2025-05-03 15:00:10,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:10,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:10,558 - INFO - Response - Page 7:
2025-05-03 15:00:10,761 - INFO - 第 7 页获取到 82 条记录
2025-05-03 15:00:10,761 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 15:00:10,761 - INFO - 获取到 682 条表单数据
2025-05-03 15:00:10,761 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 15:00:10,777 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 15:00:10,777 - INFO - 开始处理日期: 2025-02
2025-05-03 15:00:10,777 - INFO - Request Parameters - Page 1:
2025-05-03 15:00:10,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:10,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:11,293 - INFO - Response - Page 1:
2025-05-03 15:00:11,496 - INFO - 第 1 页获取到 100 条记录
2025-05-03 15:00:11,496 - INFO - Request Parameters - Page 2:
2025-05-03 15:00:11,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:11,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:11,934 - INFO - Response - Page 2:
2025-05-03 15:00:12,138 - INFO - 第 2 页获取到 100 条记录
2025-05-03 15:00:12,138 - INFO - Request Parameters - Page 3:
2025-05-03 15:00:12,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:12,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:12,591 - INFO - Response - Page 3:
2025-05-03 15:00:12,794 - INFO - 第 3 页获取到 100 条记录
2025-05-03 15:00:12,794 - INFO - Request Parameters - Page 4:
2025-05-03 15:00:12,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:12,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:13,264 - INFO - Response - Page 4:
2025-05-03 15:00:13,467 - INFO - 第 4 页获取到 100 条记录
2025-05-03 15:00:13,467 - INFO - Request Parameters - Page 5:
2025-05-03 15:00:13,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:13,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:13,905 - INFO - Response - Page 5:
2025-05-03 15:00:14,108 - INFO - 第 5 页获取到 100 条记录
2025-05-03 15:00:14,108 - INFO - Request Parameters - Page 6:
2025-05-03 15:00:14,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:14,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:14,609 - INFO - Response - Page 6:
2025-05-03 15:00:14,812 - INFO - 第 6 页获取到 100 条记录
2025-05-03 15:00:14,812 - INFO - Request Parameters - Page 7:
2025-05-03 15:00:14,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:14,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:15,328 - INFO - Response - Page 7:
2025-05-03 15:00:15,532 - INFO - 第 7 页获取到 70 条记录
2025-05-03 15:00:15,532 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 15:00:15,532 - INFO - 获取到 670 条表单数据
2025-05-03 15:00:15,532 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 15:00:15,547 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 15:00:15,547 - INFO - 开始处理日期: 2025-03
2025-05-03 15:00:15,547 - INFO - Request Parameters - Page 1:
2025-05-03 15:00:15,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:15,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:16,001 - INFO - Response - Page 1:
2025-05-03 15:00:16,204 - INFO - 第 1 页获取到 100 条记录
2025-05-03 15:00:16,204 - INFO - Request Parameters - Page 2:
2025-05-03 15:00:16,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:16,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:16,705 - INFO - Response - Page 2:
2025-05-03 15:00:16,908 - INFO - 第 2 页获取到 100 条记录
2025-05-03 15:00:16,908 - INFO - Request Parameters - Page 3:
2025-05-03 15:00:16,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:16,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:17,346 - INFO - Response - Page 3:
2025-05-03 15:00:17,550 - INFO - 第 3 页获取到 100 条记录
2025-05-03 15:00:17,550 - INFO - Request Parameters - Page 4:
2025-05-03 15:00:17,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:17,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:18,066 - INFO - Response - Page 4:
2025-05-03 15:00:18,269 - INFO - 第 4 页获取到 100 条记录
2025-05-03 15:00:18,269 - INFO - Request Parameters - Page 5:
2025-05-03 15:00:18,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:18,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:18,770 - INFO - Response - Page 5:
2025-05-03 15:00:18,973 - INFO - 第 5 页获取到 100 条记录
2025-05-03 15:00:18,973 - INFO - Request Parameters - Page 6:
2025-05-03 15:00:18,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:18,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:19,677 - INFO - Response - Page 6:
2025-05-03 15:00:19,880 - INFO - 第 6 页获取到 100 条记录
2025-05-03 15:00:19,880 - INFO - Request Parameters - Page 7:
2025-05-03 15:00:19,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:19,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:20,334 - INFO - Response - Page 7:
2025-05-03 15:00:20,537 - INFO - 第 7 页获取到 61 条记录
2025-05-03 15:00:20,537 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 15:00:20,537 - INFO - 获取到 661 条表单数据
2025-05-03 15:00:20,537 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 15:00:20,553 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 15:00:20,553 - INFO - 开始处理日期: 2025-04
2025-05-03 15:00:20,553 - INFO - Request Parameters - Page 1:
2025-05-03 15:00:20,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:20,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:21,100 - INFO - Response - Page 1:
2025-05-03 15:00:21,304 - INFO - 第 1 页获取到 100 条记录
2025-05-03 15:00:21,304 - INFO - Request Parameters - Page 2:
2025-05-03 15:00:21,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:21,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:21,757 - INFO - Response - Page 2:
2025-05-03 15:00:21,960 - INFO - 第 2 页获取到 100 条记录
2025-05-03 15:00:21,960 - INFO - Request Parameters - Page 3:
2025-05-03 15:00:21,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:21,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:22,383 - INFO - Response - Page 3:
2025-05-03 15:00:22,586 - INFO - 第 3 页获取到 100 条记录
2025-05-03 15:00:22,586 - INFO - Request Parameters - Page 4:
2025-05-03 15:00:22,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:22,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:23,071 - INFO - Response - Page 4:
2025-05-03 15:00:23,274 - INFO - 第 4 页获取到 100 条记录
2025-05-03 15:00:23,274 - INFO - Request Parameters - Page 5:
2025-05-03 15:00:23,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:23,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:23,775 - INFO - Response - Page 5:
2025-05-03 15:00:23,978 - INFO - 第 5 页获取到 100 条记录
2025-05-03 15:00:23,978 - INFO - Request Parameters - Page 6:
2025-05-03 15:00:23,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:23,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:24,447 - INFO - Response - Page 6:
2025-05-03 15:00:24,666 - INFO - 第 6 页获取到 100 条记录
2025-05-03 15:00:24,666 - INFO - Request Parameters - Page 7:
2025-05-03 15:00:24,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:24,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:25,026 - INFO - Response - Page 7:
2025-05-03 15:00:25,230 - INFO - 第 7 页获取到 27 条记录
2025-05-03 15:00:25,230 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 15:00:25,230 - INFO - 获取到 627 条表单数据
2025-05-03 15:00:25,230 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 15:00:25,245 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 15:00:25,245 - INFO - 开始处理日期: 2025-05
2025-05-03 15:00:25,245 - INFO - Request Parameters - Page 1:
2025-05-03 15:00:25,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:25,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:25,761 - INFO - Response - Page 1:
2025-05-03 15:00:25,965 - INFO - 第 1 页获取到 100 条记录
2025-05-03 15:00:25,965 - INFO - Request Parameters - Page 2:
2025-05-03 15:00:25,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:25,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:26,450 - INFO - Response - Page 2:
2025-05-03 15:00:26,653 - INFO - 第 2 页获取到 100 条记录
2025-05-03 15:00:26,653 - INFO - Request Parameters - Page 3:
2025-05-03 15:00:26,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:26,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:27,153 - INFO - Response - Page 3:
2025-05-03 15:00:27,357 - INFO - 第 3 页获取到 100 条记录
2025-05-03 15:00:27,357 - INFO - Request Parameters - Page 4:
2025-05-03 15:00:27,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:27,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:27,795 - INFO - Response - Page 4:
2025-05-03 15:00:27,998 - INFO - 第 4 页获取到 100 条记录
2025-05-03 15:00:27,998 - INFO - Request Parameters - Page 5:
2025-05-03 15:00:27,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:27,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:28,593 - INFO - Response - Page 5:
2025-05-03 15:00:28,796 - INFO - 第 5 页获取到 100 条记录
2025-05-03 15:00:28,796 - INFO - Request Parameters - Page 6:
2025-05-03 15:00:28,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 15:00:28,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 15:00:29,249 - INFO - Response - Page 6:
2025-05-03 15:00:29,453 - INFO - 第 6 页获取到 66 条记录
2025-05-03 15:00:29,453 - INFO - 查询完成，共获取到 566 条记录
2025-05-03 15:00:29,453 - INFO - 获取到 566 条表单数据
2025-05-03 15:00:29,453 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-03 15:00:29,453 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-03 15:00:29,969 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-03 15:00:29,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8971.0, 'new_value': 9339.0}, {'field': 'total_amount', 'old_value': 8971.0, 'new_value': 9339.0}]
2025-05-03 15:00:29,969 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-03 15:00:30,438 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-03 15:00:30,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8400.0, 'new_value': 8418.0}, {'field': 'total_amount', 'old_value': 8400.0, 'new_value': 8418.0}]
2025-05-03 15:00:30,454 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-03 15:00:30,923 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-03 15:00:30,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 366331.0, 'new_value': 638753.0}, {'field': 'total_amount', 'old_value': 366331.0, 'new_value': 638753.0}, {'field': 'order_count', 'old_value': 4970, 'new_value': 8974}]
2025-05-03 15:00:30,923 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-03 15:00:31,439 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-03 15:00:31,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8769.0, 'new_value': 16264.0}, {'field': 'total_amount', 'old_value': 8769.0, 'new_value': 16264.0}, {'field': 'order_count', 'old_value': 355, 'new_value': 558}]
2025-05-03 15:00:31,439 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-03 15:00:31,877 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-03 15:00:31,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1876.0, 'new_value': 3246.0}, {'field': 'total_amount', 'old_value': 1876.0, 'new_value': 3246.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 280}]
2025-05-03 15:00:31,877 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-03 15:00:32,331 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-03 15:00:32,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13992.0, 'new_value': 13992.3}, {'field': 'total_amount', 'old_value': 13992.0, 'new_value': 13992.3}]
2025-05-03 15:00:32,331 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-03 15:00:32,784 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-03 15:00:32,784 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1376.0}, {'field': 'total_amount', 'old_value': 1579.0, 'new_value': 2955.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 318}]
2025-05-03 15:00:32,784 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-03 15:00:33,363 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-03 15:00:33,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 445.0, 'new_value': 1117.0}, {'field': 'total_amount', 'old_value': 445.0, 'new_value': 1117.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 47}]
2025-05-03 15:00:33,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-03 15:00:33,801 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-03 15:00:33,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9000.0, 'new_value': 25000.0}, {'field': 'total_amount', 'old_value': 9000.0, 'new_value': 25000.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-03 15:00:33,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-03 15:00:34,223 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-03 15:00:34,223 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 13775.0}, {'field': 'total_amount', 'old_value': 18874.0, 'new_value': 32649.0}, {'field': 'order_count', 'old_value': 335, 'new_value': 490}]
2025-05-03 15:00:34,239 - INFO - 日期 2025-05 处理完成 - 更新: 10 条，插入: 0 条，错误: 0 条
2025-05-03 15:00:34,239 - INFO - 数据同步完成！更新: 10 条，插入: 0 条，错误: 0 条
2025-05-03 15:00:34,239 - INFO - =================同步完成====================
2025-05-03 18:00:03,936 - INFO - =================使用默认全量同步=============
2025-05-03 18:00:05,125 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-03 18:00:05,125 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 18:00:05,157 - INFO - 开始处理日期: 2025-01
2025-05-03 18:00:05,157 - INFO - Request Parameters - Page 1:
2025-05-03 18:00:05,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:05,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:06,236 - INFO - Response - Page 1:
2025-05-03 18:00:06,439 - INFO - 第 1 页获取到 100 条记录
2025-05-03 18:00:06,439 - INFO - Request Parameters - Page 2:
2025-05-03 18:00:06,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:06,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:06,971 - INFO - Response - Page 2:
2025-05-03 18:00:07,174 - INFO - 第 2 页获取到 100 条记录
2025-05-03 18:00:07,174 - INFO - Request Parameters - Page 3:
2025-05-03 18:00:07,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:07,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:07,706 - INFO - Response - Page 3:
2025-05-03 18:00:07,909 - INFO - 第 3 页获取到 100 条记录
2025-05-03 18:00:07,909 - INFO - Request Parameters - Page 4:
2025-05-03 18:00:07,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:07,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:08,363 - INFO - Response - Page 4:
2025-05-03 18:00:08,566 - INFO - 第 4 页获取到 100 条记录
2025-05-03 18:00:08,566 - INFO - Request Parameters - Page 5:
2025-05-03 18:00:08,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:08,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:09,067 - INFO - Response - Page 5:
2025-05-03 18:00:09,270 - INFO - 第 5 页获取到 100 条记录
2025-05-03 18:00:09,270 - INFO - Request Parameters - Page 6:
2025-05-03 18:00:09,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:09,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:09,740 - INFO - Response - Page 6:
2025-05-03 18:00:09,943 - INFO - 第 6 页获取到 100 条记录
2025-05-03 18:00:09,943 - INFO - Request Parameters - Page 7:
2025-05-03 18:00:09,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:09,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:10,428 - INFO - Response - Page 7:
2025-05-03 18:00:10,631 - INFO - 第 7 页获取到 82 条记录
2025-05-03 18:00:10,631 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 18:00:10,631 - INFO - 获取到 682 条表单数据
2025-05-03 18:00:10,631 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 18:00:10,647 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 18:00:10,647 - INFO - 开始处理日期: 2025-02
2025-05-03 18:00:10,647 - INFO - Request Parameters - Page 1:
2025-05-03 18:00:10,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:10,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:11,100 - INFO - Response - Page 1:
2025-05-03 18:00:11,304 - INFO - 第 1 页获取到 100 条记录
2025-05-03 18:00:11,304 - INFO - Request Parameters - Page 2:
2025-05-03 18:00:11,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:11,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:11,804 - INFO - Response - Page 2:
2025-05-03 18:00:12,008 - INFO - 第 2 页获取到 100 条记录
2025-05-03 18:00:12,008 - INFO - Request Parameters - Page 3:
2025-05-03 18:00:12,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:12,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:12,461 - INFO - Response - Page 3:
2025-05-03 18:00:12,665 - INFO - 第 3 页获取到 100 条记录
2025-05-03 18:00:12,665 - INFO - Request Parameters - Page 4:
2025-05-03 18:00:12,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:12,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:13,118 - INFO - Response - Page 4:
2025-05-03 18:00:13,321 - INFO - 第 4 页获取到 100 条记录
2025-05-03 18:00:13,321 - INFO - Request Parameters - Page 5:
2025-05-03 18:00:13,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:13,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:13,978 - INFO - Response - Page 5:
2025-05-03 18:00:14,182 - INFO - 第 5 页获取到 100 条记录
2025-05-03 18:00:14,182 - INFO - Request Parameters - Page 6:
2025-05-03 18:00:14,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:14,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:14,667 - INFO - Response - Page 6:
2025-05-03 18:00:14,870 - INFO - 第 6 页获取到 100 条记录
2025-05-03 18:00:14,870 - INFO - Request Parameters - Page 7:
2025-05-03 18:00:14,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:14,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:15,308 - INFO - Response - Page 7:
2025-05-03 18:00:15,511 - INFO - 第 7 页获取到 70 条记录
2025-05-03 18:00:15,511 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 18:00:15,511 - INFO - 获取到 670 条表单数据
2025-05-03 18:00:15,511 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 18:00:15,527 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 18:00:15,527 - INFO - 开始处理日期: 2025-03
2025-05-03 18:00:15,527 - INFO - Request Parameters - Page 1:
2025-05-03 18:00:15,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:15,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:15,996 - INFO - Response - Page 1:
2025-05-03 18:00:16,200 - INFO - 第 1 页获取到 100 条记录
2025-05-03 18:00:16,200 - INFO - Request Parameters - Page 2:
2025-05-03 18:00:16,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:16,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:16,700 - INFO - Response - Page 2:
2025-05-03 18:00:16,903 - INFO - 第 2 页获取到 100 条记录
2025-05-03 18:00:16,903 - INFO - Request Parameters - Page 3:
2025-05-03 18:00:16,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:16,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:17,451 - INFO - Response - Page 3:
2025-05-03 18:00:17,654 - INFO - 第 3 页获取到 100 条记录
2025-05-03 18:00:17,654 - INFO - Request Parameters - Page 4:
2025-05-03 18:00:17,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:17,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:18,139 - INFO - Response - Page 4:
2025-05-03 18:00:18,342 - INFO - 第 4 页获取到 100 条记录
2025-05-03 18:00:18,342 - INFO - Request Parameters - Page 5:
2025-05-03 18:00:18,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:18,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:18,812 - INFO - Response - Page 5:
2025-05-03 18:00:19,015 - INFO - 第 5 页获取到 100 条记录
2025-05-03 18:00:19,015 - INFO - Request Parameters - Page 6:
2025-05-03 18:00:19,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:19,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:19,469 - INFO - Response - Page 6:
2025-05-03 18:00:19,672 - INFO - 第 6 页获取到 100 条记录
2025-05-03 18:00:19,672 - INFO - Request Parameters - Page 7:
2025-05-03 18:00:19,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:19,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:20,126 - INFO - Response - Page 7:
2025-05-03 18:00:20,329 - INFO - 第 7 页获取到 61 条记录
2025-05-03 18:00:20,329 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 18:00:20,329 - INFO - 获取到 661 条表单数据
2025-05-03 18:00:20,329 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 18:00:20,345 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 18:00:20,345 - INFO - 开始处理日期: 2025-04
2025-05-03 18:00:20,345 - INFO - Request Parameters - Page 1:
2025-05-03 18:00:20,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:20,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:20,861 - INFO - Response - Page 1:
2025-05-03 18:00:21,064 - INFO - 第 1 页获取到 100 条记录
2025-05-03 18:00:21,064 - INFO - Request Parameters - Page 2:
2025-05-03 18:00:21,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:21,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:21,549 - INFO - Response - Page 2:
2025-05-03 18:00:21,752 - INFO - 第 2 页获取到 100 条记录
2025-05-03 18:00:21,752 - INFO - Request Parameters - Page 3:
2025-05-03 18:00:21,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:21,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:22,425 - INFO - Response - Page 3:
2025-05-03 18:00:22,628 - INFO - 第 3 页获取到 100 条记录
2025-05-03 18:00:22,628 - INFO - Request Parameters - Page 4:
2025-05-03 18:00:22,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:22,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:23,082 - INFO - Response - Page 4:
2025-05-03 18:00:23,285 - INFO - 第 4 页获取到 100 条记录
2025-05-03 18:00:23,285 - INFO - Request Parameters - Page 5:
2025-05-03 18:00:23,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:23,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:23,754 - INFO - Response - Page 5:
2025-05-03 18:00:23,958 - INFO - 第 5 页获取到 100 条记录
2025-05-03 18:00:23,958 - INFO - Request Parameters - Page 6:
2025-05-03 18:00:23,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:23,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:24,458 - INFO - Response - Page 6:
2025-05-03 18:00:24,662 - INFO - 第 6 页获取到 100 条记录
2025-05-03 18:00:24,662 - INFO - Request Parameters - Page 7:
2025-05-03 18:00:24,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:24,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:25,068 - INFO - Response - Page 7:
2025-05-03 18:00:25,272 - INFO - 第 7 页获取到 27 条记录
2025-05-03 18:00:25,272 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 18:00:25,272 - INFO - 获取到 627 条表单数据
2025-05-03 18:00:25,272 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 18:00:25,272 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-05-03 18:00:25,741 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MQ5
2025-05-03 18:00:25,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 248469.0, 'new_value': 248620.0}, {'field': 'offline_amount', 'old_value': 12501.0, 'new_value': 13109.0}, {'field': 'total_amount', 'old_value': 260970.0, 'new_value': 261729.0}]
2025-05-03 18:00:25,741 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-05-03 18:00:26,085 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MS7
2025-05-03 18:00:26,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19923.45, 'new_value': 8172.44}, {'field': 'total_amount', 'old_value': 81806.71, 'new_value': 70055.7}]
2025-05-03 18:00:26,085 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-05-03 18:00:26,570 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-05-03 18:00:26,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186638.03, 'new_value': 178638.03}, {'field': 'total_amount', 'old_value': 193033.03, 'new_value': 185033.03}]
2025-05-03 18:00:26,570 - INFO - 日期 2025-04 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-03 18:00:26,570 - INFO - 开始处理日期: 2025-05
2025-05-03 18:00:26,570 - INFO - Request Parameters - Page 1:
2025-05-03 18:00:26,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:26,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:26,992 - INFO - Response - Page 1:
2025-05-03 18:00:27,196 - INFO - 第 1 页获取到 100 条记录
2025-05-03 18:00:27,196 - INFO - Request Parameters - Page 2:
2025-05-03 18:00:27,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:27,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:27,634 - INFO - Response - Page 2:
2025-05-03 18:00:27,837 - INFO - 第 2 页获取到 100 条记录
2025-05-03 18:00:27,837 - INFO - Request Parameters - Page 3:
2025-05-03 18:00:27,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:27,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:28,259 - INFO - Response - Page 3:
2025-05-03 18:00:28,463 - INFO - 第 3 页获取到 100 条记录
2025-05-03 18:00:28,463 - INFO - Request Parameters - Page 4:
2025-05-03 18:00:28,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:28,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:28,979 - INFO - Response - Page 4:
2025-05-03 18:00:29,182 - INFO - 第 4 页获取到 100 条记录
2025-05-03 18:00:29,182 - INFO - Request Parameters - Page 5:
2025-05-03 18:00:29,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:29,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:29,651 - INFO - Response - Page 5:
2025-05-03 18:00:29,855 - INFO - 第 5 页获取到 100 条记录
2025-05-03 18:00:29,855 - INFO - Request Parameters - Page 6:
2025-05-03 18:00:29,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 18:00:29,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 18:00:30,340 - INFO - Response - Page 6:
2025-05-03 18:00:30,543 - INFO - 第 6 页获取到 66 条记录
2025-05-03 18:00:30,543 - INFO - 查询完成，共获取到 566 条记录
2025-05-03 18:00:30,543 - INFO - 获取到 566 条表单数据
2025-05-03 18:00:30,543 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-03 18:00:30,543 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-03 18:00:30,950 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-03 18:00:30,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 517.8, 'new_value': 1200.5}, {'field': 'total_amount', 'old_value': 517.8, 'new_value': 1200.5}, {'field': 'order_count', 'old_value': 37, 'new_value': 84}]
2025-05-03 18:00:30,950 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-03 18:00:31,403 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-03 18:00:31,403 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1875.0, 'new_value': 6434.0}, {'field': 'offline_amount', 'old_value': 7839.0, 'new_value': 15755.0}, {'field': 'total_amount', 'old_value': 9714.0, 'new_value': 22189.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 152}]
2025-05-03 18:00:31,403 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-03 18:00:31,779 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-03 18:00:31,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 640.71, 'new_value': 1337.29}, {'field': 'offline_amount', 'old_value': 2837.6, 'new_value': 6023.6}, {'field': 'total_amount', 'old_value': 3478.31, 'new_value': 7360.89}, {'field': 'order_count', 'old_value': 47, 'new_value': 97}]
2025-05-03 18:00:31,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-03 18:00:32,248 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-03 18:00:32,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8008.0, 'new_value': 7752.0}, {'field': 'total_amount', 'old_value': 8008.0, 'new_value': 7752.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 62}]
2025-05-03 18:00:32,248 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-03 18:00:32,639 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-03 18:00:32,639 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 704.97, 'new_value': 1531.28}, {'field': 'offline_amount', 'old_value': 504.74, 'new_value': 1250.28}, {'field': 'total_amount', 'old_value': 1209.71, 'new_value': 2781.56}, {'field': 'order_count', 'old_value': 69, 'new_value': 158}]
2025-05-03 18:00:32,639 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-03 18:00:33,077 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-03 18:00:33,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202.7, 'new_value': 666.0}, {'field': 'offline_amount', 'old_value': 3582.5, 'new_value': 10438.2}, {'field': 'total_amount', 'old_value': 3785.2, 'new_value': 11104.2}, {'field': 'order_count', 'old_value': 68, 'new_value': 180}]
2025-05-03 18:00:33,077 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-03 18:00:33,499 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-03 18:00:33,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2785.43, 'new_value': 4996.13}, {'field': 'total_amount', 'old_value': 2785.43, 'new_value': 4996.13}, {'field': 'order_count', 'old_value': 139, 'new_value': 239}]
2025-05-03 18:00:33,499 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-03 18:00:34,015 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-03 18:00:34,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2038.0, 'new_value': 3447.0}, {'field': 'total_amount', 'old_value': 2038.0, 'new_value': 3447.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 17}]
2025-05-03 18:00:34,015 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-03 18:00:34,500 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-03 18:00:34,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 703.74, 'new_value': 1262.55}, {'field': 'offline_amount', 'old_value': 26665.8, 'new_value': 20740.6}, {'field': 'total_amount', 'old_value': 27369.54, 'new_value': 22003.15}, {'field': 'order_count', 'old_value': 758, 'new_value': 1035}]
2025-05-03 18:00:34,500 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-03 18:00:34,938 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-03 18:00:34,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3997.0, 'new_value': 7378.0}, {'field': 'total_amount', 'old_value': 3997.0, 'new_value': 7378.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 380}]
2025-05-03 18:00:34,938 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-03 18:00:35,423 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-03 18:00:35,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5295.6, 'new_value': 9494.0}, {'field': 'total_amount', 'old_value': 5295.6, 'new_value': 9494.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 48}]
2025-05-03 18:00:35,423 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-03 18:00:35,939 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-03 18:00:35,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4002.0, 'new_value': 4080.0}, {'field': 'total_amount', 'old_value': 4002.0, 'new_value': 4080.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 52}]
2025-05-03 18:00:35,939 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-03 18:00:36,408 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-03 18:00:36,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1108.0, 'new_value': 3808.0}, {'field': 'total_amount', 'old_value': 1201.0, 'new_value': 3901.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-05-03 18:00:36,408 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-03 18:00:36,831 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-03 18:00:36,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 444.37, 'new_value': 902.91}, {'field': 'offline_amount', 'old_value': 2009.7, 'new_value': 3682.7}, {'field': 'total_amount', 'old_value': 2454.07, 'new_value': 4585.61}, {'field': 'order_count', 'old_value': 46, 'new_value': 88}]
2025-05-03 18:00:36,831 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-03 18:00:37,284 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-03 18:00:37,284 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2718.0, 'new_value': 4397.0}, {'field': 'total_amount', 'old_value': 2718.0, 'new_value': 4397.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 617}]
2025-05-03 18:00:37,284 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-03 18:00:37,738 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-03 18:00:37,754 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2718.0, 'new_value': 5237.0}, {'field': 'total_amount', 'old_value': 2718.0, 'new_value': 5237.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 617}]
2025-05-03 18:00:37,754 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-03 18:00:38,145 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-03 18:00:38,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7288.64, 'new_value': 7533.92}, {'field': 'total_amount', 'old_value': 7288.64, 'new_value': 7533.92}, {'field': 'order_count', 'old_value': 71, 'new_value': 65}]
2025-05-03 18:00:38,145 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-03 18:00:38,583 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-03 18:00:38,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1664.0, 'new_value': 5278.0}, {'field': 'total_amount', 'old_value': 1664.0, 'new_value': 5278.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 16}]
2025-05-03 18:00:38,583 - INFO - 日期 2025-05 处理完成 - 更新: 18 条，插入: 0 条，错误: 0 条
2025-05-03 18:00:38,583 - INFO - 数据同步完成！更新: 21 条，插入: 0 条，错误: 0 条
2025-05-03 18:00:38,583 - INFO - =================同步完成====================
2025-05-03 21:00:04,120 - INFO - =================使用默认全量同步=============
2025-05-03 21:00:05,308 - INFO - MySQL查询成功，共获取 3206 条记录
2025-05-03 21:00:05,308 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-03 21:00:05,340 - INFO - 开始处理日期: 2025-01
2025-05-03 21:00:05,340 - INFO - Request Parameters - Page 1:
2025-05-03 21:00:05,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:05,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:06,263 - INFO - Response - Page 1:
2025-05-03 21:00:06,466 - INFO - 第 1 页获取到 100 条记录
2025-05-03 21:00:06,466 - INFO - Request Parameters - Page 2:
2025-05-03 21:00:06,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:06,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:06,998 - INFO - Response - Page 2:
2025-05-03 21:00:07,201 - INFO - 第 2 页获取到 100 条记录
2025-05-03 21:00:07,201 - INFO - Request Parameters - Page 3:
2025-05-03 21:00:07,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:07,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:07,921 - INFO - Response - Page 3:
2025-05-03 21:00:08,124 - INFO - 第 3 页获取到 100 条记录
2025-05-03 21:00:08,124 - INFO - Request Parameters - Page 4:
2025-05-03 21:00:08,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:08,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:08,640 - INFO - Response - Page 4:
2025-05-03 21:00:08,843 - INFO - 第 4 页获取到 100 条记录
2025-05-03 21:00:08,843 - INFO - Request Parameters - Page 5:
2025-05-03 21:00:08,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:08,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:09,360 - INFO - Response - Page 5:
2025-05-03 21:00:09,563 - INFO - 第 5 页获取到 100 条记录
2025-05-03 21:00:09,563 - INFO - Request Parameters - Page 6:
2025-05-03 21:00:09,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:09,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:09,985 - INFO - Response - Page 6:
2025-05-03 21:00:10,189 - INFO - 第 6 页获取到 100 条记录
2025-05-03 21:00:10,189 - INFO - Request Parameters - Page 7:
2025-05-03 21:00:10,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:10,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:10,642 - INFO - Response - Page 7:
2025-05-03 21:00:10,846 - INFO - 第 7 页获取到 82 条记录
2025-05-03 21:00:10,846 - INFO - 查询完成，共获取到 682 条记录
2025-05-03 21:00:10,846 - INFO - 获取到 682 条表单数据
2025-05-03 21:00:10,846 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-03 21:00:10,861 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 21:00:10,861 - INFO - 开始处理日期: 2025-02
2025-05-03 21:00:10,861 - INFO - Request Parameters - Page 1:
2025-05-03 21:00:10,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:10,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:11,330 - INFO - Response - Page 1:
2025-05-03 21:00:11,534 - INFO - 第 1 页获取到 100 条记录
2025-05-03 21:00:11,534 - INFO - Request Parameters - Page 2:
2025-05-03 21:00:11,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:11,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:12,003 - INFO - Response - Page 2:
2025-05-03 21:00:12,206 - INFO - 第 2 页获取到 100 条记录
2025-05-03 21:00:12,206 - INFO - Request Parameters - Page 3:
2025-05-03 21:00:12,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:12,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:12,676 - INFO - Response - Page 3:
2025-05-03 21:00:12,879 - INFO - 第 3 页获取到 100 条记录
2025-05-03 21:00:12,879 - INFO - Request Parameters - Page 4:
2025-05-03 21:00:12,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:12,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:13,395 - INFO - Response - Page 4:
2025-05-03 21:00:13,599 - INFO - 第 4 页获取到 100 条记录
2025-05-03 21:00:13,599 - INFO - Request Parameters - Page 5:
2025-05-03 21:00:13,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:13,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:14,036 - INFO - Response - Page 5:
2025-05-03 21:00:14,255 - INFO - 第 5 页获取到 100 条记录
2025-05-03 21:00:14,255 - INFO - Request Parameters - Page 6:
2025-05-03 21:00:14,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:14,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:14,740 - INFO - Response - Page 6:
2025-05-03 21:00:14,944 - INFO - 第 6 页获取到 100 条记录
2025-05-03 21:00:14,944 - INFO - Request Parameters - Page 7:
2025-05-03 21:00:14,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:14,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:15,413 - INFO - Response - Page 7:
2025-05-03 21:00:15,616 - INFO - 第 7 页获取到 70 条记录
2025-05-03 21:00:15,616 - INFO - 查询完成，共获取到 670 条记录
2025-05-03 21:00:15,616 - INFO - 获取到 670 条表单数据
2025-05-03 21:00:15,616 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-03 21:00:15,632 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 21:00:15,632 - INFO - 开始处理日期: 2025-03
2025-05-03 21:00:15,632 - INFO - Request Parameters - Page 1:
2025-05-03 21:00:15,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:15,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:16,101 - INFO - Response - Page 1:
2025-05-03 21:00:16,305 - INFO - 第 1 页获取到 100 条记录
2025-05-03 21:00:16,305 - INFO - Request Parameters - Page 2:
2025-05-03 21:00:16,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:16,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:16,758 - INFO - Response - Page 2:
2025-05-03 21:00:16,961 - INFO - 第 2 页获取到 100 条记录
2025-05-03 21:00:16,961 - INFO - Request Parameters - Page 3:
2025-05-03 21:00:16,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:16,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:17,431 - INFO - Response - Page 3:
2025-05-03 21:00:17,634 - INFO - 第 3 页获取到 100 条记录
2025-05-03 21:00:17,634 - INFO - Request Parameters - Page 4:
2025-05-03 21:00:17,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:17,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:18,056 - INFO - Response - Page 4:
2025-05-03 21:00:18,260 - INFO - 第 4 页获取到 100 条记录
2025-05-03 21:00:18,260 - INFO - Request Parameters - Page 5:
2025-05-03 21:00:18,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:18,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:18,776 - INFO - Response - Page 5:
2025-05-03 21:00:18,979 - INFO - 第 5 页获取到 100 条记录
2025-05-03 21:00:18,979 - INFO - Request Parameters - Page 6:
2025-05-03 21:00:18,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:18,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:19,464 - INFO - Response - Page 6:
2025-05-03 21:00:19,667 - INFO - 第 6 页获取到 100 条记录
2025-05-03 21:00:19,667 - INFO - Request Parameters - Page 7:
2025-05-03 21:00:19,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:19,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:20,059 - INFO - Response - Page 7:
2025-05-03 21:00:20,262 - INFO - 第 7 页获取到 61 条记录
2025-05-03 21:00:20,262 - INFO - 查询完成，共获取到 661 条记录
2025-05-03 21:00:20,262 - INFO - 获取到 661 条表单数据
2025-05-03 21:00:20,262 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-03 21:00:20,277 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-03 21:00:20,277 - INFO - 开始处理日期: 2025-04
2025-05-03 21:00:20,277 - INFO - Request Parameters - Page 1:
2025-05-03 21:00:20,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:20,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:20,715 - INFO - Response - Page 1:
2025-05-03 21:00:20,919 - INFO - 第 1 页获取到 100 条记录
2025-05-03 21:00:20,919 - INFO - Request Parameters - Page 2:
2025-05-03 21:00:20,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:20,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:21,404 - INFO - Response - Page 2:
2025-05-03 21:00:21,607 - INFO - 第 2 页获取到 100 条记录
2025-05-03 21:00:21,607 - INFO - Request Parameters - Page 3:
2025-05-03 21:00:21,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:21,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:22,076 - INFO - Response - Page 3:
2025-05-03 21:00:22,280 - INFO - 第 3 页获取到 100 条记录
2025-05-03 21:00:22,280 - INFO - Request Parameters - Page 4:
2025-05-03 21:00:22,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:22,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:22,780 - INFO - Response - Page 4:
2025-05-03 21:00:22,983 - INFO - 第 4 页获取到 100 条记录
2025-05-03 21:00:22,983 - INFO - Request Parameters - Page 5:
2025-05-03 21:00:22,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:22,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:23,421 - INFO - Response - Page 5:
2025-05-03 21:00:23,625 - INFO - 第 5 页获取到 100 条记录
2025-05-03 21:00:23,625 - INFO - Request Parameters - Page 6:
2025-05-03 21:00:23,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:23,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:24,063 - INFO - Response - Page 6:
2025-05-03 21:00:24,266 - INFO - 第 6 页获取到 100 条记录
2025-05-03 21:00:24,266 - INFO - Request Parameters - Page 7:
2025-05-03 21:00:24,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:24,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:24,657 - INFO - Response - Page 7:
2025-05-03 21:00:24,860 - INFO - 第 7 页获取到 27 条记录
2025-05-03 21:00:24,860 - INFO - 查询完成，共获取到 627 条记录
2025-05-03 21:00:24,860 - INFO - 获取到 627 条表单数据
2025-05-03 21:00:24,860 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-03 21:00:24,876 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-05-03 21:00:25,330 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MZ5
2025-05-03 21:00:25,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37063.0, 'new_value': 38948.0}, {'field': 'total_amount', 'old_value': 37063.0, 'new_value': 38948.0}, {'field': 'order_count', 'old_value': 203, 'new_value': 214}]
2025-05-03 21:00:25,330 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-05-03 21:00:25,768 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-05-03 21:00:25,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230085.5, 'new_value': 238084.5}, {'field': 'total_amount', 'old_value': 334263.5, 'new_value': 342262.5}, {'field': 'order_count', 'old_value': 66, 'new_value': 67}]
2025-05-03 21:00:25,783 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU6
2025-05-03 21:00:26,253 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MU6
2025-05-03 21:00:26,253 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 115.56, 'new_value': 388.24}, {'field': 'offline_amount', 'old_value': 21377.94, 'new_value': 23265.4}, {'field': 'total_amount', 'old_value': 21493.5, 'new_value': 23653.64}, {'field': 'order_count', 'old_value': 1038, 'new_value': 1143}]
2025-05-03 21:00:26,253 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN7
2025-05-03 21:00:26,659 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MN7
2025-05-03 21:00:26,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82040.0, 'new_value': 91960.0}, {'field': 'total_amount', 'old_value': 82040.0, 'new_value': 91960.0}, {'field': 'order_count', 'old_value': 595, 'new_value': 659}]
2025-05-03 21:00:26,659 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB8
2025-05-03 21:00:27,238 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MB8
2025-05-03 21:00:27,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19061.38, 'new_value': 21336.18}, {'field': 'offline_amount', 'old_value': 109131.26, 'new_value': 121183.04}, {'field': 'total_amount', 'old_value': 128192.64, 'new_value': 142519.22}, {'field': 'order_count', 'old_value': 3946, 'new_value': 4332}]
2025-05-03 21:00:27,238 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSH
2025-05-03 21:00:27,660 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSH
2025-05-03 21:00:27,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26936.27, 'new_value': 38247.28}, {'field': 'offline_amount', 'old_value': 83553.25, 'new_value': 93843.81}, {'field': 'total_amount', 'old_value': 110489.52, 'new_value': 132091.09}, {'field': 'order_count', 'old_value': 5502, 'new_value': 6591}]
2025-05-03 21:00:27,660 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-05-03 21:00:28,067 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M8I
2025-05-03 21:00:28,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151171.49, 'new_value': 161404.76}, {'field': 'total_amount', 'old_value': 151171.49, 'new_value': 161404.76}, {'field': 'order_count', 'old_value': 5865, 'new_value': 6246}]
2025-05-03 21:00:28,067 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ7
2025-05-03 21:00:28,442 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MZ7
2025-05-03 21:00:28,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341647.67, 'new_value': 384243.21}, {'field': 'total_amount', 'old_value': 341647.67, 'new_value': 384243.21}, {'field': 'order_count', 'old_value': 14474, 'new_value': 16183}]
2025-05-03 21:00:28,442 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-05-03 21:00:28,865 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M08
2025-05-03 21:00:28,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 535463.32, 'new_value': 552049.32}, {'field': 'total_amount', 'old_value': 535463.32, 'new_value': 552049.32}, {'field': 'order_count', 'old_value': 106, 'new_value': 110}]
2025-05-03 21:00:28,865 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M18
2025-05-03 21:00:29,303 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M18
2025-05-03 21:00:29,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146641.88, 'new_value': 166652.12}, {'field': 'total_amount', 'old_value': 146641.88, 'new_value': 166652.12}, {'field': 'order_count', 'old_value': 2388, 'new_value': 2678}]
2025-05-03 21:00:29,303 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-05-03 21:00:29,709 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-05-03 21:00:29,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178638.03, 'new_value': 151638.03}, {'field': 'total_amount', 'old_value': 185033.03, 'new_value': 158033.03}]
2025-05-03 21:00:29,725 - INFO - 日期 2025-04 处理完成 - 更新: 11 条，插入: 0 条，错误: 0 条
2025-05-03 21:00:29,725 - INFO - 开始处理日期: 2025-05
2025-05-03 21:00:29,725 - INFO - Request Parameters - Page 1:
2025-05-03 21:00:29,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:29,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:30,210 - INFO - Response - Page 1:
2025-05-03 21:00:30,413 - INFO - 第 1 页获取到 100 条记录
2025-05-03 21:00:30,413 - INFO - Request Parameters - Page 2:
2025-05-03 21:00:30,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:30,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:30,867 - INFO - Response - Page 2:
2025-05-03 21:00:31,070 - INFO - 第 2 页获取到 100 条记录
2025-05-03 21:00:31,070 - INFO - Request Parameters - Page 3:
2025-05-03 21:00:31,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:31,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:31,477 - INFO - Response - Page 3:
2025-05-03 21:00:31,680 - INFO - 第 3 页获取到 100 条记录
2025-05-03 21:00:31,680 - INFO - Request Parameters - Page 4:
2025-05-03 21:00:31,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:31,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:32,103 - INFO - Response - Page 4:
2025-05-03 21:00:32,306 - INFO - 第 4 页获取到 100 条记录
2025-05-03 21:00:32,306 - INFO - Request Parameters - Page 5:
2025-05-03 21:00:32,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:32,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:32,728 - INFO - Response - Page 5:
2025-05-03 21:00:32,932 - INFO - 第 5 页获取到 100 条记录
2025-05-03 21:00:32,932 - INFO - Request Parameters - Page 6:
2025-05-03 21:00:32,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-03 21:00:32,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-03 21:00:33,369 - INFO - Response - Page 6:
2025-05-03 21:00:33,573 - INFO - 第 6 页获取到 66 条记录
2025-05-03 21:00:33,573 - INFO - 查询完成，共获取到 566 条记录
2025-05-03 21:00:33,573 - INFO - 获取到 566 条表单数据
2025-05-03 21:00:33,573 - INFO - 当前日期 2025-05 有 566 条MySQL数据需要处理
2025-05-03 21:00:33,588 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-03 21:00:33,980 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF
2025-05-03 21:00:33,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1750.0, 'new_value': 2400.0}, {'field': 'total_amount', 'old_value': 1750.0, 'new_value': 2400.0}, {'field': 'order_count', 'old_value': 240, 'new_value': 323}]
2025-05-03 21:00:33,980 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-03 21:00:33,980 - INFO - 数据同步完成！更新: 12 条，插入: 0 条，错误: 0 条
2025-05-03 21:00:33,980 - INFO - =================同步完成====================
