2025-05-28 08:00:03,572 - INFO - ==================================================
2025-05-28 08:00:03,572 - INFO - 程序启动 - 版本 v1.0.0
2025-05-28 08:00:03,572 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250528.log
2025-05-28 08:00:03,572 - INFO - ==================================================
2025-05-28 08:00:03,572 - INFO - 程序入口点: __main__
2025-05-28 08:00:03,572 - INFO - ==================================================
2025-05-28 08:00:03,572 - INFO - 程序启动 - 版本 v1.0.1
2025-05-28 08:00:03,572 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250528.log
2025-05-28 08:00:03,572 - INFO - ==================================================
2025-05-28 08:00:03,885 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-28 08:00:03,885 - INFO - sales_data表已存在，无需创建
2025-05-28 08:00:03,885 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-28 08:00:03,885 - INFO - DataSyncManager初始化完成
2025-05-28 08:00:03,885 - INFO - 未提供日期参数，使用默认值
2025-05-28 08:00:03,885 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-28 08:00:03,885 - INFO - 开始综合数据同步流程...
2025-05-28 08:00:03,885 - INFO - 正在获取数衍平台日销售数据...
2025-05-28 08:00:03,885 - INFO - 查询数衍平台数据，时间段为: 2025-03-28, 2025-05-27
2025-05-28 08:00:03,885 - INFO - 正在获取********至********的数据
2025-05-28 08:00:03,885 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:03,885 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E6ADDCB1512CDCA4BF9A8EA248F715F5'}
2025-05-28 08:00:06,900 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:06,916 - INFO - 过滤后保留 1535 条记录
2025-05-28 08:00:08,916 - INFO - 正在获取********至********的数据
2025-05-28 08:00:08,916 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:08,916 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '665252C1CD2760BEF989869B395BBD20'}
2025-05-28 08:00:11,057 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:11,072 - INFO - 过滤后保留 1497 条记录
2025-05-28 08:00:13,088 - INFO - 正在获取********至********的数据
2025-05-28 08:00:13,088 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:13,088 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '239163D9BD9FD1E51E7077C24965CBF0'}
2025-05-28 08:00:14,900 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:14,900 - INFO - 过滤后保留 1509 条记录
2025-05-28 08:00:16,916 - INFO - 正在获取********至********的数据
2025-05-28 08:00:16,916 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:16,916 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A1800A587C92341EEECB3EA47834F348'}
2025-05-28 08:00:18,853 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:18,869 - INFO - 过滤后保留 1487 条记录
2025-05-28 08:00:20,885 - INFO - 正在获取********至********的数据
2025-05-28 08:00:20,885 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:20,885 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BF091F3434BB9FEB8038DECBC39458B3'}
2025-05-28 08:00:22,635 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:22,635 - INFO - 过滤后保留 1498 条记录
2025-05-28 08:00:24,650 - INFO - 正在获取********至********的数据
2025-05-28 08:00:24,650 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:24,650 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'ED24E61DB4E2E9C15F67D6CC4B33A21A'}
2025-05-28 08:00:26,181 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:26,197 - INFO - 过滤后保留 1459 条记录
2025-05-28 08:00:28,213 - INFO - 正在获取********至********的数据
2025-05-28 08:00:28,213 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:28,213 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '720574D3455C30270CCC9FEC2716DF13'}
2025-05-28 08:00:29,931 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:29,947 - INFO - 过滤后保留 1489 条记录
2025-05-28 08:00:31,963 - INFO - 正在获取********至********的数据
2025-05-28 08:00:31,963 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:31,963 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C895EA4C995C8FAF4E9033B04EE9CE53'}
2025-05-28 08:00:33,916 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:33,931 - INFO - 过滤后保留 1473 条记录
2025-05-28 08:00:35,931 - INFO - 正在获取********至********的数据
2025-05-28 08:00:35,931 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-28 08:00:35,931 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C7225AE28E632DBEC87E2BDE1093F215'}
2025-05-28 08:00:37,056 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-28 08:00:37,072 - INFO - 过滤后保留 1031 条记录
2025-05-28 08:00:39,088 - INFO - 开始保存数据到SQLite数据库，共 12978 条记录待处理
2025-05-28 08:00:39,588 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-05-01
2025-05-28 08:00:39,588 - INFO - 变更字段: recommend_amount: 2783.0 -> 8528.0, daily_bill_amount: 0.0 -> 8528.0
2025-05-28 08:00:39,697 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-05-11
2025-05-28 08:00:39,713 - INFO - 变更字段: amount: 1381 -> 1840, count: 7 -> 8, instore_amount: 1750.0 -> 2209.0, instore_count: 7 -> 8
2025-05-28 08:00:39,760 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-21
2025-05-28 08:00:39,760 - INFO - 变更字段: recommend_amount: 8220.63 -> 8235.53, amount: 8220 -> 8235, count: 168 -> 169, instore_amount: 7205.9 -> 7220.8, instore_count: 142 -> 143
2025-05-28 08:00:39,775 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EN1GSFF80I86N3H2U18C001EHI, sale_time=2025-05-19
2025-05-28 08:00:39,775 - INFO - 变更字段: recommend_amount: 499.0 -> 1377.0, daily_bill_amount: 0.0 -> 1377.0
2025-05-28 08:00:39,775 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-05-18
2025-05-28 08:00:39,775 - INFO - 变更字段: amount: 1790 -> 2833, count: 4 -> 5, instore_amount: 1790.0 -> 2833.0, instore_count: 4 -> 5
2025-05-28 08:00:39,791 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIP3GSKTFR6E7AERKQ83J2001UN4, sale_time=2025-05-22
2025-05-28 08:00:39,791 - INFO - 变更字段: amount: 1926 -> 2421, count: 10 -> 11, instore_amount: 1926.8 -> 2421.8, instore_count: 10 -> 11
2025-05-28 08:00:39,791 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-26
2025-05-28 08:00:39,791 - INFO - 变更字段: recommend_amount: 0.0 -> 3251.9, daily_bill_amount: 0.0 -> 3251.9
2025-05-28 08:00:39,791 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=78655ECA4A32471AB7842F8DE2018120, sale_time=2025-05-26
2025-05-28 08:00:39,791 - INFO - 变更字段: recommend_amount: 18097.0 -> 19285.0, amount: 18097 -> 19285, count: 3 -> 4, instore_amount: 18097.0 -> 19285.0, instore_count: 3 -> 4
2025-05-28 08:00:39,791 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1HHDS9O4DUBH2L6U1G19QP11V40018CI, sale_time=2025-05-26
2025-05-28 08:00:39,791 - INFO - 变更字段: recommend_amount: 0.0 -> 3219.4, daily_bill_amount: 0.0 -> 3219.4
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9PGR3703D752ASKKUBQUM50018DU, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: recommend_amount: 8792.0 -> 8910.2, amount: 8792 -> 8910, count: 108 -> 109, online_amount: 0.0 -> 118.2, online_count: 0 -> 1
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: recommend_amount: 2309.42 -> 2465.02, amount: 2234 -> 2465, count: 45 -> 48, online_amount: 116.43 -> 346.56, online_count: 4 -> 7
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: amount: 3408 -> 3384, online_amount: 1794.27 -> 1770.27
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDSD911K3E7Q2OV4FVC7C800148P, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: amount: 3619 -> 3735, count: 168 -> 172, instore_amount: 3703.54 -> 3819.64, instore_count: 168 -> 172
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDS537TI7U7Q2OV4FVC7C400148L, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: amount: 117 -> 375, count: 1 -> 8, instore_amount: 117.0 -> 375.5, instore_count: 1 -> 8
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: amount: 2612 -> 2657, count: 45 -> 47, online_amount: 662.01 -> 706.71, online_count: 35 -> 37
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: amount: 939 -> 974, count: 33 -> 35, online_amount: 627.72 -> 662.52, online_count: 26 -> 28
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-05-25
2025-05-28 08:00:39,806 - INFO - 变更字段: recommend_amount: 4836.5 -> 3458.6, daily_bill_amount: 4836.5 -> 3458.6
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: recommend_amount: 0.0 -> 7755.0, daily_bill_amount: 0.0 -> 7755.0
2025-05-28 08:00:39,806 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-26
2025-05-28 08:00:39,806 - INFO - 变更字段: amount: 2343 -> 6831, count: 5 -> 6, instore_amount: 2343.0 -> 6831.0, instore_count: 5 -> 6
2025-05-28 08:00:39,822 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-26
2025-05-28 08:00:39,822 - INFO - 变更字段: recommend_amount: 1832.96 -> 1802.76, amount: 1832 -> 1802, count: 112 -> 113, online_amount: 1507.91 -> 1513.91, online_count: 95 -> 96
2025-05-28 08:00:39,822 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-05-26
2025-05-28 08:00:39,822 - INFO - 变更字段: recommend_amount: 8727.05 -> 8909.55, amount: 8727 -> 8909, count: 205 -> 206, instore_amount: 7405.32 -> 7604.32, instore_count: 172 -> 173, online_amount: 1321.73 -> 1305.23
2025-05-28 08:00:39,822 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-05-26
2025-05-28 08:00:39,822 - INFO - 变更字段: amount: 5717 -> 5757, count: 235 -> 238, instore_amount: 3190.21 -> 3202.21, instore_count: 118 -> 119, online_amount: 2615.5 -> 2643.6, online_count: 117 -> 119
2025-05-28 08:00:39,822 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-05-26
2025-05-28 08:00:39,822 - INFO - 变更字段: recommend_amount: 1043.03 -> 1049.3, amount: 1043 -> 1049, count: 70 -> 72, online_amount: 711.55 -> 717.82, online_count: 53 -> 55
2025-05-28 08:00:39,822 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-26
2025-05-28 08:00:39,822 - INFO - 变更字段: recommend_amount: 4217.39 -> 4234.99, amount: 4217 -> 4234, count: 240 -> 242, online_amount: 3218.85 -> 3236.45, online_count: 189 -> 191
2025-05-28 08:00:39,822 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-26
2025-05-28 08:00:39,822 - INFO - 变更字段: recommend_amount: 0.0 -> 16332.1, daily_bill_amount: 0.0 -> 16332.1
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: amount: 19902 -> 20420, count: 144 -> 145, instore_amount: 11686.98 -> 12204.98, instore_count: 51 -> 52
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: amount: 8576 -> 8578, count: 79 -> 80, online_amount: 1879.8 -> 1881.8, online_count: 38 -> 39
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: amount: 2922 -> 2932, count: 158 -> 159, online_amount: 1701.83 -> 1711.73, online_count: 77 -> 78
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: amount: 12672 -> 12667
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-05-25
2025-05-28 08:00:39,838 - INFO - 变更字段: recommend_amount: 18775.18 -> 19244.18, amount: 18775 -> 19244, count: 103 -> 104, instore_amount: 18775.18 -> 19244.18, instore_count: 103 -> 104
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9G31FV3GL0I86N3H2U190001EI6, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: amount: 0 -> 12498, count: 0 -> 2, instore_amount: 0.0 -> 12498.0, instore_count: 0 -> 2
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: recommend_amount: 0.0 -> 8297.0, daily_bill_amount: 0.0 -> 8297.0
2025-05-28 08:00:39,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-05-26
2025-05-28 08:00:39,838 - INFO - 变更字段: amount: 2511 -> 2694, count: 98 -> 99, instore_amount: 1355.56 -> 1538.56, instore_count: 67 -> 68
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: amount: 3370 -> 3419, count: 225 -> 235, instore_amount: 336.83 -> 340.08, instore_count: 23 -> 24, online_amount: 3149.43 -> 3195.15, online_count: 202 -> 211
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: instore_amount: 4096.76 -> 4112.45, instore_count: 267 -> 271, online_amount: 1340.39 -> 1324.7, online_count: 109 -> 105
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-25
2025-05-28 08:00:39,853 - INFO - 变更字段: instore_amount: 5094.59 -> 5128.19, instore_count: 361 -> 363, online_amount: 2368.79 -> 2335.19, online_count: 167 -> 165
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: amount: 1374 -> 1787, count: 43 -> 45, online_amount: 374.56 -> 787.86, online_count: 10 -> 12
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: daily_bill_amount: 0.0 -> 11235.2
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-05-25
2025-05-28 08:00:39,853 - INFO - 变更字段: recommend_amount: 13915.0 -> 14403.9, amount: 13915 -> 14403, count: 47 -> 48, instore_amount: 14034.0 -> 14522.9, instore_count: 47 -> 48
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: amount: 17408 -> 17432, count: 147 -> 148, online_amount: 2740.5 -> 2764.8, online_count: 70 -> 71
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1FE0MR50JEM3SR7Q2OVAE57DM4001Q85, sale_time=2025-05-24
2025-05-28 08:00:39,853 - INFO - 变更字段: amount: 10207 -> 10222, count: 96 -> 97, instore_amount: 4143.0 -> 4158.0, instore_count: 34 -> 35
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: recommend_amount: 5148.52 -> 5174.22, amount: 5148 -> 5174, count: 308 -> 310, instore_amount: 2796.8 -> 2809.6, instore_count: 161 -> 162, online_amount: 2386.06 -> 2398.96, online_count: 147 -> 148
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: amount: 11047 -> 11920, count: 141 -> 144, instore_amount: 9974.08 -> 10846.98, instore_count: 92 -> 95
2025-05-28 08:00:39,853 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-05-26
2025-05-28 08:00:39,853 - INFO - 变更字段: amount: 14036 -> 16907, count: 28 -> 29, instore_amount: 13963.12 -> 16834.12, instore_count: 27 -> 28
2025-05-28 08:00:39,869 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-05-26
2025-05-28 08:00:39,869 - INFO - 变更字段: recommend_amount: 0.0 -> 5906.13, daily_bill_amount: 0.0 -> 5906.13
2025-05-28 08:00:39,869 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRD2F2MCTBD6AJB6QM8HA650011P3, sale_time=2025-05-26
2025-05-28 08:00:39,869 - INFO - 变更字段: recommend_amount: 38806.2 -> 40112.2, amount: 38806 -> 40112, count: 113 -> 114, instore_amount: 38806.2 -> 40112.2, instore_count: 113 -> 114
2025-05-28 08:00:39,869 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=E79F261889C1492982227C207062C267, sale_time=2025-05-26
2025-05-28 08:00:39,869 - INFO - 变更字段: amount: 12309 -> 12283
2025-05-28 08:00:40,103 - INFO - SQLite数据保存完成，统计信息：
2025-05-28 08:00:40,103 - INFO - - 总记录数: 12978
2025-05-28 08:00:40,103 - INFO - - 成功插入: 203
2025-05-28 08:00:40,103 - INFO - - 成功更新: 47
2025-05-28 08:00:40,103 - INFO - - 无需更新: 12728
2025-05-28 08:00:40,103 - INFO - - 处理失败: 0
2025-05-28 08:00:45,478 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250528.xlsx
2025-05-28 08:00:45,478 - INFO - 成功获取数衍平台数据，共 12978 条记录
2025-05-28 08:00:45,478 - INFO - 正在更新SQLite月度汇总数据...
2025-05-28 08:00:45,494 - INFO - 月度数据sqllite清空完成
2025-05-28 08:00:45,759 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-28 08:00:45,759 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-28 08:00:45,759 - INFO - 正在获取宜搭日销售表单数据...
2025-05-28 08:00:45,759 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-28 00:00:00 至 2025-05-27 23:59:59
2025-05-28 08:00:45,759 - INFO - 查询分段 1: 2025-03-28 至 2025-04-03
2025-05-28 08:00:45,759 - INFO - 查询日期范围: 2025-03-28 至 2025-04-03，使用分页查询，每页 100 条记录
2025-05-28 08:00:45,759 - INFO - Request Parameters - Page 1:
2025-05-28 08:00:45,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:00:45,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:00:53,884 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C7F47B37-EEEF-7E90-8CFE-C847CDFF5495 Response: {'code': 'ServiceUnavailable', 'requestid': 'C7F47B37-EEEF-7E90-8CFE-C847CDFF5495', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-28 08:00:53,884 - ERROR - 服务不可用，将等待后重试
2025-05-28 08:00:53,884 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C7F47B37-EEEF-7E90-8CFE-C847CDFF5495 Response: {'code': 'ServiceUnavailable', 'requestid': 'C7F47B37-EEEF-7E90-8CFE-C847CDFF5495', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-28 08:00:53,884 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-05-28 08:00:59,900 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-05-28 08:00:59,900 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C7F47B37-EEEF-7E90-8CFE-C847CDFF5495 Response: {'code': 'ServiceUnavailable', 'requestid': 'C7F47B37-EEEF-7E90-8CFE-C847CDFF5495', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-05-28 08:01:09,916 - INFO - Request Parameters - Page 1:
2025-05-28 08:01:09,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:09,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:10,712 - INFO - API请求耗时: 797ms
2025-05-28 08:01:10,712 - INFO - Response - Page 1
2025-05-28 08:01:10,712 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:01:11,228 - INFO - Request Parameters - Page 2:
2025-05-28 08:01:11,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:11,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:11,962 - INFO - API请求耗时: 734ms
2025-05-28 08:01:11,962 - INFO - Response - Page 2
2025-05-28 08:01:11,978 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:01:12,478 - INFO - Request Parameters - Page 3:
2025-05-28 08:01:12,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:12,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:13,291 - INFO - API请求耗时: 812ms
2025-05-28 08:01:13,291 - INFO - Response - Page 3
2025-05-28 08:01:13,291 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:01:13,806 - INFO - Request Parameters - Page 4:
2025-05-28 08:01:13,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:13,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:14,509 - INFO - API请求耗时: 703ms
2025-05-28 08:01:14,509 - INFO - Response - Page 4
2025-05-28 08:01:14,509 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:01:15,009 - INFO - Request Parameters - Page 5:
2025-05-28 08:01:15,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:15,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:15,712 - INFO - API请求耗时: 703ms
2025-05-28 08:01:15,712 - INFO - Response - Page 5
2025-05-28 08:01:15,712 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:01:16,212 - INFO - Request Parameters - Page 6:
2025-05-28 08:01:16,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:16,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:17,009 - INFO - API请求耗时: 797ms
2025-05-28 08:01:17,009 - INFO - Response - Page 6
2025-05-28 08:01:17,009 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:01:17,525 - INFO - Request Parameters - Page 7:
2025-05-28 08:01:17,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:17,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:18,337 - INFO - API请求耗时: 812ms
2025-05-28 08:01:18,353 - INFO - Response - Page 7
2025-05-28 08:01:18,353 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:01:18,869 - INFO - Request Parameters - Page 8:
2025-05-28 08:01:18,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:18,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:19,603 - INFO - API请求耗时: 734ms
2025-05-28 08:01:19,603 - INFO - Response - Page 8
2025-05-28 08:01:19,603 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:01:20,103 - INFO - Request Parameters - Page 9:
2025-05-28 08:01:20,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:20,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:20,869 - INFO - API请求耗时: 766ms
2025-05-28 08:01:20,869 - INFO - Response - Page 9
2025-05-28 08:01:20,869 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:01:21,384 - INFO - Request Parameters - Page 10:
2025-05-28 08:01:21,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:21,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:22,165 - INFO - API请求耗时: 781ms
2025-05-28 08:01:22,165 - INFO - Response - Page 10
2025-05-28 08:01:22,165 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:01:22,665 - INFO - Request Parameters - Page 11:
2025-05-28 08:01:22,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:22,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:23,603 - INFO - API请求耗时: 937ms
2025-05-28 08:01:23,603 - INFO - Response - Page 11
2025-05-28 08:01:23,603 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:01:24,103 - INFO - Request Parameters - Page 12:
2025-05-28 08:01:24,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:24,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:24,884 - INFO - API请求耗时: 781ms
2025-05-28 08:01:24,884 - INFO - Response - Page 12
2025-05-28 08:01:24,884 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:01:25,384 - INFO - Request Parameters - Page 13:
2025-05-28 08:01:25,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:25,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:26,056 - INFO - API请求耗时: 672ms
2025-05-28 08:01:26,056 - INFO - Response - Page 13
2025-05-28 08:01:26,056 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:01:26,556 - INFO - Request Parameters - Page 14:
2025-05-28 08:01:26,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:26,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:27,369 - INFO - API请求耗时: 812ms
2025-05-28 08:01:27,384 - INFO - Response - Page 14
2025-05-28 08:01:27,384 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:01:27,884 - INFO - Request Parameters - Page 15:
2025-05-28 08:01:27,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:27,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:28,587 - INFO - API请求耗时: 703ms
2025-05-28 08:01:28,587 - INFO - Response - Page 15
2025-05-28 08:01:28,587 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:01:29,103 - INFO - Request Parameters - Page 16:
2025-05-28 08:01:29,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:29,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:30,009 - INFO - API请求耗时: 906ms
2025-05-28 08:01:30,009 - INFO - Response - Page 16
2025-05-28 08:01:30,009 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:01:30,509 - INFO - Request Parameters - Page 17:
2025-05-28 08:01:30,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:30,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:31,290 - INFO - API请求耗时: 781ms
2025-05-28 08:01:31,290 - INFO - Response - Page 17
2025-05-28 08:01:31,290 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:01:31,790 - INFO - Request Parameters - Page 18:
2025-05-28 08:01:31,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:31,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:32,509 - INFO - API请求耗时: 719ms
2025-05-28 08:01:32,509 - INFO - Response - Page 18
2025-05-28 08:01:32,509 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:01:33,025 - INFO - Request Parameters - Page 19:
2025-05-28 08:01:33,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:33,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:33,712 - INFO - API请求耗时: 688ms
2025-05-28 08:01:33,712 - INFO - Response - Page 19
2025-05-28 08:01:33,712 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:01:34,228 - INFO - Request Parameters - Page 20:
2025-05-28 08:01:34,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:34,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:35,072 - INFO - API请求耗时: 844ms
2025-05-28 08:01:35,072 - INFO - Response - Page 20
2025-05-28 08:01:35,072 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:01:35,587 - INFO - Request Parameters - Page 21:
2025-05-28 08:01:35,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:35,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:36,322 - INFO - API请求耗时: 734ms
2025-05-28 08:01:36,322 - INFO - Response - Page 21
2025-05-28 08:01:36,322 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:01:36,837 - INFO - Request Parameters - Page 22:
2025-05-28 08:01:36,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:36,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:37,603 - INFO - API请求耗时: 766ms
2025-05-28 08:01:37,603 - INFO - Response - Page 22
2025-05-28 08:01:37,603 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:01:38,119 - INFO - Request Parameters - Page 23:
2025-05-28 08:01:38,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:38,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:38,978 - INFO - API请求耗时: 859ms
2025-05-28 08:01:38,978 - INFO - Response - Page 23
2025-05-28 08:01:38,978 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:01:39,478 - INFO - Request Parameters - Page 24:
2025-05-28 08:01:39,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:39,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:40,306 - INFO - API请求耗时: 828ms
2025-05-28 08:01:40,306 - INFO - Response - Page 24
2025-05-28 08:01:40,306 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:01:40,822 - INFO - Request Parameters - Page 25:
2025-05-28 08:01:40,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:40,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:41,478 - INFO - API请求耗时: 656ms
2025-05-28 08:01:41,478 - INFO - Response - Page 25
2025-05-28 08:01:41,478 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:01:41,978 - INFO - Request Parameters - Page 26:
2025-05-28 08:01:41,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:41,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:42,728 - INFO - API请求耗时: 750ms
2025-05-28 08:01:42,728 - INFO - Response - Page 26
2025-05-28 08:01:42,728 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:01:43,243 - INFO - Request Parameters - Page 27:
2025-05-28 08:01:43,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:43,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:44,009 - INFO - API请求耗时: 766ms
2025-05-28 08:01:44,009 - INFO - Response - Page 27
2025-05-28 08:01:44,009 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:01:44,509 - INFO - Request Parameters - Page 28:
2025-05-28 08:01:44,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:44,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:45,197 - INFO - API请求耗时: 687ms
2025-05-28 08:01:45,197 - INFO - Response - Page 28
2025-05-28 08:01:45,197 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:01:45,697 - INFO - Request Parameters - Page 29:
2025-05-28 08:01:45,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:45,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:46,384 - INFO - API请求耗时: 687ms
2025-05-28 08:01:46,384 - INFO - Response - Page 29
2025-05-28 08:01:46,384 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:01:46,900 - INFO - Request Parameters - Page 30:
2025-05-28 08:01:46,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:46,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:47,665 - INFO - API请求耗时: 766ms
2025-05-28 08:01:47,665 - INFO - Response - Page 30
2025-05-28 08:01:47,665 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:01:48,181 - INFO - Request Parameters - Page 31:
2025-05-28 08:01:48,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:48,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:48,962 - INFO - API请求耗时: 781ms
2025-05-28 08:01:48,962 - INFO - Response - Page 31
2025-05-28 08:01:48,962 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:01:49,462 - INFO - Request Parameters - Page 32:
2025-05-28 08:01:49,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:49,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:50,181 - INFO - API请求耗时: 719ms
2025-05-28 08:01:50,181 - INFO - Response - Page 32
2025-05-28 08:01:50,181 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:01:50,681 - INFO - Request Parameters - Page 33:
2025-05-28 08:01:50,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:50,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:51,384 - INFO - API请求耗时: 703ms
2025-05-28 08:01:51,384 - INFO - Response - Page 33
2025-05-28 08:01:51,384 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:01:51,884 - INFO - Request Parameters - Page 34:
2025-05-28 08:01:51,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:51,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:52,634 - INFO - API请求耗时: 750ms
2025-05-28 08:01:52,634 - INFO - Response - Page 34
2025-05-28 08:01:52,634 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:01:53,134 - INFO - Request Parameters - Page 35:
2025-05-28 08:01:53,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:53,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:53,868 - INFO - API请求耗时: 734ms
2025-05-28 08:01:53,868 - INFO - Response - Page 35
2025-05-28 08:01:53,868 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:01:54,384 - INFO - Request Parameters - Page 36:
2025-05-28 08:01:54,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:54,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:55,009 - INFO - API请求耗时: 625ms
2025-05-28 08:01:55,009 - INFO - Response - Page 36
2025-05-28 08:01:55,009 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:01:55,525 - INFO - Request Parameters - Page 37:
2025-05-28 08:01:55,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:55,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:56,181 - INFO - API请求耗时: 656ms
2025-05-28 08:01:56,181 - INFO - Response - Page 37
2025-05-28 08:01:56,181 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:01:56,697 - INFO - Request Parameters - Page 38:
2025-05-28 08:01:56,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:56,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200759, 1743609600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:57,212 - INFO - API请求耗时: 516ms
2025-05-28 08:01:57,212 - INFO - Response - Page 38
2025-05-28 08:01:57,212 - INFO - 第 38 页获取到 19 条记录
2025-05-28 08:01:57,212 - INFO - 查询完成，共获取到 3719 条记录
2025-05-28 08:01:57,212 - INFO - 分段 1 查询成功，获取到 3719 条记录
2025-05-28 08:01:58,228 - INFO - 查询分段 2: 2025-04-04 至 2025-04-10
2025-05-28 08:01:58,228 - INFO - 查询日期范围: 2025-04-04 至 2025-04-10，使用分页查询，每页 100 条记录
2025-05-28 08:01:58,228 - INFO - Request Parameters - Page 1:
2025-05-28 08:01:58,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:58,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:01:58,931 - INFO - API请求耗时: 703ms
2025-05-28 08:01:58,931 - INFO - Response - Page 1
2025-05-28 08:01:58,946 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:01:59,462 - INFO - Request Parameters - Page 2:
2025-05-28 08:01:59,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:01:59,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:00,150 - INFO - API请求耗时: 687ms
2025-05-28 08:02:00,150 - INFO - Response - Page 2
2025-05-28 08:02:00,150 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:02:00,650 - INFO - Request Parameters - Page 3:
2025-05-28 08:02:00,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:00,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:01,556 - INFO - API请求耗时: 906ms
2025-05-28 08:02:01,556 - INFO - Response - Page 3
2025-05-28 08:02:01,556 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:02:02,071 - INFO - Request Parameters - Page 4:
2025-05-28 08:02:02,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:02,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:02,790 - INFO - API请求耗时: 719ms
2025-05-28 08:02:02,790 - INFO - Response - Page 4
2025-05-28 08:02:02,790 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:02:03,290 - INFO - Request Parameters - Page 5:
2025-05-28 08:02:03,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:03,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:04,009 - INFO - API请求耗时: 719ms
2025-05-28 08:02:04,009 - INFO - Response - Page 5
2025-05-28 08:02:04,009 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:02:04,525 - INFO - Request Parameters - Page 6:
2025-05-28 08:02:04,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:04,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:05,259 - INFO - API请求耗时: 734ms
2025-05-28 08:02:05,259 - INFO - Response - Page 6
2025-05-28 08:02:05,259 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:02:05,759 - INFO - Request Parameters - Page 7:
2025-05-28 08:02:05,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:05,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:06,603 - INFO - API请求耗时: 844ms
2025-05-28 08:02:06,603 - INFO - Response - Page 7
2025-05-28 08:02:06,603 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:02:07,118 - INFO - Request Parameters - Page 8:
2025-05-28 08:02:07,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:07,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:07,806 - INFO - API请求耗时: 687ms
2025-05-28 08:02:07,806 - INFO - Response - Page 8
2025-05-28 08:02:07,806 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:02:08,306 - INFO - Request Parameters - Page 9:
2025-05-28 08:02:08,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:08,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:09,071 - INFO - API请求耗时: 766ms
2025-05-28 08:02:09,071 - INFO - Response - Page 9
2025-05-28 08:02:09,071 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:02:09,587 - INFO - Request Parameters - Page 10:
2025-05-28 08:02:09,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:09,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:10,290 - INFO - API请求耗时: 703ms
2025-05-28 08:02:10,290 - INFO - Response - Page 10
2025-05-28 08:02:10,290 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:02:10,790 - INFO - Request Parameters - Page 11:
2025-05-28 08:02:10,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:10,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:11,493 - INFO - API请求耗时: 703ms
2025-05-28 08:02:11,493 - INFO - Response - Page 11
2025-05-28 08:02:11,493 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:02:12,009 - INFO - Request Parameters - Page 12:
2025-05-28 08:02:12,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:12,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:12,728 - INFO - API请求耗时: 719ms
2025-05-28 08:02:12,728 - INFO - Response - Page 12
2025-05-28 08:02:12,728 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:02:13,243 - INFO - Request Parameters - Page 13:
2025-05-28 08:02:13,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:13,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:14,103 - INFO - API请求耗时: 859ms
2025-05-28 08:02:14,103 - INFO - Response - Page 13
2025-05-28 08:02:14,103 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:02:14,618 - INFO - Request Parameters - Page 14:
2025-05-28 08:02:14,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:14,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:15,462 - INFO - API请求耗时: 844ms
2025-05-28 08:02:15,462 - INFO - Response - Page 14
2025-05-28 08:02:15,462 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:02:15,962 - INFO - Request Parameters - Page 15:
2025-05-28 08:02:15,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:15,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:16,728 - INFO - API请求耗时: 766ms
2025-05-28 08:02:16,728 - INFO - Response - Page 15
2025-05-28 08:02:16,728 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:02:17,243 - INFO - Request Parameters - Page 16:
2025-05-28 08:02:17,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:17,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:18,009 - INFO - API请求耗时: 766ms
2025-05-28 08:02:18,009 - INFO - Response - Page 16
2025-05-28 08:02:18,009 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:02:18,509 - INFO - Request Parameters - Page 17:
2025-05-28 08:02:18,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:18,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:19,321 - INFO - API请求耗时: 812ms
2025-05-28 08:02:19,321 - INFO - Response - Page 17
2025-05-28 08:02:19,321 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:02:19,821 - INFO - Request Parameters - Page 18:
2025-05-28 08:02:19,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:19,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:20,556 - INFO - API请求耗时: 734ms
2025-05-28 08:02:20,556 - INFO - Response - Page 18
2025-05-28 08:02:20,556 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:02:21,056 - INFO - Request Parameters - Page 19:
2025-05-28 08:02:21,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:21,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:21,774 - INFO - API请求耗时: 719ms
2025-05-28 08:02:21,774 - INFO - Response - Page 19
2025-05-28 08:02:21,774 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:02:22,274 - INFO - Request Parameters - Page 20:
2025-05-28 08:02:22,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:22,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:23,024 - INFO - API请求耗时: 750ms
2025-05-28 08:02:23,024 - INFO - Response - Page 20
2025-05-28 08:02:23,024 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:02:23,540 - INFO - Request Parameters - Page 21:
2025-05-28 08:02:23,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:23,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:24,243 - INFO - API请求耗时: 703ms
2025-05-28 08:02:24,243 - INFO - Response - Page 21
2025-05-28 08:02:24,243 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:02:24,743 - INFO - Request Parameters - Page 22:
2025-05-28 08:02:24,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:24,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:25,462 - INFO - API请求耗时: 719ms
2025-05-28 08:02:25,462 - INFO - Response - Page 22
2025-05-28 08:02:25,462 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:02:25,962 - INFO - Request Parameters - Page 23:
2025-05-28 08:02:25,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:25,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:26,681 - INFO - API请求耗时: 719ms
2025-05-28 08:02:26,681 - INFO - Response - Page 23
2025-05-28 08:02:26,681 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:02:27,181 - INFO - Request Parameters - Page 24:
2025-05-28 08:02:27,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:27,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:27,899 - INFO - API请求耗时: 719ms
2025-05-28 08:02:27,915 - INFO - Response - Page 24
2025-05-28 08:02:27,915 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:02:28,415 - INFO - Request Parameters - Page 25:
2025-05-28 08:02:28,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:28,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:29,087 - INFO - API请求耗时: 672ms
2025-05-28 08:02:29,087 - INFO - Response - Page 25
2025-05-28 08:02:29,087 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:02:29,587 - INFO - Request Parameters - Page 26:
2025-05-28 08:02:29,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:29,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:30,306 - INFO - API请求耗时: 719ms
2025-05-28 08:02:30,306 - INFO - Response - Page 26
2025-05-28 08:02:30,306 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:02:30,806 - INFO - Request Parameters - Page 27:
2025-05-28 08:02:30,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:30,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:31,524 - INFO - API请求耗时: 719ms
2025-05-28 08:02:31,524 - INFO - Response - Page 27
2025-05-28 08:02:31,524 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:02:32,040 - INFO - Request Parameters - Page 28:
2025-05-28 08:02:32,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:32,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:32,743 - INFO - API请求耗时: 703ms
2025-05-28 08:02:32,743 - INFO - Response - Page 28
2025-05-28 08:02:32,743 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:02:33,259 - INFO - Request Parameters - Page 29:
2025-05-28 08:02:33,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:33,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:34,040 - INFO - API请求耗时: 781ms
2025-05-28 08:02:34,040 - INFO - Response - Page 29
2025-05-28 08:02:34,040 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:02:34,556 - INFO - Request Parameters - Page 30:
2025-05-28 08:02:34,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:34,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:35,274 - INFO - API请求耗时: 719ms
2025-05-28 08:02:35,274 - INFO - Response - Page 30
2025-05-28 08:02:35,274 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:02:35,790 - INFO - Request Parameters - Page 31:
2025-05-28 08:02:35,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:35,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:36,571 - INFO - API请求耗时: 781ms
2025-05-28 08:02:36,571 - INFO - Response - Page 31
2025-05-28 08:02:36,587 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:02:37,087 - INFO - Request Parameters - Page 32:
2025-05-28 08:02:37,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:37,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:37,774 - INFO - API请求耗时: 687ms
2025-05-28 08:02:37,774 - INFO - Response - Page 32
2025-05-28 08:02:37,774 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:02:38,290 - INFO - Request Parameters - Page 33:
2025-05-28 08:02:38,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:38,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:39,040 - INFO - API请求耗时: 750ms
2025-05-28 08:02:39,040 - INFO - Response - Page 33
2025-05-28 08:02:39,040 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:02:39,556 - INFO - Request Parameters - Page 34:
2025-05-28 08:02:39,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:39,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:40,274 - INFO - API请求耗时: 719ms
2025-05-28 08:02:40,274 - INFO - Response - Page 34
2025-05-28 08:02:40,274 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:02:40,806 - INFO - Request Parameters - Page 35:
2025-05-28 08:02:40,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:40,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:41,477 - INFO - API请求耗时: 672ms
2025-05-28 08:02:41,477 - INFO - Response - Page 35
2025-05-28 08:02:41,477 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:02:41,977 - INFO - Request Parameters - Page 36:
2025-05-28 08:02:41,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:41,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:42,727 - INFO - API请求耗时: 750ms
2025-05-28 08:02:42,727 - INFO - Response - Page 36
2025-05-28 08:02:42,727 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:02:43,243 - INFO - Request Parameters - Page 37:
2025-05-28 08:02:43,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:43,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:43,915 - INFO - API请求耗时: 672ms
2025-05-28 08:02:43,915 - INFO - Response - Page 37
2025-05-28 08:02:43,915 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:02:44,415 - INFO - Request Parameters - Page 38:
2025-05-28 08:02:44,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:44,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:45,134 - INFO - API请求耗时: 719ms
2025-05-28 08:02:45,134 - INFO - Response - Page 38
2025-05-28 08:02:45,134 - INFO - 第 38 页获取到 100 条记录
2025-05-28 08:02:45,634 - INFO - Request Parameters - Page 39:
2025-05-28 08:02:45,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:45,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743696000759, 1744214400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:46,196 - INFO - API请求耗时: 562ms
2025-05-28 08:02:46,196 - INFO - Response - Page 39
2025-05-28 08:02:46,196 - INFO - 第 39 页获取到 36 条记录
2025-05-28 08:02:46,196 - INFO - 查询完成，共获取到 3836 条记录
2025-05-28 08:02:46,196 - INFO - 分段 2 查询成功，获取到 3836 条记录
2025-05-28 08:02:47,196 - INFO - 查询分段 3: 2025-04-11 至 2025-04-17
2025-05-28 08:02:47,196 - INFO - 查询日期范围: 2025-04-11 至 2025-04-17，使用分页查询，每页 100 条记录
2025-05-28 08:02:47,196 - INFO - Request Parameters - Page 1:
2025-05-28 08:02:47,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:47,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:47,868 - INFO - API请求耗时: 672ms
2025-05-28 08:02:47,868 - INFO - Response - Page 1
2025-05-28 08:02:47,868 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:02:48,368 - INFO - Request Parameters - Page 2:
2025-05-28 08:02:48,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:48,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:49,149 - INFO - API请求耗时: 781ms
2025-05-28 08:02:49,149 - INFO - Response - Page 2
2025-05-28 08:02:49,149 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:02:49,665 - INFO - Request Parameters - Page 3:
2025-05-28 08:02:49,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:49,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:50,337 - INFO - API请求耗时: 672ms
2025-05-28 08:02:50,337 - INFO - Response - Page 3
2025-05-28 08:02:50,337 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:02:50,837 - INFO - Request Parameters - Page 4:
2025-05-28 08:02:50,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:50,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:51,602 - INFO - API请求耗时: 766ms
2025-05-28 08:02:51,602 - INFO - Response - Page 4
2025-05-28 08:02:51,602 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:02:52,118 - INFO - Request Parameters - Page 5:
2025-05-28 08:02:52,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:52,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:52,852 - INFO - API请求耗时: 734ms
2025-05-28 08:02:52,852 - INFO - Response - Page 5
2025-05-28 08:02:52,852 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:02:53,352 - INFO - Request Parameters - Page 6:
2025-05-28 08:02:53,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:53,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:54,102 - INFO - API请求耗时: 750ms
2025-05-28 08:02:54,102 - INFO - Response - Page 6
2025-05-28 08:02:54,102 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:02:54,618 - INFO - Request Parameters - Page 7:
2025-05-28 08:02:54,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:54,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:55,430 - INFO - API请求耗时: 812ms
2025-05-28 08:02:55,430 - INFO - Response - Page 7
2025-05-28 08:02:55,430 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:02:55,930 - INFO - Request Parameters - Page 8:
2025-05-28 08:02:55,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:55,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:56,743 - INFO - API请求耗时: 812ms
2025-05-28 08:02:56,743 - INFO - Response - Page 8
2025-05-28 08:02:56,743 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:02:57,243 - INFO - Request Parameters - Page 9:
2025-05-28 08:02:57,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:57,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:57,993 - INFO - API请求耗时: 750ms
2025-05-28 08:02:57,993 - INFO - Response - Page 9
2025-05-28 08:02:57,993 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:02:58,493 - INFO - Request Parameters - Page 10:
2025-05-28 08:02:58,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:58,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:02:59,259 - INFO - API请求耗时: 766ms
2025-05-28 08:02:59,259 - INFO - Response - Page 10
2025-05-28 08:02:59,259 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:02:59,759 - INFO - Request Parameters - Page 11:
2025-05-28 08:02:59,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:02:59,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:00,446 - INFO - API请求耗时: 687ms
2025-05-28 08:03:00,446 - INFO - Response - Page 11
2025-05-28 08:03:00,446 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:03:00,946 - INFO - Request Parameters - Page 12:
2025-05-28 08:03:00,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:00,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:01,649 - INFO - API请求耗时: 703ms
2025-05-28 08:03:01,649 - INFO - Response - Page 12
2025-05-28 08:03:01,649 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:03:02,165 - INFO - Request Parameters - Page 13:
2025-05-28 08:03:02,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:02,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:02,993 - INFO - API请求耗时: 828ms
2025-05-28 08:03:02,993 - INFO - Response - Page 13
2025-05-28 08:03:02,993 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:03:03,493 - INFO - Request Parameters - Page 14:
2025-05-28 08:03:03,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:03,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:04,243 - INFO - API请求耗时: 750ms
2025-05-28 08:03:04,243 - INFO - Response - Page 14
2025-05-28 08:03:04,259 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:03:04,759 - INFO - Request Parameters - Page 15:
2025-05-28 08:03:04,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:04,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:05,477 - INFO - API请求耗时: 719ms
2025-05-28 08:03:05,477 - INFO - Response - Page 15
2025-05-28 08:03:05,477 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:03:05,993 - INFO - Request Parameters - Page 16:
2025-05-28 08:03:05,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:05,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:06,680 - INFO - API请求耗时: 687ms
2025-05-28 08:03:06,680 - INFO - Response - Page 16
2025-05-28 08:03:06,680 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:03:07,196 - INFO - Request Parameters - Page 17:
2025-05-28 08:03:07,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:07,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:07,868 - INFO - API请求耗时: 672ms
2025-05-28 08:03:07,868 - INFO - Response - Page 17
2025-05-28 08:03:07,868 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:03:08,368 - INFO - Request Parameters - Page 18:
2025-05-28 08:03:08,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:08,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:09,009 - INFO - API请求耗时: 641ms
2025-05-28 08:03:09,009 - INFO - Response - Page 18
2025-05-28 08:03:09,024 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:03:09,524 - INFO - Request Parameters - Page 19:
2025-05-28 08:03:09,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:09,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:10,227 - INFO - API请求耗时: 703ms
2025-05-28 08:03:10,227 - INFO - Response - Page 19
2025-05-28 08:03:10,227 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:03:10,727 - INFO - Request Parameters - Page 20:
2025-05-28 08:03:10,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:10,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:11,352 - INFO - API请求耗时: 625ms
2025-05-28 08:03:11,352 - INFO - Response - Page 20
2025-05-28 08:03:11,352 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:03:11,868 - INFO - Request Parameters - Page 21:
2025-05-28 08:03:11,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:11,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:12,571 - INFO - API请求耗时: 703ms
2025-05-28 08:03:12,571 - INFO - Response - Page 21
2025-05-28 08:03:12,571 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:03:13,087 - INFO - Request Parameters - Page 22:
2025-05-28 08:03:13,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:13,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:13,852 - INFO - API请求耗时: 766ms
2025-05-28 08:03:13,852 - INFO - Response - Page 22
2025-05-28 08:03:13,852 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:03:14,368 - INFO - Request Parameters - Page 23:
2025-05-28 08:03:14,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:14,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:15,008 - INFO - API请求耗时: 641ms
2025-05-28 08:03:15,008 - INFO - Response - Page 23
2025-05-28 08:03:15,008 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:03:15,524 - INFO - Request Parameters - Page 24:
2025-05-28 08:03:15,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:15,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:16,290 - INFO - API请求耗时: 766ms
2025-05-28 08:03:16,290 - INFO - Response - Page 24
2025-05-28 08:03:16,290 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:03:16,790 - INFO - Request Parameters - Page 25:
2025-05-28 08:03:16,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:16,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:17,540 - INFO - API请求耗时: 750ms
2025-05-28 08:03:17,540 - INFO - Response - Page 25
2025-05-28 08:03:17,540 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:03:18,055 - INFO - Request Parameters - Page 26:
2025-05-28 08:03:18,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:18,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:18,805 - INFO - API请求耗时: 750ms
2025-05-28 08:03:18,805 - INFO - Response - Page 26
2025-05-28 08:03:18,805 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:03:19,321 - INFO - Request Parameters - Page 27:
2025-05-28 08:03:19,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:19,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:19,993 - INFO - API请求耗时: 672ms
2025-05-28 08:03:19,993 - INFO - Response - Page 27
2025-05-28 08:03:19,993 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:03:20,508 - INFO - Request Parameters - Page 28:
2025-05-28 08:03:20,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:20,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:21,212 - INFO - API请求耗时: 703ms
2025-05-28 08:03:21,212 - INFO - Response - Page 28
2025-05-28 08:03:21,212 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:03:21,727 - INFO - Request Parameters - Page 29:
2025-05-28 08:03:21,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:21,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:22,462 - INFO - API请求耗时: 734ms
2025-05-28 08:03:22,462 - INFO - Response - Page 29
2025-05-28 08:03:22,462 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:03:22,962 - INFO - Request Parameters - Page 30:
2025-05-28 08:03:22,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:22,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:23,696 - INFO - API请求耗时: 734ms
2025-05-28 08:03:23,696 - INFO - Response - Page 30
2025-05-28 08:03:23,696 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:03:24,212 - INFO - Request Parameters - Page 31:
2025-05-28 08:03:24,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:24,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:24,899 - INFO - API请求耗时: 687ms
2025-05-28 08:03:24,899 - INFO - Response - Page 31
2025-05-28 08:03:24,899 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:03:25,415 - INFO - Request Parameters - Page 32:
2025-05-28 08:03:25,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:25,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:26,024 - INFO - API请求耗时: 609ms
2025-05-28 08:03:26,040 - INFO - Response - Page 32
2025-05-28 08:03:26,040 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:03:26,540 - INFO - Request Parameters - Page 33:
2025-05-28 08:03:26,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:26,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:27,212 - INFO - API请求耗时: 672ms
2025-05-28 08:03:27,212 - INFO - Response - Page 33
2025-05-28 08:03:27,212 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:03:27,712 - INFO - Request Parameters - Page 34:
2025-05-28 08:03:27,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:27,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:28,399 - INFO - API请求耗时: 687ms
2025-05-28 08:03:28,399 - INFO - Response - Page 34
2025-05-28 08:03:28,399 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:03:28,899 - INFO - Request Parameters - Page 35:
2025-05-28 08:03:28,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:28,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:29,571 - INFO - API请求耗时: 672ms
2025-05-28 08:03:29,571 - INFO - Response - Page 35
2025-05-28 08:03:29,571 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:03:30,087 - INFO - Request Parameters - Page 36:
2025-05-28 08:03:30,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:30,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:30,805 - INFO - API请求耗时: 719ms
2025-05-28 08:03:30,805 - INFO - Response - Page 36
2025-05-28 08:03:30,805 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:03:31,305 - INFO - Request Parameters - Page 37:
2025-05-28 08:03:31,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:31,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:32,008 - INFO - API请求耗时: 703ms
2025-05-28 08:03:32,008 - INFO - Response - Page 37
2025-05-28 08:03:32,008 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:03:32,524 - INFO - Request Parameters - Page 38:
2025-05-28 08:03:32,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:32,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:33,165 - INFO - API请求耗时: 641ms
2025-05-28 08:03:33,165 - INFO - Response - Page 38
2025-05-28 08:03:33,165 - INFO - 第 38 页获取到 100 条记录
2025-05-28 08:03:33,680 - INFO - Request Parameters - Page 39:
2025-05-28 08:03:33,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:33,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800759, 1744819200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:34,399 - INFO - API请求耗时: 719ms
2025-05-28 08:03:34,399 - INFO - Response - Page 39
2025-05-28 08:03:34,399 - INFO - 第 39 页获取到 88 条记录
2025-05-28 08:03:34,399 - INFO - 查询完成，共获取到 3888 条记录
2025-05-28 08:03:34,399 - INFO - 分段 3 查询成功，获取到 3888 条记录
2025-05-28 08:03:35,399 - INFO - 查询分段 4: 2025-04-18 至 2025-04-24
2025-05-28 08:03:35,399 - INFO - 查询日期范围: 2025-04-18 至 2025-04-24，使用分页查询，每页 100 条记录
2025-05-28 08:03:35,399 - INFO - Request Parameters - Page 1:
2025-05-28 08:03:35,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:35,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:36,102 - INFO - API请求耗时: 703ms
2025-05-28 08:03:36,102 - INFO - Response - Page 1
2025-05-28 08:03:36,102 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:03:36,602 - INFO - Request Parameters - Page 2:
2025-05-28 08:03:36,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:36,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:37,243 - INFO - API请求耗时: 641ms
2025-05-28 08:03:37,243 - INFO - Response - Page 2
2025-05-28 08:03:37,243 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:03:37,743 - INFO - Request Parameters - Page 3:
2025-05-28 08:03:37,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:37,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:38,571 - INFO - API请求耗时: 828ms
2025-05-28 08:03:38,571 - INFO - Response - Page 3
2025-05-28 08:03:38,571 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:03:39,086 - INFO - Request Parameters - Page 4:
2025-05-28 08:03:39,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:39,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:39,711 - INFO - API请求耗时: 625ms
2025-05-28 08:03:39,711 - INFO - Response - Page 4
2025-05-28 08:03:39,711 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:03:40,211 - INFO - Request Parameters - Page 5:
2025-05-28 08:03:40,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:40,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:40,883 - INFO - API请求耗时: 672ms
2025-05-28 08:03:40,883 - INFO - Response - Page 5
2025-05-28 08:03:40,899 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:03:41,415 - INFO - Request Parameters - Page 6:
2025-05-28 08:03:41,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:41,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:42,040 - INFO - API请求耗时: 625ms
2025-05-28 08:03:42,040 - INFO - Response - Page 6
2025-05-28 08:03:42,055 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:03:42,555 - INFO - Request Parameters - Page 7:
2025-05-28 08:03:42,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:42,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:43,336 - INFO - API请求耗时: 781ms
2025-05-28 08:03:43,336 - INFO - Response - Page 7
2025-05-28 08:03:43,336 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:03:43,852 - INFO - Request Parameters - Page 8:
2025-05-28 08:03:43,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:43,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:44,508 - INFO - API请求耗时: 656ms
2025-05-28 08:03:44,508 - INFO - Response - Page 8
2025-05-28 08:03:44,508 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:03:45,008 - INFO - Request Parameters - Page 9:
2025-05-28 08:03:45,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:45,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:45,774 - INFO - API请求耗时: 766ms
2025-05-28 08:03:45,774 - INFO - Response - Page 9
2025-05-28 08:03:45,774 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:03:46,290 - INFO - Request Parameters - Page 10:
2025-05-28 08:03:46,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:46,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:47,024 - INFO - API请求耗时: 734ms
2025-05-28 08:03:47,024 - INFO - Response - Page 10
2025-05-28 08:03:47,024 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:03:47,524 - INFO - Request Parameters - Page 11:
2025-05-28 08:03:47,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:47,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:48,211 - INFO - API请求耗时: 688ms
2025-05-28 08:03:48,227 - INFO - Response - Page 11
2025-05-28 08:03:48,227 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:03:48,727 - INFO - Request Parameters - Page 12:
2025-05-28 08:03:48,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:48,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:49,415 - INFO - API请求耗时: 687ms
2025-05-28 08:03:49,415 - INFO - Response - Page 12
2025-05-28 08:03:49,415 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:03:49,930 - INFO - Request Parameters - Page 13:
2025-05-28 08:03:49,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:49,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:50,743 - INFO - API请求耗时: 813ms
2025-05-28 08:03:50,743 - INFO - Response - Page 13
2025-05-28 08:03:50,743 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:03:51,243 - INFO - Request Parameters - Page 14:
2025-05-28 08:03:51,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:51,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:51,977 - INFO - API请求耗时: 734ms
2025-05-28 08:03:51,977 - INFO - Response - Page 14
2025-05-28 08:03:51,977 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:03:52,493 - INFO - Request Parameters - Page 15:
2025-05-28 08:03:52,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:52,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:53,289 - INFO - API请求耗时: 797ms
2025-05-28 08:03:53,289 - INFO - Response - Page 15
2025-05-28 08:03:53,289 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:03:53,805 - INFO - Request Parameters - Page 16:
2025-05-28 08:03:53,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:53,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:54,571 - INFO - API请求耗时: 766ms
2025-05-28 08:03:54,571 - INFO - Response - Page 16
2025-05-28 08:03:54,571 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:03:55,071 - INFO - Request Parameters - Page 17:
2025-05-28 08:03:55,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:55,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:55,805 - INFO - API请求耗时: 734ms
2025-05-28 08:03:55,805 - INFO - Response - Page 17
2025-05-28 08:03:55,805 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:03:56,305 - INFO - Request Parameters - Page 18:
2025-05-28 08:03:56,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:56,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:57,055 - INFO - API请求耗时: 750ms
2025-05-28 08:03:57,055 - INFO - Response - Page 18
2025-05-28 08:03:57,055 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:03:57,555 - INFO - Request Parameters - Page 19:
2025-05-28 08:03:57,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:57,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:58,305 - INFO - API请求耗时: 750ms
2025-05-28 08:03:58,305 - INFO - Response - Page 19
2025-05-28 08:03:58,305 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:03:58,821 - INFO - Request Parameters - Page 20:
2025-05-28 08:03:58,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:03:58,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:03:59,571 - INFO - API请求耗时: 750ms
2025-05-28 08:03:59,571 - INFO - Response - Page 20
2025-05-28 08:03:59,571 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:04:00,086 - INFO - Request Parameters - Page 21:
2025-05-28 08:04:00,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:00,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:00,758 - INFO - API请求耗时: 672ms
2025-05-28 08:04:00,758 - INFO - Response - Page 21
2025-05-28 08:04:00,758 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:04:01,258 - INFO - Request Parameters - Page 22:
2025-05-28 08:04:01,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:01,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:01,946 - INFO - API请求耗时: 687ms
2025-05-28 08:04:01,946 - INFO - Response - Page 22
2025-05-28 08:04:01,946 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:04:02,461 - INFO - Request Parameters - Page 23:
2025-05-28 08:04:02,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:02,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:03,211 - INFO - API请求耗时: 750ms
2025-05-28 08:04:03,211 - INFO - Response - Page 23
2025-05-28 08:04:03,211 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:04:03,727 - INFO - Request Parameters - Page 24:
2025-05-28 08:04:03,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:03,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:04,633 - INFO - API请求耗时: 906ms
2025-05-28 08:04:04,633 - INFO - Response - Page 24
2025-05-28 08:04:04,633 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:04:05,133 - INFO - Request Parameters - Page 25:
2025-05-28 08:04:05,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:05,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:05,883 - INFO - API请求耗时: 750ms
2025-05-28 08:04:05,883 - INFO - Response - Page 25
2025-05-28 08:04:05,883 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:04:06,383 - INFO - Request Parameters - Page 26:
2025-05-28 08:04:06,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:06,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:07,118 - INFO - API请求耗时: 734ms
2025-05-28 08:04:07,118 - INFO - Response - Page 26
2025-05-28 08:04:07,118 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:04:07,633 - INFO - Request Parameters - Page 27:
2025-05-28 08:04:07,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:07,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:08,305 - INFO - API请求耗时: 672ms
2025-05-28 08:04:08,305 - INFO - Response - Page 27
2025-05-28 08:04:08,305 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:04:08,805 - INFO - Request Parameters - Page 28:
2025-05-28 08:04:08,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:08,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:09,477 - INFO - API请求耗时: 672ms
2025-05-28 08:04:09,477 - INFO - Response - Page 28
2025-05-28 08:04:09,477 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:04:09,993 - INFO - Request Parameters - Page 29:
2025-05-28 08:04:09,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:09,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:10,852 - INFO - API请求耗时: 859ms
2025-05-28 08:04:10,852 - INFO - Response - Page 29
2025-05-28 08:04:10,852 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:04:11,368 - INFO - Request Parameters - Page 30:
2025-05-28 08:04:11,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:11,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:12,102 - INFO - API请求耗时: 734ms
2025-05-28 08:04:12,102 - INFO - Response - Page 30
2025-05-28 08:04:12,102 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:04:12,602 - INFO - Request Parameters - Page 31:
2025-05-28 08:04:12,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:12,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:13,321 - INFO - API请求耗时: 719ms
2025-05-28 08:04:13,321 - INFO - Response - Page 31
2025-05-28 08:04:13,321 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:04:13,821 - INFO - Request Parameters - Page 32:
2025-05-28 08:04:13,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:13,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:14,539 - INFO - API请求耗时: 719ms
2025-05-28 08:04:14,539 - INFO - Response - Page 32
2025-05-28 08:04:14,539 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:04:15,055 - INFO - Request Parameters - Page 33:
2025-05-28 08:04:15,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:15,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:15,789 - INFO - API请求耗时: 734ms
2025-05-28 08:04:15,789 - INFO - Response - Page 33
2025-05-28 08:04:15,789 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:04:16,305 - INFO - Request Parameters - Page 34:
2025-05-28 08:04:16,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:16,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:16,992 - INFO - API请求耗时: 687ms
2025-05-28 08:04:16,992 - INFO - Response - Page 34
2025-05-28 08:04:16,992 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:04:17,492 - INFO - Request Parameters - Page 35:
2025-05-28 08:04:17,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:17,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:18,258 - INFO - API请求耗时: 766ms
2025-05-28 08:04:18,258 - INFO - Response - Page 35
2025-05-28 08:04:18,258 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:04:18,774 - INFO - Request Parameters - Page 36:
2025-05-28 08:04:18,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:18,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:19,461 - INFO - API请求耗时: 687ms
2025-05-28 08:04:19,461 - INFO - Response - Page 36
2025-05-28 08:04:19,477 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:04:19,977 - INFO - Request Parameters - Page 37:
2025-05-28 08:04:19,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:19,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:20,727 - INFO - API请求耗时: 750ms
2025-05-28 08:04:20,727 - INFO - Response - Page 37
2025-05-28 08:04:20,727 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:04:21,242 - INFO - Request Parameters - Page 38:
2025-05-28 08:04:21,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:21,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:21,946 - INFO - API请求耗时: 703ms
2025-05-28 08:04:21,946 - INFO - Response - Page 38
2025-05-28 08:04:21,946 - INFO - 第 38 页获取到 100 条记录
2025-05-28 08:04:22,461 - INFO - Request Parameters - Page 39:
2025-05-28 08:04:22,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:22,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744905600759, 1745424000759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:22,821 - INFO - API请求耗时: 359ms
2025-05-28 08:04:22,821 - INFO - Response - Page 39
2025-05-28 08:04:22,821 - INFO - 第 39 页没有数据，已到达最后一页
2025-05-28 08:04:22,821 - INFO - 查询完成，共获取到 3800 条记录
2025-05-28 08:04:22,821 - INFO - 分段 4 查询成功，获取到 3800 条记录
2025-05-28 08:04:23,821 - INFO - 查询分段 5: 2025-04-25 至 2025-05-01
2025-05-28 08:04:23,821 - INFO - 查询日期范围: 2025-04-25 至 2025-05-01，使用分页查询，每页 100 条记录
2025-05-28 08:04:23,821 - INFO - Request Parameters - Page 1:
2025-05-28 08:04:23,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:23,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:24,524 - INFO - API请求耗时: 703ms
2025-05-28 08:04:24,524 - INFO - Response - Page 1
2025-05-28 08:04:24,524 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:04:25,024 - INFO - Request Parameters - Page 2:
2025-05-28 08:04:25,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:25,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:25,727 - INFO - API请求耗时: 703ms
2025-05-28 08:04:25,727 - INFO - Response - Page 2
2025-05-28 08:04:25,727 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:04:26,227 - INFO - Request Parameters - Page 3:
2025-05-28 08:04:26,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:26,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:27,008 - INFO - API请求耗时: 781ms
2025-05-28 08:04:27,008 - INFO - Response - Page 3
2025-05-28 08:04:27,008 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:04:27,508 - INFO - Request Parameters - Page 4:
2025-05-28 08:04:27,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:27,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:28,258 - INFO - API请求耗时: 750ms
2025-05-28 08:04:28,274 - INFO - Response - Page 4
2025-05-28 08:04:28,274 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:04:28,789 - INFO - Request Parameters - Page 5:
2025-05-28 08:04:28,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:28,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:29,524 - INFO - API请求耗时: 734ms
2025-05-28 08:04:29,524 - INFO - Response - Page 5
2025-05-28 08:04:29,524 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:04:30,039 - INFO - Request Parameters - Page 6:
2025-05-28 08:04:30,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:30,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:30,742 - INFO - API请求耗时: 703ms
2025-05-28 08:04:30,742 - INFO - Response - Page 6
2025-05-28 08:04:30,742 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:04:31,258 - INFO - Request Parameters - Page 7:
2025-05-28 08:04:31,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:31,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:31,977 - INFO - API请求耗时: 719ms
2025-05-28 08:04:31,977 - INFO - Response - Page 7
2025-05-28 08:04:31,977 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:04:32,477 - INFO - Request Parameters - Page 8:
2025-05-28 08:04:32,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:32,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:33,180 - INFO - API请求耗时: 703ms
2025-05-28 08:04:33,180 - INFO - Response - Page 8
2025-05-28 08:04:33,180 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:04:33,680 - INFO - Request Parameters - Page 9:
2025-05-28 08:04:33,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:33,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:34,430 - INFO - API请求耗时: 750ms
2025-05-28 08:04:34,430 - INFO - Response - Page 9
2025-05-28 08:04:34,445 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:04:34,961 - INFO - Request Parameters - Page 10:
2025-05-28 08:04:34,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:34,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:35,664 - INFO - API请求耗时: 703ms
2025-05-28 08:04:35,664 - INFO - Response - Page 10
2025-05-28 08:04:35,664 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:04:36,164 - INFO - Request Parameters - Page 11:
2025-05-28 08:04:36,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:36,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:36,836 - INFO - API请求耗时: 672ms
2025-05-28 08:04:36,836 - INFO - Response - Page 11
2025-05-28 08:04:36,836 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:04:37,352 - INFO - Request Parameters - Page 12:
2025-05-28 08:04:37,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:37,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:38,102 - INFO - API请求耗时: 750ms
2025-05-28 08:04:38,102 - INFO - Response - Page 12
2025-05-28 08:04:38,102 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:04:38,617 - INFO - Request Parameters - Page 13:
2025-05-28 08:04:38,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:38,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:39,274 - INFO - API请求耗时: 656ms
2025-05-28 08:04:39,274 - INFO - Response - Page 13
2025-05-28 08:04:39,274 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:04:39,774 - INFO - Request Parameters - Page 14:
2025-05-28 08:04:39,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:39,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:40,461 - INFO - API请求耗时: 687ms
2025-05-28 08:04:40,461 - INFO - Response - Page 14
2025-05-28 08:04:40,477 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:04:40,977 - INFO - Request Parameters - Page 15:
2025-05-28 08:04:40,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:40,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:41,695 - INFO - API请求耗时: 719ms
2025-05-28 08:04:41,695 - INFO - Response - Page 15
2025-05-28 08:04:41,695 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:04:42,195 - INFO - Request Parameters - Page 16:
2025-05-28 08:04:42,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:42,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:42,852 - INFO - API请求耗时: 656ms
2025-05-28 08:04:42,852 - INFO - Response - Page 16
2025-05-28 08:04:42,852 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:04:43,367 - INFO - Request Parameters - Page 17:
2025-05-28 08:04:43,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:43,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:44,102 - INFO - API请求耗时: 734ms
2025-05-28 08:04:44,102 - INFO - Response - Page 17
2025-05-28 08:04:44,102 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:04:44,617 - INFO - Request Parameters - Page 18:
2025-05-28 08:04:44,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:44,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:45,274 - INFO - API请求耗时: 656ms
2025-05-28 08:04:45,274 - INFO - Response - Page 18
2025-05-28 08:04:45,274 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:04:45,789 - INFO - Request Parameters - Page 19:
2025-05-28 08:04:45,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:45,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:46,492 - INFO - API请求耗时: 703ms
2025-05-28 08:04:46,492 - INFO - Response - Page 19
2025-05-28 08:04:46,492 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:04:47,008 - INFO - Request Parameters - Page 20:
2025-05-28 08:04:47,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:47,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:47,898 - INFO - API请求耗时: 891ms
2025-05-28 08:04:47,898 - INFO - Response - Page 20
2025-05-28 08:04:47,898 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:04:48,399 - INFO - Request Parameters - Page 21:
2025-05-28 08:04:48,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:48,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:49,133 - INFO - API请求耗时: 734ms
2025-05-28 08:04:49,133 - INFO - Response - Page 21
2025-05-28 08:04:49,133 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:04:49,648 - INFO - Request Parameters - Page 22:
2025-05-28 08:04:49,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:49,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:50,305 - INFO - API请求耗时: 656ms
2025-05-28 08:04:50,305 - INFO - Response - Page 22
2025-05-28 08:04:50,305 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:04:50,820 - INFO - Request Parameters - Page 23:
2025-05-28 08:04:50,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:50,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:51,523 - INFO - API请求耗时: 703ms
2025-05-28 08:04:51,523 - INFO - Response - Page 23
2025-05-28 08:04:51,523 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:04:52,039 - INFO - Request Parameters - Page 24:
2025-05-28 08:04:52,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:52,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:52,773 - INFO - API请求耗时: 734ms
2025-05-28 08:04:52,773 - INFO - Response - Page 24
2025-05-28 08:04:52,773 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:04:53,289 - INFO - Request Parameters - Page 25:
2025-05-28 08:04:53,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:53,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:54,023 - INFO - API请求耗时: 734ms
2025-05-28 08:04:54,023 - INFO - Response - Page 25
2025-05-28 08:04:54,023 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:04:54,523 - INFO - Request Parameters - Page 26:
2025-05-28 08:04:54,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:54,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:55,273 - INFO - API请求耗时: 750ms
2025-05-28 08:04:55,273 - INFO - Response - Page 26
2025-05-28 08:04:55,273 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:04:55,773 - INFO - Request Parameters - Page 27:
2025-05-28 08:04:55,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:55,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:56,461 - INFO - API请求耗时: 688ms
2025-05-28 08:04:56,461 - INFO - Response - Page 27
2025-05-28 08:04:56,461 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:04:56,977 - INFO - Request Parameters - Page 28:
2025-05-28 08:04:56,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:56,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:57,805 - INFO - API请求耗时: 828ms
2025-05-28 08:04:57,805 - INFO - Response - Page 28
2025-05-28 08:04:57,805 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:04:58,320 - INFO - Request Parameters - Page 29:
2025-05-28 08:04:58,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:58,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:04:59,023 - INFO - API请求耗时: 703ms
2025-05-28 08:04:59,023 - INFO - Response - Page 29
2025-05-28 08:04:59,023 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:04:59,539 - INFO - Request Parameters - Page 30:
2025-05-28 08:04:59,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:04:59,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:00,258 - INFO - API请求耗时: 719ms
2025-05-28 08:05:00,258 - INFO - Response - Page 30
2025-05-28 08:05:00,258 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:05:00,773 - INFO - Request Parameters - Page 31:
2025-05-28 08:05:00,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:00,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:01,461 - INFO - API请求耗时: 687ms
2025-05-28 08:05:01,461 - INFO - Response - Page 31
2025-05-28 08:05:01,461 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:05:01,961 - INFO - Request Parameters - Page 32:
2025-05-28 08:05:01,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:01,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:02,680 - INFO - API请求耗时: 719ms
2025-05-28 08:05:02,680 - INFO - Response - Page 32
2025-05-28 08:05:02,680 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:05:03,195 - INFO - Request Parameters - Page 33:
2025-05-28 08:05:03,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:03,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:03,883 - INFO - API请求耗时: 687ms
2025-05-28 08:05:03,883 - INFO - Response - Page 33
2025-05-28 08:05:03,883 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:05:04,383 - INFO - Request Parameters - Page 34:
2025-05-28 08:05:04,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:04,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:05,055 - INFO - API请求耗时: 672ms
2025-05-28 08:05:05,070 - INFO - Response - Page 34
2025-05-28 08:05:05,070 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:05:05,586 - INFO - Request Parameters - Page 35:
2025-05-28 08:05:05,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:05,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:06,352 - INFO - API请求耗时: 766ms
2025-05-28 08:05:06,352 - INFO - Response - Page 35
2025-05-28 08:05:06,352 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:05:06,867 - INFO - Request Parameters - Page 36:
2025-05-28 08:05:06,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:06,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:07,570 - INFO - API请求耗时: 703ms
2025-05-28 08:05:07,570 - INFO - Response - Page 36
2025-05-28 08:05:07,570 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:05:08,086 - INFO - Request Parameters - Page 37:
2025-05-28 08:05:08,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:08,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:08,820 - INFO - API请求耗时: 734ms
2025-05-28 08:05:08,820 - INFO - Response - Page 37
2025-05-28 08:05:08,820 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:05:09,320 - INFO - Request Parameters - Page 38:
2025-05-28 08:05:09,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:09,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:10,133 - INFO - API请求耗时: 812ms
2025-05-28 08:05:10,133 - INFO - Response - Page 38
2025-05-28 08:05:10,133 - INFO - 第 38 页获取到 100 条记录
2025-05-28 08:05:10,648 - INFO - Request Parameters - Page 39:
2025-05-28 08:05:10,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:10,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 39, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:11,414 - INFO - API请求耗时: 766ms
2025-05-28 08:05:11,414 - INFO - Response - Page 39
2025-05-28 08:05:11,414 - INFO - 第 39 页获取到 100 条记录
2025-05-28 08:05:11,914 - INFO - Request Parameters - Page 40:
2025-05-28 08:05:11,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:11,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 40, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:12,664 - INFO - API请求耗时: 750ms
2025-05-28 08:05:12,664 - INFO - Response - Page 40
2025-05-28 08:05:12,664 - INFO - 第 40 页获取到 100 条记录
2025-05-28 08:05:13,164 - INFO - Request Parameters - Page 41:
2025-05-28 08:05:13,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:13,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 41, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:13,992 - INFO - API请求耗时: 828ms
2025-05-28 08:05:13,992 - INFO - Response - Page 41
2025-05-28 08:05:13,992 - INFO - 第 41 页获取到 100 条记录
2025-05-28 08:05:14,508 - INFO - Request Parameters - Page 42:
2025-05-28 08:05:14,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:14,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 42, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:15,258 - INFO - API请求耗时: 750ms
2025-05-28 08:05:15,258 - INFO - Response - Page 42
2025-05-28 08:05:15,258 - INFO - 第 42 页获取到 100 条记录
2025-05-28 08:05:15,773 - INFO - Request Parameters - Page 43:
2025-05-28 08:05:15,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:15,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 43, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:16,633 - INFO - API请求耗时: 859ms
2025-05-28 08:05:16,633 - INFO - Response - Page 43
2025-05-28 08:05:16,633 - INFO - 第 43 页获取到 100 条记录
2025-05-28 08:05:17,148 - INFO - Request Parameters - Page 44:
2025-05-28 08:05:17,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:17,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 44, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:17,820 - INFO - API请求耗时: 672ms
2025-05-28 08:05:17,820 - INFO - Response - Page 44
2025-05-28 08:05:17,820 - INFO - 第 44 页获取到 100 条记录
2025-05-28 08:05:18,336 - INFO - Request Parameters - Page 45:
2025-05-28 08:05:18,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:18,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 45, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400759, 1746028800759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:18,867 - INFO - API请求耗时: 531ms
2025-05-28 08:05:18,867 - INFO - Response - Page 45
2025-05-28 08:05:18,867 - INFO - 第 45 页获取到 33 条记录
2025-05-28 08:05:18,867 - INFO - 查询完成，共获取到 4433 条记录
2025-05-28 08:05:18,867 - INFO - 分段 5 查询成功，获取到 4433 条记录
2025-05-28 08:05:19,867 - INFO - 查询分段 6: 2025-05-02 至 2025-05-08
2025-05-28 08:05:19,867 - INFO - 查询日期范围: 2025-05-02 至 2025-05-08，使用分页查询，每页 100 条记录
2025-05-28 08:05:19,867 - INFO - Request Parameters - Page 1:
2025-05-28 08:05:19,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:19,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:20,492 - INFO - API请求耗时: 625ms
2025-05-28 08:05:20,492 - INFO - Response - Page 1
2025-05-28 08:05:20,492 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:05:21,008 - INFO - Request Parameters - Page 2:
2025-05-28 08:05:21,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:21,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:21,867 - INFO - API请求耗时: 859ms
2025-05-28 08:05:21,867 - INFO - Response - Page 2
2025-05-28 08:05:21,867 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:05:22,383 - INFO - Request Parameters - Page 3:
2025-05-28 08:05:22,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:22,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:23,086 - INFO - API请求耗时: 703ms
2025-05-28 08:05:23,086 - INFO - Response - Page 3
2025-05-28 08:05:23,086 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:05:23,586 - INFO - Request Parameters - Page 4:
2025-05-28 08:05:23,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:23,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:24,289 - INFO - API请求耗时: 703ms
2025-05-28 08:05:24,305 - INFO - Response - Page 4
2025-05-28 08:05:24,305 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:05:24,820 - INFO - Request Parameters - Page 5:
2025-05-28 08:05:24,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:24,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:25,492 - INFO - API请求耗时: 672ms
2025-05-28 08:05:25,492 - INFO - Response - Page 5
2025-05-28 08:05:25,492 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:05:26,008 - INFO - Request Parameters - Page 6:
2025-05-28 08:05:26,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:26,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:26,758 - INFO - API请求耗时: 750ms
2025-05-28 08:05:26,758 - INFO - Response - Page 6
2025-05-28 08:05:26,758 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:05:27,258 - INFO - Request Parameters - Page 7:
2025-05-28 08:05:27,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:27,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:28,117 - INFO - API请求耗时: 859ms
2025-05-28 08:05:28,117 - INFO - Response - Page 7
2025-05-28 08:05:28,117 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:05:28,633 - INFO - Request Parameters - Page 8:
2025-05-28 08:05:28,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:28,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:29,633 - INFO - API请求耗时: 1000ms
2025-05-28 08:05:29,633 - INFO - Response - Page 8
2025-05-28 08:05:29,633 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:05:30,148 - INFO - Request Parameters - Page 9:
2025-05-28 08:05:30,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:30,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:30,992 - INFO - API请求耗时: 844ms
2025-05-28 08:05:30,992 - INFO - Response - Page 9
2025-05-28 08:05:30,992 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:05:31,492 - INFO - Request Parameters - Page 10:
2025-05-28 08:05:31,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:31,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:32,179 - INFO - API请求耗时: 687ms
2025-05-28 08:05:32,179 - INFO - Response - Page 10
2025-05-28 08:05:32,179 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:05:32,679 - INFO - Request Parameters - Page 11:
2025-05-28 08:05:32,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:32,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:33,492 - INFO - API请求耗时: 812ms
2025-05-28 08:05:33,492 - INFO - Response - Page 11
2025-05-28 08:05:33,492 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:05:34,008 - INFO - Request Parameters - Page 12:
2025-05-28 08:05:34,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:34,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:34,804 - INFO - API请求耗时: 797ms
2025-05-28 08:05:34,804 - INFO - Response - Page 12
2025-05-28 08:05:34,804 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:05:35,320 - INFO - Request Parameters - Page 13:
2025-05-28 08:05:35,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:35,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:36,039 - INFO - API请求耗时: 719ms
2025-05-28 08:05:36,039 - INFO - Response - Page 13
2025-05-28 08:05:36,039 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:05:36,539 - INFO - Request Parameters - Page 14:
2025-05-28 08:05:36,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:36,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:37,195 - INFO - API请求耗时: 656ms
2025-05-28 08:05:37,211 - INFO - Response - Page 14
2025-05-28 08:05:37,211 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:05:37,711 - INFO - Request Parameters - Page 15:
2025-05-28 08:05:37,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:37,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:38,383 - INFO - API请求耗时: 672ms
2025-05-28 08:05:38,383 - INFO - Response - Page 15
2025-05-28 08:05:38,383 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:05:38,898 - INFO - Request Parameters - Page 16:
2025-05-28 08:05:38,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:38,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:39,633 - INFO - API请求耗时: 734ms
2025-05-28 08:05:39,633 - INFO - Response - Page 16
2025-05-28 08:05:39,633 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:05:40,133 - INFO - Request Parameters - Page 17:
2025-05-28 08:05:40,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:40,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:40,820 - INFO - API请求耗时: 687ms
2025-05-28 08:05:40,820 - INFO - Response - Page 17
2025-05-28 08:05:40,820 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:05:41,336 - INFO - Request Parameters - Page 18:
2025-05-28 08:05:41,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:41,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:42,133 - INFO - API请求耗时: 797ms
2025-05-28 08:05:42,133 - INFO - Response - Page 18
2025-05-28 08:05:42,133 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:05:42,633 - INFO - Request Parameters - Page 19:
2025-05-28 08:05:42,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:42,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:43,383 - INFO - API请求耗时: 750ms
2025-05-28 08:05:43,383 - INFO - Response - Page 19
2025-05-28 08:05:43,383 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:05:43,883 - INFO - Request Parameters - Page 20:
2025-05-28 08:05:43,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:43,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:44,523 - INFO - API请求耗时: 641ms
2025-05-28 08:05:44,523 - INFO - Response - Page 20
2025-05-28 08:05:44,523 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:05:45,039 - INFO - Request Parameters - Page 21:
2025-05-28 08:05:45,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:45,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:45,820 - INFO - API请求耗时: 781ms
2025-05-28 08:05:45,820 - INFO - Response - Page 21
2025-05-28 08:05:45,820 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:05:46,320 - INFO - Request Parameters - Page 22:
2025-05-28 08:05:46,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:46,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:47,039 - INFO - API请求耗时: 719ms
2025-05-28 08:05:47,039 - INFO - Response - Page 22
2025-05-28 08:05:47,039 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:05:47,539 - INFO - Request Parameters - Page 23:
2025-05-28 08:05:47,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:47,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:48,304 - INFO - API请求耗时: 766ms
2025-05-28 08:05:48,304 - INFO - Response - Page 23
2025-05-28 08:05:48,304 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:05:48,804 - INFO - Request Parameters - Page 24:
2025-05-28 08:05:48,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:48,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:49,554 - INFO - API请求耗时: 750ms
2025-05-28 08:05:49,554 - INFO - Response - Page 24
2025-05-28 08:05:49,554 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:05:50,070 - INFO - Request Parameters - Page 25:
2025-05-28 08:05:50,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:50,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:50,882 - INFO - API请求耗时: 812ms
2025-05-28 08:05:50,882 - INFO - Response - Page 25
2025-05-28 08:05:50,882 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:05:51,382 - INFO - Request Parameters - Page 26:
2025-05-28 08:05:51,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:51,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:52,195 - INFO - API请求耗时: 812ms
2025-05-28 08:05:52,195 - INFO - Response - Page 26
2025-05-28 08:05:52,195 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:05:52,711 - INFO - Request Parameters - Page 27:
2025-05-28 08:05:52,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:52,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:53,429 - INFO - API请求耗时: 719ms
2025-05-28 08:05:53,429 - INFO - Response - Page 27
2025-05-28 08:05:53,429 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:05:53,945 - INFO - Request Parameters - Page 28:
2025-05-28 08:05:53,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:53,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:54,632 - INFO - API请求耗时: 687ms
2025-05-28 08:05:54,632 - INFO - Response - Page 28
2025-05-28 08:05:54,632 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:05:55,148 - INFO - Request Parameters - Page 29:
2025-05-28 08:05:55,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:55,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:55,929 - INFO - API请求耗时: 781ms
2025-05-28 08:05:55,929 - INFO - Response - Page 29
2025-05-28 08:05:55,929 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:05:56,445 - INFO - Request Parameters - Page 30:
2025-05-28 08:05:56,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:56,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:57,179 - INFO - API请求耗时: 734ms
2025-05-28 08:05:57,179 - INFO - Response - Page 30
2025-05-28 08:05:57,179 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:05:57,679 - INFO - Request Parameters - Page 31:
2025-05-28 08:05:57,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:57,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:58,398 - INFO - API请求耗时: 719ms
2025-05-28 08:05:58,398 - INFO - Response - Page 31
2025-05-28 08:05:58,398 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:05:58,898 - INFO - Request Parameters - Page 32:
2025-05-28 08:05:58,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:05:58,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:05:59,632 - INFO - API请求耗时: 734ms
2025-05-28 08:05:59,632 - INFO - Response - Page 32
2025-05-28 08:05:59,632 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:06:00,148 - INFO - Request Parameters - Page 33:
2025-05-28 08:06:00,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:00,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:00,882 - INFO - API请求耗时: 734ms
2025-05-28 08:06:00,882 - INFO - Response - Page 33
2025-05-28 08:06:00,882 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:06:01,398 - INFO - Request Parameters - Page 34:
2025-05-28 08:06:01,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:01,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:02,101 - INFO - API请求耗时: 703ms
2025-05-28 08:06:02,101 - INFO - Response - Page 34
2025-05-28 08:06:02,101 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:06:02,601 - INFO - Request Parameters - Page 35:
2025-05-28 08:06:02,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:02,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:03,289 - INFO - API请求耗时: 688ms
2025-05-28 08:06:03,289 - INFO - Response - Page 35
2025-05-28 08:06:03,289 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:06:03,789 - INFO - Request Parameters - Page 36:
2025-05-28 08:06:03,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:03,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:04,476 - INFO - API请求耗时: 687ms
2025-05-28 08:06:04,476 - INFO - Response - Page 36
2025-05-28 08:06:04,492 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:06:04,992 - INFO - Request Parameters - Page 37:
2025-05-28 08:06:04,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:04,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:05,867 - INFO - API请求耗时: 859ms
2025-05-28 08:06:05,867 - INFO - Response - Page 37
2025-05-28 08:06:05,867 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:06:06,367 - INFO - Request Parameters - Page 38:
2025-05-28 08:06:06,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:06,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746115200759, 1746633600759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:06,914 - INFO - API请求耗时: 547ms
2025-05-28 08:06:06,914 - INFO - Response - Page 38
2025-05-28 08:06:06,914 - INFO - 第 38 页获取到 30 条记录
2025-05-28 08:06:06,914 - INFO - 查询完成，共获取到 3730 条记录
2025-05-28 08:06:06,914 - INFO - 分段 6 查询成功，获取到 3730 条记录
2025-05-28 08:06:07,929 - INFO - 查询分段 7: 2025-05-09 至 2025-05-15
2025-05-28 08:06:07,929 - INFO - 查询日期范围: 2025-05-09 至 2025-05-15，使用分页查询，每页 100 条记录
2025-05-28 08:06:07,929 - INFO - Request Parameters - Page 1:
2025-05-28 08:06:07,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:07,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:08,664 - INFO - API请求耗时: 734ms
2025-05-28 08:06:08,664 - INFO - Response - Page 1
2025-05-28 08:06:08,664 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:06:09,179 - INFO - Request Parameters - Page 2:
2025-05-28 08:06:09,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:09,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:09,976 - INFO - API请求耗时: 797ms
2025-05-28 08:06:09,976 - INFO - Response - Page 2
2025-05-28 08:06:09,976 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:06:10,492 - INFO - Request Parameters - Page 3:
2025-05-28 08:06:10,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:10,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:11,179 - INFO - API请求耗时: 687ms
2025-05-28 08:06:11,179 - INFO - Response - Page 3
2025-05-28 08:06:11,179 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:06:11,679 - INFO - Request Parameters - Page 4:
2025-05-28 08:06:11,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:11,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:12,414 - INFO - API请求耗时: 734ms
2025-05-28 08:06:12,414 - INFO - Response - Page 4
2025-05-28 08:06:12,414 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:06:12,929 - INFO - Request Parameters - Page 5:
2025-05-28 08:06:12,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:12,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:13,679 - INFO - API请求耗时: 750ms
2025-05-28 08:06:13,679 - INFO - Response - Page 5
2025-05-28 08:06:13,679 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:06:14,195 - INFO - Request Parameters - Page 6:
2025-05-28 08:06:14,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:14,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:14,898 - INFO - API请求耗时: 703ms
2025-05-28 08:06:14,898 - INFO - Response - Page 6
2025-05-28 08:06:14,898 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:06:15,398 - INFO - Request Parameters - Page 7:
2025-05-28 08:06:15,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:15,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:16,039 - INFO - API请求耗时: 641ms
2025-05-28 08:06:16,039 - INFO - Response - Page 7
2025-05-28 08:06:16,039 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:06:16,554 - INFO - Request Parameters - Page 8:
2025-05-28 08:06:16,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:16,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:17,351 - INFO - API请求耗时: 797ms
2025-05-28 08:06:17,351 - INFO - Response - Page 8
2025-05-28 08:06:17,351 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:06:17,851 - INFO - Request Parameters - Page 9:
2025-05-28 08:06:17,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:17,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:18,585 - INFO - API请求耗时: 734ms
2025-05-28 08:06:18,585 - INFO - Response - Page 9
2025-05-28 08:06:18,585 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:06:19,085 - INFO - Request Parameters - Page 10:
2025-05-28 08:06:19,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:19,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:20,023 - INFO - API请求耗时: 937ms
2025-05-28 08:06:20,023 - INFO - Response - Page 10
2025-05-28 08:06:20,023 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:06:20,523 - INFO - Request Parameters - Page 11:
2025-05-28 08:06:20,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:20,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:21,164 - INFO - API请求耗时: 641ms
2025-05-28 08:06:21,164 - INFO - Response - Page 11
2025-05-28 08:06:21,164 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:06:21,679 - INFO - Request Parameters - Page 12:
2025-05-28 08:06:21,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:21,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:22,367 - INFO - API请求耗时: 687ms
2025-05-28 08:06:22,367 - INFO - Response - Page 12
2025-05-28 08:06:22,367 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:06:22,867 - INFO - Request Parameters - Page 13:
2025-05-28 08:06:22,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:22,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:23,601 - INFO - API请求耗时: 734ms
2025-05-28 08:06:23,601 - INFO - Response - Page 13
2025-05-28 08:06:23,601 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:06:24,101 - INFO - Request Parameters - Page 14:
2025-05-28 08:06:24,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:24,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:24,820 - INFO - API请求耗时: 719ms
2025-05-28 08:06:24,820 - INFO - Response - Page 14
2025-05-28 08:06:24,820 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:06:25,320 - INFO - Request Parameters - Page 15:
2025-05-28 08:06:25,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:25,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:26,085 - INFO - API请求耗时: 766ms
2025-05-28 08:06:26,085 - INFO - Response - Page 15
2025-05-28 08:06:26,085 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:06:26,601 - INFO - Request Parameters - Page 16:
2025-05-28 08:06:26,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:26,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:27,320 - INFO - API请求耗时: 719ms
2025-05-28 08:06:27,320 - INFO - Response - Page 16
2025-05-28 08:06:27,320 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:06:27,835 - INFO - Request Parameters - Page 17:
2025-05-28 08:06:27,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:27,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:28,585 - INFO - API请求耗时: 750ms
2025-05-28 08:06:28,585 - INFO - Response - Page 17
2025-05-28 08:06:28,585 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:06:29,101 - INFO - Request Parameters - Page 18:
2025-05-28 08:06:29,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:29,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:29,788 - INFO - API请求耗时: 687ms
2025-05-28 08:06:29,788 - INFO - Response - Page 18
2025-05-28 08:06:29,788 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:06:30,288 - INFO - Request Parameters - Page 19:
2025-05-28 08:06:30,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:30,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:30,992 - INFO - API请求耗时: 703ms
2025-05-28 08:06:30,992 - INFO - Response - Page 19
2025-05-28 08:06:30,992 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:06:31,492 - INFO - Request Parameters - Page 20:
2025-05-28 08:06:31,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:31,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:32,226 - INFO - API请求耗时: 734ms
2025-05-28 08:06:32,226 - INFO - Response - Page 20
2025-05-28 08:06:32,226 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:06:32,726 - INFO - Request Parameters - Page 21:
2025-05-28 08:06:32,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:32,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:33,351 - INFO - API请求耗时: 625ms
2025-05-28 08:06:33,351 - INFO - Response - Page 21
2025-05-28 08:06:33,351 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:06:33,851 - INFO - Request Parameters - Page 22:
2025-05-28 08:06:33,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:33,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:34,538 - INFO - API请求耗时: 687ms
2025-05-28 08:06:34,538 - INFO - Response - Page 22
2025-05-28 08:06:34,538 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:06:35,038 - INFO - Request Parameters - Page 23:
2025-05-28 08:06:35,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:35,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:35,773 - INFO - API请求耗时: 734ms
2025-05-28 08:06:35,773 - INFO - Response - Page 23
2025-05-28 08:06:35,788 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:06:36,304 - INFO - Request Parameters - Page 24:
2025-05-28 08:06:36,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:36,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:36,976 - INFO - API请求耗时: 672ms
2025-05-28 08:06:36,976 - INFO - Response - Page 24
2025-05-28 08:06:36,976 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:06:37,476 - INFO - Request Parameters - Page 25:
2025-05-28 08:06:37,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:37,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:38,132 - INFO - API请求耗时: 656ms
2025-05-28 08:06:38,132 - INFO - Response - Page 25
2025-05-28 08:06:38,132 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:06:38,648 - INFO - Request Parameters - Page 26:
2025-05-28 08:06:38,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:38,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:39,398 - INFO - API请求耗时: 750ms
2025-05-28 08:06:39,398 - INFO - Response - Page 26
2025-05-28 08:06:39,398 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:06:39,913 - INFO - Request Parameters - Page 27:
2025-05-28 08:06:39,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:39,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:40,679 - INFO - API请求耗时: 766ms
2025-05-28 08:06:40,679 - INFO - Response - Page 27
2025-05-28 08:06:40,679 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:06:41,179 - INFO - Request Parameters - Page 28:
2025-05-28 08:06:41,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:41,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:41,804 - INFO - API请求耗时: 625ms
2025-05-28 08:06:41,804 - INFO - Response - Page 28
2025-05-28 08:06:41,804 - INFO - 第 28 页获取到 100 条记录
2025-05-28 08:06:42,304 - INFO - Request Parameters - Page 29:
2025-05-28 08:06:42,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:42,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:42,976 - INFO - API请求耗时: 672ms
2025-05-28 08:06:42,976 - INFO - Response - Page 29
2025-05-28 08:06:42,976 - INFO - 第 29 页获取到 100 条记录
2025-05-28 08:06:43,476 - INFO - Request Parameters - Page 30:
2025-05-28 08:06:43,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:43,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:44,273 - INFO - API请求耗时: 797ms
2025-05-28 08:06:44,273 - INFO - Response - Page 30
2025-05-28 08:06:44,273 - INFO - 第 30 页获取到 100 条记录
2025-05-28 08:06:44,773 - INFO - Request Parameters - Page 31:
2025-05-28 08:06:44,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:44,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:45,648 - INFO - API请求耗时: 875ms
2025-05-28 08:06:45,648 - INFO - Response - Page 31
2025-05-28 08:06:45,648 - INFO - 第 31 页获取到 100 条记录
2025-05-28 08:06:46,163 - INFO - Request Parameters - Page 32:
2025-05-28 08:06:46,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:46,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:46,945 - INFO - API请求耗时: 781ms
2025-05-28 08:06:46,945 - INFO - Response - Page 32
2025-05-28 08:06:46,945 - INFO - 第 32 页获取到 100 条记录
2025-05-28 08:06:47,460 - INFO - Request Parameters - Page 33:
2025-05-28 08:06:47,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:47,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:48,132 - INFO - API请求耗时: 672ms
2025-05-28 08:06:48,132 - INFO - Response - Page 33
2025-05-28 08:06:48,132 - INFO - 第 33 页获取到 100 条记录
2025-05-28 08:06:48,648 - INFO - Request Parameters - Page 34:
2025-05-28 08:06:48,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:48,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:49,351 - INFO - API请求耗时: 703ms
2025-05-28 08:06:49,351 - INFO - Response - Page 34
2025-05-28 08:06:49,351 - INFO - 第 34 页获取到 100 条记录
2025-05-28 08:06:49,866 - INFO - Request Parameters - Page 35:
2025-05-28 08:06:49,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:49,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 35, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:50,554 - INFO - API请求耗时: 687ms
2025-05-28 08:06:50,554 - INFO - Response - Page 35
2025-05-28 08:06:50,554 - INFO - 第 35 页获取到 100 条记录
2025-05-28 08:06:51,054 - INFO - Request Parameters - Page 36:
2025-05-28 08:06:51,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:51,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 36, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:51,757 - INFO - API请求耗时: 703ms
2025-05-28 08:06:51,757 - INFO - Response - Page 36
2025-05-28 08:06:51,757 - INFO - 第 36 页获取到 100 条记录
2025-05-28 08:06:52,273 - INFO - Request Parameters - Page 37:
2025-05-28 08:06:52,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:52,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 37, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:52,898 - INFO - API请求耗时: 625ms
2025-05-28 08:06:52,898 - INFO - Response - Page 37
2025-05-28 08:06:52,898 - INFO - 第 37 页获取到 100 条记录
2025-05-28 08:06:53,413 - INFO - Request Parameters - Page 38:
2025-05-28 08:06:53,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:53,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 38, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000759, 1747238400759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:54,054 - INFO - API请求耗时: 641ms
2025-05-28 08:06:54,054 - INFO - Response - Page 38
2025-05-28 08:06:54,054 - INFO - 第 38 页获取到 91 条记录
2025-05-28 08:06:54,054 - INFO - 查询完成，共获取到 3791 条记录
2025-05-28 08:06:54,054 - INFO - 分段 7 查询成功，获取到 3791 条记录
2025-05-28 08:06:55,054 - INFO - 查询分段 8: 2025-05-16 至 2025-05-22
2025-05-28 08:06:55,054 - INFO - 查询日期范围: 2025-05-16 至 2025-05-22，使用分页查询，每页 100 条记录
2025-05-28 08:06:55,054 - INFO - Request Parameters - Page 1:
2025-05-28 08:06:55,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:55,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:55,726 - INFO - API请求耗时: 672ms
2025-05-28 08:06:55,726 - INFO - Response - Page 1
2025-05-28 08:06:55,726 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:06:56,226 - INFO - Request Parameters - Page 2:
2025-05-28 08:06:56,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:56,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:57,054 - INFO - API请求耗时: 828ms
2025-05-28 08:06:57,054 - INFO - Response - Page 2
2025-05-28 08:06:57,054 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:06:57,554 - INFO - Request Parameters - Page 3:
2025-05-28 08:06:57,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:57,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:58,226 - INFO - API请求耗时: 672ms
2025-05-28 08:06:58,226 - INFO - Response - Page 3
2025-05-28 08:06:58,226 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:06:58,726 - INFO - Request Parameters - Page 4:
2025-05-28 08:06:58,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:58,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:06:59,398 - INFO - API请求耗时: 672ms
2025-05-28 08:06:59,398 - INFO - Response - Page 4
2025-05-28 08:06:59,398 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:06:59,913 - INFO - Request Parameters - Page 5:
2025-05-28 08:06:59,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:06:59,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:00,554 - INFO - API请求耗时: 641ms
2025-05-28 08:07:00,554 - INFO - Response - Page 5
2025-05-28 08:07:00,554 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:07:01,070 - INFO - Request Parameters - Page 6:
2025-05-28 08:07:01,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:01,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:01,820 - INFO - API请求耗时: 750ms
2025-05-28 08:07:01,820 - INFO - Response - Page 6
2025-05-28 08:07:01,820 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:07:02,335 - INFO - Request Parameters - Page 7:
2025-05-28 08:07:02,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:02,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:03,116 - INFO - API请求耗时: 781ms
2025-05-28 08:07:03,116 - INFO - Response - Page 7
2025-05-28 08:07:03,116 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:07:03,616 - INFO - Request Parameters - Page 8:
2025-05-28 08:07:03,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:03,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:04,366 - INFO - API请求耗时: 750ms
2025-05-28 08:07:04,366 - INFO - Response - Page 8
2025-05-28 08:07:04,366 - INFO - 第 8 页获取到 100 条记录
2025-05-28 08:07:04,882 - INFO - Request Parameters - Page 9:
2025-05-28 08:07:04,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:04,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:05,648 - INFO - API请求耗时: 766ms
2025-05-28 08:07:05,648 - INFO - Response - Page 9
2025-05-28 08:07:05,648 - INFO - 第 9 页获取到 100 条记录
2025-05-28 08:07:06,148 - INFO - Request Parameters - Page 10:
2025-05-28 08:07:06,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:06,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:07,038 - INFO - API请求耗时: 891ms
2025-05-28 08:07:07,038 - INFO - Response - Page 10
2025-05-28 08:07:07,038 - INFO - 第 10 页获取到 100 条记录
2025-05-28 08:07:07,538 - INFO - Request Parameters - Page 11:
2025-05-28 08:07:07,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:07,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:08,194 - INFO - API请求耗时: 656ms
2025-05-28 08:07:08,194 - INFO - Response - Page 11
2025-05-28 08:07:08,194 - INFO - 第 11 页获取到 100 条记录
2025-05-28 08:07:08,694 - INFO - Request Parameters - Page 12:
2025-05-28 08:07:08,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:08,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:09,366 - INFO - API请求耗时: 672ms
2025-05-28 08:07:09,366 - INFO - Response - Page 12
2025-05-28 08:07:09,382 - INFO - 第 12 页获取到 100 条记录
2025-05-28 08:07:09,898 - INFO - Request Parameters - Page 13:
2025-05-28 08:07:09,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:09,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:10,616 - INFO - API请求耗时: 719ms
2025-05-28 08:07:10,616 - INFO - Response - Page 13
2025-05-28 08:07:10,616 - INFO - 第 13 页获取到 100 条记录
2025-05-28 08:07:11,116 - INFO - Request Parameters - Page 14:
2025-05-28 08:07:11,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:11,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:11,773 - INFO - API请求耗时: 656ms
2025-05-28 08:07:11,773 - INFO - Response - Page 14
2025-05-28 08:07:11,773 - INFO - 第 14 页获取到 100 条记录
2025-05-28 08:07:12,273 - INFO - Request Parameters - Page 15:
2025-05-28 08:07:12,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:12,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:12,913 - INFO - API请求耗时: 641ms
2025-05-28 08:07:12,913 - INFO - Response - Page 15
2025-05-28 08:07:12,913 - INFO - 第 15 页获取到 100 条记录
2025-05-28 08:07:13,413 - INFO - Request Parameters - Page 16:
2025-05-28 08:07:13,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:13,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:14,132 - INFO - API请求耗时: 719ms
2025-05-28 08:07:14,132 - INFO - Response - Page 16
2025-05-28 08:07:14,132 - INFO - 第 16 页获取到 100 条记录
2025-05-28 08:07:14,648 - INFO - Request Parameters - Page 17:
2025-05-28 08:07:14,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:14,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:15,351 - INFO - API请求耗时: 703ms
2025-05-28 08:07:15,351 - INFO - Response - Page 17
2025-05-28 08:07:15,351 - INFO - 第 17 页获取到 100 条记录
2025-05-28 08:07:15,851 - INFO - Request Parameters - Page 18:
2025-05-28 08:07:15,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:15,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:16,616 - INFO - API请求耗时: 766ms
2025-05-28 08:07:16,616 - INFO - Response - Page 18
2025-05-28 08:07:16,632 - INFO - 第 18 页获取到 100 条记录
2025-05-28 08:07:17,148 - INFO - Request Parameters - Page 19:
2025-05-28 08:07:17,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:17,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:17,929 - INFO - API请求耗时: 781ms
2025-05-28 08:07:17,929 - INFO - Response - Page 19
2025-05-28 08:07:17,944 - INFO - 第 19 页获取到 100 条记录
2025-05-28 08:07:18,444 - INFO - Request Parameters - Page 20:
2025-05-28 08:07:18,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:18,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:19,132 - INFO - API请求耗时: 687ms
2025-05-28 08:07:19,132 - INFO - Response - Page 20
2025-05-28 08:07:19,132 - INFO - 第 20 页获取到 100 条记录
2025-05-28 08:07:19,632 - INFO - Request Parameters - Page 21:
2025-05-28 08:07:19,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:19,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:20,351 - INFO - API请求耗时: 719ms
2025-05-28 08:07:20,351 - INFO - Response - Page 21
2025-05-28 08:07:20,351 - INFO - 第 21 页获取到 100 条记录
2025-05-28 08:07:20,866 - INFO - Request Parameters - Page 22:
2025-05-28 08:07:20,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:20,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:21,538 - INFO - API请求耗时: 672ms
2025-05-28 08:07:21,538 - INFO - Response - Page 22
2025-05-28 08:07:21,538 - INFO - 第 22 页获取到 100 条记录
2025-05-28 08:07:22,054 - INFO - Request Parameters - Page 23:
2025-05-28 08:07:22,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:22,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:22,772 - INFO - API请求耗时: 719ms
2025-05-28 08:07:22,772 - INFO - Response - Page 23
2025-05-28 08:07:22,772 - INFO - 第 23 页获取到 100 条记录
2025-05-28 08:07:23,272 - INFO - Request Parameters - Page 24:
2025-05-28 08:07:23,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:23,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:23,944 - INFO - API请求耗时: 672ms
2025-05-28 08:07:23,944 - INFO - Response - Page 24
2025-05-28 08:07:23,944 - INFO - 第 24 页获取到 100 条记录
2025-05-28 08:07:24,444 - INFO - Request Parameters - Page 25:
2025-05-28 08:07:24,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:24,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:25,179 - INFO - API请求耗时: 734ms
2025-05-28 08:07:25,179 - INFO - Response - Page 25
2025-05-28 08:07:25,179 - INFO - 第 25 页获取到 100 条记录
2025-05-28 08:07:25,694 - INFO - Request Parameters - Page 26:
2025-05-28 08:07:25,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:25,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:26,366 - INFO - API请求耗时: 672ms
2025-05-28 08:07:26,366 - INFO - Response - Page 26
2025-05-28 08:07:26,366 - INFO - 第 26 页获取到 100 条记录
2025-05-28 08:07:26,882 - INFO - Request Parameters - Page 27:
2025-05-28 08:07:26,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:26,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:27,632 - INFO - API请求耗时: 750ms
2025-05-28 08:07:27,632 - INFO - Response - Page 27
2025-05-28 08:07:27,632 - INFO - 第 27 页获取到 100 条记录
2025-05-28 08:07:28,147 - INFO - Request Parameters - Page 28:
2025-05-28 08:07:28,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:28,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747324800759, 1747843200759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:28,632 - INFO - API请求耗时: 484ms
2025-05-28 08:07:28,632 - INFO - Response - Page 28
2025-05-28 08:07:28,632 - INFO - 第 28 页获取到 10 条记录
2025-05-28 08:07:28,632 - INFO - 查询完成，共获取到 2710 条记录
2025-05-28 08:07:28,632 - INFO - 分段 8 查询成功，获取到 2710 条记录
2025-05-28 08:07:29,632 - INFO - 查询分段 9: 2025-05-23 至 2025-05-27
2025-05-28 08:07:29,632 - INFO - 查询日期范围: 2025-05-23 至 2025-05-27，使用分页查询，每页 100 条记录
2025-05-28 08:07:29,632 - INFO - Request Parameters - Page 1:
2025-05-28 08:07:29,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:29,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600759, 1748361599759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:30,335 - INFO - API请求耗时: 703ms
2025-05-28 08:07:30,335 - INFO - Response - Page 1
2025-05-28 08:07:30,335 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:07:30,835 - INFO - Request Parameters - Page 2:
2025-05-28 08:07:30,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:30,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600759, 1748361599759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:31,538 - INFO - API请求耗时: 703ms
2025-05-28 08:07:31,538 - INFO - Response - Page 2
2025-05-28 08:07:31,538 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:07:32,054 - INFO - Request Parameters - Page 3:
2025-05-28 08:07:32,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:32,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600759, 1748361599759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:32,788 - INFO - API请求耗时: 734ms
2025-05-28 08:07:32,788 - INFO - Response - Page 3
2025-05-28 08:07:32,788 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:07:33,288 - INFO - Request Parameters - Page 4:
2025-05-28 08:07:33,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:33,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600759, 1748361599759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:33,944 - INFO - API请求耗时: 656ms
2025-05-28 08:07:33,944 - INFO - Response - Page 4
2025-05-28 08:07:33,944 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:07:34,460 - INFO - Request Parameters - Page 5:
2025-05-28 08:07:34,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:34,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600759, 1748361599759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:35,179 - INFO - API请求耗时: 719ms
2025-05-28 08:07:35,179 - INFO - Response - Page 5
2025-05-28 08:07:35,194 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:07:35,694 - INFO - Request Parameters - Page 6:
2025-05-28 08:07:35,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:07:35,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600759, 1748361599759], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:07:36,351 - INFO - API请求耗时: 656ms
2025-05-28 08:07:36,351 - INFO - Response - Page 6
2025-05-28 08:07:36,351 - INFO - 第 6 页获取到 82 条记录
2025-05-28 08:07:36,351 - INFO - 查询完成，共获取到 582 条记录
2025-05-28 08:07:36,351 - INFO - 分段 9 查询成功，获取到 582 条记录
2025-05-28 08:07:37,366 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 30489 条记录，失败 0 次
2025-05-28 08:07:37,366 - INFO - 成功获取宜搭日销售表单数据，共 30489 条记录
2025-05-28 08:07:37,366 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-28 08:07:37,366 - INFO - 开始对比和同步日销售数据...
2025-05-28 08:07:38,272 - INFO - 成功创建宜搭日销售数据索引，共 10822 条记录
2025-05-28 08:07:38,272 - INFO - 开始处理数衍数据，共 12978 条记录
2025-05-28 08:07:38,882 - INFO - 更新表单数据成功: FINST-XL866HB1AOZU2KH6EBM005W9F7ON33FHNBAAMV11
2025-05-28 08:07:38,882 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_********, 变更字段: [{'field': 'amount', 'old_value': 13369.29, 'new_value': 13575.49}, {'field': 'count', 'old_value': 132, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 3849.77, 'new_value': 4055.97}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 26}]
2025-05-28 08:07:39,350 - INFO - 更新表单数据成功: FINST-DOA66K91VPZUWRJGCAWQD9IJHTSV2VRH3RBAMY5
2025-05-28 08:07:39,350 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4351.6, 'new_value': 4429.6}, {'field': 'dailyBillAmount', 'old_value': 4351.6, 'new_value': 4429.6}]
2025-05-28 08:07:39,788 - INFO - 更新表单数据成功: FINST-FD966QA1M50VT7ZR67TW28XXXUOF3TUAC16AMBH
2025-05-28 08:07:39,788 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2783.0, 'new_value': 8528.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8528.0}]
2025-05-28 08:07:40,257 - INFO - 更新表单数据成功: FINST-XL666BD15CGVYUD8E6EYH6MLZX623UNA99PAM9
2025-05-28 08:07:40,257 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_20250511, 变更字段: [{'field': 'amount', 'old_value': 1381.0, 'new_value': 1840.0}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 1750.0, 'new_value': 2209.0}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-05-28 08:07:40,772 - INFO - 更新表单数据成功: FINST-VFF66XA1J7JVUBIND0A48ARTGTEK2U95RBUAM9D
2025-05-28 08:07:40,772 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 21477.64, 'new_value': 21507.64}, {'field': 'amount', 'old_value': 21477.64, 'new_value': 21507.64}, {'field': 'instoreAmount', 'old_value': 20135.0, 'new_value': 20165.0}]
2025-05-28 08:07:41,241 - INFO - 更新表单数据成功: FINST-YPE66RB1WEOVLBAO8KI89ATQQE0327HWGW2BMXE
2025-05-28 08:07:41,241 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 8220.63, 'new_value': 8235.53}, {'field': 'amount', 'old_value': 8220.63, 'new_value': 8235.53}, {'field': 'count', 'old_value': 168, 'new_value': 169}, {'field': 'instoreAmount', 'old_value': 7205.9, 'new_value': 7220.8}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 143}]
2025-05-28 08:07:41,788 - INFO - 更新表单数据成功: FINST-7PF66CC13AKVLF84EWBD0AB9MYP92H3G7RVAM81
2025-05-28 08:07:41,788 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 499.0, 'new_value': 1377.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1377.0}]
2025-05-28 08:07:42,257 - INFO - 更新表单数据成功: FINST-OLC66Z61U9KVF1PQDBYKVBIHKWIQ32BUN6XAMU3
2025-05-28 08:07:42,257 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_20250518, 变更字段: [{'field': 'amount', 'old_value': 1790.0, 'new_value': 2833.0}, {'field': 'count', 'old_value': 4, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1790.0, 'new_value': 2833.0}, {'field': 'instoreCount', 'old_value': 4, 'new_value': 5}]
2025-05-28 08:07:42,772 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2HA31H1BM3D
2025-05-28 08:07:42,772 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_20250519, 变更字段: [{'field': 'amount', 'old_value': 10942.2, 'new_value': 11173.2}, {'field': 'count', 'old_value': 119, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 5393.4, 'new_value': 5624.4}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 56}]
2025-05-28 08:07:43,241 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK384NK10BMKA
2025-05-28 08:07:43,241 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 41171.64}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 41171.64}]
2025-05-28 08:07:43,741 - INFO - 更新表单数据成功: FINST-XMC66R91B3OVP1PVC8ERUAC9N0ZF29E4HW2BMD7
2025-05-28 08:07:43,741 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250520, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 56069.49}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 56069.49}]
2025-05-28 08:07:44,194 - INFO - 更新表单数据成功: FINST-NWE664C1IDOVVO3MDJLY960TZFCV2IA31H1BMZD
2025-05-28 08:07:44,194 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 38630.68}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 38630.68}]
2025-05-28 08:07:44,679 - INFO - 更新表单数据成功: FINST-IQE66ZC1FKNVW4QR8MX848SEDAWK394NK10BM1C
2025-05-28 08:07:44,679 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_********, 变更字段: [{'field': 'amount', 'old_value': 1926.8, 'new_value': 2421.8}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 1926.8, 'new_value': 2421.8}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-05-28 08:07:45,147 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3ESDDR5BMG3
2025-05-28 08:07:45,147 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3251.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3251.9}]
2025-05-28 08:07:45,585 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3ESDDR5BMW3
2025-05-28 08:07:45,600 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 18097.0, 'new_value': 19285.0}, {'field': 'amount', 'old_value': 18097.0, 'new_value': 19285.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 18097.0, 'new_value': 19285.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-05-28 08:07:46,147 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BMK4
2025-05-28 08:07:46,147 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3219.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3219.4}]
2025-05-28 08:07:46,600 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BMT4
2025-05-28 08:07:46,600 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 8792.0, 'new_value': 8910.2}, {'field': 'amount', 'old_value': 8792.0, 'new_value': 8910.2}, {'field': 'count', 'old_value': 108, 'new_value': 109}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 118.2}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 1}]
2025-05-28 08:07:47,147 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BMV4
2025-05-28 08:07:47,147 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 2309.42, 'new_value': 2465.02}, {'field': 'amount', 'old_value': 2234.89, 'new_value': 2465.02}, {'field': 'count', 'old_value': 45, 'new_value': 48}, {'field': 'onlineAmount', 'old_value': 116.43, 'new_value': 346.56}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 7}]
2025-05-28 08:07:47,647 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BME5
2025-05-28 08:07:47,647 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_20250526, 变更字段: [{'field': 'amount', 'old_value': 3408.36, 'new_value': 3384.36}, {'field': 'onlineAmount', 'old_value': 1794.27, 'new_value': 1770.27}]
2025-05-28 08:07:48,147 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BMN5
2025-05-28 08:07:48,147 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_20250526, 变更字段: [{'field': 'amount', 'old_value': 3619.45, 'new_value': 3735.5499999999997}, {'field': 'count', 'old_value': 168, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 3703.54, 'new_value': 3819.64}, {'field': 'instoreCount', 'old_value': 168, 'new_value': 172}]
2025-05-28 08:07:48,569 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BMP5
2025-05-28 08:07:48,569 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_20250526, 变更字段: [{'field': 'amount', 'old_value': 117.0, 'new_value': 375.5}, {'field': 'count', 'old_value': 1, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 117.0, 'new_value': 375.5}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 8}]
2025-05-28 08:07:49,054 - INFO - 更新表单数据成功: FINST-XO8662C1ILRVTSS9CGOVSACKIKSN3FSDDR5BMT5
2025-05-28 08:07:49,054 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_20250526, 变更字段: [{'field': 'amount', 'old_value': 2612.8100000000004, 'new_value': 2657.51}, {'field': 'count', 'old_value': 45, 'new_value': 47}, {'field': 'onlineAmount', 'old_value': 662.01, 'new_value': 706.71}, {'field': 'onlineCount', 'old_value': 35, 'new_value': 37}]
2025-05-28 08:07:49,522 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O20FGDR5BMK9
2025-05-28 08:07:49,522 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_20250526, 变更字段: [{'field': 'amount', 'old_value': 939.4200000000001, 'new_value': 974.22}, {'field': 'count', 'old_value': 33, 'new_value': 35}, {'field': 'onlineAmount', 'old_value': 627.72, 'new_value': 662.52}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 28}]
2025-05-28 08:07:50,038 - INFO - 更新表单数据成功: FINST-12766OC1XAPVG5SNESF0Q66Q9V4N3NPXWB4BMV
2025-05-28 08:07:50,038 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 4836.5, 'new_value': 3458.6}, {'field': 'dailyBillAmount', 'old_value': 4836.5, 'new_value': 3458.6}]
2025-05-28 08:07:50,460 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O21FGDR5BMCA
2025-05-28 08:07:50,460 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7755.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7755.0}]
2025-05-28 08:07:50,960 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O21FGDR5BMLA
2025-05-28 08:07:50,960 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250526, 变更字段: [{'field': 'amount', 'old_value': 2343.0, 'new_value': 6831.0}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 2343.0, 'new_value': 6831.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-05-28 08:07:51,382 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O21FGDR5BMBB
2025-05-28 08:07:51,382 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 1832.96, 'new_value': 1802.76}, {'field': 'amount', 'old_value': 1832.96, 'new_value': 1802.76}, {'field': 'count', 'old_value': 112, 'new_value': 113}, {'field': 'onlineAmount', 'old_value': 1507.91, 'new_value': 1513.91}, {'field': 'onlineCount', 'old_value': 95, 'new_value': 96}]
2025-05-28 08:07:51,866 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O22FGDR5BMXB
2025-05-28 08:07:51,866 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 8727.05, 'new_value': 8909.55}, {'field': 'amount', 'old_value': 8727.05, 'new_value': 8909.55}, {'field': 'count', 'old_value': 205, 'new_value': 206}, {'field': 'instoreAmount', 'old_value': 7405.32, 'new_value': 7604.32}, {'field': 'instoreCount', 'old_value': 172, 'new_value': 173}, {'field': 'onlineAmount', 'old_value': 1321.73, 'new_value': 1305.23}]
2025-05-28 08:07:52,382 - INFO - 更新表单数据成功: FINST-OLF6658145RVH0VV9PCZG9C4RQ7O22FGDR5BMZB
2025-05-28 08:07:52,382 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_20250526, 变更字段: [{'field': 'amount', 'old_value': 5717.81, 'new_value': 5757.910000000001}, {'field': 'count', 'old_value': 235, 'new_value': 238}, {'field': 'instoreAmount', 'old_value': 3190.21, 'new_value': 3202.21}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 119}, {'field': 'onlineAmount', 'old_value': 2615.5, 'new_value': 2643.6}, {'field': 'onlineCount', 'old_value': 117, 'new_value': 119}]
2025-05-28 08:07:52,850 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3H3JDR5BM4
2025-05-28 08:07:52,850 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 1043.03, 'new_value': 1049.3}, {'field': 'amount', 'old_value': 1043.03, 'new_value': 1049.3}, {'field': 'count', 'old_value': 70, 'new_value': 72}, {'field': 'onlineAmount', 'old_value': 711.55, 'new_value': 717.82}, {'field': 'onlineCount', 'old_value': 53, 'new_value': 55}]
2025-05-28 08:07:53,272 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3H3JDR5BME
2025-05-28 08:07:53,272 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 4217.39, 'new_value': 4234.99}, {'field': 'amount', 'old_value': 4217.39, 'new_value': 4234.99}, {'field': 'count', 'old_value': 240, 'new_value': 242}, {'field': 'onlineAmount', 'old_value': 3218.85, 'new_value': 3236.45}, {'field': 'onlineCount', 'old_value': 189, 'new_value': 191}]
2025-05-28 08:07:53,741 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3H3JDR5BMQ
2025-05-28 08:07:53,757 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16332.1}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16332.1}]
2025-05-28 08:07:54,225 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3I3JDR5BMB1
2025-05-28 08:07:54,225 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_20250526, 变更字段: [{'field': 'amount', 'old_value': 19902.980000000003, 'new_value': 20420.980000000003}, {'field': 'count', 'old_value': 144, 'new_value': 145}, {'field': 'instoreAmount', 'old_value': 11686.98, 'new_value': 12204.98}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 52}]
2025-05-28 08:07:54,647 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3I3JDR5BMH1
2025-05-28 08:07:54,647 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_20250526, 变更字段: [{'field': 'amount', 'old_value': 8576.49, 'new_value': 8578.49}, {'field': 'count', 'old_value': 79, 'new_value': 80}, {'field': 'onlineAmount', 'old_value': 1879.8, 'new_value': 1881.8}, {'field': 'onlineCount', 'old_value': 38, 'new_value': 39}]
2025-05-28 08:07:55,116 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3I3JDR5BMO1
2025-05-28 08:07:55,116 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_20250526, 变更字段: [{'field': 'amount', 'old_value': 2922.8799999999997, 'new_value': 2932.7799999999997}, {'field': 'count', 'old_value': 158, 'new_value': 159}, {'field': 'onlineAmount', 'old_value': 1701.83, 'new_value': 1711.73}, {'field': 'onlineCount', 'old_value': 77, 'new_value': 78}]
2025-05-28 08:07:55,663 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3I3JDR5BMT1
2025-05-28 08:07:55,663 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_20250526, 变更字段: [{'field': 'amount', 'old_value': 12672.67, 'new_value': 12667.869999999999}]
2025-05-28 08:07:56,100 - INFO - 更新表单数据成功: FINST-3ME66E81O2OVZKGPB16668L51S1A29B0XB4BM701
2025-05-28 08:07:56,100 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 18775.18, 'new_value': 19244.18}, {'field': 'amount', 'old_value': 18775.18, 'new_value': 19244.18}, {'field': 'count', 'old_value': 103, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 18775.18, 'new_value': 19244.18}, {'field': 'instoreCount', 'old_value': 103, 'new_value': 104}]
2025-05-28 08:07:56,616 - INFO - 更新表单数据成功: FINST-YWD66FA1LHRVMKVUDN2I76S71A9L3I3JDR5BMT2
2025-05-28 08:07:56,616 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_20250526, 变更字段: [{'field': 'amount', 'old_value': 0.0, 'new_value': 12498.0}, {'field': 'count', 'old_value': 0, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 0.0, 'new_value': 12498.0}, {'field': 'instoreCount', 'old_value': 0, 'new_value': 2}]
2025-05-28 08:07:56,991 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BMV8
2025-05-28 08:07:56,991 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8297.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8297.0}]
2025-05-28 08:07:57,460 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BM49
2025-05-28 08:07:57,460 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_20250526, 变更字段: [{'field': 'amount', 'old_value': 2511.07, 'new_value': 2694.07}, {'field': 'count', 'old_value': 98, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 1355.56, 'new_value': 1538.56}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 68}]
2025-05-28 08:07:57,944 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BM79
2025-05-28 08:07:57,944 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_20250526, 变更字段: [{'field': 'amount', 'old_value': 3370.96, 'new_value': 3419.93}, {'field': 'count', 'old_value': 225, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 336.83, 'new_value': 340.08}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 24}, {'field': 'onlineAmount', 'old_value': 3149.43, 'new_value': 3195.15}, {'field': 'onlineCount', 'old_value': 202, 'new_value': 211}]
2025-05-28 08:07:58,382 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BMO9
2025-05-28 08:07:58,382 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250526, 变更字段: [{'field': 'instoreAmount', 'old_value': 4096.76, 'new_value': 4112.45}, {'field': 'instoreCount', 'old_value': 267, 'new_value': 271}, {'field': 'onlineAmount', 'old_value': 1340.39, 'new_value': 1324.7}, {'field': 'onlineCount', 'old_value': 109, 'new_value': 105}]
2025-05-28 08:07:58,913 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BMT9
2025-05-28 08:07:58,913 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_20250526, 变更字段: [{'field': 'amount', 'old_value': 1374.41, 'new_value': 1787.71}, {'field': 'count', 'old_value': 43, 'new_value': 45}, {'field': 'onlineAmount', 'old_value': 374.56, 'new_value': 787.86}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 12}]
2025-05-28 08:07:59,350 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BMW9
2025-05-28 08:07:59,350 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_20250526, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11235.2}]
2025-05-28 08:07:59,803 - INFO - 更新表单数据成功: FINST-XO8662C1GENV7A5E6XMCT4ROK2093NY2XB4BM7N
2025-05-28 08:07:59,803 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_20250525, 变更字段: [{'field': 'recommendAmount', 'old_value': 13915.0, 'new_value': 14403.9}, {'field': 'amount', 'old_value': 13915.0, 'new_value': 14403.9}, {'field': 'count', 'old_value': 47, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 14034.0, 'new_value': 14522.9}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 48}]
2025-05-28 08:08:00,319 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BM7A
2025-05-28 08:08:00,319 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_20250526, 变更字段: [{'field': 'amount', 'old_value': 17408.5, 'new_value': 17432.8}, {'field': 'count', 'old_value': 147, 'new_value': 148}, {'field': 'onlineAmount', 'old_value': 2740.5, 'new_value': 2764.8}, {'field': 'onlineCount', 'old_value': 70, 'new_value': 71}]
2025-05-28 08:08:00,819 - INFO - 更新表单数据成功: FINST-I3F66991MFPV0GYV77LSY6TLXKBM3TS1HW2BMR2
2025-05-28 08:08:00,819 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_20250524, 变更字段: [{'field': 'amount', 'old_value': 10207.2, 'new_value': 10222.2}, {'field': 'count', 'old_value': 96, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 4143.0, 'new_value': 4158.0}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 35}]
2025-05-28 08:08:01,319 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BMVA
2025-05-28 08:08:01,319 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 5148.52, 'new_value': 5174.22}, {'field': 'amount', 'old_value': 5148.5199999999995, 'new_value': 5174.22}, {'field': 'count', 'old_value': 308, 'new_value': 310}, {'field': 'instoreAmount', 'old_value': 2796.8, 'new_value': 2809.6}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 162}, {'field': 'onlineAmount', 'old_value': 2386.06, 'new_value': 2398.96}, {'field': 'onlineCount', 'old_value': 147, 'new_value': 148}]
2025-05-28 08:08:01,772 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BM0B
2025-05-28 08:08:01,772 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_20250526, 变更字段: [{'field': 'amount', 'old_value': 11047.66, 'new_value': 11920.56}, {'field': 'count', 'old_value': 141, 'new_value': 144}, {'field': 'instoreAmount', 'old_value': 9974.08, 'new_value': 10846.98}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 95}]
2025-05-28 08:08:02,241 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3RRLDR5BM9B
2025-05-28 08:08:02,241 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_20250526, 变更字段: [{'field': 'amount', 'old_value': 14036.42, 'new_value': 16907.42}, {'field': 'count', 'old_value': 28, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 13963.12, 'new_value': 16834.12}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 28}]
2025-05-28 08:08:02,694 - INFO - 更新表单数据成功: FINST-X8D66N81MLRV9MT4BSZYCARXCYLL3SRLDR5BMDB
2025-05-28 08:08:02,694 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5906.13}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5906.13}]
2025-05-28 08:08:03,100 - INFO - 更新表单数据成功: FINST-2PF66JD115RVSO9W514UH9SBTQA03OEODR5BMQ6
2025-05-28 08:08:03,100 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_20250526, 变更字段: [{'field': 'recommendAmount', 'old_value': 38806.2, 'new_value': 40112.2}, {'field': 'amount', 'old_value': 38806.2, 'new_value': 40112.2}, {'field': 'count', 'old_value': 113, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 38806.2, 'new_value': 40112.2}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 114}]
2025-05-28 08:08:03,600 - INFO - 更新表单数据成功: FINST-2PF66JD115RVSO9W514UH9SBTQA03OEODR5BMW6
2025-05-28 08:08:03,600 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_20250526, 变更字段: [{'field': 'amount', 'old_value': 12309.46, 'new_value': 12283.759999999998}]
2025-05-28 08:08:03,663 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-28 08:08:04,116 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-28 08:08:07,132 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-28 08:08:07,663 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-28 08:08:10,678 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-28 08:08:11,194 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-28 08:08:14,210 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-28 08:08:14,616 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-28 08:08:17,631 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-28 08:08:18,006 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-28 08:08:21,022 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-28 08:08:21,491 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-28 08:08:24,506 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-28 08:08:24,913 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-28 08:08:27,928 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-28 08:08:28,350 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-28 08:08:31,366 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-28 08:08:31,788 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-28 08:08:34,803 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-28 08:08:35,225 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-28 08:08:38,241 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-28 08:08:38,772 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-28 08:08:41,788 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-28 08:08:42,209 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-28 08:08:45,225 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-28 08:08:45,819 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-28 08:08:48,834 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-28 08:08:49,209 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-28 08:08:52,225 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-28 08:08:52,709 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-28 08:08:55,725 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-28 08:08:56,178 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-28 08:08:59,209 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-28 08:08:59,647 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-28 08:09:02,662 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-28 08:09:03,162 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-28 08:09:06,178 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-28 08:09:06,694 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-28 08:09:09,709 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-28 08:09:10,194 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-28 08:09:13,209 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-28 08:09:13,709 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-28 08:09:16,725 - INFO - 正在批量插入每日数据，批次 22/22，共 56 条记录
2025-05-28 08:09:17,053 - INFO - 批量插入每日数据成功，批次 22，56 条记录
2025-05-28 08:09:20,069 - INFO - 批量插入每日数据完成: 总计 2156 条，成功 2156 条，失败 0 条
2025-05-28 08:09:20,069 - INFO - 批量插入日销售数据完成，共 2156 条记录
2025-05-28 08:09:20,069 - INFO - 日销售数据同步完成！更新: 53 条，插入: 2156 条，错误: 0 条，跳过: 10769 条
2025-05-28 08:09:20,069 - INFO - 正在获取宜搭月销售表单数据...
2025-05-28 08:09:20,069 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-28 08:09:20,069 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-28 08:09:20,069 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:20,069 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:20,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:20,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:20,522 - INFO - API请求耗时: 453ms
2025-05-28 08:09:20,522 - INFO - Response - Page 1
2025-05-28 08:09:20,522 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:20,522 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:20,522 - WARNING - 月度分段 1 查询返回空数据
2025-05-28 08:09:20,522 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-28 08:09:20,522 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:20,522 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:20,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:20,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:21,006 - INFO - API请求耗时: 484ms
2025-05-28 08:09:21,006 - INFO - Response - Page 1
2025-05-28 08:09:21,006 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:21,006 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:21,006 - WARNING - 单月查询返回空数据: 2024-05
2025-05-28 08:09:21,522 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-28 08:09:21,522 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:21,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:21,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:21,740 - INFO - API请求耗时: 219ms
2025-05-28 08:09:21,740 - INFO - Response - Page 1
2025-05-28 08:09:21,740 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:21,740 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:21,740 - WARNING - 单月查询返回空数据: 2024-06
2025-05-28 08:09:22,256 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:22,256 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:22,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:22,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:22,553 - INFO - API请求耗时: 297ms
2025-05-28 08:09:22,553 - INFO - Response - Page 1
2025-05-28 08:09:22,553 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:22,553 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:22,553 - WARNING - 单月查询返回空数据: 2024-07
2025-05-28 08:09:24,069 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-28 08:09:24,069 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:24,069 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:24,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:24,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:24,272 - INFO - API请求耗时: 203ms
2025-05-28 08:09:24,272 - INFO - Response - Page 1
2025-05-28 08:09:24,272 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:24,272 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:24,272 - WARNING - 月度分段 2 查询返回空数据
2025-05-28 08:09:24,272 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-28 08:09:24,272 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:24,272 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:24,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:24,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:24,490 - INFO - API请求耗时: 219ms
2025-05-28 08:09:24,490 - INFO - Response - Page 1
2025-05-28 08:09:24,490 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:24,490 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:24,490 - WARNING - 单月查询返回空数据: 2024-08
2025-05-28 08:09:25,006 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-28 08:09:25,006 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:25,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:25,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:25,209 - INFO - API请求耗时: 203ms
2025-05-28 08:09:25,209 - INFO - Response - Page 1
2025-05-28 08:09:25,209 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:25,209 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:25,209 - WARNING - 单月查询返回空数据: 2024-09
2025-05-28 08:09:25,709 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:25,709 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:25,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:25,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:25,975 - INFO - API请求耗时: 266ms
2025-05-28 08:09:25,975 - INFO - Response - Page 1
2025-05-28 08:09:25,975 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-28 08:09:25,975 - INFO - 查询完成，共获取到 0 条记录
2025-05-28 08:09:25,975 - WARNING - 单月查询返回空数据: 2024-10
2025-05-28 08:09:27,475 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-28 08:09:27,475 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:27,475 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:27,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:27,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:28,084 - INFO - API请求耗时: 609ms
2025-05-28 08:09:28,084 - INFO - Response - Page 1
2025-05-28 08:09:28,100 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:09:28,600 - INFO - Request Parameters - Page 2:
2025-05-28 08:09:28,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:28,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:29,381 - INFO - API请求耗时: 781ms
2025-05-28 08:09:29,381 - INFO - Response - Page 2
2025-05-28 08:09:29,381 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:09:29,912 - INFO - Request Parameters - Page 3:
2025-05-28 08:09:29,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:29,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:30,365 - INFO - API请求耗时: 453ms
2025-05-28 08:09:30,365 - INFO - Response - Page 3
2025-05-28 08:09:30,365 - INFO - 第 3 页获取到 48 条记录
2025-05-28 08:09:30,365 - INFO - 查询完成，共获取到 248 条记录
2025-05-28 08:09:30,365 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-28 08:09:31,381 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-28 08:09:31,381 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-28 08:09:31,381 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:31,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:31,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:32,022 - INFO - API请求耗时: 641ms
2025-05-28 08:09:32,022 - INFO - Response - Page 1
2025-05-28 08:09:32,022 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:09:32,522 - INFO - Request Parameters - Page 2:
2025-05-28 08:09:32,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:32,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:33,068 - INFO - API请求耗时: 547ms
2025-05-28 08:09:33,068 - INFO - Response - Page 2
2025-05-28 08:09:33,068 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:09:33,584 - INFO - Request Parameters - Page 3:
2025-05-28 08:09:33,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:33,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:34,115 - INFO - API请求耗时: 531ms
2025-05-28 08:09:34,115 - INFO - Response - Page 3
2025-05-28 08:09:34,115 - INFO - 第 3 页获取到 100 条记录
2025-05-28 08:09:34,615 - INFO - Request Parameters - Page 4:
2025-05-28 08:09:34,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:34,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:35,147 - INFO - API请求耗时: 531ms
2025-05-28 08:09:35,147 - INFO - Response - Page 4
2025-05-28 08:09:35,147 - INFO - 第 4 页获取到 100 条记录
2025-05-28 08:09:35,647 - INFO - Request Parameters - Page 5:
2025-05-28 08:09:35,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:35,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:36,193 - INFO - API请求耗时: 547ms
2025-05-28 08:09:36,193 - INFO - Response - Page 5
2025-05-28 08:09:36,193 - INFO - 第 5 页获取到 100 条记录
2025-05-28 08:09:36,709 - INFO - Request Parameters - Page 6:
2025-05-28 08:09:36,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:36,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:37,459 - INFO - API请求耗时: 750ms
2025-05-28 08:09:37,459 - INFO - Response - Page 6
2025-05-28 08:09:37,459 - INFO - 第 6 页获取到 100 条记录
2025-05-28 08:09:37,959 - INFO - Request Parameters - Page 7:
2025-05-28 08:09:37,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:37,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:38,662 - INFO - API请求耗时: 703ms
2025-05-28 08:09:38,662 - INFO - Response - Page 7
2025-05-28 08:09:38,662 - INFO - 第 7 页获取到 100 条记录
2025-05-28 08:09:39,162 - INFO - Request Parameters - Page 8:
2025-05-28 08:09:39,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:39,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:39,522 - INFO - API请求耗时: 359ms
2025-05-28 08:09:39,522 - INFO - Response - Page 8
2025-05-28 08:09:39,522 - INFO - 第 8 页获取到 16 条记录
2025-05-28 08:09:39,522 - INFO - 查询完成，共获取到 716 条记录
2025-05-28 08:09:39,522 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-28 08:09:40,522 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-28 08:09:40,522 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-28 08:09:40,522 - INFO - Request Parameters - Page 1:
2025-05-28 08:09:40,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:40,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:41,037 - INFO - API请求耗时: 516ms
2025-05-28 08:09:41,037 - INFO - Response - Page 1
2025-05-28 08:09:41,037 - INFO - 第 1 页获取到 100 条记录
2025-05-28 08:09:41,553 - INFO - Request Parameters - Page 2:
2025-05-28 08:09:41,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:41,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:42,100 - INFO - API请求耗时: 547ms
2025-05-28 08:09:42,100 - INFO - Response - Page 2
2025-05-28 08:09:42,100 - INFO - 第 2 页获取到 100 条记录
2025-05-28 08:09:42,600 - INFO - Request Parameters - Page 3:
2025-05-28 08:09:42,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-28 08:09:42,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-28 08:09:42,990 - INFO - API请求耗时: 391ms
2025-05-28 08:09:42,990 - INFO - Response - Page 3
2025-05-28 08:09:42,990 - INFO - 第 3 页获取到 24 条记录
2025-05-28 08:09:42,990 - INFO - 查询完成，共获取到 224 条记录
2025-05-28 08:09:42,990 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-28 08:09:44,006 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-28 08:09:44,006 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-28 08:09:44,006 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-28 08:09:44,006 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-28 08:09:44,006 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-28 08:09:44,068 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-28 08:09:44,506 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-28 08:09:44,506 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 185494.08000000002, 'new_value': 191106.86000000002}, {'field': 'dailyBillAmount', 'old_value': 185494.08000000002, 'new_value': 191106.86000000002}, {'field': 'amount', 'old_value': 5310.3, 'new_value': 5366.2}, {'field': 'count', 'old_value': 74, 'new_value': 75}, {'field': 'onlineAmount', 'old_value': 5386.3, 'new_value': 5442.2}, {'field': 'onlineCount', 'old_value': 74, 'new_value': 75}]
2025-05-28 08:09:44,959 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-28 08:09:44,959 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 472707.05, 'new_value': 487060.09}, {'field': 'dailyBillAmount', 'old_value': 472707.05, 'new_value': 487060.09}, {'field': 'amount', 'old_value': 252658.8, 'new_value': 260246.9}, {'field': 'count', 'old_value': 2369, 'new_value': 2457}, {'field': 'instoreAmount', 'old_value': 103397.4, 'new_value': 106821.6}, {'field': 'instoreCount', 'old_value': 804, 'new_value': 837}, {'field': 'onlineAmount', 'old_value': 149613.8, 'new_value': 153777.9}, {'field': 'onlineCount', 'old_value': 1565, 'new_value': 1620}]
2025-05-28 08:09:45,397 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMMJ
2025-05-28 08:09:45,397 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 323325.93, 'new_value': 329850.74}, {'field': 'dailyBillAmount', 'old_value': 323325.93, 'new_value': 329850.74}, {'field': 'amount', 'old_value': 326104.03, 'new_value': 332517.93}, {'field': 'count', 'old_value': 2159, 'new_value': 2203}, {'field': 'instoreAmount', 'old_value': 309156.48, 'new_value': 315226.08}, {'field': 'instoreCount', 'old_value': 1911, 'new_value': 1946}, {'field': 'onlineAmount', 'old_value': 17189.75, 'new_value': 17618.95}, {'field': 'onlineCount', 'old_value': 248, 'new_value': 257}]
2025-05-28 08:09:45,912 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-28 08:09:45,912 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 698735.48, 'new_value': 725083.37}, {'field': 'dailyBillAmount', 'old_value': 698735.48, 'new_value': 725083.37}, {'field': 'amount', 'old_value': 503697.68, 'new_value': 521681.08}, {'field': 'count', 'old_value': 2436, 'new_value': 2519}, {'field': 'instoreAmount', 'old_value': 503697.68, 'new_value': 521681.08}, {'field': 'instoreCount', 'old_value': 2436, 'new_value': 2519}]
2025-05-28 08:09:46,397 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-28 08:09:46,397 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 548742.6, 'new_value': 561172.39}, {'field': 'dailyBillAmount', 'old_value': 548742.6, 'new_value': 561172.39}, {'field': 'amount', 'old_value': 902658.0, 'new_value': 924104.0}, {'field': 'count', 'old_value': 3129, 'new_value': 3221}, {'field': 'instoreAmount', 'old_value': 903908.0, 'new_value': 925354.0}, {'field': 'instoreCount', 'old_value': 3129, 'new_value': 3221}]
2025-05-28 08:09:46,990 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-28 08:09:46,990 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64492.9, 'new_value': 65203.8}, {'field': 'dailyBillAmount', 'old_value': 64492.9, 'new_value': 65203.8}, {'field': 'amount', 'old_value': 85054.31, 'new_value': 85934.61}, {'field': 'count', 'old_value': 325, 'new_value': 333}, {'field': 'instoreAmount', 'old_value': 45418.6, 'new_value': 46016.6}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 47}, {'field': 'onlineAmount', 'old_value': 43732.62, 'new_value': 44472.92}, {'field': 'onlineCount', 'old_value': 279, 'new_value': 286}]
2025-05-28 08:09:47,475 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-28 08:09:47,475 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'count', 'old_value': 129, 'new_value': 134}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 134}]
2025-05-28 08:09:47,897 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-28 08:09:47,897 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 764162.41, 'new_value': 785985.94}, {'field': 'dailyBillAmount', 'old_value': 764162.41, 'new_value': 785985.94}, {'field': 'amount', 'old_value': 675541.35, 'new_value': 690890.4}, {'field': 'count', 'old_value': 4793, 'new_value': 4938}, {'field': 'instoreAmount', 'old_value': 557335.41, 'new_value': 570557.41}, {'field': 'instoreCount', 'old_value': 2408, 'new_value': 2483}, {'field': 'onlineAmount', 'old_value': 122288.17, 'new_value': 124432.37}, {'field': 'onlineCount', 'old_value': 2385, 'new_value': 2455}]
2025-05-28 08:09:48,397 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-28 08:09:48,397 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 757619.32, 'new_value': 779283.89}, {'field': 'dailyBillAmount', 'old_value': 757619.32, 'new_value': 779283.89}, {'field': 'amount', 'old_value': 208465.04, 'new_value': 213588.03}, {'field': 'count', 'old_value': 1182, 'new_value': 1204}, {'field': 'instoreAmount', 'old_value': 208465.04, 'new_value': 213588.03}, {'field': 'instoreCount', 'old_value': 1182, 'new_value': 1204}]
2025-05-28 08:09:48,818 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-28 08:09:48,818 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 462346.71, 'new_value': 481288.58}, {'field': 'dailyBillAmount', 'old_value': 462346.71, 'new_value': 481288.58}, {'field': 'amount', 'old_value': 73097.0, 'new_value': 75036.0}, {'field': 'count', 'old_value': 77, 'new_value': 78}, {'field': 'instoreAmount', 'old_value': 73097.0, 'new_value': 75036.0}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 78}]
2025-05-28 08:09:49,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-28 08:09:49,240 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 109570.2, 'new_value': 122919.1}, {'field': 'count', 'old_value': 303, 'new_value': 319}, {'field': 'instoreAmount', 'old_value': 109571.9, 'new_value': 122920.8}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 319}]
2025-05-28 08:09:49,678 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-28 08:09:49,678 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1039195.7, 'new_value': 1064825.99}, {'field': 'dailyBillAmount', 'old_value': 1039195.7, 'new_value': 1064825.99}, {'field': 'amount', 'old_value': -378179.93, 'new_value': -390257.31}, {'field': 'count', 'old_value': 1139, 'new_value': 1162}, {'field': 'instoreAmount', 'old_value': 665773.51, 'new_value': 678528.59}, {'field': 'instoreCount', 'old_value': 1139, 'new_value': 1162}]
2025-05-28 08:09:50,162 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-28 08:09:50,162 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 449580.0, 'new_value': 460163.0}, {'field': 'amount', 'old_value': 449580.0, 'new_value': 460163.0}, {'field': 'count', 'old_value': 1473, 'new_value': 1520}, {'field': 'instoreAmount', 'old_value': 449580.0, 'new_value': 460163.0}, {'field': 'instoreCount', 'old_value': 1473, 'new_value': 1520}]
2025-05-28 08:09:50,600 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-28 08:09:50,600 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 411007.69, 'new_value': 421305.49}, {'field': 'dailyBillAmount', 'old_value': 317989.19, 'new_value': 327421.19}, {'field': 'amount', 'old_value': 411007.69, 'new_value': 421305.49}, {'field': 'count', 'old_value': 1390, 'new_value': 1423}, {'field': 'instoreAmount', 'old_value': 411007.69, 'new_value': 421305.49}, {'field': 'instoreCount', 'old_value': 1390, 'new_value': 1423}]
2025-05-28 08:09:51,006 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-28 08:09:51,006 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 209728.71, 'new_value': 217483.71}, {'field': 'dailyBillAmount', 'old_value': 209728.71, 'new_value': 217483.71}]
2025-05-28 08:09:51,397 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-28 08:09:51,412 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 107133.56, 'new_value': 110231.06999999999}, {'field': 'dailyBillAmount', 'old_value': 107133.56, 'new_value': 110231.06999999999}, {'field': 'amount', 'old_value': 65303.47, 'new_value': 66946.27}, {'field': 'count', 'old_value': 975, 'new_value': 1005}, {'field': 'instoreAmount', 'old_value': 67269.37, 'new_value': 68912.17}, {'field': 'instoreCount', 'old_value': 975, 'new_value': 1005}]
2025-05-28 08:09:51,865 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-28 08:09:51,865 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 155773.31, 'new_value': 161893.66999999998}, {'field': 'amount', 'old_value': 155772.45, 'new_value': 161892.81}, {'field': 'count', 'old_value': 5346, 'new_value': 5551}, {'field': 'instoreAmount', 'old_value': 135536.68, 'new_value': 140869.56}, {'field': 'instoreCount', 'old_value': 4837, 'new_value': 5019}, {'field': 'onlineAmount', 'old_value': 20236.63, 'new_value': 21024.11}, {'field': 'onlineCount', 'old_value': 509, 'new_value': 532}]
2025-05-28 08:09:52,428 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-28 08:09:52,428 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 315343.22, 'new_value': 328214.22}, {'field': 'dailyBillAmount', 'old_value': 309953.0, 'new_value': 322824.0}, {'field': 'amount', 'old_value': 261677.01, 'new_value': 273860.01}, {'field': 'count', 'old_value': 244, 'new_value': 253}, {'field': 'instoreAmount', 'old_value': 261444.0, 'new_value': 273627.0}, {'field': 'instoreCount', 'old_value': 241, 'new_value': 250}]
2025-05-28 08:09:52,881 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-28 08:09:52,881 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 600227.55, 'new_value': 617790.38}, {'field': 'dailyBillAmount', 'old_value': 599723.0, 'new_value': 617285.83}, {'field': 'amount', 'old_value': 600227.55, 'new_value': 617790.38}, {'field': 'count', 'old_value': 537, 'new_value': 553}, {'field': 'instoreAmount', 'old_value': 600228.55, 'new_value': 617791.38}, {'field': 'instoreCount', 'old_value': 537, 'new_value': 553}]
2025-05-28 08:09:53,381 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-28 08:09:53,381 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109801.1, 'new_value': 112446.7}, {'field': 'dailyBillAmount', 'old_value': 109801.1, 'new_value': 112446.7}, {'field': 'amount', 'old_value': 119362.5, 'new_value': 123366.0}, {'field': 'count', 'old_value': 318, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 119368.4, 'new_value': 123371.9}, {'field': 'instoreCount', 'old_value': 318, 'new_value': 330}]
2025-05-28 08:09:53,850 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-28 08:09:53,850 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172122.7, 'new_value': 180566.7}, {'field': 'amount', 'old_value': 172122.7, 'new_value': 180566.7}, {'field': 'count', 'old_value': 198, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 172249.7, 'new_value': 180693.7}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 209}]
2025-05-28 08:09:54,287 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-28 08:09:54,287 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 241951.38, 'new_value': 246830.28}, {'field': 'dailyBillAmount', 'old_value': 241951.38, 'new_value': 246830.28}, {'field': 'amount', 'old_value': 255036.55, 'new_value': 260425.45}, {'field': 'count', 'old_value': 1687, 'new_value': 1725}, {'field': 'instoreAmount', 'old_value': 256245.55, 'new_value': 261634.45}, {'field': 'instoreCount', 'old_value': 1687, 'new_value': 1725}]
2025-05-28 08:09:54,725 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-28 08:09:54,725 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 152884.72, 'new_value': 160267.07}, {'field': 'dailyBillAmount', 'old_value': 152884.72, 'new_value': 160267.07}, {'field': 'amount', 'old_value': 15657.06, 'new_value': 15865.96}, {'field': 'count', 'old_value': 1452, 'new_value': 1484}, {'field': 'instoreAmount', 'old_value': 20911.22, 'new_value': 21281.92}, {'field': 'instoreCount', 'old_value': 1452, 'new_value': 1484}]
2025-05-28 08:09:55,240 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-28 08:09:55,240 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 295512.41, 'new_value': 316127.18}, {'field': 'amount', 'old_value': 295508.26, 'new_value': 316123.03}, {'field': 'count', 'old_value': 6814, 'new_value': 7056}, {'field': 'instoreAmount', 'old_value': 288595.86, 'new_value': 309062.13}, {'field': 'instoreCount', 'old_value': 6576, 'new_value': 6809}, {'field': 'onlineAmount', 'old_value': 11144.13, 'new_value': 11587.63}, {'field': 'onlineCount', 'old_value': 238, 'new_value': 247}]
2025-05-28 08:09:55,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-28 08:09:55,646 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 250304.1, 'new_value': 254859.7}, {'field': 'dailyBillAmount', 'old_value': 250304.1, 'new_value': 254859.7}, {'field': 'amount', 'old_value': 250304.1, 'new_value': 254859.7}, {'field': 'count', 'old_value': 744, 'new_value': 759}, {'field': 'instoreAmount', 'old_value': 250304.1, 'new_value': 254859.7}, {'field': 'instoreCount', 'old_value': 744, 'new_value': 759}]
2025-05-28 08:09:56,053 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-28 08:09:56,053 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 220450.83, 'new_value': 227343.43}, {'field': 'dailyBillAmount', 'old_value': 220450.83, 'new_value': 227343.43}, {'field': 'amount', 'old_value': 73572.2, 'new_value': 75237.2}, {'field': 'count', 'old_value': 172, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 73572.2, 'new_value': 75237.2}, {'field': 'instoreCount', 'old_value': 172, 'new_value': 175}]
2025-05-28 08:09:56,584 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-28 08:09:56,584 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 434260.18, 'new_value': 447221.48}, {'field': 'dailyBillAmount', 'old_value': 434260.18, 'new_value': 447221.48}, {'field': 'amount', 'old_value': 180465.7, 'new_value': 185207.0}, {'field': 'count', 'old_value': 675, 'new_value': 694}, {'field': 'instoreAmount', 'old_value': 180465.96, 'new_value': 185207.26}, {'field': 'instoreCount', 'old_value': 675, 'new_value': 694}]
2025-05-28 08:09:57,037 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFK
2025-05-28 08:09:57,053 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24749.0, 'new_value': 25044.0}, {'field': 'amount', 'old_value': 24749.0, 'new_value': 25044.0}, {'field': 'count', 'old_value': 21, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 24749.0, 'new_value': 25044.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 22}]
2025-05-28 08:09:57,506 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-28 08:09:57,506 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95151.12, 'new_value': 98891.12}, {'field': 'dailyBillAmount', 'old_value': 95151.12, 'new_value': 98891.12}, {'field': 'amount', 'old_value': 28644.65, 'new_value': 29585.77}, {'field': 'count', 'old_value': 1042, 'new_value': 1078}, {'field': 'instoreAmount', 'old_value': 6740.53, 'new_value': 6971.34}, {'field': 'instoreCount', 'old_value': 176, 'new_value': 182}, {'field': 'onlineAmount', 'old_value': 22227.17, 'new_value': 22937.7}, {'field': 'onlineCount', 'old_value': 866, 'new_value': 896}]
2025-05-28 08:09:58,053 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-28 08:09:58,053 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151351.01, 'new_value': 157459.63}, {'field': 'dailyBillAmount', 'old_value': 151351.01, 'new_value': 157459.63}, {'field': 'amount', 'old_value': 25384.78, 'new_value': 26055.44}, {'field': 'count', 'old_value': 616, 'new_value': 638}, {'field': 'instoreAmount', 'old_value': 22062.86, 'new_value': 22623.14}, {'field': 'instoreCount', 'old_value': 548, 'new_value': 567}, {'field': 'onlineAmount', 'old_value': 3322.61, 'new_value': 3432.99}, {'field': 'onlineCount', 'old_value': 68, 'new_value': 71}]
2025-05-28 08:09:58,459 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-28 08:09:58,459 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 21221.43, 'new_value': 21517.93}, {'field': 'dailyBillAmount', 'old_value': 21221.43, 'new_value': 21517.93}, {'field': 'amount', 'old_value': 16657.58, 'new_value': 16954.08}, {'field': 'count', 'old_value': 608, 'new_value': 621}, {'field': 'instoreAmount', 'old_value': 17025.18, 'new_value': 17321.68}, {'field': 'instoreCount', 'old_value': 608, 'new_value': 621}]
2025-05-28 08:09:58,881 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-28 08:09:58,896 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48067.14, 'new_value': 49403.47}, {'field': 'dailyBillAmount', 'old_value': 48067.14, 'new_value': 49403.47}, {'field': 'amount', 'old_value': 32530.48, 'new_value': 33267.98}, {'field': 'count', 'old_value': 1646, 'new_value': 1691}, {'field': 'instoreAmount', 'old_value': 17329.72, 'new_value': 17606.72}, {'field': 'instoreCount', 'old_value': 660, 'new_value': 673}, {'field': 'onlineAmount', 'old_value': 16144.88, 'new_value': 16641.09}, {'field': 'onlineCount', 'old_value': 986, 'new_value': 1018}]
2025-05-28 08:09:59,365 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-28 08:09:59,365 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 339664.22, 'new_value': 353981.93}, {'field': 'dailyBillAmount', 'old_value': 339664.22, 'new_value': 353981.93}, {'field': 'amount', 'old_value': 157497.46, 'new_value': 164673.24}, {'field': 'count', 'old_value': 662, 'new_value': 694}, {'field': 'instoreAmount', 'old_value': 162147.9, 'new_value': 169609.9}, {'field': 'instoreCount', 'old_value': 662, 'new_value': 694}]
2025-05-28 08:09:59,912 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-28 08:09:59,912 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 18499.55, 'new_value': 18823.37}, {'field': 'count', 'old_value': 159, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 18573.79, 'new_value': 18898.11}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 170}]
2025-05-28 08:10:00,350 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-28 08:10:00,350 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 208737.63, 'new_value': 216485.79}, {'field': 'dailyBillAmount', 'old_value': 208737.63, 'new_value': 216485.79}, {'field': 'amount', 'old_value': 101944.95, 'new_value': 106307.81999999999}, {'field': 'count', 'old_value': 4359, 'new_value': 4541}, {'field': 'instoreAmount', 'old_value': 104069.38, 'new_value': 108479.1}, {'field': 'instoreCount', 'old_value': 4359, 'new_value': 4541}]
2025-05-28 08:10:00,834 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-28 08:10:00,834 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 498196.2, 'new_value': 518538.0}, {'field': 'dailyBillAmount', 'old_value': 498196.2, 'new_value': 518538.0}, {'field': 'amount', 'old_value': 498196.2, 'new_value': 518538.0}, {'field': 'count', 'old_value': 630, 'new_value': 653}, {'field': 'instoreAmount', 'old_value': 498196.2, 'new_value': 518538.0}, {'field': 'instoreCount', 'old_value': 630, 'new_value': 653}]
2025-05-28 08:10:01,209 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-28 08:10:01,209 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 217157.13, 'new_value': 222753.63}, {'field': 'dailyBillAmount', 'old_value': 217157.13, 'new_value': 222753.63}, {'field': 'amount', 'old_value': 124756.25, 'new_value': 128049.45}, {'field': 'count', 'old_value': 328, 'new_value': 338}, {'field': 'instoreAmount', 'old_value': 126172.85, 'new_value': 129466.05}, {'field': 'instoreCount', 'old_value': 328, 'new_value': 338}]
2025-05-28 08:10:01,756 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-28 08:10:01,756 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54216.0, 'new_value': 57035.0}, {'field': 'dailyBillAmount', 'old_value': 54216.0, 'new_value': 57035.0}, {'field': 'amount', 'old_value': 54216.0, 'new_value': 57035.0}, {'field': 'count', 'old_value': 1064, 'new_value': 1114}, {'field': 'instoreAmount', 'old_value': 54255.0, 'new_value': 57074.0}, {'field': 'instoreCount', 'old_value': 1064, 'new_value': 1114}]
2025-05-28 08:10:02,178 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-28 08:10:02,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94475.84, 'new_value': 98030.77}, {'field': 'dailyBillAmount', 'old_value': 94475.84, 'new_value': 98030.77}, {'field': 'amount', 'old_value': 97751.14, 'new_value': 101201.88}, {'field': 'count', 'old_value': 5134, 'new_value': 5335}, {'field': 'instoreAmount', 'old_value': 47220.4, 'new_value': 49301.58}, {'field': 'instoreCount', 'old_value': 2359, 'new_value': 2462}, {'field': 'onlineAmount', 'old_value': 51833.83, 'new_value': 53269.75}, {'field': 'onlineCount', 'old_value': 2775, 'new_value': 2873}]
2025-05-28 08:10:02,834 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-28 08:10:02,834 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33762.2, 'new_value': 35233.72}, {'field': 'dailyBillAmount', 'old_value': 33762.2, 'new_value': 35233.72}, {'field': 'amount', 'old_value': 45895.75, 'new_value': 47765.07}, {'field': 'count', 'old_value': 1330, 'new_value': 1390}, {'field': 'instoreAmount', 'old_value': 42136.1, 'new_value': 44005.42}, {'field': 'instoreCount', 'old_value': 1179, 'new_value': 1239}]
2025-05-28 08:10:03,287 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-28 08:10:03,287 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68814.5, 'new_value': 71615.97}, {'field': 'dailyBillAmount', 'old_value': 68814.5, 'new_value': 71615.97}, {'field': 'amount', 'old_value': 68740.39, 'new_value': 71541.86}, {'field': 'count', 'old_value': 2664, 'new_value': 2775}, {'field': 'instoreAmount', 'old_value': 44956.34, 'new_value': 46811.18}, {'field': 'instoreCount', 'old_value': 1595, 'new_value': 1662}, {'field': 'onlineAmount', 'old_value': 24106.88, 'new_value': 25053.510000000002}, {'field': 'onlineCount', 'old_value': 1069, 'new_value': 1113}]
2025-05-28 08:10:03,725 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-28 08:10:03,725 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 67371.08, 'new_value': 69536.36}, {'field': 'count', 'old_value': 838, 'new_value': 862}, {'field': 'instoreAmount', 'old_value': 67899.98, 'new_value': 70065.26}, {'field': 'instoreCount', 'old_value': 838, 'new_value': 862}]
2025-05-28 08:10:04,178 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-28 08:10:04,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 73446.7, 'new_value': 75726.6}, {'field': 'amount', 'old_value': 73446.2, 'new_value': 75726.1}, {'field': 'count', 'old_value': 1882, 'new_value': 1940}, {'field': 'instoreAmount', 'old_value': 74536.5, 'new_value': 76834.0}, {'field': 'instoreCount', 'old_value': 1882, 'new_value': 1940}]
2025-05-28 08:10:04,662 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-28 08:10:04,662 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 343172.42, 'new_value': 348408.42}, {'field': 'dailyBillAmount', 'old_value': 343172.42, 'new_value': 348408.42}, {'field': 'amount', 'old_value': 108408.82, 'new_value': 110878.82}, {'field': 'count', 'old_value': 375, 'new_value': 384}, {'field': 'instoreAmount', 'old_value': 108408.82, 'new_value': 110878.82}, {'field': 'instoreCount', 'old_value': 375, 'new_value': 384}]
2025-05-28 08:10:05,178 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-28 08:10:05,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 91534.86, 'new_value': 92455.86}, {'field': 'dailyBillAmount', 'old_value': 91534.86, 'new_value': 92455.86}, {'field': 'amount', 'old_value': 92720.66, 'new_value': 93641.66}, {'field': 'count', 'old_value': 337, 'new_value': 342}, {'field': 'instoreAmount', 'old_value': 94855.29, 'new_value': 95776.29}, {'field': 'instoreCount', 'old_value': 337, 'new_value': 342}]
2025-05-28 08:10:05,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-28 08:10:05,646 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52096.0, 'new_value': 56211.0}, {'field': 'dailyBillAmount', 'old_value': 52096.0, 'new_value': 56211.0}, {'field': 'amount', 'old_value': 64265.0, 'new_value': 68871.0}, {'field': 'count', 'old_value': 123, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 69220.0, 'new_value': 74223.0}, {'field': 'instoreCount', 'old_value': 123, 'new_value': 132}]
2025-05-28 08:10:06,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-28 08:10:06,115 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94671.75, 'new_value': 99565.05}, {'field': 'amount', 'old_value': 94669.15, 'new_value': 99562.45}, {'field': 'count', 'old_value': 294, 'new_value': 310}, {'field': 'instoreAmount', 'old_value': 106252.65, 'new_value': 112850.55}, {'field': 'instoreCount', 'old_value': 294, 'new_value': 310}]
2025-05-28 08:10:06,709 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-28 08:10:06,709 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 123433.2, 'new_value': 126579.5}, {'field': 'dailyBillAmount', 'old_value': 123433.2, 'new_value': 126423.9}, {'field': 'amount', 'old_value': 69900.22, 'new_value': 72082.63}, {'field': 'count', 'old_value': 1890, 'new_value': 1943}, {'field': 'instoreAmount', 'old_value': 61200.1, 'new_value': 62891.72}, {'field': 'instoreCount', 'old_value': 1604, 'new_value': 1646}, {'field': 'onlineAmount', 'old_value': 9837.31, 'new_value': 10328.12}, {'field': 'onlineCount', 'old_value': 286, 'new_value': 297}]
2025-05-28 08:10:07,178 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-28 08:10:07,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 177620.42, 'new_value': 185763.36000000002}, {'field': 'dailyBillAmount', 'old_value': 172454.5, 'new_value': 180404.81}, {'field': 'amount', 'old_value': 177620.42, 'new_value': 185763.16}, {'field': 'count', 'old_value': 2183, 'new_value': 2281}, {'field': 'instoreAmount', 'old_value': 169446.85, 'new_value': 177382.85}, {'field': 'instoreCount', 'old_value': 2088, 'new_value': 2183}, {'field': 'onlineAmount', 'old_value': 8232.81, 'new_value': 8439.75}, {'field': 'onlineCount', 'old_value': 95, 'new_value': 98}]
2025-05-28 08:10:07,693 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-28 08:10:07,693 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'amount', 'old_value': 105397.55, 'new_value': 107453.43}, {'field': 'count', 'old_value': 468, 'new_value': 483}, {'field': 'instoreAmount', 'old_value': 101800.58, 'new_value': 103721.76}, {'field': 'instoreCount', 'old_value': 420, 'new_value': 433}, {'field': 'onlineAmount', 'old_value': 3734.97, 'new_value': 3869.67}, {'field': 'onlineCount', 'old_value': 48, 'new_value': 50}]
2025-05-28 08:10:08,162 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-28 08:10:08,162 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 200470.6, 'new_value': 207099.3}, {'field': 'dailyBillAmount', 'old_value': 200470.6, 'new_value': 207099.3}, {'field': 'amount', 'old_value': 204914.2, 'new_value': 211719.9}, {'field': 'count', 'old_value': 758, 'new_value': 784}, {'field': 'instoreAmount', 'old_value': 208479.1, 'new_value': 215284.8}, {'field': 'instoreCount', 'old_value': 758, 'new_value': 784}]
2025-05-28 08:10:08,693 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-28 08:10:08,693 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 18803.04, 'new_value': 22278.76}, {'field': 'count', 'old_value': 37, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 19084.44, 'new_value': 22560.16}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 39}]
2025-05-28 08:10:09,162 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-05-28 08:10:09,162 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20931.8, 'new_value': 24151.2}, {'field': 'dailyBillAmount', 'old_value': 20931.8, 'new_value': 24151.2}]
2025-05-28 08:10:09,584 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-28 08:10:09,584 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 50109.94, 'new_value': 52615.04}, {'field': 'amount', 'old_value': 50108.3, 'new_value': 52613.4}, {'field': 'count', 'old_value': 2504, 'new_value': 2627}, {'field': 'instoreAmount', 'old_value': 57570.04, 'new_value': 60174.88}, {'field': 'instoreCount', 'old_value': 2504, 'new_value': 2627}]
2025-05-28 08:10:10,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-28 08:10:10,115 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137471.15, 'new_value': 144580.22}, {'field': 'dailyBillAmount', 'old_value': 137471.15, 'new_value': 144580.22}, {'field': 'amount', 'old_value': 110165.1, 'new_value': 115378.2}, {'field': 'count', 'old_value': 450, 'new_value': 473}, {'field': 'instoreAmount', 'old_value': 110165.1, 'new_value': 115353.9}, {'field': 'instoreCount', 'old_value': 449, 'new_value': 471}, {'field': 'onlineAmount', 'old_value': 36.0, 'new_value': 60.3}, {'field': 'onlineCount', 'old_value': 1, 'new_value': 2}]
2025-05-28 08:10:10,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-28 08:10:10,646 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 390591.64, 'new_value': 404692.22}, {'field': 'dailyBillAmount', 'old_value': 390591.64, 'new_value': 404692.22}, {'field': 'amount', 'old_value': 224278.75, 'new_value': 232004.62}, {'field': 'count', 'old_value': 2582, 'new_value': 2657}, {'field': 'instoreAmount', 'old_value': 99857.99, 'new_value': 104213.71}, {'field': 'instoreCount', 'old_value': 1116, 'new_value': 1152}, {'field': 'onlineAmount', 'old_value': 124423.86, 'new_value': 127794.01}, {'field': 'onlineCount', 'old_value': 1466, 'new_value': 1505}]
2025-05-28 08:10:11,053 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-28 08:10:11,068 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'amount', 'old_value': 240448.19999999998, 'new_value': 245383.1}, {'field': 'count', 'old_value': 1467, 'new_value': 1504}, {'field': 'instoreAmount', 'old_value': 241228.1, 'new_value': 246163.0}, {'field': 'instoreCount', 'old_value': 1467, 'new_value': 1504}]
2025-05-28 08:10:11,459 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBL
2025-05-28 08:10:11,474 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 75353.89, 'new_value': 75428.89}, {'field': 'amount', 'old_value': 75353.89, 'new_value': 75428.89}, {'field': 'count', 'old_value': 35, 'new_value': 36}, {'field': 'instoreAmount', 'old_value': 75353.89, 'new_value': 75428.89}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 36}]
2025-05-28 08:10:11,943 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-28 08:10:11,943 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 157969.94, 'new_value': 165168.42}, {'field': 'dailyBillAmount', 'old_value': 157969.94, 'new_value': 165168.42}, {'field': 'amount', 'old_value': 100075.41, 'new_value': 105141.99}, {'field': 'count', 'old_value': 1125, 'new_value': 1180}, {'field': 'instoreAmount', 'old_value': 91286.37, 'new_value': 95872.45}, {'field': 'instoreCount', 'old_value': 807, 'new_value': 850}, {'field': 'onlineAmount', 'old_value': 10580.21, 'new_value': 11060.71}, {'field': 'onlineCount', 'old_value': 318, 'new_value': 330}]
2025-05-28 08:10:12,396 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-28 08:10:12,396 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 2607.05, 'new_value': 2893.05}, {'field': 'count', 'old_value': 33, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 2607.05, 'new_value': 2893.05}, {'field': 'instoreCount', 'old_value': 33, 'new_value': 35}]
2025-05-28 08:10:12,787 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-28 08:10:12,787 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 206324.44, 'new_value': 214665.34}, {'field': 'dailyBillAmount', 'old_value': 201518.69, 'new_value': 209859.59}, {'field': 'amount', 'old_value': 206324.44, 'new_value': 214665.34}, {'field': 'count', 'old_value': 867, 'new_value': 902}, {'field': 'instoreAmount', 'old_value': 206324.44, 'new_value': 214665.34}, {'field': 'instoreCount', 'old_value': 867, 'new_value': 902}]
2025-05-28 08:10:13,193 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-28 08:10:13,193 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25112.66, 'new_value': 25966.34}, {'field': 'dailyBillAmount', 'old_value': 25112.66, 'new_value': 25966.34}, {'field': 'amount', 'old_value': 29808.16, 'new_value': 30713.74}, {'field': 'count', 'old_value': 883, 'new_value': 917}, {'field': 'instoreAmount', 'old_value': 29827.96, 'new_value': 30733.54}, {'field': 'instoreCount', 'old_value': 883, 'new_value': 917}]
2025-05-28 08:10:13,646 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-28 08:10:13,646 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 315767.8, 'new_value': 325704.8}, {'field': 'amount', 'old_value': 315767.8, 'new_value': 325704.8}, {'field': 'count', 'old_value': 488, 'new_value': 506}, {'field': 'instoreAmount', 'old_value': 315767.8, 'new_value': 325704.8}, {'field': 'instoreCount', 'old_value': 488, 'new_value': 506}]
2025-05-28 08:10:14,146 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-28 08:10:14,146 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46799.59, 'new_value': 47964.09}, {'field': 'amount', 'old_value': 46799.59, 'new_value': 47964.09}, {'field': 'count', 'old_value': 394, 'new_value': 402}, {'field': 'instoreAmount', 'old_value': 46799.59, 'new_value': 47964.09}, {'field': 'instoreCount', 'old_value': 394, 'new_value': 402}]
2025-05-28 08:10:14,553 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-28 08:10:14,553 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 329507.0, 'new_value': 341582.0}, {'field': 'amount', 'old_value': 329507.0, 'new_value': 341582.0}, {'field': 'count', 'old_value': 74, 'new_value': 77}, {'field': 'instoreAmount', 'old_value': 329507.0, 'new_value': 341582.0}, {'field': 'instoreCount', 'old_value': 74, 'new_value': 77}]
2025-05-28 08:10:14,959 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-28 08:10:14,959 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 52612.090000000004, 'new_value': 54489.090000000004}, {'field': 'count', 'old_value': 552, 'new_value': 555}, {'field': 'instoreAmount', 'old_value': 52612.090000000004, 'new_value': 54489.090000000004}, {'field': 'instoreCount', 'old_value': 552, 'new_value': 555}]
2025-05-28 08:10:15,459 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-28 08:10:15,459 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45238.2, 'new_value': 45896.7}, {'field': 'dailyBillAmount', 'old_value': 45238.2, 'new_value': 45896.7}, {'field': 'amount', 'old_value': 46701.5, 'new_value': 47360.0}, {'field': 'count', 'old_value': 57, 'new_value': 59}, {'field': 'instoreAmount', 'old_value': 47599.5, 'new_value': 48258.0}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 59}]
2025-05-28 08:10:15,928 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-28 08:10:15,928 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 458209.32, 'new_value': 474035.32}, {'field': 'dailyBillAmount', 'old_value': 458209.32, 'new_value': 474035.32}, {'field': 'amount', 'old_value': 465380.32, 'new_value': 481206.32}, {'field': 'count', 'old_value': 1486, 'new_value': 1545}, {'field': 'instoreAmount', 'old_value': 465380.32, 'new_value': 481206.32}, {'field': 'instoreCount', 'old_value': 1486, 'new_value': 1545}]
2025-05-28 08:10:16,412 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-28 08:10:16,412 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 325017.0, 'new_value': 368202.0}, {'field': 'dailyBillAmount', 'old_value': 325017.0, 'new_value': 368202.0}, {'field': 'amount', 'old_value': 1051950.69, 'new_value': 1090339.59}, {'field': 'count', 'old_value': 1323, 'new_value': 1374}, {'field': 'instoreAmount', 'old_value': 1051950.86, 'new_value': 1090339.76}, {'field': 'instoreCount', 'old_value': 1323, 'new_value': 1374}]
2025-05-28 08:10:16,849 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-28 08:10:16,849 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 156944.1, 'new_value': 160196.0}, {'field': 'dailyBillAmount', 'old_value': 156944.1, 'new_value': 160196.0}, {'field': 'amount', 'old_value': 33524.6, 'new_value': 33845.6}, {'field': 'count', 'old_value': 129, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 33526.1, 'new_value': 33847.1}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 131}]
2025-05-28 08:10:17,349 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-28 08:10:17,349 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 201714.36, 'new_value': 204674.1}, {'field': 'amount', 'old_value': 201711.84, 'new_value': 204671.58}, {'field': 'count', 'old_value': 2127, 'new_value': 2163}, {'field': 'instoreAmount', 'old_value': 129124.65, 'new_value': 130259.13}, {'field': 'instoreCount', 'old_value': 1194, 'new_value': 1206}, {'field': 'onlineAmount', 'old_value': 77993.31, 'new_value': 79970.53}, {'field': 'onlineCount', 'old_value': 933, 'new_value': 957}]
2025-05-28 08:10:17,803 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-28 08:10:17,803 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 346532.26, 'new_value': 357488.38}, {'field': 'dailyBillAmount', 'old_value': 346532.26, 'new_value': 357488.38}, {'field': 'amount', 'old_value': 30681.47, 'new_value': 31237.9}, {'field': 'count', 'old_value': 938, 'new_value': 956}, {'field': 'instoreAmount', 'old_value': 35836.54, 'new_value': 36529.07}, {'field': 'instoreCount', 'old_value': 938, 'new_value': 956}]
2025-05-28 08:10:18,271 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-28 08:10:18,271 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 316947.63, 'new_value': 322553.78}, {'field': 'dailyBillAmount', 'old_value': 316947.63, 'new_value': 322553.78}, {'field': 'amount', 'old_value': 161902.9, 'new_value': 167025.41}, {'field': 'count', 'old_value': 3656, 'new_value': 3731}, {'field': 'instoreAmount', 'old_value': 134702.56, 'new_value': 139089.25}, {'field': 'instoreCount', 'old_value': 3042, 'new_value': 3101}, {'field': 'onlineAmount', 'old_value': 29447.5, 'new_value': 30183.32}, {'field': 'onlineCount', 'old_value': 614, 'new_value': 630}]
2025-05-28 08:10:18,709 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-28 08:10:18,724 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 286137.9, 'new_value': 291463.5}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11235.2}, {'field': 'amount', 'old_value': 286136.3, 'new_value': 291461.0}, {'field': 'count', 'old_value': 1135, 'new_value': 1156}, {'field': 'instoreAmount', 'old_value': 289516.7, 'new_value': 294842.3}, {'field': 'instoreCount', 'old_value': 1135, 'new_value': 1156}]
2025-05-28 08:10:19,146 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-28 08:10:19,146 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 455916.66, 'new_value': 459466.35}, {'field': 'count', 'old_value': 8623, 'new_value': 8671}, {'field': 'instoreAmount', 'old_value': 425765.42, 'new_value': 428151.75}, {'field': 'instoreCount', 'old_value': 8035, 'new_value': 8067}, {'field': 'onlineAmount', 'old_value': 31923.85, 'new_value': 33087.92}, {'field': 'onlineCount', 'old_value': 588, 'new_value': 604}]
2025-05-28 08:10:19,678 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-28 08:10:19,678 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 394111.52, 'new_value': 415589.67}, {'field': 'dailyBillAmount', 'old_value': 84306.85, 'new_value': 100651.44}, {'field': 'amount', 'old_value': 361826.15, 'new_value': 383304.3}, {'field': 'count', 'old_value': 8523, 'new_value': 9017}, {'field': 'instoreAmount', 'old_value': 280902.2, 'new_value': 292578.7}, {'field': 'instoreCount', 'old_value': 6082, 'new_value': 6320}, {'field': 'onlineAmount', 'old_value': 81083.85, 'new_value': 90885.5}, {'field': 'onlineCount', 'old_value': 2441, 'new_value': 2697}]
2025-05-28 08:10:20,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUL
2025-05-28 08:10:20,115 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30723.579999999998, 'new_value': 30332.579999999998}, {'field': 'count', 'old_value': 838, 'new_value': 842}, {'field': 'onlineAmount', 'old_value': 45018.729999999996, 'new_value': 45101.729999999996}, {'field': 'onlineCount', 'old_value': 795, 'new_value': 799}]
2025-05-28 08:10:20,584 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-28 08:10:20,584 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 108167.04, 'new_value': 114983.87}, {'field': 'dailyBillAmount', 'old_value': 108167.04, 'new_value': 114983.87}, {'field': 'amount', 'old_value': 171960.38, 'new_value': 177762.46}, {'field': 'count', 'old_value': 11746, 'new_value': 12144}, {'field': 'instoreAmount', 'old_value': 138363.01, 'new_value': 142880.23}, {'field': 'instoreCount', 'old_value': 9174, 'new_value': 9483}, {'field': 'onlineAmount', 'old_value': 37589.14, 'new_value': 39005.99}, {'field': 'onlineCount', 'old_value': 2572, 'new_value': 2661}]
2025-05-28 08:10:21,037 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-28 08:10:21,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 281651.83, 'new_value': 287629.37}, {'field': 'dailyBillAmount', 'old_value': 281651.83, 'new_value': 287629.37}, {'field': 'amount', 'old_value': 272787.96, 'new_value': 277513.16}, {'field': 'count', 'old_value': 8001, 'new_value': 8152}, {'field': 'instoreAmount', 'old_value': 274489.77, 'new_value': 279214.97}, {'field': 'instoreCount', 'old_value': 8001, 'new_value': 8152}]
2025-05-28 08:10:21,459 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-28 08:10:21,459 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82954.42, 'new_value': 85208.28}, {'field': 'amount', 'old_value': 82951.17, 'new_value': 85205.03}, {'field': 'count', 'old_value': 4496, 'new_value': 4624}, {'field': 'instoreAmount', 'old_value': 46021.89, 'new_value': 46843.65}, {'field': 'instoreCount', 'old_value': 2702, 'new_value': 2761}, {'field': 'onlineAmount', 'old_value': 36932.53, 'new_value': 38364.63}, {'field': 'onlineCount', 'old_value': 1794, 'new_value': 1863}]
2025-05-28 08:10:21,974 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-28 08:10:21,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 145767.26, 'new_value': 149211.53}, {'field': 'dailyBillAmount', 'old_value': 145767.26, 'new_value': 149211.53}, {'field': 'amount', 'old_value': 29951.190000000002, 'new_value': 30726.940000000002}, {'field': 'count', 'old_value': 1077, 'new_value': 1101}, {'field': 'instoreAmount', 'old_value': 30967.84, 'new_value': 31770.64}, {'field': 'instoreCount', 'old_value': 1077, 'new_value': 1101}]
2025-05-28 08:10:22,412 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-28 08:10:22,412 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 97375.49, 'new_value': 100077.65}, {'field': 'count', 'old_value': 4852, 'new_value': 4973}, {'field': 'instoreAmount', 'old_value': 20736.68, 'new_value': 21078.54}, {'field': 'instoreCount', 'old_value': 1495, 'new_value': 1516}, {'field': 'onlineAmount', 'old_value': 78200.54, 'new_value': 80560.84}, {'field': 'onlineCount', 'old_value': 3357, 'new_value': 3457}]
2025-05-28 08:10:22,865 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-28 08:10:22,865 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 113282.81, 'new_value': 115740.81}, {'field': 'amount', 'old_value': 113281.4, 'new_value': 115739.4}, {'field': 'count', 'old_value': 2902, 'new_value': 2965}, {'field': 'instoreAmount', 'old_value': 107490.12, 'new_value': 109770.32}, {'field': 'instoreCount', 'old_value': 2816, 'new_value': 2876}, {'field': 'onlineAmount', 'old_value': 6851.21, 'new_value': 7045.91}, {'field': 'onlineCount', 'old_value': 86, 'new_value': 89}]
2025-05-28 08:10:23,318 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-28 08:10:23,318 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 169059.05, 'new_value': 176907.18}, {'field': 'count', 'old_value': 6960, 'new_value': 7280}, {'field': 'instoreAmount', 'old_value': 172412.93, 'new_value': 180346.88}, {'field': 'instoreCount', 'old_value': 6899, 'new_value': 7215}, {'field': 'onlineAmount', 'old_value': 2292.41, 'new_value': 2399.41}, {'field': 'onlineCount', 'old_value': 61, 'new_value': 65}]
2025-05-28 08:10:23,662 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-28 08:10:23,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 185471.43, 'new_value': 190453.05}, {'field': 'dailyBillAmount', 'old_value': 185471.43, 'new_value': 190453.05}, {'field': 'amount', 'old_value': 122862.8, 'new_value': 126610.71}, {'field': 'count', 'old_value': 9952, 'new_value': 10190}, {'field': 'instoreAmount', 'old_value': 9063.65, 'new_value': 9502.44}, {'field': 'instoreCount', 'old_value': 515, 'new_value': 547}, {'field': 'onlineAmount', 'old_value': 119162.38, 'new_value': 122539.53}, {'field': 'onlineCount', 'old_value': 9437, 'new_value': 9643}]
2025-05-28 08:10:24,115 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-28 08:10:24,115 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 167296.7, 'new_value': 169617.11}, {'field': 'dailyBillAmount', 'old_value': 167296.7, 'new_value': 169617.11}, {'field': 'amount', 'old_value': 142196.61, 'new_value': 144454.25}, {'field': 'count', 'old_value': 4722, 'new_value': 4791}, {'field': 'instoreAmount', 'old_value': 77742.66, 'new_value': 78725.56999999999}, {'field': 'instoreCount', 'old_value': 3403, 'new_value': 3447}, {'field': 'onlineAmount', 'old_value': 73658.23, 'new_value': 75069.03}, {'field': 'onlineCount', 'old_value': 1319, 'new_value': 1344}]
2025-05-28 08:10:24,553 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-05-28 08:10:24,553 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 121704.45, 'new_value': 123322.45}, {'field': 'amount', 'old_value': 121703.92, 'new_value': 123321.92}, {'field': 'count', 'old_value': 83, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 121704.45, 'new_value': 123322.45}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 85}]
2025-05-28 08:10:25,084 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-28 08:10:25,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60141.979999999996, 'new_value': 62001.02}, {'field': 'dailyBillAmount', 'old_value': 60141.979999999996, 'new_value': 62001.02}, {'field': 'amount', 'old_value': 79378.0, 'new_value': 81602.47}, {'field': 'count', 'old_value': 3111, 'new_value': 3201}, {'field': 'instoreAmount', 'old_value': 25533.79, 'new_value': 26049.46}, {'field': 'instoreCount', 'old_value': 1096, 'new_value': 1118}, {'field': 'onlineAmount', 'old_value': 54983.38, 'new_value': 56692.18}, {'field': 'onlineCount', 'old_value': 2015, 'new_value': 2083}]
2025-05-28 08:10:25,521 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-28 08:10:25,521 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 100774.41, 'new_value': 102397.87}, {'field': 'dailyBillAmount', 'old_value': 100774.41, 'new_value': 102397.87}, {'field': 'amount', 'old_value': 103754.69, 'new_value': 105617.42}, {'field': 'count', 'old_value': 3653, 'new_value': 3728}, {'field': 'instoreAmount', 'old_value': 103754.69, 'new_value': 105490.02}, {'field': 'instoreCount', 'old_value': 3653, 'new_value': 3722}, {'field': 'onlineAmount', 'old_value': 0.0, 'new_value': 127.4}, {'field': 'onlineCount', 'old_value': 0, 'new_value': 6}]
2025-05-28 08:10:25,959 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-28 08:10:25,959 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 319724.0, 'new_value': 335144.0}, {'field': 'dailyBillAmount', 'old_value': 319724.0, 'new_value': 335144.0}, {'field': 'amount', 'old_value': 355977.0, 'new_value': 364250.0}, {'field': 'count', 'old_value': 285, 'new_value': 291}, {'field': 'instoreAmount', 'old_value': 386156.0, 'new_value': 397285.0}, {'field': 'instoreCount', 'old_value': 285, 'new_value': 291}]
2025-05-28 08:10:26,381 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-28 08:10:26,381 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 238704.85, 'new_value': 240782.75}, {'field': 'dailyBillAmount', 'old_value': 238704.85, 'new_value': 240782.75}, {'field': 'amount', 'old_value': 243049.21, 'new_value': 245127.11}, {'field': 'count', 'old_value': 476, 'new_value': 482}, {'field': 'instoreAmount', 'old_value': 246518.71, 'new_value': 248596.61}, {'field': 'instoreCount', 'old_value': 476, 'new_value': 482}]
2025-05-28 08:10:26,865 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-28 08:10:26,881 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 79161.0, 'new_value': 81825.0}, {'field': 'dailyBillAmount', 'old_value': 79161.0, 'new_value': 81825.0}, {'field': 'amount', 'old_value': 39413.0, 'new_value': 42495.0}, {'field': 'count', 'old_value': 108, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 40875.0, 'new_value': 43957.0}, {'field': 'instoreCount', 'old_value': 108, 'new_value': 114}]
2025-05-28 08:10:27,303 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-28 08:10:27,303 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 71557.0, 'new_value': 78180.0}, {'field': 'dailyBillAmount', 'old_value': 51403.0, 'new_value': 61308.0}]
2025-05-28 08:10:28,006 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-28 08:10:28,006 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 69643.1, 'new_value': 71986.1}, {'field': 'amount', 'old_value': 69640.9, 'new_value': 71983.9}, {'field': 'count', 'old_value': 188, 'new_value': 193}, {'field': 'instoreAmount', 'old_value': 70131.8, 'new_value': 72474.8}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 193}]
2025-05-28 08:10:28,474 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-28 08:10:28,474 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 621285.0, 'new_value': 648782.0}, {'field': 'dailyBillAmount', 'old_value': 621285.0, 'new_value': 648782.0}, {'field': 'amount', 'old_value': 655316.0, 'new_value': 682813.0}, {'field': 'count', 'old_value': 81, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 655316.0, 'new_value': 682813.0}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 84}]
2025-05-28 08:10:28,943 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM671
2025-05-28 08:10:28,943 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90855.0, 'new_value': 97772.0}, {'field': 'amount', 'old_value': 90855.0, 'new_value': 97772.0}, {'field': 'count', 'old_value': 25, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 90855.0, 'new_value': 97772.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 27}]
2025-05-28 08:10:29,427 - INFO - 更新表单数据成功: FINST-KLF66WC1ZU6VH5NR8PRRSCENITCO29B1M6DAM6G
2025-05-28 08:10:29,427 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27805.0, 'new_value': 28124.0}, {'field': 'amount', 'old_value': 27805.0, 'new_value': 28124.0}, {'field': 'count', 'old_value': 41, 'new_value': 42}, {'field': 'instoreAmount', 'old_value': 27805.0, 'new_value': 28124.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 42}]
2025-05-28 08:10:29,865 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-28 08:10:29,865 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68368.0, 'new_value': 72055.0}, {'field': 'amount', 'old_value': 68368.0, 'new_value': 72055.0}, {'field': 'count', 'old_value': 78, 'new_value': 82}, {'field': 'instoreAmount', 'old_value': 68368.0, 'new_value': 72055.0}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 82}]
2025-05-28 08:10:30,381 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-28 08:10:30,381 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 265782.6, 'new_value': 274774.0}, {'field': 'dailyBillAmount', 'old_value': 265782.6, 'new_value': 274774.0}, {'field': 'amount', 'old_value': 353851.7, 'new_value': 362843.1}, {'field': 'count', 'old_value': 442, 'new_value': 452}, {'field': 'instoreAmount', 'old_value': 368004.56, 'new_value': 376995.96}, {'field': 'instoreCount', 'old_value': 442, 'new_value': 452}]
2025-05-28 08:10:30,834 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-28 08:10:30,834 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 53599.4, 'new_value': 54908.9}, {'field': 'count', 'old_value': 532, 'new_value': 558}, {'field': 'instoreAmount', 'old_value': 52293.48, 'new_value': 53569.88}, {'field': 'instoreCount', 'old_value': 470, 'new_value': 495}, {'field': 'onlineAmount', 'old_value': 3868.02, 'new_value': 3901.12}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 63}]
2025-05-28 08:10:31,334 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-28 08:10:31,334 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15127.0, 'new_value': 15813.0}, {'field': 'amount', 'old_value': 15127.0, 'new_value': 15813.0}, {'field': 'count', 'old_value': 39, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 15127.0, 'new_value': 15813.0}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 41}]
2025-05-28 08:10:31,802 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-28 08:10:31,802 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38388.0, 'new_value': 38887.0}, {'field': 'dailyBillAmount', 'old_value': 38388.0, 'new_value': 38887.0}, {'field': 'amount', 'old_value': 43674.0, 'new_value': 44970.0}, {'field': 'count', 'old_value': 136, 'new_value': 140}, {'field': 'instoreAmount', 'old_value': 43674.0, 'new_value': 44970.0}, {'field': 'instoreCount', 'old_value': 136, 'new_value': 140}]
2025-05-28 08:10:32,240 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-28 08:10:32,240 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33938.7, 'new_value': 35207.7}, {'field': 'amount', 'old_value': 33938.7, 'new_value': 35207.7}, {'field': 'count', 'old_value': 204, 'new_value': 211}, {'field': 'instoreAmount', 'old_value': 34276.7, 'new_value': 35545.7}, {'field': 'instoreCount', 'old_value': 204, 'new_value': 211}]
2025-05-28 08:10:32,677 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-28 08:10:32,677 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 8452.0, 'new_value': 8725.0}, {'field': 'dailyBillAmount', 'old_value': 8452.0, 'new_value': 8725.0}, {'field': 'amount', 'old_value': 41831.0, 'new_value': 42377.0}, {'field': 'count', 'old_value': 133, 'new_value': 135}, {'field': 'instoreAmount', 'old_value': 42606.0, 'new_value': 43152.0}, {'field': 'instoreCount', 'old_value': 133, 'new_value': 135}]
2025-05-28 08:10:33,146 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-28 08:10:33,162 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 833568.18, 'new_value': 847574.85}, {'field': 'dailyBillAmount', 'old_value': 833568.18, 'new_value': 847574.85}, {'field': 'amount', 'old_value': 52345.24, 'new_value': 53252.78}, {'field': 'count', 'old_value': 518, 'new_value': 532}, {'field': 'instoreAmount', 'old_value': 41935.37, 'new_value': 42207.07}, {'field': 'instoreCount', 'old_value': 368, 'new_value': 374}, {'field': 'onlineAmount', 'old_value': 11434.98, 'new_value': 12070.82}, {'field': 'onlineCount', 'old_value': 150, 'new_value': 158}]
2025-05-28 08:10:33,709 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-28 08:10:33,709 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81969.5, 'new_value': 84588.5}, {'field': 'amount', 'old_value': 81769.5, 'new_value': 84388.5}, {'field': 'count', 'old_value': 108, 'new_value': 111}, {'field': 'instoreAmount', 'old_value': 84316.0, 'new_value': 86935.0}, {'field': 'instoreCount', 'old_value': 108, 'new_value': 111}]
2025-05-28 08:10:34,131 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-28 08:10:34,131 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 17044.0, 'new_value': 17283.0}, {'field': 'amount', 'old_value': 17044.0, 'new_value': 17283.0}, {'field': 'count', 'old_value': 30, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 17044.0, 'new_value': 17283.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 31}]
2025-05-28 08:10:34,646 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-28 08:10:34,646 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25639.79, 'new_value': 26081.79}, {'field': 'amount', 'old_value': 25639.09, 'new_value': 26081.09}, {'field': 'count', 'old_value': 101, 'new_value': 103}, {'field': 'instoreAmount', 'old_value': 25639.79, 'new_value': 26081.79}, {'field': 'instoreCount', 'old_value': 101, 'new_value': 103}]
2025-05-28 08:10:35,177 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-28 08:10:35,177 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44429.0, 'new_value': 45171.0}, {'field': 'dailyBillAmount', 'old_value': 44429.0, 'new_value': 45171.0}, {'field': 'amount', 'old_value': 44645.0, 'new_value': 45387.0}, {'field': 'count', 'old_value': 110, 'new_value': 112}, {'field': 'instoreAmount', 'old_value': 45891.0, 'new_value': 46633.0}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 112}]
2025-05-28 08:10:35,646 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-28 08:10:35,646 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 357309.67, 'new_value': 362038.58999999997}, {'field': 'dailyBillAmount', 'old_value': 330287.6, 'new_value': 334250.6}, {'field': 'amount', 'old_value': 355329.64, 'new_value': 360058.38}, {'field': 'count', 'old_value': 936, 'new_value': 961}, {'field': 'instoreAmount', 'old_value': 358817.7, 'new_value': 363546.62}, {'field': 'instoreCount', 'old_value': 936, 'new_value': 961}]
2025-05-28 08:10:36,068 - INFO - 更新表单数据成功: FINST-VRA66VA1RMZU72KJ6T3JJ8YBFM4G3QAM6RBAM032
2025-05-28 08:10:36,068 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38283.0, 'new_value': 44223.0}, {'field': 'amount', 'old_value': 38283.0, 'new_value': 44223.0}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 38283.0, 'new_value': 44223.0}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-05-28 08:10:36,506 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-28 08:10:36,506 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 106413.79, 'new_value': 110545.29}, {'field': 'dailyBillAmount', 'old_value': 106413.79, 'new_value': 110545.29}, {'field': 'amount', 'old_value': 109360.31, 'new_value': 113491.81}, {'field': 'count', 'old_value': 664, 'new_value': 695}, {'field': 'instoreAmount', 'old_value': 109360.31, 'new_value': 113491.81}, {'field': 'instoreCount', 'old_value': 664, 'new_value': 695}]
2025-05-28 08:10:36,943 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-28 08:10:36,943 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105362.66, 'new_value': 108524.29}, {'field': 'dailyBillAmount', 'old_value': 105362.66, 'new_value': 108524.29}, {'field': 'amount', 'old_value': 37441.97, 'new_value': 38232.61}, {'field': 'count', 'old_value': 3674, 'new_value': 3762}, {'field': 'instoreAmount', 'old_value': 39817.85, 'new_value': 40615.51}, {'field': 'instoreCount', 'old_value': 3674, 'new_value': 3762}]
2025-05-28 08:10:37,412 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-28 08:10:37,412 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 637093.76, 'new_value': 649807.4299999999}, {'field': 'dailyBillAmount', 'old_value': 637093.76, 'new_value': 649807.4299999999}, {'field': 'amount', 'old_value': 652303.9400000001, 'new_value': 664021.96}, {'field': 'count', 'old_value': 6484, 'new_value': 6683}, {'field': 'instoreAmount', 'old_value': 496577.27, 'new_value': 504830.78}, {'field': 'instoreCount', 'old_value': 2498, 'new_value': 2555}, {'field': 'onlineAmount', 'old_value': 161279.29, 'new_value': 164749.47}, {'field': 'onlineCount', 'old_value': 3986, 'new_value': 4128}]
2025-05-28 08:10:37,787 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-28 08:10:37,787 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 202245.98, 'new_value': 203760.88}, {'field': 'amount', 'old_value': 202245.98, 'new_value': 203760.88}, {'field': 'count', 'old_value': 1361, 'new_value': 1374}, {'field': 'instoreAmount', 'old_value': 202680.98, 'new_value': 204195.88}, {'field': 'instoreCount', 'old_value': 1361, 'new_value': 1374}]
2025-05-28 08:10:38,209 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-28 08:10:38,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 95638.82, 'new_value': 97856.67}, {'field': 'dailyBillAmount', 'old_value': 95638.82, 'new_value': 97856.67}, {'field': 'amount', 'old_value': 118093.28, 'new_value': 121625.76}, {'field': 'count', 'old_value': 5551, 'new_value': 5727}, {'field': 'instoreAmount', 'old_value': 60148.18, 'new_value': 61435.61}, {'field': 'instoreCount', 'old_value': 3176, 'new_value': 3262}, {'field': 'onlineAmount', 'old_value': 59223.91, 'new_value': 61469.74}, {'field': 'onlineCount', 'old_value': 2375, 'new_value': 2465}]
2025-05-28 08:10:38,631 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-28 08:10:38,631 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66292.0, 'new_value': 80688.0}, {'field': 'amount', 'old_value': 66292.0, 'new_value': 80688.0}, {'field': 'count', 'old_value': 35, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 66292.0, 'new_value': 80688.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 38}]
2025-05-28 08:10:39,131 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-28 08:10:39,131 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 124025.58, 'new_value': 125762.72}, {'field': 'dailyBillAmount', 'old_value': 124025.58, 'new_value': 125762.72}, {'field': 'amount', 'old_value': 59871.99, 'new_value': 60628.12}, {'field': 'count', 'old_value': 4275, 'new_value': 4335}, {'field': 'instoreAmount', 'old_value': 8222.14, 'new_value': 8310.74}, {'field': 'instoreCount', 'old_value': 349, 'new_value': 358}, {'field': 'onlineAmount', 'old_value': 51649.85, 'new_value': 52317.38}, {'field': 'onlineCount', 'old_value': 3926, 'new_value': 3977}]
2025-05-28 08:10:39,584 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-28 08:10:39,584 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 363036.83, 'new_value': 370557.94}, {'field': 'dailyBillAmount', 'old_value': 363036.83, 'new_value': 370557.94}, {'field': 'amount', 'old_value': 340917.67, 'new_value': 347754.06}, {'field': 'count', 'old_value': 3010, 'new_value': 3086}, {'field': 'instoreAmount', 'old_value': 248833.18, 'new_value': 253475.3}, {'field': 'instoreCount', 'old_value': 1285, 'new_value': 1317}, {'field': 'onlineAmount', 'old_value': 92086.62, 'new_value': 94281.38}, {'field': 'onlineCount', 'old_value': 1725, 'new_value': 1769}]
2025-05-28 08:10:40,099 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-28 08:10:40,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 404207.87, 'new_value': 414955.02999999997}, {'field': 'dailyBillAmount', 'old_value': 404207.87, 'new_value': 414955.02999999997}, {'field': 'amount', 'old_value': 414259.26, 'new_value': 423147.87}, {'field': 'count', 'old_value': 2519, 'new_value': 2574}, {'field': 'instoreAmount', 'old_value': 377218.56, 'new_value': 386107.17}, {'field': 'instoreCount', 'old_value': 2124, 'new_value': 2179}]
2025-05-28 08:10:40,568 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-28 08:10:40,568 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1015996.22, 'new_value': 1042059.04}, {'field': 'dailyBillAmount', 'old_value': 1015996.22, 'new_value': 1042059.04}, {'field': 'amount', 'old_value': 1126472.06, 'new_value': 1155612.34}, {'field': 'count', 'old_value': 6304, 'new_value': 6503}, {'field': 'instoreAmount', 'old_value': 848604.72, 'new_value': 870963.75}, {'field': 'instoreCount', 'old_value': 3429, 'new_value': 3526}, {'field': 'onlineAmount', 'old_value': 286747.22, 'new_value': 293902.12}, {'field': 'onlineCount', 'old_value': 2875, 'new_value': 2977}]
2025-05-28 08:10:41,131 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-28 08:10:41,131 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 335809.75, 'new_value': 339244.28}, {'field': 'dailyBillAmount', 'old_value': 335809.75, 'new_value': 339244.28}, {'field': 'amount', 'old_value': 477915.11, 'new_value': 483319.41}, {'field': 'count', 'old_value': 2262, 'new_value': 2294}, {'field': 'instoreAmount', 'old_value': 448431.26, 'new_value': 452972.26}, {'field': 'instoreCount', 'old_value': 1807, 'new_value': 1822}, {'field': 'onlineAmount', 'old_value': 30244.05, 'new_value': 31107.35}, {'field': 'onlineCount', 'old_value': 455, 'new_value': 472}]
2025-05-28 08:10:41,599 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-28 08:10:41,599 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 393265.84, 'new_value': 400330.72000000003}, {'field': 'dailyBillAmount', 'old_value': 393265.84, 'new_value': 400330.72000000003}, {'field': 'amount', 'old_value': 369532.1, 'new_value': 375882.4}, {'field': 'count', 'old_value': 1671, 'new_value': 1703}, {'field': 'instoreAmount', 'old_value': 376203.7, 'new_value': 382570.0}, {'field': 'instoreCount', 'old_value': 1671, 'new_value': 1703}]
2025-05-28 08:10:42,099 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-28 08:10:42,099 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 815126.85, 'new_value': 839454.67}, {'field': 'amount', 'old_value': 815125.75, 'new_value': 839453.57}, {'field': 'count', 'old_value': 6519, 'new_value': 6769}, {'field': 'instoreAmount', 'old_value': 815126.85, 'new_value': 839454.67}, {'field': 'instoreCount', 'old_value': 6519, 'new_value': 6769}]
2025-05-28 08:10:42,537 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-28 08:10:42,537 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 673436.72, 'new_value': 693727.62}, {'field': 'dailyBillAmount', 'old_value': 673436.72, 'new_value': 693727.62}, {'field': 'amount', 'old_value': 830409.31, 'new_value': 853537.21}, {'field': 'count', 'old_value': 5800, 'new_value': 5960}, {'field': 'instoreAmount', 'old_value': 457467.6, 'new_value': 467651.3}, {'field': 'instoreCount', 'old_value': 2411, 'new_value': 2467}, {'field': 'onlineAmount', 'old_value': 383515.7, 'new_value': 396716.2}, {'field': 'onlineCount', 'old_value': 3389, 'new_value': 3493}]
2025-05-28 08:10:42,974 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-28 08:10:42,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 430387.01, 'new_value': 439400.7}, {'field': 'dailyBillAmount', 'old_value': 430387.01, 'new_value': 439400.7}, {'field': 'amount', 'old_value': 499692.94, 'new_value': 508092.25}, {'field': 'count', 'old_value': 5494, 'new_value': 5623}, {'field': 'instoreAmount', 'old_value': 343098.82, 'new_value': 346859.52}, {'field': 'instoreCount', 'old_value': 2357, 'new_value': 2393}, {'field': 'onlineAmount', 'old_value': 158635.96, 'new_value': 163294.77}, {'field': 'onlineCount', 'old_value': 3137, 'new_value': 3230}]
2025-05-28 08:10:43,427 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-28 08:10:43,427 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 532633.16, 'new_value': 547846.1}, {'field': 'dailyBillAmount', 'old_value': 532633.16, 'new_value': 547846.1}, {'field': 'amount', 'old_value': 539757.37, 'new_value': 555036.85}, {'field': 'count', 'old_value': 5121, 'new_value': 5283}, {'field': 'instoreAmount', 'old_value': 471126.79, 'new_value': 483944.22}, {'field': 'instoreCount', 'old_value': 2732, 'new_value': 2812}, {'field': 'onlineAmount', 'old_value': 69750.95, 'new_value': 72213.0}, {'field': 'onlineCount', 'old_value': 2389, 'new_value': 2471}]
2025-05-28 08:10:43,912 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-28 08:10:43,912 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126422.8, 'new_value': 127910.8}, {'field': 'amount', 'old_value': 126422.3, 'new_value': 127910.3}, {'field': 'count', 'old_value': 597, 'new_value': 606}, {'field': 'instoreAmount', 'old_value': 126422.8, 'new_value': 127910.8}, {'field': 'instoreCount', 'old_value': 597, 'new_value': 606}]
2025-05-28 08:10:44,396 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-28 08:10:44,396 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 366312.68, 'new_value': 369257.08}, {'field': 'dailyBillAmount', 'old_value': 366312.68, 'new_value': 369257.08}, {'field': 'amount', 'old_value': -287528.18, 'new_value': -290577.38}, {'field': 'count', 'old_value': 996, 'new_value': 1010}, {'field': 'instoreAmount', 'old_value': 7178.1, 'new_value': 7342.1}, {'field': 'instoreCount', 'old_value': 329, 'new_value': 335}, {'field': 'onlineAmount', 'old_value': 20607.37, 'new_value': 20804.17}, {'field': 'onlineCount', 'old_value': 667, 'new_value': 675}]
2025-05-28 08:10:44,834 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-28 08:10:44,834 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 628109.92, 'new_value': 644442.02}, {'field': 'dailyBillAmount', 'old_value': 628109.92, 'new_value': 644442.02}, {'field': 'amount', 'old_value': 468814.51, 'new_value': 477054.23}, {'field': 'count', 'old_value': 1981, 'new_value': 2014}, {'field': 'instoreAmount', 'old_value': 468814.51, 'new_value': 477054.23}, {'field': 'instoreCount', 'old_value': 1981, 'new_value': 2014}]
2025-05-28 08:10:45,302 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-28 08:10:45,302 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 412055.37, 'new_value': 422999.95}, {'field': 'dailyBillAmount', 'old_value': 412055.37, 'new_value': 422999.95}, {'field': 'amount', 'old_value': 172179.7, 'new_value': 175333.2}, {'field': 'count', 'old_value': 711, 'new_value': 724}, {'field': 'instoreAmount', 'old_value': 178147.1, 'new_value': 181196.6}, {'field': 'instoreCount', 'old_value': 687, 'new_value': 699}, {'field': 'onlineAmount', 'old_value': 1647.8, 'new_value': 1751.8}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 25}]
2025-05-28 08:10:45,709 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-28 08:10:45,709 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 321249.81, 'new_value': 324294.52}, {'field': 'dailyBillAmount', 'old_value': 321249.81, 'new_value': 324294.52}, {'field': 'amount', 'old_value': 311496.69, 'new_value': 314383.11}, {'field': 'count', 'old_value': 2073, 'new_value': 2121}, {'field': 'instoreAmount', 'old_value': 292789.41000000003, 'new_value': 294898.8}, {'field': 'instoreCount', 'old_value': 1570, 'new_value': 1589}, {'field': 'onlineAmount', 'old_value': 18871.43, 'new_value': 19648.46}, {'field': 'onlineCount', 'old_value': 503, 'new_value': 532}]
2025-05-28 08:10:46,209 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-28 08:10:46,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 343600.48, 'new_value': 350768.98}, {'field': 'dailyBillAmount', 'old_value': 343600.48, 'new_value': 350768.98}, {'field': 'amount', 'old_value': 145079.61000000002, 'new_value': 148595.21}, {'field': 'count', 'old_value': 2482, 'new_value': 2554}, {'field': 'instoreAmount', 'old_value': 83879.12, 'new_value': 85454.01}, {'field': 'instoreCount', 'old_value': 656, 'new_value': 674}, {'field': 'onlineAmount', 'old_value': 61203.8, 'new_value': 63144.51}, {'field': 'onlineCount', 'old_value': 1826, 'new_value': 1880}]
2025-05-28 08:10:46,631 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-28 08:10:46,631 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 143802.98, 'new_value': 148736.88}, {'field': 'amount', 'old_value': 143791.36, 'new_value': 148724.27}, {'field': 'count', 'old_value': 6662, 'new_value': 6892}, {'field': 'instoreAmount', 'old_value': 51511.39, 'new_value': 52456.77}, {'field': 'instoreCount', 'old_value': 2043, 'new_value': 2079}, {'field': 'onlineAmount', 'old_value': 99859.44, 'new_value': 103847.96}, {'field': 'onlineCount', 'old_value': 4619, 'new_value': 4813}]
2025-05-28 08:10:47,084 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-28 08:10:47,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44649.9, 'new_value': 45736.9}, {'field': 'amount', 'old_value': 44649.9, 'new_value': 45736.9}, {'field': 'count', 'old_value': 203, 'new_value': 207}, {'field': 'instoreAmount', 'old_value': 44649.9, 'new_value': 45736.9}, {'field': 'instoreCount', 'old_value': 203, 'new_value': 207}]
2025-05-28 08:10:47,521 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-28 08:10:47,521 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 413063.64, 'new_value': 422686.94}, {'field': 'dailyBillAmount', 'old_value': 413063.64, 'new_value': 422686.94}, {'field': 'amount', 'old_value': 167144.2, 'new_value': 169675.1}, {'field': 'count', 'old_value': 3140, 'new_value': 3192}, {'field': 'instoreAmount', 'old_value': 168440.1, 'new_value': 170971.0}, {'field': 'instoreCount', 'old_value': 3140, 'new_value': 3192}]
2025-05-28 08:10:48,084 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-28 08:10:48,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 164348.06, 'new_value': 166652.9}, {'field': 'amount', 'old_value': 164346.86000000002, 'new_value': 166651.7}, {'field': 'count', 'old_value': 3927, 'new_value': 3986}, {'field': 'instoreAmount', 'old_value': 164605.54, 'new_value': 166910.38}, {'field': 'instoreCount', 'old_value': 3927, 'new_value': 3986}]
2025-05-28 08:10:48,552 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-28 08:10:48,552 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32268.34, 'new_value': 33469.02}, {'field': 'amount', 'old_value': 32262.69, 'new_value': 33463.07}, {'field': 'count', 'old_value': 1997, 'new_value': 2082}, {'field': 'instoreAmount', 'old_value': 16476.87, 'new_value': 16739.87}, {'field': 'instoreCount', 'old_value': 822, 'new_value': 838}, {'field': 'onlineAmount', 'old_value': 16363.76, 'new_value': 17322.43}, {'field': 'onlineCount', 'old_value': 1175, 'new_value': 1244}]
2025-05-28 08:10:49,021 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-28 08:10:49,021 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 53880.6, 'new_value': 54933.0}, {'field': 'amount', 'old_value': 53880.6, 'new_value': 54933.0}, {'field': 'count', 'old_value': 139, 'new_value': 141}, {'field': 'instoreAmount', 'old_value': 53880.6, 'new_value': 54933.0}, {'field': 'instoreCount', 'old_value': 139, 'new_value': 141}]
2025-05-28 08:10:49,427 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-28 08:10:49,427 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 197263.43, 'new_value': 203755.71}, {'field': 'dailyBillAmount', 'old_value': 163818.1, 'new_value': 168660.2}, {'field': 'amount', 'old_value': 197262.75, 'new_value': 203755.03}, {'field': 'count', 'old_value': 2790, 'new_value': 2905}, {'field': 'instoreAmount', 'old_value': 188321.3, 'new_value': 194531.3}, {'field': 'instoreCount', 'old_value': 2423, 'new_value': 2527}, {'field': 'onlineAmount', 'old_value': 9194.53, 'new_value': 9476.81}, {'field': 'onlineCount', 'old_value': 367, 'new_value': 378}]
2025-05-28 08:10:49,849 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-28 08:10:49,849 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 30694.16, 'new_value': 31533.100000000002}, {'field': 'amount', 'old_value': 30693.36, 'new_value': 31532.3}, {'field': 'count', 'old_value': 1318, 'new_value': 1350}, {'field': 'instoreAmount', 'old_value': 25583.06, 'new_value': 26270.3}, {'field': 'instoreCount', 'old_value': 1173, 'new_value': 1200}, {'field': 'onlineAmount', 'old_value': 5196.8, 'new_value': 5348.5}, {'field': 'onlineCount', 'old_value': 145, 'new_value': 150}]
2025-05-28 08:10:50,255 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-28 08:10:50,255 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 406345.27, 'new_value': 414905.46}, {'field': 'dailyBillAmount', 'old_value': 406345.27, 'new_value': 414905.46}, {'field': 'amount', 'old_value': 524677.32, 'new_value': 537156.38}, {'field': 'count', 'old_value': 5379, 'new_value': 5542}, {'field': 'instoreAmount', 'old_value': 493769.45, 'new_value': 505204.71}, {'field': 'instoreCount', 'old_value': 3712, 'new_value': 3819}, {'field': 'onlineAmount', 'old_value': 40920.76, 'new_value': 42162.12}, {'field': 'onlineCount', 'old_value': 1667, 'new_value': 1723}]
2025-05-28 08:10:50,709 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-28 08:10:50,709 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'amount', 'old_value': 39545.65, 'new_value': 40271.43}, {'field': 'count', 'old_value': 647, 'new_value': 670}, {'field': 'instoreAmount', 'old_value': 25983.33, 'new_value': 26450.72}, {'field': 'instoreCount', 'old_value': 349, 'new_value': 363}, {'field': 'onlineAmount', 'old_value': 14452.46, 'new_value': 14710.85}, {'field': 'onlineCount', 'old_value': 298, 'new_value': 307}]
2025-05-28 08:10:51,193 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-28 08:10:51,193 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 157009.71, 'new_value': 162289.37}, {'field': 'dailyBillAmount', 'old_value': 142089.43, 'new_value': 147357.13}, {'field': 'amount', 'old_value': 157007.13999999998, 'new_value': 162286.58}, {'field': 'count', 'old_value': 8872, 'new_value': 9183}, {'field': 'instoreAmount', 'old_value': 96345.04, 'new_value': 99139.93}, {'field': 'instoreCount', 'old_value': 5316, 'new_value': 5481}, {'field': 'onlineAmount', 'old_value': 62549.96, 'new_value': 65065.81}, {'field': 'onlineCount', 'old_value': 3556, 'new_value': 3702}]
2025-05-28 08:10:51,646 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-28 08:10:51,646 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82938.1, 'new_value': 84476.93000000001}, {'field': 'amount', 'old_value': 82926.31, 'new_value': 84465.14}, {'field': 'count', 'old_value': 5316, 'new_value': 5436}, {'field': 'instoreAmount', 'old_value': 36906.82, 'new_value': 37433.73}, {'field': 'instoreCount', 'old_value': 2137, 'new_value': 2193}, {'field': 'onlineAmount', 'old_value': 48607.17, 'new_value': 49656.38}, {'field': 'onlineCount', 'old_value': 3179, 'new_value': 3243}]
2025-05-28 08:10:52,052 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-28 08:10:52,068 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 163148.89, 'new_value': 166285.65}, {'field': 'count', 'old_value': 1634, 'new_value': 1687}, {'field': 'instoreAmount', 'old_value': 163440.37, 'new_value': 166577.13}, {'field': 'instoreCount', 'old_value': 1634, 'new_value': 1687}]
2025-05-28 08:10:52,505 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-28 08:10:52,505 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 133311.92, 'new_value': 136961.22}, {'field': 'dailyBillAmount', 'old_value': 138031.27, 'new_value': 141853.35}, {'field': 'amount', 'old_value': 133306.11, 'new_value': 136955.41}, {'field': 'count', 'old_value': 2779, 'new_value': 2879}, {'field': 'instoreAmount', 'old_value': 127319.06, 'new_value': 130615.46}, {'field': 'instoreCount', 'old_value': 2312, 'new_value': 2379}, {'field': 'onlineAmount', 'old_value': 6114.68, 'new_value': 6467.58}, {'field': 'onlineCount', 'old_value': 467, 'new_value': 500}]
2025-05-28 08:10:53,084 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-28 08:10:53,084 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 216938.93, 'new_value': 228515.78}, {'field': 'dailyBillAmount', 'old_value': 216938.93, 'new_value': 228515.78}, {'field': 'amount', 'old_value': 27452.55, 'new_value': 29477.760000000002}, {'field': 'count', 'old_value': 1064, 'new_value': 1129}, {'field': 'instoreAmount', 'old_value': 31268.04, 'new_value': 33358.15}, {'field': 'instoreCount', 'old_value': 1064, 'new_value': 1129}]
2025-05-28 08:10:53,552 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-28 08:10:53,552 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 558440.96, 'new_value': 573423.56}, {'field': 'dailyBillAmount', 'old_value': 558440.96, 'new_value': 573423.56}, {'field': 'amount', 'old_value': 56764.26, 'new_value': 58625.46}, {'field': 'count', 'old_value': 274, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 56990.26, 'new_value': 58852.26}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 284}]
2025-05-28 08:10:54,021 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-28 08:10:54,021 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 17793.71, 'new_value': 18532.38}, {'field': 'count', 'old_value': 914, 'new_value': 954}, {'field': 'onlineAmount', 'old_value': 18015.37, 'new_value': 18754.04}, {'field': 'onlineCount', 'old_value': 914, 'new_value': 954}]
2025-05-28 08:10:54,474 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-28 08:10:54,474 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 328768.22, 'new_value': 341180.92}, {'field': 'amount', 'old_value': 328614.44, 'new_value': 341027.14}, {'field': 'count', 'old_value': 3369, 'new_value': 3519}, {'field': 'instoreAmount', 'old_value': 313048.4, 'new_value': 325033.8}, {'field': 'instoreCount', 'old_value': 2843, 'new_value': 2972}, {'field': 'onlineAmount', 'old_value': 21458.15, 'new_value': 22120.25}, {'field': 'onlineCount', 'old_value': 526, 'new_value': 547}]
2025-05-28 08:10:54,880 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-28 08:10:54,880 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 184189.01, 'new_value': 191392.21}, {'field': 'dailyBillAmount', 'old_value': 180388.54, 'new_value': 187591.74}, {'field': 'amount', 'old_value': 135978.29, 'new_value': 141816.11}, {'field': 'count', 'old_value': 4937, 'new_value': 5138}, {'field': 'instoreAmount', 'old_value': 56710.810000000005, 'new_value': 57599.86}, {'field': 'instoreCount', 'old_value': 1940, 'new_value': 1964}, {'field': 'onlineAmount', 'old_value': 81107.5, 'new_value': 86092.37}, {'field': 'onlineCount', 'old_value': 2997, 'new_value': 3174}]
2025-05-28 08:10:55,302 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-28 08:10:55,302 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 5689.4800000000005, 'new_value': 6232.11}, {'field': 'count', 'old_value': 258, 'new_value': 283}, {'field': 'instoreAmount', 'old_value': 5689.68, 'new_value': 6232.31}, {'field': 'instoreCount', 'old_value': 258, 'new_value': 283}]
2025-05-28 08:10:55,787 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-28 08:10:55,787 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 6525.0, 'new_value': 6800.99}, {'field': 'count', 'old_value': 281, 'new_value': 292}, {'field': 'onlineAmount', 'old_value': 6525.0, 'new_value': 6800.99}, {'field': 'onlineCount', 'old_value': 281, 'new_value': 292}]
2025-05-28 08:10:56,209 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-28 08:10:56,209 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109261.36, 'new_value': 111890.06999999999}, {'field': 'dailyBillAmount', 'old_value': 53839.94, 'new_value': 54789.44}, {'field': 'amount', 'old_value': 109260.77, 'new_value': 111889.48}, {'field': 'count', 'old_value': 2719, 'new_value': 2796}, {'field': 'instoreAmount', 'old_value': 58533.35, 'new_value': 59644.85}, {'field': 'instoreCount', 'old_value': 1433, 'new_value': 1466}, {'field': 'onlineAmount', 'old_value': 53731.14, 'new_value': 55248.35}, {'field': 'onlineCount', 'old_value': 1286, 'new_value': 1330}]
2025-05-28 08:10:56,646 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-28 08:10:56,646 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57343.97, 'new_value': 61778.68}, {'field': 'amount', 'old_value': 57343.97, 'new_value': 61778.68}, {'field': 'count', 'old_value': 2195, 'new_value': 2440}, {'field': 'instoreAmount', 'old_value': 58026.16, 'new_value': 62521.25}, {'field': 'instoreCount', 'old_value': 2195, 'new_value': 2440}]
2025-05-28 08:10:57,052 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-28 08:10:57,052 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52847.15, 'new_value': 54834.0}, {'field': 'dailyBillAmount', 'old_value': 52847.15, 'new_value': 54834.0}, {'field': 'amount', 'old_value': 40334.64, 'new_value': 41644.07}, {'field': 'count', 'old_value': 1906, 'new_value': 2000}, {'field': 'instoreAmount', 'old_value': 21260.81, 'new_value': 21804.71}, {'field': 'instoreCount', 'old_value': 730, 'new_value': 747}, {'field': 'onlineAmount', 'old_value': 19156.05, 'new_value': 19921.58}, {'field': 'onlineCount', 'old_value': 1176, 'new_value': 1253}]
2025-05-28 08:10:57,427 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-28 08:10:57,427 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 85868.25, 'new_value': 88296.89}, {'field': 'amount', 'old_value': 85868.25, 'new_value': 88296.89}, {'field': 'count', 'old_value': 2615, 'new_value': 2690}, {'field': 'instoreAmount', 'old_value': 34851.35, 'new_value': 36241.8}, {'field': 'instoreCount', 'old_value': 1318, 'new_value': 1369}, {'field': 'onlineAmount', 'old_value': 51118.39, 'new_value': 52156.58}, {'field': 'onlineCount', 'old_value': 1297, 'new_value': 1321}]
2025-05-28 08:10:57,880 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-28 08:10:57,880 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 52071.41, 'new_value': 53662.08}, {'field': 'amount', 'old_value': 52070.51, 'new_value': 53661.18}, {'field': 'count', 'old_value': 1241, 'new_value': 1288}, {'field': 'instoreAmount', 'old_value': 40389.22, 'new_value': 41742.92}, {'field': 'instoreCount', 'old_value': 992, 'new_value': 1031}, {'field': 'onlineAmount', 'old_value': 12158.2, 'new_value': 12437.17}, {'field': 'onlineCount', 'old_value': 249, 'new_value': 257}]
2025-05-28 08:10:58,318 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-28 08:10:58,318 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 242746.71, 'new_value': 251597.83000000002}, {'field': 'dailyBillAmount', 'old_value': 242746.71, 'new_value': 251597.83000000002}, {'field': 'amount', 'old_value': 163873.82, 'new_value': 169590.38}, {'field': 'count', 'old_value': 4129, 'new_value': 4279}, {'field': 'instoreAmount', 'old_value': 104108.12, 'new_value': 108297.52}, {'field': 'instoreCount', 'old_value': 2052, 'new_value': 2141}, {'field': 'onlineAmount', 'old_value': 73607.04, 'new_value': 75868.2}, {'field': 'onlineCount', 'old_value': 2077, 'new_value': 2138}]
2025-05-28 08:10:58,787 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-28 08:10:58,787 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 719178.78, 'new_value': 737230.67}, {'field': 'dailyBillAmount', 'old_value': 719178.78, 'new_value': 737230.67}, {'field': 'amount', 'old_value': 705617.7, 'new_value': 718335.6}, {'field': 'count', 'old_value': 4221, 'new_value': 4311}, {'field': 'instoreAmount', 'old_value': 501019.1, 'new_value': 508867.2}, {'field': 'instoreCount', 'old_value': 3262, 'new_value': 3328}, {'field': 'onlineAmount', 'old_value': 204601.5, 'new_value': 209471.3}, {'field': 'onlineCount', 'old_value': 959, 'new_value': 983}]
2025-05-28 08:10:59,302 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-28 08:10:59,302 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1090093.22, 'new_value': 1130357.42}, {'field': 'amount', 'old_value': 1090092.72, 'new_value': 1130356.92}, {'field': 'count', 'old_value': 3847, 'new_value': 3983}, {'field': 'instoreAmount', 'old_value': 1090093.22, 'new_value': 1130357.42}, {'field': 'instoreCount', 'old_value': 3847, 'new_value': 3983}]
2025-05-28 08:10:59,771 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-28 08:10:59,771 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 621624.52, 'new_value': 643092.95}, {'field': 'dailyBillAmount', 'old_value': 550436.38, 'new_value': 569603.8}, {'field': 'amount', 'old_value': 621624.52, 'new_value': 643092.95}, {'field': 'count', 'old_value': 3866, 'new_value': 4023}, {'field': 'instoreAmount', 'old_value': 566542.31, 'new_value': 585701.4}, {'field': 'instoreCount', 'old_value': 2428, 'new_value': 2507}, {'field': 'onlineAmount', 'old_value': 55454.19, 'new_value': 57763.53}, {'field': 'onlineCount', 'old_value': 1438, 'new_value': 1516}]
2025-05-28 08:11:00,193 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-28 08:11:00,193 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 613611.78, 'new_value': 621750.74}, {'field': 'dailyBillAmount', 'old_value': 596484.11, 'new_value': 604325.58}, {'field': 'amount', 'old_value': 613605.19, 'new_value': 621744.15}, {'field': 'count', 'old_value': 1508, 'new_value': 1544}, {'field': 'instoreAmount', 'old_value': 570322.9, 'new_value': 577591.4}, {'field': 'instoreCount', 'old_value': 1171, 'new_value': 1197}, {'field': 'onlineAmount', 'old_value': 43416.16, 'new_value': 44286.62}, {'field': 'onlineCount', 'old_value': 337, 'new_value': 347}]
2025-05-28 08:11:00,537 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-28 08:11:00,537 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 755502.61, 'new_value': 780260.88}, {'field': 'amount', 'old_value': 755501.29, 'new_value': 780259.5599999999}, {'field': 'count', 'old_value': 4044, 'new_value': 4222}, {'field': 'instoreAmount', 'old_value': 708508.46, 'new_value': 730482.46}, {'field': 'instoreCount', 'old_value': 2629, 'new_value': 2713}, {'field': 'onlineAmount', 'old_value': 47138.95, 'new_value': 49923.22}, {'field': 'onlineCount', 'old_value': 1415, 'new_value': 1509}]
2025-05-28 08:11:00,974 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-28 08:11:00,974 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 827626.77, 'new_value': 858011.59}, {'field': 'dailyBillAmount', 'old_value': 827626.77, 'new_value': 858011.59}, {'field': 'amount', 'old_value': 756121.14, 'new_value': 782498.48}, {'field': 'count', 'old_value': 3776, 'new_value': 3920}, {'field': 'instoreAmount', 'old_value': 692567.55, 'new_value': 716887.46}, {'field': 'instoreCount', 'old_value': 3114, 'new_value': 3230}, {'field': 'onlineAmount', 'old_value': 64386.89, 'new_value': 66630.37}, {'field': 'onlineCount', 'old_value': 662, 'new_value': 690}]
2025-05-28 08:11:01,427 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-28 08:11:01,427 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 208976.84, 'new_value': 213305.84}, {'field': 'dailyBillAmount', 'old_value': 207570.29, 'new_value': 211899.29}, {'field': 'amount', 'old_value': 205165.66, 'new_value': 208592.66}, {'field': 'count', 'old_value': 295, 'new_value': 305}, {'field': 'instoreAmount', 'old_value': 205165.66, 'new_value': 208592.66}, {'field': 'instoreCount', 'old_value': 295, 'new_value': 305}]
2025-05-28 08:11:01,849 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-28 08:11:01,849 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 173418.33000000002, 'new_value': 176999.73}, {'field': 'dailyBillAmount', 'old_value': 173418.33000000002, 'new_value': 176999.73}, {'field': 'amount', 'old_value': 153735.95, 'new_value': 156403.95}, {'field': 'count', 'old_value': 252, 'new_value': 259}, {'field': 'instoreAmount', 'old_value': 150793.9, 'new_value': 153461.9}, {'field': 'instoreCount', 'old_value': 232, 'new_value': 239}]
2025-05-28 08:11:02,287 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-28 08:11:02,287 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 22889.82, 'new_value': 23594.14}, {'field': 'amount', 'old_value': 22889.82, 'new_value': 23594.14}, {'field': 'count', 'old_value': 478, 'new_value': 489}, {'field': 'instoreAmount', 'old_value': 22889.82, 'new_value': 23594.14}, {'field': 'instoreCount', 'old_value': 478, 'new_value': 489}]
2025-05-28 08:11:02,709 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-28 08:11:02,709 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 97402.76, 'new_value': 100691.82}, {'field': 'amount', 'old_value': 97402.76, 'new_value': 100691.82}, {'field': 'count', 'old_value': 825, 'new_value': 851}, {'field': 'instoreAmount', 'old_value': 97953.6, 'new_value': 101242.66}, {'field': 'instoreCount', 'old_value': 825, 'new_value': 851}]
2025-05-28 08:11:03,209 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-28 08:11:03,209 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 329841.89, 'new_value': 340560.82}, {'field': 'dailyBillAmount', 'old_value': 329841.89, 'new_value': 340560.82}, {'field': 'amount', 'old_value': 349041.57, 'new_value': 360633.28}, {'field': 'count', 'old_value': 9589, 'new_value': 9911}, {'field': 'instoreAmount', 'old_value': 329717.09, 'new_value': 340080.88}, {'field': 'instoreCount', 'old_value': 8610, 'new_value': 8875}, {'field': 'onlineAmount', 'old_value': 24540.25, 'new_value': 25874.63}, {'field': 'onlineCount', 'old_value': 979, 'new_value': 1036}]
2025-05-28 08:11:03,599 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-28 08:11:03,599 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 847619.89, 'new_value': 861844.71}, {'field': 'dailyBillAmount', 'old_value': 847619.89, 'new_value': 861844.71}, {'field': 'amount', 'old_value': 752664.85, 'new_value': 767342.98}, {'field': 'count', 'old_value': 1969, 'new_value': 2043}, {'field': 'instoreAmount', 'old_value': 793111.15, 'new_value': 807335.97}, {'field': 'instoreCount', 'old_value': 1635, 'new_value': 1689}, {'field': 'onlineAmount', 'old_value': 7751.91, 'new_value': 8205.22}, {'field': 'onlineCount', 'old_value': 334, 'new_value': 354}]
2025-05-28 08:11:04,162 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-28 08:11:04,162 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1292980.8, 'new_value': 1334807.4}, {'field': 'amount', 'old_value': 1292980.8, 'new_value': 1334807.2}, {'field': 'count', 'old_value': 4151, 'new_value': 4281}, {'field': 'instoreAmount', 'old_value': 1294191.8, 'new_value': 1336018.4}, {'field': 'instoreCount', 'old_value': 4151, 'new_value': 4281}]
2025-05-28 08:11:04,615 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-28 08:11:04,615 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1012081.4199999999, 'new_value': 1058549.71}, {'field': 'dailyBillAmount', 'old_value': 1012081.4199999999, 'new_value': 1058549.71}, {'field': 'amount', 'old_value': 805109.59, 'new_value': 838568.39}, {'field': 'count', 'old_value': 2866, 'new_value': 2978}, {'field': 'instoreAmount', 'old_value': 785630.91, 'new_value': 818836.6}, {'field': 'instoreCount', 'old_value': 1769, 'new_value': 1847}, {'field': 'onlineAmount', 'old_value': 33660.32, 'new_value': 34698.62}, {'field': 'onlineCount', 'old_value': 1097, 'new_value': 1131}]
2025-05-28 08:11:05,084 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-28 08:11:05,084 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1878971.07, 'new_value': 1958877.56}, {'field': 'dailyBillAmount', 'old_value': 1878971.07, 'new_value': 1958877.56}, {'field': 'amount', 'old_value': 1924740.0, 'new_value': 1992894.0}, {'field': 'count', 'old_value': 5020, 'new_value': 5162}, {'field': 'instoreAmount', 'old_value': 1926366.0, 'new_value': 1999676.0}, {'field': 'instoreCount', 'old_value': 5020, 'new_value': 5162}]
2025-05-28 08:11:05,490 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-28 08:11:05,490 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 297095.13, 'new_value': 311307.83}, {'field': 'dailyBillAmount', 'old_value': 297095.13, 'new_value': 311307.83}, {'field': 'amount', 'old_value': 307315.45, 'new_value': 315568.02}, {'field': 'count', 'old_value': 1650, 'new_value': 1708}, {'field': 'instoreAmount', 'old_value': 298134.7, 'new_value': 305850.1}, {'field': 'instoreCount', 'old_value': 1392, 'new_value': 1441}, {'field': 'onlineAmount', 'old_value': 16012.03, 'new_value': 16603.2}, {'field': 'onlineCount', 'old_value': 258, 'new_value': 267}]
2025-05-28 08:11:06,005 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-28 08:11:06,005 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1013964.0700000001, 'new_value': 1047955.2}, {'field': 'dailyBillAmount', 'old_value': 1013964.0700000001, 'new_value': 1047955.2}, {'field': 'amount', 'old_value': 1072141.1400000001, 'new_value': 1106132.27}, {'field': 'count', 'old_value': 4499, 'new_value': 4660}, {'field': 'instoreAmount', 'old_value': 1072141.79, 'new_value': 1106132.92}, {'field': 'instoreCount', 'old_value': 4499, 'new_value': 4660}]
2025-05-28 08:11:06,490 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-28 08:11:06,490 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 449065.16000000003, 'new_value': 462589.16000000003}, {'field': 'dailyBillAmount', 'old_value': 449065.16000000003, 'new_value': 462589.16000000003}, {'field': 'amount', 'old_value': 721541.72, 'new_value': 751692.2999999999}, {'field': 'count', 'old_value': 1220, 'new_value': 1268}, {'field': 'instoreAmount', 'old_value': 716225.8, 'new_value': 746376.8}, {'field': 'instoreCount', 'old_value': 1182, 'new_value': 1230}]
2025-05-28 08:11:06,912 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-28 08:11:06,912 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 264536.5, 'new_value': 270481.57}, {'field': 'dailyBillAmount', 'old_value': 264536.5, 'new_value': 270481.57}, {'field': 'amount', 'old_value': 302392.3, 'new_value': 306446.3}, {'field': 'count', 'old_value': 2116, 'new_value': 2143}, {'field': 'instoreAmount', 'old_value': 306427.3, 'new_value': 310481.3}, {'field': 'instoreCount', 'old_value': 2116, 'new_value': 2143}]
2025-05-28 08:11:07,302 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-28 08:11:07,302 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 178506.75, 'new_value': 187567.68}, {'field': 'dailyBillAmount', 'old_value': 178506.75, 'new_value': 187567.68}, {'field': 'amount', 'old_value': 146067.37, 'new_value': 153711.77}, {'field': 'count', 'old_value': 984, 'new_value': 1026}, {'field': 'instoreAmount', 'old_value': 146384.0, 'new_value': 153993.0}, {'field': 'instoreCount', 'old_value': 928, 'new_value': 969}, {'field': 'onlineAmount', 'old_value': 2927.37, 'new_value': 2962.77}, {'field': 'onlineCount', 'old_value': 56, 'new_value': 57}]
2025-05-28 08:11:07,802 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-28 08:11:07,802 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55070.5, 'new_value': 56711.5}, {'field': 'dailyBillAmount', 'old_value': 55070.5, 'new_value': 56711.5}]
2025-05-28 08:11:08,255 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-28 08:11:08,255 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 158461.95, 'new_value': 161716.19}, {'field': 'count', 'old_value': 7634, 'new_value': 7796}, {'field': 'instoreAmount', 'old_value': 83820.86, 'new_value': 84755.41}, {'field': 'instoreCount', 'old_value': 4234, 'new_value': 4275}, {'field': 'onlineAmount', 'old_value': 78932.96, 'new_value': 81456.16}, {'field': 'onlineCount', 'old_value': 3400, 'new_value': 3521}]
2025-05-28 08:11:08,724 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-28 08:11:08,724 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228354.77, 'new_value': 236017.5}, {'field': 'amount', 'old_value': 228344.21, 'new_value': 236006.49}, {'field': 'count', 'old_value': 4354, 'new_value': 4501}, {'field': 'instoreAmount', 'old_value': 207552.59, 'new_value': 213721.02}, {'field': 'instoreCount', 'old_value': 3932, 'new_value': 4051}, {'field': 'onlineAmount', 'old_value': 20802.18, 'new_value': 22296.48}, {'field': 'onlineCount', 'old_value': 422, 'new_value': 450}]
2025-05-28 08:11:09,146 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-28 08:11:09,162 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 34141.9, 'new_value': 36126.3}, {'field': 'dailyBillAmount', 'old_value': 3126.4, 'new_value': 3186.4}, {'field': 'amount', 'old_value': 34141.9, 'new_value': 36126.3}, {'field': 'count', 'old_value': 231, 'new_value': 242}, {'field': 'instoreAmount', 'old_value': 34141.9, 'new_value': 36126.3}, {'field': 'instoreCount', 'old_value': 231, 'new_value': 242}]
2025-05-28 08:11:09,584 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-28 08:11:09,584 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 50733.700000000004, 'new_value': 51546.200000000004}, {'field': 'count', 'old_value': 459, 'new_value': 467}, {'field': 'instoreAmount', 'old_value': 51081.1, 'new_value': 51893.6}, {'field': 'instoreCount', 'old_value': 459, 'new_value': 467}]
2025-05-28 08:11:10,021 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-28 08:11:10,021 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54431.0, 'new_value': 55877.0}, {'field': 'dailyBillAmount', 'old_value': 54431.0, 'new_value': 55877.0}]
2025-05-28 08:11:10,458 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-28 08:11:10,458 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 254546.0, 'new_value': 281697.0}, {'field': 'dailyBillAmount', 'old_value': 254546.0, 'new_value': 281697.0}, {'field': 'amount', 'old_value': 192273.33, 'new_value': 208697.53}, {'field': 'count', 'old_value': 5370, 'new_value': 5551}, {'field': 'instoreAmount', 'old_value': 188068.36, 'new_value': 203903.66}, {'field': 'instoreCount', 'old_value': 5184, 'new_value': 5357}, {'field': 'onlineAmount', 'old_value': 7334.68, 'new_value': 7970.48}, {'field': 'onlineCount', 'old_value': 186, 'new_value': 194}]
2025-05-28 08:11:10,896 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-28 08:11:10,896 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54502.2, 'new_value': 55345.3}, {'field': 'dailyBillAmount', 'old_value': 54502.2, 'new_value': 55345.3}, {'field': 'amount', 'old_value': 54525.7, 'new_value': 55378.8}, {'field': 'count', 'old_value': 324, 'new_value': 329}, {'field': 'instoreAmount', 'old_value': 57245.6, 'new_value': 58098.7}, {'field': 'instoreCount', 'old_value': 320, 'new_value': 325}]
2025-05-28 08:11:11,380 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-28 08:11:11,380 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76069.41, 'new_value': 78887.04}, {'field': 'dailyBillAmount', 'old_value': 76069.41, 'new_value': 78887.04}]
2025-05-28 08:11:11,833 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-28 08:11:11,833 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 51320.33, 'new_value': 53378.41}, {'field': 'amount', 'old_value': 51319.43, 'new_value': 53377.51}, {'field': 'count', 'old_value': 2988, 'new_value': 3125}, {'field': 'instoreAmount', 'old_value': 52186.69, 'new_value': 54260.020000000004}, {'field': 'instoreCount', 'old_value': 2988, 'new_value': 3125}]
2025-05-28 08:11:12,333 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-28 08:11:12,333 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80322.66, 'new_value': 82835.86}, {'field': 'dailyBillAmount', 'old_value': 80322.66, 'new_value': 82835.86}, {'field': 'amount', 'old_value': 82635.08, 'new_value': 85205.81999999999}, {'field': 'count', 'old_value': 4077, 'new_value': 4214}, {'field': 'instoreAmount', 'old_value': 76733.3, 'new_value': 79152.8}, {'field': 'instoreCount', 'old_value': 3815, 'new_value': 3946}, {'field': 'onlineAmount', 'old_value': 5983.83, 'new_value': 6156.87}, {'field': 'onlineCount', 'old_value': 262, 'new_value': 268}]
2025-05-28 08:11:12,818 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-28 08:11:12,818 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55675.74, 'new_value': 57610.6}, {'field': 'amount', 'old_value': 55675.74, 'new_value': 57610.6}, {'field': 'count', 'old_value': 2727, 'new_value': 2819}, {'field': 'instoreAmount', 'old_value': 34393.63, 'new_value': 35557.29}, {'field': 'instoreCount', 'old_value': 1787, 'new_value': 1847}, {'field': 'onlineAmount', 'old_value': 21406.26, 'new_value': 22177.46}, {'field': 'onlineCount', 'old_value': 940, 'new_value': 972}]
2025-05-28 08:11:13,255 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-28 08:11:13,255 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39882.33, 'new_value': 41047.2}, {'field': 'dailyBillAmount', 'old_value': 39882.33, 'new_value': 41047.2}, {'field': 'amount', 'old_value': 27795.87, 'new_value': 28640.37}, {'field': 'count', 'old_value': 1117, 'new_value': 1154}, {'field': 'instoreAmount', 'old_value': 28081.87, 'new_value': 28926.37}, {'field': 'instoreCount', 'old_value': 1117, 'new_value': 1154}]
2025-05-28 08:11:13,724 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-28 08:11:13,724 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72344.8, 'new_value': 74235.33}, {'field': 'amount', 'old_value': 72336.37, 'new_value': 74226.14}, {'field': 'count', 'old_value': 4292, 'new_value': 4402}, {'field': 'instoreAmount', 'old_value': 18766.86, 'new_value': 19205.79}, {'field': 'instoreCount', 'old_value': 1074, 'new_value': 1094}, {'field': 'onlineAmount', 'old_value': 55612.97, 'new_value': 57119.27}, {'field': 'onlineCount', 'old_value': 3218, 'new_value': 3308}]
2025-05-28 08:11:14,224 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-28 08:11:14,224 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137827.51, 'new_value': 143283.0}, {'field': 'dailyBillAmount', 'old_value': 137827.51, 'new_value': 143283.0}, {'field': 'amount', 'old_value': 114681.27, 'new_value': 119155.37}, {'field': 'count', 'old_value': 1113, 'new_value': 1159}, {'field': 'instoreAmount', 'old_value': 114681.27, 'new_value': 119155.37}, {'field': 'instoreCount', 'old_value': 1113, 'new_value': 1159}]
2025-05-28 08:11:14,693 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-28 08:11:14,693 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 109556.15, 'new_value': 111707.15}, {'field': 'dailyBillAmount', 'old_value': 109556.15, 'new_value': 111707.15}, {'field': 'amount', 'old_value': 126289.8, 'new_value': 129075.8}, {'field': 'count', 'old_value': 536, 'new_value': 548}, {'field': 'instoreAmount', 'old_value': 126289.8, 'new_value': 129075.8}, {'field': 'instoreCount', 'old_value': 536, 'new_value': 548}]
2025-05-28 08:11:15,162 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-28 08:11:15,162 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 71309.6, 'new_value': 72276.6}, {'field': 'dailyBillAmount', 'old_value': 71309.6, 'new_value': 72276.6}, {'field': 'amount', 'old_value': 57715.49, 'new_value': 58022.49}, {'field': 'count', 'old_value': 312, 'new_value': 314}, {'field': 'instoreAmount', 'old_value': 59152.49, 'new_value': 59459.49}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 314}]
2025-05-28 08:11:15,630 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-28 08:11:15,630 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 135974.0, 'new_value': 139986.0}, {'field': 'amount', 'old_value': 135974.0, 'new_value': 139986.0}, {'field': 'count', 'old_value': 1386, 'new_value': 1432}, {'field': 'instoreAmount', 'old_value': 135974.0, 'new_value': 139986.0}, {'field': 'instoreCount', 'old_value': 1386, 'new_value': 1432}]
2025-05-28 08:11:16,099 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-28 08:11:16,099 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 33988.13, 'new_value': 35713.99}, {'field': 'dailyBillAmount', 'old_value': 33988.13, 'new_value': 35713.99}, {'field': 'amount', 'old_value': 4468.79, 'new_value': 4715.09}, {'field': 'count', 'old_value': 186, 'new_value': 193}, {'field': 'instoreAmount', 'old_value': 4946.32, 'new_value': 5217.82}, {'field': 'instoreCount', 'old_value': 186, 'new_value': 193}]
2025-05-28 08:11:16,583 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-28 08:11:16,583 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 24234.47, 'new_value': 25487.57}, {'field': 'dailyBillAmount', 'old_value': 24234.47, 'new_value': 25487.57}, {'field': 'amount', 'old_value': 25014.64, 'new_value': 26298.02}, {'field': 'count', 'old_value': 677, 'new_value': 706}, {'field': 'instoreAmount', 'old_value': 24920.58, 'new_value': 26173.68}, {'field': 'instoreCount', 'old_value': 671, 'new_value': 699}, {'field': 'onlineAmount', 'old_value': 170.57999999999998, 'new_value': 200.86}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 7}]
2025-05-28 08:11:17,083 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-28 08:11:17,083 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47476.4, 'new_value': 48299.0}, {'field': 'dailyBillAmount', 'old_value': 47476.4, 'new_value': 48299.0}, {'field': 'amount', 'old_value': 70819.5, 'new_value': 72025.5}, {'field': 'count', 'old_value': 285, 'new_value': 292}, {'field': 'instoreAmount', 'old_value': 71008.5, 'new_value': 72214.5}, {'field': 'instoreCount', 'old_value': 284, 'new_value': 291}]
2025-05-28 08:11:17,583 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-28 08:11:17,583 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45467.0, 'new_value': 46363.0}, {'field': 'dailyBillAmount', 'old_value': 45467.0, 'new_value': 46363.0}, {'field': 'amount', 'old_value': 48779.0, 'new_value': 49650.0}, {'field': 'count', 'old_value': 269, 'new_value': 276}, {'field': 'instoreAmount', 'old_value': 48793.0, 'new_value': 49664.0}, {'field': 'instoreCount', 'old_value': 269, 'new_value': 276}]
2025-05-28 08:11:18,052 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-28 08:11:18,052 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77456.65, 'new_value': 79265.66}, {'field': 'dailyBillAmount', 'old_value': 77456.65, 'new_value': 79265.66}, {'field': 'amount', 'old_value': 68947.53, 'new_value': 70829.62}, {'field': 'count', 'old_value': 2328, 'new_value': 2393}, {'field': 'instoreAmount', 'old_value': 62913.46, 'new_value': 64527.25}, {'field': 'instoreCount', 'old_value': 2038, 'new_value': 2090}, {'field': 'onlineAmount', 'old_value': 6070.51, 'new_value': 6338.8099999999995}, {'field': 'onlineCount', 'old_value': 290, 'new_value': 303}]
2025-05-28 08:11:18,474 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-28 08:11:18,474 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 48317.83, 'new_value': 51068.0}, {'field': 'dailyBillAmount', 'old_value': 48317.83, 'new_value': 51068.0}, {'field': 'amount', 'old_value': 54595.99, 'new_value': 57344.92}, {'field': 'count', 'old_value': 358, 'new_value': 377}, {'field': 'instoreAmount', 'old_value': 53157.11, 'new_value': 55790.01}, {'field': 'instoreCount', 'old_value': 289, 'new_value': 300}, {'field': 'onlineAmount', 'old_value': 1589.48, 'new_value': 1705.51}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 77}]
2025-05-28 08:11:18,974 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-28 08:11:18,974 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 219263.35, 'new_value': 223405.1}, {'field': 'dailyBillAmount', 'old_value': 219263.35, 'new_value': 223405.1}, {'field': 'amount', 'old_value': 226454.3, 'new_value': 230761.3}, {'field': 'count', 'old_value': 1477, 'new_value': 1513}, {'field': 'instoreAmount', 'old_value': 219551.7, 'new_value': 223613.7}, {'field': 'instoreCount', 'old_value': 1323, 'new_value': 1356}, {'field': 'onlineAmount', 'old_value': 10869.6, 'new_value': 11114.6}, {'field': 'onlineCount', 'old_value': 154, 'new_value': 157}]
2025-05-28 08:11:18,974 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-28 08:11:18,974 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-28 08:11:18,974 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-28 08:11:18,974 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-28 08:11:18,974 - INFO - 正在批量插入月度数据，批次 1/1，共 4 条记录
2025-05-28 08:11:19,130 - INFO - 批量插入月度数据成功，批次 1，4 条记录
2025-05-28 08:11:22,146 - INFO - 批量插入月度数据完成: 总计 4 条，成功 4 条，失败 0 条
2025-05-28 08:11:22,146 - INFO - 批量插入月销售数据完成，共 4 条记录
2025-05-28 08:11:22,146 - INFO - 月销售数据同步完成！更新: 205 条，插入: 4 条，错误: 0 条，跳过: 983 条
2025-05-28 08:11:22,146 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-28 08:11:22,740 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250528.xlsx
2025-05-28 08:11:22,740 - INFO - 综合数据同步流程完成！
2025-05-28 08:11:22,818 - INFO - 综合数据同步完成
2025-05-28 08:11:22,818 - INFO - ==================================================
2025-05-28 08:11:22,818 - INFO - 程序退出
2025-05-28 08:11:22,818 - INFO - ==================================================
