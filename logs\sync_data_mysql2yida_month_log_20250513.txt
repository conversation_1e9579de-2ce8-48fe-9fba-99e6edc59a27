2025-05-13 00:00:03,929 - INFO - =================使用默认全量同步=============
2025-05-13 00:00:05,247 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-13 00:00:05,248 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 00:00:05,275 - INFO - 开始处理日期: 2025-01
2025-05-13 00:00:05,278 - INFO - Request Parameters - Page 1:
2025-05-13 00:00:05,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:05,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:06,243 - INFO - Response - Page 1:
2025-05-13 00:00:06,444 - INFO - 第 1 页获取到 100 条记录
2025-05-13 00:00:06,444 - INFO - Request Parameters - Page 2:
2025-05-13 00:00:06,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:06,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:07,186 - INFO - Response - Page 2:
2025-05-13 00:00:07,387 - INFO - 第 2 页获取到 100 条记录
2025-05-13 00:00:07,387 - INFO - Request Parameters - Page 3:
2025-05-13 00:00:07,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:07,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:07,896 - INFO - Response - Page 3:
2025-05-13 00:00:08,096 - INFO - 第 3 页获取到 100 条记录
2025-05-13 00:00:08,096 - INFO - Request Parameters - Page 4:
2025-05-13 00:00:08,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:08,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:08,623 - INFO - Response - Page 4:
2025-05-13 00:00:08,823 - INFO - 第 4 页获取到 100 条记录
2025-05-13 00:00:08,823 - INFO - Request Parameters - Page 5:
2025-05-13 00:00:08,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:08,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:09,258 - INFO - Response - Page 5:
2025-05-13 00:00:09,459 - INFO - 第 5 页获取到 100 条记录
2025-05-13 00:00:09,459 - INFO - Request Parameters - Page 6:
2025-05-13 00:00:09,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:09,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:09,885 - INFO - Response - Page 6:
2025-05-13 00:00:10,085 - INFO - 第 6 页获取到 100 条记录
2025-05-13 00:00:10,085 - INFO - Request Parameters - Page 7:
2025-05-13 00:00:10,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:10,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:10,554 - INFO - Response - Page 7:
2025-05-13 00:00:10,755 - INFO - 第 7 页获取到 82 条记录
2025-05-13 00:00:10,755 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 00:00:10,755 - INFO - 获取到 682 条表单数据
2025-05-13 00:00:10,767 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 00:00:10,778 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 00:00:10,778 - INFO - 开始处理日期: 2025-02
2025-05-13 00:00:10,778 - INFO - Request Parameters - Page 1:
2025-05-13 00:00:10,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:10,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:11,301 - INFO - Response - Page 1:
2025-05-13 00:00:11,501 - INFO - 第 1 页获取到 100 条记录
2025-05-13 00:00:11,501 - INFO - Request Parameters - Page 2:
2025-05-13 00:00:11,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:11,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:11,941 - INFO - Response - Page 2:
2025-05-13 00:00:12,141 - INFO - 第 2 页获取到 100 条记录
2025-05-13 00:00:12,141 - INFO - Request Parameters - Page 3:
2025-05-13 00:00:12,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:12,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:12,670 - INFO - Response - Page 3:
2025-05-13 00:00:12,871 - INFO - 第 3 页获取到 100 条记录
2025-05-13 00:00:12,871 - INFO - Request Parameters - Page 4:
2025-05-13 00:00:12,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:12,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:13,349 - INFO - Response - Page 4:
2025-05-13 00:00:13,550 - INFO - 第 4 页获取到 100 条记录
2025-05-13 00:00:13,550 - INFO - Request Parameters - Page 5:
2025-05-13 00:00:13,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:13,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:14,109 - INFO - Response - Page 5:
2025-05-13 00:00:14,310 - INFO - 第 5 页获取到 100 条记录
2025-05-13 00:00:14,310 - INFO - Request Parameters - Page 6:
2025-05-13 00:00:14,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:14,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:14,790 - INFO - Response - Page 6:
2025-05-13 00:00:14,990 - INFO - 第 6 页获取到 100 条记录
2025-05-13 00:00:14,990 - INFO - Request Parameters - Page 7:
2025-05-13 00:00:14,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:14,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:15,461 - INFO - Response - Page 7:
2025-05-13 00:00:15,662 - INFO - 第 7 页获取到 70 条记录
2025-05-13 00:00:15,662 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 00:00:15,662 - INFO - 获取到 670 条表单数据
2025-05-13 00:00:15,674 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 00:00:15,686 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 00:00:15,687 - INFO - 开始处理日期: 2025-03
2025-05-13 00:00:15,687 - INFO - Request Parameters - Page 1:
2025-05-13 00:00:15,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:15,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:16,153 - INFO - Response - Page 1:
2025-05-13 00:00:16,355 - INFO - 第 1 页获取到 100 条记录
2025-05-13 00:00:16,355 - INFO - Request Parameters - Page 2:
2025-05-13 00:00:16,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:16,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:16,799 - INFO - Response - Page 2:
2025-05-13 00:00:17,000 - INFO - 第 2 页获取到 100 条记录
2025-05-13 00:00:17,000 - INFO - Request Parameters - Page 3:
2025-05-13 00:00:17,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:17,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:17,466 - INFO - Response - Page 3:
2025-05-13 00:00:17,667 - INFO - 第 3 页获取到 100 条记录
2025-05-13 00:00:17,667 - INFO - Request Parameters - Page 4:
2025-05-13 00:00:17,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:17,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:18,130 - INFO - Response - Page 4:
2025-05-13 00:00:18,330 - INFO - 第 4 页获取到 100 条记录
2025-05-13 00:00:18,330 - INFO - Request Parameters - Page 5:
2025-05-13 00:00:18,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:18,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:18,831 - INFO - Response - Page 5:
2025-05-13 00:00:19,031 - INFO - 第 5 页获取到 100 条记录
2025-05-13 00:00:19,031 - INFO - Request Parameters - Page 6:
2025-05-13 00:00:19,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:19,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:19,539 - INFO - Response - Page 6:
2025-05-13 00:00:19,739 - INFO - 第 6 页获取到 100 条记录
2025-05-13 00:00:19,739 - INFO - Request Parameters - Page 7:
2025-05-13 00:00:19,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:19,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:20,151 - INFO - Response - Page 7:
2025-05-13 00:00:20,351 - INFO - 第 7 页获取到 61 条记录
2025-05-13 00:00:20,351 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 00:00:20,351 - INFO - 获取到 661 条表单数据
2025-05-13 00:00:20,363 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 00:00:20,375 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 00:00:20,375 - INFO - 开始处理日期: 2025-04
2025-05-13 00:00:20,375 - INFO - Request Parameters - Page 1:
2025-05-13 00:00:20,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:20,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:20,880 - INFO - Response - Page 1:
2025-05-13 00:00:21,080 - INFO - 第 1 页获取到 100 条记录
2025-05-13 00:00:21,080 - INFO - Request Parameters - Page 2:
2025-05-13 00:00:21,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:21,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:21,540 - INFO - Response - Page 2:
2025-05-13 00:00:21,740 - INFO - 第 2 页获取到 100 条记录
2025-05-13 00:00:21,740 - INFO - Request Parameters - Page 3:
2025-05-13 00:00:21,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:21,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:22,296 - INFO - Response - Page 3:
2025-05-13 00:00:22,496 - INFO - 第 3 页获取到 100 条记录
2025-05-13 00:00:22,496 - INFO - Request Parameters - Page 4:
2025-05-13 00:00:22,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:22,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:22,953 - INFO - Response - Page 4:
2025-05-13 00:00:23,154 - INFO - 第 4 页获取到 100 条记录
2025-05-13 00:00:23,154 - INFO - Request Parameters - Page 5:
2025-05-13 00:00:23,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:23,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:23,605 - INFO - Response - Page 5:
2025-05-13 00:00:23,806 - INFO - 第 5 页获取到 100 条记录
2025-05-13 00:00:23,806 - INFO - Request Parameters - Page 6:
2025-05-13 00:00:23,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:23,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:24,284 - INFO - Response - Page 6:
2025-05-13 00:00:24,484 - INFO - 第 6 页获取到 100 条记录
2025-05-13 00:00:24,484 - INFO - Request Parameters - Page 7:
2025-05-13 00:00:24,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:24,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:24,917 - INFO - Response - Page 7:
2025-05-13 00:00:25,118 - INFO - 第 7 页获取到 54 条记录
2025-05-13 00:00:25,118 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 00:00:25,119 - INFO - 获取到 654 条表单数据
2025-05-13 00:00:25,132 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 00:00:25,143 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 00:00:25,144 - INFO - 开始处理日期: 2025-05
2025-05-13 00:00:25,144 - INFO - Request Parameters - Page 1:
2025-05-13 00:00:25,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:25,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:25,588 - INFO - Response - Page 1:
2025-05-13 00:00:25,788 - INFO - 第 1 页获取到 100 条记录
2025-05-13 00:00:25,788 - INFO - Request Parameters - Page 2:
2025-05-13 00:00:25,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:25,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:26,246 - INFO - Response - Page 2:
2025-05-13 00:00:26,446 - INFO - 第 2 页获取到 100 条记录
2025-05-13 00:00:26,446 - INFO - Request Parameters - Page 3:
2025-05-13 00:00:26,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:26,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:26,901 - INFO - Response - Page 3:
2025-05-13 00:00:27,101 - INFO - 第 3 页获取到 100 条记录
2025-05-13 00:00:27,101 - INFO - Request Parameters - Page 4:
2025-05-13 00:00:27,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:27,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:27,524 - INFO - Response - Page 4:
2025-05-13 00:00:27,724 - INFO - 第 4 页获取到 100 条记录
2025-05-13 00:00:27,724 - INFO - Request Parameters - Page 5:
2025-05-13 00:00:27,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:27,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:28,363 - INFO - Response - Page 5:
2025-05-13 00:00:28,564 - INFO - 第 5 页获取到 100 条记录
2025-05-13 00:00:28,564 - INFO - Request Parameters - Page 6:
2025-05-13 00:00:28,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:28,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:29,146 - INFO - Response - Page 6:
2025-05-13 00:00:29,346 - INFO - 第 6 页获取到 100 条记录
2025-05-13 00:00:29,346 - INFO - Request Parameters - Page 7:
2025-05-13 00:00:29,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 00:00:29,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 00:00:29,623 - INFO - Response - Page 7:
2025-05-13 00:00:29,823 - INFO - 第 7 页获取到 24 条记录
2025-05-13 00:00:29,823 - INFO - 查询完成，共获取到 624 条记录
2025-05-13 00:00:29,823 - INFO - 获取到 624 条表单数据
2025-05-13 00:00:29,836 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-13 00:00:29,837 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-13 00:00:30,343 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-13 00:00:30,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37782.0, 'new_value': 41095.0}, {'field': 'offline_amount', 'old_value': 47301.0, 'new_value': 51449.0}, {'field': 'total_amount', 'old_value': 85083.0, 'new_value': 92544.0}, {'field': 'order_count', 'old_value': 2017, 'new_value': 2186}]
2025-05-13 00:00:30,343 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-13 00:00:30,801 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-13 00:00:30,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37064.68, 'new_value': 37978.88}, {'field': 'total_amount', 'old_value': 37064.68, 'new_value': 37978.88}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-05-13 00:00:30,802 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-13 00:00:31,198 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-13 00:00:31,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30367.0, 'new_value': 37604.0}, {'field': 'total_amount', 'old_value': 96510.0, 'new_value': 103747.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-13 00:00:31,199 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-13 00:00:31,677 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-13 00:00:31,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16143.7, 'new_value': 16625.7}, {'field': 'total_amount', 'old_value': 16143.7, 'new_value': 16625.7}, {'field': 'order_count', 'old_value': 88, 'new_value': 91}]
2025-05-13 00:00:31,679 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-13 00:00:32,113 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-13 00:00:32,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55644.48, 'new_value': 60862.6}, {'field': 'total_amount', 'old_value': 174777.23, 'new_value': 179995.35}, {'field': 'order_count', 'old_value': 2018, 'new_value': 2071}]
2025-05-13 00:00:32,113 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-13 00:00:32,564 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-13 00:00:32,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6540.1, 'new_value': 7053.7}, {'field': 'total_amount', 'old_value': 12682.14, 'new_value': 13195.74}, {'field': 'order_count', 'old_value': 56, 'new_value': 58}]
2025-05-13 00:00:32,565 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-13 00:00:33,009 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-13 00:00:33,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33595.0, 'new_value': 34691.0}, {'field': 'total_amount', 'old_value': 33595.0, 'new_value': 34691.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-13 00:00:33,009 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-13 00:00:33,414 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-13 00:00:33,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70726.17, 'new_value': 77827.93}, {'field': 'offline_amount', 'old_value': 196864.64, 'new_value': 203721.21}, {'field': 'total_amount', 'old_value': 267590.81, 'new_value': 281549.14}, {'field': 'order_count', 'old_value': 6594, 'new_value': 7066}]
2025-05-13 00:00:33,414 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-13 00:00:33,913 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-13 00:00:33,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28727.0, 'new_value': 29231.0}, {'field': 'total_amount', 'old_value': 28727.0, 'new_value': 29231.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-05-13 00:00:33,914 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-13 00:00:34,360 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-13 00:00:34,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4095.35, 'new_value': 4559.45}, {'field': 'offline_amount', 'old_value': 44281.26, 'new_value': 47636.9}, {'field': 'total_amount', 'old_value': 48376.61, 'new_value': 52196.35}, {'field': 'order_count', 'old_value': 1469, 'new_value': 1547}]
2025-05-13 00:00:34,361 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-13 00:00:34,791 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-13 00:00:34,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42567.68, 'new_value': 45409.92}, {'field': 'offline_amount', 'old_value': 23618.26, 'new_value': 24255.76}, {'field': 'total_amount', 'old_value': 66185.94, 'new_value': 69665.68}, {'field': 'order_count', 'old_value': 4272, 'new_value': 4473}]
2025-05-13 00:00:34,792 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-13 00:00:35,189 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-13 00:00:35,189 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147295.0, 'new_value': 155706.65}, {'field': 'offline_amount', 'old_value': 9563.35, 'new_value': 10051.55}, {'field': 'total_amount', 'old_value': 156858.35, 'new_value': 165758.2}, {'field': 'order_count', 'old_value': 5842, 'new_value': 6121}]
2025-05-13 00:00:35,189 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-13 00:00:35,616 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-13 00:00:35,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59647.46, 'new_value': 63058.56}, {'field': 'offline_amount', 'old_value': 147630.41, 'new_value': 152994.81}, {'field': 'total_amount', 'old_value': 207277.87, 'new_value': 216053.37}, {'field': 'order_count', 'old_value': 2375, 'new_value': 2561}]
2025-05-13 00:00:35,616 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-13 00:00:36,046 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-13 00:00:36,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 650.0, 'new_value': 700.0}, {'field': 'offline_amount', 'old_value': 11664.41, 'new_value': 12286.7}, {'field': 'total_amount', 'old_value': 12314.41, 'new_value': 12986.7}, {'field': 'order_count', 'old_value': 241, 'new_value': 256}]
2025-05-13 00:00:36,047 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-13 00:00:36,538 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-13 00:00:36,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18489.0, 'new_value': 18746.0}, {'field': 'total_amount', 'old_value': 18489.0, 'new_value': 18746.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 172}]
2025-05-13 00:00:36,538 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-13 00:00:36,931 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-13 00:00:36,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40415.89, 'new_value': 43082.73}, {'field': 'offline_amount', 'old_value': 75741.01, 'new_value': 76708.71}, {'field': 'total_amount', 'old_value': 116156.9, 'new_value': 119791.44}, {'field': 'order_count', 'old_value': 1124, 'new_value': 1160}]
2025-05-13 00:00:36,932 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-13 00:00:37,410 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-13 00:00:37,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17534.0, 'new_value': 17891.0}, {'field': 'total_amount', 'old_value': 17534.0, 'new_value': 17891.0}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-13 00:00:37,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-13 00:00:37,863 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-13 00:00:37,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55502.8, 'new_value': 56665.3}, {'field': 'offline_amount', 'old_value': 83603.6, 'new_value': 84437.1}, {'field': 'total_amount', 'old_value': 139106.4, 'new_value': 141102.4}, {'field': 'order_count', 'old_value': 2783, 'new_value': 2842}]
2025-05-13 00:00:37,864 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-13 00:00:38,266 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-13 00:00:38,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47013.27, 'new_value': 49854.25}, {'field': 'total_amount', 'old_value': 47013.27, 'new_value': 49854.25}, {'field': 'order_count', 'old_value': 1418, 'new_value': 1503}]
2025-05-13 00:00:38,268 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-13 00:00:38,644 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-13 00:00:38,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123714.0, 'new_value': 129193.0}, {'field': 'total_amount', 'old_value': 123714.0, 'new_value': 129193.0}, {'field': 'order_count', 'old_value': 362, 'new_value': 382}]
2025-05-13 00:00:38,644 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-13 00:00:39,134 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-13 00:00:39,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31439.42, 'new_value': 34544.71}, {'field': 'offline_amount', 'old_value': 47167.27, 'new_value': 48891.32}, {'field': 'total_amount', 'old_value': 78606.69, 'new_value': 83436.03}, {'field': 'order_count', 'old_value': 3623, 'new_value': 3828}]
2025-05-13 00:00:39,134 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-13 00:00:39,523 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-13 00:00:39,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182453.25, 'new_value': 191720.05}, {'field': 'total_amount', 'old_value': 204615.63, 'new_value': 213882.43}, {'field': 'order_count', 'old_value': 8376, 'new_value': 8839}]
2025-05-13 00:00:39,523 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-13 00:00:39,908 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-13 00:00:39,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11082.76, 'new_value': 13008.77}, {'field': 'offline_amount', 'old_value': 130327.64, 'new_value': 136078.94}, {'field': 'total_amount', 'old_value': 141410.4, 'new_value': 149087.71}, {'field': 'order_count', 'old_value': 4309, 'new_value': 4561}]
2025-05-13 00:00:39,910 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-13 00:00:40,296 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-13 00:00:40,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17678.2, 'new_value': 18093.2}, {'field': 'total_amount', 'old_value': 17678.2, 'new_value': 18093.2}, {'field': 'order_count', 'old_value': 101, 'new_value': 103}]
2025-05-13 00:00:40,297 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-13 00:00:40,733 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-13 00:00:40,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115606.0, 'new_value': 117548.0}, {'field': 'total_amount', 'old_value': 115606.0, 'new_value': 117548.0}, {'field': 'order_count', 'old_value': 1908, 'new_value': 1945}]
2025-05-13 00:00:40,733 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-13 00:00:41,151 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-13 00:00:41,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155390.0, 'new_value': 167739.0}, {'field': 'total_amount', 'old_value': 155390.0, 'new_value': 167739.0}, {'field': 'order_count', 'old_value': 225, 'new_value': 242}]
2025-05-13 00:00:41,151 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-13 00:00:41,550 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-13 00:00:41,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12898.55, 'new_value': 15181.24}, {'field': 'total_amount', 'old_value': 33135.94, 'new_value': 35418.63}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1528}]
2025-05-13 00:00:41,552 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-13 00:00:41,950 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-13 00:00:41,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 181772.0, 'new_value': 194564.0}, {'field': 'total_amount', 'old_value': 182768.0, 'new_value': 195560.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 49}]
2025-05-13 00:00:41,951 - INFO - 日期 2025-05 处理完成 - 更新: 28 条，插入: 0 条，错误: 0 条
2025-05-13 00:00:41,952 - INFO - 数据同步完成！更新: 28 条，插入: 0 条，错误: 0 条
2025-05-13 00:00:41,953 - INFO - =================同步完成====================
2025-05-13 03:00:04,069 - INFO - =================使用默认全量同步=============
2025-05-13 03:00:05,394 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-13 03:00:05,395 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 03:00:05,426 - INFO - 开始处理日期: 2025-01
2025-05-13 03:00:05,429 - INFO - Request Parameters - Page 1:
2025-05-13 03:00:05,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:05,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:06,466 - INFO - Response - Page 1:
2025-05-13 03:00:06,667 - INFO - 第 1 页获取到 100 条记录
2025-05-13 03:00:06,667 - INFO - Request Parameters - Page 2:
2025-05-13 03:00:06,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:06,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:07,185 - INFO - Response - Page 2:
2025-05-13 03:00:07,385 - INFO - 第 2 页获取到 100 条记录
2025-05-13 03:00:07,385 - INFO - Request Parameters - Page 3:
2025-05-13 03:00:07,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:07,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:07,858 - INFO - Response - Page 3:
2025-05-13 03:00:08,059 - INFO - 第 3 页获取到 100 条记录
2025-05-13 03:00:08,059 - INFO - Request Parameters - Page 4:
2025-05-13 03:00:08,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:08,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:08,780 - INFO - Response - Page 4:
2025-05-13 03:00:08,981 - INFO - 第 4 页获取到 100 条记录
2025-05-13 03:00:08,981 - INFO - Request Parameters - Page 5:
2025-05-13 03:00:08,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:08,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:09,553 - INFO - Response - Page 5:
2025-05-13 03:00:09,753 - INFO - 第 5 页获取到 100 条记录
2025-05-13 03:00:09,753 - INFO - Request Parameters - Page 6:
2025-05-13 03:00:09,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:09,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:10,294 - INFO - Response - Page 6:
2025-05-13 03:00:10,494 - INFO - 第 6 页获取到 100 条记录
2025-05-13 03:00:10,494 - INFO - Request Parameters - Page 7:
2025-05-13 03:00:10,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:10,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:10,947 - INFO - Response - Page 7:
2025-05-13 03:00:11,147 - INFO - 第 7 页获取到 82 条记录
2025-05-13 03:00:11,147 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 03:00:11,147 - INFO - 获取到 682 条表单数据
2025-05-13 03:00:11,159 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 03:00:11,170 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 03:00:11,170 - INFO - 开始处理日期: 2025-02
2025-05-13 03:00:11,170 - INFO - Request Parameters - Page 1:
2025-05-13 03:00:11,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:11,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:11,641 - INFO - Response - Page 1:
2025-05-13 03:00:11,841 - INFO - 第 1 页获取到 100 条记录
2025-05-13 03:00:11,841 - INFO - Request Parameters - Page 2:
2025-05-13 03:00:11,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:11,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:12,298 - INFO - Response - Page 2:
2025-05-13 03:00:12,498 - INFO - 第 2 页获取到 100 条记录
2025-05-13 03:00:12,498 - INFO - Request Parameters - Page 3:
2025-05-13 03:00:12,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:12,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:12,978 - INFO - Response - Page 3:
2025-05-13 03:00:13,178 - INFO - 第 3 页获取到 100 条记录
2025-05-13 03:00:13,178 - INFO - Request Parameters - Page 4:
2025-05-13 03:00:13,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:13,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:13,636 - INFO - Response - Page 4:
2025-05-13 03:00:13,837 - INFO - 第 4 页获取到 100 条记录
2025-05-13 03:00:13,837 - INFO - Request Parameters - Page 5:
2025-05-13 03:00:13,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:13,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:14,306 - INFO - Response - Page 5:
2025-05-13 03:00:14,506 - INFO - 第 5 页获取到 100 条记录
2025-05-13 03:00:14,506 - INFO - Request Parameters - Page 6:
2025-05-13 03:00:14,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:14,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:14,982 - INFO - Response - Page 6:
2025-05-13 03:00:15,182 - INFO - 第 6 页获取到 100 条记录
2025-05-13 03:00:15,182 - INFO - Request Parameters - Page 7:
2025-05-13 03:00:15,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:15,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:15,619 - INFO - Response - Page 7:
2025-05-13 03:00:15,819 - INFO - 第 7 页获取到 70 条记录
2025-05-13 03:00:15,819 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 03:00:15,819 - INFO - 获取到 670 条表单数据
2025-05-13 03:00:15,832 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 03:00:15,843 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 03:00:15,843 - INFO - 开始处理日期: 2025-03
2025-05-13 03:00:15,843 - INFO - Request Parameters - Page 1:
2025-05-13 03:00:15,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:15,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:16,269 - INFO - Response - Page 1:
2025-05-13 03:00:16,469 - INFO - 第 1 页获取到 100 条记录
2025-05-13 03:00:16,469 - INFO - Request Parameters - Page 2:
2025-05-13 03:00:16,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:16,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:17,049 - INFO - Response - Page 2:
2025-05-13 03:00:17,249 - INFO - 第 2 页获取到 100 条记录
2025-05-13 03:00:17,249 - INFO - Request Parameters - Page 3:
2025-05-13 03:00:17,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:17,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:17,732 - INFO - Response - Page 3:
2025-05-13 03:00:17,932 - INFO - 第 3 页获取到 100 条记录
2025-05-13 03:00:17,932 - INFO - Request Parameters - Page 4:
2025-05-13 03:00:17,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:17,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:18,417 - INFO - Response - Page 4:
2025-05-13 03:00:18,618 - INFO - 第 4 页获取到 100 条记录
2025-05-13 03:00:18,618 - INFO - Request Parameters - Page 5:
2025-05-13 03:00:18,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:18,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:19,069 - INFO - Response - Page 5:
2025-05-13 03:00:19,271 - INFO - 第 5 页获取到 100 条记录
2025-05-13 03:00:19,271 - INFO - Request Parameters - Page 6:
2025-05-13 03:00:19,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:19,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:19,865 - INFO - Response - Page 6:
2025-05-13 03:00:20,066 - INFO - 第 6 页获取到 100 条记录
2025-05-13 03:00:20,066 - INFO - Request Parameters - Page 7:
2025-05-13 03:00:20,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:20,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:20,488 - INFO - Response - Page 7:
2025-05-13 03:00:20,688 - INFO - 第 7 页获取到 61 条记录
2025-05-13 03:00:20,688 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 03:00:20,688 - INFO - 获取到 661 条表单数据
2025-05-13 03:00:20,701 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 03:00:20,714 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 03:00:20,714 - INFO - 开始处理日期: 2025-04
2025-05-13 03:00:20,714 - INFO - Request Parameters - Page 1:
2025-05-13 03:00:20,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:20,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:21,183 - INFO - Response - Page 1:
2025-05-13 03:00:21,383 - INFO - 第 1 页获取到 100 条记录
2025-05-13 03:00:21,383 - INFO - Request Parameters - Page 2:
2025-05-13 03:00:21,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:21,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:21,821 - INFO - Response - Page 2:
2025-05-13 03:00:22,023 - INFO - 第 2 页获取到 100 条记录
2025-05-13 03:00:22,023 - INFO - Request Parameters - Page 3:
2025-05-13 03:00:22,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:22,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:22,527 - INFO - Response - Page 3:
2025-05-13 03:00:22,728 - INFO - 第 3 页获取到 100 条记录
2025-05-13 03:00:22,728 - INFO - Request Parameters - Page 4:
2025-05-13 03:00:22,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:22,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:23,314 - INFO - Response - Page 4:
2025-05-13 03:00:23,515 - INFO - 第 4 页获取到 100 条记录
2025-05-13 03:00:23,515 - INFO - Request Parameters - Page 5:
2025-05-13 03:00:23,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:23,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:23,981 - INFO - Response - Page 5:
2025-05-13 03:00:24,181 - INFO - 第 5 页获取到 100 条记录
2025-05-13 03:00:24,181 - INFO - Request Parameters - Page 6:
2025-05-13 03:00:24,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:24,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:24,750 - INFO - Response - Page 6:
2025-05-13 03:00:24,952 - INFO - 第 6 页获取到 100 条记录
2025-05-13 03:00:24,952 - INFO - Request Parameters - Page 7:
2025-05-13 03:00:24,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:24,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:25,330 - INFO - Response - Page 7:
2025-05-13 03:00:25,530 - INFO - 第 7 页获取到 54 条记录
2025-05-13 03:00:25,530 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 03:00:25,531 - INFO - 获取到 654 条表单数据
2025-05-13 03:00:25,543 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 03:00:25,555 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 03:00:25,555 - INFO - 开始处理日期: 2025-05
2025-05-13 03:00:25,555 - INFO - Request Parameters - Page 1:
2025-05-13 03:00:25,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:25,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:26,059 - INFO - Response - Page 1:
2025-05-13 03:00:26,260 - INFO - 第 1 页获取到 100 条记录
2025-05-13 03:00:26,260 - INFO - Request Parameters - Page 2:
2025-05-13 03:00:26,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:26,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:26,706 - INFO - Response - Page 2:
2025-05-13 03:00:26,908 - INFO - 第 2 页获取到 100 条记录
2025-05-13 03:00:26,908 - INFO - Request Parameters - Page 3:
2025-05-13 03:00:26,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:26,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:27,342 - INFO - Response - Page 3:
2025-05-13 03:00:27,543 - INFO - 第 3 页获取到 100 条记录
2025-05-13 03:00:27,543 - INFO - Request Parameters - Page 4:
2025-05-13 03:00:27,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:27,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:28,002 - INFO - Response - Page 4:
2025-05-13 03:00:28,202 - INFO - 第 4 页获取到 100 条记录
2025-05-13 03:00:28,202 - INFO - Request Parameters - Page 5:
2025-05-13 03:00:28,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:28,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:28,683 - INFO - Response - Page 5:
2025-05-13 03:00:28,883 - INFO - 第 5 页获取到 100 条记录
2025-05-13 03:00:28,883 - INFO - Request Parameters - Page 6:
2025-05-13 03:00:28,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:28,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:29,329 - INFO - Response - Page 6:
2025-05-13 03:00:29,530 - INFO - 第 6 页获取到 100 条记录
2025-05-13 03:00:29,530 - INFO - Request Parameters - Page 7:
2025-05-13 03:00:29,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 03:00:29,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 03:00:29,844 - INFO - Response - Page 7:
2025-05-13 03:00:30,046 - INFO - 第 7 页获取到 24 条记录
2025-05-13 03:00:30,046 - INFO - 查询完成，共获取到 624 条记录
2025-05-13 03:00:30,046 - INFO - 获取到 624 条表单数据
2025-05-13 03:00:30,057 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-13 03:00:30,067 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 03:00:30,067 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 03:00:30,069 - INFO - =================同步完成====================
2025-05-13 06:00:04,374 - INFO - =================使用默认全量同步=============
2025-05-13 06:00:05,685 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-13 06:00:05,685 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 06:00:05,712 - INFO - 开始处理日期: 2025-01
2025-05-13 06:00:05,715 - INFO - Request Parameters - Page 1:
2025-05-13 06:00:05,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:05,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:06,597 - INFO - Response - Page 1:
2025-05-13 06:00:06,797 - INFO - 第 1 页获取到 100 条记录
2025-05-13 06:00:06,797 - INFO - Request Parameters - Page 2:
2025-05-13 06:00:06,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:06,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:07,674 - INFO - Response - Page 2:
2025-05-13 06:00:07,874 - INFO - 第 2 页获取到 100 条记录
2025-05-13 06:00:07,874 - INFO - Request Parameters - Page 3:
2025-05-13 06:00:07,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:07,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:08,353 - INFO - Response - Page 3:
2025-05-13 06:00:08,554 - INFO - 第 3 页获取到 100 条记录
2025-05-13 06:00:08,554 - INFO - Request Parameters - Page 4:
2025-05-13 06:00:08,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:08,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:09,002 - INFO - Response - Page 4:
2025-05-13 06:00:09,202 - INFO - 第 4 页获取到 100 条记录
2025-05-13 06:00:09,202 - INFO - Request Parameters - Page 5:
2025-05-13 06:00:09,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:09,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:09,661 - INFO - Response - Page 5:
2025-05-13 06:00:09,861 - INFO - 第 5 页获取到 100 条记录
2025-05-13 06:00:09,861 - INFO - Request Parameters - Page 6:
2025-05-13 06:00:09,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:09,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:10,361 - INFO - Response - Page 6:
2025-05-13 06:00:10,562 - INFO - 第 6 页获取到 100 条记录
2025-05-13 06:00:10,562 - INFO - Request Parameters - Page 7:
2025-05-13 06:00:10,562 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:10,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:11,014 - INFO - Response - Page 7:
2025-05-13 06:00:11,214 - INFO - 第 7 页获取到 82 条记录
2025-05-13 06:00:11,214 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 06:00:11,214 - INFO - 获取到 682 条表单数据
2025-05-13 06:00:11,226 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 06:00:11,237 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 06:00:11,237 - INFO - 开始处理日期: 2025-02
2025-05-13 06:00:11,237 - INFO - Request Parameters - Page 1:
2025-05-13 06:00:11,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:11,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:11,766 - INFO - Response - Page 1:
2025-05-13 06:00:11,967 - INFO - 第 1 页获取到 100 条记录
2025-05-13 06:00:11,967 - INFO - Request Parameters - Page 2:
2025-05-13 06:00:11,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:11,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:12,422 - INFO - Response - Page 2:
2025-05-13 06:00:12,623 - INFO - 第 2 页获取到 100 条记录
2025-05-13 06:00:12,623 - INFO - Request Parameters - Page 3:
2025-05-13 06:00:12,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:12,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:13,179 - INFO - Response - Page 3:
2025-05-13 06:00:13,380 - INFO - 第 3 页获取到 100 条记录
2025-05-13 06:00:13,380 - INFO - Request Parameters - Page 4:
2025-05-13 06:00:13,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:13,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:13,844 - INFO - Response - Page 4:
2025-05-13 06:00:14,045 - INFO - 第 4 页获取到 100 条记录
2025-05-13 06:00:14,045 - INFO - Request Parameters - Page 5:
2025-05-13 06:00:14,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:14,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:14,497 - INFO - Response - Page 5:
2025-05-13 06:00:14,697 - INFO - 第 5 页获取到 100 条记录
2025-05-13 06:00:14,697 - INFO - Request Parameters - Page 6:
2025-05-13 06:00:14,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:14,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:15,206 - INFO - Response - Page 6:
2025-05-13 06:00:15,407 - INFO - 第 6 页获取到 100 条记录
2025-05-13 06:00:15,407 - INFO - Request Parameters - Page 7:
2025-05-13 06:00:15,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:15,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:15,900 - INFO - Response - Page 7:
2025-05-13 06:00:16,100 - INFO - 第 7 页获取到 70 条记录
2025-05-13 06:00:16,100 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 06:00:16,101 - INFO - 获取到 670 条表单数据
2025-05-13 06:00:16,121 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 06:00:16,142 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 06:00:16,143 - INFO - 开始处理日期: 2025-03
2025-05-13 06:00:16,143 - INFO - Request Parameters - Page 1:
2025-05-13 06:00:16,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:16,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:16,648 - INFO - Response - Page 1:
2025-05-13 06:00:16,849 - INFO - 第 1 页获取到 100 条记录
2025-05-13 06:00:16,849 - INFO - Request Parameters - Page 2:
2025-05-13 06:00:16,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:16,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:17,352 - INFO - Response - Page 2:
2025-05-13 06:00:17,553 - INFO - 第 2 页获取到 100 条记录
2025-05-13 06:00:17,553 - INFO - Request Parameters - Page 3:
2025-05-13 06:00:17,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:17,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:18,039 - INFO - Response - Page 3:
2025-05-13 06:00:18,240 - INFO - 第 3 页获取到 100 条记录
2025-05-13 06:00:18,240 - INFO - Request Parameters - Page 4:
2025-05-13 06:00:18,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:18,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:18,738 - INFO - Response - Page 4:
2025-05-13 06:00:18,940 - INFO - 第 4 页获取到 100 条记录
2025-05-13 06:00:18,940 - INFO - Request Parameters - Page 5:
2025-05-13 06:00:18,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:18,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:19,504 - INFO - Response - Page 5:
2025-05-13 06:00:19,704 - INFO - 第 5 页获取到 100 条记录
2025-05-13 06:00:19,704 - INFO - Request Parameters - Page 6:
2025-05-13 06:00:19,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:19,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:20,187 - INFO - Response - Page 6:
2025-05-13 06:00:20,388 - INFO - 第 6 页获取到 100 条记录
2025-05-13 06:00:20,388 - INFO - Request Parameters - Page 7:
2025-05-13 06:00:20,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:20,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:20,908 - INFO - Response - Page 7:
2025-05-13 06:00:21,109 - INFO - 第 7 页获取到 61 条记录
2025-05-13 06:00:21,109 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 06:00:21,109 - INFO - 获取到 661 条表单数据
2025-05-13 06:00:21,126 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 06:00:21,138 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 06:00:21,138 - INFO - 开始处理日期: 2025-04
2025-05-13 06:00:21,138 - INFO - Request Parameters - Page 1:
2025-05-13 06:00:21,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:21,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:21,662 - INFO - Response - Page 1:
2025-05-13 06:00:21,863 - INFO - 第 1 页获取到 100 条记录
2025-05-13 06:00:21,863 - INFO - Request Parameters - Page 2:
2025-05-13 06:00:21,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:21,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:22,357 - INFO - Response - Page 2:
2025-05-13 06:00:22,557 - INFO - 第 2 页获取到 100 条记录
2025-05-13 06:00:22,557 - INFO - Request Parameters - Page 3:
2025-05-13 06:00:22,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:22,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:23,085 - INFO - Response - Page 3:
2025-05-13 06:00:23,285 - INFO - 第 3 页获取到 100 条记录
2025-05-13 06:00:23,285 - INFO - Request Parameters - Page 4:
2025-05-13 06:00:23,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:23,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:23,770 - INFO - Response - Page 4:
2025-05-13 06:00:23,971 - INFO - 第 4 页获取到 100 条记录
2025-05-13 06:00:23,971 - INFO - Request Parameters - Page 5:
2025-05-13 06:00:23,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:23,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:24,454 - INFO - Response - Page 5:
2025-05-13 06:00:24,655 - INFO - 第 5 页获取到 100 条记录
2025-05-13 06:00:24,655 - INFO - Request Parameters - Page 6:
2025-05-13 06:00:24,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:24,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:25,129 - INFO - Response - Page 6:
2025-05-13 06:00:25,329 - INFO - 第 6 页获取到 100 条记录
2025-05-13 06:00:25,329 - INFO - Request Parameters - Page 7:
2025-05-13 06:00:25,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:25,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:25,722 - INFO - Response - Page 7:
2025-05-13 06:00:25,923 - INFO - 第 7 页获取到 54 条记录
2025-05-13 06:00:25,923 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 06:00:25,923 - INFO - 获取到 654 条表单数据
2025-05-13 06:00:25,936 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 06:00:25,948 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 06:00:25,949 - INFO - 开始处理日期: 2025-05
2025-05-13 06:00:25,949 - INFO - Request Parameters - Page 1:
2025-05-13 06:00:25,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:25,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:26,490 - INFO - Response - Page 1:
2025-05-13 06:00:26,690 - INFO - 第 1 页获取到 100 条记录
2025-05-13 06:00:26,690 - INFO - Request Parameters - Page 2:
2025-05-13 06:00:26,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:26,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:27,122 - INFO - Response - Page 2:
2025-05-13 06:00:27,323 - INFO - 第 2 页获取到 100 条记录
2025-05-13 06:00:27,323 - INFO - Request Parameters - Page 3:
2025-05-13 06:00:27,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:27,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:27,836 - INFO - Response - Page 3:
2025-05-13 06:00:28,036 - INFO - 第 3 页获取到 100 条记录
2025-05-13 06:00:28,036 - INFO - Request Parameters - Page 4:
2025-05-13 06:00:28,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:28,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:28,525 - INFO - Response - Page 4:
2025-05-13 06:00:28,726 - INFO - 第 4 页获取到 100 条记录
2025-05-13 06:00:28,726 - INFO - Request Parameters - Page 5:
2025-05-13 06:00:28,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:28,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:29,172 - INFO - Response - Page 5:
2025-05-13 06:00:29,374 - INFO - 第 5 页获取到 100 条记录
2025-05-13 06:00:29,374 - INFO - Request Parameters - Page 6:
2025-05-13 06:00:29,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:29,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:29,991 - INFO - Response - Page 6:
2025-05-13 06:00:30,191 - INFO - 第 6 页获取到 100 条记录
2025-05-13 06:00:30,191 - INFO - Request Parameters - Page 7:
2025-05-13 06:00:30,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 06:00:30,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 06:00:30,648 - INFO - Response - Page 7:
2025-05-13 06:00:30,848 - INFO - 第 7 页获取到 24 条记录
2025-05-13 06:00:30,848 - INFO - 查询完成，共获取到 624 条记录
2025-05-13 06:00:30,848 - INFO - 获取到 624 条表单数据
2025-05-13 06:00:30,860 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-13 06:00:30,870 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-13 06:00:31,473 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-13 06:00:31,473 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39513.0, 'new_value': 42288.0}, {'field': 'total_amount', 'old_value': 41363.0, 'new_value': 44138.0}, {'field': 'order_count', 'old_value': 239, 'new_value': 257}]
2025-05-13 06:00:31,474 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-13 06:00:31,474 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-13 06:00:31,476 - INFO - =================同步完成====================
2025-05-13 09:00:01,928 - INFO - =================使用默认全量同步=============
2025-05-13 09:00:03,336 - INFO - MySQL查询成功，共获取 3291 条记录
2025-05-13 09:00:03,337 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 09:00:03,365 - INFO - 开始处理日期: 2025-01
2025-05-13 09:00:03,368 - INFO - Request Parameters - Page 1:
2025-05-13 09:00:03,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:03,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:04,217 - INFO - Response - Page 1:
2025-05-13 09:00:04,418 - INFO - 第 1 页获取到 100 条记录
2025-05-13 09:00:04,418 - INFO - Request Parameters - Page 2:
2025-05-13 09:00:04,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:04,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:04,914 - INFO - Response - Page 2:
2025-05-13 09:00:05,114 - INFO - 第 2 页获取到 100 条记录
2025-05-13 09:00:05,114 - INFO - Request Parameters - Page 3:
2025-05-13 09:00:05,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:05,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:05,649 - INFO - Response - Page 3:
2025-05-13 09:00:05,850 - INFO - 第 3 页获取到 100 条记录
2025-05-13 09:00:05,850 - INFO - Request Parameters - Page 4:
2025-05-13 09:00:05,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:05,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:06,773 - INFO - Response - Page 4:
2025-05-13 09:00:06,974 - INFO - 第 4 页获取到 100 条记录
2025-05-13 09:00:06,974 - INFO - Request Parameters - Page 5:
2025-05-13 09:00:06,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:06,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:07,421 - INFO - Response - Page 5:
2025-05-13 09:00:07,621 - INFO - 第 5 页获取到 100 条记录
2025-05-13 09:00:07,621 - INFO - Request Parameters - Page 6:
2025-05-13 09:00:07,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:07,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:08,176 - INFO - Response - Page 6:
2025-05-13 09:00:08,377 - INFO - 第 6 页获取到 100 条记录
2025-05-13 09:00:08,377 - INFO - Request Parameters - Page 7:
2025-05-13 09:00:08,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:08,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:08,838 - INFO - Response - Page 7:
2025-05-13 09:00:09,039 - INFO - 第 7 页获取到 82 条记录
2025-05-13 09:00:09,039 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 09:00:09,039 - INFO - 获取到 682 条表单数据
2025-05-13 09:00:09,051 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 09:00:09,062 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 09:00:09,062 - INFO - 开始处理日期: 2025-02
2025-05-13 09:00:09,062 - INFO - Request Parameters - Page 1:
2025-05-13 09:00:09,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:09,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:09,529 - INFO - Response - Page 1:
2025-05-13 09:00:09,729 - INFO - 第 1 页获取到 100 条记录
2025-05-13 09:00:09,729 - INFO - Request Parameters - Page 2:
2025-05-13 09:00:09,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:09,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:10,279 - INFO - Response - Page 2:
2025-05-13 09:00:10,479 - INFO - 第 2 页获取到 100 条记录
2025-05-13 09:00:10,479 - INFO - Request Parameters - Page 3:
2025-05-13 09:00:10,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:10,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:11,033 - INFO - Response - Page 3:
2025-05-13 09:00:11,233 - INFO - 第 3 页获取到 100 条记录
2025-05-13 09:00:11,233 - INFO - Request Parameters - Page 4:
2025-05-13 09:00:11,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:11,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:11,761 - INFO - Response - Page 4:
2025-05-13 09:00:11,961 - INFO - 第 4 页获取到 100 条记录
2025-05-13 09:00:11,961 - INFO - Request Parameters - Page 5:
2025-05-13 09:00:11,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:11,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:12,398 - INFO - Response - Page 5:
2025-05-13 09:00:12,599 - INFO - 第 5 页获取到 100 条记录
2025-05-13 09:00:12,599 - INFO - Request Parameters - Page 6:
2025-05-13 09:00:12,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:12,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:13,109 - INFO - Response - Page 6:
2025-05-13 09:00:13,310 - INFO - 第 6 页获取到 100 条记录
2025-05-13 09:00:13,310 - INFO - Request Parameters - Page 7:
2025-05-13 09:00:13,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:13,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:13,865 - INFO - Response - Page 7:
2025-05-13 09:00:14,065 - INFO - 第 7 页获取到 70 条记录
2025-05-13 09:00:14,065 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 09:00:14,065 - INFO - 获取到 670 条表单数据
2025-05-13 09:00:14,078 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 09:00:14,089 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 09:00:14,089 - INFO - 开始处理日期: 2025-03
2025-05-13 09:00:14,090 - INFO - Request Parameters - Page 1:
2025-05-13 09:00:14,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:14,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:14,634 - INFO - Response - Page 1:
2025-05-13 09:00:14,834 - INFO - 第 1 页获取到 100 条记录
2025-05-13 09:00:14,834 - INFO - Request Parameters - Page 2:
2025-05-13 09:00:14,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:14,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:15,353 - INFO - Response - Page 2:
2025-05-13 09:00:15,554 - INFO - 第 2 页获取到 100 条记录
2025-05-13 09:00:15,554 - INFO - Request Parameters - Page 3:
2025-05-13 09:00:15,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:15,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:16,094 - INFO - Response - Page 3:
2025-05-13 09:00:16,294 - INFO - 第 3 页获取到 100 条记录
2025-05-13 09:00:16,294 - INFO - Request Parameters - Page 4:
2025-05-13 09:00:16,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:16,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:16,744 - INFO - Response - Page 4:
2025-05-13 09:00:16,945 - INFO - 第 4 页获取到 100 条记录
2025-05-13 09:00:16,945 - INFO - Request Parameters - Page 5:
2025-05-13 09:00:16,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:16,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:17,455 - INFO - Response - Page 5:
2025-05-13 09:00:17,655 - INFO - 第 5 页获取到 100 条记录
2025-05-13 09:00:17,655 - INFO - Request Parameters - Page 6:
2025-05-13 09:00:17,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:17,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:18,258 - INFO - Response - Page 6:
2025-05-13 09:00:18,458 - INFO - 第 6 页获取到 100 条记录
2025-05-13 09:00:18,458 - INFO - Request Parameters - Page 7:
2025-05-13 09:00:18,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:18,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:18,912 - INFO - Response - Page 7:
2025-05-13 09:00:19,112 - INFO - 第 7 页获取到 61 条记录
2025-05-13 09:00:19,112 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 09:00:19,112 - INFO - 获取到 661 条表单数据
2025-05-13 09:00:19,125 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 09:00:19,136 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 09:00:19,137 - INFO - 开始处理日期: 2025-04
2025-05-13 09:00:19,137 - INFO - Request Parameters - Page 1:
2025-05-13 09:00:19,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:19,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:19,655 - INFO - Response - Page 1:
2025-05-13 09:00:19,856 - INFO - 第 1 页获取到 100 条记录
2025-05-13 09:00:19,856 - INFO - Request Parameters - Page 2:
2025-05-13 09:00:19,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:19,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:20,368 - INFO - Response - Page 2:
2025-05-13 09:00:20,569 - INFO - 第 2 页获取到 100 条记录
2025-05-13 09:00:20,570 - INFO - Request Parameters - Page 3:
2025-05-13 09:00:20,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:20,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:21,086 - INFO - Response - Page 3:
2025-05-13 09:00:21,286 - INFO - 第 3 页获取到 100 条记录
2025-05-13 09:00:21,286 - INFO - Request Parameters - Page 4:
2025-05-13 09:00:21,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:21,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:21,861 - INFO - Response - Page 4:
2025-05-13 09:00:22,062 - INFO - 第 4 页获取到 100 条记录
2025-05-13 09:00:22,062 - INFO - Request Parameters - Page 5:
2025-05-13 09:00:22,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:22,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:22,575 - INFO - Response - Page 5:
2025-05-13 09:00:22,775 - INFO - 第 5 页获取到 100 条记录
2025-05-13 09:00:22,775 - INFO - Request Parameters - Page 6:
2025-05-13 09:00:22,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:22,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:23,291 - INFO - Response - Page 6:
2025-05-13 09:00:23,491 - INFO - 第 6 页获取到 100 条记录
2025-05-13 09:00:23,491 - INFO - Request Parameters - Page 7:
2025-05-13 09:00:23,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:23,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:23,963 - INFO - Response - Page 7:
2025-05-13 09:00:24,163 - INFO - 第 7 页获取到 54 条记录
2025-05-13 09:00:24,163 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 09:00:24,163 - INFO - 获取到 654 条表单数据
2025-05-13 09:00:24,176 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 09:00:24,186 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 09:00:24,187 - INFO - 开始处理日期: 2025-05
2025-05-13 09:00:24,187 - INFO - Request Parameters - Page 1:
2025-05-13 09:00:24,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:24,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:24,710 - INFO - Response - Page 1:
2025-05-13 09:00:24,910 - INFO - 第 1 页获取到 100 条记录
2025-05-13 09:00:24,910 - INFO - Request Parameters - Page 2:
2025-05-13 09:00:24,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:24,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:25,426 - INFO - Response - Page 2:
2025-05-13 09:00:25,626 - INFO - 第 2 页获取到 100 条记录
2025-05-13 09:00:25,626 - INFO - Request Parameters - Page 3:
2025-05-13 09:00:25,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:25,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:26,103 - INFO - Response - Page 3:
2025-05-13 09:00:26,304 - INFO - 第 3 页获取到 100 条记录
2025-05-13 09:00:26,304 - INFO - Request Parameters - Page 4:
2025-05-13 09:00:26,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:26,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:26,835 - INFO - Response - Page 4:
2025-05-13 09:00:27,036 - INFO - 第 4 页获取到 100 条记录
2025-05-13 09:00:27,036 - INFO - Request Parameters - Page 5:
2025-05-13 09:00:27,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:27,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:27,542 - INFO - Response - Page 5:
2025-05-13 09:00:27,743 - INFO - 第 5 页获取到 100 条记录
2025-05-13 09:00:27,743 - INFO - Request Parameters - Page 6:
2025-05-13 09:00:27,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:27,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:28,351 - INFO - Response - Page 6:
2025-05-13 09:00:28,552 - INFO - 第 6 页获取到 100 条记录
2025-05-13 09:00:28,552 - INFO - Request Parameters - Page 7:
2025-05-13 09:00:28,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 09:00:28,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 09:00:28,906 - INFO - Response - Page 7:
2025-05-13 09:00:29,106 - INFO - 第 7 页获取到 24 条记录
2025-05-13 09:00:29,106 - INFO - 查询完成，共获取到 624 条记录
2025-05-13 09:00:29,106 - INFO - 获取到 624 条表单数据
2025-05-13 09:00:29,118 - INFO - 当前日期 2025-05 有 624 条MySQL数据需要处理
2025-05-13 09:00:29,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-13 09:00:29,629 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-13 09:00:29,629 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 865.0, 'new_value': 1115.0}, {'field': 'offline_amount', 'old_value': 23375.0, 'new_value': 23835.0}, {'field': 'total_amount', 'old_value': 24240.0, 'new_value': 24950.0}, {'field': 'order_count', 'old_value': 301, 'new_value': 313}]
2025-05-13 09:00:29,629 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-13 09:00:30,108 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-13 09:00:30,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000000.0, 'new_value': 5400000.0}, {'field': 'total_amount', 'old_value': 5100000.0, 'new_value': 5500000.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-13 09:00:30,108 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-13 09:00:30,561 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-13 09:00:30,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71317.37, 'new_value': 82311.4}, {'field': 'total_amount', 'old_value': 71317.37, 'new_value': 82311.4}, {'field': 'order_count', 'old_value': 3120, 'new_value': 3476}]
2025-05-13 09:00:30,562 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-13 09:00:31,118 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-13 09:00:31,119 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11063.77, 'new_value': 11777.65}, {'field': 'offline_amount', 'old_value': 13261.73, 'new_value': 13840.43}, {'field': 'total_amount', 'old_value': 24325.5, 'new_value': 25618.08}, {'field': 'order_count', 'old_value': 1141, 'new_value': 1220}]
2025-05-13 09:00:31,119 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-13 09:00:31,524 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-13 09:00:31,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35830.0, 'new_value': 37830.0}, {'field': 'total_amount', 'old_value': 35830.0, 'new_value': 37830.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 64}]
2025-05-13 09:00:31,525 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-13 09:00:31,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-13 09:00:31,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97871.42, 'new_value': 100368.42}, {'field': 'total_amount', 'old_value': 97871.42, 'new_value': 100368.42}, {'field': 'order_count', 'old_value': 105, 'new_value': 117}]
2025-05-13 09:00:31,969 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-13 09:00:32,476 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-13 09:00:32,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34444.0, 'new_value': 37464.0}, {'field': 'total_amount', 'old_value': 34444.0, 'new_value': 37464.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 317}]
2025-05-13 09:00:32,476 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-13 09:00:32,932 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-13 09:00:32,932 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14966.0, 'new_value': 15507.0}, {'field': 'total_amount', 'old_value': 16016.0, 'new_value': 16557.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-05-13 09:00:32,933 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-13 09:00:33,394 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-13 09:00:33,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55554.3, 'new_value': 57202.77}, {'field': 'total_amount', 'old_value': 58699.32, 'new_value': 60347.79}, {'field': 'order_count', 'old_value': 1361, 'new_value': 1400}]
2025-05-13 09:00:33,394 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-13 09:00:33,866 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-13 09:00:33,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35220.0, 'new_value': 35589.0}, {'field': 'total_amount', 'old_value': 39348.32, 'new_value': 39717.32}, {'field': 'order_count', 'old_value': 428, 'new_value': 429}]
2025-05-13 09:00:33,866 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-13 09:00:34,395 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-13 09:00:34,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96670.85, 'new_value': 99573.95}, {'field': 'total_amount', 'old_value': 96670.85, 'new_value': 99573.95}, {'field': 'order_count', 'old_value': 299, 'new_value': 308}]
2025-05-13 09:00:34,396 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-13 09:00:34,870 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-13 09:00:34,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55043.9, 'new_value': 60769.0}, {'field': 'total_amount', 'old_value': 78684.61, 'new_value': 84409.71}, {'field': 'order_count', 'old_value': 2588, 'new_value': 2808}]
2025-05-13 09:00:34,871 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-13 09:00:35,340 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-13 09:00:35,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 197957.0, 'new_value': 209486.0}, {'field': 'total_amount', 'old_value': 197957.0, 'new_value': 209486.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-05-13 09:00:35,340 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-13 09:00:35,796 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-13 09:00:35,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3240.23, 'new_value': 3419.32}, {'field': 'offline_amount', 'old_value': 82644.69, 'new_value': 92241.02}, {'field': 'total_amount', 'old_value': 85884.92, 'new_value': 95660.34}, {'field': 'order_count', 'old_value': 596, 'new_value': 654}]
2025-05-13 09:00:35,796 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-13 09:00:36,300 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-13 09:00:36,300 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183256.9, 'new_value': 189583.9}, {'field': 'offline_amount', 'old_value': 29074.0, 'new_value': 41040.0}, {'field': 'total_amount', 'old_value': 212330.9, 'new_value': 230623.9}, {'field': 'order_count', 'old_value': 263, 'new_value': 281}]
2025-05-13 09:00:36,301 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-13 09:00:36,765 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-13 09:00:36,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66307.0, 'new_value': 69606.0}, {'field': 'total_amount', 'old_value': 89929.48, 'new_value': 93228.48}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-13 09:00:36,765 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-13 09:00:37,214 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-13 09:00:37,215 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7299.64, 'new_value': 7591.57}, {'field': 'offline_amount', 'old_value': 223803.99, 'new_value': 235412.52}, {'field': 'total_amount', 'old_value': 231103.63, 'new_value': 243004.09}, {'field': 'order_count', 'old_value': 893, 'new_value': 943}]
2025-05-13 09:00:37,215 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-13 09:00:37,733 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-13 09:00:37,733 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112899.19, 'new_value': 117703.5}, {'field': 'offline_amount', 'old_value': 70113.23, 'new_value': 71755.38}, {'field': 'total_amount', 'old_value': 183012.42, 'new_value': 189458.88}, {'field': 'order_count', 'old_value': 691, 'new_value': 722}]
2025-05-13 09:00:37,733 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-13 09:00:38,221 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-13 09:00:38,221 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11319.86, 'new_value': 11768.79}, {'field': 'offline_amount', 'old_value': 151384.7, 'new_value': 154683.91}, {'field': 'total_amount', 'old_value': 162704.56, 'new_value': 166452.7}, {'field': 'order_count', 'old_value': 759, 'new_value': 780}]
2025-05-13 09:00:38,221 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-13 09:00:38,675 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-13 09:00:38,675 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16188.14, 'new_value': 17017.98}, {'field': 'offline_amount', 'old_value': 295624.05, 'new_value': 308906.76}, {'field': 'total_amount', 'old_value': 311812.19, 'new_value': 325924.74}, {'field': 'order_count', 'old_value': 1773, 'new_value': 1834}]
2025-05-13 09:00:38,675 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-13 09:00:39,182 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-13 09:00:39,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34970.25, 'new_value': 37001.45}, {'field': 'offline_amount', 'old_value': 521851.44, 'new_value': 563272.04}, {'field': 'total_amount', 'old_value': 556821.69, 'new_value': 600273.49}, {'field': 'order_count', 'old_value': 4259, 'new_value': 4661}]
2025-05-13 09:00:39,183 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-13 09:00:39,646 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-13 09:00:39,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15062.0, 'new_value': 15492.4}, {'field': 'offline_amount', 'old_value': 12289.7, 'new_value': 12506.7}, {'field': 'total_amount', 'old_value': 27351.7, 'new_value': 27999.1}, {'field': 'order_count', 'old_value': 149, 'new_value': 151}]
2025-05-13 09:00:39,647 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-13 09:00:40,147 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-13 09:00:40,147 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186003.0, 'new_value': 190675.0}, {'field': 'total_amount', 'old_value': 186003.0, 'new_value': 190675.0}, {'field': 'order_count', 'old_value': 153, 'new_value': 159}]
2025-05-13 09:00:40,147 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-13 09:00:40,604 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-13 09:00:40,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7175.74, 'new_value': 7616.8}, {'field': 'offline_amount', 'old_value': 120120.64, 'new_value': 129414.34}, {'field': 'total_amount', 'old_value': 127296.38, 'new_value': 137031.14}, {'field': 'order_count', 'old_value': 6684, 'new_value': 7259}]
2025-05-13 09:00:40,604 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-13 09:00:41,154 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-13 09:00:41,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178214.1, 'new_value': 184596.6}, {'field': 'total_amount', 'old_value': 178214.1, 'new_value': 184596.6}, {'field': 'order_count', 'old_value': 2027, 'new_value': 2089}]
2025-05-13 09:00:41,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-13 09:00:41,693 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-13 09:00:41,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24548.61, 'new_value': 25407.49}, {'field': 'offline_amount', 'old_value': 14362.0, 'new_value': 15536.0}, {'field': 'total_amount', 'old_value': 38910.61, 'new_value': 40943.49}, {'field': 'order_count', 'old_value': 496, 'new_value': 524}]
2025-05-13 09:00:41,694 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-13 09:00:42,258 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-13 09:00:42,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76494.78, 'new_value': 81887.75}, {'field': 'total_amount', 'old_value': 76494.78, 'new_value': 81887.75}, {'field': 'order_count', 'old_value': 358, 'new_value': 391}]
2025-05-13 09:00:42,259 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-13 09:00:42,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-13 09:00:42,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147266.3, 'new_value': 155435.4}, {'field': 'total_amount', 'old_value': 147266.3, 'new_value': 155435.4}, {'field': 'order_count', 'old_value': 236, 'new_value': 249}]
2025-05-13 09:00:42,711 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-13 09:00:43,153 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-13 09:00:43,153 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35900.72, 'new_value': 40044.8}, {'field': 'offline_amount', 'old_value': 291311.84, 'new_value': 302311.84}, {'field': 'total_amount', 'old_value': 327212.56, 'new_value': 342356.64}, {'field': 'order_count', 'old_value': 1235, 'new_value': 1375}]
2025-05-13 09:00:43,154 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-13 09:00:43,588 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-13 09:00:43,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19733.0, 'new_value': 26633.0}, {'field': 'total_amount', 'old_value': 19733.0, 'new_value': 26633.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-13 09:00:43,588 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-13 09:00:43,997 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-13 09:00:43,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120701.0, 'new_value': 122469.0}, {'field': 'total_amount', 'old_value': 120701.0, 'new_value': 122469.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 81}]
2025-05-13 09:00:43,997 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-13 09:00:44,381 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-13 09:00:44,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3521.52, 'new_value': 5611.52}, {'field': 'total_amount', 'old_value': 63114.69, 'new_value': 65204.69}, {'field': 'order_count', 'old_value': 5395, 'new_value': 5544}]
2025-05-13 09:00:44,382 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-13 09:00:44,812 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-13 09:00:44,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48329.0, 'new_value': 49268.0}, {'field': 'total_amount', 'old_value': 48329.0, 'new_value': 49268.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-13 09:00:44,814 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-13 09:00:45,265 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-13 09:00:45,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40733.2, 'new_value': 42644.2}, {'field': 'total_amount', 'old_value': 40733.2, 'new_value': 42644.2}, {'field': 'order_count', 'old_value': 108, 'new_value': 113}]
2025-05-13 09:00:45,266 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-13 09:00:45,734 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-13 09:00:45,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 611.0}, {'field': 'offline_amount', 'old_value': 29659.0, 'new_value': 39837.0}, {'field': 'total_amount', 'old_value': 29659.0, 'new_value': 40448.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-05-13 09:00:45,735 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-13 09:00:46,208 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-13 09:00:46,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 417281.6, 'new_value': 432140.6}, {'field': 'total_amount', 'old_value': 417281.6, 'new_value': 432140.6}, {'field': 'order_count', 'old_value': 1045, 'new_value': 1103}]
2025-05-13 09:00:46,208 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-13 09:00:46,662 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-13 09:00:46,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 349052.0, 'new_value': 351689.0}, {'field': 'total_amount', 'old_value': 349052.0, 'new_value': 351689.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-05-13 09:00:46,663 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-13 09:00:47,178 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-13 09:00:47,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340258.0, 'new_value': 350528.0}, {'field': 'total_amount', 'old_value': 340698.0, 'new_value': 350968.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 152}]
2025-05-13 09:00:47,178 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-13 09:00:47,703 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-13 09:00:47,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148852.0, 'new_value': 149227.0}, {'field': 'total_amount', 'old_value': 148852.0, 'new_value': 149227.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-13 09:00:47,703 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-13 09:00:48,205 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-13 09:00:48,205 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 172171.22, 'new_value': 180859.22}, {'field': 'offline_amount', 'old_value': 5370.0, 'new_value': 5710.0}, {'field': 'total_amount', 'old_value': 177541.22, 'new_value': 186569.22}, {'field': 'order_count', 'old_value': 1344, 'new_value': 1425}]
2025-05-13 09:00:48,206 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-13 09:00:48,646 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-13 09:00:48,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 362276.0, 'new_value': 382276.0}, {'field': 'total_amount', 'old_value': 362276.0, 'new_value': 382276.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 96}]
2025-05-13 09:00:48,646 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-13 09:00:49,099 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-13 09:00:49,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45649.0, 'new_value': 46417.0}, {'field': 'total_amount', 'old_value': 45649.0, 'new_value': 46417.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 119}]
2025-05-13 09:00:49,100 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-13 09:00:49,490 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-13 09:00:49,491 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22417.0, 'new_value': 24384.82}, {'field': 'offline_amount', 'old_value': 165358.32, 'new_value': 173826.39}, {'field': 'total_amount', 'old_value': 187775.32, 'new_value': 198211.21}, {'field': 'order_count', 'old_value': 1144, 'new_value': 1225}]
2025-05-13 09:00:49,491 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-13 09:00:49,932 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-13 09:00:49,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52634.0, 'new_value': 53993.0}, {'field': 'total_amount', 'old_value': 52634.0, 'new_value': 53993.0}, {'field': 'order_count', 'old_value': 1563, 'new_value': 1602}]
2025-05-13 09:00:49,933 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-13 09:00:50,359 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-13 09:00:50,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78331.6, 'new_value': 82137.2}, {'field': 'total_amount', 'old_value': 78331.6, 'new_value': 82137.2}, {'field': 'order_count', 'old_value': 141, 'new_value': 149}]
2025-05-13 09:00:50,359 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-13 09:00:50,798 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-13 09:00:50,798 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59833.0, 'new_value': 61621.0}, {'field': 'offline_amount', 'old_value': 48006.74, 'new_value': 48425.24}, {'field': 'total_amount', 'old_value': 107839.74, 'new_value': 110046.24}, {'field': 'order_count', 'old_value': 698, 'new_value': 718}]
2025-05-13 09:00:50,798 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-13 09:00:51,276 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-13 09:00:51,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201107.4, 'new_value': 205054.1}, {'field': 'total_amount', 'old_value': 201107.4, 'new_value': 205054.1}, {'field': 'order_count', 'old_value': 240, 'new_value': 245}]
2025-05-13 09:00:51,277 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-13 09:00:51,693 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-13 09:00:51,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 487357.0, 'new_value': 522679.0}, {'field': 'total_amount', 'old_value': 487357.0, 'new_value': 522679.0}, {'field': 'order_count', 'old_value': 544, 'new_value': 584}]
2025-05-13 09:00:51,693 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-13 09:00:52,160 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-13 09:00:52,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12261.0, 'new_value': 13281.0}, {'field': 'total_amount', 'old_value': 12261.0, 'new_value': 13281.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-05-13 09:00:52,161 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-13 09:00:52,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-13 09:00:52,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62408.0, 'new_value': 66186.8}, {'field': 'offline_amount', 'old_value': 53083.9, 'new_value': 56692.2}, {'field': 'total_amount', 'old_value': 115491.9, 'new_value': 122879.0}, {'field': 'order_count', 'old_value': 2724, 'new_value': 2884}]
2025-05-13 09:00:52,602 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-13 09:00:53,007 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-13 09:00:53,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1330000.0, 'new_value': 1380000.0}, {'field': 'total_amount', 'old_value': 1330000.0, 'new_value': 1380000.0}, {'field': 'order_count', 'old_value': 268, 'new_value': 269}]
2025-05-13 09:00:53,007 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-13 09:00:53,612 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-13 09:00:53,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97604.3, 'new_value': 100859.8}, {'field': 'total_amount', 'old_value': 97604.3, 'new_value': 100859.8}, {'field': 'order_count', 'old_value': 1211, 'new_value': 1259}]
2025-05-13 09:00:53,613 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-13 09:00:54,149 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-13 09:00:54,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19807.43, 'new_value': 21437.74}, {'field': 'offline_amount', 'old_value': 572019.7, 'new_value': 599022.05}, {'field': 'total_amount', 'old_value': 591827.13, 'new_value': 620459.79}, {'field': 'order_count', 'old_value': 2566, 'new_value': 2748}]
2025-05-13 09:00:54,149 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-13 09:00:54,698 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-13 09:00:54,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25421.0, 'new_value': 27107.0}, {'field': 'total_amount', 'old_value': 25421.0, 'new_value': 27107.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 106}]
2025-05-13 09:00:54,698 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-13 09:00:55,213 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-13 09:00:55,213 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140134.55, 'new_value': 145882.55}, {'field': 'offline_amount', 'old_value': 86475.21, 'new_value': 89333.91}, {'field': 'total_amount', 'old_value': 226609.76, 'new_value': 235216.46}, {'field': 'order_count', 'old_value': 2004, 'new_value': 2105}]
2025-05-13 09:00:55,214 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-13 09:00:55,842 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-13 09:00:55,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430000.0, 'new_value': 435000.0}, {'field': 'total_amount', 'old_value': 430000.0, 'new_value': 435000.0}, {'field': 'order_count', 'old_value': 135, 'new_value': 136}]
2025-05-13 09:00:55,842 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-13 09:00:56,341 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-13 09:00:56,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 410000.0, 'new_value': 415000.0}, {'field': 'total_amount', 'old_value': 410000.0, 'new_value': 415000.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 135}]
2025-05-13 09:00:56,342 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-13 09:00:56,778 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-13 09:00:56,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2448674.0, 'new_value': 2498674.0}, {'field': 'total_amount', 'old_value': 2448674.0, 'new_value': 2498674.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 289}]
2025-05-13 09:00:56,779 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-13 09:00:57,226 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-13 09:00:57,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85243.0, 'new_value': 97643.0}, {'field': 'total_amount', 'old_value': 85243.0, 'new_value': 97643.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-13 09:00:57,227 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-13 09:00:57,628 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-13 09:00:57,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80183.1, 'new_value': 84039.9}, {'field': 'total_amount', 'old_value': 80183.1, 'new_value': 84039.9}, {'field': 'order_count', 'old_value': 373, 'new_value': 388}]
2025-05-13 09:00:57,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-13 09:00:58,053 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-13 09:00:58,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88437.4, 'new_value': 89769.1}, {'field': 'total_amount', 'old_value': 88437.4, 'new_value': 89769.1}, {'field': 'order_count', 'old_value': 2436, 'new_value': 2473}]
2025-05-13 09:00:58,053 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-13 09:00:58,483 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-13 09:00:58,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1168974.0, 'new_value': 1209877.0}, {'field': 'total_amount', 'old_value': 1168974.0, 'new_value': 1209877.0}, {'field': 'order_count', 'old_value': 4404, 'new_value': 4571}]
2025-05-13 09:00:58,484 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-13 09:00:58,909 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-13 09:00:58,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20984.52, 'new_value': 22604.15}, {'field': 'offline_amount', 'old_value': 27155.76, 'new_value': 27923.12}, {'field': 'total_amount', 'old_value': 48140.28, 'new_value': 50527.27}, {'field': 'order_count', 'old_value': 3663, 'new_value': 3839}]
2025-05-13 09:00:58,909 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-13 09:00:59,360 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-13 09:00:59,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229477.6, 'new_value': 233415.5}, {'field': 'total_amount', 'old_value': 229477.6, 'new_value': 233415.5}]
2025-05-13 09:00:59,360 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-13 09:00:59,818 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-13 09:00:59,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267859.0, 'new_value': 305497.0}, {'field': 'total_amount', 'old_value': 267859.0, 'new_value': 305497.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 41}]
2025-05-13 09:00:59,819 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-13 09:01:00,340 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-13 09:01:00,340 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 512.0, 'new_value': 605.0}, {'field': 'offline_amount', 'old_value': 30905.0, 'new_value': 32855.0}, {'field': 'total_amount', 'old_value': 31417.0, 'new_value': 33460.0}, {'field': 'order_count', 'old_value': 241, 'new_value': 254}]
2025-05-13 09:01:00,341 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-13 09:01:00,828 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-13 09:01:00,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150997.0, 'new_value': 161686.0}, {'field': 'total_amount', 'old_value': 150997.0, 'new_value': 161686.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 184}]
2025-05-13 09:01:00,828 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-13 09:01:01,337 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-13 09:01:01,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206771.6, 'new_value': 209647.0}, {'field': 'total_amount', 'old_value': 206771.6, 'new_value': 209647.0}, {'field': 'order_count', 'old_value': 4472, 'new_value': 4531}]
2025-05-13 09:01:01,337 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-13 09:01:01,786 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-13 09:01:01,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 415242.07, 'new_value': 436960.22}, {'field': 'total_amount', 'old_value': 415242.07, 'new_value': 436960.22}, {'field': 'order_count', 'old_value': 2151, 'new_value': 2241}]
2025-05-13 09:01:01,786 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-13 09:01:02,327 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-13 09:01:02,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73467.93, 'new_value': 76955.46}, {'field': 'total_amount', 'old_value': 73467.93, 'new_value': 76955.46}, {'field': 'order_count', 'old_value': 4980, 'new_value': 5233}]
2025-05-13 09:01:02,327 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-13 09:01:02,830 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-13 09:01:02,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311967.0, 'new_value': 317485.0}, {'field': 'total_amount', 'old_value': 311967.0, 'new_value': 317485.0}, {'field': 'order_count', 'old_value': 7059, 'new_value': 7186}]
2025-05-13 09:01:02,830 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-13 09:01:03,390 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-13 09:01:03,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54028.0, 'new_value': 55924.0}, {'field': 'total_amount', 'old_value': 54028.0, 'new_value': 55924.0}, {'field': 'order_count', 'old_value': 3678, 'new_value': 3831}]
2025-05-13 09:01:03,391 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-13 09:01:03,819 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-13 09:01:03,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32934.66, 'new_value': 35312.41}, {'field': 'total_amount', 'old_value': 32934.66, 'new_value': 35312.41}, {'field': 'order_count', 'old_value': 500, 'new_value': 532}]
2025-05-13 09:01:03,819 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-13 09:01:04,232 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-13 09:01:04,232 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-13 09:01:04,234 - INFO - 日期 2025-05 处理完成 - 更新: 74 条，插入: 0 条，错误: 0 条
2025-05-13 09:01:04,234 - INFO - 数据同步完成！更新: 74 条，插入: 0 条，错误: 0 条
2025-05-13 09:01:04,236 - INFO - =================同步完成====================
2025-05-13 12:00:01,887 - INFO - =================使用默认全量同步=============
2025-05-13 12:00:03,234 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-13 12:00:03,235 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 12:00:03,263 - INFO - 开始处理日期: 2025-01
2025-05-13 12:00:03,266 - INFO - Request Parameters - Page 1:
2025-05-13 12:00:03,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:03,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:04,187 - INFO - Response - Page 1:
2025-05-13 12:00:04,387 - INFO - 第 1 页获取到 100 条记录
2025-05-13 12:00:04,387 - INFO - Request Parameters - Page 2:
2025-05-13 12:00:04,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:04,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:05,313 - INFO - Response - Page 2:
2025-05-13 12:00:05,513 - INFO - 第 2 页获取到 100 条记录
2025-05-13 12:00:05,513 - INFO - Request Parameters - Page 3:
2025-05-13 12:00:05,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:05,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:06,087 - INFO - Response - Page 3:
2025-05-13 12:00:06,287 - INFO - 第 3 页获取到 100 条记录
2025-05-13 12:00:06,287 - INFO - Request Parameters - Page 4:
2025-05-13 12:00:06,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:06,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:06,763 - INFO - Response - Page 4:
2025-05-13 12:00:06,964 - INFO - 第 4 页获取到 100 条记录
2025-05-13 12:00:06,964 - INFO - Request Parameters - Page 5:
2025-05-13 12:00:06,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:06,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:07,502 - INFO - Response - Page 5:
2025-05-13 12:00:07,702 - INFO - 第 5 页获取到 100 条记录
2025-05-13 12:00:07,702 - INFO - Request Parameters - Page 6:
2025-05-13 12:00:07,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:07,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:08,154 - INFO - Response - Page 6:
2025-05-13 12:00:08,354 - INFO - 第 6 页获取到 100 条记录
2025-05-13 12:00:08,354 - INFO - Request Parameters - Page 7:
2025-05-13 12:00:08,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:08,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:08,864 - INFO - Response - Page 7:
2025-05-13 12:00:09,064 - INFO - 第 7 页获取到 82 条记录
2025-05-13 12:00:09,064 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 12:00:09,064 - INFO - 获取到 682 条表单数据
2025-05-13 12:00:09,076 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 12:00:09,087 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 12:00:09,087 - INFO - 开始处理日期: 2025-02
2025-05-13 12:00:09,087 - INFO - Request Parameters - Page 1:
2025-05-13 12:00:09,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:09,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:09,591 - INFO - Response - Page 1:
2025-05-13 12:00:09,791 - INFO - 第 1 页获取到 100 条记录
2025-05-13 12:00:09,791 - INFO - Request Parameters - Page 2:
2025-05-13 12:00:09,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:09,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:10,331 - INFO - Response - Page 2:
2025-05-13 12:00:10,531 - INFO - 第 2 页获取到 100 条记录
2025-05-13 12:00:10,531 - INFO - Request Parameters - Page 3:
2025-05-13 12:00:10,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:10,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:11,164 - INFO - Response - Page 3:
2025-05-13 12:00:11,364 - INFO - 第 3 页获取到 100 条记录
2025-05-13 12:00:11,364 - INFO - Request Parameters - Page 4:
2025-05-13 12:00:11,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:11,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:11,827 - INFO - Response - Page 4:
2025-05-13 12:00:12,027 - INFO - 第 4 页获取到 100 条记录
2025-05-13 12:00:12,027 - INFO - Request Parameters - Page 5:
2025-05-13 12:00:12,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:12,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:12,516 - INFO - Response - Page 5:
2025-05-13 12:00:12,717 - INFO - 第 5 页获取到 100 条记录
2025-05-13 12:00:12,717 - INFO - Request Parameters - Page 6:
2025-05-13 12:00:12,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:12,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:13,466 - INFO - Response - Page 6:
2025-05-13 12:00:13,666 - INFO - 第 6 页获取到 100 条记录
2025-05-13 12:00:13,666 - INFO - Request Parameters - Page 7:
2025-05-13 12:00:13,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:13,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:14,137 - INFO - Response - Page 7:
2025-05-13 12:00:14,338 - INFO - 第 7 页获取到 70 条记录
2025-05-13 12:00:14,338 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 12:00:14,338 - INFO - 获取到 670 条表单数据
2025-05-13 12:00:14,349 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 12:00:14,361 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 12:00:14,361 - INFO - 开始处理日期: 2025-03
2025-05-13 12:00:14,361 - INFO - Request Parameters - Page 1:
2025-05-13 12:00:14,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:14,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:14,901 - INFO - Response - Page 1:
2025-05-13 12:00:15,101 - INFO - 第 1 页获取到 100 条记录
2025-05-13 12:00:15,101 - INFO - Request Parameters - Page 2:
2025-05-13 12:00:15,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:15,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:15,542 - INFO - Response - Page 2:
2025-05-13 12:00:15,742 - INFO - 第 2 页获取到 100 条记录
2025-05-13 12:00:15,742 - INFO - Request Parameters - Page 3:
2025-05-13 12:00:15,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:15,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:16,217 - INFO - Response - Page 3:
2025-05-13 12:00:16,418 - INFO - 第 3 页获取到 100 条记录
2025-05-13 12:00:16,418 - INFO - Request Parameters - Page 4:
2025-05-13 12:00:16,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:16,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:16,920 - INFO - Response - Page 4:
2025-05-13 12:00:17,120 - INFO - 第 4 页获取到 100 条记录
2025-05-13 12:00:17,120 - INFO - Request Parameters - Page 5:
2025-05-13 12:00:17,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:17,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:17,578 - INFO - Response - Page 5:
2025-05-13 12:00:17,778 - INFO - 第 5 页获取到 100 条记录
2025-05-13 12:00:17,778 - INFO - Request Parameters - Page 6:
2025-05-13 12:00:17,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:17,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:18,434 - INFO - Response - Page 6:
2025-05-13 12:00:18,634 - INFO - 第 6 页获取到 100 条记录
2025-05-13 12:00:18,634 - INFO - Request Parameters - Page 7:
2025-05-13 12:00:18,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:18,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:19,110 - INFO - Response - Page 7:
2025-05-13 12:00:19,310 - INFO - 第 7 页获取到 61 条记录
2025-05-13 12:00:19,310 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 12:00:19,310 - INFO - 获取到 661 条表单数据
2025-05-13 12:00:19,322 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 12:00:19,333 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 12:00:19,333 - INFO - 开始处理日期: 2025-04
2025-05-13 12:00:19,333 - INFO - Request Parameters - Page 1:
2025-05-13 12:00:19,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:19,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:19,898 - INFO - Response - Page 1:
2025-05-13 12:00:20,098 - INFO - 第 1 页获取到 100 条记录
2025-05-13 12:00:20,098 - INFO - Request Parameters - Page 2:
2025-05-13 12:00:20,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:20,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:20,587 - INFO - Response - Page 2:
2025-05-13 12:00:20,787 - INFO - 第 2 页获取到 100 条记录
2025-05-13 12:00:20,787 - INFO - Request Parameters - Page 3:
2025-05-13 12:00:20,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:20,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:21,255 - INFO - Response - Page 3:
2025-05-13 12:00:21,455 - INFO - 第 3 页获取到 100 条记录
2025-05-13 12:00:21,455 - INFO - Request Parameters - Page 4:
2025-05-13 12:00:21,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:21,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:22,032 - INFO - Response - Page 4:
2025-05-13 12:00:22,232 - INFO - 第 4 页获取到 100 条记录
2025-05-13 12:00:22,232 - INFO - Request Parameters - Page 5:
2025-05-13 12:00:22,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:22,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:22,794 - INFO - Response - Page 5:
2025-05-13 12:00:22,994 - INFO - 第 5 页获取到 100 条记录
2025-05-13 12:00:22,994 - INFO - Request Parameters - Page 6:
2025-05-13 12:00:22,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:22,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:23,433 - INFO - Response - Page 6:
2025-05-13 12:00:23,633 - INFO - 第 6 页获取到 100 条记录
2025-05-13 12:00:23,633 - INFO - Request Parameters - Page 7:
2025-05-13 12:00:23,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:23,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:24,050 - INFO - Response - Page 7:
2025-05-13 12:00:24,251 - INFO - 第 7 页获取到 54 条记录
2025-05-13 12:00:24,251 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 12:00:24,251 - INFO - 获取到 654 条表单数据
2025-05-13 12:00:24,263 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 12:00:24,274 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-05-13 12:00:24,738 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9ME2
2025-05-13 12:00:24,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48720.3, 'new_value': 48969.3}, {'field': 'total_amount', 'old_value': 48720.3, 'new_value': 48969.3}]
2025-05-13 12:00:24,739 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-13 12:00:24,739 - INFO - 开始处理日期: 2025-05
2025-05-13 12:00:24,739 - INFO - Request Parameters - Page 1:
2025-05-13 12:00:24,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:24,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:25,267 - INFO - Response - Page 1:
2025-05-13 12:00:25,467 - INFO - 第 1 页获取到 100 条记录
2025-05-13 12:00:25,467 - INFO - Request Parameters - Page 2:
2025-05-13 12:00:25,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:25,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:25,938 - INFO - Response - Page 2:
2025-05-13 12:00:26,139 - INFO - 第 2 页获取到 100 条记录
2025-05-13 12:00:26,139 - INFO - Request Parameters - Page 3:
2025-05-13 12:00:26,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:26,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:26,586 - INFO - Response - Page 3:
2025-05-13 12:00:26,786 - INFO - 第 3 页获取到 100 条记录
2025-05-13 12:00:26,786 - INFO - Request Parameters - Page 4:
2025-05-13 12:00:26,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:26,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:27,320 - INFO - Response - Page 4:
2025-05-13 12:00:27,520 - INFO - 第 4 页获取到 100 条记录
2025-05-13 12:00:27,520 - INFO - Request Parameters - Page 5:
2025-05-13 12:00:27,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:27,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:28,047 - INFO - Response - Page 5:
2025-05-13 12:00:28,247 - INFO - 第 5 页获取到 100 条记录
2025-05-13 12:00:28,247 - INFO - Request Parameters - Page 6:
2025-05-13 12:00:28,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:28,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:28,794 - INFO - Response - Page 6:
2025-05-13 12:00:28,995 - INFO - 第 6 页获取到 100 条记录
2025-05-13 12:00:28,995 - INFO - Request Parameters - Page 7:
2025-05-13 12:00:28,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 12:00:28,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 12:00:29,375 - INFO - Response - Page 7:
2025-05-13 12:00:29,575 - INFO - 第 7 页获取到 24 条记录
2025-05-13 12:00:29,575 - INFO - 查询完成，共获取到 624 条记录
2025-05-13 12:00:29,575 - INFO - 获取到 624 条表单数据
2025-05-13 12:00:29,587 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-13 12:00:29,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-13 12:00:30,082 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-13 12:00:30,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167145.0, 'new_value': 175735.0}, {'field': 'total_amount', 'old_value': 167145.0, 'new_value': 175735.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 125}]
2025-05-13 12:00:30,083 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-13 12:00:30,558 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-13 12:00:30,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12985.0, 'new_value': 14840.0}, {'field': 'total_amount', 'old_value': 14575.0, 'new_value': 16430.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 62}]
2025-05-13 12:00:30,559 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-13 12:00:31,200 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-13 12:00:31,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118441.0, 'new_value': 131441.0}, {'field': 'total_amount', 'old_value': 118441.0, 'new_value': 131441.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-13 12:00:31,201 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-13 12:00:31,670 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-13 12:00:31,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 354385.08, 'new_value': 376638.98}, {'field': 'total_amount', 'old_value': 354385.08, 'new_value': 376638.98}, {'field': 'order_count', 'old_value': 1057, 'new_value': 1122}]
2025-05-13 12:00:31,670 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-13 12:00:32,147 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-13 12:00:32,147 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11568.32, 'new_value': 12844.92}, {'field': 'offline_amount', 'old_value': 6971.13, 'new_value': 7465.47}, {'field': 'total_amount', 'old_value': 18539.45, 'new_value': 20310.39}, {'field': 'order_count', 'old_value': 906, 'new_value': 1014}]
2025-05-13 12:00:32,147 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-13 12:00:32,597 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-13 12:00:32,598 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24287.88, 'new_value': 27868.64}, {'field': 'total_amount', 'old_value': 24287.88, 'new_value': 27868.64}, {'field': 'order_count', 'old_value': 57, 'new_value': 60}]
2025-05-13 12:00:32,598 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-13 12:00:33,101 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-13 12:00:33,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133040.0, 'new_value': 144020.0}, {'field': 'total_amount', 'old_value': 133040.0, 'new_value': 144020.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 76}]
2025-05-13 12:00:33,102 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-13 12:00:33,586 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-13 12:00:33,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 209649.0, 'new_value': 226206.0}, {'field': 'offline_amount', 'old_value': 120916.0, 'new_value': 127856.0}, {'field': 'total_amount', 'old_value': 330565.0, 'new_value': 354062.0}, {'field': 'order_count', 'old_value': 356, 'new_value': 382}]
2025-05-13 12:00:33,587 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-13 12:00:34,075 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-13 12:00:34,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53972.0, 'new_value': 54890.0}, {'field': 'total_amount', 'old_value': 53972.0, 'new_value': 54890.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-13 12:00:34,075 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-13 12:00:34,616 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-13 12:00:34,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59133.0, 'new_value': 62675.0}, {'field': 'offline_amount', 'old_value': 52322.28, 'new_value': 59511.28}, {'field': 'total_amount', 'old_value': 111455.28, 'new_value': 122186.28}, {'field': 'order_count', 'old_value': 2341, 'new_value': 2592}]
2025-05-13 12:00:34,617 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-13 12:00:35,097 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-13 12:00:35,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16560.0, 'new_value': 19520.0}, {'field': 'total_amount', 'old_value': 20680.0, 'new_value': 23640.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 233}]
2025-05-13 12:00:35,098 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-13 12:00:35,578 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-13 12:00:35,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25839.1, 'new_value': 25879.0}, {'field': 'total_amount', 'old_value': 29799.1, 'new_value': 29839.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 204}]
2025-05-13 12:00:35,578 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-13 12:00:36,120 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-13 12:00:36,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85493.0, 'new_value': 97621.0}, {'field': 'total_amount', 'old_value': 85493.0, 'new_value': 97621.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-13 12:00:36,120 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-13 12:00:36,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-13 12:00:36,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25621.07, 'new_value': 27771.13}, {'field': 'offline_amount', 'old_value': 50656.15, 'new_value': 55876.6}, {'field': 'total_amount', 'old_value': 76277.22, 'new_value': 83647.73}, {'field': 'order_count', 'old_value': 954, 'new_value': 1036}]
2025-05-13 12:00:36,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-13 12:00:36,974 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-13 12:00:36,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180526.3, 'new_value': 185570.7}, {'field': 'total_amount', 'old_value': 295546.0, 'new_value': 300590.4}, {'field': 'order_count', 'old_value': 1789, 'new_value': 1882}]
2025-05-13 12:00:36,974 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-13 12:00:37,438 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-13 12:00:37,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44545.0, 'new_value': 49289.0}, {'field': 'total_amount', 'old_value': 44545.0, 'new_value': 49289.0}, {'field': 'order_count', 'old_value': 2422, 'new_value': 2671}]
2025-05-13 12:00:37,438 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-13 12:00:37,868 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-13 12:00:37,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2061.93, 'new_value': 2361.78}, {'field': 'offline_amount', 'old_value': 36641.21, 'new_value': 43643.57}, {'field': 'total_amount', 'old_value': 38703.14, 'new_value': 46005.35}, {'field': 'order_count', 'old_value': 1456, 'new_value': 1734}]
2025-05-13 12:00:37,868 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-13 12:00:38,280 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-13 12:00:38,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60629.02, 'new_value': 67703.99}, {'field': 'total_amount', 'old_value': 60629.02, 'new_value': 67703.99}, {'field': 'order_count', 'old_value': 669, 'new_value': 755}]
2025-05-13 12:00:38,280 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-13 12:00:38,766 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-13 12:00:38,766 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4794.27, 'new_value': 6163.66}, {'field': 'offline_amount', 'old_value': 15355.47, 'new_value': 20431.28}, {'field': 'total_amount', 'old_value': 20149.74, 'new_value': 26594.94}, {'field': 'order_count', 'old_value': 329, 'new_value': 453}]
2025-05-13 12:00:38,767 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-13 12:00:39,255 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-13 12:00:39,255 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 1305, 'new_value': 1310}]
2025-05-13 12:00:39,255 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-13 12:00:39,732 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-13 12:00:39,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131750.0, 'new_value': 143750.0}, {'field': 'total_amount', 'old_value': 131750.0, 'new_value': 143750.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 23}]
2025-05-13 12:00:39,732 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-13 12:00:40,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-13 12:00:40,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147745.89, 'new_value': 185745.89}, {'field': 'total_amount', 'old_value': 147745.89, 'new_value': 185745.89}, {'field': 'order_count', 'old_value': 26, 'new_value': 37}]
2025-05-13 12:00:40,229 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-13 12:00:40,675 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-13 12:00:40,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117690.13, 'new_value': 151690.13}, {'field': 'total_amount', 'old_value': 157050.13, 'new_value': 191050.13}, {'field': 'order_count', 'old_value': 22, 'new_value': 30}]
2025-05-13 12:00:40,675 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-13 12:00:41,142 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-13 12:00:41,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387481.2, 'new_value': 411020.2}, {'field': 'total_amount', 'old_value': 388738.6, 'new_value': 412277.6}, {'field': 'order_count', 'old_value': 45, 'new_value': 47}]
2025-05-13 12:00:41,143 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-13 12:00:41,606 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-13 12:00:41,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14331.0, 'new_value': 14530.8}, {'field': 'total_amount', 'old_value': 44732.6, 'new_value': 44932.4}, {'field': 'order_count', 'old_value': 561, 'new_value': 563}]
2025-05-13 12:00:41,606 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-13 12:00:42,059 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-13 12:00:42,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44795.0, 'new_value': 49395.0}, {'field': 'total_amount', 'old_value': 72375.0, 'new_value': 76975.0}, {'field': 'order_count', 'old_value': 872, 'new_value': 935}]
2025-05-13 12:00:42,060 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-13 12:00:42,503 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-13 12:00:42,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3270.0, 'new_value': 3611.0}, {'field': 'total_amount', 'old_value': 14481.0, 'new_value': 14822.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-05-13 12:00:42,503 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-13 12:00:43,004 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-13 12:00:43,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90520.0, 'new_value': 96310.0}, {'field': 'total_amount', 'old_value': 90520.0, 'new_value': 96310.0}, {'field': 'order_count', 'old_value': 164, 'new_value': 179}]
2025-05-13 12:00:43,005 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-13 12:00:43,375 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-13 12:00:43,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47423.16, 'new_value': 54257.67}, {'field': 'total_amount', 'old_value': 47423.16, 'new_value': 54257.67}, {'field': 'order_count', 'old_value': 1704, 'new_value': 1867}]
2025-05-13 12:00:43,375 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-13 12:00:43,824 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-13 12:00:43,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8025.0, 'new_value': 8545.0}, {'field': 'total_amount', 'old_value': 8025.0, 'new_value': 8545.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-13 12:00:43,825 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-13 12:00:44,268 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-13 12:00:44,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20033.44, 'new_value': 26193.27}, {'field': 'total_amount', 'old_value': 69457.03, 'new_value': 75616.86}, {'field': 'order_count', 'old_value': 3996, 'new_value': 4349}]
2025-05-13 12:00:44,269 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-13 12:00:44,745 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-13 12:00:44,745 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97650.24, 'new_value': 99877.38}, {'field': 'offline_amount', 'old_value': 16871.24, 'new_value': 17328.0}, {'field': 'total_amount', 'old_value': 114521.48, 'new_value': 117205.38}, {'field': 'order_count', 'old_value': 404, 'new_value': 415}]
2025-05-13 12:00:44,745 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-13 12:00:45,208 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-13 12:00:45,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99430.0, 'new_value': 102619.0}, {'field': 'offline_amount', 'old_value': 34787.66, 'new_value': 35927.66}, {'field': 'total_amount', 'old_value': 134217.66, 'new_value': 138546.66}, {'field': 'order_count', 'old_value': 817, 'new_value': 837}]
2025-05-13 12:00:45,208 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-13 12:00:45,774 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-13 12:00:45,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19182.51, 'new_value': 21208.4}, {'field': 'offline_amount', 'old_value': 21993.7, 'new_value': 28000.62}, {'field': 'total_amount', 'old_value': 41176.21, 'new_value': 49209.02}, {'field': 'order_count', 'old_value': 2062, 'new_value': 2294}]
2025-05-13 12:00:45,774 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-13 12:00:46,166 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-13 12:00:46,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4013.0, 'new_value': 4211.0}, {'field': 'total_amount', 'old_value': 5131.0, 'new_value': 5329.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 99}]
2025-05-13 12:00:46,166 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-13 12:00:46,605 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-13 12:00:46,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39779.04, 'new_value': 43668.56}, {'field': 'total_amount', 'old_value': 39779.04, 'new_value': 43668.56}, {'field': 'order_count', 'old_value': 1016, 'new_value': 1122}]
2025-05-13 12:00:46,606 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-13 12:00:47,067 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-13 12:00:47,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88442.0, 'new_value': 100445.0}, {'field': 'total_amount', 'old_value': 88442.0, 'new_value': 100445.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-05-13 12:00:47,068 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-13 12:00:47,581 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-13 12:00:47,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77192.0, 'new_value': 80467.0}, {'field': 'offline_amount', 'old_value': 33394.77, 'new_value': 34797.14}, {'field': 'total_amount', 'old_value': 110586.77, 'new_value': 115264.14}, {'field': 'order_count', 'old_value': 762, 'new_value': 805}]
2025-05-13 12:00:47,581 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-13 12:00:48,007 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-13 12:00:48,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18437.34, 'new_value': 24737.75}, {'field': 'offline_amount', 'old_value': 174221.34, 'new_value': 257344.26}, {'field': 'total_amount', 'old_value': 192658.68, 'new_value': 282082.01}, {'field': 'order_count', 'old_value': 616, 'new_value': 882}]
2025-05-13 12:00:48,008 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-13 12:00:48,632 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-13 12:00:48,632 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4336.8, 'new_value': 5175.98}, {'field': 'offline_amount', 'old_value': 55102.78, 'new_value': 64408.98}, {'field': 'total_amount', 'old_value': 59439.58, 'new_value': 69584.96}, {'field': 'order_count', 'old_value': 916, 'new_value': 1085}]
2025-05-13 12:00:48,632 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-13 12:00:49,021 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-13 12:00:49,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5553.78, 'new_value': 5791.84}, {'field': 'offline_amount', 'old_value': 81582.99, 'new_value': 89537.26}, {'field': 'total_amount', 'old_value': 87136.77, 'new_value': 95329.1}, {'field': 'order_count', 'old_value': 960, 'new_value': 1050}]
2025-05-13 12:00:49,021 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-13 12:00:49,620 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-13 12:00:49,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30852.0, 'new_value': 32274.0}, {'field': 'total_amount', 'old_value': 64242.0, 'new_value': 65664.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-13 12:00:49,621 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-13 12:00:50,080 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-13 12:00:50,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62714.0, 'new_value': 68883.0}, {'field': 'total_amount', 'old_value': 62714.0, 'new_value': 68883.0}, {'field': 'order_count', 'old_value': 1513, 'new_value': 1642}]
2025-05-13 12:00:50,080 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-13 12:00:50,569 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-13 12:00:50,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7142.85, 'new_value': 7949.65}, {'field': 'total_amount', 'old_value': 7142.85, 'new_value': 7949.65}, {'field': 'order_count', 'old_value': 114, 'new_value': 124}]
2025-05-13 12:00:50,569 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-13 12:00:51,066 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-13 12:00:51,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38857.46, 'new_value': 41900.12}, {'field': 'offline_amount', 'old_value': 426538.01, 'new_value': 455012.01}, {'field': 'total_amount', 'old_value': 465395.47, 'new_value': 496912.13}, {'field': 'order_count', 'old_value': 1532, 'new_value': 1624}]
2025-05-13 12:00:51,067 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-13 12:00:51,564 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-13 12:00:51,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8011.0, 'new_value': 13876.0}, {'field': 'total_amount', 'old_value': 8011.0, 'new_value': 13876.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 26}]
2025-05-13 12:00:51,564 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-13 12:00:52,026 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-13 12:00:52,026 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12507.49, 'new_value': 13070.03}, {'field': 'total_amount', 'old_value': 12507.49, 'new_value': 13070.03}, {'field': 'order_count', 'old_value': 52, 'new_value': 56}]
2025-05-13 12:00:52,026 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-13 12:00:52,491 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-13 12:00:52,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67950.0, 'new_value': 75706.0}, {'field': 'total_amount', 'old_value': 67950.0, 'new_value': 75706.0}, {'field': 'order_count', 'old_value': 2442, 'new_value': 2755}]
2025-05-13 12:00:52,491 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-13 12:00:52,949 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-13 12:00:52,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332111.47, 'new_value': 357097.78}, {'field': 'total_amount', 'old_value': 332111.47, 'new_value': 357097.78}, {'field': 'order_count', 'old_value': 2369, 'new_value': 2575}]
2025-05-13 12:00:52,949 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-13 12:00:53,428 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-13 12:00:53,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16103.0, 'new_value': 14603.0}, {'field': 'offline_amount', 'old_value': 229047.0, 'new_value': 259950.0}, {'field': 'total_amount', 'old_value': 245150.0, 'new_value': 274553.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 46}]
2025-05-13 12:00:53,428 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-13 12:00:53,923 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-13 12:00:53,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56606.4, 'new_value': 63542.44}, {'field': 'total_amount', 'old_value': 56606.4, 'new_value': 63542.44}, {'field': 'order_count', 'old_value': 2102, 'new_value': 2350}]
2025-05-13 12:00:53,923 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-13 12:00:54,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-13 12:00:54,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12255.18, 'new_value': 12518.58}, {'field': 'total_amount', 'old_value': 12255.18, 'new_value': 12518.58}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-13 12:00:54,391 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-13 12:00:54,773 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-13 12:00:54,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76977.03, 'new_value': 110408.96}, {'field': 'total_amount', 'old_value': 77709.03, 'new_value': 111140.96}, {'field': 'order_count', 'old_value': 907, 'new_value': 1280}]
2025-05-13 12:00:54,773 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-13 12:00:55,276 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-13 12:00:55,277 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45914.0, 'new_value': 46359.0}, {'field': 'total_amount', 'old_value': 45914.0, 'new_value': 46359.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 75}]
2025-05-13 12:00:55,277 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-13 12:00:55,739 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-13 12:00:55,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29105.28, 'new_value': 32240.28}, {'field': 'offline_amount', 'old_value': 189687.3, 'new_value': 207133.3}, {'field': 'total_amount', 'old_value': 218792.58, 'new_value': 239373.58}, {'field': 'order_count', 'old_value': 364, 'new_value': 394}]
2025-05-13 12:00:55,739 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-13 12:00:56,269 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-13 12:00:56,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82727.83, 'new_value': 88175.71}, {'field': 'total_amount', 'old_value': 82727.83, 'new_value': 88175.71}, {'field': 'order_count', 'old_value': 420, 'new_value': 460}]
2025-05-13 12:00:56,269 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-13 12:00:56,717 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-13 12:00:56,717 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87030.28, 'new_value': 91749.54}, {'field': 'offline_amount', 'old_value': 256687.59, 'new_value': 264341.18}, {'field': 'total_amount', 'old_value': 343717.87, 'new_value': 356090.72}, {'field': 'order_count', 'old_value': 2271, 'new_value': 2375}]
2025-05-13 12:00:56,718 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-13 12:00:57,211 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-13 12:00:57,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17407.15, 'new_value': 17413.15}, {'field': 'total_amount', 'old_value': 17472.7, 'new_value': 17478.7}, {'field': 'order_count', 'old_value': 171, 'new_value': 172}]
2025-05-13 12:00:57,211 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-13 12:00:57,671 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-13 12:00:57,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68795.0, 'new_value': 76889.0}, {'field': 'total_amount', 'old_value': 68795.0, 'new_value': 76889.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 17}]
2025-05-13 12:00:57,671 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-13 12:00:58,172 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-13 12:00:58,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29385.96, 'new_value': 37165.04}, {'field': 'total_amount', 'old_value': 29385.96, 'new_value': 37165.04}, {'field': 'order_count', 'old_value': 1333, 'new_value': 1492}]
2025-05-13 12:00:58,172 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-13 12:00:58,651 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-13 12:00:58,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 53445.1}, {'field': 'total_amount', 'old_value': 976749.1, 'new_value': 1030194.2}, {'field': 'order_count', 'old_value': 1664, 'new_value': 1759}]
2025-05-13 12:00:58,652 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-13 12:00:59,126 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-13 12:00:59,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40687.0, 'new_value': 46203.0}, {'field': 'total_amount', 'old_value': 108509.65, 'new_value': 114025.65}, {'field': 'order_count', 'old_value': 587, 'new_value': 616}]
2025-05-13 12:00:59,126 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-13 12:00:59,719 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-13 12:00:59,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22947.9, 'new_value': 23622.9}, {'field': 'total_amount', 'old_value': 22947.9, 'new_value': 23622.9}, {'field': 'order_count', 'old_value': 102, 'new_value': 105}]
2025-05-13 12:00:59,719 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-13 12:01:00,171 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-13 12:01:00,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21066.8, 'new_value': 22063.8}, {'field': 'total_amount', 'old_value': 21066.8, 'new_value': 22063.8}, {'field': 'order_count', 'old_value': 88, 'new_value': 94}]
2025-05-13 12:01:00,172 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-13 12:01:00,643 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-13 12:01:00,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85348.0, 'new_value': 92723.0}, {'field': 'total_amount', 'old_value': 85348.0, 'new_value': 92723.0}, {'field': 'order_count', 'old_value': 8982, 'new_value': 9142}]
2025-05-13 12:01:00,644 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-13 12:01:01,084 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-13 12:01:01,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23594.0, 'new_value': 27719.0}, {'field': 'total_amount', 'old_value': 52240.0, 'new_value': 56365.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-13 12:01:01,084 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-13 12:01:01,521 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-13 12:01:01,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68985.0, 'new_value': 73226.0}, {'field': 'total_amount', 'old_value': 68986.0, 'new_value': 73227.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-13 12:01:01,522 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-13 12:01:02,028 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-13 12:01:02,029 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2889.86, 'new_value': 3149.06}, {'field': 'offline_amount', 'old_value': 7471.48, 'new_value': 7825.07}, {'field': 'total_amount', 'old_value': 10361.34, 'new_value': 10974.13}, {'field': 'order_count', 'old_value': 357, 'new_value': 384}]
2025-05-13 12:01:02,029 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-13 12:01:02,535 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-13 12:01:02,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76383.05, 'new_value': 84490.32}, {'field': 'offline_amount', 'old_value': 58725.76, 'new_value': 64266.26}, {'field': 'total_amount', 'old_value': 135108.81, 'new_value': 148756.58}, {'field': 'order_count', 'old_value': 1166, 'new_value': 1279}]
2025-05-13 12:01:02,536 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-13 12:01:03,074 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-13 12:01:03,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28213.26, 'new_value': 28913.26}, {'field': 'total_amount', 'old_value': 28216.56, 'new_value': 28916.56}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-13 12:01:03,075 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-13 12:01:03,611 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-13 12:01:03,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17745.0, 'new_value': 18718.0}, {'field': 'total_amount', 'old_value': 17745.0, 'new_value': 18718.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-05-13 12:01:03,611 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-13 12:01:04,166 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-13 12:01:04,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30051.0, 'new_value': 34462.0}, {'field': 'total_amount', 'old_value': 30051.0, 'new_value': 34462.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-13 12:01:04,166 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-13 12:01:04,611 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-13 12:01:04,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86760.0, 'new_value': 91139.0}, {'field': 'total_amount', 'old_value': 94335.8, 'new_value': 98714.8}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-13 12:01:04,612 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-13 12:01:05,116 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-13 12:01:05,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49869.71, 'new_value': 53535.41}, {'field': 'total_amount', 'old_value': 70463.54, 'new_value': 74129.24}, {'field': 'order_count', 'old_value': 2515, 'new_value': 2629}]
2025-05-13 12:01:05,116 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-13 12:01:05,579 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-13 12:01:05,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56893.39, 'new_value': 56993.39}, {'field': 'total_amount', 'old_value': 60662.49, 'new_value': 60762.49}, {'field': 'order_count', 'old_value': 311, 'new_value': 312}]
2025-05-13 12:01:05,580 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-13 12:01:06,183 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-13 12:01:06,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19446.0, 'new_value': 24459.0}, {'field': 'offline_amount', 'old_value': 89729.0, 'new_value': 92742.0}, {'field': 'total_amount', 'old_value': 109175.0, 'new_value': 117201.0}, {'field': 'order_count', 'old_value': 2201, 'new_value': 2401}]
2025-05-13 12:01:06,183 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-13 12:01:06,625 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-13 12:01:06,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7062.58, 'new_value': 7698.03}, {'field': 'offline_amount', 'old_value': 137861.8, 'new_value': 143663.7}, {'field': 'total_amount', 'old_value': 144924.38, 'new_value': 151361.73}, {'field': 'order_count', 'old_value': 1000, 'new_value': 1053}]
2025-05-13 12:01:06,625 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-13 12:01:07,086 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-13 12:01:07,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47781.0, 'new_value': 48057.0}, {'field': 'total_amount', 'old_value': 52922.0, 'new_value': 53198.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-13 12:01:07,086 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-13 12:01:07,531 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-13 12:01:07,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26000.0, 'new_value': 26128.0}, {'field': 'offline_amount', 'old_value': 72082.0, 'new_value': 76261.0}, {'field': 'total_amount', 'old_value': 98082.0, 'new_value': 102389.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 44}]
2025-05-13 12:01:07,532 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-13 12:01:08,072 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-13 12:01:08,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71745.79, 'new_value': 75268.46}, {'field': 'offline_amount', 'old_value': 69238.45, 'new_value': 70704.45}, {'field': 'total_amount', 'old_value': 140984.24, 'new_value': 145972.91}, {'field': 'order_count', 'old_value': 1359, 'new_value': 1420}]
2025-05-13 12:01:08,072 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-13 12:01:08,547 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-13 12:01:08,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 430295.04, 'new_value': 430418.04}, {'field': 'offline_amount', 'old_value': 139265.9, 'new_value': 140255.9}, {'field': 'total_amount', 'old_value': 569560.94, 'new_value': 570673.94}, {'field': 'order_count', 'old_value': 5383, 'new_value': 5388}]
2025-05-13 12:01:08,548 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-13 12:01:09,031 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-13 12:01:09,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49.0, 'new_value': 118.0}, {'field': 'total_amount', 'old_value': 25631.6, 'new_value': 25700.6}, {'field': 'order_count', 'old_value': 102, 'new_value': 104}]
2025-05-13 12:01:09,032 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-13 12:01:09,442 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-13 12:01:09,442 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32423.8, 'new_value': 38423.8}, {'field': 'offline_amount', 'old_value': 1188.35, 'new_value': 1934.05}, {'field': 'total_amount', 'old_value': 33612.15, 'new_value': 40357.85}, {'field': 'order_count', 'old_value': 89, 'new_value': 108}]
2025-05-13 12:01:09,442 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-13 12:01:09,959 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-13 12:01:09,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37795.98, 'new_value': 39312.08}, {'field': 'total_amount', 'old_value': 37795.98, 'new_value': 39312.08}, {'field': 'order_count', 'old_value': 1014, 'new_value': 1066}]
2025-05-13 12:01:09,959 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-13 12:01:10,471 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-13 12:01:10,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4625.4, 'new_value': 4992.4}, {'field': 'offline_amount', 'old_value': 24679.0, 'new_value': 26579.0}, {'field': 'total_amount', 'old_value': 29304.4, 'new_value': 31571.4}, {'field': 'order_count', 'old_value': 31, 'new_value': 34}]
2025-05-13 12:01:10,472 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-13 12:01:10,995 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-13 12:01:10,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130742.0, 'new_value': 182722.0}, {'field': 'total_amount', 'old_value': 130742.0, 'new_value': 182722.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 24}]
2025-05-13 12:01:10,996 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-13 12:01:11,470 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-13 12:01:11,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155229.65, 'new_value': 190258.51}, {'field': 'total_amount', 'old_value': 155229.65, 'new_value': 190258.51}, {'field': 'order_count', 'old_value': 286, 'new_value': 319}]
2025-05-13 12:01:11,470 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-13 12:01:11,975 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-13 12:01:11,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 432589.0, 'new_value': 456527.0}, {'field': 'total_amount', 'old_value': 432589.0, 'new_value': 456527.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 79}]
2025-05-13 12:01:11,975 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-13 12:01:12,457 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-13 12:01:12,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42409.0, 'new_value': 44534.0}, {'field': 'offline_amount', 'old_value': 32946.12, 'new_value': 38454.12}, {'field': 'total_amount', 'old_value': 75355.12, 'new_value': 82988.12}, {'field': 'order_count', 'old_value': 91, 'new_value': 96}]
2025-05-13 12:01:12,457 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-13 12:01:12,997 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-13 12:01:12,997 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85366.2, 'new_value': 91117.8}, {'field': 'total_amount', 'old_value': 85366.2, 'new_value': 91117.8}, {'field': 'order_count', 'old_value': 189, 'new_value': 201}]
2025-05-13 12:01:12,998 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-13 12:01:13,459 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-13 12:01:13,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53869.03, 'new_value': 55972.0}, {'field': 'total_amount', 'old_value': 53869.03, 'new_value': 55972.0}, {'field': 'order_count', 'old_value': 2060, 'new_value': 2134}]
2025-05-13 12:01:13,459 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-13 12:01:13,900 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-13 12:01:13,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27390.0, 'new_value': 28130.0}, {'field': 'total_amount', 'old_value': 27390.0, 'new_value': 28130.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-13 12:01:13,900 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-13 12:01:14,371 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-13 12:01:14,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24544.0, 'new_value': 25661.0}, {'field': 'total_amount', 'old_value': 24544.0, 'new_value': 25661.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-05-13 12:01:14,371 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-13 12:01:14,823 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-13 12:01:14,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67416.21, 'new_value': 70289.46}, {'field': 'total_amount', 'old_value': 67416.21, 'new_value': 70289.46}, {'field': 'order_count', 'old_value': 442, 'new_value': 454}]
2025-05-13 12:01:14,824 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-13 12:01:15,254 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-13 12:01:15,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43404.98, 'new_value': 46112.98}, {'field': 'total_amount', 'old_value': 43404.98, 'new_value': 46112.98}, {'field': 'order_count', 'old_value': 238, 'new_value': 251}]
2025-05-13 12:01:15,254 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-13 12:01:15,718 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-13 12:01:15,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 853.9, 'new_value': 923.35}, {'field': 'offline_amount', 'old_value': 10710.24, 'new_value': 13947.12}, {'field': 'total_amount', 'old_value': 11564.14, 'new_value': 14870.47}, {'field': 'order_count', 'old_value': 512, 'new_value': 626}]
2025-05-13 12:01:15,718 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-13 12:01:16,223 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-13 12:01:16,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57935.2, 'new_value': 59137.4}, {'field': 'total_amount', 'old_value': 57935.2, 'new_value': 59137.4}, {'field': 'order_count', 'old_value': 170, 'new_value': 176}]
2025-05-13 12:01:16,223 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-13 12:01:16,660 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-13 12:01:16,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3668.7, 'new_value': 4307.66}, {'field': 'offline_amount', 'old_value': 17622.0, 'new_value': 19267.0}, {'field': 'total_amount', 'old_value': 21290.7, 'new_value': 23574.66}, {'field': 'order_count', 'old_value': 98, 'new_value': 109}]
2025-05-13 12:01:16,660 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-13 12:01:17,125 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-13 12:01:17,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 855328.94, 'new_value': 858464.94}, {'field': 'total_amount', 'old_value': 855328.94, 'new_value': 858464.94}, {'field': 'order_count', 'old_value': 531, 'new_value': 533}]
2025-05-13 12:01:17,125 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-13 12:01:17,601 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-13 12:01:17,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22409.0, 'new_value': 22966.0}, {'field': 'total_amount', 'old_value': 22409.0, 'new_value': 22966.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-05-13 12:01:17,601 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-13 12:01:18,082 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-13 12:01:18,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24239.02, 'new_value': 25602.62}, {'field': 'total_amount', 'old_value': 24435.82, 'new_value': 25799.42}, {'field': 'order_count', 'old_value': 216, 'new_value': 225}]
2025-05-13 12:01:18,083 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-13 12:01:18,516 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-13 12:01:18,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2421.0, 'new_value': 2568.0}, {'field': 'offline_amount', 'old_value': 11861.6, 'new_value': 12754.1}, {'field': 'total_amount', 'old_value': 14282.6, 'new_value': 15322.1}, {'field': 'order_count', 'old_value': 576, 'new_value': 621}]
2025-05-13 12:01:18,516 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-13 12:01:18,983 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-13 12:01:18,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52400.02, 'new_value': 54837.02}, {'field': 'total_amount', 'old_value': 52400.02, 'new_value': 54837.02}, {'field': 'order_count', 'old_value': 169, 'new_value': 181}]
2025-05-13 12:01:18,983 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-13 12:01:19,457 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-13 12:01:19,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 548000.0, 'new_value': 628000.0}, {'field': 'total_amount', 'old_value': 548000.0, 'new_value': 628000.0}, {'field': 'order_count', 'old_value': 329, 'new_value': 331}]
2025-05-13 12:01:19,458 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-13 12:01:19,999 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-13 12:01:19,999 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-13 12:01:19,999 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-13 12:01:20,453 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-13 12:01:20,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28349.0, 'new_value': 30229.0}, {'field': 'total_amount', 'old_value': 28349.0, 'new_value': 30229.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-13 12:01:20,453 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-13 12:01:20,975 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-13 12:01:20,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5915.6, 'new_value': 8903.3}, {'field': 'offline_amount', 'old_value': 10332.53, 'new_value': 15423.01}, {'field': 'total_amount', 'old_value': 16248.13, 'new_value': 24326.31}, {'field': 'order_count', 'old_value': 683, 'new_value': 1004}]
2025-05-13 12:01:20,975 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-13 12:01:21,424 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-13 12:01:21,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29997.0, 'new_value': 40827.0}, {'field': 'total_amount', 'old_value': 29997.0, 'new_value': 40827.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-13 12:01:21,425 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-13 12:01:21,897 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-13 12:01:21,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100828.6, 'new_value': 106956.9}, {'field': 'total_amount', 'old_value': 100828.6, 'new_value': 106956.9}, {'field': 'order_count', 'old_value': 357, 'new_value': 381}]
2025-05-13 12:01:21,897 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-13 12:01:22,410 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-13 12:01:22,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350141.0, 'new_value': 450394.16}, {'field': 'total_amount', 'old_value': 350141.0, 'new_value': 450394.16}, {'field': 'order_count', 'old_value': 1386, 'new_value': 1651}]
2025-05-13 12:01:22,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-13 12:01:22,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-13 12:01:22,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29796.0, 'new_value': 30282.0}, {'field': 'total_amount', 'old_value': 30690.0, 'new_value': 31176.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 122}]
2025-05-13 12:01:22,876 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-13 12:01:23,352 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-13 12:01:23,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26215.9, 'new_value': 28115.9}, {'field': 'total_amount', 'old_value': 26215.9, 'new_value': 28115.9}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-13 12:01:23,352 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-13 12:01:23,756 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-13 12:01:23,756 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17316.5, 'new_value': 14872.0}, {'field': 'offline_amount', 'old_value': 138751.7, 'new_value': 141072.9}, {'field': 'total_amount', 'old_value': 156068.2, 'new_value': 155944.9}, {'field': 'order_count', 'old_value': 1156, 'new_value': 1164}]
2025-05-13 12:01:23,756 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-13 12:01:24,242 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-13 12:01:24,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127635.69, 'new_value': 139269.93}, {'field': 'total_amount', 'old_value': 127635.69, 'new_value': 139269.93}, {'field': 'order_count', 'old_value': 3381, 'new_value': 3708}]
2025-05-13 12:01:24,242 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-13 12:01:24,806 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-13 12:01:24,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 909521.63, 'new_value': 957510.58}, {'field': 'total_amount', 'old_value': 909521.63, 'new_value': 957510.58}, {'field': 'order_count', 'old_value': 6724, 'new_value': 7174}]
2025-05-13 12:01:24,807 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-13 12:01:25,239 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-13 12:01:25,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19530.0, 'new_value': 20530.0}, {'field': 'total_amount', 'old_value': 19530.0, 'new_value': 20530.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-13 12:01:25,240 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-13 12:01:25,751 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-13 12:01:25,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10432.52, 'new_value': 11266.05}, {'field': 'offline_amount', 'old_value': 18622.46, 'new_value': 20416.46}, {'field': 'total_amount', 'old_value': 29054.98, 'new_value': 31682.51}, {'field': 'order_count', 'old_value': 1048, 'new_value': 1141}]
2025-05-13 12:01:25,751 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-13 12:01:26,191 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-13 12:01:26,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31438.0, 'new_value': 33689.0}, {'field': 'total_amount', 'old_value': 33846.0, 'new_value': 36097.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 142}]
2025-05-13 12:01:26,191 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-13 12:01:26,623 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-13 12:01:26,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36485.0, 'new_value': 37825.0}, {'field': 'total_amount', 'old_value': 36485.0, 'new_value': 37825.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 12:01:26,623 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-13 12:01:27,101 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-13 12:01:27,101 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13581.7, 'new_value': 13805.7}, {'field': 'offline_amount', 'old_value': 30490.7, 'new_value': 31873.04}, {'field': 'total_amount', 'old_value': 44072.4, 'new_value': 45678.74}, {'field': 'order_count', 'old_value': 482, 'new_value': 507}]
2025-05-13 12:01:27,101 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-13 12:01:27,583 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-13 12:01:27,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50805.5, 'new_value': 54180.0}, {'field': 'offline_amount', 'old_value': 58530.63, 'new_value': 64560.63}, {'field': 'total_amount', 'old_value': 109336.13, 'new_value': 118740.63}, {'field': 'order_count', 'old_value': 741, 'new_value': 827}]
2025-05-13 12:01:27,584 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-13 12:01:28,164 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-13 12:01:28,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4829.0, 'new_value': 6509.0}, {'field': 'total_amount', 'old_value': 4829.0, 'new_value': 6509.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-13 12:01:28,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-13 12:01:28,628 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-13 12:01:28,629 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7885.89, 'new_value': 8827.24}, {'field': 'offline_amount', 'old_value': 16567.77, 'new_value': 18904.69}, {'field': 'total_amount', 'old_value': 24453.66, 'new_value': 27731.93}, {'field': 'order_count', 'old_value': 1290, 'new_value': 1457}]
2025-05-13 12:01:28,629 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-13 12:01:29,099 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-13 12:01:29,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68377.4, 'new_value': 77895.4}, {'field': 'total_amount', 'old_value': 68377.4, 'new_value': 77895.4}, {'field': 'order_count', 'old_value': 694, 'new_value': 756}]
2025-05-13 12:01:29,099 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-13 12:01:29,589 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-13 12:01:29,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1786.9}, {'field': 'total_amount', 'old_value': 18948.2, 'new_value': 20735.1}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 12:01:29,590 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-13 12:01:30,060 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-13 12:01:30,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3422.93, 'new_value': 3638.42}, {'field': 'offline_amount', 'old_value': 7235.94, 'new_value': 7481.74}, {'field': 'total_amount', 'old_value': 10658.87, 'new_value': 11120.16}, {'field': 'order_count', 'old_value': 786, 'new_value': 824}]
2025-05-13 12:01:30,061 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-13 12:01:30,565 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-13 12:01:30,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25845.0, 'new_value': 27065.0}, {'field': 'total_amount', 'old_value': 26194.0, 'new_value': 27414.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-05-13 12:01:30,566 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-13 12:01:31,051 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-13 12:01:31,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2561045.0, 'new_value': 3014253.0}, {'field': 'total_amount', 'old_value': 2561045.0, 'new_value': 3014253.0}, {'field': 'order_count', 'old_value': 42629, 'new_value': 51594}]
2025-05-13 12:01:31,051 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-13 12:01:31,512 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-13 12:01:31,513 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28937.0, 'new_value': 30608.0}, {'field': 'total_amount', 'old_value': 28937.0, 'new_value': 30608.0}, {'field': 'order_count', 'old_value': 198, 'new_value': 205}]
2025-05-13 12:01:31,513 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-13 12:01:32,018 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-13 12:01:32,019 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226511.4, 'new_value': 231905.89}, {'field': 'total_amount', 'old_value': 252983.13, 'new_value': 258377.62}, {'field': 'order_count', 'old_value': 1984, 'new_value': 2136}]
2025-05-13 12:01:32,019 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-13 12:01:32,405 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-13 12:01:32,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35619.0, 'new_value': 40986.98}, {'field': 'offline_amount', 'old_value': 233332.0, 'new_value': 238319.5}, {'field': 'total_amount', 'old_value': 268951.0, 'new_value': 279306.48}, {'field': 'order_count', 'old_value': 2071, 'new_value': 2123}]
2025-05-13 12:01:32,406 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-13 12:01:32,832 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-13 12:01:32,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23552.0, 'new_value': 31955.0}, {'field': 'total_amount', 'old_value': 23552.0, 'new_value': 31955.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 31}]
2025-05-13 12:01:32,832 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-13 12:01:33,223 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-13 12:01:33,223 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61278.33, 'new_value': 63802.58}, {'field': 'total_amount', 'old_value': 61278.33, 'new_value': 63802.58}, {'field': 'order_count', 'old_value': 1753, 'new_value': 1850}]
2025-05-13 12:01:33,223 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-13 12:01:33,710 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-13 12:01:33,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34683.55, 'new_value': 42207.74}, {'field': 'total_amount', 'old_value': 34683.55, 'new_value': 42207.74}, {'field': 'order_count', 'old_value': 1321, 'new_value': 1451}]
2025-05-13 12:01:33,710 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-13 12:01:34,294 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-13 12:01:34,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118559.73, 'new_value': 133171.78}, {'field': 'total_amount', 'old_value': 179081.83, 'new_value': 193693.88}, {'field': 'order_count', 'old_value': 2032, 'new_value': 2224}]
2025-05-13 12:01:34,294 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-13 12:01:34,814 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-13 12:01:34,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56532.0, 'new_value': 63101.0}, {'field': 'total_amount', 'old_value': 56532.0, 'new_value': 63101.0}, {'field': 'order_count', 'old_value': 2173, 'new_value': 2302}]
2025-05-13 12:01:34,815 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-13 12:01:35,265 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-13 12:01:35,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218156.98, 'new_value': 227852.92}, {'field': 'total_amount', 'old_value': 218156.98, 'new_value': 227852.92}, {'field': 'order_count', 'old_value': 999, 'new_value': 1051}]
2025-05-13 12:01:35,265 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-13 12:01:35,783 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-13 12:01:35,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12813.07, 'new_value': 15228.07}, {'field': 'total_amount', 'old_value': 12813.07, 'new_value': 15228.07}, {'field': 'order_count', 'old_value': 1303, 'new_value': 1423}]
2025-05-13 12:01:35,783 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-13 12:01:36,186 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-13 12:01:36,186 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92952.0, 'new_value': 103488.0}, {'field': 'total_amount', 'old_value': 92952.0, 'new_value': 103488.0}, {'field': 'order_count', 'old_value': 7746, 'new_value': 8624}]
2025-05-13 12:01:36,186 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-13 12:01:36,746 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-13 12:01:36,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28000.0, 'new_value': 32000.0}, {'field': 'total_amount', 'old_value': 28000.0, 'new_value': 32000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-13 12:01:36,747 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-13 12:01:37,173 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-13 12:01:37,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16004.7, 'new_value': 20014.7}, {'field': 'offline_amount', 'old_value': 14384.62, 'new_value': 16267.62}, {'field': 'total_amount', 'old_value': 30389.32, 'new_value': 36282.32}, {'field': 'order_count', 'old_value': 5443, 'new_value': 5856}]
2025-05-13 12:01:37,173 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-13 12:01:37,770 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M
2025-05-13 12:01:37,770 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14176.0, 'new_value': 20464.0}, {'field': 'total_amount', 'old_value': 14176.0, 'new_value': 20464.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-05-13 12:01:37,770 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-13 12:01:38,247 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-13 12:01:38,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59895.7, 'new_value': 61004.7}, {'field': 'total_amount', 'old_value': 59895.7, 'new_value': 61004.7}, {'field': 'order_count', 'old_value': 507, 'new_value': 529}]
2025-05-13 12:01:38,248 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-13 12:01:38,804 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-13 12:01:38,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129641.73, 'new_value': 138759.99}, {'field': 'total_amount', 'old_value': 175151.42, 'new_value': 184269.68}, {'field': 'order_count', 'old_value': 5476, 'new_value': 5828}]
2025-05-13 12:01:38,804 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-13 12:01:39,393 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-13 12:01:39,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145634.0, 'new_value': 176575.0}, {'field': 'total_amount', 'old_value': 145634.0, 'new_value': 176575.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-13 12:01:39,393 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-13 12:01:39,849 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-13 12:01:39,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21730.2, 'new_value': 27885.2}, {'field': 'total_amount', 'old_value': 21730.2, 'new_value': 27885.2}, {'field': 'order_count', 'old_value': 234, 'new_value': 275}]
2025-05-13 12:01:39,849 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-13 12:01:40,320 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-13 12:01:40,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4349700.0, 'new_value': 4549700.0}, {'field': 'total_amount', 'old_value': 4349700.0, 'new_value': 4549700.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 55}]
2025-05-13 12:01:40,320 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-13 12:01:40,789 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-13 12:01:40,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25433.4, 'new_value': 25792.4}, {'field': 'total_amount', 'old_value': 25433.4, 'new_value': 25792.4}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-13 12:01:40,789 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-13 12:01:41,319 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-13 12:01:41,319 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34890.0, 'new_value': 36466.0}, {'field': 'total_amount', 'old_value': 41814.0, 'new_value': 43390.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-13 12:01:41,319 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-13 12:01:41,768 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-13 12:01:41,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19556.2, 'new_value': 25496.5}, {'field': 'total_amount', 'old_value': 19556.2, 'new_value': 25496.5}, {'field': 'order_count', 'old_value': 954, 'new_value': 1057}]
2025-05-13 12:01:41,768 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-13 12:01:42,180 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-13 12:01:42,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25017.8, 'new_value': 26183.9}, {'field': 'total_amount', 'old_value': 25539.0, 'new_value': 26705.1}, {'field': 'order_count', 'old_value': 83, 'new_value': 87}]
2025-05-13 12:01:42,180 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-13 12:01:42,607 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-13 12:01:42,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3099700.0, 'new_value': 3499600.0}, {'field': 'total_amount', 'old_value': 3099700.0, 'new_value': 3499600.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-13 12:01:42,608 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-13 12:01:43,116 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-13 12:01:43,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59536.8, 'new_value': 60434.8}, {'field': 'total_amount', 'old_value': 75157.3, 'new_value': 76055.3}, {'field': 'order_count', 'old_value': 105, 'new_value': 106}]
2025-05-13 12:01:43,116 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-13 12:01:43,596 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-13 12:01:43,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20686.55, 'new_value': 22324.55}, {'field': 'total_amount', 'old_value': 20686.55, 'new_value': 22324.55}, {'field': 'order_count', 'old_value': 915, 'new_value': 989}]
2025-05-13 12:01:43,596 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-13 12:01:44,032 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-13 12:01:44,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7675.26, 'new_value': 8262.59}, {'field': 'offline_amount', 'old_value': 126387.48, 'new_value': 129408.78}, {'field': 'total_amount', 'old_value': 134062.74, 'new_value': 137671.37}, {'field': 'order_count', 'old_value': 1681, 'new_value': 1755}]
2025-05-13 12:01:44,033 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-13 12:01:44,485 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-13 12:01:44,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23617.0, 'new_value': 26121.0}, {'field': 'total_amount', 'old_value': 23617.0, 'new_value': 26121.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 131}]
2025-05-13 12:01:44,485 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-13 12:01:45,021 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-13 12:01:45,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8817.99, 'new_value': 8818.99}, {'field': 'offline_amount', 'old_value': 123932.0, 'new_value': 190545.0}, {'field': 'total_amount', 'old_value': 132749.99, 'new_value': 199363.99}, {'field': 'order_count', 'old_value': 24, 'new_value': 33}]
2025-05-13 12:01:45,022 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-13 12:01:45,491 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-13 12:01:45,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97698.0, 'new_value': 109698.0}, {'field': 'total_amount', 'old_value': 111752.0, 'new_value': 123752.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 103}]
2025-05-13 12:01:45,491 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-13 12:01:46,003 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-13 12:01:46,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44037.85, 'new_value': 49661.75}, {'field': 'total_amount', 'old_value': 116763.55, 'new_value': 122387.45}, {'field': 'order_count', 'old_value': 2943, 'new_value': 3123}]
2025-05-13 12:01:46,003 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-13 12:01:46,393 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-13 12:01:46,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127757.0, 'new_value': 131860.0}, {'field': 'total_amount', 'old_value': 127757.0, 'new_value': 131860.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 62}]
2025-05-13 12:01:46,393 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-13 12:01:46,879 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-13 12:01:46,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46405.4, 'new_value': 48998.6}, {'field': 'total_amount', 'old_value': 46405.4, 'new_value': 48998.6}, {'field': 'order_count', 'old_value': 358, 'new_value': 367}]
2025-05-13 12:01:46,879 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-13 12:01:47,354 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-13 12:01:47,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19777.46, 'new_value': 33080.21}, {'field': 'offline_amount', 'old_value': 114729.05, 'new_value': 142764.79}, {'field': 'total_amount', 'old_value': 134506.51, 'new_value': 175845.0}, {'field': 'order_count', 'old_value': 5418, 'new_value': 7246}]
2025-05-13 12:01:47,354 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-13 12:01:47,784 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-13 12:01:47,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221694.77, 'new_value': 258531.68}, {'field': 'total_amount', 'old_value': 221694.77, 'new_value': 258531.68}, {'field': 'order_count', 'old_value': 384, 'new_value': 444}]
2025-05-13 12:01:47,785 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-13 12:01:48,283 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-13 12:01:48,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34492.57, 'new_value': 38743.94}, {'field': 'offline_amount', 'old_value': 136724.31, 'new_value': 139104.66}, {'field': 'total_amount', 'old_value': 171216.88, 'new_value': 177848.6}, {'field': 'order_count', 'old_value': 2463, 'new_value': 2545}]
2025-05-13 12:01:48,283 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-13 12:01:48,761 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO
2025-05-13 12:01:48,761 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-13 12:01:48,762 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-13 12:01:49,198 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-13 12:01:49,198 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11085.0, 'new_value': 11673.0}, {'field': 'total_amount', 'old_value': 11085.0, 'new_value': 11673.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-13 12:01:49,198 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-13 12:01:49,668 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-13 12:01:49,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14990.0, 'new_value': 17028.0}, {'field': 'total_amount', 'old_value': 14990.0, 'new_value': 17028.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 165}]
2025-05-13 12:01:49,668 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-13 12:01:50,134 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-13 12:01:50,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18218.2, 'new_value': 20332.45}, {'field': 'total_amount', 'old_value': 18218.2, 'new_value': 20332.45}, {'field': 'order_count', 'old_value': 73, 'new_value': 82}]
2025-05-13 12:01:50,135 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-13 12:01:50,627 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-13 12:01:50,627 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24318.46, 'new_value': 26376.53}, {'field': 'offline_amount', 'old_value': 15725.15, 'new_value': 17040.16}, {'field': 'total_amount', 'old_value': 40043.61, 'new_value': 43416.69}, {'field': 'order_count', 'old_value': 2220, 'new_value': 2376}]
2025-05-13 12:01:50,627 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-13 12:01:51,137 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-13 12:01:51,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57256.0, 'new_value': 63255.0}, {'field': 'total_amount', 'old_value': 66180.6, 'new_value': 72179.6}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-05-13 12:01:51,137 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-13 12:01:51,584 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-13 12:01:51,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43956.0, 'new_value': 44156.0}, {'field': 'total_amount', 'old_value': 43956.0, 'new_value': 44156.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-13 12:01:51,584 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-13 12:01:51,983 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-13 12:01:51,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47968.89, 'new_value': 53058.89}, {'field': 'total_amount', 'old_value': 47968.89, 'new_value': 53058.89}, {'field': 'order_count', 'old_value': 228, 'new_value': 249}]
2025-05-13 12:01:51,984 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-13 12:01:52,402 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-13 12:01:52,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63836.74, 'new_value': 75353.74}, {'field': 'total_amount', 'old_value': 77045.46, 'new_value': 88562.46}, {'field': 'order_count', 'old_value': 4358, 'new_value': 5033}]
2025-05-13 12:01:52,402 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-13 12:01:52,858 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-13 12:01:52,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205822.63, 'new_value': 216665.77}, {'field': 'total_amount', 'old_value': 205822.63, 'new_value': 216665.77}, {'field': 'order_count', 'old_value': 734, 'new_value': 773}]
2025-05-13 12:01:52,858 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-13 12:01:53,296 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-13 12:01:53,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137990.9, 'new_value': 163516.5}, {'field': 'total_amount', 'old_value': 137990.9, 'new_value': 163516.5}, {'field': 'order_count', 'old_value': 3547, 'new_value': 3856}]
2025-05-13 12:01:53,296 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-13 12:01:53,651 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-13 12:01:53,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17365.93, 'new_value': 18853.47}, {'field': 'total_amount', 'old_value': 17365.93, 'new_value': 18853.47}, {'field': 'order_count', 'old_value': 2207, 'new_value': 2400}]
2025-05-13 12:01:53,651 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-13 12:01:54,117 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-13 12:01:54,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11156.28, 'new_value': 12472.51}, {'field': 'offline_amount', 'old_value': 15947.6, 'new_value': 17072.4}, {'field': 'total_amount', 'old_value': 27103.88, 'new_value': 29544.91}, {'field': 'order_count', 'old_value': 1190, 'new_value': 1286}]
2025-05-13 12:01:54,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-13 12:01:54,530 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-13 12:01:54,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28474.0, 'new_value': 32128.0}, {'field': 'total_amount', 'old_value': 33675.0, 'new_value': 37329.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 118}]
2025-05-13 12:01:54,530 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-13 12:01:55,006 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-13 12:01:55,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12811.0, 'new_value': 15409.0}, {'field': 'total_amount', 'old_value': 14187.0, 'new_value': 16785.0}, {'field': 'order_count', 'old_value': 1589, 'new_value': 1745}]
2025-05-13 12:01:55,006 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-13 12:01:55,527 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-13 12:01:55,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149715.0, 'new_value': 149735.0}, {'field': 'total_amount', 'old_value': 149715.0, 'new_value': 149735.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-13 12:01:55,527 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-13 12:01:56,137 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-13 12:01:56,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21813.9, 'new_value': 27217.2}, {'field': 'total_amount', 'old_value': 30476.3, 'new_value': 35879.6}, {'field': 'order_count', 'old_value': 86, 'new_value': 93}]
2025-05-13 12:01:56,138 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-13 12:01:56,649 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-13 12:01:56,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 233373.04, 'new_value': 246004.04}, {'field': 'total_amount', 'old_value': 233373.04, 'new_value': 246004.04}, {'field': 'order_count', 'old_value': 1174, 'new_value': 1245}]
2025-05-13 12:01:56,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-13 12:01:57,092 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-13 12:01:57,092 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98.0, 'new_value': 516.0}, {'field': 'total_amount', 'old_value': 5375.6, 'new_value': 5793.6}, {'field': 'order_count', 'old_value': 54, 'new_value': 59}]
2025-05-13 12:01:57,092 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-13 12:01:57,566 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-13 12:01:57,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118435.0, 'new_value': 120673.0}, {'field': 'total_amount', 'old_value': 118435.0, 'new_value': 120673.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 67}]
2025-05-13 12:01:57,567 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-13 12:01:58,074 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-13 12:01:58,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36030.0, 'new_value': 44010.0}, {'field': 'total_amount', 'old_value': 36030.0, 'new_value': 44010.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 309}]
2025-05-13 12:01:58,075 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-13 12:01:58,566 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-13 12:01:58,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251463.07, 'new_value': 263159.12}, {'field': 'total_amount', 'old_value': 251463.07, 'new_value': 263159.12}, {'field': 'order_count', 'old_value': 3290, 'new_value': 3481}]
2025-05-13 12:01:58,566 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-13 12:01:59,017 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-13 12:01:59,017 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5908.4, 'new_value': 6304.4}, {'field': 'offline_amount', 'old_value': 5618.9, 'new_value': 6169.9}, {'field': 'total_amount', 'old_value': 11527.3, 'new_value': 12474.3}, {'field': 'order_count', 'old_value': 50, 'new_value': 54}]
2025-05-13 12:01:59,017 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-13 12:01:59,431 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-13 12:01:59,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99278.0, 'new_value': 103424.0}, {'field': 'total_amount', 'old_value': 99278.0, 'new_value': 103424.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 166}]
2025-05-13 12:01:59,431 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-13 12:01:59,854 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-13 12:01:59,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294662.08, 'new_value': 312336.4}, {'field': 'total_amount', 'old_value': 295189.09, 'new_value': 312863.41}, {'field': 'order_count', 'old_value': 673, 'new_value': 722}]
2025-05-13 12:01:59,854 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-13 12:02:00,313 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-13 12:02:00,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75370.0, 'new_value': 81630.0}, {'field': 'total_amount', 'old_value': 75370.0, 'new_value': 81630.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 12:02:00,314 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-13 12:02:00,752 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-13 12:02:00,752 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4397.94, 'new_value': 4702.86}, {'field': 'offline_amount', 'old_value': 15940.99, 'new_value': 16601.38}, {'field': 'total_amount', 'old_value': 20338.93, 'new_value': 21304.24}, {'field': 'order_count', 'old_value': 733, 'new_value': 764}]
2025-05-13 12:02:00,753 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-13 12:02:01,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-13 12:02:01,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 685942.0, 'new_value': 705172.0}, {'field': 'total_amount', 'old_value': 685942.0, 'new_value': 705172.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 80}]
2025-05-13 12:02:01,166 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-13 12:02:01,645 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-13 12:02:01,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23351.79, 'new_value': 23438.11}, {'field': 'offline_amount', 'old_value': 27439.72, 'new_value': 28798.78}, {'field': 'total_amount', 'old_value': 50791.51, 'new_value': 52236.89}, {'field': 'order_count', 'old_value': 167, 'new_value': 175}]
2025-05-13 12:02:01,645 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-13 12:02:02,034 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-13 12:02:02,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82627.11, 'new_value': 85570.16}, {'field': 'total_amount', 'old_value': 82627.11, 'new_value': 85570.16}, {'field': 'order_count', 'old_value': 2059, 'new_value': 2147}]
2025-05-13 12:02:02,035 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-13 12:02:02,544 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-13 12:02:02,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182136.57, 'new_value': 189546.44}, {'field': 'total_amount', 'old_value': 200412.2, 'new_value': 207822.07}, {'field': 'order_count', 'old_value': 4684, 'new_value': 4841}]
2025-05-13 12:02:02,545 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-13 12:02:03,016 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-13 12:02:03,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183603.16, 'new_value': 190799.47}, {'field': 'total_amount', 'old_value': 183603.16, 'new_value': 190799.47}, {'field': 'order_count', 'old_value': 1481, 'new_value': 1561}]
2025-05-13 12:02:03,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-13 12:02:03,443 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-13 12:02:03,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9429.0, 'new_value': 11582.0}, {'field': 'total_amount', 'old_value': 9429.0, 'new_value': 11582.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 58}]
2025-05-13 12:02:03,443 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-13 12:02:03,823 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-13 12:02:03,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244564.6, 'new_value': 257132.9}, {'field': 'total_amount', 'old_value': 244564.6, 'new_value': 257132.9}, {'field': 'order_count', 'old_value': 1082, 'new_value': 1279}]
2025-05-13 12:02:03,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-13 12:02:04,449 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-13 12:02:04,449 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10185.3, 'new_value': 10554.5}, {'field': 'total_amount', 'old_value': 10185.3, 'new_value': 10554.5}, {'field': 'order_count', 'old_value': 335, 'new_value': 362}]
2025-05-13 12:02:04,449 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-13 12:02:04,875 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-13 12:02:04,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78642.04, 'new_value': 82037.0}, {'field': 'offline_amount', 'old_value': 28485.14, 'new_value': 29733.34}, {'field': 'total_amount', 'old_value': 107127.18, 'new_value': 111770.34}, {'field': 'order_count', 'old_value': 6749, 'new_value': 7005}]
2025-05-13 12:02:04,875 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-13 12:02:05,387 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-13 12:02:05,388 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8708.42, 'new_value': 9998.23}, {'field': 'offline_amount', 'old_value': 68761.07, 'new_value': 75950.26}, {'field': 'total_amount', 'old_value': 77469.49, 'new_value': 85948.49}, {'field': 'order_count', 'old_value': 2260, 'new_value': 2512}]
2025-05-13 12:02:05,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-13 12:02:06,285 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-13 12:02:06,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7375.9, 'new_value': 7450.7}, {'field': 'total_amount', 'old_value': 7804.9, 'new_value': 7879.7}, {'field': 'order_count', 'old_value': 112, 'new_value': 114}]
2025-05-13 12:02:06,285 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-13 12:02:06,817 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-13 12:02:06,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45018.1, 'new_value': 51558.0}, {'field': 'total_amount', 'old_value': 45018.1, 'new_value': 51558.0}, {'field': 'order_count', 'old_value': 2199, 'new_value': 2539}]
2025-05-13 12:02:06,817 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-13 12:02:07,270 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-13 12:02:07,271 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4300.5, 'new_value': 4639.5}, {'field': 'offline_amount', 'old_value': 19054.1, 'new_value': 19751.9}, {'field': 'total_amount', 'old_value': 23354.6, 'new_value': 24391.4}, {'field': 'order_count', 'old_value': 254, 'new_value': 267}]
2025-05-13 12:02:07,271 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-13 12:02:07,680 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-13 12:02:07,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2149.0, 'new_value': 2314.0}, {'field': 'offline_amount', 'old_value': 19415.0, 'new_value': 19955.0}, {'field': 'total_amount', 'old_value': 21564.0, 'new_value': 22269.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 175}]
2025-05-13 12:02:07,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-13 12:02:08,252 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-13 12:02:08,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7135.0, 'new_value': 8021.0}, {'field': 'offline_amount', 'old_value': 13147.0, 'new_value': 13827.0}, {'field': 'total_amount', 'old_value': 20282.0, 'new_value': 21848.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 47}]
2025-05-13 12:02:08,253 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-13 12:02:08,763 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-13 12:02:08,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109490.0, 'new_value': 113532.0}, {'field': 'total_amount', 'old_value': 109490.0, 'new_value': 113532.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 126}]
2025-05-13 12:02:08,764 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-13 12:02:09,354 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-13 12:02:09,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 407422.75, 'new_value': 420169.74}, {'field': 'total_amount', 'old_value': 407422.75, 'new_value': 420169.74}, {'field': 'order_count', 'old_value': 8252, 'new_value': 8510}]
2025-05-13 12:02:09,354 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-13 12:02:09,824 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-13 12:02:09,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135643.58, 'new_value': 145742.88}, {'field': 'total_amount', 'old_value': 135643.58, 'new_value': 145742.88}, {'field': 'order_count', 'old_value': 5678, 'new_value': 6029}]
2025-05-13 12:02:09,825 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-13 12:02:10,200 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-13 12:02:10,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12890.86, 'new_value': 14618.66}, {'field': 'total_amount', 'old_value': 12890.86, 'new_value': 14618.66}, {'field': 'order_count', 'old_value': 472, 'new_value': 547}]
2025-05-13 12:02:10,201 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-13 12:02:10,774 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-13 12:02:10,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356434.68, 'new_value': 382430.06}, {'field': 'total_amount', 'old_value': 356434.68, 'new_value': 382430.06}, {'field': 'order_count', 'old_value': 2530, 'new_value': 2746}]
2025-05-13 12:02:10,774 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-13 12:02:11,241 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-13 12:02:11,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28700.82, 'new_value': 30828.76}, {'field': 'offline_amount', 'old_value': 233627.96, 'new_value': 243734.56}, {'field': 'total_amount', 'old_value': 262328.78, 'new_value': 274563.32}, {'field': 'order_count', 'old_value': 1108, 'new_value': 1168}]
2025-05-13 12:02:11,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-13 12:02:11,777 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-13 12:02:11,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 764.84, 'new_value': 782.57}, {'field': 'offline_amount', 'old_value': 15201.86, 'new_value': 15617.2}, {'field': 'total_amount', 'old_value': 15966.7, 'new_value': 16399.77}, {'field': 'order_count', 'old_value': 563, 'new_value': 577}]
2025-05-13 12:02:11,777 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-13 12:02:12,322 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-13 12:02:12,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 473653.0, 'new_value': 488797.0}, {'field': 'total_amount', 'old_value': 473653.0, 'new_value': 488797.0}, {'field': 'order_count', 'old_value': 2137, 'new_value': 2205}]
2025-05-13 12:02:12,322 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-13 12:02:12,829 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-13 12:02:12,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 365475.92, 'new_value': 382018.01}, {'field': 'total_amount', 'old_value': 365475.92, 'new_value': 382018.01}, {'field': 'order_count', 'old_value': 2424, 'new_value': 2547}]
2025-05-13 12:02:12,829 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-13 12:02:13,396 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-13 12:02:13,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3730.0, 'new_value': 4729.0}, {'field': 'total_amount', 'old_value': 3730.0, 'new_value': 4729.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-13 12:02:13,397 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-13 12:02:13,851 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-13 12:02:13,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3453.4, 'new_value': 3651.42}, {'field': 'offline_amount', 'old_value': 207041.44, 'new_value': 217453.34}, {'field': 'total_amount', 'old_value': 210494.84, 'new_value': 221104.76}, {'field': 'order_count', 'old_value': 9399, 'new_value': 9890}]
2025-05-13 12:02:13,852 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-13 12:02:14,321 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-13 12:02:14,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94360.5, 'new_value': 99068.5}, {'field': 'total_amount', 'old_value': 94360.5, 'new_value': 99068.5}, {'field': 'order_count', 'old_value': 514, 'new_value': 542}]
2025-05-13 12:02:14,321 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-13 12:02:14,831 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-13 12:02:14,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47714.0, 'new_value': 53484.0}, {'field': 'total_amount', 'old_value': 47714.0, 'new_value': 53484.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-13 12:02:14,832 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-13 12:02:15,364 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-13 12:02:15,364 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7477.7, 'new_value': 7584.6}, {'field': 'total_amount', 'old_value': 12477.7, 'new_value': 12584.6}, {'field': 'order_count', 'old_value': 115, 'new_value': 116}]
2025-05-13 12:02:15,364 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-13 12:02:15,813 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-13 12:02:15,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187873.42, 'new_value': 212256.01}, {'field': 'total_amount', 'old_value': 187873.42, 'new_value': 212256.01}, {'field': 'order_count', 'old_value': 1202, 'new_value': 1349}]
2025-05-13 12:02:15,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-13 12:02:16,285 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-13 12:02:16,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64621.0, 'new_value': 67121.0}, {'field': 'total_amount', 'old_value': 79825.0, 'new_value': 82325.0}, {'field': 'order_count', 'old_value': 1776, 'new_value': 1777}]
2025-05-13 12:02:16,285 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE
2025-05-13 12:02:16,885 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE
2025-05-13 12:02:16,885 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10000.0, 'new_value': 19500.0}, {'field': 'total_amount', 'old_value': 10000.0, 'new_value': 19500.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-05-13 12:02:16,885 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-13 12:02:17,371 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-13 12:02:17,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53363.0, 'new_value': 57863.0}, {'field': 'total_amount', 'old_value': 53363.0, 'new_value': 57863.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 318}]
2025-05-13 12:02:17,371 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-13 12:02:17,835 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-13 12:02:17,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84668.66, 'new_value': 97476.07}, {'field': 'total_amount', 'old_value': 84668.66, 'new_value': 97476.07}, {'field': 'order_count', 'old_value': 1071, 'new_value': 1162}]
2025-05-13 12:02:17,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-13 12:02:18,302 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-13 12:02:18,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13107.0, 'new_value': 13307.0}, {'field': 'total_amount', 'old_value': 13107.0, 'new_value': 13307.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-13 12:02:18,302 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-13 12:02:18,910 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-13 12:02:18,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91786.0, 'new_value': 93183.0}, {'field': 'total_amount', 'old_value': 91786.0, 'new_value': 93183.0}, {'field': 'order_count', 'old_value': 2947, 'new_value': 2990}]
2025-05-13 12:02:18,910 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-13 12:02:19,358 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-13 12:02:19,358 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39712.93, 'new_value': 45124.57}, {'field': 'offline_amount', 'old_value': 233060.3, 'new_value': 235698.76}, {'field': 'total_amount', 'old_value': 272773.23, 'new_value': 280823.33}, {'field': 'order_count', 'old_value': 1769, 'new_value': 1899}]
2025-05-13 12:02:19,358 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-13 12:02:19,849 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-13 12:02:19,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59492.34, 'new_value': 60191.34}, {'field': 'total_amount', 'old_value': 59492.34, 'new_value': 60191.34}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-13 12:02:19,850 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-13 12:02:20,271 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-13 12:02:20,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134800.0, 'new_value': 184800.0}, {'field': 'total_amount', 'old_value': 134800.0, 'new_value': 184800.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-13 12:02:20,272 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-13 12:02:20,718 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-13 12:02:20,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38636.07, 'new_value': 42897.56}, {'field': 'offline_amount', 'old_value': 50700.52, 'new_value': 54957.03}, {'field': 'total_amount', 'old_value': 89336.59, 'new_value': 97854.59}, {'field': 'order_count', 'old_value': 3654, 'new_value': 3964}]
2025-05-13 12:02:20,719 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-13 12:02:21,188 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-13 12:02:21,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42436.22, 'new_value': 49106.04}, {'field': 'total_amount', 'old_value': 196455.74, 'new_value': 203125.56}, {'field': 'order_count', 'old_value': 341, 'new_value': 353}]
2025-05-13 12:02:21,188 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-13 12:02:21,644 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-13 12:02:21,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127186.25, 'new_value': 131415.07}, {'field': 'total_amount', 'old_value': 146359.68, 'new_value': 150588.5}, {'field': 'order_count', 'old_value': 3100, 'new_value': 3184}]
2025-05-13 12:02:21,645 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-13 12:02:22,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-13 12:02:22,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29513.0, 'new_value': 31998.0}, {'field': 'total_amount', 'old_value': 29513.0, 'new_value': 31998.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-05-13 12:02:22,044 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-13 12:02:22,451 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-13 12:02:22,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3088.62, 'new_value': 4561.67}, {'field': 'total_amount', 'old_value': 3088.62, 'new_value': 4561.67}, {'field': 'order_count', 'old_value': 101, 'new_value': 144}]
2025-05-13 12:02:22,451 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-13 12:02:22,935 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-13 12:02:22,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 357272.58, 'new_value': 374086.58}, {'field': 'total_amount', 'old_value': 357272.58, 'new_value': 374086.58}, {'field': 'order_count', 'old_value': 2630, 'new_value': 2818}]
2025-05-13 12:02:22,935 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-13 12:02:23,428 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-13 12:02:23,428 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339030.0, 'new_value': 352028.0}, {'field': 'total_amount', 'old_value': 339030.0, 'new_value': 352028.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-13 12:02:23,428 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-13 12:02:23,868 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-13 12:02:23,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 635773.27, 'new_value': 678428.55}, {'field': 'total_amount', 'old_value': 832179.39, 'new_value': 874834.67}, {'field': 'order_count', 'old_value': 3946, 'new_value': 4363}]
2025-05-13 12:02:23,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-13 12:02:24,294 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-13 12:02:24,294 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42615.85, 'new_value': 44067.97}, {'field': 'total_amount', 'old_value': 42615.85, 'new_value': 44067.97}, {'field': 'order_count', 'old_value': 2426, 'new_value': 2507}]
2025-05-13 12:02:24,294 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-13 12:02:24,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-13 12:02:24,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20131.0, 'new_value': 22937.0}, {'field': 'total_amount', 'old_value': 20131.0, 'new_value': 22937.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 35}]
2025-05-13 12:02:24,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-13 12:02:25,210 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-13 12:02:25,210 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27792.44, 'new_value': 29260.23}, {'field': 'offline_amount', 'old_value': 25171.33, 'new_value': 26354.33}, {'field': 'total_amount', 'old_value': 52963.77, 'new_value': 55614.56}, {'field': 'order_count', 'old_value': 1035, 'new_value': 1098}]
2025-05-13 12:02:25,210 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-13 12:02:25,674 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-13 12:02:25,675 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6749.0, 'new_value': 7188.45}, {'field': 'offline_amount', 'old_value': 135671.0, 'new_value': 141323.0}, {'field': 'total_amount', 'old_value': 142420.0, 'new_value': 148511.45}, {'field': 'order_count', 'old_value': 702, 'new_value': 727}]
2025-05-13 12:02:25,675 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-13 12:02:26,137 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-13 12:02:26,137 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 12:02:26,137 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-13 12:02:26,576 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-13 12:02:26,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143849.5, 'new_value': 158446.5}, {'field': 'total_amount', 'old_value': 143849.5, 'new_value': 158446.5}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-13 12:02:26,576 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-13 12:02:27,034 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-13 12:02:27,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142389.13, 'new_value': 146479.19}, {'field': 'total_amount', 'old_value': 142389.13, 'new_value': 146479.19}, {'field': 'order_count', 'old_value': 769, 'new_value': 889}]
2025-05-13 12:02:27,034 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-13 12:02:27,550 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-13 12:02:27,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23518.83, 'new_value': 24033.83}, {'field': 'total_amount', 'old_value': 23518.83, 'new_value': 24033.83}, {'field': 'order_count', 'old_value': 70, 'new_value': 72}]
2025-05-13 12:02:27,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-13 12:02:28,049 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-13 12:02:28,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2296.0, 'new_value': 5894.0}, {'field': 'offline_amount', 'old_value': 3596.0, 'new_value': 4894.0}, {'field': 'total_amount', 'old_value': 5892.0, 'new_value': 10788.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 39}]
2025-05-13 12:02:28,049 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-13 12:02:28,534 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-13 12:02:28,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145615.0, 'new_value': 157615.0}, {'field': 'total_amount', 'old_value': 159390.0, 'new_value': 171390.0}, {'field': 'order_count', 'old_value': 3256, 'new_value': 3257}]
2025-05-13 12:02:28,534 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-13 12:02:29,018 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-13 12:02:29,018 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38547.21, 'new_value': 39308.59}, {'field': 'offline_amount', 'old_value': 107902.36, 'new_value': 113165.37}, {'field': 'total_amount', 'old_value': 146449.57, 'new_value': 152473.96}, {'field': 'order_count', 'old_value': 2379, 'new_value': 2519}]
2025-05-13 12:02:29,018 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-13 12:02:29,547 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-13 12:02:29,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 377974.3, 'new_value': 395600.18}, {'field': 'total_amount', 'old_value': 377974.3, 'new_value': 395600.18}, {'field': 'order_count', 'old_value': 4132, 'new_value': 4400}]
2025-05-13 12:02:29,547 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-13 12:02:30,010 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-13 12:02:30,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306341.4, 'new_value': 311041.8}, {'field': 'total_amount', 'old_value': 450074.8, 'new_value': 454775.2}, {'field': 'order_count', 'old_value': 3037, 'new_value': 3041}]
2025-05-13 12:02:30,011 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-13 12:02:30,597 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-13 12:02:30,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 397579.26, 'new_value': 418393.79}, {'field': 'total_amount', 'old_value': 509415.25, 'new_value': 530229.78}, {'field': 'order_count', 'old_value': 2739, 'new_value': 2914}]
2025-05-13 12:02:30,597 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-13 12:02:31,080 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-13 12:02:31,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15595.4, 'new_value': 16366.81}, {'field': 'offline_amount', 'old_value': 201012.06, 'new_value': 207254.97}, {'field': 'total_amount', 'old_value': 216607.46, 'new_value': 223621.78}, {'field': 'order_count', 'old_value': 1217, 'new_value': 1266}]
2025-05-13 12:02:31,080 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-13 12:02:31,579 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-13 12:02:31,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101108.96, 'new_value': 105341.02}, {'field': 'total_amount', 'old_value': 101108.96, 'new_value': 105341.02}, {'field': 'order_count', 'old_value': 4209, 'new_value': 4391}]
2025-05-13 12:02:31,579 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-13 12:02:32,069 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-13 12:02:32,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225426.0, 'new_value': 242176.0}, {'field': 'total_amount', 'old_value': 225426.0, 'new_value': 242176.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 229}]
2025-05-13 12:02:32,070 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-13 12:02:32,553 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-13 12:02:32,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118684.66, 'new_value': 123827.71}, {'field': 'total_amount', 'old_value': 118684.66, 'new_value': 123827.71}, {'field': 'order_count', 'old_value': 862, 'new_value': 910}]
2025-05-13 12:02:32,553 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-13 12:02:32,969 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-13 12:02:32,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52624.0, 'new_value': 53630.8}, {'field': 'total_amount', 'old_value': 54152.8, 'new_value': 55159.6}, {'field': 'order_count', 'old_value': 328, 'new_value': 335}]
2025-05-13 12:02:32,969 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-13 12:02:33,459 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-13 12:02:33,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69472.67, 'new_value': 72595.37}, {'field': 'total_amount', 'old_value': 88512.27, 'new_value': 91634.97}, {'field': 'order_count', 'old_value': 2514, 'new_value': 2613}]
2025-05-13 12:02:33,459 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-13 12:02:33,978 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-13 12:02:33,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33796.0, 'new_value': 34084.0}, {'field': 'total_amount', 'old_value': 33796.0, 'new_value': 34084.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-05-13 12:02:33,978 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-13 12:02:34,405 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-13 12:02:34,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95200.94, 'new_value': 100117.94}, {'field': 'offline_amount', 'old_value': 7095.3, 'new_value': 7551.3}, {'field': 'total_amount', 'old_value': 102296.24, 'new_value': 107669.24}, {'field': 'order_count', 'old_value': 4414, 'new_value': 4753}]
2025-05-13 12:02:34,406 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-13 12:02:34,897 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-13 12:02:34,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38720.0, 'new_value': 42915.0}, {'field': 'offline_amount', 'old_value': 486980.0, 'new_value': 529485.0}, {'field': 'total_amount', 'old_value': 525700.0, 'new_value': 572400.0}, {'field': 'order_count', 'old_value': 11977, 'new_value': 13200}]
2025-05-13 12:02:34,898 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-13 12:02:35,342 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-13 12:02:35,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199376.37, 'new_value': 210040.58}, {'field': 'total_amount', 'old_value': 199376.37, 'new_value': 210040.58}, {'field': 'order_count', 'old_value': 680, 'new_value': 716}]
2025-05-13 12:02:35,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-13 12:02:35,786 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-13 12:02:35,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98212.0, 'new_value': 100713.0}, {'field': 'total_amount', 'old_value': 116152.0, 'new_value': 118653.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 127}]
2025-05-13 12:02:35,786 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-13 12:02:36,237 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-13 12:02:36,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19262.0, 'new_value': 20852.0}, {'field': 'total_amount', 'old_value': 19262.0, 'new_value': 20852.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-13 12:02:36,237 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-13 12:02:36,687 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-13 12:02:36,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24113.1, 'new_value': 26257.1}, {'field': 'total_amount', 'old_value': 41179.78, 'new_value': 43323.78}, {'field': 'order_count', 'old_value': 1812, 'new_value': 1930}]
2025-05-13 12:02:36,687 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-13 12:02:37,125 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-13 12:02:37,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9734.96, 'new_value': 10820.54}, {'field': 'offline_amount', 'old_value': 8647.5, 'new_value': 9012.9}, {'field': 'total_amount', 'old_value': 18382.46, 'new_value': 19833.44}, {'field': 'order_count', 'old_value': 835, 'new_value': 887}]
2025-05-13 12:02:37,125 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-13 12:02:37,582 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-13 12:02:37,582 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9615.6, 'new_value': 9754.5}, {'field': 'offline_amount', 'old_value': 28199.8, 'new_value': 28209.7}, {'field': 'total_amount', 'old_value': 37815.4, 'new_value': 37964.2}, {'field': 'order_count', 'old_value': 423, 'new_value': 425}]
2025-05-13 12:02:37,582 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-13 12:02:38,035 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-13 12:02:38,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44795.95, 'new_value': 49422.22}, {'field': 'offline_amount', 'old_value': 103304.9, 'new_value': 109756.87}, {'field': 'total_amount', 'old_value': 148100.85, 'new_value': 159179.09}, {'field': 'order_count', 'old_value': 4494, 'new_value': 4866}]
2025-05-13 12:02:38,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-13 12:02:38,455 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-13 12:02:38,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46530.0, 'new_value': 50514.0}, {'field': 'total_amount', 'old_value': 46530.0, 'new_value': 50514.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 212}]
2025-05-13 12:02:38,455 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-13 12:02:38,940 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-13 12:02:38,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26446.1, 'new_value': 30962.1}, {'field': 'total_amount', 'old_value': 26874.1, 'new_value': 31390.1}, {'field': 'order_count', 'old_value': 9231, 'new_value': 9235}]
2025-05-13 12:02:38,940 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-13 12:02:39,390 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-13 12:02:39,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73884.74, 'new_value': 80289.66}, {'field': 'offline_amount', 'old_value': 27906.19, 'new_value': 28844.75}, {'field': 'total_amount', 'old_value': 101790.93, 'new_value': 109134.41}, {'field': 'order_count', 'old_value': 5713, 'new_value': 6093}]
2025-05-13 12:02:39,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-13 12:02:39,903 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-13 12:02:39,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138215.32, 'new_value': 143327.32}, {'field': 'total_amount', 'old_value': 160702.72, 'new_value': 165814.72}, {'field': 'order_count', 'old_value': 876, 'new_value': 886}]
2025-05-13 12:02:39,903 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-13 12:02:40,393 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-13 12:02:40,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121142.19, 'new_value': 126083.53}, {'field': 'offline_amount', 'old_value': 226000.0, 'new_value': 232000.0}, {'field': 'total_amount', 'old_value': 347142.19, 'new_value': 358083.53}, {'field': 'order_count', 'old_value': 725, 'new_value': 763}]
2025-05-13 12:02:40,393 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-13 12:02:40,967 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-13 12:02:40,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87740.6, 'new_value': 93048.6}, {'field': 'total_amount', 'old_value': 87740.6, 'new_value': 93048.6}, {'field': 'order_count', 'old_value': 4400, 'new_value': 4721}]
2025-05-13 12:02:40,967 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-13 12:02:41,466 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-13 12:02:41,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 899.0}, {'field': 'total_amount', 'old_value': 5598.0, 'new_value': 6497.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-13 12:02:41,466 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-13 12:02:42,046 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-13 12:02:42,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75437.54, 'new_value': 79363.64}, {'field': 'offline_amount', 'old_value': 151736.7, 'new_value': 157579.55}, {'field': 'total_amount', 'old_value': 227174.24, 'new_value': 236943.19}, {'field': 'order_count', 'old_value': 1766, 'new_value': 1851}]
2025-05-13 12:02:42,046 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-13 12:02:42,483 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-13 12:02:42,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55055.2, 'new_value': 56262.0}, {'field': 'total_amount', 'old_value': 55055.2, 'new_value': 56262.0}, {'field': 'order_count', 'old_value': 256, 'new_value': 263}]
2025-05-13 12:02:42,484 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-13 12:02:42,904 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-13 12:02:42,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7906.04, 'new_value': 8504.38}, {'field': 'offline_amount', 'old_value': 20367.9, 'new_value': 20830.9}, {'field': 'total_amount', 'old_value': 28273.94, 'new_value': 29335.28}, {'field': 'order_count', 'old_value': 1114, 'new_value': 1163}]
2025-05-13 12:02:42,905 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-13 12:02:43,432 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-13 12:02:43,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40418.0, 'new_value': 42398.0}, {'field': 'total_amount', 'old_value': 40418.0, 'new_value': 42398.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 12:02:43,433 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-13 12:02:43,896 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-13 12:02:43,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41968.0, 'new_value': 45397.28}, {'field': 'total_amount', 'old_value': 41968.0, 'new_value': 45397.28}, {'field': 'order_count', 'old_value': 1956, 'new_value': 2122}]
2025-05-13 12:02:43,896 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-13 12:02:44,345 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-13 12:02:44,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1163.76, 'new_value': 1260.43}, {'field': 'offline_amount', 'old_value': 47812.03, 'new_value': 49537.23}, {'field': 'total_amount', 'old_value': 48975.79, 'new_value': 50797.66}, {'field': 'order_count', 'old_value': 230, 'new_value': 237}]
2025-05-13 12:02:44,345 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-13 12:02:44,799 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-13 12:02:44,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 331784.0, 'new_value': 347459.0}, {'field': 'total_amount', 'old_value': 331784.0, 'new_value': 347459.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 216}]
2025-05-13 12:02:44,799 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-13 12:02:45,315 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-13 12:02:45,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 424762.15, 'new_value': 438691.49}, {'field': 'total_amount', 'old_value': 424762.15, 'new_value': 438691.49}, {'field': 'order_count', 'old_value': 3113, 'new_value': 3270}]
2025-05-13 12:02:45,316 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-13 12:02:45,777 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-13 12:02:45,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223086.0, 'new_value': 230829.0}, {'field': 'total_amount', 'old_value': 223086.0, 'new_value': 230829.0}, {'field': 'order_count', 'old_value': 1877, 'new_value': 1976}]
2025-05-13 12:02:45,777 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-13 12:02:46,338 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-13 12:02:46,338 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2549.0, 'new_value': 2698.0}, {'field': 'total_amount', 'old_value': 6998.0, 'new_value': 7147.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 66}]
2025-05-13 12:02:46,338 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-13 12:02:46,809 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-13 12:02:46,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25924.59, 'new_value': 28504.58}, {'field': 'offline_amount', 'old_value': 18842.66, 'new_value': 21360.08}, {'field': 'total_amount', 'old_value': 44767.25, 'new_value': 49864.66}, {'field': 'order_count', 'old_value': 2408, 'new_value': 2656}]
2025-05-13 12:02:46,809 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-13 12:02:47,298 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-13 12:02:47,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1835.0, 'new_value': 1939.0}, {'field': 'offline_amount', 'old_value': 17390.0, 'new_value': 17811.6}, {'field': 'total_amount', 'old_value': 19225.0, 'new_value': 19750.6}, {'field': 'order_count', 'old_value': 701, 'new_value': 721}]
2025-05-13 12:02:47,298 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-13 12:02:47,808 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-13 12:02:47,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42033.87, 'new_value': 47620.29}, {'field': 'offline_amount', 'old_value': 49083.86, 'new_value': 54471.72}, {'field': 'total_amount', 'old_value': 91117.73, 'new_value': 102092.01}, {'field': 'order_count', 'old_value': 2298, 'new_value': 2540}]
2025-05-13 12:02:47,808 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-13 12:02:48,306 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-13 12:02:48,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14640.0, 'new_value': 44640.0}, {'field': 'total_amount', 'old_value': 14640.0, 'new_value': 44640.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-13 12:02:48,306 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-13 12:02:48,777 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-13 12:02:48,778 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445217.0, 'new_value': 481218.0}, {'field': 'total_amount', 'old_value': 445217.0, 'new_value': 481218.0}, {'field': 'order_count', 'old_value': 541, 'new_value': 575}]
2025-05-13 12:02:48,778 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-13 12:02:49,352 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-13 12:02:49,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103737.0, 'new_value': 105735.0}, {'field': 'total_amount', 'old_value': 109687.3, 'new_value': 111685.3}, {'field': 'order_count', 'old_value': 215, 'new_value': 217}]
2025-05-13 12:02:49,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-13 12:02:49,852 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-13 12:02:49,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20674.1, 'new_value': 21872.35}, {'field': 'offline_amount', 'old_value': 54776.0, 'new_value': 57151.0}, {'field': 'total_amount', 'old_value': 75450.1, 'new_value': 79023.35}, {'field': 'order_count', 'old_value': 792, 'new_value': 849}]
2025-05-13 12:02:49,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-13 12:02:50,437 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-13 12:02:50,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65204.0, 'new_value': 68669.0}, {'field': 'offline_amount', 'old_value': 47575.0, 'new_value': 50823.0}, {'field': 'total_amount', 'old_value': 112779.0, 'new_value': 119492.0}, {'field': 'order_count', 'old_value': 1367, 'new_value': 1456}]
2025-05-13 12:02:50,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-13 12:02:51,046 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-13 12:02:51,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5344.4, 'new_value': 5423.4}, {'field': 'offline_amount', 'old_value': 10886.76, 'new_value': 11687.76}, {'field': 'total_amount', 'old_value': 16231.16, 'new_value': 17111.16}, {'field': 'order_count', 'old_value': 165, 'new_value': 174}]
2025-05-13 12:02:51,046 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-13 12:02:51,551 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-13 12:02:51,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63337.0, 'new_value': 63597.0}, {'field': 'total_amount', 'old_value': 69015.48, 'new_value': 69275.48}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-13 12:02:51,551 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-13 12:02:51,982 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-13 12:02:51,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7229.07}, {'field': 'total_amount', 'old_value': 44326.65, 'new_value': 51555.72}, {'field': 'order_count', 'old_value': 250, 'new_value': 290}]
2025-05-13 12:02:51,983 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-13 12:02:52,480 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-13 12:02:52,480 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12429.62, 'new_value': 14001.62}, {'field': 'offline_amount', 'old_value': 18184.8, 'new_value': 18443.8}, {'field': 'total_amount', 'old_value': 30614.42, 'new_value': 32445.42}, {'field': 'order_count', 'old_value': 121, 'new_value': 128}]
2025-05-13 12:02:52,480 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-13 12:02:52,914 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-13 12:02:52,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108062.5, 'new_value': 116302.5}, {'field': 'total_amount', 'old_value': 108062.5, 'new_value': 116302.5}, {'field': 'order_count', 'old_value': 541, 'new_value': 590}]
2025-05-13 12:02:52,914 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-13 12:02:53,383 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-13 12:02:53,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2559.0, 'new_value': 3094.0}, {'field': 'offline_amount', 'old_value': 8088.0, 'new_value': 9774.0}, {'field': 'total_amount', 'old_value': 10647.0, 'new_value': 12868.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 126}]
2025-05-13 12:02:53,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-13 12:02:53,800 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-13 12:02:53,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21827.59, 'new_value': 23580.09}, {'field': 'total_amount', 'old_value': 25074.59, 'new_value': 26827.09}, {'field': 'order_count', 'old_value': 238, 'new_value': 257}]
2025-05-13 12:02:53,800 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-13 12:02:54,342 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-13 12:02:54,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80063.98, 'new_value': 83979.78}, {'field': 'total_amount', 'old_value': 80063.98, 'new_value': 83979.78}, {'field': 'order_count', 'old_value': 266, 'new_value': 284}]
2025-05-13 12:02:54,342 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-13 12:02:54,835 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-13 12:02:54,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8333.0, 'new_value': 8632.0}, {'field': 'total_amount', 'old_value': 8333.0, 'new_value': 8632.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-13 12:02:54,835 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-13 12:02:55,273 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-13 12:02:55,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36462.0, 'new_value': 38299.0}, {'field': 'offline_amount', 'old_value': 121673.0, 'new_value': 130182.0}, {'field': 'total_amount', 'old_value': 158135.0, 'new_value': 168481.0}, {'field': 'order_count', 'old_value': 709, 'new_value': 758}]
2025-05-13 12:02:55,274 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-13 12:02:55,733 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-13 12:02:55,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 598648.0, 'new_value': 624001.0}, {'field': 'total_amount', 'old_value': 598648.0, 'new_value': 624001.0}, {'field': 'order_count', 'old_value': 2563, 'new_value': 2663}]
2025-05-13 12:02:55,733 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-13 12:02:56,222 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-13 12:02:56,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7707032.0, 'new_value': 8071954.0}, {'field': 'total_amount', 'old_value': 7707032.0, 'new_value': 8071954.0}, {'field': 'order_count', 'old_value': 22717, 'new_value': 23794}]
2025-05-13 12:02:56,222 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-13 12:02:56,790 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-13 12:02:56,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1938998.44, 'new_value': 2050780.4}, {'field': 'total_amount', 'old_value': 1938998.44, 'new_value': 2050780.4}, {'field': 'order_count', 'old_value': 3228, 'new_value': 3420}]
2025-05-13 12:02:56,790 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-13 12:02:57,290 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-13 12:02:57,291 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 7439.64}, {'field': 'total_amount', 'old_value': 74508.81, 'new_value': 81948.45}, {'field': 'order_count', 'old_value': 5071, 'new_value': 5631}]
2025-05-13 12:02:57,291 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-13 12:02:57,773 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-13 12:02:57,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134290.0, 'new_value': 147325.0}, {'field': 'total_amount', 'old_value': 134290.0, 'new_value': 147325.0}, {'field': 'order_count', 'old_value': 2767, 'new_value': 3077}]
2025-05-13 12:02:57,773 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-13 12:02:58,293 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-13 12:02:58,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126417.0, 'new_value': 132421.0}, {'field': 'total_amount', 'old_value': 126417.0, 'new_value': 132421.0}, {'field': 'order_count', 'old_value': 254, 'new_value': 264}]
2025-05-13 12:02:58,293 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-13 12:02:58,781 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-13 12:02:58,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16536.93, 'new_value': 23313.44}, {'field': 'offline_amount', 'old_value': 25912.81, 'new_value': 33861.4}, {'field': 'total_amount', 'old_value': 42449.74, 'new_value': 57174.84}, {'field': 'order_count', 'old_value': 949, 'new_value': 1310}]
2025-05-13 12:02:58,781 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-13 12:02:59,343 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-13 12:02:59,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67900.7, 'new_value': 76990.7}, {'field': 'total_amount', 'old_value': 156325.11, 'new_value': 165415.11}]
2025-05-13 12:02:59,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-13 12:02:59,814 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-13 12:02:59,815 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21970.4, 'new_value': 24518.0}, {'field': 'total_amount', 'old_value': 21970.4, 'new_value': 24518.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 138}]
2025-05-13 12:02:59,815 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-13 12:03:00,313 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-13 12:03:00,314 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82531.0, 'new_value': 85116.0}, {'field': 'offline_amount', 'old_value': 61151.0, 'new_value': 69620.0}, {'field': 'total_amount', 'old_value': 143682.0, 'new_value': 154736.0}, {'field': 'order_count', 'old_value': 475, 'new_value': 503}]
2025-05-13 12:03:00,314 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-13 12:03:00,799 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-13 12:03:00,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56054.0, 'new_value': 62461.0}, {'field': 'total_amount', 'old_value': 56054.0, 'new_value': 62461.0}, {'field': 'order_count', 'old_value': 4042, 'new_value': 4545}]
2025-05-13 12:03:00,799 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-13 12:03:01,262 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-13 12:03:01,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20996.0, 'new_value': 21077.0}, {'field': 'total_amount', 'old_value': 20996.0, 'new_value': 21077.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-13 12:03:01,262 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-13 12:03:01,723 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-13 12:03:01,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48239.2, 'new_value': 48404.2}, {'field': 'total_amount', 'old_value': 48352.2, 'new_value': 48517.2}, {'field': 'order_count', 'old_value': 741, 'new_value': 744}]
2025-05-13 12:03:01,724 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-13 12:03:02,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-13 12:03:02,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8560.1, 'new_value': 9974.1}, {'field': 'offline_amount', 'old_value': 27259.4, 'new_value': 28930.4}, {'field': 'total_amount', 'old_value': 35819.5, 'new_value': 38904.5}, {'field': 'order_count', 'old_value': 1360, 'new_value': 1460}]
2025-05-13 12:03:02,196 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-13 12:03:02,601 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-13 12:03:02,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2321494.39, 'new_value': 2465464.39}, {'field': 'total_amount', 'old_value': 2321494.39, 'new_value': 2465464.39}, {'field': 'order_count', 'old_value': 47398, 'new_value': 50953}]
2025-05-13 12:03:02,601 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-13 12:03:03,082 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-13 12:03:03,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13416.29, 'new_value': 13600.29}, {'field': 'total_amount', 'old_value': 13416.29, 'new_value': 13600.29}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-05-13 12:03:03,082 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-13 12:03:03,677 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-13 12:03:03,677 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 4105, 'new_value': 28555}]
2025-05-13 12:03:03,678 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-13 12:03:04,106 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-13 12:03:04,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75770.31, 'new_value': 85523.96}, {'field': 'total_amount', 'old_value': 75770.31, 'new_value': 85523.96}, {'field': 'order_count', 'old_value': 1572, 'new_value': 1725}]
2025-05-13 12:03:04,106 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-13 12:03:04,560 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-13 12:03:04,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137813.0, 'new_value': 149110.0}, {'field': 'total_amount', 'old_value': 137813.0, 'new_value': 149110.0}, {'field': 'order_count', 'old_value': 2936, 'new_value': 3207}]
2025-05-13 12:03:04,560 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-13 12:03:05,041 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-13 12:03:05,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62940.2, 'new_value': 67334.5}, {'field': 'total_amount', 'old_value': 62940.2, 'new_value': 67334.5}, {'field': 'order_count', 'old_value': 1573, 'new_value': 1698}]
2025-05-13 12:03:05,041 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-13 12:03:05,510 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-13 12:03:05,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13122.0, 'new_value': 14112.0}, {'field': 'total_amount', 'old_value': 13122.0, 'new_value': 14112.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 50}]
2025-05-13 12:03:05,511 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-13 12:03:05,993 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-13 12:03:05,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42947.14, 'new_value': 45391.14}, {'field': 'offline_amount', 'old_value': 242137.7, 'new_value': 248957.2}, {'field': 'total_amount', 'old_value': 285084.84, 'new_value': 294348.34}, {'field': 'order_count', 'old_value': 1705, 'new_value': 1809}]
2025-05-13 12:03:05,994 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-13 12:03:06,510 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-13 12:03:06,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21425.95, 'new_value': 24926.98}, {'field': 'total_amount', 'old_value': 40303.42, 'new_value': 43804.45}, {'field': 'order_count', 'old_value': 2574, 'new_value': 2810}]
2025-05-13 12:03:06,511 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-13 12:03:06,966 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-13 12:03:06,966 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32074.69, 'new_value': 39120.37}, {'field': 'total_amount', 'old_value': 62482.83, 'new_value': 69528.51}, {'field': 'order_count', 'old_value': 4018, 'new_value': 4508}]
2025-05-13 12:03:06,966 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-13 12:03:07,497 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-13 12:03:07,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 565896.74, 'new_value': 597087.54}, {'field': 'total_amount', 'old_value': 565896.74, 'new_value': 597087.54}, {'field': 'order_count', 'old_value': 1677, 'new_value': 1777}]
2025-05-13 12:03:07,497 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-13 12:03:07,970 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-13 12:03:07,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68167.0, 'new_value': 76960.0}, {'field': 'total_amount', 'old_value': 68167.0, 'new_value': 76960.0}, {'field': 'order_count', 'old_value': 2438, 'new_value': 2703}]
2025-05-13 12:03:07,971 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-13 12:03:08,450 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-13 12:03:08,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 345816.13, 'new_value': 360876.71}, {'field': 'total_amount', 'old_value': 345816.13, 'new_value': 360876.71}, {'field': 'order_count', 'old_value': 1570, 'new_value': 1661}]
2025-05-13 12:03:08,450 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-13 12:03:08,888 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-13 12:03:08,888 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 373447.29, 'new_value': 555932.85}, {'field': 'total_amount', 'old_value': 373447.29, 'new_value': 555932.85}, {'field': 'order_count', 'old_value': 1321, 'new_value': 1949}]
2025-05-13 12:03:08,888 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-13 12:03:09,383 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-13 12:03:09,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28000.0, 'new_value': 42000.0}, {'field': 'total_amount', 'old_value': 28000.0, 'new_value': 42000.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-13 12:03:09,383 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-13 12:03:09,893 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-13 12:03:09,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367531.07, 'new_value': 399911.57}, {'field': 'total_amount', 'old_value': 367531.07, 'new_value': 399911.57}, {'field': 'order_count', 'old_value': 1149, 'new_value': 1265}]
2025-05-13 12:03:09,894 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-13 12:03:10,437 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-13 12:03:10,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 173.1, 'new_value': 1010.9}, {'field': 'offline_amount', 'old_value': 11335.82, 'new_value': 22198.02}, {'field': 'total_amount', 'old_value': 11508.92, 'new_value': 23208.92}, {'field': 'order_count', 'old_value': 77, 'new_value': 169}]
2025-05-13 12:03:10,438 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-13 12:03:10,914 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-13 12:03:10,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165059.74, 'new_value': 171340.81}, {'field': 'total_amount', 'old_value': 165059.74, 'new_value': 171340.81}, {'field': 'order_count', 'old_value': 459, 'new_value': 476}]
2025-05-13 12:03:10,915 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-13 12:03:11,363 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-13 12:03:11,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35406.0, 'new_value': 57339.0}, {'field': 'offline_amount', 'old_value': 22185.0, 'new_value': 35669.0}, {'field': 'total_amount', 'old_value': 57591.0, 'new_value': 93008.0}, {'field': 'order_count', 'old_value': 2474, 'new_value': 3928}]
2025-05-13 12:03:11,363 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-13 12:03:11,833 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-13 12:03:11,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50558.0, 'new_value': 54458.0}, {'field': 'total_amount', 'old_value': 50558.0, 'new_value': 54458.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 265}]
2025-05-13 12:03:11,833 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-13 12:03:12,333 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-13 12:03:12,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85985.0, 'new_value': 100085.0}, {'field': 'total_amount', 'old_value': 85985.0, 'new_value': 100085.0}, {'field': 'order_count', 'old_value': 208, 'new_value': 242}]
2025-05-13 12:03:12,334 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-13 12:03:12,814 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-13 12:03:12,814 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8288.0, 'new_value': 15588.0}, {'field': 'total_amount', 'old_value': 8288.0, 'new_value': 15588.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-13 12:03:12,814 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-13 12:03:13,315 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-13 12:03:13,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20429.0, 'new_value': 22636.0}, {'field': 'total_amount', 'old_value': 20429.0, 'new_value': 22636.0}, {'field': 'order_count', 'old_value': 389, 'new_value': 433}]
2025-05-13 12:03:13,315 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-13 12:03:13,735 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-13 12:03:13,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68351.0, 'new_value': 78713.0}, {'field': 'total_amount', 'old_value': 68351.0, 'new_value': 78713.0}, {'field': 'order_count', 'old_value': 7048, 'new_value': 8217}]
2025-05-13 12:03:13,736 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-13 12:03:14,191 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-13 12:03:14,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63292.0, 'new_value': 67861.0}, {'field': 'total_amount', 'old_value': 63292.0, 'new_value': 67861.0}, {'field': 'order_count', 'old_value': 543, 'new_value': 580}]
2025-05-13 12:03:14,192 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-13 12:03:14,616 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-13 12:03:14,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73651.67, 'new_value': 88271.53}, {'field': 'total_amount', 'old_value': 73651.67, 'new_value': 88271.53}, {'field': 'order_count', 'old_value': 626, 'new_value': 666}]
2025-05-13 12:03:14,617 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-13 12:03:15,124 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-13 12:03:15,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56490.0, 'new_value': 63088.0}, {'field': 'total_amount', 'old_value': 56491.0, 'new_value': 63089.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-13 12:03:15,125 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-13 12:03:15,586 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-13 12:03:15,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3956.6, 'new_value': 4224.6}, {'field': 'total_amount', 'old_value': 3956.6, 'new_value': 4224.6}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-13 12:03:15,587 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-13 12:03:16,015 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-13 12:03:16,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10261.0, 'new_value': 10881.0}, {'field': 'offline_amount', 'old_value': 35155.0, 'new_value': 35293.0}, {'field': 'total_amount', 'old_value': 45416.0, 'new_value': 46174.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 66}]
2025-05-13 12:03:16,015 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-13 12:03:16,474 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-13 12:03:16,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25064.61, 'new_value': 27304.15}, {'field': 'total_amount', 'old_value': 25064.61, 'new_value': 27304.15}, {'field': 'order_count', 'old_value': 389, 'new_value': 427}]
2025-05-13 12:03:16,474 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-13 12:03:16,916 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-13 12:03:16,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 318078.94, 'new_value': 345455.8}, {'field': 'total_amount', 'old_value': 387834.09, 'new_value': 415210.95}, {'field': 'order_count', 'old_value': 855, 'new_value': 922}]
2025-05-13 12:03:16,916 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG
2025-05-13 12:03:17,419 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG
2025-05-13 12:03:17,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399.0, 'new_value': 749.0}, {'field': 'total_amount', 'old_value': 399.0, 'new_value': 749.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-13 12:03:17,419 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-13 12:03:17,858 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-13 12:03:17,858 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36315.91, 'new_value': 40561.16}, {'field': 'offline_amount', 'old_value': 361476.14, 'new_value': 387559.86}, {'field': 'total_amount', 'old_value': 395917.72, 'new_value': 426246.69}, {'field': 'order_count', 'old_value': 1878, 'new_value': 2045}]
2025-05-13 12:03:17,858 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-13 12:03:18,304 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-13 12:03:18,304 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43330.0, 'new_value': 50302.0}, {'field': 'total_amount', 'old_value': 43330.0, 'new_value': 50302.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 163}]
2025-05-13 12:03:18,304 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-13 12:03:18,752 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-13 12:03:18,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21731.0, 'new_value': 22029.0}, {'field': 'total_amount', 'old_value': 27049.0, 'new_value': 27347.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-13 12:03:18,752 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-13 12:03:19,275 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-13 12:03:19,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8106888.02, 'new_value': 8772035.88}, {'field': 'total_amount', 'old_value': 8106888.02, 'new_value': 8772035.88}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 12:03:19,275 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-13 12:03:19,741 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-13 12:03:19,741 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82335.29, 'new_value': 88707.81}, {'field': 'total_amount', 'old_value': 82335.29, 'new_value': 88707.81}, {'field': 'order_count', 'old_value': 8383, 'new_value': 9089}]
2025-05-13 12:03:19,741 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-13 12:03:20,218 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-13 12:03:20,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11027.0, 'new_value': 11945.0}, {'field': 'total_amount', 'old_value': 11027.0, 'new_value': 11945.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 70}]
2025-05-13 12:03:20,218 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-13 12:03:20,677 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-13 12:03:20,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16727.43, 'new_value': 17969.43}, {'field': 'total_amount', 'old_value': 16727.43, 'new_value': 17969.43}, {'field': 'order_count', 'old_value': 611, 'new_value': 676}]
2025-05-13 12:03:20,677 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-13 12:03:21,086 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-13 12:03:21,087 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 701.56, 'new_value': 781.56}, {'field': 'offline_amount', 'old_value': 3200.98, 'new_value': 6124.03}, {'field': 'total_amount', 'old_value': 3902.54, 'new_value': 6905.59}, {'field': 'order_count', 'old_value': 154, 'new_value': 297}]
2025-05-13 12:03:21,087 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-13 12:03:21,626 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-13 12:03:21,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6601.24, 'new_value': 10575.93}, {'field': 'total_amount', 'old_value': 6601.24, 'new_value': 10575.93}, {'field': 'order_count', 'old_value': 36, 'new_value': 57}]
2025-05-13 12:03:21,626 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMM2
2025-05-13 12:03:22,016 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMM2
2025-05-13 12:03:22,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18168.1, 'new_value': 0.0}, {'field': 'total_amount', 'old_value': 18168.1, 'new_value': 0.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 0}]
2025-05-13 12:03:22,016 - INFO - 开始批量插入 1 条新记录
2025-05-13 12:03:22,170 - INFO - 批量插入响应状态码: 200
2025-05-13 12:03:22,171 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 13 May 2025 04:02:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DFD25F0A-EED9-702A-8538-2B55677AB130', 'x-acs-trace-id': '20c2ab104f4a065cfb588654a82f1ffa', 'etag': '6Bj6qM2kJ5ebJdlHrAoCqkg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-13 12:03:22,171 - INFO - 批量插入响应体: {'result': ['FINST-A17661C1T7AV4ABFACQ398ZG35BX3Q7QKZLAMD7']}
2025-05-13 12:03:22,171 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-13 12:03:22,171 - INFO - 成功插入的数据ID: ['FINST-A17661C1T7AV4ABFACQ398ZG35BX3Q7QKZLAMD7']
2025-05-13 12:03:25,172 - INFO - 批量插入完成，共 1 条记录
2025-05-13 12:03:25,172 - INFO - 日期 2025-05 处理完成 - 更新: 359 条，插入: 1 条，错误: 0 条
2025-05-13 12:03:25,172 - INFO - 数据同步完成！更新: 360 条，插入: 1 条，错误: 0 条
2025-05-13 12:03:25,174 - INFO - =================同步完成====================
2025-05-13 15:00:01,877 - INFO - =================使用默认全量同步=============
2025-05-13 15:00:03,304 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-13 15:00:03,304 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 15:00:03,332 - INFO - 开始处理日期: 2025-01
2025-05-13 15:00:03,335 - INFO - Request Parameters - Page 1:
2025-05-13 15:00:03,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:03,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:04,132 - INFO - Response - Page 1:
2025-05-13 15:00:04,332 - INFO - 第 1 页获取到 100 条记录
2025-05-13 15:00:04,332 - INFO - Request Parameters - Page 2:
2025-05-13 15:00:04,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:04,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:05,234 - INFO - Response - Page 2:
2025-05-13 15:00:05,435 - INFO - 第 2 页获取到 100 条记录
2025-05-13 15:00:05,435 - INFO - Request Parameters - Page 3:
2025-05-13 15:00:05,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:05,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:05,969 - INFO - Response - Page 3:
2025-05-13 15:00:06,169 - INFO - 第 3 页获取到 100 条记录
2025-05-13 15:00:06,169 - INFO - Request Parameters - Page 4:
2025-05-13 15:00:06,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:06,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:06,624 - INFO - Response - Page 4:
2025-05-13 15:00:06,824 - INFO - 第 4 页获取到 100 条记录
2025-05-13 15:00:06,824 - INFO - Request Parameters - Page 5:
2025-05-13 15:00:06,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:06,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:07,273 - INFO - Response - Page 5:
2025-05-13 15:00:07,474 - INFO - 第 5 页获取到 100 条记录
2025-05-13 15:00:07,474 - INFO - Request Parameters - Page 6:
2025-05-13 15:00:07,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:07,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:07,978 - INFO - Response - Page 6:
2025-05-13 15:00:08,178 - INFO - 第 6 页获取到 100 条记录
2025-05-13 15:00:08,178 - INFO - Request Parameters - Page 7:
2025-05-13 15:00:08,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:08,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:08,659 - INFO - Response - Page 7:
2025-05-13 15:00:08,859 - INFO - 第 7 页获取到 82 条记录
2025-05-13 15:00:08,859 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 15:00:08,859 - INFO - 获取到 682 条表单数据
2025-05-13 15:00:08,872 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 15:00:08,883 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 15:00:08,883 - INFO - 开始处理日期: 2025-02
2025-05-13 15:00:08,884 - INFO - Request Parameters - Page 1:
2025-05-13 15:00:08,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:08,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:09,479 - INFO - Response - Page 1:
2025-05-13 15:00:09,679 - INFO - 第 1 页获取到 100 条记录
2025-05-13 15:00:09,679 - INFO - Request Parameters - Page 2:
2025-05-13 15:00:09,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:09,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:10,267 - INFO - Response - Page 2:
2025-05-13 15:00:10,468 - INFO - 第 2 页获取到 100 条记录
2025-05-13 15:00:10,468 - INFO - Request Parameters - Page 3:
2025-05-13 15:00:10,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:10,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:11,109 - INFO - Response - Page 3:
2025-05-13 15:00:11,310 - INFO - 第 3 页获取到 100 条记录
2025-05-13 15:00:11,310 - INFO - Request Parameters - Page 4:
2025-05-13 15:00:11,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:11,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:11,750 - INFO - Response - Page 4:
2025-05-13 15:00:11,950 - INFO - 第 4 页获取到 100 条记录
2025-05-13 15:00:11,950 - INFO - Request Parameters - Page 5:
2025-05-13 15:00:11,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:11,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:12,463 - INFO - Response - Page 5:
2025-05-13 15:00:12,663 - INFO - 第 5 页获取到 100 条记录
2025-05-13 15:00:12,663 - INFO - Request Parameters - Page 6:
2025-05-13 15:00:12,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:12,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:13,177 - INFO - Response - Page 6:
2025-05-13 15:00:13,377 - INFO - 第 6 页获取到 100 条记录
2025-05-13 15:00:13,377 - INFO - Request Parameters - Page 7:
2025-05-13 15:00:13,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:13,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:13,837 - INFO - Response - Page 7:
2025-05-13 15:00:14,037 - INFO - 第 7 页获取到 70 条记录
2025-05-13 15:00:14,037 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 15:00:14,037 - INFO - 获取到 670 条表单数据
2025-05-13 15:00:14,050 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 15:00:14,062 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 15:00:14,062 - INFO - 开始处理日期: 2025-03
2025-05-13 15:00:14,062 - INFO - Request Parameters - Page 1:
2025-05-13 15:00:14,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:14,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:14,564 - INFO - Response - Page 1:
2025-05-13 15:00:14,765 - INFO - 第 1 页获取到 100 条记录
2025-05-13 15:00:14,765 - INFO - Request Parameters - Page 2:
2025-05-13 15:00:14,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:14,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:15,211 - INFO - Response - Page 2:
2025-05-13 15:00:15,411 - INFO - 第 2 页获取到 100 条记录
2025-05-13 15:00:15,411 - INFO - Request Parameters - Page 3:
2025-05-13 15:00:15,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:15,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:15,920 - INFO - Response - Page 3:
2025-05-13 15:00:16,120 - INFO - 第 3 页获取到 100 条记录
2025-05-13 15:00:16,120 - INFO - Request Parameters - Page 4:
2025-05-13 15:00:16,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:16,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:16,629 - INFO - Response - Page 4:
2025-05-13 15:00:16,829 - INFO - 第 4 页获取到 100 条记录
2025-05-13 15:00:16,829 - INFO - Request Parameters - Page 5:
2025-05-13 15:00:16,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:16,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:17,269 - INFO - Response - Page 5:
2025-05-13 15:00:17,469 - INFO - 第 5 页获取到 100 条记录
2025-05-13 15:00:17,469 - INFO - Request Parameters - Page 6:
2025-05-13 15:00:17,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:17,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:17,960 - INFO - Response - Page 6:
2025-05-13 15:00:18,160 - INFO - 第 6 页获取到 100 条记录
2025-05-13 15:00:18,160 - INFO - Request Parameters - Page 7:
2025-05-13 15:00:18,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:18,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:18,651 - INFO - Response - Page 7:
2025-05-13 15:00:18,853 - INFO - 第 7 页获取到 61 条记录
2025-05-13 15:00:18,853 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 15:00:18,853 - INFO - 获取到 661 条表单数据
2025-05-13 15:00:18,866 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 15:00:18,878 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 15:00:18,878 - INFO - 开始处理日期: 2025-04
2025-05-13 15:00:18,878 - INFO - Request Parameters - Page 1:
2025-05-13 15:00:18,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:18,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:19,460 - INFO - Response - Page 1:
2025-05-13 15:00:19,660 - INFO - 第 1 页获取到 100 条记录
2025-05-13 15:00:19,660 - INFO - Request Parameters - Page 2:
2025-05-13 15:00:19,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:19,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:20,192 - INFO - Response - Page 2:
2025-05-13 15:00:20,393 - INFO - 第 2 页获取到 100 条记录
2025-05-13 15:00:20,393 - INFO - Request Parameters - Page 3:
2025-05-13 15:00:20,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:20,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:20,987 - INFO - Response - Page 3:
2025-05-13 15:00:21,187 - INFO - 第 3 页获取到 100 条记录
2025-05-13 15:00:21,187 - INFO - Request Parameters - Page 4:
2025-05-13 15:00:21,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:21,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:21,681 - INFO - Response - Page 4:
2025-05-13 15:00:21,882 - INFO - 第 4 页获取到 100 条记录
2025-05-13 15:00:21,882 - INFO - Request Parameters - Page 5:
2025-05-13 15:00:21,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:21,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:22,377 - INFO - Response - Page 5:
2025-05-13 15:00:22,577 - INFO - 第 5 页获取到 100 条记录
2025-05-13 15:00:22,577 - INFO - Request Parameters - Page 6:
2025-05-13 15:00:22,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:22,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:23,163 - INFO - Response - Page 6:
2025-05-13 15:00:23,364 - INFO - 第 6 页获取到 100 条记录
2025-05-13 15:00:23,364 - INFO - Request Parameters - Page 7:
2025-05-13 15:00:23,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:23,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:23,811 - INFO - Response - Page 7:
2025-05-13 15:00:24,012 - INFO - 第 7 页获取到 54 条记录
2025-05-13 15:00:24,012 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 15:00:24,012 - INFO - 获取到 654 条表单数据
2025-05-13 15:00:24,024 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 15:00:24,026 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-05-13 15:00:24,377 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M1A
2025-05-13 15:00:24,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10913.4, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 105614.5, 'new_value': 122157.6}, {'field': 'total_amount', 'old_value': 116527.9, 'new_value': 122157.6}]
2025-05-13 15:00:24,384 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MG2
2025-05-13 15:00:24,852 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MG2
2025-05-13 15:00:24,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12730.0, 'new_value': 128687.0}, {'field': 'total_amount', 'old_value': 12730.0, 'new_value': 128687.0}]
2025-05-13 15:00:24,852 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-05-13 15:00:25,273 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M66
2025-05-13 15:00:25,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340944.0, 'new_value': 410943.62}, {'field': 'total_amount', 'old_value': 340944.0, 'new_value': 410943.62}, {'field': 'order_count', 'old_value': 12779, 'new_value': 3979}]
2025-05-13 15:00:25,278 - INFO - 日期 2025-04 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-13 15:00:25,279 - INFO - 开始处理日期: 2025-05
2025-05-13 15:00:25,279 - INFO - Request Parameters - Page 1:
2025-05-13 15:00:25,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:25,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:25,831 - INFO - Response - Page 1:
2025-05-13 15:00:26,031 - INFO - 第 1 页获取到 100 条记录
2025-05-13 15:00:26,031 - INFO - Request Parameters - Page 2:
2025-05-13 15:00:26,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:26,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:26,576 - INFO - Response - Page 2:
2025-05-13 15:00:26,776 - INFO - 第 2 页获取到 100 条记录
2025-05-13 15:00:26,776 - INFO - Request Parameters - Page 3:
2025-05-13 15:00:26,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:26,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:27,240 - INFO - Response - Page 3:
2025-05-13 15:00:27,441 - INFO - 第 3 页获取到 100 条记录
2025-05-13 15:00:27,441 - INFO - Request Parameters - Page 4:
2025-05-13 15:00:27,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:27,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:27,949 - INFO - Response - Page 4:
2025-05-13 15:00:28,149 - INFO - 第 4 页获取到 100 条记录
2025-05-13 15:00:28,149 - INFO - Request Parameters - Page 5:
2025-05-13 15:00:28,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:28,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:28,618 - INFO - Response - Page 5:
2025-05-13 15:00:28,818 - INFO - 第 5 页获取到 100 条记录
2025-05-13 15:00:28,818 - INFO - Request Parameters - Page 6:
2025-05-13 15:00:28,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:28,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:29,410 - INFO - Response - Page 6:
2025-05-13 15:00:29,610 - INFO - 第 6 页获取到 100 条记录
2025-05-13 15:00:29,610 - INFO - Request Parameters - Page 7:
2025-05-13 15:00:29,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 15:00:29,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 15:00:29,987 - INFO - Response - Page 7:
2025-05-13 15:00:30,187 - INFO - 第 7 页获取到 25 条记录
2025-05-13 15:00:30,187 - INFO - 查询完成，共获取到 625 条记录
2025-05-13 15:00:30,187 - INFO - 获取到 625 条表单数据
2025-05-13 15:00:30,200 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-13 15:00:30,201 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-13 15:00:30,651 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-13 15:00:30,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 422708.0, 'new_value': 520386.0}, {'field': 'offline_amount', 'old_value': 143644.0, 'new_value': 154533.0}, {'field': 'total_amount', 'old_value': 566352.0, 'new_value': 674919.0}, {'field': 'order_count', 'old_value': 666, 'new_value': 715}]
2025-05-13 15:00:30,652 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-13 15:00:31,188 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-13 15:00:31,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6197.0, 'new_value': 6877.0}, {'field': 'total_amount', 'old_value': 6197.0, 'new_value': 6877.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-13 15:00:31,189 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-13 15:00:31,704 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-13 15:00:31,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9283.2, 'new_value': 10671.2}, {'field': 'offline_amount', 'old_value': 52348.66, 'new_value': 55145.66}, {'field': 'total_amount', 'old_value': 61631.86, 'new_value': 65816.86}, {'field': 'order_count', 'old_value': 122, 'new_value': 124}]
2025-05-13 15:00:31,704 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-13 15:00:32,170 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-13 15:00:32,170 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12912.78, 'new_value': 15527.14}, {'field': 'total_amount', 'old_value': 12912.78, 'new_value': 15527.14}, {'field': 'order_count', 'old_value': 1005, 'new_value': 1207}]
2025-05-13 15:00:32,170 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-13 15:00:32,606 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-13 15:00:32,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18817.0, 'new_value': 20067.0}, {'field': 'total_amount', 'old_value': 21161.0, 'new_value': 22411.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-05-13 15:00:32,607 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-13 15:00:33,104 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-13 15:00:33,104 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24737.75, 'new_value': 27305.01}, {'field': 'offline_amount', 'old_value': 257344.26, 'new_value': 273985.86}, {'field': 'total_amount', 'old_value': 282082.01, 'new_value': 301290.87}, {'field': 'order_count', 'old_value': 882, 'new_value': 933}]
2025-05-13 15:00:33,105 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-13 15:00:33,570 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-13 15:00:33,570 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1.0, 'new_value': 2.0}, {'field': 'offline_amount', 'old_value': 20628.6, 'new_value': 23174.6}, {'field': 'total_amount', 'old_value': 20629.6, 'new_value': 23176.6}, {'field': 'order_count', 'old_value': 115, 'new_value': 132}]
2025-05-13 15:00:33,571 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-13 15:00:34,113 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-13 15:00:34,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35415.63, 'new_value': 38364.0}, {'field': 'offline_amount', 'old_value': 18399.01, 'new_value': 20350.54}, {'field': 'total_amount', 'old_value': 53814.64, 'new_value': 58714.54}, {'field': 'order_count', 'old_value': 1778, 'new_value': 1959}]
2025-05-13 15:00:34,114 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-13 15:00:34,576 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-13 15:00:34,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18483.0, 'new_value': 20013.0}, {'field': 'total_amount', 'old_value': 18483.0, 'new_value': 20013.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-13 15:00:34,577 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-13 15:00:35,012 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-13 15:00:35,012 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110408.96, 'new_value': 123117.78}, {'field': 'offline_amount', 'old_value': 732.0, 'new_value': 757.0}, {'field': 'total_amount', 'old_value': 111140.96, 'new_value': 123874.78}, {'field': 'order_count', 'old_value': 1280, 'new_value': 1427}]
2025-05-13 15:00:35,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-13 15:00:35,580 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-13 15:00:35,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65114.0, 'new_value': 68752.66}, {'field': 'total_amount', 'old_value': 66469.62, 'new_value': 70108.28}, {'field': 'order_count', 'old_value': 278, 'new_value': 301}]
2025-05-13 15:00:35,582 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-13 15:00:36,022 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-13 15:00:36,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35866.36, 'new_value': 36680.4}, {'field': 'total_amount', 'old_value': 35866.36, 'new_value': 36680.4}, {'field': 'order_count', 'old_value': 35, 'new_value': 39}]
2025-05-13 15:00:36,023 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-13 15:00:36,559 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-13 15:00:36,559 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38853.37, 'new_value': 42645.61}, {'field': 'offline_amount', 'old_value': 28804.36, 'new_value': 31263.82}, {'field': 'total_amount', 'old_value': 67657.73, 'new_value': 73909.43}, {'field': 'order_count', 'old_value': 2738, 'new_value': 2980}]
2025-05-13 15:00:36,560 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-13 15:00:36,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-13 15:00:36,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46530.0, 'new_value': 48710.0}, {'field': 'total_amount', 'old_value': 46530.0, 'new_value': 48710.0}, {'field': 'order_count', 'old_value': 2313, 'new_value': 2530}]
2025-05-13 15:00:36,981 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-13 15:00:37,464 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-13 15:00:37,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3929.0, 'new_value': 4148.0}, {'field': 'total_amount', 'old_value': 3929.0, 'new_value': 4148.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-13 15:00:37,465 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-13 15:00:37,918 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-13 15:00:37,918 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10554.5, 'new_value': 10584.5}, {'field': 'total_amount', 'old_value': 10554.5, 'new_value': 10584.5}]
2025-05-13 15:00:37,919 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-13 15:00:38,384 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-13 15:00:38,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34670.09, 'new_value': 38184.53}, {'field': 'offline_amount', 'old_value': 93403.09, 'new_value': 103698.25}, {'field': 'total_amount', 'old_value': 128073.18, 'new_value': 141882.78}, {'field': 'order_count', 'old_value': 5737, 'new_value': 6469}]
2025-05-13 15:00:38,386 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-13 15:00:38,856 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-13 15:00:38,856 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21437.74, 'new_value': 23068.05}, {'field': 'offline_amount', 'old_value': 599022.05, 'new_value': 626024.4}, {'field': 'total_amount', 'old_value': 620459.79, 'new_value': 649092.45}, {'field': 'order_count', 'old_value': 2748, 'new_value': 2930}]
2025-05-13 15:00:38,856 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-13 15:00:39,224 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-13 15:00:39,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124705.0, 'new_value': 127604.0}, {'field': 'total_amount', 'old_value': 128805.0, 'new_value': 131704.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 92}]
2025-05-13 15:00:39,225 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-13 15:00:39,709 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-13 15:00:39,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18047.0, 'new_value': 18072.0}, {'field': 'total_amount', 'old_value': 18047.0, 'new_value': 18072.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 91}]
2025-05-13 15:00:39,710 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-13 15:00:40,135 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-13 15:00:40,136 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7218.1, 'new_value': 8373.6}, {'field': 'offline_amount', 'old_value': 72608.1, 'new_value': 81371.2}, {'field': 'total_amount', 'old_value': 79826.2, 'new_value': 89744.8}, {'field': 'order_count', 'old_value': 2541, 'new_value': 2752}]
2025-05-13 15:00:40,136 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-13 15:00:40,583 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-13 15:00:40,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23313.44, 'new_value': 25045.69}, {'field': 'offline_amount', 'old_value': 33861.4, 'new_value': 35260.97}, {'field': 'total_amount', 'old_value': 57174.84, 'new_value': 60306.66}, {'field': 'order_count', 'old_value': 1310, 'new_value': 1342}]
2025-05-13 15:00:40,584 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-13 15:00:41,072 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-13 15:00:41,072 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3390.0, 'new_value': 3866.0}, {'field': 'offline_amount', 'old_value': 23505.0, 'new_value': 26781.0}, {'field': 'total_amount', 'old_value': 26895.0, 'new_value': 30647.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 39}]
2025-05-13 15:00:41,073 - INFO - 日期 2025-05 处理完成 - 更新: 23 条，插入: 0 条，错误: 0 条
2025-05-13 15:00:41,073 - INFO - 数据同步完成！更新: 26 条，插入: 0 条，错误: 0 条
2025-05-13 15:00:41,075 - INFO - =================同步完成====================
2025-05-13 18:00:01,996 - INFO - =================使用默认全量同步=============
2025-05-13 18:00:03,355 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-13 18:00:03,355 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 18:00:03,386 - INFO - 开始处理日期: 2025-01
2025-05-13 18:00:03,386 - INFO - Request Parameters - Page 1:
2025-05-13 18:00:03,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:03,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:04,418 - INFO - Response - Page 1:
2025-05-13 18:00:04,621 - INFO - 第 1 页获取到 100 条记录
2025-05-13 18:00:04,621 - INFO - Request Parameters - Page 2:
2025-05-13 18:00:04,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:04,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:05,449 - INFO - Response - Page 2:
2025-05-13 18:00:05,652 - INFO - 第 2 页获取到 100 条记录
2025-05-13 18:00:05,652 - INFO - Request Parameters - Page 3:
2025-05-13 18:00:05,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:05,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:06,136 - INFO - Response - Page 3:
2025-05-13 18:00:06,339 - INFO - 第 3 页获取到 100 条记录
2025-05-13 18:00:06,339 - INFO - Request Parameters - Page 4:
2025-05-13 18:00:06,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:06,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:06,793 - INFO - Response - Page 4:
2025-05-13 18:00:06,996 - INFO - 第 4 页获取到 100 条记录
2025-05-13 18:00:06,996 - INFO - Request Parameters - Page 5:
2025-05-13 18:00:06,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:06,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:07,464 - INFO - Response - Page 5:
2025-05-13 18:00:07,668 - INFO - 第 5 页获取到 100 条记录
2025-05-13 18:00:07,668 - INFO - Request Parameters - Page 6:
2025-05-13 18:00:07,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:07,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:08,168 - INFO - Response - Page 6:
2025-05-13 18:00:08,371 - INFO - 第 6 页获取到 100 条记录
2025-05-13 18:00:08,371 - INFO - Request Parameters - Page 7:
2025-05-13 18:00:08,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:08,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:08,824 - INFO - Response - Page 7:
2025-05-13 18:00:09,027 - INFO - 第 7 页获取到 82 条记录
2025-05-13 18:00:09,027 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 18:00:09,027 - INFO - 获取到 682 条表单数据
2025-05-13 18:00:09,027 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 18:00:09,043 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 18:00:09,043 - INFO - 开始处理日期: 2025-02
2025-05-13 18:00:09,043 - INFO - Request Parameters - Page 1:
2025-05-13 18:00:09,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:09,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:09,527 - INFO - Response - Page 1:
2025-05-13 18:00:09,730 - INFO - 第 1 页获取到 100 条记录
2025-05-13 18:00:09,730 - INFO - Request Parameters - Page 2:
2025-05-13 18:00:09,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:09,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:10,246 - INFO - Response - Page 2:
2025-05-13 18:00:10,449 - INFO - 第 2 页获取到 100 条记录
2025-05-13 18:00:10,449 - INFO - Request Parameters - Page 3:
2025-05-13 18:00:10,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:10,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:10,964 - INFO - Response - Page 3:
2025-05-13 18:00:11,168 - INFO - 第 3 页获取到 100 条记录
2025-05-13 18:00:11,168 - INFO - Request Parameters - Page 4:
2025-05-13 18:00:11,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:11,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:11,652 - INFO - Response - Page 4:
2025-05-13 18:00:11,855 - INFO - 第 4 页获取到 100 条记录
2025-05-13 18:00:11,855 - INFO - Request Parameters - Page 5:
2025-05-13 18:00:11,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:11,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:12,324 - INFO - Response - Page 5:
2025-05-13 18:00:12,527 - INFO - 第 5 页获取到 100 条记录
2025-05-13 18:00:12,527 - INFO - Request Parameters - Page 6:
2025-05-13 18:00:12,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:12,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:13,058 - INFO - Response - Page 6:
2025-05-13 18:00:13,261 - INFO - 第 6 页获取到 100 条记录
2025-05-13 18:00:13,261 - INFO - Request Parameters - Page 7:
2025-05-13 18:00:13,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:13,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:13,839 - INFO - Response - Page 7:
2025-05-13 18:00:14,043 - INFO - 第 7 页获取到 70 条记录
2025-05-13 18:00:14,043 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 18:00:14,043 - INFO - 获取到 670 条表单数据
2025-05-13 18:00:14,043 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 18:00:14,058 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 18:00:14,058 - INFO - 开始处理日期: 2025-03
2025-05-13 18:00:14,058 - INFO - Request Parameters - Page 1:
2025-05-13 18:00:14,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:14,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:14,558 - INFO - Response - Page 1:
2025-05-13 18:00:14,761 - INFO - 第 1 页获取到 100 条记录
2025-05-13 18:00:14,761 - INFO - Request Parameters - Page 2:
2025-05-13 18:00:14,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:14,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:15,339 - INFO - Response - Page 2:
2025-05-13 18:00:15,543 - INFO - 第 2 页获取到 100 条记录
2025-05-13 18:00:15,543 - INFO - Request Parameters - Page 3:
2025-05-13 18:00:15,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:15,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:16,011 - INFO - Response - Page 3:
2025-05-13 18:00:16,214 - INFO - 第 3 页获取到 100 条记录
2025-05-13 18:00:16,214 - INFO - Request Parameters - Page 4:
2025-05-13 18:00:16,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:16,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:16,636 - INFO - Response - Page 4:
2025-05-13 18:00:16,839 - INFO - 第 4 页获取到 100 条记录
2025-05-13 18:00:16,839 - INFO - Request Parameters - Page 5:
2025-05-13 18:00:16,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:16,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:17,246 - INFO - Response - Page 5:
2025-05-13 18:00:17,449 - INFO - 第 5 页获取到 100 条记录
2025-05-13 18:00:17,449 - INFO - Request Parameters - Page 6:
2025-05-13 18:00:17,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:17,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:17,996 - INFO - Response - Page 6:
2025-05-13 18:00:18,199 - INFO - 第 6 页获取到 100 条记录
2025-05-13 18:00:18,199 - INFO - Request Parameters - Page 7:
2025-05-13 18:00:18,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:18,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:18,605 - INFO - Response - Page 7:
2025-05-13 18:00:18,808 - INFO - 第 7 页获取到 61 条记录
2025-05-13 18:00:18,808 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 18:00:18,808 - INFO - 获取到 661 条表单数据
2025-05-13 18:00:18,808 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 18:00:18,824 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 18:00:18,824 - INFO - 开始处理日期: 2025-04
2025-05-13 18:00:18,824 - INFO - Request Parameters - Page 1:
2025-05-13 18:00:18,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:18,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:19,324 - INFO - Response - Page 1:
2025-05-13 18:00:19,527 - INFO - 第 1 页获取到 100 条记录
2025-05-13 18:00:19,527 - INFO - Request Parameters - Page 2:
2025-05-13 18:00:19,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:19,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:19,964 - INFO - Response - Page 2:
2025-05-13 18:00:20,183 - INFO - 第 2 页获取到 100 条记录
2025-05-13 18:00:20,183 - INFO - Request Parameters - Page 3:
2025-05-13 18:00:20,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:20,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:20,667 - INFO - Response - Page 3:
2025-05-13 18:00:20,871 - INFO - 第 3 页获取到 100 条记录
2025-05-13 18:00:20,871 - INFO - Request Parameters - Page 4:
2025-05-13 18:00:20,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:20,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:21,324 - INFO - Response - Page 4:
2025-05-13 18:00:21,527 - INFO - 第 4 页获取到 100 条记录
2025-05-13 18:00:21,527 - INFO - Request Parameters - Page 5:
2025-05-13 18:00:21,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:21,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:22,058 - INFO - Response - Page 5:
2025-05-13 18:00:22,261 - INFO - 第 5 页获取到 100 条记录
2025-05-13 18:00:22,261 - INFO - Request Parameters - Page 6:
2025-05-13 18:00:22,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:22,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:22,714 - INFO - Response - Page 6:
2025-05-13 18:00:22,917 - INFO - 第 6 页获取到 100 条记录
2025-05-13 18:00:22,917 - INFO - Request Parameters - Page 7:
2025-05-13 18:00:22,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:22,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:23,433 - INFO - Response - Page 7:
2025-05-13 18:00:23,636 - INFO - 第 7 页获取到 54 条记录
2025-05-13 18:00:23,636 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 18:00:23,636 - INFO - 获取到 654 条表单数据
2025-05-13 18:00:23,636 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 18:00:23,652 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-05-13 18:00:24,089 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M82
2025-05-13 18:00:24,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16544772.9, 'new_value': 14309306.7}, {'field': 'total_amount', 'old_value': 16544772.9, 'new_value': 14309306.7}]
2025-05-13 18:00:24,089 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-13 18:00:24,089 - INFO - 开始处理日期: 2025-05
2025-05-13 18:00:24,089 - INFO - Request Parameters - Page 1:
2025-05-13 18:00:24,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:24,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:24,558 - INFO - Response - Page 1:
2025-05-13 18:00:24,761 - INFO - 第 1 页获取到 100 条记录
2025-05-13 18:00:24,761 - INFO - Request Parameters - Page 2:
2025-05-13 18:00:24,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:24,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:25,246 - INFO - Response - Page 2:
2025-05-13 18:00:25,449 - INFO - 第 2 页获取到 100 条记录
2025-05-13 18:00:25,449 - INFO - Request Parameters - Page 3:
2025-05-13 18:00:25,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:25,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:25,949 - INFO - Response - Page 3:
2025-05-13 18:00:26,152 - INFO - 第 3 页获取到 100 条记录
2025-05-13 18:00:26,152 - INFO - Request Parameters - Page 4:
2025-05-13 18:00:26,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:26,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:27,089 - INFO - Response - Page 4:
2025-05-13 18:00:27,292 - INFO - 第 4 页获取到 100 条记录
2025-05-13 18:00:27,292 - INFO - Request Parameters - Page 5:
2025-05-13 18:00:27,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:27,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:27,792 - INFO - Response - Page 5:
2025-05-13 18:00:27,996 - INFO - 第 5 页获取到 100 条记录
2025-05-13 18:00:27,996 - INFO - Request Parameters - Page 6:
2025-05-13 18:00:27,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:27,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:28,511 - INFO - Response - Page 6:
2025-05-13 18:00:28,714 - INFO - 第 6 页获取到 100 条记录
2025-05-13 18:00:28,714 - INFO - Request Parameters - Page 7:
2025-05-13 18:00:28,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 18:00:28,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 18:00:29,027 - INFO - Response - Page 7:
2025-05-13 18:00:29,230 - INFO - 第 7 页获取到 25 条记录
2025-05-13 18:00:29,230 - INFO - 查询完成，共获取到 625 条记录
2025-05-13 18:00:29,230 - INFO - 获取到 625 条表单数据
2025-05-13 18:00:29,230 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-13 18:00:29,246 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-13 18:00:29,746 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-13 18:00:29,746 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22009.61, 'new_value': 24345.21}, {'field': 'offline_amount', 'old_value': 310797.42, 'new_value': 332404.12}, {'field': 'total_amount', 'old_value': 332807.03, 'new_value': 356749.33}, {'field': 'order_count', 'old_value': 1373, 'new_value': 1451}]
2025-05-13 18:00:29,761 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-13 18:00:30,292 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-13 18:00:30,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 450394.16, 'new_value': 481420.16}, {'field': 'total_amount', 'old_value': 450394.16, 'new_value': 481420.16}, {'field': 'order_count', 'old_value': 1651, 'new_value': 1776}]
2025-05-13 18:00:30,308 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-13 18:00:30,792 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-13 18:00:30,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66527.99, 'new_value': 71826.99}, {'field': 'total_amount', 'old_value': 66527.99, 'new_value': 71826.99}, {'field': 'order_count', 'old_value': 6100, 'new_value': 6556}]
2025-05-13 18:00:30,792 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-13 18:00:31,230 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-13 18:00:31,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50732.76, 'new_value': 53704.71}, {'field': 'offline_amount', 'old_value': 139064.93, 'new_value': 143295.53}, {'field': 'total_amount', 'old_value': 189797.69, 'new_value': 197000.24}, {'field': 'order_count', 'old_value': 1789, 'new_value': 1943}]
2025-05-13 18:00:31,230 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-13 18:00:31,761 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-13 18:00:31,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358541.0, 'new_value': 408499.0}, {'field': 'total_amount', 'old_value': 358541.0, 'new_value': 408499.0}, {'field': 'order_count', 'old_value': 745, 'new_value': 844}]
2025-05-13 18:00:31,761 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-13 18:00:32,292 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-13 18:00:32,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7329.0, 'new_value': 7609.0}, {'field': 'total_amount', 'old_value': 7329.0, 'new_value': 7609.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-13 18:00:32,292 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-13 18:00:32,871 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-13 18:00:32,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3866.0, 'new_value': 9054.0}, {'field': 'offline_amount', 'old_value': 26781.0, 'new_value': 30521.0}, {'field': 'total_amount', 'old_value': 30647.0, 'new_value': 39575.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 58}]
2025-05-13 18:00:32,871 - INFO - 日期 2025-05 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-05-13 18:00:32,871 - INFO - 数据同步完成！更新: 8 条，插入: 0 条，错误: 0 条
2025-05-13 18:00:32,871 - INFO - =================同步完成====================
2025-05-13 21:00:01,962 - INFO - =================使用默认全量同步=============
2025-05-13 21:00:03,290 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-13 21:00:03,290 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-13 21:00:03,322 - INFO - 开始处理日期: 2025-01
2025-05-13 21:00:03,322 - INFO - Request Parameters - Page 1:
2025-05-13 21:00:03,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:03,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:04,697 - INFO - Response - Page 1:
2025-05-13 21:00:04,900 - INFO - 第 1 页获取到 100 条记录
2025-05-13 21:00:04,900 - INFO - Request Parameters - Page 2:
2025-05-13 21:00:04,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:04,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:05,415 - INFO - Response - Page 2:
2025-05-13 21:00:05,619 - INFO - 第 2 页获取到 100 条记录
2025-05-13 21:00:05,619 - INFO - Request Parameters - Page 3:
2025-05-13 21:00:05,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:05,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:06,119 - INFO - Response - Page 3:
2025-05-13 21:00:06,322 - INFO - 第 3 页获取到 100 条记录
2025-05-13 21:00:06,322 - INFO - Request Parameters - Page 4:
2025-05-13 21:00:06,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:06,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:06,775 - INFO - Response - Page 4:
2025-05-13 21:00:06,978 - INFO - 第 4 页获取到 100 条记录
2025-05-13 21:00:06,978 - INFO - Request Parameters - Page 5:
2025-05-13 21:00:06,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:06,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:07,493 - INFO - Response - Page 5:
2025-05-13 21:00:07,697 - INFO - 第 5 页获取到 100 条记录
2025-05-13 21:00:07,697 - INFO - Request Parameters - Page 6:
2025-05-13 21:00:07,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:07,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:08,212 - INFO - Response - Page 6:
2025-05-13 21:00:08,415 - INFO - 第 6 页获取到 100 条记录
2025-05-13 21:00:08,415 - INFO - Request Parameters - Page 7:
2025-05-13 21:00:08,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:08,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:08,915 - INFO - Response - Page 7:
2025-05-13 21:00:09,118 - INFO - 第 7 页获取到 82 条记录
2025-05-13 21:00:09,118 - INFO - 查询完成，共获取到 682 条记录
2025-05-13 21:00:09,118 - INFO - 获取到 682 条表单数据
2025-05-13 21:00:09,118 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-13 21:00:09,134 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 21:00:09,134 - INFO - 开始处理日期: 2025-02
2025-05-13 21:00:09,134 - INFO - Request Parameters - Page 1:
2025-05-13 21:00:09,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:09,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:09,743 - INFO - Response - Page 1:
2025-05-13 21:00:09,947 - INFO - 第 1 页获取到 100 条记录
2025-05-13 21:00:09,947 - INFO - Request Parameters - Page 2:
2025-05-13 21:00:09,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:09,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:10,478 - INFO - Response - Page 2:
2025-05-13 21:00:10,681 - INFO - 第 2 页获取到 100 条记录
2025-05-13 21:00:10,681 - INFO - Request Parameters - Page 3:
2025-05-13 21:00:10,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:10,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:11,197 - INFO - Response - Page 3:
2025-05-13 21:00:11,400 - INFO - 第 3 页获取到 100 条记录
2025-05-13 21:00:11,400 - INFO - Request Parameters - Page 4:
2025-05-13 21:00:11,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:11,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:11,947 - INFO - Response - Page 4:
2025-05-13 21:00:12,150 - INFO - 第 4 页获取到 100 条记录
2025-05-13 21:00:12,150 - INFO - Request Parameters - Page 5:
2025-05-13 21:00:12,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:12,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:12,650 - INFO - Response - Page 5:
2025-05-13 21:00:12,853 - INFO - 第 5 页获取到 100 条记录
2025-05-13 21:00:12,853 - INFO - Request Parameters - Page 6:
2025-05-13 21:00:12,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:12,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:13,415 - INFO - Response - Page 6:
2025-05-13 21:00:13,618 - INFO - 第 6 页获取到 100 条记录
2025-05-13 21:00:13,618 - INFO - Request Parameters - Page 7:
2025-05-13 21:00:13,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:13,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:14,056 - INFO - Response - Page 7:
2025-05-13 21:00:14,259 - INFO - 第 7 页获取到 70 条记录
2025-05-13 21:00:14,259 - INFO - 查询完成，共获取到 670 条记录
2025-05-13 21:00:14,259 - INFO - 获取到 670 条表单数据
2025-05-13 21:00:14,259 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-13 21:00:14,275 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 21:00:14,275 - INFO - 开始处理日期: 2025-03
2025-05-13 21:00:14,275 - INFO - Request Parameters - Page 1:
2025-05-13 21:00:14,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:14,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:14,743 - INFO - Response - Page 1:
2025-05-13 21:00:14,947 - INFO - 第 1 页获取到 100 条记录
2025-05-13 21:00:14,947 - INFO - Request Parameters - Page 2:
2025-05-13 21:00:14,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:14,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:15,447 - INFO - Response - Page 2:
2025-05-13 21:00:15,650 - INFO - 第 2 页获取到 100 条记录
2025-05-13 21:00:15,650 - INFO - Request Parameters - Page 3:
2025-05-13 21:00:15,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:15,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:16,134 - INFO - Response - Page 3:
2025-05-13 21:00:16,337 - INFO - 第 3 页获取到 100 条记录
2025-05-13 21:00:16,337 - INFO - Request Parameters - Page 4:
2025-05-13 21:00:16,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:16,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:16,775 - INFO - Response - Page 4:
2025-05-13 21:00:16,978 - INFO - 第 4 页获取到 100 条记录
2025-05-13 21:00:16,978 - INFO - Request Parameters - Page 5:
2025-05-13 21:00:16,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:16,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:17,415 - INFO - Response - Page 5:
2025-05-13 21:00:17,618 - INFO - 第 5 页获取到 100 条记录
2025-05-13 21:00:17,618 - INFO - Request Parameters - Page 6:
2025-05-13 21:00:17,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:17,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:18,103 - INFO - Response - Page 6:
2025-05-13 21:00:18,306 - INFO - 第 6 页获取到 100 条记录
2025-05-13 21:00:18,306 - INFO - Request Parameters - Page 7:
2025-05-13 21:00:18,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:18,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:18,775 - INFO - Response - Page 7:
2025-05-13 21:00:18,978 - INFO - 第 7 页获取到 61 条记录
2025-05-13 21:00:18,978 - INFO - 查询完成，共获取到 661 条记录
2025-05-13 21:00:18,978 - INFO - 获取到 661 条表单数据
2025-05-13 21:00:18,993 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-13 21:00:19,009 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-13 21:00:19,009 - INFO - 开始处理日期: 2025-04
2025-05-13 21:00:19,009 - INFO - Request Parameters - Page 1:
2025-05-13 21:00:19,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:19,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:19,493 - INFO - Response - Page 1:
2025-05-13 21:00:19,697 - INFO - 第 1 页获取到 100 条记录
2025-05-13 21:00:19,697 - INFO - Request Parameters - Page 2:
2025-05-13 21:00:19,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:19,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:20,150 - INFO - Response - Page 2:
2025-05-13 21:00:20,353 - INFO - 第 2 页获取到 100 条记录
2025-05-13 21:00:20,353 - INFO - Request Parameters - Page 3:
2025-05-13 21:00:20,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:20,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:20,853 - INFO - Response - Page 3:
2025-05-13 21:00:21,056 - INFO - 第 3 页获取到 100 条记录
2025-05-13 21:00:21,056 - INFO - Request Parameters - Page 4:
2025-05-13 21:00:21,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:21,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:21,493 - INFO - Response - Page 4:
2025-05-13 21:00:21,697 - INFO - 第 4 页获取到 100 条记录
2025-05-13 21:00:21,697 - INFO - Request Parameters - Page 5:
2025-05-13 21:00:21,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:21,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:22,134 - INFO - Response - Page 5:
2025-05-13 21:00:22,337 - INFO - 第 5 页获取到 100 条记录
2025-05-13 21:00:22,337 - INFO - Request Parameters - Page 6:
2025-05-13 21:00:22,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:22,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:22,806 - INFO - Response - Page 6:
2025-05-13 21:00:23,009 - INFO - 第 6 页获取到 100 条记录
2025-05-13 21:00:23,009 - INFO - Request Parameters - Page 7:
2025-05-13 21:00:23,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:23,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:23,400 - INFO - Response - Page 7:
2025-05-13 21:00:23,603 - INFO - 第 7 页获取到 54 条记录
2025-05-13 21:00:23,603 - INFO - 查询完成，共获取到 654 条记录
2025-05-13 21:00:23,603 - INFO - 获取到 654 条表单数据
2025-05-13 21:00:23,603 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-13 21:00:23,603 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-05-13 21:00:24,243 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MR4
2025-05-13 21:00:24,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97726.48, 'new_value': 64020.26}, {'field': 'offline_amount', 'old_value': 937975.33, 'new_value': 974272.6}, {'field': 'total_amount', 'old_value': 1035701.81, 'new_value': 1038292.86}]
2025-05-13 21:00:24,243 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG5
2025-05-13 21:00:24,728 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MG5
2025-05-13 21:00:24,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347838.93, 'new_value': 348033.19}, {'field': 'total_amount', 'old_value': 347838.93, 'new_value': 348033.19}]
2025-05-13 21:00:24,728 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-05-13 21:00:25,150 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M8A
2025-05-13 21:00:25,150 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 190441.6, 'new_value': 183342.3}, {'field': 'offline_amount', 'old_value': 743137.43, 'new_value': 750136.73}, {'field': 'total_amount', 'old_value': 933579.03, 'new_value': 933479.03}]
2025-05-13 21:00:25,150 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-05-13 21:00:25,556 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MOB
2025-05-13 21:00:25,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143337.16, 'new_value': 143368.24}, {'field': 'total_amount', 'old_value': 143337.16, 'new_value': 143368.24}]
2025-05-13 21:00:25,556 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-05-13 21:00:25,931 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MK6
2025-05-13 21:00:25,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 375043.85, 'new_value': 454722.87}, {'field': 'total_amount', 'old_value': 375043.85, 'new_value': 454722.87}]
2025-05-13 21:00:25,931 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-05-13 21:00:26,400 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M58
2025-05-13 21:00:26,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162230.49, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 126903.63, 'new_value': 380834.12}, {'field': 'total_amount', 'old_value': 289134.12, 'new_value': 380834.12}]
2025-05-13 21:00:26,400 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-05-13 21:00:26,853 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MFH
2025-05-13 21:00:26,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 620.72, 'new_value': 385.75}, {'field': 'offline_amount', 'old_value': 765750.97, 'new_value': 765986.54}, {'field': 'total_amount', 'old_value': 766371.69, 'new_value': 766372.29}]
2025-05-13 21:00:26,853 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-05-13 21:00:27,321 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MSJ
2025-05-13 21:00:27,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1909664.61, 'new_value': 1835260.92}, {'field': 'offline_amount', 'old_value': 292205.0, 'new_value': 365608.69}, {'field': 'total_amount', 'old_value': 2201869.61, 'new_value': 2200869.61}]
2025-05-13 21:00:27,321 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-05-13 21:00:27,837 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MJ
2025-05-13 21:00:27,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88452.59, 'new_value': 46177.69}, {'field': 'offline_amount', 'old_value': 1065717.47, 'new_value': 1108019.21}, {'field': 'total_amount', 'old_value': 1123141.44, 'new_value': 1154196.9}]
2025-05-13 21:00:27,853 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-05-13 21:00:28,306 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO6
2025-05-13 21:00:28,306 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19930.08, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 304108.0, 'new_value': 332809.88}, {'field': 'total_amount', 'old_value': 324038.08, 'new_value': 332809.88}, {'field': 'order_count', 'old_value': 1606, 'new_value': 1675}]
2025-05-13 21:00:28,306 - INFO - 日期 2025-04 处理完成 - 更新: 10 条，插入: 0 条，错误: 0 条
2025-05-13 21:00:28,306 - INFO - 开始处理日期: 2025-05
2025-05-13 21:00:28,306 - INFO - Request Parameters - Page 1:
2025-05-13 21:00:28,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:28,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:28,821 - INFO - Response - Page 1:
2025-05-13 21:00:29,025 - INFO - 第 1 页获取到 100 条记录
2025-05-13 21:00:29,025 - INFO - Request Parameters - Page 2:
2025-05-13 21:00:29,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:29,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:29,525 - INFO - Response - Page 2:
2025-05-13 21:00:29,728 - INFO - 第 2 页获取到 100 条记录
2025-05-13 21:00:29,728 - INFO - Request Parameters - Page 3:
2025-05-13 21:00:29,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:29,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:30,165 - INFO - Response - Page 3:
2025-05-13 21:00:30,368 - INFO - 第 3 页获取到 100 条记录
2025-05-13 21:00:30,368 - INFO - Request Parameters - Page 4:
2025-05-13 21:00:30,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:30,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:30,853 - INFO - Response - Page 4:
2025-05-13 21:00:31,056 - INFO - 第 4 页获取到 100 条记录
2025-05-13 21:00:31,056 - INFO - Request Parameters - Page 5:
2025-05-13 21:00:31,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:31,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:31,665 - INFO - Response - Page 5:
2025-05-13 21:00:31,868 - INFO - 第 5 页获取到 100 条记录
2025-05-13 21:00:31,868 - INFO - Request Parameters - Page 6:
2025-05-13 21:00:31,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:31,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:32,384 - INFO - Response - Page 6:
2025-05-13 21:00:32,587 - INFO - 第 6 页获取到 100 条记录
2025-05-13 21:00:32,587 - INFO - Request Parameters - Page 7:
2025-05-13 21:00:32,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-13 21:00:32,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-13 21:00:32,931 - INFO - Response - Page 7:
2025-05-13 21:00:33,134 - INFO - 第 7 页获取到 25 条记录
2025-05-13 21:00:33,134 - INFO - 查询完成，共获取到 625 条记录
2025-05-13 21:00:33,134 - INFO - 获取到 625 条表单数据
2025-05-13 21:00:33,134 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-13 21:00:33,134 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-13 21:00:33,603 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-13 21:00:33,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12844.92, 'new_value': 14458.92}, {'field': 'offline_amount', 'old_value': 7465.47, 'new_value': 7907.47}, {'field': 'total_amount', 'old_value': 20310.39, 'new_value': 22366.39}, {'field': 'order_count', 'old_value': 1014, 'new_value': 1120}]
2025-05-13 21:00:33,603 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-13 21:00:34,056 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-13 21:00:34,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8362.6, 'new_value': 9475.3}, {'field': 'total_amount', 'old_value': 8712.6, 'new_value': 9825.3}, {'field': 'order_count', 'old_value': 630, 'new_value': 713}]
2025-05-13 21:00:34,056 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-13 21:00:34,509 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-13 21:00:34,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24232.0, 'new_value': 24987.0}, {'field': 'offline_amount', 'old_value': 83669.0, 'new_value': 91235.0}, {'field': 'total_amount', 'old_value': 107901.0, 'new_value': 116222.0}, {'field': 'order_count', 'old_value': 736, 'new_value': 787}]
2025-05-13 21:00:34,509 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-13 21:00:34,993 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-13 21:00:34,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41095.0, 'new_value': 44227.0}, {'field': 'offline_amount', 'old_value': 51449.0, 'new_value': 55981.0}, {'field': 'total_amount', 'old_value': 92544.0, 'new_value': 100208.0}, {'field': 'order_count', 'old_value': 2186, 'new_value': 2373}]
2025-05-13 21:00:34,993 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-13 21:00:35,446 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-13 21:00:35,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10084.19, 'new_value': 11483.83}, {'field': 'offline_amount', 'old_value': 42538.51, 'new_value': 48409.22}, {'field': 'total_amount', 'old_value': 52622.7, 'new_value': 59893.05}, {'field': 'order_count', 'old_value': 704, 'new_value': 805}]
2025-05-13 21:00:35,446 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-13 21:00:35,884 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-13 21:00:35,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10863.46, 'new_value': 11775.49}, {'field': 'offline_amount', 'old_value': 9473.22, 'new_value': 10764.95}, {'field': 'total_amount', 'old_value': 20336.68, 'new_value': 22540.44}, {'field': 'order_count', 'old_value': 1127, 'new_value': 1251}]
2025-05-13 21:00:35,884 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-13 21:00:36,306 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-13 21:00:36,306 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2374.5, 'new_value': 2549.7}, {'field': 'offline_amount', 'old_value': 53082.83, 'new_value': 60088.33}, {'field': 'total_amount', 'old_value': 55457.33, 'new_value': 62638.03}, {'field': 'order_count', 'old_value': 886, 'new_value': 999}]
2025-05-13 21:00:36,306 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-13 21:00:36,790 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-13 21:00:36,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33298.99, 'new_value': 36694.24}, {'field': 'total_amount', 'old_value': 33298.99, 'new_value': 36694.24}, {'field': 'order_count', 'old_value': 1697, 'new_value': 1867}]
2025-05-13 21:00:36,790 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-13 21:00:37,275 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-13 21:00:37,275 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40117.91, 'new_value': 45998.44}, {'field': 'offline_amount', 'old_value': 36558.57, 'new_value': 41781.59}, {'field': 'total_amount', 'old_value': 76676.48, 'new_value': 87780.03}, {'field': 'order_count', 'old_value': 2563, 'new_value': 2978}]
2025-05-13 21:00:37,275 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-13 21:00:37,728 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-13 21:00:37,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83353.53, 'new_value': 93594.46}, {'field': 'total_amount', 'old_value': 90269.19, 'new_value': 100510.12}, {'field': 'order_count', 'old_value': 1753, 'new_value': 1975}]
2025-05-13 21:00:37,728 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-13 21:00:38,181 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-13 21:00:38,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224072.76, 'new_value': 236721.19}, {'field': 'total_amount', 'old_value': 224072.76, 'new_value': 236721.19}, {'field': 'order_count', 'old_value': 2217, 'new_value': 2337}]
2025-05-13 21:00:38,181 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-13 21:00:38,618 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-13 21:00:38,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21154.5, 'new_value': 22155.52}, {'field': 'offline_amount', 'old_value': 114515.75, 'new_value': 123603.89}, {'field': 'total_amount', 'old_value': 135670.25, 'new_value': 145759.41}, {'field': 'order_count', 'old_value': 4082, 'new_value': 4407}]
2025-05-13 21:00:38,618 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-13 21:00:39,118 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-13 21:00:39,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77533.96, 'new_value': 80189.76}, {'field': 'total_amount', 'old_value': 77533.96, 'new_value': 80189.76}, {'field': 'order_count', 'old_value': 127, 'new_value': 133}]
2025-05-13 21:00:39,118 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-13 21:00:39,618 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-13 21:00:39,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35827.0, 'new_value': 38613.0}, {'field': 'total_amount', 'old_value': 35827.0, 'new_value': 38613.0}, {'field': 'order_count', 'old_value': 1984, 'new_value': 2125}]
2025-05-13 21:00:39,618 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-13 21:00:40,087 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-13 21:00:40,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76357.5, 'new_value': 85684.5}, {'field': 'total_amount', 'old_value': 76357.5, 'new_value': 85684.5}, {'field': 'order_count', 'old_value': 3066, 'new_value': 3500}]
2025-05-13 21:00:40,087 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-13 21:00:40,540 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-13 21:00:40,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14735.71, 'new_value': 15386.61}, {'field': 'offline_amount', 'old_value': 29123.22, 'new_value': 30478.5}, {'field': 'total_amount', 'old_value': 43858.93, 'new_value': 45865.11}, {'field': 'order_count', 'old_value': 376, 'new_value': 392}]
2025-05-13 21:00:40,540 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-13 21:00:41,025 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-13 21:00:41,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37898.0, 'new_value': 42491.0}, {'field': 'total_amount', 'old_value': 37898.0, 'new_value': 42491.0}, {'field': 'order_count', 'old_value': 856, 'new_value': 964}]
2025-05-13 21:00:41,025 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-13 21:00:41,462 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-13 21:00:41,462 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 221, 'new_value': 261}]
2025-05-13 21:00:41,462 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-13 21:00:41,915 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-13 21:00:41,915 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9589.0, 'new_value': 9687.0}, {'field': 'total_amount', 'old_value': 24834.0, 'new_value': 24932.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-13 21:00:41,915 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-13 21:00:42,415 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-13 21:00:42,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5189.97, 'new_value': 5594.02}, {'field': 'offline_amount', 'old_value': 27711.52, 'new_value': 29999.76}, {'field': 'total_amount', 'old_value': 32901.49, 'new_value': 35593.78}, {'field': 'order_count', 'old_value': 728, 'new_value': 793}]
2025-05-13 21:00:42,415 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-13 21:00:42,853 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-13 21:00:42,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57745.84, 'new_value': 61583.94}, {'field': 'total_amount', 'old_value': 57753.84, 'new_value': 61591.94}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-13 21:00:42,853 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-13 21:00:43,368 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-13 21:00:43,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24070.0, 'new_value': 26308.0}, {'field': 'total_amount', 'old_value': 24070.0, 'new_value': 26308.0}, {'field': 'order_count', 'old_value': 4862, 'new_value': 5397}]
2025-05-13 21:00:43,368 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-13 21:00:43,790 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-13 21:00:43,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34750.0, 'new_value': 38107.0}, {'field': 'total_amount', 'old_value': 34750.0, 'new_value': 38107.0}, {'field': 'order_count', 'old_value': 4862, 'new_value': 5397}]
2025-05-13 21:00:43,790 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-13 21:00:44,196 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-13 21:00:44,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50216.0, 'new_value': 53908.0}, {'field': 'total_amount', 'old_value': 50216.0, 'new_value': 53908.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 145}]
2025-05-13 21:00:44,196 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-13 21:00:44,634 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-13 21:00:44,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9049.0, 'new_value': 10627.0}, {'field': 'total_amount', 'old_value': 9049.0, 'new_value': 10627.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 60}]
2025-05-13 21:00:44,634 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-13 21:00:45,165 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-13 21:00:45,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 80.0}, {'field': 'offline_amount', 'old_value': 43618.0, 'new_value': 51522.0}, {'field': 'total_amount', 'old_value': 43618.0, 'new_value': 51602.0}, {'field': 'order_count', 'old_value': 4424, 'new_value': 5304}]
2025-05-13 21:00:45,165 - INFO - 日期 2025-05 处理完成 - 更新: 26 条，插入: 0 条，错误: 0 条
2025-05-13 21:00:45,165 - INFO - 数据同步完成！更新: 36 条，插入: 0 条，错误: 0 条
2025-05-13 21:00:45,165 - INFO - =================同步完成====================
